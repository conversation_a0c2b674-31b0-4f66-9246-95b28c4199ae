<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="true"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="费用结算时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="true"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="费用发生时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange1" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="true"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="线上单号:">
                    <!-- <el-input v-model.trim="filter.ProCode" :clearable="true" maxlength="20" placeholder="线上单号" style="width:130px;"/> -->
                    <inputYunhan ref="productCode2" :inputt.sync="filter.OrderNo" v-model="filter.OrderNo"
                        class="publicCss" placeholder="线上单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="2000" :maxlength="600000" @callback="orderNoInnerBack" title="线上单号">
                    </inputYunhan>
                </el-form-item>
                <el-form-item label="商品编号:">
                    <el-input v-model.trim="filter.ProCode" :clearable="true" maxlength="20" placeholder="商品编号" style="width:130px;"/>
                </el-form-item>
                <el-form-item label="店铺:">
                    <YhShopSelector :names="['所有店铺']" :values="[-1]"  platform="7" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{filter.shopCode=v[0].join(','); }">
                    </YhShopSelector>
                </el-form-item>
                <el-form-item label="账单项目:">
                    <el-select filterable v-model="accountTypeist" placeholder="请选择账单项目"  multiple  clearable style="width: 130px">
                        <el-option label="代收配送费" value="代收配送费" />
                        <el-option label="广告联合活动降扣佣金" value="广告联合活动降扣佣金" />
                        <el-option label="价保返佣" value="价保返佣" />
                        <el-option label="价保扣款" value="价保扣款" />
                        <el-option label="交易服务费" value="交易服务费" />
                        <el-option label="随单送的京豆" value="随单送的京豆" />
                        <el-option label="佣金" value="佣金" />
                        <el-option label="咚咚红包" value="咚咚红包" />
                        <el-option label="逆向保价" value="逆向保价" />
                        <el-option label="运费保险服务费" value="运费保险服务费" />
                        <el-option label="商品保险服务费" value="商品保险服务费" />
                        <el-option label="卖家返还运费" value="卖家返还运费" />
                        <el-option label="售后卖家赔付费" value="售后卖家赔付费" />
                        <el-option label="京喜直营服务费" value="直营服务费" />
                        <el-option label="京喜直营价保补贴款" value="直营价保补贴款" />
                        <el-option label="平台券价保补贴" value="平台券价保补贴" />
                        <el-option label="京准通代收款" value="京准通代收款" />
                        <el-option label="商家向京东返点" value="商家向京东返点" />
                        <el-option label="货款" value="货款" />
                        <el-option label="提现" value="提现" />
                        <el-option label="安联保险费" value="安联保险费" />
                        <el-option label="综合违约金" value="综合违约金" />
                        <el-option label="小额收款" value="小额收款" />
                        <el-option label="违约金" value="违约金" />
                        <el-option label="充值" value="充值" />
                    </el-select>
                </el-form-item>
                <el-form-item label="ERP账务类型:">
                    <el-select filterable  v-model="BillTypeList" placeholder="请选择ERP账务类型"  multiple clearable style="width: 130px">
                        <el-option label="代收配送费" value="代收配送费" />
                        <el-option label="广告联合活动降扣佣金" value="广告联合活动降扣佣金" />
                        <el-option label="价保返佣" value="价保返佣" />
                        <el-option label="价保扣款" value="价保扣款" />
                        <el-option label="交易服务费" value="交易服务费" />
                        <el-option label="随单送的京豆" value="随单送的京豆" />
                        <el-option label="佣金" value="佣金" />
                        <el-option label="咚咚红包" value="咚咚红包" />
                        <el-option label="逆向保价" value="逆向保价" />
                        <el-option label="运费保险服务费" value="运费保险服务费" />
                        <el-option label="商品保险服务费" value="商品保险服务费" />
                        <el-option label="卖家返还运费" value="卖家返还运费" />
                        <el-option label="售后卖家赔付费" value="售后卖家赔付费" />
                        <el-option label="京喜直营服务费" value="京喜直营服务费" />
                        <el-option label="其他账单" value="其他账单" />
                        <el-option label="综合违约金" value="综合违约金" />
                        <el-option label="无" value="无" />
                    </el-select>
                </el-form-item>
                <el-form-item label="单据类型:">
                    <el-select filterable v-model="filter.documentTypeList" placeholder="请选择单据类型"  multiple collapse-tags  clearable style="width: 173px">
                        <el-option label="取消退款单" value="取消退款单" />
                        <el-option label="订单" value="订单" />
                        <el-option label="售后服务单" value="售后服务单" />
                        <el-option label="非销售单" value="非销售单" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                <el-switch v-model="filter.isGroup"  active-color="#67C23A"  inactive-color="#409EFF"
                    @change="onSearch"   active-text="汇总查询"  active-value="1" inactive-value="0"  inactive-text="全量查询">
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="4" :sm="6" :md="8" :lg="6">
                    <el-date-picker style="width: 100%" v-model="importfilter.YearMonthDay" type="date" format="yyyyMMdd"
                    value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                  </el-col>
                <el-col :xs="4" :sm="6" :md="8" :lg="6" >
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                        :file-list="fileList">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog> -->
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import {getNewJDBillingCharge,exportNewJDBillingCharge} from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";

const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', tipmesg: '', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'feeSettlementTime', label: '费用结算时间', tipmesg: '', width: '110', sortable: 'custom', },
    { istrue: true, prop: 'feeOccurrenceTime', label: '费用发生时间', tipmesg: '', width: '110', sortable: 'custom', },
    { istrue: true, prop: 'orderNo', label: '线上单号', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '商品编号', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'proName', label: '商品名称', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom',  tipmesg: '', width: '240', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'billingItem', label: '账单项目', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'billType', label: 'ERP账务类型', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'documentType', label: '单据类型', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'amountIncome', label: '金额', tipmesg: '收入金额-支出金额', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'feeItem', label: '费用项', tipmesg: '', sortable: 'custom', },
]

const tableHandles = [
];

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton,inputYunhan,YhShopSelector },

    data() {
        return {
            that: this,
            importfilter: {
             YearMonthDay:null
            },
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                shopCode: null,
                BillType:null,
                timerange1:null,
                timerange2:null,
                startOccTime: null,
                endOccTime: null,
                startSettleTime: null,
                endSettleTime: null,
                documentTypeList: [],
            },
            platformlist:platformlist,
            BillTypeList:[],
            accountTypeist:[],
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    platform: null
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "RecordId", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            //dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: []
        };
    },

    async mounted() {
        await this.onSearch()
        //await this.onchangeplatform()
    },

    methods: {
    async onExport() {
     if (this.onExporting) return;
     try{
        this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startOccTime = null;
        this.filter.endOccTime = null;
        this.filter.startSettleTime = null;
        this.filter.endSettleTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.startOccTime = this.filter.timerange1[0];
                this.filter.endOccTime = this.filter.timerange1[1];
            }
            if (this.filter.timerange2) {
                this.filter.startSettleTime = this.filter.timerange2[0];
                this.filter.endSettleTime = this.filter.timerange2[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
        this.uploadLoading = true;
        const params = {...this.pager,...this.filter}
        var res= await exportNewJDBillingCharge(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','新版京东日报账单费用_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },

        //获取店铺
        // async onchangeplatform() {
        //     this.categorylist = []
        //     const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100 });
        //     this.filter.shopCode = null
        //     this.shopList = res1.data.list
        // },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
        this.filter.endTime = null;
        this.filter.startOccTime = null;
        this.filter.endOccTime = null;
        this.filter.startSettleTime = null;
        this.filter.endSettleTime = null;
        if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.timerange1) {
                this.filter.startOccTime = this.filter.timerange1[0];
                this.filter.endOccTime = this.filter.timerange1[1];
            }
            if (this.filter.timerange2) {
                this.filter.startSettleTime = this.filter.timerange2[0];
                this.filter.endSettleTime = this.filter.timerange2[1];
            }
            this.filter.BillType = this.BillTypeList.join(',');
            this.filter.AccountType = this.accountTypeist.join(',');
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }

            this.listLoading = true
            const res = await getNewJDBillingCharge(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        orderNoInnerBack(val) {
            this.filter.OrderNo = val;
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        // startImport() {
        //    // this.importDialog.filter.platform = null
        //     this.fileList = []
        //     this.uploadLoading=false
        //     this.dialogVisible = true;
        // },
        // //取消导入
        // cancelImport() {
        //     this.dialogVisible = false;
        // },
        // uploadSuccess(response, file, fileList) {
        //     if (response.code == 200) {
        //     } else {
        //         fileList.splice(fileList.indexOf(file), 1);
        //     }
        // },
        // async submitUpload() {
        //     if (!this.importfilter.YearMonthDay || this.importfilter.YearMonthDay == null) {
        // this.$message({ message: "请先选择日期", type: "warning" });
        // return false;
        //    }
        //     if (!this.fileList || this.fileList.length == 0) {
        //         this.$message({ message: "请选取文件", type: "warning" });
        //         return false;
        //     }
        //     this.fileHasSubmit = true;
        //     this.uploadLoading = true;
        //     this.$refs.upload.submit();
        // },
        // clearFiles(){
        //     this.$refs['upload'].clearFiles();
        // },
        // async uploadFile(item) {
        //     if (!this.fileHasSubmit) {
        //         return false;
        //     }
        //     this.fileHasSubmit = false;
        //     this.uploadLoading = true;
        //     const form = new FormData();
        //     //form.append("platForm", this.importDialog.filter.platform);
        //     form.append("token", this.token);
        //     form.append("upfile", item.file);
        //     form.append("YearMonthDay", this.importfilter.YearMonthDay);
        //     let res = await importBillFee(form);
        //         if (res.code == 1) {
        //             this.$message({ message: "上传成功,正在导入中...", type: "success" });
        //             this.$refs.upload.clearFiles();
        //             this.dialogVisible = false;
        //         }
        //     this.fileList = []
        //     this.uploadLoading = false;
        // },
        // async uploadChange(file, fileList) {
        //     let files=[];
        //     files.push(file)
        //     this.fileList = files;
        // },
        // async uploadRemove(file, fileList) {
        //     this.fileList = []
        // },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

</style>
