<template>
  <section style="padding:10px;">
    <el-row :gutter="10">
      <el-col :span="6" class="toolbar roles">
        <el-card>
          <template #header>
            <div class="clearfix">
              <span>角色</span>
              <el-button
                :loading="loadingRoles"
                type="text"
                style="float: right; padding: 3px 0"
                @click="getRoles"
              >刷新</el-button>
            </div>
          </template>
          <!-- <el-tree ref="rolesTree" :indent="15" highlight-current :expand-on-click-node="false" :data="roles" :default-expand-all="isExpansion" node-key="id" :props="{label: 'name',children: 'children',isLeaf: 'hasChildren'}" @node-click="roleSelect">
          </el-tree>-->
          <div style="height:760px;">
            <el-table
              ref="rolesTree"
              :indent="10"
              height="760px"
              :show-header="false"
              :data="roles"
              border
              lazy
              :load="rolesLoad"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              row-key="id"
              highlight-current-row
              style="width: 100%;"
              @row-click="roleSelect"
              @expand-change="handleNodeClick"
            >
              <el-table-column prop="name" />
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" class="toolbar perms">
        <el-card>
          <template #header>
            <div class="clearfix">
              <span>权限</span>

              <my-confirm-button
                v-if="checkPermission(['api:admin:permission:assign'])"
                :loading="loadingSave"
                :placement="'left'"
                type="text"
                class="save"
                style="float: right;"
                @click="save"
              >
                <template #content>
                  <p>确定要保存吗？</p>
                </template>
                保存
              </my-confirm-button>
              <el-button
                :loading="loadingPermissions"
                type="text"
                style="float: right; padding: 3px 0"
                @click="getPermissions"
              >刷新</el-button>
              <!-- <el-button
                type="text"
                @click="toggleRowExpansion"
                style="float: right; padding: 3px 0;margin-right: 5px;"
              >全部展开收缩切换+-</el-button>-->
            </div>
          </template>
          <div style="height:760px;">
            <el-table
              :data="viewList"
              style="width: 100%; margin-bottom: 20px"
              row-key="id"
              border
              lazy
              default-expand-all
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column prop="label" label="视图"></el-table-column>
              <el-table-column label="权限">
                <template #default="{row}">
                  <el-radio-group
                    v-if="row.isLeaf"
                    v-model="row.permission"
                    @input="input(row)"
                    class="ml-4"
                  >
                    <el-radio :label="0" size="large">禁止</el-radio>
                    <el-radio :label="1" size="large">需认证</el-radio>
                    <el-radio :label="2" size="large">免认证</el-radio>
                  </el-radio-group>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </section>
</template>
  
  <script>
import { treeToList, listToTree, getTreeParentsWithSelf } from "@/utils";
import {
  Getlist,
  GetRoleUShieldViewPermissionList,
  GetUserUShieldViewPermissionList,
  RoleUShieldAssign,
  UserUShieldAssign,
  GetRoleTrees
} from "@/api/admin/viewUShield.js";
import { getPermissions } from "@/api/admin/permission";
import MyConfirmButton from "@/components/my-confirm-button";

export default {
  name: "Assign",
  components: {
    MyConfirmButton
  },
  data() {
    return {
      radioValue: "",
      treeNodeMap: new Map(),
      roles: [],
      roleId: 0,
      trueId: 0,
      dataType: "role",
      permissionTree: [],
      apis: [],
      loadingRoles: false,
      loadingPermissions: false,
      loadingSave: false,
      checkedPermissions: [],
      chekedApis: [],
      isExpansion: true,
      isPermissionExpansion: true,
      modindex: 0,
      tableData: [],
      viewList: [],
      permissionData: {},
      apiIds: []
    };
  },
  computed: {
    // viewList() {
    //   return listToTree(this.tableData);
    // },
    disabledSave() {
      return !(
        this.roleId > 0 &&
        (this.checkedPermissions.length > 0 || this.chekedApis.length > 0)
      );
    }
  },
  mounted() {
    this.getlist();
    this.getRoles();
    this.getPermissions();
    this.getRolePermission();
  },
  methods: {
    //获取所有需要U盾的视图
    async getlist() {
      const res = await Getlist();
      this.tableData = res.data;
      this.tableData.forEach(item => {
        item.permission = 0;
      });
      const tmpData = JSON.parse(JSON.stringify(this.tableData));
      this.viewList = listToTree(tmpData);
    },
    //获取视图列表
    async rolesLoad(tree, treeNode, resolve) {
      this.treeNodeMap.set(tree.id, { tree, treeNode, resolve });
      setTimeout(() => {
        resolve(tree.children);
      }, 100);
    },
    // 获取角色列表
    async getRoles() {
      const self = this;
      self.loadingRoles = true;
      const res = await GetRoleTrees();
      self.loadingRoles = false;
      self.roles = res.data;
      if (self.roleId > 0) {
        self.$nextTick(function() {
          self.$nextTick(() => {
            const rows = treeToList(self.roles);
            self.$nextTick(function() {
              rows.forEach(row => {
                if (self.roleId == row.id) {
                  //高亮
                  self.$refs.rolesTree.setCurrentRow(row);
                }
              });
            });
          });
        });
      }
      //懒加载需要重新刷新节点
      self.treeNodeMap.forEach(function(value, key) {
        const { tree, treeNode, resolve } = self.treeNodeMap.get(key);
        // 收起子节点，下次点击的时候再调用load方法去后台查询
        self.$set(
          self.$refs.rolesTree.store.states.treeData[key],
          "loaded",
          false
        );
        self.$set(
          self.$refs.rolesTree.store.states.treeData[key],
          "expanded",
          false
        );
        // 清空缓存
        self.$set(self.$refs.rolesTree.store.states.lazyTreeNodeMap, key, []);
      });
    },
    // 获取权限树
    async getPermissions() {
      this.loadingPermissions = true;
      //   this.onSelectAll([]);

      const para = {};
      const res = await getPermissions(para);
      this.loadingPermissions = false;
      const tree = listToTree(_.cloneDeep(res.data));
      this.permissionTree = tree;
      this.getRolePermission();
    },
    flattenTree(treeData) {
      treeData.forEach(item => {
        let permissionData = {
          viewId: item.id,
          permission: item.permission
        };
        this.apiIds.push(permissionData);
        if (item.children && item.children.length > 0) {
          this.flattenTree(item.children);
        }
      });
    },
    input(row) {
      this.apiIds.forEach(item => {
        if (item.viewId === row.id) {
          item.permission = row.permission;
        }
      });
    },
    // 获取角色权限
    async getRolePermission() {
      if (!this.roleId > 0) {
        return;
      }
      this.loadingPermissions = true;
      var res = {};
      if (this.dataType == "role") {
        res = await GetRoleUShieldViewPermissionList({ roleId: this.roleId });
      } else {
        res = await GetUserUShieldViewPermissionList({ userId: this.trueId });
      }
      this.loadingPermissions = false;
      const permissionIds = res.data;
      var tmpData = JSON.parse(JSON.stringify(this.tableData)); //表格绑定的数据
      permissionIds.forEach(item => {
        tmpData.forEach(item2 => {
          if (item.viewId == item2.id) {
            item2.permission = item.permission;
          }
        });
      });
      this.viewList = listToTree(tmpData);
      this.flattenTree(this.viewList);
      //   this.viewList.forEach(item => {
      //     this.permissionData.viewId = item.id;
      //     this.permissionData.permission = item.permission;
      //     apiIds.push(this.permissionData);
      //   });

      //   const rows = treeToList(this.permissionTree);
      //   rows.forEach(row => {
      //     const checked = permissionIds.includes(row.id);
      //     this.$refs.multipleTable.toggleRowSelection(row, checked);
      //   });
      //   this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
      //     return s.id;
      //   });

      //   tmpData.forEach(item => {
      //     if (!this.checkedPermissions.includes(item)) {
      //       apiIds.push(item);
      //     }
      //   });
    },
    // 验证保存
    saveValidate() {
      let isValid = true;
      if (!(this.roleId > 0)) {
        this.$message({
          message: "请选择角色！",
          type: "warning"
        });
        isValid = false;
        return isValid;
      }
      if (!(this.checkedPermissions.length > 0 || this.chekedApis.length > 0)) {
        this.$message({
          message: "请选择权限！",
          type: "warning"
        });
        isValid = false;
        return isValid;
      }
      return isValid;
    },
    // 保存权限
    async save() {
      const permissions = this.apiIds;
      //   if (this.chekedApis.length > 0) {
      //     permissionIds.push(...this.chekedApis);
      //   }
      console.log(this.apiIds);
      this.loadingSave = true;
      var res = {};
      if (this.dataType == "role") {
        res = await RoleUShieldAssign({
          permissions: this.apiIds,
          id: this.roleId
        });
      } else {
        res = await UserUShieldAssign({
          permissions: this.apiIds,
          id: this.trueId
        });
      }
      this.loadingSave = false;
      if (!res?.success) {
        return;
      }
      this.$message({
        message: this.$t("admin.saveOk"),
        type: "success"
      });
    },
    roleSelect(row, column) {
      //this.$refs.rolesTree.toggleRowExpansion(row)
      this.roleId = row.id;
      this.dataType = row.dataType;
      this.trueId = row.trueId;
      //   this.onSelectAll([]);
      this.getRolePermission();
    },
    handleNodeClick(data, expanded) {
      if (this.roles.indexOf(data) != -1 && expanded) {
        this.modindex = this.roles.indexOf(data);
      }
      if (expanded) {
        //展开
        var idList = data.hierarchyCode.split(".");
        this.roles.indexOf(data) == -1
          ? this.toggleRowExpansionAll(
              this.roles[this.modindex].children,
              false,
              idList
            )
          : this.toggleRowExpansionAll(this.roles, false, idList);
      }

      //切换的时候 需要把之前的选中给清除
      this.roleId = 0;
      this.dataType = "role";
      this.trueId = 0;
      this.$refs.rolesTree.setCurrentRow({});
      //   this.onSelectAll([]);
    },
    selectApis(checked, row) {
      if (row.apis) {
        row.apis.forEach(a => {
          const index = this.chekedApis.indexOf(a.id);
          if (checked) {
            if (index === -1) {
              this.chekedApis.push(a.id);
            }
          } else {
            if (index > -1) {
              this.chekedApis.splice(index, 1);
            }
          }
        });
      }
    },
    // onSelectAll(selection) {
    //   const selections = treeToList(selection);
    //   const rows = treeToList(this.permissionTree);
    //   const checked = selections.length === rows.length;
    //   rows.forEach(row => {
    //     this.$refs.multipleTable.toggleRowSelection(row, checked);
    //     this.selectApis(checked, row);
    //   });

    //   this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
    //     return s.id;
    //   });
    // },
    // onSelect(selection, row) {
    //   const checked = selection.some(s => s.id === row.id);
    //   if (row.children && row.children.length > 0) {
    //     const rows = treeToList(row.children);
    //     rows.forEach(r => {
    //       this.$refs.multipleTable.toggleRowSelection(r, checked);
    //       this.selectApis(checked, r);
    //     });
    //   } else {
    //     this.selectApis(checked, row);
    //   }

    //   const parents = getTreeParentsWithSelf(this.permissionTree, row.id);
    //   parents.forEach(parent => {
    //     const checked = this.checkedPermissions.includes(parent.id);
    //     if (!checked) {
    //       this.$refs.multipleTable.toggleRowSelection(parent, true);
    //     }
    //   });

    //   this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
    //     return s.id;
    //   });
    // },
    // onChange(value, id) {
    //   if (value) {
    //     const parents = getTreeParentsWithSelf(this.permissionTree, id);
    //     parents.forEach(parent => {
    //       const checked = this.checkedPermissions.includes(parent.id);
    //       if (!checked) {
    //         this.$refs.multipleTable.toggleRowSelection(parent, true);
    //       }
    //     });

    //     this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
    //       return s.id;
    //     });
    //   }
    // },
    toggleRowExpansion() {
      this.isPermissionExpansion = !this.isPermissionExpansion;
      //this.permissionTree

      this.permissionTree.forEach(item => {
        this.$refs.multipleTable.toggleRowExpansion(
          item,
          this.isPermissionExpansion
        );
      });
    },
    toggleRowExpansionAll(data, isExpansion, existArray) {
      data.forEach(item => {
        if (item.dataType != "user") {
          if (!existArray.includes(item.id.toString())) {
            this.$refs.rolesTree.toggleRowExpansion(item, isExpansion);
          }
          if (item.children !== undefined && item.children !== null) {
            this.toggleRowExpansionAll(item.children, isExpansion, existArray);
          }
        }
      });
    }
  }
};
</script>
  
  <style scoped lang="scss" >
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.save ::v-deep [_button] {
  padding: 3px 0px;
}
</style>
  