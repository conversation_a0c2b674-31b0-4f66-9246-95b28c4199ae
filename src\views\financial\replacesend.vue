<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="结算月份:">
          <el-date-picker style="width:120px" v-model="filter.settMonth" type="month" format="yyyyMM" value-format="yyyy-MM" placeholder="结算月份"
                  :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
        </el-form-item>        
        <el-form-item label="店铺名称:">
              <el-input v-model="filter.shopName" placeholder="店铺名称" @change="onSearch"/>             
          </el-form-item>
          <el-form-item label="产品ID:">
              <el-input v-model="filter.proCode" placeholder="产品ID" @change="onSearch"/>             
          </el-form-item>
          <el-form-item label="订单号:">
                <el-input v-model="filter.orderNo" placeholder="订单号" @change="onSearch"/>         
            </el-form-item>
            <el-form-item label="商品编码:">
                <el-input v-model="filter.goodsCode" placeholder="商品编码" @change="onSearch"/>         
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
          </el-form>
    </template>
 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
                            :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { pageReplaceSend,exportReplaceSend} from "@/api/bookkeeper/replacesend";
const tableCols =[
       {istrue:true,prop:'date',label:'结算月份', width:'100',sortable:'custom',formatter:(row)=>formatTime(row.date, "YYYYMM")},
       {istrue:true,prop:'nameShop',label:'店铺', width:'150',sortable:'custom',},
       {istrue:true,prop:'binama',label:'商品编码', width:'110',sortable:'custom',},
       {istrue:true,prop:'nameProduct',label:'商品名称', width:'auto',sortable:'custom',},
       {istrue:true,prop:'count',label:'数量', width:'80',sortable:'custom',},
       {istrue:true,prop:'amountPay',label:'买家支付金额', width:'120',sortable:'custom',},
       {istrue:true,prop:'amountReplace',label:'代拍金额', width:'110',sortable:'custom',},
       {istrue:true,prop:'orderNumberOnline',label:'线上订单号', width:'180',sortable:'custom',},
       {istrue:true,prop:'proCode',label:'产品ID', width:'120',sortable:'custom',},
       {istrue:true,prop:'amountJstCost',label:'聚水潭成本', width:'130',sortable:'custom',},
       {istrue:true,prop:'amountMargin',label:'差额', width:'110',sortable:'custom',},
     ];
const tableHandles1=[
        {label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
        shopName:"",
        orderNo:null,
        goodsCode:null,
        proCode:null,
        settMonth:formatTime(dayjs().startOf("month"), "YYYY-MM"),
      },
      list: [],
      summaryarry:{},
      pager:{OrderBy:"nameShop",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      dialogVisible: false,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      }
    }
  },
  async mounted() {
    await this.getList(); 
  },
  methods: {
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportReplaceSend(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','代拍成本_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getList()
    },
    //分页查询
    async getList() {
      var params=this.getCondition();
      if(params==false){
        return;
      }
      this.listLoading = true
      const res = await pageReplaceSend(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    }, 
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
