<template>
    <my-container v-loading="false">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" >
                <el-row >
                    <el-col :span="24">
                        <el-form-item  label="竞品ID：">
                            {{form.goodsCompeteId}}
                        </el-form-item>                                         
                    </el-col>
                </el-row>               
                <el-row >
                    <el-col :span="24">
                        <el-form-item label="竞品标题：">
                            {{form.goodsCompeteName}}
                        </el-form-item>
                    </el-col>
                </el-row>               
                <el-row >    
                    <el-col :span="24">
                        <el-form-item label="归档原因：">                          
                            <el-radio-group v-model="form.sealCloseReasonRadio" 
                            @input="()=>{
                                    if(form.sealCloseReasonRadio!=='其他自定义')                                    
                                        form.sealCloseReason=form.sealCloseReasonRadio;
                                    else
                                        form.sealCloseReason='';
                                }">
                                <el-radio label="下架了"></el-radio>
                                <el-radio label="利润少"></el-radio>
                                <el-radio label="不想做"></el-radio>
                                <el-radio label="其他自定义"></el-radio>
                            </el-radio-group> 
                            <el-input type="textarea" v-model="form.sealCloseReason" v-if="form.sealCloseReasonRadio=='其他自定义'" :maxlength="50" show-word-limit></el-input>                       
                        </el-form-item>
                    </el-col>                   
                </el-row>               
                <el-row >
                    <el-col :span="24">
                        <span style="color:red">已选品归档后，列表将不再显示该选品！</span>
                    </el-col>     
                </el-row>               
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button type="primary" @click="onSave(true)">归档</el-button>                    
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>

<script>

    import MyContainer from "@/components/my-container";
    import {
      SealCloseChooseAsync
    } from '@/api/operatemanage/productalllink/alllink'

    export default {
        name: "HostChooseSealCloseForm",
        components: { MyContainer,   },
        data() {
            return {               
                that: this,
                form: {
                    chooseId: "0",
                    sealCloseReason: "",
                    goodsCompeteId:"",
                    goodsCompeteName:"",
                },   
            };
        },
        async mounted() {

        },
        computed: {
        },
        methods: {  
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(!this.form.sealCloseReason){
                    this.$alert('请填写归档原因！');
                    return;
                }

                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({
                    id,                   
                    goodsCompeteId,
                    goodsCompeteName,
                }) {
                this.form.chooseId=id;
                this.form.goodsCompeteId=goodsCompeteId;
                this.form.goodsCompeteName=goodsCompeteName;
                this.form.sealCloseReason="";
            },
            async save() {             
                let saveData = { ...this.form };
                let rlt = await SealCloseChooseAsync(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('归档成功！');    
                }             
                return (rlt && rlt.success);
            }
        },
    };
</script>

