<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item  label="年月:">
            <el-date-picker style="width: 110px" v-model="Filter.yearMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" >
          <el-select filterable multiple v-model="Filter.nameShopList" placeholder="请选择" class="el-select-content" style="width:700px">
            <el-option key="所有" label="所有" value="所有">
            </el-option>
            <el-option 
              v-for="item in shopList"
              :key="item.shopName"
              :label="item.shopName"
              :value="item.shopName">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          
          <!-- <el-button type="danger" @click="onStartCalc">计算</el-button> -->
        </el-form-item>
      </el-form>
    </template>
      <el-col :span="8">
        <!--列表-->
            <label>已上传</label>
            <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='false'
                    :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
                    @select='selectchange' :isSelection='false'
                :tableCols='tableCols' :loading="listLoading" style="height:730px">
            </ces-table>   
        </el-col>
        <el-col :span="16">
            <label>计算状态</label>
            <ces-table ref="table2" :that='that' :isIndex='true' :isSelectColumn='false'
                    :hasexpand='false' @sortchange='sortchange2' :tableData='ZTCKeyWordList2' 
                    @select='selectchange2' :isSelection='false'
                :tableCols='tableCols2' :loading="listLoading2" style="height:730px">
            <el-table-column type="expand">
            </el-table-column>
            </ces-table>  
        </el-col>
 
    <el-dialog title="计算店铺" :visible.sync="dialogVisible" width="500px" v-dialogDrag>
      <el-row>
        <el-col :xs="24" :sm="9" :md="9" :lg="9" :xl="9">
          <!-- format="yyyyMM" value-format="yyyyMM"  -->
           <el-date-picker style="width: 100%" v-model="calcFilter.date" type="month" placeholder="选择月份"></el-date-picker>
        </el-col>
        <el-col :xs="24" :sm="9" :md="9" :lg="9" :xl="9">
          <el-select v-model="calcFilter.shopCode" placeholder="店铺" style="width: 110px">
              <el-option label="请选择店铺" value/>
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
          </el-select>
        </el-col>
         <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
           <el-button type="primary" @click="onCalc">计算分摊</el-button>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import container from "@/components/my-container/nofooter";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllListInCalc as getAllShopList} from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import {getDataUploadList, getDataCalcList,postCalcTask} from '@/api/bookkeeper/financialDetail'

const tableCols =[
      {istrue:true,prop:'nameShop',label:'店铺名', width:'220'},
      {istrue:true,prop:'yearMonth',label:'月份', width:'80'},
      {istrue:true,prop:'dataType',label:'数据类型', width:'220'},
     ];

const tableCols2 =[
      {istrue:true,prop:'nameShop',label:'店铺名', width:'220'},
      {istrue:true,prop:'yearMonth',label:'月份', width:'80'},
      {istrue:true,prop:'calcStatus',label:'计算状态', width:'80'},
      {istrue:true,prop:'errorMsg',label:'错误信息', width:'200'},
      {istrue:true,type:'button', width:'55',btnList:[{label:"计算",display:(row)=>false,handle:(that,row)=>that.onCalcHand(row)}]},
     ];

export default {
  name: "Users",
  components: { container, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
        yearMonth:null,
        timerange: [formatTime(dayjs().subtract(3, 'month'), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        isException:false,
        nameShopList:[]
      },
      calcFilter:{shopCode:null,date:null},
      pickerOptions: {
          shortcuts: [{
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()]);
            }
          },  {
            text: '最近3个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
      pager:{OrderBy:"nameShop",IsAsc:false},
      shopList:[],
      ZTCKeyWordList: [],
      ZTCKeyWordList2: [],
      tableCols:tableCols,
      tableCols2:tableCols2,
      sels: [], // 列表选中列
      listLoading: false,
      listLoading2: false,
      pageLoading: false,
      selids:[],
      dialogVisible:false
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    sortchange2(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
     //所属店铺列表
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=res1.data;
      return;
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.getList();
    },
    onStartCalc(){
      this.dialogVisible=true;
    },
   async onCalc(){
      if (!this.calcFilter.shopCode) {
         this.$message({type: 'warning',message: '请选择店铺!'});
         return;
      }
      var para={shopCode:null,date:null}
      var date = dayjs(this.calcFilter.date).startOf('month')
      para.date = formatTime(date, 'YYYY-MM-DD HH:mm:ss')
      para.shopCode=this.calcFilter.shopCode;
      await postCalcTask(para)
            .then(res=>{
                  if (res.success)
                    this.$message({type: 'success',message: '提交完成，正在后台计算...!'});
                  else
                    this.$message({type: 'success',message: '提交失败!'});
                });
    },
    async onCalcHand(row){
      if (!row.shopCode) {
         this.$message({type: 'warning',message: '店铺错误!'});
         return;
      }
      if (!row.yearMonth) {
         this.$message({type: 'warning',message: '月份错误!'});
         return;
      }
      var para={shopCode:row.shopCode,yearMonth:row.yearMonth}
      await postCalcTask(para)
            .then(res=>{
                  if (res.success) {
                    this.$message({type: 'success',message: '提交完成，正在后台计算...!'});
                    this.getList();
                    }
                  else
                    this.$message({type: 'success',message: '提交失败!'});
                });
    },
    async onExport(){
    },
    async getList(){
      const para = {...this.Filter};
      if (this.Filter.date) {
        para.date = this.Filter.date
      }
      para.nameShop = para.nameShopList//JSON.stringify(para.nameShopList)
     
      var date = dayjs(this.Filter.date).startOf('month')
      para.date = formatTime(date, 'YYYY-MM-DD HH:mm:ss')
      const params = {    ...para,    };
      console.log(para)

      this.listLoading = true;
      const res = await getDataUploadList(params);
      this.listLoading = false;

     if(null == res)
     return

      //console.log(res.data.list)

      this.total = res.data.total
      this.ZTCKeyWordList = res.data.list;

      this.listLoading2 = true;
      const res2 = await getDataCalcList(params);
      this.listLoading2 = false;

       if(null == res2)
     return

      console.log(res2.data.list)

      this.total2 = res2.data.total
      this.ZTCKeyWordList2 = res2.data.list;

    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    selectchange2:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
  
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>