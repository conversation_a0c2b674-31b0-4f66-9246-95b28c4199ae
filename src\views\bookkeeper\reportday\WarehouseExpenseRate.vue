<template>
  <el-container style="height:100%;">
    <container v-loading="pageLoading" style="width:50%;">
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='list' :tableCols='tableCols'
        :isSelection="false" @select="selectchange" :tableHandles='tableHandles' :isSelectColumn="false"
        :loading="listLoading">
      </ces-table>
    </container>
    <!-- 编辑仓储物流费占比 -->
    <el-dialog title="编辑仓储物流费占比" :visible.sync="addFormVisible1" width="500px" v-dialogDrag>
      <el-form label-width="140px" >
        <el-form-item label="店铺:" >
          <el-input v-model="editForm.shopName" style="width: 200px; " disabled></el-input>
        </el-form-item>
        <el-form-item label="仓储物流费占比:">
          <el-input-number placeholder="仓储物流费占比" style="width: 200px; " v-model="editForm.logisticsFeeRatio"  :precision="2"
            :max="100" :min="0"></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateLogisticsFeeRatio">保存</el-button>
        <el-button @click="addFormVisible1 = false">关闭</el-button>
      </span>
    </el-dialog>
  </el-container>
</template>
  
<script>
import { updateJdShopLogisticsFeeRatio, getJdShop } from "@/api/operatemanage/base/shop"
import { getUserListPage, getUser } from "@/api/admin/user"
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from '@/components/my-confirm-button'
const tableCols1 = [
  // {istrue:true,prop:'key',label:'序号', width:'60'},
  { istrue: true, prop: 'shopName', label: '店铺', width: 'auto' },
  { istrue: true, prop: 'logisticsFeeRatio', label: '仓储物流费占比(%)', width: 'auto' },
  // ,permission:'api:operatemanage:shopmanager:UpdateDirectorGroupGzzb'
  {
    istrue: true, type: 'button', width: '55', btnList:
      [{ label: "编辑", handle: (that, row) => that.onEditDirectorGroup(row) }]
  },
];
const tableHandles1 = [];
export default {
  name: 'roles',
  components: { cesTable, container, MyConfirmButton },
  props: {
    isHistory: false,
  },
  data () {
    return {
      that: this,
      filter: {
        combineCode: null,
        combineName: null,
        goodsCode: null
      },
      editForm: {},
      list: [],
      tableCols: tableCols1,
      tableHandles: tableHandles1,
      dialogVisible: false,
      sels: [],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addFormVisible1: false,
      addLoading: false,
      deleteLoading: false,
      fileList: [],
      selids: [],//选择的id
      
    }
  },
  async mounted () {
    await this.getlist();
    await this.onInitautoform();
  },
  methods: {
    //分页查询
    async getlist () {
      this.listLoading = true
      var res = await getJdShop();

      this.listLoading = false
      if (!res?.success) return
      const data = res.data;
      this.list = data
    },
    selsChange: function (sels) {
      this.sels = sels;
    },
    selectchange: function (rows, row) {
      this.selids = []; console.log(rows)
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onEditDirectorGroup (row) {
      this.addFormVisible1 = true;
      var res = null;
      this.editForm = {
        id: row.id,
        shopName: row.shopName,
        logisticsFeeRatio: row.logisticsFeeRatio
      }
    },
    async updateLogisticsFeeRatio () {
      let p = {
        id: this.editForm.id,
        shopName: this.editForm.shopName,
        logisticsFeeRatio: this.editForm.logisticsFeeRatio
      }
      const res = await updateJdShopLogisticsFeeRatio(p);
      if (res.data) {
        this.$message({ type: 'success', message: '修改成功!' });
        this.getlist();
        this.addFormVisible1 = false;
      }
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}
</style>
  