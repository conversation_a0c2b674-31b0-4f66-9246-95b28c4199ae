<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.date" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
          class="publicCss" :clearable="false" />
        <el-select v-model="ListInfo.brandNames" multiple collapse-tags clearable filterable placeholder="请选择采购员" class="publicCss">
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
        <el-select v-model="ListInfo.deptIds" multiple collapse-tags clearable filterable placeholder="请选择架构" class="publicCss"
          style="width: 300px;">
          <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.titles" multiple collapse-tags clearable filterable placeholder="请选择岗位" class="publicCss">
          <el-option v-for="item in purTitleList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" :disabled="isDeriveExport" @click="deriveExportProps">导出</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出明细</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false" :treeProp="{}"
      :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
      :isNeedExpend="false" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
      @sortchange="sortchange" @onTrendChart="trendChart">
      <template #brandName="{ row }">
        <el-button type="text" @click="openDetails(row)">
          {{ row.brandName }}
        </el-button>
      </template>
      <template
        v-for="item in summaryRowList"
        v-slot:[`${item.prop}_footer`]="{ row }"
      >
        <div
          @click="() => {
            isSummary = true
            wmsTheoryRateChart(data.list[0], warehouseList, item.label)
          }"
          style="cursor: pointer;color:red;"
        >
          {{ formatNum(data.summary[`${item.prop}_sum`]) }}
        </div>
      </template>
    </vxetablebase>

    <vxe-modal title="缺货明细" v-model="detailsVisible" :esc-closable="true" :width='1200' :height='600' marginSize='-500'>
      <sumDetails :detailsInfo="detailsInfo" v-if="detailsVisible"
        :api="'/api/inventory/PurchaseSummary/LackDetail/'" />
    </vxe-modal>

    <el-drawer :title="chatProp.theTitle + '趋势图'" :visible.sync="chatProp.chatDialog" size="80%"
      :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions" style="margin: 10px"
          @change="handleDateChange" />
        <el-select v-model="chatProp.brandName" clearable filterable placeholder="请选择采购员" class="publicCss"
          style="margin-right: 10px;" @change="conditionsMethod($event, 1)">
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
        <el-select v-model="chatProp.deptId" clearable filterable placeholder="请选择架构" class="publicCss"
          style="margin-right: 10px;width: 300px;" @change="conditionsMethod($event, 2)">
          <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="chatProp.title" clearable filterable placeholder="请选择岗位" class="publicCss"
          style="margin-right: 10px;" @change="conditionsMethod($event, 3)">
          <el-option v-for="item in purTitleList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/inventory/PurchaseSummary/Lack/'
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getOutOfStockTimesSummaryPurchaseDeptList } from '@/api/inventory/purchaseordernew'
import sumDetails from './sumDetails.vue'
import decimal from '@/utils/decimal'
import { getEmployees, getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import { pageBianMaBrand } from '@/api/inventory/warehouse'
export default {
  name: "outOfStockFrequencySum",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, sumDetails
  },
  data() {
    return {
      api,
      platformlist,
      purTitleList: [],
      summaryRowList: [],
      warehouseList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 1000,
        orderBy: '',
        isAsc: false,
        //昨天
        date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        summarys: [],
      },
      isSummary: false,
      summaryType: '',
      verify: false,
      isDeriveExport: false,
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
        theTitle: '', // 趋势图标题
        title: '',//岗位
        brandName: '',//采购员
        deptId: '',//架构
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      loading: true,
      pickerOptions,
      isExport: false,
      positionList: [],
      brandlist: [],
      purchasegrouplist: [],
      detailsVisible: false,
      detailsInfo: {
        date: null,
        brandId: null,
        brandName: null
      },
    }
  },
  async mounted() {
    await this.userMethod()
    await this.init();
    await this.getCol();
    await this.getList()
  },
  methods: {
    // 趋势图日期change
    handleDateChange(event) {
      if (!this.isSummary) {
        this.trendChart({
          ...this.chatPropOption,
          startDate: event[0],
          endDate: event[1],
        }, this.row, true);
      } else {
        this.wmsTheoryRateChart(this.row, this.warehouseList, this.summaryType, event, { brandName: this.chatProp.brandName, deptId: this.chatProp.deptId, title: this.chatProp.title });
      }
    },
    // 趋势图条件change
    async conditionsMethod(event, type) {
      let chartConditions = {
        brandName: type === 1 ? event : this.chatProp.brandName,
        deptId: type === 2 ? event : this.chatProp.deptId,
        title: type === 3 ? event : this.chatProp.title
      }
      if (!this.isSummary) {
        this.trendChart({
          ...this.chatPropOption,
          startDate: this.chatProp.chatTime[0],
          endDate: this.chatProp.chatTime[1],
        }, this.row, true, chartConditions);
      } else {
        this.wmsTheoryRateChart(this.row, this.warehouseList, this.summaryType, this.chatProp.chatTime, chartConditions);
      }
    },
    async userMethod() {
      const { data } = await getUserInfo();
      const { data: res } = await getEmployees({ ddUserId: data.ddUserId });
      if (res && res.length > 0) {
        this.verify = true
      }
    },
    //行内趋势图
    async trendChart(option, row, isDrawer, chartConditions) {
      this.isSummary = false
      if (!isDrawer) {
        option.endDate = this.ListInfo.endDate || option.endDate;
        option.startDate = dayjs(option.endDate).subtract(7, 'day').format('YYYY-MM-DD');
        this.chatProp.brandName = row.brandName
        this.chatProp.deptId = row.deptId
        this.chatProp.title = row.title
        option.condition = { ...this.chatProp, ...option.condition }
      } else {
        option.condition = { ...option.condition, ...chartConditions }
      }

      await this.processChartData(option, row, isDrawer, '');
    },
    // 汇总趋势图
    async wmsTheoryRateChart(row, prop, type, e, chartConditions) {
      if (!chartConditions) {
        this.chatProp.brandName = this.ListInfo.brandName
        this.chatProp.deptId = this.ListInfo.deptId
        this.chatProp.title = this.ListInfo.title
      }
      this.isSummary = true
      this.summaryType = type
      const cols = [];
      this.tableCols.forEach(item => {
        if (item.cols && item.cols.length > 0) {
          item.cols.forEach(item1 => {
            cols.push(item1);
          });
        } else {
          cols.push(item);
        }
      });
      const dateCol = cols.find(a => a.isSeriesDate);
      const keys = cols.filter(a => a.isSeriesKey).map(a => a.prop);
      const fields = cols.filter(a => a.isSeriesField)
        .sort((a, b) => a.columnIndex - b.columnIndex)
        .map(a => {
          let label = a.label;
          if (a.mergeName) {
            label = `${a.mergeName} - ${label}`;
          }
          return {
            field: a.prop,
            label: label,
            isRate: a.format ? a.format.endsWith('%') : false
          };
        });
      const filter = {
        logic: 'And',
        filters: keys.map(p => ({
          field: p,
          operator: 'Eq',
          value: row[p]
        }))
      };
      const option = {
        key: keys,
        dateField: dateCol.prop,
        fields: fields,
        filter: filter,
        date: new Date(row[dateCol.prop]),
        condition: {
          ...this.ListInfo,
          ...chartConditions,
          startDate: dayjs(this.ListInfo.date).startOf('month').format("YYYY-MM-DD")
        }
      };
      option.startDate = e ? e[0] : option.startDate
      option.endDate = e ? e[1] : option.endDate
      await this.processChartData(option, row, e ? false : true, type);
    },
    async processChartData(option, row, isDrawer, type) {
      var endDate = null;
      var startDate = null;
      option.filter.filters = option.filter.filters.filter((item) => item.field !== 'brandId' && item.field !== 'brandName' && item.field !== 'deptId' && item.field !== 'deptName');
      option.key = option.key.filter((item) => item !== 'brandId' && item !== 'brandName' && item !== 'deptId' && item !== 'deptName');
      this.row = JSON.parse(JSON.stringify(row))
      const res = JSON.parse(JSON.stringify(option))
      if (res.startDate && res.endDate) {
        startDate = res.startDate;
        endDate = res.endDate;
      } else {
        endDate = res.date;
        startDate = new Date(res.date);
        startDate = dayjs(startDate).startOf('month').format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      res.filter.filters = res.filter.filters.filter(item => item.field !== res.dateField);
      res.filter.filters.push({
        field: res.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      res.filter.filters.push({
        field: res.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });
      res.startDate = startDate;
      res.endDate = endDate;
      res.condition.type = row.isRoot ? 0 : 1;
      this.chatProp.chatTime = [startDate, endDate];
      this.chatProp.chatLoading = true;
      try {
        const { data, success } = await request.post(`${this.api}GetTrendChart`, res);
        if (success) {
          let deptName = this.purchasegrouplist.find(item => item.value === res.condition.deptId)?.name
          if (res.condition.brandName || deptName) {
            this.chatProp.theTitle = `${deptName ? deptName : ''} ${res.condition.brandName ? res.condition.brandName : row.brandName ? row.brandName : ''}-`;
          } else {
            this.chatProp.theTitle = `${type}-${'汇总'}-`
          }
          data.title = null;
          this.chatProp.data = data;
        }
        this.chatProp.chatDialog = true;
        this.chatPropOption = option;
      } catch (error) {
        console.error('数据处理失败:', error);
      } finally {
        this.chatProp.chatLoading = false;
      }
    },
    openDetails(row) {
      if(!this.checkPermission('outOfStockFrequencySum:brandName')){
        return
      }
      this.detailsInfo = {
        isLack: true,
        startDate: dayjs(row.date).startOf('month').format("YYYY-MM-DD"),
        endDate: dayjs(row.date).format("YYYY-MM-DD"),
        brandId: row.brandId,
        brandName: row.brandName
      }
      this.detailsVisible = true
    },
    async init() {
      // var res2 = await getAllProBrand();
      // this.brandlist1 = res2.data;
      // this.brandlist = res2.data.filter(item => item.leaveDate == null).map(item => {
      //   return { value: item.key, label: item.value };
      // });
      let res2 = await pageBianMaBrand({ pageSize: 999999, currentPage: 1, OrderBy: "", IsAsc: true, LeaveDate: '在职', enabled: true });
      this.brandlist = res2.data.list.filter(item => item.purDept != null).map(item => { return { value: item.id, label: item.brandName }; });

      var resPosition = await getBianManPositionListV2();
      this.positionList = resPosition?.data;
      //采购组
      let { data: deptList, success } = await getOutOfStockTimesSummaryPurchaseDeptList();
      if (success) {
        this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name, name: item.dept_name }; });
      };
      //岗位
      const res = await request.post(`/api/inventory/PurchaseSummary/Goods/GetBrandGoodsCountStatTitles`);
      if (res?.success) {
        this.purTitleList = res?.data ?? [];
      }
    },
    formatNum(val) {
      return val !== null && val !== undefined ? val.toLocaleString() : '-'
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      const params = {
        ...this.ListInfo,
        startDate: dayjs(this.ListInfo.date).startOf('month').format("YYYY-MM-DD"),
        endDate: dayjs(this.ListInfo.date).endOf('month').format("YYYY-MM-DD"),
        isLack: true,
      }
      delete params.date
      delete params.orderBy
      await request.post(`/api/inventory/PurchaseSummary/LackDetail/ExportData`, params, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async deriveExportProps() {
      this.isDeriveExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isDeriveExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        let b = data?.filter(item => item.summaryType == 'Sum')
        this.summaryRowList = b?.map(item1 => {
          return {
            prop: item1.prop,
            label: item1.label
          }
        })
        this.warehouseList = b?.map(item1 => item1.prop)
        data.forEach(item => {
          // if (item.prop == 'brandName') {
          //   item.type = 'click'
          //   item.handle = (that, row) => that.openDetails(row)
          // }
          if (item.prop == 'goodsApplyRate') {
            item.formatter = (row) => {
              return row.goodsApplyRate !== null ? decimal(row.goodsApplyRate, 100, 2, '*') + '%' : ''
            }
          }
        })
        this.tableCols = data;
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
          this.data.list.forEach(item => {
            item.verify = this.verify
          })
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
