<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.GroupNameList"   placeholder="分组" clearable filterable multiple :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getgroupinquirsstatisticsList" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getJingDongGroup,
    getJingDongGroupEfficiencyPageList, getJingDongGroupEfficiencyChat, exportJingDongGroupEfficiencyList
} from '@/api/customerservice/jingdonginquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名', width: '200', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.groupclick(row, column, cell), formatter: (row) => row.groupname },
    { istrue: true, prop: 'visitTotalCount', label: '咨询量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receptionCount', label: '接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '售前接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderers', label: '促成下单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'outers', label: '促成出库人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'orderSalesvol', label: '促成下单商品金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'outSalesvol', label: '促成出库商品金额', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsRate', label: '咨询->出库转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate??0).toFixed(2) + "%" },
    { istrue: true, prop: 'orderConversionRate', label: '咨询->下单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.orderConversionRate || 0).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '平均响应时间', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'responseRate', label: '30s应答率', width: '80', sortable: 'custom', formatter: (row) => (row.responseRate??0).toFixed(2) + "%" },
  { istrue: true, prop: 'outTimes', label: '出勤人次', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'veryGoodCount', label: '非常满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'goodCount', label: '满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'normalCount', label: '一般', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'badCount', label: '不满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'veryBadCount', label: '非常不满意', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate || 0).toFixed(2) + "%" },
    { istrue: true, prop: 'unsatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.unsatisfactionRate || 0).toFixed(2) + "%" },
    { istrue: true, prop: 'invitationEvaluationCount', label: '邀评数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'invitationEvaluationRate', label: '邀评率', width: '80', sortable: 'custom', formatter: (row) => (row.invitationEvaluationRate || 0).toFixed(2) + "%" },

    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
            },
            shopList: [],
            userList: [],
            filterGroupList: [],
            groupList: [],
            groupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getJingDongGroup();
        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        await this.getJingDongGroup();
    },
    methods: {
        async getJingDongGroup() {
            let groups = await getJingDongGroup({ groupType: 0 ,isleavegroup:this.isleavegroup });
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                this.filterGroupList=groups.data;
            }
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getgroupinquirsstatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getgroupinquirsstatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getJingDongGroupEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.groupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {
            let params = this.getParam();
            params.groupName = row.groupName;
            const res = await getJingDongGroupEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            });
            this.dialogMapVisible.visible = true;
        },
        groupclick(row) {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            window.showlist34jd(row.groupName, this.filter.startDate, this.filter.endDate)
            window.showtab34jd()
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportJingDongGroupEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '京东组效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
