<template>
    <container v-loading="listLoading">
        <!--列表-->
        <div>

        </div>

        <div style="height: 600px;overflow-y: auto;">
            <div :style="showgrid ? 'display:none;' : ''" style="display: flex; flex-wrap: wrap">
                <div v-for="obj in list" el-col span="8" :style="obj.issel ? { border: '2px solid #409EFF', color: red }:{}" style=" margin-left: 7px; margin-top: 7px;" v-bind:key="obj.proCode">
                    <!-- <el-badge :value="'未过'" class="item" style="margin-top: 10px; margin-right: 0px;" @click="badge(obj)"> -->
                    <el-card
                        style="height: 580px; width: 200px; position: relative; "
                        body-style="padding: 0px">
                    <!-- <div v-if="obj.isPrint == false || obj.isPrint == null" class="iconclass_left"
                            style="background-color: #ecebea;">
                            <el-button type="text" icon="el-icon-price-tag" @click="clickPrint(obj)">打单</el-button>
                        </div>
                        <div v-else-if="obj.isPrint == true" class="iconclass_left" style="background-color: darkseagreen;">
                            <span>已打单</span>
                        </div>
                        <div v-if="obj.status == 0" class="iconclass"
                            :style="obj.status == 0 ? { backgroundColor: '#ecebea' } : obj.status == 1 ? { backgroundColor: '#67C23A' } : obj.status == 2 || obj.status == 3 ? { backgroundColor: '#F56C6C' } : { backgroundColor: '#ecebea' }">
                            <el-dropdown @command="handleCommand($event, obj)" style="padding: 3px;">
                                <span class="el-dropdown-link whitecolor">
                                    {{ obj.status == 0 ? '操作' : obj.status == 1 ? '通过' : obj.status == 2 ? '驳回' : obj.status ==
                                        3 ? '退回' : '选择' }}<i class="el-icon-arrow-down el-icon--right"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="2" @click="ispasstext = '未通过'">驳回</el-dropdown-item>
                                    <el-dropdown-item command="3" @click="ispasstext = '退回'">退回</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                        <div v-if="obj.status == 2 || obj.status == 3" class="iconclass"
                            :style="obj.status == 0 ? { backgroundColor: '#ecebea' } : obj.status == 1 ? { backgroundColor: '#67C23A' } : obj.status == 2 || obj.status == 3 ? { backgroundColor: '#F56C6C' } : { backgroundColor: '#ecebea' }">
                            <el-tooltip class="item" effect="dark" :content="obj.remark" placement="top-start">
                                <span class="el-dropdown-link whitecolor" style="padding: 3px; font-size: 15px;">
                                    {{ obj.status == 0 ? '操作' : obj.status == 1 ? '通过' : obj.status == 2 ? '驳回' : obj.status ==
                                        3 ? '退回' : '选择' }}
                                </span>
                            </el-tooltip>
                        </div> -->
                        <div style="height: 440px; width: 200px;position: absolute; top: 0; left: 0; font-size: 12px;">
                            <div style="height: 200px; width: 200px;  position: relative;" >
                                <img v-if="obj.imgPath" :src="obj.imgPath" @click="onshowMv(obj)"  :data-endTime="obj.onTime"
                                    class="sacimg" :mode="'heightFix'" />
                                <img v-else src="https://img.alicdn.com/tfs/TB1614dxqL7gK0jSZFBXXXZZpXa-800-800.png"
                                    style="height: 200px; width: 100%" :data-endTime="obj.onTime"
                                    class="image" />
                                <span class="image-text">{{ obj.wms_co_name }}</span>
                            </div>
                            <div @click.stop="selCard(obj, $event)"
                                style="padding: 5px; height: 240px; z-index: 66; margin-top: -5px; width: 100%;background-color: white;">
                                <div style="width: 210px">
                                    <div style="font-size: 13px; height: 360px; overflow-y: auto;">
                                        <table :borderColor="obj.isOutStock ? 'red' : '#000'" :cellspacing="0" :border="1">
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">任务编号</td>
                                                <td>
                                                    <div @click="onshowLogs(obj)" style="width: 120px;color:#409EFF">
                                                    <span class="blue"> {{ obj.warehousNo }} </span>
                                                    <span> {{ obj.createdUserName }} </span>
                                                        <!-- &nbsp{{ obj.createdUserName }} -->
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">上传人</td>
                                                <td>{{ obj.createdUserName }}</td>
                                            </tr> -->
                                            <tr style="height: 20px;">
                                                <td style="width: 60px;" class="overtext">上传时间</td>
                                                <td style="width: 60px;" class="overtext">
                                                    <div>{{ obj.createdTime | mynum }}</div>
                                                </td>
                                            </tr>
                                            <!-- //////////////////// -->
                                            <tr style="height: 20px;">
                                                <td style="width: 60px;" class="overtext">件数</td>
                                                <td style="width: 60px;" class="overtext">
                                                    <div>{{ obj.count }}</div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px;">
                                                <td style="width: 60px;" class="overtext">运费</td>
                                                <!-- <td style="width: 60px;" class="overtext"><div>{{ obj.freightAmont }}</div></td> -->
                                                <td>
                                                    <div >
                                                        <span style="width: 30px;">{{ obj.freightAmont }}</span>
                                                        <span style="float: right;">{{ obj.payment }}</span>
                                                    </div>

                                                </td>
                                            </tr>
                                            <tr style="height: 20px;">
                                                <td style="width: 60px;" class="overtext">货品名称</td>
                                                <td style="width: 60px;" class="overtext">
                                                    <div v-show="obj.goodistrue">
                                                        <el-input v-model.trim="obj.goodsName" maxlength="50"
                                                            style="width: 110px" placeholder="请输入货品名称" clearable />
                                                        <div style="display: flex; flex-direction: row;">
                                                            <el-button type="primary" size="mini"
                                                                @click="changeblur(obj)">保存</el-button>
                                                            <el-button size="mini" style="margin-left: 3px;"
                                                                @click="cancelchange(obj)">取消</el-button>
                                                        </div>
                                                    </div>
                                                    <el-tooltip class="item" effect="dark" :content="obj.goodsName"
                                                        placement="top" v-show="!obj.goodistrue">
                                                        <div  style="width: 110px;" class="hang"
                                                            :style="obj.goodsName ? {} : { color: '#409EFF' }">
                                                            {{ obj.goodsName ? obj.goodsName : '输入名称' }}
                                                        </div>
                                                    </el-tooltip>
                                                </td>
                                            </tr>

                                            <tr style="height: 20px; width: 200px;">
                                                <div v-if="obj.buyNostr" class="blue" @click="searchPurchase(obj.buyNostr)">采购单号
                                                </div>
                                                <td v-else style="width: 60px;" class="overtext">采购单号</td>
                                                <!-- @click="selectck(obj)" -->
                                                <td :class="{ blue: obj.isBindBuyNo == false }">
                                                    <!-- <div style="display: flex;flex-wrap: wrap; overflow-x: auto; width: 80px;">{{ obj.buyNo ? obj.buyNo : '请选择采购单号' }}</div> -->
                                                    <!-- {{ addForm.buyNo }} -->
                                                    <div style="width: 120px;" v-show="obj.istrue">
                                                        <el-select style="width: 100px;" :max="10" :maxlength="10"
                                                            v-model="obj.buyNo" multiple filterable remote reserve-keywor
                                                            placeholder="采购单号" :clearable="false" :remote-method="remoteMethod"
                                                            :loading="searchloading" @change="selectchange(obj)"
                                                            ref="refselect">
                                                            <el-option v-for="(item, i) in options" :key="i" :label="item.value"
                                                                :value="item.value">
                                                            </el-option>
                                                        </el-select>
                                                        <div style="display: flex; flex-direction: row;">
                                                            <el-button type="primary" size="mini" v-show="obj.istrue"
                                                                @click="selectblur(obj.buyNostr)">保存</el-button>
                                                            <el-button size="mini" style="margin-left: 3px;" v-show="obj.istrue"
                                                                @click="cancelsel(obj.buyNostr)">取消</el-button>
                                                        </div>


                                                    </div>

                                                    <!-- <div style="width: 120px;" v-show="!obj.istrue" @click="selwarehousNo(obj)">{{ obj.buyNo ? obj.buyNo : '请选择采购单号' }}</div> -->
                                                    <div style="width: 120px;" v-show="!obj.istrue" >
                                                        <el-tooltip class="item" effect="dark" :content="obj.buyNostr"
                                                            placement="right">
                                                            <div style="width: 110px;" class="overtext"
                                                                :style="obj.buyNostr ? {} : { color: '#409EFF' }">
                                                                {{ obj.buyNostr ? obj.buyNostr : '采购单号' }} <span
                                                                    color="#409EFF">{{ obj.modifiedUserName }}</span>
                                                            </div>
                                                        </el-tooltip>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 180px;">
                                                <td style="width: 60px;" class="overtext">物流单号</td>
                                                <td>
                                                    <div style="width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                        <span v-if="obj.logisticsImg" style="color: #409EFF;" @click="viewThumbnail(obj)">{{ obj.logisticsNo }}</span>
                                                        <span v-else>{{ obj.logisticsNo }}</span>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">绑单时间</td>
                                                <td>{{ obj.bindBuyNoDate | mynum }}</td>
                                            </tr>


                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">托运费</td>
                                                <td>
                                                    <div style="width: 120px;color: blue;cursor: pointer">
                                                        <el-input-number @change="changeUp" @click.native.stop @focus.stop="clickstop(obj)" v-model="obj.haulage" :min="0" :max="999999" :controls="false"
                                                            :precision="4" style="width: 110px" placeholder="请输入"></el-input-number>
                                                       
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">送货费</td>
                                                <td>
                                                    <div style="width: 120px;color: blue;cursor: pointer">
                                                        <!-- <el-input v-model.trim="obj.deliveryFee" @change="changeUp" maxlength="50"
                                                        style="width: 110px" placeholder="请输入" clearable /> -->

                                                        <el-input-number @change="changeUp" @click.native.stop @focus.stop="clickstop(obj)" v-model="obj.deliveryFee" :min="0" :max="999999" :controls="false"
                                                            :precision="4" style="width: 110px" placeholder="请输入"></el-input-number>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">提货费</td>
                                                <td>
                                                    <div style="width: 120px;color: blue;cursor: pointer">
                                                        <!-- <el-input v-model.trim="obj.pickUpFee" @change="changeUp" maxlength="50"
                                                        style="width: 110px" placeholder="请输入" clearable /> -->
                                                        <el-input-number @change="changeUp" @click.native.stop @focus.stop="clickstop(obj)" v-model="obj.pickUpFee" :min="0" :max="999999" :controls="false"
                                                        :precision="4" style="width: 110px" placeholder="请输入"></el-input-number>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">货拉拉</td>
                                                <td>
                                                    <div style="width: 120px;color: blue;cursor: pointer">
                                                        <!-- <el-input v-model.trim="obj.huoLaLa" @change="changeUp" maxlength="50"
                                                        style="width: 110px" placeholder="请输入" clearable /> -->
                                                        <el-input-number @change="changeUp" @focus.stop="clickstop(obj)" v-model="obj.huoLaLa" :min="0" :max="999999" :controls="false"
                                                        :precision="4" style="width: 110px" placeholder="请输入"></el-input-number>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">装车费</td>
                                                <td>
                                                    <div style="width: 120px;color: blue;cursor: pointer">
                                                        <!-- <el-input v-model.trim="obj.loadingFee" maxlength="50"
                                                        style="width: 110px" @change="changeUp" placeholder="请输入" clearable /> -->
                                                        <el-input-number @change="changeUp" @click.native.stop @focus.stop="clickstop(obj)" v-model="obj.loadingFee" :min="0" :max="999999" :controls="false"
                                                        :precision="4" style="width: 110px" placeholder="请输入"></el-input-number>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="height: 20px; width: 200px;">
                                                <td style="width: 60px;" class="overtext">合计费用</td>
                                                <td>{{allAddSum(obj.loadingFee, obj.huoLaLa, obj.pickUpFee,
                                                    obj.deliveryFee, obj.haulage
                                                )}}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            
                        </div>
                    </el-card>
                    <!-- </el-badge> -->
                </div>
            </div>
        </div>

     

        <el-dialog title="输入运费" :visible.sync="freightAmontistrue" width="40%">
            运费:
            <el-input-number :controls="false" v-model="freight.freightAmont" :precision="2" :step="0.1" :min="0"
                :max="99999"></el-input-number>

            <span slot="footer" class="dialog-footer">
                <el-button @click="freightAmontistrue = false">取 消</el-button>
                <el-button type="primary" :loading="freightAmontistloading" @click="savefreight">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="原因" :visible.sync="ispasdilog" width="30%">
            <el-input v-model="ispassmsg" maxlength="200" placeholder="请输入原因"></el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="ispasdilog = false">取 消</el-button>
                <el-button type="primary" @click="savemoney(2)">确 定</el-button>
            </span>
        </el-dialog>


        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getPages" />
        </template>

        <el-dialog title="详情信息" :visible.sync="dialogVisible" width="70%" v-dialogDrag append-to-body>
            <warehousingordervidedetail ref="warehousingordervidedetail" v-if="dialogVisible" style="height:750px"></warehousingordervidedetail>
        </el-dialog>

    
        <!-- <div class="imageModalPos" v-if="viewThumbnailVisiable">
            <viewThumbnailCarousel :index="currentInfo.index" :maxIndex="currentInfo.maxIndex" v-if="imgList" :imgList="imgList"
                    :imageInfo="imageInfo" :domList="domList" :closeModal="closeModal" ref="imageModelRef"
                    :checkList="currentInfo.groupTypes"></viewThumbnailCarousel>
        </div> -->
    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { pageWarehousingOrderVideo, getListBuyNo, bindWarehousingBuyNo, saveWarehousingAmont, exportWarehousingOrderVideoListAsync } from "@/api/inventory/warehousingordervide"
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import warehousingordervidedetail from './warehousingordervidedetail'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { getTokenKeyValue } from '@/api/admin/auth'
// import purchasequalitylog from "./purchasequalitylog.vue";
import checkPermission from '@/utils/permission'
// import purchasequalityimages from "./purchasequalityimages.vue";
// import viewThumbnailCarousel from "./viewThumbnailCarousel.vue";
import Decimal from 'decimal.js';

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: "YunHanAdminWarehousingordervide",
    components: { container, MyConfirmButton, vxetablebase, videoplayer, ElImageViewer, warehousingordervidedetail },
    props: {
        filter: {}
    },
    filters: {
        mynum(val) {
            if (val)
                return formatTime(val, "YYYY-MM-DD HH:mm")
        }
    },

    data() {
        return {
            that: this,
            // filter: {
            //     startDate: null,
            //     endDate: null,
            //     createdUserName: null,
            //     buyNo: null,
            //     warehousNo: null,
            //     wms_co_id: null,
            //     isBrand_w: null,
            //     isBindBuyNo: null,
            //     timerange: [startTime, endTime],
            // },
            isselList: [],
            filterLog: {
                warehousNo: null,
                createdUserName: null
            },
            money: {
                shootAmont: '',
                qualityAmont: '',
                warehouseAmont: ''
            },
            freight: {
                freightAmont: null,
                warehousNo: null,
                goodsName: null
            },
            selshow: false,
            moneydialogVisible: false,
            ispasdilog: false,
            list: [],
            pager: { OrderBy: "yearMonthDay", IsAsc: false },
            total: 0,
            sels: [],
            imgList: [],
            options: [],
            newWareHouseList: [],
            videoUrl: '', //视频地址
            fileType: null, //文件类型,false：视频,true：图片
            addForm: {
                warehousNo: null,
                buyNo: null,
                status: null
            },
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            ispasstext: '状态',
            ispassmsg: '',
            loading: false,
            drawer: false,
            showgrid: false,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            dialogpurlogVisible: false,
            dialogpurimagesVisible: false,
            videoplayerReload: false,
            showGoodsImage: false,
            searchloading: false,
            onFinishLoading: false,
            freightAmontistrue: false,
            freightAmontistloading: false,
            viewThumbnailVisiable: false,
            domList: [],
            currentInfo: {
                currentPage: 1,
                pageSize: 50,
                groupTypes: [4],
                index: 0,
                maxIndex: 0
            },
        };
    },

    async mounted() {
        //await this.onSearch();
    },

    methods: {
        clickstop(row) {
            if(row.issel == true){
                row.issel = false; 
            }else{
                row.issel = true; 
            }
        },
        allAddSum(a,b,c,d,e) {
            let aa = a ? new Decimal(a) : new Decimal(0);
            let bb = b ? new Decimal(b) : new Decimal(0);
            let cc = c ? new Decimal(c) : new Decimal(0);
            let dd = d ? new Decimal(d) : new Decimal(0);
            let ee = e ? new Decimal(e) : new Decimal(0);
            return aa.add(bb).add(cc).add(dd).add(ee).toNumber();
        },
        changeUp() {
            this.$forceUpdate();
        },
        clearList(){
            this.isselList = [];
            this.list.map((item) => {
                item.issel = false;
                item.loadingFee = undefined;
                item.deliveryFee = undefined;
                item.pickUpFee = undefined;
                item.haulage = undefined;
                item.huoLaLa = undefined;
            })
            setTimeout(() => {
                this.$forceUpdate();
            },200)
            console.log("调用最子集方法1", this.list)
            console.log("调用最子集方法2", this.isselList)

        },
        selList() {
            let _this = this;
            let newarr = _this.uniqueById(_this.isselList)
            return newarr;
        },
        uniqueById(arr) {
            return [...new Map(arr.map(item => [item.id, item])).values()];
        },
        async getPages() {
            // this.isselList = [];
            await this.getlist();

            await this.nextPage();

           
        },
        nextPage() {
            let me = this;
            setTimeout(() => {
                me.list.map((item) => {
                    me.isselList.map((item1) => {
                        if (item.id == item1.id) {
                            item.issel = !item.issel;
                        } 
                        // else {
                        //     item.issel = false;
                        // }
                    })
                })
                setTimeout(() => {
                    me.$forceUpdate()
                }, 100);
            }, 0);
            
            
            console.log(this.isselList)
        },
        selCard(row, event) {
            // this.selshow = !this.selshow
            console.log(row)
            if (!event.target.closest('.el-input-number')) {
                this.list.map((item) => {
                    if (item.id == row.id) {
                        item.issel = !item.issel;
                        if(item.issel){
                            this.isselList.push(item); 
                        }else{
                            this.isselList.splice(this.isselList.indexOf(item),1)
                        }
                    }
                })
                
                this.$forceUpdate()
            }
            
           
        },
        async onExport() {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            var exportType = false
            const params = { ... this.filter, exportType }

            if (params === false) {
                return;
            }
            var res = await exportWarehousingOrderVideoListAsync(params);
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '入库拍摄导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        handleCommand(val, row) {
            console.log('111', val)
            this.addForm.warehousNo = row.warehousNo;
            if (val == 2 || val == 3) {
                //this.ispasstext = '未通过'
                this.ispassmsg = ''
                this.ispasdilog = true;
                this.addForm.status = val
            } else if (val == 'true') {
                this.ispasstext = '通过'
                this.savemoney(2);
            }

        },
        async clickPrint(row) {
            if (row.status == 2 || row.status == 3) {
                this.$message({
                    type: 'info',
                    message: '当前任务被拒绝或驳回，禁止操作！'
                });
                return;
            }

            let params = { warehousNo: row.warehousNo, ViewType: 4 };

            this.$confirm('此操作将此任务设置为已打单, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await saveWarehousingAmont(params);
                if (res.success) {
                    await this.onSearch();
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async getmoney(row) {
            if (row.remark) {
                this.$message("当前任务已驳回，禁止操作")
                return
            }
            this.moneydialogVisible = true;
            this.addForm.warehousNo = row.warehousNo;
            this.money.shootAmont = row.shootAmont;
            this.money.qualityAmont = row.qualityAmont;
            this.money.warehouseAmont = row.warehouseAmont;
        },
        getmoneymsg() {

        },
        async savemoney(val) {
            let params = { ...this.money };
            params.warehousNo = this.addForm.warehousNo;
            params.status = this.addForm.status ?? 0;
            params.Remark = this.ispassmsg;
            // return
            if (val == 1) {
                params.ViewType = 1
            } else if (val == 2) {
                params.ViewType = 2
                if (!this.ispassmsg) {
                    this.$message("请输入原因!")
                    return
                }
            } else if (val == 3) {
                params.ViewType = 3
            }
            let res = await saveWarehousingAmont(params);
            if (res.success) {
                await this.onSearch();
                this.moneydialogVisible = false;
                this.ispasdilog = false;
            }
            // this.$message({style: 'success',title:'保存成功'});
        },
        selwarehousNo(row) {
            // 特殊处理，驳回可操作
            var hasEditPermission = checkPermission('refuseedit');
            if (row.remark && hasEditPermission == false) {
                this.$message("当前任务禁止操作！")
                return
            }
            this.addForm.warehousNo = row.warehousNo;
            this.addForm.buyNo = row.buyNo ? row.buyNo.join(",") : '';
            this.selshow = true;
            this.list.map((item) => {
                if (item === row) {
                    item.istrue = true;
                } else {
                    item.istrue = false;
                }
            })

        },
        goodwarehousNo(row) {
            if (row.remark) {
                this.$message("当前任务已驳回，禁止操作")
                return
            }
            // this.addForm.warehousNo = row.warehousNo;
            // this.addForm.buyNo = row.buyNo?row.buyNo.join(","):'';
            this.selshow = true;
            this.list.map((item) => {
                if (item === row) {
                    item.goodistrue = true;
                } else {
                    item.goodistrue = false;
                }
            })

        },
        clickfreightAmont(row) {
            // 特殊处理，驳回可操作
            var hasEditPermission = checkPermission('refuseedit');
            if (!hasEditPermission) return;
            this.freightAmontistrue = true;
            this.freight.freightAmont = row.freightAmont;
            this.freight.warehousNo = row.warehousNo;
            this.freight.goodsName = row.goodsName;
        },
        async savefreight() {
            let params = { ...this.freight, ViewType: 3 };

            this.$confirm('此操作将会修改运费, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.freightAmontistloading = true;
                let res = await saveWarehousingAmont(params);
                if (res.success) {
                    await this.onSearch();
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
                this.freightAmontistrue = false;
                this.freightAmontistloading = false;
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        selectchange(row) {
            if (row.buyNo.length > 9) {
                this.list.map((item) => {
                    if (item == row) {
                        item.buyNo.splice(9, 1);
                    }
                })
                this.$message("采购单号选择过多！")
                return
            }
            this.addForm.buyNo = row.buyNo.join(",");

        },
        // gicallback(data){
        //     this.filter.BuyNo = data.join(",");
        //     this.drawer = false;
        // },
        async selectblur(row) {
            this.list.map((item) => {
                if (item === row) {
                    item.istrue = true;
                } else {
                    item.istrue = false;
                }
            })
            await this.onFinish();
            await this.onSearch();
        },
        cancelsel(row) {
            this.list.map((item) => {
                if (item === row) {
                    item.istrue = true;
                } else {
                    item.istrue = false;
                }
            })
        },
        async changeblur(row) {
            this.addForm.warehousNo = row.warehousNo;
            this.money.goodsName = row.goodsName;
            await this.savemoney(3);
            // await this.onSearch();
        },
        cancelchange(row) {
            this.list.map((item) => {
                if (item.warehousNo === row.warehousNo) {
                    item.goodistrue = false;
                }
            })
        },
        ongoodFinish() {

        },
        async onSearch() {
            await this.$refs.pager.setPage(1)
            await this.getlist();
            await this.nextPage();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageWarehousingOrderVideo(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;

            // this.money.shootAmont = data.shootAmont;
            // this.money.qualityAmont = data.qualityAmont;
            // this.money.warehouseAmont = data.warehouseAmont;
            // console.log("111111",)

            data.map((item) => {
                item.istrue = false;
                item.goodistrue = false;
                item.buyNostr = item.buyNo;
                item.buyNo = item.buyNo ? item.buyNo.split(',') : [];
                item.imgPath = this.matchUrl(item.imgPath)
            })
            this.list = data
        },
        matchUrl(value) {
          let res = JSON.parse(JSON.stringify(value));
          if (!res) return res;
          res = res.replace(/(\.\w+)$/, '_300x300$1');
          return res;
        },
        // 采购单号远程搜索
        async remoteMethod(query) {
            if (query !== '') {
                this.searchloading == true
                setTimeout(async () => {
                    this.options = [];
                    const res = await getListBuyNo({ currentPage: 1, pageSize: 50, buyNo: query })
                    this.searchloading = false
                    res?.data?.list?.forEach(f => {
                        this.options.push({ value: f.buyNo, label: f.buyNo })
                    });
                }, 200)
            }
            else {
                this.options = []
            }
        },
        async onFinish() {
            this.onFinishLoading = true;
            this.selshow = false;

            // return
            console.log("this.addForm", this.addForm)
            const para = _.cloneDeep(this.addForm);
            var res = await bindWarehousingBuyNo(para);
            this.onFinishLoading = false;
            this.drawer = false;
            if (res?.success) {
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
                await this.onSearch();
            } else {
                this.$message({
                    type: 'info',
                    message: '操作失败，请检查数据！！！'
                });
            }
        },
        async selectck(row) {
            this.drawer = true;
            this.addForm.warehousNo = row.warehousNo;
        },
        async onshowMv(row) {
            this.dialogVisible = true;
            this.$nextTick(async () => {
                await this.$refs.warehousingordervidedetail.onSearch(row.warehousNo)
            });
        },
        async onshowLogs(row) {
            this.filterLog.warehousNo = row.warehousNo;
            this.filterLog.createdUserName = null;
            this.dialogpurlogVisible = true;
            this.$nextTick(async () => {
                await this.$refs.purchasequalitylog.onSearch()
            });
        },
        async closeVideoPlyer() {
            let _this = this;
            _this.dialogVisible = false;
        },
        async searchPurchase(code) {
            if (code)
                this.$router.push({ path: '/inventory/purchaseindex', query: { buyNo: code } })
        },
        async showRmPic(row) {
            if (row.buyNo == null || !row.buyNo || row.buyNo.length <= 0) {
                this.$message.warning("未绑定采购单号，请绑定采购单号并完成直接操作！");
                return;
            }
            this.dialogpurimagesVisible = true;
            var params = { buyNo: row.buyNo.join(), warehousNo: row.warehousNo }
            this.$nextTick(async () => {
                await this.$refs.purchasequalityimages.onSearch(params);
            })
        },
        viewThumbnail(row){
            if (!row.logisticsImg)
                return;
            this.imgList = [];
            this.imgList.push(row.logisticsImg);
            this.viewThumbnailVisiable = true;
            
        },
        closeModal() {
            this.viewThumbnailVisiable = false
            this.$refs.imageModelRef.clearCss()
        },
    },
};
</script>

<style lang="scss" scoped>
.blue {
    color: blue;
    cursor: pointer;
}

.sacimg {
    height: 250px;
    width: 200px;
    position: absolute;
    clip: rect(0, 200px, 180px, 0);
    transform: scale(1.2, 1.2);
}

.flexrow {
    display: flex;
    flex-direction: row;
}

.overtext {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: normal;
}

.bordercss {
    border-left: 1px solid #000;
}

.textstyele {
    width: 60px;
    height: 40px;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #000;
}

::v-deep .el-badge__content.is-fixed {
    top: 5px;
    right: 30px;
}

.iconclass {
    border-radius: 15px;
    border-color: 1px solid white;
    color: white;
    // background-color: #ecebea;
    padding: 3px;
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 99;
}

.iconclass_left {
    border-radius: 15px;
    border-color: 1px solid white;
    color: white;
    // background-color: #ecebea;
    padding: 3px;
    position: absolute;
    top: 5px;
    left: 5px;
    z-index: 99;
}

.hang {
    overflow: hidden;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.image-container {
    position: relative;
}

.image-text {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    text-align: right;
    z-index: 1;
    //max-width: 80%; /* 控制文本宽度，根据实际情况调整 */
}

.image-text::after {
    content: "";
    position: absolute;
    bottom: -15px;
    right: 0;
    border-top: 15px solid rgba(0, 0, 0, 0.5);
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    z-index: 0;
}

.imageModalPos {
    width: 100vw;
    height: 100vh;
    top: 0px;
    left: 0px;
    z-index: 999;
    overflow: hidden;
    position: fixed;
}
</style>
