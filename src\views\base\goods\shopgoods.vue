<template>
  <el-container style="height:100%;">
      <my-container v-loading="pageLoading" style="width:99%;">
        <template #header>
        </template>
        <el-tabs v-model="activeName" style="height:94%;">
          <el-tab-pane label="最新数据" name="tabShopGoods" style="height:100%;">
              <tabShopGoods :filter="filter" ref="tabShopGoods"></tabShopGoods>
          </el-tab-pane>
          <el-tab-pane label="历史数据" name="tabShopGoodsHistory" style="height:100%;">
              <tabShopGoods :filter="filter" ref="tabShopGoodsHistory" :isHistory="true"></tabShopGoods>
          </el-tab-pane>         
        </el-tabs>         
      </my-container>
  </el-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import tabShopGoods from "@/views/base/goods/shopgoodstemplate";

export default {
  name: 'Roles',
  components: { MyContainer, tabShopGoods},
  data() {
    return {
      activeName:"tabShopGoods",
      that:this,
      filter: {
      },
      pageLoading: false,
    }
  },
  async mounted() {
  },
  methods: {
    //总查询
    async Query(){
      switch(this.activeName){
        case "tabShopGoods":
          this.$refs.tabShopGoods.onSearch();
          break;
      }
    },   
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
