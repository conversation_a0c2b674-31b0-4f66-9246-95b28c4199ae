<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button-group>

        <!-- <el-card style="padding: 0;width: 155px;">
            <el-input v-if="isinputtrue" v-model="filter.procode" placeholder="产品ID" @keyup.enter.native="batchOnSearCh" @clear="clearableasync" clearable/>
          </el-card> -->
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable
            style="width: 80px">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </button>
        <button style="padding: 0;width: 105px; border: none;">
          <el-cascader v-model="filter.categoryids1" :options="categorylist"
            :props="{ checkStrictly: true, value: 'id' }" filterable style="width:100%;" placeholder="类目"
            @change="changeCategory(filter.platform, $event)" />
        </button>
        <button style="padding: 0;width: 200px; border: none;">
          <el-tooltip class="item" effect="dark" content="默认为精确匹配，在开始或者结尾输入*进行模糊匹配" placement="bottom">
            <inputYunhan title="系列编码" :row="inputrow" placeholder="系列编码" :inputt.sync="filter.styleCode"
              :inputshow="inputshow" :clearable="true" @callback="callbackStyleCode"></inputYunhan>
          </el-tooltip>
        </button>
        <button style="padding: 0;width: 135px; border: none;">
          <el-input v-model="filter.title" placeholder="标题" @keyup.enter.native="onSearch" clearable />
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.shopId" placeholder="店铺" clearable style="width: 130px">
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable clearable v-model="filter.projName" placeholder="请选择项目" style="width: 130px">
            <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
              :value="item.projName"></el-option>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-input v-model.trim="filter.remark" placeholder="请输入备注" maxlength="100" clearable style="width: 170px" />
        </button>
        <button style="padding: 0; border: none;">
          <el-select v-model="filter.mainCategories" placeholder="公司类目" collapse-tags clearable filterable
            style="width: 120px">
            <el-option v-for="item in mainCategoriesList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select v-model="filter.property" placeholder="属性" collapse-tags clearable filterable style="width: 120px">
            <el-option v-for="item in propertyList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.groupIds" placeholder="运营组长" style="width: 210px" clearable multiple collapse-tags
            @change="selectfuc">
            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.operateSpecialId" placeholder="运营专员" clearable style="width: 100px"
            @change="selectfuc">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.user1Id" placeholder="运营助理" clearable style="width: 100px"
            @change="selectfuc">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.user2Id" placeholder="产品专员" clearable style="width: 100px"
            @change="selectfuc">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.user3Id" placeholder="产品助理" clearable style="width: 95px"
            @change="selectfuc">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.brandId" placeholder="采购员" clearable style="width: 80px"
            @change="selectfuc">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.brandRate" placeholder="系统判定采购" clearable style="width: 100px"
            @change="selectfuc">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.IsProtectCornerId" placeholder="是否护墙角" clearable style="width: 100px"
            @change="selectfuc">
            <el-option label="已设置" :value="1"></el-option>
            <el-option label="未设置" :value="0"></el-option>
          </el-select>
        </button>
        <button style="padding: 0;border: none;">
          <el-select filterable v-model="filter.IsDamageLink" collapse-tags clearable placeholder="是否货损"
            style="width: 90px">
            <el-option label="否" :value="false" />
            <el-option label="是" :value="true" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select v-model="filter.status" placeholder="状态" clearable filterable style="width: 80px">
            <el-option label="未知" :value="0"></el-option>
            <el-option label="上架" :value="1"></el-option>
            <!-- <el-option label="缺货" :value="2"></el-option> -->
            <el-option label="下架" :value="3"></el-option>
          </el-select>
        </button>
        <button style="padding: 0;  border: none;">
          <el-select filterable v-model="filter.IsClassCodeCalc" placeholder="是否参与系列编码计算" clearable
            style="width: 170px">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <inputYunhan :title="inputtitle" :row="inputrow" :placeholder="a" :maxRows="1000" :inputshow="inputshow"
            :clearable="true" @callback="callback" :inputt.sync="filter.procode" filter.productCodes></inputYunhan>
        </button>
        <button style="padding: 0; border: none;">
          <inputYunhan ref="snProCode" :inputt.sync="filter.snProCode" v-model="filter.snProCode" class="publicCss"
            placeholder="苏宁ID/多条请按回车" :clearable="true" :clearabletext="true" :maxRows="200" :maxlength="3000"
            @callback="snProCodeBack" title="苏宁ID">
          </inputYunhan>
        </button>
        <button style="padding: 0; border: none;">
          <el-select v-model="filter.star" placeholder="星星" clearable filterable style="width: 80px">
            <el-option label="空白" :value="9"><span>空白</span></el-option>
            <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i
                class="el-icon-star-on">灰色</i></el-option>
            <el-option label="红色" style="color:red;size: 20px;" :value="1"><i class="el-icon-star-on">红色</i></el-option>
            <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i
                class="el-icon-star-on">橙色</i></el-option>
            <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i
                class="el-icon-star-on">黄色</i></el-option>
            <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-star-on">绿色</i></el-option>
            <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-star-on">蓝色</i></el-option>
            <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-star-on">靛色</i></el-option>
            <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-star-on">紫色</i></el-option>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select v-model="filter.flag" placeholder="旗帜" clearable filterable style="width: 80px">
            <el-option label="空白" :value="9"><span>空白</span></el-option>
            <el-option label="灰色" style="color:gray" :value="8"><i class="el-icon-s-flag">灰色</i></el-option>
            <el-option label="红色" style="color:red" :value="1"><i class="el-icon-s-flag">红色</i></el-option>
            <el-option label="橙色" style="color:orange" :value="2"><i class="el-icon-s-flag">橙色</i></el-option>
            <el-option label="黄色" style="color:yellow" :value="3"><i class="el-icon-s-flag">黄色</i></el-option>
            <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-s-flag">绿色</i></el-option>
            <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-s-flag">蓝色</i></el-option>
            <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-s-flag">靛色</i></el-option>
            <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-s-flag">紫色</i></el-option>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="上架时间"
            end-placeholder="上架时间"></el-date-picker>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.isFreeStream" placeholder="是否免流" clearable style="width: 100px">
            <el-option label="免流" :value="1"></el-option>
            <el-option label="不免流" :value="0"></el-option>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.isDel" placeholder="是否删除" clearable style="width: 100px">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.pinPai" placeholder="品牌" clearable style="width: 100px"
            @change="selectfuc">
            <el-option v-for="item in pinPailist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </button>
        <button style="padding: 0; border: none;">
          <el-select filterable v-model="filter.baoPin" placeholder="爆品" multiple collapse-tags clearable
            style="width: 200px">
            <el-option v-for="item in popularOptions_more" :key="item.value" :label="item.label" :value="item.value" />

          </el-select>
        </button>


        <button style="padding: 0;width: 150px; border: none;">
          <el-tooltip content="输入不为空可查询标签不为空的数据" placement="top" effect="light">
            <el-input v-model="filter.labels" placeholder="标签" clearable />
            <i class="el-icon-info"></i>
          </el-tooltip>
        </button>
        <button style="padding: 0;width: 200px; border: none;">
          <yhUserselectors :value.sync="filter.ownerId" maxlength="50" :text.sync="filter.owners" 
            style="width: 100%" placeholder="请输入运维">
          </yhUserselectors>
        </button>
        <button style="padding: 0; border: none;">
          <el-tooltip effect="dark" content="上新：该ID编码，首次在全平台销售   重开：该ID编码，已经在全平台销售" placement="top" >
            <el-select filterable v-model="filter.upNewOpenList" multiple collapse-tags clearable style="width: 200px;" placeholder="请选择上新/重开">
              <el-option label="无" value="无"></el-option>
              <el-option label="上新" value="上新"></el-option>
              <el-option label="重开" value="重开"></el-option>
            </el-select>
          </el-tooltip>
        </button>


        <button
          style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="onSearch">查询</button>

        <button
          style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="copyProcode">一键复制当页宝贝ID</button>
        <!-- <div style="display:flex; flex-position: row;">
            <inputYunhan :inputshow="inputshow" :clearable="true" @callback="callback"></inputYunhan>
            <el-button style="display: flex;" type="primary" @click="onSearch">查询</el-button>
        </div> -->

        <!-- <button
          style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="onOneNoticeShow">一键通知</button> -->
        <button
          style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="onOneNoticeLogShow">查看通知日志</button>
        <button
          style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="onOneCreateGroupChat">一键建群</button>
        <button
          style="padding: 0; border-color:#409EFF; background-color: #409EFF; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="onOneCreateGroupChatShow">建群日志</button>
        <button v-if="checkPermission('erp:product:syncproductcategorylog')"
          style="padding: 0; border-color:#FF4000; background-color: #FF4000; color: white; padding: 6rpx 15rpx; border-radius: 4px;"
          @click="categoryLogShow">类目日志</button>

      </el-button-group>
      <!-- {{filter.procode}} -->

    </template>

    <yhVxetable :id="'src\views\operatemanage\base\product1'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      @sortchange='sortchange' @select='selectchange' :isSelection='true' :tableData='productlist'
      :summaryarry="summaryarry" :showsummary='true' :hasexpandRight='true' :tableCols='tableCols' style="height: 95%;"
      :tableHandles='tableHandles' :loading="listLoading">
      <template #shareRateSum1>
        <vxe-column field="shareRateSum1" title="销售额分成" width='120' :sortable="true"
          :title-prefix="{content: '分成人数 | 总分成比例', icon: 'vxe-icon-info-circle-fill'}">
          <template #default="{ row }">
            <div v-if="row.shareRateCount1>0" style="cursor:pointer;color:#009380;font-weight: 600;margin-left:6px;"
              @click="getStratifyDetailStatistic(row,row.shareRateCount1, '销售额分成',1)">
              <el-popover style="overflow-y: hidden !important;" placement="right" trigger="click" width="590"
                height="400">
                <template #reference>
                  <span :style="{color: row.shareRateRed1?'red':''}">{{ row.shareRateCount1}} | {{
                    row.shareRateSum1}}%</span>
                </template>
                <div style="height: 100%; width: 100%; margin: 0; padding: 0;">
                  <div style="font-size: 16px;font-weight: bold;margin: 15px 0px 15px 0px;">销售额分成</div>
                  <vxe-table stripe :data="productShareDetail">
                    <vxe-column type="seq" width="60"></vxe-column>
                    <vxe-column field="avatar" title="头像" width="50">
                      <template #default="scoped">
                        <div>
                          <el-image style="width: 35px; height:35px; border-radius: 35px;" :src="scoped.row.avatar"
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :z-index="99999" preview-teleported
                            hide-on-click-modal :preview-src-list="[scoped.row.avatar]" :initial-index="4"
                            fit="cover" />
                        </div>
                      </template>
                    </vxe-column>
                    <vxe-column field="userName" title="姓名" width="80"></vxe-column>
                    <vxe-column field="regionName" title="区域" width="120"></vxe-column>
                    <vxe-column field="deptName" title="部门" width="85"></vxe-column>
                    <vxe-column field="post" title="职位" width="85"></vxe-column>
                    <vxe-column field="shareRate" title="分成比例" width="100">
                      <template #default="scoped">
                        <div>
                          <span> {{ scoped.row.shareRate }}%</span>
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
              </el-popover>
            </div>
          </template>
        </vxe-column>
      </template>

      <template #shareRateSum2>
        <vxe-column field="shareRateSum2" title="毛三分成" width='120' :sortable="true"
          :title-prefix="{content: '分成人数 | 总分成比例', icon: 'vxe-icon-info-circle-fill'}">
          <template #default="{ row }">
            <div v-if="row.shareRateCount2>0" style="cursor:pointer;color:#009380;font-weight: 600;margin-left:6px;"
              @click="getStratifyDetailStatistic(row,row.shareRateCount2, '毛三分成',2)">
              <el-popover style="overflow-y: hidden !important;" placement="right" trigger="click" width="590"
                height="400">
                <template #reference>
                  <span :style="{color: row.shareRateRed2?'red':''}"> {{ row.shareRateCount2}} | {{
                    row.shareRateSum2}}%</span>
                </template>
                <div style="height: 100%; width: 100%; margin: 0; padding: 0;">
                  <div style="font-size: 16px;font-weight: bold;margin: 15px 0px 15px 0px;">毛三分成</div>
                  <vxe-table stripe :data="productShareDetail">
                    <vxe-column type="seq" width="60"></vxe-column>
                    <vxe-column field="avatar" title="头像" width="50">
                      <template #default="scoped">
                        <div>
                          <el-image style="width: 35px; height:35px; border-radius: 35px;" :src="scoped.row.avatar"
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :z-index="99999" preview-teleported
                            hide-on-click-modal :preview-src-list="[scoped.row.avatar]" :initial-index="4"
                            fit="cover" />
                        </div>
                      </template>
                    </vxe-column>
                    <vxe-column field="userName" title="姓名" width="80"></vxe-column>
                    <vxe-column field="regionName" title="区域" width="120"></vxe-column>
                    <vxe-column field="deptName" title="部门" width="85"></vxe-column>
                    <vxe-column field="post" title="职位" width="85"></vxe-column>
                    <vxe-column field="shareRate" title="分成比例" width="100">
                      <template #default="scoped">
                        <div>
                          <span> {{ scoped.row.shareRate }}%</span>
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
              </el-popover>
            </div>
          </template>
        </vxe-column>
      </template>

      <template #shareRateSum3>
        <vxe-column field="shareRateSum3" title="毛四分成" width='120' :sortable="true"
          :title-prefix="{content: '分成人数 | 总分成比例', icon: 'vxe-icon-info-circle-fill'}">
          <template #default="{ row }">
            <div v-if="row.shareRateCount3>0" style="cursor:pointer;color:#009380;font-weight: 600;margin-left:6px;"
              @click="getStratifyDetailStatistic(row, row.shareRateCount3,'毛四分成',3)">
              <el-popover style="overflow-y: hidden !important;" placement="right" trigger="click" width="590"
                height="400">
                <template #reference>
                  <span :style="{color: row.shareRateRed3?'red':''}"> {{ row.shareRateCount3}} | {{
                    row.shareRateSum3}}%</span>
                </template>
                <div style="height: 100%; width: 100%; margin: 0; padding: 0;">
                  <div style="font-size: 16px;font-weight: bold;margin: 15px 0px 15px 0px;">毛四分成</div>
                  <vxe-table stripe :data="productShareDetail">
                    <vxe-column type="seq" width="60"></vxe-column>
                    <vxe-column field="avatar" title="头像" width="50">
                      <template #default="scoped">
                        <div>
                          <el-image style="width: 35px; height:35px; border-radius: 35px;" :src="scoped.row.avatar"
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :z-index="99999" preview-teleported
                            hide-on-click-modal :preview-src-list="[scoped.row.avatar]" :initial-index="4"
                            fit="cover" />
                        </div>
                      </template>
                    </vxe-column>
                    <vxe-column field="userName" title="姓名" width="80"></vxe-column>
                    <vxe-column field="regionName" title="区域" width="120"></vxe-column>
                    <vxe-column field="deptName" title="部门" width="85"></vxe-column>
                    <vxe-column field="post" title="职位" width="85"></vxe-column>
                    <vxe-column field="shareRate" title="分成比例" width="100">
                      <template #default="scoped">
                        <div>
                          <span> {{ scoped.row.shareRate }}%</span>
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
              </el-popover>
            </div>
          </template>
        </vxe-column>
      </template>

      <template #shareRateSum4>
        <vxe-column field="shareRateSum4" title="毛五分成" width='120' :sortable="true"
          :title-prefix="{content: '分成人数 | 总分成比例', icon: 'vxe-icon-info-circle-fill'}">
          <template #default="{ row }">
            <div v-if="row.shareRateCount4>0" style="cursor:pointer;color:#009380;font-weight: 600;margin-left:6px;"
              @click="getStratifyDetailStatistic(row, row.shareRateCount4, '毛五分成',4)">
              <el-popover style="overflow-y: hidden !important;" placement="right" trigger="click" width="590"
                height="400">
                <template #reference>
                  <span :style="{color: row.shareRateRed4?'red':''}"> {{ row.shareRateCount4}} | {{
                    row.shareRateSum4}}%</span>
                </template>
                <div style="height: 100%; width: 100%; margin: 0; padding: 0;">
                  <div style="font-size: 16px;font-weight: bold;margin: 15px 0px 15px 0px;">毛五分成</div>
                  <vxe-table stripe :data="productShareDetail">
                    <vxe-column type="seq" width="60"></vxe-column>
                    <vxe-column field="avatar" title="头像" width="50">
                      <template #default="scoped">
                        <div>
                          <el-image style="width: 35px; height:35px; border-radius: 35px;" :src="scoped.row.avatar"
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :z-index="99999" preview-teleported
                            hide-on-click-modal :preview-src-list="[scoped.row.avatar]" :initial-index="4"
                            fit="cover" />
                        </div>
                      </template>
                    </vxe-column>
                    <vxe-column field="userName" title="姓名" width="80"></vxe-column>
                    <vxe-column field="regionName" title="区域" width="120"></vxe-column>
                    <vxe-column field="deptName" title="部门" width="85"></vxe-column>
                    <vxe-column field="post" title="职位" width="85"></vxe-column>
                    <vxe-column field="shareRate" title="分成比例" width="100">
                      <template #default="scoped">
                        <div>
                          <span> {{ scoped.row.shareRate }}%</span>
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
              </el-popover>
            </div>
          </template>
        </vxe-column>
      </template>

      <template #shareRateSum5>
        <vxe-column field="shareRateSum5" title="净利分成" width='120' :sortable="true"
          :title-prefix="{content: '分成人数 | 总分成比例', icon: 'vxe-icon-info-circle-fill'}">
          <template #default="{ row }">
            <div v-if="row.shareRateCount5>0" style="cursor:pointer;color:#009380;font-weight: 600;margin-left:6px;"
              @click="getStratifyDetailStatistic(row, row.shareRateCount5, '净利分成',5)">
              <el-popover style="overflow-y: hidden !important;" placement="right" trigger="click" width="590"
                height="400">
                <template #reference>
                  <span :style="{color: row.shareRateRed5?'red':''}"> {{ row.shareRateCount5}} | {{
                    row.shareRateSum5}}%</span>
                </template>
                <div style="height: 100%; width: 100%; margin: 0; padding: 0;">
                  <div style="font-size: 16px;font-weight: bold;margin: 15px 0px 15px 0px;">净利分成</div>
                  <vxe-table stripe :data="productShareDetail">
                    <vxe-column type="seq" width="60"></vxe-column>
                    <vxe-column field="avatar" title="头像" width="50">
                      <template #default="scoped">
                        <div>
                          <el-image style="width: 35px; height:35px; border-radius: 35px;" :src="scoped.row.avatar"
                            :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :z-index="99999" preview-teleported
                            hide-on-click-modal :preview-src-list="[scoped.row.avatar]" :initial-index="4"
                            fit="cover" />
                        </div>
                      </template>
                    </vxe-column>
                    <vxe-column field="userName" title="姓名" width="80"></vxe-column>
                    <vxe-column field="regionName" title="区域" width="120"></vxe-column>
                    <vxe-column field="deptName" title="部门" width="85"></vxe-column>
                    <vxe-column field="post" title="职位" width="85"></vxe-column>
                    <vxe-column field="shareRate" title="分成比例" width="100">
                      <template #default="scoped">
                        <div>
                          <span> {{ scoped.row.shareRate }}%</span>
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-table>
                </div>
              </el-popover>
            </div>
          </template>
        </vxe-column>
      </template>

      <template #right>
        <vxe-column width="200" title="操作" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="scope.row.platform == 2 && filter.platform == 2" type="text"
              @click="updateIscompete(scope.row)">修改竞价</el-button>
            <el-button type="text" @click="onShowLog(scope.row)">查看日志</el-button>
          </template>
        </vxe-column>
      </template>
    </yhVxetable>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getProductlist" />
    </template>

    <el-dialog title="匹配商品编码" :visible.sync="dialogFormVisible" style="width: 100%;">
      <bindbianma ref="bindbianma" :isedit='true' :productId='bindproductId' />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogFormVisible = false">关闭</el-button>
          <el-button type="primary" @click="onSaveBindbianma()">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog title="导入商品负责人" :visible.sync="dialogVisible1" width="30%">
      <span>
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-select v-model="importFilte.platform" placeholder="请选择平台" style="width: 100%">
              <el-option label="请选择" value=""></el-option>
              <el-option label="淘系" value="1"></el-option>
              <el-option label="拼多多" value="2"></el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload1" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :http-request="uploadFile1" :on-success="uploadSuccess1">
              <template #trigger>
                <el-button size="small" type="primary">选取商品负责人文件</el-button>
              </template>
              <el-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload1">上传</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible1 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入商品编码匹配" :visible.sync="dialogVisible2" width="30%">
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
          <template #trigger>
            <el-button size="small" type="primary">选取商品编码匹配文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" :row="inputrow" size="small" type="success"
            @click="onSubmitupload2">上传</my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入商品提成比例" :visible.sync="dialogVisible5" width="30%">
      <span>
        <el-upload ref="upload5" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :on-change="uploadChange5" :on-remove="uploadRemove5" :http-request="uploadFile5">
          <template #trigger>
            <el-button size="small" type="primary">选取商品提成比例文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitupload5">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible5 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-drawer title="护墙角ID操作" :visible.sync="drawerprocid" :direction="directionbtt">
      <el-radio v-model="radio" @change="onHand(11)" label="11">绑定护墙角</el-radio>
      <el-radio v-model="radio" @change="onHand(12)" label="12">取消护墙角</el-radio>
    </el-drawer>

    <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <span style="margin-left: 80px;color: gray;" v-if="onHandNumber === 2">星星</span>
      <el-select v-model="star" v-if="onHandNumber === 2" placeholder="星星" clearable filterable
        style="width: 80px;margin-left: 12px;">
        <el-option label="空白" :value="9"><span>空白</span></el-option>
        <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i class="el-icon-star-on">灰色</i></el-option>
        <el-option label="红色" style="color:red;size: 20px;" :value="1"><i class="el-icon-star-on">红色</i></el-option>
        <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i class="el-icon-star-on">橙色</i></el-option>
        <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i class="el-icon-star-on">黄色</i></el-option>
        <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-star-on">绿色</i></el-option>
        <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-star-on">蓝色</i></el-option>
        <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-star-on">靛色</i></el-option>
        <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-star-on">紫色</i></el-option>
      </el-select>
      <span style="margin-left: 308px;color: gray;" v-if="onHandNumber === 2">旗帜</span>
      <el-select v-model="flag" v-if="onHandNumber === 2" placeholder="旗帜" clearable filterable
        style="width: 80px;margin-left: 12px;">
        <el-option label="空白" :value="9"><span>空白</span></el-option>
        <el-option label="灰色" style="color:gray" :value="8"><i class="el-icon-s-flag">灰色</i></el-option>
        <el-option label="红色" style="color:red" :value="1"><i class="el-icon-s-flag">红色</i></el-option>
        <el-option label="橙色" style="color:orange" :value="2"><i class="el-icon-s-flag">橙色</i></el-option>
        <el-option label="黄色" style="color:yellow" :value="3"><i class="el-icon-s-flag">黄色</i></el-option>
        <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-s-flag">绿色</i></el-option>
        <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-s-flag">蓝色</i></el-option>
        <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-s-flag">靛色</i></el-option>
        <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-s-flag">紫色</i></el-option>
      </el-select>
      <span style="margin-left: 100px;color: gray;" v-if="onHandNumber === 13">星星</span>
      <el-select v-model="Batchstar" v-if="onHandNumber === 13" placeholder="星星" clearable filterable
        style="width: 80px;margin-left: 12px;">
        <el-option label="空白" :value="9"><span>空白</span></el-option>
        <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i class="el-icon-star-on">灰色</i></el-option>
        <el-option label="红色" style="color:red;size: 20px;" :value="1"><i class="el-icon-star-on">红色</i></el-option>
        <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i class="el-icon-star-on">橙色</i></el-option>
        <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i class="el-icon-star-on">黄色</i></el-option>
        <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-star-on">绿色</i></el-option>
        <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-star-on">蓝色</i></el-option>
        <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-star-on">靛色</i></el-option>
        <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-star-on">紫色</i></el-option>
      </el-select>
      <span style="margin-left: 100px;color: gray;" v-if="onHandNumber === 13">旗帜</span>
      <el-select v-model="Batchflag" v-if="onHandNumber === 13" placeholder="旗帜" clearable filterable
        style="width: 80px;margin-left: 12px;">
        <el-option label="空白" :value="9"><span>空白</span></el-option>
        <el-option label="灰色" style="color:gray" :value="8"><i class="el-icon-s-flag">灰色</i></el-option>
        <el-option label="红色" style="color:red" :value="1"><i class="el-icon-s-flag">红色</i></el-option>
        <el-option label="橙色" style="color:orange" :value="2"><i class="el-icon-s-flag">橙色</i></el-option>
        <el-option label="黄色" style="color:yellow" :value="3"><i class="el-icon-s-flag">黄色</i></el-option>
        <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-s-flag">绿色</i></el-option>
        <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-s-flag">蓝色</i></el-option>
        <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-s-flag">靛色</i></el-option>
        <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-s-flag">紫色</i></el-option>
      </el-select>
      <!-- <span style="margin-left: 240px;" v-if="onHandNumber === 2">是否货损链接</span>
      <el-select v-model="IsDamageLink" v-if="onHandNumber === 2" placeholder="是否货损链接" clearable filterable
      style="width: 120px;margin-left: 12px;">
      <el-option label="否" :value="false"></el-option>
      <el-option label="是" :value="true"></el-option>
    </el-select>
    <span style="margin-left: 180px;" v-if="onHandNumber === 2">货损链接截止日期</span>
    <el-date-picker v-model="IsDamageLinkTime" type="date" placeholder="选择更新日期" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"></el-date-picker> -->
      <div class="drawer-footer" style="position: relative">
        <el-button type="primary" v-if="onHandNumber === 4" @click="onShowComm">历史提成比例</el-button>
        <el-button type="primary" v-if="onHandNumber === 4" @click.native="dialogVisible3 = true">设置成历史</el-button>
        <el-button type="primary" @click="onAdditemmethod">新增项目</el-button>
        <el-button @click.native="addFormVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="addLoading" @click="onHandSubmit" />
      </div>
    </el-drawer>

    <el-dialog title="选择更新日期" :visible.sync="dialogVisible3" width="30%" v-dialogDrag>
      <el-date-picker v-model="historyTime" type="date" placeholder="选择更新日期" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"></el-date-picker>
      <div style="float: right;">
        <el-button @click.native="dialogVisible3 = false">取消</el-button>
        <my-confirm-button type="submit" :loading="addLoading" @click="onHandSubmit" />
      </div>
    </el-dialog>

    <el-dialog title="历史提成比例" :visible.sync="dialogVisible4" width="80%" v-dialogDrag>
      <div>
        <productcommhistory ref="productcommhistory" style="height: 480px"></productcommhistory>
      </div>
    </el-dialog>

    <el-dialog title="日志" :visible.sync="logDialogVisible" width="60%" v-dialogDrag>
      <div>
        <productchangelog ref="productchangelog" style="height: 480px" :filter="logfilter"></productchangelog>
      </div>
    </el-dialog>

    <!-- 铺货到店铺 -->
    <vxe-modal title="铺货到店铺" v-model="onlineGoods.visible" :width="1600" marginSize='-500'>
      <template #default>
        <span>
          <productReportPddOnlineForm :filter1="onlineGoods.filter" ref="productReportPddOnlineForm"
            @close="closePddOnlineForm"></productReportPddOnlineForm>
        </span>
      </template>
    </vxe-modal>

    <!-- 批量修改 -->
    <el-drawer title="批量修改" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="batchEditFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <el-form :model="batchEditFormData" label-width="100px" ref="batchEditForm" label-position="right">
        <el-row>
          <el-col :span="6">
            <el-form-item label="平台" prop="platform">
              <!-- :rules="[{ required: false, message: '请选择平台', trigger: 'blur' }]" -->
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.platform"
                placeholder="请选择平台" @change="changeplatform">
                <el-option v-for="item in platformlist" :key="'p2f-' + item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.platform_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属店铺" prop="shopId">
              <!-- :rules="[{ required: batchEditFormData.platform ? true : false, message: '请选择店铺', trigger: 'blur' }]" -->
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.shopId"
                placeholder="请选择店铺">
                <el-option v-for="item in shopListbetch" :key="'shop-' + item.id" :label="item.shopName"
                  :value="item.id"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.shopId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="组长" prop="groupId">
              <!-- :rules="[{ required: true, message: '请选择组长', trigger: 'blur' }]" -->
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.groupId"
                :disabled="!checkPermission('api:operatemanage:productmanager:groupIdBatchEdit')" placeholder="请选组长">
                <el-option v-for="item in directorGroupList" :key="'grop-' + item.value" :label="item.value"
                  :value="item.key"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.groupId_Checked"
                :disabled="!checkPermission('api:operatemanage:productmanager:groupIdBatchEdit')">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运营专员" prop="operateSpecialUserId">
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.operateSpecialUserId"
                placeholder="请选择运营专员">
                <el-option v-for="item in directorList" :key="'osu-' + item.value" :label="item.value"
                  :value="item.key"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.operateSpecialUserId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="运营助理" prop="userId">
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.userId"
                placeholder="请选择运营助理">
                <el-option v-for="item in directorList" :key="'u1-' + item.value" :label="item.value"
                  :value="item.key"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.userId_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品专员" prop="userId2">
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.userId2"
                placeholder="请选择产品专员">
                <el-option v-for="item in directorListUserType" :key="'u2-' + item.value" :label="item.value"
                  :value="item.key"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.userId2_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品助理" prop="userId3">
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.userId3"
                placeholder="请选择">
                <el-option v-for="item in directorListUserType" :key="'osu-' + item.value" :label="item.value"
                  :value="item.key"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.userId3_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <div style="display:flex">
              <el-form-item label="星星" prop="star" label-width="50px">
                <el-select style="float: left;width: 100px; " clearable v-model="batchEditFormData.star"
                  placeholder="请选择星星">
                  <el-option label="空白" :value="9"><span>空白</span></el-option>
                  <el-option label="灰色" style="color:gray;size: 20px;" :value="8"><i
                      class="el-icon-star-on">灰色</i></el-option>
                  <el-option label="红色" style="color:red;size: 20px;" :value="1"><i
                      class="el-icon-star-on">红色</i></el-option>
                  <el-option label="橙色" style="color:orange;size: 20px;" :value="2"><i
                      class="el-icon-star-on">橙色</i></el-option>
                  <el-option label="黄色" style="color:yellow;size: 10px;" :value="3"><i
                      class="el-icon-star-on">黄色</i></el-option>
                  <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-star-on">绿色</i></el-option>
                  <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-star-on">蓝色</i></el-option>
                  <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-star-on">靛色</i></el-option>
                  <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-star-on">紫色</i></el-option>
                </el-select>
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.star_Checked"></el-checkbox>
              </el-form-item>
              <el-form-item label="旗帜" prop="flag" label-width="50px">
                <el-select style="float: left;width: 100px;  " clearable v-model="batchEditFormData.flag"
                  placeholder="请选择旗帜">
                  <el-option label="空白" :value="9"><span>空白</span></el-option>
                  <el-option label="灰色" style="color:gray" :value="8"><i class="el-icon-s-flag">灰色</i></el-option>
                  <el-option label="红色" style="color:red" :value="1"><i class="el-icon-s-flag">红色</i></el-option>
                  <el-option label="橙色" style="color:orange" :value="2"><i class="el-icon-s-flag">橙色</i></el-option>
                  <el-option label="黄色" style="color:yellow" :value="3"><i class="el-icon-s-flag">黄色</i></el-option>
                  <el-option label="绿色" style="color:green" :value="4"><i class="el-icon-s-flag">绿色</i></el-option>
                  <el-option label="蓝色" style="color:blue" :value="5"><i class="el-icon-s-flag">蓝色</i></el-option>
                  <el-option label="靛色" style="color:indigo" :value="6"><i class="el-icon-s-flag">靛色</i></el-option>
                  <el-option label="紫色" style="color:purple" :value="7"><i class="el-icon-s-flag">紫色</i></el-option>
                </el-select>
                <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.flag_Checked">
                  <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
                </el-checkbox>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="添加备注" prop="projName">
              <el-input v-model.trim="batchEditFormData.remark" placeholder="请输入备注" maxlength="100" clearable
                style="width: 56%;" />
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.Isremark_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主图是否原创：" prop="isBanner" label-width="175px">
              <el-switch v-model="batchEditFormData.isBanner">
                active-color="#13ce66"
                inactive-color="#ff4949"
              </el-switch>
              <el-checkbox style="margin-left: 78px;" v-model="batchEditFormData.IsBanner_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU是否原创：" prop="isSku" label-width="175px">
              <el-switch v-model="batchEditFormData.isSku">
                active-color="#13ce66"
                inactive-color="#ff4949"
              </el-switch>
              <el-checkbox style="margin-left: 78px;" v-model="batchEditFormData.IsSku_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="视频是否原创：" prop="isVideo" label-width="175px">
              <el-switch v-model="batchEditFormData.isVideo">
                active-color="#13ce66"
                inactive-color="#ff4949"
              </el-switch>
              <el-checkbox style="margin-left: 78px;" v-model="batchEditFormData.IsVideo_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="详情图是否原创：" prop="isDetailmap" label-width="180px">
              <el-switch v-model="batchEditFormData.isDetailmap">
                active-color="#13ce66"
                inactive-color="#ff4949"
              </el-switch>
              <el-checkbox style="margin-left: 78px;" v-model="batchEditFormData.IsDetailmap_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="项目" prop="projName">
              <el-select style="float: left; " filterable clearable v-model="batchEditFormData.projName"
                placeholder="请选择">
                <el-option v-for="item in projectList" :key="item.projName" :label="item.projName"
                  :value="item.projName"></el-option>
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.IsProjName_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
              <el-button type="primary" style="margin-left: 10px;" @click="onAdditemmethod">新增项目</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="爆品" prop="baoPin">
              <el-select filterable clearable v-model="batchEditFormData.baoPin" placeholder="请选择">
                <el-option v-for="item in popularOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.IsBaoPin_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="上新/重开" prop="upNewOpen">
              <el-tooltip effect="dark" content="上新：该ID编码，首次在全平台销售   重开：该ID编码，已经在全平台销售" placement="bottom" >
                <el-select filterable clearable v-model="batchEditFormData.upNewOpen" placeholder="请选择">
                  <el-option label="上新" value="上新"></el-option>
                  <el-option label="重开" value="重开"></el-option>
                </el-select>
              </el-tooltip>
              <el-checkbox style="margin-left: 10px;" v-model="batchEditFormData.IsUpNewOpen_Checked">
                <span style="color:#b4bccc;fontSize:12px;">勾选提交更新</span>
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>

      <div class="drawer-footer" style="position: relative">
        <el-button @click.native="batchEditFormVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="batchEditFormLoading" @click="batchEditFormSubmit" />
      </div>
    </el-drawer>

    <!-- 手动同步店铺商品资料时间 -->
    <el-dialog title="手动同步店铺商品资料" :visible.sync="onShowTimeRangeVisible" width="50%" v-dialogDrag
      :close-on-click-modal="false">
      <span>
        <el-tag type="success">温馨提示：时间区间数量的最大限制5个。如需添加新的时间区间，请等待执行完成之后操作。</el-tag>
      </span>
      <span>
        <onSetTimeRange ref="onSetTimeRange" style="height: 500px"></onSetTimeRange>
      </span>
    </el-dialog>

    <el-drawer title="设置亏损连接" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="SettingsDamageLinkDialog.visible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <form-create :rule="autoformparm.rule" v-model="autoformparm.fApi" :option="autoformparm.options" />
      <div class="drawer-footer">
        <el-button @click.native="SettingsDamageLinkDialog.visible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onSettingsDamageLink" />
      </div>
    </el-drawer>

    <el-dialog :title="oneNoticeDialog.title" :visible.sync="oneNoticeDialog.visible" width="30%" v-dialogDrag
      :close-on-click-modal="false">
      <el-form :model="oneNoticeDialog.oneNoticeFormData" label-width="80px" ref="oneNoticeForm" label-position="right"
        :rules="oneNoticeDialog.formRules">
        <el-row>
          <el-form-item label="系列编码" prop="seriesName">
            <el-input v-model="oneNoticeDialog.oneNoticeFormData.seriesName" auto-complete="off" :maxlength="100"
              disabled />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="标题" prop="noticeTitle">
            <el-input v-model="oneNoticeDialog.oneNoticeFormData.noticeTitle" auto-complete="off" :maxlength="50" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="内容" prop="noticeTitle">
            <el-input v-model="oneNoticeDialog.oneNoticeFormData.noticeContext" auto-complete="off" :maxlength="300"
              type="textarea" :rows="4" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="图片" prop="noticeImgs">
            <YhImgUpload :value.sync="oneNoticeDialog.oneNoticeFormData.noticeImgs" :limit="10" :ismultiple="true">
            </YhImgUpload>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click.native="oneNoticeDialog.visible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="oneNoticeDialog.formLoading" @click="onOneNoticeSave" />
      </span>
    </el-dialog>

    <el-dialog title="一键建群" :visible.sync="createdChatNew.visible" width="30%" v-dialogDrag
      :close-on-click-modal="false">
      <el-row>
        <el-input type="textarea" :rows="5" placeholder="请输入建群理由" v-model="createdChatNew.createGroupChatReason"
          maxlength="2000"> </el-input>
      </el-row>
      <el-row style="padding-top: 10px;">
        <el-button type="primary" @click="onCreatedChatNew(1)">按勾选的产品建群</el-button>
        <el-button type="primary" @click="onCreatedChatNew(2)">按搜索的结果建群</el-button>
        &nbsp; &nbsp;&nbsp;&nbsp;<el-checkbox v-model="createdChatNew.checkOnlyYunYing">仅拉运营建群</el-checkbox>
      </el-row>
      <el-row style="padding-top: 10px;">
        注释：最多25000个产品ID
      </el-row>
    </el-dialog>

    <el-dialog title='导出' :visible.sync="exportracklogoutLogs" width="30%" v-dialogDrag>
      <div class="centered-content">
        <el-date-picker v-model="logtimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions"
          style="width: 350px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
      </div>
      <div class="centered-content" style="color: red;">*如导出日志超过100000条数据会导致系统卡顿或长时间无响应,请刷新界面重新导出</div>
      <div class="centered-content">
        <el-button @click="exportracklogoutLogs = false" style="margin-right: 20px;">关闭</el-button>
        <el-button type="primary" @click="onExportLogs">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="新增项目" width="20%" ref="additemDialog" :visible.sync="addprojNameDialog.visible" v-dialogDrag>
      <div style="height: 100px;display: flex;align-items: center;width: 100%;">
        <span style="font-size: 17px;">项目：</span>
        <el-input v-model.trim="addprojNameDialog.projName" maxlength="50" placeholder="请输入项目" style="width: 80%;"
          clearable></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addprojNameDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="addWLCompany">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="类目同步失败日志" :visible.sync="categoryLogDialog.visible" width="50%" v-dialogDrag>
      <productCategoryErrorLog ref="productCategoryErrorLog" style="height: 500px;"></productCategoryErrorLog>
    </el-dialog>

    <el-dialog title="物流标签设置" :visible.sync="wlLableDialog.visiable " width="20%" v-dialogDrag>
      <div>
        <el-input class="input-new-tag" v-model.trim="wlLableDialog.wlKeyName" clearable ref="saveTagInput"
          maxlength="10" placeholder="请输入物流标签" size="small">
        </el-input>
        <div style="color:red;">多个用","逗号隔开，添加多个</div>
        <el-button type="primary" @click="addWlKey">添加</el-button>
        <br>
        <span style="line-height: 28px;height: 28px;">标签：</span>
        <br>
        <el-tag style="margin-left: 3px;" v-for="tag in wlLableDialog.wlKeyNameList" :key="tag" size="medium"
          :disable-transitions="false" @close="removeWlKey(tag)" closable>{{ tag }} </el-tag>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="wlLableDialog.visiable = false">取 消</el-button>
        <el-button type="primary" @click="saveWlLable">保存&计算</el-button>
      </span>
    </el-dialog>

    <el-drawer title="编辑分成" :visible.sync="showdrawer" :direction="'btt'" size="50%"
      :before-close="handleEditShareClose">
      <div style="text-align: center;">
        <template>
          <el-radio-group v-model="filter.addUser.type" size="small" @change="searchProductShareDetails">
            <el-radio label="1">销售额分成</el-radio>
            <el-radio label="2">毛三分成</el-radio>
            <el-radio label="3">毛四分成</el-radio>
            <el-radio label="4">毛五分成</el-radio>
            <el-radio label="5">净利分成</el-radio>
          </el-radio-group>
        </template>
      </div><!--@select='selectProfitChange'-->
      <div style="width: 100%;height: 80%;">
        <yhVxetable :id="'src\views\operatemanage\base\product2'" ref="table" :that='that' :isIndex='true'
          :hasexpand='true' :isSelection='true' :tableData='profitlist' :hasexpandRight='true' :tableCols='tableCols2'
          style="width: 99%;margin-left: 8px;" :tableHandles='tableHandles2' :loading="listLoading2">
          <template #right>
            <vxe-column width="200" title="操作" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" @click="editUser(scope.row)">修改</el-button>
                <el-button type="text" @click="deleteUser(scope.row)">删除</el-button>
              </template>
            </vxe-column>
          </template>
        </yhVxetable>
      </div>
    </el-drawer>
    <!--:before-close="handleClose"-->
    <el-dialog title="添加分成比例" :visible.sync="addUserDialogVisible" width="20%">
      <el-form id="addUserDialog" ref="form" :model="filter.addUser" size="small">
        <el-form-item style="margin-bottom: 10px 0;" label="分成人员">
          <el-select style="width: 80%;" filterable remote clearable v-model="filter.addUser.userId"
            placeholder="请选择分成人员" :remote-method="handleAddRateUserList">
            <el-option v-for="item in addRateUserList" :key="item.key" :label="item.value"
              :value="item.thirdId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-bottom: 10px 0;" label="分成比例">
          <el-input v-model.trim="filter.addUser.shareRate" maxlength="50" placeholder="请输入分成比例" style="width: 75%;"
            clearable @input="verifyNumberPercentage($event, 'add')"></el-input>%
        </el-form-item>
        <el-input v-model.trim="filter.addUser.id" v-show="false" style="width:80%;" clearable></el-input>
        <div class="dialog-footer" style="text-align:right;">
          <el-button @click="addUserDialogVisible= false">取 消</el-button>
          <el-button type="primary" @click="addUserComfirm()">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog title="编辑分成比例" :visible.sync="editUserDialogVisible" width="20%">
      <el-form id="editUserDialog" ref="form" :model="filter.addUser" size="small">
        <el-form-item style="margin-bottom: 10px 0;" label="分成人员">
          <el-select style="width: 80%;" filterable remote clearable v-model="filter.editUser.userId"
            placeholder="请选择分成人员" :remote-method="handleAddRateUserList">
            <el-option v-for="item in addRateUserList" :key="item.key" :label="item.value"
              :value="item.thirdId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-bottom: 10px 0;" label="分成比例">
          <el-input v-model.trim="filter.editUser.shareRate" maxlength="50" placeholder="请输入分成比例" style="width: 75%;"
            clearable @input="verifyNumberPercentage($event, 'edit')"></el-input>%
        </el-form-item>
        <el-input v-model.trim="filter.addUser.id" v-show="false" style="width:80%;" clearable></el-input>
        <div class="dialog-footer" style="text-align:right;">
          <el-button @click="editUserDialogVisible= false">取 消</el-button>
          <el-button type="primary" @click="editUserComfirm()">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog title="产品管理批量修改数据导入" :visible.sync="dialogVisible12" width="40%" v-dialogDrag>
      <template #title>
        <div>
          选择替换的数据列：
          <el-button style="margin-left: 10px;" @click="selectAll">全选/取消全选</el-button>
          <el-button size="small" type="primary" @click="downLoadFile">下载模版</el-button>
        </div>
      </template>
      <div>
        <el-checkbox-group v-model="colOptions" @change="changeOptions">
          <el-checkbox v-for="(item, index) in colSelect" :label="item" :key="item"></el-checkbox>
        </el-checkbox-group>
      </div>
      <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload ref="upload12" :auto-upload="false" :multiple="false" action accept=".xlsx"
            :http-request="uploadFile12" :on-change="uploadChange12" :on-remove="uploadRemove12">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="submitupload12">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible12 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="客服" :visible.sync="KFlogVisible" width="50%" v-dialogDrag>
      <customerLogs :customerInfo="customerInfo" v-if="KFlogVisible" />
    </el-dialog>
  </my-container>
</template>

<script>
import { getDirectorList, getDirectorListWithDdInfo, getDirectorGroupList, getProductBrandPageList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { getList as getcategorylist, getListByPId } from '@/api/operatemanage/base/category'
import {getUserInfo,UpdateProductShare,
  addorEditProduct, updateDamageLink, getProductById, batchDeleteProduct, batchUpdateProduct, batchUpdateProductCommission, batchUpdateProductMask, batchUpdateProductCid,
  batchUpdateProductBrandid, saveProBianMa, getProductBianMaById, getPageProductList, importProductBianMaBind, importProductDirector, exportProduct,
  batchUpdateBrand, batchIsProtectCornerId, BatchProductIsClassCodeCalc, updateProductIsCompeteAsync, batchUpdateTag, oneNoticeProductSendMsg, createGroupChatProductSendMsg
  ,getProductExpressLableSet ,saveProductExpressLableSet,getProductShareDetailList,deleteProductShareDetailById,batchInsertProductShare,updateProductShare,clearProductShareDetail,ImportProductShare
} from '@/api/operatemanage/base/product'
import { rulePlatform, ruleShop, ruleProductCategory, ruleDirectorBrand } from '@/utils/formruletools'
import { platformlist, formatTime, downloadLink, popularOptions,popularOptions_more } from '@/utils/tools'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import yhVxetable from "@/components/VxeTable/yh_vxetable.vue";
import bindbianma from '@/components/Bus/probindbianma'
import { treeToList, listToTree, getTreeParents } from '@/utils'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { formatPlatform, formatYesorno, formatYesornoBool, formatbianmastatus, formatproducmask, formatLink, formatLinkProCode } from "@/utils/tools";
import { getformaddsinglerule, getformeditbatchrule, getformcommissionbatchrule, getformproductCidbatchrule, getformproductMaskrule, getformproductBrandrule, getformeditstylerule, getTagrule } from "./productconfig";
import productcommhistory from './productcommhistory.vue'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import productchangelog from './productchangelog.vue'
import productReportPddOnlineForm from "@/views/bookkeeper/reportday/productReportPddOnlineForm.vue"
import onSetTimeRange from './onSetTimeRange.vue'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import { getProductUpDownLogExport, getProductProjectList, addProductProject, getProductRemarkList, deletePDDProduct, importBatchUpdateProduct } from "@/api/operatemanage/productmanager"
import dayjs from 'dayjs';
import { getProductPinPaiList } from '@/api/operatemanage/base/product'
import productCategoryErrorLog from '@/views/operatemanage/base/productCategoryErrorLog.vue'
import request from '@/utils/request'
import customerLogs from './customerLogs.vue'
import middlevue from "@/store/middle.js"
import yhUserselectors from "@/components/YhCom/yh-userselectors.vue";



const mainCategoriesListConst = [
  { value: 1, label: '服饰箱包' },
  { value: 2, label: '家纺家具家装' },
  { value: 3, label: '家居生活' },
  { value: 4, label: '健康医药' },
  { value: 5, label: '美容个护' },
  { value: 6, label: '母婴玩具' },
  { value: 7, label: '汽配摩托' },
  { value: 8, label: '食品保健' },
  { value: 9, label: '数码电器' },
  { value: 10, label: '水果生鲜' },
  { value: 11, label: '虚拟海淘医药' },
  { value: 12, label: '运动户外' }
]
const propertyListConst = [
  { value: 1, label: '达播' },
  { value: 2, label: '商品卡' },
]
const tableCols = [
  { type:'checkbox',label:'' },
  { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'snProCode', label: '苏宁宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.snProCode) },
  { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, prop: 'projName', label: '项目', width: '60', sortable: 'custom', },
  { istrue: true, prop: 'styleCode', label: '系列编码', width: '120', sortable: 'custom', formatter: (row) => !row.styleCode ? " " : row.styleCode },
  { istrue: true, prop: 'star', label: '星星', width: '40', type: 'newstar' },
  { istrue: true, prop: 'flag', label: '旗帜', width: '40', type: 'flag' },
  { istrue: true, prop: 'productCategoryId', label: '类目', width: '140', sortable: 'custom', permission: "productpermis", formatter: (row) => row.productCategoryId == "0" ? " " : row.productCategoryName },
  { istrue: true, prop: 'isBanner', label: '主图原创', width: '60', sortable: 'custom', formatter: (row) => row.isBanner == 1 ? '是' : '否' },
  { istrue: true, prop: 'isSku', label: 'SKU原创', width: '60', sortable: 'custom', formatter: (row) => row.isSku == 1 ? '是' : '否' },
  { istrue: true, prop: 'isVideo', label: '视频原创', width: '60', sortable: 'custom', formatter: (row) => row.isVideo == 1 ? '是' : '否' },
  { istrue: true, prop: 'isDetailmap', label: '详情图原创', width: '60', sortable: 'custom', formatter: (row) => row.isDetailmap == 1 ? '是' : '否' },
  { istrue: true, prop: 'shopId', label: '店铺', width: '150', sortable: 'custom', formatter: (row) => row.shopName },
  {
    istrue: true,  label: '客服', prop: 'kefu', width: '150', type: 'button', btnList: [
      {
        label: '查看',
        handle: (that, row) => that.openKFlog(row),
      }
    ]
  },
  { istrue: true, prop: 'isFreeStream', label: '是否免流', width: '90', sortable: 'custom', align: 'center', formatter: (row) => row.isFreeStream == 1 ? '免流' : row.isFreeStream == 0 || row.isFreeStream == null ? ' - ' : '' },
  { istrue: true, prop: 'isDel', label: '是否删除', width: '90', sortable: 'custom', align: 'center', formatter: (row) => row.isDel ? "是" : "否" },
  { istrue: true, prop: 'pinPai', label: '品牌', width: '150', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'pinPaiEditTime', label: '品牌更新时间', width: '150', sortable: 'custom', align: 'center' },
  {
    istrue: true, prop: 'mainCategories', label: '公司类目', width: '80',
    formatter: (row) => { if (!row.mainCategories) return ""; else return mainCategoriesListConst.find(f => f.value == row.mainCategories)?.label; }
  },
  {
    istrue: true, prop: 'property', label: '属性	', width: '80',
    formatter: (row) => { if (!row.property) return ""; else return propertyListConst.find(f => f.value == row.property)?.label; }
  },
  { istrue: true, prop: 'onlineGoods', label: '铺货', width: '40', align: 'center', type: 'click', formatter: (row) => { return row.platform == 2 ? '铺' : "" }, handle: (that, row) => that.onlineGoodsAsync(row.proCode, row.groupId) },
  { istrue: true, prop: 'groupId', label: '运营组', width: '70', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
  { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', sortable: 'custom', formatter: (row) => row.operateSpecialUserName || ' ' },
  { istrue: true, prop: 'userId', label: '运营助理', width: '80', sortable: 'custom', formatter: (row) => row.userRealName || ' ' },
  { istrue: true, prop: 'userId2', label: '产品专员', width: '60', sortable: 'custom', formatter: (row) => row.userRealName2 || ' ' },
  { istrue: true, prop: 'userId3', label: '产品助理', width: '60', sortable: 'custom', formatter: (row) => row.userRealName3 || ' ' },
  { istrue: true, prop: 'baoPin', label: '爆品', width: '100', sortable: 'custom', align: 'center' },
  { istrue: true, prop: 'baoPinOnTime', label: '爆品申报时间', width: '80', sortable: 'custom', formatter: (row) => formatTime(row.baoPinOnTime, 'YYYY-MM-DD') },
  { istrue: true, prop: 'isCompete', label: '是否竞价', width: '90', sortable: 'custom', formatter: (row) => row.isCompete == 1 ? '是' : '否' },
  { istrue: true, prop: 'isDamageLink', label: '是否货损', width: '90', sortable: 'custom', formatter: (row) => row.isDamageLink == 1 ? '是' : '否', type: 'click', handle: (that, row) => that.isDamageLinkClick(row) },
  { istrue: true, prop: 'isDamageLinkTime', label: '货损截止时间', width: '100', sortable: 'custom', formatter: (row) => row.isDamageLinkTime == null ? '' : row.isDamageLinkTime == '0001-01-01 00:00:00' ? "" : row.isDamageLinkTime },
  { istrue: true, prop: 'onTime', label: '上架时间', width: '150', sortable: 'custom', },
  { istrue: true, prop: 'owner1', label: '运维', width: '75', sortable: 'custom'},
  { istrue: true, prop: 'brandId', label: '采购', width: '75', sortable: 'custom', tipmesg: '采购负责人', formatter: (row) => !row.brandName ? " " : row.brandName },
  { istrue: true, prop: 'remark', label: '备注', width: '80', sortable: 'custom', },
  { istrue: true, prop: 'brandRate', label: '系统判定采购', permission: "purchaseperssion", width: '120' },
  { istrue: true, prop: 'giftlinkID', label: '赠品链接主ID', width: '110', sortable: 'custom', permission: "productpermis", },
  { istrue: true, prop: 'title', label: '标题', width: '250', sortable: 'custom' },
  { istrue: true, prop: 'status', label: '状态', width: '60', sortable: 'custom', permission: "productpermis", formatter: (row) => formatbianmastatus(row.status) },
  { istrue: true, prop: 'isClassCodeCalc', label: '是否参与系列编码计算', width: '100', sortable: 'custom', permission: "productpermis", formatter: (row) => row.isClassCodeCalc == true ? '是' : '否' },
  { istrue: true, prop: 'offTime', label: '下架时间', width: '150', sortable: 'custom', },
  { istrue: true, prop: 'labels', label: '标签', width: '150', sortable: 'custom', },
  { istrue: true, prop: 'shareRateSum1', label: '销售额分成', type: 'slot', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate1 == 0 ? '0' : row.commissionUserName1 + row.commissionRate1 + '%' },
  { istrue: true, prop: 'shareRateSum2', label: '毛三分成', type: 'slot', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate2 == 0 ? '0' : row.commissionUserName2 + row.commissionRate2 + '%' },
  { istrue: true, prop: 'shareRateSum3', label: '毛四分成', type: 'slot', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate3 == 0 ? '0' : row.commissionUserName3 + row.commissionRate3 + '%' },
  { istrue: true, prop: 'shareRateSum4', label: '毛五分成', type: 'slot', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate4 == 0 ? '0' : row.commissionUserName4 + row.commissionRate4 + '%' },
  { istrue: true, prop: 'shareRateSum5', label: '净利分成', type: 'slot', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate4 == 0 ? '0' : row.commissionUserName4 + row.commissionRate4 + '%' },
  // { istrue: true, prop: 'commissionRate1', label: '毛利提成比例1', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate1 == 0 ? '0' : row.commissionUserName1 + row.commissionRate1 + '%' },
  // { istrue: true, prop: 'commissionRate2', label: '毛利提成比例2', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate2 == 0 ? '0' : row.commissionUserName2 + row.commissionRate2 + '%' },
  // { istrue: true, prop: 'commissionRate3', label: '毛利提成比例3', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate3 == 0 ? '0' : row.commissionUserName3 + row.commissionRate3 + '%' },
  // { istrue: true, prop: 'commissionRate4', label: '毛利提成比例4', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionRate4 == 0 ? '0' : row.commissionUserName4 + row.commissionRate4 + '%' },
  // { istrue: true, prop: 'commissionProfitRate1', label: '净利提成比例1', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionProfitRate1 == 0 ? '0' : row.commissionUserName1 + row.commissionProfitRate1 + '%' },
  // { istrue: true, prop: 'commissionProfitRate2', label: '净利提成比例2', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionProfitRate2 == 0 ? '0' : row.commissionUserName2 + row.commissionProfitRate2 + '%' },
  // { istrue: true, prop: 'commissionProfitRate3', label: '净利提成比例3', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionProfitRate3 == 0 ? '0' : row.commissionUserName3 + row.commissionProfitRate3 + '%' },
  // { istrue: true, prop: 'commissionProfitRate4', label: '净利提成比例4', width: '90', sortable: 'custom', permission: "productpermis", formatter: (row) => row.commissionProfitRate4 == 0 ? '0' : row.commissionUserName4 + row.commissionProfitRate4 + '%' },
  // {istrue:true,prop:'isbmpp',label:'编码匹配	', width:'80',formatter:(row)=>formatYesornoBool(row.isbmpp)},
  // {istrue:true,prop:'mask',label:'标签', width:'80',permission:"productpermis",formatter:(row)=>formatproducmask(row.mask)},
  // {istrue:true,prop:'shopName1',label:'运费累计', width:'80',permission:"productpermis",},
  { istrue: true, prop: 'upNewOpen', label: '上新/重开', width: '120', sortable: 'custom', align: 'center', tipmesg: '上新：该ID编码，首次在全平台销售\n重开：该ID编码，已经在全平台销售' }, 
  { istrue: true, prop: 'modifiedTime', label: '编辑时间', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '添加时间', width: '150', sortable: 'custom', permission: "productpermis", },
  // {  istrue:true, prop: 'rightbtn', label:'操作',type: 'slot'}
];
const tableHandles1 = [
  { label: '单个修改', permission: "api:operatemanage:productmanager:addoreditproduct", handle: (that) => that.onHand(2) },
  { label: '批量修改', permission: 'api:operatemanage:productmanager:batchupdateproduct', handle: (that) => that.onHand(3) },
  // { label: '批量修改标识', permission: 'api:operatemanage:ProductManager:BatchUpdateTag', handle: (that) => that.onHand(13) },
  { label: '批量修改采购员', permission: 'api:operatemanage:ProductManager:BatchUpdateBrand', handle: (that) => that.onHand(9) },
  // {label:"下载导入提成比例模板", permission: 'api:operatemanage:productmanager:batchupdateproduct', handle:(that)=>that.onDownShareRate()},
  // {label:"导入提成比例", permission: 'api:operatemanage:productmanager:batchupdateproduct', handle:(that)=>that.onImportProductShare()},
  { label: '批量设置提成比例', permission: 'api:operatemanage:productmanager:batchupdateproduct', handle: (that) => that.onHand(4) },
  { label: '批量清除提成比例', permission: 'api:operatemanage:productmanager:batchupdateproduct', handle: (that) => that.clearUserRate() },
  // { label: '批量替换提成比例', handle: (that) => that.resetUserRate() },
  //{label:'批量设置平台及类目',permission:'api:operatemanage:productmanager:batchupdateproductcid', handle:(that)=>that.onHand(5)},
  { label: '批量绑定护墙角', handle: (that) => that.onHandprocid() },
  { label: '批量删除', permission: 'api:operatemanage:productmanager:batchdeleteproduct', handle: (that) => that.onHand(7) },
  //{label:'批量设置标签', handle:(that)=>that.onHand(8)},
  //{label:"下载导入负责人匹配模板", handle:(that)=>that.onDownDirector()},{label:"导入负责人匹配", handle:(that)=>that.onImportDirector()},
  //{label:"下载导入商品编码匹配模板", handle:(that)=>that.onDownBianMaBind()},{label:"导入商品编码匹配", handle:(that)=>that.onImportBianMaBind()},
  { label: '导出', permission: 'api:operatemanage:productmanager:exportproductasync', handle: (that) => that.onExport(), disabled: false },
  { label: '设置系列状态', permission: 'api:operatemanage:productmanager:BatchProductIsClassCodeCalc', handle: (that) => that.onHand(10) },
  { label: '手动同步产品', permission: 'api:operatemanage:productmanager:AddSyncShopGoodsJob_V2_TimeRange', handle: (that) => that.onShowTimeRange(11) },
  //{ label: '自动同步产品', permission: 'api:operatemanage:productmanager:AddSyncShopGoodsJob_V2_TimeRange2', handle: (that) => that.onShowProductsyncbyid() },
  { label: '运营同步宝贝', handle: (that) => that.operateSyncProductFromJst() },
  { label: '批量上下架', handle: (that) => that.bulkLoadingUn() },
  { label: '导出上下架日志', permission: 'api:operatemanage:productmanager:GetProductUpDownLogExport', handle: (that) => that.exportProps() },
  { label: '物流标签设置', permission: 'api:operatemanage:OnWlLableDialogShow', handle: (that) => that.onWlLableDialogShow() },
  // { label: '批量导入修改', permission: 'api:operatemanage:ProductManager:ImportBatchUpdateProduct' , handle: (that) => that.onBatchUpdateProduct() }
];
const tableHandles2 = [
  { label: '刷新',  handle: (that) => that.listUserRate() },
  { label: '新增人员',  handle: (that) => that.addUser() },
];
export default {
  name: 'Roles',
  components: {
    yhVxetable, MyContainer, MyConfirmButton, bindbianma, productcommhistory, inputYunhan,
    productchangelog, productReportPddOnlineForm, onSetTimeRange, YhImgUpload, productCategoryErrorLog,customerLogs,yhUserselectors
  },
  data() {
    return {
      downloadLink,
      popularOptions,
      popularOptions_more,
      showdrawer: false,
      showdrawerFlag: false,
      addUserDialogVisible:false,
      editUserDialogVisible:false,
      profitlist: [],
      tableCols2: [
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '200'},
        { istrue: true, prop: 'avatar', label: '头像', width: '150', type: "images" },
        { istrue: true, prop: 'userName', label: '姓名', width: '200' },
        { istrue: true, prop: 'regionName', label: '区域', width: '200' },
        { istrue: true, prop: 'deptName', label: '区域', width: '200' },
        { istrue: true, prop: 'post', label: '职位', width: '150' },
        { istrue: true, prop: 'shareRate', label: '分成比例', width: '150',formatter: (row) => row.shareRate + "%"  },
        { istrue: true, prop: 'modifiedUserName', label: '修改人', width: '200', formatter: (row) => row.modifiedTime ? row.modifiedUserName : row.createdUserName },
        { istrue: true, prop: 'modifiedTime', label: '修改时间', width: '200', formatter: (row) => row.modifiedTime ? row.modifiedTime : row.createdTime },
      ],
      tableHandles2:tableHandles2,
      projectList: [],
      remarkData: [],
      addprojNameDialog: {
        visible: false,
        projName: ''
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      logtimeRanges: [],
      derivationtime: {
        upDownOperateStartDate: null,
        upDownOperateEndDate: null
      },
      styleCodeList: [],
      star: null,
      flag: null,
      Batchstar: null,
      Batchflag: null,
      IsDamageLink: null,
      IsDamageLinkTime: null,
      inputtitle: '请分行输入ID',
      inputrow: 12,
      a: '宝贝ID',
      imageUrl: '',
      drawerprocid: false,
      drawerimage: false,
      exportracklogoutLogs: false,
      inputshow: 0,
      directionbtt: "btt",
      that: this,
      filter: {
        showBottomDrawer:false,
        remark: null,
        projName: null,
        isFreeStream: null,
        name: '',
        styleCode: null,
        brandRate: null,
        categoryids: [],
        categoryids1: [],
        timerange: [],
        onlineStartTime: null,
        onlineEndTime: null,
        pinPai: null,
        labels: null,
        snProCode: null,
        stratifyDetail: {
          saleVolumeStratifyDetail:[],
          grossProfitStratifyDetail:[],
          netProfitStratifyDetail:[],
          profit4StratifyDetail:[],
          profit5StratifyDetail:[],
        },
        addUser:{
          type:'1',
          userId: null,
          shareRate: null,
        },
        editUser:{
        }
      },
      productShareDetail:[],
      styleCode: null,
      historyTime: null,
      platformlist: platformlist,
      productlist: [],
      summaryarry:[],
      directorList: [],
      directorListUserType: [],
      addRateUserList: [],
      directorGroupList: [],
      shopList: [],
      bandList: [],
      categorylist: [],
      brandlist: [],
      options: [],
      pager: { OrderBy: "id", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      tableHandles2:tableHandles2,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        //options:{onSubmit:(formData)=>{alert(JSON.stringify(formData))}},
        //options:{submitBtn:false},
        rule: []
      },
      total: 0,
      sels: [],
      selids: [],
      IsProtectCornerId: '',
      selprocodes: [],
      bindproductId: 0,
      listLoading: false,
      listLoading2: false,
      isinputtrue: true,
      pageLoading: false,
      addFormVisible: false,
      onShowTimeRangeVisible: false,
      addLoading: false,
      batchEditFormVisible: false,
      batchEditFormLoading: false,
      shopListbetch: [],
      spellmoreCheckbox: [],
      batchEditFormData: {
        ids: "",
        remark: null,
        platform: null,
        platform_Checked: null,
        shopId: null,
        shopId_Checked: null,
        groupId: null,
        groupId_Checked: null,
        operateSpecialUserId: null,
        operateSpecialUserId_Checked: null,
        userId: null,
        userId_Checked: null,
        userId2: null,
        userId2_Checked: null,
        userId3: null,
        userId3_Checked: null,
        star: null,
        star_Checked: null,
        flag: null,
        flag_Checked: null,
        isBanner: null,
        IsBanner_Checked: null,
        isSku: null,
        IsSku_Checked: null,
        isVideo: null,
        IsVideo_Checked: null,
        isDetailmap: null,
        IsDetailmap_Checked: null,
        IsProjName_Checked: null,
        IsBaoPin_Checked: null,
        Isremark_Checked: null,
        upNewOpen: null,
        IsUpNewOpen_Checked: null,
      },

      deleteLoading: false,
      dialogFormVisible: false,
      formtitle: "新增",
      onHandNumber: 0,
      pager: { OrderBy: "id", IsAsc: false },
      importFilte: { platform: "" },
      dialogVisible1: false,
      dialogVisible2: false,
      dialogVisible3: false,
      dialogVisible4: false,
      dialogVisible5: false,
      fileList5: [],
      uploadLoading: false,
      radio: '',
      searchloading: false,
      logDialogVisible: false,
      logfilter: {
        proCode: null
      },
      onlineGoods: {
        visible: false,
        filter: {
          productCode: null,
          groupId: null
        }
      },
      oneNoticeDialog: {
        title: "一键通知",
        visible: false,
        formLoading: false,
        formRules: {
          noticeTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
          noticeContext: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        },
        oneNoticeFormData: {
          noticeTitle: "",
          noticeContext: "",
          noticeImgs: "",
          seriesName: ""
        }
      },
      SettingsDamageLinkDialog: {
        visible: false,
        filter: {
          proCode: null,
          platform: null,
          id: null
        }
      },
      autoformparm: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      mainCategoriesList: mainCategoriesListConst,
      propertyList: propertyListConst,

      createdChatNew: {
        visible: false,
        checkOnlyYunYing:false,
        createGroupChatReason: "",
      },
      createGroupChatDialog: {
        title: "建群日志",
        visible: false,
        logLoading: false,
        logList: [],
      },
      editparmLoading: false,
      groupIdBatchEdit: false,//包含抖音和拼多多时不允许编辑运营组
      pinPailist: [],
      categoryLogDialog: {
        visible: false,
        filter: {
          productIds: null
        }
      },
      wlLableDialog:{
        visiable:false,
        wlKeyName:"",
        wlKeyNameList:[]
      },
      resetUserRateFlag: false,
      dialogVisible12: false,
      fileList12: [],
      colOptions: [],
      colSelect: ["运营组", "运营专员", "运营助理", "产品专员", "产品助理", "星星", "旗帜"],
      customerInfo:{
        platform:null,
        shopName:null,
        shopCode:null,
        groupType:null,
      },
      KFlogVisible:false,
    }
  },
  async created() {
    await this.initformparm();
  },
  mounted() {
    if (this.$route.query) {
      this.filter.platform = this.$route.query.Platform;
      this.platformlist.map((item) => {
        if (item.value == this.$route.query.Platform) {
          this.filter.platform = item.value;
        }
      })
      this.filter.groupId = this.$route.query.groupId
      this.filter.operateSpecialId = this.$route.query.operateSpecialUserId
      this.filter.user1Id = this.$route.query.userId
    }





    this.getDirectorlist()
    this.init()
    if(!middlevue.$on){
        this.onSearch()
    }

    middlevue.$on('proCodeParams', (data) => {
        console.log("执行几次",data)
        this.filter.procode = data.proCode;
        if(this.filter.procode){
            setTimeout(()=>{
                this.onSearch();
            },50)
        }

    })
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    openKFlog(row){
      this.customerInfo = {
        platform:row.platform,
        shopName:row.shopName,
        shopCode:row.shopCode,
      }
      this.KFlogVisible= true;
    },
    async getStratifyDetailStatistic (row, commissionRate,stratifyType,type) {
        this.productShareDetail = [];
        console.log("commissionRate",commissionRate);
        if(!commissionRate || commissionRate==0) return;
        const res = await getProductShareDetailList(row.proCode, type);
        if (!res?.success) return;
        this.productShareDetail = res.data
    },
    async listUserRate() {
      this.listLoading2 =  true;
      let params ={
        ProductIds: this.selprocodes.join(''),//this.selprocodes.join(',')
        type: this.filter.addUser.type
      };
      console.log("params",params);
      const res = await getProductShareDetailList(this.selprocodes.join(','), this.filter.addUser.type);
      if (!res?.success) return;
      this.profitlist = res.data;
      console.log("profitlist",this.profitlist);
      this.listLoading2 =  false;
    },
    async handleAddRateUserList(value) {
      //加载用户列表
      const res = await getDirectorListWithDdInfo({userName: value});
      if (!res?.success) return;
      this.addRateUserList = res.data;
    },
    verifyNumberPercentage(val, type) {
      // 匹配空格
      let v = val.replace(/(^\s*)|(\s*$)/g, '');
      // 只能是数字和小数点，不能是其他输入
      v = v.replace(/[^\d.]/g, '');
      // 以0开始只能输入一个
      v = v.replace(/^0{2}$/g, '0');
      // 保证第一位只能是数字，不能是点
      v = v.replace(/^\./g, '');
      // 小数只能出现1位
      v = v.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // 小数点后面保留2位
      v = v.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
      // 数字超过100，赋值成最大值100
      v = v.replace(/^[1-9]\d\d{1,3}$/, '100');
      // 超过100之后不给再输入值
      v = v.replace(/^100\.$/, '100');
      if ('add' == type) {
        this.filter.addUser.shareRate = v;
      } else {
        this.filter.editUser.shareRate = v;
      }
    },
    async addUser() {
      this.filter.addUser.userId = null;
      this.filter.addUser.shareRate = null;
      this.addUserDialogVisible =  true;
      //加载用户列表
      this.handleAddRateUserList('');
    },
    async handleEditShareClose() {
      this.showdrawer =  false;
      //刷新列表
      this.getProductlist();
    },
    async editUser(row) {
      this.handleAddRateUserList('');
      this.addRateUserList.push({"value":row.userName,"thirdId":row.userId,"key":"0"});
      this.filter.editUser = row;
      this.editUserDialogVisible =  true;
      //加载用户列表
    },
    async deleteUser(row) {
      this.$confirm("确定删除用户分成吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
        async () => {
        const res = await deleteProductShareDetailById(row.id);
        if (!res?.success) return
        {
            //刷新列表
            this.listUserRate();
            this.$message({ message: '删除成功', type: "success" });
        }
        });
    },
    //批量新增用户分成
    async addUserComfirm(){
      if(this.resetUserRateFlag){
        //调用清除
        this.clearUserRate(0);
      }
      let params ={
          ...this.filter.addUser,
          type:this.filter.addUser.type,
          proCodes:this.selprocodes.join(',')
      };
      console.log("addUserComfirm.paras",params);
      const res = await batchInsertProductShare(params);
      if (!res?.success) return
      if (res.data && res.data.length > 0) {
        this.$message({ dangerouslyUseHTMLString: true, message: res.data, type: "error" });
      } else {
        this.$message({ message: '添加成功', type: "success" });
      }
      this.addUserDialogVisible =  false;
      //刷新列表
      this.listUserRate();
    },
    //编辑用户分成
    async editUserComfirm(){
      let params ={
          ...this.filter.editUser
      };
      console.log("addUserComfirm.paras",params);
      const res = await updateProductShare(params);
      if (!res?.success) return
      this.editUserDialogVisible =  false;
      //刷新列表
      this.listUserRate();
    },
    //批量清除用户分成
    async clearUserRate(type){
      if(this.selprocodes.length==0){
        this.$message.error('请选择宝贝ID');
        return;
      }
      this.$confirm("确定清除用户分成吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
        async () => {
          if(type){
            type = this.filter.addUser.type;
          }else type = 0;
        let params ={
            proCodes:this.selprocodes.join(','),
            type:type
        };
        console.log("clearUserRate.paras",params);
        const res = await clearProductShareDetail(this.selprocodes.join(','), type);
        if (!res?.success) return
        {
            //刷新列表
            this.getProductlist();
            this.$message({ message: '清除成功', type: "success" });
        }
        });

    },
    //批量替换用户分成
    async resetUserRate(){
      if(this.selids.length==0){
        this.$message.error('请选择宝贝ID');
        return;
      }
      let params ={
          ProductIds:this.selids.join(',')
      };
      console.log("resetUserRate.paras",params);
      this.addUserDialogVisible =  true;
      this.resetUserRateFlag =  true;
      //加载用户列表
      this.handleAddRateUserList('');
    },
    async addWLCompany() {
      if (!this.addprojNameDialog.projName) {
        this.$message.error('项目名称不能为空');
        return;
      }
      const { data } = await addProductProject({ projName: this.addprojNameDialog.projName })
      if (data) {
        this.$message.success('新增成功');
        await this.onGetItemMethod();
      } else {
        this.$message.error('新增失败');
      }
      this.addprojNameDialog.visible = false;
    },
    onAdditemmethod() {
      this.addprojNameDialog.projName = null
      this.addprojNameDialog.visible = true;
    },
    filterMethod(e, val) {
      console.log(e, 'e');
      console.log(val, 'val');
    },
    async changeCategory(platform, val) {
      if (this.filter.platform === null || this.filter.platform === undefined) {
        this.$message.error('请先选择平台');
        return;
      }
      let parentId = null
      if (val && val.length > 0) {
        parentId = val[0];
        if (val.length > 1)
          parentId = val[1];
        if (val.length > 2)
          parentId = val[2];
        if (val.length > 3)
          parentId = val[3];
        if (val.length > 4)
          parentId = val[4];
      }
      const { data } = await getListByPId({ platform, parentId })
      if (val.length == 1) {
        this.$set(this.categorylist[0], 'children', data.map(item => {
          return {
            id: item.id,
            value: item.id,
            label: item.categoryName
          }
        }))
      } else {
        if (data.length > 0) {
          this.addChildren(this.categorylist[0].children, parentId, data)
        }
      }
    },
    addChildren(list, parentId, data) {
      //递归匹配到对应的数据,并且添加到对应的children中
      list.map(item => {
        if (item.id == parentId) {
          this.$set(item, 'children', data.map(item => {
            return {
              id: item.id,
              value: item.id,
              label: item.categoryName
            }
          }))
        } else {
          if (item.children && item.children.length > 0) {
            this.addChildren(item.children, parentId, data)
          }
        }
      })
    },
    async changeTime(e) {
      this.logtimeRanges = []
      const startDate = new Date(e[0]);
      const endDate = new Date(e[1]);
      const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      if (diffDays > 7) {
        this.$message.error('日期区间不能超过7天,已为您自动调整为7天');
        const newEndDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);
        this.logtimeRanges = [startDate, newEndDate];
      } else {
        this.logtimeRanges = e;
      }
      this.derivationtime.upDownOperateStartDate = this.logtimeRanges ? formatTime(this.logtimeRanges[0], "YYYY-MM-DD") : null
      this.derivationtime.upDownOperateEndDate = this.logtimeRanges ? formatTime(this.logtimeRanges[1], "YYYY-MM-DD") : null
    },
    async exportProps() {
      this.logtimeRanges = []
      this.derivationtime.upDownOperateStartDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.derivationtime.upDownOperateEndDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.logtimeRanges = [this.derivationtime.upDownOperateStartDate, this.derivationtime.upDownOperateEndDate]
      this.exportracklogoutLogs = true;
    },
    async onExportLogs() {
      this.listLoading = true;
      const paramsList = this.getprarms();
      paramsList.platform = 2;//拼多多
      const params = { ...paramsList, ...this.derivationtime }
      const { data } = await getProductUpDownLogExport(params)
      this.exportracklogoutLogs = false;
      this.listLoading = false;
    },
    bulkLoadingUn() {
      //保留(拼多多/天猫/淘宝/抖音/京东/快手)的数据
      let oldLength = this.spellmoreCheckbox.length;
      this.spellmoreCheckbox = this.spellmoreCheckbox.filter(item => {
        return (item.platform === 1 || item.platform === 2 || item.platform === 6 || item.platform === 7 || item.platform === 9 || item.platform === 14);
      });
      if (this.spellmoreCheckbox.length < oldLength && this.spellmoreCheckbox.length == 0) {
        this.$message.error('未选择(拼多多/天猫/淘宝/抖音/京东/快手)的数据!');
        return;
      }
      else if (this.spellmoreCheckbox.length < oldLength) {
        this.$message({ type: 'warning', message: '已过滤掉平台非(拼多多/天猫/淘宝/抖音/京东/快手)的数据，正在加载编辑页...' });
        setTimeout(() => {
          this.bulkLoadingUnloading();
        }, 2000);
        return;
      }
      else if (this.spellmoreCheckbox.length == 0) {
        this.$message.error('请选择平台为(拼多多/天猫/淘宝/抖音/京东/快手)的数据!');
        return;
      }
      //数据只有拼多多的数据
      this.bulkLoadingUnloading();
    },
    bulkLoadingUnloading() {
      this.$showDialogform({
        path: `@/views/base/batchListingDelist.vue`,
        title: '批量上下架',
        autoTitle: false,
        args: {
          checkdata: this.spellmoreCheckbox
        },
        height: '650px',
        width: '80%',
      })
    },
    isDamageLinkClick(row) {
      this.SettingsDamageLinkDialog.visible = true;
      this.SettingsDamageLinkDialog.filter.proCode = row.proCode;
      this.SettingsDamageLinkDialog.filter.platform = row.platform;
      this.SettingsDamageLinkDialog.filter.id = row.id;
    },
    async onSettingsDamageLink() {
      this.editparmLoading = true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformparm.fApi.formData();
          formData.platform = this.SettingsDamageLinkDialog.filter.platform;
          formData.proCode = this.SettingsDamageLinkDialog.filter.proCode;
          formData.isDayReport = true;
          const res = await updateDamageLink(formData);
          if (res.code == 1) this.SettingsDamageLinkDialog.visible = false;
          this.SettingsDamageLinkDialog.filter.proCode = null;
          this.SettingsDamageLinkDialog.filter.platform = null;
          this.SettingsDamageLinkDialog.filter.id = null;
        } else {
          this.editparmLoading = false;
          await this.autoform.fApi.resetFields()
        }
      })
      this.editparmLoading = false;
      await this.autoform.fApi.resetFields()
    },
    async initformparm() {
      let that = this;
      this.autoformparm.rule = [
        { type: 'select', field: 'IsDamageLink', title: '是否货损链接', options: [{ value: true, label: '是', }, { value: false, label: '否', }], props: { clearable: true }, validate: [{ type: 'boolean', required: true, message: '请设置货损链接' }] },
        { type: 'DatePicker', field: 'IsDamageLinkTime', title: '货损截止日期', value: '', validate: [{ type: 'string', required: true, message: '请输入货损链接截止日期' }], props: { type: 'date', format: 'yyyy-MM-dd', placeholder: '货损链接截止日期' }, col: { span: 8 } },
      ]

      var res6 = await getProductPinPaiList();
      this.pinPailist = res6.data.map(item => {
        return { value: item.pinPai, label: item.pinPai };
      });
    },
    changeplatform(val) {
      this.batchEditFormData.shopId = null;
      getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 }).then(res => {
        if (res.success) {
          this.shopListbetch = res.data.list
        }
      });
    },
    batchEditFormSubmit() {
      this.$refs.batchEditForm.validate((valid) => {
        if (valid) {
          this.batchEditFormLoading = true
          let form = { ...this.batchEditFormData }
          if (form.platform == null) {
            form.platform = 0;
            if (form.platform_Checked) {
              this.$message({ message: "请选择平台", type: "warning", });
              this.batchEditFormLoading = false
              return
            }
          }
          if (form.shopId == null) {
            form.shopId = 0;
            if (form.shopId_Checked) {
              this.$message({ message: "请选择店铺", type: "warning", });
              this.batchEditFormLoading = false
              return
            }
          }
          if (form.groupId == null) {
            form.groupId = 0;
            if (form.groupId_Checked) {
              this.$message({ message: "请选择组长", type: "warning", });
              this.batchEditFormLoading = false
              return
            }
          }
          if (form.platform_Checked ||
            form.shopId_Checked ||
            form.groupId_Checked ||
            form.operateSpecialUserId_Checked ||
            form.userId_Checked ||
            form.userId2_Checked ||
            form.userId3_Checked ||
            form.star_Checked ||
            form.flag_Checked ||
            form.IsBanner_Checked ||
            form.IsDetailmap_Checked ||
            form.IsProjName_Checked ||
            form.IsBaoPin_Checked ||
            form.Isremark_Checked ||
            form.IsSku_Checked ||
            form.IsVideo_Checked || 
            form.IsUpNewOpen_Checked) {
            batchUpdateProduct(form).then(res => {
              this.batchEditFormLoading = false
              if (res.success) {
                // this.$message({ message: "提交成功", type: "success", });
                this.$message({ message: res.msg, type: "warning" })
                this.batchEditFormVisible = false;
                this.getProductlist();
              }
            });
          } else {
            this.batchEditFormLoading = false
            this.$message({ message: "没有任何勾选更新内容", type: "info", });
          }
        }
      })

    },
    copyProcode() {
      let data = [];
      this.productlist.forEach(a => {
        data.push(a.proCode);
      })
      this.doCopy(data.join(','))
    },
    doCopy: function (val) {
      let that = this;
      this.$copyText(val).then(function (e) {
        that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
        that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },

    async updateIscompete(row) {
      let par = {
        proCode: row.proCode,
        isCompete: row.isCompete == 1 ? 0 : 1
      };
      let res = await updateProductIsCompeteAsync(par);
      if (res.data) {
        this.$message({ message: "修改成功！", type: "success", });
      }
      this.getProductlist()
    },
    selectfuc(val) {
      this.$forceUpdate()
    },
    async onHandprocid() {
      this.drawerprocid = true;
    },
    async getcategorylist(platform) {
      const res = await getListByPId({ platform: this.filter.platform })
      if (!res?.code) {
        return
      }
      const list = [];
      res.data.forEach(f => {
        f.label = f.categoryName;
        list.push(f)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        value: '0',
        label: '所有'
      })
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    async getDirectorlist() {
      const res1 = await getDirectorList({})
      const res2 = await getDirectorGroupList({})
      const res3 = await getProductBrandPageList()

      this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
      this.directorListUserType = [];
      res1.data.forEach(f => {
        if (f.userType)
          this.directorListUserType.push(f);
      });

      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
      this.bandList = res3.data?.list
    },
    async onchangeplatform(val) {
      this.categorylist = []
      this.filter.categoryids1 = []
      this.filter.categoryids = []
      this.filter.platform = val;
      const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.categorylist = [{
        id: '0',
        value: '0',
        label: '所有'
      }]
      this.shopList = res1.data.list
      // if (val) this.getcategorylist(val)
      //const res2 = await getcategoryList({platform:val,CurrentPage:1,PageSize:100});
      //this.productCategorylist=res2.data
    },
    //回车批量查询
    // batchOnSearCh(val) {
    //   var _this = this;
    //   console.log('条件', _this.filter.procode)
    //   if  (_this.filter.procode == '' || _this.filter.procode == null || _this.filter.procode == undefined) {
    //     console.log('进入判断')
    //     console.log('进入判断',this.inputshow)
    //     _this.inputshow = this.inputshow+1
    //     return;
    //   }else {
    //     _this.inputshow = 0;
    //     }
    //     _this.filter.procode =val;
    //     _this.onSearch();
    // },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getProductlist()
    },
    //批量数据组合，回调数据
    async callback(val) {
      this.filter.procode = val
      this.$refs.pager.setPage(1)
      if(val){
        this.getProductlist()
      }
    },
    async snProCodeBack(val){
      this.filter.snProCode =val
    },
    async callbackStyleCode(val) {
      this.filter.styleCode = val
    },
    //清空数据
    async clearableasync() {

      var _this = this;
      _this.isinputtrue = false;
      if (_this.filter.procode == '' || _this.filter.procode == null) {
        _this.isinputtrue = true;
      }
      console.log(_this.filter)
      await this.onSearch()
    },
    getprarms() {
      var pager = this.$refs.pager.getPager()
      if (this.filter.categoryids1.length > 0) {
        this.$set(this.filter, 'categoryids', JSON.parse(JSON.stringify(this.filter.categoryids1)))
        this.filter.categoryids[0] = ''
        this.$set(this.filter.categoryids, 0, '')
      }
      if (this.filter.categoryids != null && this.filter.categoryids.length > 0) {
        this.filter.productCategoryId = this.filter.categoryids[this.filter.categoryids.length - 1]
      }
      this.filter.onlineStartTime = null;
      this.filter.onlineEndTime = null;
      if (this.filter.timerange) {
        this.filter.onlineStartTime = this.filter.timerange[0];
        this.filter.onlineEndTime = this.filter.timerange[1];
      }
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter
      }
      return params;
    },
    async getProductlist() {
      const params = this.getprarms();
      this.listLoading = true
      const res = await getPageProductList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.productlist = data;
      this.summaryarry = res.data.summary;
      this.selids = []
      this.spellmoreCheckbox = []
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async updateruleshop(platformid) {
      await this.autoform.fApi.setValue({ shopId: null })
      await this.autoform.fApi.updateRule('shopId', { ... await ruleShop(platformid) })
      await this.autoform.fApi.sync('shopId')
    },
    async updaterulecategory(platformid) {
      await this.autoform.fApi.setValue({ productCategoryId: '' })
      await this.autoform.fApi.updateRule('productCategoryId', { ... await ruleProductCategory(platformid) })
      await this.autoform.fApi.sync('productCategoryId')
    },
    async onHand(number) {
      this.addFormVisible = true
      this.onHandNumber = number
      if ((number == 2 || number == 3 || number == 4 || number == 5 || number == 6 || number == 7 || number == 8 || number == 9 || number == 10 || number == 11 || number == 12) && this.selids.length == 0) {
        this.$message({ message: "请先选择", type: "warning", });
        this.radio = ''
        this.addFormVisible = false
        return
      }
      else if ((number == 2 || number == 6) && this.selids.length > 1) {
        this.$message({ message: "只能选择1行", type: "warning", });
        this.addFormVisible = false
        return
      }
      if (number == 1) {
        this.autoform.rule = await getformaddsinglerule(this);
        this.formtitle = "新增"
      }
      else if (number == 2) {
        let getrule = await getformaddsinglerule(this);
        this.autoform.rule = getrule;
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          var res = await getProductById(this.selids[0]);
          res.data.operateSpecialUserId = res.data.operateSpecialUserId == "0" ? "" : res.data.operateSpecialUserId
          res.data.userId = res.data.userId == "0" ? "" : res.data.userId
          res.data.userId2 = res.data.userId2 == "0" ? "" : res.data.userId2
          res.data.userId3 = res.data.userId3 == "0" ? "" : res.data.userId3
          res.data.platform = res.data.platform == 0 ? null : res.data.platform
          res.data.groupId = res.data.groupId == "0" ? '' : res.data.groupId
          res.data.shopId = res.data.shopId == "0" ? null : parseInt(res.data.shopId)
          res.data.status = res.data.status == 0 ? null : res.data.status
          this.star = res.data.star
          // this.IsDamageLink = res.data.isDamageLink,
          // this.IsDamageLinkTime=res.data.isDamageLinkTime,
          this.flag = res.data.flag
          await this.autoform.fApi.disabled(true, 'proCode')
          await this.autoform.fApi.disabled(true, 'title')
          // if(res.data.platform==6){
          //   this.autoform.rule.find(w=>w.field=="groupId").validate=[];
          //   await this.autoform.fApi.disabled(true, 'groupId')
          // }
          await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue(res.data)
        }
        this.formtitle = "单个修改"
      }
      else if (number == 3) {
        // this.autoform.rule=await getformeditbatchrule(this);
        // var arr = Object.keys(this.autoform.fApi);
        // if(arr.length >0) {
        //    await this.autoform.fApi.resetFields()
        //    await this.autoform.fApi.setValue({ids:this.selids.join()})
        // }
        // this.formtitle="批量修改"
        this.addFormVisible = false
        this.batchEditFormVisible = true;
        this.groupIdBatchEdit = false;
        let platform26 = this.spellmoreCheckbox.find(f => f.platform == 6);
        if (platform26) {
          this.groupIdBatchEdit = true;
        }
        this.batchEditFormData = {
          ids: "",
          platform: null,
          platform_Checked: null,
          shopId: null,
          shopId_Checked: null,
          groupId: null,
          groupId_Checked: null,
          operateSpecialUserId: null,
          operateSpecialUserId_Checked: null,
          userId: null,
          userId_Checked: null,
          userId2: null,
          userId2_Checked: null,
          userId3: null,
          userId3_Checked: null,
          star: null,
          IsDamageLink: null,
          isDamageLinkTime: null,
          star_Checked: null,
          flag: null,
          flag_Checked: null,
          isBanner: null,
          IsBanner_Checked: null,
          isSku: null,
          IsSku_Checked: null,
          isVideo: null,
          IsVideo_Checked: null,
          isDetailmap: null,
          IsDetailmap_Checked: null,
          IsProjName_Checked: null,
          Isremark_Checked: null,
          IsBaoPin_Checked:null,
          IsUpNewOpen_Checked: null,
        },
          // this.$nextTick(() => {
          //   this.$refs.batchEditForm.resetFields();
          // });
          this.batchEditFormData.ids = this.selids.join();
      }
      else if (number == 4) {
        // this.autoform.rule = await getformcommissionbatchrule();
        // var arr = Object.keys(this.autoform.fApi);
        // this.historyTime = null
        // if (arr.length > 0) {
        //   await this.autoform.fApi.resetFields()
        //   await this.autoform.fApi.setValue({ ids: this.selids.join(), procodes: this.selprocodes.join() })
        // }
        // this.formtitle = "批量设置提成比例"
        this.filter.addUser.type = '1';
        this.searchProductShareDetails();
      }
      else if (number == 5) {
        this.autoform.rule = await getformproductCidbatchrule(this);
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue({ ids: this.selids.join() })
        }
        this.formtitle = "批量设置平台及类目"
      }
      else if (number == 11) {

        this.addFormVisible = false
        await this.onHandSubmit()

      }
      else if (number == 12) {

        this.addFormVisible = false
        await this.onHandSubmit()

      }
      else if (number == 13) {
        if (this.selids.length == 0) {
          this.$message({ message: "请先选择", type: "warning", });
          this.radio = ''
          this.addFormVisible = false
          return
        }
        this.autoform.rule = await getTagrule();
        var arr = Object.keys(this.autoform.fApi)
        if (arr.length > 0) {
          await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue({ ids: this.selids.join() })
        }
        this.formtitle = "批量修改标识"
      }
      // else if (number==13) {
      //  this.drawerimage=true
      //  this.addFormVisible = false
      //  await this.onHandSubmit()

      // }
      else if (number == 6) {
        this.addFormVisible = false
        this.bindproductId = this.selids[0]
        //await this.$refs.bindbianma.Init(this.selids[0]);
        this.dialogFormVisible = true
        return
      }
      else if (number == 7) {
        this.addFormVisible = false
        await this.onHandSubmit()
      }
      else if (number == 8) {
        this.autoform.rule = await getformproductMaskrule();
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0) {
          await this.autoform.fApi.setValue({ ids: this.selids.join() })
        }
        this.formtitle = "批量设置标签"
      }
      else if (number == 9) {
        this.autoform.rule = await getformproductBrandrule();
        var arr = Object.keys(this.autoform.fApi)
        if (arr.length > 0) {
          await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue({ ids: this.selids.join() })
        }
        this.formtitle = "批量设置采购负责人"
      }
      else if (number == 10) {
        this.autoform.rule = await getformeditstylerule();
        var arr = Object.keys(this.autoform.fApi)
        var istrue;
        if (this.selids.length > 1) {
          istrue = true;
        } else if (this.selids.length == 1) {
          var res = await getProductById(this.selids[0]);
          if (res?.data != null) {
            istrue = res.data.isClassCodeCalc
          }
        }
        if (arr.length > 0) {
          await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue({ ids: this.selids.join(), isClassCodeCalc: istrue })
        }
        this.formtitle = "设置是否参与系列编码计算"
      }
      var arr = Object.keys(this.autoform.fApi);
      if (arr.length > 0)
        this.autoform.fApi.reload()
    },
    async searchProductShareDetails() {
      this.addFormVisible = false;
      this.showdrawer = true;
      this.showdrawerFlag = true;
      this.listLoading2 = true;
      let params = {
        proCodes: this.selprocodes.join(','),
        type: this.filter.addUser.type
      };
      console.log("params", params);
      const res = await getProductShareDetailList(this.selprocodes.join(','), this.filter.addUser.type);
      if (!res?.success) return;
      this.profitlist = res.data;
      console.log("profitlist", this.profitlist);
      this.listLoading2 = false;
    },
    async onEdit(row) {
      this.formtitle = '编辑';
      this.addFormVisible = true
      const res = await getbyid(row.id)
      await this.autoform.fApi.setValue(res.data)
    },
    async onHandSubmit() {
      this.addLoading = true;
      this.dialogVisible3 = false;

      if (this.onHandNumber == 11) {
        var isproconid = this.IsProtectCornerId = 1;
        this.$confirm(`确认设置${this.selids.length}行数据, 是否继续?`, '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
          .then(async () => {
            console.log('this.selids.join()', this.selids.join())
            const res = await batchIsProtectCornerId({ ids: this.selids.join(), IsProtectCornerId: isproconid })
            if (!res?.code) { return }
            this.$message({ type: 'success', message: '设置护墙角ID成功!' });
            this.radio = ''
            await this.getProductlist()
          }).catch(() => {
            this.$message({ type: 'info', message: '已取消设置' });
            this.radio = ''
          });
        this.addLoading = false;
        return
      }
      if (this.onHandNumber == 12) {
        var noisproconid = this.IsProtectCornerId = 0;
        this.$confirm(`确认取消${this.selids.length}行数据, 是否继续?`, '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
          .then(async () => {
            console.log('this.selids.join()', this.selids.join())
            const res = await batchIsProtectCornerId({ ids: this.selids.join(), IsProtectCornerId: noisproconid })
            if (!res?.code) { return }
            this.$message({ type: 'success', message: '取消设置护墙角ID成功!' });
            this.radio = ''
            await this.getProductlist()
          }).catch(() => {
            this.$message({ type: 'info', message: '已取消' });
            this.radio = ''
          });
        this.addLoading = false;
        return
      }
      if (this.onHandNumber == 7) {
        this.$confirm(`确认删除${this.selids.length}行数据, 是否继续?`, '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
          .then(async () => {
            console.log('this.selids.join()', this.selids.join())
            const res = await batchDeleteProduct({ ids: this.selids.join() })
            if (!res?.code) { return }
            this.$message({ type: 'success', message: '操作成功!' });
            await this.getProductlist()
          }).catch(() => {
            this.$message({ type: 'info', message: '已取消操作' });
          });
        this.addLoading = false;
        return
      }
      await this.autoform.fApi.validate(async (valid, fail) => {
        this.addLoading = true;
        if (valid) {
          // if((this.IsDamageLink!=null&&this.IsDamageLinkTime==null)||(this.IsDamageLink==null&&this.IsDamageLinkTime!=null))
          // {
          //   this.$message({ message: "货损状态和货损截止日期必填", type: "warning" });
          //   return;
          // }
          const formData = this.autoform.fApi.formData()
          if (this.onHandNumber == 2 && formData.remark) {
            formData.remark = formData.remark.replace(/\s+/g, "");
          }
          formData.star = this.star
          // formData.IsDamageLink = this.IsDamageLink
          // formData.IsDamageLinkTime = this.IsDamageLinkTime
          formData.flag = this.flag
          if (this.onHandNumber == 1 || this.onHandNumber == 2) {
            let saveRes = await addorEditProduct(formData);
            if (saveRes.success) {
              this.addFormVisible = false
              this.$message({ message: saveRes.msg, type: "success" })
            }
          }
          else if (this.onHandNumber == 3) {
            let saveRes = await batchUpdateProduct(formData)
          }
          else if (this.onHandNumber == 4) {
            formData.historyTime = this.historyTime
            console.log('时间', formData)
            await batchUpdateProductCommission(formData)
          }

          else if (this.onHandNumber == 5) {
            formData.productCategoryId = formData.productCategoryId.pop()
            await batchUpdateProductCid(formData)
          }
          else if (this.onHandNumber == 6) {

          }
          else if (this.onHandNumber == 8) {
            formData.mask = formData.mask.join()
            await batchUpdateProductMask(formData)
          }
          else if (this.onHandNumber == 9) {
            console.log('输出参数', formData)
            await batchUpdateBrand(formData)
          }
          else if (this.onHandNumber == 13) {
            formData.star = this.Batchstar
            formData.flag = this.Batchflag
            await batchUpdateTag(formData)
            this.Batchstar = null
            this.Batchflag = null
          }
          else if (this.onHandNumber == 10) {
            await BatchProductIsClassCodeCalc(formData)
          }
          //  else if (this.onHandNumber==13){

          // }

          if (this.onHandNumber != 2 && this.onHandNumber != 1) {
            this.addFormVisible = false
          }
          this.getProductlist()
        } else {
          //todo 表单验证未通过
          this.$message({ message: '请规范所填信息或必填项', type: 'warning' });
        }
        this.addLoading = false;
      })
      //this.addLoading = false;
    },
    deleteValidate(row) {
      let isValid = true
      if (row && row.name === 'admin') {
        this.$message({
          message: row.description + '，禁止删除！',
          type: 'warning'
        })
        isValid = false
      }
      return isValid
    },
    async onDelete(row) {
      row._loading = true
      const res = await deletebyid(row.id)
      row._loading = false
      if (!res?.success) {
        return
      }
      this.$message({
        message: this.$t('admin.deleteOk'),
        type: 'success'
      })
      this.getProductlist()
    },
    async onSaveBindbianma() {
      this.dialogFormVisible = false
      var bindids = await this.$refs.bindbianma.getBindIds();
      var parm = { productId: this.selids[0], bianMaIdsList: bindids }
      await saveProBianMa(parm);
    },
    async init() {
      var res = await getAllProBrand();
      this.brandlist = res.data.map(item => {
        return { value: item.key, label: item.value };
      });
      await this.onGetItemMethod();
    },
    async onGetItemMethod() {
      const { data, success } = await getProductProjectList({ projName: '' });
      if (success) {
        this.projectList = data;
      }
      return
      const { data: data1, success: success1 } = await getProductRemarkList();
      if (success1) {
        this.remarkData = data1;
      }
    },
    async onShowTimeRange() {
      this.onShowTimeRangeVisible = true;
      this.$nextTick(async () => {
        await this.$refs.onSetTimeRange.onSearch();
      })

    },
    selsChange: function (sels) {
      this.sels = sels
    },
    async onDownShareRate() {
      downloadLink("https://nanc.yunhanmy.com:10020/common/20241219/1869548634787430400.xlsx", "商品分成比例导入模板.xlsx");
    },
    async onDownDirector() {
      let url = "../static/excel/operation/商品负责人匹配导入模板.xlsx";
      let link = document.createElement("a");
      let fileName = "商品负责人匹配导入模板" + ".xlsx";
      document.body.appendChild(link);
      link.href = url;
      link.dowmload = fileName;
      link.click();
      link.remove();
    },
    async onDownBianMaBind() {
      let url = "../static/excel/operation/商品编码匹配导入模板.xlsx";
      let link = document.createElement("a");
      let fileName = "商品编码匹配导入模板" + ".xlsx";
      document.body.appendChild(link);
      link.href = url;
      link.dowmload = fileName;
      link.click();
      link.remove();
    },
    async onImportDirector() { this.dialogVisible1 = true },
    async onImportBianMaBind() { this.dialogVisible2 = true },
    async onSubmitupload1() {
      if (!this.importFilte.platform) {
        this.$message({ message: "请选择平台！", type: "warning" });
        return;
      }
      this.$refs.upload1.submit()
    },
    async onSubmitupload2() { this.$refs.upload2.submit() },
    async uploadSuccess1(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async uploadSuccess2(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async onImportProductShare() {
      this.dialogVisible5 = true
    },
    async onSubmitupload5() {
      if(!this.fileList5||this.fileList5.length==0){
        this.$message({message: "请先选择文件", type: "warning" });
        return false;
      }
      this.uploadLoading=true
      this.$refs.upload5.submit()
    },
    async uploadChange5(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList5 = list;
      }
    },
    async uploadRemove5(file, fileList){
       this.uploadChange5(file, fileList);
    },
    async uploadSuccess5(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async uploadFile1(item) {
      const form = new FormData();
      form.append("platform", this.importFilte.platform);
      form.append("upfile", item.file);
      const res = importProductDirector(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importProductBianMaBind(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    async uploadFile5(item) {
      if(!item||!item.file||!item.file.size){
        this.$message({message: "请先选择文件", type: "warning" });
        this.uploadLoading=false
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      const res = await ImportProductShare(form);
      if (res?.success) {
        this.$message({message: "上传成功,正在导入中...", type: "success" });
      } else {
        this.$message({message: res.msg, type: "warning" });
      }
      this.uploadLoading=false
      this.$refs.upload5.clearFiles();
      this.fileList5 = [];
    },
    selectchange: function (rows, row) {
      this.selids = [];
      this.selprocodes = [];
      this.styleCodeList = rows
      this.spellmoreCheckbox = rows
      rows.forEach(f => {
        this.selids.push(f.id);
        this.selprocodes.push(f.proCode)
      })
    },
    async onExport() {
      let btn = this.tableHandles.find(item => item.label == "导出")
      btn.disabled = true
      btn.title = '导出中...'
      let pager = this.$refs.pager.getPager()
      if (this.filter.categoryids != null && this.filter.categoryids.length > 0) {
        this.filter.productCategoryId = this.filter.categoryids[this.filter.categoryids.length - 1]
      }
      this.filter.onlineStartTime = null;
      this.filter.onlineEndTime = null;
      if (this.filter.timerange) {
        this.filter.onlineStartTime = this.filter.timerange[0];
        this.filter.onlineEndTime = this.filter.timerange[1];
      }
      let params = {
        ...pager,
        ...this.pager,
        ... this.filter
      }
      let validaParams = { ...this.filter };
      let hasparm = false;
      for (let key of Object.keys(validaParams)) {
        if (key == "categoryids") {
          if (params[key] != null && params[key].length > 0) {
            hasparm = true;
          }
        }
        else {
          if (params[key]) {
            hasparm = true;
          }
        }
      }
      if (!hasparm) {
        this.$message({ message: "请选择条件后导出！", type: "warning", });
        return;
      }
      var res = await exportProduct(params)
      btn.disabled = false
    },
    async onShowComm() {
      this.dialogVisible4 = true
      console.log('传值', this.selprocodes.join())
      this.$nextTick(async () => {
        this.$refs.productcommhistory.onSearch(this.selprocodes.join())
      })
    },
    async onShowLog(row) {
      this.logfilter.proCode = row.proCode;
      this.logDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.productchangelog.clearnFilter();
        this.$refs.productchangelog.onSearch();
      })
    },
    async onlineGoodsAsync(productCode, groupId) {
      this.onlineGoods.filter.productCode = productCode;
      this.onlineGoods.filter.groupId = groupId;
      this.onlineGoods.visible = true;
      this.$nextTick(() => {
        this.$refs.productReportPddOnlineForm.firstLoad();
      });
    },
    async closePddOnlineForm() {
      this.onlineGoods.visible = false;
    },
    async onOneNoticeShow() {
      let isSame = this.styleCodeList.every((item, index, arr) => {
        return item.styleCode == arr[0].styleCode;
      });
      if (!isSame) return this.$message({ message: "请勾选同一款式的产品", type: "warning", });
      if (!this.selids || this.selids.length <= 0) {
        this.$message({ message: "请勾选要参与通知的产品", type: "warning", });
        return;
      }
      this.oneNoticeDialog.oneNoticeFormData.seriesName = this.styleCodeList[0].styleCode;
      this.oneNoticeDialog.visible = true;
    },
    async onOneNoticeSave() {
      this.oneNoticeDialog.formLoading = true;
      this.oneNoticeDialog.oneNoticeFormData.productIds = this.selids;
      let res = await oneNoticeProductSendMsg(this.oneNoticeDialog.oneNoticeFormData);
      this.oneNoticeDialog.formLoading = false;
      if (res?.success) {
        this.$message({ message: '通知成功，可点击【查看通知日志】查看', type: "success" });
        this.oneNoticeDialog.oneNoticeFormData = {
          noticeTitle: "",
          noticeContext: "",
          noticeImgs: ""
        }
        this.oneNoticeDialog.visible = false;
      }
    },
    async onOneNoticeLogShow() {
      this.$showDialogform({
        path: `@/views/operatemanage/base/productnoticelog.vue`,
        title: '通知日志',
        args: {},
        height: '600px',
        width: '75%',
      })
    },

    async onShowProductsyncbyid() {
      this.$showDialogform({
        path: `@/views/operatemanage/base/productsyncbyid.vue`,
        title: '自动同步产品',
        args: {},
        height: '490px',
        width: '50%',

      })
    },

    async onOneCreateGroupChatShow() {
      this.$showDialogform({
        path: `@/views/operatemanage/base/productcreategroupchatlog.vue`,
        title: '建群日志',
        args: {},
        height: '600px',
        width: '75%',
      })
    },

    async onCreatedChatNew(createdtype) {
      if (this.createdChatNew.createGroupChatReason == null || this.createdChatNew.createGroupChatReason == "") {
        this.$message({ message: "请输入建群理由", type: "warning", });
        return;
      }
      if (createdtype == 1) {
        let isSame = this.styleCodeList.every((item, index, arr) => {
          return item.styleCode == arr[0].styleCode;
        });
        if (!isSame) return this.$message({ message: "请勾选同一款式的产品", type: "warning", });
        if (!this.selids || this.selids.length <= 0) {
          this.$message({ message: "请勾选要参与通知的产品", type: "warning", });
          return;
        }
        this.$confirm("确定按勾选的产品建群吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
          async () => {
            this.createdChatNew.visible = false;
            this.pageLoading = true;
            let res = await createGroupChatProductSendMsg({
              "onlyYunYing":(this.createdChatNew.checkOnlyYunYing==true?1:0),
              "createdType": 1,
              "seriesName": this.styleCodeList[0].styleCode,
              "createGroupChatReason": this.createdChatNew.createGroupChatReason,
              "productIds": this.selids,
              "request": null,
            });
            this.pageLoading = false;
            if (res?.success) {
              this.$message({ message: '建群成功，请前往钉钉查看', type: "success" });

            }
          });
      }
      else if (createdtype == 2) {
        this.$confirm("确定搜索的结果建群吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
          async () => {
            this.createdChatNew.visible = false;
            const params = this.getprarms();
            this.pageLoading = true;
            let res = await createGroupChatProductSendMsg({
              "onlyYunYing":(this.createdChatNew.checkOnlyYunYing==true?1:0),
              "createdType": 2,
              "createGroupChatReason": this.createdChatNew.createGroupChatReason,
              "seriesName": "",
              "productIds": "",
              "request": params,
            });
            this.pageLoading = false;
            if (res?.success) {
              this.$message({ message: '建群成功，请前往钉钉查看', type: "success" });
            }
          });
      }
    },
    async onOneCreateGroupChat() {
      this.createdChatNew.createGroupChatReason = "";
      this.createdChatNew.checkOnlyYunYing=false;
      this.createdChatNew.visible = true;
    },
    async DeletePDDProduct() {
      this.$confirm("确定删除吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
        async () => {
          let res = await deletePDDProduct(this.selprocodes);
          if (res?.success) {
            this.getProductlist();
            this.$message({ message: '删除成功', type: "success" });
          }
        });
    },
    async categoryLogShow() {
      this.categoryLogDialog.visible = true;
      let that = this;
      let ids = "";
      if (!this.selprocodes || this.selprocodes.length <= 0) {
        ids = "";
      } else {
        ids = this.selprocodes.join(',');
      }
      this.$nextTick(function () {
        that.$refs.productCategoryErrorLog.filter.productIds = ids;
        that.$refs.productCategoryErrorLog.filter.startTime= null;
        that.$refs.productCategoryErrorLog.filter.endTime= null;
        that.$refs.productCategoryErrorLog.filter.platform = null;
        that.$refs.productCategoryErrorLog.syncFilter.platform= null;
        that.$refs.productCategoryErrorLog.syncFilter.shopCode= null;
        that.$refs.productCategoryErrorLog.syncFilter.proCodes= null;
        that.$refs.productCategoryErrorLog.onSearch();
      })
    },
    async onWlLableDialogShow() {
      let res = await getProductExpressLableSet();
      if (res?.success) {
        if (res.data != "") {
          this.wlLableDialog.wlKeyNameList = res.data.split(",");
        }
        this.wlLableDialog.visiable = true;
      }
    },
    async removeWlKey(key){
      this.wlLableDialog.wlKeyNameList = this.wlLableDialog.wlKeyNameList.filter(x => x != key);
    },
    async addWlKey(){
      if(this.wlLableDialog.wlKeyName == null || this.wlLableDialog.wlKeyName==""){
        this.$message({ message: '请输入标签', type: "warning" });
        return;
      }
      if (this.wlLableDialog.wlKeyNameList.length >= 10)
      {
        this.$message({ message: '标签最多只能设置10个！', type: "warning" });
        return;
      }
      var values = this.wlLableDialog.wlKeyName.split(",");
      values.forEach(item => {
        if(this.wlLableDialog.wlKeyNameList.indexOf(item)==-1){
        this.wlLableDialog.wlKeyNameList.push(item);
        }
      })
      this.wlLableDialog.wlKeyName = "";
    },
    async saveWlLable(){
      if (this.wlLableDialog.wlKeyNameList.length == 0)
      {
        this.$message({ message: '请输入标签!', type: "warning" });
        return;
      }
      if (this.wlLableDialog.wlKeyNameList.length > 10)
      {
        this.$message({ message: '标签最多只能设置10个！', type: "warning" });
        return;
      }
      let res = await saveProductExpressLableSet(this.wlLableDialog.wlKeyNameList);
      if (res?.success) {
        this.$message({ message: '正在计算中，请稍后，请留意小昀助理计算完成消息通知！', type: "success" });
        this.wlLableDialog.visiable = false;
      }
    },
    //批量导入修改-打开上传弹窗
    onBatchUpdateProduct() {
      this.colOptions = [];
      this.dialogVisible12 = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
        if (this.$refs.upload12) {
          this.$refs.upload12.clearFiles();
        }
      });
      this.fileList12.splice(0, 1);
    },
    //批量导入修改-上传文件
    async uploadFile12(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("updateColumns", JSON.stringify(this.colOptions));
      const col = this.colSelect.filter(a => !this.colOptions.includes(a));
      if (col.length == 7) {
        this.$message({ message: '请至少选择一列修改列!', type: 'error'});
        return;
      }
      if (col.length > 0) {
        this.$message({ message: col+'列未选择修改。', type: 'warning'});
      }
      const res = await importBatchUpdateProduct(form);
      if (res.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success" });
        this.dialogVisible12 = false;
      }
    },
    //批量导入修改-更改上传文件
    async uploadChange12(file, fileList) {
      if (file.length > 1 || fileList.length > 1) {
        fileList.splice(1, 1);
        this.$message({ message: "只允许单文件导入", type: "warning" });
        return false;
      }
      this.fileList12.push(file);
    },
    //批量导入修改-移除上传文件
    uploadRemove12() {
      this.fileList12.splice(0, 1);
    },
    //批量导入修改-提交上传文件
    async submitupload12() {
      if (this.fileList12.length == 0) {
        this.$message({ message: "请选择文件", type: "warning" });
        return;
      }
      this.$refs.upload12.submit();
      this.$refs.upload12.clearFiles();
      this.fileList12.splice(0, 1);
    },
    //批量导入修改-全选
    selectAll() {
      if (this.colOptions.length != this.colSelect.length) {
        this.colOptions = this.colSelect.map(i => i);
      } else {
        this.colOptions = [];
      }
    },
    //批量导入修改-勾选
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //下载批量修改导入模板
    downLoadFile(){
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250611/1932630788709728257.xlsx', '批量导入修改模板.xlsx');
    },
    async operateSyncProductFromJst(){
      this.$showDialogform({
        path: `@/views/operatemanage/base/syncPoructFromJst.vue`,
        title: '运营同步宝贝',
        autoTitle: false,
        height: '650px',
        width: '80%',
      })
    }
  }
}
</script>

<style scoped>
::v-deep .el-input__icon {
  height: auto;
}

::v-deep .el-drawer__body {
  overflow: hidden;
}

.centered-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 25px;
}
</style>
