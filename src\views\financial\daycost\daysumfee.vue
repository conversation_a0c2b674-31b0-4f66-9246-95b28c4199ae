<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              :loading="listLoading">
         <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode"  placeholder="产品ID"/></el-button>
            <el-button style="padding: 0;margin: 0;">
            <el-select filterable v-model="filter1.platform" placeholder="平台" clearable style="width: 80px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.groupId" placeholder="运营组" style="width: 110px">
                 <el-option label="全部" value/>
                 <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.shopId" placeholder="店铺" style="width: 110px">
                <el-option label="全部" value/>
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">刷新</el-button>
            <el-button type="primary" @click="dialogCalcEstimatedCost">计算预估费用</el-button> 
            <el-button type="primary" @click="dialogCalCGFareVis=!dialogCalCGFareVis">计算采购运费</el-button> 
            <el-button type="primary" @click="dialogCalcTXtg">计算淘系推广费</el-button> 
            <el-button type="primary" @click="dialogCalcCashRed">计算现金红包</el-button> 
            <!-- 注释掉(计算预估费用已经包括计算运营工资) -->
            <!-- <el-button type="primary" @click="dialogCalcOperatingWage">计算运营工资</el-button>  -->
          </el-button-group>
        </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-dialog title="计算采购运费" :visible.sync="dialogCalCGFareVis" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calCGFareDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期" :picker-options="pickerOptions2"></el-date-picker> 
              <el-button  type="success" style="margin-left:  30px;"  @click="calCGFare">计算采购运费</el-button>
          </el-col> 
        </el-row> 
      </span> 
    </el-dialog>
    <el-dialog title="计算淘系运营费用" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期" :picker-options="pickerOptions3"></el-date-picker> 
              <el-button  type="success" style="margin-left:  30px;"  @click="CalcTXtg">计算淘系运营费用</el-button>
          </el-col> 
        </el-row> 
      </span> 
    </el-dialog>
    <el-dialog title="计算现金红包" :visible.sync="dialogCalDayRepotCashRed" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calDayRepotyCashRedDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期" :picker-options="pickerOptions4"></el-date-picker> 
              <el-button  type="success" style="margin-left:  30px;"  @click="CalcCashRed">计算现金红包</el-button>
          </el-col> 
        </el-row> 
      </span> 
    </el-dialog>
    
    <el-dialog title="计算所有平台预估费用" :visible.sync="dialogCalEstimatedCost" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calEstimatedCostDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期" :picker-options="pickerOptions1"></el-date-picker> 
              <el-button  type="success" style="margin-left:  30px;" :loading="IsCalcEstimatedCosts" @click="CalcEstimatedCosts">计算预估费用</el-button>
          </el-col> 
        </el-row> 
      </span> 
    </el-dialog>


    <el-dialog title="计算所有平台运营工资" :visible.sync="dialogCalOperatingWage" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="calOperatingWageDate" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期" :picker-options="pickerOptions5"></el-date-picker> 
              <el-button  type="success" style="margin-left:  30px;"  @click="CalcOperatingWages">计算运营工资</el-button>
          </el-col> 
        </el-row> 
      </span> 
    </el-dialog>
    
    


  </container>
</template>
<script>
import { platformlist } from '@/utils/tools'
import {pageDayReport,computDayCalCGFareAsync,calcTXyyfy,calcEstimatedCost,calcTxCashRed,CalcOperatingWage} from '@/api/financial/daycost'
import {getList as getshopList } from '@/api/operatemanage/base/shop'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
const tableCols =[
      {istrue:true,prop:'yearMonthDay',label:'日期', width:'80',sortable:'custom'},
      {istrue:true,prop:'platformName',label:'平台', width:'80',sortable:'custom' },
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'130',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'shopCode',label:'店铺', width:'160',sortable:'custom',formatter:(row)=> row.shopName},
      {istrue:true,prop:'groupId',label:'运营组', width:'100',sortable:'custom',formatter:(row)=> row.groupName},
      {istrue:true,prop:'cuiShouFee',label:'催收费', width:'100',sortable:'custom'},
      {istrue:true,prop:'sampleFeeBX',label:'样品费', width:'100',sortable:'custom'},
      {istrue:true,prop:'sampleFee',label:'拿样费', width:'100',sortable:'custom'},
      {istrue:true,prop:'shootFee',label:'道具费', width:'100',sortable:'custom'},
      {istrue:true,prop:'wages',label:'工资', width:'100',sortable:'custom'},
      {istrue:true,prop:'productFreight',label:'产品运费', width:'100',sortable:'custom'},
      {istrue:true,prop:'purchaseFreight',label:'采购运费', width:'100',sortable:'custom'},
      {istrue:true,prop:'inventoryCheckFee',label:'盘亏', width:'100',sortable:'custom'},
      {istrue:true,prop:'cashRedAmount',label:'现金红包', width:'100',sortable:'custom'},
     //{istrue:true,prop:'totalAmont',label:'实际合计', width:'80',sortable:'custom'},
     ];
const tableHandles=[ ];
export default {
  name: 'Roles',
  components: {cesTable, container},
   props:{
       filter: { }
     },
  data() {
    return {
      calDayRepotyDate:null,
      calDayRepotyCashRedDate:null,
      calEstimatedCostDate:null,
      calOperatingWageDate:null,
      dialogCalDayRepotVis:false,
      dialogCalDayRepotCashRed:false,
      dialogCalEstimatedCost:false,
      dialogCalOperatingWage:false,
      filter1: {
        proCode:null,
        platform:null,
        fKId:null,
        groupId:null,
        shopId :null,
        type :null,
        shareOper :null
       },
      platformlist:platformlist,
      grouplist:[],
      shoplist:[],
      that: this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"YearMonthDay",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      dialogExmainPurchaseFreightVisible:false,
      dialogCalCGFareVis:false,
      calCGFareDate:null,
      IsCalcEstimatedCosts:false,
      pickerOptions1: {
                    disabledDate(time) {
                        return time.getTime() >(Date.now()-1 * 24 * 60 * 60 * 1000);
                    }
                },
      pickerOptions2: {
                    disabledDate(time) {
                        return time.getTime() >(Date.now()-1 * 24 * 60 * 60 * 1000);
                    }
                },
      pickerOptions3: {
                    disabledDate(time) {
                        return time.getTime() >(Date.now()-1 * 24 * 60 * 60 * 1000);
                    }
                },
      pickerOptions4:{
                    disabledDate(time) {
                        return time.getTime() >(Date.now()-1 * 24 * 60 * 60 * 1000);
                    }
                },
                pickerOptions5: {
                    disabledDate(time) {
                        return time.getTime() >(Date.now()-1 * 24 * 60 * 60 * 1000);
                    }
                },
    }
  },
  async mounted() {
     await this.init()
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    async dialogCalcTXtg(){
      this.dialogCalDayRepotVis = true;
    },
    async dialogCalcCashRed(){
      this.dialogCalDayRepotCashRed = true;
    },
    async dialogCalcEstimatedCost(){
      this.dialogCalEstimatedCost = true;
    },
    async dialogCalcOperatingWage(){
      this.dialogCalOperatingWage = true;
    },
    async CalcEstimatedCosts(){
      if(this.calEstimatedCostDate==null) 
      {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
      this.IsCalcEstimatedCosts = true;

        let res= await calcEstimatedCost({YearMonthDay:this.calEstimatedCostDate});
        this.IsCalcEstimatedCosts = false;
        if (!res?.success)  {
          return
        }
        this.$message({type: 'success',message: '正在计算中,请稍候...'});
        this.dialogCalEstimatedCost=false;
      },
      async CalcOperatingWages(){
      if(this.calOperatingWageDate==null) 
      {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
        let res= await CalcOperatingWage({YearMonthDay:this.calOperatingWageDate});
        if (!res?.success)  return
        this.$message({type: 'success',message: '正在计算中,请稍候...'});
        this.dialogCalOperatingWage=false;
      },
    async CalcTXtg(){
      if(this.calDayRepotyDate==null) 
      {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
        let res= await calcTXyyfy({YearMonthDay:this.calDayRepotyDate});
        if (!res?.success)  return
        this.$message({type: 'success',message: '正在计算中,请稍候...'});
        this.dialogCalDayRepotVis=false;
      },
     async CalcCashRed(){
      if(this.calDayRepotyCashRedDate==null) 
      {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
        let res= await calcTxCashRed({YearMonthDay:this.calDayRepotyCashRedDate});
        if (!res?.success)  return
        this.$message({type: 'success',message: '正在计算中,请稍候...'});
        this.dialogCalDayRepotCashRed=false;
      },
    async init(){
        this.shoplist =[]
        const res1 = await getshopList({isOpen:1,platform:1,CurrentPage:1,PageSize:100});
        res1?.data?.list.forEach(f=>{this.shoplist.push(f)})
       
        const res11 = await getshopList({isOpen:1,platform:2,CurrentPage:1,PageSize:100});
        res11?.data?.list.forEach(f=>{this.shoplist.push(f)})

        var res2= await getDirectorGroupList();
        this.grouplist = res2.data.map(item => {return { value: item.key, label: item.value }}); 
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.pager,... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageDayReport(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    async calCGFare(){
      if(this.calCGFareDate==null) return
      let res= await computDayCalCGFareAsync({yearMonthDay:this.calCGFareDate});
      if (!res?.success)  return
      this.$message({type: 'success',message: '开始计算,请稍候...'});
      this.dialogCalCGFareVis=false;
    } 
  },
  }

</script>
