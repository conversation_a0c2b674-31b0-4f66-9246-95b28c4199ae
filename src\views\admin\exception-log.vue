<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="记录时间:">
                    <el-date-picker style="width: 280px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始"
                        end-placeholder="结束"></el-date-picker>
                </el-form-item>
                <el-form-item label="环境:">
                    <el-select v-model="filter.environment" clearable placeholder="环境" style="width: 120px">
                        <el-option label="Production" value="Production"></el-option>
                        <el-option label="Development" value="Development"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="异常类型:">
                    <el-select v-model="filter.nickName" clearable placeholder="异常类型" style="width: 120px">
                        <el-option label="请选择" value></el-option>
                        <el-option label="Error" value="Error"></el-option>
                        <el-option label="Warning" value="Warning"></el-option>
                        <el-option label="Information" value="Information"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目:">
                    <el-select v-model="filter.project" clearable placeholder="项目">
                        <el-option label="yunhan.core.api.customerservice"
                            value="yunhan.core.api.customerservice"></el-option>
                        <el-option label="yunhan.core.api.manager" value="yunhan.core.api.manager"></el-option>
                        <el-option label="yunhan.core.api.inventory" value="yunhan.core.api.inventory"></el-option>
                        <el-option label="yunhan.core.api.express" value="yunhan.core.api.express"></el-option>
                        <el-option label="yunhan.core.api.bookkeeper" value="yunhan.core.api.bookkeeper"></el-option>
                        <el-option label="yunhan.core.api.monthbookkeeper" value="yunhan.core.api.monthbookkeeper"></el-option>
                        <el-option label="yunhan.core.api.operatemanage" value="yunhan.core.api.operatemanage"></el-option>
                        <el-option label="yunhan.core.api.pddoperatemanage"
                            value="yunhan.core.api.pddoperatemanage"></el-option>
                        <el-option label="yunhan.core.api.financial" value="yunhan.core.api.financial"></el-option>
                        <el-option label="yunhan.core.api.order" value="yunhan.core.api.order"></el-option>
                        <el-option label="yunhan.core.api.media" value="yunhan.core.api.media"></el-option>
                        <el-option label="yunhan.core.api.profit" value="yunhan.core.api.profit"></el-option>
                        <el-option label="yunhan.core.api.warning" value="yunhan.core.api.warning"></el-option>
                        <el-option label="yunhan.core.hangfire" value="yunhan.core.hangfire"></el-option>
                        <el-option label="yunhan.core.hangfire.sync" value="yunhan.core.hangfire.sync"></el-option>
                        <el-option label="yunhan.core.api.open.wlan" value="yunhan.core.api.open.wlan"></el-option>
                        <el-option label="yunhan.core.api.upload" value="yunhan.core.api.upload"></el-option>
                        <el-option label="yunhan.core.api.distribution" value="yunhan.core.api.distribution"></el-option>
                        <el-option label="yunhan.core.api.pddoperatemanage"
                            value="yunhan.core.api.pddoperatemanage"></el-option>
                        <el-option label="yunhan.core.api.packprocess" value="yunhan.core.api.packprocess"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input v-model="filter.notContainKey" placeholder="排除关键字" clearable maxlength="100"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input v-model="filter.key" placeholder="关键字" clearable maxlength="300"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <el-table v-loading="listLoading" :data="list" highlight-current-row height="'100%'"
            style="width: 100%;height:100%;">
            <el-table-column prop="createdTime" label="记录时间" :formatter="formatCreatedTime" width="180" />
            <el-table-column prop="ip" label="IP地址" width="100" />
            <el-table-column prop="nickName" label="异常类型" width="80" />
            <el-table-column prop="browser" label="项目" width="120" />
            <el-table-column prop="msg" label="异常消息" />
            <el-table-column label="查看" class-name="address"  width="120">
                <template slot-scope="scope">
                    <el-button type="primary" @click="viewLog(scope.row.msg)">查看日志</el-button>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getList" />
        </template>

        <el-dialog title="日志" :visible.sync="dialogVisible" width="80%" :before-close="handleClose" v-dialogDrag>
          <div class="log">
            {{ logProps }}
          </div>
        </el-dialog>
    </my-container>
</template>

<script>
import { formatTime } from '@/utils'
import dayjs from "dayjs";
import { getExceptionPage } from '@/api/admin/opration-log'
import MyContainer from '@/components/my-container'
const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'OprationLog',
    components: { MyContainer },
    data() {
        return {
            filter: {
                nickName: 'Error',
                environment: 'Production',
                project: '',
                timerange: [
                    formatTime(dayjs().subtract(2, "day"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
                key: '',
                notContainKey: ''
                //timerange: [startDate, endDate]
            },
            list: [],
            total: 0,
            listLoading: false,
            dialogVisible:false,
            logProps:null
        }
    },
    mounted() {
        console.log(this.filter.timerange, 'dasdad')
        this.getList()
    },
    methods: {
        handleClose(){
            this.dialogVisible = false
        },
        //查看日志
        viewLog(row){
            this.logProps = row
            this.dialogVisible = true
        },
        formatCreatedTime: function (row, column, time) {
            return formatTime(time, 'YYYY-MM-DD HH:mm:ss')
        },
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getList()
        },
        // 获取列表
        async getList() {
            const pager = this.$refs.pager.getPager()
            const para = {
                ...pager,
                ...this.filter
            }
            if (this.filter.timerange) {
                para.startCreatedTime = this.filter.timerange[0];
                para.endCreatedTime = this.filter.timerange[1];
            }
            this.listLoading = true
            const res = await getExceptionPage(para)
            this.listLoading = false

            if (!res?.success) {
                return
            }

            this.total = res.data.total
            this.list = res.data.list
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__cell .cell {
    max-height: 200px;
    //超出部分隐藏,显示省略号
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: wrap;
}

.log{
    max-height: 500px;
    overflow: auto;
}
</style>
