<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
            value-format="yyyyMM" placeholder="月份"></el-date-picker>
        </el-form-item>


        <el-form-item label="">
          <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable disabled
            style="width: 130px">
            <el-option label="淘工厂" :value=8 />
          </el-select>
        </el-form-item>
        <el-form-item label="" label-position="right">
          <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺" class="el-select-content"
            style="width:300px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="" label-position="right">
          <el-input v-model="filter.serialNumberOrder" style="width:183px;" placeholder="交易主单" />
        </el-form-item>

        <el-form-item label="" label-position="right">
          <el-select filterable v-model="filter.recoganizeType" placeholder="收入支出" clearable style="width: 130px">
            <el-option label="收入" :value=1 />
            <el-option label="支出" :value=2 />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport" :loading="onExportLoading">汇总导出</el-button>
        </el-form-item>
      </el-form>
    </template>

    <vxetablebase :id="'financialDetailTgcWx20241031001'" ref="table" :that='that' :isIndex='true'
      :cstmExportFunc="onExport" :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :showsummary="true" @select="selectchange" :tableData='ZTCKeyWordList' :tableCols='tableCols' :isSelection="true"
      :loading="listLoading">
    </vxetablebase>



    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink } from "@/utils/tools";
import { GetFinancialDetail_TgcWx, ExportFinancialDetail_TgcWx } from '@/api/monthbookkeeper/financialDetail'

const tableCols = [
  { istrue: true, prop: 'accountType', label: '账单类型', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'orderDirection', label: '单据方向', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'businessOrderNo', label: '业务单据号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'accountDate', label: '账单日期', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'mainOrderNumber', label: '交易主单号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'childOrderNumber', label: '交易子单号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'zongSettlementAmount', label: '账单金额', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'settlementDate', label: '结算日期', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'proCode', label: '商品ID', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'proCode', label: 'skuID', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'orderDate', label: '创单日期', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'qty', label: '购买件数', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'buyerPayAmount', label: '支付金额', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'settlementAmount', label: '确认收货金额', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'backAmount', label: '退款金额', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'payTool', label: '结算方式', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'serialNumberBusinessZfb', label: '商户订单号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'serialNumberMerchantOrder', label: '资金流水号', sortable: 'custom', width: 'auto' },

  //{ istrue: true, prop: 'recoganizeType', label: '收入支出', sortable: 'custom', width: 'auto', formatter: (row) => (row.recoganizeType == 0 ? "收入" : row.recoganizeType == 1 ? "支出" : "") },
];
export default {
  name: "financialDetailTgcWx",
  components: { cesTable, vxetablebase, MyContainer, MyConfirmButton, MySearch, MySearchWindow, },
  data() {
    return {
      that: this,
      filter: {
        yearMonth: null,
        platform: 8,
        shopCode: null,
      },
      shopList: [],
      userList: [],
      groupList: [],
      ZTCKeyWordList: [],
      tableCols: tableCols,
      total: 0,
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      summaryarry: {},
      onExportLoading: false,
    };
  },
  async mounted() {
    await this.onchangeplatform(8);
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onchangeplatform(val) {
      this.filter.shopCode = null;
      this.shopList = [];
      const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    async getShopList() {
      const res1 = await getAllShopList();
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    getCondition() {
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };

      return params;
    },
    async getList() {
      let params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await GetFinancialDetail_TgcWx(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },

    async setExportCols() {
      await this.$refs.table.setExportCols();
    },
    async onExport(opt) {
      if (!this.filter.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      let pars = this.getCondition();
      if (pars === false) {
        return;
      }
      const params = { ...pars, ...opt };
      let res = await ExportFinancialDetail_TgcWx(params);
      if (!res?.data) {
        return
      }
      this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>