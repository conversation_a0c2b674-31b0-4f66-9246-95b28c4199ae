<template>
    <div>
        <el-row :gutter="10" class="panel-group">
            <el-col :xs="12" :sm="12" :lg="2" class="card-panel-col" v-for="(item,index) in linkList" :key="index">
                <div v-if="checkPermission('homepermission')" class="card-panel" @click="clickLink(item.link)">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            {{item.name}}
                        </div>                       
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: 'YunhanAdminEditorIndex',

    data() {
        return {
            linkList:[
              
            ]
        };
    },

    mounted() {
        
    },

    methods: {
        async clickLink(item){
            console.log('来了',item)
        
            this.$router.push({path: item})
        }
    },
};
</script>

<style lang="scss" scoped>
.panel-group{
    margin-top: 18px;

  .card-panel-col {
    margin: 15px;
  }

    .card-panel {
    // height: 108px;
    cursor: pointer;
    //font-size: 12px;
    position: relative;
    //overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel {
        color: #fff;
      }
        transform: scale(1.05);
        overflow: hidden;
        background-color: rgb(240, 242, 245);
    }

    }
    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 18px;
      }
    }
}
</style>