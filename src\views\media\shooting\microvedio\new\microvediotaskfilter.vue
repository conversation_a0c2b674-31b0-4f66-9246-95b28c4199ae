<template>
  <!-- 筛选 -->
  <el-button-group>
    <div class="ssanc">
      <div style="width:20%;display: inline-block;text-align: left;">
        <span style="margin-right:0.8%;">
          <el-input style="width:30%;" v-model.trim="filter.microVedioTaskId" :maxlength=100 placeholder="编号"
            @keyup.enter.native="onSearch" clearable />
        </span>
        <span style="margin-right:0.8%;">
          <el-input style="width:66.4%;" v-model.trim="filter.productShortName" :maxlength=100 placeholder="产品简称"
            suffix-icon="el-icon-search" @keyup.enter.native="onSearch" clearable />
        </span>
      </div>
      <div style="width:25%;display: inline-block;">
        <span style="margin-left:10px;">
          <el-button style="width:90px;" type="primary" @click="onSearch">查&nbsp;询</el-button>
        </span>
        <span style="margin-left:5px;">
          <el-button @click="onclear" plain>重置</el-button>
        </span>
      </div>
      <div style="width:55%;display: inline-block;text-align: right;">
        <span v-if="listtype==1" style=" margin-left:20px;padding:0 2px;">
          <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain @click="onAddTask"
            v-if="checkPermission('api:media:microvedio:AddOrUpdateShootingVideoTaskAsync')"><i
              class="el-icon-plus"></i>&nbsp;创建任务</el-button>
        </span>
        <span  v-if="listtype==1" style="box-sizing: border-box; margin-left:6px;">
          <el-button size="mini" type="primary" @click="onAddOrder"
            v-if="checkPermission('microvediosqny')">申请拿样</el-button>
        </span>
        <span>
          <el-dropdown style="box-sizing: border-box; margin-left:6px;" v-if="checkPermission('microvediodrop')"
            size="mini" split-button type="primary" icon="el-icon-share" @command="handleCommand"> 批量操作 <el-dropdown-menu
              slot="dropdown">
              <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
              <!-- <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a"
                v-if="(listtype == 1)">批量完成</el-dropdown-item> -->
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="b"
                v-if="(listtype == 1)">批量重启</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="c"
                v-if="(listtype == 1)">批量终止</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="d"
                v-if="(listtype == 1)">批量标记</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="e"
                v-if="(listtype == 1)">取消标记</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="f"
                v-if="(listtype == 1)">批量删除</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="k"
                v-if="(listtype == 3 || listtype == 2)">批量存档</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="l"
                v-if="(listtype == 4)">取消存档</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="g"
                v-if="(listtype == 2)">批量统计</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="h"
                v-if="(listtype == 3)">取消统计</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="j"
                v-if="(listtype == 5)">批量回收</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="i"
                v-if="(listtype == 5)">彻底删除</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <span style="box-sizing: border-box; margin-left:6px;">
          <el-button type="primary" @click="onExport"
            v-if="checkPermission('microvedioexport')">导出</el-button>
        </span>
        <span style="margin-left:6px;"  v-if="listtype==1">
          <el-checkbox style="width: 111px;position: relative;top: 1px;" v-model="filter.isComplateCheck"
            :checked-value="1" :unchecked-value="0" border>隐藏已确认</el-checkbox>
        </span>
        <span>
          <el-radio-group style="margin-left:6px;" size="mini" v-if="checkPermission('microvediomr2qb')"
            v-model="onCommand" @change="ShowHideonSearch">
            <el-radio-button label="b">默认</el-radio-button>
            <el-radio-button label="a">全部</el-radio-button>
          </el-radio-group>
        </span>
      </div>
    </div>
    <div class="heardcss">
      <span>
        <el-select style="width: 6%;" v-model="filter.isTopOld" :clearable="true" placeholder="是否标记">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width: 6%;" v-model="filter.hasOverTime" :clearable="true" placeholder="完成时间">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width: 6%;" v-model="filter.hasConfirmTime" @keyup.enter.native="onSearch" :clearable="true"
          placeholder="是否确认">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </span>
      <span>
        <el-select style="width: 12%;" v-model="filter.warehouse" placeholder="大货仓" multiple :collapse-tags="true"
          clearable>
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>
      <span>
        <el-select style="width: 10%;" v-model="filter.fpPhotoLqName" placeholder="分配查询" multiple :collapse-tags="true"
          filterable clearable>
          <el-option v-for="item in fpPhotoLqNameList" :key="item" :label="item" :value="item" />
        </el-select>
      </span>
      <span>
        <el-select style="width:6%;" v-model="filter.operationGroup" :clearable="true" placeholder="运营组" filterable>
          <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>
      <span>
        <el-select style="width: 6%;" v-model="filter.dockingPeople" :clearable="true" placeholder="对接人" filterable>
          <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
        </el-select>
      </span>
      <span>
        <el-select style="width:7.5%;" v-model="filter.platform" placeholder="平台" multiple :collapse-tags="true" clearable
          @change="onchangeplatform">
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>
      <span>
        <el-select style="width:12.8%;" filterable v-model="filter.shopName" placeholder="店铺" clearable>
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
        </el-select>
      </span>
      <!--下拉（完成时间，创建时间，到货日期，申请日期，）-->
      <span>
        <el-select style="width:5.5%;" v-model="filter.searchTimeType" :clearable="true" placeholder="选择时间">
          <el-option label="创建时间" value="2"></el-option>
          <el-option label="完成时间" value="1"></el-option>
          <el-option label="确认时间" value="3"></el-option>
        </el-select>
      </span>
      <span>
        <el-date-picker style="width:18%;position: relative;top:1px;" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          v-model="filter.createdtimerange" />
      </span>
    </div>
    <!-- <div class="gddwz">
                    <p class="gddwznr">
                        暂无公告……暂22无公告……暂无公告……暂无公告……暂无公告……暂无公告……
                    </p>
                </div> -->
  </el-button-group>
</template>
<script>
import { getList as getshopList } from '@/api/operatemanage/base/shop'

export default {
  props: {
    platformList: { type: Array, default: [] }, //平台
    warehouselist: { type: Array, default: [] }, //仓库
    dockingPeopleList: { type: Array, default: [] }, //对接人
    fpPhotoLqNameList: { type: Array, default: [] }, //分配查询
    groupList: { type: Array, default: [] }, //运营组 
    taskUrgencyList: { type: Array, default: [] },
    islook: { type: Boolean, default: true }, //平台 
    //批量操作方法 
    listtype: { type: Number, default: 99 },
    //1 任务,2 已完成,3 确认信息,4 已使用,5 统计列表,6 信息存档,7存档,8回收站
  },
  data() {
    return {
      shopList: [],
      onCommand: "b",
      filter: {
        searchTimeType: "2",
        productShortName: '',
        packageDesignTaskId: undefined,
        hasOverTime: '',
        hasConfirmTime: '',
        shopName: null,//店铺
        operationGroup: null,//运营组
        hasOverTime: null,//完成时间
        hasConfirmTime: null,//确认时间
        warehouse: null,//仓库
        fpPhotoLqName: null,//分配人
        dockingPeople: null,//对接人
        platform: null,//平台
        izcjdz: [],
        packClass: null,
        brand: null,
        isComplateCheck: true
      },
    };
  },
  async mounted() {
    //await this.getrole();
  },
  methods: {
    onSearch() {
      if (this.filter.createdtimerange) {
        this.filter.createdstartTime = this.filter.createdtimerange[0];
        this.filter.createdendTime = this.filter.createdtimerange[1];
      } else {
        this.filter.createdstartTime = null;
        this.filter.createdendTime = null;
      }
      this.filter.isComplateChecked = this.filter.isComplateCheck == true ? 0 : 1;
      this.$emit('topSearch', this.filter)
    },
    onAddTask() {
      this.$emit('onAddTask')
    },
    onAddOrder() {
      this.$emit('onAddOrder')
    },
    ShowHideonSearch() {
      this.$emit('ShowHideonSearch', this.onCommand)
    },
    handleCommand(command) {
      this.$emit('handleCommand', command)
    },
    onExport(type) {
      this.$emit('onExport', type)
    },
    onclear() {
      this.filter.productShortName = '';
      this.filter.microVedioTaskId = null;
      this.filter.hasOverTime = '';
      this.filter.hasConfirmTime = '';
      this.filter.warehouse = [];
      this.filter.createdtimerange =null;
      this.filter.searchTimeType = '2';
      this.filter.shopName = null;
      this.filter.platform = '';
      this.filter.dockingPeople = '';
      this.filter.operationGroup = null;
      this.filter.fpPhotoLqName = [];
      this.filter.brand = [];
      this.filter.packClass = [];
      this.filter.izcjdz = null;
      this.filter.isComplateCheck = false;
      this.filter.isTopOld = '';
       
    },
    async onchangeplatform(val) {
      var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
      this.filter.shopName = "";
      this.shopList = res1.data.list;
    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  border: none !important;
  padding: 16px 24px 0 24px;
}

::v-deep .el-header {
  padding: 10px 5px 5px 5px !important;
}

.ssanc {
  width: 100%;
  height: 38px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  display: inline-block;

}

.heardcss {
  width: 100%;
  min-width: 1150px;
  min-height: 35px;
  // background-color: aqua;
  box-sizing: border-box;
  display: inline-block;
  margin-top: 8px;
}


.heardcss span,
.ssanc span {
  padding: 4px 0.2% 4px 0;
}




::v-deep span .el-radio-button__inner {
  line-height: 14px !important;
}

::v-deep .vxetablecss {
  width: 100%;
  margin-top: -20px !important;
}

::v-deep .vxetoolbar20221212 {
  top: 97px;
  right: 15px;
}

.textcss {
  color: #C0C4CC;
}

::v-deep .el-button-group>.el-button:last-child {
  margin-left: -2px !important;
}

::v-deep .vxe-header--row {
  height: 58px;
}

::v-deep .el-table__body-wrapper {
  height: 220px !important;
}

::v-deep .el-table__body-wrapper {
  overflow-y: auto;
}</style>

