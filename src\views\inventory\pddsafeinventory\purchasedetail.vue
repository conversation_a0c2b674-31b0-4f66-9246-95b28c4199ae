<template>
  <container>
    <template #header>
      <div style="height:568px;margin-top: 5px;">
        <ces-table :tablekey="tablekey" ref="table" :showsummary='true' :summaryarry='summaryarry' :that='that'
          :isIndex='true' :hasexpand='false' :isSelectColumn="false" :tableData='list' :tableCols='tableCols'
          :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading">
        </ces-table>
      </div>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { pagePddSafeInventoryPurchaseDetailAsync } from '@/api/inventory/pddSafeInventorys'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui'; 
import { formatTime } from "@/utils/tools";

let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols = [
  { istrue: true, prop: 'indexNo', label: 'ERP编号', width: '200' },
  { istrue: true, prop: 'buyNo', sortable: 'custom', label: '采购单号', width: '120', align: 'center' },
  { istrue: true, prop: 'status', sortable: 'custom', label: '采购单状态', width: '100', align: 'center' },
  { istrue: true, prop: 'planArrivalTime', sortable: 'custom',label: '预计到货日期' ,width: '120',formatter:(row)=>row.planArrivalTime == null ? "":formatTime(row.planArrivalTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'expressNo', sortable: 'custom',label: '快递单号' },
  { istrue: true, prop: 'b.count', sortable: 'custom',label: '进货数量',width: '120',formatter:(row)=>row.totalCount}
];
const tableHandles = [
];

export default {
  name: "PddsafeinventoryErrorDetail",
  components: { container, cesTable },
  props: {
    filter: {}
  },
  data() {
    return {
      tablekey: null,
      that: this,
      tableCols: tableCols,
      tableHandles: tableHandles,
      list: [],
      total: 0,
      pager: { OrderBy: "goodsCode", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      summaryarry: {},
      selids: []
    };
  },
  async mounted() {
    this.onSearch();
  },
  async created() {

  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.listLoading = true;
      let params = { ...pager, ...this.pager, ... this.filter }
      let res = await pagePddSafeInventoryPurchaseDetailAsync(params);
      this.listLoading = false;
      this.list = res.data?.list;
      this.total = res.data?.total;
    }
  }
}

</script>
<style></style>