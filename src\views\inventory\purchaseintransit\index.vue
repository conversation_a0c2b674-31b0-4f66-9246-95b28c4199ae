<template>
    <my-container>
        <my-container v-loading="pageLoading" style="height: 100%">
            <template #header>
                <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                    <el-form-item>
                        <el-input v-model.trim="filter.goodsCode" style="width: 140px" placeholder="商品编码/名称" @keyup.enter.native="onSearch" clearable maxlength="40" />
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model.trim="filter.supplier" style="width: 140px" placeholder="供应商名称" @keyup.enter.native="onSearch" clearable maxlength="50" />
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model.trim="filter.buyNo" style="width: 120px" placeholder="采购单号" @keyup.enter.native="onSearch" clearable maxlength="20" />
                    </el-form-item>
                    <el-form-item v-if="activeName=='first1'">
                        <el-button style="padding: 0;margin-left: 0;">
                            <el-select ref="filterbrandId" v-model="filter.brandId" multiple collapse-tags filterable clearable
                                placeholder="采购员" style="width: 160px">
                                <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model.trim="filter.warehousingNo" style="width: 140px" placeholder="入库单号" @keyup.enter.native="onSearch" clearable maxlength="20" />
                    </el-form-item>
                    <el-form-item label="">
                        <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始采购时间" end-placeholder="结束采购时间" :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="">
                        <el-date-picker style="width: 240px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始到货时间" end-placeholder="结束到货时间" :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="">
                        <el-date-picker style="width: 240px" v-model="filter.timerange3" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始入库时间" end-placeholder="结束入库时间" :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item v-if="activeName=='first1'">
                        <el-select v-model="filter.consignmentType" style="width:86px" placeholder="发货方式" @keyup.enter.native="onSearch" clearable>
                            <el-option value="物流" label="物流"></el-option>
                            <el-option value="其他" label="其他"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item v-if="activeName=='first1'">
                        <el-input v-model.trim="filter.consignmentName" style="width: 140px" placeholder="操作人" @keyup.enter.native="onSearch" clearable maxlength="10" />
                    </el-form-item> -->
                    <el-form-item v-if="activeName=='first1'">
                        <el-date-picker style="width: 240px" v-model="filter.timerange4" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始操作时间" end-placeholder="结束操作时间" :picker-options="pickerOptions" @change="changeDoTime">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item v-if="activeName=='first1'">
                        <el-date-picker style="width: 280px" v-model="filter.timerange5" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="数据接入开始时间" end-placeholder="数据接入结束时间" :picker-options="pickerOptions" @change="changePropsTime">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item v-if="activeName=='first1'">
                        <el-input v-model.trim="filter.NewConsignmentName" style="width: 140px" placeholder="最新操作人" @keyup.enter.native="onSearch" clearable maxlength="10" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">查询</el-button>
                    </el-form-item>
                    <el-form-item v-if="activeName=='first1'">
                        <el-tooltip effect="dark" content="注：最多只能复制1000条" placement="top">
                            <el-button type="primary" @click="copyBuyNos">复制采购单号</el-button>
                        </el-tooltip>
                    </el-form-item>
                </el-form>
                <div style="float:right;position: absolute;right: 30px;" v-if="activeName=='first1'">
                    {{ buyNoTotal }} | {{ dataTotal }}
                </div>
            </template>
            <el-tabs v-model="activeName" style="height: 94%">
                <el-tab-pane label="在途明细" name="first1" style="height: 100%">
                    <intransitdetail :filter="filter" :showTableHeaderHandles="false" ref="intransitdetail" style="height: 100%" @setTotalData="setTotalData"></intransitdetail>
                </el-tab-pane>
                <el-tab-pane label="商品在途" name="first2" style="height: 100%" lazy>
                    <intransitgoods :filter="filter" :showTableHeaderHandles="false" ref="intransitgoods" style="height: 100%"></intransitgoods>
                </el-tab-pane>
                <el-tab-pane label="供应商在途" name="first3" style="height: 100%" lazy>
                    <intransitsupplier :filter="filter" :showTableHeaderHandles="false" ref="intransitsupplier" style="height: 100%"></intransitsupplier>
                </el-tab-pane>
            </el-tabs>
        </my-container>
    </my-container>
</template>

  <script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import { getAllProBrand } from '@/api/inventory/warehouse'
    import intransitdetail from "@/views/inventory/purchaseintransit/intransitdetail.vue";
    import intransitgoods from "@/views/inventory/purchaseintransit/intransitgoods.vue";
    import intransitsupplier from "@/views/inventory/purchaseintransit/intransitsupplier.vue";
    export default {
        name: "purchaseintransitindex",
        components: {
            cesTable, MyContainer, MyConfirmButton,
            intransitdetail, intransitgoods, intransitsupplier
        },
        data() {
            return {
                that: this,
                pageLoading: false,
                activeName: "first1",
                buyNoTotal: 0,
                dataTotal: 0,
                brandlist: [],
                filter: {
                    goodsCode: null,
                    supplier: null,
                    buyNo: null,
                    warehousingNo: null,
                    timerange: [
                        formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],
                    timerange2: [],
                    timerange3: [],
                    timerange4: [],
                    timerange5: null,

                    startPurchaseDate: null,
                    endPurchaseDate: null,
                    startArrivalDate: null,
                    endArrivalDate: null,
                    startWarehousingDate: null,
                    endWarehousingDate: null,
                    consignmentType:null,
                    consignmentName:'',
                    brandId: null,
                    startBindTime: null,
                    endBindTime: null,
                    NewConsignmentName: null,
                    createdTime: null,
                    StartCreatedTime:null,
                    EndCreatedTime:null
                },
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '昨天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime());
                                picker.$emit('pick', [start, start]);
                            }
                        }, {
                            text: '近三天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime());
                                const end = new Date(new Date(tdate.toLocaleDateString()));
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一周',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                                const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一个月',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                                console.log("获取前一个月的时间", tdate.getDay());
                                const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                },
            };
        },
        async mounted() {
            await this.init();
        },
        methods: {
            changePropsTime(e){
                //如果e为空，说明清空了时间，需要清空开始时间和结束时间
                if(!e){
                    this.filter.StartCreatedTime = null;
                    this.filter.EndCreatedTime = null;
                }
            },
            changeDoTime(e){
                //如果e为空，说明清空了时间，需要清空开始时间和结束时间
                if(!e){
                    this.filter.startBindTime = null;
                    this.filter.endBindTime = null;
                }
            },
            async init() {
                var res2 = await getAllProBrand();
                this.brandlist = res2.data.map(item => {
                    return { value: item.key, label: item.value };
                });
            },
            async onSearch() {
                //如果没有采购时间或者数据接入时间就提示
                console.log(this.filter.timerange5, 'this.filter.timerange5');
                console.log(this.filter.timerange, 'this.filter.timerange');
                if(!this.filter.timerange && !this.filter.timerange5) {
                    return this.$message({ message: "请先选择采购时间或数据接入时间", type: "warning" });
                }
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startPurchaseDate = this.filter.timerange[0];
                    this.filter.endPurchaseDate = this.filter.timerange[1];
                }
                // else {
                //     this.$message({ message: "请先选择采购时间", type: "warning" });
                //     return false;
                // }
                if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                    this.filter.startArrivalDate = this.filter.timerange2[0];
                    this.filter.endArrivalDate = this.filter.timerange2[1];
                }
                if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
                    this.filter.startWarehousingDate = this.filter.timerange3[0];
                    this.filter.endWarehousingDate = this.filter.timerange3[1];
                }
                if (this.filter.timerange4 && this.filter.timerange4.length > 1) {
                    this.filter.startBindTime = this.filter.timerange4[0];
                    this.filter.endBindTime = this.filter.timerange4[1];
                }
                if (this.filter.timerange5 && this.filter.timerange5.length > 1) {
                    this.filter.StartCreatedTime = this.filter.timerange5[0];
                    this.filter.EndCreatedTime = this.filter.timerange5[1];
                }
                this.$nextTick(() => {
                    if (this.activeName == "first1")
                        this.$refs.intransitdetail.onSearch();
                    else if (this.activeName == "first2")
                        this.$refs.intransitgoods.onSearch();
                    else if (this.activeName == "first3")
                        this.$refs.intransitsupplier.onSearch();
                });
            },
            setTotalData(buyNoTotal,dataTotal){
                this.buyNoTotal = buyNoTotal;
                this.dataTotal = dataTotal;
            },
            // 复制采购单号
            copyBuyNos(){
                this.$refs.intransitdetail.copyBuyNo();
            }
        },
    };
  </script>

  <style lang="scss" scoped>
</style>
