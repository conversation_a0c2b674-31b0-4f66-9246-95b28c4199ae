<!-- 
这是 AllRegionalPersonnel/index.vue 的修改示例
展示如何使用 tableColumnMerge.js 来实现 dept 和 deptName 列的合并
-->

<template>
  <MyContainer>
    <!-- header 部分保持不变 -->
    <template #header>
      <!-- ... 原有的 header 内容 ... -->
    </template>
    
    <!-- 修改表格配置，使用新的列合并方法 -->
    <vxe-table
      border
      ref="newtable"
      show-footer
      height="100%"
      :loading="loading"
      :merge-footer-items="mergeFooterItems"
      :footer-data="footerData"
      :span-method="tableColumnMergeMethod"
      :footer-span-method="footerSpanMethod"
      :row-class-name="rowClassName"
      :cell-class-name="cellClassName"
      :row-config="{height: 40}"
      show-overflow
      :data="tableData">
      
      <!-- 普通列保持不变 -->
      <vxe-column field="calculateMonth" width="80" title="月份" footer-align="center">
        <template #footer="{ items, _columnIndex, row }">
          <span v-if="mergeColumn?.column?.includes('calculateMonth')" class="display_centered">
            {{ getMergeDisplayValue(row) }}
          </span>
          <span v-else>{{ items[_columnIndex] }}</span>
        </template>
      </vxe-column>
      
      <vxe-column field="type" width="80" title="类型" footer-align="center">
        <template #footer="{ items, _columnIndex, row }">
          <span v-if="mergeColumn?.column?.includes('type')" class="display_centered">
            {{ getMergeDisplayValue(row) }}
          </span>
          <span v-else>{{ items[_columnIndex] }}</span>
        </template>
      </vxe-column>

      <vxe-column field="regionName" title="区域" width="70" footer-align="center">
        <template #footer="{ items, _columnIndex, row }">
          <span v-if="mergeColumn?.column?.includes('regionName')" class="display_centered">
            {{ getMergeDisplayValue(row) }}
          </span>
          <span v-else>{{ items[_columnIndex] }}</span>
        </template>
      </vxe-column>
      
      <!-- 修改部门类型列，添加列合并逻辑 -->
      <vxe-column field="deptName" width="100" title="部门类型" footer-align="center">
        <template #default="{ row }">
          <!-- 使用新的列合并显示逻辑 -->
          <span v-if="shouldShowTableColumnMerged(row, { field: 'deptName' })">
            {{ getTableColumnMergeDisplayValue(row) }}
          </span>
          <span v-else>{{ row.deptName }}</span>
        </template>
        <template #footer="{ items, _columnIndex, row }">
          <span v-if="mergeColumn?.column?.includes('deptName')" class="display_centered">
            {{ getMergeDisplayValue(row) }}
          </span>
          <span v-else>{{ items[_columnIndex] }}</span>
        </template>
      </vxe-column>
      
      <!-- 修改部门列，添加列合并逻辑 -->
      <vxe-column field="dept" width="80" title="部门" footer-align="center">
        <template #default="{ row }">
          <!-- 第二个合并列，当第一列显示合并内容时，这列被隐藏 -->
          <span v-if="!shouldShowTableColumnMerged(row, { field: 'deptName' })">
            {{ row.dept }}
          </span>
        </template>
        <template #footer="{ items, _columnIndex, row }">
          <span v-if="mergeColumn?.column?.includes('dept')" class="display_centered">
            {{ getMergeDisplayValue(row) }}
          </span>
          <span v-else>{{ items[_columnIndex] }}</span>
        </template>
      </vxe-column>
      
      <!-- 其他列保持不变 -->
      <vxe-column field="total" width="80" title="总人数"></vxe-column>
      <!-- ... 其他列 ... -->
      
    </vxe-table>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools';
import departmentEdit from "./departmentEdit.vue";
import { downloadLink } from "@/utils/tools.js";
import dayjs from 'dayjs'
import checkPermission from '@/utils/permission'
import { allRegionPersonPage, allRegionPersonArchive, allRegionPersonImport, allRegionPersonRemove } from '@/api/people/peoplessc.js';

// 引入两个 mixin
import tableFooterMerge from "@/views/profit/sscManager/tableFooterMerge.js";
import tableColumnMerge from "@/views/profit/sscManager/tableColumnMerge.js";

export default {
  name: "scanCodePage",
  // 同时使用两个 mixin
  mixins: [tableFooterMerge, tableColumnMerge],
  components: {
    MyContainer, vxetablebase, departmentEdit
  },

  data() {
    return {
      // 配置列合并：将 deptName 和 dept 两列合并，显示 deptName 的值
      tableMergeColumn: {
        column: ['deptName', 'dept'], // 需要合并的列
        default: 'deptName' // 合并后显示 deptName 的值
      },
      
      // 原有的汇总行合并配置保持不变
      mergeColumn: {
        column: ['calculateMonth', 'type', 'regionName', 'deptName', 'dept'],
        default: 'type'
      },
      
      // 原有的行合并配置保持不变
      somerow: 'regionName,type,calculateMonth',
      
      // 其他数据保持不变
      tableData: [],
      loading: false,
      // ... 其他原有数据
    }
  },

  methods: {
    // 原有的方法保持不变，新的合并方法由 mixin 提供
    
    // 如果需要条件合并，可以这样配置：
    setupConditionalMerge() {
      this.tableMergeColumn = {
        column: ['deptName', 'dept'],
        default: 'deptName',
        condition: (row) => {
          // 例如：只有当 deptName 包含 "小计" 时才合并
          return row.deptName && row.deptName.includes('小计');
        }
      };
    },
    
    // ... 其他原有方法保持不变
  }
}
</script>

<style scoped>
/* 样式保持不变 */
</style>
