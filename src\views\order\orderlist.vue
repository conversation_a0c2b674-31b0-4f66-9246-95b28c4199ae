<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item label="时间:">
          <el-date-picker
            v-model="Filter.timerange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="线上订单号:">
          <el-input v-model="Filter.OrderNo" />
        </el-form-item>
        <el-form-item label="快递单号:">
          <el-input v-model="Filter.ExpressNo" />
        </el-form-item>
        <el-form-item label="快递公司:">
          <el-input v-model="Filter.ExpressCompany" />
        </el-form-item>
        <el-form-item label="店铺名称:">
          <el-input v-model="Filter.ShopName" />
        </el-form-item>
        <el-form-item label="状态:">
          <el-input v-model="Filter.Status" />
        </el-form-item>
        <el-form-item label="异常类型:">
          <el-input v-model="Filter.ErrorType" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <a href="../static/excel/订单导入模板.xlsx">
            <el-button type="primary">下载导入魔板</el-button>
          </a>
        </el-form-item>
        <el-form-item>
            <el-button size="small" type="primary" @click="startImport">导入订单</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <el-table
      v-loading="listLoading"
      :data="OrderList"
      highlight-current-row
      :lazy="true"
      height="'100%'"
      style="width: 100%; height: 100%"
      @selection-change="onSelsChange"
      :default-expand-all="false">
      <!-- <el-table-column type="selection" width="40" /> -->
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column prop="batchNumber" label="批次号" width="180" />
      <el-table-column prop="orderNoInner" label="内部订单号" width="100" />       
      <el-table-column prop="orderNo" label="线上订单号" width="180" />
      <el-table-column prop="orderTime" label="订单日期" width="150" />
      <el-table-column prop="buyerAccount" label="买家账号" width="150" />
      <el-table-column prop="shopName" label="店铺名称" width="120" />
      <el-table-column prop="amont" label="应付金额" width="100" />
      <el-table-column prop="amonted" label="已付金额" width="100" />
      <el-table-column prop="freightMoney" label="运费" width="100" />
      <el-table-column prop="status" label="状态" width="180" />
      <el-table-column prop="errorType" label="异常类型" width="100" />
      <el-table-column prop="buyerRmark" label="卖家备注" width="100" />
      <el-table-column prop="expressCompany" label="快递公司" width="100" />
      <el-table-column prop="expressNo" label="快递单号" width="100" />
      <el-table-column prop="consigneeAddress" label="收货地址" width="100" />
      <el-table-column prop="orderType" label="订单类型" width="100" />
      <el-table-column prop="sendTime" label="发货日期" width="150" />
      <el-table-column prop="sendWarehouse" label="发货仓" width="100" />
      <el-table-column prop="payTime" label="付款日期" width="150" />
      <el-table-column prop="note" label="便签" width="100" />
      <el-table-column prop="createTime" label="创建时间" :formatter="formatCreatedTime" width="160"/>
      <el-table-column prop="createUserName" label="创建者" width="100" />
    </el-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getOrderList"
      />
    </template>
     <el-dialog title="导入订单数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload
          ref="upload"
          class="upload-demo"
          :auto-upload="false"
          :multiple="false"
          :limit="1"
          action
          accept=".xlsx"
          :http-request="uploadFile"
          :file-list="fileList"
        >
         <template #trigger>
            <el-button size="small" type="primary">选取订单数据文件</el-button>
         </template> 
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { pageOrders, importOrderData} from "@/api/order/ordergoods";
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow },
  data() {
    return {
      Filter: {
        StartCreatedTime: "",
        EndCreatedTime: "", 
        timerange: "",
      },
      OrderList: [],
      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      userNameReadonly: true,
      fileList:[],
      importFilte:{companyid:null,warehouse:null},
      batchNumber:""
    };
  },
  async mounted() {
    await this.onSearch();
   // await this.getOrderList();
  },
  methods: {
    formatCreatedTime(row, column, time) {
      return formatTime(time, "YYYY-MM-DD HH:mm");
    },
    startImport(){
      this.dialogVisible=true;
    },
    cancelImport(){
      this.dialogVisible=false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
        this.$refs.upload.submit();
    },
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderData(form);
       this.$message({
                      message: '上传成功,正在导入中...',
                      type: "success",
                    }); 
    }, 
    // 查询
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getOrderList();
    },
    async getOrderList() {
      const para = {...this.Filter};
      if (this.Filter.timerange) {
        para.startCreatedTime = this.Filter.timerange[0];
        para.endCreatedTime = this.Filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...para,
      };
      this.listLoading = true;
      const res = await pageOrders(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.OrderList = data;
    },
    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
