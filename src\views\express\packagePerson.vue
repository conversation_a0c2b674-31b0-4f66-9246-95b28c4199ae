<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker
          v-model="timeRanges"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="publicCss"
          style="width: 350px"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="changeTime"
          :picker-options="pickerOptions"
        />
        <el-input v-model="ListInfo.packagePersonName" placeholder="集包人员名称" clearable class="publicCss" />
        <el-input v-model="ListInfo.packageCode" placeholder="集包编号" clearable class="publicCss" />
        <div>
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button type="warning" @click="clearFilters" style="margin-left: 10px;">清空</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                  @sortchange='sortchange' :summaryarry='summaryarry' :showsummary='true' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
                  :isSelectColumn="false" style="width: 100%;  margin: 0; " :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {pickerOptions} from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {packagePersonPage} from '@/api/order/orderData'

const tableCols = [
  // {sortable: 'custom', width: 'auto', align: 'center', prop: 'packagePersonId', label: '集包人员id',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'packagePersonName', label: '集包人员名称',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'startTime', label: '开始时间',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'endTime', label: '结束时间',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'packageCode', label: '集包编号',},
  {sortable: 'custom', width: 'auto', align: 'center', prop: 'duration', label: '时长(h)',},
  // {sortable: 'custom', width: 'auto', align: 'center', prop: 'createTime', label: '创建时间',},
]
export default {
  name: "packagePerson",
  components: {
    MyContainer, vxetablebase, dateRange
  },
  data() {
    return {
      that: this,
      summaryarry: {},
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,
        endTime: null,
        packagePersonId: null,
        packagePersonName: null,
        packageCode: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      if (e && e.length === 2) {
        [this.ListInfo.startTime, this.ListInfo.endTime] = e;
      } else {
        this.ListInfo.startTime = null;
        this.ListInfo.endTime = null;
      }
      await this.getList();
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }

      this.loading = true;
      try {
        const res = await packagePersonPage(this.ListInfo);
        if (res?.success) {
          var data = res.data;
          this.tableData = data.list;
          this.total = Number(data.total);
          this.summaryarry = data.summary
          this.loading = false;
        } else {
          this.loading = false;
          this.$message.error('获取列表失败');
        }
      } catch (error) {
        this.loading = false;
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({order, prop}) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    clearFilters() {
      this.timeRanges = [];
      this.ListInfo.startTime = null;
      this.ListInfo.endTime = null;
      this.ListInfo.packagePersonId = null;
      this.ListInfo.packagePersonName = null;
      this.ListInfo.packageCode = null;
      this.getList();
    },
    validateInputLength(value, maxLength) {
      if (value && value.length > maxLength) {
        this.$message.error(`输入内容不能超过 ${maxLength} 个字符`);
        return value.slice(0, maxLength);
      }
      return value;
    }
  },
  watch: {
    'ListInfo.packagePersonName': function (newVal) {
      this.ListInfo.packagePersonName = this.validateInputLength(newVal, 50);
    },
    'ListInfo.packageCode': function (newVal) {
      this.ListInfo.packageCode = this.validateInputLength(newVal, 50);
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}
</style>
