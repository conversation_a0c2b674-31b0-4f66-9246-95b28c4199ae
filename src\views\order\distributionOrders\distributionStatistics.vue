<template>
  <MyContainer>
    <div class="pattern">
      <div class="pattern_top">
        <div class="pattern_top_item">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 1)">
          </el-date-picker>
          <dateRange :startDate.sync="ListInfoTop.sendTimeStart" :endDate.sync="ListInfoTop.sendTimeEnd"
            style="width: 230px;margin-right: 5px;" class="publicCss" startPlaceholder="发货时间" endPlaceholder="发货时间" />
          <el-select v-model="ListInfoTop.shipperFxName" placeholder="货主分销" class="publicCss" clearable filterable>
            <el-option v-for="item in fxUserNames" :key="'2' + item" :label="item" :value="item" />
          </el-select>
          <el-button type="primary" @click="inquire">搜索</el-button>
          <el-button type="warning" @click="UserSet">用户设置</el-button>
          <el-button type="warning" @click="fxsMaintenanceVisible = true">分销商维护</el-button>
          <!-- <el-button type="warning" @click="OpenFlatEffectSet">坪效设置</el-button> -->
        </div>
        <div class="pattern_bottom_item">
          <div class="merchant_Css_top">
            <div class="imgCss">
              <img
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAnJJREFUWEe9lzFs01AQhv9LnKglIIUFxERgY0DQjTGhS0kjEbEgdUGiEgMDpBsoqXAlCp1aF4mFAXVlgSChwoLajbGRupOwUZZ2IATFLz54bt00bhLbz268WX53/3f/3Xu2Ca5rqtLKkhUvEsVuE3HG/Vzlnhk1gGtWp7PwZWm8cTQHOTfZEqdTZ8QzMEoqIn5jLIbRamoLmwbtyRgbQIqfSokNIlz3myjMOunIn6aWkxA2wPS8uXLSlbuBpROfFxNzNPWEM3FN1MNUpBpLQI5uldtrMaJ7qknCxFkWr9J0xdwCRtN7NywzNyQAh6kibKwywOREDA/yhNTY4U4+ZPm1x/jdAj5+Y3zdsoYyKgFI8bk7Mc/id3YZs8udaAH8ikvVyAHc4ju7gPGhg+064/xZwuQEYSbX60xhXkTjQL/KV95bx3r88n4cVy915yISgEG2310UaP7tLVA6MHOz60JogGE9f/S6g+8/e3exHE4Z41yhALwGTvb+6dvulF++ALx6qPVYogzgJe6oyEnfrgOnx4EbV3rPBDmks8uKQ/iurCE15rnVhy7oN6TugIEHkXuag6L4Ebc/SAa9C+S+lhDn0kGlAb/iQwHkQxWIIOKeAEEhgor7AvALoSLuG8ALQlU8EMAgiDDiNkC+3G4Q0UW/s+4Mpjwj3qwffxn5zXOwrkb5Stsg0OOAgdEsJ16jQsXMMrARTcZgWRKa2H9xFyqmwcBIXWCLV9dfJEs2QFHntCmEdGEkv2b/JWsJTctV9YNfMwdCCKGftBOy8mQyoUtxexe4u1bUOWMKU993g64F62r/1cz8A4RqDFT99DyxeXTVPyYYLokXKOfNAAAAAElFTkSuQmCC"
                alt="商家图标" class='imgCss_item'>
              <span class="typeface_name">昀晗代发</span>
              <!-- <span class="typeface_size">(厂家销售的订单及费用)</span> -->
            </div>
            <div class="merchant_number">
              <div class="shortNum">
                <span class="typeface_bold">{{ formatThousands(data.sendOrderCount) }}</span>
                <span class="salesText">订单量</span>
              </div>
              <div class="shortNum">
                <span class="typeface_bold">{{ formatThousands(data.sendGoodsCount) }}</span>
                <span class="salesText">商品数量</span>
              </div>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.exPressFee) }}</span>
                  <span class="salesText">快递费</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.realExPressFee) }}</span>
                  <span class="salesText">快递费(真实)</span>
                </div>
              </div>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.avgExPressFee) }}</span>
                  <span class="salesText">均快递费</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.avgRealExPressFee) }}</span>
                  <span class="salesText">均快递费(真实)</span>
                </div>
              </div>
              <span class="divider"></span>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.outWareFee) }}</span>
                  <span class="salesText">出仓费</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.realOutWareFee) }}</span>
                  <span class="salesText">出仓费(真实)</span>
                </div>
              </div>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.avgOutWareFee) }}</span>
                  <span class="salesText">均出仓费</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.avgRealOutWareFee) }}</span>
                  <span class="salesText">均出仓费(真实)</span>
                </div>
              </div>
              <span class="divider"></span>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.packFee) }}</span>
                  <span class="salesText">包材费</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.realPackFee) }}</span>
                  <span class="salesText">包材费(真实)</span>
                </div>
              </div>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.avgPackFee) }}</span>
                  <span class="salesText">均包材费</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.avgRealPackFee) }}</span>
                  <span class="salesText">均包材费(真实)</span>
                </div>
              </div>
              <span class="divider"></span>
              <div style="display: flex;flex-direction: column;">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.totalFee) }}</span>
                  <span class="salesText">合计金额</span>
                </div>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.realTotalFee) }}</span>
                  <span class="salesText">合金金额(真实)</span>
                </div>
              </div>
            </div>
          </div>
          <div class="merchant_Css_bottom">
            <div class="merchant_left-content">
              <div class="imgCss">
                <img
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAmpJREFUWEftlz1s00AUx/9nlaVDa9IMMARSMXQAhLOwBKnp2iXJWKQqsDEgkS4gsTQMzE2kInXDEgOj4w5dQEoqkaVLLAFrMXSAIZSEAYaiO/QcHExiJxcnWB36JA/JvY/fvffuTo+hXww1Ayg5KMgCSA6sh/lDCAtgFgR/gnzb9rpgvR+GqkLBJqAUw8QYw6YM7oC0yaYL4ARnNYBpYziaQFVY4GKFILoAproVwc77gcvIHm8wGGoSivJhgu2EN+V8hcGM6QAK4b1MYMlRYTDPN8PUXp2ZgzZ/HfaPT7B/HoWlsCkDQsaaAmbit5BZSGM5nnaCkxDA4uuUjAtfnaEAdxJr0OauIXtxFcnZS4FB2O7C9AFKSw+xufRIyvF/AdBT2ygk1qQBchdWYXXejt0PgSXwAux/baDeetODoXJ44fIH6zBuvnDWCWK/1YB+9BLW93cjNyAFQE1GzeaVWnrXaUiSu837eJ7aHghGNubnPZQPdwIzIwfwShtw0Fyu905CEIBLRNkgHT+RAnCPm+tAPTcP+lyJBGBYMb0AlcMdtE86jnohcRvJ2YTTDxNnQAbA/LKH3MF6T5XuEeqNyADqrQboRLR/dTOwdfUpilfuRQfgbp2639snkWUgqExnAGcZCJUB9wyPfE2GvAXem5Ig/K/iaswGw2W/RYKgc6zO/L12+/W+nXSw8f4xdO2ZbwB6iOjzFQGLwYiVoeCBzE6nriOgMxjxDBRem7pzGYecL/4ZTGJlIPIsVJA9Lp6S0YzS5cyHSimCTFTAeenf4dRbr+6oRiA0qN6QKeVIHYGPEKgCShX5Vt2r/xuo50uXNyKe/wAAAABJRU5ErkJggg=="
                  alt="昀晗/分销订单图标" class='imgCss_item'>
                <span class="typeface_name">昀晗销售</span>
              </div>
              <div class="merchant_number">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.yhSaleOrderCount) }}</span>
                  <span class="salesText">订单量</span>
                </div>
                <span class="divider"></span>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.yhSaleGoodsCount) }}</span>
                  <span class="salesText">商品数量</span>
                </div>
                <span class="divider"></span>
                <div class="shortNum">
                  <span class="typeface_bold">￥{{ formatThousands(data.yhSaleGoodsCost) }}</span>
                  <span class="salesText">合计金额</span>
                </div>
                <span class="divider"></span>
                <div class="shortNum">
                  <span class="typeface_bold">￥{{ formatThousands(data.yhInventoryMarginAmount) }}</span>
                  <span class="salesText">库存差额</span>
                </div>
              </div>
            </div>
            <div class="merchant_right-content">
              <div class="imgCss">
                <img
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAetJREFUWEfNl6FywkAQhv+zqAo0bR2uRYMoAix9g4YnKBUMEpAMovAADPcIYDGtAA0SV4pGoLBpfkg6NBAuuRzQnclkMne7+93eZu9WwCe2ZT0BeAZQAnDnH9f8njk2+TSFlIt9G8L7sC3rBkAdQEXTSVi1jguypsIWwHX+AeAxrJWY8xiNvJBy7QG8X2DlfuaOkPJN2JbFff6KuSJd9TwBpAPwomshpl6XANML7r2fd0EAW7mKRAIoFIBcTjl1O2G1Anq93Vsh4QCaTSCVUtn6O75cAq0WsNmc1FMDcNXZbDTn3uzJBBiPYwLouQ6tFRwB7jtXzndcYSQC8iEYoFYD0um4rnf6dF6tHrUVDNBuA8mkGQBaKZcNAzC5GFpKJgMUi6dhjQMwrN6+MlKqaBkHiLo5kQEY0lLJzF8wHAKDQcQciLpCzfnBfwH3VLcC+mG06oBO/Q+Kwny+OxeOSHAE+n3NoB5R0ypEKoDRCJjyKhGiDpwFYL8O8Kg+dWZoAZgsxbwb1HnjPxTmABuF24Mh3gNYB1QVTpUpvJCwDnDLDmVGADYKryo7ZxqXBGArxqbkGnLvNSbXiELX6Ywq/6M1Y+zd/rBxgXzoOv4a7Avp97c79hLAbdUIwkb1wVBifDv2eBwOhJSf+zZ/AGRyxzSQnYmqAAAAAElFTkSuQmCC"
                  alt="库存图标" class='imgCss_item'>
                <span class="typeface_name" @click="toLinkInventory" style="color:#409eff;cursor: pointer;">库存</span>
                <span class="typeface_size">(库存数据不受时间筛选隐藏)</span>
              </div>
              <div class="merchant_number">
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.inventoryCount) }}</span>
                  <span class="salesText">库存</span>
                </div>
                <span class="divider"></span>
                <div class="shortNum">
                  <span class="typeface_bold">￥{{ formatThousands(data.inventoryAmount) }}</span>
                  <span class="salesText">库存金额</span>
                </div>
                <span class="divider"></span>
                <div class="shortNum">
                  <span class="typeface_bold">{{ formatThousands(data.inventoryGoodsCount) }}</span>
                  <span class="salesText">编码数</span>
                </div>
              </div>
            </div>
            <div class="merchant_right-content" style="flex: 1;">
              <div class="imgCss" style="display: flex;justify-content: space-between;">
                <div>
                  <img
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAetJREFUWEfNl6FywkAQhv+zqAo0bR2uRYMoAix9g4YnKBUMEpAMovAADPcIYDGtAA0SV4pGoLBpfkg6NBAuuRzQnclkMne7+93eZu9WwCe2ZT0BeAZQAnDnH9f8njk2+TSFlIt9G8L7sC3rBkAdQEXTSVi1jguypsIWwHX+AeAxrJWY8xiNvJBy7QG8X2DlfuaOkPJN2JbFff6KuSJd9TwBpAPwomshpl6XANML7r2fd0EAW7mKRAIoFIBcTjl1O2G1Anq93Vsh4QCaTSCVUtn6O75cAq0WsNmc1FMDcNXZbDTn3uzJBBiPYwLouQ6tFRwB7jtXzndcYSQC8iEYoFYD0um4rnf6dF6tHrUVDNBuA8mkGQBaKZcNAzC5GFpKJgMUi6dhjQMwrN6+MlKqaBkHiLo5kQEY0lLJzF8wHAKDQcQciLpCzfnBfwH3VLcC+mG06oBO/Q+Kwny+OxeOSHAE+n3NoB5R0ypEKoDRCJjyKhGiDpwFYL8O8Kg+dWZoAZgsxbwb1HnjPxTmABuF24Mh3gNYB1QVTpUpvJCwDnDLDmVGADYKryo7ZxqXBGArxqbkGnLvNSbXiELX6Ywq/6M1Y+zd/rBxgXzoOv4a7Avp97c79hLAbdUIwkb1wVBifDv2eBwOhJSf+zZ/AGRyxzSQnYmqAAAAAElFTkSuQmCC"
                    alt="库存图标" class='imgCss_item'>
                  <span class="typeface_name" @click="toLinkInventory"
                    style="color:#409eff;cursor: pointer;">对冲金额</span>
                </div>
                <div>
                  <el-button type="text" @click="logDetailsVisible = true">日志</el-button>
                  <el-button type="text" @click="openBanlance" v-if="checkPermission('api:order:ShipperFxOrder:BalanceShipperFxByDate')">结算</el-button>
                </div>
              </div>
              <div class="merchant_number">
                <div style="display: flex;flex-direction: column;">
                  <div class="shortNum">
                    <span class="typeface_bold">{{ formatThousands(data.cwMergeFee) }}</span>
                    <span class="salesText">合计金额</span>
                  </div>
                  <div class="shortNum">
                    <span class="typeface_bold">{{ formatThousands(data.cwRealMergeFee) }}</span>
                    <span class="salesText">合计金额(真实)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pattern_bottom">
        <div>
          订单数据分析
        </div>
        <div>
          <el-date-picker v-model="orderTime" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 2)">
          </el-date-picker>
          <el-select v-model="orderAnalysis.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
            <el-option v-for="item in fxUserNames" :label="item" :value="item" />
          </el-select>
          <el-button type="primary" @click="onOrderAnalysisMethod">搜索</el-button>
        </div>
        <div v-loading="orderLoading" style="height: 390px;">
          <buschar v-if="orderAnalysisChar.visible" ref="orderAnalysisChar" :analysisData="orderAnalysisChar.data"
            :thisStyle="{
              width: '100%',
              height: '390px',
              'box-sizing': 'border-box',
            }">
          </buschar>
        </div>
      </div>
      <div class="pattern_bottom">
        <div>
          库存数据分析
        </div>
        <div>
          <el-date-picker v-model="inventoryTime" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions"
            style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
            @change="changeTime($event, 3)">
          </el-date-picker>
          <el-select v-model="inventoryAnalysis.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
            <el-option v-for="item in fxUserNames" :label="item" :value="item" />
          </el-select>
          <el-input v-model.trim="inventoryAnalysis.goodsCode" placeholder="商品编码" maxlength="50" clearable
            class="publicCss" />
          <el-button type="primary" @click="onInventoryAnalysisMethod">搜索</el-button>
        </div>
        <div v-loading="inventoryLoading" style="height: 390px;">
          <buschar v-if="inventoryAnalysisChar.visible" ref="inventoryAnalysisChar"
            :analysisData="inventoryAnalysisChar.data" :thisStyle="{
              width: '100%',
              height: '390px',
              'box-sizing': 'border-box',
            }">
          </buschar>
        </div>
      </div>
    </div>

    <el-dialog :title="fatEffectSetDialog.title" :visible.sync="fatEffectSetDialog.visiable" width="50%" v-dialogDrag>
      <fatEffectSet style="height:600px"></fatEffectSet>
    </el-dialog>

    <el-dialog :title="userSetDialog.title" :visible.sync="userSetDialog.visiable" width="50%" v-dialogDrag>
      <userSet style="height:600px" v-if="userSetDialog.visiable"></userSet>
    </el-dialog>

    <el-dialog title="日志" :visible.sync="logDetailsVisible" width="50%" v-dialogDrag>
      <shipperLogDetails style="height:600px" v-if="logDetailsVisible">
      </shipperLogDetails>
    </el-dialog>

    <el-dialog title="结算" :visible.sync="BalanceVisible" width="20%" v-dialogDrag>
      <div style="display: flex;">
        <el-select v-model="balanceData.shipperFxName" placeholder="货主分销" class="publicCss" clearable
          style="width: 230px;margin-right: 5px;">
          <el-option v-for="item in fxUserNames" :label="item" :value="item" />
        </el-select>
        <dateRange :startDate.sync="balanceData.startTime" :endDate.sync="balanceData.endTime" v-if="BalanceVisible"
          style="width: 230px;margin-right: 5px;" class="publicCss" startPlaceholder="结算时间" endPlaceholder="结算时间" />
      </div>
      <div style="display: flex;justify-content: center;margin-top: 20px;">
        <el-button @click="BalanceVisible = false">取消</el-button>
        <el-button type="primary" @click="BalanceSubmit">确认</el-button>
      </div>
    </el-dialog>

      <el-dialog title="分销商维护" :visible.sync="fxsMaintenanceVisible" width="50%" v-dialogDrag :before-close="fsxClose">
      <distributorMaintenance style="height:600px" v-if="fxsMaintenanceVisible" @getFxNameList="getFxNameList">
      </distributorMaintenance>
    </el-dialog>


  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getShipperFxNameList, getSumShipperFxSaleInfo, getSumShipperFxInventoryInfo, getSumYunHanFxSaleInfo, getSumShipperFxOrdersAnalysis, getSumShipperFxGoodsAnalysis, BalanceShipperFxByDate } from '@/api/order/shipperFxOrder';
import { replaceSpace } from "@/utils/getCols";
import dayjs from "dayjs";
import buschar from "@/components/Bus/buschar";
import fatEffectSet from "@/views/order/distributionOrders/fatEffectSet.vue"
import userSet from "@/views/order/distributionOrders/distributionUserSet.vue"
import { todayOrderTimeFrame } from "@/api/order/orderData";
import { getMonthBill_PlatFormChart } from "@/api/express/express";
import shipperLogDetails from './shipperLogDetails.vue'
import dateRange from "@/components/date-range/index.vue";
import distributorMaintenance from './distributorMaintenance'
export default {
  name: "warehouseTrendChart",
  components: {
    MyContainer,
    vxetablebase,
    buschar,
    fatEffectSet,
    dateRange,
    userSet,
    shipperLogDetails,
    distributorMaintenance
  },

  data() {
    return {
      fxsMaintenanceVisible:false,
      BalanceVisible: false,
      balanceData: {
        startTime: null,
        endTime: null,
        shipperFxName: null,
      },
      orderAnalysisChar: { visible: false, title: "", data: {} },
      inventoryAnalysisChar: { visible: false, title: "", data: {} },
      that: this,
      ListInfoTop: {
        payTimeStart: null, //开始时间
        payTimeEnd: null, //结束时间
        shipperFxName: null,
      },
      saleOrderCount: 0,//订单数
      sendOrderCount: 0,//发货中订单数
      sellsaleGoodsCount: 0,//销售商品数
      sendGoodsCount: 0,//发货中商品数
      expressFee: 0,//快递
      pingFee: 0,//坪效
      logDetailsVisible: false,
      saleGoodsCount: 0,//商品数
      saleGoodsCost: 0,//商品成本
      goodsCount: 0,//编码数
      inventoryCount: 0,//库存
      inventoryAmount: 0,//库存金额
      balanceInventoryCount: 0,
      inventoryGoodCount: 0,
      inventoryMarginAmount: 0,
      realExPressFeeOrderCount: 0,
      realExPressFee: 0,
      avgRealExPressFee: 0,
      realOutWareFeeOrderCount: 0,
      realOutWareFee: 0,
      avgRealOutWareFee: 0,
      realPackFeeOrderCount: 0,
      realPackFee: 0,
      avgRealPackFee: 0,
      orderAnalysis: {
        payTimeStart: null, //开始时间
        payTimeEnd: null, //结束时间
        shipperFxName: null,
      },
      inventoryAnalysis: {
        payTimeStart: null, //开始时间
        payTimeEnd: null, //结束时间
        shipperFxName: null,
        goodsCode: null,
      },
      timeRanges: [],
      orderTime: [], 
      fxUserNames: [],
      inventoryTime: [],
      data: {},
      loading: false,
      orderLoading: false,
      inventoryLoading: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '近7日',
            onClick(picker) {
              const end = dayjs().startOf('day');
              const start = end.subtract(7, 'day');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },
          {
            text: '近15日',
            onClick(picker) {
              const end = dayjs().startOf('day');
              const start = end.subtract(15, 'day');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },
          {
            text: '近30日',
            onClick(picker) {
              const end = dayjs().startOf('day');
              const start = end.subtract(30, 'day');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },
          {
            text: '近半年',
            onClick(picker) {
              const end = dayjs().startOf('day');
              const start = end.subtract(6, 'month');
              picker.$emit('pick', [start.toDate(), end.toDate()]);
            }
          },
        ]
      },
      fatEffectSetDialog: {
        visiable: false,
        title: "坪效设置",
      },
      userSetDialog: {
        visiable: false,
        title: "用户设置",
      },
    };
  },
  async mounted() {
    this.ListInfoTop.payTimeStart = dayjs().subtract(1, 'month').startOf('day').format('YYYY-MM-DD');
    this.ListInfoTop.payTimeEnd = dayjs().startOf('day').format('YYYY-MM-DD');
    this.timeRanges = [
      dayjs().subtract(1, 'month').startOf('day').format('YYYY-MM-DD'),
      dayjs().startOf('day').format('YYYY-MM-DD')
    ];
    this.orderAnalysis.payTimeStart = dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD');
    this.orderAnalysis.payTimeEnd = dayjs().startOf('day').format('YYYY-MM-DD');
    this.orderTime = [
      dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD'),
      dayjs().startOf('day').format('YYYY-MM-DD')
    ];
    this.inventoryAnalysis.payTimeStart = dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD');
    this.inventoryAnalysis.payTimeEnd = dayjs().startOf('day').format('YYYY-MM-DD');
    this.inventoryTime = [
      dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD'),
      dayjs().startOf('day').format('YYYY-MM-DD')
    ];
    await this.inquire()
    await this.onOrderAnalysisMethod();
    await this.onInventoryAnalysisMethod();
    await this.getFxNameList()
    // await getShipperFxNameList()
    //   .then(({ data }) => {
    //     this.fxUserNames = data;
    //   })
  },
  methods: {
   async fsxClose(){
      this.fxsMaintenanceVisible = false
      await this.getFxNameList()
    },
   async getFxNameList(){
      const {data} = await getShipperFxNameList()
      this.$set(this, 'fxUserNames', data)
    },
    openBanlance() {
      this.balanceData = {
        startTime: null,
        endTime: null,
        shipperFxName: null,
      }
      this.BalanceVisible = true
    },
    async BalanceSubmit() {
      if (this.balanceData.startTime == null || this.balanceData.endTime == null) {
        this.$message.error('请选择结算时间');
        return;
      }
      if (this.balanceData.shipperFxName == null) {
        this.$message.error('请选择货主分销');
        return;
      }
      const { success } = await BalanceShipperFxByDate(this.balanceData)
      if (success) {
        this.$message.success('结算成功')
        this.BalanceVisible = false
      }
    },
    toLinkInventory() {
      this.$emit('changeTab', 'first4')
    },
    formatThousands(value) {
      if (value === null || value === undefined) {
        return value;
      }
      return value.toLocaleString();
    },
    async onOrderAnalysisMethod() {
      this.orderLoading = true;
      this.orderAnalysisChar.visible = false;
      const { data, success } = await getSumShipperFxOrdersAnalysis(this.orderAnalysis);
      this.orderLoading = false;
      if (success) {
        data.series.map((item) => {
          item.itemStyle = {
            normal: {
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 14,
                },
              },
            },
          };

          item.emphasis = {
            focus: "series",
          };
          item.smooth = false;
        });
        this.orderAnalysisChar.visible = true;
        this.orderAnalysisChar.data = data;
      }
    },
    async onInventoryAnalysisMethod() {
      this.inventoryLoading = true;
      this.inventoryAnalysisChar.visible = false;
      const { data, success } = await getSumShipperFxGoodsAnalysis(this.inventoryAnalysis);
      this.inventoryLoading = false;
      if (success) {
        data.series.map((item) => {
          item.itemStyle = {
            normal: {
              label: {
                show: true,
                position: "top",
                textStyle: {
                  fontSize: 14,
                },
              },
            },
          };

          item.emphasis = {
            focus: "series",
          };
          item.smooth = false;
        });
        this.inventoryAnalysisChar.visible = true;
        this.inventoryAnalysisChar.data = data;
      }
    },
    async inquire() {
      await this.onShipperFxSaleInfo();
      // await this.onFxInventoryInfo();
      // await this.onFxSaleInfo();
    },

    async onShipperFxSaleInfo() {
      const { data, success } = await getSumShipperFxSaleInfo(this.ListInfoTop);
      if (success) {
        this.data = data
        // this.saleOrderCount = data.saleOrderCount;
        // this.balanceInventoryCount = data.balanceInventoryCount;
        // this.inventoryGoodCount = data.inventoryGoodCount
        // this.sendOrderCount = data.sendOrderCount;
        // this.sellsaleGoodsCount = data.saleGoodsCount;
        // this.sendGoodsCount = data.sendGoodsCount;
        // this.expressFee = data.expressFee;
        // this.pingFee = data.pingFee;
        // this.realExPressFeeOrderCount = data.realExPressFeeOrderCount;
        // this.realExPressFee = data.realExPressFee;
        // this.avgRealExPressFee = data.avgRealExPressFee;
        // this.realOutWareFeeOrderCount = data.realOutWareFeeOrderCount;
        // this.realOutWareFee = data.realOutWareFee;
        // this.avgRealOutWareFee = data.avgRealOutWareFee;
        // this.realPackFeeOrderCount = data.realPackFeeOrderCount;
        // this.realPackFee = data.realPackFee;
        // this.avgRealPackFee = data.avgRealPackFee;
      }
    },
    async onFxInventoryInfo() {
      const { data, success } = await getSumShipperFxInventoryInfo({ shipperFxName: this.ListInfoTop.shipperFxName });
      if (success) {
        this.goodsCount = data.goodsCount;
        this.inventoryCount = data.inventoryCount;
        this.inventoryAmount = data.inventoryAmount;
      }
    },
    //
    async onFxSaleInfo() {
      const { data, success } = await getSumYunHanFxSaleInfo({ payTimeStart: this.ListInfoTop.payTimeStart, payTimeEnd: this.ListInfoTop.payTimeEnd, shipperFxName: this.ListInfoTop.shipperFxName });
      if (success) {
        this.saleGoodsCount = data.saleGoodsCount;
        this.saleGoodsCost = data.saleGoodsCost;
        this.inventoryMarginAmount = data.inventoryMarginAmount;
      }
    },

    async changeTime(e, val) {
      if (val == 1) {
        this.ListInfoTop.payTimeStart = e ? e[0] : null;
        this.ListInfoTop.payTimeEnd = e ? e[1] : null;
      } else if (val == 2) {
        this.orderAnalysis.payTimeStart = e ? e[0] : null;
        this.orderAnalysis.payTimeEnd = e ? e[1] : null;
      } else if (val == 3) {
        this.inventoryAnalysis.payTimeStart = e ? e[0] : null;
        this.inventoryAnalysis.payTimeEnd = e ? e[1] : null;
      }
    },
    async OpenFlatEffectSet() {
      this.fatEffectSetDialog.visiable = true;
    },
    async UserSet() { 
      this.userSetDialog.visiable = true;
    },
  },
};
</script>

<style scoped lang="scss">
.publicCss {
  width: 10%;
  margin-right: 10px;
}

.pattern {
  width: 100%;
  height: 1500px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #ececec;
  padding: 5px;
  box-sizing: border-box;

  .pattern_top,
  .pattern_bottom {
    flex: 1;
    background-color: white;
    display: flex;
    flex-direction: column;
    padding: 10px;
  }
}

.pattern_bottom {
  gap: 10px;
}

.pattern_top_item {
  display: flex;
  padding: 10px;
}

.pattern_bottom_item {
  display: flex;
  flex: 1;
  padding: 10px;
  flex-direction: column;
  gap: 10px;
}

.merchant_Css_top {
  flex: 1;
  background-color: #f0f2f5;
  border-radius: 5px;
  padding: 10px;
}

.merchant_Css_bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 50%;
}

.merchant_number {
  display: flex;
  // justify-content: space-between;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  height: 90%;
}

// .merchant_number>div {
//   flex:1;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   text-align: center;
//   flex-direction: column;
//   position: relative;
//   gap: 10px;
// }
.merchant_number {
  .shortNum {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
    position: relative;
    gap: 10px;
  }

  .longNum {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: right;
    flex-direction: column;
    position: relative;
    //gap: 10px;

    .longNum_item {
      display: flex;

      .longNum_item_left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin: 0 3px 3px 0;

        &:last-child {
          margin: 0
        }
      }
    }
  }

}

.divider {
  width: 1px;
  height: 60%;
  background-color: #dddddd;
  margin: 0 10px;
  flex-shrink: 0;
}






.merchant_left-content,
.merchant_right-content {
  flex: 2;
  border-radius: 5px;
  padding: 10px;
  background-color: #f0f2f5;
  margin-left: 5px;
  height: 90%;
}

.imgCss {
  display: flex;
  align-items: center;
}

.imgCss_item {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-right: 5px;
}

.typeface_bold {
  font-weight: bold;
  font-size: 18px;
}

.typeface_name {
  font-size: 15px;
  font-weight: bold;
}

.typeface_size {
  font-size: 15px;
  margin-left: 5px;
  color: #999999;
}

.salesText {
  color: #c4c5c6;
}
</style>
