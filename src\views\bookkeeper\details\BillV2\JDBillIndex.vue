<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="京东账单" name="first1" style="height: 100%">
        <JDBill ref="refJDBill" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="京东钱包" name="first2" style="height: 100%" lazy>
        <JDPurseBill ref="refJDPurseBill" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="京东自营入仓账单" name="first3" style="height: 100%" lazy>
        <JDSelfOperated ref="refJDSelfOperated" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="先行赔付" name="first4" style="height: 100%" lazy>
        <AdvanceCompensation ref="refAdvanceCompensation" style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import JDBill from "./JDBill.vue";
import JDPurseBill from "./JDPurseBill.vue";
import JDSelfOperated from "./JDSelfOperated.vue";
import AdvanceCompensation from "./AdvanceCompensation.vue"
export default {
  name: "videoBillIndex",
  components: {
    MyContainer, JDBill, JDPurseBill, JDSelfOperated, AdvanceCompensation
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
