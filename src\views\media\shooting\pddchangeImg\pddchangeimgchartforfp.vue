<template>
    <!-- 任务列表 合计行弹窗 -->
    <div>
        <div class="sybj">
            <!--上新统计start-->
                <div class="tjnrnk">
                    <div class="tjbt">
                        <div style="width:45%;display: inline-block;">
                            <span>
                                <el-select size="mini" style="width: 20%;margin-right:2px;" filterable :clearable="true"
                                multiple  :collapse-tags="true"  
                                    v-model="mainviewfilter.platform" placeholder="平台" @change="onchangeplatfor">
                                    <el-option v-for="item in platformList" :key="item.value"  :label="item.label"
                                        :value="item.value"></el-option>
                                </el-select>
                            </span>
                            <span>
                                <el-select size="mini" style="width: 32%;margin-right:2px;" filterable :clearable="true"
                                    v-model="mainviewfilter.shopName" placeholder="店铺">
                                    <el-option v-for="item in shopListmain" :key="item.shopCode" :label="item.shopName"
                                        :value="item.shopCode"> </el-option>
                                </el-select>
                            </span>
                            <span>
                                <el-date-picker size="mini" style="position: relative; top: 1px; width: 45%;"
                                    type="daterange" align="right" unlink-panels range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                                    :picker-options="pickerOptions" v-model="mainviewfilter.timerange">
                                </el-date-picker>
                            </span>
                        </div>
                        <div style="width:30%;display: inline-block;">
                            <span>
                                <el-button size="mini"  type="primary"
                                    @click="initsearch">查询</el-button>
                            </span>

                            <span style="padding-left: 5rem;">
                                <el-button size="mini"  type="danger"
                                    @click="daliysearch">每日完成</el-button>
                            </span>
                        </div>

                        <div style="width: 25%;display: inline-block;text-align:right;">
                            <el-checkbox-group v-model="mainviewcheckList">
                                <el-checkbox style="margin: 5px 6px" label="任务列表">任务列表</el-checkbox>
                                <el-checkbox style="margin: 5px 6px" label="已完成">已完成</el-checkbox>
                                <el-checkbox style="margin: 5px 6px" label="统计列表">统计列表</el-checkbox>
                                <el-checkbox style="margin: 5px 6px" label="存档">存档</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                    <!--任务统计start-->
                    <div style="height: 500px" class="nrqk">
                        <div style="width: 100%; float: left; margin: 5px 0"></div>
                        <div style="padding: 8px 25px;font-size: 14px; color: #666;height:60px;line-height:30px;">
                            <span>照片总数：{{ taskzptoal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>视频总数：{{ tasksptotal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>详情总数：{{ taskxqtotal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>建模总数：{{ taskjmtotal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>工作时长：{{ taskgztoal }}</span>
                            <span style="margin-right: 20px"></span>
                        </div>
                        <div id="rwtjtb" style="width: 100%; height: 100%; margin: 0 auto"></div>
                    </div>
                    <!--任务统计end-->
            </div>
        </div>

        <el-dialog title="个人数据趋势" :visible.sync="view5show" width="80%" element-loading-text="拼命加载中"
            :close-on-click-modal="true" :append-to-body="true" v-dialogDrag>
            <div style="display:inline-block">
                <el-button style="padding: 0;">
                    <el-select filterable v-model="view5filter.groupName" placeholder="人员" clearable :collapse-tags="true">
                        <el-option v-for="item in groupNames" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-button>
                <el-date-picker style="width:310px;margin-left:13px; margin-top: 10px " type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    v-model="view5filter.timerange" :picker-options="pickerOptions" />
                <el-button type="primary" @click="showViewforper">查询</el-button>
            </div>
            <div>
                <buschar ref="view5" :analysisData="viewData5" :thisStyle="thisStyleView5" :gridStyle="gridStyleView5">
                </buschar>
            </div>
        </el-dialog>

        <el-dialog title="每日完成" :visible.sync="viewDailyshow" width="80%" element-loading-text="拼命加载中"
            :close-on-click-modal="true" :append-to-body="true" v-dialogDrag>
            <div>
                <buschar  v-if="viewDailyshow" :analysisData="viewDailyData" :thisStyle="thisStyleView5" :gridStyle="gridStyleView5">
                </buschar>
            </div>
        </el-dialog>
    </div>
</template> 
<script>


import * as echarts from "echarts";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform } from "@/utils/formruletools";
// import {
//     getOperationGroupStatistics, getTaskNcOrYwStatistics, modelingTaskDetailStatistics, modelingTaskStatistics
//     , getOperationGroupStatisticsForPerson, getFpStrpStatisticsForPerson, getTaskTotalStatistics, getShootingTaskStatistics
//     , getPlatmStrpStatistics, getTaskforPersonTableStatistics, getTaskInfoStatistics
//     , getShootingViewPersonAsync, getTaskScoreGroupStatistics, getTaskScoreStatistics,getTaskDailyCompletionStatistics
// } from '@/api/media/ShootingVideo';
import {
  getFpStrpStatisticsForPerson, getTaskTotalStatistics, getShootingTaskStatistics
    , getTaskforPersonTableStatistics, getTaskInfoStatistics
    , getShootingViewPersonAsync,getTaskDailyCompletionStatistics
} from '@/api/media/pddchangeimg';
import buschar from '@/components/Bus/buscharforShooting.vue';
const tableCols = [
    { istrue: true, prop: 'name', label: '负责人' },
    { istrue: true, prop: 'yfpNum', label: '已分配', sortable: 'custom' },
    { istrue: true, prop: 'overNum', label: '已完成', sortable: 'custom' },
    { istrue: true, prop: 'overRate', label: '完成率（%）', sortable: 'custom' },
    { istrue: true, prop: 'lastTask', label: '剩余任务', sortable: 'custom' },
    { istrue: true, prop: 'yqr', label: '已确认', sortable: 'custom' },
    { istrue: true, prop: 'dqr', label: '待确认', sortable: 'custom' },
    { istrue: true, prop: 'cqts', label: '出勤天数', sortable: 'custom' }
];
export default {
    components: { buschar, vxetablebase },
    data() {
        return {
            //所有任务区域
            maintasktotal: null,
            maintaskovertotal: null,
            maintasklasttotal: null,
            maintaskconfirmtotal: null,
            maintaskawaitconfirmtotal: null,
            tasktotal: null,
            taskovertotal: null,
            tasklasttotal: null,
            taskconfirmtotal: null,
            taskawaitconfirmtotal: null,
            //所有任务区域end
            listLoading: false,
            tableCols: tableCols,
            tasklist: [],
            groupNames: [],
            viewData5: [],
            viewDailyData:{},
            thisStyleView5: { width: '100%', height: '400px', 'box-sizing': 'border-box', 'line-height': '240px' },
            gridStyleView5: { left: '1%', right: 15, bottom: 20, top: '10%', containLabel: true },
            view5filter: {
                startTime: null,
                endTime: null,
                timerange: [],
                groupName: null
            },
            addLoading: false,
            view5show: false,
            viewDailyshow: false,
            that: this,
            taoxisxtotal: null,//淘系上新
            pddsxtotal: null,//拼多多上新
            othersxtotal: null,//其他上新
            taskzptoal: null,//照片总数：
            tasksptotal: null,//视频总数：
            taskxqtotal: null,//详情总数：
            taskjmtotal: null,//建模总数：
            taskgztoal: null,//工作时长： 
            pager: {},
            //建模统计
            jmtaskwctotal: null,//完成款数：
            jmtaskxytotal: null,//图片张数：
            jmtasksptotal: null,//视频个数：

            expressData: [],//上新统计数据
            sxDataDetail: [],//上新统计数据详情
            taskFpData: [],//上新统计数据
            taskFpDataDetail: [],//上新统计数据详情

            moedlqyData: [],//类型统计
            moedlperData: [],//人员类型统计
            moedlperDataDetail: [],//人员类型统计详情
            sxData: [],
            tableData: [],
            platformList: [],
            shopListmain: [],
            mainviewcheckList: ['任务列表', '已完成'],
            mainviewTaskcheckList: ['照片拍摄', '排版任务', '建模任务'],
            mainviewfilter: {
                startTime: null,
                endTime: null,
                timerange: [],
                shopName: null,
                platform: null,
            },
            pickerOptions: { disabledDate(time) { return time.getTime() > Date.now() } },
        }
    },
    async mounted() {
        await this.onGetdrowList();
        await this.getShootingViewPer();
        this.initsearch();
        console.log(2222);
    },
    methods: {
        async initsearch() {
            //this.getshangxinInfo();
            this.gettotaltaskInfo();
           // this.onechart();
            this.twochart();
           // this.threechart();
           // this.fourchart();
            //this.getconfirmListInfo();
            //this.fivechart();
            //this.sixchart();
        },
        async daliysearch()
        {
            this.viewDailyData={};
            this.viewDailyshow=false;
            this.listLoading = true;
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res = await getTaskDailyCompletionStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platformList": this.mainviewfilter.platform,
                ...this.pager,
            });
            this.listLoading = false;

            res.series.map((item)=>{
                item.itemStyle = {
                    "normal": {
                        "label": {
                            "show": true,
                            "position": "top",
                            "textStyle": {
                                "fontSize": 14
                            }
                        }
                    }
                }

                item.emphasis = {
                    "focus":"series"
                }
                item.smooth = false;
            })
            this.viewDailyData = res;
            
            this.viewDailyshow=true;
        
           
        },
        //获取下拉数据
        async onGetdrowList() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        //切换平台
        async onchangeplatfor(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 200 });
            this.mainviewfilter.shopName = null;
            this.shopListmain = res1.data.list;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.getconfirmListInfo();
        },

        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await getShootingViewPersonAsync();
            if (res?.success) {
                this.groupNames = res?.data?.fpallList?.map(item => { return item });
            }
        },
        //获取确认列表信息
        async gettotaltaskInfo() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0]; 
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            this.listLoading = true;
            var res = await getTaskInfoStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platformList": this.mainviewfilter.platform,
                ...this.pager,
            });
            this.listLoading = false;
            if (res.success) {
                this.maintasktotal = res.data.maintasktotal;
                this.maintaskovertotal = res.data.maintaskovertotal;
                this.maintasklasttotal = res.data.maintasklasttotal;
                this.maintaskconfirmtotal = res.data.maintaskconfirmtotal;
                this.maintaskawaitconfirmtotal = res.data.maintaskawaitconfirmtotal;
                this.tasktotal = res.data.tasktotal;
                this.taskovertotal = res.data.taskovertotal;
                this.tasklasttotal = res.data.tasklasttotal;
                this.taskconfirmtotal = res.data.taskconfirmtotal;
                this.taskawaitconfirmtotal = res.data.taskawaitconfirmtotal;
            }
        },
        //获取确认列表信息
        async getconfirmListInfo() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            this.listLoading = true;
            var res = await getTaskforPersonTableStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platformList": this.mainviewfilter.platform,
                ...this.pager,
            });
            this.listLoading = false;
            if (res.success) {
                this.tasklist = res.data;
            }
        },
        // //获取淘系，拼多多，其他上新数据
        // async getshangxinInfo() {
        //     //获取数据
        //     if (this.mainviewfilter.timerange) {
        //         this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        //         this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
        //     } else {
        //         this.mainviewfilter.startTime = null;
        //         this.mainviewfilter.endTime = null;
        //     }
        //     var res = await getPlatmStrpStatistics({
        //         "startTime": this.mainviewfilter.startTime,
        //         "endTime": this.mainviewfilter.endTime,
        //         "checkdata": this.mainviewcheckList,
        //         "checkTaskType": this.mainviewTaskcheckList,
        //         "shopName": this.mainviewfilter.shopName,
        //         "platformList": this.mainviewfilter.platform,
        //     });
        //     if (res.success) {
        //         this.taoxisxtotal = res.data.taoxinum;
        //         this.pddsxtotal = res.data.pddnum;
        //         this.othersxtotal = res.data.allnum;
        //     }
        // },
        // //上新统计图表-组数据详情
        // async OperationGroupForPerson(params) {
        //     //获取数据
        //     if (this.mainviewfilter.timerange) {
        //         this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        //         this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
        //     } else {
        //         this.mainviewfilter.startTime = null;
        //         this.mainviewfilter.endTime = null;
        //     }
        //     var res1 = await getOperationGroupStatisticsForPerson({
        //         "startTime": this.mainviewfilter.startTime,
        //         "endTime": this.mainviewfilter.endTime,
        //         "checkdata": this.mainviewcheckList,
        //         "checkTaskType": this.mainviewTaskcheckList,
        //         "shopName": this.mainviewfilter.shopName,
        //         "platformList": this.mainviewfilter.platform,
        //         "groupName": params.name
        //     })
        //     this.sxDataDetail = res1;
        // },
        // // // 上新统计图表
        // async onechart() {
        //     //获取数据
        //     if (this.mainviewfilter.timerange) {
        //         this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
        //         this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
        //     } else {
        //         this.mainviewfilter.startTime = null;
        //         this.mainviewfilter.endTime = null;
        //     }
        //     var res1 = await getOperationGroupStatistics({
        //         "viewName": "viewMain", "startTime": this.mainviewfilter.startTime,
        //         "endTime": this.mainviewfilter.endTime,
        //         "checkdata": this.mainviewcheckList,
        //         "CheckTaskType": this.mainviewTaskcheckList,
        //         "checkTaskType": this.mainviewfilter.shopName,
        //         "platformList": this.mainviewfilter.platform
        //     });
        //     this.expressData = res1;
        //     var myChart = echarts.init(document.getElementById("sxtjtb"));
        //     const option = {
        //         tooltip: {
        //             trigger: "axis",
        //             axisPointer: {
        //                 type: "cross",
        //                 crossStyle: {
        //                     color: "#999",
        //                 },
        //             },
        //         },
        //         toolbox: {
        //             show: true,
        //             orient: 'vertical',
        //             left: 'right',
        //             top: 'center',
        //             feature: {
        //                 mark: { show: true },
        //                 dataView: { show: true, readOnly: false },
        //                 magicType: { show: true, type: ['line', 'bar', 'stack'] },
        //                 restore: { show: true },
        //                 saveAsImage: { show: true }
        //             }
        //         },
        //         xAxis: [
        //             {
        //                 type: "category",
        //                 data: this.expressData.xAxis,
        //                 axisPointer: {
        //                     type: "shadow",
        //                 },
        //             },
        //         ],
        //         yAxis: [
        //             {
        //                 type: "value",
        //                 name: "",
        //                 axisLabel: {
        //                     formatter: "{value}",
        //                 },
        //             },
        //         ],
        //         grid: {
        //             top: "40px",
        //             bottom: "50px",
        //             left: "60",
        //             right: "65",
        //         },
        //         series: [
        //             {
        //                 name: "上新款数",
        //                 type: "bar",
        //                 id: "sales",
        //                 barGap: "5%",
        //                 label: {
        //                     show: true,
        //                     position: "top",
        //                 },
        //                 emphasis: {
        //                     focus: "series",
        //                 },
        //                 tooltip: {
        //                     valueFormatter: function (value) {
        //                         return value + "";
        //                     },
        //                 },
        //                 data: this.expressData.series[0].data,
        //             },

        //         ],
        //         graphic: [
        //             {
        //                 type: "text",
        //                 left: "right",
        //                 top: 12,
        //                 style: {
        //                     text: "",
        //                     fontSize: 14,
        //                 },

        //             }
        //         ],
        //     };
        //     myChart.on("click", async params => {
        //         if (params && params.name != "" && this.expressData.xAxis.includes(params.name)) {
        //             await this.OperationGroupForPerson(params);
        //             if (this.sxDataDetail.length == 0) {
        //                 return;
        //             }
        //             myChart.setOption({
        //                 legend: {
        //                     top: 5,
        //                     data: ["每日总上新", "每日组上新"],
        //                 },
        //                 xAxis: {
        //                     data: this.sxDataDetail.xAxis,
        //                 },
        //                 series: [
        //                     {
        //                         name: "每日总上新",
        //                         label: {
        //                             show: true,
        //                             position: "top",
        //                         },
        //                         type: "line",
        //                         id: "sales",
        //                         data: this.sxDataDetail.series[1].data,
        //                         universalTransition: {
        //                             enabled: true,
        //                             divideShape: "clone",
        //                         },
        //                     },
        //                     {
        //                         name: "每日组上新",
        //                         type: "line",
        //                         id: "sales2",
        //                         label: {
        //                             show: true,
        //                             position: "top",
        //                         },
        //                         data: this.sxDataDetail.series[0].data,
        //                         universalTransition: {
        //                             enabled: true,
        //                             divideShape: "clone",
        //                         },
        //                     },
        //                 ],
        //                 graphic: [
        //                     {
        //                         type: "text",
        //                         left: "right",
        //                         top: 12,
        //                         style: {
        //                             text: "Back",
        //                             fontSize: 14,
        //                         },
        //                         onclick: function () {
        //                             myChart.setOption(option, true);
        //                         },
        //                     },

        //                 ],
        //             });
        //         }
        //     });
        //     myChart.setOption(option);
        // },
        // //上新统计图表

        //任务统计图表-组数据详情
        async fpTaskDetails(params) {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var check = [params.name];
            var res1 = await getShootingTaskStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": check,
                "shopName": this.mainviewfilter.shopName,
                "platformList": this.mainviewfilter.platform,
            })
            this.taskFpDataDetail = res1;
            this.taskFpData.series.forEach((item, index) => {
                item.id = "sales" + index;
                item.label = { show: true, position: "top" };
                item.universalTransition = { enabled: true, divideShape: "clone", };
                if (item.name.indexOf("率") > -1) {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' } } };
                } else {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 } } } };
                }
            });
        },
        //任务统计图表
        async twochart() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await getTaskTotalStatistics({
                "viewName": "viewMain", "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platformList": this.mainviewfilter.platform
            });
            this.taskFpData = res1;
            this.taskFpData.series.forEach((item, index) => {
                item.id = "sales" + index;
                item.barGap = "5%";
                item.label = { show: true, position: "top" };
                item.emphasis = { focus: "series", };
                item.tooltip = { valueFormatter: function (value) { return value + ""; }, }
                if (item.name.indexOf("率") > -1) {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' } } };
                } else {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 } } } };
                }
            });
            this.taskFpData.summuydata.forEach((item, index) => {
                switch (item.seriesName) {
                    case "照片拍摄":
                        this.taskzptoal = item.seriestotal;
                        break;
                    case "视频拍摄":
                        this.tasksptotal = item.seriestotal;
                        break;
                    case "详情排版":
                        this.taskxqtotal = item.seriestotal;
                        break;
                    case "建模任务":
                        this.taskjmtotal = item.seriestotal;
                        break;
                    case "工作时长":
                        this.taskgztoal = item.seriestotal;
                        break;
                }
            });
            var myChart = echarts.init(document.getElementById("rwtjtb"));
            myChart.clear();
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                legend: {
                    top: 5,
                    selected: { "工作时长": false, "完成率": false },
                    data: ["分配数", "已完成", "剩余任务", "工作时长", "完成率"],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.taskFpData.xAxis,
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value}%",
                        },
                        splitLine: {
                            show: false // 不显示网格线
                        },
                    },
                ],
                grid: {
                    top: "55px",
                    bottom: "60px",
                    left: "60",
                    right: "65",
                },
                series: this.taskFpData.series,
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    },
                    {
                        type: "text",
                        left: "left",
                        top: 12,
                        style: {
                            text: '',
                            fontSize: 14,
                        },
                    },
                ],
            };
            myChart.on("click", async params => {
                if (params && this.taskFpData.xAxis.includes(params.name)) {
                    await this.fpTaskDetails(params);
                    if (this.taskFpDataDetail.length == 0) {
                        return;
                    }

                    myChart.setOption({
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                axisLabel: {
                                    formatter: "{value} ",
                                },
                            },
                            {
                                type: "value",
                                name: "",
                                axisLabel: {
                                    formatter: "{value}%",
                                },
                            },
                        ],
                        xAxis: {
                            data: this.taskFpDataDetail.xAxis
                        },
                        series: this.taskFpDataDetail.series,
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.clear();
                                    myChart.setOption(option);
                                },
                            },
                            {
                                type: "text",
                                left: "left",
                                top: 12,
                                style: {
                                    text: '',
                                    fontSize: 14,
                                },
                            },
                        ],
                    });
                } else
                    if (params && params.name != "" && this.taskFpDataDetail.xAxis.includes(params.name)) {
                        //弹出新的窗口，获取人员的工作时长和对应的
                        await this.OpenDetail(params);
                    }
            });
            myChart.setOption(option);
        },
        async OpenDetail(params) {
            this.view5filter.timerange = this.mainviewfilter.timerange;
            this.view5filter.groupName = params.name;
            this.view5filter.shopName = this.mainviewfilter.shopName;
            this.view5filter.platform = this.mainviewfilter.platform;
            await this.showViewforper();
        },
        async showViewforper() {
            //获取数据 
            if (this.mainviewcheckList.length == 0) {
                this.$message({ message: '必须选择一种任务列表', type: "error" });
                return;
            }
            if (this.mainviewTaskcheckList.length == 0) {
                this.$message({ message: '必须选择一种拍摄任务', type: "error" });
                return;
            }
            //获取数据
            if (this.view5filter.timerange) {
                this.view5filter.startTime = this.view5filter.timerange[0];
                this.view5filter.endTime = this.view5filter.timerange[1];
            } else {
                this.view5filter.startTime = null;
                this.view5filter.endTime = null;
            }
            this.addLoading = true;
            this.view5show = true;
            var res1 = await getFpStrpStatisticsForPerson({
                "startTime": this.view5filter.startTime,
                "endTime": this.view5filter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "groupName": this.view5filter.groupName,
                "platformList":this.mainviewfilter.platform, 
            })
            this.setOptions(res1);
            this.viewData5 = res1;
            console.log("打印数据999",res1)
            await this.$refs.view5.initcharts();
            this.addLoading = false;
        },
        setOptions(element) {
            element.series.forEach(s => {
                s.barMaxWidth = '50';
                if (s.name.indexOf("率") > -1) {
                    s.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' }, color: s.backColor } };
                } else {
                    s.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 } } } };
                }
                s.emphasis = { focus: 'series' };
                s.smooth = false;
            })
        },

        //  任务统计图表


    },
};
</script>
  
<style lang="scss" scoped>

.sybj {
    min-width: 1100px;
}


.el-menu-demo {
    text-align: center;
}


.tjbt {
    /* background-color: aquamarine; */
    /* font-weight: bold; */
    color: #333;
    line-height: 30px;
}


.tjnrnk {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 20px;
    float: left;
    border-radius: 6px;
}

</style>