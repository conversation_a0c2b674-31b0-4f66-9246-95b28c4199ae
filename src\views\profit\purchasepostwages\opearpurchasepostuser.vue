<template>
  <container>
    <template>
      <el-form
        :model="detailUserInfo"
        ref="ruleForm"
        label-width="130px"
        v-if="detailUserInfo" 
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="分公司" prop="company">
              <el-select
                v-model="detailUserInfo.company"
                clearable
                placeholder="分公司"
                @change="clearUsername"
              >
                <el-option label="义乌" value="义乌" />
                <el-option label="南昌" value="南昌" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="岗位" prop="purchasePost">
              <el-select
                v-model="detailUserInfo.purchasePost"
                filterable
                clearable
                placeholder="岗位"
                @change="changeSetPost"
                style="width: 150px; margin-right: 10px"
              >
                <el-option
                  v-for="item in postList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="取值">
              <el-select
                v-model="detailUserInfo.userName"
                filterable
                remote
                clearable
                placeholder="姓名"
                style="width: 150px; margin-right: 10px"
                @change="clearName"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userName"
                  :label="item.userName"
                  :value="item.userName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否启用" prop="enabled">
              <el-radio-group v-model="detailUserInfo.enabled">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 如果是核价组长或者是采购组长才显示 -->
        <el-row>
          <el-col :span="6">
            <el-form-item
              label="是否参与组员"
              prop="isParticipate"
              v-if="isShow"
            >
              <el-radio-group v-model="detailUserInfo.hasTeam">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="detailUserInfo.hasTeam && isShow">
            <el-form-item label="分公司" prop="brandCode">
              <el-select
                v-model="detailUserInfo.teamCompany"
                clearable
                placeholder="分公司"
              >
                <el-option label="义乌" value="义乌" />
                <el-option label="南昌" value="南昌" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="detailUserInfo.hasTeam && isShow">
            <el-form-item label="岗位" prop="brandCode">
              <el-select
                v-model="detailUserInfo.teamPosition"
                clearable
                placeholder="岗位"
                @change="changeSetTeamPost"
              >
                <el-option
                  v-for="item in postList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="detailUserInfo.hasTeam && isShow">
            <el-form-item label="取值">
              <el-select
                v-model="detailUserInfo.teamUsers"
                filterable
                remote
                clearable
                placeholder="姓名"
                multiple
                style="width: 150px; margin-right: 10px"
                collapse-tags
                @change="clearTeamName"
              >
                <el-option
                  v-for="item in teamUsers"
                  :key="item.userName"
                  :label="item.userName"
                  :value="item.ddUserId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-card style="width: 100%; height: 100%; overflow: auto">
        <div style="height: 100%">
          <div class="top_box">
            <el-select
              v-model="detailUserInfo.wagesSetType"
              @change="changeType"
            >
              <el-option
                v-for="item in checkGroup"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                clearable
              />
            </el-select>
            <el-button type="text" @click="addData">添加一行</el-button>
          </div>
          <el-table
            :data="detailUserInfo.detailSet"
            border
            height="300"
            :v-loading="listLoading"
          >
            <el-table-column label="#" width="40">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column prop="employeeType" label="类型">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.employeeType"
                  clearable
                  placeholder="类型"
                  style="width: 120px"
                >
                  <el-option label="正式" :value="3" />
                  <el-option label="试用" :value="2" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="commissionType" label="取值">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.commissionType"
                  clearable
                  placeholder="取值"
                  style="width: 120px"
                  @change="changeCom(scope.row)"
                  :disabled="detailUserInfo.wagesSetType != 3"
                >
                  <el-option label="个人冲抵" value="个人冲抵" />
                  <el-option label="组员冲抵" value="组员冲抵" />
                  <el-option label="个人毛三" value="个人毛三" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="count" label="底薪">
              <template slot-scope="scope">
                <el-input-number
                  style="width: 100px"
                  v-model="scope.row.baseSalary"
                  :precision="2"
                  :controls="false"
                  :min="0"
                  :max="100000"
                  placeholder="底薪"
                  align="center"
                  @blur="
                    handleInput(
                      scope.row.baseSalary,
                      scope.$index,
                      'baseSalary'
                    )
                  "
                >
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="applyCount" label="绩效">
              <template slot-scope="scope">
                <el-input-number
                  style="width: 100px"
                  v-model="scope.row.performance"
                  :controls="false"
                  :max="100000"
                  :min="0"
                  :precision="2"
                  placeholder="绩效"
                  align="center"
                  :disabled="detailUserInfo.wagesSetType == 1"
                  @blur="
                    handleInput(
                      scope.row.performance,
                      scope.$index,
                      'performance'
                    )
                  "
                >
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="commission" label="提成">
              <template slot-scope="scope">
                <el-input-number
                  style="width: 100px"
                  v-model="scope.row.commission"
                  :controls="false"
                  placeholder="提成"
                  width="120"
                  :precision="2"
                  :max="500"
                  :min="0"
                  :disabled="
                    detailUserInfo.wagesSetType == 1 ||
                    detailUserInfo.wagesSetType == 2
                  "
                  @blur="
                    handleInput(
                      scope.row.commission,
                      scope.$index,
                      'commission'
                    )
                  "
                >
                </el-input-number
                >%
              </template>
            </el-table-column>
            <el-table-column prop="minimumMonthlyKPI" label="月最低KPI">
              <template slot-scope="scope">
                <el-input-number
                  style="width: 100px"
                  v-model="scope.row.minimumMonthlyKPI"
                  :controls="false"
                  placeholder="月最低KPI"
                  width="120"
                  :precision="2"
                  :max="100000"
                  :min="0"
                  :disabled="
                    detailUserInfo.wagesSetType == 1 ||
                    detailUserInfo.wagesSetType == 2
                  "
                  @blur="
                    handleInput(
                      scope.row.minimumMonthlyKPI,
                      scope.$index,
                      'minimumMonthlyKPI'
                    )
                  "
                >
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="attendanceDays" label="休息天数">
              <template slot-scope="scope">
                <el-input-number
                  style="width: 100px"
                  v-model="scope.row.attendanceDays"
                  :controls="false"
                  :min="0"
                  :max="10"
                  placeholder="休息天数"
                  width="120"
                >
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="packCount" label="">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="deleteTableClo(scope.row, scope.$index)"
                  >删除</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="copyProp(scope.row, scope.$index)"
                  >复制一行</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <template>
          <span style="padding-top: 10px; float: right">
            <el-button @click="onclose()">取 消</el-button>
            <el-button
              type="primary"
              :loading="onFinishLoading"
              v-throttle="3000"
              @click="onFinish"
              >确 定</el-button
            >
          </span>
        </template>
      </el-card>
    </template>
  </container>
</template>

<script>
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {
  addOrUpdPurchasePostUserWagesAsync,
  getPurchasePostUserWagesDetailAsync,
  getPurchaseWagesCalPositions,
  getUserByPositionAsync,
  getUserByPositionByCurUserAsync
} from "@/api/profit/purchasepostwages";

const checkGroup = [
  {
    label: "底薪",
    value: 1,
  },
  {
    label: "底薪+绩效",
    value: 2,
  },
  {
    label: "底薪+绩效+提成",
    value: 3,
  },
];
export default {
  name: "YunHanAdminOpearpurchasepostuser",
  comments: { container },
  props: {
    filter: {},
  },

  data() {
    return {
      checkGroup,
      teamPostList: [], //组员岗位
      teamUsers: [],
      isShow: false,
      positions: [], //岗位
      userList: [], //人员列表
      postList: [], //岗位列表
      value: null,
      that: this,
      entities: [],
      formData: {
        Id: null,
        effectiveDate: null,
        employeeType: null,
        getValueType: null,
        attendanceDays: null,
        baseSalary: null,
        performance: null,
        commission: null,
        minimumMonthlyKPI: null,
      },
      wagesSetType: 1,
      detailUserInfo: {
        id: null,
        company: null,
        purchasePost: null,
        ddUserId: null,
        userName: null,
        hasTeam: false,
        enabled: false,
        teamCompany: null,
        teamPosition: null,
        hasTeam: false,
        wagesSetType: null,
        detailSet: [],
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      onFinishLoading: false,
      listLoading: false,
    };
  },

  async mounted() {},

  methods: {
   async clearUsername(e){
      // if(e == null || e == "" || e == undefined){
        this.detailUserInfo.userName = null;
      // }
      //根据公司和岗位查询人员
      if(this.detailUserInfo.company && this.detailUserInfo.purchasePost){
        this.positions = [];
        this.positions.push(this.detailUserInfo.purchasePost);
        const { data, success } = await getUserByPositionByCurUserAsync({
          positions: this.positions,
          company: this.detailUserInfo.company,
        });
        if (!success) {
          //给错误提示
          this.$message.error("获取岗位薪资人员失败");
          return;
        } else {
          this.userList = data;
        }
      }
    },
    changeType(e) {
      if (e == 1) {
        //将表格中的commission,performance,minimumMonthlyKPI的值变为0
        this.detailUserInfo.detailSet.forEach((f) => {
          f.commission = 0;
          f.performance = 0;
          f.minimumMonthlyKPI = 0;
        });
      }
      if (e == 2) {
        //将表格中的commission,minimumMonthlyKPI的值变为0
        this.detailUserInfo.detailSet.forEach((f) => {
          f.commission = 0;
          f.minimumMonthlyKPI = 0;
        });
      }
      this.detailUserInfo.wagesSetType = e;
    },
    handleInput(row, index, type) {
      //如果row为null、""、undefined,就将列表中的当前数据的commission,performance,minimumMonthlyKPI的值变为0
      if (row == null || row == "" || row == undefined) {
        this.detailUserInfo.detailSet[index][type] = 0;
      }
    },
    changeCom(row) {
      if (row.commissionType == "") {
        row.commissionType = null;
      }
    },
    clearTeamName(e) {
      //如果组员名字为空,就清空岗位
      if (e == null || e == "" || e == []) {
        this.detailUserInfo.teamPosition = null;
        return;
      }
    },
    async changeSetTeamPost(e) {
      //如果岗位为空,就清空姓名
      if (e == null && e == "") {
        this.detailUserInfo.teamUsers = [];
        return;
      }
      //清空岗位
      this.positions = [];
      //根据e找出对应的岗位
      this.positions.push(this.postList.find((item) => item.label === e).label);
      const { data, success } = await GetUserByPositionAsync({
        positions: this.positions,
      });
      if (!success) {
        //给错误提示
        this.$message.error("获取岗位薪资人员失败");
        return;
      } else {
        this.teamUsers = data;
      }
    },
    //复制一行
    copyProp(row, i) {
      //为什么要深拷贝,因为浅拷贝只是拷贝了引用地址,如果直接push到列表中,那么列表中的每一项都是同一个对象,修改其中一个,其他的也会跟着改变
      //深拷贝一行
      const copyRow = JSON.parse(JSON.stringify(row));
      //把深拷贝的一行添加到列表中
      this.detailUserInfo.detailSet.push(copyRow);
    },
    //姓名改变
    clearName(e) {
      //如果姓名为空,就清空岗位
      if (!e) {
        this.detailUserInfo.purchasePost = null;
        return;
      }
      this.detailUserInfo.userName = e;
      //根据姓名获取dduserID
      this.detailUserInfo.ddUserId = this.userList.find(
        (item) => item.userName === e
      ).ddUserId;
      console.log(this.detailUserInfo.ddUserId, "this.detailUserInfo.ddUserId");
    },
    //岗位改变
    async changeSetPost(e) {
      //如果岗位为空,就清空姓名
      if (e == null || e == "") {
        this.detailUserInfo.userName = null;
        return;
      }
      //清空岗位
      this.positions = [];
      //清空取值
      this.detailUserInfo.userName = null;
      //清空组员岗位
      this.detailUserInfo.teamPosition = null;
      //清空组员姓名
      this.detailUserInfo.teamUsers = [];
      //根据e找出对应的岗位
      this.positions.push(this.postList.find((item) => item.value === e).label);
      this.detailUserInfo.purchasePost = this.postList.find(
        (item) => item.value === e
      ).label;
      if (
        this.detailUserInfo.purchasePost == "核价组长" ||
        this.detailUserInfo.purchasePost == "采购组长"
      ) {
        this.isShow = true;
      } else {
        this.isShow = false;
      }
      const { data, success } = await getUserByPositionByCurUserAsync({
        positions: this.positions,
        company: this.detailUserInfo.company,
      });
      if (!success) {
        //给错误提示
        this.$message.error("获取岗位薪资人员失败");
        return;
      } else {
        this.userList = data;
      }
    },
    async onSearch(val) {
      await this.getlist(val);
      this.init();
    },
    deleteTableClo(row, i) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //找出列表和删除的数据相同的下标
          var index = this.detailUserInfo.detailSet.findIndex(
            (item) => item == row
          );
          //删除列表中对应的数据filter
          this.detailUserInfo.detailSet.splice(index, 1);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    async getlist(val) {
      if (val) {
        this.detailUserInfo = this.filter;
        console.log(this.detailUserInfo, "this.detailUserInfo");
        this.listLoading = true;
        this.entities = [];
        const { data, success } = await getPurchasePostUserWagesDetailAsync({
          id: this.filter.id,
        });
        if (!success) return;
        this.detailUserInfo = data;
        //如果有岗位,就根据岗位查询人员
        if (this.detailUserInfo.purchasePost) {
          if (
            this.detailUserInfo.purchasePost == "核价组长" ||
            this.detailUserInfo.purchasePost == "采购组长"
          ) {
            this.isShow = true;
          } else {
            this.isShow = false;
          }
          this.positions = [];
          this.positions.push(this.detailUserInfo.purchasePost);
          const { data, success } = await getUserByPositionByCurUserAsync({
            positions: this.positions,
            company: this.detailUserInfo.company,
          });
          if (!success) {
            //给错误提示
            this.$message.error("获取岗位薪资人员失败");
            return;
          } else {
            this.userList = data;
            //如果有userName，就在userList中找到对应的ddUserId
            if (this.detailUserInfo.userName) {
              this.detailUserInfo.ddUserId = this.userList.find(
                (item) => item.userName === this.detailUserInfo.userName
              ).ddUserId;
            }
          }
        }
        //如果有teamPosition,就根据岗位查询人员
        if (this.detailUserInfo.teamPosition) {
          this.positions = [];
          this.positions.push(this.detailUserInfo.teamPosition);
          const { data, success } = await getUserByPositionByCurUserAsync({
            positions: this.positions,
            company: this.detailUserInfo.company,
          });
          if (!success) {
            //给错误提示
            this.$message.error("获取岗位薪资人员失败");
            return;
          } else {
            this.teamUsers = data;
            // this.detailUserInfo.teamUsers = data.filter(item => this.detailUserInfo.teamUsers.includes(item.ddUserId)).map(item => item.userName)
          }
        }
      } else {
        this.isShow = false;
        //清空数据
        this.detailUserInfo = {
          wagesSetType:1,
          id: 0,
          enabled: true,
          createdTime: null,
          createdUserId: 0,
          createdUserName: null,
          modifiedUserId: 0,
          modifiedUserName: null,
          modifiedTime: null,
          purchasePersonalWagesCalSetId: 0,
          company: null,
          purchasePost: null,
          userName: null,
          hasTeam: false,
          teamCompany: null,
          teamPosition: null,
          teamUsers: [],
          detailSet: [
            {
              id: 0,
              enabled: true,
              createdTime: null,
              createdUserId: 0,
              createdUserName: null,
              modifiedUserId: 0,
              modifiedUserName: null,
              modifiedTime: null,
              purchasePersonalWagesCalSetId: 0,
              employeeType: null,
              baseSalary: null,
              performance: null,
              commission: null,
              commissionType: null,
              attendanceDays: null,
              minimumMonthlyKPI: null,
            },
          ],
        };
      }
    },
    async init() {
      const { data, success } = await getPurchaseWagesCalPositions();
      if (!success) {
        return;
      } else {
        this.postList = data.map((item, i) => {
          return {
            label: item,
            value: i,
          };
        });
      }
    },
    addData() {
      this.detailUserInfo.detailSet.push({
        id: 0,
        enabled: true,
        createdTime: null,
        createdUserId: 0,
        createdUserName: null,
        modifiedUserId: 0,
        modifiedUserName: null,
        modifiedTime: null,
        purchasePersonalWagesCalSetId: 0,
        employeeType: null,
        baseSalary: null,
        performance: null,
        commission: null,
        commissionType: null,
        attendanceDays: null,
        minimumMonthlyKPI: null,
      });
    },
    async onFinish() {
      //如果没选择分公司
      if (this.detailUserInfo.company == null) {
        this.$message.warning("温馨提示：请选择分公司！");
        return;
      }
      //如果没选择岗位,就提示
      if (this.detailUserInfo.purchasePost == null) {
        this.$message.warning("温馨提示：请选择岗位！");
        return;
      }
      //如果没选择姓名,就提示
      if (this.detailUserInfo.userName == null) {
        this.$message.warning("温馨提示：请选择姓名！");
        return;
      }
      if (this.detailUserInfo.detailSet.length <= 0) {
        this.$message.warning("请添加数据！");
        return;
      }
      if (
        this.detailUserInfo.wagesSetType == null ||
        this.detailUserInfo.wagesSetType == "" ||
        this.detailUserInfo.wagesSetType == undefined ||
        this.detailUserInfo.wagesSetType == 0
      ) {
        this.$message.warning("温馨提示：请选择薪资类型！");
        return;
      }
      if (this.detailUserInfo.wagesSetType == 3) {
        //判断每一项的数据是否为空.如果有为空的就报错

        this.detailUserInfo.detailSet.forEach((item) => {
          if (
            item.employeeType == null ||
            item.employeeType == "" ||
            item.employeeType == undefined
          ) {
            this.$message.warning("温馨提示：请填写类型！");
            throw "";
          }
          if (
            item.commissionType == null ||
            item.commissionType == "" ||
            item.commissionType == undefined
          ) {
            this.$message.warning("温馨提示：请填写取值！");
            throw "";
          }
        });
      } else {
        this.detailUserInfo.detailSet.forEach((item) => {
          if (
            item.employeeType == null ||
            item.employeeType == "" ||
            item.employeeType == undefined
          ) {
            this.$message.warning("温馨提示：请填写类型！");
            throw "";
          }
        });
      }

      // const err = this.compareArrayItems(this.detailUserInfo.detailSet);
      // console.log(err,'err');
      // // this.compareArrayItems(this.detailUserInfo.detailSet);
      // if (err.length > 0) {
      //   setTimeout(() => {
      //     this.$message.warning(err.join(","));
      //   }, 100);
      //   return;
      // }
      this.onFinishLoading = true;
      var res = await addOrUpdPurchasePostUserWagesAsync(this.detailUserInfo);
      this.onFinishLoading = false;
      if (res?.success) {
        this.$message({ type: "success", message: "操作成功!" });
        this.$emit("onClose", true);
      } else {
        //this.$message({ type: 'success', message: res?.msg });
      }
    },
    compareArrayItems(arr) {
      const duplicates = {};
      const result = [];
      for (let i = 0; i < arr.length; i++) {
        if (duplicates[i] !== undefined) {
          continue; // 如果当前项已经比较过，则跳过
        }

        const currentGroup = [i];

        for (let j = i + 1; j < arr.length; j++) {
          if (duplicates[j] !== undefined) {
            continue; // 如果当前项已经比较过，则跳过
          }

          if (JSON.stringify(arr[i]) == JSON.stringify(arr[j])) {
            currentGroup.push(j);
          }
        }

        if (currentGroup.length > 1) {
          result.push(
            `表格中第 ${currentGroup
              .map((index) => index + 1)
              .join(", ")} 项相同`
          );
          currentGroup.forEach((index) => (duplicates[index] = true)); // 标记已经比较过的项
        }
      }
      return result;

    },
    
    async onclose() {
      this.$emit("onClose", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.infoBox {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
  height: 40px;
  justify-content: space-between;

  .desc {
    width: 60px;
    margin-right: 10px;
  }
}

.mar {
  margin-right: 10px;
}

.title {
  font-size: 16px;
  color: #000;
}

.btnBox {
  display: flex;
  justify-content: space-around;
}

.pickerBox {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.top_box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

::v-deep .el-table {
  height: 300px !important;
}
</style>