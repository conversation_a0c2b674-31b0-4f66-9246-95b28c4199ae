<template>
    <div style="height: 100%;">
        <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="showToolbar">
            <template #buttons>
                <slot name="tbHeader" />
            </template>
        </vxe-toolbar>
        <vxe-table ref="xTable" border="default" :show-footer="showsummary" style="width: 100%;"
            class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
            show-footer-overflow keep-source size="mini" :height="height" :loading="loading" :data="tableData"
            :scroll-y="{ gt: 100, enabled: true }" :scroll-x="{ gt: 100, enabled: true }" :footer-method="footerMethod"
            @footer-cell-click="footercellclick" @checkbox-change="selectChangeEvent" @checkbox-all="checkboxall"
            :row-config="{ isCurrent: true, isHover: true }" @cell-dblclick="rowChange" 
          
            :row-class-name="rowStyleFun"> 
            <vxe-column field="" title="" type="checkbox" width="40" fixed="left"></vxe-column>
            <vxe-column field="videoTaskId" title="编号" width='50' fixed='left'></vxe-column>
            <vxe-column field="productShortName" title="产品简称" width='135' fixed='left'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="openComputOutInfo(row)"> {{
                        row.productShortName }} </a>
                </template>
            </vxe-column>
            <vxe-column field="urgencyediticon" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.isTopOldNum == '0'">
                        <span></span>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-dot" style="color: #ff0101;"></i>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="taskUrgencyStr" title="紧急程度" width='72' fixed='left'>
                <template #default="{ row }">
                    <template v-if="row.taskUrgency == 1">
                        <el-button size="mini" effect="plain" type="danger" @click="shootUrgencyCilck(row)">{{
                            row.taskUrgencyStr }}</el-button>
                    </template>
                    <template v-else-if="(row.taskUrgency == 2)">
                        <el-button size="mini" effect="plain" type="primary" @click="shootUrgencyCilck(row)"> 
                            {{   checkPermission('api:media:vediotask:ShootUrgencyTaskTgAsync')?'审核': '待审' }}
                        </el-button>
                    </template>
                    <template v-else-if="(row.taskUrgency == 0)">
                        <el-button size="mini" effect="plain" type="warning" @click="shootUrgencyCilck(row)">{{
                            row.taskUrgencyStr }}</el-button>
                    </template>
                    <template v-else>
                        <el-button size="mini" effect="plain" type="" @click="shootUrgencyCilck(row)">{{
                            row.taskUrgencyStr }}</el-button>
                    </template>
                </template>
            </vxe-column>
            <vxe-column field="warehouseStr" title="拍摄样品" width='118' fixed='left'></vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-bz')" field="beiz" title="" width='38' fixed='left'>
                <template #default="{ row }">
                    <el-popover style="overflow-y: hidden;" v-if="row.remarkList.length > 0" placement="right" trigger="hover"
                    width="500">
                    <el-card class="box-card">
                        <div style="display: flex; flex-direction: column; max-height: 250px; min-height: 60px; overflow-y: auto;">
                        <div v-for="(item, i) in row.remarkList" :key="i" style="border: 1px solid #fff;">
                            <span style="font-weight: 600;">{{ i + 1 }}</span>、{{ item }}
                        </div>
                        </div>

                    </el-card>
                     <i class="vxe-icon-flag-fill"  style="font-size: 14px;color: #F56C6C;" slot="reference"
                        @click="openTaskRmarkInfo(row)"></i>
                    </el-popover>

                    <i v-else class="vxe-icon-flag-fill" style="font-size: 15px;color: #dcdfe6;" slot="reference"
                    @click="openTaskRmarkInfo(row)"></i>


                    <!-- <template v-if="row.markCssName == '0'">
                        <i class="vxe-icon-flag-fill" style="color: #F56C6C;" @click="openTaskRmarkInfo(row)"></i>
                    </template>
                    <template v-else>
                        <i class="vxe-icon-flag-fill" style="color: #dcdfe6;" @click="openTaskRmarkInfo(row)"></i>
                    </template> -->
                </template>
            </vxe-column>
            <vxe-column field="cankao" title="" width='28' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-file-txt" @click="videotaskuploadfileDetal(row)"></i>
                </template>
            </vxe-column>
            <vxe-column field="caozuo" v-if="checkPermission('api:media:packdesgin:AddOrUpdateShootingVideoTaskAsync')" title=""
                width='38' fixed='left'>
                <template #default="{ row }">
                    <i class="vxe-icon-ellipsis-h" @click="editTask(row)"></i>
                </template>
            </vxe-column>
            <vxe-column field="fengex1" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant1" title="拍摄一" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-psts')" field="claimDay1" title="天数" width='50'> </vxe-column>
            <vxe-column field="claimTime1Str" title="拍摄日期" width='70'>
                <!-- <template #default="{ row }">{{ formatIsCommission(row.claimTime1) }} </template> -->
            </vxe-column>
            <vxe-column field="fengex2" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant2" title="拍摄二" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-psts')" field="claimDay2" title="天数" width='50'> </vxe-column>
            <vxe-column field="claimTime2Str" title="拍摄日期" width='70'>
                <!-- <template #default="{ row }">{{ formatIsCommission(row.claimTime2) }} </template> -->
            </vxe-column>
            <vxe-column field="fengex3" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant3" title="拍摄三" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-psts')"  field="claimDay3" title="天数" width='50'> </vxe-column>
            <vxe-column field="claimTime3Str" title="拍摄日期" width='70'>
                <!-- <template #default="{ row }">{{ formatIsCommission(row.claimTime3) }} </template> -->
            </vxe-column>
            <vxe-column field="fengex4" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant4" title="拍摄四" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-psts')"  field="claimDay4" title="天数" width='50'> </vxe-column>
            <vxe-column field="claimTime4Str" title="拍摄日期" width='70'>
                <!-- <template #default="{ row }">{{ formatIsCommission(row.claimTime4) }} </template> -->
            </vxe-column>
            <vxe-column field="fengex5" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimant5" title="拍摄五" width='65'> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-psts')"  field="claimDay5" title="天数" width='50'> </vxe-column>
            <vxe-column field="claimTime5Str" title="拍摄日期" width='70'>
                <!-- <template #default="{ row }">{{ formatIsCommission(row.claimTime5) }} </template>  -->
            </vxe-column>
            <vxe-column field="fengex6" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="cuteLqName" title="负责人" width='70'>
                <template #default="{ row }">
                    <a type="text" :style="row.cuteLqName == '' || row.cuteLqName == null ? 'color:#409EFF' : 'color:#606266'"
                        @click="pickTask(row, 0)"> {{ row.cuteLqName == "" || row.cuteLqName == null ? "认领" : row.cuteLqName }} </a>
                </template>
            </vxe-column>
            <vxe-column field="dockingPeopleStr" title="分配拍摄一" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr2" title="分配拍摄二" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr3" title="分配拍摄三" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr4" title="分配拍摄四" width='70'> </vxe-column>
            <vxe-column field="dockingPeopleStr5" title="分配拍摄五" width='70'> </vxe-column>
            <vxe-column field="fengex2" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="operationsGroupStr" title="运营小组" width='70'> </vxe-column>
            <vxe-column field="productId" title="产品ID" width='120'>
                <template #default="{ row }">
                    <a :href="formatProCode(row)" target="_black" style="color:blue">{{ row.productId }}</a>
                </template>
            </vxe-column>
            <vxe-column field="platformStr" title="平台" width='80'> </vxe-column>
            <vxe-column field="shopName" title="店铺" width='180'> </vxe-column>
            <vxe-column field="fengex3" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="claimOverDate" title="拍摄完成" width='75'> <template #default="{ row }">{{ formatIsCommission(row.claimOverDate) }} </template> </vxe-column>
            <vxe-column field="cutClaimOverDate" title="剪辑完成" width='75'>  <template #default="{ row }">{{ formatIsCommission(row.cutClaimOverDate) }} </template></vxe-column>
            <vxe-column field="arrivalDate" title="到货日期" width='75'> <template #default="{ row }">{{
                formatIsCommission(row.arrivalDate) }} </template></vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-orderts')"  field="arrivalTimeDays" title="到货天数" width='35'> </vxe-column>
            <vxe-column field="deliverTime" title="发货日期" width='75'> <template #default="{ row }">{{
                formatIsCommission(row.deliverTime) }} </template> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-orderts')" field="deliverTimeDays" title="发货天数" width='35'> </vxe-column>
            <vxe-column field="applyTime" title="申请日期" width='75'> <template #default="{ row }">{{
                formatIsCommission(row.applyTime) }} </template> </vxe-column>
            <vxe-column v-if="checkPermission('vedioTask-list-orderts')" field="applyTimeDays" title="申请天数" width='35'> </vxe-column>
            <vxe-column field="updateDate" title="修改日期" width='75'> <template #default="{ row }">{{
                formatIsCommission(row.updateDate) }} </template></vxe-column>
            <vxe-column field="createdTime" title="创建日期" width='78'>
                <template #default="{ row }">{{ formatIsCommission(row.createdTime) }} </template>
            </vxe-column>
            <vxe-column field="fengex7" title="|" width ='28'> <span style="color: #999;"> | </span> </vxe-column>
            <vxe-column field="sampleRrderNo" title="内部单号" width='100'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.orderNoInner }} </a>
                </template>
            </vxe-column>
            <vxe-column field="sampleExpressNo" title="快递单号" width='135'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowExproessHttp(row)"> {{
                        row.expressNo }} </a>
                </template>
            </vxe-column>
            <vxe-column field="orderTrack" title="拿样跟踪" width='80'>
                <template #default="{ row }">
                    <a type="text" show-overflow="ellipsis" style="color:#409EFF" @click="onShowOrderDtl(row)"> {{
                        row.shootOrderTrack }} </a>
                </template>
            </vxe-column>
        </vxe-table>
    </div>
</template>
<script>
import { formatTime } from "@/utils";
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx' 
// import ExcelJS from 'exceljs'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })
import checkPermission from '@/utils/permission'
export default {
    props: {
        editconfig: { type: Object, default: () => { return {} } },
        treeProp: { type: Object, default: () => { return {} } },
        hasSeq: { type: Boolean, default: () => { return true } },
        hascheck: { type: Boolean, default: () => { return false } },
        showToolbar: { type: Boolean, default: () => { return true } },
        // 表格数据
        tableData: { type: Array, default: () => [] },
        // 表格型号：mini,medium,small
        size: { type: String, default: 'mini' },
        type: { type: String, default: 'primary' },
        isBorder: { type: Boolean, default: true },
        // 表格列配置
        tableCols: { type: Array, default: () => [] },
        isRemoteSort: { type: Boolean, default: () => { return true } },
        id: { type: String, default: () => { return new Date().valueOf().toString() } },
        that: { type: Object, default: () => { return null } },
        loading: { type: Boolean, default: () => { return false; } },
        border: { type: Boolean | Object, default: () => { return 'default' } },
        tableHandles: { type: Array, default: () => [] },
        showsummary: { type: Boolean, default: false },
        align: { type: String, default: '' }, //对齐方式
        summaryarry: { type: Object, default: () => { } },
        tablekey: { type: String, default: '' },//表格key
        height: { type: String, default: '95%' },//固定表头作用

        showCacle: { type: Boolean, default: () => { return false } },

    },
    created() {
        // VXETable.use(VXETablePluginExportXLSX);
        this.$nextTick(() => {
            // 手动将表格和工具栏进行关联
            this.$refs.xTable.connect(this.$refs.xToolbar)
        })
    },
    data() {
        return {
            lastSortArgs: {
                field: "",
                order: "",
            },
            arrlist: [],
            summarycolumns: [],
            tablecolumns: [],
            aaaa: ''
        }
    },
    async mounted() {
        this.$nextTick(() => {
            this.tablecolumns = this.$refs.xTable.getColumns()

        })
    },
    methods: {
        formatProCode(row) {
            var proBaseUrl = '';
            switch (row.platform) {
                case 1://淘系
                case '淘系'://淘系
                    proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productId;
                    break;
                case 2://拼多多
                case '拼多多'://拼多多
                    proBaseUrl = "https://mobile.yangkeduo.com/goods2.html?goods_id=" + row.productId;
                    break;
                case 8://淘系
                case '淘工厂'://淘系
                    proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productId;
                    break;
                case 9://淘系
                case '淘宝'://淘系
                    proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productId;
                    break;
                case 7://京东
                case '京东'://京东
                    proBaseUrl = "https://item.jd.com/" + + row.productId + ".html";
                    break;

            }
            return proBaseUrl == '' ? "#" : proBaseUrl;
        },
        rowStyleFun({ row, rowIndex, $rowIndex }) {
            
            if (row && row.isend == 0) {
                return '';
            } else { 
                return 'droprow'; 
            }  
        },
        pickTask(row, index) {
            this.$emit('pickTask', row, index)
        },
        async checkboxall() {
            const records = this.$refs.xTable.getCheckboxRecords()
            this.$emit('checkboxall', records);
        },
        onShowOrderDtl(row) {
            this.$emit('onShowOrderDtl', row)
        },
        onShowExproessHttp(row) {
            this.$emit('onShowExproessHttp', row)
        },
        //行切换事件
        rowChange({ newValue, oldValue, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
            this.$emit('rowChange', row);
        },
        openComputOutInfo(row) {
            this.$emit('openComputOutInfo', row)
        },
        shootUrgencyCilck(row) {
            this.$emit('shootUrgencyCilck', row)
        },
        openTaskRmarkInfo(row) {

            this.$emit('openTaskRmarkInfo', row)
        },
        videotaskuploadfileDetal(row) {

            this.$emit('videotaskuploadfileDetal', row)
        },
        editTask(row) {
            this.$emit('editTask', row)
        },
        selectChangeEvent({ checked }) {
            const records = this.$refs.xTable.getCheckboxRecords()
            this.$emit('selectchangeevent', records);
        },
        //导出
        exportData(filename) {
            // this.$refs.xTable.exportData({ filename: filename, type: 'csv' })
            this.$refs.xTable.exportData({filename:filename,    sheetName: 'Sheet1',type: 'xlsx' })
        },
        formatIsCommission(value) {
            return value == null ? null : formatTime(value, 'YY-MM-DD')
        },
        //批量控制列的显影
        async ShowHidenColums(arrlist) {
            this.$refs.xTable.getTableColumn().collectColumn.forEach(column => {
                if (arrlist.includes(column.property)) {
                    column.visible = false
                } else {
                    column.visible = true
                }
            })
            if (this.$refs.xTable) {
                this.$refs.xTable.refreshColumn()
            }
        },
        //清空全选
        clearSelection() {
            this.$refs.xTable.clearCheckboxRow()
        },
        footerMethod({ columns, data }) {
            const sums = [];
            if (!this.summaryarry)
                return sums
            var arr = Object.keys(this.summaryarry);
            if (arr.length == 0)
                return sums
            //const { columns, data } = param;
            var hashj = false;
            columns.forEach((column, index) => {
                if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                    var sum = this.summaryarry[column.property + '_sum'];
                    if (sum == null) return;
                    else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                    else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                    else sums[index] = sum.toFixed(2)
                }
                else sums[index] = ''
            });
            return [sums]
        },
        footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }) {

            let self = this;
            var col = findcol(self.tableCols, column.property);
            if (col && col.summaryEvent)
                self.$emit('summaryClick', column.property)

            function findcol(cols, property) {
                let column;
                for (var i = 0; i < cols.length; i++) {
                    var c = cols[i];
                    if (column) break
                    else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                        column = c;
                        break
                    }
                    else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                }
                return column
            }
        }
    }


}
</script> 


<style lang="scss" scoped> 
.vxe-table--render-default.border--default .vxe-table--header-wrapper {
     background-color: #fafbff;
 }

 /*斑马线颜色*/
 .vxe-table--render-default .vxe-body--row.row--stripe {
     background-color: #fafbff;
 }

 .vxe-table--render-default .vxe-body--row.row--current {
     background-color: #e5ecf5;
 }

 /*滚动条整体部分*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar {
     width: 18px;
     height: 26px;
 }

 /*滚动条的轨道*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
     background-color: #f1f1f1;
 }

 /*滚动条里面的小方块，能向上向下移动*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
     background-color: #c1c1c1;
     border-radius: 3px;
     box-sizing: border-box;
     border: 2px solid #F1F1F1;
     box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
 }
// 滚动条鼠标悬停颜色
 .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
     background-color: #A8A8A8;
 }
// 滚动条拖动颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
     background-color: #787878;
 }

 /*边角，即两个滚动条的交汇处*/
 .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
     background-color: #dcdcdc;
 }

 // 图片大小
 .mytable-scrollbar20221212 .images20221212 {
     max-width: 150px;
     max-height: 150px;
     width: 40px !important;
     height: 40px !important;
 }
 // 表格内边距
 ::v-deep .vxe-table--render-default .vxe-cell {
     padding: 0 0 0 8px !important;
 }
 // 图片张数标记
 .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed {
     top: 10px;
 }

 /*  工具箱位置  */
 .vxetoolbar20221212 {
     position: absolute;
     top: 53px;
     right: 2px;
     padding-top: 0;
     padding-bottom: 0;
     z-index: 999;
     background-color: rgb(255 255 255 / 0%);
 }

// 表头高度
::v-deep .vxe-table--render-default.size--mini .vxe-header--column:not(.col--ellipsis) {
    height:50px !important;
 }

// 表头文字行间距
::v-deep  .vxe-header--column {
    line-height: 18px !important;
}
 // 表格内边距
 ::v-deep .vxe-table--render-default .vxe-cell {
     padding: 0 0 0 8px !important;
 }
 ::v-deep  .vxe-header--column {
    line-height: 18px !important;
}

 .vxetableheadercell-left-20221216 {
     text-align: left;
 }

 .vxetableheadercell-center-20221216 {
     text-align: center;
 }

 .vxetableheadercell-right-20221216 {
     text-align: right;
 }

 .vxe-icon-ellipsis-h:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
 }

 .vxe-icon-ellipsis-h {
     color: #999;
     font-size: 15px;
 }

 .vxe-icon-file-txt:hover {
     color: #409EFF;
     margin-left: 2px;
     background-color: #F1F1F1;
     font-weight: 600;
 }

 .vxe-icon-file-txt {
     color: #999;
     font-size: 15px;
 }

 .vxetablecss {
     margin: 0;
 }

 ::v-deep span.vxe-cell--item {
     cursor: pointer !important;
 }

 ::v-deep .el-tag {

     cursor: pointer !important;
 }

//  ::v-deep .droprow tbody{

//  } 

 ::v-deep .droprow td {
    color:  rgb(250, 9, 9);
    position: relative;
}

::v-deep  .droprow  ::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0.1px;
    background-color: rgb(250, 9, 9);
    transform: translateY(-50%);
}
</style> 