<template>
    <MyContainer>
        <vxetablebase ref="table" :id="'allIdPickingLog20240807'" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" height="400px">
        </vxetablebase>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { getGoodsCostApplyPackLogList } from '@/api/inventory/goodscostpricechg'
const tableCols = [
    { width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
    { width: 'auto', align: 'center', prop: 'createdTime', label: '操作时间', },
    { width: 'auto', align: 'center', prop: 'operationContent', label: '操作内容', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        applyId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList(this.applyId)
    },
    methods: {
        async getList(applyId) {
            try {
                const { data, success } = await getGoodsCostApplyPackLogList({ applyId })
                if (success) {
                    this.tableData = data
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
