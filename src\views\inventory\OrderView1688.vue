<template>
    <div class="content" v-loading="loading">
        <div class="content_info publicSpace">
            <div class="word"><span class="title">ERP单号:</span>(货号{{ viewData.indexNo }})</div>
            <div class="word"><span class="title">仓库:</span>{{ viewData.wareHouseName }}</div>
            <div class="word"><span class="title">账号:</span>{{ viewData.payAccount }}</div>
        </div>
        <div class="word publicSpace"><span class="title">收货地址:</span>{{ viewData.adress }} (货号{{ viewData.indexNo }})</div>
        <div class="content_info">
            <div class="word publicSpace"><span class="title">收件人:</span>{{ viewData.fullName }}</div>
            <div class="word publicSpace"><span class="title">电话:</span>{{ viewData.phone }}</div>
            <div class="word publicSpace"><span class="title">供应商:</span>{{ viewData.supplierName }}</div>
        </div>
        <div class="publicSpace">
            <el-radio-group v-model="viewData.orderType" @change="changeType">
                <el-radio-button :label="1">按数量</el-radio-button>
                <el-radio-button :label="2">按金额</el-radio-button>
            </el-radio-group>
        </div>
        <div class="content_info publicSpace">
            <div style="width: 49%;">
                <div class="publicSpace"><span class="title">ERP采购单信息:</span></div>
                <vxetablebase ref="table" :isWebSort="true" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
                    id="20240930165455" :tableData='erpData.dtlGoods' :tableCols='tableCols' :isSelection="false"
                    :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" :isNeedExpend="false">
                    <template #goodsCode="{ row }">
                        <div>
                            <el-button @click="copyGoodsCode(row.goodsCode)" type="text">复制</el-button>
                            {{ row.goodsCode }}
                        </div>
                    </template>
                </vxetablebase>
                <div style="display: flex;justify-content: end">数量合计:{{ erpDataSummary.count_sum }}</div>
            </div>
            <div style="width: 49%;">
                <div class="publicSpace" style="display: flex;">
                    <div style="margin-right: 20px;display: flex;">
                        <span class="title">1688订单信息:</span>
                        <div class="title_1688">{{
                            viewData.aliGoodInfos ?
                                viewData.aliGoodInfos[0].aliProductName :
                                '' }}</div>
                    </div>
                </div>
                <vxetablebase ref="table1688" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
                    id="20240930165506" :tableData='viewData.aliGoodInfos' :tableCols='tableCols1' :isSelection="false"
                    :isSelectColumn="false" style="width: 100%; height: 300px; margin: 0" :isNeedExpend="false">
                    <template #goodCode="{ row }">
                        <el-input v-model.trim="row.goodCode" clearable maxlength="30" style="width:150px"
                            placeholder="商品编码" @change="changeMutil($event, row)" />
                    </template>
                    <template #mutil="{ row, index }">
                        <el-input-number v-model.trim="row.mutil" style="width:60px" placeholder="倍数" :precision="3"
                            :max="9999" :min="0.001" :controls="false" @change="changeMutil($event, row)" />
                    </template>
                    <template #qty="{ row, index }">
                        <div v-if="viewData.orderType == 1">{{ row.qty }}</div>
                        <el-input-number v-else v-model.trim="row.countQty" style="width:60px" placeholder="数量"
                            :precision="0" :max="999999" :min="0" :controls="false"
                            @change="computedSum($event, row)" />
                    </template>
                </vxetablebase>
                <div style="display: flex;justify-content: end">数量合计:{{ viewDataSummary.qty_sum }}</div>
            </div>
        </div>
        <div>
            <div class="publicSpace"><span class="title">1688采购单留言:</span> </div>
            <div style="display: flex;align-items: center;flex-wrap: wrap;" class="publicSpace" v-loading="tagLoading">
                <el-tag @click="copyGoodsInfo" size="medium" class="tagClass">商品信息</el-tag>
                <el-tag v-for="item in oftenTag" @click="copyTag(item)" :key="item" size="medium" class="tagClass">{{
                    item
                }}</el-tag>
                <el-tag :key="tag.keyWord" v-for="tag in this.viewData.userCommonSets" closable
                    :disable-transitions="false" @close="handleClose(tag)" @click="copyTag(tag.keyWord)" size="medium"
                    class="tagClass">
                    {{ tag.keyWord }}
                </el-tag>
                <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"
                    maxlength="50" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm"
                    style="width: 120px; margin: 0 10px 10px 0;">
                </el-input>
                <div v-else style="width: 120px; margin: 0 10px 10px 0;">
                    <el-button class="button-new-tag" @click="showInput"
                        v-if="this.viewData.userCommonSets && this.viewData.userCommonSets.length < 9">+自定义留言</el-button>
                </div>
            </div>
            <el-input class="publicSpace" type="textarea" :rows="5" placeholder="请输入内容" v-model="viewData.message"
                style="width: 100%;" maxlength="500" show-word-limit />
        </div>
        <div class="publicSpace" style="display: flex;justify-content: center;">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="submit">下单</el-button>
        </div>
    </div>
</template>

<script>
import { PreviewCreate1688PurchaseOrder, Create1688PurchaseOrder, SaveInventoryUserCommonSet, DeleteInventoryUserCommonSet } from '@/api/inventory/purorder'
import { getAllWarehouse, } from '@/api/inventory/warehouse'
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import decimal from "@/utils/decimalToFixed"
const tableCols = [
    { width: 'auto', align: 'center', prop: 'picture', label: '图片', type: 'images', width: '70' },
    { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', width: '180', align: 'left', sortable: "custom", },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', sortable: "custom", },
    { width: 'auto', align: 'center', prop: 'count', label: '数量', width: '50', sortable: "custom", },
]
const tableCols1 = [
    { width: 'auto', align: 'center', prop: 'productImgUrl1', label: '图片', type: 'images', width: '70' },
    { width: 'auto', align: 'center', prop: 'aliProductAttribute', label: '规格属性', },
    { width: 'auto', align: 'center', prop: 'goodCode', label: 'ERP商品编码', width: '170' },
    { width: 'auto', align: 'center', prop: 'mutil', label: '倍数', width: '70' },
    { width: 'auto', align: 'center', prop: 'qty', label: '数量', width: '70' },
]
export default {
    props: {
        params: {
            type: Object,
            default: () => { }
        },
        propsInfo: {
            type: Object,
            default: () => { }
        },
        data: {
            type: Object,
            default: () => { }
        }
    },
    components: {
        vxetablebase
    },
    data() {
        return {
            viewData: {},
            tableCols1,
            tableCols,
            tableData: [],
            that: this,
            loading: false,
            erpData: null,
            warehouselist: [],
            oftenTag: ['不同规格/颜色麻烦分开打包，或者拿东西隔开，不要混在一起发货，外箱做好标记规格/颜色以及数量，感谢！！',],
            inputVisible: false,
            inputValue: '',
            tagLoading: false,
            erpDataSummary: {
                count_sum: 0
            },
            viewDataSummary: {
                qty_sum: 0
            }
            // this.viewData.userCommonSets: [],
        }
    },
    async mounted() {
        const res = JSON.parse(JSON.stringify(this.propsInfo))
        res.dtlGoods = res.dtlGoods.filter(item => item.goodsCode != 'CGBCJ-001' && item.goodsCode != 'CGYF')
        this.erpData = JSON.parse(JSON.stringify(res))
        this.viewData = JSON.parse(JSON.stringify(this.data))
        this.erpDataSummary.count_sum = this.erpData.dtlGoods.reduce((total, item) => total + Number(item.count), 0)
        this.viewDataSummary.qty_sum = this.viewData.aliGoodInfos.reduce((total, item) => total + Number(item.qty), 0)
        this.init()
    },
    methods: {
        computedSum() {
            if (this.viewData.orderType == 2) {
                const res = this.viewData.aliGoodInfos.reduce((total, item) => total + Number(item.countQty), 0)
                this.$set(this.viewDataSummary, 'qty_sum', res)
            } else {
                const res = this.viewData.aliGoodInfos.reduce((total, item) => total + Number(item.qty), 0)
                this.$set(this.viewDataSummary, 'qty_sum', res)
            }
        },
        changeType(type) {
            this.$set(this.viewData, 'orderType', type)
            if (type == 2) {
                this.$refs.table1688.$refs.xTable.hideColumn(this.$refs.table1688.$refs.xTable.getColumnByField('goodCode'))
                this.$refs.table1688.$refs.xTable.hideColumn(this.$refs.table1688.$refs.xTable.getColumnByField('mutil'))
            } else {
                this.$refs.table1688.$refs.xTable.showColumn(this.$refs.table1688.$refs.xTable.getColumnByField('goodCode'))
                this.$refs.table1688.$refs.xTable.showColumn(this.$refs.table1688.$refs.xTable.getColumnByField('mutil'))
            }
            this.computedSum()
        },
        changeMutil(mutil, row) {
            if (!row.goodCode) {
                this.$set(row, 'qty', 0)
                return this.$message.error('商品编码不能为空')
            }
            //找出对应的编码
            const flag = this.erpData.dtlGoods.filter(item => item.goodsCode == row.goodCode)
            if (flag.length > 0) {
                this.$set(row, 'qty', decimal(Number(row.mutil ? row.mutil : 0), Number(flag[0].count), '*'))
            } else {
                this.$message.error('ERP中没有该商品编码')
                this.$set(row, 'qty', 0)
            }
            this.viewDataSummary.qty_sum = this.viewData.aliGoodInfos.reduce((total, item) => total + Number(item.qty), 0)
        },
        handleClose(tag) {
            this.$confirm('确定删除这条留言吗, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.tagLoading = true
                const { data, success } = await DeleteInventoryUserCommonSet({ keyWord: tag.keyWord, id: tag.id, type: '1688下单备注' })
                if (success) {
                    this.viewData.userCommonSets = data
                }
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                this.tagLoading = false
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        showInput() {
            this.inputVisible = true;
            this.$nextTick(_ => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },
        async handleInputConfirm() {
            if (!this.inputValue) {
                this.inputVisible = false;
                this.inputValue = '';
                return
            }
            this.tagLoading = true
            const { data, success } = await SaveInventoryUserCommonSet({ keyWord: this.inputValue, type: '1688下单备注' })
            if (success) {
                this.viewData.userCommonSets = data
                this.$message({
                    type: 'success',
                    message: '添加成功!'
                });
                this.inputVisible = false;
                this.inputValue = '';
                this.tagLoading = false
            } else {
                this.$message.error('添加失败')
                this.inputVisible = false;
                this.inputValue = '';
                this.tagLoading = false
            }
        },
        copyGoodsInfo() {
            let str = ''
            this.erpData.dtlGoods.forEach((item, i) => {
                if (i + 1 !== this.erpData.dtlGoods.length) {
                    str += (this.viewData.message == 0 || this.viewData.message == undefined) ? `${item.goodsName} x ${item.count}\n` : `\n${item.goodsName} x ${item.count}`
                } else {
                    str += (this.viewData.message == 0 || this.viewData.message == undefined) ? `${item.goodsName} x ${item.count}` : `\n${item.goodsName} x ${item.count}`
                }
            })
            let res = (this.viewData.message ? this.viewData.message : '') + str
            if (res.length > 500) {
                this.$message.error('留言内容超过500字')
                return
            }
            this.$set(this.viewData, 'message', res)
        },
        copyTag(item) {
            let res = (this.viewData.message == 0 || this.viewData.message == undefined) ? item : `${this.viewData.message}\n${item}`
            if (res.length > 500) {
                this.$message.error('留言内容超过500字')
                return
            }
            this.$set(this.viewData, 'message', res)
        },
        copyGoodsCode(goodsCode) {
            if (!goodsCode) return this.$message.error('商品编码为空')
            let textarea = document.createElement("textarea")
            textarea.value = goodsCode
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                    message: '复制成功',
                    type: 'success'
                })
            }
            textarea.remove()
        },
        async init() {
            var res3 = await getAllWarehouse();
            this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
        },
        async submit() {
            if (!this.viewData.aliGoodInfos) return this.$message.error('1688暂无数据')
            if (this.viewData.aliGoodInfos.length == 0) return this.$message.error('1688暂无数据')
            //找出data.aliGoodInfos和erpData.dtlGoods数据中商品编码相同的数据
            let params;
            if (this.viewData.orderType == 1) {
                const res = this.erpData.dtlGoods.filter(item => this.viewData.aliGoodInfos.some(good => good.goodCode === item.goodsCode))
                res.forEach(item => {
                    this.viewData.aliGoodInfos.forEach(good => {
                        if (good.goodCode === item.goodsCode) {
                            good.originQty = item.count
                        }
                    })
                })
                params = JSON.parse(JSON.stringify(this.viewData))
                params.aliGoodInfos = params.aliGoodInfos.filter(item => item.goodCode)
                params.aliGoodInfos.forEach(item => {
                    if (item.qty == 0) {
                        this.$message.error(`编码${item.goodCode}的商品数量为0,请调整后重试`)
                        throw new Error(`编码${item.goodCode}的商品数量为0,请调整后重试`)
                    }
                })
            } else {
                params = JSON.parse(JSON.stringify(this.viewData))
                const flag = params.aliGoodInfos.every(item => item.countQty == 0)
                if (flag) {
                    this.$message.error('没有可用的数据,数量都为0')
                    return
                }
                params.aliGoodInfos = params.aliGoodInfos.filter(item => (item.countQty != 0 && item.countQty != null && item.countQty != undefined))
                params.aliGoodInfos.forEach(item => {
                    item.mutil = null
                    item.goodCode = ''
                    item.qty = item.countQty
                })
            }
            this.loading = true
            const { success, data } = await Create1688PurchaseOrder(params)
            if (success) {
                this.$message.success('下单成功')
                this.loading = false
                this.$emit('closeOrder', data)
            } else {
                this.loading = false
            }
        },
        close() {
            this.$emit('closeOrder')
        },
    }
}
</script>

<style scoped lang="scss">
.content {
    width: 100%;
    padding: 10px;

    .content_info {
        display: flex;
        justify-content: space-between;
    }

    .title {
        font-weight: 700;
        margin-right: 10px;
    }
}

.word {
    width: 100%;
    word-break: break-all;
}

.title_1688 {
    width: 500px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.publicSpace {
    margin-bottom: 15px;
}

.tagClass {
    cursor: pointer;
    margin: 0 10px 10px 0;
}
</style>