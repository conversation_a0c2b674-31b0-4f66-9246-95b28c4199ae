<template>
    <my-container style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">

            <el-tab-pane label="达人报表" name="reporttiktokwise1" style="height: 100%">
                <reporttiktokwise1 ref="reporttiktokwise1" style="height: 100%">
                </reporttiktokwise1>
            </el-tab-pane>

            <el-tab-pane label="提成汇总" name="reporttiktokwise2" style="height: 100%" lazy>
                <reporttiktokwise2 ref="reporttiktokwise2" style="height: 100%">
                </reporttiktokwise2>
            </el-tab-pane>

        </el-tabs>
    </my-container>
</template>

<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import reporttiktokwise1 from "./reporttiktokwise1.vue";
import reporttiktokwise2 from "./reporttiktokwise2.vue";

export default {
    name: "Index",
    components: {
        cesTable,
        MyContainer,
        MyConfirmButton,
        reporttiktokwise1,
        reporttiktokwise2,
    },
    data() {
        return {
            that: this,
            activeName: "reporttiktokwise1",
        };
    },
    async mounted() {
    },
    methods: {
    },
};
</script>

<style lang="scss" scoped></style>