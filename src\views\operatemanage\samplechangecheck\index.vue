<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-select v-model="filter.approvalStatus" placeholder="状态" clearable style="width: 120px;">
                    <el-option label="未发起" value="未发起" />
                    <el-option label="审批中" value="审批中" />
                    <el-option label="已审批" value="已审批" />
                    <el-option label="已拒绝" value="已拒绝" />
                    <el-option label="已撤销" value="已撤销" />
                </el-select>
                <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions"
                    style="width: 240px;" :value-format="'yyyy-MM-dd'" />
                <el-input v-model.trim="filter.styleCode" placeholder="系列编码" maxlength="50" clearable
                    style="width: 120px;" />
                <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" maxlength="40" clearable
                    style="width: 120px;" />
                <el-input v-model.trim="filter.groupName" placeholder="运营组" maxlength="40" clearable
                    style="width: 120px;" />

                <el-input-number v-model="filter.newLengthNum" placeholder="长" clearable :precision="2" :min="0"
                    :controls="false" :max="9999" style="width: 100px;" />
                <el-input-number v-model="filter.newWidthNum" placeholder="宽" clearable :precision="2" :min="0"
                    :controls="false" :max="9999" controls-position="right" style="width: 100px;" />
                <el-input-number v-model="filter.newHeightNum" placeholder="高" clearable :precision="2" :min="0"
                    :controls="false" :max="9999" controls-position="right" style="width: 100px;" />
                <el-input-number v-model="filter.newThicknessNum" placeholder="厚度" clearable :precision="2"
                    :controls="false" :min="0" :max="9999" controls-position="right" style="width: 100px;" />
                <el-input-number v-model="filter.newWeightNum" placeholder="克重" clearable :precision="2"
                    :controls="false" :min="0" :max="999999" controls-position="right" style="width: 100px;" />

                <el-input v-model.trim="filter.newColor" placeholder="颜色" clearable style="width: 120px;" />
                <el-input v-model.trim="filter.newMaterial" placeholder="材质" clearable style="width: 120px;" />
                <el-input v-model.trim="filter.createdUserName" placeholder="添加人" maxlength="10" clearable
                    style="width: 120px;" />
                <el-input v-model.trim="filter.newPackMethod" placeholder="包装方式" maxlength="10" clearable
                    style="width: 120px;" />
                <el-select v-model="filter.newTransportMethod" placeholder="货运方式" clearable style="width: 160px;">
                    <el-option v-for="item in freightMethodList" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filter.similarity" placeholder="对比结果" clearable style="width: 120px;">
                    <el-option v-for="item in similarityList" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="拒绝开始日期" end-placeholder="拒绝结束日期" :picker-options="pickerOptions"
                    style="width: 240px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-input v-model.trim="filter.approvalRefuseUserName" placeholder="拒绝人" maxlength="10" clearable
                    style="width: 120px;" />
                <el-select v-model="filter.isApprovePrice" placeholder="是否核价" clearable style="width: 100px;">
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                </el-select>
                <el-input v-model.trim="filter.supplierName" placeholder="厂家名称" clearable style="width: 120px;" />
                <el-button type="primary" @click="onSearch()">查询</el-button>
                <el-button type="primary" @click="onClearfilter()">清空</el-button>
                <el-button type="primary" @click="onAdd()" style="margin-left: 20px;">新增</el-button>
            </div>
        </template>
        <vxetablebase :id="'samplechangecheck_index202408041730'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="200" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text" v-if="(row.approvalStatus == '未发起' || row.approvalStatus == '已拒绝')"
                                @click="onEdit(row, '编辑')" v-throttle="1000">编辑</el-button>
                            <!-- <el-button type="primary" v-if="(row.approvalStatus == '未发起')">发起审批</el-button> -->
                            <el-button type="text" v-if="(row.approvalStatus != '未发起' && row.approvalStatus != '已拒绝')"
                                @click="onEdit(row, '详情')" v-throttle="1000">详情</el-button>
                            <el-button type="text" v-if="(row.approvalStatus == '未发起' || row.approvalStatus == '已拒绝')"
                                @click="onSaveApproval1(row)" v-throttle="1000">发起审批</el-button>
                            <el-button type="text" v-if="(row.approvalStatus == '审批中')" @click="onBackApproval(row)"
                                v-throttle="1000">撤销申请</el-button>
                            <el-button type="text" v-if="(row.approvalStatus != '未发起')" @click="getAuditList(row.id)"
                                v-throttle="1000">审批记录</el-button>
                            <el-button type="text" @click="openLogDetails(row.id)" v-throttle="1000">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-drawer :title="dialogContent.title" :visible.sync="dialogContent.visible" width="45%" direction="rtl"
            size="50%" :close-on-click-modal="false" :wrapperClosable="false">
            <el-form ref="dialogContentAddFormData" :model="dialogContent.addFormData" label-width="120px"
                label-position="left" :rules="dialogContent.addFormRules" v-loading="dialogContent.addFromLoading"
                :disabled="dialogContent.addFromDisabled" style="padding: 20px;" v-if="dialogContent.visible">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="12">
                        <el-form-item prop="goodsCode" label="商品编码">
                            <el-select v-model="dialogContent.addFormData.goodsCode" placeholder="商品编码" clearable
                                @change="onGoodsCodeChange" :remote-method="onGoodsCodeRmoteMethod" filterable remote
                                reserve-keyword :loading="dialogContent.goodsCodeLoading">
                                <el-option v-for="item in dialogContent.goodsCodeList" :label="item.goodsCode"
                                    :value="item.goodsCode">
                                </el-option>
                            </el-select>
                            <span style="color: red;margin-left: 10px;">编码前加*精确搜索</span>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="12" :span="12">
                        <span>{{ this.dialogContent.addFormData.styleCode }}</span>
                    </el-col>
                </el-row>
                <el-row style="background-color: #ccc; text-align: center;font-size: 16px; " :gutter="24">
                    <el-col :span="12"
                        style="border-right: 1px solid white;padding-right: 27px;height: 30px;line-height: 30px;box-sizing: border-box;">
                        <span>旧</span>
                        <i class="el-icon-right right" @click="sendAllProps" v-show="dialogContent.title != '详情'" />
                    </el-col>
                    <el-col :span="12"
                        style="border-right: 1px solid white;padding-right: 20px;height: 30px;line-height: 30px;box-sizing: border-box;">
                        新
                    </el-col>
                </el-row>
                <el-row style="margin-top: 10px;" :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldLengthNum" label="长(cm)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.oldLengthNum" placeholder="长(cm)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.oldLengthImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldLengthNum, dialogContent.addFormData.oldLengthImg], ['oldLengthNum', 'oldLengthImg'])" />
                                <!-- <i class="el-icon-right" @click="sendProps2" /> -->
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newLengthNum" label="长(cm)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.newLengthNum" placeholder="长(cm)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.newLengthImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldLengthNum" label="宽(cm)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.oldWidthNum" placeholder="宽(cm)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.oldWidthImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldWidthNum, dialogContent.addFormData.oldWidthImg], ['oldWidthNum', 'oldWidthImg'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newLengthNum" label="宽(cm)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.newWidthNum" placeholder="宽(cm)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.newWidthImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldLengthNum" label="高(cm)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.oldHeightNum" placeholder="高(cm)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.oldHeightImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldHeightNum, dialogContent.addFormData.oldHeightImg], ['oldHeightNum', 'oldHeightImg'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newLengthNum" label="高(cm)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.newHeightNum" placeholder="高(cm)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.newHeightImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldThicknessNum" label="厚度(丝)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.oldThicknessNum" placeholder="厚度(丝)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.oldThicknessImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldThicknessNum, dialogContent.addFormData.oldThicknessImg], ['oldThicknessNum', 'oldThicknessImg'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newThicknessNum" label="厚度(丝)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.newThicknessNum" placeholder="厚度(丝)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.newThicknessImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldLengthNum" label="克重(g)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.oldWeightNum" placeholder="克重(g)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.oldWeightImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldWeightNum, dialogContent.addFormData.oldWeightImg], ['oldWeightNum', 'oldWeightImg'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newLengthNum" label="克重(g)">
                            <div class="itemBox">
                                <el-input-number v-model="dialogContent.addFormData.newWeightNum" placeholder="克重(g)"
                                    :precision="2" :min="0" :max="9999" controls-position="right" :controls="false"
                                    class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.newWeightImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldColor" label="颜色">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.oldColor" placeholder="颜色" clearable
                                    maxlength="10" class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.oldColorImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldColor, dialogContent.addFormData.oldColorImg], ['oldColor', 'oldColorImg'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newColor" label="颜色">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.newColor" placeholder="颜色" clearable
                                    maxlength="10" class="iptCss" />
                                <YhImgUpload :value.sync="dialogContent.addFormData.newColorImg" :limit="1"
                                    ref="goodFrontBackImgs" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="dialogContent.title != '详情'" :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldVideo" label="视频">
                            <div class="itemBox">
                                <viodeUpload :minisize="false" ref="uploadexl" :limit="1" accepttyes=".mp4"
                                    :uploadprogress="true" :uploadInfo="dialogContent.addFormData.oldVideo"
                                    style="height: 70px; width: 70px;" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newVideo" label="视频">
                            <div class="itemBox">
                                <viodeUpload :minisize="false" ref="uploadexl1" :limit="1" accepttyes=".mp4"
                                    :uploadprogress="true" :uploadInfo="dialogContent.addFormData.newVideo"
                                    style="height: 70px; width: 70px;" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-else :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldVideo" label="视频">
                            <div class="itemBox">
                                <img src="../../.././static/images/vedio.jpg"
                                    @click="videoplay(dialogContent.addFormData.oldVideo)"
                                    style="height: 50px; width: 50px;" mode="aspectFit"
                                    v-if="dialogContent.addFormData.oldVideo" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newVideo" label="视频">
                            <div class="itemBox">
                                <img src="../../.././static/images/vedio.jpg"
                                    @click="videoplay(dialogContent.addFormData.newVideo)"
                                    style="height: 50px; width: 50px;" mode="aspectFit"
                                    v-if="dialogContent.addFormData.newVideo" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldMaterial" label="材质">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.oldMaterial" placeholder="材质" clearable
                                    maxlength="10" class="iptCss" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldMaterial], ['oldMaterial'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newMaterial" label="材质">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.newMaterial" placeholder="材质" clearable
                                    maxlength="10" class="iptCss" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldPackMethod" label="包装方式">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.oldPackMethod" placeholder="包装方式" clearable
                                    maxlength="10" class="iptCss" />
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldPackMethod], ['oldPackMethod'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newPackMethod" label="包装方式">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.newPackMethod" placeholder="包装方式" clearable
                                    maxlength="10" class="iptCss" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="oldLengthNum" label="货运方式">
                            <div class="itemBox">
                                <el-select v-model="dialogContent.addFormData.oldTransportMethod" placeholder="货运方式"
                                    clearable class="iptCss">
                                    <el-option v-for="item in freightMethodList" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                                <i class="el-icon-right" v-show="dialogContent.title != '详情'"
                                    @click="sendProps([dialogContent.addFormData.oldTransportMethod], ['oldTransportMethod'])" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newLengthNum" label="货运方式">
                            <div class="itemBox">
                                <el-select v-model="dialogContent.addFormData.newTransportMethod" placeholder="货运方式"
                                    clearable class="iptCss">
                                    <el-option v-for="item in freightMethodList" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="isApprovePrice" label="是否核价">
                            <el-select v-model="dialogContent.addFormData.isApprovePrice" placeholder="是否核价" clearable
                                class="iptCss">
                                <el-option label="是" value="是" />
                                <el-option label="否" value="否" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="isChangeSupplier" label="是否更换厂家">
                            <el-select v-model="dialogContent.addFormData.isChangeSupplier" placeholder="是否更换厂家"
                                clearable class="iptCss" @change="changeIsChangeSupplier">
                                <el-option label="是" value="是" />
                                <el-option label="否" value="否" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24" v-if="dialogContent.addFormData.isChangeSupplier == '是'">
                    <el-col :span="12">
                        <el-form-item prop="oldSupplierName" label="厂家名称">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.oldSupplierName" placeholder="厂家名称"
                                    maxlength="20" clearable class="iptCss" />
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="newSupplierName" label="厂家名称">
                            <div class="itemBox">
                                <el-input v-model="dialogContent.addFormData.newSupplierName" placeholder="厂家名称"
                                    maxlength="20" clearable class="iptCss" />
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item prop="similarity" label="对比结果">
                            {{ getSimilarity }}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="remark" label="备注">
                            <el-input type="textarea" :rows="5" placeholder="备注" maxlength="300"
                                v-model="dialogContent.addFormData.remark">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div style="display: flex;justify-content: end;margin:0 20px 20px 0 ;">
                <el-button type="primary" @click="onSave()" v-show="dialogContent.title != '详情'" :loading="saveloading"
                    v-throttle="3000">保存</el-button>
                <el-button type="primary" @click="onSaveApproval()" v-show="dialogContent.title != '详情'"
                    :loading="saveloading" v-throttle="3000">保存&发起审批</el-button>
                <el-button @click="onCancel()">取消</el-button>
            </div>
        </el-drawer>

        <el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" @close="closeVideoPlyer"
            :append-to-body="true" v-dialogDrag>
            <videoplayer v-if="videoplayerReload" autoplay ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="视频播放" :visible.sync="dialogContentVideo.visible" width="50%" :close-on-click-modal="false"
            v-dialogDrag>
            <div style=" width: 45.3125vw; height: 50.1vh;  position: relative;">
                <video controls="" ref="video" id="video" controlslist="nodownload noremoteplayback" :autoplay="true"
                    oncontextmenu="return false;" style="width:100%;height:100%" :src="dialogContentVideo.videosrc">
                </video>
            </div>
        </el-dialog>

        <el-dialog title="审批记录" :visible.sync="auditVisible" width="30%" :close-on-click-modal="false" v-dialogDrag>
            <el-timeline>
                <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.date">
                    {{ activity.userId + '-' + activity.result + (activity.remark ? '-' + activity.remark : '') }}
                </el-timeline-item>
            </el-timeline>
        </el-dialog>

        <el-dialog title="日志" :visible.sync="logVisible" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <logDetails v-if="logVisible" :id="logListId" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import dayjs from 'dayjs';
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    getGoodsList10Row, getSampleChangeCheckPageList, getSampleChangeCheckById,
    saveSampleChangeCheck, sampleChangeCheckApprovalInitiate, getSampleChangeCheckApprovalRecord, sampleChangeCheckApprovalBack
} from '@/api/operatemanage/samplechangecheck'
import viodeUpload from "@/components/upload/yh-video-upload.vue";
import logDetails from './components/logDetails.vue'
const freightMethodList = [
    { label: "包邮-物流点自提", value: "包邮-物流点自提" },
    { label: "包邮-快运送货上门", value: "包邮-快运送货上门" },
    { label: "包邮-快递送货上门（自提）", value: "包邮-快递送货上门（自提）" },
    { label: "包邮-货拉拉送货上门", value: "包邮-货拉拉送货上门" },
    { label: "包邮-跑腿", value: "包邮-跑腿" },
    { label: "包邮-直接配送", value: "包邮-直接配送" },
    { label: "不包邮-物流到付", value: "不包邮-物流到付" },
    { label: "不包邮-快运到付", value: "不包邮-快运到付" },
    { label: "不包邮-货拉拉到付", value: "不包邮-货拉拉到付" },
    { label: "不包邮-跑腿", value: "不包邮-跑腿" },
]
const similarityListbase = [{ label: "相似度100%", value: 100 }, { label: "相似度90%", value: 90 }, { label: "相似度80%", value: 80 }, { label: "相似度50%", value: 50 }, { label: "相似度0%", value: 0 }];
const tableCols = [
    { sortable: 'custom', width: '100', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'approvalStatus', label: '审批状态', type: 'clickLink', style: (that, row) => that.shenheStatus(row), },
    { sortable: 'custom', width: '100', align: 'center', prop: 'groupNames', label: '运营组', },
    {
        label: `长`, merge: true, prop: 'mergeField',
        cols: [
            { prop: 'oldLengthNum', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'oldLengthImg', label: '图', width: '90', type: 'image' },
            { prop: 'newLengthNum', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newLengthNum'), },
            { prop: 'newLengthImg', label: '图', width: '90', type: 'image' },
        ]
    },
    {
        label: `宽`, merge: true, prop: 'mergeField1',
        cols: [
            { prop: 'oldWidthNum', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'oldWidthImg', label: '图', width: '90', type: 'image' },
            { prop: 'newWidthNum', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newWidthNum'), },
            { prop: 'newWidthImg', label: '图', width: '90', type: 'image' },
        ]
    },
    {
        label: `高`, merge: true, prop: 'mergeField2',
        cols: [
            { prop: 'oldHeightNum', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'oldHeightImg', label: '图', width: '90', type: 'image' },
            { prop: 'newHeightNum', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newHeightNum'), },
            { prop: 'newHeightImg', label: '图', width: '90', type: 'image' },
        ]
    },
    {
        label: `厚度`, merge: true, prop: 'mergeField3',
        cols: [
            { prop: 'oldThicknessNum', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'oldThicknessImg', label: '图', width: '90', type: 'image' },
            { prop: 'newThicknessNum', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newThicknessNum'), },
            { prop: 'newThicknessImg', label: '图', width: '90', type: 'image' },
        ]
    },
    {
        label: `克重`, merge: true, prop: 'mergeField4',
        cols: [
            { prop: 'oldWeightNum', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'oldWeightImg', label: '图', width: '90', type: 'image' },
            { prop: 'newWeightNum', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newWeightNum'), },
            { prop: 'newWeightImg', label: '图', width: '90', type: 'image' },
        ]
    },
    {
        label: `颜色`, merge: true, prop: 'mergeField5',
        cols: [
            { prop: 'oldColor', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'oldColorImg', label: '图', width: '90', type: 'image' },
            { prop: 'newColor', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newColor'), },
            { prop: 'newColorImg', label: '图', width: '90', type: 'image' },
        ]
    },
    {
        label: `视频`, merge: true, prop: 'mergeField6',
        cols: [
            {
                prop: 'oldVideo', label: '旧', width: '90', type: 'button', btnList: [
                    { label: '查看', handle: (that, row) => that.videoplay(row.oldVideo) }
                ]
            },
            {
                prop: 'newVideo', label: '新', width: '90', type: 'button', btnList: [
                    { label: '查看', handle: (that, row) => that.videoplay(row.newVideo) }
                ]
            },
        ]
    },
    {
        label: `材质`, merge: true, prop: 'mergeField7',
        cols: [
            { prop: 'oldMaterial', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'newMaterial', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newMaterial'), },
        ]
    },
    {
        label: `包装方式`, merge: true, prop: 'mergeField8',
        cols: [
            { prop: 'oldPackMethod', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'newPackMethod', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newPackMethod'), },
        ]
    },
    {
        label: `货运方式`, merge: true, prop: 'mergeField9',
        cols: [
            { prop: 'oldTransportMethod', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'newTransportMethod', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newTransportMethod'), },
        ]
    },
    {
        label: `厂家名称`, merge: true, prop: 'mergeField10',
        cols: [
            { prop: 'oldSupplierName', label: '旧', sortable: 'custom', width: '90' },
            { prop: 'newSupplierName', label: '新', sortable: 'custom', width: '90', type: 'clickLink', style: (that, row) => that.renderStatus(row, 'newSupplierName'), },
        ]
    },
    { sortable: 'custom', width: '100', align: 'center', prop: 'isApprovePrice', label: '是否核价', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '添加人', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdTime', label: '添加时间', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'similarity', label: '对比结果', formatter: (row) => similarityListbase.find(a => a.value == row.similarity)?.label },
    { sortable: 'custom', width: '150', align: 'center', prop: 'remark', label: '备注', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'approvalRefuseTime', label: '拒绝时间', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'approvalRefuseUserName', label: '拒绝人', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'approvalRefuseReason', label: '拒绝原因', },
];
export default {
    name: "samplechangecheck",
    components: {
        MyContainer, datepicker, vxetablebase, YhImgUpload, videoplayer, viodeUpload, logDetails
    },
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            dialogContentVideo: {
                visible: false,
                videosrc: "",
            },
            videoUrl: null,
            videoDialogVisible: false,//视频弹窗
            videoplayerReload: false,//视频重新加载
            freightMethodList,
            that: this,
            similarityList: similarityListbase,
            pickerOptions,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                approvalStatus: null,
                styleCode: null,
                goodsCode: null,
                groupName: null,
                newLengthNum: undefined,
                newWidthNum: undefined,
                newHeightNum: undefined,
                newThicknessNum: undefined,
                newWeightNum: undefined,
                newColor: null,
                newMaterial: null,
                createdUserName: null,
                newPackMethod: null,
                newTransportMethod: null,
                similarity: null,
                startApprovalRefuseTime: null,
                endApprovalRefuseTime: null,
                approvalRefuseUserName: null,
            },
            pager: { OrderBy: "createdTime", IsAsc: false },
            logListId: null,
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            saveloading: false,
            logVisible: false,
            dialogContent: {
                title: "",
                visible: false,
                addFromLoading: false,
                goodsCodeList: [],
                goodsCodeLoading: false,
                addFromDisabled: false,
                addFormData: {
                    id: null,
                    approvalStatus: null,
                    styleCode: null,
                    goodsCode: null,
                    goodsName: null,

                    newLengthNum: undefined,
                    newLengthImg: null,
                    oldLengthNum: undefined,
                    oldLengthImg: null,

                    newWidthNum: undefined,
                    newWidthImg: null,
                    oldWidthNum: undefined,
                    oldWidthImg: null,

                    newHeightNum: undefined,
                    newHeightImg: null,
                    oldHeightNum: undefined,
                    oldHeightImg: null,

                    newThicknessNum: undefined,
                    newThicknessImg: null,
                    oldThicknessNum: undefined,
                    oldThicknessImg: null,

                    newWeightNum: undefined,
                    newWeightImg: null,
                    oldWeightNum: undefined,
                    oldWeightImg: null,

                    newColor: null,
                    newColorImg: null,
                    oldColor: null,
                    oldColorImg: null,

                    newVideo: null,
                    oldVideo: null,

                    newMaterial: null,
                    oldMaterial: null,

                    newPackMethod: null,
                    oldPackMethod: null,

                    newTransportMethod: null,
                    oldTransportMethod: null,

                    isApprovePrice: null,
                    isChangeSupplier: null,
                    oldSupplierName: null,
                    newSupplierName: null,

                    similarity: 100,
                    remark: null,
                },
                addFormRules: {
                    goodsCode: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
                    oldSupplierName: [{ required: true, message: '请输入厂家地址', trigger: 'blur' }],
                    newSupplierName: [{ required: true, message: '请输入厂家地址', trigger: 'blur' }],
                },
            },
        }
    },
    async mounted() {
        this.onSearch();
    },
    computed: {
        getSimilarity() {
            /*
            对比结果：相似度100%、90%、80%、50%。自动计算
            相似度100%：旧  新 除视频外，都相同
            相似度90%：旧  新 除视频外 两个不同
            相似度80%：旧  新 除视频外 4个不同
            相似度50%：旧  新 除视频外 4个以上不同
            相似度0%：旧  新 除视频外全不同
            */
            let similarity = 100;
            let oldData = this.dialogContent.addFormData;
            let newData = this.dialogContent.addFormData;
            let diffCount = 0;
            Object.keys(oldData).forEach(key => {
                //排除掉img和video
                if (key.indexOf("Img") == -1 && key.indexOf("Video") == -1) {
                    //找出包含old的值
                    if (key.indexOf("old") > -1) {
                        if (oldData[key] != newData[key.replace("old", "new")]) {
                            diffCount++;
                        }
                    }
                }
            });
            if (diffCount == 0) {
                similarity = 100;
            }
            else if (diffCount <= 2) {
                similarity = 90;
            }
            else if (diffCount <= 4) {
                similarity = 80;
            }
            else if (diffCount > 4 && diffCount <= 8) {
                similarity = 50;
            }
            else {
                similarity = 0;
            }
            this.dialogContent.addFormData.similarity = similarity;
            return similarity + '%';
        }
    },
    methods: {
        changeIsChangeSupplier(e) {
            if (e == '否') {
                this.dialogContent.addFormData.oldSupplierName = null;
                this.dialogContent.addFormData.newSupplierName = null;
            }
        },
        //日志
        openLogDetails(id) {
            this.logListId = id;
            this.logVisible = true;
        },
        async changeTime(e) {
            this.filter.startApprovalRefuseTime = e ? e[0] : null
            this.filter.endApprovalRefuseTime = e ? e[1] : null
        },
        shenheStatus(row) {
            if (row.approvalStatus == '已审批') {
                return 'color:green';
            } else if (row.approvalStatus == '已拒绝') {
                return 'color:red';
            } else {
                return 'color:black';
            }
        },
        async getAuditList(id) {
            const { data, success } = await getSampleChangeCheckApprovalRecord({ id })
            if (success) {
                this.activities = data
                this.auditVisible = true
                console.log(data, 'data');
            }
        },
        renderStatus(row, key) {
            if (key.indexOf("Num") > -1) {
                if (row[key] > row[key.replace("new", "old")]) {
                    return 'color:red';
                } else if (row[key] < row[key.replace("new", "old")]) {
                    return 'color:green';
                } else {
                    return 'color:black';
                }
            } else {
                if (row[key] != row[key.replace("new", "old")]) {
                    return 'color:red';
                } else {
                    return 'color:black';
                }
            }
        },
        sendAllProps() {
            Object.keys(this.dialogContent.addFormData).forEach(key => {
                if (key.indexOf("old") > -1 && key.indexOf('Video') == -1) {
                    let newk = key.replace("old", "new");
                    this.dialogContent.addFormData[newk] = this.dialogContent.addFormData[key];
                }
            });
        },
        videoplay(videoUrl) {
            this.dialogContentVideo.videosrc = null
            if (!videoUrl) return this.$message.error('暂无视频');
            this.dialogContentVideo.videosrc = videoUrl;
            this.dialogContentVideo.visible = true;
        },
        sendProps(val, key) {
            val.forEach((item, index) => {
                this.dialogContent.addFormData[key[index].replace("old", "new")] = item;
            });
        },
        clear() {
            Object.keys(this.dialogContent.addFormData).forEach(key => {
                if (key.indexOf("Num") > -1) {

                    this.dialogContent.addFormData[key] = undefined;
                }
                else {
                    this.dialogContent.addFormData[key] = null;
                }
            });
            this.dialogContent.addFormData.newVideo = []
            this.dialogContent.addFormData.oldVideo = []
            this.dialogContent.goodsCodeList = []
        },
        // 关闭视频弹窗
        async closeVideoPlyer() {
            this.videoDialogVisible = false;
            this.videoplayerReload = false;
        },
        onClearfilter() {
            Object.keys(this.filter).forEach(key => {
                if (key == "timerange" || key == "startDate" || key == "endDate") {
                    return;
                }
                if (key.indexOf("Num") > -1) {
                    this.filter[key] = undefined;
                }
                else {
                    this.filter[key] = null;
                }
            });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange.length == 0) {
                //默认给时间
                this.filter.startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
                this.filter.endDate = dayjs().format('YYYY-MM-DD')
                this.filter.timerange = [this.filter.startDate, this.filter.endDate]
            }
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message.error('日期必填');
                return;
            }
            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await getSampleChangeCheckPageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onAdd() {
            this.clear()
            this.dialogContent.addFormData.similarity = 100
            this.dialogContent.title = "新增";
            this.dialogContent.addFromDisabled = false
            this.dialogContent.visible = true;
        },
        async onEdit(row, title) {
            this.dialogContent.addFromLoading = true;
            this.clear()
            this.dialogContent.title = title;
            this.dialogContent.addFromDisabled = (title == '详情') ? true : false;
            this.dialogContent.visible = true;
            this.$nextTick(() => {
                if (this.dialogContent.title != '详情') {
                    let video1 = [];
                    if (row.oldVideo) {
                        video1 = [
                            {
                                url: row.oldVideo,
                                fileName: row.oldVideo,
                                relativePath: null,
                                domain: null,
                            }
                        ]
                    }
                    let video2 = [];
                    if (row.newVideo) {
                        video2 = [
                            {
                                url: row.newVideo,
                                fileName: row.newVideo,
                                relativePath: null,
                                domain: null,
                            }
                        ]
                    }
                    this.$refs.uploadexl.setData(video1);
                    this.$refs.uploadexl1.setData(video2);
                }
            });
            let res = await getSampleChangeCheckById({ id: row.id });
            if (res?.success) {
                let data = res.data;
                Object.keys(this.dialogContent.addFormData).forEach(key => {
                    this.dialogContent.addFormData[key] = data[key];
                });
            }
            this.dialogContent.addFromLoading = false;
        },
        async onGoodsCodeRmoteMethod(value) {
            this.dialogContent.goodsCodeLoading = true;
            let res = await getGoodsList10Row({ goodsCode: value });
            this.dialogContent.goodsCodeLoading = false;
            if (res?.success) {
                this.dialogContent.goodsCodeList = res.data;
            }
        },
        async onGoodsCodeChange(value) {
            if (value) {
                let myGoods = this.dialogContent.goodsCodeList.find(f => f.goodsCode == value);
                this.dialogContent.addFormData.goodsName = myGoods?.goodsName;
                this.dialogContent.addFormData.styleCode = myGoods?.styleCode;
            }
            else {
                this.dialogContent.addFormData.goodsName = null;
                this.dialogContent.addFormData.styleCode = null;
            }
        },
        getReturn() {
            let videoRes = this.$refs.uploadexl.getReturns();
            if (videoRes.data?.length > 0) {
                this.dialogContent.addFormData.oldVideo = videoRes.data[0].url;
            } else {
                this.dialogContent.addFormData.oldVideo = null;
            }
            let videoRes1 = this.$refs.uploadexl1.getReturns();
            if (videoRes1.data?.length > 0) {
                this.dialogContent.addFormData.newVideo = videoRes1.data[0].url;
            } else {
                this.dialogContent.addFormData.newVideo = null;
            }
        },
        async onSave() {
            if (this.dialogContent.addFormData.isChangeSupplier == '是') {
                if (!this.dialogContent.addFormData.oldSupplierName) return this.$message.error('请输入厂家地址');
                if (!this.dialogContent.addFormData.newSupplierName) return this.$message.error('请输入厂家地址');
                if (this.dialogContent.addFormData.oldSupplierName == this.dialogContent.addFormData.newSupplierName) return this.$message.error('旧厂家名称和新厂家名称不能相同');
            }
            this.dialogContent.addFormData = this.getProps(this.dialogContent.addFormData);
            this.getReturn()
            if (!this.dialogContent.addFormData.id)
                this.dialogContent.addFormData.id = 0;
            this.saveloading = true;
            let res = await saveSampleChangeCheck(this.dialogContent.addFormData);
            this.saveloading = false;
            if (res?.success) {
                this.onSearch();
                this.dialogContent.visible = false;
                this.$message.success('保存成功')
            }
            else {
                //this.$message.error('保存失败，请刷新后重试')
            }
        },
        getProps(obj) {
            //判断是否是一个数组
            if (this.dialogContent.addFormData.oldVideo && this.dialogContent.addFormData.oldVideo.length == 0) {
                this.dialogContent.addFormData.oldVideo = null;
            }
            if (this.dialogContent.addFormData.newVideo && this.dialogContent.addFormData.newVideo.length == 0) {
                this.dialogContent.addFormData.newVideo = null;
            }
            //遍历对象,取出obj的key,如果key包含Img,就将其值转化成json数组
            for (let key in obj) {
                if (key.indexOf("Img") > -1) {
                    if (obj[key] && obj[key][0] !== '[') {
                        obj[key] = JSON.stringify([{ name: '1', url: obj[key] }]);
                    }
                }
            }
            return obj
        },
        onSaveApproval() {
            this.$confirm('此操作将发起钉钉审批, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.dialogContent.addFormData = this.getProps(this.dialogContent.addFormData);
                this.getReturn()
                if (!this.dialogContent.addFormData.id)
                    this.dialogContent.addFormData.id = 0;
                if (this.dialogContent.addFormData.isChangeSupplier == '是') {
                    if (!this.dialogContent.addFormData.oldSupplierName) return this.$message.error('请输入厂家地址');
                    if (!this.dialogContent.addFormData.newSupplierName) return this.$message.error('请输入厂家地址');
                    if (this.dialogContent.addFormData.oldSupplierName == this.dialogContent.addFormData.newSupplierName) return this.$message.error('旧厂家名称和新厂家名称不能相同');
                }
                this.saveloading = true;
                let res = await saveSampleChangeCheck(this.dialogContent.addFormData);
                this.saveloading = false;
                if (res?.success) {
                    this.dialogContent.addFormData.id = res.data;
                    let res2 = await sampleChangeCheckApprovalInitiate(this.dialogContent.addFormData);
                    if (res2?.success) {
                        await this.onSearch();
                        this.dialogContent.visible = false;
                        this.$message.success('发起审批成功')
                    }
                }
                this.saveloading = false;
            })
        },
        onSaveApproval1(row) {
            this.$confirm('此操作将发起钉钉审批, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.listLoading = true;
                if (!this.dialogContent.addFormData.id)
                    this.dialogContent.addFormData.id = 0;
                let res2 = await sampleChangeCheckApprovalInitiate(row);
                if (res2?.success) {
                    await this.onSearch();
                    this.listLoading = false;
                    this.$message.success('发起审批成功')
                } else {
                    this.listLoading = false;
                    this.$message.error('发起审批失败')
                }
            })
        },
        async onCancel() {
            this.dialogContent.visible = false;
        },
        async onBackApproval(row) {
            console.log(row);
            if (!row.id) {
                this.$message.error('数据异常，请刷新后重试');
                return;
            }
            this.$confirm('此操作将撤销钉钉审批, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.listLoading = true;
                let res2 = await sampleChangeCheckApprovalBack(row);
                this.listLoading = false;
                if (res2?.success) {
                    this.$message.success('撤销审批成功')
                    await this.onSearch();
                }
            });
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
