<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <!-- <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker> -->
        <el-date-picker  class="publicCss"  v-model="ListInfo.yearMonth" type="month" format="yyyy-MM"
        value-format="yyyy-MM" placeholder="选择月份"></el-date-picker>
        <div>
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </div>
        <!-- <el-select v-model="ListInfo.expressName" clearable filterable placeholder="快递公司" class="publicCss"
          @change="getprosimstatelist(2)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.prosimstate" clearable filterable placeholder="快递站点" class="publicCss"
          @change="checkSite">
          <el-option label="暂无站点" value="" />
          <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.warehouse" clearable filterable placeholder="发货仓库" class="publicCss"
          @change="checkWarehouse">
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onDailyComputing">计算月账单</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'monthBillSummary202410161535'" :tablekey="'monthBillSummary202410161535'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="计算月账单" :visible.sync="dialogCalDayRepotVis" width="40%" v-dialogDrag>
      <div style="height: 100px;">
        <el-row>
          <el-select v-model="expressCompanyId" placeholder="快递公司" style="width: 200px;margin-bottom: 10px;" clearable
            @change="getprosimstatelist(1)">
            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px;margin: 0 10px;">
            <el-option label="暂无站点" value="" />
            <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
          <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 50%; float: left;" v-model="lastExcuteTime" type="month" format="yyyy-MM"
              value-format="yyyy-MM" placeholder="选择月份"></el-date-picker>
            <el-button type="success" style="margin-left:  30px;" @click="calDayRepoty">计算月账单</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <el-dialog title="编辑" :visible.sync="editDialogVisible" width="40%" v-dialogDrag :close-on-click-modal="false"
      style="margin-top: -3vh;">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="快递公司" :label-width="'125px'" prop="expressCompany">
              {{ ruleForm.expressCompany }}
            </el-form-item>
            <el-form-item label="日期" :label-width="'125px'" prop="yearMonthDayDtae">
              {{ ruleForm.yearMonthDayDtae }}
            </el-form-item>
            <el-form-item label="理赔" :label-width="'125px'" prop="claimAmount">
              <el-input-number v-model.trim="ruleForm.claimAmount" placeholder="理赔" :min="-99999999" :max="99999999"
                @change="onPayment" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
            <el-form-item label="重量差" :label-width="'125px'" prop="diffWeight">
              <el-input-number v-model.trim="ruleForm.diffWeight" placeholder="重量差" :min="-99999999" :max="99999999"
                @change="onPayment" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
            <el-form-item label="已付面单费" :label-width="'125px'" prop="platformDeduction">
              <el-input-number v-model.trim="ruleForm.platformDeduction" placeholder="平台扣款" :min="-99999999"
                @change="onPayment" :max="99999999" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
            <el-form-item label="已付款金额" :label-width="'125px'" prop="hadPayAmount">
              <el-input-number v-model.trim="ruleForm.hadPayAmount" placeholder="已付款金额" :min="-99999999" :max="99999999"
                @change="onRemaining" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
            <el-form-item label="应付金额" :label-width="'125px'" prop="payableAmount">
              <el-input-number v-model.trim="ruleForm.payableAmount" placeholder="应付金额" :min="-99999999" :max="99999999"
                @change="onRemaining" :precision="2" :controls="false" class="editCss" disabled />
            </el-form-item>
            <el-form-item label="返款金额" :label-width="'125px'" prop="rebateAmount" v-if="siteVerify">
              <el-input-number v-model.trim="ruleForm.rebateAmount" placeholder="返款金额" :min="-99999999" :max="99999999"
                @change="onRefund" :precision="2" :controls="false" class="editCss" disabled />
            </el-form-item>
            <el-form-item label="已返款金额" :label-width="'125px'" prop="isRebateAmount" v-if="siteVerify">
              <el-input-number v-model.trim="ruleForm.isRebateAmount" placeholder="已返款金额" :min="-99999999"
                @change="onRefund" :max="99999999" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
            <el-form-item label="已付操作费（元）" :label-width="'125px'" prop="isPayOperatingCost" v-if="stashVerify">
              <el-input-number v-model.trim="ruleForm.isPayOperatingCost" placeholder="已付操作费(元)" :min="-99999999"
                :max="99999999" @change="onOutstanding" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递站点" :label-width="'125px'" prop="prosimstate">
              {{ ruleForm.prosimstate }}
            </el-form-item>
            <el-form-item label="发货仓库" :label-width="'125px'" prop="warehouseName">
              {{ ruleForm.warehouseName }}
            </el-form-item>
            <!-- <el-form-item label="期初金额" :label-width="'125px'" prop="openingAmount">
              <el-input-number v-model.trim="ruleForm.openingAmount" placeholder="期初金额" :min="-99999999" :max="99999999"
                @change="onRemaining" :precision="2" :controls="false" class="editCss" />
            </el-form-item> -->
            <el-form-item label="调整金额" :label-width="'125px'" prop="adjustmentAmount">
              <el-input-number v-model.trim="ruleForm.adjustmentAmount" placeholder="调整金额" :min="-99999999"
                @change="onPayment" :max="99999999" :precision="2" :controls="false" class="editCss" />
            </el-form-item>
            <el-form-item label="付款时间" :label-width="'125px'" prop="payTime">
              <el-date-picker v-model="ruleForm.payTime" type="datetime" placeholder="选择日期时间" style="float: left;"
                class="editCss" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="剩余金额" :label-width="'125px'" prop="residualAmount">
              <el-input-number v-model.trim="ruleForm.residualAmount" placeholder="剩余金额" :min="-99999999"
                :max="99999999" :precision="2" :controls="false" class="editCss" disabled />
            </el-form-item>
            <el-form-item label="返款金额调整" :label-width="'125px'" prop="noRebateAmount" v-if="siteVerify">
              <el-input-number v-model.trim="ruleForm.rebateAmountAdjust" placeholder="返款金额调整" :min="-99999999"
                :max="99999999" :precision="2" :controls="false" class="editCss"   @change="onRefund"/>
            </el-form-item>
            <el-form-item label="未返款余额" :label-width="'125px'" prop="noRebateAmount" v-if="siteVerify">
              <el-input-number v-model.trim="ruleForm.noRebateAmount" placeholder="未返款余额" :min="-99999999"
                :max="99999999" :precision="2" :controls="false" class="editCss" disabled />
            </el-form-item>
            <el-form-item label="" :label-width="'125px'">
              <div style="height: 28px;"></div>
            </el-form-item>
            <el-form-item label="未付操作费（元）" :label-width="'125px'" prop="noPayOperatingCost" v-if="stashVerify">
              <el-input-number v-model.trim="ruleForm.noPayOperatingCost" placeholder="未付操作费(元)" :min="-99999999"
                :max="99999999" :precision="2" :controls="false" class="editCss" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注" :label-width="'125px'" prop="remark">
            <el-input type="textarea" v-model.trim="ruleForm.remark" :autosize="{ minRows: 2, maxRows: 2 }"
              placeholder="备注" style="width: 96%;">
            </el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <div style="display: flex;align-items: center;justify-content: center;margin-top: 10px;">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveForm">保存</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { editDayExpressCompanyFeeCalculate_Summary, dayExpressCompanyFeeCalculate_Summary, getExpressDayBillsSummary, getExpressComanyStationName, getExpressComanyAll, exportExpressDayBillsSummary } from "@/api/express/express";
import { formatTime } from "@/utils";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import decimal from "@/utils/decimalToFixed"
import queryCondition from "./queryCondition.vue";

const tableCols = [
  { sortable: 'custom', width: '90', align: 'center', prop: 'yearMonthDayDtae', label: '日期', formatter: (row) => formatTime(row.yearMonthDayDtae, "YYYY-MM") },
  { sortable: 'custom', width: '120', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'prosimstate', label: '快递站点', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'warehouseName', label: '发货仓库' },
  { sortable: 'custom', width: '75', align: 'center', prop: 'receiveCount', label: '收寄量', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'expressFee', label: '邮费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'czFee', label: '操作费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'packageFee', label: '包材费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'overWeightFee', label: '续重费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'faceFee', label: '面单费', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'sjTotalFee', label: '运费总额', },
  {  width: '75', align: 'center', prop: '', label: '运费均值', formatter: (row) => row.receiveCount !=0? (row.sjTotalFee/row.receiveCount).toFixed(2):"" },
  { sortable: 'custom', width: '75', align: 'center', prop: 'claimAmount', label: '理赔', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'addFee', label: '加收', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'diffWeight', label: '重量差', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'platformDeduction', label: '已付面单费', },
  { sortable: 'custom', width: '95', align: 'center', prop: 'adjustmentAmount', label: '调整金额', tipmesg: '调整列', },
  { sortable: 'custom', width: '95', align: 'center', prop: 'payableAmount', label: '应付款金额', tipmesg: '运费总额-理赔-重量差-已付面单费-调整金额', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'hadPayAmount', label: '已付款金额', },
  // { sortable: 'custom', width: '95', align: 'center', prop: 'openingBalance', label: '期初余额', tipmesg: '前一天的剩余的金额', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'payTime', label: '付款时间', },
  { sortable: 'custom', width: '95', align: 'center', prop: 'residualAmount', label: '剩余金额', tipmesg: '期初数（前一天的剩余余额）-应付款金额+已付款金额', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'rebateAmount', label: '返款金额', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'rebateAmountAdjust', label: '返款金额调整', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'isRebateAmount', label: '已返款金额', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'noRebateAmount', label: '未返款余额', },
  { sortable: 'custom', width: '95', align: 'center', prop: 'isPayOperatingCost', label: '已付操作费（元）', },
  { sortable: 'custom', width: '95', align: 'center', prop: 'noPayOperatingCost', label: '未付操作费（元）', },
  // { sortable: 'custom', width: '95', align: 'center', prop: 'yesterdayLeftoverNoPayOperatingCost', label: '昨日剩余的未付操作费（元）', },
  { sortable: 'custom', width: '75', align: 'center', prop: 'remark', label: '备注', },
]
export default {
  name: "monthBillSummary",
  components: {
    MyContainer, vxetablebase, queryCondition
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      siteVerify: false,
      stashVerify: false,
      expressCompanyId: null,//快递公司
      prosimstate: null,//快递站点
      warehouse: null,//发货仓库
      warehouselist,
      formatWarehouseNew,
      prosimstatelist: [],
      ruleForm: {
        claimAmount: '',
        platformDeduction: 0,
        hadPayAmount: 0,
        payTime: '',
        residualAmount: 0,
        remark: '',
        openingAmount: 0,
        adjustmentAmount: 0,
        expressCompany: 0,
        yearMonthDayDtae: 0,
        warehouse: 0,
        prosimstate: 0,
        sjTotalFee: 0,
        diffWeight: 0,
        payableAmount: 0,
        rebateAmount: 0,
        difference: 0,
        isRebateAmount: 0,
        rebateAmountAdjust: 0,
        noRebateAmount: 0,
        openingBalance: 0,
        noPayOperatingCost: 0,
        isPayOperatingCost: 0,
        czFee: 0,
      },
      editVisible: false,
      expresscompanylist: [],
      dialogCalDayRepotVis: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'yearMonthDayDtae',
        isAsc: true,
       // startTime: null,//开始时间
       // endTime: null,//结束时间
        expressName: null,
        prosimstate: null,
        warehouse: null,
        summaryType: 2,
        yearMonth:null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      lastExcuteTime: null,
      editDialogVisible: false,
      ruleForm: {},
      rules: {},
    }
  },
  watch: {
    topfilter: {
      handler: function (newValue, oldValue) {
        this.ListInfo = Object.assign({}, this.ListInfo, newValue)
        this.checkSite(this.topfilter.prosimstateId)
        this.checkWarehouse(this.topfilter.warehouseId)
      },
      deep: true
    }
  },
  async mounted() {
    if(!this.ListInfo.yearMonth){
      this.ListInfo.yearMonth = dayjs().startOf('month').format('YYYY-MM')
    }
    await this.init()
    // await this.getList()
  },
  methods: {
    checkSite(e) {
      console.log(e, 'e');
      this.$nextTick(() => {
        if (e == 77) {
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('noRebateAmount'))
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('isRebateAmount'))
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('rebateAmountAdjust'))
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('rebateAmount'))
          this.siteVerify = true
        } else {
          this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('noRebateAmount'))
          this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('isRebateAmount'))
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('rebateAmountAdjust'))
          this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('rebateAmount'))
          this.siteVerify = false
        }
      })
    },
    checkWarehouse(e) {
      console.log(e, 'e');
      this.$nextTick(() => {
        if (e == 33 && this.topfilter.prosimstateId == 58) {
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('noPayOperatingCost'))
          this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('isPayOperatingCost'))
          this.stashVerify = true
        } else {
          this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('noPayOperatingCost'))
          this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('isPayOperatingCost'))
          this.stashVerify = false
        }
      })
    },
    async exportProps() {
      this.loading = true
      const params = { ...this.ListInfo, expressName: this.topfilter.expressCompanyId, prosimstate: this.topfilter.prosimstateId, warehouse: this.topfilter.warehouseId }
      const { data } = await exportExpressDayBillsSummary(params)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '日账单汇总数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //加法
    precisionAdd(arg1, arg2) {
      var r1, r2, m, n;
      try {
        r1 = arg1.toString().split(".")[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split(".")[1].length
      } catch (e) {
        r2 = 0
      }
      m = Math.pow(10, Math.max(r1, r2))
      n = (r1 >= r2) ? r1 : r2;
      return ((arg1 * m + arg2 * m) / m).toFixed(n);
    },
    //减法
    precisionSub(arg1, arg2) {
      var r1, r2, m, n;
      try {
        r1 = arg1.toString().split(".")[1].length
      } catch (e) {
        r1 = 0
      }
      try {
        r2 = arg2.toString().split(".")[1].length
      } catch (e) {
        r2 = 0
      }
      m = Math.pow(10, Math.max(r1, r2))
      n = (r1 >= r2) ? r1 : r2;
      return ((arg1 * m - arg2 * m) / m).toFixed(n);
    },
    async getprosimstatelist(val) {
      var id;
      if (val == 1) {
        id = this.expressCompanyId
        this.prosimstate = null
      }
      else if (val == 2) {
        id = this.ListInfo.expressName
        this.ListInfo.prosimstate = null
      }
      var res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
    },
    onOutstanding() {
      //完成未付操作费=昨日剩余的未付操作费+操作费-已付款操作费
      this.ruleForm.noPayOperatingCost = decimal(decimal(this.ruleForm.czFee, this.ruleForm.isPayOperatingCost || 0, '-'), this.ruleForm.yesterdayLeftoverNoPayOperatingCost || 0, '+')
      this.$forceUpdate()
    },
    onRefund() {
      //未返款余额=期初余额+返款金额-已返款金额-返款金额调整
      this.ruleForm.noRebateAmount = this.precisionSub(this.precisionAdd(this.ruleForm.yesterdayNoRebateAmount || 0, this.ruleForm.rebateAmount || 0), this.ruleForm.isRebateAmount || 0)
      this.ruleForm.noRebateAmount = this.precisionSub(this.ruleForm.noRebateAmount,this.ruleForm.rebateAmountAdjust||0)
      this.$forceUpdate()
    },
    onRemaining() {

      if (this.ruleForm.warehouseId == 33) {
        //嘉定仓(剩余金额=昨天的剩余金额-运费总额-平台扣款-理赔-重量差+已付款金额+操作费)
        this.ruleForm.residualAmount = this.precisionAdd(this.precisionAdd(this.precisionSub(this.precisionAdd(this.ruleForm.openingAmount || 0, this.ruleForm.openingBalance || 0), this.ruleForm.payableAmount || 0), this.ruleForm.hadPayAmount || 0), this.ruleForm.czFee || 0)
      } else {
        //剩余金额=期初数（前一天的剩余余额）-应付款金额+已付款金额
        this.ruleForm.residualAmount = this.precisionAdd(this.precisionSub(this.precisionAdd(this.ruleForm.openingAmount || 0, this.ruleForm.openingBalance || 0), this.ruleForm.payableAmount || 0), this.ruleForm.hadPayAmount || 0)
      }

      this.$forceUpdate()
    },
    onPayment() {
      if(this.ruleForm.prosimstateId==77){
        this.ruleForm.rebateAmount = this.precisionAdd(this.ruleForm.difference||0,this.ruleForm.diffWeight || 0)
      }
      if (this.ruleForm.warehouseId == 33) {
        //嘉定仓(应付金额=运费总额-平台扣款-理赔-重量差-操作费)
        this.ruleForm.payableAmount = this.precisionSub(this.precisionSub(this.precisionSub(this.precisionSub(this.precisionSub(this.ruleForm.sjTotalFee || 0, this.ruleForm.platformDeduction || 0), this.ruleForm.claimAmount || 0), this.ruleForm.diffWeight || 0), this.ruleForm.adjustmentAmount || 0), this.ruleForm.czFee || 0)
      } else {
        //应付金额=运费总额-平台扣款-理赔-重量差-调整金额
        this.ruleForm.payableAmount = this.precisionSub(this.precisionSub(this.precisionSub(this.precisionSub(this.ruleForm.sjTotalFee || 0, this.ruleForm.platformDeduction || 0), this.ruleForm.claimAmount || 0), this.ruleForm.diffWeight || 0), this.ruleForm.adjustmentAmount || 0)
      }
      this.onRemaining()
      this.$forceUpdate()
    },
    onStorageMethod() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const res = await editDayExpressCompanyFeeCalculate_Summary(this.ruleForm)
          if (res?.success) {
            this.$message({ type: 'success', message: '编辑成功' })
            this.editVisible = false
            this.getList()
          } else {
            this.$message.error('编辑失败')
          }
        }
      })
    },
    handleEdit(row) {
      this.ruleForm = JSON.parse(JSON.stringify(row))
      this.editVisible = true
    },
    onDailyComputing() {
      this.expressCompanyId = null
      this.prosimstate = null
      this.warehouse = null
      this.lastExcuteTime = null
      this.expressCompanyId = this.topfilter.expressCompanyId ? (this.topfilter.expressCompanyId).toString() : null
      setTimeout(async() => {
        this.getprosimstatelist(1)
        this.prosimstate = this.topfilter.prosimstateId
        this.warehouse = this.topfilter.warehouseId
      }, 100);
      this.dialogCalDayRepotVis = true;
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      setTimeout(() => {
        this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('noPayOperatingCost'))
        this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('isPayOperatingCost'))
        this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('isRebateAmount'))
        this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('noRebateAmount'))
        this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('rebateAmount'))
      }, 500)
    },
    async calDayRepoty() {
      if (this.lastExcuteTime == null) {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
      const params = { yearMonth: this.lastExcuteTime,calcType:2, expressCompanyId: this.expressCompanyId, warehouse: this.warehouse, prosimstate: this.prosimstate }
      this.$confirm('确认计算?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await dayExpressCompanyFeeCalculate_Summary(params);
        if (!res?.success) return
        this.$message({ type: 'success', message: '正在计算中,可到右上角查看计算进度...' });
        this.dialogCalDayRepotVis = false;
      }).catch(() => {
        this.$message.info('已取消计算')
      });
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      // if (this.timeRanges && this.timeRanges.length == 0) {
      //   this.ListInfo.startTime = dayjs().startOf('month').format('YYYY-MM-DD')
      //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      // }

      this.loading = true
      const params = { ...this.ListInfo, expressName: this.topfilter.expressCompanyId, prosimstate: this.topfilter.prosimstateId, warehouse: this.topfilter.warehouseId }
      const { data, success } = await getExpressDayBillsSummary(params)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.yearMonthDayDtae = item.yearMonthDayDtae ? dayjs(item.yearMonthDayDtae).format('YYYY-MM-DD') : ""
        })
        this.total = data.total
        // this.summaryarry = data.summary

        let summary = data.summary || {}

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
            resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
            const options = {
                useGrouping: true,
            };
            return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    onEdit(row) {
      Object.keys(this.ruleForm).forEach(key => {
        this.ruleForm[key] = 0;
      });
      Object.keys(row).forEach(key => {
        this.ruleForm[key] = row[key];
      });
      Object.keys(this.ruleForm).forEach(key => {
        if (this.ruleForm[key] !== 0 && this.ruleForm[key] !== '' && this.ruleForm[key] !== null) {
          this.ruleForm[key] = 0;
        }
      });
      this.ruleForm = JSON.parse(JSON.stringify(row))
      this.editDialogVisible = true
    },

    saveForm() {
      if (this.ruleForm.isRebateAmount && !this.ruleForm.payTime) {
        this.$message.error('存在已返款金额请选择付款时间')
        return
      }
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await editDayExpressCompanyFeeCalculate_Summary(this.ruleForm)
          if (success) {
            this.editDialogVisible = false
            this.$message.success('操作成功')
            this.getList()
          }
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.editCss {
  width: 90%;
}

.form-item-container {
  width: 100%;
  display: flex;
}

.label-width {
  width: 40%;
}

.flex-container {
  width: 60%;
  display: flex;
}

.sub-label {
  margin-right: 5px;
  width: 65px;
}

.flex-1 {
  flex: 1;
}
</style>
