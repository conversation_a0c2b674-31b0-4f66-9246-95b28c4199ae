<template>
    <my-container>
        <template #header>
        </template>

      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
        :tableData='list'    :tableCols='tableCols' :isSelection="false" :tablefixed="true"
        :tableHandles='tableHandles' :isSelectColumn="false" @select="selsChange"
        :loading="listLoading" >
      </ces-table> 

      <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template> 
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue"
import {proDaysTurnover} from "@/api/order/procodesimilarity"

const tableCols=[
    {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
    {istrue:true,prop:'goodsName',label:'商品名称', width:'230',formatter:(row) => !row.goodsName?" " : row.goodsName},
    {istrue:true,prop:'turnoverRate3',label:'周转天数', width:'auto',sortable:'custom',formatter:(row) => !row.turnoverRate3? "0" : row.turnoverRate3.toFixed(0)},

];

export default {
    name: 'YunhanAdminDaysturnoverdetail',
    components: {MyContainer, MyConfirmButton, cesTable},
    props:{
        filter:{
            // goodsCode:null,
            // startDate:null,
            // endDate:null,
            // timerange:[startDate,endDate]
        }
    },
    data() {
        return {
            that:this,
            list: [],
            pager:{OrderBy:"turnoverRate3",IsAsc:false},
            tableCols:tableCols,
            tableHandles:[],
            summaryarry:{},
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading : false,            
        };
    },

    async mounted() {
        //await this.onSearch()
    },

    methods: {
        async onSearch(){            
            //this.filter.goodsCode = row
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist(){        
            var pager = this.$refs.pager.getPager();
            this.filter.startDate =null;
            this.filter.endDate =null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = {...pager, ...this.pager,...this.filter}
            this.listLoading = true
            var res = await proDaysTurnover(params)
            this.listLoading = false
            if (!res?.success) return;
            this.total = res.data.total
            const data = res.data.list
            //this.summaryarry =res.data.summary;
            // if(this.summaryarry)
            // this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
            data.forEach(d => {
            d._loading = false
            })
            this.list = data;  
        },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else{
                var orderBy =column.prop;
                this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
            }
            await this.onSearch();
        },
        selsChange: function(sels) {
            this.sels = sels;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>