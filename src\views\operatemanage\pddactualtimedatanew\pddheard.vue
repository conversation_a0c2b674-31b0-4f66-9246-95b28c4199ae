<template>
    <div class="allbody">
        <el-button-group>
            <div class="ssanc">
                <!-- <div style="display: inline-block;text-align: left;"> -->
                <div class="flexrow" style="margin: 5px;">
                    <!-- <div style="margin-right:0.8%;">
                        <el-input style="width:100%;" v-model.trim="filter.shopCode" :maxlength=100 placeholder="店铺编号"
                            @keyup.enter.native="onSearch" clearable />
                    </div>
                    <div style="margin-right:0.8%;">
                        <el-input v-model.trim="filter.shopName" :maxlength=100 placeholder="店铺名称"
                            suffix-icon="el-icon-search" @keyup.enter.native="onSearch" clearable />
                    </div> -->
                    <div style="margin-right:0.8%;">
                        <el-input v-model.trim="filter.productCode" :maxlength=100 placeholder="商品Id"
                            @keyup.enter.native="onSearch" clearable />
                    </div>

                    <div style="margin-right:0.8%;">
                        <el-input v-model.trim="filter.productName" :maxlength=100 placeholder="产品名称"
                            @keyup.enter.native="onSearch" clearable />
                    </div>
                    <!-- <div style="margin-right:0.8%;">
                        <el-input v-model.trim="filter.UserId" :maxlength=100 placeholder="运营助理"
                            suffix-icon="el-icon-search" @keyup.enter.native="onSearch" clearable />
                    </div>
                    <div style="margin-right:0.8%;">
                        <el-input v-model.trim="filter.OperateSpecialUserId" :maxlength=100 placeholder="运营专员"
                            suffix-icon="el-icon-search" @keyup.enter.native="onSearch" clearable />
                    </div>
                    <div style="margin-right:0.8%;">
                        <el-input v-model.trim="filter.UserId3" :maxlength=100 placeholder="备用负责人"
                            @keyup.enter.native="onSearch" clearable />
                    </div> -->
                    <el-date-picker style="width:18%;position: relative;top:1px;" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    v-model="filter.searchtime" />

                    <div style="margin-left:10px;">
                        <el-button type="primary" @click="onSearch">查&nbsp;询</el-button>
                    </div>
                    <div>
                        <el-button @click="clearfifter" plain>重置</el-button>
                    </div>


                </div>

                <div class="flexrow" style="margin-left: 5px;">
                    <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable
                        style="width: 160px;margin-left: 5px;">
                        <el-option v-for="(item,i) in shopList" :key="i" :label="item.shopName" :value="item.shopCode" />
                    </el-select>
                    <el-select style="width: 12%;" v-model="filter.groupId" placeholder="运营组" filterable
                        :collapse-tags="true" clearable>
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>

                    <el-select filterable style="width: 12%;" v-model="filter.userId" placeholder="运营助理"
                        :collapse-tags="true" clearable>
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>

                    <el-select filterable style="width: 12%;" v-model="filter.operateSpecialUserId" placeholder="运营专员"
                        :collapse-tags="true" clearable>
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>

                    <el-select filterable style="width: 12%;" v-model="filter.userId3" placeholder="备用负责人"
                        :collapse-tags="true" clearable>
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>




                    <!-- <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain><i
                            class="el-icon-plus"></i>&nbsp;创建任务</el-button> -->

                    <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button type="primary"
                        icon="el-icon-share" @command="handleCommand"> 批量操作 <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
                            <el-dropdown-item class="Batcoperation" style="padding: 0 25px"
                                command="a">批量修改</el-dropdown-item>
                            <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>

                    <!-- <div style="box-sizing: border-box; margin-left:6px;">
                        <el-button type="primary">导出</el-button>
                    </div> -->
                </div>

            </div>

        </el-button-group>
        <el-dialog title="批量操作" :visible.sync="bulkEditingshow" width='50%' height='500px' v-dialogDrag
            :close-on-click-modal="false">
            <plcztabs ref="refplcz" :listrows="listrows"></plcztabs>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="bulkEditingshow = false">取 消</el-button>
                    <el-button type="primary" @click="submitplcz">确 定</el-button>
                </div>
            </template>
        </el-dialog>

    </div>
</template>

<script>
import plcztabs from "@/views/operatemanage/pddactualtimedatanew/dialogfile/plcztabs.vue";
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { getList as getshopList } from '@/api/operatemanage/base/shop'
export default {
    name: "Vue2demoPddheard",
    components: { plcztabs },
    props: ['rows'],
    data() {
        return {
            filter: {
                shopCode: ''
            },
            bulkEditingshow: false,
            warehouselist: [],
            fpPhotoLqNameList: [],
            dockingPeopleList: [],
            shopList: [],
            platformList: [],
            searchtime: [],

            grouplist: [],
            directorlist: [],
            listrows: []
        };
    },
    watch: {
        rows: {
            handler(val){
                this.listrows = val;
            },
            immediate: true,
        }
    },

    mounted() {
        this.getheardlist();
        this.getshopname();
    },

    methods: {
        submitplcz(){
            this.$refs.refplcz.submit();
        },
        async getshopname(){
            let res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.filter.shopCode = ''
            this.shopList = res1.data.list
        },
        async getheardlist(){
            let res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

            let res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        clearfifter() {
            this.filter = {};
        },
        async handleCommand(command) {
            if (this.listrows.length == 0 ) {
                this.$message({ type: 'warning', message: "请选择操作行" });
                return;
            }
            switch (command) {
                //批量完成
                case "a":
                    await this.bulkEditing();
                    break;
            }
        },
        bulkEditing() {
            this.bulkEditingshow = true;
        },
        onSearch() {
            this.$emit("search", this.filter);
        },
        onclear() { },
        onExeprotShootingTask() { }
    }
};
</script>

<style lang="scss" scoped>
.allbody {
    // padding: 1px 10px;
}

::v-deep .el-drawer__header {
    border: none !important;
    // padding: 16px 24px 0 24px;
}

::v-deep .el-header {
    padding: 10px 5px 5px 5px !important;
}

.ssanc {
    width: 100%;
    height: 38px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    display: inline-block;
}

.heardcss {
    width: 100%;
    min-width: 1150px;
    min-height: 35px;
    // background-color: aqua;
    box-sizing: border-box;
    display: inline-block;
    margin-top: 8px;
}

.heardcss div,
.ssanc {
    // padding: 4px 0.2% 4px 0;
    display: flex;
    flex-direction: column;
}

::v-deep div .el-radio-button__inner {
    line-height: 14px !important;
}

::v-deep .vxetablecss {
    width: 100%;
    margin-top: -20px !important;
}

// ::v-deep .vxetoolbar20221212 {
//     top: 97px;
//     right: 15px;
// }

::v-deep .el-button-group>.el-button:last-child {
    margin-left: -2px !important;
}

::v-deep .el-button-group {
    margin: 0 !important;
    padding: 0 !important;
}

// ::v-deep .vxe-table--render-default .vxe-cell {
//     padding:0 5px !important;
// }
::v-deep .vxe-header--row {
    height: 58px;
}

::v-deep .el-table__body-wrapper {
    height: 220px !important;
}

::v-deep .el-table__body-wrapper {
    overflow-y: auto;
}

.gddwz {
    width: 100%;
    /* background-color: #F2F6FC; */
    //   border: 1px solid #ff1414;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
    text-align: center;
}

.gddwznr {
    padding-left: 20px;
    font-size: 14px;
    color: #eb0000;
    display: inline-block;
    white-space: nowrap;
    animation: 20s wordsLoop linear infinite normal;
    margin: 0;
}

.flexrow {
    display: flex;
    flex-direction: row;
}

@keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}

@-webkit-keyframes wordsLoop {
    0% {
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }

    100% {
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
}
</style>
