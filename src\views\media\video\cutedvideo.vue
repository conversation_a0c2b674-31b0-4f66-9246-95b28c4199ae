<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                    <el-button style="padding: 0;width: 150px;">
                        <el-input v-model="Filter.taskId" style="padding: 0;width: 130px;" type="number" placeholder="任务编号"  clearable></el-input>
                    </el-button>
                   <!--  <el-button style="padding: 0;width: 135px;">
                        <el-input-number v-model="Filter.taskId" type="number" placeholder="任务编号"  :min="1" :max="100000"
                        @keyup.enter.native="onSearch"
                        :step="1"  :step-strictly="true" controls-position="right" clearable />
                    </el-button> -->
                    <!-- <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.videoTaskName" placeholder="任务名称" @keyup.enter.native="onSearch" clearable />
                    </el-button> -->
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.platform" placeholder="平台" clearable :collapse-tags="true" @change="onchangeplatform" style="width: 80px">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;">
                        <el-select filterable v-model="Filter.shopId" placeholder="店铺" clearable style="width: 130px">
                            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productId" placeholder="产品ID" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.productShortName" placeholder="产品简称" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;width: 155px;">
                        <el-input v-model="Filter.goodCode" placeholder="商品编码" @keyup.enter.native="onSearch" clearable />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.CreatedUserName" placeholder="剪辑人" clearable style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="Filter.Sdate"></datepicker>
                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary"  v-if="checkPermission('recutevideo')" @click="recute">重新剪辑</el-button>
                </el-button-group>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='datalist' @select='selectchange'
        :isSelection='true' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>

            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getVideoList" />
        </template>

        <el-dialog title="视频预览" :visible="dialogVisible" width="50%">
            <span>
                <div class="PlayVideo">
                    <!-- 视频播放 -->
                    <video class="video" controls="" ref="video" id="video" v-on:error.prevent="error($event)">
                    </video>
                    <!-- 信息提示 -->
                    <div class="msg" v-if="isError">
                        <div style="color: #fff">{{errMsg}}</div>
                    </div>
                </div>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="不通过" :visible.sync="dialogNoPassVisible" width="30%">
            <span>
                <el-form ref="noPassForm" :model="noPassForm" label-width="120px">
                    <el-form-item prop="noPassRemark" label="不通过原因：">
                        <el-input v-model="noPassForm.noPassRemark" auto-complete="off" style="width:90%" />
                    </el-form-item>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogNoPassVisible = false">取消</el-button>
                <my-confirm-button type="submit" @click="videoNoPassOK" />
            </span>
        </el-dialog>
    </my-container>

</template>
<script>
    import datepicker from '@/views/customerservice/datepicker'
    import { getCuteVideoList, deleteInquirsBatch, processCutePassStatusAsync,reUpdateCuteVideoAsync ,delTaskCuteVideo} from '@/api/media/video'
    import { rulePlatform } from "@/utils/formruletools";
    import { getList as getshopList } from '@/api/operatemanage/base/shop'

    import cesTable from "@/components/Table/tableforvedio.vue";
    import { formatTime } from "@/utils";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    const tableCols = [
        { istrue: true, prop: 'taskId', label: '任务编号', width: '53', sortable: 'custom' },
        { istrue: true, prop: 'shopId', label: '店铺', width: '200', sortable: 'custom', formatter: (row) => row.shopName || ' ' },
        { istrue: true, prop: 'productId', label: '产品ID', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'productShortName', label: '产品简称', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'goodCode', label: '商品编码', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'imgPath', videoprop: 'videoPath', label: '地址', width: '60', type: 'imgvideo' },
        { istrue: true, prop: 'createdUserName', label: '剪辑人', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'videoIndexName', label: '参考', width: '50' },
        { istrue: true, prop: 'title', label: '段落', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'beginTime', label: '开始时间', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'endTime', label: '结束时间', width: '100', sortable: 'custom' },

        { istrue: true, prop: 'createdTime', label: '剪辑时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'cuteStatus', label: '剪切进度', width: '80', sortable: 'custom' },
        {
            istrue: true, type: "button", width: "180", align: 'center', label: '操作',
            btnList: [
                { label: " 下载", handle: (that, row) => that.downVideo(row) },
                { label: " 删除", handle: (that, row) => that.delideo(row) }
            ]
        }
    ];
    export default {
        name: "uploadvideo",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
        data() {
            return {
                videoUrls: 'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4',
                that: this,
                Filter: {
                    Sdate: [formatTime(new Date(), "YYYY-MM-DD"), formatTime(new Date(), "YYYY-MM-DD")],
                },
                tableCols: tableCols,
                total: 0,
                show: true,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "createdTime", IsAsc: false },
                listLoading: false,
                pageLoading: false,
                fileList: [],
                datalist: [],
                sels: [],
                selids: [],
                dialogVisible: true,
                isError: false, // 是否不能播放视频
                errMsg: "",
                dialogNoPassVisible: false,
                noPassForm: { noPassId: null, noPassRemark: "" },
                platformList: [],//平台下拉
                shopList: []
            };
        },
        async mounted() {

            var that = this;
            this.show = true;
            this.dialogVisible = false;
            await this.setPlatform();//平台下拉
            let video = document.getElementById("video");
            //this.videoUrl = 'http://www.si-tech.com.cn/pub-ui/images/radio/sitech.mp4',
            //    this.$refs.video.src = this.videoUrl
        },
        methods: {
            async recute(){
                console.log(this.selids);
                let res = await reUpdateCuteVideoAsync(this.selids);
            },
            async  delideo(row)
            {
                let res = await  delTaskCuteVideo({cuteId : row.id})
                if (res?.success) {
                    that.$message({ message: '操作成功', type: "success" });
                    that.onRefresh();
                }
            },
            //设置平台下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
            },
            async onchangeplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 10000 });
                this.shopList = res1.data.list;
            },
            async deleteBatch(row) {
                var that = this;
                this.$confirm("此操作将删除此批次客服人员咨询数据数据?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                .then(async () => {
                    await deleteInquirsBatch({ batchNumber: row.batchNumber })
                    that.$message({ message: '已删除', type: "success" });
                    that.onRefresh()

                });
            },
            async downVideo(row) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', row.videoPath, true);
                xhr.responseType = 'arraybuffer'; // 返回类型blob
                xhr.onload = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        let blob = this.response;
                        console.log(blob);
                        // 转换一个blob链接
                        // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                        // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                        let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                        // 视频的type是video/mp4，图片是image/jpeg
                        // 01.创建a标签
                        let a = document.createElement('a');
                        // 02.给a标签的属性download设定名称
                        a.download = "";
                        // 03.设置下载的文件名
                        a.href = downLoadUrl;
                        // 04.对a标签做一个隐藏处理
                        a.style.display = 'none';
                        // 05.向文档中添加a标签
                        document.body.appendChild(a);
                        // 06.启动点击事件
                        a.click();
                        // 07.下载完毕删除此标签
                        a.remove();
                    };
                    };
                xhr.send();
                //window.open("http://192.168.90.12:8001/media/video/20221018/1582185265700892672.mp4");
                /* var res = await downVideoAsync({ id: row.id, type: 2 });
                if(res?.data?.type  =='application/json') {
                    return;
                }
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "video/mp4" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', row.seriId + "_" + row.title + "_" + new Date().toLocaleString() + row.fileExt)
                aLink.click();  */
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {

                this.$refs.pager.setPage(1);
                this.getVideoList();
            },

            async getVideoList() {

                if (this.Filter.Sdate) {
                    this.Filter.startSdate = this.Filter.Sdate[0];
                    this.Filter.endSdate = this.Filter.Sdate[1];
                }
                else {
                    this.Filter.startSdate = null;
                    this.Filter.endSdate = null;
                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,

                };

                this.listLoading = true;
                const res = await getCuteVideoList(params);
                this.listLoading = false;
                this.total = res.data.total
                this.datalist = res.data.list;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            showVideo(row) {
                var that = this;
                that.dialogVisible = true;
                this.videoUrl = row.videoPath,
                    this.$refs.video.src = this.videoUrl
                let video = document.getElementById("video");
            },
            close() {
                this.show = false;
                this.$emit("close");
            },
            error(e) {
                console.log(e);
                if (this.videoUrl == '') {
                    this.isError = true
                    this.errMsg = "暂无视频！"
                } else {
                    this.isError = true
                    this.errMsg = "视频链接失效！无法播放！"
                }
            },
            checkCuteStatus(cuteStatus) {
                if (cuteStatus != "已剪切") {
                    return "非已剪切的数据无法操作";
                }
                return "";
            },
            isPassBtnShow(row) {
                if (this.checkCuteStatus(row.cuteStatus) != "")
                    return true;
                if (row.status != "待审核")
                    return true;
                //判断是否隐藏，false为不隐藏
                return false;
            },
            isNoPassBtnShow(row) {
                if (this.checkCuteStatus(row.cuteStatus) != "")
                    return true;
                if (row.status != "待审核")
                    return true;
                //判断是否隐藏，false为不隐藏
                return false;
            },
            videoPass(row) {
                var that = this;
                this.$confirm("确定要执行【通过】操作吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning", }).then(
                    async () => {
                        var res = await processCutePassStatusAsync({ cuteId: row.id, statusEnum: 3, statusRemark: "" });
                        if (res?.success) {
                            that.$message({ message: '操作成功', type: "success" });
                            that.onRefresh();
                        }
                    });
            },
            videoNoPass(row) {
                this.noPassForm.noPassId = row.id;
                this.noPassForm.noPassRemark = "";
                this.dialogNoPassVisible = true;
            },
            async videoNoPassOK() {
                var res = await processCutePassStatusAsync({ cuteId: this.noPassForm.noPassId, statusEnum: 2, statusRemark: this.noPassForm.noPassRemark });
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.dialogNoPassVisible = false;
                    this.onRefresh();
                }
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>

<style lang='scss' scoped>
    .PlayVideo {
        width: 45.3125vw;
        height: 50.1vh;
        position: relative;
        // z-index: 99999;
        // background: rgba(0, 0, 0, 0.5);
        .close {
            position: absolute;
            top: -32px;
            right: -32px;
            z-index: 9999;
        }
        .video {
            width: 45.3125vw;
            height: 50.1vh;
            background: #000;
            // position: absolute;
            // top: 50%;
            // left: 50%;
            // transform: translate(-50%,-50%);
            // z-index: 100000;
        }
        .msg {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
    }
</style>
