<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' tablekey="computeresult"
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="true"
              :loading="listLoading">
          <template slot='extentbtn'>
            <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                <el-select
                  v-model="filter.platform"
                  placeholder="平台" style="width: 100px"  @change="changePlatform"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in platformList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                <el-select
                  v-model="filter.shopCode" @change="onSearch"
                  placeholder="店铺" style="width: 250px"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in shopList"
                    :key="item.shopCode"
                    :label="item.shopName"
                    :value="item.shopCode"/>
                </el-select>
              </el-button>      
              <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.Version" clearable placeholder="类型" style="width: 100px">
                <el-option label="工资月报" value="v1"></el-option>
                <el-option label="参考月报" value="v2"></el-option>
            </el-select>
            </el-button>       
              <el-button style="padding: 0;margin: 0;"><el-input style="width: 200px" v-model="filter.proCode"  placeholder="产品ID" @change="onSearch"/></el-button>          
            </el-button-group>
        </template>
       </ces-table> 
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {
  pageOrderWithholdCostShare,
} from '@/api/financial/ordercost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatPlatform,formatTime} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'setMonth',label:'结算月份', width:'120',sortable:'custom',formatter:(row)=>formatTime(row.registDate,'YYYY-MM-DD')},
      {istrue:true,prop:'platform',label:'平台', width:'100',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'shopCode',label:'店铺', width:'250',sortable:'custom',formatter:(row)=>row.shopName},
      {istrue:true,prop:'proCode',label:'产品ID', width:'150',sortable:'custom'},
      {istrue:true,prop:'proCount',label:'产品数量', width:'120',sortable:'custom'},
      {istrue:true,prop:'withholdfee',label:'扣款费用', width:'120',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'计算时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
     ];
const tableHandles=[
        {label:"导出", handle:(that)=>that.onExport()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container },
   props:{
       filter: { },
       shopList:[],
       platformList:[],
     },
  data() {
    return {
      shareFeeType:0,
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      if(!this.filter.settMonth){
        this.$message({message: "请选择结算月份", type: "warning" });
        return false;
      }
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      const params = {...pager, ...this.pager, ... this.filter}
      this.listLoading = true
      const res = await pageOrderWithholdCostShare(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    changePlatform(){
      this.$emit("changePlatform",this.filter.platform);
      this.onSearch();
    },
    onExport(){
      this.$emit("onExport");
    }
  }
}
</script>
