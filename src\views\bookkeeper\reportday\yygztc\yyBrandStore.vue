<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.brandName" placeholder="品牌" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.shopName" placeholder="品牌店铺" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
        <el-button type="success" @click="importProps">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'brandStores202506121011'" :tablekey="'brandStores202506121011'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" @click="onDelete(row)"><span style="color: red;">删除</span></el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="ruleTitle" :visible.sync="editDialogVisible" width="20%" v-dialogDrag>
      <div style="padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
          v-if="editDialogVisible" v-loading="editloading">
          <el-row :gutter="20">
            <el-form-item label="品牌" :label-width="'125px'" prop="brandName">
              <el-input v-model.trim="ruleForm.brandName" placeholder="请输入品牌" maxlength="50" clearable
                class="editCss" />
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="品牌店铺" :label-width="'125px'" prop="shopCode">
              <el-select v-model="ruleForm.shopCode" placeholder="请选择品牌店铺" clearable filterable class="editCss">
                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                  :value="item.shopCode" />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="生效开始日期" :label-width="'125px'" prop="enableStartDate">
              <el-date-picker class="editCss" v-model="ruleForm.enableStartDate" type="date" placeholder="请选择生效开始日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="生效结束日期" :label-width="'125px'" prop="enableEndDate">
              <el-date-picker class="editCss" v-model="ruleForm.enableEndDate" type="date" placeholder="请选择生效截止日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSingleSave">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import { downloadLink } from "@/utils/tools.js";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { pickerOptions } from '@/utils/tools'
import { importyyBrandShopAsync, getyyBrandShopList, addorEdityyBrandShop, deleteyyBrandShopAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '300', align: 'center', prop: 'brandName', label: '品牌', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '品牌店铺', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'enableStartDate', label: '生效开始日期', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'enableEndDate', label: '生效结束日期', },
]
export default {
  name: "yybrandStores",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      editloading: false,
      shopList: [],
      ruleTitle: '新增',
      editDialogVisible: false,
      ruleForm: {
        shopCode: '',
        shopName: '',
        brandName: '',
        enableStartDate:'',
        enableEndDate:'',
      },
      editrules: {
        shopCode: [{ required: true, message: '品牌店铺不能为空', trigger: 'change' }],
        brandName: [{ required: true, message: '品牌不能为空', trigger: 'change' }],
        enableStartDate: [{ required: true, message: '生效起始日期不能为空', trigger: 'change' }],
        enableEndDate: [{ required: true, message: '生效结束日期不能为空', trigger: 'change' }],
      },
      importVisible: false,
      fileList: [],
      file: null,
      fileparm: {},
      uploadLoading: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        shopName: '',
        brandName: '',
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    async init() {
      const res = await getshopList({ CurrentPage: 1, PageSize: 1000000 });
      this.shopList = res.data.list
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteyyBrandShopAsync({ id: row.id, FeeType: 1 })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const shop = this.shopList.find(item => item.shopCode == this.ruleForm.shopCode)
          this.ruleForm.shopName = shop.shopName
          console.log(this.ruleForm)
          this.editloading = true
          const { success } = await addorEdityyBrandShop(this.ruleForm)
          this.editloading = false
          if (success) {
            this.$message.success('操作成功')
            this.editDialogVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.$refs.ruleForm.clearValidate();
      });
      this.ruleForm = {
        shopCode: '',
        shopName: '',
        brandName: '',
        enableStartDate:'',
        enableEndDate:'',
      }
    },
    async onEdit(row) {
      this.onCleardataMethod()
      setTimeout(() => {
        this.ruleForm = JSON.parse(JSON.stringify(row))
        this.ruleTitle = '编辑'
        this.editDialogVisible = true
      }, 100)
    },
    onAddMethod() {
      this.editDialogVisible = true
      this.onCleardataMethod()
      this.ruleTitle = '新增'
    },
    //导入弹窗
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.importVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      const res = await importyyBrandShopAsync(form)
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.importVisible = false;
    },
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250217/1891426875750449152.xlsx', '品牌店铺导入模版.xlsx');
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getyyBrandShopList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.enableEndDate = item.enableEndDate ? dayjs(item.enableEndDate).format('YYYY-MM-DD') : ''
          item.enableStartDate = item.enableStartDate ? dayjs(item.enableStartDate).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 220px;
    margin-right: 5px;
  }
}

.editCss {
  width: 90%;
  margin-right: 5px;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
