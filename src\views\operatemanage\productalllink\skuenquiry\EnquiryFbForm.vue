<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form ref="form" :model="form" :rules="formRules" label-width="110px" label-position="right" :disabled="!formEditMode">
                <el-row >  
                    <el-col :span="10">
                        <el-form-item label="竞品ID：">
                            {{form.goodsCompeteId}}
                        </el-form-item>
                    </el-col>    
                    <el-col :span="14">
                        <el-form-item label="竞品标题：">
                            {{form.goodsCompeteName}}
                        </el-form-item>   
                    </el-col>  
                </el-row>                
             
                <el-row >  
                    <el-col :span="10">
                        <el-form-item label="供应商平台：" prop="supplierPlatformName">
                            <template v-if="mode==3">
                                {{form.supplierPlatformName}}
                            </template>
                            <el-select v-else v-model="form.supplierPlatformName">
                                <el-option value="1688" label="1688"></el-option>
                                <el-option value="微信" label="微信"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>    
                    <el-col :span="14">
                        <el-form-item label="供应商名称：" prop="supplierName">
                            <template v-if="mode==3">
                                {{form.supplierName}}
                            </template>
                            <el-input v-else v-model.trim="form.supplierName" :minlength="2" :maxlength="30"></el-input>
                        </el-form-item>
                    </el-col>  
                </el-row>
                <el-row >  
                    <el-col :span="10">
                        <el-form-item label="报价凭证：" prop="voucherImgUrls">
                            <yh-img-upload :value.sync="form.voucherImgUrls" :limit="5" ></yh-img-upload>
                        </el-form-item>
                    </el-col>    
                    <el-col :span="14">
                        <el-form-item label="产品链接：" v-if="form.supplierPlatformName=='1688'" prop="factoryUrl">
                            <template v-if="mode==3">
                                {{form.factoryUrl}}
                            </template>
                            <el-input v-else v-model.trim="form.factoryUrl" :minlength="2" :maxlength="200"></el-input>
                        </el-form-item>
                        <el-form-item label="微信账号：" v-if="form.supplierPlatformName=='微信'" prop="supplierWxNum">
                            <template v-if="mode==3">
                                {{form.supplierWxNum}}
                            </template>
                            <el-input v-else v-model.trim="form.supplierWxNum" :minlength="0" :maxlength="25"></el-input>
                        </el-form-item>
                    </el-col>  
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：">
                            <template v-if="mode==3">
                                {{form.fbRemark}}
                            </template>
                            <el-input v-else type="textarea"
                            :rows="2" v-model.trim="form.fbRemark"  :maxlength="200"  show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                
                
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="询价明细">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="skuTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='skuTableData' 
                                    :tableCols='skuTableCols' :loading="false" :isSelectColumn="false"
                                     rowkey="id" >
                                        <!-- center from jsonCols array  -->
                                        <template slot="right">
                                            <!-- right  -->                                           
                                            <el-table-column width="100" label="报价" prop="fbCostPrice" >
                                                <template slot-scope="scope">
                                                    <template v-if="mode==3">
                                                        {{scope.row.fbCostPrice}}
                                                    </template>
                                                    <el-input-number v-else v-model.number="scope.row.fbCostPrice" 
                                                        style="width:80px" type="number" 
                                                        :min="0" :max="10000" :precision="3" :controls="false"  size="mini"                                                       
                                                    />      
                                                </template>
                                            </el-table-column> 
                                        </template>
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>
                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button type="primary" @click="onSave(true)">提交报价&关闭</el-button>                    
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    
    import { EnquiryLaunchAsync,GetEnquiryInfoAndSelfFb,EnquiryFeedbackAsync    } from '@/api/operatemanage/productalllink/alllink';
      
    import YhImgUpload from "@/components/upload/yh-img-upload.vue";

    const skuTableCols = [
        { istrue: true, prop: 'skuImgUrl', label: 'SKU图', width: '64', type: 'image' },        
        { istrue: true, prop: 'skuName', label: '规格名称', minwidth: '180'},//, width: '120'
        { istrue: true, prop: 'hopeCostPrice', label: '期望成本价', width: '90' },   
        { istrue: true, prop: 'estimatedQuantity', label: '每次大概进货量', width: '120' },   
    ];


    export default {
        name: "EnquiryFbForm",
        components: { MyContainer, MyConfirmButton, cesTable,YhImgUpload    },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                skuTableCols: skuTableCols,
                total: 0,
                skuTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,
                goodschoiceVisible:false,
             
            };
        },
        async mounted() {

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.skuTableData && this.skuTableData.length > 0) {
                    rowsCount = this.skuTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },  
            formRules(){
                return {
                    "supplierPlatformName":[  { required: true, message: '请选择供应商平台', trigger: 'change' }],
                    "supplierName":[  { required: true, message: '请填写供应商名称', trigger: 'blur' }],
                    "voucherImgUrls":[  { required: true, message: ' ', trigger: 'blur' }],
                    "factoryUrl":[  { required: this.form.supplierPlatformName=='1688', message: '请填写产品链接', trigger: 'blur' }],
                    "supplierWxNum":[  { required: this.form.supplierPlatformName=='微信', message: '请填写微信账号', trigger: 'blur' }],
                }  
            }
        },
        methods: {  
          
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({chooseId,mode}) {                              
                this.mode=mode;
                this.formEditMode = mode!=3;

                this.pageLoading = true;

                let rlt=await GetEnquiryInfoAndSelfFb({chooseId:chooseId});
                if(rlt && rlt.success){
                    this.form={...rlt.data};
                    this.skuTableData=[...rlt.data.fbList];    
                }
                
                this.pageLoading = false;
            },
            async save() {
                let self=this;
              

                try{
                    await self.$refs["form"].validate((v)=>{return v});
                }catch(ex){   
                }

                this.pageLoading = true;
                
                let errMsg='';
                if( !self.form.supplierName ){
                    errMsg='请填写供应商名称!';
                    self.$alert(errMsg);   
                    self.pageLoading = false; 
                    return false;
                }
                if( !self.form.supplierPlatformName ){
                    errMsg='请选择供应商平台!';
                    self.$alert(errMsg);   
                    self.pageLoading = false; 
                    return false;
                }
                if( self.form.supplierPlatformName && self.form.supplierPlatformName=="1688" && !self.form.factoryUrl ){
                    errMsg='请填写产品链接!';
                    self.$alert(errMsg);   
                    self.pageLoading = false; 
                    return false;
                }else if(self.form.supplierPlatformName && self.form.supplierPlatformName=="微信" && !self.form.supplierWxNum){
                     errMsg='请填写微信账号!';
                    self.$alert(errMsg);   
                    self.pageLoading = false; 
                    return false;
                }

                if( !self.form.voucherImgUrls || self.form.voucherImgUrls.length<2 ){
                    errMsg='请上传报价凭证!';
                    self.$alert(errMsg);   
                    self.pageLoading = false; 
                    return false;
                }

                if(this.skuTableData&& this.skuTableData.length>0){                
                  
                    this.skuTableData.forEach(element => {
                        // hopeCostPrice  estimatedQuantity
                        if(!(element.fbCostPrice && element.fbCostPrice>0)){
                            errMsg='请填写有效的报价金额，0.01~10000间！';                            
                        }                      
                    });
                    
                }else{
                    errMsg="没有可报价的SKU！"    ;               
                }

                if(errMsg){
                    this.$alert(errMsg);   
                    this.pageLoading = false; 
                    return false;
                }

                    console.log(self.form);

                let saveData = { 
                    chooseId:self.form.chooseId,
                    supplierName:self.form.supplierName,
                    supplierPlatformName:self.form.supplierPlatformName,
                    supplierWxNum:self.form.supplierWxNum,
                    factoryUrl:self.form.factoryUrl,
                    voucherImgUrls:self.form.voucherImgUrls,
                    fbRemark:self.form.fbRemark,
                    skuList:self.skuTableData.map(sku=>{
                        return {
                            skuId:sku.skuId,
                            fbCostPrice:sku.fbCostPrice                            
                        }
                    })
                };              

                let rlt = await EnquiryFeedbackAsync(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('报价成功！');  
                }
                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
