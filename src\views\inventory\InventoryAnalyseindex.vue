<template>
    <my-container>
        <template #header>
            <el-form :inline="true" :model="filter" @submit.native.prevent>
            <el-form-item label="采购员:">
                <el-select v-model="filter.brandId" multiple collapse-tags clearable filterable placeholder="请选择采购员" style="width: 140px">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
            </el-form-item>
            <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="true" :loading="listLoading" @cellclick="cellclick">
            <template slot='extentbtn'>
                <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-radio-group v-model="isTrue" @change="onSearch()">
                            <el-radio-button label="1">全部</el-radio-button>
                            <el-radio-button label="true">在维护</el-radio-button>
                            <el-radio-button label="false">其他</el-radio-button>
                    </el-radio-group>
                </el-button> 
                </el-button-group>
            </template>
        </ces-table>

        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <template>
                    <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
                        <el-row>
                            <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                                <el-form-item label="日期:">
                                    <el-date-picker style="width: 260px"
                                        v-model="filter.timerange"
                                        type="daterange"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :clearable="false"
                                    ></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                                <el-form-item>
                                    <el-button type="primary" @click="getechart">刷新</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
            </template> 
            </span>
            <span>
                <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import buschar from '@/components/Bus/buschar'
import { formatPlatform, formatNoLink, formatmoney} from "@/utils/tools";
import {getAllProBrand} from '@/api/inventory/warehouse'
import { getInventoryAnalyseList, getInventoryAnalyse } from '@/api/inventory/abnormal'

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
  var money= formatmoney(
    Math.abs(value) > 1 ? Math.round(value,2) : Math.round(value, 1)
   );
  return money
};

const tableCols =[
        {istrue:true,prop:'brandId',label:'品牌', width:'100',sortable:'custom',formatter:(row)=> !row.brandName? "" : row.brandName},
        {istrue:true,prop:'goodsCodeCount',label:'编码计数', width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.goodsCodeCount)}, 
        {istrue:true,prop:'styleCodeCount',label:'系列编码数', width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.styleCodeCount)},
        {istrue:true,prop:'styleCodeMean',label:'系列编码均值', width:'110',sortable:'custom',formatter:(row)=>  row.styleCodeMean.toFixed(2)},       
        {istrue:true,prop:'goodsNum',label:'商品总数量', width:'110',sortable:'custom',tipmesg:'商品总数量（含在途）',formatter:(row)=>myformatmoney(row.goodsNum)},  
        {istrue:true,prop:'salesMonth',label:'月销量', width:'100',sortable:'custom',formatter:(row)=>myformatmoney(row.salesMonth) },     
        {istrue:true,prop:'turnoverDaysNum',label:'销量周转天数', width:'125',sortable:'custom',tipmesg:'销售总数量/月销售数（合算对应天数）',formatter:(row)=>  row.turnoverDaysNum.toFixed(2)},
        {istrue:true,prop:'inAmont',label:'库存金额',sortable:'custom', width:'100',tipmesg:'库存金额（含在途）',formatter:(row)=>myformatmoney(row.inAmont)}, 
        {istrue:true,prop:'onAmont',label:'在途金额',sortable:'custom', width:'100',tipmesg:'在途金额',formatter:(row)=>myformatmoney(row.onAmont)}, 
        {istrue:true,prop:'salesMonthAmont',label:'月销金额', width:'100',sortable:'custom',tipmesg:'月销金额（成本）',formatter:(row)=>myformatmoney(row.salesMonthAmont)},
        {istrue:true,prop:'turnoverDaysAmont',label:'销售额周转天数', width:'140',sortable:'custom',tipmesg:'月销金额/库存金额（合算对应天数）',formatter:(row)=>  row.turnoverDaysAmont.toFixed(2)},
        {istrue:true,prop:'brandId',label:'趋势图',style:"color:red;cursor:pointer;",width:'auto',formatter:(row)=>'趋势图',type:'click',handle:(that,row)=>that.showprchart(row)},
]

const tableHandles=[];
export default {
    name: 'YunhanAdminInventoryAnalyseindex',
    components: {MyContainer, MySearch, cesTable, MySearchWindow, MyConfirmButton, buschar},

    data() {
        return {
            that:this,
            filter: {
                startTime:null,
                endTime:null,
                isHome:true,
                isTrue:false,
                brandId:null,
                occurrenceTime:null,
                timerange:[formatTime(dayjs().subtract(7,"day"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
            },
            isTrue:1,
            filterdetail: {
                brandId:null,
                brandName:null,
            },
            
            list:[],
            summaryarry:{},
            pager:{OrderBy:"GoodsCodeCount",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles,
            total: 0,
            sels: [], 
            brandlist:[],
            listLoading: false,
            pageLoading: false,
            buscharDialog:{visible:false,title:"",data:[]},
        };
    },

    async mounted() {
        await this.onSearch()
        await this.init()
    },

    methods: {
        async init(){     
        var res= await getAllProBrand();
        this.brandlist = res.data.map(item => {
          return { value: item.key, label: item.value };
        });

        },
        async onSearch(){
            await this.getlist();
        },
        async getlist() {
            var params=this.getCondition();
            if(params===false){
                    return;
            }
            this.listLoading = true
            const res = await getInventoryAnalyseList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = 28;
            const data = res.data.list;
            this.summaryarry=res.data.summary;
            // if(this.summaryarry)
            //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
            // data.forEach(d => {
            //     d._loading = false
            // })
            this.list = data
       },
       getCondition(){
            
            //this.filter.platform=2
            const params = {
                ... this.filter,
                ... this.pager
            }
            params.isHome = false
            console.log('来了',this.isTrue)
            if (this.isTrue == '1'){
                params.isHome = false,
                params.isTrue = false
            }
            else if (this.isTrue == 'true'){
                params.isHome = true,
                params.isTrue = true
            }
            else if (this.isTrue == 'false'){
                params.isHome = true,
                params.isTrue = false
            }
            params.brandId = params.brandId.join()
            console.log('参数输出',params)

            return params;
        },
        async getechart(){
            var para = {brandId:this.brandId}
            await this.showprchart(this.filterdetail)
        },
        async showprchart(row){
             this.filter.startTime = null
             this.filter.endTime =  null
            if (this.filter.timerange&&this.filter.timerange.length>1) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            else {
                this.$message({message:"请先选择日期",type:"warning"});
                return false;
            }
            this.filterdetail.brandId = row.brandId
            this.filterdetail.brandName = row.brandName
            const params = {...this.filter};
            params.brandId = row.brandId
            let that=this;
            const res = await getInventoryAnalyse(params).then(res=>{
            that.buscharDialog.visible=true;
            that.buscharDialog.data=res.data
            that.buscharDialog.title=row.brandName
            });
            await this.$refs.buschar.initcharts()
        },
        cellclick(row, column, cell, event){
            
        }, 
        async sortchange(column) {
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                OrderBy: column.prop,
                IsAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            }
            await this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>

</style>