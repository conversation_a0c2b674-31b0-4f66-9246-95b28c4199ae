<template>
    <my-container v-loading="pageLoading">
        <el-row>
            <el-col :span="10">
                <el-select style="width:280px" v-model="Filter.platform" placeholder="平台" multiple clearable filterable :collapse-tags="false">
                    <el-option v-for="(item ) in platformlist" :key="'platform-'+item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-date-picker style="width:220px;margin-left:10px;" v-model="Filter.gDate" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions">
                </el-date-picker>
                <el-button type="primary" @click="loadData">查询</el-button>
            </el-col>
            <el-col :span="14">
                <el-button-group>

                    <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="float:left;margin-left:30px;">全选</el-checkbox>

                    <el-checkbox-group v-model="checkeds" @change="handleCheckedChange" style="float:left;margin-left:20px;">
                        <el-checkbox v-for="v in checkOpts" :label="v" :key="v">{{v}}</el-checkbox>
                    </el-checkbox-group>

                </el-button-group>
            </el-col>
        </el-row>
        <el-row style="width:100%;">
            <el-col :span="24" style="width:100%;margin-top:20px;">
                <div style="min-width:1024px;height:320px;width:100%;" :ref="'echarts1'" :id="'rptIdecharts1'" v-loading="echartsLoading1"></div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24"  style="width:100%;margin-top:20px;">
                <div style="min-width:1024px;height:320px;width:100%;" :ref="'echarts2'" :id="'rptIdecharts2'" v-loading="echartsLoading2"></div>
            </el-col>
        </el-row>


        <el-dialog :title="echartsDtlTitle" :visible.sync="echartsDtlVisible" :loading="echartsDtlLoading" width="80%" v-dialogDrag>
            <div style="width:100%;text-align:right">
                <el-button type="primary"  @click="onDtlExport">导出</el-button>
            </div>
            <div style="min-width:900px;width:100%;height:600px;" :ref="'echartsDtl'"></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="echartsDtlVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>

    import * as echarts from 'echarts'
    import MyContainer from "@/components/my-container";
    import {
        platformlist, pickerOptions, formatTime
    } from "@/utils/tools";
    import dayjs from "dayjs";
    import { GetChooseSummaryCharts,exportChooseSummaryChartsData } from '@/api/operatemanage/productalllink/alllink'

    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const checkOptions = ['已选品', '已计算利润', '已确认', '已采样', '已建编码', '已归档'];

    const constEchartOpts = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        toolbox: {
            feature: {
                dataView: { show: true, readOnly: false },
                magicType: { show: true, type: ['line', 'bar'] },
                restore: { show: false },
                saveAsImage: { show: true }
            }
        },
        yAxis: {
            type: 'value'
        },
    }

    export default {
        name: "ChooseSummaryCharts",
        components: { MyContainer },
        data() {
            return {
                platformlist: platformlist,
                pickerOptions: pickerOptions,
                Filter: {
                    platform: [],
                    startTime: '',
                    endTime: '',
                    gDate: [startTime, endTime]
                },
                that: this,
                pageLoading: false,
                checkOpts: checkOptions,
                checkeds: ['已选品'],
                isIndeterminate: true,
                checkAll: false,
                echartsLoading1: true,
                echartsLoading2: true,
                constEchartOpts: constEchartOpts,
                chart1Opt: {},
                myChart1:{},
                chart2Opt: {},
                myChart2:{},
                echartsDtlVisible:false,
                echartsDtlTitle:'',
                echartsDtlLoading:true,
                dtlFilter:{}
            };
        },
        mounted(){
            this.loadData();
        },
        methods: {
            async onDtlExport(){

                let rlt = await exportChooseSummaryChartsData({ ...this.dtlFilter });
             
                if (rlt && rlt.data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([rlt.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '选品统计报表_明细_' + new Date().toLocaleString() + '_.xlsx')
                    aLink.click()
                }
            },
            resize(){
                let self=this;
                if(self.myChart1 && self.myChart1.resize)
                    self.myChart1.resize();

                if(self.myChart2 && self.myChart2.resize)
                    self.myChart2.resize();
                
            },
            async loadData() {
                let self = this;
                this.echartsLoading1 = true;
                this.echartsLoading2 = true;

                if (this.Filter.gDate) {
                    this.Filter.startTime = this.Filter.gDate[0];
                    this.Filter.endTime = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startTime = null;
                    this.Filter.endTime = null;
                }

                let rlt = await GetChooseSummaryCharts({ ...this.Filter,checks:this.checkeds });
                if (rlt && rlt.success) {
                    let opt1 = { ...constEchartOpts, ...rlt.data.option1 };

                    //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                    let myChart = echarts.getInstanceByDom(
                        this.$refs['echarts1']
                    );
                    if (myChart == null) {
                        myChart = echarts.init(this.$refs['echarts1']);

                        myChart.on('legendselectchanged',function(v){
                           
                            self.chart1legendselectchanged(v.name);
                        });

                        myChart.on('selectchanged',function(v){                           
                            self.groupDtlReload(self.chart1Opt.xAxis[0].data[v.fromActionPayload.dataIndexInside])
                        });

                        window.addEventListener("resize", () => {
                            myChart.resize();
                        });
                    }

                    self.myChart1=myChart;

                    //this.checkOpts = opt1.legend.data;

                    opt1.legend.selected = {};
                    this.checkOpts.forEach(c => {
                        opt1.legend.selected[c] = !!(self.checkeds.find(f => f == c));
                    });

                    this.chart1Opt = opt1;

                    myChart.setOption(this.chart1Opt,true);

                    let opt2 = { ...constEchartOpts, ...rlt.data.option2 };
                    this.loadChart2(opt2);
                }

                this.echartsLoading1 = false;
                this.echartsLoading2 = false;

            },
            async chart2Reload(){
                let self = this;
                this.echartsLoading2 = true;

                if (this.Filter.gDate) {
                    this.Filter.startTime = this.Filter.gDate[0];
                    this.Filter.endTime = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startTime = null;
                    this.Filter.endTime = null;
                }

                let rlt = await GetChooseSummaryCharts({ ...this.Filter,checks:this.checkeds });
                if (rlt && rlt.success){
                    let opt2 = { ...constEchartOpts, ...rlt.data.option2 };
                    self.loadChart2(opt2);

                    this.echartsLoading2 = false;
                }
            },
            async groupDtlReload(groupName){

                if(!groupName)
                    return;

              

                let self = this;    
                if (this.Filter.gDate) {
                    this.Filter.startTime = this.Filter.gDate[0];
                    this.Filter.endTime = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startTime = null;
                    this.Filter.endTime = null;
                }
                this.dtlFilter={ ...this.Filter,groupName:groupName };
                let rlt = await GetChooseSummaryCharts({ ...this.dtlFilter });
                if (rlt && rlt.success){
                    let opt = { ...constEchartOpts, ...rlt.data.option1 };
                    
                    self.echartsDtlTitle=groupName +"明细";
                    self.echartsDtlVisible=true;
                 
                    self.echartsDtlLoading=true;
                    
                    
                    self.$nextTick(()=>{
                        
                        //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                        let myChart = echarts.getInstanceByDom(
                            this.$refs['echartsDtl']
                        );
                        if (myChart == null) {
                            myChart = echarts.init(this.$refs['echartsDtl']);    
                            window.addEventListener("resize", () => {
                                myChart.resize();
                            });                                            
                        }
                        myChart.setOption(opt,true);
                        self.echartsDtlLoading=false;      
                        
                        _.delay(function(){
                            myChart.resize()
                        },300);
                    })

                }
            },
            handleCheckAllChange(val) {
                this.checkeds = val ? [...checkOptions] : [];
                this.isIndeterminate = false;
                this.chart1legendChange();

                this.chart2Reload();
            },
            handleCheckedChange(value) {
                let checkedCount = value.length;
                this.checkAll = checkedCount === this.checkOpts.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkOpts.length;
                this.chart1legendChange();

                this.chart2Reload();
            },
            chart1legendChange() {
                let self = this;
                self.checkOpts.forEach(c => {
                    self.chart1Opt.legend.selected[c] = !!(self.checkeds.find(f => f == c));
                });

                let myChart = echarts.getInstanceByDom(
                    this.$refs['echarts1']
                );
                if (myChart)
                    myChart.setOption(this.chart1Opt,true);
            },
            chart1legendselectchanged(v){
                  
                let self = this;
                self.chart1Opt.legend.selected[v]=!self.chart1Opt.legend.selected[v];
                let cIndex=self.checkeds.findIndex(c=>c==v);
                if(self.chart1Opt.legend.selected[v]){                   
                    if(cIndex==-1)
                        self.checkeds.push(v)                    
                }else{
                    if(cIndex>=0)
                        self.checkeds.splice(cIndex,1);
                }

                self.handleCheckedChange(self.checkeds);
            },
            loadChart2(opt){
                
                //console.log(JSON.stringify( opt));
                
                let self=this;
                //检测是否已经存在echarts实例，如果不存在，则不再去初始化                
                let myChart = echarts.getInstanceByDom(
                    this.$refs['echarts2']
                );
                if (myChart == null) {
                    myChart = echarts.init(this.$refs['echarts2']);

                    window.addEventListener("resize", () => {
                        myChart.resize();
                    });
                }                

                self.chart2Opt=opt;
                myChart.setOption(opt,true);

                self.myChart2=myChart;
            }
        }
    }

</script>