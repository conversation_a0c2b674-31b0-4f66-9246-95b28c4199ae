<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="厂家名称：">
                            <el-input v-model.trim="form.name" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="代拍平台：">
                            <el-select v-model="form.shopPlatform">
                                <el-option v-for="item in platformOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="平台店铺ID：">
                            <el-input v-model.trim="form.code" maxlength="20" show-word-limit />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="店铺网址：">
                            <el-input v-model.trim="form.shopWebUrl" maxlength="150" show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="省市区/县：">
                            <yh-cityselector :Address="[form.province,form.city,form.county]" @change="(v)=>{form.province=v[0];form.city=v[1];form.county=v[2];}" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="详细地址：">
                            <el-input v-model="form.doorplate" maxlength="50" show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="完整地址：">
                            <span>{{calcAddress}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：">
                            <el-input type="textarea" v-model="form.remark" clearable maxlength="200" show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <!-- <el-tab-pane label="联系人">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <ces-table ref="contactTable" :that='that' :isIndex='false' :hasexpandRight='true' :hasexpand='true' :tableData='contactTableData' :tableCols='contactTableCols' :loading="false" :isSelectColumn="false">
                                        <template slot="right">
                                            <el-table-column width="130" label="职位/关系" prop="position" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.position" style="width:100%" size="mini" clearable maxlength="8" />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="110" label="联系人" prop="contactName" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.contactName" style="width:100%" size="mini" clearable maxlength="8" />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="200" label="联系电话" prop="mobile" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.mobile" style="width:100%" size="mini" clearable maxlength="25" />
                                                </template>
                                            </el-table-column>
                                            <el-table-column min-width="160" label="备注" prop="remark" :sortable="false">
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row.remark" style="width:100%" size="mini" clearable maxlength="200" show-word-limit />
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="110" label="">
                                                <template slot="header">
                                                    <el-button type="primary" @click="contactTableData.push({id:0})">添加一行</el-button>
                                                </template>
                                                <template slot-scope="scope">
                                                    <el-button type="text" @click="contactTableData.splice(scope.$index, 1)">移除</el-button>
                                                </template>

                                            </el-table-column>

                                        </template>
                                    </ces-table>
                                </div>
                            </el-tab-pane> -->

                            <el-tab-pane label="商品">
                                <el-form>
                                    
                                <div :style="'height:'+ goodsTableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="goodsTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='goodsTableData' :tableCols='goodsTableCols' :loading="false" :isSelectColumn="false"
                                    rowkey="id" :treeprops="{children: 'skuList', hasChildren: true}"
                                    >
                                        <!-- center from jsonCols array  -->
                                        <template slot="right">   
                                            <el-table-column width="110" label="">
                                                <template slot="header">
                                                    <el-button type="primary" @click="onOpenAddGoods">新增</el-button>
                                                </template>
                                                <template  slot-scope="scope" v-if="scope.row.skuOrGoods=='商品'">
                                                    <el-button type="text"  @click="onOpenEditGoods(scope.row.id,2)">编辑</el-button>
                                                    <!-- <el-button type="text"  @click="onOpenEditGoods(scope.row.id,3)">详情</el-button> -->
                                                    <el-button type="text" @click="onDeleteGoods(scope.row.id)">删除</el-button>
                                                </template>
                                            </el-table-column>
                                        </template>
                                    </ces-table>
                                </div>
                                
                                </el-form>
                            </el-tab-pane>

                        </el-tabs>
                    </el-col>
                </el-row>

            </el-form>

        </template>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="this.formEditMode" type="primary" @click="onSave(false)">保存</el-button>
                    <el-button v-if="this.formEditMode" type="primary" @click="onSave(true)">保存&关闭</el-button>
                    
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,formatLink} from "@/utils/tools";
    
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {
        GetDpSupplierByIdAsync, SaveDpSupplierAsync, GetSupplierGoodsByIdAsync, DelSupplierGoodsAsync, SaveSupplierGoodsAsync
    } from '@/api/order/alllinkDaiPai';

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import YhImgUpload from '@/components/upload/yh-img-upload.vue';
    import YhCityselector from '@/components/YhCom/yh-cityselector.vue';


    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState=(v)=>{
        switch(v){
            case 0:return '下架';
            case 1:return '已查询';
            case -1:return '失败';
        }
        return ' ';
    };



    const platformOptions = [
        { label: '其他', value: 0 },
        { label: '1688', value: 1 }
    ]

    const contactTableCols = [
    ];

    const goodsTableCols = [
        { istrue: true, prop: 'spGoodsName', label: '商品/SKU名称', minwidth: '160',type:'html', formatter:(row)=>formatLink(row.spGoodsName,row.spGoodsUrl) },
            
        { istrue: true, prop: 'skuOrGoods', label: '类型', width: '100' },        
         { istrue: true, prop: 'price', label: '价格', width: '100' }, 
        { istrue: true, prop: 'jqrNoticeState', label: '查询状态', width: '80',formatter:(row)=>fmtJqrNoticeState(row.jqrNoticeState) },    
        { istrue: true, prop: 'jqrLastNoticeTime', label: '查询时间', width: '100',formatter:(row)=>formatTime(row.jqrLastNoticeTime,'MM月DD HH:mm') },
        { istrue: true, prop: 'jqrErrMsg', label: '错误内容', width: '100' },

       
        { istrue: true, prop: 'spSkuAttr1', label: 'SKU属性1', width: '100' },
        { istrue: true, prop: 'spSkuAttr2', label: 'SKU属性2', width: '100' },
        { istrue: true, prop: 'spSkuAttr3', label: 'SKU属性3', width: '100' },
        { istrue: true, prop: 'spSkuAttr4', label: 'SKU属性4', width: '100' },
    ];

    export default {
        name: "SupplierForm",
        components: { MyContainer, MyConfirmButton, cesTable, YhQuillEditor, YhImgUpload, YhCityselector, },
        data() {
            return {
                platformOptions: platformOptions,
                that: this,
                form: {
                    "id": 0,
                    "enabled": true,
                    "code": "",
                    "name": "",
                    "province": "",
                    "city": "",
                    "county": "",
                    "street": "",
                    "doorplate": "",
                    "address": "",
                    "shopLogoUrl": "",
                    "shopPlatform": null,
                    "shopWebUrl": "",
                    "clTime": null,
                    "remark": "",
                    "contactList": [
                        {
                            "id": 0,
                            "enabled": true,
                            "dpSupplierId": 0,
                            "position": "",
                            "contactName": "",
                            "mobile": "",
                            "remark": ""
                        }
                    ]
                },
                contactTableCols: contactTableCols,
                total: 0,
                contactTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式
                //------------goods列表 start
                goodsTableCols:goodsTableCols,
                goodsTableData:[],
                //------------goods列表 end
               
            };
        },
        async mounted() {

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.contactTableData && this.contactTableData.length > 0) {
                    rowsCount = this.contactTableData.length;
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 280 ? 280 : rowsHeight;
            },
            goodsTableHeight() {
                let rowsCount = 1;
                if (this.goodsTableData && this.goodsTableData.length > 0) {
                    rowsCount = this.goodsTableData.length;
                    this.goodsTableData.forEach(row=>{
                        if(row.skuList && row.skuList.length>0){
                            rowsCount+=row.skuList.length;
                        }
                    });
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 280 ? 280 : rowsHeight;
            },           
            calcAddress() {
                //Province City County  Street
                this.form.address = (this.form.province ?? "") + (this.form.city ?? "") + (this.form.county ?? "") + (this.form.street ?? "") + (this.form.doorplate ?? "");
                return this.form.address;
            }
        },
        methods: {  
            fmtJqrNoticeState:fmtJqrNoticeState,
            async onDeleteGoods(oid){
                if(oid){
                    let rlt = await DelSupplierGoodsAsync({ spGoodsId: oid });
                    if (rlt && rlt.success) { 
                        this.$message.success('删除成功！');
                        this.afterGoodsSave();                
                    }
                }
            },
            onOpenEditGoods(oid,mode){
                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/SupplierGoodsForm.vue`,
                    title:'厂家商品',
                    args:{oid:oid,mode:mode , dpSupplierId:self.form.id},
                    height:610, 
                    callOk:self.afterGoodsSave             
                });     
            },
            onOpenAddGoods(){
                let supId=this.form.id;
                if(!(supId && supId>0)){
                    this.$alert('请先保存厂家后，再添加商品！');
                    return;
                }

                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/SupplierGoodsForm.vue`,
                    title:'新增厂家商品',
                    args:{oid:'0',mode:1 , dpSupplierId:supId},
                    height:610, 
                    callOk:self.afterGoodsSave             
                }); 
              
            }   ,    
            async afterGoodsSave(){
                let rlt = await GetDpSupplierByIdAsync({ spId: this.form.id });
                if (rlt && rlt.success) {                       
                    this.goodsTableData=rlt.data.goodsList==null?[]:rlt.data.goodsList;                      
                }
            },  
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({oid, mode}) {
                this.pageLoading = true;
                this.formEditMode = mode!=3;
                if (oid && oid > 0) {
                    let rlt = await GetDpSupplierByIdAsync({ spId: oid });
                    if (rlt && rlt.success) {
                        this.form = rlt.data.supplier;
                        //this.contactTableData = rlt.data.contactList==null?[]:rlt.data.contactList;
                        this.goodsTableData=rlt.data.goodsList==null?[]:rlt.data.goodsList;
                        this.pageLoading = false;
                    }
                } else {
                    Object.keys(this.form).forEach(key => (this.form[key] = null));
                    this.form.enabled=true;
                    this.form.id=0;
                    this.form.createUserId=0;
                    this.form.shopPlatform=1;
                    //this.contactTableData =[];

                    this.pageLoading = false;                    
                }
            },
            async save() {
                this.pageLoading = true;

                let saveData = { ...this.form };
                
                //saveData.contactList = [...this.contactTableData];

                let rlt = await SaveDpSupplierAsync(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('保存成功！');    

                    this.form = rlt.data.supplier;
                    //this.contactTableData = rlt.data.contactList==null?[]:rlt.data.contactList;
                    this.goodsTableData=rlt.data.goodsList==null?[]:rlt.data.goodsList;             
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
