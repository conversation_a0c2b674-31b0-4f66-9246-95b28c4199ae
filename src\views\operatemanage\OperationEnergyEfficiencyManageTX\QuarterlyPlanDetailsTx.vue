<template>
    <my-container v-loading="pageLoading">
        <vxe-table
              border
              show-overflow
              ref="table"
              :loading="listLoading"
              :data="list"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              height="770px"
              :row-config="{height: 251}"
              :column-config="{resizable: true}"
              header-align="center"
              >
              <vxe-column type="seq" width="60"></vxe-column>
              <vxe-colgroup title="季度">
                <vxe-column field="date3" title="月份"width="100"></vxe-column>
              </vxe-colgroup>
              <vxe-column field="title" title="目标类别" width="100">
              </vxe-column>
              <vxe-colgroup title="第一季度">
                <vxe-column field="date3" title="1月"width="100"></vxe-column>
                <vxe-column field="date3" title="2月"width="100"></vxe-column>
                <vxe-column field="date3" title="3月"width="100"></vxe-column>
              </vxe-colgroup>
              <vxe-colgroup title="第二季度">
                <vxe-column field="date3" title="4月"width="100"></vxe-column>
                <vxe-column field="date3" title="5月"width="100"></vxe-column>
                <vxe-column field="date3" title="6月"width="100"></vxe-column>
              </vxe-colgroup>
              <vxe-colgroup title="第三季度">
                <vxe-column field="date3" title="7月"width="100"></vxe-column>
                <vxe-column field="date3" title="8月"width="100"></vxe-column>
                <vxe-column field="date3" title="9月"width="100"></vxe-column>
              </vxe-colgroup>
              <vxe-colgroup title="第四季度">
                <vxe-column field="date3" title="10月"width="100"></vxe-column>
                <vxe-column field="date3" title="11月"width="100"></vxe-column>
                <vxe-column field="date3" title="12月"width="100"></vxe-column>
              </vxe-colgroup>
              <vxe-column  width="100" title="操作" >
                <template  #default="{ row }">
                    <vxe-button style="color: green;" type="text" @click="UploadDatas(row)" >保存</vxe-button>
                  </template>
              </vxe-column>
            </vxe-table>
            <template #footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
            </template>
      </my-container>
    </template>
    <script>
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
    import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import InputMult from "@/components/Comm/InputMult";
    import { Loading } from 'element-ui';
    import { ruleDirectorGroup } from '@/utils/formruletools'
    
    let loading;
    const startLoading = () => {
      loading = Loading.service({
      lock: true,
      text: '加载中……',
      background: 'rgba(0, 0, 0, 0.7)'
      });
    }; 
    
    export default {
      name: "Users",
      components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,InputMult,vxetablebase},
      data() {
        return {
           pageLoading: false,
           sels: [],
        pager: { OrderBy: "", IsAsc: false },
        total: 0,
        };
      },
      
       mounted() {
        formCreate.component('editor', FcEditor);
         this.initform();
         this.onSearch();
      },
      
      methods: {
        
       async sortchange(column){
          if(!column.order)
            this.pager={};
          else
            this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
         await this.onSearch();
        },
        onRefresh(){
          this.onSearch()
        },
        async onSearch(){
          this.$refs.pager.setPage(1);
          await this.getlist().then(res=>{  });
        },
        async getlist(){
          var that=this;
          var pager = this.$refs.pager.getPager();
          const params = {...pager,...this.pager,...this.filter};
          startLoading(); 
          const res = await getOperationPlanTx(params).then(res=>{
              loading.close();
              that.total = res.data?.total;
              that.financialreportlist = res.data?.list;
              that.summaryarry=res.data?.summary;
          });
        },
     
        selectchange:function(rows,row) {
          this.selids=[];
          rows.forEach(f=>{
            this.selids.push(f.id);
          })
        },
       onRefresh(){
            this.onSearch()
        },
      
    },
      
    };
    </script>
    <style lang="scss" scoped>
      .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
      }
    </style>
     
     