<template>
    <el-container style="height: 100%; border: 1px solid #eee">
      <el-main>
        <container>
          <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :loading="listLoading" style="width:100%;height:91%;margin: 0">
            <template slot='extentbtn'>
              <el-button-group>
                 <el-button style="padding: 0;margin: 0;">
                   <el-date-picker style="width: 200px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="timestartholder" :end-placeholder="timeendholder"></el-date-picker>
                 </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-input placeholder="内部订单号" v-model="filter.orderNoInner" style="width: 110px" clearable></el-input>
                  </el-button>
                  <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable style="width: 70px">
                      <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable style="width: 110px">
                      <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;margin: 0;">
                    <el-select  filterable clearable v-model="filter.orderStatus" placeholder="订单状态" style="width: 95px">
                      <el-option label="其他" :value="0"></el-option>
                      <el-option label="异常" :value="6"></el-option>
                      <el-option label="取消" :value="7"></el-option>
                      <el-option label="待付款" :value="1"></el-option>
                      <el-option label="发货中" :value="2"></el-option>
                      <el-option label="已发货" :value="3"></el-option>
                      <el-option label="被拆分" :value="4"></el-option>
                      <el-option label="被合并" :value="5"></el-option>
                      <el-option label="已客审待财审" :value="8"></el-option>
                      <el-option label="已付款待审核" :value="9"></el-option>
                    </el-select>
                  </el-button>
                <el-button style="padding: 0;margin: 0;">
                  <el-select v-model="filter.warehouse" clearable filterable placeholder="选择发货仓" style="width: 100px">
                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                  <el-select  filterable clearable v-model="filter.orderError" placeholder="异常订单" style="width: 90px">
                    <el-option label="否" :value="false"></el-option>
                    <el-option label="是" :value="true"></el-option>
                  </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                  <el-select v-model="filter.selectedNodeIndex" clearable filterable placeholder="节点" style="width:80px" @change="selectedNodeChange">
                    <el-option label="审单" :value="0"></el-option>
                    <el-option label="打单" :value="1"></el-option>
                    <el-option label="配货" :value="2"></el-option>
                    <el-option label="发货" :value="3"></el-option>
                  </el-select>
                  <el-select v-model="filter.orderPosition" clearable filterable placeholder="岗位" style="width:80px">
                    <el-option v-for="item in orderpositionlist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
              </el-button-group>
            </template>
          </ces-table>
        <template #footer>
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
        </template>
       </container>
      </el-main>
    <el-footer height="400px">
       <analysis ref="analysisordernodes" :filter="filter"></analysis>
  </el-footer>

  <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile" :file-list="fileList" :data="fileparm">
                <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer title="岗位维护" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt"  class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoformparm.rule" v-model="autoformparm.fApi" :option="autoformparm.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editparmVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onSetEditParm" />
       </div>
   </el-drawer>
  </el-container>
</template>
<script>

import cesTable from "@/components/Table/table.vue";
import container from '@/components/my-container';
import MyConfirmButton from '@/components/my-confirm-button';
import analysis from "@/views/order/ordernodes/analysish.vue";
import {platformlist,sendwarehouselist as warehouselist,orderpositionlist} from '@/utils/tools'
import {getList as getshopList } from '@/api/operatemanage/base/shop'
import {getOrderList,setParmPostion,getParmPostion,importOrderNodes,ExportOrderNodesAsync,importOrderNodesPick} from '@/api/order/ordernodes';

const tableCols =[
      {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'100',sortable:'custom',type:'orderLogInfo',orderType:'orderNoInner'},
      {istrue:true,prop:'orderNo',label:'线上订单号', width:'100',sortable:'custom'},
      {istrue:true,prop:'shopCode',label:'店铺名称', width:'140',sortable:'custom',formatter:(row)=>row.shopName},
      {istrue:true,prop:'warehouse',label:'仓库', width:'80',sortable:'custom',formatter:(row)=>row.warehouseName},
      {istrue:true,prop:'payTime',label:'订单付款时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'confirmTime',label:'审单时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'printTime',label:'打单时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'pickTime',label:'拣货时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'deliveryTime',label:'发货时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'weighTime',label:'称重时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'confirmSpanTime',label:'审单时长', width:'90',tipmesg:'分钟'},
      {istrue:true,prop:'printSpanTime',label:'打单时长', width:'90',tipmesg:'分钟'},
      {istrue:true,prop:'pickSpanTime',label:'拣货时长', width:'90',sortable:'custom',tipmesg:'分钟'},
      {istrue:true,prop:'deliverySpanTime',label:'打包发货时长', width:'115',sortable:'custom',tipmesg:'分钟'},
      {istrue:true,prop:'confirmer',label:'审单人员', width:'80',sortable:'custom'},
      {istrue:true,prop:'printer',label:'打单人员', width:'80',sortable:'custom'},
      {istrue:true,prop:'picker',label:'拣货人员', width:'80',sortable:'custom'},

      {istrue:true,prop:'delivery',label:'发货人员', width:'80',sortable:'custom'},
      
      {istrue:true,prop:'sendOutSpanTime',label:'发货时长', width:'115',sortable:'custom',tipmesg:'分钟'},
      {istrue:true,prop:'operateSpanTime',label:'仓内操作时长', width:'120',sortable:'custom',tipmesg:'分钟'}, 

      {istrue:true,prop:'positionConfirmer',label:'审单岗位', width:'80',sortable:'custom',formatter:(row)=>row.positionConfirmerName},
      {istrue:true,prop:'positionPrinter',label:'打单岗位', width:'80',sortable:'custom',formatter:(row)=>row.positionPrinterName},
      {istrue:true,prop:'positionPicker',label:'拣货岗位', width:'80',sortable:'custom',formatter:(row)=>row.positionPickerName},
      {istrue:true,prop:'positionDelivery',label:'发货岗位', width:'80',sortable:'custom',formatter:(row)=>row.positionDeliveryName},
      //{istrue:true,prop:'isConfirm',label:'是否审单', width:'90',sortable:'custom',formatter:(row)=>{return row.isConfirm==true?'是':'否'}},
      {istrue:true,prop:'orderStatus',label:'订单状态', width:'100',sortable:'custom',formatter:(row)=>row.orderStatusName},
      {istrue:true,prop:'modifiedTime',label:'更新时间', width:'110',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'110',sortable:'custom'},
     ];
const tableHandles=[
      {label:"导入订单关键节点", handle:(that)=>that.onstartImport(0)},
      {label:"导入补货", handle:(that)=>that.onstartImport(1)},
      {label:"导出", handle:(that)=>that.onExport()},
      {label:"岗位维护", handle:(that)=>that.PostMaintenance()},
      {label:"刷新", handle:(that)=>that.getlist()},
    ];
export default {
  name: 'Roles',
  components: {cesTable, container,MyConfirmButton,analysis},
   props:{
     width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
     },
  data() {
    return {
      timestartholder:'发货开始日期',
      timeendholder:'发货结束日期',
      chart:null,
      linelists:null,
      show:null,
      shareFeeType:5,
      filter: {
        startTime:null,
        endTime:null,
        timerange:null,
        orderNoInner:null,
        orderNo:null,
        isConfirm:null,
        orderStatus:null,
        shopCode:null,
        warehouse:null,
        orderError:null,
       },
       autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      editparmVisible:false,
      editparmLoading:false,
      platformlist:platformlist,
      warehouselist:warehouselist,
      orderpositionlist:orderpositionlist,
      params:{},
      that:this,
      list: [],
      shopList: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      listLoading: false,
      expresscompanylist: [],
      dialogVisible: false,
      uploadLoading:false,
      postDialogVisible:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      importtype:null
    }
  },
  async created() {
    await this.initformparm()
    await this.defaultDate()
    await this.onSearch()
   // await this.updatePositionKey(1)
  },
  mounted() {
  },
  beforeUpdate() { },
  methods: {
    async PostMaintenance(){
      this.editparmVisible = true
    },
    async initformparm(){
       let that=this;
       this.autoformparm.rule= [{type:'select',field:'postion',title:'岗位',value: 1,col:{span:12},validate: [{type: 'number', required: true, message:'请选择'}],
                                 update(val, rule){{that.updatePositionKey(val)}},
                                options: [{value:1, label:'审单'},{value:2, label:'组团打单'},{value:3, label:'组团'},{value:4, label:'配货'},{value:5, label:'套袋子'},
                                          {value:6, label:'机器包'},{value:7, label:'杂包'},{value:8, label:'大包打包'},{value:9, label:'上架'}]},
                     {type:'input',field:'postionKeyWords',title:'岗位前缀',value:'',validate: [{type: 'string', required: true, message:'请输入'}],
                        props:{placeholder:'多个用“,”隔开'}},
                    ]
    },
    async updatePositionKey(postion) {
        await this.autoformparm.fApi.setValue({postionKeyWords:''})
        var word=await getParmPostion({postion:postion});
        await this.autoformparm.fApi.setValue({postionKeyWords:word.data})
        await this.autoformparm.fApi.sync('postionKeyWords')
    },
    async onSetEditParm() {
      this.editparmLoading=true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoformparm.fApi.formData()
          var res= await setParmPostion(formData)
          if(res.code==1)
            this.$message({type: 'success',message: '提交成功'});
          else
             this.$message({type: 'warn',message: '提交失败'});
        }else{
                //todo 表单验证未通过
              }
          })
     this.editparmLoading=false;
    },
    async defaultDate(){
       const end = new Date();
       const start = new Date();
       start.setTime(start.getTime() - 1800 * 1000 * 24 * 30);
       this.filter.timerange = [start,end]
    },
    async onchangeplatform(val){
      this.categorylist =[]
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shopList=res1.data.list
    },
    async selectedNodeChange(val){
      if(val==0){
         this.timestartholder="审单开始日期";this.timeendholder="审单结束日期";
      }
      else if(val==1){
         this.timestartholder="打单开始日期";this.timeendholder="打单结束日期";
      }
      else if(val==2){
         this.timestartholder="配货开始日期";this.timeendholder="配货结束日期";
      }
      else if(val==3){
         this.timestartholder="发货开始日期";this.timeendholder="发货结束日期";
      }
    },
    async onSearch() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      else{
        this.$message({message: "请先选择审核确认日期！",type: "warning",});
        return;
      }
      this.$refs.pager.setPage(1)
      await this.getlist()
      let _th=this;
      await this.onSearchChild()
      //await _th.loadanalysis()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
       const params = { ...pager,...page,...this.filter}
       if(params==false) return;
       this.listLoading=true
       const res= await getOrderList(params)
       this.listLoading=false
       console.log()
       if(!res?.success){
        return
       }
       this.total=res.data.total;
       const data=res.data.list;
       this.list=data;
       this.summaryarry=res.data.summary;
    },
    async loadanalysis() {
      let _th=this;
      let params={...this.pager, ... this.filter};
      await this.$nextTick(async () => {
        await _th.$refs.ordernodesanalysis.getAnalysis(params);
      });
    },
   async onSearchChild() {
      let _th=this;
      await this.$nextTick(async () => {
        await _th.$refs.analysisordernodes.onSearch();
      });
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  selsChange: function(sels) {
    this.sels = sels
  },
  selectchange:function(rows,row) {
    this.selids=[];
    rows.forEach(f=>{
      this.selids.push(f.id);
    })
   },
  //导入 0：关键节点 1：补货
  async onstartImport(type){
      this.dialogVisible=true;
      this.importtype=type;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      var res;
      if(this.importtype==0)
        res= await importOrderNodes(form);
      else if(this.importtype==1)
        res= await importOrderNodesPick(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await ExportOrderNodesAsync(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");

        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单关键节点_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
      const params = {
        ... this.filter
      }
      return params;
    },
  }
}
</script>
