<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'提交开始时间'" :end-placeholder="'提交结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <el-button type="primary" @click="onSearch(true)">查询</el-button>
        </template>
        <el-scrollbar style="height: 165px;">
        <div>
            <div style="text-align: center; margin-bottom: 5px;">
                <el-checkbox name="type0" v-model="filter.myTypeCheck.type0" @change="onSearch(false)">所有</el-checkbox>
                <el-checkbox name="type1" v-model="filter.myTypeCheck.type1" @change="onSearch(false)">发货-批次</el-checkbox>
                <el-checkbox name="type2" v-model="filter.myTypeCheck.type2" @change="onSearch(false)">批次-面单</el-checkbox>
                <el-checkbox name="type3" v-model="filter.myTypeCheck.type3" @change="onSearch(false)">面单-出库</el-checkbox>
                <el-checkbox name="type4" v-model="filter.myTypeCheck.type4" @change="onSearch(false)">出库-打包</el-checkbox>
                <el-checkbox name="type15" v-model="filter.myTypeCheck.type15" @change="onSearch(false)">打包-发货</el-checkbox>
                <el-checkbox name="type16" v-model="filter.myTypeCheck.type16" @change="onSearch(false)">发货-称重</el-checkbox>
                <el-checkbox name="type5" v-model="filter.myTypeCheck.type5" @change="onSearch(false)">打包-称重</el-checkbox>
                <el-checkbox name="type6" v-model="filter.myTypeCheck.type6" @change="onSearch(false)">称重-揽收</el-checkbox>
                <el-checkbox name="type7" v-model="filter.myTypeCheck.type7" @change="onSearch(false)">揽收-中转</el-checkbox>
                <el-checkbox name="type8" v-model="filter.myTypeCheck.type8" @change="onSearch(false)">中转-派送</el-checkbox>
                <el-checkbox name="type9" v-model="filter.myTypeCheck.type9" @change="onSearch(false)">派送-签收</el-checkbox>
                <el-checkbox name="type10" v-model="filter.myTypeCheck.type10" @change="onSearch(false)">发货-揽收</el-checkbox>
                <el-checkbox name="type11" v-model="filter.myTypeCheck.type11" @change="onSearch(false)">付款-揽收</el-checkbox>
                <el-checkbox name="type12" v-model="filter.myTypeCheck.type12" @change="onSearch(false)">付款-签收</el-checkbox>
                <i :class="isExpanded ? 'el-icon-arrow-up toggle-icon' : 'el-icon-arrow-down toggle-icon'" @click="toggleExpand(1)"></i>
                <div v-show="isExpanded">
                <el-checkbox name="type13" v-model="filter.myTypeCheck.type13" @change="onSearch(false)">付款-签收40h未完成</el-checkbox>
                <el-checkbox name="type14" v-model="filter.myTypeCheck.type14" @change="onSearch(false)">付款-签收40h已完成</el-checkbox>
                </div>
            </div>
            <div style="text-align: center; margin-bottom: 5px;">
                <el-checkbox name="cang0" v-model="filter.myWareCheck.cang0" @change="onSearch(false)">全仓</el-checkbox>
                <el-checkbox name="cang1" v-model="filter.myWareCheck.cang1" @change="onSearch(false)">义乌市昀晗供应链管理有限公司</el-checkbox>
                <el-checkbox name="cang2" v-model="filter.myWareCheck.cang2" @change="onSearch(false)">【义乌圆通孵化仓】</el-checkbox>
                <el-checkbox name="cang3" v-model="filter.myWareCheck.cang3" @change="onSearch(false)">【义乌圆通爆款仓】</el-checkbox>
                <el-checkbox name="cang4" v-model="filter.myWareCheck.cang4" @change="onSearch(false)">【义乌圆通2楼】</el-checkbox>
                <el-checkbox name="cang5" v-model="filter.myWareCheck.cang5" @change="onSearch(false)">【义乌圆通3楼】</el-checkbox>
                <el-checkbox name="cang6" v-model="filter.myWareCheck.cang6" @change="onSearch(false)">【义乌圆通5楼】</el-checkbox>
                <el-checkbox name="cang7" v-model="filter.myWareCheck.cang7" @change="onSearch(false)">【义乌邮政仓】</el-checkbox>
                <el-checkbox name="cang8" v-model="filter.myWareCheck.cang8" @change="onSearch(false)">【义乌加工仓】</el-checkbox>
                <el-checkbox name="cang9" v-model="filter.myWareCheck.cang9" @change="onSearch(false)">【天天2楼仓】</el-checkbox>
                <i :class="isFullWarehouse ? 'el-icon-arrow-up toggle-icon' : 'el-icon-arrow-down toggle-icon'" @click="toggleExpand(2)"></i>
                <div v-show="isFullWarehouse">
                <el-checkbox name="cang10" v-model="filter.myWareCheck.cang10" @change="onSearch(false)">【义乌跨境仓】</el-checkbox>
                <el-checkbox name="cang11" v-model="filter.myWareCheck.cang11" @change="onSearch(false)">【杭州分仓】</el-checkbox>
                <el-checkbox name="cang12" v-model="filter.myWareCheck.cang12" @change="onSearch(false)">【南昌全品类仓】</el-checkbox>
                <el-checkbox name="cang13" v-model="filter.myWareCheck.cang13" @change="onSearch(false)">【南昌定制仓】</el-checkbox>
                <el-checkbox name="cang14" v-model="filter.myWareCheck.cang14" @change="onSearch(false)">【南昌裁剪仓】</el-checkbox>
                <el-checkbox name="cang15" v-model="filter.myWareCheck.cang15" @change="onSearch(false)">【西安港务分仓】</el-checkbox>
                <el-checkbox name="cang16" v-model="filter.myWareCheck.cang16" @change="onSearch(false)">【西安分仓】</el-checkbox>
                <el-checkbox name="cang17" v-model="filter.myWareCheck.cang17" @change="onSearch(false)">预包加工仓</el-checkbox>
                <el-checkbox name="cang18" v-model="filter.myWareCheck.cang18" @change="onSearch(false)">昀晗-KS</el-checkbox>
                <el-checkbox name="cang19" v-model="filter.myWareCheck.cang19" @change="onSearch(false)">昀晗-退件仓</el-checkbox>
                <el-checkbox name="cang20" v-model="filter.myWareCheck.cang20" @change="onSearch(false)">昀晗-云仓</el-checkbox>
                <!-- <el-checkbox name="cang21" v-model="filter.myWareCheck.cang21" @change="onSearch(false)">昀晗-gd</el-checkbox> -->
                <el-checkbox name="cang22" v-model="filter.myWareCheck.cang22" @change="onSearch(false)">JD-昀晗义乌仓</el-checkbox>
                <el-checkbox name="cang23" v-model="filter.myWareCheck.cang23" @change="onSearch(false)">外仓加工-成品仓</el-checkbox>
                <el-checkbox name="cang24" v-model="filter.myWareCheck.cang24" @change="onSearch(false)">昀晗-中转仓</el-checkbox>
                <el-checkbox name="cang25" v-model="filter.myWareCheck.cang25" @change="onSearch(false)">【昀晗-WH】</el-checkbox>
                </div>
            </div>
            <div style="text-align: center; margin-bottom: 5px;">
                <el-checkbox name="kuaidi0" v-model="filter.myKuaiDiCheck.kuaidi0" @change="onSearch(false)">所有快递</el-checkbox>
                <el-checkbox name="kuaidi1" v-model="filter.myKuaiDiCheck.kuaidi1" @change="onSearch(false)">义乌邮政--佛堂王进</el-checkbox>
                <el-checkbox name="kuaidi2" v-model="filter.myKuaiDiCheck.kuaidi2" @change="onSearch(false)">义乌申通--市网点</el-checkbox>
                <el-checkbox name="kuaidi3" v-model="filter.myKuaiDiCheck.kuaidi3" @change="onSearch(false)">昆山邮政--云仓</el-checkbox>
                <el-checkbox name="kuaidi4" v-model="filter.myKuaiDiCheck.kuaidi4" @change="onSearch(false)">南昌申通--供储产业园</el-checkbox>
                <el-checkbox name="kuaidi5" v-model="filter.myKuaiDiCheck.kuaidi5" @change="onSearch(false)">上海邮政--云仓</el-checkbox>
                <el-checkbox name="kuaidi6" v-model="filter.myKuaiDiCheck.kuaidi6" @change="onSearch(false)">南昌中通--南昌县三部</el-checkbox>
                <el-checkbox name="kuaidi7" v-model="filter.myKuaiDiCheck.kuaidi7" @change="onSearch(false)">西安港务分仓-邮政快递</el-checkbox>
                <el-checkbox name="kuaidi8" v-model="filter.myKuaiDiCheck.kuaidi8" @change="onSearch(false)">安徽邮政--云仓</el-checkbox>
                <el-checkbox name="kuaidi9" v-model="filter.myKuaiDiCheck.kuaidi9" @change="onSearch(false)">南昌韵达--罗家集站点</el-checkbox>
                <i :class="isExpressDelivery ? 'el-icon-arrow-up toggle-icon' : 'el-icon-arrow-down toggle-icon'" @click="toggleExpand(3)"></i>
                <div v-show="isExpressDelivery">
                <el-checkbox name="kuaidi10" v-model="filter.myKuaiDiCheck.kuaidi10" @change="onSearch(false)">义乌韵达大件-张敏</el-checkbox>
                <el-checkbox name="kuaidi11" v-model="filter.myKuaiDiCheck.kuaidi11" @change="onSearch(false)">南昌极兔--南昌县</el-checkbox>
                </div>
            </div>
        </div>
      </el-scrollbar>
            <div>
                <buschar ref="indexchatbuschar" :analysisData="indexChatData" :legendChanges="legendChanges">
                </buschar>
            </div>
    </my-container>
</template>

<script>
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { pickerOptions } from '@/utils/tools'
import { GetDeductNodeInfoChatData } from "@/api/order/deductbefore"
import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import MyConfirmButton from '@/components/my-confirm-button'
import buschar from '@/components/Bus/buschar';
export default {
    name: 'DeductBeforeZrDtlList',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    data() {
        return {
            isExpanded: false,
            isFullWarehouse: false,
            isExpressDelivery: false,
            that: this,
            filter: {
                submitDateStart: null,
                submitDateEnd: null,
                isSearch: true,
                timerange: [
                    formatTime(dayjs().subtract(31, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs(), "YYYY-MM-DD"),
                ],
                myTypeCheck: {
                    type0: true,
                    type1: false,
                    type2: false,
                    type3: false,
                    type4: false,
                    type5: false,
                    type6: false,
                    type7: false,
                    type8: false,
                    type9: false,
                    type10: false,
                    type11: false,
                    type12: false,
                    type13: false,
                    type14: false,
                    type15: false,
                    type16: false,
                },
                myWareCheck: {
                    cang0: true,
                    cang1: false,
                    cang2: false,
                    cang3: false,
                    cang4: false,
                    cang5: false,
                    cang6: false,
                    cang7: false,
                    cang8: false,
                    cang9: false,
                    cang10: false,
                    cang11: false,
                    cang12: false,
                    cang13: false,
                    cang14: false,
                    cang15: false,
                    cang16: false,
                    cang17: false,
                    cang18: false,
                    cang19: false,
                    cang20: false,
                    cang21: false,
                    cang22: false,
                    cang23: false,
                    cang24: false,
                    cang25: false,
                },
                myKuaiDiCheck:{
                    kuaidi0:true,
                    kuaidi1:false,
                    kuaidi2:false,
                    kuaidi3:false,
                    kuaidi4:false,
                    kuaidi5:false,
                    kuaidi6:false,
                    kuaidi7:false,
                    kuaidi8:false,
                    kuaidi9:false,
                    kuaidi10:false,
                    kuaidi11:false,
                }
            },
            pickerOptions: pickerOptions,
            list: [],
            listLoading: false,
            pageLoading: false,

            indexChatData: {},
            indexChatSelectedLegend: [],
        };
    },
    async mounted() {
        await this.onSearch(true);
    },
    methods: {
        toggleExpand(val) {
          if(val == 1){
            this.isExpanded = !this.isExpanded;
          } else if(val == 2){
            this.isFullWarehouse = !this.isFullWarehouse;
          } else if(val == 3){
            this.isExpressDelivery = !this.isExpressDelivery;
          }
        },
        //查询第一页
        async onSearch(isSearch) {
            this.filter.isSearch = isSearch;

            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.submitDateStart = this.filter.timerange[0];
                this.filter.submitDateEnd = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            this.pageLoading = true;
            let res = await GetDeductNodeInfoChatData(this.filter);
            this.pageLoading = false;
            this.indexChatData = res;
            this.indexChatData.legend = {
                data: this.indexChatData.legend,
                type: 'scroll',
                top: 25,
            },
            this.$nextTick(() => {
                this.$refs.indexchatbuschar.initcharts();
            });
        },
        async legendChanges(selected) {
            this.indexChatSelectedLegend = selected;
        },
    },
};
</script>

<style lang="scss" scoped>
.toggle-icon {
  cursor: pointer;
  font-size: 20px;
  color: #409EFF;
  margin-left: 10px;
}
</style>
