<template>
  <my-container>
    <el-tabs v-model="activeName" style="height:95%;">
      <el-tab-pane label="样品登记" name="first1" style="height: 100%;">
        <sampleRegistration ref="refsampleRegistration" :deptList="deptList" />
      </el-tab-pane>
      <el-tab-pane label="领用记录" name="first2" style="height: 100%;" lazy>
        <receiptRecord ref="refreceiptRecord" :deptList="deptList" />
      </el-tab-pane>
      <el-tab-pane label="库位管理" name="first3" style="height: 100%;" lazy>
        <warehouseLocation ref="refwarehouseLocation" />
      </el-tab-pane>
      <el-tab-pane label="1688官方验货" name="first4" style="height: 100%;" lazy>
        <OfficialInspection ref="OfficialInspection" />
      </el-tab-pane>
      <el-tab-pane label="1688官方验货待拍" name="first5" style="height: 100%;" lazy>
        <OfficialInspectionDp ref="OfficialInspectionDp" />
      </el-tab-pane>
      <el-tab-pane label="有库存待拍" name="first6" style="height: 100%;" lazy>
        <proxyBiddingSelection ref="proxyBiddingSelection" />
      </el-tab-pane>
      <el-tab-pane label="样品间相似品待查" name="first7" style="height: 100%;" lazy
        v-if="checkPermission('SimilarProductAwaitingInvestigationYpj')">
        <similarProductCommon v-if="activeName == 'first7'" ref="refsimilarProductCommon1" :sampleOrBrand="1" />
      </el-tab-pane>
      <el-tab-pane label="采购相似品待查" name="first8" style="height: 100%;" lazy
        v-if="checkPermission('SimilarProductAwaitingInvestigationCg')">
        <similarProductCommon v-if="activeName == 'first8'" ref="refsimilarProductCommon0" :sampleOrBrand="2" />
      </el-tab-pane>
      <el-tab-pane label="质检不一致" name="first9" style="height: 100%;" lazy
        v-if="checkPermission('QualityInspectionInconsistentPermission')">
        <qualityInspectionInconsistent v-if="activeName == 'first9'" ref="qualityInspectionInconsistent" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import warehouseLocation from './components/warehouseLocation.vue'
import sampleRegistration from './components/sampleRegistration.vue'
import receiptRecord from './components/receiptRecord.vue'
import OfficialInspection from './components/1688OfficialInspection.vue'
import OfficialInspectionDp from './components/1688OfficialInspectionDp.vue'
import proxyBiddingSelection from './components/proxyBiddingSelection.vue'
import similarProductCommon from './components/similarProductCommon.vue'
import qualityInspectionInconsistent from './components/qualityInspectionInconsistent.vue'

import { AllDDDeptTreeNcWh } from '@/api/profit/personnel'

export default {
  name: "sampleTabIndex",
  components: {
    MyContainer, warehouseLocation, sampleRegistration, receiptRecord, OfficialInspection, OfficialInspectionDp, proxyBiddingSelection, similarProductCommon, qualityInspectionInconsistent
  },
  data() {
    return {
      activeName: 'first1',
      deptList: [],
    };
  },
  mounted() {
    this.getDeptList()
  },
  methods: {
    async getDeptList() {
      await AllDDDeptTreeNcWh().then(res => {
        if (res.success) {
          this.deptList = []
          let a = []
          res.data.childDeptList.forEach(item => {
            if (item.childDeptList && item.childDeptList.length > 0) {
              item.childDeptList.forEach(item2 => {
                a.push(item2.name)
              })
            }
          })
          this.deptList = [...new Set(a)]
        } else {
          this.$message({ message: res.msg, type: "danger" });
        }
      })
    },
  },
};
</script>
<style lang="scss" scoped></style>
