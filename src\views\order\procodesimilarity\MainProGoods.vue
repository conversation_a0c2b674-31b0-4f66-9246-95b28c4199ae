<template>
  <my-container v-loading="pageLoading">
    <template #header>  
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent size="mini">
          <el-form-item label="销量取值期间:">
          <el-select v-model="filter.days" placeholder="请选择周转天数" style="width: 70px">
            <el-option label="1天" value="1"></el-option>
            <el-option label="3天" value="3"></el-option>
            <el-option label="7天" value="7"></el-option>
            <el-option label="15天" value="15"></el-option>
            <el-option label="30天" value="30"></el-option>
            <el-option label="45天" value="45"></el-option>
          </el-select>
        </el-form-item>
      </el-form>           
    </template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarryCompute"
        :tableData='list'    :tableCols='tableCols' :isSelection="false" :tablefixed="true"
        :tableHandles='tableHandles' :isSelectColumn="false" @select="selsChange"
        :loading="listLoading" >
      </ces-table>  
    <template #footer>
      <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getlist"
      />
    </template>
  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatmoney} from "@/utils/tools";
import { 
  pageProCodeSimilarityGoods
} from "@/api/order/procodesimilarity"

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney=function(value){
  return formatmoney(Math.abs(value)>1?Math.round(value):Math.round(value,1));
};

const tableCols=[
    {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
    {istrue:true,prop:'goodsName',label:'商品名称', width:'300',sortable:'custom'},
    {istrue:true,prop:'createdTime',label:'创建时间', width:'100',sortable:'custom'},
    {istrue:true,prop:'costPrice',label:'成本', width:'130',sortable:'custom'},
    {istrue:true,prop:'turnover1',label:'周转天数', width:'100',},
    {istrue:true,prop:'goodsImage',label:'图片', width:'60',sortable:'custom',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
    {istrue:true,prop:'invAmount',label:'编码库存资金', width:'120',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmount)},
    {istrue:true,prop:'invAmountPredict',label:'编码库存资金预估', width:'140',sortable:'custom',formatter:(row)=>myformatmoney(row.invAmountPredict)},
];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
        that:this,
        list: [],
        summaryarry:{},
        pager:{OrderBy:"invAmount",IsAsc:false},
        tableCols:tableCols,  
        tableHandles:[],      
        total: 0,
        sels: [], 
        listLoading: false,
        pageLoading:false,
        visible:false,       
    }
  },
  props:{
    filter:{
        parentId:null,
        goodsCode:null,
        // days:null
    },
  },
  async mounted() {
    await this.getlist();       
  },
  methods: {
    clearFilter(){
      this.filter={
          parentId:null,
          goodsCode:null
      };
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {      
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true;
      var res = await pageProCodeSimilarityGoods(params);
          
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      this.summaryarry = res.data.summary;
      const data = res.data.list;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;     
    },
  },
  computed:{
    summaryarryCompute(){
      var sum ={};
      if(this.summaryarry){
        for (const key in this.summaryarry) {
          sum[key]=myformatmoney(this.summaryarry[key])
        }
      }
      return sum;
    },  
  },
}
</script>
<style scoped>
  ::v-deep .el-link.el-link--primary{
    margin-right: 7px;
  }

  ::v-deep .el-table__fixed 
  {
      pointer-events:auto;
  } 
</style>
