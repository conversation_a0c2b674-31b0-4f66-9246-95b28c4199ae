<template>
  <div style="display: flex; justify-content: space-between;">
    <div style="flex: 1;" v-show="selectProfitrates.includes('毛三利润率(发生)')">
      <el-select filterable v-model="threeProfitRate" :clearable="clearable" placeholder="毛三利润率(发生)"
        :style="{ width: width }" @change="onTrichosan($event)">
        <el-option key="低于" label="毛三利润率(发生)低于" :value="1"></el-option>
        <el-option key="高于" label="毛三利润率(发生)高于" :value="2"></el-option>
        <el-option key="等于" label="毛三利润率(发生)等于" :value="3"></el-option>
        <el-option key="介于" label="毛三利润率(发生)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxProfit3Rate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeProfitRate == 1" />
      <el-input-number v-model.trim="ListInfo.minProfit3Rate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeProfitRate == 2" />
      <el-input-number v-model.trim="ListInfo.profit3Rate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeProfitRate == 3" />
      <el-input-number v-model.trim="ListInfo.minProfit3Rate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeProfitRate == 4" />
      <el-input-number v-model.trim="ListInfo.maxProfit3Rate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeProfitRate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('毛四利润率(发生)')">
      <el-select filterable v-model="fourProfitRate" :clearable="clearable" placeholder="毛四利润率(发生)"
        :style="{ width: width }" @change="onFourfoule($event)">
        <el-option key="低于" label="毛四利润率(发生)低于" :value="1"></el-option>
        <el-option key="高于" label="毛四利润率(发生)高于" :value="2"></el-option>
        <el-option key="等于" label="毛四利润率(发生)等于" :value="3"></el-option>
        <el-option key="介于" label="毛四利润率(发生)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxProfit33Rate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourProfitRate == 1" />
      <el-input-number v-model.trim="ListInfo.minProfit33Rate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourProfitRate == 2" />
      <el-input-number v-model.trim="ListInfo.profit33Rate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourProfitRate == 3" />
      <el-input-number v-model.trim="ListInfo.minProfit33Rate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourProfitRate == 4" />
      <el-input-number v-model.trim="ListInfo.maxProfit33Rate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourProfitRate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('毛五利润率(发生)')">
      <el-select filterable v-model="fiveProfitRate" :clearable="clearable" placeholder="毛五利润率(发生)"
        :style="{ width: width }" @change="onMaoFive($event)">
        <el-option key="低于" label="毛五利润率(发生)低于" :value="1"></el-option>
        <el-option key="高于" label="毛五利润率(发生)高于" :value="2"></el-option>
        <el-option key="等于" label="毛五利润率(发生)等于" :value="3"></el-option>
        <el-option key="介于" label="毛五利润率(发生)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxProfit5Rate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveProfitRate == 1" />
      <el-input-number v-model.trim="ListInfo.minProfit5Rate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveProfitRate == 2" />
      <el-input-number v-model.trim="ListInfo.profit5Rate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveProfitRate == 3" />
      <el-input-number v-model.trim="ListInfo.minProfit5Rate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveProfitRate == 4" />
      <el-input-number v-model.trim="ListInfo.maxProfit5Rate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveProfitRate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('毛六利润率(发生)')">
      <el-select filterable v-model="sixProfitRate" :clearable="clearable" placeholder="毛六利润率(发生)"
        :style="{ width: width }" @change="onMaosix($event)">
        <el-option key="低于" label="毛六利润率(发生)低于" :value="1"></el-option>
        <el-option key="高于" label="毛六利润率(发生)高于" :value="2"></el-option>
        <el-option key="等于" label="毛六利润率(发生)等于" :value="3"></el-option>
        <el-option key="介于" label="毛六利润率(发生)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxProfit6Rate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixProfitRate == 1" />
      <el-input-number v-model.trim="ListInfo.minProfit6Rate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixProfitRate == 2" />
      <el-input-number v-model.trim="ListInfo.profit6Rate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixProfitRate == 3" />
      <el-input-number v-model.trim="ListInfo.minProfit6Rate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixProfitRate == 4" />
      <el-input-number v-model.trim="ListInfo.maxProfit6Rate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixProfitRate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('运营毛三利润率(减退款)')">
      <el-select filterable v-model="threeOperate" :clearable="clearable" placeholder="运营毛三利润率(减退款)"
        :style="{ width: width }" @change="onOperatingMargin($event)">
        <el-option key="低于" label="运营毛三利润率(减退款)低于" :value="1"></el-option>
        <el-option key="高于" label="运营毛三利润率(减退款)高于" :value="2"></el-option>
        <el-option key="等于" label="运营毛三利润率(减退款)等于" :value="3"></el-option>
        <el-option key="介于" label="运营毛三利润率(减退款)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxyyProfit3AfterRate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeOperate == 1" />
      <el-input-number v-model.trim="ListInfo.minyyProfit3AfterRate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeOperate == 2" />
      <el-input-number v-model.trim="ListInfo.yyProfit3AfterRate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeOperate == 3" />
      <el-input-number v-model.trim="ListInfo.minyyProfit3AfterRate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeOperate == 4" />
      <el-input-number v-model.trim="ListInfo.maxyyProfit3AfterRate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="threeOperate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('运营毛四利润率(减退款)')">
      <el-select filterable v-model="fourOperate" :clearable="clearable" placeholder="运营毛四利润率(减退款)"
        :style="{ width: width }" @change="onOperatingQuarter($event)">
        <el-option key="低于" label="运营毛四利润率(减退款)低于" :value="1"></el-option>
        <el-option key="高于" label="运营毛四利润率(减退款)高于" :value="2"></el-option>
        <el-option key="等于" label="运营毛四利润率(减退款)等于" :value="3"></el-option>
        <el-option key="介于" label="运营毛四利润率(减退款)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxyyProfit4AfterRate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourOperate == 1" />
      <el-input-number v-model.trim="ListInfo.minyyProfit4AfterRate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourOperate == 2" />
      <el-input-number v-model.trim="ListInfo.yyProfit4AfterRate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourOperate == 3" />
      <el-input-number v-model.trim="ListInfo.minyyProfit4AfterRate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourOperate == 4" />
      <el-input-number v-model.trim="ListInfo.maxyyProfit4AfterRate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fourOperate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('运营毛五利润率(减退款)')">
      <el-select filterable v-model="fiveOperate" :clearable="clearable" placeholder="运营毛五利润率(减退款)"
        :style="{ width: width }" @change="onOperatingmarginfive($event)">
        <el-option key="低于" label="运营毛五利润率(减退款)低于" :value="1"></el-option>
        <el-option key="高于" label="运营毛五利润率(减退款)高于" :value="2"></el-option>
        <el-option key="等于" label="运营毛五利润率(减退款)等于" :value="3"></el-option>
        <el-option key="介于" label="运营毛五利润率(减退款)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxyyProfit5AfterRate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveOperate == 1" />
      <el-input-number v-model.trim="ListInfo.minyyProfit5AfterRate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveOperate == 2" />
      <el-input-number v-model.trim="ListInfo.yyProfit5AfterRate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveOperate == 3" />
      <el-input-number v-model.trim="ListInfo.minyyProfit5AfterRate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveOperate == 4" />
      <el-input-number v-model.trim="ListInfo.maxyyProfit5AfterRate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="fiveOperate == 4" />
    </div>
    <div style="flex: 1;" v-show="selectProfitrates.includes('运营毛六利润率(减退款)')">
      <el-select filterable v-model="sixOperate" :clearable="clearable" placeholder="运营毛六利润率(减退款)"
        :style="{ width: width }" @change="onOperatingmarginsix($event)">
        <el-option key="低于" label="运营毛六利润率(减退款)低于" :value="1"></el-option>
        <el-option key="高于" label="运营毛六利润率(减退款)高于" :value="2"></el-option>
        <el-option key="等于" label="运营毛六利润率(减退款)等于" :value="3"></el-option>
        <el-option key="介于" label="运营毛六利润率(减退款)介于" :value="4"></el-option>
      </el-select>
      <el-input-number v-model.trim="ListInfo.maxyyProfit6AfterRate" placeholder="低于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixOperate == 1" />
      <el-input-number v-model.trim="ListInfo.minyyProfit6AfterRate" placeholder="高于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixOperate == 2" />
      <el-input-number v-model.trim="ListInfo.yyProfit6AfterRate" placeholder="等于" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixOperate == 3" />
      <el-input-number v-model.trim="ListInfo.minyyProfit6AfterRate" placeholder="最小值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixOperate == 4" />
      <el-input-number v-model.trim="ListInfo.maxyyProfit6AfterRate" placeholder="最大值" :min="minimum" :max="maximum"
        :precision="precisionValue" :controls="false" class="publicCss" v-show="sixOperate == 4" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'dailyProfitrate',
  props: {
    width: {
      type: String,
      default() {
        return '120px';
      }
    },
    clearable: {
      type: Boolean,
      default() { return true; }
    },
    valueChanged: {
      type: Object,
      default() {
        return {}
      }
    },
    maximum: {
      type: Number,
      default() {
        return 999999;
      }
    },
    minimum: {
      type: Number,
      default() {
        return -999999;
      }
    },
    precisionValue: {
      type: Number,
      default() {
        return 2;
      }
    }
  },
  data() {
    return {
      selectProfitrates: [],
      threeProfitRate: null,
      fourProfitRate: null,
      fiveProfitRate: null,
      sixProfitRate: null,
      threeOperate: null,
      fourOperate: null,
      fiveOperate: null,
      sixOperate: null,
      loading: false,
      innerValue: null,
      grouplist: [],
      ListInfo: {
        maxProfit3Rate: undefined,//毛三利润率(发生)高于
        minProfit3Rate: undefined,//毛三利润率(发生)低于
        profit3Rate: undefined,//毛三利润率(发生)等于
        maxProfit33Rate: undefined,//毛四利润率(发生)高于
        minProfit33Rate: undefined,//毛四利润率(发生)低于
        profit33Rate: undefined,//毛四利润率(发生)等于
        maxyyProfit3AfterRate: undefined,//运营毛三利润率(减退款)高于
        minyyProfit3AfterRate: undefined,//运营毛三利润率(减退款)低于
        yyProfit3AfterRate: undefined,//运营毛三利润率(减退款)等于
        maxyyProfit4AfterRate: undefined,//运营毛四利润率(减退款)高于
        minyyProfit4AfterRate: undefined,//运营毛四利润率(减退款)低于
        yyProfit4AfterRate: undefined,//运营毛四利润率(减退款)等于
        maxProfit5Rate: undefined,//毛五利润率(发生)高于
        minProfit5Rate: undefined,//毛五利润率(发生)低于
        profit5Rate: undefined,//毛五利润率(发生)等于
        maxyyProfit5AfterRate: undefined,//运营毛五利润率(减退款)高于
        minyyProfit5AfterRate: undefined,//运营毛五利润率(减退款)低于
        yyProfit5AfterRate: undefined,//运营毛五利润率(减退款)等于
        maxProfit6Rate: undefined,//毛六利润率(发生)高于
        minProfit6Rate: undefined,//毛六利润率(发生)低于
        profit6Rate: undefined,//毛六利润率(发生)等于
        maxyyProfit6AfterRate: undefined,//运营毛六利润率(减退款)高于
        minyyProfit6AfterRate: undefined,//运营毛六利润率(减退款)低于
        yyProfit6AfterRate: undefined,//运营毛六利润率(减退款)等于
      }
    }
  },

  watch: {
    ListInfo: {
      handler: function (newValue, oldValue) {
        this.$emit("update:valueChanged", newValue);
      },
      deep: true
    }
  },

  async mounted() {

  },
  methods: {
    onRevealingMethod(e) {
      const valuesToCheck = [
        '毛三利润率(发生)',
        '毛四利润率(发生)',
        '毛五利润率(发生)',
        '毛六利润率(发生)',
        '运营毛三利润率(减退款)',
        '运营毛四利润率(减退款)',
        '运营毛五利润率(减退款)',
        '运营毛六利润率(减退款)'
      ];
      valuesToCheck.forEach(item => {
        if (e.includes(item)) {
          if (!this.selectProfitrates.includes(item)) {
            this.selectProfitrates.push(item);
          }
        } else {
          const index = this.selectProfitrates.indexOf(item);
          if (index !== -1) {
            this.selectProfitrates.splice(index, 1);
          }
        }
      });
    },
    onTrichosan(e) {
      this.ListInfo.maxProfit3Rate = undefined
      this.ListInfo.minProfit3Rate = undefined
      this.ListInfo.profit3Rate = undefined
    },
    onFourfoule(e) {
      this.ListInfo.maxProfit33Rate = undefined
      this.ListInfo.minProfit33Rate = undefined
      this.ListInfo.profit33Rate = undefined
    },
    onMaoFive(e) {
      this.ListInfo.maxProfit5Rate = undefined
      this.ListInfo.minProfit5Rate = undefined
      this.ListInfo.profit5Rate = undefined
    },
    onOperatingMargin(e) {
      this.ListInfo.maxyyProfit3AfterRate = undefined
      this.ListInfo.minyyProfit3AfterRate = undefined
      this.ListInfo.yyProfit3AfterRate = undefined
    },
    onOperatingQuarter(e) {
      this.ListInfo.maxyyProfit4AfterRate = undefined
      this.ListInfo.minyyProfit4AfterRate = undefined
      this.ListInfo.yyProfit4AfterRate = undefined
    },
    onOperatingmarginfive(e) {
      this.ListInfo.maxyyProfit5AfterRate = undefined
      this.ListInfo.minyyProfit5AfterRate = undefined
      this.ListInfo.yyProfit5AfterRate = undefined
    },
    onOperatingmarginsix(e) {
      this.ListInfo.maxyyProfit6AfterRate = undefined
      this.ListInfo.minyyProfit6AfterRate = undefined
      this.ListInfo.yyProfit6AfterRate = undefined
    },
    onMaosix(e) {
      this.ListInfo.maxProfit6Rate = undefined
      this.ListInfo.minProfit6Rate = undefined
      this.ListInfo.profit6Rate = undefined
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 60px;
}
</style>
