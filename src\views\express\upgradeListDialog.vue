<template>
  <el-dialog :title="title" :visible.sync="visible" width="80%" v-dialogDrag @close="handleClose">
    <div class="top">
      <el-input v-model="ListInfo.version" clearable placeholder="请输入版本号" class="search-input" />
      <el-select v-model="ListInfo.status" clearable placeholder="请选择状态" class="search-select">
        <el-option label="已启用" :value="1" />
        <el-option label="已禁用" :value="0" />
      </el-select>
      <el-button type="primary" @click="getList('search')">查询</el-button>
      <el-button type="primary" v-if="!isupgrade" @click="handleAdd">新增版本</el-button>
    </div>
    <vxetablebase
      ref="table"
      :that="that"
      :isIndex="true"
      :tablefixed="true"
      :hasexpand='true'
      @sortchange="sortchange"
      @select="selectChange"
      :tableData="tableData"
      :tableCols="tableCols"
      :isSelection="false"
      :isSelectColumn="false"
      style="width: 100%; margin: 0; height: 500px;"
      :loading="loading"
    >
      <template slot="right" width="100" v-if="!isupgrade">
        <vxe-column title="操作">
          <template #default="{ row }">
            <div style="display: flex; justify-content: center">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <div class="btnGroup" v-if="isupgrade">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="confirmUpgrade">确定</el-button>
            </div>
    </template>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="30%"
      v-dialogDrag
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本号"></el-input>
        </el-form-item>
        <el-form-item label="版本内容" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入版本内容"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-upload
            class="upload-demo"
            action="/api/uploadnew/file/UploadCommonFileAsync"
            :limit="1"
            :on-remove="removeFile"
            :file-list="fileList"
            accept="image/*"
            :on-success="handleUploadSuccess"
          >
            <el-tooltip
              class="item"
              effect="dark"
              content="可以上传图片或输入文字"
              placement="top-start"
            >
              <el-button size="small" type="primary">点击上传图片</el-button>
            </el-tooltip>
            <div v-if="form.remark" style="margin-top: 10px;">
              <template v-if="isImageUrl(form.remark)">
                <img :src="form.remark" style="width: 100px; height: 100px; cursor: pointer; object-fit: contain;" @click="previewImage">
              </template>
              <!-- <template v-else>
                <div class="text-content">{{ form.remark }}</div>
              </template> -->
            </div>
          </el-upload>
          <el-input
            v-if="!isImageUrl(form.remark)"
            type="textarea"
            v-model="form.remark"
            placeholder="请输入备注文字"
            style="margin-top: 10px;"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            active-value="1"
            inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <div class="btnGroup">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getUpgradeList, upgradeListSubmit, deleteUpgrade, upgradeListStatus } from '@/api/order/orderData';

const tableCols = [
  { type: 'checkbox', label: '' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'version', label: '版本号' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'fileName', label: '版本内容' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => row.status === 1 ? '已启用' : '已禁用' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark',type:'html', label: '备注', formatter: (row) => {
    if (!row.remark) return '-';
    // 检查是否为图片URL（可以根据实际情况调整判断逻辑）
    const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(row.remark);
    if (isImage) {
      return `<img src="${row.remark}" style="width: 50px; height: 50px; cursor: pointer; object-fit: contain;" onclick="window.open('${row.remark}', '_blank')">`;
    }
    return row.remark;
  }},
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createTime', label: '创建时间' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'createUserName', label: '创建人' },
];

export default {
  name: "upgradeListDialog",
  components: {
    MyContainer,
    vxetablebase,
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isupgrade: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '升级列表'
    },
  },
  data() {
    return {
      that: this,
      dialogTitle: this.title,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        version: '',
        status: null
      },
      selectList: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      dialogVisible: false,
      dialogTitle: '',
      form: {
        version: '',
        file: null,
        remark: '',
        id: null,
        fileName: '',
        fileType: '',
        fileSize: 0,
        status: "1"
      },
      rules: {
        version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
      },
      fileList: [],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.getList();
      }
    }
  },
  methods: {
    async getList(type) {
      this.loading = true;
      // 清空选择状态
      this.selectList = [];
      try {
        const { data, success } = await getUpgradeList(this.ListInfo);
        if (success) {
          this.tableData = data.list;
          this.total = data.total;
        } else {
          this.$message.error('获取列表失败');
        }
      } catch (error) {
        this.$message.error('获取列表失败');
      } finally {
        this.loading = false;
      }
    },
    handleClose() {
      this.$emit('close');
    },
    confirmUpgrade() {
      if (this.selectList.length > 1) {
        this.$message.warning('只允许选择一个版本进行升级！');
        return;
      }
      if (this.selectList.length == 0) {
        this.$message.warning('请选择一个版本进行升级！');
        return;
      }
      this.$emit('sendConfigCommand',this.selectList[0].fileName + ',' + this.selectList[0].version);
      // 发送指令后清空选择状态
      this.selectList = [];
      // 刷新表格数据
      this.getList();
      // 关闭对话框
      this.$emit('update:visible', false);
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList();
    },
    selectChange(val) {
      this.selectList = val
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList();
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop;
        this.ListInfo.isAsc = order.indexOf("descending") === -1;
        this.getList();
      }
    },
    handleAdd() {
      this.dialogTitle = '新增版本';
      this.form = {
        version: '',
        file: null,
        remark: '',
        id: null,
        fileName: '',
        fileType: '',
        fileSize: 0,
        status: "1"
      };
      this.fileList = [];
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑版本';
      this.form = { ...row };
      this.form.status = row.status.toString();

      // 初始化fileList，如果remark是图片URL，则添加到fileList中
      this.fileList = [];
      if (this.isImageUrl(row.remark)) {
        this.fileList = [{
          name: '已上传图片',
          url: row.remark
        }];
      }

      this.dialogVisible = true;
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该版本吗？', '提示', {
          type: 'warning',
        });
        const { success } = await deleteUpgrade({ids: row.id});
        if (success) {
          this.$message.success('删除成功');
          this.getList();
        } else {
          this.$message.error('删除失败');
        }
      } catch (error) {
        console.log('取消删除');
      }
    },
    isImageUrl(url) {
      if (!url) return false;
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.success) {
        // 更新form.remark为新上传图片的URL
        this.form.remark = response.data.url;
        // 更新fileList，确保UI显示最新上传的图片
        this.fileList = [{
          name: file.name,
          url: response.data.url
        }];
        // 强制更新视图
        this.$forceUpdate();
      } else {
        this.$message.error('图片上传失败');
      }
    },
    removeFile() {
      this.form.remark = '';
      this.fileList = [];
    },
    previewImage() {
      if (this.isImageUrl(this.form.remark)) {
        window.open(this.form.remark, '_blank');
      }
    },
    async handleSwitchChange(row) {
      try {
        const { success } = await upgradeListStatus({ id: row.id, status: row.status });
        if (!success) {
          this.$message.error('状态更新失败');
          row.status = row.status == "1" ? "0" : "1";
        } else {
          this.$message.success('状态更新成功');
        }
      } catch (error) {
        this.$message.error('状态更新失败');
        row.status = row.status == "1" ? "0" : "1";
      }
    },
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            const params = {
              version: this.form.version,
              filePath: this.form.file,
              fileName: this.form.fileName,
              fileType: this.form.fileType,
              fileSize: this.form.fileSize,
              remark: this.form.remark,
              status: this.form.status
            };
            if (this.form.id) {
              params.id = this.form.id;
            }

            const { success } = await upgradeListSubmit(params);

            if (success) {
              this.$message.success(`${this.form.id ? '修改' : '新增'}成功`);
              this.dialogVisible = false;
              this.getList();
            } else {
              this.$message.error(`${this.form.id ? '修改' : '新增'}失败`);
            }
          } catch (error) {
            this.$message.error(`${this.form.id ? '修改' : '新增'}失败`);
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .search-input,
  .search-select {
    width: 200px;
    margin-right: 10px;
  }
}

.btnGroup {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.text-content {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  word-break: break-all;
  max-height: 100px;
  overflow-y: auto;
}
</style>
