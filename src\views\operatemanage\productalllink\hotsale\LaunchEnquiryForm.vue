<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row >                  
                    <el-col :span="24">                    
                    
                        <p>竞品ID：{{form.goodsCompeteId}}</p>
                        <p>竞品标题：{{form.goodsCompeteName}}</p>
                        <p style="color:red">注意：只有启用的SKU才能询价，当前页面已自动过滤掉了禁用的SKU！</p>
                    </el-col>
                </el-row>
                
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="询价明细">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="skuTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='skuTableData' 
                                    :tableCols='skuTableCols' :loading="false" :isSelectColumn="false"
                                     rowkey="id" >
                                        <!-- center from jsonCols array  -->
                                        <template slot="right">
                                            <!-- right  -->                                           
                                            <el-table-column width="100" label="期望成本价" prop="hopeCostPrice" >
                                                <template slot-scope="scope">
                                                    <el-input-number v-model.number="scope.row.hopeCostPrice" 
                                                        style="width:80px" type="number" 
                                                        :min="0" :max="10000" :precision="3" :controls="false"  size="mini"                                                       
                                                    />      
                                                </template>
                                            </el-table-column> 
                                            <el-table-column width="130" label="每次大概进货数量" prop="estimatedQuantity" >
                                                <template slot-scope="scope">
                                                    <el-input-number v-model.number="scope.row.estimatedQuantity" 
                                                        style="width:110px" type="number" 
                                                        :min="0" :max="10000" :precision="0" :controls="false"  size="mini"                                                       
                                                    />        
                                                </template>
                                            </el-table-column> 

                                        </template>
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button type="primary" @click="onSave(true)">发起询价&关闭</el-button>                    
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    
    import { EnquiryLaunchAsync    } from '@/api/operatemanage/productalllink/alllink';
      
    const skuTableCols = [
        { istrue: true, prop: 'skuImgUrl', label: 'SKU图', width: '64', type: 'image' },        
        { istrue: true, prop: 'skuName', label: '规格名称', minwidth: '180'},//, width: '120'

    ];


    export default {
        name: "LaunchEnquiryForm",
        components: { MyContainer, MyConfirmButton, cesTable,    },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                skuTableCols: skuTableCols,
                total: 0,
                skuTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,
                goodschoiceVisible:false,
             
            };
        },
        async mounted() {

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.skuTableData && this.skuTableData.length > 0) {
                    rowsCount = this.skuTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },    
        },
        methods: {  
          
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData(data) {
                let mode=2;
              
                this.mode=mode;
                this.formEditMode = mode!=3;

                this.form={...data};
                this.skuTableData=[...data.eqList];               
            },
            async save() {
                let self=this;
                this.pageLoading = true;
                
                let errMsg='';
                if(this.skuTableData&& this.skuTableData.length>0){                
                  
                    this.skuTableData.forEach(element => {
                        // hopeCostPrice  estimatedQuantity
                        if(!(element.hopeCostPrice && element.hopeCostPrice>0)){
                            errMsg='请填写有效的期望成本价，0.01~10000间！';                            
                        }
                        else if(!(element.estimatedQuantity && element.estimatedQuantity>0)){
                            errMsg='请填写有效的每次大概进货数量，1~10000间！';                            
                        }
                    });
                    
                }else{
                    errMsg="没有可询价的SKU！"    ;               
                }

                if(errMsg){
                    this.$alert(errMsg);   
                    this.pageLoading = false; 
                    return false;
                }

                    console.log(self.form);

                let saveData = { 
                    chooseId:self.form.id,
                    goodsCompeteShortName:self.form.goodsCompeteShortName,
                    goodsCompeteName:self.form.goodsCompeteName,
                    skuList:self.skuTableData.map(sku=>{
                        return {
                            skuId:sku.id,
                            hopeCostPrice:sku.hopeCostPrice,
                            estimatedQuantity:sku.estimatedQuantity
                        }
                    })
                };              

                let rlt = await EnquiryLaunchAsync(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('询价成功！');  
                }
                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
