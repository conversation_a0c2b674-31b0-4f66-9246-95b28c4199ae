<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
    </el-form>
    <!--列表-->
    <vxetableNotFixNum ref="table" :that='that' style="height:93%;" :summaryarry="summaryarry" :isIndex='true'
      :hasexpand='false' @sortchange='sortchange' :tableData='inquirslist' @select='selectchange' :isSelection='false'
      :tableCols='tableCols' :loading="listLoading">
      <template slot='extentbtn'>
        <el-input v-model.trim="filter.groupName" placeholder="组" style="width:120px;" clearable disabled
          :maxlength="50" />
        <el-input v-model="filter.sname" v-model.trim="filter.sname" placeholder="姓名" style="width:120px;"
          disabled="true" :maxlength="50" />
        <el-input v-model="filter.startDate" style="width:120px;" disabled="true" />至
        <el-input v-model="filter.endDate" style="width:120px;" disabled="true" />
      </template>
    </vxetableNotFixNum>
    <!--分页-->
    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
  </my-container>
</template>
<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import vxetableNotFixNum from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { formatTime } from "@/utils";
import { getSPHPersonalInquirsList } from '@/api/customerservice/shipinhaoinquirs.js';

import Decimal from 'decimal.js';
function precision(number, multiple) {
  return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
  { istrue: true, prop: 'shopName', label: '店铺', width: '240', sortable: 'custom' },
  { istrue: true, prop: 'sname', label: '姓名', width: '80', sortable: 'custom' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询用户数', prop: 'inquirsCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '咨询会话数', prop: 'receiveCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟人工回复率', prop: 'threeSecondReplyRate', formatter: (row) => row.threeSecondReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '3分钟回复人数', prop: 'threeSecondReplyCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '平均回复时长（秒）', prop: 'responseTime' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '用户满意度', prop: 'satisDegree', formatter: (row) => row.satisDegree.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '满意人数', prop: 'satisDegreeCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复率', prop: 'noReplyRate', formatter: (row) => row.noReplyRate.toFixed(2) + "%" },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '不回复人数', prop: 'noReplyCount', },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '出勤人次', prop: 'dutyCount' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '人均接待量', prop: 'perReceptionCount' },
];

export default {
  name: "sphInquirsstatisticsusershop",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar, vxetableNotFixNum },
  props: [""],
  data() {
    return {
      that: this,
      filter: {
        groupType: 0,
        inquirsType: 0,
        sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
      },
      inquirslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "shopName", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
    };
  },
  async mounted() {

  },
  methods: {
    loadData(args) {
      this.filter = args;
      this.onSearch();
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getinquirsList();
    },
    getParam() {
      if (this.filter.sdate) {
        this.filter.startDate = this.filter.sdate[0];
        this.filter.endDate = this.filter.sdate[1];
      }
      else {
        this.filter.startDate = null;
        this.filter.endDate = null;
      }
      this.filter.matchType = 1;
      const para = { ...this.filter };
      let pager = this.$refs.pager.getPager();

      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      return params;
    },
    async getinquirsList() {
      let params = this.getParam();
      this.listLoading = true;
      const res = await getSPHPersonalInquirsList(params);
      this.listLoading = false;
      this.total = res.data.total;
      this.inquirslist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>
