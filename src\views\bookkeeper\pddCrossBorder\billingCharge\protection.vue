<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :clearable="false" :value-format="'yyyy-MM-dd'" @change="changeTime" clearable>
        </el-date-picker>
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.skc" maxlength="50" placeholder="SKC" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.proCode" maxlength="50" placeholder="SKU ID" clearable class="publicCss" />


        <el-select filterable clearable v-model="ListInfo.financialType" placeholder="财务类型" style="width: 120px">
          <el-option label="商品环保费" value="商品环保费"> </el-option>
          <el-option label="物流包装环保费" value="物流包装环保费"> </el-option>
        </el-select>

        <el-button type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('temu_protection_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'protection202408041404'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;margin: 0"
      v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入环保费数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs'
import { importPddTemuBillingChargeAsync, getBillingCharge_HbKf_PddTemu, batchDeleteBillingCharge_PddTemuAsync } from '@/api/bookkeeper/reportdayV2'
import { billingCharge_HbKf_PddTemu_Export } from '@/api/bookkeeper/crossBorderV2'

const tableCols = [
  { istrue: true, prop: 'shopCode', label: '店铺ID', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'yearMonthDay', label: '日期', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'shopName', label: '店铺', width: '190', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'returnPackageNumber', label: '批次编号', width: '190', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'financialType', label: '财务类型', width: '190', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'hwbNumber', label: '账务时间', width: 'auto', align: 'center', },
  { istrue: true, prop: 'billingType', label: '计费类型', width: 'auto', align: 'center', },
  { istrue: true, prop: 'deductionFeeCycle', label: '扣费费用周期', width: 'auto', align: 'center', sortable: 'custom', },
  // { istrue: true, prop: 'skuid', label: 'SKU ID', width: 'auto', align: 'center', },
  // { istrue: true, prop: 'skc', label: 'SKC', sortable: 'custom', width: 'auto', align: 'center', },
  // { istrue: true, prop: 'goodsCode', sortable: 'custom', sortable: 'custom', label: 'SKU货号', width: 'auto', align: 'center', },
  { istrue: true, prop: 'salesSite', label: '销售站点', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'hbCategory', label: '环保品类', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'billingUnitPrice', label: '计费单价(EUR)', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'chargeQuantity', label: '计费数量', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'amountDeducted', label: '已扣费金额(CNY)', width: 'auto', align: 'center', sortable: 'custom', },
  {
    istrue: true, type: 'button', label: '操作', width: '60',
    btnList: [
      { label: "批次删除", handle: (that, row) => that.onDeleteOperation(row) },
    ]
  },
]
export default {
  name: "protection",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      summaryarry: {},
      yearMonthDay: null,//导入日期
      fileparm: {},//上传文件参数
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        financialType: null
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async onDeleteOperation(row) {
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        var res = await batchDeleteBillingCharge_PddTemuAsync({ batchNumber: row.batchNumber, billingType: 2 })
        if (res?.success) {
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
        }
      }).catch(() => {
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("billingType", 2);
      form.append("yearMonthDay", this.yearMonthDay);
      var res = await importPddTemuBillingChargeAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges != null && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getBillingCharge_HbKf_PddTemu(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjBillCharges/TEMU全托环保费.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；
      if (this.timeRanges != null && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      var res = await billingCharge_HbKf_PddTemu_Export(this.ListInfo);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
