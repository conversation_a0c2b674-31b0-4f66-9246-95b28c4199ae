<template>
  <MyContainer>
    <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212">
      <template #buttons>
        <slot name="tbHeader"></slot>
      </template>
    </vxe-toolbar>
    <vxe-table :id="'underWay20240824'" border show-overflow="tooltip" show-header-overflow="tooltip"
      show-footer-overflow="tooltip" show-footer ref="xTable" height="100%"
      :sort-config="{ transform: true, sortMethod: customSortMethod }" :loading="loading" :data="tableData"
      :custom-config="{ storage: { fixed: true, sort: true, resizable: true, visible: true }, restoreStore: restoreStore, updateStore: updateStore }"
      @checkbox-all="selectAllEvent" @checkbox-change="selectChangeEvent" :treeProp="{}"
      :checkbox-config="{ highlight: true, checkMethod: checkMethod }" :cell-style="rowStyle"
      :column-config="{ resizable: true }"
      :row-config="{ height: 40, isCurrent: true, isHover: true, keyField: 'isRoot' }" :footer-method="footerMethod">
      <vxe-column type="checkbox" width="50"></vxe-column>
      <vxe-column type="seq" show-header-overflow width="90"></vxe-column>
      <vxe-column field="buyNo" title="采购单号" width="80" :align="'center'">
        <template #default="{ row }">
          <a :href="'https://trade.1688.com/order/new_step_order_detail.htm?orderId=' + row.aliOrderNo" target="_blank"
            style="color: blue;">{{ row.buyNo }}</a>
        </template>
      </vxe-column>
      <vxe-column field="createdTime" title="采购单建立时间" width="150" :align="'center'" sortable></vxe-column>
      <vxe-column field="styleCode" title="款式编码" width="80" :align="'left'">
        <template #default="{ row }">
          <span :style="{ color: row.styleCodeStatusColor == 1 ? 'red' : '' }">
            {{ row.styleCode }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="status" title="采购单状态" width="135" :align="'center'">
        <template #default="{ row }">
          <!-- :disabled="isView"  -->
          <el-select disabled v-if="(row.trackStatus == '已确认未发货' || row.trackStatus == '已发货') && !row.goodsCode"
            v-model="row.trackStatus" placeholder="请选择" row.statusColor
            :class="[row.statusColor == 1 ? 'custom-select' : 'custom-select1']" @change="odifySingledata(row)">
            <!-- <el-option key="已提交审批中" label="已提交审批中" value="已提交审批中" /> -->
            <el-option key="已确认未发货" label="已确认未发货" value="已确认未发货" />
            <el-option key="已发货" label="已发货" value="已发货" />
            <!-- <el-option key="已完成" label="已完成" value="已完成" /> -->
          </el-select>
          <span v-else>{{ row.trackStatus }}</span>
        </template>
      </vxe-column>
      <vxe-column field="purchaseInType" title="入库状态" width="80" :align="'center'">
        <template #default="{ row }">
          <el-button type="text" @click="getPurchaseInTypePage(row)"> {{
            row.purchaseInType }}</el-button>
        </template>
      </vxe-column>
      <vxe-column field="countStr" title="商品总量" width="80" :align="'center'">
        <template #default="{ row }">
          <span>{{ row.countStr[0] }}/{{ row.countStr[1] }}</span>
        </template>
      </vxe-column>
      <vxe-column field="amount" title="总金额" width="75" :align="'center'"
        v-if="checkPermission('OperationPersonnel')"></vxe-column>
      <vxe-column field="newConsignmentType" title="发货方式" width="200" :align="'center'">
        <template #default="{ row, $rowIndex }">
          <el-select v-model="row.consignmentTypesListDb" placeholder="请选择" v-if="row.isEdit" filterable multiple
            clearable collapse-tags>
            <el-option v-for="item in consignmentTypesListDb" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <span v-else>{{ row.newConsignmentType }}</span>
        </template>
      </vxe-column>
      <vxe-column field="orderNumber" title="单号" width="150" :align="'left'">
        <template #default="{ row, $rowIndex }">
          <el-input v-model="row.orderNumber" placeholder="单号(最多50个字符)" maxlength="50" clearable v-if="row.isEdit" />
          <span v-else-if="row.orderNumberColor" :style="{color:row.orderNumberColor}" @click="viewLogisticsLog(row)">{{ row.orderNumber }}</span>
          <span v-else @click="viewLogisticsLog(row)">{{ row.orderNumber }}</span>
        </template>
      </vxe-column>
      <vxe-column field="expectedDeliveryTime" title="预计发货/到货时间" width="200" :align="'center'">
        <template #default="{ row, $rowIndex }">
          <!-- <el-date-picker v-model="row.expectedDeliveryTime" type="date" placeholder="选择日期" style="width: 80%;"
            v-if="row.isEdit && row.trackStatus == '已发货'" value-format="yyyy-MM-dd" />
          <span v-else>{{ row.expectedDeliveryTime }}</span> -->

          <span>{{ row.trackStatus == '已发货' ? row.expectedDeliveryTime : row.trackStatus == '已确认未发货' ?
            formatDateYear(row.expectedSendTime) : '' }}</span>
        </template>
      </vxe-column>
      <vxe-column field="remarkImages" title="发货图" width="70">
        <template #default="{ row }">
          <div style="position: relative; display: inline-block;" v-if="row.remarkImages">
            <el-image class="custom-image" slot="reference" :src="row.remarkImages[0] || ''" fit="fill"
              :preview-src-list="row.remarkImages != '' ? row.remarkImages : ''" style="width: 40px; height: 38px;">
            </el-image>
            <span class="circle-badge">
              {{ row.remarkImages.length }}
            </span>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="remark" title="备注" width="200" :align="'left'">
        <template #default="{ row }">
          <el-input v-model="row.remark" style="width:80%;" placeholder="备注内容" maxlength="200" type="textarea" :rows="1"
            resize="none" v-if="row.isEdit" />
          <div v-else>{{ row.remark }}</div>
        </template>
      </vxe-column>
      <!-- <vxe-column field="" title="备注" width="60"  :align="'center'">
        <template #default="{ row }">
          <i v-if="row.children" class="vxe-icon-flag-fill" style="font-size: 14px;cursor: pointer;"
            :style="{ color: (row.remark || row.remarkImages) ? '#F56C6C' : '#808080' }" slot="reference"
            @click="remarkpopupclick(row)">
          </i>
        </template>
      </vxe-column> -->
      <vxe-column field="lastTrackTime" title="最新操作日期" width="130" :align="'center'">
        <template #default="{ row }">
          <div>
            {{ formatDate(row.lastTrackTime) }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="brandId" title="采购员" width="90" show-overflow="tooltip" :align="'center'" sortable>
        <template #default="{ row }">
          <span>{{ row.brandName }}</span>
        </template>
      </vxe-column>
      <vxe-column field="wmsId" title="仓库" width="150" show-overflow="tooltip" :align="'left'">
        <template #default="{ row }">
          <span>{{ row.wmsName }}</span>
        </template>
      </vxe-column>
      <vxe-column field="inTransitSpanAvg" title="平均在途时长(天)" width="150" show-overflow="tooltip" :align="'center'">
        <template #default="{ row }">
          <span>{{ row.inTransitSpanAvgStr }}</span>
        </template>
      </vxe-column>
      <vxe-column field="logs" title="日志" width="60" :align="'center'">
        <template #default="{ row }">
          <i v-if="row.children" class="el-icon-document" style="cursor: pointer;" @click="logs(row)"></i>
        </template>
      </vxe-column>
      <vxe-column title="操作" field="opert" width="90" show-overflow="tooltip" :align="'center'">
        <template #default="{ row, $rowIndex }">
          <!-- <el-button type="text" v-if="!row.isEdit" @click="row.isEdit = !row.isEdit">编辑</el-button> -->
          <el-button type="text" v-if="!row.isEdit" @click="odifySingledata(row)">编辑</el-button>
          <el-button type="text" v-else="row.isEdit" @click="returnEdit(row, $rowIndex)">取消</el-button>
          <el-button type="text" @click="rowSubmit(row)" v-throttle="1000" v-if="row.isEdit">保存</el-button>
        </template>
      </vxe-column>
      <vxe-column field="is1688" title="是否1688" width="100" :align="'center'" :visible="false">
        <template #default="{ row }">
          {{ row.is1688 == true ? '是' : row.is1688 == false ? '否' : '' }}
        </template>
      </vxe-column>
      <vxe-column field="indexNo" title="erp编码" width="75" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="payAccount" title="采购账号" width="100" :align="'center'" sortable :visible="false"></vxe-column>
      <vxe-column field="orderNo" title="订单编号" width="110" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="telePhoneNumber" title="电话" width="110" :align="'center'" :visible="false">
        <template #default="{ row }">
          <div>
            {{ row.telePhoneNumber ? row.telePhoneNumber : '' }}
          </div>
        </template>
      </vxe-column>

      <vxe-column field="isSigned" title="是否签收" width="110" :align="'center'" :visible="false">
        <template #default="{ row }">
          <div>
            {{ row.isSigned == null ? '' : row.isSigned ? '是' : '否' }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="lastNeedTrackTime" title="最近一次需要更新日期" width="120" :align="'center'" sortable :visible="false">
      </vxe-column>
      <vxe-column field="image" title="商品图片" width="80" :align="'center'" :visible="false">
        <template #default="{ row }" :visible="false">
          <el-image style="width: 60px; height: 60px" :src="row.image" :preview-src-list="[row.image]">
          </el-image>
        </template>
      </vxe-column>
      <vxe-column field="price" title="单价(元)" width="80" :align="'center'" v-if="checkPermission('OperationPersonnel')"
        :visible="false">
      </vxe-column>
      <vxe-column field="inventoryDay" title="可用库存天数" width="110" :align="'center'" :visible="false">
      </vxe-column>
      <vxe-column field="sellStock" title="实际可用数" width="95" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="trackUserName" title="跟单员" width="130" :align="'center'" sortable
        :visible="false"></vxe-column>
      <vxe-column field="startDept" title="发起部门" width="120" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="abnormalStatus" title="异常状态" width="90" show-overflow="tooltip" :align="'center'"
        :visible="false">
        <template #default="{ row }">
          <span>{{ row.abnormalStatusStr }}</span>
        </template>
      </vxe-column>

    </vxe-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <!-- <el-dialog title='备注' :visible.sync="remarkPopup" width="40%" v-dialogDrag>
      <div style="height: 150px;">
        <div>
          <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 3 }" placeholder="请输入备注内容" maxlength="200"
            :disabled="isView" v-model="remarklist.remark" style="width: 400px;" resize="none" :rows="3">
          </el-input>
        </div>
        <div style="margin-top: 10px;">
          <uploadimgFile ref="uploadimgFile" v-if="remarkPopup" :accepttyes="accepttyes" :isImage="true"
            :uploadInfo="remarklist.remarkImages" :keys="[1, 1]" @callback="getImg" :imgmaxsize="9" :limit="9"
            :noDel="isView && (remarklist.remarkImages.length == 9)" :multiple="true" :disabled="isView">
          </uploadimgFile>
        </div>
      </div>
      <div class="btnGroup">
        <el-button @click="remarkPopup = false">取消</el-button>
        <el-button type="primary" @click="submit" v-throttle="2000" :loading="savenoteLoading">{{ (savenoteLoading ?
          '保存中' :
          '保存备注') }}</el-button>
      </div>
    </el-dialog> -->

    <el-dialog title='日志' :visible.sync="logDetailsVisible" width="65%" v-dialogDrag>
      <div style="height: 400px;">
        <logDetails ref="logDetails" v-if="logDetailsVisible" />
      </div>
    </el-dialog>

    <el-dialog :title='dialogTitle' :visible.sync="statusVisable" width="30%" v-dialogDrag @close="formCloseEvent"
      :close-on-click-modal="false">
      <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
        v-if="statusVisable">
        <el-form-item label="采购单状态" prop="trackStatus" v-if="blanking">
          <el-select v-model="ruleForm.trackStatus" placeholder="采购单状态" clearable style="width:220px"
            @change="changeTrackStatus">
            <!-- <el-option key="已提交审批中" label="已提交审批中" value="已提交审批中" /> -->
            <el-option key="已确认未发货" label="已确认未发货" value="已确认未发货" />
            <el-option key="已发货" label="已发货" value="已发货" />
            <!-- <el-option key="已完成" label="已完成" value="已完成" /> -->
          </el-select>
        </el-form-item>
        <el-form-item label="发货方式" :prop="'consignmentTypes'" v-if="blanking && (ruleForm.trackStatus == '已发货')">
          <el-select v-model="ruleForm.consignmentTypes" placeholder="发货方式" clearable filterable @change="checktrue"
            style="width:220px">
            <el-option v-for="item in consignmentTypesListDb" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="单号" :prop="ruleForm.consignmentTypes == '物流' ? '' : 'orderNumber'"
          v-if="blanking && (ruleForm.trackStatus == '已发货')">
          <el-input v-model="ruleForm.orderNumber" @input="handleInput" autocomplete="off" style="width:220px"
            placeholder="单号" maxlength="30" />
        </el-form-item>
        <el-form-item label="电话" :prop="''" v-if="blanking && (ruleForm.trackStatus == '已发货')">
          <el-input v-model="ruleForm.telePhoneNumber" style="width:220px" placeholder="电话" maxlength="16" />
        </el-form-item>

        <el-form-item label="图片" :prop="!blanking || ispasstwo ? '' : 'remarkImages'"
          v-if="(ruleForm.trackStatus == '已发货')">
          <uploadimgFile ref="uploadimgFile" v-if="statusVisable" :accepttyes="accepttyes" :isImage="true"
            :uploadInfo="picture" :keys="[1, 1]" @callback="getRuleFormPic" :imgmaxsize="9" :limit="9" :multiple="true">
          </uploadimgFile>
        </el-form-item>

        <el-form-item label="预计到货时间" :prop="'expectedDeliveryTime'" v-if="blanking && ruleForm.trackStatus == '已发货'"
          label-width="120px">
          <el-date-picker v-model="ruleForm.expectedDeliveryTime" type="date" placeholder="选择日期"
            value-format="yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="预计发货时间" prop="expectedSendTime" v-if="ruleForm.trackStatus == '已确认未发货'"
          label-width="120px">
          <el-date-picker v-model="ruleForm.expectedSendTime" type="date" placeholder="选择日期"
            value-format="yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="备注内容"
          :prop="remarkContent">
          <el-input v-model="ruleForm.remark" style="width:220px" placeholder="备注内容" maxlength="200" type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }" show-word-limit />
        </el-form-item>

        <el-form-item label="是否签收" prop="trackStatus" v-if="!blanking && (ruleForm.trackStatus == '已发货')">
          <el-switch v-model="ruleForm.isSigned" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item>
          <el-button @click="statusVisable = false">取消</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <vxe-modal title="入库状态" v-model="PurchaseInTypeIndexVisible" :esc-closable="true" :width='1200' :height='600'
      marginSize='-500'>
      <PurchaseInTypeIndex :buyNo="pageInfo.buyNo" ref="PurchaseInTypeIndex" v-if="PurchaseInTypeIndexVisible" />
    </vxe-modal>

    <el-dialog title="物流（快递）跟踪信息" :visible.sync="viewLogisticsTraceVisible" width="30%" v-dialogDrag>
      <div style="height: 500px;overflow-y: auto;">
        <el-form :model="logisticsTrace" status-icon ref="logisticsTrace">
          <el-form-item prop="logisticsBillNo">
            <el-row>
              <el-col style="font-size: 18px; text-align: center;">查询结果</el-col>
              <el-col style="font-size: 18px; text-align: center;">{{ logisticsTrace.logisticsBillNo }}</el-col>
            </el-row>
          </el-form-item>
          <el-form-item v-if="logisticsTrace.hasData" v-for="item in logisticsTrace.traceList">
            <el-row>
              <el-col :span="8">{{ item.acceptTime }}</el-col>
              <el-col :span="16">{{ item.remark }}</el-col>
            </el-row>
          </el-form-item>
          <el-form-item v-if="!logisticsTrace.hasData">
            <el-row>
              <el-col style="text-align: center;line-height: 300px;color: #8B8B8B;font-size: 18px;">暂无数据</el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import { getTableColumnCache, GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import PurchaseInTypeIndex from './PurchaseInTypeIndex.vue'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import logDetails from './components/logDetails.vue'
import dayjs from 'dayjs'
import { addDictionary, getDictionaryListPage, editDictionary, getDictionary } from '@/api/admin/dictionary'
import {
  getColumns,
  pageGetData,
  updateCount,
  updateConsignmentType,
  updateTrackStatus,
  updateRemark,
  updateRemarkImages,
  getPurchaseOrderTrack,
  updateConsignmentInfos,
  mergePurchaseOrderTrack,
  queryPurchaseOrderDetailAsync,
  getPurchaseOrderLogisticsTrace
} from '@/api/inventory/purchaseOrderTrack'
import { pageGetChangeLogs } from "@/api/inventory/basicgoods"
const shippingMethodList = [
  {
    label: '物流',
    value: '1'
  },
  {
    label: '快运',
    value: '2'
  },
  {
    label: '快递',
    value: '3'
  },
  {
    label: '货拉拉',
    value: '4'
  },
]
export default {
  name: "underWay",
  components: {
    MyContainer, vxetablebase, uploadimgFile, logDetails, PurchaseInTypeIndex
  },
  data() {
    return {
      blanking: true,
      formSaving: true,
      shippingMethodList,
      quantityStorage: null,
      savenoteLoading: false,
      remarklist: {
        remark: null,
        remarkImages: [],
        id: null,
      },
      isView: true,
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      remarkPopup: false,
      quantity: true,
      that: this,
      ListInfo: {

      },
      ispass: false,
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      pageSize: 50,
      loading: false,
      summaryarry: {},//汇总
      pickerOptions,
      logDetailsVisible: false,
      ispasstwo: true,
      lastSortArgs: {
        field: "",
        order: "",
      },
      ruleForm: {
        id: null,
        consignmentTypes: '',
        // consignmentTypesList: [],
        orderNumber: '',
        telePhoneNumber: '',
        remark: '',
        trackStatus: '',
        // picture: [],
        remarkImages: '',
        isSigned: false,
        expectedDeliveryTime: ''
      },
      consignmentTypesList: [],
      picture: [],
      chatUrls: [],
      statusVisable: false,
      rules: {
        trackStatus: [
          { required: true, message: '请选择采购单状态', trigger: 'change' }
        ],
        orderNumber: [
          { required: true, message: '请输入单号', trigger: 'blur' },
          { max: 20, message: '单号长度不能超过20位', trigger: 'blur' }
        ],
        telePhoneNumber: [
          { required: true, message: '请输入电话', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入备注内容', trigger: 'blur' }
        ],
        remarkImages: [
          { required: true, message: '请上传图片', trigger: 'blur' }
        ],
        expectedDeliveryTime: [
          { required: true, message: '请选择预计到货时间', trigger: 'blur' }
        ],
        expectedSendTime: [
          { required: true, message: '请选择预计发货时间', trigger: 'blur' }
        ],
        consignmentTypes: [
          { required: true, message: '请选择发货方式', trigger: 'blur' }
        ]
      },
      dialogTitle: '',
      ids: [],
      consignmentTypesListDb: [],
      historyList: [],
      pageInfo: {
        buyNo: '',
      },
      PurchaseInTypeIndexVisible: false,
      viewLogisticsTraceVisible: false,
      logisticsTrace:{
        logisticsBillNo: null,
        traceList: [],
        hasData: false
      }
    }
  },
  created() {
    //手动将表格和工具栏进行关联
    this.$nextTick(() => {
      this.$refs.xTable.connect(this.$refs.xToolbar)
    })
  },
  async mounted() {
    this.getConsignmentTypes()
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
    const { data } = await getUserInfo()
    if (data.brandId && data.brandId > 0) {
      this.isView = false
    }
  },
  methods: {
    async rowSubmit(row) {
      if (!row.orderNumber) return this.$message.error('单号不能为空')
      if (!row.remark) return this.$message.error('备注不能为空')
      if (row.trackStatus == '已发货' && !row.expectedDeliveryTime) return this.$message.error('采购单状态为已发货时预计到货时间不能为空')
      let params = {}
      const { data, success } = await getPurchaseOrderTrack({ id: row.id, })
      if (success) {
        params = data ? { ...params, ...data } : params
        params.purchaseOrderId = row.id
        params.trackStatus = row.trackStatus
        params.orderNumber = row.orderNumber
        params.consignmentTypes = row.consignmentTypesListDb ? row.consignmentTypesListDb.join(',') : ''
        params.expectedDeliveryTime = row.expectedDeliveryTime
        params.remark = row.remark
        const { success } = await mergePurchaseOrderTrack(params)
        if (success) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          await this.getList('', this.ListInfo, false)
        }
      }
    },
    returnEdit(row, i) {
      row.isEdit = !row.isEdit
      const res = this.historyList[i]
      this.$set(this.tableData[i], 'trackStatus', res.trackStatus)
      this.$set(this.tableData[i], 'orderNumber', res.orderNumber)
      this.$set(this.tableData[i], 'consignmentTypesListDb', res.consignmentTypesListDb)
      this.$set(this.tableData[i], 'expectedDeliveryTime', res.expectedDeliveryTime)
      this.$set(this.tableData[i], 'consignmentTypes', res.consignmentTypes)
      this.$set(this.tableData[i], 'remark', res.remark)
    },
    changeTrackStatus(val) {
      console.log(val)
      this.ruleForm.expectedDeliveryTime = ''
      this.$set(this.ruleForm, 'consignmentTypes', '')
      this.$set(this.ruleForm, 'orderNumber', '')
      this.$set(this.ruleForm, 'telePhoneNumber', '')
      this.$set(this.ruleForm, 'remark', '')
      this.$set(this.ruleForm, 'remarkImages', '')
      this.$set(this.ruleForm, 'isSigned', false)
      this.$set(this.ruleForm, 'expectedDeliveryTime', '')
      this.$set(this.ruleForm, 'expectedSendTime', '')
      // this.$set(this.ruleForm, 'consignmentTypes', '')
      // this.$set(this.ruleForm, 'consignmentTypesList', [])
      // this.$set(this.ruleForm, 'picture', [])
      this.$set(this, 'picture', [])
      this.$set(this.ruleForm, 'chatUrls', [])

      if (val == '已发货') {
        this.ispass = false;
      }
    },
    checktrue(val) {
      if (val != '物流' && val != '货拉拉') {
        this.ispass = false;
        this.ispasstwo = true;
      } else {
        this.ispass = true;
        this.ispasstwo = false;
      }
    },
    getPurchaseInTypePage(row) {
      console.log(row, 'row');
      this.pageInfo.buyNo = row.buyNo
      this.PurchaseInTypeIndexVisible = true
    },
    async getConsignmentTypes() {
      const { data, success } = await getDictionaryListPage({ name: 'consignmentTypes' })
      if (success) {
        const res = data.list.filter(item => item.code == 'purchaseOrderTrack')[0]
        this.consignmentTypesListDb = res.value ? res.value.split(',').map(item => {
          return {
            label: item,
            value: item
          }
        }) : []
      }
    },
    formatDate(row) {
      return row ? dayjs(row).format('MM-DD HH:mm:ss') : ''
    },
    formatDateYear(row) {
      return row ? dayjs(row).format('YYYY-MM-DD') : ''
    },
    //校验
    handleInput(event) {
      const value = event
      this.ruleForm.orderNumber = value.replace(/[^a-zA-Z0-9]/g, '');
    },
    async restoreStore({ id, type, storeData }) {
      let resp = await GetVxeTableColumnCacheAsync({ tableId: id });
      let store = null;
      if (resp && resp.success && resp.data) {
        store = JSON.parse(resp.data);
      }
      return store ?? storeData;
    },
    async updateStore({ id, type, storeData }) {
      await SetVxeTableColumnCacheAsync({ tableId: id, ColumnConfig: JSON.stringify(storeData) });
    },
    rowStyle({ row, column }) {
      if (column.field == 'inventoryDay') {
        if (row.inventoryStatusColor == 1) {
          return {
            background: 'red',
            color: '#fff',
            border: '1px solid #fff',
            boxSizing: 'border-box'
          }
        }
      }
    },
    clearCheckBox() {
      this.$refs.xTable.clearCheckboxRow()
      this.ids = []
    },
    checkMethod({ row }) {
      if (row.goodsCode) {
        return false
      } else {
        return true
      }
    },
    selectAllEvent({ checked }) {
      const records = this.$refs.xTable.getCheckboxRecords()
      this.$emit('oddNumber', records)
      this.ids = records.map(item => item.id)
      this.$emit('getIds', this.ids)
      this.$emit('getSelectProp', records)
    },
    selectChangeEvent({ checked }) {
      const records = this.$refs.xTable.getCheckboxRecords()
      this.$emit('oddNumber', records)
      this.ids = records.map(item => item.id)
      this.$emit('getIds', this.ids)
      this.$emit('getSelectProp', records)
    },
    getSelectEvent() {
      let selectRecords = this.$refs.xTable.getCheckboxRecords()
      VXETable.modal.alert(selectRecords.length)
    },
    formCloseEvent() {
      //   if (this.formSaving) {
      //     this.tableData.forEach(item => {
      //       if (item.id == this.ruleForm.id) {
      //         item.trackStatus = item.diffstatus
      //       }
      //     })
      //   }
    },
    //获取数据详细信息
    async getOrderTrack(id, goodsCode, title, trackStatus) {
      this.ruleForm = {
        id: null,
        consignmentTypes: '',
        expressNo: '',
        phone: '',
        remake: '',
        trackStatus: '',
        remarkImages: '',
        isSigned: false,
        expectedDeliveryTime: ''
      }
      // this.consignmentTypesList = []
      this.picture = []
      this.chatUrls = []
      this.dialogTitle = title
      const { data, success } = await getPurchaseOrderTrack({ id, goodsCode })
      if (success) {
        // this.ruleForm = data ? data : {}
        if (data) {
          data.expectedDeliveryTime = data.expectedDeliveryTime ? dayjs(data.expectedDeliveryTime).format('YYYY-MM-DD') : ''
          this.ruleForm = data
          // this.consignmentTypesList = data.consignmentTypes ? data.consignmentTypes.split(',').map(item => {
          //   return this.consignmentTypesListDb.find(i => i.label == item)?.value ?? []
          // }) : []
          this.checktrue(this.ruleForm.consignmentTypes);
          this.picture = data.remarkImages ? data.remarkImages.split(',').map(item => {
            return {
              url: item
            }
          }) : []
        }
        this.ruleForm.trackStatus = trackStatus
        this.ruleForm.id = id
        this.ruleForm.purchaseOrderId = id
        // this.consignmentTypesList = data.consignmentTypes ? data.consignmentTypes.split(',') : []
        //找出匹配的发货方式
        this.statusVisable = true
      }
    },
    async submitForm(formName) {
      if (this.ruleForm.trackStatus == '已发货') {
        if (!this.ruleForm.consignmentTypes) {
          this.$message.error('请填写发货方式');
          return;
        }
        if (!this.ruleForm.expectedDeliveryTime) {
          this.$message.error('请选择预计到货时间');
          return;
        }
        if (this.ruleForm.consignmentTypes == '物流' || this.ruleForm.consignmentTypes == '货拉拉') {
          // if ((!this.ruleForm.remark)) {
          //   this.$message.error('请填写备注');
          //   return;
          // }
          //图片
          if (!this.ruleForm.remarkImages) {
            this.$message.error('请上传图片');
            return;
          }
        }
        else {
          //单号
          if (this.ruleForm.orderNumber == 0 || !this.ruleForm.orderNumber) {
            this.$message.error('请填写单号');
            return;
          }
        }
        const { success } = await mergePurchaseOrderTrack(this.ruleForm)
        if (success) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.formSaving = false
          this.statusVisable = false
          await this.getList('', this.ListInfo, false)
        }
        return;
      }
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // this.ruleForm.consignmentTypes = this.consignmentTypesList ? this.consignmentTypesList.join(',') : ''
          // if (!/^[0-9]*$/.test(this.ruleForm.telePhoneNumber) && this.ruleForm.telePhoneNumber) {
          //   this.$message({ type: 'warning', message: '电话只能为数字,请重新输入!' });
          //   return
          // }
          // if (this.ruleForm.telePhoneNumber) {
          //   let num = Number(this.ruleForm.telePhoneNumber);
          //   this.ruleForm.telePhoneNumber = isNaN(num) ? 0 : num;
          // } else {
          //   this.ruleForm.telePhoneNumber = 0
          // }
          //找出匹配的发货方式
          // this.ruleForm.consignmentTypes = this.consignmentTypesList.map(item => {
          //   return this.consignmentTypesListDb.find(i => i?.value == item).label
          // }).join(',')
          const { success } = await mergePurchaseOrderTrack(this.ruleForm)
          if (success) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.formSaving = false
            this.statusVisable = false
            await this.getList('', this.ListInfo, false)
          }
          return true;
        } else {
          console.log('表单校验失败!!');
          return false;
        }
      });
    },
    //修改采购单状态
    async odifySingledata(row) {
      let params = {}
      const { data, success } = await getPurchaseOrderTrack({ id: row.id })
      if (success) {
        params = data ? data : {}
        params.purchaseOrderId = row.id
        params.trackStatus = row.trackStatus
      }
      // const { success: success1 } = await mergePurchaseOrderTrack(params)
      // if (success1) {
      //   this.$message({
      //     type: 'success',
      //     message: '操作成功!'
      //   });
      //   await this.getList('', this.ListInfo, false)
      // }
      this.blanking = true
      //   await this.getOrderTrack(row.id, row.goodsCode, '变更状态', row.trackStatus)
      await this.getOrderTrack(row.id, row.goodsCode, '已发货的弹窗', row.trackStatus)
      this.checktrue(this.ruleForm.consignmentTypes);
    },
    //备注保存
    async submit() {
      this.savenoteLoading = true
      let remarkimages = this.remarklist.remarkImages.map(item => item.url).join(',')
      const { success } = await updateRemark({ id: this.remarklist.id, remark: this.remarklist.remark })
      const { success: success1 } = await updateRemarkImages({ id: this.remarklist.id, remarkImages: remarkimages })
      if (success && success1) {
        this.$message({
          type: 'success',
          message: '操作成功!'
        });
        this.savenoteLoading = false
        this.remarkPopup = false
        await this.getList('', this.ListInfo, false)
      }
    },
    //获取图片
    getImg(data) {
      if (data) {
        this.remarklist.remarkImages = data
      }
    },
    getRuleFormPic(data) {
      if (data) {
        this.picture = data
        this.ruleForm.remarkImages = data.map(item => item.url).join(',')
      }
    },
    //备注弹窗
    async remarkpopupclick(row) {
      await this.getOrderTrack(row.id, row.goodsCode, '备注', row.trackStatus)
      this.blanking = false
      // this.consignmentTypesList = []
      // this.ruleForm.orderNumber = null
      // this.ruleForm.telePhoneNumber = null
      // this.remarklist.remarkImages = []
      // if (row.remarkImages) {
      //   this.remarklist.remarkImages = row.remarkImages.map((item, i) => {
      //     return {
      //       url: item,
      //       fileName: item
      //     }
      //   })
      // }
      // this.remarklist.remark = row.remark
      // this.remarklist.id = row.id
      // this.remarkPopup = true
    },
    //日志弹窗
    async logs(row) {
      const params = {
        logicId: row.id,
        scene: 'purchaseOrderTrack',
      }
      this.logDetailsVisible = true
      this.$nextTick(() => {
        this.$refs.logDetails.getList('search', params);
      })
    },
    //发货方式修改
    deliveryMethod(row) {
      //父级不允许修改
      if (row.children) {
        return
      }
      let message = '';
      if (row.consignmentType == '物流发货') {
        message = '快递发货';
      } else if (row.consignmentType == '快递发货') {
        message = '物流发货';
      } else {
        return
      }
      this.$confirm(`确认修改物流状态为${message}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        row.consignmentType = message
        const { data, success } = await updateConsignmentType({ goodsCode: row.goodsCode, consignmentType: message, id: row.id })
        if (success) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          await this.getList('', this.ListInfo, false)
        }
      }).catch(() => {
      });
    },
    //数量修改保存
    async performancescoreverify(row) {
      if (row.countStr[0] < 0) {
        this.$message({
          type: 'warning',
          message: '数量不能小于0!'
        });
        return
      }
      if (!Number.isInteger(Number(row.countStr[0])) || Number(row.countStr[0]) < 0) {
        this.$message.error('请输入正整数');
        return
      }
      if (this.quantityStorage != row.countStr[0]) {
        const { data, success } = await updateCount({ goodsCode: row.goodsCode, count: row.countStr[0], id: row.id })
        if (success) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          await this.getList('', this.ListInfo, false)
        }
      }
      row.quantity = true
    },
    //数量修改
    quantityCheck(row) {
      //父级不允许修改
      if (row.children || this.isView) {
        return
      } else {
        row.quantity = false
        this.quantityStorage = row.countStr[0]
        this.$nextTick(() => {
          this.$refs.inputRef.focus();
        })
      }
    },
    //自定义排序
    async customSortMethod({ data, sortList }) {
      if (sortList && sortList.length > 0) {
        if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
          this.lastSortArgs = { ...sortList[0] };
          let a = {
            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
            prop: this.lastSortArgs.field
          };
          this.ListInfo.orderBy = a.prop
          this.ListInfo.isAsc = a.order.indexOf("descending") == -1 ? true : false
          await this.getList('', this.ListInfo, false)
        }
      }
    },
    //合计
    footerMethod({ columns, data }) {
      const sums = [];
      if (!this.summaryarry)
        return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
        return sums
      var hashj = false;
      columns.forEach((column, index) => {
        if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
          var sum = this.summaryarry[column.property + '_sum'];
          if (sum == null) return;
          sums[index] = sum
        }
        else sums[index] = ''
      });
      return [sums]
    },
    async getList(type, listInfo, cpt) {
      this.ListInfo = { ...listInfo, pageSize: this.pageSize }
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        // this.$nextTick(() => {
        this.$refs.pager.setPage(1)
        // })
        this.ListInfo = { ...listInfo, isCpt: cpt, ...this.ListInfo, pageSize: this.pageSize }
      }
      this.loading = true
      const { data: { list, total, summary, extData }, success } = await pageGetData(this.ListInfo)
      if (success) {
        list.forEach((item, i) => {
          item.isRoot = i
          item.diffstatus = item.status
          item.trackStatus = item.trackStatus ? item.trackStatus : item.status
          item.countStr = item.countStr.split('/');
          item.quantity = true;
          item.isEdit = false
          item.expectedDeliveryTime = item.expectedDeliveryTime ? dayjs(item.expectedDeliveryTime).format('YYYY-MM-DD') : ''
          if (item.remarkImages) {
            item.remarkImages = item.remarkImages.split(',')
          }
          item.consignmentTypesListDb = item.consignmentTypes ? item.consignmentTypes.split(',') : item.consignmentType ? item.consignmentType.split(',') : []
          item.newConsignmentType = item.consignmentTypes ? item.consignmentTypes : item.consignmentType
          item.children.forEach(child => {
            child.trackStatus = item.trackStatus ? item.trackStatus : item.status
            child.countStr = child.countStr.split('/');
            child.quantity = true;
            if (child.remarkImages) {
              child.remarkImages = child.remarkImages.split(',')
            }

          });
        });
        this.historyList = JSON.parse(JSON.stringify(list))
        this.$emit('onrevealing', extData?.hasBrendQuery);
        this.tableData = list
        this.total = total
        this.summaryarry = summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    async Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.pageSize = val
      await this.getList('search', this.ListInfo, false)
    },
    //当前页改变
    async Pagechange(val) {
      this.ListInfo.currentPage = val;
      await this.getList('', this.ListInfo, false)
    },
    viewLogisticsLog(row){
      if(!row.indexNo)
        return;
      this.logisticsTrace.logisticsBillNo = row.orderNumber;
      this.logisticsTrace.traceList = [];
      this.viewLogisticsTraceVisible = true;
      this.getLogisticsList(row.indexNo);
    },
    async getLogisticsList(indexNo){
      var res = await getPurchaseOrderLogisticsTrace(indexNo);
      if(res?.success){
        this.logisticsTrace.traceList = res.data;
        if (this.logisticsTrace.traceList.length > 0)
          this.logisticsTrace.hasData = true;
      }
      console.log(this.logisticsTrace.hasData,'hasData');
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .custom-image img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.btnGroup {
  margin-top: 60px;
  display: flex;
  justify-content: end;
}

::v-deep .custom-select .el-input__inner {
  background-color: #FFD942;
  color: #fff;
}

::v-deep .custom-select1 .el-input__inner {
  background-color: #409EFF;
  color: #fff;
}

/*  工具箱位置  */
.vxetoolbar20221212 {
  position: absolute;
  top: 0px;
  right: 0px;
  padding-top: 0;
  padding-bottom: 0;
  z-index: 999;
  background-color: rgb(255 255 255 / 0%);
}

.vxetoolbar20221212 ::v-deep .vxe-custom--wrapper {
  margin-left: 0px !important;
}

.circle-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  font-size: 12px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  border-radius: 50%;
}
</style>
