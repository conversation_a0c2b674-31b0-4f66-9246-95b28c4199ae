<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList" placeholder="分组" style="width:180px;" clearable filterable multiple collapse-tags>
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:180px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getgroupinquirsstatisticsList" />
        </template>
        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getDouYinGroup,
    getDouYinShopEfficiencyPageList, getDouYinShopEfficiencyChat, exportDouYinShopEfficiencyPageList
} from '@/api/customerservice/douyininquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    { istrue: true, prop: 'shopName', label: '店名', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '人工已接待会话量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '人工已接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate).toFixed(2) + "%" },
    { istrue: true, prop: 'noSatisfactions', label: '不满意人数', width: '70', sortable: 'custom', formatter: (row) => (row.noSatisfactions).toFixed(0) },
    { istrue: true, prop: 'threeResponseRate', label: '3分钟人工回复率', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponseRate).toFixed(2) + "%" },
    { istrue: true, prop: 'threeResponses', label: '3分钟回复人数', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponses).toFixed(2) },
    { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '80', sortable: 'custom', formatter: (row) => (row.responseTime).toFixed(2) },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate).toFixed(2) + "%" },
    { istrue: true, prop: 'satisfactions', label: '满意人数', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactions).toFixed(0) },
    { istrue: true, prop: 'salesvol', label: '客服销售额(元)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'payers', label: '支付人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate).toFixed(2) + "%" },
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '90', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props:["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
            },
            shopList: [],
            filterGroupList: [],
            userList: [],
            groupList: [],
            groupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            isleavegroup:this.partInfo,//是否离组
        };
    },
    watch:{
        partInfo(){
            this.isleavegroup = this.partInfo;
            this.getDouYinGroup();
        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        await this.getDouYinGroup();
        await this.getAllShopList();
    },
    methods: {
        async getDouYinGroup() {
            let groups = await getDouYinGroup({ groupType: 0 ,isleavegroup:this.isleavegroup});
            if (groups?.success && groups?.data && groups?.data.length > 0) {
                this.filterGroupList=groups.data;
            }
        },
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 6)
                    this.shopList.push(f);
            });
            console.log(this.shopList)
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getShopInquirsStatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getShopInquirsStatisticsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await getDouYinShopEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.groupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {
            let params = this.getParam();
            params.shopCode = row.shopCode;
            const res = await getDouYinShopEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            })
            this.dialogMapVisible.visible = true
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportDouYinShopEfficiencyPageList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音店效率统计(售前组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}


//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px !important;
}

</style>
