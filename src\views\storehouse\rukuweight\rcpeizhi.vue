<template>
    <div>
        <el-form label-position="right" label-width="90px" :model="formLabelAlign" :rules="addFormRules">
            <el-form-item label="入库类型:" v-if="peizhidata.operateType == '入库'" prop="inStockType">
                <el-select v-model="formLabelAlign.inStockType" placeholder="请选择" clearable>
                    <el-option label="全部入库" value="全部入库" />
                    <el-option label="部分入库" value="部分入库" />
                </el-select>
            </el-form-item>
            <el-form-item label="驳回类型:" v-if="peizhidata.operateType == '驳回'" prop="rejectType">
                <el-select v-model="formLabelAlign.rejectType" placeholder="请选择" clearable>
                    <el-option label="批量驳回" value="批量驳回" />
                    <el-option label="部分驳回" value="部分驳回" />
                </el-select>
            </el-form-item>
            <el-form-item label="凭证:" prop="evidence">
                <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="false" :ispaste="true"
                    :noDel="false" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                    @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
                </uploadimgFile>
            </el-form-item>
            <el-form-item label="备注：" >
                <el-input type="textarea" placeholder="请输入内容" v-model="formLabelAlign.remark" maxlength="30"
                    show-word-limit>
                </el-input>
            </el-form-item>
        </el-form>
        <div
            style="display: flex; justify-content: space-between; width: 100%; padding: 0 100px; box-sizing: border-box;">
            <el-button type="" @click="closefuc">取消</el-button>
            <el-button type="primary" @click="savesubmit">确定</el-button>
        </div>
    </div>
</template>

<script>
import { saveWarehousingOrderVideoWeightCompareConfig, setOrderVideoWeighingStatus, batchSetOrderVideoWeighingStatus } from '@/api/inventory/purchasequality.js'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";

export default {
    components: { uploadimgFile },
    name: "vendorSumIndex",
    props: ['peizhidata'],
    data() {
        return {
            formLabelAlign: {},
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',//上传格式
            chatUrls: [],
            editPriceVisible: true,
            addFormRules: {
                // remark: [{ required: true, message: '请输入', trigger: 'blur' }],
                rejectType: [{ required: true, message: '请输入', trigger: 'blur' }],
                inStockType: [{ required: true, message: '请输入', trigger: 'blur' }],
                evidence: [{ required: true, message: '请输入', trigger: 'blur' }],

            },
        };
    },
    mounted() {
        this.formLabelAlign = JSON.parse(JSON.stringify(this.peizhidata));
    },
    methods: {
        getImg(val) {
            this.formLabelAlign.evidence = val.map(item => {
                return {
                    fileName: item.fileName,
                    url: item.url
                }
            })
            console.log(this.formLabelAlign.evidence);
        },
        closefuc() {
            this.$emit('closedialog');
        },
        async savesubmit() {
            let params = {
                buyNo: this.formLabelAlign.buyNo,
                mainId: this.formLabelAlign.mainId,
                id: this.formLabelAlign.id,
                // operateType: this.formLabelAlign.operateType,
                inStockType: this.formLabelAlign.inStockType,
                operateType: this.peizhidata.operateType,
                rejectType: this.formLabelAlign.rejectType,
                evidence: JSON.stringify(this.formLabelAlign.evidence),
                remark: this.formLabelAlign.remark,
            }
            if (this.formLabelAlign.evidence.length == 0 || !this.formLabelAlign.evidence) return this.$message.error('请上传凭证');
            let res;
            if (this.peizhidata.parentId) {
                res = await setOrderVideoWeighingStatus(params);
            } else {
                res = await batchSetOrderVideoWeighingStatus(params);
            }
            if (!res.success) {
                return;
            }
            this.$message.success("保存成功");
            this.$emit('closedialog');
        }
    }
}
</script>
<style scoped lang="scss">
.el-form-item {
    justify-content: space-between;
    box-sizing: border-box;
}
</style>
