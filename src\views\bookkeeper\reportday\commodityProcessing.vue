<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div style="margin-right: 5px;">
          <inputYunhan ref="productGoodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
            width="180px" placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
            :maxlength="60000" @callback="productGoodsCodeCallback" title="商品编码">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')" style="width: 80px;">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
        <el-button type="success" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'commodityProcessing202501071330'" :tablekey="'commodityProcessing202501071330'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" @click="onDelete(row)"><span style="color: red;">删除</span></el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="ruleTitle" :visible.sync="editDialogVisible" width="20%" v-dialogDrag>
      <div style="padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
          v-if="editDialogVisible">
          <el-row :gutter="20">
            <el-form-item label="商品编码" :label-width="'125px'" prop="goodsCode">
              <el-input v-model.trim="ruleForm.goodsCode" placeholder="商品编码" maxlength="50" clearable
                :disabled="ruleTitle == '编辑'" class="editCss" />
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="工价" :label-width="'125px'" prop="labourPrice">
              <el-input-number v-model="ruleForm.labourPrice" :min="0" :max="999999" placeholder="工价" class="editCss"
                :controls="false" :precision="2" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSingleSave">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { pickerOptions } from '@/utils/tools'
import { importDmProductAsync, getDmProductList, addorEditksDmProduct, deleteDmProductBatchAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '300', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'labourPrice', label: '工价', },
]
export default {
  name: "commodityProcessing",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      ruleTitle: '新增',
      editDialogVisible: false,
      ruleForm: {
        goodsCode: '',
        labourPrice: undefined,
      },
      editrules: {
        goodsCode: [{ required: true, message: '商品编码不能为空', trigger: 'change' }],
        labourPrice: [{ required: true, message: '工价不能为空', trigger: 'change' }],
      },
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        goodsCode: '',
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    productGoodsCodeCallback(val) {
      this.ListInfo.goodsCode = val;
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteDmProductBatchAsync({ goodsCode: row.goodsCode })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addorEditksDmProduct(this.ruleForm)
          if (success) {
            this.$message.success('操作成功')
            this.editDialogVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.$refs.ruleForm.clearValidate();
      });
      this.ruleForm = {
        goodsCode: '',
        labourPrice: undefined,
      }
    },
    async onEdit(row) {
      this.onCleardataMethod()
      setTimeout(() => {
        this.ruleForm = JSON.parse(JSON.stringify(row))
        this.ruleTitle = '编辑'
        this.editDialogVisible = true
      }, 100)
    },
    onAddMethod() {
      this.onCleardataMethod()
      this.ruleTitle = '新增'
      this.editDialogVisible = true
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importDmProductAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getDmProductList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}

.editCss {
  width: 80%;
  margin-right: 5px;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
