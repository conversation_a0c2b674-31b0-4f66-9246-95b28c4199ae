<template>
    <section style="padding:10px;">
        <el-row :gutter="10">
            <el-col :span="6" class="toolbar roles">
                <el-card>
                    <template #header>
                        <div class="clearfix">
                            <span>角色</span>
                            <el-button :loading="loadingRoles" type="text" style="float: right; padding: 3px 0" @click="getRoles">刷新</el-button>

                        </div>
                    </template>
                    <!-- <el-tree ref="rolesTree" :indent="15" highlight-current :expand-on-click-node="false" :data="roles" :default-expand-all="isExpansion" node-key="id" :props="{label: 'name',children: 'children',isLeaf: 'hasChildren'}" @node-click="roleSelect">
                    </el-tree> -->
                    <el-table ref="rolesTree" :indent="10" :show-header="false" :data="roles" border lazy :load="rolesLoad" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="id" highlight-current-row style="width: 100%;" @row-click="roleSelect" @expand-change="handleNodeClick">
                        <el-table-column prop="name" />
                    </el-table>
                </el-card>
            </el-col>
            <el-col :span="18" class="toolbar perms">
                <el-card>
                    <template #header>
                        <div class="clearfix">
                            <span>权限</span>
                            <my-confirm-button v-if="checkPermission(['api:admin:permission:assign'])" :validate="saveValidate" :loading="loadingSave" :disabled="disabledSave" :placement="'left'" type="text" class="save" style="float: right;" @click="save">
                                <template #content>
                                    <p>确定要保存吗？</p>
                                </template>
                                保存
                            </my-confirm-button>
                            <el-button :loading="loadingPermissions" type="text" style="float: right; padding: 3px 0" @click="getPermissions">刷新</el-button>
                        </div>
                    </template>
                    <el-table ref="multipleTable" :data="permissionTree" :default-expand-all="true" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="id" highlight-current-row style="width: 100%;" @select-all="onSelectAll" @select="onSelect">
                        <el-table-column type="selection" width="50" />
                        <el-table-column prop="label" label="导航菜单" width="200" />
                        <el-table-column label="菜单接口" width>
                            <template #default="{ row }">
                                <el-checkbox-group v-if="row.apis && row.apis.length > 0" v-model="chekedApis">
                                    <el-checkbox v-for="api in row.apis" :key="api.id" :label="api.id" @change="(value)=>onChange(value, row.id)">{{ api.label }}</el-checkbox>
                                </el-checkbox-group>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
    </section>
</template>

<script>
    import { treeToList, listToTree, getTreeParentsWithSelf } from '@/utils'
    import { getRoleUserListPage } from '@/api/admin/role'
    import {
        getPermissionListByLabels,
        getPermissions,
        getPermissionIds,
        addRolePermission,
        addUserPermission,
        getUserPermissionIds
    } from '@/api/admin/permission'
    import MyConfirmButton from '@/components/my-confirm-button'

    export default {
        name: 'Assign',
        components: {
            MyConfirmButton
        },
        data () {
            return {
                treeNodeMap: new Map(),
                roles: [],
                roleId: 0,
                trueId: 0,
                dataType: 'role',
                permissionTree: [],
                apis: [],
                loadingRoles: false,
                loadingPermissions: false,
                loadingSave: false,
                checkedPermissions: [],
                chekedApis: [],
                isExpansion: true
            }
        },
        computed: {
            disabledSave () {
                return !(
                    this.roleId > 0 &&
                    (this.checkedPermissions.length > 0 || this.chekedApis.length > 0)
                )
            }
        },
        mounted () {
            this.getRoles();
            this.getPermissions();
        },
        methods: {
            async rolesLoad (tree, treeNode, resolve) {
                this.treeNodeMap.set(tree.id, { tree, treeNode, resolve });
                setTimeout(() => {
                    resolve(tree.children)
                }, 100)
            },
            // 获取角色列表
            async getRoles () {
                const self = this
                self.loadingRoles = true
                const res = await getRoleUserListPage()
                self.loadingRoles = false
                self.roles = res.data
                if (self.roleId > 0) {
                    self.$nextTick(function () {
                        self.$nextTick(() => {
                            const rows = treeToList(self.roles)
                            self.$nextTick(function () {
                                rows.forEach(row => {
                                    if (self.roleId == row.id) {
                                        //高亮
                                        self.$refs.rolesTree.setCurrentRow(row)
                                    }
                                })
                            });
                        });
                    });
                }
                //懒加载需要重新刷新节点
                self.treeNodeMap.forEach(function (value, key) {
                    const { tree, treeNode, resolve } = self.treeNodeMap.get(key);
                    // 收起子节点，下次点击的时候再调用load方法去后台查询
                    self.$set(self.$refs.rolesTree.store.states.treeData[key], "loaded", false);
                    self.$set(self.$refs.rolesTree.store.states.treeData[key], "expanded", false);
                    // 清空缓存
                    self.$set(self.$refs.rolesTree.store.states.lazyTreeNodeMap, key, []);
                })
            },
            // 获取权限树
            async getPermissions () {
                this.loadingPermissions = true
                this.onSelectAll([])
                const para = {parentLabels:"视觉设计部"}
                const res = await getPermissionListByLabels(para)
                this.loadingPermissions = false
                const tree = listToTree(_.cloneDeep(res.data))
                this.permissionTree = tree
                this.getRolePermission()
            },
            // 获取角色权限
            async getRolePermission () {
                if (!this.roleId > 0) {
                    return
                }

                this.loadingPermissions = true;
                var res = {};
                if (this.dataType == 'role') {
                    res = await getPermissionIds({ roleId: this.roleId })
                } else {
                    res = await getUserPermissionIds({ userId: this.trueId })
                }

                this.loadingPermissions = false
                const permissionIds = res.data
                const rows = treeToList(this.permissionTree)
                rows.forEach(row => {
                    const checked = permissionIds.includes(row.id)
                    this.$refs.multipleTable.toggleRowSelection(row, checked)
                })
                this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
                    return s.id
                })

                const apiIds = []
                permissionIds.forEach(permissionId => {
                    if (!this.checkedPermissions.includes(permissionId)) {
                        apiIds.push(permissionId)
                    }
                })
                this.chekedApis = apiIds
            },
            // 验证保存
            saveValidate () {
                let isValid = true
                if (!(this.roleId > 0)) {
                    this.$message({
                        message: '请选择角色！',
                        type: 'warning'
                    })
                    isValid = false
                    return isValid
                }
                if (!(this.checkedPermissions.length > 0 || this.chekedApis.length > 0)) {
                    this.$message({
                        message: '请选择权限！',
                        type: 'warning'
                    })
                    isValid = false
                    return isValid
                }
                return isValid
            },
            // 保存权限
            async save () {
                const permissionIds = [...this.checkedPermissions]
                if (this.chekedApis.length > 0) {
                    permissionIds.push(...this.chekedApis)
                }
                this.loadingSave = true
                var res = {};
                if (this.dataType == 'role') {
                    res = await addRolePermission({ permissionIds, roleId: this.roleId });
                } else {
                    res = await addUserPermission({ permissionIds, roleId: this.trueId });
                }
                this.loadingSave = false
                if (!res?.success) {
                    return
                }
                this.$message({
                    message: this.$t('admin.saveOk'),
                    type: 'success'
                })
            },
            roleSelect (row, column) {
                //this.$refs.rolesTree.toggleRowExpansion(row)
                this.roleId = row.id
                this.dataType = row.dataType
                this.trueId = row.trueId
                this.onSelectAll([])
                this.getRolePermission()
            },
            handleNodeClick (data, expanded) {
                if (expanded) { //展开
                    var idList = data.hierarchyCode.split('.');
                    this.toggleRowExpansionAll(this.roles, false, idList)
                    //let other_nodes = this.$refs.rolesTree.data.filter(item => !(idList.includes(item.id.toString())));
                    // other_nodes.forEach((item) => {

                    //     this.$refs.rolesTree.toggleRowExpansion(item, false)
                    // })
                }
                //切换的时候 需要把之前的选中给清除
                this.roleId = 0;
                this.dataType = "role";
                this.trueId = 0;
                this.$refs.rolesTree.setCurrentRow({});
                this.onSelectAll([]);
            },
            selectApis (checked, row) {
                if (row.apis) {
                    row.apis.forEach(a => {
                        const index = this.chekedApis.indexOf(a.id)
                        if (checked) {
                            if (index === -1) {
                                this.chekedApis.push(a.id)
                            }
                        } else {
                            if (index > -1) {
                                this.chekedApis.splice(index, 1)
                            }
                        }
                    })
                }
            },
            onSelectAll (selection) {
                const selections = treeToList(selection)
                const rows = treeToList(this.permissionTree)
                const checked = selections.length === rows.length
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row, checked)
                    this.selectApis(checked, row)
                })

                this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
                    return s.id
                })
            },
            onSelect (selection, row) {
                const checked = selection.some(s => s.id === row.id)
                if (row.children && row.children.length > 0) {
                    const rows = treeToList(row.children)
                    rows.forEach(r => {
                        this.$refs.multipleTable.toggleRowSelection(r, checked)
                        this.selectApis(checked, r)
                    })
                } else {
                    this.selectApis(checked, row)
                }

                const parents = getTreeParentsWithSelf(this.permissionTree, row.id)
                parents.forEach(parent => {
                    const checked = this.checkedPermissions.includes(parent.id)
                    if (!checked) {
                        this.$refs.multipleTable.toggleRowSelection(parent, true)
                    }
                })

                this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
                    return s.id
                })
            },
            onChange (value, id) {
                if (value) {
                    const parents = getTreeParentsWithSelf(this.permissionTree, id)
                    parents.forEach(parent => {
                        const checked = this.checkedPermissions.includes(parent.id)
                        if (!checked) {
                            this.$refs.multipleTable.toggleRowSelection(parent, true)
                        }
                    })

                    this.checkedPermissions = this.$refs.multipleTable.selection.map(s => {
                        return s.id
                    })
                }
            },
            toggleRowExpansion () {
                this.isExpansion = !this.isExpansion;
                this.toggleRowExpansionAll(this.roles, this.isExpansion);
            },
            toggleRowExpansionAll (data, isExpansion, existArray) {
                data.forEach((item) => {
                    if (item.dataType != "user") {
                        if (!(existArray.includes(item.id.toString()))) {
                            this.$refs.rolesTree.toggleRowExpansion(item, isExpansion);
                        }
                        if (item.children !== undefined && item.children !== null) {
                            this.toggleRowExpansionAll(item.children, isExpansion, existArray);
                        }
                    }
                });
            }
        }
    }
</script>

<style scoped lang="scss" >
    .clearfix:before,
    .clearfix:after {
        display: table;
        content: '';
    }
    .clearfix:after {
        clear: both;
    }

    .save ::v-deep [_button] {
        padding: 3px 0px;
    }
</style>
