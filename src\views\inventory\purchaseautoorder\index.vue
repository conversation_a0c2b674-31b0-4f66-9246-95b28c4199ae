<template>
    <container>
        <el-tabs v-model="activeName" @tab-click="handleClick" style="height: calc(100% - 40px)">
            <el-tab-pane label="设置" name="first" style="height: 100%;">
                <operation></operation>
            </el-tab-pane>
            <el-tab-pane label="开单记录" name="second" style="height: 100%;">
                <autoindexVue></autoindexVue>
            </el-tab-pane>
            <el-tab-pane label="开单统计汇总" name="autoStatistics" style="height: 100%;">
                <autoStatistics></autoStatistics>
            </el-tab-pane>
            <el-tab-pane label="开单统计明细" name="autoStatisticsDtl" style="height: 100%;">
                <autoStatisticsDtl></autoStatisticsDtl>
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>
import container from "@/components/my-container";
import autoindexVue from './autoindex.vue';
import operation from './operation.vue';
import autoStatistics from './autoStatistics.vue';
import autoStatisticsDtl from './autoStatisticsDtl.vue';

export default {
    name: 'YunHanAdminIndex',
    components: {container, operation, autoindexVue, autoStatistics, autoStatisticsDtl},

    data() {
        return {
            activeName: 'first'
        };
    },

    async mounted() {

    },

    methods: {
        handleClick(tab, event) {
            //console.log(tab, event);
        }
    },
};
</script>


<style lang="scss" scoped>

</style>