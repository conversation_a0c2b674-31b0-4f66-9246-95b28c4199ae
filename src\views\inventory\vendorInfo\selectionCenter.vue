<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane label="供应商" name="first" style="height: 100%;" lazy>
        <vendor />
      </el-tab-pane>
      <el-tab-pane label="供应商选品" name="second" style="height: 100%;" lazy>
        <supplierSelection />
      </el-tab-pane>
      <el-tab-pane label="小程序操作日志" name="third" style="height: 100%;" lazy>
        <changeBindingLog />
      </el-tab-pane>
      <el-tab-pane label="系列维度" name="fourth" style="height: 100%;" lazy>
        <seriesDimensions />
      </el-tab-pane>
      <el-tab-pane label="运营选品" name="fifth" style="height: 100%;" lazy>
        <operationalSelection />
      </el-tab-pane>
      <el-tab-pane label="运营选品拒绝记录" name="fifth2" style="height: 100%;" lazy>
        <operationalSelection2 />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vendor from './components/vendor.vue';
import supplierSelection from './components/supplierSelection.vue';
import changeBindingLog from './components/changeBindingLog.vue';
import seriesDimensions from './components/seriesDimensions.vue';
import operationalSelection from './components/operationalSelection.vue';
import operationalSelection2 from './components/operationalSelection2.vue';
export default {
  components: {
    MyContainer, vendor, supplierSelection, changeBindingLog, seriesDimensions, operationalSelection,operationalSelection2
  },
  data() {
    return {
      activeName: 'first'
    };
  },
  methods: {

  }
};
</script>

<style lang="scss" scoped></style>
