<template>
    <MyContainer>
        <div class="top">
            <div class="publicCss">
                <div class="pub_left">平台</div> 
                <el-tooltip class="item" effect="dark" :content="goodsDetailsInfo.platform" placement="top-start">
                    <div class="pub_right">{{ goodsDetailsInfo.platformName }}</div>
                </el-tooltip>
            </div>
            <div class="publicCss">
                <div class="pub_left">运营组</div>
                <el-tooltip class="item" effect="dark" :content="goodsDetailsInfo.groupName" placement="top-start">
                    <div>{{ goodsDetailsInfo.groupName }}</div>
                </el-tooltip>
            </div>
            <div class="publicCss">
                <div class="pub_left1">产品ID</div>
                <el-tooltip class="item" effect="dark" :content="goodsDetailsInfo.proCode" placement="top-start">
                </el-tooltip>
                <div class="pub_right">{{ goodsDetailsInfo.proCode }}</div>
            </div>
            <div class="publicCss">
                <div class="pub_left1">近30天销量</div>
                <el-tooltip class="item" effect="dark" :content="goodsDetailsInfo.day30Sales" placement="top-start">
                    <div class="pub_right">{{ goodsDetailsInfo.day30Sales }}</div>
                </el-tooltip>
            </div>
            <div class="publicCss1">
                <div class="pub_left1">近30天销售额</div>
                <el-tooltip class="item" effect="dark" :content="goodsDetailsInfo.day30SalesRevenue"
                    placement="top-start">
                    <div>{{ goodsDetailsInfo.day30SalesRevenue }}</div>
                </el-tooltip>
            </div>
        </div>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0; height: 200px;"  :loading="listLoading">
        </vxetablebase>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getGoodsCodeDataByProCode } from '@/api/operatemanage/continuLosses'
const tableCols = [
    { width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { width: 'auto', align: 'center', prop: 'day30Sales', label: '近30天销量', },
    { width: 'auto', align: 'center', prop: 'day30SalesRevenue', label: '近30天销售额', },
    { width: 'auto', align: 'center', prop: 'day30Profit3', label: '毛三利润', },
    { width: 'auto', align: 'center', prop: 'profit3Rate', label: '毛三利率', formatter: (row) => (row.profit3Rate !== null && row.profit3Rate !== undefined) ? row.profit3Rate + '%' : null },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    props: {
        queryInfo: {
            type: Object,
        },
        goodsDetailsInfo: {
            type: Object,
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {},
            tableCols,
            tableData: [],
            listLoading:false
        }
    },
    async mounted() {
        this.ListInfo = this.queryInfo
        await this.getList()
    },
    methods: {
        async getList() {
            this.listLoading=true;
            const { data, success } = await getGoodsCodeDataByProCode(this.ListInfo)
            if (success) {
                this.tableData = data
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
            this.listLoading=false;
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
    border: 1px solid #ebeef5;

    .publicCss,
    .publicCss1 {
        width: 50%;
        display: flex;
        // border-right: 1px solid #ebeef5;
        line-height: 40px;

        .pub_left,
        .pub_left1 {
            width: 100px;
            border-right: 1px solid #ebeef5;
            //合并边框线
            height: 40px;
        }

        .pub_right {
            flex: 1;
            border-right: 1px solid #ebeef5;
            //超出隐藏省略号
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .publicCss {
        width: 50%;
        border-bottom: 1px solid #ebeef5;
    }
}
</style>