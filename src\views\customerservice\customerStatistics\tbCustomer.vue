<template>
    <MyContainer>
        <el-tabs v-model="activeName">
            <el-tab-pane label="组绩效统计(售前组)" name="first" lazy>
                <tbGroupStatistics @toUser="toUser"></tbGroupStatistics>
            </el-tab-pane>
            <el-tab-pane label="店绩效统计(售前组)" name="second" lazy>
                <tbShopStatistics></tbShopStatistics>
            </el-tab-pane>
            <el-tab-pane label="个人绩效统计(售前组)" name="third" lazy>
                <tbPersonnelStatistics></tbPersonnelStatistics>
            </el-tab-pane>
            <el-tab-pane label="新绩效核算表(售前组)" name="forth" lazy>
                <tbNewPerformanceStatement></tbNewPerformanceStatement>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import tbPersonnelStatistics from './components/tbPersonnelStatistics.vue';
import tbShopStatistics from './components/tbShopStatistics.vue';
import tbGroupStatistics from './components/tbGroupStatistics.vue';
import tbNewPerformanceStatement from './components/tbNewPerformanceStatement.vue'
export default {
    components: {
        MyContainer, tbPersonnelStatistics, tbShopStatistics, tbGroupStatistics, tbNewPerformanceStatement
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {
        toUser(e) {
            this.activeName = e
        }
    }
};
</script>

<style lang="scss" scoped></style>