<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'">
        </el-date-picker>
        <el-select v-model="ListInfo.categoryArr" style="width: 200px;" multiple collapse-tags clearable
          placeholder="类别" class="publicCss">
          <el-option v-for="item in categoryList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.regionArr" style="width: 200px;" multiple collapse-tags clearable placeholder="区域"
          class="publicCss">
          <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
    </template>

    <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
      :loading="loading" :column-config="{ resizable: true }" :merge-footer-items="mergeFooterItems"
      :footer-data="footerData" :span-method="mergeRowMethod" :row-class-name="rowClassName"
      :footer-cell-class-name="footerCellClassName" :data="tableData">
      <vxe-column field="month" width="100" title="月份"></vxe-column>
      <vxe-column field="region" width="100" title="区域"></vxe-column>
      <vxe-column field="category" width="100" title="类别"></vxe-column>
      <vxe-colgroup title="水费">
        <vxe-column field="waterConsumption" width="100" title="用水吨数"></vxe-column>
        <vxe-column field="waterPricePerTon" width="100" title="水费单价"></vxe-column>
        <vxe-column field="waterFeeAmount" width="100" title="水费金额"></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="电费">
        <vxe-column field="electricityConsumption" width="100" title="用电度数"></vxe-column>
        <vxe-column field="electricityPricePerKwh" width="100" title="电费单价"></vxe-column>
        <vxe-column field="electricityFeeAmount" width="100" title="电费金额"></vxe-column>
      </vxe-colgroup>
      <vxe-column field="otherSharedElectricityFee" width="100" title="其他公用均摊电费金额(单价:元)"></vxe-column>
      <vxe-column field="subtotalAmount" width="100" title="金额小计"></vxe-column>
      <vxe-column field="remarks" max-width="100%" title="备注"></vxe-column>
      <vxe-column title="操作" footer-align="left" width="100px" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini" v-if="scope.row.region && !scope.row.region.includes('合计')"
            @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" style="color:red"
            v-if="scope.row.region && !scope.row.region.includes('合计')" @click="handleRemove(scope.row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
      <hgudingzichanEdit ref="hgudingzichanEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
        @cancellationMethod="dialogVisibleEdit = false" />
    </el-drawer>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { warehouseUtilityBillsPage, warehouseUtilityBillsImport, warehouseUtilityBillsRemove } from '@/api/people/peoplessc.js';
import hgudingzichanEdit from "./hgudingzichanEdit.vue";
import checkPermission from '@/utils/permission'
export default {
  name: "hgudingzichanIndex",
  components: {
    MyContainer, hgudingzichanEdit
  },
  props: {
    // 可以添加props如果需要从父组件传递数据
  },
  data() {
    return {
      // 工具函数
      downloadLink,
      // 对话框状态
      dialogVisibleEdit: false,
      dialogVisible: false,
      // 编辑相关
      editInfo: {},
      // 文件上传
      fileList: [],
      uploadLoading: false,
      // 表格数据
      tableData: [],
      footerData: [],
      mergeFooterItems: [],
      // 下拉选项数据
      regionList: [],
      categoryList: [],
      // 表格配置
      somerow: 'region,month',
      // 加载状态
      loading: false,
      exportloading: false,
      // 查询条件
      ListInfo: {
        regionArr: [],
        categoryArr: [],
        calculateMonthArr: [
          dayjs().subtract(1, 'month').format('YYYY-MM'),
          dayjs().subtract(1, 'month').format('YYYY-MM')
        ],
        startMonth: null,
        endMonth: null,
        region: '',
        category: ''
      }
    }
  },
  computed: {
    // 是否有选择的文件
    hasSelectedFile() {
      return this.fileList.length > 0;
    },
    // 是否可以导出
    canExport() {
      return this.tableData.length > 0;
    },
    // 格式化的时间范围显示
    formattedTimeRange() {
      const { calculateMonthArr } = this.ListInfo;
      if (calculateMonthArr?.length >= 2) {
        return `${calculateMonthArr[0]} 至 ${calculateMonthArr[1]}`;
      }
      return '';
    }
  },

  async mounted() {
    await this.getList()
  },
  methods: {
    footerMethod({ columns }) {
      const sums = [];
      if (!this.footerData)
        return sums
      let newfield = columns.map(item => item.field)
      let newfooterdata = [];
      this.footerData.forEach((item) => {
        let newarr2 = [];
        newfield.forEach((item2) => {
          newarr2.push(item[item2])
        })
        newfooterdata.push(newarr2)
      })
      return newfooterdata;
    },
    rowClassName(event) {
      if (event.row.region && event.row.region.indexOf('合计') != -1) {
        return 'row-yellow1'
      }
      return null
    },
    footerCellClassName(event) {
      if (event.row.month == '总计') {
        return 'row-yellow5'
      }
      return null
    },
    //上传文件
    onUploadRemove() {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess() {
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      try {
        const form = new FormData();
        form.append("file", item.file);
        const res = await warehouseUtilityBillsImport(form);
        if (res?.success) {
          this.$message({ message: res.msg || "导入成功", type: "success" });
          this.dialogVisible = false;
          await this.getList()
        } else {
          this.$message({ message: res?.msg || "导入失败", type: "error" });
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        this.$message({ message: "文件上传失败，请重试", type: "error" });
      } finally {
        this.uploadLoading = false
      }
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    downExcel() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250719/1946386316820717568.xlsx', '水电费-导入模板.xlsx');
    },
    async exportExcel() {
      this.exportloading = true;
      this.$refs.newtable.exportData({ filename: '仓储行政看板-水电费数据' + new Date().toLocaleString(), sheetName: 'Sheet1', type: 'xlsx' })
      this.$nextTick(() => {
        this.exportloading = false;
      })
    },
    closeGetlist() {
      this.dialogVisibleEdit = false;
      this.getList()
    },
    handleEdit(row) {
      this.editInfo = row;
      this.dialogVisibleEdit = true;
    },
    async handleRemove(row) {
      this.$confirm('是否删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (!row || !row.id) {
          this.$message.error('删除失败：数据异常');
          return;
        }
        try {
          this.loading = true
          const { success, msg } = await warehouseUtilityBillsRemove({ ids: row.id })

          if (success) {
            this.$message.success(msg || '删除成功')
            await this.getList();
          } else {
            this.$message.error(msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败，请重试');
        } finally {
          this.loading = false
        }
      }).catch(() => {
      });
    },
    // 通用行合并函数（将相同多列数据合并为一行）
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = this.somerow.split(',')
      const cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },

    async getList() {
      try {
        this.prepareListParams();// 查询条件
        this.loading = true
        const { data, success, msg } = await warehouseUtilityBillsPage(this.ListInfo)
        if (success && data) {
          this.tableData = data.list || []
          this.footerData = data.summary || []
          // 更新下拉选项
          this.updateDropdownOptions();
        } else {
          this.$message.error(msg || '获取列表失败')
          this.tableData = []
          this.footerData = []
        }
      } catch (error) {
        console.error('获取列表失败:', error);
        this.$message.error('获取列表失败，请重试');
        this.tableData = []
        this.footerData = []
      } finally {
        this.loading = false
      }
    },

    // 准备查询参数
    prepareListParams() {
      const { calculateMonthArr, regionArr, categoryArr } = this.ListInfo;
      if (calculateMonthArr?.length >= 2) {
        this.ListInfo.startMonth = calculateMonthArr[0];
        this.ListInfo.endMonth = calculateMonthArr[1];
      } else {
        this.ListInfo.startMonth = null;
        this.ListInfo.endMonth = null;
      }
      // 处理区域和类别数组，使用更简洁的写法
      this.ListInfo.region = regionArr?.length ? regionArr.join(',') : '';
      this.ListInfo.category = categoryArr?.length ? categoryArr.join(',') : '';
    },

    // 更新下拉选项
    updateDropdownOptions() {
      if (!this.tableData?.length) return;
      // 使用Set来避免重复，提高性能
      const regionSet = new Set(this.regionList);
      const categorySet = new Set(this.categoryList);
      // 一次遍历同时处理区域和类别
      this.tableData.forEach(item => {
        if (item.region && !item.region.includes('合计')) {
          regionSet.add(item.region);
        }
        if (item.category && !item.category.includes('合计')) {
          categorySet.add(item.category);
        }
      });
      this.regionList = Array.from(regionSet);
      this.categoryList = Array.from(categorySet);
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

// :deep(.row-green) {
//   background-color: #e0eed6;
// }

:deep(.row-yellow1) {
  background-color: #f8e2d3;
}

:deep(.row-yellow5) {
  background-color: #00937e;
  color: white;
}
</style>
