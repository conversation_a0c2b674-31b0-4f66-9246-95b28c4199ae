<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss" />
                <el-input v-model.trim="ListInfo.userName" placeholder="用户" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.regionName" placeholder="区域" class="publicCss" clearable filterable>
                    <el-option :label="item" :value="item" v-for="item in regionNameList" :key="item" />
                </el-select>
                <el-select v-model="ListInfo.deptName" placeholder="一级部门" class="publicCss" clearable filterable>
                    <el-option :label="item" :value="item" v-for="item in deptNameList" :key="item" />
                </el-select>
                <el-select v-model="ListInfo.twoDeptName" placeholder="二级部门" class="publicCss" clearable filterable>
                    <el-option :label="item" :value="item" v-for="item in twoDeptNameList" :key="item" />
                </el-select>
                <el-select v-model="ListInfo.groupName" placeholder="小组" class="publicCss" clearable filterable>
                    <el-option :label="item" :value="item" v-for="item in groupNameList" :key="item" />
                </el-select>
                <el-input v-model.trim="ListInfo.question" placeholder="关键字" maxlength="200" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            id="20250629135858" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="50" align="center" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleView(row)">查看</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="模拟对话" :visible.sync="viewVisible" :close-on-click-modal="true" v-dialogDrag>
            <viewAIConversationsLog :AIConversations="AIConversations" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { logUserQuestionPage, logUserQuestionListValue } from '@/api/people/logUserQuestion'
import viewAIConversationsLog from './viewAIConversationsLog.vue'
const tableCols = [
    { sortable: 'custom', width: '80', align: 'left', prop: 'userName', label: '用户', },
    { sortable: 'custom', width: '120', align: 'left', prop: 'regionName', label: '区域', },
    { sortable: 'custom', width: '90', align: 'left', prop: 'deptName', label: '一级部门', },
    { sortable: 'custom', width: '90', align: 'left', prop: 'twoDeptName', label: '二级部门', },
    { sortable: 'custom', width: '90', align: 'left', prop: 'groupName', label: '小组', },
    { sortable: 'custom', width: '200', align: 'left', prop: 'question', label: '问题', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'answer', label: '答案', },
    { sortable: 'custom', width: '130', align: 'left', prop: 'createTime', label: '创建时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, viewAIConversationsLog
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: dayjs().format('YYYY-MM-DD'),//开始时间
                endTime: dayjs().format('YYYY-MM-DD'),//结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            regionNameList: [],
            deptNameList: [],
            twoDeptNameList: [],
            groupNameList: [],
            viewVisible: false,
            AIConversations: []
        }
    },
    async mounted() {
        await this.getSelectList()
        await this.getList()
    },
    methods: {
        handleView(row) {
            console.log('查看', row);
            const res = JSON.parse(JSON.stringify(row))
            res.answer = res.answer.replace(/\\n/g, "\n");
            res.question = res.question.replace(/\\n/g, "\n");
            this.AIConversations = [
                {
                    authorRole: 'user',
                    content: res.question
                },
                {
                    authorRole: 'assistant',
                    content: res.answer
                }
            ]
            this.viewVisible = true
        },
        async getSelectList() {
            const formData = new FormData()
            formData.append('fieldName', 'regionName')
            const { data } = await logUserQuestionListValue(formData)
            this.regionNameList = data
            const formData1 = new FormData()
            formData1.append('fieldName', 'deptName')
            const { data: data1 } = await logUserQuestionListValue(formData1)
            this.deptNameList = data1
            const formData3 = new FormData()
            formData3.append('fieldName', 'twoDeptName')
            const { data: data3 } = await logUserQuestionListValue(formData3)
            this.twoDeptNameList = data3
            const formData2 = new FormData()
            formData2.append('fieldName', 'groupName')
            const { data: data2 } = await logUserQuestionListValue(formData2)
            this.groupNameList = data2

        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await logUserQuestionPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
