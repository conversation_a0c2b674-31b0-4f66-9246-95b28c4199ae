<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-tooltip effect="dark" :content="ListInfo.sku" placement="top-start">
                    <div class="item">{{ ListInfo.sku ? ListInfo.sku : null }}</div>
                </el-tooltip>
                <el-input-number v-model.trim="ListInfo.releaseNo" class="publicCss" max="100" :controls="false" :precision="0" placeholder="放单批次号"/>
                <el-button type="primary" @click="getList(true)">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'groupOrderPage202408041749'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%; height: 400px; margin: 0" v-loading="loading"
            :showsummary='true' :summaryarry='summaryarry' />
        <my-pagination ref="pager" :total="groupOrderTotal" @page-change="Pagechange" @size-change="Sizechange" />
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getReleaseRecord } from '@/api/vo/VerifyOrder'
import { replaceSpace } from '@/utils/getCols'
const tableCols = [
    { istrue: true, width: 'auto', sortable: 'custom', label: '导入时间', prop: 'importTime', },
    { istrue: true, width: 'auto', sortable: 'custom', label: '线上订单号', prop: 'orderNo', },
    { istrue: true, width: 'auto', sortable: 'custom', label: '付款日期', prop: 'timePay', },
    { istrue: true, width: 'auto', sortable: 'custom', label: '订单商品重量', prop: 'weight', },
    { istrue: true, width: 'auto', sortable: 'custom', label: '系列编码', prop: 'seriesName', },
    { istrue: true, width: 'auto', sortable: 'custom', label: '放单时间', prop: 'syncRpaTime', },
    { istrue: true, width: 'auto', sortable: 'custom', label: '放单批次号', prop: 'releaseNo', },
]
export default {
    name: "groupOrderPage",
    props: {
        groupOrderQueryInfo: {
            type: Object,
            default: null
        }
    },
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: true,
                startDate: null,//开始时间
                endDate: null,//结束时间
                sku: null,//sku
            },
            tableCols,
            tableData: [],
            loading: false,
            groupOrderTotal: 0,
            summaryarry: {}
        }
    },
    async mounted() {
        this.ListInfo.sku = this.groupOrderQueryInfo.sku
        this.ListInfo.startDate = this.groupOrderQueryInfo.startDate
        this.ListInfo.endDate = this.groupOrderQueryInfo.endDate
        await this.getList()
    },
    methods: {
        async getList(isSearch) {
            if (isSearch) {
                if (this.ListInfo.releaseNo <= 0) return this.$message.error('放单批次号不能小于等于0')
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            const { data, success } = await getReleaseRecord(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.groupOrderTotal = data.total
                this.summaryarry = data.summary
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    font-weight: 700;

    .item {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0;
        padding: 0;
    }

    .publicCss {
        width: 200px;
        margin: 0 10px;
    }
}
</style>