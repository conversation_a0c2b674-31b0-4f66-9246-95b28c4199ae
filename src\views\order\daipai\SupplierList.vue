<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <!-- <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>               
            </el-form>
        </template> -->
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' 
        :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading" >

            <template slot='extentbtn'>
                <el-button-group>                   
                    <el-button type="primary" href="/static/excel/order/daipai/代拍厂家导入模板.xlsx" target="_blank" @click="downImportTemp(0)">下载导入模板</el-button>
                    
                    <el-button type="primary" @click="onImport">导入</el-button>
               
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.name" type="text" maxlength="100" clearable placeholder="供应商" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.createUserName" type="text" maxlength="100" clearable placeholder="添加人" style="width:100px;" />
                    </el-button>
                    <el-button type="primary" @click="onSearch" icon="el-icon-search">查询</el-button>
                    <el-button @click="()=>{Filter={};}"  icon="el-icon-close">清空查询条件</el-button>
                    <el-button type="primary" @click="onOpenDtl(0,1)" icon="el-icon-plus">新增厂家</el-button>
               
                </el-button-group>
            </template>
            
            <el-table-column width="120" label="操作" fixed="right">               
                <template slot-scope="scope">                   
                    <el-button type="text" @click="onOpenDtl(scope.row.id,2)" >编辑</el-button>
                    <el-button type="text" @click="onOpenDtl(scope.row.id,3)" >详情</el-button>
                </template>
            </el-table-column>

        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>       

    </my-container>
</template>
<script>  

    import {
        PageDpSupplierListAsync, ImportSuppliersAsync,GetDpSupplierByIdAsync
    } from '@/api/order/alllinkDaiPai'

    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import { formatmoney, formatPercen, getUrlParam,  setStore, getStore,formatLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    //import SupplierForm from "@/views/order/daipai/SupplierForm.vue";

    const platformOptions=[
        {label:'其他',value:0},
        {label:'1688',value:1}
    ]

    const fmtShopPlatform = function (platform) {
        switch (platform) {
            case 0:
                return "其他";
            case 1:
                return '1688';
        }
        return " ";
    }

    const tableCols = [
        { istrue: true, prop: 'shopPlatform', label: '代拍平台', width: '100', sortable: 'custom', formatter: (row) => fmtShopPlatform(row.shopPlatform) },
        { istrue: true, prop: 'name', label: '厂家名称', minwidth: '140', sortable: 'custom', type:'html', formatter:(row)=>formatLink(row.name,row.shopWebUrl) },
        { istrue: true, prop: 'createUserName', label: '创建人', width: '100', sortable: 'custom' },

        { istrue: true, prop: 'province', label: '省', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'city', label: '市', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'county', label: '区县', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'address', label: '地址', width: '120', sortable: 'custom' },

        { istrue: true, prop: 'remark', label: '备注', width: '120', sortable: 'custom' },

    ];

    export default {
        name: "SupplierList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },//,SupplierForm
        data() {
            return {
                that: this,
                Filter: {
                    Name: "",
                    CreateUserName: ""
                },
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
              

            };
        },
        async mounted() {

            this.onSearch();
        },
        methods: {
            downImportTemp() {               
                window.open("/static/excel/order/daipai/代拍厂家导入模板.xlsx");                
            },   
            fmtShopPlatform:fmtShopPlatform,
            onImport(){
                let self=this;
                this.$showSimpleimport({
                    action:ImportSuppliersAsync,
                    accept:'.xlsx',
                    title:'导入厂家',  
                    tip:'请上传.xlsx格式的Excel', 
                    autoClose:true,                 
                    callOk:self.onImportRlt             
                });   
            },
            onImportRlt(res){
                if(res && res.success){
                    if(res.data.errMsg){
                        this.$alert(res.data.errMsg);
                    }else{
                        this.$message.success(`导入成功，本次新增供应商${res.data.newCount}个！`);
                        this.onRefresh();
                    }
                }
            },
            onOpenDtl(oid,mode){
                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/SupplierForm.vue`,
                    title:'待拍厂家',
                    args:{oid:oid,mode:mode},
                    height:300, 
                    callOk:self.onRefresh             
                });                 
            },
           
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await PageDpSupplierListAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;

            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>
