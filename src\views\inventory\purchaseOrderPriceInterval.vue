<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            <el-form-item>
              <el-input v-model.trim="filter.styleCode" placeholder="系列编码" :maxlength="30"  clearable />
            </el-form-item> 
            <el-form-item>
              <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" :maxlength="30"  clearable />
            </el-form-item> 
            <el-form-item>
            时间：
            <el-date-picker style="width: 250px" v-model="filter.timerange"  type="daterange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"></el-date-picker>
            </el-form-item> 
            <el-form-item>以供应商
              <el-input style="width: 150px" v-model.trim="filter.supplierName" placeholder="供应商名称" :maxlength="50" clearable />
              为起点
            </el-form-item> 
            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button> 
        </el-form>
      </div>
    </template>
    <template>  
        <vxetablebase ref="TableCols" :tableData="proTableList" :tableCols="TableCols" :is-index="true" :that="that"
            :showsummary="true" :summaryarry='proSummaryarry' style="width: 100%; height: 650px; margin: 0" @sortchange='sortchange' 
            :treeProp="{ rowField: 'disId', parentField: 'disParentId' }" :loading="listLoading" class="already">
        </vxetablebase>
    </template>  
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </container>
</template>
<script>
import { getPurchaseOrderPriceIntervalAsync } from '@/api/inventory/purchaseordernew' 
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import container from "@/components/my-container"; 

const tableCols1 = [
  { istrue: true, prop: 'styleCode', sortable: 'custom', label: '系列编码', width: '150',treeNode:true },
  { istrue: true, prop: 'goodsCode', sortable: 'custom', label: '商品编码',  width: '150' },
  { istrue: true, prop: 'purchaseDate', sortable: 'custom', label: '采购日期', width: '150' }, 
  { istrue: true, prop: 'supplier', sortable: 'custom', label: '供应商名称' },
  { istrue: true, prop: 'firstCostPrice', sortable: 'custom', label: '原成本价', width: '150' }, 
  { istrue: true, prop: 'lastPrice', sortable: 'custom', label: '上次采购单价', width: '150' },
  { istrue: true, prop: 'price', sortable: 'custom', label: '本次采购单价', width: '150' },
  { istrue: true, prop: 'purchaseCount', sortable: 'custom', label: '采购数量', width: '150' },
  { istrue: true, prop: 'purchaseAmount', sortable: 'custom', label: '采购金额', width: '150' },
  { istrue: true, prop: 'marginLastPurchaseAmount', sortable: 'custom', label: '对比上次供应商金额', width: '150' },
  { istrue: true, prop: 'marginCostPurchaseAmount', sortable: 'custom', label: '对比原成本价金额', width: '150' } 
];

const tableHandles = [
];

export default {
  name: "PurchaseOrderPriceInterval",
  components: { container, vxetablebase },
  data() {
    return {
      that: this,
      filter: {
        styleCode: null,
        goodsCode: null,
        startDate: null,
        endDate: null,
        timerange: null,
        supplierName: null
      }, 
      TableCols: tableCols1,
      tableHandles: tableHandles,
      total: 0,
      proPager: { OrderBy: null, IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      proTableList: [],
      proSummaryarry: {},
      selids: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      }, 
    };
  },
  async mounted() {
    let end = new Date();
    let start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    end.setTime(end.getTime());
    this.filter.timerange = [start, end]; 
  },
  async created() {
  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.proPager = {};
      else
        this.proPager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { ...pager, ...this.proPager, ... this.filter }
      let res = await getPurchaseOrderPriceIntervalAsync(params);
      this.listLoading = false;
      this.proTableList = res.data?.list;
      this.total = res.data?.total;
      this.proSummaryarry = res.data?.summary;
    }
  }
}

</script>
<style></style>
