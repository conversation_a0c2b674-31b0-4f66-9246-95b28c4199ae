<template>
  <MyContainer style="height: 98%;" v-loading="pageLoading">
    <template #header>
      <div class="top">
        <div class="top_one" style="display: flex;">
          <el-date-picker v-model="timeRanges" type="datetimerange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            style="width: 340px;margin-right: 10px;" format="yyyy-MM-dd HH:mm:ss" :value-format="'yyyy-MM-dd HH:mm:ss'"
            @change="changeTime">
          </el-date-picker>
          <el-select v-model="ListInfo.platForm" placeholder="平台" class="publicCss" clearable filterable
            style="width: 110px;">
            <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="ListInfo.shopCode" placeholder="店铺" class="publicCss" clearable filterable
            style="width: 110px;">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopName">
            </el-option>
          </el-select>
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="130px"
              placeholder="订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
              @callback="callbackGoodsCode" title="订单号">
            </inputYunhan>
          </div>
          <div class="publicCss">
            <inputYunhan ref="productCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode" width="130px"
              placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
              @callback="productCodeCallback" title="产品ID">
            </inputYunhan>
          </div>
          <el-input v-model="ListInfo.goodsName" placeholder="产品" maxlength="100" clearable class="publicCss" />
          <el-select v-model="ListInfo.complaintType" placeholder="投诉类型" class="publicCss" clearable multiple filterable
            style="width: 220px;" collapse-tags>
            <el-option label="卖家承诺不履行" value="卖家承诺不履行" />
            <el-option label="骚扰他人" value="骚扰他人" />
            <el-option label="发货问题" value="发货问题" />
            <el-option label="诱导线下交易" value="诱导线下交易" />
            <el-option label="卖家态度问题" value="卖家态度问题" />
            <el-option label="发票问题" value="发票问题" />
          </el-select>
          <el-select v-model="ListInfo.isAutomaticIntervention" placeholder="是否自动介入" class="publicCss" clearable
            style="width: 100px;" filterable>
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
          <el-select v-model="ListInfo.isPlatFormComplaint" placeholder="是否平台任务发出投诉"
            style="width:100px ;margin-right: 10px;" clearable filterable>
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
          <el-select v-model="ListInfo.department" placeholder="部门" class="publicCss" clearable multiple collapse-tags
            style="width: 165px;" filterable>
            <el-option label="仓库" value="仓库" />
            <el-option label="运营" value="运营" />
            <el-option label="采购" value="采购" />
            <el-option label="客服" value="客服" />
            <el-option label="快递" value="快递" />
            <el-option label="厂家" value="厂家" />
            <el-option label="卖家" value="卖家" />
            <el-option label="公司" value="公司" />
          </el-select>
          <el-select v-model="ListInfo.complaintResult" placeholder="投诉结果" class="publicCss" style="width: 210px;"
            clearable multiple filterable collapse-tags>
            <el-option label="投诉完结" value="投诉完结" />
            <el-option label="投诉已完结" value="投诉已完结" />
            <el-option label="投诉成立" value="投诉成立" />
            <el-option label="投诉已撤销" value="投诉已撤销" />
            <el-option label="投诉不成立" value="投诉不成立" />
            <el-option label="等待平台处理" value="等待平台处理" />
            <el-option label="等待买家处理" value="等待买家处理" />
            <el-option label="等待卖家处理" value="等待卖家处理" />
          </el-select>
        </div>
        <div class="top_thr">
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" style="margin-left: 10px;" @click="exportProps">导出</el-button>
          <el-button type="primary" style="margin-left: 10px;" @click="batchEditing">批量编辑</el-button>
          <el-button type="primary" style="width: 80px;margin-left: 10px;" @click="getList('search')">搜索</el-button>
          <el-button type="primary" style="margin-left: 10px;" @click="onExpressMaintenance">快递维护</el-button>
          <el-button type="primary" style="margin-left: 10px;" @click="onExpressDelivery">快递维护导入模版</el-button>
          <el-button type="primary" style="margin-left: 10px;" @click="onExpressMaintenance_SF">顺丰快递维护</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' @checkbox-range-end="chooseCode"
      style="width: 100%; height: 680px; margin: 0" v-loading="loading" />
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="批量编辑" :visible.sync="batchdialogVisible" width="70%" v-dialogDrag>
      <div>
        <el-table :data="eltableData" style="width: 100%;max-height: 300px; overflow: auto;" height="300">
          <el-table-column type="index" width="50">
          </el-table-column>
          <el-table-column prop="platFormString" label="平台" width="100">
          </el-table-column>
          <el-table-column prop="shopName" label="店铺" width="130">
          </el-table-column>
          <el-table-column prop="orderNo" label="订单编号" width="170">
          </el-table-column>
          <el-table-column prop="proCode" label="产品ID" width="140">
          </el-table-column>
          <el-table-column label="产品" width="240" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.goodsName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="complaintType" label="投诉类型" width="120">
          </el-table-column>
          <el-table-column label="具体原因" width="150" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.specificReason }}</span>
            </template>
          </el-table-column>
          <el-table-column label="部门" width="110" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.department }}</span>
            </template>
          </el-table-column>
          <el-table-column label="责任人" :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              <span>{{ row.chargePerson }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="dialog-content">
        <span class="dialog-label">部门：</span>
        <el-select v-model="popupData.department" placeholder="部门" style="width: 40%" clearable filterable>
          <el-option label="仓库" value="仓库" />
          <el-option label="运营" value="运营" />
          <el-option label="采购" value="采购" />
          <el-option label="客服" value="客服" />
          <el-option label="快递" value="快递" />
          <el-option label="厂家" value="厂家" />
          <el-option label="卖家" value="卖家" />
          <el-option label="公司" value="公司" />
        </el-select>
      </div>
      <div class="textarea-container">
        <span class="dialog-label">责任人:</span>
        <el-input v-model="popupData.chargePerson" placeholder="请输入责任人" style="width: 40%" maxlength="100"></el-input>
      </div>
      <div class="textarea-container">
        <span style="margin-right: 20px;">具体原因:</span>
        <el-input type="textarea" style="width: 40%;" maxlength="300" :autosize="{ minRows: 5, maxRows: 5 }"
          placeholder="请输入具体原因" v-model="popupData.specificReason"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchdialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="confirmSave">确认保存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-drawer title="物流日志" :visible.sync="logisticsLogDrawer" direction="rtl" size="50%">
      <div style="margin:15px 0 15px 30px">
        <span style="margin-right: 10px;font-size: 20px;">快递号:</span>
        <span style="font-size: 20px;">{{ express }}</span>
      </div>
      <div class="MaterialFlow">
        <div class="Material" v-for="item in activities">
          <el-timeline>
            <el-timeline-item v-for="(activity, index) in item.logisticsTrackDetail" :key="index" :icon="activity.icon"
              :type="activity.type" :color="activity.color" :size="activity.size"
              :timestamp="activity.statusInformationDate">
              {{ activity.track }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>

    <el-dialog title="快递维护" :visible.sync="expressMaintenanceVisible" width="45%" v-dialogDrag>
      <div style="height: 500px;">
        <txExpressMaintenance v-if="expressMaintenanceVisible"/>
      </div>
    </el-dialog>

    <el-dialog title="顺丰快递维护" :visible.sync="expressMaintenance_sfVisible" width="45%" v-dialogDrag>
      <div style="height: 500px;">
        <txExpressMaintenance_sf v-if="expressMaintenance_sfVisible"/>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, platformlist } from '@/utils/tools'
import dayjs from 'dayjs'
import { importTianMaoComplainAsync, getTianMaoComplainList, updateTianMaoComplainAsync, exportTianMaoComplainAsync, getTianMaoLogisticsTrackList } from '@/api/customerservice/taobaoshouhou'
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime } from "@/utils";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import txExpressMaintenance from "./txExpressMaintenance.vue";
import txExpressMaintenance_sf from "./txExpressMaintenance_sf.vue";
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { istrue: true, prop: 'dataTime', label: '日期', sortable: 'custom', width: '130' },
  { istrue: true, prop: 'platFormString', label: '平台', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '110' },
  { istrue: true, prop: 'orderNo', type: 'orderLogInfo', orderType: 'orderNo', label: '订单编号', sortable: 'custom', width: '130' },
  { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '130' },
  { istrue: true, prop: 'goodsName', label: '产品', sortable: 'custom', width: '190' },
  { istrue: true, prop: 'complaintType', label: '投诉类型', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'isAutomaticIntervention', label: '是否自动介入', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'isPlatFormComplaint', label: '是否平台任务发起投诉', sortable: 'custom', width: '120' },
  { istrue: true, prop: 'specificReason', label: '具体原因', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'department', label: '部门', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'chargePerson', label: '责任人', sortable: 'custom', width: '70' },
  { istrue: true, prop: 'compensationAmount', label: '赔付金额', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'complaintResult', label: '投诉结果', sortable: 'custom', width: '100' },
  {
    istrue: true, type: 'button', label: '日志',
    btnList: [
      { label: "物流日志", handle: (that, row) => that.logisticsLog(row) },
    ]
  },
]
export default {
  name: "txComplain",
  components: {
    MyContainer, vxetablebase, inputYunhan, txExpressMaintenance,txExpressMaintenance_sf
  },
  data() {
    return {
      expressMaintenanceVisible: false,
      expressMaintenance_sfVisible: false,
      activities: [],
      express: '',
      summaryarry: {},
      eltableData: [],
      batchdialogVisible: false,
      pickerOptions,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'dataTime',
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        complaintType: [],//投诉类型
        goodsName: null,//产品ID
        proCode: '',//投诉人
        orderNo: '',//订单编号
        shopCode: null,//店铺
        platForm: null,//平台
        complaintResult: [],//投诉结果
        department: [],//部门
        isPlatFormComplaint: null,//是否平台任务发出投诉
        isAutomaticIntervention: null,//是否自动介入
      },
      timeRanges: [],
      shopList: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      ids: [],
      editBackup: {}, //备份编辑前的数据
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      dialogVisible: false,
      pageLoading: false,
      logisticsLogDrawer: false,
      platformlist: platformlist,
      popupData: {
        specificReason: null,
        chargePerson: null,
        department: null,
      },
    }
  },
  async mounted() {
    const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD HH:mm:ss");
    const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD HH:mm:ss");
    this.timeRanges = [startDate, endDate]
    const res1 = await getAllShopList({ platforms: [1, 9] });
    this.shopList = [];
    res1.data?.forEach(f => {
      if (f.isCalcSettlement && f.shopCode)
        this.shopList.push(f);
    });
    await this.getList()
  },
  methods: {
    //快递维护导入模版
    onExpressDelivery() {
      window.open("../static/excel/customerservice/快递维护导入模板.xlsx","_self");
    },
    //快递维护
    onExpressMaintenance(){
      this.expressMaintenanceVisible = true
    },
    onExpressMaintenance_SF(){
      this.expressMaintenance_sfVisible = true
    },

    //物流日志
    async logisticsLog(row) {
      const { data, success } = await getTianMaoLogisticsTrackList({ orderNo: row.orderNo, startDate: row.dataTime, endDate: row.dataTime });
      if (success) {
        this.express = data[0]?.logisticsTrackDetail[0]?.logisticNumber?? ''
        this.activities = data
        this.logisticsLogDrawer = true
      }
    },
    //订单号
    callbackGoodsCode(val) {
      this.ListInfo.orderNo = val;
    },
    //产品ID
    productCodeCallback(val) {
      this.ListInfo.proCode = val;
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importTianMaoComplainAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    //批量编辑
    batchEditing() {
      if (this.eltableData.length == 0) {
        this.$message({ message: "请选择要编辑的行", type: "warning" });
        return
      }
      if (this.eltableData.length == 1) {
        this.popupData.specificReason = this.eltableData[0].specificReason
        this.popupData.chargePerson = this.eltableData[0].chargePerson
        this.popupData.department = this.eltableData[0].department
      } else {
        this.popupData.specificReason = null
        this.popupData.chargePerson = null
        this.popupData.department = null
      }
      this.batchdialogVisible = true
    },
    //编辑保存
    async confirmSave() {
      const params = {
        ids: this.ids, ...this.popupData
      }
      const { success } = await updateTianMaoComplainAsync(params);
      if (success) {
        this.$message({ message: "批量编辑成功", type: "success" });
        this.batchdialogVisible = false
        await this.getList()
      }
    },
    //复选框数据
    chooseCode(row) {
      this.ids = []
      this.eltableData = []
      this.eltableData = row;
      this.ids = row.map(item => item.id)
    },
    //时间
    async changeTime(e) {
      if (e) {
        e[1] = formatTime(dayjs(e[1]).endOf('day'), "YYYY-MM-DD HH:mm:ss");
        this.ListInfo.startDate = e[0]
        this.ListInfo.endDate = e[1]
      } else {
        this.ListInfo.startDate = null
        this.ListInfo.endDate = null
      }
    },
    //导出
    async exportProps() {
      this.pageLoading = true
      const { data } = await exportTianMaoComplainAsync(this.ListInfo)
      this.pageLoading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '投诉分析数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //获取列表
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges) {
        this.ListInfo.startDate = this.timeRanges[0];
        this.ListInfo.endDate = this.timeRanges[1];
      }
      const replaceArr = ['orderNo', 'goodsName', 'proCode']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await getTianMaoComplainList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.top_one,
.top_thr {
  width: 100%;
  margin-bottom: 10px;
}

.publicCss {
  width: 150px;
  margin-right: 5px;
}

.dialog-content {
  padding: 30px 0 5px 10px;
  margin-left: 250px;
}

.dialog-label {
  margin-right: 34px;
}

.textarea-container {
  padding: 20px 0 5px 10px;
  display: flex;
  align-items: center;
  margin-left: 250px;
}

.MaterialFlow {
  display: flex;
  justify-content: space-between;
  height: 93%;
  overflow: auto;
}

.Material {
  flex: 1;
}
</style>
