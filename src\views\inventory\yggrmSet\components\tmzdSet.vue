<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-button type="primary" @click="getList(true)">刷新</el-button>
                <el-button type="primary" v-if="checkPermission('costSettings')"
                    @click="handleAdd(true)">成本设置</el-button>
                <!-- <el-button type="primary" v-if="checkPermission('sellingPriceSettings')"
                    @click="handleAdd(false)">售价设置</el-button> -->
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'99%'">
        </vxetablebase>
        <!-- 阳光隔热膜设置 -->
        <el-dialog title="成本设置" :visible.sync="drawer" width="50%" :close-on-click-modal="false" v-dialogDrag>
            <el-button type="text" @click="addProps" v-if="isCbSet">新增一行</el-button>
            <el-table :data="formData" style="width: 95%;height:95%" max-height="400">
                <el-table-column label="#" type="index" width="40" />
                <el-table-column prop="norms" label="规格" width="auto">
                    <template #header="{ column }">
                        <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                    </template>
                    <template #default="{ row, $index }">
                        <el-select v-model="row.norms" placeholder="规格" @change="changeNorms($event, $index)">
                            <el-option v-for="item in ggList" :key="item.label" :label="item.label"
                                :value="item.label" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="actualLand" label="实际厚度" width="auto">
                    <template #default="{ row }">
                        <el-input-number v-model="row.actualLand" :max="10000" :precision="2" :controls="false"
                            label="实际厚度" class="iptCss" :min="0" disabled />
                    </template>
                </el-table-column>
                <el-table-column prop="type" label="类型" width="auto">
                    <template #default="{ row }">
                        <el-select v-model="row.type" placeholder="类型">
                            <el-option label="磨砂" value="磨砂" v-show="row.norms != 1" />
                            <el-option label="透明" value="透明" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="isEdgeGrinding" label="是否磨边" width="auto">
                    <template #default="{ row }">
                        <el-select v-model="row.isEdgeGrinding" placeholder="是否磨边">
                            <el-option label="是" :value="true" />
                            <el-option label="否" :value="false" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="proportion" label="比重" width="auto">
                    <template #default="{ row }">
                        <el-input-number v-model="row.proportion" :max="10000" :precision="4" :controls="false"
                            label="比重" class="iptCss" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="kgPriceCost" label="公斤价格" width="auto">
                    <template #default="{ row }">
                        <el-input-number v-model="row.kgPriceCost" :max="10000" :precision="4" :controls="false"
                            label="裁剪费(张" class="iptCss" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="croppingCost" label="裁剪费(张)" width="auto">
                    <template #default="{ row }">
                        <el-input-number v-model="row.croppingCost" :max="10000" :precision="4" :controls="false"
                            label="裁剪费(张" class="iptCss" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="packProcessCost" label="打包费(单)" width="auto">
                    <template #default="{ row }">
                        <el-input-number v-model="row.packProcessCost" :max="10000" :precision="4" :controls="false"
                            label="打包费(张)" class="iptCss" :min="0" />
                    </template>
                </el-table-column>
                <el-table-column prop="goodsCode" label="商品编码" width="auto">
                    <template #default="{ row }">
                        <el-tooltip class="item" effect="dark" :content="row.goodsCode" placement="top">
                            <el-input v-model="row.goodsCode" maxlength="20" placeholder="商品编码" class="iptCss" />
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="sheetSquareSaleAmount" label="操作" width="auto" v-if="isCbSet">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="formData.splice($index, 1)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="btnGroup">
                <el-button style="margin-right: 10px;" @click="drawer = false">取消</el-button>
                <el-button type="primary" @click="submit" v-throttle="3000">保存</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getCostSetByYGGRM, saveCostSetByYGGRM, saveSalePriceSetByYGGRM, getCostSetByTMZD, saveCostSetByTMZD } from "@/api/inventory/customNormsGoods";
const ys = {
    norms: '规格',
    actualLand: '实际厚度',
    type: '类型',
    proportion: '比重',
    kgPriceCost: '公斤价格',
    croppingCost: '裁剪费(张)',
    packProcessCost: '打包费(张)',
    goodsCode: '商品编码',
}
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'norms', label: '规格', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'actualLand', label: '实际厚度', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'type', label: '类型', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'isEdgeGrinding', label: '是否磨边',formatter: (row) => row.isEdgeGrinding ? '是' : '否' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'proportion', label: '比重', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'kgPriceCost', label: '公斤价格', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'croppingCost', label: '裁剪费(张)', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'packProcessCost', label: '打包费(张)', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', formatter: (row) => row.goodsCode !== null ? row.goodsCode : '' },
]
const ggList = [
    {
        label: '1.0',
        value: 0.4
    },
    {
        label: '1.5',
        value: 0.7
    },
    {
        label: '2.0',
        value: 1.1
    },
    {
        label: '3.0',
        value: 1.4
    },
    {
        label: '5.0',
        value: 2.2
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            ggList,
            ys,
            that: this,
            ListInfo: {
                orderBy: null,
                isAsc: false,
            },
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            drawer: false,
            formData: [
                {
                    norms: null,//规格
                    actualLand: null,//实际厚度
                    type: null,//类型
                    isEdgeGrinding:null,//是否磨边
                    proportion: null,//比重
                    kgPriceCost: null,//公斤价格
                    croppingCost: null,//裁剪费(张)
                    packProcessCost: null,//打包费(张)
                    goodsCode: null,//商品编码
                }
            ],
            isCbSet: true,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        //匹配数据
        changeNorms(e, i) {
            this.formData[i].actualLand = this.ggList.find(item => item.label == e).value
            this.formData[i].type = e ? null : this.formData[i].type
        },
        async submit() {
            const arr = ['norms', 'actualLand', 'type', 'proportion', 'kgPriceCost', 'croppingCost', 'packProcessCost', 'goodsCode']
            this.formData.forEach((item, i) => {
                for (const key in item) {
                    //根据key的值,提示对应的字段
                    if (arr.includes(key) && (item[key] === null || item[key] === '' || item[key] === undefined)) {
                        this.$message.error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                        throw new Error(`第${i + 1}行${ys[key]}为空的数据,请检查`)
                    }
                    //如果是比重,公斤价格,裁剪费,打包费,则判断是否小于0
                    if (key == 'proportion' || key == 'kgPriceCost' || key == 'croppingCost' || key == 'packProcessCost') {
                        if (item[key] < 0) {
                            this.$message.error(`第${i + 1}行${ys[key]}小于0的数据,请检查`)
                            throw new Error(`第${i + 1}行${ys[key]}小于0的数据,请检查`)
                        }
                    }
                }
            })
            const { success } = await saveCostSetByTMZD(this.formData)
            if (success) {
                await this.getList()
                this.$message.success('保存成功')
                this.drawer = false
            } else {
                this.$message.error('保存失败')
            }
        },
        addProps() {
            this.formData.push({
                norms: null,//规格
                actualLand: null,//实际厚度
                type: null,//类型
                proportion: null,//比重
                kgPriceCost: null,//公斤价格
                croppingCost: null,//裁剪费(张)
                packProcessCost: null,//打包费(张)
                goodsCode: null,//商品编码
            })
        },
        async handleAdd(isCbSet) {
            this.isCbSet = isCbSet
            const { data, success } = await getCostSetByTMZD(this.ListInfo)
            if (success) {
                this.formData = data
                this.drawer = true
            } else {
                this.$message.error('获取数据失败')
                this.drawer = true
            }
        },
        async getList(isFresh) {
            this.ListInfo.orderBy = isFresh ? null : this.ListInfo.orderBy
            this.ListInfo.isAsc = isFresh ? false : this.ListInfo.isAsc
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getCostSetByTMZD(this.ListInfo)
            if (success) {
                this.tableData = data
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取数据失败')
            }
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.iptCss {
    width: 70px;
}

.btnGroup {
    margin-top: 80px;
    display: flex;
    justify-content: end;
}

::v-deep .el-table__body-wrapper {
    min-height: 300px !important;
    max-height: 400px !important;
}

::v-deep .cell {
    padding-left: 3px;
}
</style>
