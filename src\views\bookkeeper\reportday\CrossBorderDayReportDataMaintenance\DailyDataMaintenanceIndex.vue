<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane label="国外汇率" name="tab1" style="height: 100%;">
        <ForeignExchangeRate :filter="Filter" ref="ForeignExchangeRate" style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="仓储费" name="tab2" style="height: 100%;" lazy>
        <storageFees style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="头程" name="tab4" style="height: 100%;" lazy>
        <headTrip style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="头程费用" name="tab18" style="height: 100%;" lazy>
        <headTripCost style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="头程-卸货上架费" name="tab13" style="height: 100%;" lazy>
        <loadingAndUnloadingFee style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="其他平台费用" name="tab19" style="height: 100%;" lazy>
        <otherHeadTripCost style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="代发成本" name="tab5" style="height: 100%;" lazy>
        <dropshippingCosts style="height: 100%;" />
      </el-tab-pane>
      <!-- <el-tab-pane label="客服工资" name="tab6" style="height: 100%;" lazy>
        <crossBorderServiceWages style="height: 100%;" />
      </el-tab-pane> -->
      <el-tab-pane label="预估费用" name="tab7" style="height: 100%;" lazy>
        <crossBorderEstimatedCost style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="快递费均价" name="tab8" style="height: 100%;" lazy>
        <crossBorderCourierFeeAverage style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="产品运费" name="tab9" style="height: 100%;" lazy>
        <crossBorderProductFreight style="height: 100%;" />
      </el-tab-pane>
      <!-- <el-tab-pane label="损耗下架" name="tab10" style="height: 100%;" lazy>
        <crossBorderLossOffShelf style="height: 100%;" />
      </el-tab-pane> -->
      <el-tab-pane label="公摊费率" name="tab11" style="height: 100%;" lazy>
        <crossBorderContributoryRate style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="出仓成本均价" name="tab12" style="height: 100%;" lazy>
        <averageUnitCost style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="海外仓-产品成本" name="tab14" style="height: 100%;" lazy>
        <productCost style="height: 100%;" />
      </el-tab-pane>
      <!-- <el-tab-pane label="海外仓-预估费用" name="tab15" style="height: 100%;" lazy>
        <estimateCosts style="height: 100%;" />
      </el-tab-pane> -->
      <el-tab-pane label="运营工资维护" name="tab16" style="height: 100%;" lazy>
        <operationMaintenance style="height: 100%;" />
      </el-tab-pane>
      <el-tab-pane label="厂家代发" name="tab17" style="height: 100%;" lazy>
        <factoryProxySend style="height: 100%;" />
      </el-tab-pane>


      
    </el-tabs>
  </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import ForeignExchangeRate from './ForeignExchangeRate'
import storageFees from './storageFees'
import headTrip from './headTrip'
import dropshippingCosts from './dropshippingCosts'
import crossBorderServiceWages from './crossBorderServiceWages'
import crossBorderEstimatedCost from './crossBorderEstimatedCost'
import crossBorderCourierFeeAverage from './crossBorderCourierFeeAverage'
import crossBorderProductFreight from './crossBorderProductFreight'
// import crossBorderLossOffShelf from './crossBorderLossOffShelf'
import crossBorderContributoryRate from './crossBorderContributoryRate'
import averageUnitCost from './averageUnitCost'
import loadingAndUnloadingFee from './loadingAndUnloadingFee'
import productCost from './productCost'
// import estimateCosts from './estimateCosts'
import operationMaintenance from './operationMaintenance'
import factoryProxySend from './factoryProxySend'
import headTripCost from './headTripCost'
import otherHeadTripCost from './otherHeadTripCost'


export default {
  name: "Users",
  components: {
    MyContainer, ForeignExchangeRate, storageFees, headTrip, dropshippingCosts, crossBorderEstimatedCost, crossBorderCourierFeeAverage, crossBorderProductFreight, crossBorderContributoryRate, averageUnitCost
    , productCost, loadingAndUnloadingFee,operationMaintenance,factoryProxySend,headTripCost,otherHeadTripCost
    // estimateCosts,crossBorderServiceWages,crossBorderLossOffShelf
  },
  data() {
    return {
      that: this,
      Filter: {
      },
      pageLoading: "",
      activeName: "tab1",
      shopList: [],
      userList: [],
      groupList: [],
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
    };
  },
  mounted() { },
  methods: {
    async onSearch() {
      if (this.activeName == 'tab1')
        this.$refs.ForeignExchangeRate.onSearch();
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
