<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <span>日期：</span>
        <el-date-picker v-model="timeRanges" type="date" placeholder="选择日期" :value-format="'yyyy-MM-dd'"
          @change="changeTime" style="width: 150px;margin-right: 5px;" :clearable="false" :editable="false">
        </el-date-picker>
      </div>
    </template>
    <vxetablebase :id="'dailyDataCheck20250114'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='false' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :treeProp="{ rowField: 'id', parentField: 'parentId' }">
    </vxetablebase>
    <template #footer>
      <!-- <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :pageSize="10"
        :sizes="[10, 20, 30, 40]" /> -->
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";

import { replaceSpace } from '@/utils/getCols'
import {  checkDayReportItem } from '@/api/bookkeeper/dayReport'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
const tableCols = [
  { width: '200px', align: 'left', prop: 'itemName', label: '数据项' ,treeNode: true, },
  { width: '160px', align: 'left', prop: 'sourceDecimal', label: '源数据值', },
  { width: '160px', align: 'left', prop: 'reportDecimal', label: '日报值', },
  { width: '160px', align: 'left', prop: 'diffDecimal', label: '差额', },

]
export default {
  name: "dailyConfirmation",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    dailyPaperList: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        platform: null,//平台
      },
      dailyReportType: null,
      timeRanges: null,
      tableCols,
      tableData: [],
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.ListInfo.startTime = this.dailyPaperList.startTime
    this.ListInfo.endTime = this.dailyPaperList.endTime
    this.ListInfo.platform = this.dailyPaperList.platform
    this.dailyReportType = this.dailyPaperList.dailyReportType
    this.timeRanges = this.dailyPaperList.endTime
    this.ListInfo.yearMonthDay = this.dailyPaperList.endTime
    await this.getList()
  },
  methods: {
    async onVerifyMethod(row, type) {
      this.$confirm('是否确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (type == 1 && row.importConfirmZt == 0) {
          row.importConfirmZt = 1
        } else if (type == 2 && row.clacConfirmZt == 0) {
          row.clacConfirmZt = 1
        }
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            dailyReportType: this.dailyReportType,
            platform: this.ListInfo.platform,
            dailyReportDate:this.timeRanges 
          }
        })
        const res = await insertDailyReportItemConfirmList(this.tableData)
        if (res.success) {
          await this.getList()
          this.$message.success('操作成功')
        } else {
          await this.getList()
        }
      }).catch(() => {
      });
    },
    async cellStyle(row, column, callback) {
      if (row.importConfirmZt == 1 && column.field == 'importConfirmZt') {
        callback({ backgroundColor: '#92d04f', color: '#668f263' })
      } else if (row.importConfirmZt == 0 && column.field == 'importConfirmZt') {
        callback({ color: '#fe3836' })
      } else if (row.clacConfirmZt == 0 && column.field == 'clacConfirmZt') {
        callback({ color: '#fe3836' })
      } else if (row.clacConfirmZt == 1 && column.field == 'clacConfirmZt') {
        callback({ backgroundColor: '#92d04f', color: '#668f263' })
      }
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e : null
      this.ListInfo.endTime = e ? e : null
      await this.getList()
    },
    async getList() {
      this.loading = true
      const { data, success } = await checkDayReportItem(this.ListInfo)
      if (success) {
        this.tableData = data
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
