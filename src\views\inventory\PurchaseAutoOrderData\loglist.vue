<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode"
                    placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 1)" title="款式编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 2)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-select filterable v-model.trim="ListInfo.brandIds" clearable multiple collapse-tags placeholder="采购"
                    class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select filterable v-model.trim="ListInfo.deptIds" clearable multiple collapse-tags placeholder="架构"
                    class="publicCss">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.supplierName" v-model="ListInfo.supplierName"
                    placeholder="供应商名称/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 3)" title="供应商名称"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <chooseWareHouse v-model="ListInfo.wmsId" placeholder="仓库" class="publicCss" />
                <div class="serchDay">
                    <div>最近在途天数</div>
                    <el-select v-model="ListInfo.recentlyInTransitType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.recentlyInTransitMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '最近在途天数'" />
                    </el-select>
                    <el-input-number v-if="ListInfo.recentlyInTransitType != '介于'" style="width: 60px;"
                        v-model.trim="ListInfo.recentlyInTransitMin" placeholder="天" maxlength="50" clearable
                        :max="99999" :min="0" class="publicCss" :precision="1" :controls="false" />
                    <number-range v-else :min.sync="ListInfo.recentlyInTransitMin" :maxNumber="99999"
                        :max.sync="ListInfo.recentlyInTransitMax" min-label=" 最小值" max-label="最大值" class="publicCss"
                        :precision="1" />
                </div>
                <div class="serchDay">
                    <div>平均在途天数</div>
                    <el-select v-model="ListInfo.averageInTransitType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.averageInTransitMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '历史平均在途天数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.averageInTransitMin" placeholder="天"
                        maxlength="50" clearable :max="99999" :min="0" class="publicCss" :precision="1"
                        :controls="false" v-if="ListInfo.averageInTransitType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.averageInTransitMin" :maxNumber="99999"
                        :max.sync="ListInfo.averageInTransitMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>触发进货天数</div>
                    <el-select v-model="ListInfo.triggerDaysInTransitType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.triggerDaysInTransitMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '触发进货天数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.triggerDaysInTransitMin"
                        placeholder="天" maxlength="50" clearable :max="99999" :min="0" class="publicCss" :precision="1"
                        :controls="false" v-if="ListInfo.triggerDaysInTransitType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.triggerDaysInTransitMin" :maxNumber="99999"
                        :max.sync="ListInfo.triggerDaysInTransitMax" min-label=" 最小值" max-label="最大值"
                        class="publicCss" />
                </div>
                <div class="serchDay">
                    <div>同系列连带天数</div>
                    <el-select v-model="ListInfo.jointPurchaseType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            if (e != '介于') {
                                ListInfo.jointPurchaseMax = undefined
                            }
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '同系列连带天数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.jointPurchaseMin" placeholder="天"
                        maxlength="50" clearable :max="99999" :min="0" class="publicCss" :precision="1"
                        :controls="false" v-if="ListInfo.jointPurchaseType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.jointPurchaseMin" :max.sync="ListInfo.jointPurchaseMax"
                        :maxNumber="99999" :precision="1" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <el-select v-model="ListInfo.isSemiOrder" placeholder="是否半成品开单" class="publicCss" clearable>
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                </el-select>
                <el-select v-model="ListInfo.isAutoOrder" placeholder="是否自动开单" class="publicCss" clearable>
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                </el-select>
                <el-select v-model="ListInfo.isRemark" placeholder="是否备注" class="publicCss" clearable>
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                </el-select>
                <el-select v-model="ListInfo.isCreatedPlan" placeholder="是否生成计划采购建议" class="publicCss" clearable>
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                </el-select>
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    style="width: 200px;" startPlaceholder="生成时间" endPlaceholder="生成时间" />
                <el-input v-model.trim="ListInfo.createdName" placeholder="生成人" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template #inTransitDayLast="{ row }">
                <div>{{ row.inTransitDayLast }}</div>
                <div>{{ row.inTransitDayAvg }}</div>
            </template>
            <template #inTransitDayLast_header>
                <div>最近在途天数</div>
                <div>平均在途天数</div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import numberRange from "@/components/number-range/index.vue";
import {
    getPurchaseNewPlanTurnDayBrandList,
    getPurchaseNewPlanTurnDayDeptList,
} from '@/api/inventory/purchaseordernew'
import { GetPurchaseAutoOrderDataLogPage, ExportPurchaseAutoOrderDataLog } from '@/api/inventory/purchaseAutoOrder'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '采购', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'deptName', label: '架构', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'supplierName', label: '供应商名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'wmsName', label: '仓库', },
    { width: 'auto', align: 'center', prop: 'inTransitDayLast', label: '最近在途天数', tipmesg: '取值计划采购建议对应全仓及分仓数据' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'triggerPurchaseDay', label: '触发进货天数', tipmesg: '默认N+1，向上取整，可保留一位小数，不可为0' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'equalStyleIncludedDay', label: '同系列连带天数', tipmesg: '默认（N+1）*0.6，向上取整，不可为0' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'maxPurchaseDay', label: '最大进货天数', tipmesg: '默认2N+1，向上取整，结果必须大于触发进货天数、同系列连带天数' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isSemiOrderStr', label: '是否半成品开单', tipmesg: '根据成品半成品关系判断' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'isCreatedPlanStr', label: '是否生成计划采购建议', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '生成时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '生成人', },
]
const operatorList = [
    { label: '大于', value: '大于' },
    { label: '小于', value: '小于' },
    { label: '等于', value: '等于' },
    { label: '介于', value: '介于' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, chooseWareHouse, inputYunhan, numberRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
            },
            operatorList,
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            brandlist: [],
            purchasegrouplist: [],
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        async init() {
            this.listLoading = true
            //采购
            var { data } = await getPurchaseNewPlanTurnDayBrandList();
            this.brandlist = data.map(item => { return { value: item.id, label: item.brandName }; });
            //架构
            let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
        },
        proCodeCallback(val, type) {
            console.log('2222');

            if (type == 1) {
                this.ListInfo.styleCode = val
            } else if (type == 2) {
                this.ListInfo.goodsCode = val
            } else {
                this.ListInfo.supplierName = val
            }
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportPurchaseAutoOrderDataLog(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '自动开单日志' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetPurchaseAutoOrderDataLogPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0px;
    }

    .serchDay {
        display: flex;
        align-items: center;
        font-size: 14px;
        gap: 5px;
    }
}
</style>
