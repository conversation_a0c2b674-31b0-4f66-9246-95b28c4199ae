<template>
    <container v-loading="pageLoading">
        <template #header>
        </template>
        <!--列表-->
        <vxetablebase :id="'goodsrapidretirelogdetail20230701'" :tableData='list' :tableCols='tableCols'
            @cellClick='cellclick' @select='selectchange' :tableHandles='tableHandles' :loading='listLoading' :border='true'
            :that="that" ref="vxetable" @sortchange='sortchange'>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

    </container>
</template>

<script>
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import { formatTime, formatNoLink } from "@/utils/tools";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getDirectorList } from '@/api/operatemanage/base/shop'
import { getGoodsRapidRetireLogDetailAsync } from "@/api/inventory/goodsrapidretire"

const tableCols = [
    { istrue: true, prop: 'platform', align: 'center', label: '操作平台', width: '120', sortable: 'custom', formatter: (row) => row.platformName },
    { istrue: true, prop: 'groupId', label: '运营组', width: '120', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'proCount', align: 'center', label: 'ID数', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'createdTime', label: '修改日期', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'createdUserName', label: '操作人', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'operatingResult', label: '是否卖完下架', width: '120', sortable: 'custom', formatter: (row) => row.operatingResult == true ? "同意" : "拒绝" },

];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];


const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminGoodsrapidretire',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan, },
    props: {
        filter:{}
    },

    data() {
        return {
            that: this,
            jstfilter: {
                goodsCode: null
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "", IsAsc: false },
            total: 0,
            sels: [],
            selids: [],
            groupList: [],
            directorList: [],
            listLoading: false,
            pageLoading: false,
            dialogAddVisible: false,
            onFinishLoading: false,
            dialogLoading: false
        };
    },

    async mounted() {
        await this.init();
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async init() {
            const res = await getGroupKeyValue({});
            this.groupList=res.data;
            const res1 = await getDirectorList({});
            this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getGoodsRapidRetireLogDetailAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },
        async onSetInfo(row, val) {
            
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>