<template>
    <container v-loading="pageLoading">
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :loading="listLoading">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag @close="onCloseDialog" v-loading="buscharDialog.loading">
            <span>
                <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
    import container from '@/components/my-container/noheader'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime, } from "@/utils";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { formatMinuteToHour } from "@/utils/tools";
    import buschar from '@/components/Bus/buscharY'
    import { getPurchaseInTransitSupplierPageList, getPurchaseInTransitSupplierChart } from '@/api/inventory/purchase';
    const tableCols = [
        { istrue: true, prop: 'supplier_id', label: '供应商ID', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'supplier', label: '供应商', width: '240', sortable: 'custom' },
        { istrue: true, prop: 'totalInTransitSpan', label: '平均总在途时长', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showBuscharDetail(row), formatter: (row) => formatMinuteToHour(row.totalInTransitSpan) },
        { istrue: true, prop: 'checkSpan', label: '平均财审时长', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showBuscharDetail(row), formatter: (row) => formatMinuteToHour(row.checkSpan) },
        { istrue: true, prop: 'transportSpan', label: '平均运输时长', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showBuscharDetail(row), formatter: (row) => row.transportSpan == 0 ? null : formatMinuteToHour(row.transportSpan) },
        { istrue: true, prop: 'warehousingSpan', label: '平均入库时长', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.showBuscharDetail(row), formatter: (row) => row.warehousingSpan == 0 ? null : formatMinuteToHour(row.warehousingSpan) },
    ]
    export default {
        name: 'intransitdetail',
        components: { cesTable, container, MyConfirmButton, MySearch, MySearchWindow, buschar },
        props: {
            filter: {},
        },
        data() {
            return {
                that: this,
                list: [],
                summaryarry: {},
                pager: { OrderBy: "supplier_id", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                buscharDialog: { visible: false, title: "供应商商品", loading: false, data: {} },
                formatMinuteToHour: formatMinuteToHour,
            };
        },
        async mounted() {
            await this.onSearch()
        },
        methods: {
            //查询第一页
            async onSearch() {
                if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startPurchaseDate = null;
                this.filter.endPurchaseDate = null;
                this.filter.startArrivalDate = null;
                this.filter.endArrivalDate = null;
                this.filter.startWarehousingDate = null;
                this.filter.endWarehousingDate = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startPurchaseDate = this.filter.timerange[0];
                    this.filter.endPurchaseDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择采购时间", type: "warning" });
                    return false;
                }
                if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                    this.filter.startArrivalDate = this.filter.timerange2[0];
                    this.filter.endArrivalDate = this.filter.timerange2[1];
                }
                if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
                    this.filter.startWarehousingDate = this.filter.timerange3[0];
                    this.filter.endWarehousingDate = this.filter.timerange3[1];
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getPurchaseInTransitSupplierPageList(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summaryarry;
                console.log(res.data, 'resdata')
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async showBuscharDetail(row) {
                var params = this.getCondition();
                if (params === false) {
                    this.$message({ message: "获取参数失败，请刷新后重试", type: "warning" });
                    return;
                }
                if (!row.supplier_id) {
                    this.$message({ message: "获取供应商ID失败，请刷新后重试", type: "warning" });
                    return;
                }
                params.supplier_id = row.supplier_id;
                this.buscharDialog.visible = true;
                this.buscharDialog.loading = true;
                const res = await getPurchaseInTransitSupplierChart(params)
                this.buscharDialog.loading = false;
                if (!res) {
                    this.$message({ message: "获取趋势图数据失败，请刷新后重试", type: "warning" });
                    return;
                }
                let me = this;
                let tooltip = {
                    // axisPointer: {
                    //     type: 'cross',
                    //     label: {
                    //         // formatter: function (params) {
                    //         //     if (isNaN(params.value))
                    //         //         return me.formatMinuteToHour(value);

                    //         //     return params.value;
                    //         // }
                    //     }
                    // },
                    valueFormatter: (value) => me.formatMinuteToHour(value)
                }
                res.tooltip = tooltip;
                res.yAxis.forEach(s => {
                    s.axisLabel = {
                        formatter: function (value) {
                            return (value / 60).toFixed(0) + "小时";
                        }
                    };
                });
                res.series.forEach(s => {
                    s.itemStyle = {
                        normal: {
                            label: {
                                show: true, position: 'top',
                                formatter: function (params) {
                                    return me.formatMinuteToHour(params.value)
                                },
                            },
                        }
                    };
                });

                this.buscharDialog.data = res;
                await this.$refs.buschar.initcharts();
                console.log(this.$refs.buschar)
            },
            async onCloseDialog() {
                this.buscharDialog.data = {};
            },
            //导出
            async onExportDetail() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                console.log(params, 'params')
                var res = await exportOrderWithholdListNew(params);
                loadingInstance.close();
                console.log(res, 'res')
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '采购-拼多多扣款详情_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
        },
    };
</script>

<style lang="scss" scoped></style>
