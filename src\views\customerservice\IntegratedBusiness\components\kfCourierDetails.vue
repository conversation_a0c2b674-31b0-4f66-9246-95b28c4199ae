<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="addTimeRanges" type="daterange" unlink-panels range-separator="至"
                    :clearable="false" start-placeholder="开始添加日期" end-placeholder="结束添加日期"
                    :picker-options="pickerOptions" style="width: 270px;margin-right: 10px;"
                    :value-format="'yyyy-MM-dd'" @change="changeTime($event, 'add')">
                </el-date-picker>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.business" placeholder="请选择对应类型" class="publicCss" clearable multiple
                    filterable collapse-tags>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.expressCompanyNameList" class="publicCss" placeholder="快递公司" clearable
                    collapse-tags multiple filterable style="width: 200px;">
                    <el-option label="无快递公司" value="无快递公司" />
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <el-input v-model.trim="ListInfo.addUserName" placeholder="添加人" maxlength="50" clearable
                    class="publicCss" />
                <inputYunhan ref="productCode1" :inputt.sync="ListInfo.expressNo" v-model="ListInfo.expressNo"
                    style="width: 200px;margin:0 10px 0 0;" class="publicCss" placeholder="物流单号/多条请按回车"
                    :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                    @callback="productCodeCallback" title="物流单号">
                </inputYunhan>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="downLoadFile">下载模版</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                <el-button type="primary" @click="importProps">导入</el-button>
                <el-button type="primary" @click="setTzTime">设置通知时间</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="150">
                    <template #default="{ row, $index }">
                        <div style="display: flex">
                            <el-button type="text"
                                :disabled="row.business != 1 || (row.status == 2 && row.business != 1)"
                                @click="uploadImage(row)">上传图片</el-button>
                            <el-button type="text" :disabled="row.status == 2" @click="onCancel(row)">取消</el-button>
                            <el-button type="text" @click="onLog(row)">日志</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 导入数据 -->
        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-tooltip>
            </el-upload>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <!-- 设置通知时间 -->
        <el-dialog title="设置通知时间" :visible.sync="setTzTimeVisible" width="80%" v-dialogDrag @close="onClosesetTzTime"
            v-loading="setTzTimeLoading">
            <div style="display: flex;">
                <el-select v-model="formData.expressCompanyName" placeholder="快递公司"
                    style="width: 270px;margin-right: 5px;" filterable clearable>
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select v-model="formData.business" placeholder="请选择对应类型" class="publicCss" clearable multiple
                    filterable collapse-tags>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getSetNoticeTimeProps(formData.expressCompanyName)"
                    style="margin-left: 10px;">搜索</el-button>

                <div style="margin-left: 100px;">
                    <el-button type="primary" @click="addSetNoticeCom()"
                        v-show="!addSetNoticeComShow">添加快递公司</el-button>
                    <el-select v-model="addSetNoticeComSel" placeholder="快递公司" filterable multiple collapse-tags
                        v-show="addSetNoticeComShow" style="width: 400px;">
                        <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                    </el-select>
                    <el-button type="primary" @click="addSetNoticeComSave()"
                        v-show="addSetNoticeComShow">确认添加</el-button>
                </div>

            </div>
            <el-table :data="formData.setTableData" style="width: 100%" max-height="600">
                <el-table-column prop="date" label="快递公司" width="250">
                    <template #default="{ row }">
                        <el-select v-model="row.expressCompanyName" placeholder="快递公司" filterable disabled
                            style="width: 240px;">
                            <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="workTime" label="工作时间">
                    <template #default="{ row, $index }">
                        <div style="display: flex;">
                            <el-time-select placeholder="起始时间" v-model="row.workStartTime" :picker-options="{
                                start: '00:00',
                                step: '00:15',
                                end: '23:45'
                            }">
                            </el-time-select>
                            <el-time-select placeholder="结束时间" v-model="row.workEndTime" :picker-options="{
                                start: '00:00',
                                step: '00:15',
                                end: '23:45',
                                minTime: row.workStartTime
                            }">
                            </el-time-select>
                            <el-button type="text" @click="batchEditProps($index, 'work')">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="通知间隔" width="160">
                    <template #default="{ row, $index }">
                        <div style="display: flex;">
                            <el-time-select v-model="row.noticeIntervalTimeStr" :picker-options="{
                                start: '00:30',
                                step: '00:30',
                                end: '48:00'
                            }" placeholder="通知间隔">
                            </el-time-select>
                            <el-button type="text"
                                @click="batchEditProps(row.noticeIntervalTimeStr, 'tz')">批量</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作网址" width="120">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.operateUrl" placeholder="操作网址" maxlength="400" clearable
                            class="publicCss" />
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作账号" width="120">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.operateAccount" placeholder="操作账号" maxlength="100" clearable
                            class="publicCss" />
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="操作密码" width="120">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.operatePassword" placeholder="操作密码" maxlength="100" clearable
                            class="publicCss" />
                    </template>
                </el-table-column>
                <el-table-column prop="actualPayAmountIsShow" label="是否显示支付金额" width="140">
                    <template #default="{ row, $index }">
                        <el-select v-model="row.actualPayAmountIsShow" placeholder="是否显示支付金额" clearable
                            class="publicCss">
                            <el-option label="是" :value=1 />
                            <el-option label="否" :value=0 />
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column lable="操作" width="80">
                    <template slot-scope="scope">
                        <el-button type="danger" @click="onDelCom(scope.row, scope.$index)">删除<i
                                class="el-icon-remove-outline"></i>
                        </el-button>
                    </template>
                </el-table-column>

            </el-table>
            <div class="btnGroup">
                <el-button @click="setTzTimeVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbitSetTime" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

        <!-- 导入数据 -->
        <el-dialog title="日志" :visible.sync="logVisible" width="40%" v-dialogDrag>
            <kfDetailsLogs v-if="logVisible" :expressInterceptId="expressInterceptId" style="height: 400px;" />
        </el-dialog>

        <el-dialog title="上传图片" :visible.sync="uploadVisible" width="20%" v-dialogDrag>
            <uploadimgFile ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true" :uploadInfo="addForm.picture"
                :keys="[1, 1]" @callback="getImg" :imgmaxsize="3" :limit="3" :multiple="true" v-if="uploadVisible">
            </uploadimgFile>
            <div class="btnGroup">
                <el-button @click="uploadVisible = false">取消</el-button>
                <el-button type="primary" @click="uploadSubmit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, downloadLink } from '@/utils/tools'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import kfDetailsLogs from './kfDetailsLogs.vue';
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    GetExpressInterceptPageList,
    ImporExpressInterceptAsync,
    ExportExpressInterceptList,
    GetExpressCompanyNameList,
    GetExpressInterceptSetNoticeList,
    SaveExpressInterceptSetNotice, DeleteExpressInterceptSetNotice, CancelExpressIntercept
} from '@/api/customerservice/expressIntercept'
import {
    GetToolkitExpressClaimOrderList,
    CancelToolkitExpressClaimNotice,
    ImportToolkitExpressClaimOrder,
    ExportToolkitExpressClaimOrderList,
    AddToolkitExpressClaimOrderPicture,
    GetToolkitExpressClaimSetNotice,
    DelToolkitExpressClaimSetNotice,
    SaveToolkitExpressClaimSetNotice
} from '@/api/customerservice/toolkitExpressClaimOrder'
const scanStatusResult = [
    { label: '无法识别', value: 0 },
    { label: '匹配成功', value: 1 },
    { label: '待再匹配', value: 2 },
    { label: '拆包', value: 3 },
    { label: '丢弃', value: 4 },
]
const options = [
    { value: 4, label: '虚假签收' },
    { value: 3, label: '催件' },
    { value: 2, label: '丢件' },
    { value: 1, label: '破损' },
    { value: 0, label: '改地址' },
]
const tableCols = [
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'send_date', label: '发货时间',
        formatter: (row) => row.send_date !== null ? (dayjs(row.send_date).format('YYYY-MM-DD') == '1901-01-01' ? '查询区间' : dayjs(row.send_date).format('YYYY-MM-DD')) : ''
    },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'business', label: '业务', formatter: (row) => row.business !== null ? options.find(item => item.value == row.business)?.label : row.business },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => row.status !== null ? statusList.find(item => item.value == row.status)?.label : row.status },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressNo', label: '快递单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressReceiveInfo', label: '快递收件人信息', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'newExpressReceiveInfo', label: '更改后快递收件人信息', },
    { width: 'auto', align: 'center', prop: 'picture1', label: '图片1', type: 'images' },
    { width: 'auto', align: 'center', prop: 'picture2', label: '图片2', type: 'images' },
    { width: 'auto', align: 'center', prop: 'picture3', label: '图片3', type: 'images' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualPayAmount', label: '支付金额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCost', label: '订单成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'noticeTime', label: '通知时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operateTime', label: '机器人操作时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'cancelNoticeTime', label: '取消通知时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '添加时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '添加人', },
]
const statusList = [
    { value: 0, label: '已通知' },
    { value: 1, label: '待通知' },
    { value: 2, label: '取消' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan, kfDetailsLogs, uploadimgFile
    },
    data() {
        return {
            logVisible: false,
            uploadVisible: false,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            statusList,
            options,
            that: this,
            addForm: {
                picture: []
            },
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startAddTime: null,//添加开始时间
                endAddTime: null,//添加结束时间
                startNoticeTime: null,//通知开始时间
                endNoticeTime: null,//通知结束时间
                startOperateTime: null,//操作开始时间
                endOperateTime: null,//操作结束时间
                orderNoInner: null,//内部订单号
                orderNoInnerList: [],//内部订单号列表
                newOrderNoInner: null,//新内部订单号
                newOrderNoInnerList: [],//新内部订单号列表
                addUserName: null,//添加人
            },
            scanStatusResult: scanStatusResult,
            timeRanges: [],
            rebotNoticeTime: [],
            rebotOperateTime: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false,
            importVisible: false,
            file: null,
            setTzTimeVisible: false,
            setTzTimeLoading: false,
            formData: {
                expressCompanyName: null,//快递公司
                setTableData: [
                    {
                        expressCompanyName: null,//快递公司
                        workStartTime: null,//开始时间
                        workEndTime: null,// 结束时间
                        noticeIntervalTime: null,//通知间隔时间
                        operateUrl: null,//操作地址
                        operateAccount: null,//操作账号
                        operatePassword: null,//操作密码
                        noticeIntervalTimeStr: null,//通知间隔时间
                    }
                ],//批量设置通知时间
            },
            importLoading: false,
            fileList: [],
            addTimeRanges: [],
            kdCompany: [],//快递公司
            addSetNoticeComSel: [],
            addSetNoticeComShow: false,
            expressInterceptId: null,//拦截id
            inlineRow: {}
        }
    },
    async mounted() {
        await this.getKdCompany()
        await this.getList()
    },
    methods: {
        getImg(data) {
            if (data) {
                this.addForm.picture = data
            }
        },
        uploadImage(row) {
            this.inlineRow = row
            this.uploadVisible = true
        },
        async uploadSubmit() {
            if (this.addForm.picture.length === 0) {
                this.$message.error('请上传图片')
                return
            }
            const { id, expressNo } = this.inlineRow
            const { success } = await AddToolkitExpressClaimOrderPicture({
                id,
                expressNo,
                picture: this.addForm.picture.map(item => item.url)
            })
            if (!success) return
            this.addForm.picture = []
            this.uploadVisible = false
            this.$message.success('上传成功')
            await this.getList()
        },
        onCancel(row) {
            this.$confirm('确定要取消吗?, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await CancelToolkitExpressClaimNotice({ expressNo: row.expressNo })
                if (!success) return
                this.$message.success('取消成功')
                this.getList()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        onLog(row) {
            this.expressInterceptId = row.id
            this.logVisible = true
        },
        async getKdCompany() {
            const { data, success } = await GetExpressCompanyNameList()
            if (success) {
                this.kdCompany = data
            }
        },
        downLoadFile() {
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250712/1943966609077411841.xlsx', '综合业务明细导入模版.xlsx');
        },
        async sumbitSetTime() {
            this.formData.setTableData.forEach((item, i) => {
                if ((item.workStartTime && !item.workEndTime) || (!item.workStartTime && item.workEndTime)) {
                    this.$message.error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                    throw new Error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                }
                if ((item.workStartTime && item.workEndTime) && !item.noticeIntervalTimeStr) {
                    this.$message.error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                    throw new Error(`第${i + 1}行通知间隔,工作开始时间/工作结束时间/通知间隔必须同时存在`)
                }
                if (!(item.workStartTime && item.workEndTime) && item.noticeIntervalTimeStr) {
                    this.$message.error(`第${i + 1}行通知间隔,工作开始时间和工作结束时间必须同时存在`)
                    throw new Error(`第${i + 1}行通知间隔,工作开始时间和工作结束时间必须同时存在`)
                }
            })
            this.setTzTimeLoading = true;
            const { success } = await SaveToolkitExpressClaimSetNotice(this.formData.setTableData);
            this.setTzTimeLoading = false;
            if (success) {
                this.$message({
                    message: '保存成功',
                    type: 'success'
                })
                this.formData.expressCompanyName = null
                this.setTzTimeVisible = false
            } else {

            }
        },
        //获取设置通知时间数据
        async getSetNoticeTimeProps(expressCompanyName) {
            const params = expressCompanyName ? { ...this.ListInfo, expressCompanyName } : { ...this.ListInfo , orderBy: '' }
            const { data, success } = await GetToolkitExpressClaimSetNotice(params)
            if (success) {
                data.list.forEach(item => {
                    item.workStartTime = item.workStartTime ? dayjs(item.workStartTime).format('HH:mm') : ''
                    item.workEndTime = item.workEndTime ? dayjs(item.workEndTime).format('HH:mm') : ''

                    if (item.orderCost == null) {
                        item.orderCost = undefined
                    }
                })
                this.formData.setTableData = data.list
            }
        },
        //设置通知时间
        async setTzTime() {
            this.formData.expressCompanyName = null;
            this.setTzTimeVisible = true;
            this.setTzTimeLoading = true;
            await this.getSetNoticeTimeProps();
            this.setTzTimeLoading = false;
        },
        batchEditProps(e, type) {
            const map = {
                work: '工作时间',
                tz: '通知时间',
                cost: '订单成本-拦截标准'
            }
            this.$confirm(`此操作将批量操作${map[type]}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.formData.setTableData.forEach((item, i) => {
                    if (type === 'work') {
                        item.workStartTime = this.formData.setTableData[e].workStartTime ? this.formData.setTableData[e].workStartTime : null
                        item.workEndTime = this.formData.setTableData[e].workEndTime ? this.formData.setTableData[e].workEndTime : null
                    }
                    else if (type === 'cost') {
                        item.orderCost = e
                    }
                    else {
                        item.noticeIntervalTimeStr = e
                    }
                })
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在上传中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await ImportToolkitExpressClaimOrder(form).then(({ success }) => {
                if (success) {
                    this.$message.success('上传成功，正在排队导入中...')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async productCodeCallback(val) {
            this.ListInfo.expressNo = val;
        },
        orderNoInnerBack(val) {
            this.ListInfo.orderNoInner = val;
        },
        newOrderNoInnerBack(val) {
            this.ListInfo.newOrderNoInner = val;
        },
        async changeTime(e, type) {
            if (type == 'add') {
                this.ListInfo.startAddTime = e ? e[0] : null
                this.ListInfo.endAddTime = e ? e[1] : null
            } else if (type == 'notice') {
                this.ListInfo.startNoticeTime = e ? e[0] : null
                this.ListInfo.endNoticeTime = e ? e[1] : null
            } else {
                this.ListInfo.startOperateTime = e ? e[0] : null
                this.ListInfo.endOperateTime = e ? e[1] : null
            }
            await this.getList()
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportToolkitExpressClaimOrderList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '客服综合业务明细' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.addTimeRanges && this.addTimeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.startAddTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endAddTime = dayjs().format('YYYY-MM-DD')
                this.addTimeRanges = [this.ListInfo.startAddTime, this.ListInfo.endAddTime]
            }
            const replaceArr = ['expressNo', 'addUserName', 'orderNoInner']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            try {
                const { data: { list, total }, success } = await GetToolkitExpressClaimOrderList(this.ListInfo)

                if (success) {
                    this.tableData = list
                    this.total = total
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        onClosesetTzTime() {
            this.addSetNoticeComSel = [];
            this.addSetNoticeComShow = false;
        },
        addSetNoticeCom() {
            //添加快递公司
            this.addSetNoticeComSel = [];
            this.addSetNoticeComShow = true;
        },
        addSetNoticeComSave() {
            //添加快递公司
            if (this.addSetNoticeComSel.length <= 0) {
                this.addSetNoticeComShow = false;
                return;
            }
            this.addSetNoticeComSel.forEach(f => {
                if (this.formData.setTableData.find(w => w.expressCompanyName == f) == null) {
                    this.formData.setTableData.unshift({
                        id: 0,
                        expressCompanyName: f,
                        workStartTime: null,
                        workEndTime: null,
                        noticeIntervalTimeStr: null,
                    });
                }
            });
            this.addSetNoticeComShow = false;
        },
        async onDelCom(row, index) {
            if (row.id) {
                this.$confirm('确定要删除吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await DelToolkitExpressClaimSetNotice({ id: row.id });
                    if (res?.success) {
                        this.formData.setTableData.splice(index, 1)
                        this.$message.success('删除成功')
                    }
                }).catch(() => {
                });
            }
            else {
                this.formData.setTableData.splice(index, 1)
                this.$message.error('删除成功')
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
    align-items: center;

    .publicCss {
        width: 150px;
        margin: 0 10px 0 0;
        align-items: center;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

::v-deep(.el-select__tags-text) {
    max-width: 40px;
}
</style>
