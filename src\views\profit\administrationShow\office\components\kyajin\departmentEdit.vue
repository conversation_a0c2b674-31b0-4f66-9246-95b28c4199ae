<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="165px" class="demo-ruleForm">
        <el-form-item label="月初押金总额" prop="startMonthTotalDeposit">
          <inputNumberYh v-model="ruleForm.startMonthTotalDeposit" :placeholder="'请输入月初押金总额'" class="publicCss" />
        </el-form-item>
        <el-form-item label="当月新增押金金额" prop="addTotalDeposit">
          <inputNumberYh v-model="ruleForm.addTotalDeposit" :placeholder="'请输入当月新增押金金额'" class="publicCss" />
        </el-form-item>
        <el-form-item label="当月新增押金金额明细" prop="addDetails">
          <el-input v-model.trim="ruleForm.addDetails" placeholder="请输入当月新增押金金额明细" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="当月退回押金金额" prop="refund">
          <inputNumberYh v-model="ruleForm.refund" :placeholder="'请输入当月退回押金金额'" class="publicCss" />
        </el-form-item>
        <el-form-item label="当月退回押金明细" prop="refundDetails">
          <el-input v-model.trim="ruleForm.refundDetails" placeholder="请输入当月退回押金明细" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="当月违规扣除押金" prop="violation">
          <inputNumberYh v-model="ruleForm.violation" :placeholder="'请输入当月违规扣除押金'" class="publicCss" />
        </el-form-item>
        <el-form-item label="当月违规扣除明细" prop="violationDetails">
          <el-input v-model.trim="ruleForm.violationDetails" placeholder="请输入当月违规扣除明细" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { depositSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        startMonthTotalDeposit: '',
        addTotalDeposit: '',
        addDetails: '',
        refund: '',
        refundDetails: '',
        violation: '',
        violationDetails: '',
      },
      rules: {
        startMonthTotalDeposit: [
          { required: true, message: '请输入月初押金总额', trigger: 'blur' },
        ],
        addTotalDeposit: [
          { required: true, message: '请输入当月新增押金金额', trigger: 'blur' },
        ],
        addDetails: [
          { required: true, message: '请输入当月新增押金金额明细', trigger: 'blur' },
        ],
        refund: [
          { required: true, message: '请输入当月退回押金金额', trigger: 'blur' },
        ],
        refundDetails: [
          { required: true, message: '请输入当月退回押金明细', trigger: 'blur' },
        ],
        violation: [
          { required: true, message: '请输入当月违规扣除押金', trigger: 'blur' },
        ],
        violationDetails: [
          { required: true, message: '请输入当月违规扣除明细', trigger: 'blur' },
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await depositSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
