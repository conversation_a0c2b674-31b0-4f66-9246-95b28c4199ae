<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="发生时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable
                        style="width: 100px">
                        <el-option label="天猫" :value="1"> </el-option>
                        <el-option label="拼多多" :value="2"> </el-option>
                        <el-option label="阿里巴巴" :value="4"> </el-option>
                        <el-option label="抖音" :value="6"> </el-option>
                        <el-option label="京东" :value="7"> </el-option>
                        <el-option label="淘工厂" :value="8"> </el-option>
                        <el-option label="淘宝" :value="9"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺:">
                    <!-- <el-select filterable v-model="filter.shopCode" placeholder="店铺" multiple collapse-tags clearable style="width: 160px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopName"/>
                    </el-select> -->
                    <inputYunhan ref="CategoryName" v-model="filter.shopCode" :inputt.sync="filter.shopCode"
                        placeholder="店铺" :clearable="true" @callback="callbackCategoryshopCode" title="店铺"></inputYunhan>
                </el-form-item>
                <el-form-item label="账务类型/账单项目:">
                    <el-select filterable v-model="filter.BillType" placeholder="请选择账单类型"
                        @change="onchangeplatformCategoryName" clearable multiple collapse-tags style="width: 130px">
                        <el-option v-for="item in BillingSummaryBillingItemList" :key="item.billingItem"
                            :label="item.billingItem" :value="item.billingItem"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="类目:">
                    <el-select filterable v-model="filter.Category" placeholder="请选择类目" clearable multiple collapse-tags
                        style="width: 130px">
                        <el-option v-for="item in BillingSummaryCategoryList" :key="item.category" :label="item.category"
                            :value="item.category"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="CreateCategoryDialog">类目创建</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="SettingsCategoryDialog">设置类目</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="NewAddTypeShow">新增类型查看</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="exportProps">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-alert title="温馨提示：拼多多请选择两个文件上传(最多两个文件)" type="success" :closable="false" style="margin-bottom:10px;">
            </el-alert>
            <span :gutter="20">
                <el-row >
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-date-picker style="width: 100%" v-model="importDialog.filter.YearMonthDay" type="date" format="yyyyMMdd"
                        value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                      </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-select filterable v-model="importDialog.filter.PlatForm" placeholder="请选择平台" clearable>
                            <el-option label="淘系" value="1" />
                            <el-option label="拼多多" value="2" />
                            <el-option label="阿里巴巴" value="4" />
                            <el-option label="抖音" value="6" />
                            <el-option label="京东" value="7" />
                            <el-option label="淘工厂" value="8" />
                            <el-option label="苏宁" value="10" />
                        </el-select>
                    </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true"  :limit="2" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                            :file-list="fileList">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog> -->
        <el-dialog title="类目创建" :visible.sync="dialogFormVisible" v-dialogDrag width="50%">
            <span :gutter="20">
                <el-row>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-select filterable v-model="CreateCategoryFilter.platform" @change="onchangeplatform"
                            placeholder="请选择平台" clearable style="width: 150px">
                            <el-option v-for="item in optionsList" :label="item.label" :value="item.value"> </el-option>
                            <!-- <el-option label="拼多多" :value="2"> </el-option>
                            <el-option label="阿里巴巴" :value="4"> </el-option>
                            <el-option label="抖音" :value="6"> </el-option>
                            <el-option label="京东" :value="7"> </el-option>
                            <el-option label="淘工厂" :value="8"> </el-option> -->

                        </el-select>
                    </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <el-select filterable v-model="CreateCategoryFilter.billingItem" placeholder="请选择账单类型" clearable
                            style="width: 150px" @change="changeBill" collapse-tags>
                            <el-option v-for="item in BillingSummaryBillingItemList" :key="item.billingItem"
                                :label="item.billingItem" :value="item.billingItem"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <div style="float: right;color: #409EFF;cursor: pointer;" @click="addCategoryProps">新增一行</div>
                    </el-col>
                    <!-- <el-col :xs="4" :sm="6" :md="8" :lg="6">
                        <inputYunhan  ref="CategoryName" v-model="CreateCategoryFilter.CategoryName" :inputt.sync="CreateCategoryFilter.CategoryName" placeholder="类目" :clearable="true" @callback="callbackCategoryName" title="类目" ></inputYunhan>
                    </el-col> -->
                </el-row>
                <div style="margin: 20px 0;"></div>
                <!-- <el-row>
                  <el-input style="width: 500px" type="textarea" :autosize="{ minRows: 10, maxRows: 15}" placeholder="请输入类目" v-model="CreateCategoryFilter.CategoryName"></el-input>
                </el-row> -->
                <el-table :data="categoryList" max-height="400" style="margin-bottom: 10px;" cell-dblclick="cellClick">
                    <el-table-column type="index" width="50" />
                    <el-table-column prop="platForm" label="平台">
                        <template #default="{ row, $index }">
                            {{ optionsList.filter(item => item.value == row.platForm)[0].label }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="billingItem" label="账务类型/账单项目">
                    </el-table-column>
                    <el-table-column prop="category" label="类目名称">
                        <template #default="{ row, $index }">
                            <el-input v-model="row.category" placeholder="类目名称" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="{ row, $index }">
                            <el-button type="danger" @click="deleteProps(row, $index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="detailTotal"
                    @page-change="detailPagechange" @size-change="detailSizechange" style="margin-top: 20px;" />
            </span>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="addCategoryList(categoryList, true)">确 定</el-button>
            </div>
        </el-dialog>
        <el-dialog title="设置类目" :visible.sync="SettingsCategorydialogFormVisible" v-dialogDrag v-loading="dialogLoading">
            <div>
                <el-checkbox-group v-model="SettingsCategoryFilter.Category">
                    <el-checkbox v-for="item in BillingSummaryCategoryCheckboxList" :label="item.category"
                        :key="item.category">{{ item.category }}</el-checkbox>
                </el-checkbox-group>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="SettingsCategorydialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="SettingsCategory">确 定</el-button>
            </div>
        </el-dialog>
        <el-dialog title="类型查看" :visible.sync="typeviewDialog" v-dialogDrag>
            <div>
                <el-select filterable v-model="ListInfo.platForm" placeholder="请选择平台" clearable style="width: 130px"
                    @change="NewAddTypeShow(ListInfo)">
                    <el-option label="天猫" :value="1"> </el-option>
                    <el-option label="拼多多" :value="2"> </el-option>
                    <el-option label="阿里巴巴" :value="4"> </el-option>
                    <el-option label="抖音" :value="6"> </el-option>
                    <el-option label="京东" :value="7"> </el-option>
                    <el-option label="淘工厂" :value="8"> </el-option>
                    <el-option label="淘宝" :value="9"> </el-option>
                </el-select>
            </div>
            <template>
                <ces-table ref="viewtable" :isIndex='true' :hasexpand='true' style="height: 350px;"
                    :tableData='NewAddTypelist' :tableCols='typeviewtableCols' :isSelection="false" :isSelectColumn="false"
                    :loading="listLoading" @sortchange='sortchange3' @checkbox-range-end="chooseCode">
                </ces-table>
            </template>
            <template #footer>
                <my-pagination ref="pager" :total="viewtotal" @page-change="detailPagechange"
                    @size-change="detailSizechange" />
            </template>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatPlatform, formatPlatformtx } from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { importBillingCharge as importBillFee, importPDDBillingCharge, billingSummary, getBillingSummaryBillingItem, addBillingSummaryCategory, settingsBillingSummaryCategory, getBillingSummaryNewAddType, getBillingSummaryCategorySelect, BillingSummaryCategoryCheckbox, getBillingSummaryCategory, deleteBillingSummaryCategory, addBillingSummaryCategoryAsync,exportBillingSummary } from '@/api/bookkeeper/reportdayV2'
import { getNewPddBillingCharge, exportNewPddBillingCharge } from '@/api/bookkeeper/reportday'
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '发生时间', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'platForm', label: '平台', tipmesg: '', width: '200', sortable: 'custom', formatter: (row) => formatPlatform(row.platForm), type: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', tipmesg: '', width: '200', sortable: 'custom', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'billingItem', label: '账务类型/账单项目', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'category', label: '类目', tipmesg: '', width: 'auto', type: "click", handle: (that, row) => that.SettingsCategoryDialog(row) },
    { istrue: true, prop: 'amountIncome', label: '金额', tipmesg: '收入金额-支出金额', width: 'auto', sortable: 'custom', },

]

const typeviewtableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '发生时间', tipmesg: '', width: '278', },
    { istrue: true, prop: 'platForm', label: '平台', tipmesg: '', width: '300', formatter: (row) => formatPlatform(row.platForm), type: 'custom' },
    { istrue: true, prop: 'billingItem', label: '账务类型/账单项目', tipmesg: '', width: '300', },
]

const tableHandles = [
    // { label: "导入", handle: (that) => that.startImport() },
];

{/* <el-option label="淘系" :value="1"> </el-option>
                            <el-option label="拼多多" :value="2"> </el-option>
                            <el-option label="阿里巴巴" :value="4"> </el-option>
                            <el-option label="抖音" :value="6"> </el-option>
                            <el-option label="京东" :value="7"> </el-option>
                            <el-option label="淘工厂" :value="8"> </el-option> */}

const optionsList = [
    {
        label: "天猫",
        value: 1
    },
    {
        label: "拼多多",
        value: 2
    },
    {
        label: "阿里巴巴",
        value: 4
    },
    {
        label: "抖音",
        value: 6
    },
    {
        label: "京东",
        value: 7
    },
    {
        label: "淘工厂",
        value: 8
    },
    {
        label: "淘宝",
        value: 9
    }
]

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton, inputYunhan },

    data() {
        return {
            that: this,
            optionsList,
            dialogLoading: false,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                shopCode: null,
                BillType: null,
                platform: null
            },
            //platformlist: platformlist,
            importDialog: {
                filter: {
                    YearMonthDay: null,
                    PlatForm: null
                }
            },
            list: [],
            shopList: [],
            selids: [],
            summaryarry: {},
            pager: { OrderBy: "yearMonthDay", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            typeviewtableCols: typeviewtableCols,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: [],
            BillingSummaryBillingItemList: [],
            BillingSummaryCategoryList: [],
            BillingSummaryCategoryCheckboxList: [],
            dialogFormVisible: false,
            SettingsCategorydialogFormVisible: false,
            CreateCategoryFilter: {
                platform: null,
                billingItem: null,
                CategoryName: null

            },
            BillingSummaryCategorySelectFilter:
            {
                platform: null,
                billingItem: null
            },
            SettingsCategoryFilter: {
                Category: [],
                ids: []
            },
            NewAddTypelist: [],
            NewAddTypelistFilter: {
                PlatForm: null,
            },
            typeviewDialog: false,
            ListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                platForm: null,
            },
            viewtotal: 0,
            categoryListInfo: {
                currentPage: 1,//当前页
                pageSize: 50,//每页条数
                orderBy: null,//排序字段
                isAsc: true,//是否升序
                platform: null,//平台
                billType: [],//账务类型
            },
            categoryList: [],
            addList: [],
            detailTotal: 0,
            exportInfo: null
        };
    },
    async mounted() {
        await this.onSearch()

    },

    methods: {
        async exportProps() {
            const { data } = await exportBillingSummary(this.exportInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '账单费用汇总' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //页面数量改变
        detailSizechange(val) {
            this.categoryListInfo.currentPage = 1;
            this.categoryListInfo.pageSize = val;
            this.CreateCategoryDialog();
        },
        //当前页改变
        detailPagechange(val) {
            this.categoryListInfo.currentPage = val;
            this.CreateCategoryDialog();
        },
        async addCategoryList(list, type) {
            list.forEach(item => {
                if (item.category == null) {
                    throw ('请输入类目名称')
                }
            })
            console.log(list, 'list');
            let addList = []
            list.forEach(item => {
                if (item.isFrontEnd == 1) {
                    addList.push(item)
                }
            })
            console.log(addList, 'addList');
            const { data, success } = await addBillingSummaryCategoryAsync({ AddBillingSummaryCategory: addList })
            if (success) {
                if (type) {
                    this.dialogFormVisible = false
                }
            }
        },
        addCategoryProps() {
            //如果没选平台,就提示
            if (this.categoryListInfo.platform == null) {
                this.CreateCategoryFilter.billingItem = null;
                this.$message.warning('请先选择平台');
                return
            }
            //如果没选账务类型,就提示
            if (this.CreateCategoryFilter.billingItem == null) {
                this.$message.warning('请先选择账务类型');
                return
            }
            this.categoryList.push({
                platForm: this.CreateCategoryFilter.platform,
                billingItem: this.CreateCategoryFilter.billingItem,
                category: null,
                isFrontEnd: 1
            })
        },
        cellClick(row, column, cell) {
            console.log(row, column, cell, 'row, column, cell');
        },
        deleteProps(row, i) {
            console.log(row.id, 'id');
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteBillingSummaryCategory(row.id)
                if (success) {
                    this.CreateCategoryDialog()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        callbackCategoryshopCode(val) {
            // if(val != )
            console.log(val, 'val');
            let a = val
            this.filter.shopCode = val
            console.log(this.filter.shopCode, 'this.filter.shopCode');
        },
        //点击复选框
        chooseCode(row) {
            console.log(row, 'row');
            //将row这个数组里面的styleCode放入setCategoryName里面的styleCodes
            // this.setCategoryName.styleCodes = row.map(item => item.styleCode)

        },
        sortchange3({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = null
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.NewAddTypeShow(this.ListInfo)
            }
        },
        async NewAddTypeShow(row) {
            console.log(this.ListInfo.platForm, 'platForm');
            const { data } = await getBillingSummaryNewAddType(row)
            this.viewtotal = (data.list.length > 0) ? data.list.length : 0
            this.NewAddTypelist = data.list;
            this.ListInfo.orderBy = null
            this.typeviewDialog = true;
        },
        SettingsCategoryview() {

        },
        //页面数量改变
        detailSizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.NewAddTypeShow(this.ListInfo);
        },
        //当前页改变
        detailPagechange(val) {
            this.ListInfo.currentPage = val;
            this.NewAddTypeShow(this.ListInfo);
        },
        async SettingsCategoryDialog() {
            if (this.selids.length == 0) {
                this.$message.warning('请选择需要设置类目的行');
                return
            }
            const res1 = await BillingSummaryCategoryCheckbox({ ids: this.selids });
            if (res1.data.length > 0) {
                this.BillingSummaryCategoryCheckboxList = res1.data;
                this.SettingsCategorydialogFormVisible = true;
                //拉一次查询列表
                this.onSearch()
            }
            else {
                this.$message.warning('选择的数据暂无类目,请先创建类目！！！');
                return
            }


        },
        async SettingsCategory() {
            this.SettingsCategoryFilter.ids = this.selids;
            this.dialogLoading = true
            const res = await settingsBillingSummaryCategory(this.SettingsCategoryFilter);
            if (res.code == 1) {
                this.$message.success('设置成功！');
                this.dialogLoading = false
                this.SettingsCategorydialogFormVisible = false;
            }
            this.BillingSummaryCategoryCheckboxList = [];
            this.SettingsCategoryFilter.ids = [];
            this.SettingsCategoryFilter.Category = [];
        },
        //打开创建类目弹窗,获取账务数据
        async CreateCategoryDialog() {
            this.onchangeplatform(false)
            this.categoryList = []
            this.categoryListInfo.billType = [];
            this.categoryListInfo.platform = null;
            this.CreateCategoryFilter.platform = null;
            this.CreateCategoryFilter.billingItem = null;
            const { data, success } = await getBillingSummaryCategory(this.categoryListInfo)
            if (success) {
                this.categoryList = data.list
                console.log(data, 'data');
                this.dialogFormVisible = true;
                this.detailTotal = data.total
            }
        },

        async addCategory() {
            const res = await addBillingSummaryCategory(this.CreateCategoryFilter);
            if (res.code == 1) {
                this.$message.success('创建成功！');
                this.dialogFormVisible = false;
            }
        },
        async callbackCategoryName(val) {
            this.CreateCategoryFilter.CategoryName = val;
        },
        async onchangeplatformBillingSummary(val) {
            this.categoryListInfo.platform = val;
            const { data, success } = await getBillingSummaryCategory(this.categoryListInfo)
            if (success) {
                this.categoryList = data.list
            }
            const res1 = await getBillingSummaryBillingItem({ platform: val });
            this.BillingSummaryBillingItemList = res1.data
        },
        async changeBill(val) {
            //如果没选平台,就提示
            if (this.categoryListInfo.platform == null) {
                this.CreateCategoryFilter.billingItem = null;
                this.$message.warning('请先选择平台');
                return
            }
            if (!val) {
                this.categoryListInfo.billType = []
            } else {
                this.categoryListInfo.billType = [val];
            }
            console.log(this.categoryListInfo.billType, 'this.categoryListInfo.billType');
            this.addCategoryList(this.categoryList)
            if (!this.CreateCategoryFilter.billingItem) {
                this.categoryListInfo.billType = null
            }
            const { data, success } = await getBillingSummaryCategory(this.categoryListInfo)
            if (success) {
                this.categoryList = data.list
            }
        },
        async onchangeplatformCategoryName(val) {

            const res1 = await getBillingSummaryCategorySelect({ platform: this.BillingSummaryCategorySelectFilter.platform, billingItem: val });
            this.BillingSummaryCategoryList = res1.data
        },
        // async onExport() {
        //  if (this.onExporting) return;
        //  try{
        //     this.filter.startTime = null;
        //     this.filter.endTime = null;
        //     if (this.filter.timerange) {
        //             this.filter.startTime = this.filter.timerange[0];
        //             this.filter.endTime = this.filter.timerange[1];
        //         }
        //     this.uploadLoading = true;
        //     const params = {...this.pager,...this.filter}
        //     var res= await exportNewPddBillingCharge(params);
        //     if(!res?.data) return
        //     const aLink = document.createElement("a");
        //     let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        //     aLink.href = URL.createObjectURL(blob)
        //     aLink.setAttribute('download','新版拼多多账单费用_' + new Date().toLocaleString() + '.xlsx' )
        //     this.uploadLoading = false;

        //     aLink.click()
        //     }catch(err){
        //       console.log(err)
        //       console.log(err.message);
        //     }
        //   this.onExporting=false;
        //  },

        //获取店铺
        async onchangeplatform(val) {
            //如果没有平台就清空服务类型
            if (!this.CreateCategoryFilter.platform) {
                this.CreateCategoryFilter.billingItem = null
            }
            if (val) {
                this.addCategoryList(this.categoryList)
            }
            this.filter.shopCode = null;
            this.BillingSummaryCategorySelectFilter.platform = val;
            this.onchangeplatformBillingSummary(val);
            this.shopList = []
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ...this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            console.log(params, 'params');
            this.exportInfo = params
            const res = await billingSummary(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        // startImport() {
        //    // this.importDialog.filter.platform = null
        //     this.fileList = []
        //     this.uploadLoading=false
        //     this.dialogVisible = true;
        // },
        // //取消导入
        // cancelImport() {
        //     this.dialogVisible = false;
        // },
        // uploadSuccess(response, file, fileList) {
        //     if (response.code == 200) {
        //     } else {
        //         fileList.splice(fileList.indexOf(file), 1);
        //     }
        // },
        // async submitUpload() {
        //     if (this.importDialog.filter.PlatForm == null) {
        //         this.$message({ message: "请选择平台", type: "warning" });
        //         return false;
        //     }
        //     if (this.importDialog.filter.YearMonthDay == null) {
        //         this.$message({ message: "请先选择日期", type: "warning" });
        //         return false;
        //     }
        //     if (!this.fileList || this.fileList.length == 0) {
        //         this.$message({ message: "请选取文件", type: "warning" });
        //         return false;
        //     }
        //     this.fileHasSubmit = true;
        //     this.uploadLoading = true;
        //     this.$refs.upload.submit();
        // },
        // clearFiles(){
        //     this.$refs['upload'].clearFiles();
        // },
        // async uploadFile(item) {
        //     if (!this.fileHasSubmit) {
        //         return false;
        //     }
        //     this.fileHasSubmit = false;
        //     this.uploadLoading = true;
        //     const form = new FormData();
        //     if(this.importDialog.filter.PlatForm==2)
        //     {
        //         if(this.fileList.length<2)
        //         {
        //             this.$message({ message: "请选择两个文件上传", type: "warning" });
        //             this.fileList = []
        //             this.uploadLoading = false;
        //             return false;
        //         }
        //     form.append("token", this.token);
        //     form.append("upfile1", this.fileList[0].raw);
        //     form.append("upfile2", this.fileList[1].raw);
        //     form.append("PlatForm", this.importDialog.filter.PlatForm);
        //     form.append("YearMonthDay", this.importDialog.filter.YearMonthDay);
        //     let res = await importPDDBillingCharge(form);
        //         if (res.code == 1) {
        //             this.$message({ message: "上传成功,正在导入中...", type: "success" });
        //             this.$refs.upload.clearFiles();
        //             this.dialogVisible = false;
        //         }
        //     this.fileList = []
        //     this.uploadLoading = false;

        //     }
        //     else
        //     {
        //         if(this.fileList.length>1)
        //         {
        //             this.$message({ message: "目前只支持拼多多多个文件上传", type: "warning" });
        //             this.fileList = []
        //             this.uploadLoading = false;
        //             return false;
        //         }
        //         form.append("token", this.token);
        //         form.append("upfile",  item.file);
        //         form.append("PlatForm", this.importDialog.filter.PlatForm);
        //         form.append("YearMonthDay", this.importDialog.filter.YearMonthDay);
        //         let res = await importBillFee(form);
        //             if (res.code == 1) {
        //                 this.$message({ message: "上传成功,正在导入中...", type: "success" });
        //                 this.$refs.upload.clearFiles();
        //                 this.dialogVisible = false;
        //             }
        //         this.fileList = []
        //         this.uploadLoading = false;
        //     }

        // },
        // async uploadChange(file, fileList) {
        //     let files=[];
        //     files.push(file)
        //     this.fileList = fileList;
        // },
        // async uploadRemove(file, fileList) {
        //     this.fileList = []
        // },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__cell .cell {
    font-size: 14px;
}
</style>
