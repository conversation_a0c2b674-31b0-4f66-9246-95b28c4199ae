<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.dayItem" type="date" placeholder="选择日期"
                    :picker-options="pickerOptions" class="publicCss" @change="changeTime" :clearable="false" />
                <el-input v-model="ListInfo.seriesName" placeholder="系列编码" class="publicCss" clearable maxlength="50" />
                <el-input v-model="ListInfo.sku" placeholder="SKUS" class="publicCss" clearable maxlength="50" />
                <div class=" iptBox">
                    <el-input-number v-model="ListInfo.minOrderCount" class="publicCss" :max="9999999" :controls="false"
                        placeholder="昨日最小订单量" clearable :precision="0" @change="changeCount($event, 'min')" />
                    <span class="delPropsImg" v-show="minDelIsShow" @click="delCount('min')">x</span>
                </div>
                <div class=" iptBox">
                    <el-input-number v-model="ListInfo.maxOrderCount" class="publicCss" :max="9999999" :controls="false"
                        placeholder="昨日最大订单量" clearable :precision="0" @change="changeCount($event, 'max')" />
                    <span class="delPropsImg" v-show="maxDelIsShow" @click="delCount('max')">x</span>
                </div>
                <el-button type="primary" @click="openSetDialog">全局设置</el-button>
                <el-button type="primary" @click="getList(true)">搜索</el-button>
            </div>
        </template>
        <vxetablebase :id="'chargeOrder202408041746'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" v-loading="loading" style="width: 100%; height: 680px; margin: 0"
            @rowStyle="rowStyle" :showsummary='true' :summaryarry='summaryarry' />
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

        <!-- 全局设置 -->
        <el-dialog title="全局设置" :visible.sync="setVisible" width="60%" v-dialogDrag :close-on-click-modal="false">
            <el-tabs v-model="activeName">
                <el-tab-pane label=" 订单量设置" name="first">
                    <orderVolume @close="close" @getList="getList" ref="orderVolume" :lazy="true" />
                </el-tab-pane>
                <el-tab-pane label="仓库设置" name="second">
                    <warehouseSet @close="close" @getList="getList" ref="warehouseSet" :lazy="true" />
                </el-tab-pane>
                <el-tab-pane label="二次组团设置" name="third">
                    <setGroup @close="close" @getList="getList" ref="setGroup" :lazy="true" />
                </el-tab-pane>
            </el-tabs>
        </el-dialog>

        <el-dialog title="组团订单（>=10单）" :visible.sync="groupOrderPageVisible" width="60%" v-dialogDrag>
            <groupOrderPage ref="distributionTable" v-if="groupOrderPageVisible"
                :groupOrderQueryInfo="groupOrderQueryInfo" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import orderVolume from './orderVolume.vue'
import warehouseSet from "./warehouseSet.vue";
import setGroup from "./setGroup.vue";
import groupOrderPage from './groupOrderPage.vue'
import {
    pageGetDayStat,
    getMobilize,
    releaseOrders,
} from '@/api/vo/VerifyOrder'
const tableCols = [
    { istrue: true, prop: 'DayItem', label: '日期', sortable: 'custom', width: 'auto', formatter: (row) => { return row.dayItem ? dayjs(row.dayItem).format('YYYY-MM-DD') : null } },
    { istrue: true, prop: 'SeriesName', label: '系列编码', sortable: 'custom', width: '120', align: 'left', formatter: (row) => row.seriesName ? row.seriesName : null },
    { istrue: true, prop: 'Sku', label: 'SKUS', sortable: 'custom', align: 'left', width: ' 120', formatter: (row) => row.sku ? row.sku : null },
    { istrue: true, prop: 'TotalOrderCount_1Day', label: '前一天订单量', sortable: 'custom', width: 'auto', formatter: (row) => (row.totalOrderCount_1Day && row.totalOrderCount_1Day != 'NaN') ? row.totalOrderCount_1Day : null },
    // { istrue: true, prop: 'TotalOrderCount_3Day', label: '3日均订单', tipmesg: '日销量为0数据不参与计算', sortable: 'custom', width: 'auto', formatter: (row) => (row.totalOrderCount_3Day && row.totalOrderCount_3Day != 'NaN') ? row.totalOrderCount_3Day.toFixed(2) : null },
    // { istrue: true, prop: 'TotalOrderCount_7Day', label: '7日均订单', tipmesg: '日销量为0数据不参与计算', sortable: 'custom', width: 'auto', formatter: (row) => (row.totalOrderCount_7Day && row.totalOrderCount_7Day != 'NaN') ? row.totalOrderCount_7Day.toFixed(2) : null },
    // { istrue: true, prop: 'TotalOrderCount_15Day', label: '15日均订单', tipmesg: '日销量为0数据不参与计算', sortable: 'custom', width: 'auto', formatter: (row) => (row.totalOrderCount_15Day && row.totalOrderCount_15Day != 'NaN') ? row.totalOrderCount_15Day.toFixed(2) : null },
    // { istrue: true, prop: 'TotalOrderCount_30Day', label: '30日均订单', tipmesg: '日销量为0数据不参与计算', sortable: 'custom', width: 'auto', formatter: (row) => (row.totalOrderCount_30Day && row.totalOrderCount_30Day != 'NaN') ? row.totalOrderCount_30Day.toFixed(2) : null },
    { istrue: true, prop: 'OrderCount0_8', label: '0-8订单', sortable: 'custom', width: 'auto', formatter: (row) => row.orderCount0_8 },
    { istrue: true, prop: 'OrderCount9_17', label: '8-17订单', sortable: 'custom', width: 'auto', formatter: (row) => row.orderCount9_17 },
    { istrue: true, prop: 'OrderCount18_24', label: '17-0订单', sortable: 'custom', width: 'auto', formatter: (row) => row.orderCount18_24 },
    { istrue: true, prop: 'TotalOrderCount', label: '订单量', sortable: 'custom', width: 'auto', formatter: (row) => row.totalOrderCount },
    { istrue: true, prop: 'MobilizeOrderCount', label: '已蓄单订单', sortable: 'custom', align: 'center', width: 'auto', formatter: (row) => row.mobilizeOrderCount, type: 'click', handle: (that, row) => that.opengroupOrder(row) },
    { istrue: true, prop: 'SurplusOrderCount', label: '剩余订单', width: 'auto', sortable: 'custom', formatter: (row) => row.surplusOrderCount },
    { istrue: true, prop: 'Hour', label: '蓄单时间(小时)', width: '130', formatter: (row) => row.hour ? row.hour : null },
    { istrue: true, prop: 'OrderCount', label: '蓄单量', width: 'auto', formatter: (row) => row.orderCount ? row.orderCount : null },
    {
        istrue: true, prop: 'releaseOrderCount', label: '操作', width: 'auto', type: 'button', btnList:
            [
                { istrue: true, label: '放单', handle: (that, row) => that.releaseOrder(row) },
            ]
    },
]
export default {
    components: {
        MyContainer, vxetablebase, orderVolume, warehouseSet, groupOrderPage, setGroup
    },
    data() {
        return {
            summaryarry: {},
            activeName: 'first',
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                isMain: true,
                seriesName: null,
                sku: null,
                dayItem: null,
            },
            tableCols,
            loading: false,
            that: this,
            tableData: [],
            total: 0,
            setVisible: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
                shortcuts: [{
                    text: '今天',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '昨天',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一周前',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', date);
                    }
                }]
            },
            minDelIsShow: false,
            maxDelIsShow: false,
            groupOrderPageVisible: false,//组团订单
            groupOrderQueryInfo: {
                startDate: null,
                endDate: null,
                sku: null,
            }
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        opengroupOrder(row) {
            if (row.mobilizeOrderCount < 10) return this.$message.error('当前订单不是组团订单')
            this.groupOrderQueryInfo = {
                startDate: this.ListInfo.dayItem,
                endDate: this.ListInfo.dayItem,
                sku: row.sku
            }
            this.groupOrderPageVisible = true
        },
        releaseOrder(row) {
            this.$confirm('此操作将进行放单, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await releaseOrders([{ dayItem: row.dayItem, sku: row.sku }])
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    await this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        delCount(type) {
            if (type == 'min') {
                delete this.ListInfo.minOrderCount
                this.minDelIsShow = false
            } else {
                delete this.ListInfo.maxOrderCount
                this.maxDelIsShow = false
            }
        },
        changeCount(e, type) {
            if (type == 'min') {
                this.minDelIsShow = e ? true : false
            } else {
                this.maxDelIsShow = e ? true : false
            }
        },
        async getProps() {
            const { data, success } = await getMobilize()
            if (success) {
                localStorage.setItem('mobilize', JSON.stringify(data.minMobilizeCount))
            }
        },
        close() {
            this.setVisible = false
        },
        changeTime(e) {
            this.ListInfo.dayItem = e ? dayjs(e).format('YYYY-MM-DD') : null
            this.getList()
        },
        async openSetDialog() {
            this.activeName = 'first'
            this.setVisible = false
            this.setVisible = true
            this.$nextTick(() => {
                this.$refs.orderVolume.getProps()
                this.$refs.warehouseSet.getProps()
                this.$refs.setGroup.getSecondGroup()
                this.$refs.setGroup.getProps()
            })
        },
        init() {
            // dayItem默认给今天
            this.ListInfo.dayItem = !this.ListInfo.dayItem ? dayjs().subtract(0, 'day').format('YYYY-MM-DD') : this.ListInfo.dayItem
        },
        //查询列表
        async getList(isSearch) {
            isSearch ? this.ListInfo.currentPage = 1 : null
            const replaceArr = ['seriesName', 'sku']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetDayStat(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary
                for (let key in this.summaryarry) {
                    let newKey = key.charAt(0).toUpperCase() + key.slice(1)
                    this.summaryarry[newKey] = this.summaryarry[key]
                    delete this.summaryarry[key]
                }
                await this.getProps()
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        rowStyle(row, callback) {
            const mobilize = localStorage.getItem('mobilize')
            let obj = {};
            if (this.tableData[row.rowIndex]['totalOrderCount'] <= mobilize) {
                obj = {
                    backgroundColor: '#ffffff',
                    color: '#ccc'
                }
            }
            callback(obj);
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }

    .btnCss {
        width: 100px;
    }
}

.custom-row-class {
    background-color: #fff;
    color: #ccc;
}

.iptBox {
    position: relative;

    .delPropsImg {
        position: absolute;
        right: 15px;
        top: 7px;
        border: 1px solid #a9b4b7;
        color: #a9b4b7;
        font-size: 12px;
        width: 10px;
        height: 10px;
        text-align: center;
        line-height: 7px;
        cursor: pointer;
        border-radius: 50%;
    }
}
</style>