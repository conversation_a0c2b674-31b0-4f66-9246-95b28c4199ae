<template>
  <my-container v-loading="pageLoading">
    <template #header>
    <el-form :inline="true" :model="filter" @submit.native.prevent>
      <el-form-item label="采购员:">
        <el-select v-model="filter.brandId" multiple collapse-tags clearable placeholder="请选择采购员" style="width: 300px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="预警:">
        <el-select v-model="filter.isError" disabled placeholder="请选择">
          <el-option label="所有" value></el-option>
          <el-option label="正常" value='0'></el-option>
          <el-option label="进货" value="1"></el-option>
          <el-option label="缺货" value="2"></el-option>
          <el-option label="催物流" value="3"></el-option>
          <el-option label="催上架" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品编码:"><el-input v-model="filter.goodsCode" /></el-form-item>
      <el-form-item label="商品名称:"><el-input v-model="filter.goodsName" /></el-form-item>
      <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
    </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'  :showsummary='true' :summaryarry='summaryarry'
        @cellclick='cellclick' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
         <template slot='extentbtn'>
            <el-button-group>
              <el-button style="margin: 0;">
                {{lastUpdateTime}}
              </el-button>
            </el-button-group>
        </template>
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-dialog title="分析" :visible.sync="dialogVisible" v-dialogDrag width="80%">
      <span>
          <probianmaanalysis :filter="analysisfilter" ref="probianmaanalysis1"/>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="visiblepopover" v-dialogDrag :show-close="false">
       <goodscoderecord :filter="goodscoderecordfilter" style="height: 400px"></goodscoderecord> 
    </el-dialog>

     <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
       <logistics ref="logistics"></logistics>
    </el-drawer>
    
  </my-container>
</template>

<script>
import {pagePurchasePlan,getLastUpdateTimeyPurchasePlan,exportPurchasePlan,getPurchasePlan} from '@/api/inventory/purchase'
import {getAllProBrand} from '@/api/inventory/warehouse'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import logistics from '@/components/Comm/logistics'
import { formatPurchasePlanError,formatTime,formatNoLink,formatSecondToHour} from "@/utils/tools";
import { throttle } from 'throttle-debounce';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
const tableCols =[
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100',sortable:'custom'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'150',sortable:'custom'},
      {istrue:true,prop:'images',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'brandName',label:'采购员', width:'65'},
      {istrue:true,prop:'isError',label:'预警', width:'50',type:'html',formatter:(row)=>formatPurchasePlanError(row.isError)},
      {istrue:true,prop:'useableCount',label:'实际可用数', width:'70',sortable:'custom'},
      {istrue:true,prop:'sumTurnover',label:'合计周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'useableTurnover',label:'可用周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'inTransitTurnover',label:'在途周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'lastInTransitTime',label:'最近在途时长', width:'70',sortable:'custom',formatter:(row)=>formatSecondToHour(row.lastInTransitTime)},
      {istrue:true,prop:'avgInTransitTime',label:'历史平均时长', width:'70',sortable:'custom',formatter:(row)=>formatSecondToHour(row.avgInTransitTime)},
      {istrue:true,prop:'avgSalesDays4D',label:'4维日均销量', width:'65',sortable:'custom'},
      {istrue:true,prop:'salesToday',label:'今日销量', width:'60',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.salesToday)},
      {istrue:true,prop:'goodsLable',label:'商品标签', width:'80',sortable:'custom'},
      {istrue:true,prop:'inTransitNum',label:'采购在途', width:'90',sortable:'custom'},
      {istrue:true,prop:'stock',label:'进货仓库存', width:'70',sortable:'custom'},
      {istrue:true,prop:'count',label:'数量', width:'50'},
      {istrue:true,prop:'planArrivalTime',label:'预计到货日期', width:'110',sortable:'custom',formatter:(row)=>formatTime(row.planArrivalTime,'YYYY-MM-DD')},
      {istrue:true,prop:'companyName',label:'物流公司', width:'80'},
      {istrue:true,prop:'expressNo',label:'物流单号', width:'80',type:'html',formatter:(row)=>formatNoLink(row.expressNo)},
      {istrue:true,prop:'remark',label:'备注', width:'160',type:'editor'},
     ];
const tableHandles=[{label:"导出", handle:(that)=>that.onExport()}];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton,probianmaanalysis,goodscoderecord,logistics},
  data() {
    return {
      that:this,
      formatTime:formatTime,
      filter: {
        goodsCode:null, 
        brandId:null,
        goodsName:null,
        isError:'4'
      },
      analysisfilter:{
        startDate: null,
        endDate: null,
        proBianMa:""
      },
      goodscoderecordfilter:{goodsCode:"",buyNo:""},
      list: [],
      brandlist:[],
      recodelist:[],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      importVisible: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopover: false,
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      selids: [],
      fileList:[],
      listLoading: false, 
      pageLoading: false,
      uploadLoading:false,
      lastUpdateTime:'',
      onExporting:false,
      dialogVisible:false,
      editVisible:false,
      editLoading:false,
      drawervisible:false,
    }
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    this.init();
    this.getlist();
    formCreate.component('editor', FcEditor);
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async init(){
        var date1=new Date();
        this.analysisfilter.endDate=await this.datetostr(date1);
        var date2=await this.addDate(this.analysisfilter.endDate,-90);
        this.analysisfilter.startDate=await this.datetostr(new Date(date2));

        var res2= await getAllProBrand();
        this.brandlist = res2.data.map(item => {
            return { value: item.key, label: item.value };
        });

        var res3= await getLastUpdateTimeyPurchasePlan();
        this.lastUpdateTime= "最晚更新时间:"+res3.data
    }, 
    async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          // 滚动节流
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },

   async datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
   async addDate(date,days){ 
        var d=new Date(date); 
        d.setDate(d.getDate()+days); 
        var m=d.getMonth()+1; 
        return d.getFullYear()+'-'+m+'-'+d.getDate(); 
    },
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pagePurchasePlan(params)
      this.listLoading = false
      if (!res?.success)return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   async cellclick(row, column, cell, event){
     if (column.property=='salesToday')  
        await this.onanalysis(row.goodsCode)
     else if (column.property=='expressNo'&&row.expressNo)
        await this.showlogistics(row.companyCode,row.expressNo);
     else if (column.property=='planArrivalTime'
         ||column.property=='companyCode'
         ||column.property=='count' ||column.property=='remark') {
      await this.getrecordlist(row.goodsCode)
      this.visiblepopover=true;
     }
    },
   async getrecordlist(goodsCode){
      this.goodscoderecordfilter={buyNo:"",goodsCode:goodsCode};
     },
   async onanalysis(goodsCode){
        this.dialogVisible=true;
        this.$nextTick(() => {
           this.$refs.probianmaanalysis1.onShow([goodsCode]);
        });
    },
    // 清空编辑组件
  async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail= !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail= false;
    },
   async onHand(row){
      this.editVisible = true
      const res = await getPurchasePlan({goodsCode:row.goodsCode})
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },
   async onDisPlay(row){
     return row.isHandle==true;
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    },
  async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,... this.filter}
        var res= await exportPurchasePlan(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','建议采购_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    async showlogistics(companycode,number){
      this.drawervisible=true;
       this.$nextTick(function(){
         this.$refs.logistics.showlogistics(companycode,number);
       })
    }
  }
}
</script>
