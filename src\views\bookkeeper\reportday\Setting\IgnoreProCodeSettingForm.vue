<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-row>
                <el-col :span="4">
                    <el-select prop="platform"  v-model="form.platform" style="width:100%">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"  :value="item.value"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="16">
                    <el-input v-model="form.proCodes" type="textarea" rows="2" placeholder="请输入商品编码,多个编码用逗号或换行隔开" style="width:100%"></el-input>
                </el-col>
                <el-col :span="4">
                    <el-button type="primary" @click="addSave">添加</el-button>
                </el-col>
            </el-row>
            <el-row>             
                <el-col :span="24" style="height:450px">                 
                    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist' :hasexpandRight="true"
                        :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' :loading="sellistLoading">
                        <!-- <template slot="right">
                            <el-table-column  label="操作" width="100">
                            <template #default="{ $index, row }">                              
                                <my-confirm-button  type="text"   @click="delRow(row)">
                                    删除
                                </my-confirm-button>                               
                            </template>
                        </el-table-column>
                        </template> -->
                    </ces-table>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button @click="onClose">关闭</el-button>
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, platformlist,    formatLinkProCode } from "@/utils/tools";


import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";

import { GetRptIgnoreProCodeList,AddRptIgnoreProCodeList } from '@/api/bookkeeper/reportday'


const tableCols = [
    { istrue: true, prop: 'proCode', label: '商品ID', width:'160', sortable: true, type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode)},
    { istrue: true, prop: 'platform', label: '平台', width:'80', sortable: true,  formatter: (row) => formatPlatform(row.platform)},  
    { istrue: true, prop: 'addUserName', label: '添加人', width:'90', sortable: true},    
    { istrue: true, prop: 'addDateTime', label: '添加时间', sortable: true} 
]
export default {
    name: "IgnoreProCodeSettingForm",
    components: { cesTable, MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
           
            platformlist:platformlist,         
            form:{
                platform:0,
                proCodes:'',
            },
            sellist: [],
            sellistLoading: false,
            tableCols: tableCols,
            mode: 3,         
       
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式              
        };
    },
    async mounted() {
       
    },
    computed: {

    },
    methods: {  
        async addSave(){
            this.pageLoading = true;
            let dto={...this.form};
            let rsp= await AddRptIgnoreProCodeList(dto);
            if(rsp && rsp.success){
                this.$message.success('操作成功！');
                this.loadData();       
            }

            this.pageLoading = false;
        },
       
        async loadData() {
            this.pageLoading = true;
            let rsp=await GetRptIgnoreProCodeList({"ProCode":""});
            if(rsp &&rsp.success){
                this.sellist=rsp.data;
            }
            this.pageLoading = false;
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        }        
    },
};
</script>
