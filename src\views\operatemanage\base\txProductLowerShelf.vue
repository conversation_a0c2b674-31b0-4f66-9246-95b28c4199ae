<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="">
          <el-select filterable v-model="filter.shopId" placeholder="店铺" clearable style="width: 130px">
            <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-select filterable v-model="filter.groupId" placeholder="运营组长" style="width: 100px" clearable>
            <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-select filterable v-model="filter.operateSpecialId" placeholder="运营专员" clearable style="width: 100px">
            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="总访客量">
          <el-input v-model="filter.OrderCount" placeholder="总访客量" @input="handleEditOrderCount" />
        </el-form-item>
        <el-form-item label="搜索访客数">
          <el-input v-model="filter.SearchVisitorNumber" placeholder="搜索访客数" @input="handleEditSearchVisitorNumber" />
        </el-form-item>
        <el-form-item label="加购人数">
          <el-input v-model="filter.GoodsPurchasedPeopleNumber" placeholder="加购人数"
            @input="handleEditGoodsPurchasedPeopleNumber" />
        </el-form-item>
        <el-form-item label="支付买家数">
          <el-input v-model="filter.PayBuyNumber" placeholder="支付买家数" @input="handleEditPayBuyNumber" />
        </el-form-item>
        <el-form-item label="上架天数">
          <el-input v-model="filter.OnlineDay" placeholder="上架天数" @input="handleEditOnlineDay" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getList">筛选</el-button>
          <el-button v-if="checkPermission('api:operatemanage:productmanager:BatchDownProductAsync')" type="primary"
            @click="batchDown">批量下架</el-button>
        </el-form-item>
      </el-form>
    </template>
    <ces-table ref="table" :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false"
      :tableData='list' :tableCols='tableCols' :loading="listLoading" @cellclick="cellclick" :hasexpandRight="true">
      <template slot="right">
        <el-table-column label="趋势">
          <template slot-scope="scope">
            <div style="height: 120px;width:100%;margin-left: -20px;" :ref="'echarts' + scope.row.proCode"
              v-loading="echartsLoading"></div>
          </template>
        </el-table-column>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>

    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <template>
          <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
            <el-row>
              <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                <el-form-item label="日期:">
                  <el-date-picker style="width: 260px" v-model="detailfilter.timerange" type="daterange"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-form-item>
                  <el-button type="primary" @click="getecharts">刷新</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </span>
      <span>
        <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>

</template>

<script>
import MyContainer from '@/components/my-container'
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatLinkProCode } from "@/utils/tools";
import { getCanLowerShelfProductsAsync, queryCanLowerShelfProductsAsync, batchDownProducts } from '@/api/operatemanage/base/product'
import * as echarts from 'echarts'
import dayjs from "dayjs";
import buschar from '@/components/Bus/buschar'
import { getDirectorList, getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop'

const tableCols = [
  { istrue: true, prop: 'proCode', label: 'ID', width: '200', sortable: 'custom', type: 'html' ,formatter:(row)=>formatLinkProCode(1,row.proCode) },
  { istrue: true, prop: 'shopName', label: '店铺', width: '200', sortable: 'custom' },
  { istrue: true, prop: 'groupName', label: '小组', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'director', label: '运营专员', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'onTime', label: '上架时间', width: '200', sortable: 'custom', formatter: (row) => formatTime(row.onTime, 'YYYY-MM-DD HH:mm:ss') },
  { istrue: true, prop: 'orderCount', label: '总访客量	', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'searchVisitorNumber', label: '搜索访客数', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'goodsPurchasedPeopleNumber', label: '加购人数', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'payBuyNumber', label: '支付买家数', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'onlineDay', label: '上架天数', width: '120', sortable: 'custom' }
];

const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");

export default {
  name: "txProductLowerShelf",
  components: { MyContainer, cesTable, buschar },
  data() {
    return {
      pageLoading: false,
      filter: {
        OrderCount: '',
        SearchVisitorNumber: '',
        GoodsPurchasedPeopleNumber: '',
        PayBuyNumber: '',
        OnlineDay: '',
        shopId: '',
        groupId: '',
        operateSpecialId: ''
      },
      list: [],
      tableCols: tableCols,
      pager: { OrderBy: "", IsAsc: false },
      listLoading: false,
      echartsLoading: false,
      total: 0,
      detailfilter: {
        procode: null,
        startTime: null,
        endTime: null,
        timerange: [star, endTime]
      },
      buscharDialog: { visible: false, title: "", data: [] },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            let end = new Date();
            let start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近半个月',
          onClick(picker) {
            let end = new Date();
            let start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            let end = new Date();
            let start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      shopList: [],
      directorList: [],
      directorGroupList: [],
    }
  },
  async mounted() {
    await this.getShop()
    await this.getDirectorlist()
    await this.getList()
  },
  methods: {
    async getList() {
      let pager = this.$refs.pager.getPager();
      let params = { ...pager, ...this.pager, ...this.filter };
      let res = await getCanLowerShelfProductsAsync(params);
      if (!res.success) return
      this.list = res.data.list
      this.total = res.data.total
      this.getEcharts()
    },
    regixNum(e) {
      let value = e.replace(/^(0+)|[^\d]+/g, '');
      value = value.replace(/(\d{15})\d*/, '');
      return value
    },
    handleEditOrderCount(e) {
      this.filter.OrderCount = this.regixNum(e)
    },
    handleEditSearchVisitorNumber(e) {
      this.filter.SearchVisitorNumber =this.regixNum(e)
    },
    handleEditGoodsPurchasedPeopleNumber(e) {
      this.filter.GoodsPurchasedPeopleNumber = this.regixNum(e)
    },
    handleEditPayBuyNumber(e) {
      this.filter.PayBuyNumber = this.regixNum(e)
    },
    handleEditOnlineDay(e) {
      this.filter.OnlineDay = this.regixNum(e)
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderField = column.prop;
        this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      }
      await this.getList();
    },
    getEcharts() {
      setTimeout(_ => {
        this.list.forEach(e => {
          let myChart = echarts.init(this.$refs['echarts' + e.proCode]);
          var series = []
          this.echartsLoading = true
          e.series.forEach(s => {
            series.push({ smooth: true, showSymbol: false, ...s })
          })
          this.echartsLoading = false
          myChart.setOption({
            legend: {
              show: false
            },
            grid: {
              left: "0",
              top: "6",
              right: "0",
              bottom: "0",
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              //不显示x轴线
              show: false,
              data: e.xAxis
            },
            yAxis: {
              type: 'value',
              show: false,
            },
            series: series
          });
          window.addEventListener("resize", () => {
            myChart.resize();
          });
        })
      }, 1000)
    },
    async batchDown() {
      if (this.filter.OnlineDay == '' && this.filter.OrderCount == '' && this.filter.SearchVisitorNumber == ''
        && this.filter.GoodsPurchasedPeopleNumber == '' && this.filter.PayBuyNumber == '' && this.filter.shopId == '' && this.filter.groupId == '' && this.filter.operateSpecialId == '') {
        return
      }
      this.$confirm('该操作将按照筛选条件批量下架商品，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let pager = this.$refs.pager.getPager();
        let params = { ...pager, ...this.pager, ...this.filter };
        const res = await batchDownProducts(params)
        if (!res?.success) { return }
        if (res.data) {
          this.$message({ type: 'success', message: '操作成功!' });
          this.getList()
        } else {
          this.$message({ type: 'error', message: '操作失败!' });
        }
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消操作' });
      });
    },
    async cellclick(row, column, cell, event) {
      if (column.label == '趋势') {
        this.detailfilter.procode = row.proCode
        this.getecharts()
      }
    },
    async getecharts() {
      this.detailfilter.startTime = null;
      this.detailfilter.endTime = null;
      if (this.detailfilter.timerange) {
        this.detailfilter.startTime = this.detailfilter.timerange[0];
        this.detailfilter.endTime = this.detailfilter.timerange[1];
      }
      let params = { ...this.detailfilter, isMedia: true }
      let that = this;
      let res = await queryCanLowerShelfProductsAsync(params).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = '趋势'
      })
      await this.$refs.buschar.initcharts()
    },
    async getShop() {
      this.categorylist = []
      let res = await getshopList({ platform: 1, CurrentPage: 1, PageSize: 100 });
      this.shopList = res.data.list
    },
    async getDirectorlist() {
      let res1 = await getDirectorList({})
      let res2 = await getDirectorGroupList({})

      this.directorList = res1.data
      this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
    },
  },
}
</script>

<style>

</style>