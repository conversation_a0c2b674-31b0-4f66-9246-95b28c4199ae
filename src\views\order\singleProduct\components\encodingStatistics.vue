<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker class="publicCss" v-model="ListInfo.date" align="right" type="date" placeholder="选择日期"
                    :clearable="false" value-format="yyyy-MM-dd" :picker-options="pickerOptionsDate" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="goodsCodesCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <chooseWareHouse v-model="ListInfo.wmsId" :filter="sendWmsesFilter" style="width: 200px;"
                    class="publicCss" />
                <number-range :min.sync="ListInfo.wmsTurnoverDayMin" :max.sync="ListInfo.wmsTurnoverDayMax"
                    min-label="发货仓周转天数最小" max-label="发货仓周转天数最大" class="publicCss" style="width: 250px;" />
                <el-select filterable v-model="ListInfo.company" clearable placeholder="分公司" class="publicCss">
                    <el-option key="义乌" label="义乌" value="义乌"></el-option>
                    <el-option key="南昌" label="南昌" value="南昌"></el-option>
                    <el-option key="武汉" label="武汉" value="武汉"></el-option>
                    <el-option key="深圳" label="深圳" value="深圳"></el-option>
                    <el-option key="其他" label="其他" value="其他"></el-option>
                </el-select>
                <el-select v-model="ListInfo.title" clearable filterable placeholder="请选择岗位" class="publicCss">
                    <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                        :value="item.titleName" />
                </el-select>
                <el-select v-model="ListInfo.brandId" clearable filterable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.deptId" clearable filterable placeholder="请选择架构" class="publicCss">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" id="20241201104629" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :cstmExportFunc="onExport" :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;"
            height="100%" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode, pickerOptionsDate } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/CodeStat/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getPurchaseDeptList } from '@/api/inventory/purchaseordernew'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            pickerOptionsDate,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
                date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            purchasegrouplist: [],
            brandlist: [],
            brandlist1: [],
            positionList: [],
        }
    },
    async mounted() {
        this.init()
        await this.getCol();
        await this.getList()
    },
    methods: {
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
            //采购组
            let { data: deptList, success } = await getPurchaseDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
        },
        sendWmsesFilter(wmses) {
            this.wmsesList = wmses;
            return wmses.filter((a) => a.name.includes('【昀晗-'));
        },
        async trendChart(option) {
            var endDate = null;
            var startDate = null;
            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });
            option.startDate = startDate;
            option.endDate = endDate;
            this.chatProp.chatTime = [startDate, endDate];
            this.chatProp.chatLoading = true;
            const { data, success } = await await request.post(`${this.api}GetTrendChart`, option);
            if (success) {
                this.chatProp.data = data;
            }
            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;
            this.chatPropOption = option;
        },
        goodsCodesCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        async onExport(val) {
            this.isExport = true
            await request.post(`${this.api}ExportData`, { ...this.ListInfo, ...val }, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.$refs.table.setExportCols()
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                    if (data.summary) {
                        Object.keys(data.summary).forEach(key => {
                            if (key == 'theoryRate_sum') {
                                data.summary[key] = (data.summary[key] * 100).toFixed(2) + '%';
                            }
                        });
                    }
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;

    .publicCss {
        width: 180px;
        margin-right: 10px;
    }
}

</style>
