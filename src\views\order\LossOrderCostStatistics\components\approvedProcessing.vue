<template>
    <!-- 认可处理 -->
    <div class="containal">
        <div class="top">
            <div>认可操作须知:</div>
            <div>1、对方发起申诉后【是否认可】操作时限为17:30到次日9:00,过时将自动认可!</div>
            <div>2、不认可,需要认真填写原因,便于后期审核。</div>
            <div>3、如认可,责任将按申诉填写的新责任部门与责任人划分。</div>
        </div>
        <div class="center">
            <!-- 加上插值 -->
            <div class="center_item">
                <div class="item_box" style="width: 220px;">线上订单号: {{ auditDialogProps.otherInfo.b.orderNo }}</div>
                <div class="item_box">发起时间: {{ auditDialogProps.otherInfo.b.afterSaleApproveDate }}</div>
                <div class="item_box">损耗金额: {{ auditDialogProps.otherInfo.b.damagedAmount }}</div>
            </div>
            <div class="center_item">
                <div class="item_box">原责任部门: {{ auditDialogProps.orgZrDepartment }}</div>
                <div class="item_box">原责任类型: {{ auditDialogProps.orgZrType2 }}</div>
                <div class="item_box">原责任人: {{ auditDialogProps.orgZrUserName }}</div>
            </div>
            <div class="center_item">
                <div class="item_box">新责任部门: {{ auditDialogProps.newZrDepartment }}</div>
                <div class="item_box">新责任类型: {{ auditDialogProps.newZrType2 }}</div>
                <div class="item_box">新责任人: {{ auditDialogProps.newZrUserName }}</div>
            </div>
            <div class="center_item">申诉理由:{{ auditDialogProps.applyReason }}</div>
            定责资料:
            <!-- <div style="min-height: 200px; width: 100%;"> -->
                <div v-html="auditDialogProps.applyContent" class="tempdivv"></div>
            <!-- </div> -->
            <div style="display: flex;align-items: center;margin-bottom: 20px;">
                <div style="margin-right: 10px;">是否认可:</div>
                <el-radio-group v-if="!auditDialogProps.disabled" v-model="radio" :disabled="auditDialogProps.disabled">
                    <el-radio :label="1">认可</el-radio>
                    <el-radio :label="0">不认可</el-radio>
                </el-radio-group>
                <div v-else class="tempdiv">{{auditDialogProps.firstAuditStateText}}</div>
                
            </div>
            <yh-quill-editor v-if="radio==0&&!auditDialogProps.disabled" :value.sync="auditDialogProps.auditRemark" ></yh-quill-editor>
            <!-- <el-input style="width: 300px" v-if="!auditDialogProps.disabled" v-model="auditDialogProps.auditRemark" placeholder="输入不认可原因" @input="changesel" clearable maxlength="10" /> -->
            <div v-if="auditDialogProps.disabled&&auditDialogProps.firstAuditState==-1" v-html="auditDialogProps.firstAuditRemark" class="tempdivv"></div>
        </div>
        <div class="footer" v-if="!auditDialogProps.disabled">
            <div class="footer_left">
                <el-button size="medium" type="primary" :disabled="radio==1" @click="transferViewfuc">转派给部门其他人</el-button>
            </div>
            <div class="footer_right">
                <el-button size="medium" @click="handleClose">关闭</el-button>
                <el-button type="primary" size="medium" @click="waisave">确认保存</el-button>
            </div>
        </div>

        <!-- <el-dialog title="责任转派" :visible.sync="TransferView" width="50%" :before-close="closeChild" v-dialogDrag :modal="fasle" :append-to-body="true"> -->
        <el-dialog title="责任转派" :visible.sync="TransferView" width="50%" v-if="TransferView" :before-close="closeChild" v-dialogDrag :append-to-body="true">
            <el-row>
                <el-col :span="7">线上订单号:{{ auditDialogProps.otherInfo.b.orderNo }}</el-col>
                <el-col :span="9">发起时间:{{ auditDialogProps.otherInfo.b.afterSaleApproveDate }}</el-col>
                <el-col :span="7">损耗金额:{{ auditDialogProps.otherInfo.b.damagedAmount }}</el-col>
            </el-row>
            <!-- <div class="item"> -->
                <el-row style="margin-top: 10px">
                    <el-col :span="7">
                        <div style="display: flex; flex-direction: row;align-items: center;width: 100%">新责任部门：
                        <!-- <el-form-item label="新责任部门：" prop="newZrDepartment" :rules="[
                                { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }
                            ]"> -->
                            <el-select disabled class="marginleft" v-model="auditDialogProps.newZrDepartment"    clearable placeholder="新责任部门(大类)" style="width: 150px;" @change="getZrType(auditDialogProps.newZrDepartment)"  >
                                <el-option v-for="item in damagedList" :key="item" :label="item" :value="item"  />
                            </el-select>
                        <!-- </el-form-item> -->
                        </div>
                    </el-col>

                    <el-col :span="9">
                        <div style="display: flex; flex-direction: row;align-items: center; width: 100%">新责任类型：
                        <!-- <el-form-item label="新责任类型：" prop="newZrType2" :rules="[
                        { required: true, message: '请选择新责任类型', trigger: ['blur', 'change'] }    
                        ]"> -->
                            <el-select disabled class="marginleft" v-model="auditDialogProps.newZrType2"  @change="changesel"  clearable placeholder="原责任类型(细类)" style="width: 200px;"  >
                                <el-option v-for="item in damagedList2" :key="item" :label="item" :value="item" />
                            </el-select>
                        <!-- </el-form-item> -->
                        </div>
                    </el-col>
                        <el-col :span="7">
                            <div style="display: flex; flex-direction: row;align-items: center;width: 100%">新责任人：
                            <!-- <el-form-item label="新责任人：" prop="newMemberName"> -->
                               
                                    <el-select v-if="auditDialogProps.newZrDepartment == '采购'" v-model="auditDialogProps.newMemberId" filterable
                                        @change="newMemberIdChange">
                                        <el-option v-for="item in brandlist" :key="item.key" :label="item.value"
                                            :value="item.key" />
                                    </el-select>
                                    <el-select v-else-if="auditDialogProps.newZrDepartment == '运营'" v-model="auditDialogProps.newMemberId" filterable
                                        @change="newMemberIdChange">
                                        <el-option v-for="item in directorList" :key="item.key" :label="item.value"
                                            :value="item.key" />
                                    </el-select>
                                    <YhUserelector v-else-if="auditDialogProps.newZrDepartment == '客服'||auditDialogProps.newZrDepartment == '仓库'" 
                                        :value.sync="auditDialogProps.newMemberDDUserId" :text.sync="auditDialogProps.newMemberName"
                                    ></YhUserelector>
                                    <el-input v-else v-model="auditDialogProps.newMemberName" @input="changesel" clearable maxlength="10" />
                                   
                                
                                <!-- <span v-else>{{ form.newMemberName }}</span> -->
                            <!-- </el-form-item> -->
                                </div>
                        </el-col>
                    </el-row>
            <!-- </div> -->
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" style="margin-top: 20px" label-width="100px" class="demo-ruleForm">
                <el-form-item label="定责资料" prop="">
                      <yh-quill-editor  :value.sync="auditDialogProps.opContent" ></yh-quill-editor>
                </el-form-item>
            </el-form>
            <el-row>
                <div class="footer_right">
                    <el-button size="medium" @click="TransferView = false">取消</el-button>
                    <el-button type="primary" size="medium" @click="neisave">确认</el-button>
                </div>
            </el-row>
        </el-dialog>
    </div>
</template>

<script>
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { firstAuditDeductZrAppeal, firstAuditDeductZrTrans2SameDept  } from '@/api/customerservice/DamagedOrders'
import { getAllWarehouse, getAllProBrand } from '@/api/inventory/warehouse'
import {
 getDirectorList,
 getDirectorGroupList,
 getProductBrandPageList,
 getList as getshopList,
} from "@/api/operatemanage/base/shop";
export default {
    name: 'approvedProcessing',
    props: {
        //接收父组件传递的值
        handleClose: {
            type: Function,
            required: true
        },
        auditDialogProps: {
            type: Object,
            required: true
        }
    },
    components: { YhQuillEditor, YhUserelector },
    data() {
        return {
            textarea: '',
            ruleForm: null,
            rules: {
                desc: [
                    { required: true, message: '请选择活动资源', trigger: 'change,blur,input' }
                ]
            },
            radio: 1,
            directorList: [],
            damagedList: [],
            damagedList2: [],
            brandlist: [],
            TransferView: false
        }

    },
    async mounted() {
        await this.setBandSelect();
        await this.getDirectorlist();
    },
    methods: {
        transferViewfuc(){
            let _this = this;
            // _this.TransferView = true;

            _this.auditDialogProps.newMemberName = "";
            _this.auditDialogProps.newMemberId = null;
            _this.auditDialogProps.newMemberDDUserId = null;
            // auditDialogProps.newMemberName
            // auditDialogProps.newMemberId
            this.$forceUpdate();
            _this.$nextTick(()=>{
                _this.TransferView = true;
            });
        },
        //关闭弹窗
        closeChild() {
            this.TransferView = false
        },
        async waisave(){
            let params = {
                id: this.auditDialogProps.id,
                auditState: this.radio,
                auditRemark: this.auditDialogProps.auditRemark
            }
            var res = await firstAuditDeductZrAppeal(params);
            if (!res?.success) return;
            this.$message.success('操作成功！');
            this.TransferView = false
            this.handleClose();
            // this.brandlist = res.data;
            this.$emit('getList')
            this.$emit('closedia');
        },
        async neisave(){
            let params = {
                ...this.auditDialogProps,
                appealId: this.auditDialogProps.id,
            }
            var res = await firstAuditDeductZrTrans2SameDept(params);
            if (!res?.success) return;
            this.TransferView = false
            this.$message.success('操作成功！');
            this.$emit('getList')
            this.TransferView = false;
            this.$emit('closedia');
            // this.brandlist = res.data;
        },
        async setBandSelect() {
            var res = await getAllProBrand();
            if (!res?.success) return;
            this.brandlist = res.data;
        },
        async getZrDept(){
            let res = await getDamagedOrdersZrDept();
            if(!res.success){
                return
            }
            this.damagedList =  res?.data;
            console.log("打印数据GetDamagedOrdersZrDept",this.damagedList)
            // damagedList2
            // this.changesel();
        },
        async getZrType(name){
            let res = await getDamagedOrdersZrType(name);
            if(!res.success){
                return
            }
            this.damagedList2 =  res.data;
            this.auditDialogProps.newMemberName = '';
            this.auditDialogProps.newMemberDDUserId = '';
            this.auditDialogProps.newMemberId = '';
            this.auditDialogProps.newZrType2 = '';
            this.$forceUpdate();

            console.log("打印数据",this.damagedList)
        },
        changesel(){
            this.$forceUpdate();
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({});
            const res2 = await getDirectorGroupList({});

            this.directorList = res1.data;
            this.directorGroupList = [{ key: "0", value: "未知" }].concat(
            res2.data || []
            );
        },
        newMemberIdChange() {
         let arr = null;
         if (this.auditDialogProps.newZrDepartment == "采购") {
             arr = [...this.brandlist];
         }
         else if(this.auditDialogProps.newZrDepartment=="运营"){
            arr=[...this.directorList];                    
        }  
        //  if (arr != null && arr && this.auditDialogProps.newMemberId) {
             let opt = arr.find(x => x.key == this.auditDialogProps.newMemberId);
             if (opt) {
                 this.auditDialogProps.newMemberName = opt.value;
             }
        //  }
        this.changesel();
     },
    }
}
</script>

<style scoped lang="scss">
.containal {
    padding: 0 20px;

    .top {
        color: red;
        margin-bottom: 30px;
    }

    .center {
        .center_item {
            padding-left: 33px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;

            .item_box {
                width: 220px;
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
    }
}

.item{
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.tempdivv ::v-deep img{ max-width: 980px}
</style>
