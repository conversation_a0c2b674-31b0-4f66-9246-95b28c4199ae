<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols'
            :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange' />

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { pageWarehousingOrderVideoUrgent, getListBuyNo, bindWarehousingBuyNo, saveWarehousingAmont ,exportWarehousingOrderVideoListAsync} from "@/api/inventory/warehousingordervide"


const tableCols = [
    { istrue: true, prop: 'warehousNo', label: '任务编号', width: '80', },
    { istrue: true, prop: 'wms_co_name', label: '仓库', width: '80', },
    { istrue: true, prop: 'imgPath', label: '主图', width: '120', type: 'images' },
    { istrue: true, prop: 'createdUserName', label: '上传人', width: '120', },
    { istrue: true, prop: 'createdTime', label: '上传时间', width: '120', },
    { istrue: true, prop: 'count', label: '件数', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'isPrint', label: '是否打单', width: '120', sortable: 'custom', formatter:(row)=> row.isPrint == true ? '已打单' : '未打单'},
    { istrue: true, prop: 'goodsName', label: '货品名称', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'freightAmont', label: '运费', width: '120', },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'modifiedUserName', label: '绑定人', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'bindBuyNoDate', label: '绑定时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'warehousingDate', label: '入库时间', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'shootAmont', label: '拍摄奖金', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'qualityAmont', label: '质检奖金', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'warehouseAmont', label: '入库奖金', width: '120', sortable: 'custom', },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];


export default {
    name: 'YunHanAdminWarehousingordervidelate',
    components: { container, MyConfirmButton, vxetablebase },
    props: { filter: {} },

    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "yearMonthDay", IsAsc: false },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async onExport() {
          var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
          this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            var exportType=true
            const params = {  ... this.filter,exportType }

            if (params === false) {
                return;
            }
            var res = await exportWarehousingOrderVideoListAsync(params);
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '入库拍摄加急导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageWarehousingOrderVideoUrgent(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;

            data.map((item) => {
                item.istrue = false;
                item.buyNostr = item.buyNo;
                item.buyNo = item.buyNo ? item.buyNo.split(',') : [];
            })
            this.list = data
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
