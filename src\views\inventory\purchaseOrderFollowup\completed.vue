<template>
  <MyContainer>
    <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212">
      <template #buttons>
        <slot name="tbHeader"></slot>
      </template>
    </vxe-toolbar>
    <vxe-table :id="'completed20240824'" border show-overflow="tooltip" show-header-overflow="tooltip"
      show-footer-overflow="tooltip" show-footer ref="xTable" height="100%"
      :sort-config="{ transform: true, sortMethod: customSortMethod }" :loading="loading" :data="tableData"
      :custom-config="{ storage: { fixed: true, sort: true, resizable: true, visible: true }, restoreStore: restoreStore, updateStore: updateStore }"
      :checkbox-config="{ highlight: true, checkMethod: checkMethod }" :treeProp="{}"
      :column-config="{ resizable: true }" @checkbox-all="selectAllEvent" @checkbox-change="selectChangeEvent"
      :cell-style="rowStyle" :row-config="{ height: 40, isCurrent: true, isHover: true }" :footer-method="footerMethod">
      <vxe-column type="checkbox" width="50"></vxe-column>
      <vxe-column type="seq" width="90"></vxe-column>
      <vxe-column field="buyNo" title="采购单号" width="80" :align="'center'">
        <template #default="{ row }">
          <a :href="'https://trade.1688.com/order/new_step_order_detail.htm?orderId=' + row.aliOrderNo" target="_blank"
            style="color: blue;">{{ row.buyNo }}</a>
        </template>
      </vxe-column>
      <vxe-column field="createdTime" title="采购单建立时间" width="150" :align="'center'" sortable></vxe-column>
      <vxe-column field="styleCode" title="款式编码" width="80" :align="'left'">
        <template #default="{ row }">
          <div :style="{ color: row.styleCodeStatusColor == 1 ? 'red' : '' }">
            {{ row.styleCode }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="status" title="采购单状态" width="135" :align="'center'">
        <template #default="{ row }">
          <span>{{ row.status }}</span>
        </template>
      </vxe-column>
      <vxe-column field="purchaseInType" title="入库状态" width="80" :align="'center'">
        <template #default="{ row }">
          <el-button type="text" @click="getPurchaseInTypePage(row)"> {{
            row.purchaseInType }}</el-button>
        </template>
      </vxe-column>
      <vxe-column field="countStr" title="商品总量" width="100" :align="'center'">
      </vxe-column>
      <vxe-column field="amount" title="金额" width="75" :align="'center'"
        v-if="checkPermission('OperationPersonnel')"></vxe-column>
      <vxe-column field="consignmentType" title="发货方式" width="80" :align="'center'">
      </vxe-column>
      <vxe-column field="orderNumber" title="单号" width="110" :align="'left'">
      </vxe-column>
      <vxe-column field="expectedDeliveryTime" title="预计到货时间" width="200" :align="'center'">
      </vxe-column>
      <vxe-column field="remarkImages" title="发货图" width="70">
        <template #default="{ row }">
          <div style="position: relative; display: inline-block;" v-if="row.remarkImages">
            <el-image class="custom-image" slot="reference" :src="row.remarkImages[0] || ''" fit="fill"
              :preview-src-list="row.remarkImages != '' ? row.remarkImages : ''" style="width: 40px; height: 38px;">
            </el-image>
            <span class="circle-badge">
              {{ row.remarkImages.length }}
            </span>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="remark" title="备注" width="200" :align="'left'"></vxe-column>
      <!-- <vxe-column field="" title="备注" width="60" :align="'center'">
        <template #default="{ row }">
          <i v-if="row.children" class="vxe-icon-flag-fill" style="font-size: 14px;cursor: pointer;"
            :style="{ color: (row.remark || row.remarkImages) ? '#F56C6C' : '#808080' }" slot="reference"
            @click="remarkpopupclick(row)">
          </i>
        </template>
      </vxe-column> -->
      <vxe-column field="lastTrackTime" title="最新操作日期" width="130" :align="'center'">
        <template #default="{ row }">
          <div>
            {{ formatDate(row.lastTrackTime) }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="brandId" title="采购员" width="90" :align="'center'" sortable>
        <template #default="{ row }">
          <span>{{ row.brandName }}</span>
        </template>
      </vxe-column>
      <vxe-column field="wmsId" title="仓库" width="150" :align="'left'">
        <template #default="{ row }">
          <span>{{ row.wmsName }}</span>
        </template>
      </vxe-column>
      <vxe-column field="inTransitSpanAvg" title="平均在途时长(天)" width="150" :align="'center'">
        <template #default="{ row }">
          <span>{{ row.inTransitSpanAvgStr }}</span>
        </template>
      </vxe-column>
      <vxe-column field="logs" title="日志" width="60" :align="'center'">
        <template #default="{ row }">
          <i class="el-icon-document" @click="logs(row)"></i>
        </template>
      </vxe-column>
      <vxe-column field="is1688" title="是否1688" width="100" :align="'center'" :visible="false">
        <template #default="{ row }">
          {{ row.is1688 == true ? '是' : row.is1688 == false ? '否' : '' }}
        </template>
      </vxe-column>
      <vxe-column field="indexNo" title="erp编码" width="75" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="payAccount" title="采购账号" width="100" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="orderNo" title="订单编号" width="110" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="telePhoneNumber" title="电话" width="110" :align="'center'" :visible="false">
        <template #default="{ row }">
          <div>
            {{ row.telePhoneNumber ? row.telePhoneNumber : '' }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="isSigned" title="是否签收" width="110" :align="'center'" :visible="false">
        <template #default="{ row }">
          <div>
            {{ row.isSigned == null ? '' : row.isSigned ? '是' : '否' }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="lastNeedTrackTime" title="最近一次需要更新日期" width="120" :align="'center'"
        :visible="false"></vxe-column>
      <vxe-column field="image" title="商品图片" width="80" :align="'center'" :visible="false">
        <template #default="{ row }">
          <el-image style="width: 60px; height: 60px" :src="row.image" :preview-src-list="[row.image]">
          </el-image>
        </template>
      </vxe-column>
      <vxe-column field="price" title="单价(元)" width="80" :align="'center'" v-if="checkPermission('OperationPersonnel')"
        :visible="false">
      </vxe-column>
      <vxe-column field="inventoryDay" title="可用库存天数" width="110" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="sellStock" title="实际可用数" width="95" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="trackUserName" title="跟单员" width="130" :align="'center'" sortable
        :visible="false"></vxe-column>
      <vxe-column field="startDept" title="发起部门" width="120" :align="'center'" :visible="false"></vxe-column>
      <vxe-column field="abnormalStatus" title="异常状态" width="90" :align="'center'" :visible="false">
        <template #default="{ row }">
          <span>{{ row.abnormalStatusStr }}</span>
        </template>
      </vxe-column>
    </vxe-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title='备注' :visible.sync="remarkPopup" width="40%" v-dialogDrag>
      <div style="height: 150px;">
        <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入备注内容" disabled
          v-model="remarklist.remark" style="width: 400px;">
        </el-input>
        <div style="margin-top: 10px;">
          <uploadimgFile ref="uploadimgFile" v-if="remarkPopup" :ispaste="false" :accepttyes="accepttyes"
            :isImage="true" :noDel="true" :disabled="true" :uploadInfo="remarklist.remarkImages" :keys="[1, 1]"
            @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
          </uploadimgFile>
        </div>
      </div>
      <div class="btnGroup">
        <el-button type="primary" v-throttle="3000" disabled>保存备注</el-button>
      </div>
    </el-dialog>

    <el-dialog title='日志' :visible.sync="logDetailsVisible" width="55%" v-dialogDrag>
      <div style="height: 400px;">
        <logDetails ref="logDetails" v-if="logDetailsVisible" />
      </div>
    </el-dialog>

    <vxe-modal title="入库状态" v-model="PurchaseInTypeIndexVisible" :esc-closable="true" :width='1200' :height='600'
      marginSize='-500'>
      <PurchaseInTypeIndex :buyNo="pageInfo.buyNo" ref="PurchaseInTypeIndex" v-if="PurchaseInTypeIndexVisible" />
    </vxe-modal>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import logDetails from './components/logDetails.vue'
import dayjs from 'dayjs'
import { getColumns, pageGetData, updateCount, getPurchaseOrderTrack } from '@/api/inventory/purchaseOrderTrack'
import { getTableColumnCache, GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '@/api/admin/business'
import pageGetChangeLogs from "@/api/inventory/basicgoods"
import PurchaseInTypeIndex from './PurchaseInTypeIndex.vue'
export default {
  name: "completed",
  components: {
    MyContainer, vxetablebase, uploadimgFile, logDetails, PurchaseInTypeIndex
  },
  data() {
    return {
      remarklist: {
        remark: null,
        remarkImages: []
      },
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      remarkPopup: false,
      quantity: true,
      that: this,
      ListInfo: {},
      timeRanges: [],
      tableCols: [],
      tableData: [],
      pageSize: 50,
      epositionlist: [],
      total: 0,
      loading: false,
      summaryarry: {},//汇总
      pickerOptions,
      logDetailsVisible: false,
      lastSortArgs: {
        field: "",
        order: "",
      },
      pageInfo: {
        buyNo: '',
      },
      PurchaseInTypeIndexVisible: false,
    }
  },
  created() {
    //手动将表格和工具栏进行关联
    this.$nextTick(() => {
      this.$refs.xTable.connect(this.$refs.xToolbar)
    })
  },
  async mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  methods: {
    getPurchaseInTypePage(row) {
      console.log(row, 'row');
      this.pageInfo.buyNo = row.buyNo
      this.PurchaseInTypeIndexVisible = true
    },
    formatDate(row) {
      return row ? dayjs(row).format('MM-DD HH:mm:ss') : ''
    },
    async restoreStore({ id, type, storeData }) {
      let resp = await GetVxeTableColumnCacheAsync({ tableId: id });
      let store = null;
      if (resp && resp.success && resp.data) {
        store = JSON.parse(resp.data);
      }
      return store ?? storeData;
    },
    async updateStore({ id, type, storeData }) {
      await SetVxeTableColumnCacheAsync({ tableId: id, ColumnConfig: JSON.stringify(storeData) });
    },
    checkMethod({ row }) {
      if (row.goodsCode) {
        return false
      } else {
        return true
      }
    },
    selectAllEvent({ checked }) {
      const records = this.$refs.xTable.getCheckboxRecords()
      this.$emit('oddNumber', records)
    },
    selectChangeEvent({ checked }) {
      const records = this.$refs.xTable.getCheckboxRecords()
      this.$emit('oddNumber', records)
    },
    rowStyle({ row, column }) {
      if (column.field == 'inventoryDay') {
        if (row.inventoryStatusColor == 1) {
          return {
            background: 'red',
            color: '#fff',
            border: '1px solid #fff',
            boxSizing: 'border-box'
          }
        } else {
          return {
            background: 'white',
            color: '#000'
          }
        }
      }
    },
    //获取图片
    getImg(data) {
      if (data) {
        this.remarklist.remarkImages = data
        // this.ruleForm.pictures = data.map(item => item.url).join(',')
      }
    },
    //备注
    remarkpopupclick(row) {
      this.remarklist.remarkImages = []
      if (row.remarkImages) {
        this.remarklist.remarkImages = row.remarkImages.map((item, i) => {
          return {
            url: item,
            name: `备注截图${i + 1}`
          }
        })
      }
      this.remarklist.remark = row.remark
      this.remarklist.id = row.id
      this.remarkPopup = true
    },
    //日志
    async logs(row) {
      const params = {
        logicId: row.id,
        scene: 'purchaseOrderTrack',
      }
      this.logDetailsVisible = true
      this.$nextTick(() => {
        this.$refs.logDetails.getList('search', params);
      })
    },
    //自定义排序
    async customSortMethod({ data, sortList }) {
      if (sortList && sortList.length > 0) {
        if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
          this.lastSortArgs = { ...sortList[0] };
          let a = {
            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
            prop: this.lastSortArgs.field
          };
          this.ListInfo.orderBy = a.prop
          this.ListInfo.isAsc = a.order.indexOf("descending") == -1 ? true : false
          await this.getList('', this.ListInfo, true)
        }
      }
    },
    //合计
    footerMethod({ columns, data }) {
      const sums = [];
      if (!this.summaryarry)
        return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
        return sums
      var hashj = false;
      columns.forEach((column, index) => {
        if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
          var sum = this.summaryarry[column.property + '_sum'];
          if (sum == null) return;
          sums[index] = sum
        }
        else sums[index] = ''
      });
      return [sums]
    },
    //获取列表
    async getList(type, listInfo, cpt) {
      this.ListInfo = { ...listInfo, pageSize: this.pageSize }
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
        this.ListInfo = { ...listInfo, isCpt: cpt, ...this.ListInfo, pageSize: this.pageSize }
      }
      this.loading = true
      const { data: { list, total, summary, extData }, success } = await pageGetData(this.ListInfo)
      if (success) {
        list.forEach(item => {
          if (item.remarkImages) {
            item.remarkImages = item.remarkImages.split(',')
          }
          item.children.forEach(child => {
            if (child.remarkImages) {
              child.remarkImages = child.remarkImages.split(',')
            }

          });
        });
        this.$emit('onrevealing', extData?.hasBrendQuery);
        this.tableData = list
        this.total = total
        this.summaryarry = summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    async Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.pageSize = val
      await this.getList('search', this.ListInfo, true)
    },
    //当前页改变
    async Pagechange(val) {
      this.ListInfo.currentPage = val;
      await this.getList('', this.ListInfo, true)
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .custom-image img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.btnGroup {
  margin-top: 40px;
  display: flex;
  justify-content: end;
}

::v-deep .custom-select .el-input__inner {
  background-color: #FFD942;
}

/*  工具箱位置  */
.vxetoolbar20221212 {
  position: absolute;
  top: 0px;
  right: 0px;
  padding-top: 0;
  padding-bottom: 0;
  z-index: 999;
  background-color: rgb(255 255 255 / 0%);
}

.vxetoolbar20221212 ::v-deep .vxe-custom--wrapper {
  margin-left: 0px !important;
}

.circle-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  font-size: 12px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  border-radius: 50%;
}
</style>
