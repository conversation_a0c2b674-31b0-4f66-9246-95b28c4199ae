<template>
    <my-container>
      <el-tabs v-model="activeName" style="height: 95%">
        <el-tab-pane label="人效数据分析" name="first1" style="height: 100%">
          <AhumanPerformance ref="AhumanPerformance" />
        </el-tab-pane>
        <el-tab-pane label="运营组占比" name="first2" style="height: 100%" lazy>
          <BproportionOperation ref="BproportionOperation" />
        </el-tab-pane>
        <el-tab-pane label="明细数据" name="first3" style="height: 100%" lazy>
          <CdetailData ref="CdetailData" />
        </el-tab-pane>
      </el-tabs>
    </my-container>
  </template>
  <script>
  import MyContainer from "@/components/my-container";
//   import recruitment from "./recruitment/index.vue";
//   import department from "./department/index.vue";
  
  import AhumanPerformance from "@/views/inventory/energyEfficiency/AhumanPerformance/index.vue";
  import BproportionOperation from "@/views/inventory/energyEfficiency/BproportionOperation/index.vue";
  import CdetailData from "@/views/inventory/energyEfficiency/CdetailData/index.vue";
//   import BsscData from "@/views/profit/sscManager/BsscData/index.vue";
//   import CleaveManagement from "@/views/profit/sscManager/CleaveManagement/index.vue";
//   import CmtrainingData from "@/views/profit/sscManager/CmtrainingData/index.vue";
  
  export default {
    name: "sscManagementIndex",
    components: {
      MyContainer, AhumanPerformance, BproportionOperation, CdetailData
    },
    data() {
      return {
        activeName: "first1",
      };
    },
  };
  </script>
  <style lang="scss" scoped></style>
  