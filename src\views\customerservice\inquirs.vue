<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.Shopname" placeholder="店铺" style="width:120px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.Snick" placeholder="旺旺" style="width:120px;" />
          </el-button>

          <el-button style="padding: 0;margin: 0;">
            <datepicker v-model="Filter.Sdate"></datepicker>
          </el-button>
          <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onImportSyj">导入</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
    </template>
    <el-dialog title="添加客服人员咨询数据信息" :visible.sync="add客服人员咨询数据dialogVisibleSyj" width="30%">
      <span>

      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="客服人员咨询数据" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :on-change="onUploadChange2"
          :on-remove="onUploadRemove2">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success"
            @click="onSubmitupload2">上传</my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import { importInquirsAsync, getInquirsList, deleteInquirsBatch } from '@/api/customerservice/inquirs'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
  { istrue: true, prop: 'shopname', label: '店铺', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'snick', label: '旺旺', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'inquirs', label: '咨询人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'receivecount', label: '接待人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'ipscount', label: '询单人数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'nextdaysuccesspaycount', label: '询单>次日付款人数', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'nextdaysuccesspayrate', label: '询单>次日付款成功率', width: '120', sortable: 'custom', formatter: (row) => { return row.nextdaysuccesspayrate ? (row.nextdaysuccesspayrate * 100).toFixed(1) + "%" : 0 } },
  { istrue: true, prop: 'successpaycount', label: '询单>最终付款人数', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'successpayrate', label: '询单>最终付款成功率', width: '120', sortable: 'custom', formatter: (row) => { return row.successpayrate ? (row.successpayrate * 100).toFixed(1) + "%" : 0 } },
  { istrue: true, prop: 'responseTime', label: '平均响应(秒)', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'satisDegree', label: '满意度', width: '100', sortable: 'custom',formatter:(row) => row.satisDegree !== null  ? (100 * row.satisDegree).toFixed(2) +'%'  : '0%' },
  { istrue: true, prop: 'evaluateCount', label: '服务满意度评价参与量', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'evaluateVerySatisCount', label: '服务满意度评价很满意', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'evaluateSatisCount', label: '服务满意度评价满意', width: '120', sortable: 'custom' },
  
  { istrue: true, prop: 'salesvol', label: '销售额', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'sdate', label: '日期', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom' },
  { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
  { istrue: true, type: "button", label: '操作', width: "120", btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) }] }
];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
  data() {
    return {
      that: this,
      Filter: {
      },
      shopList: [],
      userList: [],
      groupList: [],
      inquirslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      add客服人员咨询数据dialogVisibleSyj: false,
    };
  },
  async mounted() {
    var that = this;
  },
  methods: {
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次客服人员咨询数据数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteInquirsBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()

        });

    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async onUploadChange2(file, fileList) {
      this.fileList = fileList;
    },
    async onUploadRemove2(file, fileList) {
      this.fileList = [];
    },
    async uploadFile2(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      const res = await importInquirsAsync(form);
      if (res?.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success" });
        this.dialogVisibleSyj = false;
      }
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getinquirsList();
    },

    async getinquirsList() {
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0];
        this.Filter.endAccountDate = this.Filter.UseDate[1];
      }
      if (this.Filter.Sdate) {
        this.Filter.startSdate = this.Filter.Sdate[0];
        this.Filter.endSdate = this.Filter.Sdate[1];
      }
      else {
        this.Filter.startSdate = null;
        this.Filter.endSdate = null;
      }
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getInquirsList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.inquirslist = res.data.list;
      //this.summaryarry=res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
