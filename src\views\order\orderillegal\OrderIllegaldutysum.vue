<template>
  <my-container v-loading="pageLoading">
    <el-row style="margin-top: 30px">
      <el-col :span="3" :offset="1">
        <el-card :body-style="{ padding: '20px', paddingLeft: '10px', paddingRight: '10px' }" shadow="always">
          <div slot="header">
            <span>发货仓汇总</span>
          </div>
          <el-table :data="fhdutysumlist" style="width: 100%">
            <el-table-column prop="date" label="日期" width="180">
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="address" label="地址">
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="3" :offset="1">
        <el-card :body-style="{ padding: '20px', paddingLeft: '10px', paddingRight: '10px' }" shadow="always">
          <div slot="header">
            <span>采购汇总</span>
          </div>
          <el-table :data="fhdutysumlist" style="width: 100%">
            <el-table-column prop="date" label="日期" width="180">
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="address" label="地址">
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="3" :offset="1">
        <el-card :body-style="{ padding: '20px', paddingLeft: '10px', paddingRight: '10px' }" shadow="always">
          <div slot="header">
            <span>运营汇总</span>
          </div>
          <el-table :data="fhdutysumlist" style="width: 100%">
            <el-table-column prop="date" label="日期" width="180">
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="address" label="地址">
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="3" :offset="1">
        <el-card :body-style="{ padding: '20px', paddingLeft: '10px', paddingRight: '10px' }" shadow="always">
          <div slot="header">
            <span>快递汇总</span>
          </div>
          <el-table :data="fhdutysumlist" style="width: 100%">
            <el-table-column prop="date" label="日期" width="180">
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="address" label="地址">
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import { getWithProCodeSum } from "@/api/order/orderdeductmoney"

export default {
  name: 'OrderIllegaldutysum',
  components: { MyContainer },
  props: {
    filter: {}
  },
  data () {
    return {
      that: this,
      pageLoading: false,
      fhdutysumlist: [],
    };
  },
  async mounted () {
    await this.onSearch()
  },
  methods: {
    async onSearch () {
      //if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
      await this.getlist();
    },
    //查询
    async getlist () {
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getWithProCodeSum(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }

    },


  }
};
</script>

<style lang="scss" scoped></style>
