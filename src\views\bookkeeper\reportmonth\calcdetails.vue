<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;" v-if="filter.oldcalcType == 2">
                    <el-select filterable v-model="filter.calcType" collapse-tags clearable placeholder="结算类型"
                        style="width: 90px">
                        <el-option key="0" label="普通天猫" :value="0"></el-option>
                        <el-option key="2" label="菜鸟" :value="2"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;" v-if="filter.column == 'techServiceFee' || filter.column == 'techServiceFeeBefor'
                    || filter.column == 'smallPayment' || filter.column == 'fullRefundPayment' || filter.column == 'ddInBao'
                    || filter.column == 'deposit' || filter.column == 'insurance' || filter.column == 'villageServiceFee'
                    || filter.column == 'withholdingPoints' || filter.column == 'withholdingReturned' || filter.column == 'publicDonation'
                    || filter.column == 'hbServiceFee' || filter.column == 'enjoymentFee' || filter.column == 'cashRed_Tx'
                    || filter.column == 'taobaoAllianceRefund' || filter.column == 'tmallCommission' || filter.column == 'creditCardServiceFee'
                    || filter.column == 'quickPaymentServiceFee' || filter.column == 'enjoymentFirstPlan' || filter.column == 'enjoymentFirstPlan11'
                    || filter.column == 'taobaoSpecial' || filter.column == 'yxkk' || filter.column == 'directJointPromotion'
                    || filter.column == 'c2mReturn' || filter.column == 'dayBuy' || filter.column == 'deductionHand'
                    || filter.column == 'platformServiceFee' || filter.column == 'commissionFee' || filter.column == 'channelDivision'
                    || filter.column == 'investmentServiceFee' || filter.column == 'promotionFee' || filter.column == 'otherShareAmount'

                    || filter.column == 'enjoymentYJ' || filter.column == 'overseaCalc' || filter.column == 'bbFarmSoftServiceFee'
                    || filter.column == 'brandNewProductSoft' || filter.column == 'brandNewManPlan' || filter.column == 'daySepcialSaelSoft'
                    || filter.column == 'tbNewManGiftTechnology' || filter.column == 'tmAbroadBackInsure' || filter.column == 'ttGearCommissionDeduct'
                    || filter.column == 'settlementCpsDeduct' || filter.column == 'tbFreeCommissionDeduct'
                    || filter.column == 'suprPlusServiceFee' || filter.column == 'billionSubsidyServiceFee' || filter.column == 'typeNewCusServeceFee'
                    || filter.column == 'advanceBackRepairFee' || filter.column == 'timedPlusServiceFee' || filter.column == 'costEffectiveCommission'
                    || filter.column == 'kuaJingTrustTechnology' || filter.column == 'kuaJingPopularizeService' || filter.column == 'saleAfterPay'
                    || filter.column == 'taoBaoPopularizeService' || filter.column == 'baBaNongChangCpsCommission'
                    || filter.column == 'sellerDepositPromotionService' || filter.column == 'techServiceFeeBefor2'
                    || filter.column == 'taoCustomDeductCommission' || filter.column == 'billionSubsidyFreightInsure'
                    || filter.column == 'pinPaiXinXiang' || filter.column == 'jiChuRuanJianFuWuFei' || filter.column == 'tiShengJiHuaFuWuFei'
                    || filter.column == 'baiYiDingXiangFeiYong' || filter.column == 'daCuRuanJianFuWuFei' || filter.column == 'lianMengYongJinDaiKou'
                    || filter.column == 'backCompensate' || filter.column == 'redSignSupplierCpsCommission' || filter.column == 'gfjjSoftwareServiceFee'
                ">
                    <el-tooltip class="item" effect="dark" content="计算步骤,账单A+账单B-账单C" placement="top-end">
                        <span><i class="el-icon-question"></i></span>
                    </el-tooltip>
                    <el-select filterable v-model="filter.step" collapse-tags placeholder="计算步骤" style="width: 90px">
                        <el-option key="1" label="账单A" :value="1"></el-option>
                        <el-option key="2" label="账单B" :value="2"></el-option>
                        <el-option key="3" label="账单C" :value="3"></el-option>
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch1">查询</el-button>
                <!-- <el-button v-if="filter.oldcalcType==2" type="primary" @click="onSearch1">查询</el-button> -->
                <!-- <el-button  type="primary" @click="onSearch1"
         v-else-if="filter.column=='techServiceFee'||filter.column=='techServiceFeeBefor'
               ||filter.column=='smallPayment'||filter.column=='fullRefundPayment'||filter.column=='ddInBao'
               ||filter.column=='deposit'||filter.column=='insurance'||filter.column=='villageServiceFee'               
               ||filter.column=='withholdingPoints'||filter.column=='withholdingReturned'||filter.column=='publicDonation'
               ||filter.column=='hbServiceFee'||filter.column=='enjoymentFee'||filter.column=='cashRed_Tx'
               ||filter.column=='taobaoAllianceRefund'||filter.column=='tmallCommission'||filter.column=='creditCardServiceFee'
               ||filter.column=='quickPaymentServiceFee'||filter.column=='enjoymentFirstPlan'||filter.column=='enjoymentFirstPlan11'
               ||filter.column=='taobaoSpecial'||filter.column=='yxkk'||filter.column=='directJointPromotion'
               ||filter.column=='c2mReturn'||filter.column=='dayBuy'||filter.column=='deductionHand'">查询
        </el-button> -->
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button style="padding: 0;">
                    <el-alert style="padding: 4px;" title="温馨提示:由于明细中没有包含分摊部分数据，所以明细中的汇总可能跟报表中不一致" type="warning"
                        show-icon :closable="false"></el-alert>
                </el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='list'
            :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </container>
</template>
<script>
import { getbycode, getList as getshopList } from '@/api/operatemanage/base/shop';
import { pageAmountDetail, exportAmountDetail } from '@/api/monthbookkeeper/monthreport'
import { formatProName, formatTime, formatPlatform, formatLinkProCode, platformlist } from "@/utils/tools";
//import cesTable from "@/components/Table/table.vue";
import cesTable from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import container from "@/components/my-container";
import { Loading } from 'element-ui';
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, prop: 'orderNo', fix: true, label: '订单', width: '200', type: 'custom' },//,sortable:'custom'
    { istrue: true, prop: 'amont', label: '金额', width: '100' },//sortable:'custom',
];
export default {
    name: "Users",
    components: { container, cesTable },
    data() {
        return {
            that: this,
            filter: {
                column: null,
                version: null,
                shopCode: null,
                yearMonth: null,
                proCode: null,
                calcType: null,
                oldcalcType: null,
                recoganizeType: null,
                step: 1,
            },
            list: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: " orderno ", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
        };
    },
    async mounted() {
    },
    methods: {
        convertrecoganizeType(column) {
            this.filter.recoganizeType = null;
            if (column == 'techServiceFee') this.filter.recoganizeType = 2;
            else if (column == 'techServiceFeeBefor') this.filter.recoganizeType = 3;
            else if (column == 'smallPayment') this.filter.recoganizeType = 4;
            else if (column == 'fullRefundPayment') this.filter.recoganizeType = 5;
            else if (column == 'ddInBao') this.filter.recoganizeType = 6;
            else if (column == 'expressKK') this.filter.recoganizeType = 7;
            else if (column == 'deposit') this.filter.recoganizeType = 40;
            else if (column == 'insurance') this.filter.recoganizeType = 10;
            else if (column == 'villageServiceFee') this.filter.recoganizeType = 11;
            else if (column == 'withholdingPoints') this.filter.recoganizeType = 12;
            else if (column == 'withholdingReturned') this.filter.recoganizeType = 13;
            else if (column == 'publicDonation') this.filter.recoganizeType = 14;
            else if (column == 'hbServiceFee') this.filter.recoganizeType = 15;
            else if (column == 'cashRed_Tx') this.filter.recoganizeType = 16;
            else if (column == 'enjoymentFee') this.filter.recoganizeType = 33;
            else if (column == 'taobaoAllianceRefund') this.filter.recoganizeType = 20;
            else if (column == 'tmallCommission') this.filter.recoganizeType = 17;
            else if (column == 'creditCardServiceFee') this.filter.recoganizeType = 18;
            else if (column == 'quickPaymentServiceFee') this.filter.recoganizeType = 27;
            else if (column == 'enjoymentFirstPlan') this.filter.recoganizeType = 22;
            else if (column == 'enjoymentFirstPlan11') this.filter.recoganizeType = 21;
            else if (column == 'taobaoSpecial') this.filter.recoganizeType = 25;
            else if (column == 'yxkk') this.filter.recoganizeType = 32;
            else if (column == 'directJointPromotion') this.filter.recoganizeType = 29;
            else if (column == 'c2mReturn') this.filter.recoganizeType = 30;
            else if (column == 'dayBuy') this.filter.recoganizeType = 34;
            else if (column == 'deductionHand') this.filter.recoganizeType = 41;
            else if (column == 'brandNewProductSoft') this.filter.recoganizeType = 51;
            else if (column == 'brandNewManPlan') this.filter.recoganizeType = 52;
            else if (column == 'daySepcialSaelSoft') this.filter.recoganizeType = 53;
            else if (column == 'tbNewManGiftTechnology') this.filter.recoganizeType = 54;
            else if (column == 'tmAbroadBackInsure') this.filter.recoganizeType = 55;
            else if (column == 'ttGearCommissionDeduct') this.filter.recoganizeType = 56;
            else if (column == 'settlementCpsDeduct') this.filter.recoganizeType = 57;
            else if (column == 'tbFreeCommissionDeduct') this.filter.recoganizeType = 58;
            else if (column == 'suprPlusServiceFee') this.filter.recoganizeType = 90;
            else if (column == 'billionSubsidyServiceFee') this.filter.recoganizeType = 91;
            else if (column == 'typeNewCusServeceFee') this.filter.recoganizeType = 92;
            else if (column == 'advanceBackRepairFee') this.filter.recoganizeType = 93;
            else if (column == 'timedPlusServiceFee') this.filter.recoganizeType = 94;
            else if (column == 'costEffectiveCommission') this.filter.recoganizeType = 95;

            else if (column == 'kuaJingTrustTechnology') this.filter.recoganizeType = 101;
            else if (column == 'kuaJingPopularizeService') this.filter.recoganizeType = 102;
            else if (column == 'saleAfterPay') this.filter.recoganizeType = 103;
            else if (column == 'taoBaoPopularizeService') this.filter.recoganizeType = 104;
            else if (column == 'baBaNongChangCpsCommission') this.filter.recoganizeType = 105;

            else if (column == 'sellerDepositPromotionService') this.filter.recoganizeType = 112;
            else if (column == 'techServiceFeeBefor2') this.filter.recoganizeType = 113;
            else if (column == 'taoCustomDeductCommission') this.filter.recoganizeType = 116;
            else if (column == 'billionSubsidyFreightInsure') this.filter.recoganizeType = 119;

            else if (column == 'pinPaiXinXiang') this.filter.recoganizeType = 61;
            else if (column == 'jiChuRuanJianFuWuFei') this.filter.recoganizeType = 62;
            else if (column == 'tiShengJiHuaFuWuFei') this.filter.recoganizeType = 63;
            else if (column == 'baiYiDingXiangFeiYong') this.filter.recoganizeType = 64;
            else if (column == 'daCuRuanJianFuWuFei') this.filter.recoganizeType = 65;
            else if (column == 'lianMengYongJinDaiKou') this.filter.recoganizeType = 66;
            else if (column == 'backCompensate') this.filter.recoganizeType = 68;
            else if (column == 'redSignSupplierCpsCommission') this.filter.recoganizeType = 69;
            else if (column == 'gfjjSoftwareServiceFee') this.filter.recoganizeType = 70;

            else if (column == 'priceBackCommissionFee') this.filter.recoganizeType = 2;
            else if (column == 'priceDeductFee') this.filter.recoganizeType = 3;
            else if (column == 'insuranceServiceFee') this.filter.recoganizeType = 4;
            else if (column == 'saleAfterCompensateFee') this.filter.recoganizeType = 5;
            else if (column == 'orderGiveBean') this.filter.recoganizeType = 6;
            else if (column == 'commissionFee') this.filter.recoganizeType = 7;
            else if (column == 'freightInsuranceServiceFee') this.filter.recoganizeType = 8;
            else if (column == 'collectionDeliveryFee') this.filter.recoganizeType = 9;
            else if (column == 'advertReducCommission') this.filter.recoganizeType = 10;
            else if (column == 'tradeServiceFee') this.filter.recoganizeType = 11;
            else if (column == 'sellerBackFreight') this.filter.recoganizeType = 12;
            else if (column == 'jingZhunTongCollection') this.filter.recoganizeType = 13;
            else if (column == 'jingXiZhiYingServiceFee') this.filter.recoganizeType = 14;
            else if (column == 'jingXiZhiYingJiaBaoBuTie') this.filter.recoganizeType = 15;
            else if (column == 'pingTaiQuanJiaBaoBuTie') this.filter.recoganizeType = 16;
            else if (column == 'productInsuranceServiceFee') this.filter.recoganizeType = 17;
            else if (column == 'zongHeWeiYueJin') this.filter.recoganizeType = 18;

            else if (column == 'platformServiceFee') this.filter.recoganizeType = 1;
            else if (column == 'channelDivision') this.filter.recoganizeType = 3;
            else if (column == 'investmentServiceFee') this.filter.recoganizeType = 4;
            else if (column == 'promotionFee') this.filter.recoganizeType = 5;
            else if (column == 'otherShareAmount') this.filter.recoganizeType = 6;
            //else if (column == 'commissionFee') this.filter.recoganizeType = 2;
        },
        async onSearch(column, version, shopCode, yearMonth, proCode) {
            this.filter.column = column;
            this.filter.version = version;
            this.filter.shopCode = shopCode;
            this.filter.yearMonth = yearMonth;
            this.filter.proCode = proCode;
            var res = await getbycode(shopCode);
            this.filter.calcType = res.data?.settlementCalcType;
            this.filter.oldcalcType = res.data?.settlementCalcType;
            this.convertrecoganizeType(column);
            await this.onSearch1();
        },
        async onSearch1() {
            this.list = [];
            this.$refs.pager.setPage(1);
            await this.getList()
        },
        async getList() {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };
            startLoading();
            const res = await pageAmountDetail(params);
            this.total = res.data?.total
            this.list = res.data?.list;
            this.summaryarry = res.data?.summary;
            loading.close();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await exportAmountDetail(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '明细数据' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch1();
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>