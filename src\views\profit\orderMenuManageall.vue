<template>
 <container v-loading="pageLoading">
     <template #header>
         <el-button-group>
             <el-button style="padding:0;margin:0;">

                 <el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"></el-date-picker>

                 <el-select v-model="filter.gysName" placeholder="供应商类型" clearable filterable style="width: 160px">
                  <el-option :label="item" :value="item" v-for="(item,index) in gysList" :key="index" />
                 </el-select>

                 <!-- <el-input v-model.trim="filter.zoneName" placeholder="地区搜索" style="width: 160px" :maxlength="50" clearable></el-input> -->

                 <!-- <el-select v-model="filter.zoneName" placeholder="地区搜索" clearable filterable style="width: 160px">
                    <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
                </el-select> -->

             </el-button>
             <el-button type="primary" @click="onSearch">查询</el-button>
             <el-button type="primary" @click="exportCurrDataEvent4">导出订单</el-button>
         </el-button-group>

     </template>

     
    <el-tabs v-model="tabindex" :tab-position="tabPosition" style="height: 100%;" @tab-click="onSearch">
        <el-tab-pane :label="item" style="height: 100%;" v-for="(item,index) in quyuList" :key="index">
            <div id="YunHanAdminGoods20231018" style="width: 100%; height: 100%">
                <yhVxetable ref="tabletwowee" v-if="tableshow"  :resizable="true" :somerow="'dateStr'" :hasSeq='false' :scrollY="{gt: 500, enabled: false}" :scrollX="{gt: 500, enabled: false}" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
                :tableData='tableData' :tableCols='tableCols' :border="true" :cellClassName="cellClassName"
                :isSelectColumn="false" :loading="listLoading">
                </yhVxetable>
            </div>
            <!-- <div id="YunHanAdminGoods20231018" style="width: 100%; height: 100%">
                <yhVxetable ref="tabletwowee" v-if="tableshoww" :resizable="true" :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
                :tableData='tableData' :tableCols='tableCols' :border="true" :cellClassName="cellClassName"
                :isSelectColumn="false" :loading="listLoading">
                </yhVxetable>
            </div> -->
        </el-tab-pane>
    </el-tabs>

     
     
     

 </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import yhVxetable from '@/components/VxeTable/yh_vxetableNotFixNum.vue';
import exportExecl from "@/utils/exportExecl.js"

import { formatLinkProCode, platformlist } from '@/utils/tools'
import { getOrderFoodMenuList, exportOrderMenuManageAsync,exportOrderMenuGroupAsync,exportOrderMenuAsync, getOrderFoodMenuProvier, getOrderFoodMenuStatisList, getAreaSetList   } from '@/api/profit/orderfood';
import { setTimeout } from "timers";

const tableCols = [
 { istrue: true, prop: 'id', label: '订单编号', sortable: 'custom', width: '160' },
 { istrue: true, prop: 'createdUserName', label: '点餐人', sortable: 'custom', width: '160' },
 { istrue: true, prop: 'createdGroupName', label: '对应组', sortable: 'custom', width: '120' },
 { istrue: true, prop: 'createdTime', label: '点餐时间', width: '160', sortable: 'custom', },
 { istrue: true, prop: 'gysName', label: '供应商名称', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'menuLabel', label: '标签', width: '120', sortable: 'custom' },
 { istrue: true, prop: 'menuNames', label: '菜名', width: '200', sortable: 'custom' },
 {istrue:true,summaryEvent: true,prop:'',label:'退款', merge:true,permission:"cgcoltxpddprsi",
 cols:[
     {istrue:true,summaryEvent:true,prop:'cancelOrderCount',label:'发货前退款单量',sortable:'custom', width:'80',type:'custom'},
     {istrue:true,summaryEvent:true,prop:'refundAmontBefore',label:'发货前退款',sortable:'custom', width:'80',type:'custom'},
 ]},
 {
     istrue: true, prop: 'enabled', label: '状态', width: '150', sortable: 'custom',
     formatter: (row) => { return row.enabled ? "正常点餐" : "取消点餐" }
 },
];

const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
 name: "Users",
 components: { container, yhVxetable },
 data () {
     return {
         that: this,
         tabindex: 0,
         tabPosition: 'left',
         daochushow: false,
         tableshow: true,
         tableshoww: true,
         gysList: [],
         pickerOptions: {  
             disabledDate(time) {  
             const currentDate = new Date();  
             const currentTime = currentDate.getTime();  
             return time.getTime() > currentTime;  
             }  
         },
         filter: {
             timerange: [
                 formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                 formatTime(new Date(), "YYYY-MM-DD"),
             ],
             gysName: '',
             zoneName: ''
         },
         platformlist: platformlist,
         tableCols: [],
         tableCols1: [],

         tableHandles: null,
         // tableData: [],
         // total: 0,
         pager: { OrderBy: "id", IsAsc: false },
         listLoading: false,
         pageLoading: false,
         summaryarry: {},
         sels: [],
         quyuList: [],
         allAlign: 'center',
         tableData: [
         ],
         tableData1: [
         ],
         mergeCells: [
         { row: 1, col: 1, rowspan: 3, colspan: 3 },
         { row: 5, col: 0, rowspan: 2, colspan: 2 }
         ]
     };
 },
 mounted () {
     this.onSearch();
     // let num = new Date(endDate).getDay();

     
     // if(num==1){
     //     this.filter.weekTime = '周一';
     // }else if(num == 2){
     //     this.filter.weekTime = '周二';
     // }else if(num == 3){
     //     this.filter.weekTime = '周三';
     // }else if(num == 4){
     //     this.filter.weekTime = '周四';
     // }else if(num == 5){
     //     this.filter.weekTime = '周五';
     // }else if(num == 6){
     //     this.filter.weekTime = '周六';
     // }else if(num == 7){
     //     this.filter.weekTime = '周日';
     // }
     // return formatTime(new Date(endDate), 'YYYY-MM-DD')

     
 },
 methods: {
     cellClassName ({ row, column }) {
         return null
     },
     exportCurrDataEvent4 () {
      let _this = this;
      _this.tableshoww = false;
      _this.$nextTick(function () {
        exportExecl("YunHanAdminGoods20231018", `汇总订单统计——${_this.filter.gysName?_this.filter.gysName:''+_this.quyuList[_this.tabindex]}` + endDate+'.xlsx');
        setTimeout(()=>{
            _this.tableshoww = true;
        },0)
      })
     },
     async onSearch () {
        await this.getquyu();
         await this.getgys();
         
         await this.getList();
     },
     async getgys(){
         const res = await getOrderFoodMenuProvier();
         if(!res.success){
             return
         }
         this.gysList = res.data;
     },
     async getquyu(){
        const res = await getAreaSetList({getLevel: 1});
        if(!res.success){
            return
        }
        this.quyuList = res.data;
        this.filter.zoneName = this.quyuList[this.tabindex];
    },
     
     async exportOrderMenuGroup () {
         if (this.filter.timerange) {
             this.filter.startTime = this.filter.timerange;
             this.filter.endTime = this.filter.timerange;
         }
         const params = { ...this.filter };
         const res = await exportOrderMenuAsync(params);
         if (!res?.data) return;
         const aLink = document.createElement("a");
         let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
         aLink.href = URL.createObjectURL(blob)
         aLink.setAttribute('download', '菜单数量合计_' + new Date().toLocaleString() + '.xlsx');
         aLink.click()
     },
     async exportOrderMenuManage () {
         if (this.filter.timerange) {
             this.filter.startTime = this.filter.timerange;
             this.filter.endTime = this.filter.timerange;
         }
         const params = { ...this.filter };
         const res = await exportOrderMenuManageAsync(params);
         if (!res?.data) return;
         const aLink = document.createElement("a");
         let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
         aLink.href = URL.createObjectURL(blob)
         aLink.setAttribute('download', '点餐订单数据_' + new Date().toLocaleString() + '.xlsx');
         aLink.click()
     },
     async sortchange (column) {
         if (!column.order)
             this.pager = {};
         else
             this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
         await this.onSearch();
     },
     async getList () {

         var that = this;
         this.listLoading = true;
         this.pageLoading = true;
         this.tableshow = false;

         this.filter.startTime =  this.filter.timerange[0];
         this.filter.endTime =  this.filter.timerange[1];

         const params = {...this.filter, };
         const res = await getOrderFoodMenuStatisList(params).then(res => {


             let onearr = [
                 { istrue: true, prop: 'dateStr', label: '时间',    width: '100' },
                 { istrue: true, prop: 'menuLabel', label: '类型',    width: '100' },
             ]



             
             

             res.data.forEach((item, zuindex)=>{
                 item.totalDatas.forEach((detailItem, detailItemindex)=>{

                     var isexit = false;
                     onearr.map((itemm)=>{
                         if(itemm.prop == 'name' + detailItemindex){
                             isexit = true;
                         }
                     })
                     if(!isexit){
                         onearr.push({
                             istrue: true,
                             prop: 'name' + detailItemindex,
                             label: detailItem.name,
                             width: '100',
                             type: 'custom',
                         });
                     }
                     

                     item['name' + detailItemindex] = detailItem.totalQunatity;
       
                     return;
                 });

                 
                 //树状结构
                 
                 item.groupDatas.forEach((itemgroupDatas, groupDatasindex)=>{
                
                  onearr.push({istrue:true,summaryEvent: true,prop:groupDatasindex+"faname",label: itemgroupDatas['name'], merge:true, cols:[
                   ]})
                 })

                 

                 onearr = onearr.filter((item, index, self) => {  
                     return self.findIndex(t => t.label === item.label) === index;  
                 }); 

                 function matchAndAssign(A, B) {
                    A.forEach(aItem => {
                        const bItem = B.find(bItem => bItem.name === aItem.label);
                        if (bItem) {
                            aItem.cols = bItem.details.map(detail => ({
                                istrue: true,
                                summaryEvent: true,
                                prop: detail.name,
                                label: detail.name,
                                sortable: 'custom',
                                width: '100',
                                type: 'custom'
                            }));
                        }
                    });
                    return A;
                }

                let newc =  matchAndAssign(onearr, item.groupDatas);
                

                function flattenArrayWithParentLabel(arr) {
                    return arr.map(parentItem => {
                        if(!parentItem.cols){
                            return parentItem;
                        }
                        const updatedCols = parentItem.cols.map(childItem => ({
                            ...childItem,
                            prop: `${parentItem.label}+${childItem.prop}`
                        }));
                        return {
                            ...parentItem,
                            cols: updatedCols
                        };
                    });
                }

                let columns = flattenArrayWithParentLabel(newc);


                onearr = columns;



                item.groupDatas.forEach(parentItem => {
                        parentItem.details.map(detailItem => (
                                    item[`${parentItem.name}+${detailItem.name}`] = detailItem.totalQunatity
                                ))
                        }
                    
                );
                

                 onearr.map((itemaa)=>{
                  if(itemaa.merge){
                   itemaa.cols.map((itemaacols)=>{
                       itemaa.cols = itemaa.cols.filter((item, index, self) => {  
                          return self.findIndex(t => t.prop === item.prop) === index;  
                      }); 
                   })
                  }
                 })
                 
                 

                 
                 

                  //树状结束
             })


             this.tableCols = onearr;
             this.tableData = res.data;
             this.pageLoading = false;




         });
         this.tableshow = true;
         this.listLoading = false;
     },
     removeDuplicatesByName(array, name) {  
         return array.filter((item, index) => {
             return array.findIndex(obj => obj[name] === item[name]) === index;  
         });  
     }  
 }
})
</script>
<style lang="scss" scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
 color: blue;
}
// ::v-deep .vxe-table--body-wrapper{
//     overflow-x: hidden;
// }
.vxetable202212161323{
 border: none !important;
}
// ::v-deep .vxe-table--body-wrapper{
//     display: none;
// }
</style>

