<template>
  <my-container>
    <div style="margin: 10px 0; display: flex; align-items: center; justify-content: space-between;">
      <div style="flex: 1; align-items: center; justify-content: flex-start;">
        <div style="display: inline-block; margin-right:10px; margin-bottom: 10px;width: 160px;" class="publicCss">
          <inputYunhan ref="productCode" :inputt.sync="filter.finishedProductCode" v-model="filter.finishedProductCode"
            placeholder="成品编码" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="3998"
            :width="'238'" @callback="callbackGoodsCode" title="成品编码">
          </inputYunhan>
        </div>
        <el-input style="width: 200px; margin-right: 10px;" v-model.trim="filter.finishedProductName" :maxlength="100"
          placeholder="成品名称" suffix-icon="el-icon-search" clearable class="publicCss" />
        <el-input style="width: 160px; margin-right: 10px;" v-model.trim="filter.halfProductCode" :maxlength="100"
          placeholder="半成品编码" @keyup.enter.native="onSearch" clearable class="publicCss" />
        <el-input style="width: 250px; margin-right: 10px;" v-model.trim="filter.halfProductName" :maxlength="100"
          placeholder="半成品名称" suffix-icon="el-icon-search" clearable class="publicCss" />
        <el-button type="primary" @click="onSearch" style="width: 90px; margin-right: 10px;">查询</el-button>
        <el-button @click="onclear" plain>重置</el-button>
      </div>
      <div style="width: 40%; display: flex; align-items: center; justify-content: flex-end;">
        <div style="color: red; font-size: 14px; text-align: right; margin-right: 20px;">库存最后更新时间:{{ uploadTime }}</div>
        <el-button type="primary" @click="flushedTime" style="margin-left: 20px;"
          :disabled="isClick">刷新实际库存&nbsp;&nbsp;&nbsp;{{ (diffTime && diffTime > 0) ? '剩余' + diffTime + 's' : ''
          }}</el-button>
      </div>
    </div>

    <!-- <div style="width:42%;display: inline-block;text-align: right;">
        <el-button type="primary" @click="handleExportCommand">导出</el-button>
      </div> -->

    <div style="height: 90%;">
      <vxe-table ref="xTable" border="default" height="100%" :show-footer="true" style="width: 100%;"
        class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe show-overflow
        :show-footer-overflow="'tooltip'" keep-source size="mini" :loading="pageLoading" :data="tableData"
        :scroll-y="{ gt: 100 }" :scroll-x="{ gt: 100 }" :header-row-class-name="'height cellheight1'"
        :expand-config="{ accordion: true }" :row-config="{ isCurrent: true, isHover: true }" @checkbox-all="checkmethod"
        @checkbox-change="checkmethod" :sort-config="{ transform: true, sortMethod: customSortMethod }" >
        <vxe-column type="checkbox" width="40" fixed="left"></vxe-column>
        <!-- <vxe-column title="编号" field="packagesProcessingId" width="55" align="left" fixed='left'>
        </vxe-column> -->
        <vxe-column type="seq" width="45" fixed="left"></vxe-column>
        <vxe-column field="halfProductImg" title="半成品图" width="66" align="left" fixed='left'>
          <template #default="{ row }">
            <el-image style="width: 50px; height: 50px" :src="row.halfProductImg"
              :preview-src-list="[row.halfProductImg]">
            </el-image>
          </template>
        </vxe-column>
        <vxe-column type="expand" align="left" width="25" :edit-render="{}" fixed='left'>
          <template #content="{ row, rowIndex }">
            <div style="padding: 20px 220px 20px 180px;background-color: #8a8484;">
              <vxe-table height="200px" show-overflow resizable border="default" @checkbox-all="secondcheckmethod"
                @checkbox-change="secondcheckmethod" :row-config="{ height: '36', isCurrent: true, isHover: true }"
                :header-row-class-name="'height1'" :cell-class-name="'cellheight1'" :align="allAlign1" stripe
                :data="row.detailList" style="background-color:#fff">
                <vxe-column type="checkbox" width="40" fixed="left"></vxe-column>
                <vxe-column title="编号" width="60">
                  <template #default="{ row }">
                    <div>{{ row.showCode }}</div>
                  </template>
                </vxe-column>
                <!-- <vxe-column field="id" title="半成品Id" width="135" v-if="false" fixed='left'></vxe-column> -->
                <vxe-column field="finishedProductImg" title="成品图片" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.finishedProductImg"
                      :preview-src-list="[row.finishedProductImg]">
                    </el-image>
                  </template>
                </vxe-column>

                <vxe-column field="finishedProductCode" title="成品编码" width="220">
                  <template #default="{ row }">
                    <div class="relativebox">
                      <div class="textover" style="width: 100px;">{{ row.finishedProductCode }}</div>
                      <div class="copyhover" @click="copytext(row.finishedProductCode)">
                        <i class="el-icon-document-copy"></i>
                      </div>
                    </div>

                  </template>
                </vxe-column>
                <vxe-column field="finishedProductName" title="成品名称" width="350" align="left">
                  <template #default="{ row }">
                    <div class="relativeboxx">
                      <div @click="finishedproduct(row)" class="point  textover" style="width: 80%;">{{
                        row.finishedProductName }}
                      </div>
                    </div>
                  </template>
                </vxe-column>
                <vxe-column field="halfProductQuantity" title="半成品组合" width="105"></vxe-column>
                <vxe-column field="quantityRequired" title="所需半成品" width="105">
                  <template #default="{ row }">
                    <span>{{ row.quantityRequired.toFixed(4) }}</span>
                  </template>
                </vxe-column>
                <vxe-column field="machineTypeName" title="加工方式" width="105"></vxe-column>
                <vxe-column field="combinCodeQuantity" title="组合编码数" width="105" align="left">
                  <template #default="{ row }">
                    <div class="relativeboxx">
                      <div @click="finishedproduct(row)" class="point  textover" style="width: 80%;">{{
                        row.combinCodeQuantity }}
                      </div>
                    </div>
                  </template>
                </vxe-column>

                <vxe-column title="操作">
                  <template #default="{ row }">
                    <el-button type="primary" @click="showupfuc(row)">创建加工</el-button>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="halfProductCode" title="半成品编码" width="210" align="left">
          <template #default="{ row }">
            <div class="relativebox">
              <div class="textover" style="width: 205px;">{{ row.halfProductCode }}</div>
              <div class="copyhover" @click="copytext(row.halfProductCode)">
                <i class="el-icon-document-copy"></i>
              </div>
            </div>
          </template>
        </vxe-column>

        <vxe-column field="halfProductName" title="半成品名称" width="480" align="left">
          <template #default="{ row }">
            <div class="relativeboxx">
              <el-tooltip effect="dark" :content="row.halfProductName" placement="top">
                <div @click="doubleclick(row)" class="point  textover" style="width: 80%;">{{ row.halfProductName }}
                </div>
              </el-tooltip>
            </div>
          </template>
        </vxe-column>

        <!-- <vxe-column field="workPrice" title="加工工价" width="100" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessWorkPrice')">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 1)" size="mini">{{
              row.workPrice ? row.workPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="actualStock" title="主仓实际库存" width="145" align="left" sortable>
          <template #default="{ row }">
            <div>{{ row.actualStock }}</div>
          </template>
        </vxe-column>
        <vxe-column field="purchasingUnit" title="进货单位" width="145" align="left" sortable>
          <template #default="{ row }">
            <div>{{ row.purchasingUnit }}</div>
          </template>
        </vxe-column>
        <!-- <vxe-column field="allotWorkPrice" title="调拨工价" width="100">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 4)" size="mini">{{
              row.allotWorkPrice ? row.allotWorkPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="processStock" title="加工库存" width="180" align="left" sortable>
          <template #default="{ row }">
            <div>{{ row.processStock }}</div>
          </template>
        </vxe-column>

        <vxe-column field="modifiedTime" title="修改日期" width="185" align="left" sortable>
          <template #default="{ row }">
            <div>{{ row.modifiedTime }}</div>
          </template>
        </vxe-column>

        <vxe-column title="操作" align="left">
          <template #default="{ row }">
            <!-- <el-button type="primary" @click="editImg(row)">创建加工</el-button> -->
            <el-button type="primary" icon="el-icon-picture-outline" @click="editImg(row)"></el-button>
            <!-- <el-button type="danger" icon="el-icon-delete" @click="deltask(row)"></el-button> -->
          </template>
        </vxe-column>
      </vxe-table>
      <!-- <div style="font-size: 14px; color: #606266;">共：{{ tableData?.length }} 条</div> -->
    </div>

    <template #footer>
      <my-pagination :sizes="[50, 100, 300, 1000, 2000, 5000]" :page-size="1000" ref="pager" :total="total"
        :checked-count="sels.length" @page-change="pagechange" @size-change="sizechange" />
    </template>

    <el-dialog :visible.sync="innerDialog" width="750px" v-dialogDrag :close-on-click-modal="true">
      <el-form :model="ruleForm" ref="ruleForm" label-width="120px">
        <div class="bzjzcjrw">
          <div class="bt">
            <span style="float: left">创建加工</span>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">成品名称</div>
            <div class="lxwz2 formtop">
              <el-form-item prop="finishedProductName" label=" " label-width="12px">
                <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                <vxe-input :maxlength="100" disabled style="height: 28px; width: 320px; font-size: 12px;"
                  v-model="ruleForm.finishedProductName" placeholder="成品名称" clearable></vxe-input>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品编码</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <div style="width:63%">
                    <el-input :clearable="true" disabled @change="changecode" v-model="ruleForm.finishedProductCode"
                      :maxlength=100 key="cpbm">
                    </el-input>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品数量</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <el-input-number :step="1" controls-position="right" style="width:250px" :clearable="true"
                    v-model.trim="ruleForm.finishedProductQuantity" :min="1" :max="99999"
                    :precision="0"></el-input-number>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品调入仓</div>
            <div class="lxwz2">
              <el-form-item prop="finishTranWarehouse" label=" " label-width="12px">
                <el-select style="width:45%" v-model="ruleForm.finishTranWarehouse" :clearable="true"
                  :collapse-tags="true" filterable>
                  <el-option v-for="item in wareList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">计划完成日期</div>
            <div class="lxwz2">
              <el-form-item prop="pfDate" label=" " label-width="12px">
                <el-date-picker v-model="ruleForm.pfDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                  style="width:35%" placeholder="结束时间">
                </el-date-picker>
              </el-form-item>
            </div>
          </div>

          <div class="box-card">
            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;"><span style="color: #F56C6C;">*</span>半成品编码</div>
            </div>
            <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="ruleForm.detialList" header-row-class-name="bcpb">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <!-- <el-table-column prop="id" label="id" v-if="false" /> -->
                <el-table-column prop="halfProductCode" label="半成品编码" width="150" />
                <el-table-column prop="halfProductName" label="半成品名称" width="350" />
                <el-table-column prop="halfProductQuantity" label="组合数量">
                  <!-- <template slot-scope="scope">
                    <el-input-number v-model="scope.row.halfProductQuantity" :min="1" :max="10000" placeholder="数量"
                      :precision="0">
                    </el-input-number>
                  </template> -->
                </el-table-column>
                <!-- <el-table-column lable="操作">
                  <template slot-scope="scope">
                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">移除 <i class="el-icon-remove-outline"></i>
                    </el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </div>
          </div>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerDialog = false">取 消</el-button>
        <el-button type="primary" @click="onaddprocess" v-loading="btnloading" v-throttle="2000">提 交</el-button>
      </span>
    </el-dialog>

    <el-dialog title="编辑半成品图片" :visible.sync="showfinishedProductImg" width="30%" v-dialogDrag
      :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="10">
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="upFinishForm.halfProductImg" ref="supplier_id" :limit="1"
              keys="three"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showfinishedProductImg = false">取 消</el-button>
        <el-button type="primary" @click="saveImg" :loading="uploadLoading">{{ (uploadLoading ? '上传中' : '上传')
        }}</el-button>
      </span>
    </el-dialog>

    <el-drawer :visible.sync="editTaskshow" size="724px">
      <el-form :model="editoperation" ref="editoperation" label-width="100px" style="height: 100%;overflow-y: auto;">
        <div class="bzjzcjrw">
          <div class="bt">
            <span style="float: left">编辑/操作</span>
          </div>
          <div class="rwmc">
            <div class="xh" style="width: 150px">
              <el-tooltip class="item" effect="dark" :content="editoperation.halfProductCode" placement="top">
                <div style="overflow: hidden;  text-overflow: ellipsis;  white-space: nowrap;  ">
                  {{ editoperation.halfProductCode }}
                </div>
              </el-tooltip>
            </div>
            <div class="mc" style="height: 66px">|</div>
            <div class="mc" style="width: 406px; height: 60px;">
              <el-tooltip class="item" effect="dark" :content="editoperation.halfProductName" placement="top">
                <div style="overflow: hidden;  text-overflow: ellipsis;  white-space: nowrap;  ">
                  {{ editoperation.halfProductName }}
                </div>
              </el-tooltip>
            </div>
            <div class="icon" style="float: right;width: 70px;">
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </div>
          </div>

          <div class="bzccjlxpur">
            <div class="lxwz">图片</div>
            <div class="lxwz2">
              <el-form-item prop="purImageUrl" label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <yh-img-upload :value.sync="editoperation.halfProductImg" ref="supplier_id" :limit="1"></yh-img-upload>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">进货单位</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <div style="display: flex; flex-direction: row;">
                  <el-input-number style="width:250px" v-model.trim="editoperation.purchasingUnit" :controls="false"
                    @input="validateInput" :min="0" :max="99999" :precision="4"></el-input-number>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="box-card">
            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;"><span>关联成品</span></div>
            </div>
            <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="editoperation.detailList" header-row-class-name="bcpb">
                <el-table-column label="#" width="50" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="id" label="id" v-if="false" />
                <el-table-column prop="finishedProductCode" label="成品编码" width="150" />
                <el-table-column prop="finishedProductName" label="成品名称" />
              </el-table>
            </div>
          </div>
        </div>
      </el-form>
    </el-drawer>

    <el-drawer :visible.sync="combinedcodenumber" direction="btt" size="300px" close-on-click-modal append-to-body
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
      <!-- <el-from :model="combinedcode" ref="combinedcode"> -->
      <div class="bzbjbt">
        <span style="float: left">组合编码数</span>
      </div>
      <div class="box-card tablehei">
        <!-- //TODO:点开弹层没有数据   :seq-config="{ seqMethod }" :span-method="rowspanMethod"-->
        <vxe-table height="200px" resizable :key="tokey" :align="allAlign2" :scroll-y="{ enabled: false }" border
          :data="combinedcode.tableData2" show-footer :span-method="mergeRowMethod">
          <vxe-column width="0" class="seqCss"></vxe-column>
          <vxe-column field="finishedProductImg" title="成品图片" width="100" align="left">
            <template #default="{ row }">
              <el-image style="width: 50px; height: 50px" :src="row.finishedProductImg"
                :preview-src-list="[row.finishedProductImg]">
              </el-image>
            </template>
          </vxe-column>
          <vxe-column field="finishedProductCode" title="成品编码" width="155">
          </vxe-column>
          <vxe-column field="finishedProductName" title="成品名称" width="330">
          </vxe-column>
          <vxe-column field="finishedProductImg1" title="#" width="100" align="left">
            <template #default="{ row }">
              {{ combinedcode.tableData2.indexOf(row) + 1 }}
            </template>
          </vxe-column>
          <vxe-column field="halfProductImg" title="图片" width="75">
            <template #default="{ row }">
              <el-image style="width: 50px; height: 20px" :src="row.halfProductImg"
                :preview-src-list="[row.halfProductImg]">
              </el-image>
            </template>
          </vxe-column>
          <vxe-column field="halfProductCode" title="半成品编码" width="175" height="20"></vxe-column>
          <vxe-column field="halfProductName" title="半成品名称" width="445" height="20"></vxe-column>
          <vxe-column field="halfProductQuantity" title="半成品组合" width="115" height="20"></vxe-column>
          <vxe-column field="quantityRequired" title="所需半成品" width="135" height="20">
          </vxe-column>
          <vxe-column field="machineTypeName" title="加工方式" width="125">
          </vxe-column>
          <vxe-column field="age" title="操作">
            <template #default="{ row }">
              <div class="flexrow">
                <div class="flexrow">
                  <el-button @click="showupfuc(row)" type="primary">创建加工</el-button>
                </div>
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- </el-from> -->
    </el-drawer>
  </my-container>
</template>

<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import YhImgUpload from "@/components/upload/yh-img-upload1.vue";
import MyContainer from "@/components/my-container";
// import VXETable from 'vxe-table'
// import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
// import ExcelJS from 'exceljs'
// VXETable.use(VXETablePluginExportXLSX, {
//       ExcelJS
//     })

import { formatTime } from "@/utils";
import dayjs from 'dayjs'
import {
  getHalfBindFinishList,
  getAllWarehouse,
  getFinishBindHalfDetail,
  updateFinishBindHalfDetial,
  createPackagesProcessing,
  updateFinishBindHalfProductImg,
  getHalfBindFinishDetail,
  getLastUpdateStockTime,
  refreshHalfProductStock,
} from "@/api/inventory/packagesprocess";
export default {
  name: 'processingsemifinished',
  components: { MyContainer, YhImgUpload, inputYunhan },
  data() {
    return {
      combinedcode: {
        tableData2: [],
      },
      editoperation: {
        detailList: [],
        purchasingUnit: null,
        halfProductImg: null,
        halfProductCode: null,
        halfProductName: null,
      },
      filter: {
        pageSize: 100,
        currentPage: 1,
        orderBy: "",
        isAsc: true,
        finishedProductCode: '',
        finishedProductName: '',
        halfProductCode: '',
        halfProductName: '',
      },
      total: null,
      wareList: [],//成品调入仓
      sels: [], // 列表选中列
      addForm: {
        detailList: [],
        halfProductName: "",
        halfProductCode: "",
        halfProductQuantity: 0,
        createdtimerange: [],
        finishedProductName: "",
        finishedProductCode: "",
        finishedProductImg: "",
        machineTypeCode: "",
        machineTypeName: "",
        finishedProductQuantity: 0,
        pfDate: "",
        quantityRequired: 0,
        remark: "",
      },
      showfinishedProductImg: false,
      uploadLoading: false,
      userid: '',
      keys: 'four',
      tokey: 'one',
      btnloading: false,
      addLoading: true,
      pageLoading: false,
      disabled: true,
      editTaskshow: false,
      upFinishForm: {},
      tableData: [],
      allAlign1: null,
      allAlign2: null,
      typeId: null,
      finishedProductCode: "",
      pager: {},
      pickerOptions: {
        disabledDate(time) {
          // 设置禁用最小日期
          const minDate = new Date(1970, 0, 1);
          return time.getTime() < minDate.getTime();
        }
      },
      machineTypeList: [],
      innerDialog: false,
      ruleForm: {
        detialList: [],
      },
      combinedcodenumber: false,
      uploadTime: null,
      isClick: false,
      disabledTime: null,
      timer: null,
      lastSortArgs: {
        field: "",
        order: "",
      },
      diffTime: null
    };
  },

  async mounted() {
    await this.onSearch();
    await this.setWare();//获取成品调入仓
    this.getLastTime()
  },

  methods: {
    //自定义排序
    async customSortMethod({ data, sortList }) {
      if (sortList && sortList.length > 0) {
        if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
          this.lastSortArgs = { ...sortList[0] };
          let a = {
            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
            prop: this.lastSortArgs.field
          };
          this.filter.orderBy = a.prop
          this.filter.isAsc = a.order.indexOf("descending") == -1 ? true : false
          this.pageLoading = true;
          this.onSearch()
        }
      }
    },
    //成品编码
    async callbackGoodsCode(val) {
      this.filter.finishedProductCode = val;
    },
    async getLastTime() {
      const { data, success } = await getLastUpdateStockTime();
      if (success) {
        if (data == "" || data == null || data == undefined || data == '') return
        this.uploadTime = dayjs(data).format('YYYY-MM-DD HH:mm:ss');
        const nowTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
        console.log(data, '页面进来修改时间');
        const time = dayjs(nowTime).diff(dayjs(this.uploadTime), 'second');
        console.log(time, '时间间隔');
        if (time < 60) {
          this.isClick = true
          this.diffTime = 60 - time
          this.timer = setInterval(() => {
            this.$nextTick(() => {
              this.diffTime--
            })
            if (this.diffTime <= 0) {
              this.isClick = false
              clearInterval(this.timer); // 清除定时器
            }
          }, 1000);
        }
      }
    },
    async flushedTime() {
      this.diffTime = 60
      const { data, success } = await refreshHalfProductStock();
      if (success) {
        this.uploadTime = dayjs(data).format('YYYY-MM-DD HH:mm:ss');
        console.log(this.uploadTime, '修改时间');
        //获取当前时间
        const nowTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
        console.log(nowTime, '当前时间');

        //将nowTime- this.uploadTime 转换成秒
        // const time = dayjs(nowTime).diff(dayjs(this.uploadTime), 'second');
        // console.log(time, '时间间隔');

        // if (time < 60) {
        this.isClick = true
        // this.disabledTime = 60 - time
        this.timer = setInterval(() => {
          this.$nextTick(() => {
            this.diffTime--
          })
          if (this.diffTime <= 0) {
            this.isClick = false
            clearInterval(this.timer); // 清除定时器
          }
        }, 1000);
        // }
      }
    },
    // 限制输入格式不能为小数
    validateInput(value) {
      if (value.indexOf('.') >= 0) {
        this.editoperation.purchasingUnit = parseInt(value.replace(/[^\d]/g, ''), 10);
      } else {
        this.editoperation.purchasingUnit = value;
      }
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ['finishedProductImg', 'finishedProductCode', 'finishedProductName', 'machineTypeName', 'age']
      const cellValue = row[column.property]
      if (fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    //获取成品调入仓
    async setWare() {
      const { data, success } = await getAllWarehouse();
      if (!success) {
        return
      }
      this.wareList = data;
    },
    async saveImg() {
      this.uploadLoading = true;
      await updateFinishBindHalfProductImg(this.upFinishForm).then(res => {
        this.uploadLoading = false;
        if (!res?.success) {
          return
        }
        if (res?.success) {
          // this.onSearch()
          this.$message({
            message: '操作成功！',
            type: 'success'
          })
          this.onSearch();
          this.showfinishedProductImg = false;
        }
      });
    },
    async onaddprocess() {
      if (this.ruleForm.finishedProductQuantity === undefined) {
        this.$message.error('成品数量必须大于0');
        return
      }
      this.btnloading = true;
      const params = { ...this.ruleForm }
      const res = await createPackagesProcessing(params)
      if (res?.success) {
        this.$message({ message: res.data, type: "success" });
        await this.onSearch();
        this.innerDialog = false;
      }
      this.btnloading = false;
    },
    //创建加工
    async showupfuc(row) {
      const { data } = await getFinishBindHalfDetail({ pid: row.packagesProcessingId })
      this.editIndex = this.tableData.indexOf(row); // 设置当前编辑的行索引
      this.ruleForm = { ...row, detialList: data };
      this.innerDialog = true; // 打开编辑对话框
    },
    //复制内容
    copytext(e) {
      let textarea = document.createElement("textarea")
      textarea.value = e
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select()
      let result = document.execCommand("copy")
      if (result) {
        this.$message({
          message: '复制成功',
          type: 'success'
        })
      }
      textarea.remove()
    },
    onclear() {
      this.filter.finishedProductCode = '';
      this.filter.finishedProductName = '';
      this.filter.halfProductCode = null;
      this.filter.halfProductName = '';
    },
    handleExportCommand() {
      // var res = null;
      // var fileName = "包装加工-"
      // const params = { ... this.filter }
      // this.pageLoading = true;
      // var res = await exportPackagesWorkPriceData(params);
      // fileName = fileName + "半成品绑定成品"
      // this.pageLoading = false;
      // const aLink = document.createElement("a");
      // let blob = new Blob([res?.data], { type: "application/vnd.ms-excel" })
      // aLink.href = URL.createObjectURL(blob)
      // aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
      // aLink.click()
    },
    changecode(val) {
      this.addForm.finishedProductCode = val;
    },
    async onSubmit() {
      this.editoperation.purchasingUnit = parseFloat(this.editoperation.purchasingUnit);
      const params = { halfProductCode: this.editoperation.halfProductCode, halfProductImg: this.editoperation.halfProductImg, purchasingUnit: this.editoperation.purchasingUnit }
      const { success } = await updateFinishBindHalfDetial(params)
      if (success) {
        this.$message({ message: '保存成功', type: 'success' })
        await this.onSearch();
      }
    },
    async onSearch() {
      // this.$refs.pager.setPage(1);
      this.getTaskList();
    },
    //获取数据
    async getTaskList() {
      // var pager = this.$refs.pager.getPager();
      // if (this.filter.createdtimerange) {
      //   this.filter.startCreateTime = this.filter.createdtimerange[0];
      //   this.filter.endCreateTime = this.filter.createdtimerange[1];
      // } else {
      //   this.filter.startCreateTime = null;
      //   this.filter.endCreateTime = null;
      // }
      // this.filter.isComplate = this.filter.isComplateChecked == true ? 0 : 1;
      const params = {
        // ...pager,
        // ...this.pager,
        ...this.addForm,
        ...this.filter,
      };
      // debugger
      // params.platform.length>0?params.platform:params.platform=""
      // this.listLoading = true;
      // this.$refs.refpackagelist.tableloading = true;
      this.pageLoading = true;
      const res = await getHalfBindFinishList(params);
      if (!res?.success) return;
      this.tableData = res.data.list;
      this.$forceUpdate();
      // this.$refs.refpackagelist.updatelist()
      this.listLoading = false;
      this.total = res.data.total;
      this.tasklist = res.data.list;
      this.summaryarry = res.data.summary;
      this.pageLoading = false;
    },
    rowspanMethod({ row, $rowIndex, column, data }) {
      const fields = ["classTypeStr", "createUserName", "totalWorkPrice", "age"];//存储你要合并的列的 field
      const cellValue = this.$utils.get(row, column.property);//获取当前行当前列的内容
      if (cellValue && fields.includes(column.property)) {//如果 当前行当前列的内容 不为空并且是需要合并的列
        const prevRow = data[$rowIndex - 1];//获取前一行数据
        let nextRow = data[$rowIndex + 1];//获取下一行数据
        if (
          prevRow &&
          this.$utils.get(prevRow, column.property) === cellValue
        ) {//如果前一行数据不为空并且前一行当前列内容与本行本列内容一致 则合并
          return { rowspan: 0, colspan: 0 };
        } else {
          //前一行内容为空或者前一行本列内容与本行本列内容不一致
          let countRowspan = 1;
          while (
            nextRow &&
            this.$utils.get(nextRow, column.property) === cellValue
          ) {
            //当下一行内容不为空并且下一行当前列内容与本行本列内容一致时
            nextRow = data[++countRowspan + $rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    //分页
    sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.onSearch();
    },
    //分页
    pagechange(val) {
      this.filter.currentPage = val;
      this.onSearch();
    },
    //二级复选框
    secondcheckmethod(row) {
      this.printrow = row.records;
      this.userIdList = row.records.map(record => record.userId)
    },
    async finishedproduct(row) {
      const { data } = await getFinishBindHalfDetail({ pid: row.packagesProcessingId })
      this.combinedcode.tableData2 = data
      // this.combinedcode.tableData2 = data
      this.combinedcodenumber = true;
    },
    //表格复选框
    checkmethod(row) {
      this.printrow = row.records;
      this.userIdList = row.records.map(record => record.userId)
    },
    //半成品绑定成品明细
    async doubleclick(row) {
      const { data } = await getHalfBindFinishDetail({ halfProductCode: row.halfProductCode, halfProductName: row.halfProductName })
      this.editoperation = data;
      this.editTaskshow = true;
    },
    //修改半成品图片
    editImg(row) {
      // // this.editIndex = this.tableData.indexOf(row); // 设置当前编辑的行索引
      // this.innerDialog = true; // 打开编辑对话框
      // // this.ruleForm = { ...row };
      this.upFinishForm.halfProductCode = row.halfProductCode;
      this.upFinishForm.halfProductImg = row.halfProductImg;
      this.upFinishForm.typeId = 2;
      this.$nextTick(() => {
        this.showfinishedProductImg = true;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-row .custom-img-column {
  line-height: 10px;
  /* 设置行高为 60px */
}

::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}

::v-deep .vxe-table--render-default .vxe-header--column {
  line-height: 18px !important;
}

/*滚动条整体部分*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 18px !important;
  height: 26px !important;
}

/*滚动条的轨道*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #f1f1f1 !important;
}

/*滚动条里面的小方块，能向上向下移动*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #c1c1c1 !important;
  border-radius: 3px !important;
  box-sizing: border-box !important;
  border: 2px solid #F1F1F1 !important;
  box-shadow: inset 0 0 6px rgba(255, 255, 255, .5) !important;
}

// 滚动条鼠标悬停颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #A8A8A8 !important;
}

// 滚动条拖动颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878 !important;
}

/*边角，即两个滚动条的交汇处*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #dcdcdc !important;
}


// 表格内边距
::v-deep .vxe-table--render-default .vxe-cell {
  padding: 0 0 0 8px !important;
}





.flexrow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.qualibtn ::v-deep .el-button {
  margin-left: 0 !important;
  margin-top: 10px;
}

.marginrt {
  margin: 0 10px 0 auto;
}

.point:hover {
  cursor: pointer;
}

.point {
  color: #409EFF;
}


.item {
  margin-bottom: 18px;
}

.clearfix {
  font-size: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

::v-deep .box-card {
  margin-top: 20px;
  box-sizing: border-box;
  padding: 0 30px;
  height: 40%;
}

.flexcenter {
  display: flex;
  justify-content: left;
  align-items: left;
}

::v-deep .height {
  height: 58px !important;
}

::v-deep .height1 {
  height: 48px !important;
  font-size: 12px !important;
}

::v-deep .cellheight1 {
  font-size: 12px !important;
}

.relativebox {
  position: relative;
  width: 100%;
}

.relativeboxx {
  position: relative;
  width: 100%;
}

.positioncenter {
  position: absolute;
  right: 10px;
  top: 28%;
  bottom: 50%;
  // transform: translate(-50%,-50%);
}

::v-deep .droprow td {
  color: rgb(250, 9, 9);
  position: relative;
}

::v-deep .droprow ::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 0.1px;
  background-color: rgb(250, 9, 9);
  transform: translateY(-50%);
}


.copyhover {
  display: none;
  cursor: pointer;
}

.relativebox {
  width: 250px;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover {
  width: 225px;
}

.textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover {
  display: block;
  position: absolute;
  top: 50%;
  left: 80%;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409EFF;
  font-weight: 600;
}

// .copyhover:hover>.copyhover{
//   display: block;

//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%,-50%);
// }

.minibtn ::v-deep .el-button--mini {
  padding: 7px 8px;
}

.vxeclass {
  z-index: 10000 !important;
}

::v-deep .vxe-body--expanded-cell {
  padding: 0 !important;
}

.el-icon-document-copy {
  font-size: 16px;
  margin-left: 2px;
}

.el-icon-document-copy:hover {
  font-size: 16px;
  margin-left: 2px;

}

::v-deep .vxe-modal--header {
  margin-top: 8px !important;
  background-color: transparent !important;
  padding: 0 6px;

}

::v-deep .vxe-modal--header-title {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

::v-deep .vxe-modal--header-right {
  // color: transparent ;
  font-size: 12px;
  line-height: 32px;
}

::v-deep .vxe-modal--content {
  padding: 20px 35px 35px 35px;
}

::v-deep .bzbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .rwmc {
  // width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 25px;
  display: flex;
}

::v-deep .bzjzcjrw .rwmc {
  margin-bottom: 20px;
}

::v-deep .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

.tablehei {
  height: 20vh;
}

::v-deep .rwmc .mc,
::v-deep .icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-drawer .el-drawer__header {
  padding: 0 !important;
}

::v-deep .vxe-footer--row {
  height: 50px;
}

.statuscss ::v-deep .el-tag--dark {
  border-color: #fff !important;
}

.minisize ::v-deep .vxe-button {
  padding: 0 !important;
}

::v-deep .el-main {
  overflow: hidden;
}

::v-deep .bzjzcjrw {
  width: 100%;
  margin-top: 15px;
  background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
  height: 40px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  // margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
}

// 图片
::v-deep .bzjzcjrw .bzccjlxpur {
  width: 100%;
  height: 75px;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlxpur .lxwz {
  width: 20%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
}

::v-deep .bzjzcjrw .bzccjlxpur .lxwz2 {
  width: 80%;
  text-align: left;
}

::v-deep .bzjzcjrw .bzccjlx {
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
  width: 20%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
  width: 80%;
}

::v-deep .seqCss .col--seq {
  .vxe-cell {
    background-color: transparent !important;
    color: red !important;
    opacity: 0 !important;
  }
}

.publicCss {
  width: 220px;
  margin-right: 10px;
}
</style>

