<template>
    <MyContainer>
        <vxetablebase ref="table" id="20241101175614" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            :isRemoteSort="false" style="width: 100%;height: 95%;  margin: 0" :loading="loading">
        </vxetablebase>
        <div>共{{ tableData ? tableData.length + '条' : 0 + '条' }}</div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dateRange from "@/components/date-range/index.vue";
import { queryPurchaseOrderDetailAsync } from '@/api/inventory/purchaseOrderTrack'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'lastWarehousingDate', label: '入库时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inCount', label: '入库数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'preparer', label: '入库人员', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        buyNo: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList() {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await queryPurchaseOrderDetailAsync(this.buyNo)
                if (success) {
                    this.tableData = data
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                }
            } catch (error) {
                this.loading = false
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
