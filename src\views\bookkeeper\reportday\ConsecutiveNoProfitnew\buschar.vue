<template>
    <div style="height:100%;padding-left:10px;overflow: auto;border: 1px solid #eee; border-radius: 10px; height: 500px; overflow: hidden;"
        v-loading="loading">
        {{ platname }}
        <div v-if="analysisData && analysisData.series && analysisData.series != null && analysisData.series.length > 0"
            :id="'buschar' + randrom" :style="thisStyle"></div>
        <div v-else>没有可供展示的图表数据！</div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
    name: 'buschar',
    components: {},
    props: {
        isslice: false,
        chartsdata: {},
        isselplatformlr: [],
        loading: {
            type: Boolean, default: function () {
                return false;
            }
        },
        action: { type: Function, default: null },
        parms: { type: Object, default: null },

        thisStyle: {
            type: Object,
            default: function () {
                return {
                    width: '100%', height: '470px', 'box-sizing': 'border-box', 'line-height': '470px'
                }
            }
        },
        gridStyle: {
            type: Object, default: function () {
                return {
                    top: '20%',
                    left: '5%',
                    right: '4%',
                    bottom: '9%',
                    containLabel: false
                }
            }
        },
        legendChanges: { type: Function, default: null },
    },
    data() {
        return {
            that: this,
            alignleft: false,
            randrom: "",
            period: 0,
            pageLoading: false,
            listLoading: false,
            procode: '',
            platname: '',
            index: 0,
            analysisData: {},
            isDot: null,
            sellr: [],
            datatime: '',
            clicklengend: false
        }
    },
    created() {
        var e = 10;
        var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
            a = t.length,
            n = "";
        for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
        this.randrom = n;
    },
    // watch: {
    //  isselplatformlr: {
    //     handler(newVal){
    //       console.log("打印变化数据2",newVal)
    //       if(newVal.length>0){
    //        this.sellr = newVal;
    //       }
    //     },
    //     deep: true
    //   }
    // },
    async mounted() {


        // this.chartsdata.analysis.series.map((item)=>{
        //  if(item.name == "个数趋势"||item.name == "金额趋势"){

        //  }else{
        //   item.itemStyle = {
        //     color: '#fff'
        //   }
        //  }
        // });
        if (this.isslice) {
            let numarr = [];
            this.isselplatformlr.map((item) => {
                numarr.push(item.slice(0, item.indexOf('天') + 1))
            })
            // console.log(numarr,'numarr');
            // console.log(this.isselplatformlr,'this.isselplatformlr.');
            this.chartsdata.analysis.series.splice(0, 2)
            this.chartsdata.analysis.legend.splice(0, 2)


            let newseries = [];
            let newlegend = [];

            console.log(numarr,'numarr');
            numarr.forEach((value, index) => {

                // newseries = this.chartsdata.analysis.series.filter(item => item.name.indexOf(value) !== -1)
                this.chartsdata.analysis.series.map((item) => {
                    if (item.name.indexOf(value) != -1 && item.name.indexOf(value) == 0) {
                        newseries.push(item)
                    }
                })

                this.chartsdata.analysis.legend.map((item) => {
                    if (item.indexOf(value) != -1 && item.indexOf(value) == 0) {
                        newlegend.push(item)
                    }
                })
                newlegend.sort((a, b) => {
                    let numA = parseInt(a.match(/\d+/g)[0]);
                    let numB = parseInt(b.match(/\d+/g)[0]);
                    return numA - numB;
                });


            })




            this.chartsdata.analysis.series = newseries;
            this.chartsdata.analysis.legend = newlegend;


        }
        this.chartsdata.analysis.series.map((item) => {
            if (item.name.indexOf("个数") != -1) {
                item.yAxisIndex = 1;
            }
        })
        //  if(this.isslice){
        //   this.chartsdata.analysis.series = this.chartsdata.analysis.series.splice(0,2)
        //   this.chartsdata.analysis.legend = this.chartsdata.analysis.legend.splice(0,2)
        //  }

        //间隔处理
        this.chartsdata.analysis.yAxis.map((item) => {
            // item.interval = 10;
            // item.min= -220000;
            // item.max= 200;
        })


        await this.$nextTick(() => {
            this.analysisData = this.chartsdata.analysis;
            // console.log(this.analysisData,'this.analysisData2222222222222222');

            this.platname = this.chartsdata.platformName;

            this.initcharts();
        });




    },
    methods: {
        routerpush(params) {

            let that = this;
            // debugger
            const dateString = that.datatime;


            const formattedDate = dateString.replace(/-/g, '');
            params.query.yearMonthDay = dateString;



            if (!this.clicklengend) {
                if (JSON.parse(localStorage.getItem('isselplatformlr')).length > 1) {

                    this.$message.info('请只选择一个标签负利润后才能跳转平台！');
                    return
                }
            }

            if (!this.clicklengend) {
                that.$router.push({ path: params.path, query: params.query })
            }

            this.clicklengend = false;
            // this.$router.push(...params)
        },
        initcharts() {
            let that = this;

            if (!(that.analysisData && that.analysisData.series && that.analysisData.series != null && that.analysisData.series.length > 0))
                return;

            this.$nextTick(() => {
                var chartDom = document.getElementById('buschar' + that.randrom);
                var myChart = echarts.init(chartDom);
                myChart.clear();
                if (that.analysisData.series) {
                    // console.log(that.analysisData.series,'that.analysisData.series');
                    var option = that.Getoptions(that.analysisData);
                    option && myChart.setOption(option);
                }
                myChart.on('legendselectchanged', function (params) {
                    if (that.legendChanges != null) {
                        that.legendChanges(params.selected);
                    }
                });

                myChart.off('legendselectchanged');
                myChart.on('legendselectchanged', (e) => {
                    this.clicklengend = true;
                })

                myChart.on('mouseover', (params) => {
                    // this.isDot = params.event.target.;//获取鼠标停留拐点的数据
                    // this.classPass = this.subListClone[this.isDot].passing_rate[0]//雷达图拐点本班及格率
                    // this.gradePass = this.subListClone[this.isDot].passing_rate[1]//雷达图拐点年段及格率
                })
            });
        },
        randomString() {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            return n
        },
        Getoptions(element) {
            var series = []
            console.log(element, 'element');
            element.series.forEach(s => {
                series.push({ smooth: true, ...s })
            })
            let _this = this;
            var yAxis = []
            if (Array.isArray(element.yAxis)) {
                element.yAxis.forEach(s => {
                    yAxis.push({
                        type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
                            formatter: function (value) {
                                if (value >= 100000000) {
                                    value = (value / 100000000).toFixed(1) + 'Y';
                                }
                                if (value >= 10000000) {
                                    value = (value / 10000000).toFixed(1) + 'KW';
                                }
                                if (value >= 10000) {
                                    value = (value / 10000).toFixed(1) + 'W';
                                }
                                if (value >= 1000) {
                                    value = (value / 1000).toFixed(1) + 'K';
                                }
                                if (value <= -100000000) {
                                    value = (value / 100000000).toFixed(1) + 'Y';
                                }
                                if (value <= -10000000) {
                                    value = (value / 10000000).toFixed(1) + 'KW';
                                }
                                if (value <= -10000) {
                                    value = (value / 10000).toFixed(1) + 'W';
                                }
                                if (value <= -1000) {
                                    value = (value / 1000).toFixed(1) + 'K';
                                }
                                return value + s.unit;
                            }
                        }
                    })
                })
            } else {
                yAxis = { ...element.yAxis };
            }

            yAxis[1] = {
                min: 0,
                // max: 100,
                interval: function (value) {
                    if (value <= 100) {
                        return 10
                    } else {
                        return 100
                    }
                },
                axisLabel: {
                    formatter: '{value} 个'
                }

            }



            var selectedLegend = {};//{};
            if (element.selectedLegend) {
                element.legend.forEach(f => {
                    // console.log(f,'f');
                    //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
                    if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
                })
            }
            // console.log(element.legend,'element.legend');
            var option = _this.isslice ? {
                title: { text: element.title },
                // tooltip: { trigger: 'axis' },
                legend: {
                    selected: selectedLegend,
                    data: element.legend
                },
                grid: this.gridStyle,
                toolbox: {
                    show: false,
                    feature: {
                        //  magicType: { show: true, type: ['line'] },
                        //restore: {show: true},
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    padding: [30, 0, 0, 0],
                    axisPointer: {
                        type: 'shadow'
                    },
                    position: function (point, params, dom, rect, size) {
                        let obj = { left: (point[0] + 30), top: point[1] - 40 }
                        if (_this.alignleft) {
                            obj = { left: (point[0] - 300), top: point[1] - 40 };
                        }
                        return obj;
                    },

                    formatter: function (params) {
                        _this.chartsdata.datas.map((item) => {
                            const inputDate = item.yearMonthDay;
                            const formattedDate = inputDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
                            item.newdate = formattedDate;
                        })
                        var array = _this.chartsdata.datas;


                        var alldata = {};
                        _this.datatime = params[0].name;

                        var num = 0;
                        array = array.forEach((item, index) => {
                            if (item.newdate == params[0].name) {
                                alldata = item;
                                num = index;
                            }
                        })

                        if (params[0].dataIndex > _this.chartsdata.datas.length - 5) {
                            _this.alignleft = true;
                        } else {
                            _this.alignleft = false;
                        }

                        // return
                        var relVal = '';
                        _this.sellr = (JSON.parse(localStorage.getItem('isselplatformlr')) && JSON.parse(localStorage.getItem('isselplatformlr')).length > 0) ? JSON.parse(localStorage.getItem('isselplatformlr')) : [];
                        // var relVal1 = params[0].consecutiveDay3Profit6;
                        console.log(_this.sellr, '_this.sellr');
                        relVal += params[0].name + '<br/>'
                        let allday = 0;
                        let allmoney = 0;

                        // if (_this.sellr.indexOf('3天负利润') != -1) {
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(84,112,198);\"></span>" + `<span style=\"width: 300px;\">3天个数：${alldata.consecutiveDay3 || 0}</span>`
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(151,207,125);\"></span>" + '3天金额' + ": " + `<span style=\"\">${alldata.consecutiveDay3Profit6 || 0}</span>`
                        //     allday = alldata.consecutiveDay3;
                        //     allmoney = alldata.consecutiveDay3Profit6
                        // }
                        // if (_this.sellr.indexOf('5天负利润') != -1) {
                        //     relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(252,140,94);\"></span>" + `<span style=\"width: 300px;\">5天个数：${alldata.consecutiveDay5 || 0}</span>`
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(160,106,185);\"></span>" + '5天金额' + ": " + `<span style=\"\">${alldata.consecutiveDay5Profit6 || 0}</span>`
                        //     allday = allday + alldata.consecutiveDay5;
                        //     allmoney = allmoney + alldata.consecutiveDay5Profit6;
                        // }
                        // if (_this.sellr.indexOf('7天负利润') != -1) {
                        //     relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(252,140,94);\"></span>" + `<span style=\"width: 300px;\">7天个数：${alldata.consecutiveDay7 || 0}</span>`
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(160,106,185);\"></span>" + '7天金额' + ": " + `<span style=\"\">${alldata.consecutiveDay7Profit6 || 0}</span>`
                        //     allday = allday + alldata.consecutiveDay7;
                        //     allmoney = allmoney + alldata.consecutiveDay7Profit6;
                        // }
                        // if (_this.sellr.indexOf('10天负利润') != -1) {
                        //     relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(252,140,94);\"></span>" + `<span style=\"width: 300px;\">10天个数：${alldata.consecutiveDay10 || 0}</span>`
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(160,106,185);\"></span>" + '10天金额' + ": " + `<span style=\"\">${alldata.consecutiveDay10Profit6 || 0}</span>`
                        //     allday = allday + alldata.consecutiveDay10;
                        //     allmoney = allmoney + alldata.consecutiveDay10Profit6;
                        // }
                        // if (_this.sellr.indexOf('15天负利润') != -1) {
                        //     relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(140,203,228);\"></span>" + `<span style=\"width: 300px;\">15天个数：${alldata.consecutiveDay15 || 0}</span>`
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color: rgb(59,162,114);\"></span>" + '15天金额' + ": " + `<span style=\"\">${alldata.consecutiveDay15Profit6 || 0}</span>`
                        //     allday = allday + alldata.consecutiveDay15;
                        //     allmoney = allmoney + alldata.consecutiveDay15Profit6;
                        // }
                        // if (_this.sellr.indexOf('30天负利润') != -1) {
                        //     relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(250,200,89);\"></span>" + `<span style=\"width: 300px;\">30天个数：${alldata.consecutiveDay30 || 0}</span>`
                        //     relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(238,104,104);\"></span>" + '30天金额' + ": " + `<span style=\"\">${alldata.consecutiveDay30Profit6 || 0}</span>`
                        //     allday = allday + alldata.consecutiveDay30;
                        //     allmoney = allmoney + alldata.consecutiveDay30Profit6;
                        // }

                        function formatAmount(amount) {
                            if (amount < 0) {
                                return `-${formatAmount(Math.abs(amount))}`;
                            } else if (amount < 100) {
                                return amount.toFixed(2);
                            } else {
                                return amount.toLocaleString();
                            }
                        }

                            if (_this.sellr.indexOf('3天负利润') != -1) {
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(84,112,198);\"></span>" + `<span style=\"width: 300px;\">3天个数：${formatAmount(alldata.consecutiveDay3 || 0)}</span>`;
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(151,207,125);\"></span>" + '3天金额' + ": " + `<span style=\"\">${formatAmount(alldata.consecutiveDay3Profit6 || 0)}</span>`;
                                allday = alldata.consecutiveDay3;
                                allmoney = alldata.consecutiveDay3Profit6;
                            }

                            if (_this.sellr.indexOf('5天负利润') != -1) {
                                relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(252,140,94);\"></span>" + `<span style=\"width: 300px;\">5天个数：${formatAmount(alldata.consecutiveDay5 || 0)}</span>`;
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(160,106,185);\"></span>" + '5天金额' + ": " + `<span style=\"\">${formatAmount(alldata.consecutiveDay5Profit6 || 0)}</span>`;
                                allday += alldata.consecutiveDay5;
                                allmoney += alldata.consecutiveDay5Profit6;
                            }

                            if (_this.sellr.indexOf('7天负利润') != -1) {
                                relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(252,140,94);\"></span>" + `<span style=\"width: 300px;\">7天个数：${formatAmount(alldata.consecutiveDay7 || 0)}</span>`;
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(160,106,185);\"></span>" + '7天金额' + ": " + `<span style=\"\">${formatAmount(alldata.consecutiveDay7Profit6 || 0)}</span>`;
                                allday += alldata.consecutiveDay7;
                                allmoney += alldata.consecutiveDay7Profit6;
                            }

                            if (_this.sellr.indexOf('10天负利润') != -1) {
                                relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(252,140,94);\"></span>" + `<span style=\"width: 300px;\">10天个数：${formatAmount(alldata.consecutiveDay10 || 0)}</span>`;
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(160,106,185);\"></span>" + '10天金额' + ": " + `<span style=\"\">${formatAmount(alldata.consecutiveDay10Profit6 || 0)}</span>`;
                                allday += alldata.consecutiveDay10;
                                allmoney += alldata.consecutiveDay10Profit6;
                            }

                            if (_this.sellr.indexOf('15天负利润') != -1) {
                                relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(140,203,228);\"></span>" + `<span style=\"width: 300px;\">15天个数：${formatAmount(alldata.consecutiveDay15 || 0)}</span>`;
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color: rgb(59,162,114);\"></span>" + '15天金额' + ": " + `<span style=\"\">${formatAmount(alldata.consecutiveDay15Profit6 || 0)}</span>`;
                                allday += alldata.consecutiveDay15;
                                allmoney += alldata.consecutiveDay15Profit6;
                            }

                            if (_this.sellr.indexOf('30天负利润') != -1) {
                                relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(250,200,89);\"></span>" + `<span style=\"width: 300px;\">30天个数：${formatAmount(alldata.consecutiveDay30 || 0)}</span>`;
                                relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:rgb(238,104,104);\"></span>" + '30天金额' + ": " + `<span style=\"\">${formatAmount(alldata.consecutiveDay30Profit6 || 0)}</span>`;
                                allday += alldata.consecutiveDay30;
                                allmoney += alldata.consecutiveDay30Profit6;
                            }




                        //    if(_this.sellr?.length>0){
                        relVal += "<br/><span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#eee;\"></span>" + `<span style=\"width: 300px;\">总个数：${formatAmount(allday || 0)}</span>`
                        relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px; margin-left: 20px; width:10px;height:10px;background-color:#eee;\"></span>" + '总金额' + ": " + `<span style=\"\">${!allmoney ? 0 : formatAmount(allmoney)}</span>`


                        
                        //    }else{

                        //         for (var i = 0, l = params.length; i < l; i++) {
                        //         relVal += '<br/>' + '总' +  params[i].seriesName +": " + params[i].value 
                        //        }
                        //    }


                        return relVal
                    },




                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series: series,
            } : {
                title: { text: element.title },
                // tooltip: { trigger: 'axis' },
                legend: {
                    selected: selectedLegend,
                    data: element.legend
                },
                grid: this.gridStyle,
                toolbox: {
                    show: false,
                    feature: {
                        //  magicType: { show: true, type: ['line'] },
                        //restore: {show: true},
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    padding: [30, 0, 0, 0],
                    axisPointer: {
                        type: 'shadow'
                    },
                    //  position: function (point, params, dom, rect, size) {
                    //     return {left: (point[0]+30), top: point[1]-40};
                    // },

                    position: function (point, params, dom, rect, size) {
                        let obj = { left: (point[0] + 30), top: point[1] - 40 }
                        if (_this.alignleft) {
                            obj = { left: (point[0] - 150), top: point[1] - 40 };
                        }


                        return obj;
                    },
                    // formatter: function (params) {

                    //     if (params[0].dataIndex > _this.chartsdata.analysis.xAxis.length - 5) {
                    //         _this.alignleft = true;
                    //     } else {
                    //         _this.alignleft = false;
                    //     }
                    //     var relVal = '';
                    //     relVal += params[0].name + '<br/>' + "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(92,123,217);\"></span>"

                    //     for (var i = 0, l = params.length; i < l; i++) {
                    //         if (i == 1) {
                    //             relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(158,207,142);\"></span>" + params[i].seriesName + ": " + params[i].value + '<br/>'
                    //         } else {
                    //             relVal += params[i].seriesName + ": " + params[i].value + '<br/>'
                    //         }
                    //     }

                    //     return relVal
                    // }
                    formatter: function (params) {
                    // 判断数据点是否接近图表右边界
                    if (params[0].dataIndex > _this.chartsdata.analysis.xAxis.length - 5) {
                        _this.alignleft = true;
                    } else {
                        _this.alignleft = false;
                    }

                    var relVal = '';
                    relVal += params[0].name + '<br/>' + "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(92,123,217);\"></span>"

                    // 格式化数值的函数
                    function formatNumber(value) {
                        if (Math.abs(value) > 100) {
                            // 大于100，添加千位符且不显示小数点
                            return value.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
                        } else {
                            // 小于等于100，保留两位小数
                            return value.toFixed(2);
                        }
                    }

                    for (var i = 0, l = params.length; i < l; i++) {
                        if (i == 1) {
                            relVal += "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(158,207,142);\"></span>" + params[i].seriesName + ": " + formatNumber(params[i].value) + '<br/>'
                        } else {
                            relVal += params[i].seriesName + ": " + formatNumber(params[i].value) + '<br/>'
                        }
                    }

                    return relVal;
                    }

                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series: series,
            };


            return option;
        },
    }
}
</script>

