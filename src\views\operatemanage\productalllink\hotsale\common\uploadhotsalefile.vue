<template>
  <div :key="keys">
    <el-upload ref="thisupload" action="#" list-type="picture-card"
      :class="{ disabled: (fileList.length >= limit || thisDisabled) }" :on-preview="handlePictureCardPreview" :key="keys"
      :on-exceed="handleExceed" :auto-upload="true" :multiple="ismultiple" :limit="limit" :accept="accept"
      :on-remove="handleRemove" :http-request="UpSuccessload" :file-list="fileList" :disabled="thisDisabled">
      <div style="display: flex;
justify-content: center;
align-items: center;
height: 100%;
">
        <i class="el-icon-plus">
          <div v-if="(fileList && fileList != null) && !thisDisabled" style="width: 60px;font-size: 9px;">
            {{ limit - fileList.length }}
          </div>
        </i>

      </div>
      <div v-if="!isImg" slot="file" slot-scope="scope">
        <span style="font-size: 9px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  line-height: 15px;
  ">
          {{
            (scope.file.status == "success" ? "" : scope.file.status != "uploading" ? "【上传失败】" : ("【上传中" +
              scope.file.percentage.toFixed(0)
              + "%】")) + scope.file.name }}
        </span>
        <span v-if="scope.file.status == 'uploading'" :style="{
          position: 'absolute',
          top: '0px',
          left: '0px',
          width: scope.file.percentage.toFixed(0) + '%',
          height: '100%',
          backgroundColor: 'green',
          filter: 'alpha(Opacity=30)',
          '-moz-opacity': '0.3',
          opacity: '0.3'
        }">
        </span>

        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-delete" @click="handlePictureCardPreview(scope.file)">
            <i class="el-icon-download"></i>
          </span>
          <span v-if="!thisDisabled" class="el-upload-list__item-delete" @click="handleRemoveFile(scope)">
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </div>

    </el-upload>

    <div v-if="isImg" style="display:none">
      <el-image ref="imgShow" style="width: 100px; height: 100px" :src="dialogImageUrl" :preview-src-list="srcList">
      </el-image>
    </div>
  </div>
</template>

<script>
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
export default {
  name: 'YhImgUpload',
  props: {
    limit: {
      type: Number,
      default: 1
    },
    value: {
      type: Array,
      default() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    keys: {
      type: String,
      default() {
        return ""
      }
    },
    accept: {
      type: String,
      default() {
        return ".img,.jpg,.jpeg,.png,.bmp,.gif"
      }
    },
    isImg: {
      type: Boolean,
      default() {
        return true;
      }
    },
    ismultiple: {
      type: Boolean,
      default() {
        return true
      }
    },
    isrztz: {
      type: Boolean,
      default: true,
    },

  },
  data() {
    let tempDisabled = !!(this.$parent.elForm && this.$parent.elForm.disabled);

    return {
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      handleSelf: false,
      srcList: [],
      thisDisabled: this.disabled || tempDisabled,
      retdata: [],
      deldata: [],
      uploading: false,
      imgindex: 0,
      icontype: 'primary',
      imgList: [],
      showGoodsImage: false,
      IsChang: false,
      dialogVisible: false,
      videoplayerReload: false,
      heightpro: null
    }
  },
  computed: {

  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        // let tempDisabled= !!(this.$parent.elForm && this.$parent.elForm.disabled);
        // this.thisDisabled=this.disabled || tempDisabled;

        // if (newVal == null || newVal == "" || newVal == undefined) {
        //     console.log('重置状态1')  ;
        //     this.fileList = [];
        //     this.handleSelf = false;
        // }

        // if (this.handleSelf)
        //     return;


        var tempList = [];
        // if (newVal) {
        this.fileList = newVal;
        //     if (this.ismultiple) {
        //         if (newVal.indexOf('[') == 0) {
        //             tempList = JSON.parse(newVal);
        //         } else {
        //             newVal.forEach(item => {
        //             tempList.push({
        //             domain: "",
        //             fileName: "",
        //             relativePath: "",
        //             url: "",
        //             });
        //         })
        //         }
        //     } else {
        //         if (newVal.indexOf('[') == 0) {
        //         tempList = JSON.parse(newVal);
        //     } else {
        //         tempList.push({
        //         domain: "",
        //         fileName: "",
        //         relativePath: "",
        //         url: "",
        //         });
        //     }
        //     }
        //     //如果数组长度不同，或字段内容不同，赋值。
        //     if (this.fileList.length != tempList.length || (tempList.length > 0 && tempList[tempList.length - 1].url != this.fileList[this.fileList.length - 1].url)) {
        //         this.fileList = tempList;
        //     }
        // } else {
        //     if (this.fileList.length > 0)
        //         this.fileList = [];
        // }
        console.log("点击数据", this.fileList);
        this.handleSelf = false;
        console.log('重置状态1');
      },
      immediate: true
    }
  },
  mounted() {

  },
  methods: {
    //上传方法
    async UpSuccessload(item) {
      this.uploadprogressPercentage = 0;
      this.uploading = true;
      this.$emit('uploadFinish', this.uploading);
      await this.AjaxFile(item.file, 0, "");
      this.uploading = false;
      this.$emit('uploadFinish', this.uploading);

    },
    //切片上传
    async AjaxFile(file, i, batchnumber) {
      var name = file.name; //文件名
      var size = file.size; //总大小
      var shardSize = 200 * 1024;//2m
      var shardCount = Math.ceil(size / shardSize); //总片数
      if (i >= shardCount) {
        return;
      }
      //计算每一片的起始与结束位置
      var start = i * shardSize;
      var end = Math.min(size, start + shardSize);
      //构造一个表单，FormData是HTML5新增的
      i = i + 1;
      var form = new FormData();
      form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
      form.append("batchnumber", batchnumber);
      form.append("fileName", name);
      form.append("total", shardCount); //总片数
      form.append("index", i); //当前是第几片
      try {
        const res = await xMTVideoUploadBlockAsync(form);
        if (res?.success) {
          this.uploadprogressPercentage = (i / shardCount).toFixed(2) * 100;
          if (i == shardCount) {
            res.data.fileName = name;
            res.data.uid = file.uid;
            res.data.upLoadPhotoId = 0;
            console.log(res.data, 'res,data');
            this.fileList.push(res.data);
            console.log(this.fileList, 'this.fileList');
            this.$emit("update:value", this.fileList);
            // this.handleAvatarSuccess()
            // this.imglist.push(res.data.url)
            await this.$message.success('上传完成！')
          } else {
            await this.AjaxFile(file, i, res.data);
          }
        } else {
          this.$message({ message: res?.msg, type: "warning" });
        }
      } catch (error) {
        this.uploading = false;
        this.$emit('uploadFinish', this.uploading);
        this.$message.error('上传文件异常！')
        this.$refs.upload.clearFiles();
        this.fileList = [];
      }
    },
    callback() {
      console.log(this.fileList, ' this.fileList');
      return this.fileList;
    },
    handleAvatarSuccess: function (response, file, fileList) {
      console.log("222", response)
      //debugger;
      if (response && response.success && response.data.url) {

        this.handleSelf = true;

        let newValue = response.data;
        this.fileList.push(newValue);
        // let newValue1 = this.fileList;
        // this.$emit("update:value", newValue1);

        // if (this.limit == 1 && this.isImg) {
        //     let newValue = response.data;
        //     this.fileList.push(response.data);
        //     this.$emit("update:value", newValue);
        // } else {

        //     this.fileList=[...fileList];

        //     var tempList = [];


        //     this.fileList.forEach(x=>{
        //         // if(x.response && x.response.success && x.response.data &&  x.response.data.url)
        //         //     x.url=x.response.data.url;
        //         if(x.status=="success")
        //             tempList.push(...x)
        //     });

        //     console.log(299999999,this.fileList);

        //      let newValue = JSON.stringify(tempList);
        //      this.$emit("update:value", newValue);
        // }
      }
    },
    handleRemove(file, fileList) {
      this.handleSelf = true;

      this.fileList = fileList;


      // this.removeFile(file.uid);

      // let newValue = this.fileList.splice(this.fileList.indexOf(file),1)
      for (let i = 0; i < this.fileList.length; i++) {
        if (this.fileList[i].uid === file.uid) {
          this.fileList.splice(i, 1)
        }
      }
      this.$emit("update:value", this.fileList);
      // if (this.limit == 1  && this.isImg) {
      //     this.$emit("update:value", "");
      // } else {
      //     if (this.fileList == null || this.fileList.length == 0) {
      //         this.$emit("update:value", "");
      //     }
      //     else {
      //         // var tempList = [];
      //         // this.fileList.forEach(x => {
      //         //     if(!x.status || x.status=="success")
      //         //         tempList.push(...x);
      //         // });

      //         // let newValue = JSON.stringify(tempList);
      //         let newValue = this.fileList.splice(this.fileList.indexOf(row),1)
      //         this.$emit("update:value", newValue);
      //     }

      // }
    },
    handleRemoveFile(scope) {
      console.log(scope);
      console.log(this.fileList);
      console.log(this.$refs.thisupload);
      console.log(4, this.$refs.thisupload.uploadFiles);
      console.log(5, this.$refs.thisupload.fileList);

      this.handleSelf = true;


      let file = scope.file;

      //将上传中的移除
      if (file.status == "uploading") {
        let upFiles = this.$refs.thisupload.uploadFiles;
        let fileIdx = this.findFileIndexInArray(scope.file.uid, upFiles);
        if (fileIdx > -1)
          upFiles.splice(fileIdx, 1);

        return;
      }

      //将已上传的移除

      let fileList = this.$refs.thisupload.uploadFiles;

      this.removeFileInArray(file.uid, fileList);
      if (this.limit == 1 && this.isImg) {
        this.$emit("update:value", "");
      } else {
        if (fileList == null || fileList.length == 0) {
          this.$emit("update:value", "");
        }
        else {
          var tempList = [];
          fileList.forEach(x => {
            if (!x.status || x.status == "success")
              tempList.push(...x);
          });

          if (tempList == null || tempList.length == 0) {
            this.$emit("update:value", "");
          }
          else {
            let newValue = JSON.stringify(tempList);
            this.$emit("update:value", newValue);
          }
        }

      }
    },
    handlePictureCardPreview(file) {
      if (this.isImg == false) {
        window.open(file.url);
        return;
      }

      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
      this.srcList = [];
      this.fileList.forEach(x => {
        this.srcList.push(x.url);
      });

      this.$refs.imgShow.showViewer = true;

    },
    findFileIndexInArray(uid, fileList) {
      for (var i = 0; i < fileList.length; i++) {
        if (fileList[i].uid == uid) {
          return i;
        }
      }

      return -1;
    },
    removeFileInArray(uid, fileList) {
      let idx = this.findFileIndexInArray(uid, fileList);
      if (idx > -1) {
        fileList.splice(idx, 1);
      }
    },
    findFileIndex(uid) {
      for (var i = 0; i < this.fileList.length; i++) {
        if (this.fileList[i].uid == uid) {
          return i;
        }
      }

      return -1;
    },
    removeFile(uid) {

      let idx = this.findFileIndex(uid);
      if (idx > -1) {
        this.fileList.splice(idx, 1);
      }
    },
    beforeAvatarUpload(file) {
      let extIdx = file.name.lastIndexOf('.');
      if (extIdx < 1) {
        this.$message.info('不支持的文件格式！');
        return false;
      }
      let extname = file.name.substring(extIdx);
      var findExt = this.accept.split(',').find(x => x.toLowerCase() == extname.toLowerCase());
      if (!findExt) {
        this.$message.info('不支持的文件格式！');
        return false;
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 60px;
  height: 60px;
}

::v-deep .el-upload--picture-card {
  width: 60px;
  height: 60px;
  line-height: 60px;
}

::v-deep .disabled .el-upload--picture-card {
  display: none;
}
</style>

