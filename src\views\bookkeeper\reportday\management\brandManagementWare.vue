<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.brandName" placeholder="品牌" maxlength="20" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.namewareHouse" placeholder="仓库" maxlength="20" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
      </div>
    </template>
    <vxetablebase :id="'brandManagementWare1202502171715'" :tablekey="'brandManagementWare1202502171715'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onDelete(row)"><span style="color: red;">删除</span></el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="ruleTitle" :visible.sync="editDialogVisible" width="20%" v-dialogDrag>
      <div style="padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
          v-if="editDialogVisible" v-loading="editloading">
          <el-row :gutter="20">
            <el-form-item label="品牌" :label-width="'125px'" prop="brandName">
              <el-input v-model.trim="ruleForm.brandName" placeholder="请输入品牌" maxlength="50" clearable
                class="editCss" />
            </el-form-item>
          </el-row>
          <el-row :gutter="20">
            <el-form-item label="仓库" :label-width="'125px'" prop="namewareHouse">
              <el-input v-model.trim="ruleForm.namewareHouse" placeholder="请输入仓库" maxlength="50" clearable
                class="editCss" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSingleSave">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import { downloadLink } from "@/utils/tools.js";
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { GetBrandWareList, AddBrandWare, DeleteBrandWare } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '250', align: 'center', prop: 'brandName', label: '品牌', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'namewareHouse', label: '仓库', },
]
export default {
  name: "brandManagementWare",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ruleTitle: '新增',
      editDialogVisible: false,
      ruleForm: {
        brandName: '',
        namewareHouse: '',
      },
      editrules: {
        brandName: [{ required: true, message: '品牌不能为空', trigger: 'change' }],
        namewareHouse: [{ required: true, message: '仓库不能为空', trigger: 'change' }],
      },
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        brandName: '',
        namewareHouse: '',
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await DeleteBrandWare({ brandName: row.brandName, namewareHouse:row.namewareHouse })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          console.log(this.ruleForm, 'this.ruleForm');
          this.editloading = true
          const { success } = await AddBrandWare(this.ruleForm)
          this.editloading = false
          if (success) {
            this.$message.success('操作成功')
            this.editDialogVisible = false
            this.getList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.$refs.ruleForm.clearValidate();
      });
      this.ruleForm = {
        brandName: '',
        namewareHouse: '',
      }
    },
    onAddMethod() {
      this.editDialogVisible = true
      this.onCleardataMethod()
      this.ruleTitle = '新增'
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await GetBrandWareList(this.ListInfo)
      this.loading = false
      if (success) {
        this.tableData = data.list
        this.total = data.total
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 220px;
    margin-right: 5px;
  }
}

.editCss {
  width: 90%;
  margin-right: 5px;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
