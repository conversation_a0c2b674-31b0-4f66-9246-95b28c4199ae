<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.yearMonth" type="month" placeholder="选择月" value-format="yyyy-MM"
                    class="publicCss" :clearable="false" />
                <el-select v-model="ListInfo.priceChecker" clearable filterable placeholder="请选择采购员" class="publicCss">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.label" />
                </el-select>
                <el-select v-model="ListInfo.purDept" clearable filterable placeholder="请选择架构" class="publicCss">
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                <el-button type="primary" size="mini" :disabled="isDetailsExport"
                    @click="exportDetailsProps">导出明细</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :isNeedExpend="false" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <vxe-modal title="入库状态" v-model="detailsVisible" :esc-closable="true" :width='1200' :height='600'>
            <sumDetails :detailsInfo="detailsInfo" v-if="detailsVisible" />
        </vxe-modal>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/inventory/PurchaseSummary/Reduction/'
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getPurchaseDeptList } from '@/api/inventory/purchaseordernew'
import sumDetails from './sumDetails.vue'
import decimal from '@/utils/decimal'
import { pageBianMaBrand } from '@/api/inventory/warehouse'

const purchasegrouplist = [
    { value: 907444163, label: '江西南昌分公司-采购部-核价组-核价1组' },
    { value: 907484170, label: '江西南昌分公司-采购部-核价组-核价2组' }
]

export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, sumDetails
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                // yearMonth 上个月
                yearMonth: dayjs().subtract(1, 'month').format('YYYY-MM'),
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            positionList: [],
            brandlist: [],
            purchasegrouplist: purchasegrouplist,
            detailsVisible: false,
            detailsInfo: {
                yearMonth: null,
                userId: null,
                userName: null
            },
            isDetailsExport: false
        }
    },
    async mounted() {
        this.init();
        await this.getCol();
        await this.getList()
    },
    methods: {
        openDetails(row) {
            this.detailsInfo = {
                yearMonth: row.yearMonth,
                userId: row.userId
            }
            this.detailsVisible = true
        },
        async init() {
            // var res2 = await getAllProBrand();
            // this.brandlist1 = res2.data;
            // this.brandlist = res2.data.filter(item => item.leaveDate == null).map(item => {
            //     return { value: item.key, label: item.value };
            // });
            let { data, success } = await pageBianMaBrand({ pageSize: 999999, currentPage: 1, OrderBy: "", IsAsc: true, LeaveDate: '在职', enabled: true });
            this.brandlist = data.list.filter(item => item.purDept != null).map(item => { return { value: item.id, label: item.brandName }; });

            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
            //采购组
            // let { data: deptList, success } = await getPurchaseDeptList();
            // if (success) {
            //     this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            // }
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async exportDetailsProps() {
            this.isDetailsExport = true
            await request.post(`/api/inventory/PurchaseSummary/ReductionDetail/ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isDetailsExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'userName') {
                        item.type = 'click'
                        item.handle = (that, row) => that.openDetails(row)
                    }
                    if (item.prop == 'goodsApplyRate') {
                        item.formatter = (row) => {
                            return row.goodsApplyRate !== null ? decimal(row.goodsApplyRate, 100, 2, '*') + '%' : ''
                        }
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
