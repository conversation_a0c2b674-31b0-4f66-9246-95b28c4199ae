<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">
            <el-tab-pane label="产品操作日志" name="first1" style="height: 100%">
                <prooperationloglist ref="prooperationloglist" style="height: 100%"></prooperationloglist>
            </el-tab-pane>
            <el-tab-pane label="商品推广" name="ten" style="height: 100%" lazy>
                <ProductPromoTab ref="ProductPromo" style="height: 100%" />
            </el-tab-pane>
            <el-tab-pane label="全站推广" name="first" style="height: 100%" lazy>
                <qzPopularizeTab ref="qzPopularizeTab" style="height: 100%" />
            </el-tab-pane>
            <el-tab-pane label="标准推广" name="second" style="height: 100%" lazy>
                <bzPopularize ref="bzPopularize" style="height: 100%" />
            </el-tab-pane>
            <!-- <el-tab-pane label="多多搜索" name="third" style="height: 100%" lazy>
                <ddSearch ref="ddSearch" style="height: 100%" />
            </el-tab-pane> -->
            <!-- <el-tab-pane label="多多场景" name="forth" style="height: 100%" lazy>
                <ddSence ref="ddSence" style="height: 100%" />
            </el-tab-pane> -->
            <el-tab-pane label="直播推广" name="third" style="height: 100%" lazy>
                <zbPopularize ref="zbPopularize" style="height: 100%" />
            </el-tab-pane>
            <!-- <el-tab-pane label="明星店铺" name="sixth" style="height: 100%" lazy>
                <starShop ref="zbPopularize" style="height: 100%" />
            </el-tab-pane> -->
            <!-- <el-tab-pane label="整合营销" name="seventh" style="height: 100%" lazy>
                <zhMarketing ref="zhMarketing" style="height: 100%" />
            </el-tab-pane> -->
            <!-- <el-tab-pane label="活动推广" name="eighth" style="height: 100%" lazy>
                <hdPopularize ref="hdPopularize" style="height: 100%" />
            </el-tab-pane> -->
            <el-tab-pane label="账户" name="forth" style="height: 100%" lazy>
                <account ref="account" style="height: 100%" />
            </el-tab-pane>
            <el-tab-pane label="红包占比" name = "tenth" style="height: 100%" lazy>
                <redEnvelopePercentage ref="redEnvelopePercentage" style="height: 100%" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import prooperationloglist from "@/views/pddplatform/productoperationlog/prooperationloglist.vue";
import qzPopularizeTab from './qzPopularizeTab.vue'
import bzPopularize from './bzPopularize.vue'
import ddSearch from './ddSearch.vue'
import ddSence from './ddSence.vue'
import zbPopularize from './zbPopularize.vue'
import starShop from './starShop.vue'
import zhMarketing from './zhMarketing.vue'
import hdPopularize from './hdPopularize.vue'
import account from './account.vue'
import ProductPromoTab from '@/views/pddplatform/productoperationlog/ProductPromoTab.vue'
import redEnvelopePercentage from '@/views/pddplatform/productoperationlog/redEnvelopePercentage.vue'
export default {
    name: "tailorlossindex",
    components: {
        cesTable, MyContainer, MyConfirmButton, prooperationloglist, qzPopularizeTab, bzPopularize, ddSearch, ddSence, 
        zbPopularize, starShop, zhMarketing, hdPopularize, account,ProductPromoTab, redEnvelopePercentage
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    async mounted() {

    },
    methods: {
    },
};
</script>
  
<style lang="scss" scoped></style>
  