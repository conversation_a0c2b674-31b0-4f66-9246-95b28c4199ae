<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <span>日期：</span>
        <el-date-picker v-model="timeRanges" type="date" placeholder="选择日期" :value-format="'yyyy-MM-dd'"
          @change="changeTime" style="width: 150px;margin-right: 5px;" :clearable="false" :editable="false">
        </el-date-picker>
        <el-button type="primary" @click="oneClickOnVerifyMethod(1)">一键导入确认</el-button>
        <el-button type="primary" @click="oneClickOnVerifyMethod(2)">一键计算确认</el-button>


      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" @cellStyle="cellStyle"
      cellStyle style="width: 100%;  margin: 0" :loading="loading" :border="true" :height="'100%'">
      <template #importStatus="{ row }">
        <div>
          <i
            :class="row.isImportConfirmShow === true ? (row.importStatus === 1 ? 'el-icon-check' : 'el-icon-close') : ''"></i>
        </div>
      </template>
      <template #calculationStatus="{ row }">
        <div>
          <i
            :class="row.isCalculationConfirmShow === true ? (row.calculationStatus === 1 ? 'el-icon-check' : 'el-icon-close') : ''"></i>
        </div>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" v-if="row.isImportConfirmButton == true"
                @click="onVerifyMethod(row, 1)">导入确认</el-button>
              <el-button type="text" v-if="row.isCalculationConfirmButton == true"
                @click="onVerifyMethod(row, 2)">计算确认</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <!-- <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :pageSize="10"
        :sizes="[10, 20, 30, 40]" /> -->
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getKJ_DailyReportDataItemConfirmList, addKJ_DailyReportDataItemConfirm, bath_AddKJ_DailyReportDataItemConfirm } from '@/api/bookkeeper/crossBorderV2'
import { pickerOptions } from '@/utils/tools'
const tableCols = [
  { width: 'auto', align: 'left', prop: 'name', label: '数据项', },
  { width: 'auto', align: 'left', prop: 'importStatus', label: '导入确认状态', },
  { width: 'auto', align: 'left', prop: 'importUserName', label: '导入确认人', },
  { width: 'auto', align: 'left', prop: 'importTime', label: '导入确认时间', },
  { width: 'auto', align: 'left', prop: 'calculationStatus', label: '计算确认状态', },
  { width: 'auto', align: 'left', prop: 'calculationUserName', label: '计算确认人', },
  { width: 'auto', align: 'left', prop: 'calculationTime', label: '计算确认时间', },
]
export default {
  name: "dailyConfirmation",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    dailyPaperList: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        // startTime: null,//开始时间
        // endTime: null,//结束时间
        // platform: null,//平台
      },
      dailyReportType: null,
      timeRanges: null,
      tableCols,
      tableData: [],
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.ListInfo.dailyDate = this.dailyPaperList.startTime
    // this.ListInfo.endTime = this.dailyPaperList.endTime
    this.ListInfo.platform = this.dailyPaperList.platform
    this.dailyReportType = this.dailyPaperList.dailyReportType
    this.timeRanges = this.dailyPaperList.endTime
    await this.getList()
  },
  methods: {
    async oneClickOnVerifyMethod(type) {
      this.loading = true
      // 如果数组长度小于2，无法判断前 n-1 个元素，直接提示
      if (this.tableData.length < 2) {
        this.$message({ type: 'warning', message: '数据数量小于2，无法判断' });
        this.loading = false
        return
      }
      // 获取前 n-1 个元素
      const firstNMinusOne = this.tableData.slice(0, length - 1);
      // 检查前 n-1 个元素的 calculationStatus 是否全部为1
      var istrue = false
      if (type == 1) {
        istrue = firstNMinusOne.every(item => item.importStatus === 1);
      } else if (type == 2) {
        istrue = firstNMinusOne.every(item => item.calculationStatus === 1);
      }

      if (istrue) {
        // 所有条件满足，继续执行下面的逻辑
        this.$message({ type: 'warning', message: '暂无数据需要审核！！！' });
        this.loading = false
        return
      }

      this.$confirm('是否确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = {
          dailyDate: this.ListInfo.dailyDate,
          platform: this.ListInfo.platform,
        };
        if (type == 1) {
          params.importStatus = 1;
        } else if (type == 2) {
          params.calculationStatus = 1;
        }
        const res = await bath_AddKJ_DailyReportDataItemConfirm(params)
        this.loading = false
        if (res.success)
          this.$message({ type: 'success', message: '操作成功!' });

        await this.getList()
      }).catch(() => {
      });
    },
    async onVerifyMethod(row, type) {
      this.$confirm('是否确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = {
          dailyDate: this.ListInfo.dailyDate,
          platform: this.ListInfo.platform,
          name: row.name
        };
        if (type == 1 && row.importStatus == 0) {
          params.importStatus = 1;
        } else if (type == 2 && row.calculationStatus == 0) {
          params.calculationStatus = 1;
        }
        const res = await addKJ_DailyReportDataItemConfirm(params)
        if (res.success) {
          this.$message({ type: 'success', message: '操作成功!' });
          await this.getList()
        } else {
          await this.getList()
        }
      }).catch(() => {
      });
    },
    async cellStyle(row, column, callback) {

      if (row.importStatus == 1 && column.field == 'importStatus') {
        callback({ backgroundColor: '#92d04f', color: '#668f263' })
      } else if (row.importStatus == 0 && column.field == 'importStatus') {
        callback({ color: '#fe3836' })
      } else if (row.calculationStatus == 0 && column.field == 'calculationStatus') {
        callback({ color: '#fe3836' })
      } else if (row.calculationStatus == 1 && column.field == 'calculationStatus') {
        callback({ backgroundColor: '#92d04f', color: '#668f263' })
      }
    },
    async changeTime(e) {
      this.ListInfo.dailyDate = e ? e : null
      // this.ListInfo.endTime = e ? e : null
      await this.getList()
    },
    async getList() {
      this.loading = true
      const res = await getKJ_DailyReportDataItemConfirmList(this.ListInfo)
      if (res.success) {
        this.tableData = res.data
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
