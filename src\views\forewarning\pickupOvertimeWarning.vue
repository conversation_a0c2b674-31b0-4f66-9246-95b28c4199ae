<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="payTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="支付时间" end-placeholder="支付时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'zf')" />
                <el-date-picker v-model="sendTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="发货时间" end-placeholder="发货时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'fh')" />
                <el-date-picker v-model="planTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="计划发货时间" end-placeholder="计划发货时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'jh')" />
                <el-input v-model.trim="ListInfo.orderNoInner" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNoOuter" placeholder="线上订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.expressNo" placeholder="快递单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.minRemainingPickupHour" placeholder="最小揽收时间" maxlength="5" clearable
                    style="width: 120px;margin-bottom: 5px;margin-right: 5px;" />
                <el-input v-model.trim="ListInfo.maxRemainingPickupHour" placeholder="最大揽收时间" maxlength="5" clearable
                    style="width: 120px;margin-bottom: 5px;margin-right: 5px;" />
                <el-select v-model="ListInfo.orderStatuses" multiple placeholder="订单状态" class="publicCss" clearable
                    :collapse-tags="true">
                    <el-option key="已发货" label="已发货" value="已发货" />
                    <el-option key="异常" label="异常" value="异常" />
                    <el-option key="发货中" label="发货中" value="发货中" />
                    <el-option key="已支付" label="已支付" value="已支付" />
                    <el-option key="待审核" label="待审核" value="待审核" />
                </el-select>
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable filterable>
                    <el-option v-for="item in platformlist" :key="item" :label="item" :value="item" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.storeStr" v-model="ListInfo.storeStr"
                    width="200px" placeholder="店铺/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="shopCallback" title="店铺" style="margin: 0 5px 5px 0;padding: 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.sendWarehouses" multiple filterable collapse-tags placeholder="发货仓"
                    style="width: 290px;margin-right: 5px;" clearable>
                    <el-option v-for="(item, i) in wareHouseList" :key="item.wmsId + '-' + i" :label="item.wmsName"
                        :value="item.wmsName" />
                </el-select>
                <el-select v-model="ListInfo.expressCompanies" placeholder="快递公司"
                    style="width: 250px;margin-right: 5px;" clearable collapse-tags multiple filterable>
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <!-- <div style="display: flex;align-items: center;">
                    <span style="font-size: 14px;">排除订单:</span>
                    <el-select v-model="ListInfo.excludeTypes" placeholder="排除订单" class="publicCss" clearable multiple
                        :collapse-tags="true">
                        <el-option v-for="item in excludeTypesSel " :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div> -->
                <div style="display: flex;">
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="exportDisabled">导出</el-button>
                    <el-button type="primary" @click="setWareHouse">设置仓库负责人</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'pickupOvertimewarning202408040534'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @cellClick="openExpand" @sortchange='sortchange' :tableData='tableData' :tableCols="tableCols"
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading"
            :height="'100%'" :visibleMethod="() => { return false }"
            :expandConfig="{ accordion: true, trigger: 'cell', }">
            <template slot="left">
                <vxe-column title="宝贝id" width="60" type="expand" align="left" :edit-render="{}" field="expand">
                    <template #default="{ row, rowIndex }">
                        <div class="clihover" v-show="row.products">
                            <i :class="(rowIndex == isselindex && isselnow) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
                                style="font-size: 14px;"></i>
                        </div>
                    </template>
                    <template #content="{ row, rowIndex }">
                        <div v-if="neitableshow" style="height: 110px;">
                            <vxetablebase :id="'pickupOvertimewarning202408040534_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                                @sortchange='sortchange' :tableData='row.products ? JSON.parse(row.products) : []'
                                :tableCols="tableCols1" :isSelection="false" :isSelectColumn="false"
                                style="margin: 0;height: 100%;" v-loading="loading" class="already" />
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="设置仓库负责人" :visible.sync="setWareHouseVisible" width="40%" v-dialogDrag>
            <!-- <el-button type="text" @click="addProps">新增一行</el-button> -->
            <el-table ref="singleTable" :data="setWareHouseList" highlight-current-row style="width: 100%"
                max-height="300">
                <el-table-column type="index" width="50">
                </el-table-column>
                <el-table-column property="wmsId" label="仓库" width="auto">
                    <template #default="{ row, $index }">
                        <el-select v-model="row.wmsName" placeholder="发货仓" class="publicCss" clearable disabled
                            filterable @change="changeHouse($event, $index)">
                            <el-option v-for="(item, i) in wareHouseList" :key="item.wmsName + '-' + i"
                                :label="item.wmsName" :value="item.wmsName" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column property="name" label="负责人" width="auto">
                    <template #default="{ row, $index }">
                        <YhUserelector :value.sync="row.headDuid" maxlength="50" @change="getUserList($event, $index)"
                            :text.sync="row.headName" style="width:80%;" placeholder="请输入负责人">
                        </YhUserelector>
                    </template>
                </el-table-column>
                <!-- <el-table-column property="address" label="操作人">
                </el-table-column> -->
                <!-- <el-table-column property="address" label="操作">
                    <template #default="{ row, $index }">
                        <el-button type="danger" @click="setWareHouseList.splice($index, 1)">删除</el-button>
                    </template>
                </el-table-column> -->
            </el-table>
            <div class="btnGroup">
                <el-button @click="setWareHouseVisible = false" style="margin-right: 10px;">取消</el-button>
                <el-button type="primary" @click="sumbit" v-throttle="1000">保存</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import { pickerOptions } from '@/utils/tools'
import { PageGetData, GetLogisticsWmsHeads, GetColumns, GetExpressCompanies, UpdateLogisticsWmsHeads, ExportData, GetPlatforms } from '@/api/warning/LogisticsCollecting'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getCurrentUser } from '@/api/inventory/packagesprocess'
const excludeTypesSel = [
    { label: '售后订单', value: 0 },
    { label: '补差价订单', value: 1 },
    { label: '物流公司-南昌菜鸟京广 ', value: 2 },
    { label: '物流公司-包含有【代发】字样', value: 3 },
]
const tableCols1 = [
    { istrue: true, prop: 'ProCode', label: '宝贝id', width: '200', align: 'left' },
    { istrue: true, prop: 'GoodsName', label: '商品名称', align: 'left' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan, YhUserelector
    },
    data() {
        return {
            isselindex: -1,
            isselnow: false,
            neitableshow: false,
            tableCols: [],
            tableCols1,
            that: this,
            excludeTypesSel,
            platformlist: [],
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                payStartTime: null,//支付开始时间
                payEndTime: null,//支付结束时间
                sendStartTime: null,//发货开始时间
                sendEndTime: null,//发货结束时间
                planSendStartTime: null,//计划发货开始时间
                planSendEndTime: null,//计划发货结束时间
                expressCompanies: [],//快递公司
                // excludeTypes: [0, 1, 2, 3],
                isTimeOut: false,
                sendWarehouse: null,//发货仓
                sendWarehouses: [],//发货仓
                storeId: null,//店铺
                store: null,//店铺
                storeStr: null,//店铺
                stores: null,
                platform: null,//平台
                orderStatuses: null,//订单状态
                remainingPickupHour: null,//剩余揽收时间
                expressNo: null,//快递单号
                orderNoOuter: null,//线上订单号
                orderNoInner: null,//内部订单号
                minRemainingPickupHour: null,//最小揽收时间
                maxRemainingPickupHour: null,//最大揽收时间
            },
            payTimeRanges: [],
            sendTimeRanges: [],
            planTimeRanges: [],
            total: 0,
            loading: false,
            pickerOptions,
            tableData: [],
            wareHouseList: [],
            kdCompany: [],
            setWareHouseVisible: false,
            setWareHouseList: [
                {
                    wmsId: null,//仓库id
                    wmsName: null,//仓库名称
                    headUid: null,//负责人id
                    headDuid: null,//负责人钉钉id
                    headName: null //负责人名称
                }
            ],
            userName: null,
            exportDisabled: false
        }
    },
    async mounted() {
        await this.getClos()
        await this.getList()
        await this.getWareHouse()
        await this.getKdCompany()
        await this.getUser()
        await this.getPlatForm()
    },
    methods: {
        async getPlatForm() {
            const { data, success } = await GetPlatforms()
            if (success) {
                this.platformlist = data
            }
        },
        async openExpand({ row, rowIndex }) {
            this.neitableshow = false
            if (!row.products) return
            let _this = this;
            if (rowIndex == _this.isselindex) {
                this.isselnow = !this.isselnow;
            } else {
                this.isselnow = true
            }
            _this.isselindex = rowIndex;
            this.neitableshow = true
        },
        async sumbit() {
            const { success } = await UpdateLogisticsWmsHeads(this.setWareHouseList)
            if (success) {
                this.setWareHouseVisible = false
                this.$message.success('设置成功')
            } else {
                this.$message.error('设置失败')
            }
        },
        getUserList(val, i) {
            this.setWareHouseList[i].headUid = val ? val[0].extData.userId : null
            this.setWareHouseList[i].headDuid = val ? val[0].value : null
            this.setWareHouseList[i].headName = val ? val[0].label : null
        },
        changeHouse(e, i) {
            //根据e找出名字 wmsName
            this.setWareHouseList[i].wmsId = e ? this.wareHouseList.find(item => item.wmsName == e).wmsId : null
            console.log(this.setWareHouseList[i], 'this.setWareHouseList[i].wmsName');
        },
        async exportProps() {
            this.exportDisabled = true
            const { data } = await ExportData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '揽收超时预警' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
            this.exportDisabled = false
        },
        addProps() {
            this.setWareHouseList.push({
                wmsId: null,//仓库id
                wmsName: null,//仓库名称
                headUid: null,//负责人id
                headDuid: null,//负责人钉钉id
                headName: null //负责人名称
            })
        },
        async getUser() {
            const { data, success } = await getCurrentUser()
            if (success) {
                this.userName = data.userName
            }
        },
        async setWareHouse() {
            this.setWareHouseList = []
            const { data, success } = await GetLogisticsWmsHeads()
            if (success) {
                this.setWareHouseList = data
                this.setWareHouseVisible = true
            }
        },
        async getKdCompany() {
            const { data, success } = await GetExpressCompanies()
            if (success) {
                this.kdCompany = data
            }
        },
        async getClos() {
            const { data, success } = await GetColumns()
            if (success) {
                data.forEach(item => {
                    item.width = '120'
                    if (item.label == '内部订单号') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                        item.align = 'left'
                    }
                    if (item.label == '发货时间') {
                        item.width = '150'
                    }
                    if (item.label == '计划发货时间') {
                        item.width = '150'
                    }
                    // if (item.prop == 'remainingPickupRealTimeStr') {
                    //     item.prop = item.sortBy
                    //     item.formatter = (row) => row.remainingPickupRealTimeStr
                    // }
                })
                this.tableCols = data
            }
        },
        shopCallback(val) {
            this.ListInfo.storeStr = val
            this.ListInfo.stores = this.ListInfo.storeStr ? this.ListInfo.storeStr.split(',') : []
        },
        async getWareHouse() {
            const { data, success } = await GetLogisticsWmsHeads()
            if (success) {
                this.wareHouseList = data
            }
        },
        async changeTime(e, type) {
            //如果type为支付时间,并且有时间范围,就赋值,否则就清空,使用三元
            if (type == 'zf') {
                this.ListInfo.payStartTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
                this.ListInfo.payEndTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            } else if (type == 'fh') {
                this.ListInfo.sendStartTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
                this.ListInfo.sendEndTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            } else if (type == 'jh') {
                this.ListInfo.planSendStartTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
                this.ListInfo.planSendEndTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            }
            this.getList()
        },
        async getList(type) {
            this.isselindex = -1
            //如果最大揽收时间小于最小揽收时间,就提示
            if (this.ListInfo.minRemainingPickupHour && this.ListInfo.maxRemainingPickupHour && this.ListInfo.minRemainingPickupHour > this.ListInfo.maxRemainingPickupHour) {
                this.$message.error('最大揽收时间不能小于最小揽收时间')
                return
            }
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            // const replaceArr = ['orderNo', 'orderNoInner'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            // this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await PageGetData(this.ListInfo)
            if (success) {
                this.tableData = list
                this.loading = false
                this.total = total
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                if (prop == 'remainingPickupRealTimeStr') {
                    this.ListInfo.orderBy = 'RemainingPickupRealTime'
                } else {
                    this.ListInfo.orderBy = prop
                }
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.clihover:hover {
    cursor: pointer;
}

.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
    align-items: center;

    .publicCss {
        width: 190px;
        margin: 0 5px 5px 0;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.already ::v-deep .vxe-tools--operate {
    display: none !important;
}

.dropdownCss {
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    height: 28px;
    width: 250px;
    color: #dcdfe6;
    box-sizing: border-box;
    line-height: 28px;
    margin-bottom: 5px;
    font-size: 12px;
    padding-left: 10px;
}
</style>
