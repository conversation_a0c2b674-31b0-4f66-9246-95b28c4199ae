<template>
    <my-container>
        <!--顶部操作-->
        <template #header>
            <div class=".top">
                <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                    <el-form-item>
                        <el-switch v-model="Filter.switchshow" inactive-text="售前数据" active-text="售后数据"
                            @change="changeShowgroup">
                        </el-switch>
                    </el-form-item>

                    <el-form-item label="">
                        <el-date-picker style="width: 320px" v-model="Filter.conversationTime" type="datetimerange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="会话开始日期"
                            end-placeholder="会话结束日期" :picker-options="pickerOptions"
                            :default-value="defaultDate"></el-date-picker>
                    </el-form-item>

                    <el-form-item label="">
                        <el-date-picker style="width: 320px" v-model="Filter.lastOperatorTime" type="datetimerange"
                            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="审核开始日期"
                            end-placeholder="审核结束日期" :picker-options="pickerOptions"
                            :default-value="defaultDate"></el-date-picker>
                    </el-form-item>

                    <el-form-item label="">
                        <el-button-group>
                        <!-- <el-input maxlength="50" v-model.trim="Filter.orderNo" placeholder="线上订单号" clearable /> -->
                        <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref=""
                            :inputt.sync="Filter.orderNo" v-model.trim="Filter.orderNo" placeholder="线上订单号"
                            :clearable="true" @callback="callback" title="线上订单号" @entersearch="entersearch">
                        </inputYunhan>
                        </el-button-group>

                    </el-form-item>
                    <el-form-item label="">
                        <!-- <el-input maxlength="20" v-model.trim="Filter.proId" placeholder="宝贝ID" style="width:120px"
                            clearable /> -->
                            <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref=""
                            :inputt.sync="Filter.proId" v-model.trim="Filter.proId" placeholder="宝贝ID"
                            :clearable="true" @callback="callbackProId" title="宝贝ID" @entersearch="entersearch">
                        </inputYunhan>
                    </el-form-item>
                    <el-form-item label="" prop="refuseAuditTypeList" v-if="!Filter.switchshow">
                        <el-select v-model="Filter.refuseAuditTypeList" placeholder="评判类型" class="el-select-content"
                            clearable filterable multiple collapse-tags>
                            <el-option v-for="item in PreStatusList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>


                    <el-form-item label="" prop="refuseAuditTypeList" v-if="Filter.switchshow">
                        <el-select v-model="Filter.refuseAuditTypeList" placeholder="评判类型" class="el-select-content"
                            clearable filterable multiple collapse-tags>
                            <el-option v-for="item in AfterStatusList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>


                    <!-- <el-form-item label="" prop="newMemberName">
                        <template>
                            <el-select v-model="Filter.responsibleName" placeholder="责任客服" :remote-method="remoteMethod"
                                remote clearable filterable maxlength="20">
                                <el-option v-for="item in customerlist" :key="item.ddUserId" :label="item.label"
                                    :value="item.userName" />
                            </el-select>
                        </template>
                    </el-form-item> -->
                    <el-form-item label="">
                        <el-input v-model.trim="Filter.responsibleName" placeholder="责任客服" style="width:120px"
                            maxlength="20" clearable />
                    </el-form-item>

                    <el-form-item label="" prop="">
                        <el-select v-model="Filter.appealStatusList" placeholder="状态" class="el-select-content"
                            clearable filterable multiple collapse-tags>
                            <el-option v-for="item in appealTypeList" :key="item.value" :label="item.name"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>


                    <el-form-item >
                    <el-select v-model="Filter.platform" placeholder="平台" style="width:120px" class="el-select-content" clearable
                               @change="changePlatform">
                      <el-option v-for="item in platformList" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                  </el-form-item>


                  <el-form-item label="">
                        <el-select v-model="Filter.groupNameList" placeholder="分组" class="el-select-content" filterable
                            multiple clearable collapse-tags  @focus="changPlatformState">
                            <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>

                  <el-form-item >
                    <el-select v-model="Filter.groupManagerList" placeholder="组长"  class="el-select-content" multiple collapse-tags
                               clearable filterable @focus="changPlatformState">
                      <el-option v-for="item in groupManagerList" :key="item" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>

                    <!-- <el-form-item label="">
                        <el-select v-model="Filter.groupManagerList" placeholder="组长" class="el-select-content"
                            filterable multiple clearable collapse-tags>
                            <el-option v-for="item in groupManagers" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item> -->
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">查询</el-button>
                        <el-button type="primary" @click="onExport()" v-if="Filter.switchshow ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSalesExport']) : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewExport'])">导出</el-button>
                        <el-button type="primary" @click="BatchApproval()"  v-if="Filter.switchshow ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales']) : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReview'])">批量通过</el-button>
                        <el-button type="primary" @click="BatchRejection()"  v-if="Filter.switchshow ? checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales']) : checkPermission(['api:customerservice:UnPayOrderAppeal:AppealReview'])">批量拒绝</el-button>

                    </el-form-item>
                </el-form>
            </div>
        </template>


        <vxetablebase :id="'devApproval202408160425'" v-if="tableshow" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false" @checCheckboxkMethod="checCheckboxkMethod" @select="checkboxRangeEnd"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="true"    :isDisableCheckBox="true"
            :tree-config="{}" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="listLoading" :showsummary="true" :summaryarry="summaryarry"
            :height="'100%'">
            <el-table-column type="expand">
                <!-- <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template> -->
            </el-table-column>
        </vxetablebase>


        <!-- 列表 -->
        <!-- <Ces-table ref="table" :that="that" :isIndex="true" :hasexpand='false' @sortchange="sortchange"
            :tableData="tableData" :showsummary="true" :summaryarry="summaryarry" @select='selectchange'
            :isSelection='true' :tableCols="tableCols" :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
        </Ces-table> -->

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getPageList" />
        </template>

        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="历史" v-if="viewChatDialogVisible" :visible.sync="viewChatDialogVisible" width="50%"
            height="600px" v-dialogDrag append-to-body>
            <ViewChatDialogueDialog ref="ViewChatDialogueDialog" :HistoryROW="HistoryROW"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <!-- 订单日志信息 -->
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag append-to-body>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <!-- 聊天记录 -->
        <AppealChartListDialog :v-if="resourcedialogVisible" :totalROW="totalROW"  :isShow="resourcedialogVisible"
            @closeDialog="resourcedialogVisible = false" ref="chartRef">
        </AppealChartListDialog>

        <AppealDialog :v-if="appealdialogVisible" :isShow="appealdialogVisible"
            @closeDialog="appealdialogVisible = false" ref="appealRef">
        </AppealDialog>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import {
    getAppealReviewPageList, getAppealReviewAfterSalesPageList, appealReviewExport, appealReviewAfterSalesExport, /*getGroupNameList, getGroupManagerList,*/ appealReview, appealReviewAfterSales
} from "@/api/customerservice/chartAppeal";

import ViewChatDialogueDialog from "@/views/customerservice/chartCheck/SalesDialog/ViewChatDialogueDialog"
import { formatLinkProCode } from "@/utils/tools";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import { formatTime } from "@/utils";
import AppealChartListDialog from "@/views/customerservice/chartCheckAppeal/SalesDialog/AppealChartListDialog.vue";
import AppealDialog from "@/views/customerservice/chartCheckAppeal/SalesDialog/AppealDialog.vue";
import { queryAllCustomerServiceDDUserTop100 } from "@/api/admin/deptuser";
// import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import {
  getAfterSalesOrderList,
  getUnPayOrderById,
  getUnpayOrderList,
  getGroupManagerList,
  getGruopNameList,
  GetUnpayOrderSalesList
} from "@/api/customerservice/chartCheck";

const appealTypeList = [
    { name: "申诉中", value: 1 },
    { name: "已通过", value: 2 },
    { name: "不通过", value: 3 }
];
//店铺
const platformList = [
    { name: "拼多多", value: 2 },
    { name: "天猫", value: 1 },
    { name: "淘宝", value: 9 },
    { name: "抖音", value: 6 }
];
const PreList = ["回复问题", "专业能力", "敷衍怠慢", "存在违规/过度承诺","存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦"];//全部
const AfterList = ["回复问题", "流程问题", "专业能力", "敷衍怠慢", "存在违规/过度承诺","存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦", "小额打款异常"];//全部

// ["回复问题", "流程问题", "敷衍怠慢", "专业能力", "存在违规/过度承诺", "存在违规/引导线下交易", "存在违规/其他违规", "态度问题/辱骂客户", "态度问题/怒怼客户", "态度问题/反问客户", "态度问题/不耐烦"];//全部
//售前数据
const tableCols0 = [
{ istrue: true, label: '', type: "checkbox" },
{ istrue: true, prop: 'conversationId', label: '数据编码', sortable: 'custom',width:'250' },
    {
        istrue: true,
        prop: "orderNo",
        label: "线上订单号",
        sortable: "custom",
        type: 'click',
        handle: (that, row) => that.showLogDetail(row)
    },
    {
        istrue: true,
        display: true,
        prop: "proId",
        label: "宝贝ID",
        sortable: "custom",
        type: "html",
        formatter: (row) => formatLinkProCode(row.platform, row.proId),
    },
    {
        istrue: true,
        prop: "conversationTime",
        label: "会话时间",
        sortable: "custom",
        formatter: (row) => {
            return row.conversationTime ? formatTime(row.conversationTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "platform",
        label: "平台",
        width: "60",
        sortable: "custom",
        formatter: (row) => {
            return platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'chatAccount', label: '聊天账号', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'oldResponsibleName', label: '原责任客服', sortable: 'custom' },
    { istrue: true, prop: 'responsibleName', label: '责任客服', sortable: 'custom' },
    { istrue: true, prop: 'reviewedBy', label: '审核稽查', sortable: 'custom' },
    { istrue: true, prop: 'groupName', label: '分组', sortable: 'custom' },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'appealStatus', label: '状态', type: 'clickLink', treeNode: true,
        style: (that, row) => that.renderStatus(row.appealStatus),
        formatter: (row) => {
            return appealTypeList.filter(item => item.value == row.appealStatus)[0]?.name
        },
    },

    { istrue: true, prop: 'refuseAuditType', label: '评判类型', sortable: 'custom' },
    { istrue: true, prop: 'lastOperator', label: '最后审核人', sortable: 'custom' },
    {
        istrue: true,
        prop: "lastOperatorTime",
        label: "最后审核时间",
        sortable: "custom",
        formatter: (row) => {
            return row.lastOperatorTime ? formatTime(row.lastOperatorTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        type: "button",
        label: "操作",
        width: "150",
        align: "center",
        fixed: "right",
        btnList: [
            { label: "查看", handle: (that, row) => that.showDetail(row) },
            { label: "同意", display: (row) => { return row.appealStatus==1 ? false : true; }, handle: (that, row) => that.appealPreResultLog(row,2),permission: "api:customerservice:UnPayOrderAppeal:AppealReview"},
            { label: "拒绝", display: (row) => { return row.appealStatus==1 ? false : true; }, handle: (that, row) => that.appealPreResultLog(row,3),permission: "api:customerservice:UnPayOrderAppeal:AppealReview"},
        ],
    },
];
const tableCols1 = [
{ istrue: true, label: '', type: "checkbox" },
    {
        istrue: true,
        prop: "orderNo",
        label: "线上订单号后",
        sortable: "custom",
        type: 'click',
        handle: (that, row) => that.showLogDetail(row)
    },
    {
        istrue: true,
        display: true,
        prop: "proId",
        label: "宝贝ID",
        sortable: "custom",
        type: "html",
        formatter: (row) => formatLinkProCode(row.platform, row.proId),
    },
    {
        istrue: true,
        prop: "conversationTime",
        label: "会话时间",
        sortable: "custom",
        formatter: (row) => {
            return row.conversationTime ? formatTime(row.conversationTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        prop: "platform",
        label: "平台",
        width: "60",
        sortable: "custom",
        formatter: (row) => {
            return platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom' },
    { istrue: true, prop: 'chatAccount', label: '聊天账号', sortable: 'custom' },
    { istrue: true, prop: 'groupManager', label: '组长', sortable: 'custom' },
    { istrue: true, prop: 'oldResponsibleName', label: '原责任客服', sortable: 'custom' },
    { istrue: true, prop: 'responsibleName', label: '责任客服', sortable: 'custom' },
    { istrue: true, prop: 'reviewedBy', label: '审核稽查', sortable: 'custom' },
    { istrue: true, prop: 'groupName', label: '分组', sortable: 'custom' },
    {
        sortable: 'custom', width: 'auto', align: 'center', prop: 'appealStatus', label: '状态', type: 'clickLink', treeNode: true,
        style: (that, row) => that.renderStatus(row.appealStatus),
        formatter: (row) => {
            return appealTypeList.filter(item => item.value == row.appealStatus)[0]?.name
        },
    },

    { istrue: true, prop: 'refuseAuditType', label: '评判类型', sortable: 'custom' },
    { istrue: true, prop: 'lastOperator', label: '最后审核人', sortable: 'custom' },
    {
        istrue: true,
        prop: "lastOperatorTime",
        label: "最后审核时间",
        sortable: "custom",
        formatter: (row) => {
            return row.lastOperatorTime ? formatTime(row.lastOperatorTime, "YYYY-MM-DD") : "";
        },
    },
    {
        istrue: true,
        type: "button",
        label: "操作",
        width: "150",
        align: "center",
        fixed: "right",
        btnList: [
            { label: "查看", handle: (that, row) => that.showDetail(row) },
            { label: "同意", display: (row) => { return row.appealStatus==1 ? false : true; }, handle: (that, row) => that.appealAfterResultLog(row,2),permission: "api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales" },
            { label: "拒绝", display: (row) => { return row.appealStatus==1 ? false : true; }, handle: (that, row) => that.appealAfterResultLog(row,3),permission: "api:customerservice:UnPayOrderAppeal:AppealReviewAfterSales" },
        ],
    },
];
export default {
    name: "salesFifth",
    components: { MyContainer, CesTable, buschar, datepicker, ViewChatDialogueDialog, OrderActionsByInnerNos, AppealChartListDialog, AppealDialog,vxetablebase, inputYunhan },
    data() {
        return {
            that: this,
            tableshow:  true,
            Filter: {
                orderNo: '',
                proId: '',
                switchshow:false,
                platform:null,
                groupNameList:[],
            },
            userNameList: [],
            initialList: [],
            groupNameList: [],
            groupManagerList: [],
            pickerOptions: {
                disabledDate(date) {
                    // 设置禁用日期
                    const start = new Date("1970/1/1");
                    const end = new Date("9999/12/31");
                    return date < start || date > end;
                },
            },
            defaultDate: new Date(),
            tableData: [],
            summaryarry: null,
            listLoading: false,
            total: 0,
            nameList: [],
            groupName: [],
            platformList: platformList,
            appealTypeList: appealTypeList,
            userNameLists: [],
            groupNameLists: [],
            dialogMapVisible: { visible: false, title: "", data: [] },
            tableCols: [],
            showHistoryDialogVisibleSyj: false,
            viewChatDialogVisible: false,
            HistoryROW: {},
            pager: {
                orderBy: "",
                isAsc: false
            },
            PreStatusList: PreList,
            AfterStatusList: AfterList,
            dialogHisVisible: false,
            resourcedialogVisible: false,
            appealdialogVisible: false,
            appealStatusList: [],
            refuseAuditTypeList: [],
            customerlist: [],
            orgOptions: [],
            groupNames: [],
            groupManagers: [],
            groupManagerList:[],
            selids: [],
            orderNo: '',
            proId: '',

        };
    },
    async mounted() {
        this.orgOptions = [...this.customerlist];
        await this.onSearch();
    },
    methods: {
        onSearch() {
            this.getPageList();
            this.getGroupList();
        },

        checCheckboxkMethod(row, callback) {
            console.log(row.status, 'row.status');
            let isNotStock = row.appealStatus == 1
            callback(isNotStock)
        },
        async entersearch(val) {
            // this.filter.indexNo = val;
            this.onSearch();
        },
        async callback(val) {
            this.Filter.orderNo = val;
            //this.onSearch();
        },
        async callbackProId(val) {
            this.Filter.proId = val;
            //this.onSearch();
        },

        renderStatus(appealStatus) {
            const map = {
                '0': "background-color: #54bcbd;color: white;padding: 10px;",
                '1': "background-color: #409dfe;color: white;padding: 10px;",
                '2': "background-color: #81b337;color: white;padding: 10px;",
                '3': "background-color: #ff0000;color: white;padding: 10px;",
                '4': "background-color: #e99d42;color: white;padding: 10px;"
            }
            return map[appealStatus]
        },
        changPlatformState(val) {
          if (!this.Filter.platform) {
            this.$message({ message: "请先选择平台！", type: "warning" });
            return false;
          }
        },
        async changePlatform(val) {
          this.groupManagerList = [];
          this.groupNameList = [];
          this.Filter.groupManagerList = null
          this.Filter.groupNameList = null

          if (val) {
            const groupManagerList = await getGroupManagerList({ platform: val});
            const gruopName = await getGruopNameList({ platform: val});
            this.groupManagerList = groupManagerList.data;
            this.groupNameList = gruopName.data;
          }
        },
        // async getGroupList() {

        //     const groupName = await getGroupNameList({  }) /*this.Filter.switchshow ? await getGroupNameList({ salesType: 1 }) : await getGroupNameList({ salesType: 0 })*/;
        //     this.groupNames = groupName.data;


        //     const manager =  await getGroupManagerList({ }) /*this.Filter.switchshow ? await getGroupManagerList({ salesType: 1 }) : await getGroupManagerList({ salesType: 0 })*/;
        //     this.groupManagers = manager.data;
        // },

        showLogDetail(row) {
            this.dialogHisVisible = true;
            this.orderNo = row.orderNo;
        },
        async getPageList() {
            // this.tableCols = tableCols


            var newArr = this.Filter.switchshow ? tableCols1 : tableCols0
            console.log("newArr",newArr)
            this.tableCols = newArr


            const para = { ...this.Filter };
            if (this.Filter.conversationTime) {
                para.conversationTimeStart = this.Filter.conversationTime[0];
                para.conversationTimeEnd = this.Filter.conversationTime[1];
            }
            if (this.Filter.lastOperatorTime) {
                para.lastOperatorTimeStart = this.Filter.lastOperatorTime[0];
                para.lastOperatorTimeEnd = this.Filter.lastOperatorTime[1];
            }
            para.salesType = this.Filter.switchshow ? 1 : 0
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            this.listLoading = true;

            const res = this.Filter.switchshow ? await getAppealReviewAfterSalesPageList(params) : await getAppealReviewPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
            this.summaryarry = res.data.summary;
            this.tableshow = true;
        },
        changeShowgroup() {
            console.log("2323",this.Filter.switchshow)
            // this.Filter.timerange = [];
            this.tableshow = false;
            this.Filter.groupNameList = "";
            // this.Filter.userNameList = "";
            // this.groupNameLists = [];
            // this.userNameLists = [];
            this.onSearch();
        },
        sortchange(column) {
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                    orderBy: column.prop,
                    isAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
                this.onSearch();
            }
        },
        async showDetail(row) { //查看聊天记录
        //   var params = {
        //     "conversationId": row.conversationId
        //   }
          if (!this.Filter.switchshow) {
            //售前数据获取
            const res = await getUnPayOrderById({id:row.conversationId});
            if (res.success) {
              const thisData = res.data;
              console.log("thisData",thisData);
              if (thisData) {
                //将审核类型,责任人,责任客服等等放入
                row.initialAuditType = thisData.initialAuditType;
                row.refuseInitialAuditType = thisData.refuseInitialAuditType;
                row.initialResponsibleName = thisData.initialResponsibleName ? thisData.initialResponsibleName : thisData.responsibleName;
                row.initialAuditRemark = thisData.initialAuditRemark;
                row.initialOperator = thisData.initialOperator;
                row.initialAuditImgs = thisData.initialAuditImgs;
              }
            }
          } else {
            //售后数据获取
            const res = await getUnPayOrderById({id:row.conversationId});
            if (res.success) {
              const thisData = res.data;
              console.log("thisData",thisData);
              if (thisData) {
                //将审核类型,责任人,责任客服等等放入
                row.initialAuditType = thisData.initialAuditType;
                row.refuseInitialAuditType = thisData.refuseInitialAuditType;
                row.initialResponsibleName = thisData.initialResponsibleName ? thisData.initialResponsibleName : thisData.responsibleName;
                row.initialAuditRemark = thisData.initialAuditRemark;
                row.initialOperator = thisData.initialOperator;
                row.initialAuditImgs = thisData.initialAuditImgs;
                row.person = thisData.person;
                row.reasonForRefund = thisData.reasonForRefund;
              }
            }
          }
          if (row.conversationUserId) {
            var params = {
              conversationUserId: row.conversationUserId,
              conversationId: row.conversationId,
              salesType: 0,
              shopId: row.shopId,
              auditState:2,
              orderBy: "createdTime",
              isAsc: false
            }
            const res = await GetUnpayOrderSalesList(params);
            if (!res?.success) {
              return;
            }
            this.$refs.chartRef.reviewList = res.data.list;
          } else {
            this.$refs.chartRef.reviewList = [];
          }
          this.$refs.chartRef.keyWord = row.conversationId
          this.$refs.chartRef.platform = row.platform
          this.$refs.chartRef.dataJson = row;
          this.$refs.chartRef.tableData = this.tableData;
          const totalROW = {
            switchshow: this.Filter.switchshow,
          }
          this.totalROW = totalROW;
          this.resourcedialogVisible = true;
        },
        async onExport() {  //导出
            const para = { ...this.Filter };
            if (this.Filter.conversationTime) {
                para.conversationTimeStart = this.Filter.conversationTime[0];
                para.conversationTimeEnd = this.Filter.conversationTime[1];
            }
            if (this.Filter.lastOperatorTime) {
                para.lastOperatorTimeStart = this.Filter.lastOperatorTime[0];
                para.lastOperatorTimeEnd = this.Filter.lastOperatorTime[1];
            }


            para.salesType = this.Filter.switchshow ? 1 : 0
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            var res = this.Filter.switchshow ? await appealReviewAfterSalesExport(params) : await appealReviewExport(params);
            const aLink = document.createElement("a");
            let blob = new Blob([res], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);

            var excelName = this.Filter.switchshow ? '售后数据' : '售前数据'
            aLink.setAttribute(
                "download",
                excelName + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        // async remoteMethod(query) {
        //     if (query && query.length > 50) return this.$message.error("输入内容过长");
        //     if (query !== '') {
        //         var res = await queryAllCustomerServiceDDUserTop100({ keywords: query });
        //         if (res && res.success) {
        //             this.customerlist = res.data?.map(item => {
        //                 return {
        //                     label: [
        //                         item.userName ? item.userName : '',
        //                         item.position ? `(${item.position}` : '',
        //                         item.empStatusText ? `, ${item.empStatusText})` : '',
        //                         item.deptName ? ` ${item.deptName}` : ''
        //                     ].filter(Boolean).join(''),
        //                     value: item.ddUserId || null,
        //                     userName: item.userName,
        //                     extData: item
        //                 }
        //             });
        //         }
        //     } else {
        //         this.customerlist = [...this.orgOptions];
        //     }
        // },
        async appealPreResultLog(row,num) {
            const params = {
                reviewType: num,
                conversationIdList: [row.conversationId]
            }

            var res = await appealReview(params);
            this.getPageList();
        },

        async appealAfterResultLog(row,num) {
            const params = {
                reviewType: num,
                conversationIdList: [row.conversationId]
            }
            var res = await appealReviewAfterSales(params);
            this.getPageList();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.conversationId);
            })
        },
        checkboxRangeEnd(row) {
            this.selids = row.map((item) => item.conversationId);
        },
        //批量通过
        async BatchApproval() {
            var that = this;
            if (this.selids.length == 0) {
                this.$message({ message: "至少选择一行", type: "warning", });
                return
            }
            this.$confirm("确认要执行批量通过的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {

            const params = {
                reviewType: 2,
                conversationIdList: this.selids
            }
            let res = this.Filter.switchshow ? await appealReviewAfterSales(params) : await appealReview(params) ;
                if (res?.success) {
                    that.$message({ message: '已通过', type: "success" });
                    that.getPageList();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });

            // const params = {
            //     reviewType: 2,
            //     conversationIdList: this.selids
            // }
            // var res = await appealReviewAfterSales(params);
            // this.getPageList();
        },

        async BatchRejection() {
            var that = this;
            if (this.selids.length == 0) {
                this.$message({ message: "至少选择一行", type: "warning", });
                return
            }
            this.$confirm("确认要执行批量拒绝的操作吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {

            const params = {
                reviewType: 3,
                conversationIdList: this.selids
            }
            let res =  this.Filter.switchshow ? await appealReviewAfterSales(params) : await appealReview(params);

                if (res?.success) {
                    that.$message({ message: '已拒绝', type: "success" });
                    that.getPageList();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },

    }

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 55px;
}

/* 确保你的样式文件正确加载，并且在页面中可以找到 */
.status-wait {
    color: yellow;
}

.status-in-progress {
    color: blue;
}

.status-approved {
    color: green;
}

.status-rejected,
.status-expired {
    color: red;
}
</style>
