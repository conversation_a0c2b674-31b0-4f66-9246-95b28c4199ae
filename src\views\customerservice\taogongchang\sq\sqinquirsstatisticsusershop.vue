<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' style="height:93%;" :summaryarry="summaryarry" :isIndex='true'
            :hasexpand='false' @sortchange='sortchange' :tableData='inquirslist' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-input v-model.trim="filter.groupName" placeholder="组" style="width:120px;" clearable disabled
                    :maxlength="50" />
                <el-input v-model="filter.sname" v-model.trim="filter.sname" placeholder="姓名" style="width:120px;"
                    disabled="true" :maxlength="50" />
                <el-input v-model="filter.startDate" style="width:120px;" disabled="true" />至
                <el-input v-model="filter.endDate" style="width:120px;" disabled="true" />
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getinquirsList" />
        </template>
    </my-container>
</template>
<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";
import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import {
    GetGongChangUserInquirsShopPageList
} from '@/api/customerservice/gongchanginquirs'

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺', width: '240', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '接起量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'satisfactions', label: '满意量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'satisfactions2', label: '一般满意量', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactions', label: '不满意量', width: '100', sortable: 'custom' },

    { istrue: true, prop: 'satisfactionRate', label: '满意度', width: '90', sortable: 'custom', formatter: (row) => { return (row.satisfactionRate ? precision(row.satisfactionRate, 100).toFixed(2) : 0) + "%" } },
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意度', width: '100', sortable: 'custom', formatter: (row) => { return (row.noSatisfactionRate ? precision(row.noSatisfactionRate, 100).toFixed(2) : 0) + "%" } },

    { istrue: true, prop: 'responseTime', label: '平均响应时长', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '120', sortable: 'custom' },
];

export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props: [""],
    data() {
        return {
            that: this,
            filter: {
                groupType: 0,
                inquirsType: 0,
                sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
            },
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
        };
    },
    async mounted() {

    },
    methods: {
        loadData(args) {
            this.filter = args;
            this.onSearch();
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            this.filter.matchType = 1;
            const para = { ...this.filter };
            let pager = this.$refs.pager.getPager();

            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };

            return params;
        },
        async getinquirsList() {
            let params = this.getParam();
            this.listLoading = true;
            const res = await GetGongChangUserInquirsShopPageList(params);
            this.listLoading = false;
            this.total = res.data.total;
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
