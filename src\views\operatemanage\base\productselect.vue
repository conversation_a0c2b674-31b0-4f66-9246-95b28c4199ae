<template>
  <my-container v-loading="pageLoading">
    <template #header>
       <el-button-group >
          <el-button style="padding: 0;width: 155px;">
            <el-input v-model="filter.procode" placeholder="产品ID" @keyup.enter.native="onSearch" clearable/>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable style="width: 80px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;width: 105px;">
             <el-cascader v-model="filter.categoryids" :options="categorylist" :props="{ checkStrictly: true, value: 'id' }" filterable  style="width:100%;" placeholder="类目"/>
          </el-button>
          <el-button style="padding: 0;width: 135px;">
            <el-select v-model="styleCode" multiple filterable remote reserve-keyword placeholder="系列编码" clearable :remote-method="remoteMethod" :loading="searchloading">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;width: 135px;">
             <el-input v-model="filter.title" placeholder="标题" @keyup.enter.native="onSearch" clearable/>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.shopId" placeholder="店铺" clearable style="width: 130px">
              <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupId" placeholder="运营组长" style="width: 100px" clearable>
              <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operateSpecialId" placeholder="运营专员" clearable style="width: 100px">
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.user1Id"  placeholder="运营助理" clearable style="width: 100px">
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.user2Id"  placeholder="运营车手" clearable style="width: 100px">
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.user3Id"  placeholder="运营备用" clearable style="width: 95px">
              <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.brandId"  placeholder="采购员" clearable style="width: 80px">
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.brandRate"  placeholder="系统判定采购" clearable style="width: 100px">
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.IsProtectCornerId"  placeholder="是否护墙角" clearable style="width: 100px">
              <el-option label="已设置" :value="1"></el-option>
              <el-option label="未设置" :value="0"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select v-model="filter.status" placeholder="状态" clearable filterable style="width: 80px">
               <el-option label="未知" :value="0"></el-option>
               <el-option label="上架" :value="1"></el-option>
               <!-- <el-option label="缺货" :value="2"></el-option> -->
               <el-option label="下架" :value="3"></el-option>
             </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
       </el-button-group>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
         :tableData='productlist' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="height:90%;"/>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getProductlist" />
    </template>
  </my-container>
</template>

<script>
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import { getList as getcategorylist } from '@/api/operatemanage/base/category'
import {getPageProductList} from '@/api/operatemanage/base/product'
import { platformlist} from '@/utils/tools'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import bindbianma from '@/components/Bus/probindbianma'
import {listToTree} from '@/utils'
import {getAllProBrand} from '@/api/inventory/warehouse'
import { formatPlatform,formatYesorno,formatYesornoBool,formatbianmastatus,formatproducmask,formatLink,formatLinkProCode} from "@/utils/tools";
import productcommhistory from './productcommhistory.vue'
import { getListByStyleCode } from "@/api/inventory/basicgoods"

const tableCols =[
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'platform',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
      {istrue:true,prop:'styleCode',label:'系列编码', width:'120',sortable:'custom',formatter:(row)=> !row.styleCode ? " " : row.styleCode},
      {istrue:true,prop:'productCategoryId',label:'类目', width:'200',sortable:'custom',permission:"productpermis",formatter:(row)=> row.productCategoryId=="0"?" ":row.productCategoryName},
      {istrue:true,prop:'shopId',label:'店铺', width:'150',sortable:'custom',formatter:(row)=> row.shopName},
      {istrue:true,prop:'groupId',label:'运营组', width:'70',sortable:'custom',formatter:(row)=>row.groupName||' '},
      {istrue:true,prop:'operateSpecialUserId',label:'运营专员', width:'80',sortable:'custom',formatter:(row)=> row.operateSpecialUserName||' '},
      {istrue:true,prop:'userId',label:'运营助理', width:'80',sortable:'custom',formatter:(row)=> row.userRealName||' '},
      {istrue:true,prop:'userId2',label:'车手', width:'70',sortable:'custom',formatter:(row)=> row.userRealName2||' '},
      {istrue:true,prop:'userId3',label:'备用', width:'70',sortable:'custom',formatter:(row)=>row.userRealName3||' '},
      {istrue:true,prop:'onTime',label:'上架时间', width:'150',sortable:'custom',},
      {istrue:true,prop:'brandId',label:'采购', width:'65',sortable:'custom',tipmesg:'采购负责人',formatter:(row)=> !row.brandName ? " " : row.brandName},
      {istrue:true,prop:'brandRate',label:'系统判定采购', width:'120'},
      {istrue:true,prop:'giftlinkID',label:'赠品链接主ID', width:'110',sortable:'custom',permission:"productpermis",},
      {istrue:true,prop:'title',label:'标题', width:'250',sortable:'custom'},
      {istrue:true,prop:'status',label:'状态', width:'60',sortable:'custom',permission:"productpermis",formatter:(row)=>formatbianmastatus(row.status)},
      {istrue:true,prop:'offTime',label:'下架时间', width:'150',sortable:'custom',},
      {istrue:true,prop:'commissionRate1',label:'毛利提成比例1', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionRate1 == 0 ? '0' : row.commissionUserName1 + row.commissionRate1+'%'},
      {istrue:true,prop:'commissionRate2',label:'毛利提成比例2', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionRate2 == 0 ? '0' : row.commissionUserName2 + row.commissionRate2+'%'},
      {istrue:true,prop:'commissionRate3',label:'毛利提成比例3', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionRate3 == 0 ? '0' : row.commissionUserName3 + row.commissionRate3+'%'},
      {istrue:true,prop:'commissionRate4',label:'毛利提成比例4', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionRate4 == 0 ? '0' : row.commissionUserName4 + row.commissionRate4+'%'},
      {istrue:true,prop:'commissionProfitRate1',label:'净利提成比例1', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionProfitRate1 == 0 ? '0' : row.commissionUserName1 + row.commissionProfitRate1+'%'},
      {istrue:true,prop:'commissionProfitRate2',label:'净利提成比例2', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionProfitRate2 == 0 ? '0' : row.commissionUserName2 + row.commissionProfitRate2+'%'},
      {istrue:true,prop:'commissionProfitRate3',label:'净利提成比例3', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionProfitRate3 == 0 ? '0' : row.commissionUserName3 + row.commissionProfitRate3+'%'},
      {istrue:true,prop:'commissionProfitRate4',label:'净利提成比例4', width:'90',sortable:'custom',permission:"productpermis",formatter:(row)=> row.commissionProfitRate4 == 0 ? '0' : row.commissionUserName4 + row.commissionProfitRate4+'%'},
      {istrue:true,prop:'modifiedTime',label:'编辑时间', width:'150',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'添加时间', width:'150',sortable:'custom',permission:"productpermis",}
     ];
const tableHandles1=[
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton,bindbianma,productcommhistory },
  data() {
    return {
      imageUrl: '',
      drawerprocid:false,
      drawerimage:false,
      directionbtt:"btt",
      that:this,
      filter: {
        name: '',
        styleCode:null,
        brandRate:null,
        categoryids:[],
      },
      styleCode:null,
      historyTime:null,
      platformlist:platformlist,
      productlist: [],
      directorList:[],
      directorGroupList:[],
      shopList:[],
      bandList:[],
      categorylist:[],
      brandlist:[],
      options: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      autoform:{
               fApi:{},
                options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                //options:{onSubmit:(formData)=>{alert(JSON.stringify(formData))}},
                //options:{submitBtn:false},
                rule:[]
        },
      total: 0,
      sels: [],
      selids: [],
      IsProtectCornerId:'',
      listLoading: false,
      pageLoading: false,
      searchloading:false,
      selrows:[],
    }
  },
  mounted() {
    //this.getcategorylist()
    this.getDirectorlist()
    this.onSearch()
    this.init()
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async getchoicelistOnly(){
       if(!this.selrows||this.selrows.length==0)
         this.$message({message: "请选择一条数据，",type: "warning",});
       if(!this.selrows||this.selrows.length >1)
         this.$message({message: "只能选择一条数据",type: "warning",});
       return this.selrows
    },
     async getcategorylist(platform) {
      const res = await getcategorylist({platform:platform })
      if (!res?.code) {
        return
      }
      const list=[];
      res.data.forEach(f=>{
         f.label=f.categoryName;
         list.push(f)
      })
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: '',
        parentId: '',
        label: '所有'
      })
    },
    //系列编码远程搜索
    async remoteMethod(query){
      if (query !== ''){
          this.searchloading == true
          setTimeout(async () => {
              const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
              this.searchloading = false
              res?.data?.forEach(f=>{
              this.options.push({value:f.styleCode,label:f.styleCode})
              });
          }, 200)
      }
      else{
          this.options = []
      }
    },
    async getDirectorlist() {
      const res1 = await getDirectorList({})
      const res2 = await getDirectorGroupList({})
      const res3 = await getProductBrandPageList()

      this.directorList = res1.data
      this.directorGroupList =[{key:'0',value:'未知'}].concat(res2.data ||[]);
      this.bandList = res3.data?.list
    },
   async onchangeplatform(val){
      this.categorylist =[]
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shopList=res1.data.list
      if (val) this.getcategorylist(val)
      //const res2 = await getcategoryList({platform:val,CurrentPage:1,PageSize:100});
      //this.productCategorylist=res2.data
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getProductlist()
    },
    async getProductlist() {
      var pager = this.$refs.pager.getPager()
     if(this.filter.categoryids != null && this.filter.categoryids.length > 0){
        this.filter.productCategoryId = this.filter.categoryids[this.filter.categoryids.length-1]
      }
      this.filter.styleCode = this.styleCode.join()
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter
      }
      this.listLoading = true
      const res = await getPageProductList(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.productlist = data
      this.selids=[]
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
  async init(){
      var res= await getAllProBrand();
      this.brandlist = res.data.map(item => {
          return { value: item.key, label: item.value };
      });
    },
    selsChange: function(sels) {
      this.sels = sels;
    },
    selectchange:function(rows,row) {
      this.selrows=rows;
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
