<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='pddgroupinquirsstatisticslist' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="Filter.GroupNames" placeholder="组名称" multiple clearable filterable
                            :collapse-tags="true">
                            <el-option v-for="item in groupList" :key="item.groupname" :label="item.groupname"
                                :value="item.groupname" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="Filter.Sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getGroupInquirsStatisticsList" />
        </template>
        <el-dialog title="添加客服组效率统计信息" :visible.sync="add客服组效率统计dialogVisibleSyj" width="30%"
            :close-on-click-modal="false" v-dialogDrag>
            <span>

            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="客服组效率统计" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">
                        上传
                    </my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import { pagePddGroupKfEfficiencyList, pagePddGroupKfEfficiencyListMap, getPddGroup, exportPddGroupKfEfficiencyList } from '@/api/customerservice/pddInquirs'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import dayjs from "dayjs";
import { formatTime } from "@/utils";

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}
const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名', width: '160', sortable: 'custom', type: "click", handle: (that, row, column, cell) => that.groupclick(row, column, cell), formatter: (row) => row.groupname },

    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100).toFixed(2) : 0) + "%" } },

    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100).toFixed(2) : 0) + "%" } },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100).toFixed(2) : 0) + "%" } },

    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },

    { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },

    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    props: ["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            Filter: {

                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],

            },
            shopList: [],
            userList: [],
            groupList: [],
            pddgroupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "ipscount", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            //
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            add客服组效率统计dialogVisibleSyj: false,
            isleavegroup: this.partInfo,//是否离组
        };
    },
    watch: {
        partInfo() {
            this.isleavegroup = this.partInfo;
            this.setGroupSelect();
        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        await this.setGroupSelect();
    },
    methods: {
        async setGroupSelect() {
            const form = new FormData();
            form.append("enmPddGroupType", 1);
            form.append("isleavegroup", this.isleavegroup);
            const res = await getPddGroup(form);
            this.groupList = res.data.filter(item => item.groupname.includes("一体"));

        },
        async showchart(row) {

            if (this.Filter.timerange) {
                var d = new Date(this.Filter.Sdate[0])
                var startsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                this.Filter.startSdate = startsdate;

                d = new Date(this.Filter.Sdate[1])
                var endsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                this.Filter.startSdate = endsdate;
            }

            var params = { GroupNames: [row.groupName], StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate, EnmPddGroupType: 1 }
            let that = this;

            const res = await pagePddGroupKfEfficiencyListMap(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res;
                that.dialogMapVisible.title = res.title;
                res.title = "";
            })
            this.dialogMapVisible.visible = true

        },

        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportSyj() {
            this.dialogVisibleSyj = true
        },

        async onSubmitupload2() {
            this.$refs.upload2.submit()
        },
        onRefresh() {
            this.setGroupSelect();
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);

            this.getGroupInquirsStatisticsList();
        },
        getParam() {
            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            this.Filter.EnmPddGroupType = 1;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para

            };
            return params;
        },
        async getGroupInquirsStatisticsList() {
            let params = this.getParam();
            params.isYitiGroupQuery = true;
            this.listLoading = true;
            const res = await pagePddGroupKfEfficiencyList(params);
            this.listLoading = false;

            this.total = res.total
            this.pddgroupinquirsstatisticslist = res.list;
            this.summaryarry = res.summary;
        },
        groupclick(row) {

            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }

            window.showpddpddlist(row.groupName, this.Filter.startSdate == null ? "" : this.Filter.startSdate
                , this.Filter.endSdate == null ? "" : this.Filter.endSdate)

            window.showpddtabSh4()

        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async uploadFile2() { },
        async uploadSuccess2() { },
        async onExport() {
            let params = this.getParam();
            params.isYitiGroupQuery = true;
            this.listLoading = true
            const res = await exportPddGroupKfEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多组效率统计(售后组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
