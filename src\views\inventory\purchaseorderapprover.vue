<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="运营组">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 180px">
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="审批人">
                    <el-input v-model="filter.DUserName" placeholder="审批人用户" maxlength="20" clearable></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAddDirector">新增成员</el-button> 
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols'
            :treeProp="{transform: true, rowField: 'id', parentField: 'parentId'}" :loading='listLoading' :border='true'
            :that="that" ref="vxetable" :somerow="'groupId'" @sortchange='sortchange'>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 信息编辑 -->
        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="addFormVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;" @close="closeDraw"> 
            <el-form ref="addForm" :model="editDialog.data" :rules="addFormRules"  label-width="150px" :inline="false">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="18" :xl="18">
                        <el-col :xs="24" :sm="24" :md="24" :lg="18" :xl="18">
                            <el-form-item label="运营组" prop="groupId"> 
                                <el-select :disabled="editDialog.data.id>0" filterable v-model="editDialog.data.groupId" collapse-tags clearable placeholder="请选择运营组" style="width: 265px">
                                    <el-option v-for="item in grouplist" :key="'groupDatas' +item.value" :label="item.label" :value="item.value"/>
                                </el-select>
                            </el-form-item>
                        </el-col>  
                        <el-col :xs="12" :sm="12" :md="12" :lg="9" :xl="9">
                            <el-form-item label="步骤1审批人" prop="userId">
                                <YhUserelectorMulti v-if="addFormVisible" ref="userIdSelect" selectKey="userIdSelect" 
                                :value.sync="editDialog.data.userId" maxlength="50" 
                                :text.sync="editDialog.data.userName" 
                                   style="width:80%;" placeholder="请输入审批人">
                                </YhUserelectorMulti>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="12" :sm="12" :md="12" :lg="9" :xl="9">
                            <el-form-item label="步骤1审批类型" prop="type">
                                <el-select clearable v-model="editDialog.data.type">
                                    <el-option label="或签" value="或"></el-option>
                                    <el-option label="会签" value="且"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="12" :sm="12" :md="12" :lg="9" :xl="9">
                            <el-form-item label="步骤2审批人"  prop="userId1">
                                <YhUserelectorMulti v-if="addFormVisible" ref="userIdSelect1" selectKey="userIdSelect1" 
                                :value.sync="editDialog.data.userId1" maxlength="50" :text.sync="editDialog.data.userName1" 
                                   style="width:80%;" placeholder="请输入审批人">
                                </YhUserelectorMulti>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="12" :sm="12" :md="12" :lg="9" :xl="9">
                            <el-form-item label="步骤2审批类型" prop="type1">
                                <el-select clearable v-model="editDialog.data.type1">
                                    <el-option label="或签" value="或"></el-option>
                                    <el-option label="会签" value="且"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="12" :sm="12" :md="12" :lg="9" :xl="9">
                            <el-form-item label="步骤3审批人" prop="userId2">
                                <YhUserelectorMulti v-if="addFormVisible" ref="userIdSelect2" selectKey="userIdSelect2" 
                                :value.sync="editDialog.data.userId2" maxlength="50" :text.sync="editDialog.data.userName2" 
                                   style="width:80%;" placeholder="请输入审批人">
                                </YhUserelectorMulti>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="12" :sm="12" :md="12" :lg="9" :xl="9">
                            <el-form-item label="步骤3审批类型" prop="type2">
                                <el-select clearable v-model="editDialog.data.type2" >
                                    <el-option label="或签" value="或"></el-option>
                                    <el-option label="会签" value="且"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-col>
                </el-row>
            </el-form> 
            <div class="drawer-footer">
                <el-button @click.native="addFormVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="addLoading" @click="onAddSubmit" />
            </div>
        </el-drawer>
        <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
            <span>确认删除</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="deluser">确 定</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { pagePurchaseOrderApprover,SavePurchaseOrderApprover,DelPurchaseOrderApprover,GetPurchaseOrderApprover } from "@/api/inventory/purchaseordernew";
import { getUserListPage, getUser } from "@/api/admin/user"
import YhUserelectorMulti from '@/components/YhCom/yh-userselectormulti.vue'
import { ruleDirectorGroup,ruleMultityDirectorGroup } from '@/utils/formruletools'

const tableCols = [
    { istrue: true, prop: 'groupId', label: '运营组', width: '120', formatter: (row) =>row.groupName },
    { istrue: true, prop: 'dUserNames', label: '审批步骤1用户名', width: 'auto', },
    { istrue: true, prop: 'dUserNames1', label: '审批步骤2用户名', width: 'auto', }, 
    { istrue: true, prop: 'dUserNames2', label: '审批步骤3用户名', width: 'auto', }, 
    {
    istrue: true, type: 'button', label: '操作', width: '120', btnList: [
    {
        label: "编辑", handle: (that, row) => that.showEditDialog(row)
    },
    {
        label: "删除", type:'danger', handle: (that, row) => that.showdialog(row)
    }]
},

];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
    ];
export default {
    name: 'YunHanAdminPurchaseorderapprover',
    components: {cesTable, container, MyConfirmButton, vxetablebase,YhUserelectorMulti},
    data() {
        return {
            that: this,
            taskid: '',
            addForm: {
                id: null,
                checkDate: null,
                buyNo: null,
                indexNo: null,
                parentId: null,
                oldCostPrice: null,
                newCostPrice: null,
                brandName:null,
            },
            filter: {
                groupId: null,
            },
            list: [],
            grouplist:[],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "groupId", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            formtitle: '',
            pageLoading: false,
            listLoading: false,
            addLoading: false,
            addFormVisible: false,
            dialogVisible: false,
            addFormRules: {
                groupId: [{ required: true,  message: '请选择运营组', trigger: 'blur' }],
                userId: [{ required: true, type: 'array',message: '请输入步骤1审批人', trigger: 'blur' }],
                type: [{ required: true,  message: '请输入步骤1审批类型', trigger: 'blur' }], 
            },
            editDialog:{
                show:false,
                loding:false,
                groupDatas:[], 
                data:{
                    id:0,
                    groupId:null,
                    userId:null,
                    userName:[],
                    type:null,
                    userId1:null,
                    userName1:[],
                    type1:null,
                    userId2:null,
                    userName2:[],
                    type2:null
                }
            }
        };
    },

    async mounted() {
        await this.init(); 
        await this.onSearch();
    },

    methods: { 
        showdialog(row){
            this.taskid = row.id;
            this.dialogVisible = true;
        },
        closeDraw(){
            this.$refs.addForm.clearValidate();
        },
        async showEditDialog(row){
            this.editDialog.loding=true;
            this.formtitle = "编辑运营组审批人员"; 
            let res = await GetPurchaseOrderApprover({id:row.id});
            let that = this;
            if(res.data){  
                this.editDialog.data.id = res.data.id; 
                this.editDialog.data.userName = res.data.userName;
                this.editDialog.data.type = res.data.type;
                this.editDialog.data.userName1 = res.data.userName1;
                this.editDialog.data.type1 = res.data.type1;
                this.editDialog.data.userName2 = res.data.userName2;
                this.editDialog.data.type2 = res.data.type2;  
                this.editDialog.data.userId = res.data.userId;
                this.editDialog.data.userId1 = res.data.userId1;
                this.editDialog.data.userId2 = res.data.userId2; 
                this.editDialog.data.groupId = res.data.groupId+'';  
                this.addFormVisible = true;   
            }else{
                this.$message({message:"获取数据失败，请刷新界面后重试！！！",type:'error'})
            }
            this.editDialog.loding=false;
        },
        async deluser(val){
            let _this = this;
            let res = await DelPurchaseOrderApprover({id:_this.taskid});
            if(res.data){
                this.getlist()
            }else{
                this.$message({message:"删除失败，请刷新界面后重试！！！",type:'error'})
            }
            _this.dialogVisible = false; 
        }, 
        //修改组员信息
        async onAddSubmit() {
            this.addLoading = true; 
            await this.$refs["addForm"].validate(async (valid, fail) => {
                if (valid) {
                    const formData = this.editDialog.data;
                    //formData.Enabled=true;
                    const res = await SavePurchaseOrderApprover(formData);
                    if (res.data) {
                        this.getlist();
                        this.addFormVisible = false;
                    }
                    else {
                        this.$message({message:"添加数据失败，请检查数据后重试！！！",type:'error'})
                    }
                } else {
                    
                }
            })
            this.addLoading = false;
        },
        async onAddDirector() { 
            this.formtitle = "新增运营组审批人员"; 
            this.editDialog.data.id = null;
            this.editDialog.data.groupId = null; 
            this.editDialog.data.userName = [];
            this.editDialog.data.type = null;
            this.editDialog.data.userName1 = [];
            this.editDialog.data.type1 = null;
            this.editDialog.data.userName2 = [];
            this.editDialog.data.type2 = null;  
            this.editDialog.data.userId = null;
            this.editDialog.data.userId1 = null;
            this.editDialog.data.userId2 = null;
            let that =this;
            // that.$forceUpdate();
            this.$nextTick(()=>{ 
                this.addFormVisible = true; 
            })
        },
        async init() {
            var res1 = await getDirectorGroupList();
            this.grouplist = res1.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async getlist() {
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }
            this.listLoading = true
            const res = await pagePurchaseOrderApprover(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data.total
            let dtList=[];
            res.data.list.forEach(x => {
                dtList.push(x);
                if(x.dtList && x.dtList.length>0){
                    dtList=dtList.concat([...x.dtList]);
                }
            })
            this.list = dtList
            this.summaryarry = res.data.summary;
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>