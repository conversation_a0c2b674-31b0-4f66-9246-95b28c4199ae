<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'">
        </el-date-picker>
        <el-select v-model="ListInfo.regionWarehouse" style="width: 200px;" clearable filterable placeholder="区域仓库"
          class="publicCss">
          <el-option v-for="item in regionWarehouseList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
    </template>

    <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
      :loading="loading" :column-config="{ resizable: true }" :merge-footer-items="mergeFooterItems"
      :span-method="mergeRowMethod" :row-class-name="rowClassName" :data="tableData">
      <vxe-column field="month" width="100" title="月份"></vxe-column>
      <vxe-column field="regionWarehouse" width="200" title="区域仓库"></vxe-column>
      <vxe-column field="monthlyAvgDailyInService" width="200" title="月度日平均在职人数"></vxe-column>
      <vxe-column field="monthlyAvgDailyAttendance" width="200" title="月度日平均出勤人数"></vxe-column>
      <vxe-column field="monthlyAvgDailyAttendanceRate" width="200" title="月度平均日出勤率	">
        <template slot-scope="scope">
          {{ scope.row.monthlyAvgDailyAttendanceRate ? scope.row.monthlyAvgDailyAttendanceRate + '%' : '0' }}
        </template>
      </vxe-column>
      <vxe-column field="monthlyAvgDailyShipmentVolume" width="200" title="月度日均发货量"></vxe-column>
      <vxe-column field="innerWarehousePerCapitaShipment" width="200" title="内仓人均发货"></vxe-column>
      <vxe-column field="monthlyAvgDailyShipmentGrowthRate" width="200" title="月均日发货涨幅率">
        <template slot-scope="scope">
          {{ scope.row.monthlyAvgDailyShipmentGrowthRate ? scope.row.monthlyAvgDailyShipmentGrowthRate + '%' : '0' }}
        </template>
      </vxe-column>
      <vxe-column title="操作" footer-align="left" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini"
            v-if="scope.row.regionWarehouse && !scope.row.regionWarehouse.includes('小计')"
            @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" style="color:red"
            v-if="scope.row.regionWarehouse && !scope.row.regionWarehouse.includes('小计')"
            @click="handleRemove(scope.row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
      <kdeliveryDataEdit ref="kdeliveryDataEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
        @cancellationMethod="dialogVisibleEdit = false" />
    </el-drawer>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { warehouseShipmentDataPage, warehouseShipmentDataImport, warehouseShipmentDataRemove } from '@/api/people/peoplessc.js';
import kdeliveryDataEdit from "./kdeliveryDataEdit.vue";
import checkPermission from '@/utils/permission'
export default {
  name: "kdeliveryDataIndex",
  components: {
    MyContainer, kdeliveryDataEdit
  },
  props: {
    // 可以添加props如果需要从父组件传递数据
  },
  data() {
    return {
      // 工具函数
      downloadLink,
      // 对话框状态
      dialogVisibleEdit: false,
      dialogVisible: false,
      // 编辑相关
      editInfo: {},
      // 文件上传
      fileList: [],
      uploadLoading: false,
      // 表格数据
      tableData: [],
      mergeFooterItems: [],
      // 下拉选项数据
      regionWarehouseList: [],
      // 表格配置
      somerow: 'regionWarehouse,month',
      // 加载状态
      loading: false,
      exportloading: false,
      // 查询条件
      ListInfo: {
        calculateMonthArr: [
          dayjs().subtract(1, 'month').format('YYYY-MM'),
          dayjs().subtract(1, 'month').format('YYYY-MM')
        ],
        startMonth: null,
        endMonth: null,
        regionWarehouse: '',
      }
    }
  },
  computed: {
    // 是否有选择的文件
    hasSelectedFile() {
      return this.fileList.length > 0;
    },
    // 是否可以导出
    canExport() {
      return this.tableData.length > 0;
    },
    // 格式化的时间范围显示
    formattedTimeRange() {
      const { calculateMonthArr } = this.ListInfo;
      if (calculateMonthArr?.length >= 2) {
        return `${calculateMonthArr[0]} 至 ${calculateMonthArr[1]}`;
      }
      return '';
    }
  },

  async mounted() {
    await this.getList()
  },
  methods: {
    rowClassName(event) {
      if (event.row.regionWarehouse && event.row.regionWarehouse.indexOf('小计') != -1) {
        return 'row-yellow1'
      }
      return null
    },
    //上传文件
    onUploadRemove() {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess() {
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      try {
        const form = new FormData();
        form.append("file", item.file);
        const res = await warehouseShipmentDataImport(form);
        if (res?.success) {
          this.$message({ message: res.msg || "导入成功", type: "success" });
          this.dialogVisible = false;
          await this.getList()
        } else {
          this.$message({ message: res?.msg || "导入失败", type: "error" });
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        this.$message({ message: "文件上传失败，请重试", type: "error" });
      } finally {
        this.uploadLoading = false
      }
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    downExcel() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250719/1946389882281107456.xlsx', '仓储发货数据-导入模板.xlsx');
    },
    async exportExcel() {
      this.exportloading = true;
      this.$refs.newtable.exportData({ filename: '仓储行政看板-仓储发货数据' + new Date().toLocaleString(), sheetName: 'Sheet1', type: 'xlsx' })
      this.$nextTick(() => {
        this.exportloading = false;
      })
    },
    closeGetlist() {
      this.dialogVisibleEdit = false;
      this.getList()
    },
    handleEdit(row) {
      this.editInfo = row;
      this.dialogVisibleEdit = true;
    },
    async handleRemove(row) {
      this.$confirm('是否删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (!row || !row.id) {
          this.$message.error('删除失败：数据异常');
          return;
        }
        try {
          this.loading = true
          const { success, msg } = await warehouseShipmentDataRemove({ ids: row.id })

          if (success) {
            this.$message.success(msg || '删除成功')
            await this.getList();
          } else {
            this.$message.error(msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败，请重试');
        } finally {
          this.loading = false
        }
      }).catch(() => {
      });
    },
    // 通用行合并函数（将相同多列数据合并为一行）
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = this.somerow.split(',')
      const cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },

    async getList() {
      try {
        this.prepareListParams();// 查询条件
        this.loading = true
        const { data, success, msg } = await warehouseShipmentDataPage(this.ListInfo)
        if (success && data) {
          this.tableData = data.list || []
          // 更新下拉选项
          this.updateDropdownOptions();
        } else {
          this.$message.error(msg || '获取列表失败')
          this.tableData = []
        }
      } catch (error) {
        console.error('获取列表失败:', error);
        this.$message.error('获取列表失败，请重试');
        this.tableData = []
      } finally {
        this.loading = false
      }
    },

    // 准备查询参数
    prepareListParams() {
      const { calculateMonthArr } = this.ListInfo;
      if (calculateMonthArr?.length >= 2) {
        this.ListInfo.startMonth = calculateMonthArr[0];
        this.ListInfo.endMonth = calculateMonthArr[1];
      } else {
        this.ListInfo.startMonth = null;
        this.ListInfo.endMonth = null;
      }
    },

    // 更新下拉选项
    updateDropdownOptions() {
      if (!this.tableData?.length) return;
      // 使用Set来避免重复，提高性能
      const regionWarehouseSet = new Set(this.regionWarehouseList);
      // 一次遍历同时处理区域仓库
      this.tableData.forEach(item => {
        if (item.regionWarehouse && !item.regionWarehouse.includes('小计')) {
          regionWarehouseSet.add(item.regionWarehouse);
        }
      });
      this.regionWarehouseList = Array.from(regionWarehouseSet);
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

// :deep(.row-green) {
//   background-color: #e0eed6;
// }

:deep(.row-yellow1) {
  background-color: #f8e2d3;
}

:deep(.row-yellow5) {
  background-color: #00937e;
  color: white;
}
</style>
