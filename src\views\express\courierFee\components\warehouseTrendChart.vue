<template>
  <MyContainer>
    <template #header>
      <div style="margin-bottom: 10px">
        <span>趋势图</span>
        <el-date-picker v-model="timeRanges" type="monthrange" align="right" unlink-panels range-separator="至"
          start-placeholder="开始月份" end-placeholder="结束月份" style="width: 250px; margin: 0 10px"
          :picker-options="pickerOptions" :value-format="'yyyy-MM'" @change="changeTime" :clearable="false">
        </el-date-picker>
      </div>
    </template>
    <template #default>
      <div v-loading="loading">
        <buschar v-if="dialogMapVisible.visible" ref="buschar" :analysisData="dialogMapVisible.data" :thisStyle="{
          width: '100%',
          height: '700px',
          'box-sizing': 'border-box',
          'line-height': '360px',
        }">
        </buschar>
      </div>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from "@/utils/getCols";
// import { pickerOptions } from "@/utils/tools";
import dayjs from "dayjs";
import buschar from "@/components/Bus/buschar";
import { todayOrderTimeFrame } from "@/api/order/orderData";
import { getMonthBill_PlatFormChart } from "@/api/express/express";
export default {
  name: "warehouseTrendChart",
  components: {
    MyContainer,
    vxetablebase,
    buschar,
  },

  data() {
    return {
      dialogMapVisible: { visible: false, title: "", data: {} },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        yearMonthStart: null, //开始时间
        yearMonthEnd: null, //结束时间
      },
      timeRanges: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    };
  },
  async mounted() {
    if (this.timeRanges && this.timeRanges.length == 0) {
      this.ListInfo.yearMonthStart = dayjs().subtract(6, 'month').startOf('month').format('YYYY-MM');
      this.ListInfo.yearMonthEnd = dayjs().endOf('month').format('YYYY-MM');
      this.timeRanges = [this.ListInfo.yearMonthStart, this.ListInfo.yearMonthEnd];
    }
    // await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.yearMonthStart = e ? e[0] : null;
      this.ListInfo.yearMonthEnd = e ? e[1] : null;
      this.getList();
    },
    async getList(type) {
      if (type == "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      // 使用时将下面的方法替换成自己的接口
      let startTime = this.ListInfo.yearMonthStart
      let endTime = this.ListInfo.yearMonthEnd
      startTime = startTime ? dayjs(startTime).format('YYYYMM') : null;
      endTime = endTime ? dayjs(endTime).format('YYYYMM') : null;
      this.dialogMapVisible.visible = false;
      const res = await getMonthBill_PlatFormChart({ dataType: 9, yearMonthStart: startTime, yearMonthEnd: endTime });
      this.loading = false;
      res.series.map((item) => {
        item.itemStyle = {
          normal: {
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 14,
              },
            },
          },
        };

        item.emphasis = {
          focus: "series",
        };
        item.smooth = false;
      });
      this.dialogMapVisible.data = res;
      this.dialogMapVisible.visible = true;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList();
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList();
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop;
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
