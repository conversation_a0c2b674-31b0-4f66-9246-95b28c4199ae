<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
        </template>
        <buschar ref="HotSaleBrandPushNewSale4_2qst" :analysisData="indexChatData1" :legendChanges="legendChanges1" style="height: 560px; width: 98%;"
            v-if="indexChatData1">
        </buschar>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import {
    GetHotSaleBrandPushNewSaleStyleChartData2
} from '@/api/operatemanage/productalllink/alllink'
import buschar from '@/components/Bus/buschar';
export default {
    name: "HotSaleBrandPushNewSale4_2",
    components: {
        MyContainer, buschar
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                saleStartDate: null,
                saleEndDate: null,
                createdUserId: 999999999,
                createdUserName: "",
                styleCodeCount: 0,
            },
            indexChatShow: false,
            indexChatData1: null,
            indexChatSelectedLegend1: [],
        }
    },
    async mounted() {

    },
    computed: {
    },
    methods: {
        async onSearch(args) {
            if (args) {
                this.filter.createdUserId = args.createdUserId;
                this.filter.createdUserName = args.createdUserName;
                this.filter.styleCodeCount = args.styleCodeCount;
                this.filter.timerange = args.timerange;
                this.isSum = args.isSum;

                this.filter.createdUserNames = args.createdUserNames;
                this.filter.createUserAreas = args.createUserAreas;
                this.filter.createUserRoles = args.createUserRoles;
                this.filter.createUserDeptNames = args.createUserDeptNames;

                console.log(this.filter, "this.filter111_2");
            }
            await this.getList();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.saleStartDate = this.filter.timerange[0];
                this.filter.saleEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.saleStartDate = null;
                this.filter.saleEndDate = null;
            }
            const params = {
                ...this.filter,
            };
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewSaleStyleChartData2(param)
            this.listLoading = false
            this.indexChatData1 = res;
        },
        async legendChanges1(selected) {
            this.indexChatSelectedLegend1 = selected;
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
