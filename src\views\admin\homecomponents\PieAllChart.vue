<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
//require('echarts/theme/macarons') // echarts theme
//import resize from './mixins/resize'

export default {
  //mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    title: {
      type: String,
      default: '违规扣款分析'
    },
    pieData: {}
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    pieData: {
      deep: true,
      handler(val) {   
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      
    },
    setOptions(f){
        var series = []
        var data = []
        
        f.pieSeries.forEach(s=>{
            data.push({value: s.value, name: s.name})
        })
         
          series.push({
            name: f.title,
            type: 'pie',
            //roseType: 'radius',
            radius: [15, 120],
            center: ['50%', '50%'],
            data: data,
            animationEasing: 'cubicInOut',
            //animationDuration: 2600
          
        })   
        
        this.chart.setOption({
          title: {
            text: this.title,
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
        // legend: {
        //   orient: 'vertical',
        //   right: 10,
        //   top: 20,
        //   bottom: 20,
        //   data: f.legend
        // },
        series: series
      })
    },
  }
}
</script>
