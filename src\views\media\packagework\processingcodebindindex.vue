<template>
  <my-container v-loading="pageLoading" >
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="（成品）绑定半成品" name="first1" style="height: 100%">
        <processingfinished ref="processingfinished" style="height: 100%"></processingfinished>
      </el-tab-pane>
      <el-tab-pane label="（半成品）绑定成品" name="first2" :lazy="true" style="height: 100%">
        <processingsemifinished ref="processingsemifinished" style="height: 100%">
        </processingsemifinished>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import processingfinished from "@/views/media/packagework/processingfinished";
import processingsemifinished from "@/views/media/packagework/processingsemifinished";

export default {
  name: 'processingcodebindindex',
  components: {
    MyContainer, processingfinished, processingsemifinished
  },

  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },

  async mounted() {

  },

  methods: {

  },
};
</script>

<style lang="scss" scoped></style>
