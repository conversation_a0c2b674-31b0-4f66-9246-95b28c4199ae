<template>
    <MyContainer>
        <vxetablebase ref="table" :id="'newStryleCodeReports_20240810165432'" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :showsummary='true' :summaryarry='summaryarry' @sortchange='sortchange'
            :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {
    pageStyleCodeGoodsDtlRptByProCodeListAsync
} from '@/api/bookkeeper/styleCodeRptData'
import { type } from "jquery";
const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'goodsImage', label: '商品图片', width: 'auto', sortable: 'custom', type: 'images' },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'costPrice', label: '成本', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'kczj', label: '库存资金', width: 'auto', sortable: 'custom' },
    { istrue: true, prop: 'threeDayZZRate', label: '3天周转天数', width: 'auto', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        filter: {
            default: () => {
                return {}
            }
        },
        proCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                // proCode: this.proCode
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            summaryarry: {},
        }
    },
    async mounted() {
        console.log(this.filter, 'this.proCodeMian');
        console.log(this.proCode, 'this.proCode');
        this.ListInfo = { ...this.ListInfo, ...this.filter, proCode: this.proCode }
        console.log(this.ListInfo, 'this.主链接');
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo = { ...this.ListInfo, ...this.filter, proCode: this.proCode }
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await pageStyleCodeGoodsDtlRptByProCodeListAsync(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
