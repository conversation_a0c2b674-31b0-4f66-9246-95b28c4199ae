<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <!-- <el-dropdown @command="command" style="margin-right: 10px;">
                    <el-button type="primary">
                        时间筛选<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1">订单日期</el-dropdown-item>
                        <el-dropdown-item :command="2">付款时间</el-dropdown-item>
                        <el-dropdown-item :command="3">计划发货日期</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown> -->
                <span style="font-size: 15px;">付款时间：</span>
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    :clearable="false" :start-placeholder="startPlaceholder" :end-placeholder="endPlaceholder"
                    value-format="yyyy-MM-dd" :picker-options="pickerOptions" style="width: 250px;margin-right: 10px;"
                    @change="changeTime" />
                <el-input v-model.trim="ListInfo.goodsKeyWords" placeholder="商品" maxlength="50" clearable
                    class="publicCss" />
                <el-dropdown @command="orderCommand" style="margin-right: 10px;">
                    <el-button type="primary">
                        订单号<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1">线上订单号</el-dropdown-item>
                        <el-dropdown-item :command="2">内部订单号</el-dropdown-item>
                        <el-dropdown-item :command="3">外部交易单号</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-input v-model.trim="ListInfo.orderNo" :placeholder="orderNoPlaceholder" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'cross-border_index202408041648'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template #paid_amount="{ row }">
                <div style="display: flex;flex-direction: column;">
                    <div>{{ row.paid_amount }}</div>
                    <div>{{ row.currency }}</div>
                </div>
            </template>
            <template #pay_amount="{ row }">
                <div style="display: flex;flex-direction: column;">
                    <div>{{ row.pay_amount }}</div>
                    <div>{{ row.freight }}</div>
                </div>
            </template>
            <template #invoice_title="{ row }">
                <div style="display: flex;flex-direction: column;">
                    <div>{{ row.invoice_title }}</div>
                    <div>{{ row.buyer_tax_no }}</div>
                </div>
            </template>
            <template #NodeRemork="{ row }">
                <div style="display: flex;flex-direction: column;">
                    <div>{{ row.NodeRemork }}</div>
                    <div>{{ row.node }}</div>
                </div>
            </template>
            <template #shop_buyer_id="{ row }">
                <div style="display: flex">
                    <div>{{ row.shop_buyer_id }}{{ row.shop_buyer_id || row.shop_name ? '-' : '' }}</div>
                    <div>{{ row.shop_name }}</div>
                </div>
            </template>
            <template #items="{ row }">
                <!-- <div style="display: flex;padding-left: 20px;" v-if="row.items.length > 0"
                    @click="imgClick(row.items, row.currency)">
                    <el-image v-for="(item, i) in row.items" style="width: 40px; height: 40px; margin-right: 10px;"
                        :src="item.picture ? item.picture.split(',')[0] : ''" v-if="i < 8" :key="i" />
                </div> -->
                <div style="display: flex;padding-left: 20px;position: relative;height: 40px;"
                    v-if="row.items.length > 0" @click="imgClick(row.items, row.currency)">
                    <el-badge :value="row.items ? row.items.length : ''" style="position: absolute;top: 10px;">
                        <el-image style="width: 40px; height: 40px; margin-right: 10px;"
                            :src="row.picture ? row.picture : ''" />
                    </el-badge>
                </div>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="商品详情" :visible.sync="itemsVisible" width="25%" v-dialogDrag>
            <el-scrollbar>
                <div class="father_dialog">
                    <div v-if="itemsVisible" class="father" v-for="(item, i) in items" :key="i">
                        <div class="father_left">
                            <el-image style="width: 50px; height: 50px; margin-right: 10px;"
                                :src="item.picture ? item.picture.split(',')[0] : ''" />
                            <div class="father_left_right">
                                <el-tooltip class="item" effect="dark" :content="item.name" placement="top-start">
                                    <div class="father_left_right_name">{{ item.name }}</div>
                                </el-tooltip>
                                <div class="father_left_right_sku">{{ item.sku_id }}</div>
                            </div>
                        </div>
                        <div class="father_center">
                            <div class="father_center_left">
                                <div>{{ item.currency }} {{ item.price }}</div>
                                <div>* {{ item.qty }}</div>
                            </div>
                            <!-- <div class="father_center_right">
                        <div>CNY 0</div>
                        <div class="father_center_right_info">
                            <div>0</div>
                            <div>0</div>
                            <div>0</div>
                        </div>
                    </div> -->
                        </div>
                        <!-- <div class="father_right">可配货库存:0</div> -->
                    </div>
                </div>
            </el-scrollbar>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getCrossBorderOrderPage, exportCrossBorderOrder } from '@/api/order/ordergoods'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'o_id', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },//1
    { sortable: 'custom', width: '120', align: 'center', prop: 'labels', label: '标签', },
    { width: '100', align: 'center', prop: 'items', label: '商品', },
    // { width: '120', align: 'center', prop: 'packageMaterial', label: '包材', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'so_id', label: '线上订单号', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'outer_pay_id', label: '外部交易号', },
    { width: '150', align: 'center', prop: 'externalDocket', label: '外部单据号', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'inTime', label: '订单日期', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'pay_date', label: '付款时间', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'shop_buyer_id', label: '买家账号+店铺', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'pay_amount', label: '应付+运费', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'paid_amount', label: '已付金额', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'statusStr', label: '状态', },
    // { width: '120', align: 'center', prop: 'exceptionHandling', label: '异常处理建议', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'buyer_message', label: '买家留言', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'remark', label: '卖家备注', },
    // { width: '120', align: 'center', prop: 'distributionRemark', label: '供分销备注', },
    // { width: '150', align: 'center', prop: 'promiseDeliveryTime', label: '承诺送达时间', },
    // { width: '150', align: 'center', prop: 'latestCollectingTime', label: '最晚揽收时间', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'node', label: '便签|线下备注', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'shipment', label: '买家指定物流', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'logistics_company', label: '快递公司', },
    // { width: '120', align: 'center', prop: 'banExpressCompany', label: '平台禁发快递公司', },
    // { width: '120', align: 'center', prop: 'paymentMethod', label: '付款方式', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'receiver_country', label: '收货国家地区', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'un_lid', label: '国际单号', },
    // { width: '150', align: 'center', prop: 'expressSigningTime', label: '物流签收时间', },
    // { width: '120', align: 'center', prop: 'customsDeclarationInfo', label: '报关信息', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'receiver_address', label: '收货地址', },
    // { width: '120', align: 'center', prop: 'stopSendingProposal', label: '停发建议', },
    { width: '120', align: 'center', prop: 'distributor', label: '分销商', },
    { width: '120', align: 'center', prop: 'supplier', label: '供销商', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'weight', label: '重量', },
    // { width: '120', align: 'center', prop: 'volume', label: '体积', },
    { width: '150', align: 'center', prop: 'invoice_title', label: '发票抬头+税号', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'send_date', label: '发货日期', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'plan_delivery_date', label: '计划发货日期', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'wms_co_id', label: '发货仓', formatter: (row) => row.warechasingName },
    { sortable: 'custom', width: '150', align: 'center', prop: 'creator_name', label: '业务员', },
    { width: '150', align: 'center', prop: 'estimatedDeliveryTime', label: '预计送达时间', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'end_time', label: '确认收货时间', },
    // { width: '120', align: 'center', prop: 'shipperDistribution', label: '货主分销', },
    // { width: '120', align: 'center', prop: 'cloudWarehouse', label: '运营分仓', },
    // { width: '150', align: 'center', prop: 'errorDescription', label: '创建快件错误描述', },
    { width: '150', align: 'center', prop: 'waitSendTime', label: '剩余发货时间', type: 'clickLink', style: (that, row) => that.renderStatus(row.istImeout, row.status), },
    // { width: '150', align: 'center', prop: 'allowModifyNoTime', label: '允许修改单号时间', },
    { width: '150', align: 'center', prop: 'waitReceivingTime', label: '等待买家收货时间', },
    // { width: '150', align: 'center', prop: 'expirationTime', label: '过期时间', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'chosen_channel', label: '实发快递渠道', formatter: (row) => row.chosen_channel == "-" ? '' : row.chosen_channel },
    { sortable: 'custom', width: '120', align: 'center', prop: 'receiver_zip', label: '收货邮编', },
    // { width: '120', align: 'center', prop: 'offlineCustomers', label: '线下客户', },
    // { width: '120', align: 'center', prop: 'lockUser', label: '锁定客户', },
    // { width: '120', align: 'center', prop: 'outboundNo', label: '出库单号', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                queryOrderType: 1,
                queryTimeType: 2,
                startTime: null,//开始时间
                endTime: null,//结束时间
                orderNo: null,//订单号
                goodsKeyWords: null,//商品
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            itemsVisible: false,
            items: []
        }
    },
    async mounted() {
        await this.getList()
    },
    computed: {
        orderNoPlaceholder() {
            let res = ''
            switch (this.ListInfo.queryOrderType) {
                case 1:
                    res = '线上订单号'
                    break;
                case 2:
                    res = '内部订单号'
                    break;
                case 3:
                    res = '外部交易单号'
                    break;
            }
            return res
        },
        startPlaceholder() {
            let res = ''
            switch (this.ListInfo.queryTimeType) {
                case 1:
                    res = '订单开始日期'
                    break;
                case 2:
                    res = '付款开始时间'
                    break;
                case 3:
                    res = '计划发货开始日期'
                    break;
                case 4:
                    res = '预计送达开始时间'
                    break;
            }
            return res
        },
        endPlaceholder() {
            let res = ''
            switch (this.ListInfo.queryTimeType) {
                case 1:
                    res = '订单结束日期'
                    break;
                case 2:
                    res = '付款结束时间'
                    break;
                case 3:
                    res = '计划发货结束日期'
                    break;
                case 4:
                    res = '预计送达结束时间'
                    break;
            }
            return res
        }
    },
    methods: {
        renderStatus(istImeout) {
            return istImeout ? 'color: red' : 'color:black'
        },
        changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
        },
        command(e) {
            this.ListInfo.queryTimeType = e
            this.ListInfo.startTime = null
            this.ListInfo.endTime = null
            this.timeRanges = []
        },
        orderCommand(e) {
            this.ListInfo.queryOrderType = e
            this.ListInfo.orderNo = null
        },
        imgClick(items, currency) {
            items.forEach(item => {
                item.currency = currency
            })
            this.items = items
            this.itemsVisible = true
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportCrossBorderOrder(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '跨境订单' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            if (this.timeRanges && this.timeRanges.length == 0) {
                //默认给近7天时间
                this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getCrossBorderOrderPage(this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.picture = item.items.length > 0 ? item.items[0].picture : ''
                    })
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .publicCss {
        width: 230px;
        margin-right: 10px;
    }
}

.father_dialog {
    max-height: 400px;

    .father {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #ebeef5;
        padding: 10px;
        box-sizing: border-box;
        margin-bottom: 5px;

        .father_left {
            // width: 50%;
            flex: 1;
            padding: 10px;
            display: flex;
            align-items: center;

            .father_left_right {
                display: flex;
                flex-direction: column;

                .father_left_right_name,
                .father_left_right_sku {
                    width: 200px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }

        .father_right {
            width: 200px;
            color: red;
        }

        .father_center {
            display: flex;
            width: 200px;
            // justify-content: space-between;
            justify-content: end;
            align-items: center;

            .father_center_left {
                display: flex;
                // width: 30%;
                justify-content: space-between;
                align-items: center;
            }

            .father_center_right {
                flex: 1;
                display: flex;
                // justify-content: space-between;
                padding-left: 10px;
                align-items: center;

                .father_center_right_info {
                    padding-left: 20px;
                    display: flex;
                    flex-direction: column;
                    // justify-content: space-between;
                }
            }
        }
    }
}
</style>
