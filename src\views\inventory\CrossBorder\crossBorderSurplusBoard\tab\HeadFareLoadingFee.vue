<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-form-item label="">
                    <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始时间"
                        end-placeholder="结束时间" :picker-options="pickerOptions"
                        :default-value="defaultDate"></el-date-picker>
                </el-form-item>

                <el-form-item label="">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'220px'" ref="" :inputt.sync="Filter.goodsCodes"
                        v-model.trim="Filter.goodsCodes" placeholder="商品编码/若输入多条请按回车" :clearable="true"
                        @callback="callbackGoodsCode" title="商品编码" @entersearch="entersearch" :maxRows="100">
                    </inputYunhan>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 5px;">导出</el-button>

                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <vxetablebase ref="table" :id="'crossBorderCourierFeeAverage202408310425'" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='dahuixionglist'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry'
            :showsummary='true' style="margin: 0" :loading="listLoading" :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
        </template>
    </my-container>
</template>
<script>

import { getFirstLegBalance_Temu_BantuoNewPageList, firstLegBalance_Temu_BantuoNew_Export } from '@/api/bookkeeper/crossBorderV2'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs';
import inputYunhan from "@/components/Comm/inputYunhan";

import { platformlistKj } from "@/utils/tools";
const tableCols = [
    { istrue: true, prop: 'yearMonthDayDate ', label: '数据日期', sortable: 'custom', formatter: row => { return dayjs(row.yearMonthDayDate).format('YYYY-MM-DD') } },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
    { istrue: true, prop: 'initialBalance', label: '期初余额', sortable: 'custom', },
    { istrue: true, prop: 'initialQuantity', label: '期初数量', sortable: 'custom', },
    { istrue: true, prop: 'currentAmount', label: '当期金额', sortable: 'custom', },
    { istrue: true, prop: 'currentQuantity', label: '当期数量', sortable: 'custom', },
    { istrue: true, prop: 'inventoryQuantity', label: '库存数量', sortable: 'custom', },
    { istrue: true, prop: 'currentPrice', label: '当期单价', sortable: 'custom', },
    { istrue: true, prop: 'dayQuantity', label: '日报用量', sortable: 'custom', },
    { istrue: true, prop: 'dayAmount', label: '日报用额', sortable: 'custom', },
    { istrue: true, prop: 'finalBalance', label: '期末余额', sortable: 'custom', },
    { istrue: true, prop: 'finalQuantity', label: '期末数量', sortable: 'custom', },
];
export default {
    name: "crossBorderCourierFeeAverage",
    components: { MyContainer, vxetablebase, inputYunhan },
    data() {
        return {
            that: this,
            editLoading: false,
            addVisible: false,
            Filter: {
                timerange: [],
            },
            userList: [],
            groupList: [],
            dahuixionglist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            // pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            platform: 1,
            yearMonth: "",
            editVisible: false,
            defaultDate: new Date(),
            pickerOptions: {
                disabledDate(date) {
                    // 设置禁用日期
                    const start = new Date("1970/1/1");
                    const end = new Date("9999/12/31");
                    return date < start || date > end;
                },
            },
            dialogVisible: false,//导入弹窗
            fileList: [],//上传文件列表
            uploadLoading: false,//上传按钮loading
            fileparm: {},//上传文件参数
            yearMonthDay: null,//导入日期
            shopList: [],
            ViolationTypeList: [],
            platformlistKj
        };
    },
    async mounted() {
        await this.init();
        this.onSearch();
    },
    methods: {
        datetostr(date) {
            var y = date.getFullYear();
            var m = ("0" + (date.getMonth() + 1)).slice(-2);
            var d = ("0" + date.getDate()).slice(-2);
            return y + "-" + m + "-" + d;
        },
        async init() {
            var date1 = new Date(); date1.setDate(date1.getDate() - 6);
            var date2 = new Date(); date2.setDate(date2.getDate() - 1);
            this.Filter.timerange = [];
            this.Filter.timerange[0] = this.datetostr(date1);
            this.Filter.timerange[1] = this.datetostr(date2);
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getjSpeedDriveList();
        },
        async getjSpeedDriveList() {
            const para = { ...this.Filter };
            if (this.Filter.timerange) {
                para.startDate = this.Filter.timerange[0];
                para.endDate = this.Filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                feeType: 3,
                ...pager,
                ...this.pager,
                ...para,

            };
            this.listLoading = true;
            const res = await getFirstLegBalance_Temu_BantuoNewPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.dahuixionglist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {//导出列表数据；
            const para = { ...this.Filter };
            if (this.Filter.timerange) {
                para.startDate = this.Filter.timerange[0];
                para.endDate = this.Filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                feeType: 3,
                ...pager,
                ...this.pager,
                ...para,

            };
            var res = await firstLegBalance_Temu_BantuoNew_Export(params);
            if (res?.success) {
                this.$message({ message: res.msg, type: "success" });
            }
        },
        //多条查询部分
        async entersearch(val) {
            this.onSearch();
        },
        async callbackGoodsCode(val) {
            this.Filter.goodsCodes = val;
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>