<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :toolbarshow="false"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%; height:680px; margin: 0" v-loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :title="title" :visible.sync="MatchOrReleasesVisible" width="30%" v-dialogDrag>
            <matchOrReleasesDialog ref="distributionTable" v-if="MatchOrReleasesVisible"
                :queryInfo="queryInfo" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pageGetWmsStat, exportWmsStat, getMatchOrReleases } from '@/api/vo/prePackScan'
import matchOrReleasesDialog from './matchOrReleasesDialog.vue'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'wmsName', label: '发货仓', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'quantity', label: '库存', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inWmsInventory', label: '进货仓库存', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'allotQty', label: '调拨库存', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'wmsWaitSendQty', label: '仓库待发数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'mobilizeCount', label: '已蓄订单数', type: 'click', handle: (that, row) => that.openGoodDialog(row, false, '已蓄订单数') },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'releaseCount', label: '已放订单数', type: 'click', handle: (that, row) => that.openGoodDialog(row, true, '已放订单数') },
]
export default {
    name: "shippingWareHouse",
    components: {
        MyContainer, vxetablebase,matchOrReleasesDialog
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: null
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            queryInfo: {
                wmsId: null,
                goodsCode: null,
                isRelease: null
            },
            title: null,
            MatchOrReleasesVisible: false,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        openGoodDialog(row, isRelease, title) {
            this.queryInfo = {
                wmsId: row.wmsId,
                goodsCode: row.goodsCode,
                isRelease,
            }
            this.title = title
            this.MatchOrReleasesVisible = true
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            const { data } = await exportWmsStat(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/zip" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '发货仓' + new Date().toLocaleString() + '.zip')
            aLink.click()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await pageGetWmsStat(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>