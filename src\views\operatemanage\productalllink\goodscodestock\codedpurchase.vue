<template>
    <container v-loading="pageLoading">
        <!--列表-->
        <div style="height: 200px;">
            <el-descriptions :column="8" direction="vertical" v-loading="listLoading" border>
                <el-descriptions-item label="图片" label-class-name="my-label" content-class-name="my-content">
                    <el-image :src="addForm.picture" @click="showImg(addForm.picture)"
                        style="max-width: 60px; max-height: 60px;" fit="fill" :lazy="true"
                        loading="https://via.placeholder.com/80x80?text=Loading"
                        error="https://via.placeholder.com/80x80?text=Error"></el-image>
                </el-descriptions-item>
                <el-descriptions-item label="商品编码">
                    {{ addForm.goodsCode }}
                </el-descriptions-item>
                <el-descriptions-item label="毛三利率">
                    {{ addForm.profit3Rate == null ? 0 : addForm.profit3Rate.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="1天周转天数">
                    {{ addForm.turnoverDays == null ? 0 : addForm.turnoverDays.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="3天周转天数">
                    {{ addForm.turnoverDays3 == null ? 0 : addForm.turnoverDays3.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="进货数量">
                    <el-input-number v-model="addForm.count" :disabled="true" :precision="0" :step="1" :min="0" :max="100000000"
                        :controls="false" label="描述文字"></el-input-number>
                </el-descriptions-item>
                <el-descriptions-item label="日常进货量">
                    <template>
                        <span>
                            <el-input-number v-model="addForm.dayCount" :precision="0" :step="1" :min="0" :max="10000000" @change="changeDayNum()"
                                :controls="false" label="描述文字"></el-input-number>
                        </span>
                        <span>
                            <el-slider v-model="dayNum" :step="1" :min="0" :max="15" show-stops @change="changeNum()">
                                </el-slider>
                        </span>
                    </template>              
                </el-descriptions-item>
                <el-descriptions-item label="活动预估量">
                    <el-input-number v-model="addForm.hotCount" :precision="0" :step="1" :min="0" :max="10000000" @change="changeHotNum()"
                        :controls="false" label="描述文字"></el-input-number>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getProductGoodsCode, addOrUpdateProductGoodsCode, approvalPurchaseGoods } from "@/api/inventory/goodscodestock"
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

const tableCols = [];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

export default {
    name: 'YunHanAdminCodedpurchase',
    components: { container, MyConfirmButton, vxetablebase, cesTable, ElImageViewer },
    props: {
        //filter: {}
        title: {
            type: String,
            default: '编码进货'
        },
    },

    data() {
        return {
            that: this,
            filter: {
                goodsCode: null,
                appStatus: 0,
                isSuccess: 0
            },
            list: [],
            addForm: {
                id: null,
                goodsCode: null,
                picture: null,
                profit3Rate: null,
                turnoverDays: null,
                turnoverDays3: null,
                expectedSale: 0,
                count: 0,
                dayCount: 0,
                hotCount: 0,
                salesDay: 0,
                salesDay7: 0,
                salesDay15: 0,
            },
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            dayNum: 0,
            listLoading: false,
            pageLoading: false,
            showGoodsImage: false,
        };
    },

    async mounted() {

    },

    methods: {
        async onSearch(row) {
            console.log('接收数据', row)
            this.listLoading = true;
            if (row) {
                const { goodsCode, picture, profit3Rate, turnoverDays, turnoverDays3, expectedSale, salesDay, salesDay7, salesDay15 } = row;
                this.addForm = {
                    ...this.addForm,
                    goodsCode,
                    picture,
                    profit3Rate,
                    turnoverDays,
                    turnoverDays3,
                    expectedSale,
                    salesDay,
                    salesDay7,
                    salesDay15,
                };             
                this.dayNum = 0
                try {
                    var para = { goodsCode, appStatus: 0 }
                    const res = await getProductGoodsCode(para);
                    const { data } = res;
                    this.addForm.count = data?.count || 0;
                    this.addForm.dayCount = data?.dayCount || 0;
                    this.addForm.hotCount = data?.hotCount || 0;
                    this.dayNum = parseInt(this.addForm.dayCount / this.addForm.expectedSale);
                    this.addForm.id = data?.id || 0;
                } catch (error) {
                    this.addForm.count = 0;
                    this.addForm.id = 0;
                    this.addForm.dayCount = 0;
                    this.addForm.hotCount = 0;
                }
            }
            this.listLoading = false;
        },
        async changeNum() {
            this.addForm.dayCount = this.dayNum * this.addForm.expectedSale;
            this.addForm.count = this.addForm.dayCount + this.addForm.hotCount;
        },
        async changeDayNum() {       
            if (!this.addForm.dayCount) {
                this.addForm.dayCount = 0;     
            }
            this.addForm.count = this.addForm.dayCount + this.addForm.hotCount;
        },
        async changeHotNum() {
            if (!this.addForm.hotCount){
                this.addForm.hotCount = 0;
            } 
            this.addForm.count = this.addForm.dayCount + this.addForm.hotCount;
        },  
        async onFinish(isEnd) {
            if (this.addForm.dayCount == null || this.addForm.dayCount == 0) {
                this.$message({ message: '请填写日常进货量！', type: "warning" });
                return false;
            }
            // if (this.addForm.hotCount == null || this.addForm.hotCount == 0) {
            //     this.$message({ message: '请填写活动预估量！', type: "warning" });
            //     return false;
            // }
            var para = { ... this.addForm, IsEnd: isEnd, appStatus: 0 };
            var res = await addOrUpdateProductGoodsCode(para);
            if (res?.success) {
                this.addForm.id = res?.data || 0;
                this.$message({ message: '成功', type: "success" });
                return true;
            } else {
                //this.$message({ message: res.msg, type: "warning" });
                return false;
            }
        },
        async showImg(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            this.imgList.push(e);
        },
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>