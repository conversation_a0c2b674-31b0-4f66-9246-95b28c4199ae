<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form
          class="ad-form-query"
          :inline="true"
          :model="Filter"
          @submit.native.prevent>
        </el-form>
      </template>
      <!--列表-->
      <ces-table ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='dahuixionglist'
                @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
           :tableCols='tableCols' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
        </el-table-column>
         <template slot='extentbtn'>
              <el-button-group>
              <el-button type="primary" @click="onSearch">刷新</el-button>
              <el-button type="primary" @click="addButton">添加店铺</el-button>
            </el-button-group>
          </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getjSpeedDriveList"
        />
      </template>


      <el-drawer title="编辑店铺" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit"  @click="onEditSubmit" />
      </div>
    </el-drawer>
    <el-drawer title="新增店铺" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addVisible"
    direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
<form-create :rule="autoformAdd.rule1" v-model="autoformAdd.fApi" :option="autoformAdd.options"/>
<div class="drawer-footer">
<el-button @click.native="addVisible = false">取消</el-button>
<my-confirm-button type="submit"  @click="onaddSubmit" />
</div>
</el-drawer>
    </my-container>
  </template>
  <script>

  import {getDmShopList as getLossOffFee,addDmShop as editLossOffFee,deleteDmShop} from '@/api/bookkeeper/reportdayV2'
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatTime } from "@/utils";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import {formatPlatform} from "@/utils/tools";
  import { ruleShop } from '@/utils/formruletools'
  const tableCols = [
  { istrue: true, prop: 'shopName', label: '店铺', width: '500', sortable: 'custom' },
  { istrue: true, prop: 'qyDate', label: '启用时间', width: '300', sortable: 'custom' },
  { istrue: true, prop: 'isZbShop', label: '是否直播店铺', width: '300', sortable: 'custom',formatter:(row)=>row.isZbShop == 1 ? "是" :"否"},
  //{istrue:true,prop:'scale',label:'快递均价', width:'400',sortable:'custom',tipmesg:'快递均价',formatter:(row)=> !row.scale?" ": row.scale},
  { istrue: true, type: "button", label: '操作', width: "auto", btnList: [{ label: "编辑", handle: (that, row) => that.onEditMethod(row) }, { label: "删除", handle: (that, row) => that.deleteButton(row) }] }
];
  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
    data() {
      return {
        that:this,
        editLoading:false,
        addVisible:false,
        Filter: {


        },
        shopList:[],
        userList:[],
        groupList:[],
        dahuixionglist: [],
        tableCols:tableCols,
        total: 0,
        summaryarry:{},
        pager:{OrderBy:"shopName",IsAsc:false},
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        //
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
        platform:0,
        yearMonth:"",
        editVisible:false,
        autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
        autoformAdd:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      };
    },
    async mounted() {
    await this.initform();
      this.onSearch();
    },
    methods: {
    onEditMethod(row) {
      this.EditButton(row);
    },
    addButton(){
      this.addVisible=true;
    },
async onaddSubmit(){
      await this.autoformAdd.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoformAdd.fApi.formData();
          const res = await editLossOffFee(formData);
          await this.autoformAdd.fApi.resetFields()
          if(res.success){
            this.$message.success('添加成功！');
            this.getjSpeedDriveList();
            this.addVisible=false;
          }
        }else{}
     })
     },
    EditButton(row){
      this.editVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
          this.$nextTick(async() =>{
      await this.autoform.fApi.setValue(row)
      })
        },
        async onEditSubmit() {
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editLossOffFee(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
             this.getjSpeedDriveList();
            this.editVisible=false;
          }
        }else{}
     })
    },
    async initform() {
      this.autoform.rule = [
          { type: 'select', field: 'shopId', title: '店铺', value: '', ...await ruleShop(6), props: { filterable: true, clearable: true } },
          { type: 'select', field: 'isZbShop', title: '是否直播店铺', value: '',...{options:[{label:"是",value:1},{label:"否",value:0}]}, props: { filterable: true, clearable: true } },

          {
            type: "DatePicker",
            field: "qyDate",
            title: "启用时间",
            value: "", // 单选日期默认值为字符串
            props: {
              type: "date", // 修改为单选日期
              format: "yyyy-MM-dd", // 日期格式
              placeholder: "请选择启用时间",
            },
            validate: [
              { required: true, message: "请选择启用时间", trigger: "change" }
            ]
          }
        ],
        this.autoformAdd.rule1 = [
          { type: 'select', field: 'shopId', title: '店铺', value: '', ...await ruleShop(6), props: { filterable: true, clearable: true } },
          {
            type: "DatePicker",
            field: "qyDate",
            title: "启用时间",
            value: "", // 单选日期默认值为字符串
            props: {
              type: "date", // 修改为单选日期
              format: "yyyy-MM-dd", // 日期格式
              placeholder: "请选择启用时间",
            },
            validate: [
              { required: true, message: "请选择启用时间", trigger: "change" }
            ]
          }
        ]
    },
    async deleteButton(row){
        var that=this;
        this.$confirm("此操作将删除此数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async() => {
       let del= await deleteDmShop({shopId:row.shopId})

       if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onRefresh()
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
          });
      },

      setplatform(platform){
  this.platform=platform;
  },
      sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        this.onSearch();
      },
      onRefresh(){
          this.onSearch()
      },
      onSearch(){
         this.$refs.pager.setPage(1);
         this.getjSpeedDriveList();
      },
      async getjSpeedDriveList(){
        this.Filter.proCode=null;

        this.Filter.promotePlan=null;
        this.Filter.startAccountDate=null;
           this.Filter.endAccountDate=null;
        if(this.Filter.UseDate){
          this.Filter.startAccountDate=this.Filter.UseDate[0];
           this.Filter.endAccountDate=this.Filter.UseDate[1];
         }

         if(/^\d+$/.test(this.Filter.BatchNumber)==false&&this.Filter.BatchNumber!=null&&this.Filter.BatchNumber!=""){
          this.$message.error('请输入正确的批次号！！！！！');
          this.Filter.BatchNumber=null;
          return;
         }
         this.Filter.platform=this.platform;
        const para = {...this.Filter};
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
          isCrossBorder: 0

        };

        console.log(para)

        this.listLoading = true;
        const res = await getLossOffFee(params);
        console.log(res)
        this.listLoading = false;
        console.log(res.data.list)
        //console.log(res.data.summary)

        this.total = res.data.total
        this.dahuixionglist = res.data.list;
        this.dahuixionglist.forEach(f=>{
          f.qyDate = dayjs(f.qyDate).format('YYYY-MM-DD')
        })
        this.summaryarry=res.data.summary;
      },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
