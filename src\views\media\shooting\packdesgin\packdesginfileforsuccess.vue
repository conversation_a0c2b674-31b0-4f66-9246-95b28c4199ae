<template>
<el-row> 
    <div class="cgxbt">
                <span>{{title}}</span><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {{retdata.length}}</span>
                <span style="float: right; line-height: 22px">
                    <div style=" display: inline-flex;">
                    <el-upload ref="upload"
                            class="inline-block"
                            action ="#" 
                            :auto-upload="true" 
                            :multiple="true" 
                            :limit="limit" 
                            :show-file-list ="false"
                            :accept ="accepttyes"
                            :http-request="UpSuccessload"
                            :on-exceed ="exceed"
                            :file-list="retdata" 
                            :islook="islook"
                            >
                            <el-button size="mini" type="primary" :disabled="islook" >上传文件 </el-button> 
                        </el-upload>  
                        <span>&nbsp;</span>
                        <el-button size="mini" type="primary" :disabled="islook" @click="downfileall">一键下载  </el-button>
                        </div>
                  </span >
              </div>
              <div class="wjnrq">
                <draggablevue v-model="retdata" :scroll="true" animation="300"  @update="dataUpdate">
                    <transition-group> 
                    <div v-for="(i,index) in retdata"  :key="i.uid">
                        <el-tooltip   effect="dark" :content="i.fileName" placement="left">
                            <div class="wjdnr" @click="imgclick(i.url,retdata[index],index)"> {{ i.fileName }} </div>
                        </el-tooltip>
                    <div class="dnrzt"></div>
                    <div class="dnrxz"> <el-link :underline="false" type="primary"  @click="downfile(i)"  ><i :class="i.filestaus==2?'el-icon-download':'el-icon-warning'"></i></el-link></div>
                    <div class="dnrsc"> <el-link :underline="false" type="danger"   @click="removefile(i)"><i class="el-icon-delete"></i ></el-link></div>
                    </div>
                    </transition-group>
                </draggablevue>
              </div>
        <el-image-viewer v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList"  :wrapperClosable="false" :on-close="closeFunc" style="z-index:9999;" />
         <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"  :append-to-body="true" >
        <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeVideoPlyer">关闭</el-button>
        </span>
    </el-dialog>
</el-row>
</template>
<script>
import draggablevue from 'vuedraggable' 
import MyContainer from "@/components/my-container";
import ElImageViewer from '@/views/media/shooting/imageviewer.vue'; 
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
function pause(msec) {
    return new Promise(
        (resolve, reject) => {
            setTimeout(resolve, msec || 500);
        }
    );
}
export default {
    components: { MyContainer,draggablevue,ElImageViewer,videoplayer}, 
    props:{
        uploadInfo:{ type: Array, default:[] }, 
        accepttyes:{ type: String, default:"*" },
        uptype:{ type: String, default:"other" },
        title:{ type: String, default:"other" },
        limit:{ type: Number, default: 100000  },
        delfunction:{ type: Function, default: null},
        islook:{ type: Boolean, default:false },
    },
    data() {
        return {
            retdata:[],
            deldata:[], 
            uploading:false,
            imgindex:0,
            icontype: 'primary',
            imgList: [],
            showGoodsImage: false,
            IsChang:false,
            dialogVisible:false,
            videoplayerReload:false
        };
    },
    async mounted() {   
        console.log("111",this.uploadInfo)
        this.retdata = this.uploadInfo; 
        this.deldata = [];  
    },  
    methods: {
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        async downfileall(){
            for(let num in this.retdata)
            { 
                if(this.retdata[num].filestaus ==2  ){
                    await pause(500);
                    await this.downfile(this.retdata[num]);
                }  
            }
        },
        dataUpdate(){
            this.IsChang =true;
        },
        getChange(){ 
            return this.islook ? false:this.IsChang;
        },
        //获取返回值
        getReturns(){ 
            var curdata= [];
            this.retdata.forEach(function(item){
                //filestaus 0 新增，1移除，2原来的文件
                curdata.push({ 
                    fileName:item.fileName
                    ,url:item.url
                    ,OutComeId:item.outComeId
                    ,uid:item.uid
                    ,filestaus:item.filestaus
                    ,file: item.file,
                })
            });

            this.deldata.forEach(function(item){
                //filestaus 0 新增，1移除，2原来的文件
                curdata.push({ 
                    fileName:item.fileName
                    ,url:item.url
                    ,OutComeId:item.outComeId
                    ,uid:item.uid
                    ,filestaus:item.filestaus
                    ,file: item.file,
                })
            });

            return {success:true ,data :curdata};
        },
        //下载文件
        async downfile(file)
        { 
            if(file.filestaus!=2)
                return;
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let blob = this.response;
                    console.log(blob);
                    // 转换一个blob链接
                    // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                    // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                    let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                    // 视频的type是video/mp4，图片是image/jpeg
                    // 01.创建a标签
                    let a = document.createElement('a');
                    // 02.给a标签的属性download设定名称
                    a.download = file.fileName;
                    // 03.设置下载的文件名
                    a.href = downLoadUrl;
                    // 04.对a标签做一个隐藏处理
                    a.style.display = 'none';
                    // 05.向文档中添加a标签
                    document.body.appendChild(a);
                    // 06.启动点击事件
                    a.click();
                    // 07.下载完毕删除此标签
                    a.remove();
                };
            };
            xhr.send();
        },
        //移除文件
        async removefile(file){
            this.IsChang =true;
            //发出父级页面移除请求
            if(this.delfunction){
                await this.delfunction(file);
            }
            for(let num in this.retdata)
            {
                if(this.retdata[num].uid==file.uid){
                    //原上传文件，移除到删除列表
                    if(this.retdata[num].outComeId > 0){
                        //打算删除标记
                        this.retdata[num].filestaus = 1;
                        this.deldata.push(this.retdata[num]);
                    }
                    this.retdata.splice(num,1)
                }
            }
        },
        //上传超出提出
        exceed(){
            this.$message({ message: "超出上传数量限制", type: "warning" });
        },
        //上传方法
        async UpSuccessload(file){
            this.IsChang =true;
            this.uploading = true; 
            this.retdata.push({
                fileName:file.file.name
                ,url:""
                ,outComeId:0
                ,uid:file.file.uid
                ,filestaus:0
                ,file: file.file
            });
            this.uploading = false; 
        }, 
        imgclick(e,data,index){ 
            //判断当前是 图片，还是视频，还是不可预览文件 
            if(this.uptype =="imgtype" ){   
                this.imgList = [];
                for(let num in this.retdata)
                {  
                    if(this.retdata[num].filestaus==2)
                        this.imgList.push(this.retdata[num].url); 
                }  
                this.imgindex=index;
                this.showGoodsImage = true;
            }
            else if(this.uptype =="rartype" ){
                //不可预览
            }
            else if(this.uptype =="psdtype" ){
                 //不可预览
            }
            else if(this.uptype =="vediotype" ){
                 //视频预览
                 this.playVideo(data.url);
            } 
            return; 
        }, 
        
        //完成表单界面关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        }, 
    },
};
</script>
<style  lang="scss" scoped>  
  .dnrzt,
  .dnrxz,
  .dnrsc {
    width: 25px;
    display: inline-block;
    text-align: right;
    font-weight: bold;
    line-height: 20px;
    overflow: hidden;
    white-space: nowrap;
  }
</style>