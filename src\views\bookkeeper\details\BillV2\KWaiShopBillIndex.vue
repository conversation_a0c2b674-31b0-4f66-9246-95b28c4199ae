<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="快手账单" name="first1" style="height: 100%">
        <KWaiShopBill ref="refKWaiShopBill" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="快手资金账单" name="first2" style="height: 100%" lazy>
        <KWaiShopFundStatement ref="refKWaiShopFundStatement" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="快手小额打款" name="first3" style="height: 100%" lazy>
        <KWaiShopPettyPayment ref="refKWaiShopPettyPayment" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="快手退货补运费" name="first4" style="height: 100%" lazy>
        <KWaiShopReturnFreight ref="refKWaiShopReturnFreight" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="快手保证金" name="first5" style="height: 100%" lazy>
        <KWaiShopMargin ref="refKWaiShopMargin" style="height: 100%" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import KWaiShopBill from "./KWaiShopBill.vue";
import KWaiShopFundStatement from "./KWaiShopFundStatement.vue";
import KWaiShopPettyPayment from "./KWaiShopPettyPayment.vue";
import KWaiShopReturnFreight from "./KWaiShopReturnFreight.vue";
import KWaiShopMargin from "./KWaiShopMargin.vue";
export default {
  name: "videoBillIndex",
  components: {
    MyContainer, KWaiShopBill, KWaiShopFundStatement, KWaiShopPettyPayment,KWaiShopReturnFreight,KWaiShopMargin
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
