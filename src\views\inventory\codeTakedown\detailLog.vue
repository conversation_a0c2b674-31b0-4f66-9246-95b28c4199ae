<template>
  <MyContainer>
    <template #header>
    </template>
    <div style="height: 100%;width: 100%;" v-loading="loading">
      <el-table :data="tableData" border stripe height="360" style="width: 100%"
        :default-sort="{ prop: 'createdTime', order: 'descending' }">
        <el-table-column prop="execStatusStr" label="执行状态" width="80" :show-overflow-tooltip="true" tooltip="true" :resizable="false">
        </el-table-column>
        <el-table-column prop="execResult" label="执行结果" :show-overflow-tooltip="true" tooltip="true">
        </el-table-column>
        <el-table-column prop="syncTime" label="导入时间" width="155" :show-overflow-tooltip="true" tooltip="true" :resizable="false">
        </el-table-column>
        <el-table-column prop="createdTime" label="操作时间" width="155" sortable :show-overflow-tooltip="true" tooltip="true" :resizable="false">
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getGoodsBanDetailLog } from "@/api/inventory/basicgoods"
export default {
  name: "detailLog",
  props: {
    logsid: {
      type: Number,
      default() {
        return 0;
      }
    }
  },
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      tableData: [],
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const { data, success } = await getGoodsBanDetailLog({ id: this.logsid ? this.logsid : null });
      this.loading = false
      if (success) {
        this.tableData = data
      } else {
        this.$message.error('获取列表失败')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
