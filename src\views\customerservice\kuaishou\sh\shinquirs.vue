<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading" :summaryarry="summaryarry">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="filter.snick" placeholder="客服昵称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model.trim="filter.sdate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date">
                        </datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onImportSyj"  v-if="checkPermission(['api:Customerservice:KuaiShouInquirs:ImportKuaiShouInquirsAsync1'])">导入</el-button>
                    <el-button type="primary" @click="onImportSyjModel">下载模板</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getinquirsList" />
        </template>
        <!-- <el-dialog title="京东客服人员咨询数据" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-form ref="improtGroupForm" :model="improtGroupForm" label-width="55px" label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                            :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                                @click="onSubmitupload2">上传</my-confirm-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog> -->
        
        <el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="600px" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false"  action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleData = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getKuaiShouInquirsPageList, deleteKuaiShouInquirsAsync, importKuaiShouInquirsAsync
} from '@/api/customerservice/kuaishouinquirs'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
const tableCols = [
    //{ istrue: false, prop: 'id', label: 'id', width: '160', sortable: 'custom', display: false, },
    { istrue: true, prop: 'shopName', label: '店铺', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'snick', label: '昵称', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'inquireCount', label: '咨询人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'artificialConversation', label: '人工会话量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'conversation3MinRate', label: '三分钟回复率（会话）', width: '100', sortable: 'custom', formatter: (row) => (row.conversation3MinRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'customerService3MinRate', label: '三分钟回复率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.customerService3MinRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'noServiceRate', label: '不服务率', width: '80', sortable: 'custom', formatter: (row) => (row.noServiceRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'artificialAvgReply', label: '人工平均回复时长', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'conversationNiceCommentRate', label: '好评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationNiceCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'niceCommentRate', label: '好评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.niceCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'badCommentRate', label: '差评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.badCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'centreCommentRate', label: '中评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.centreCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'imBadRate', label: 'IM不满意率（人维度）', width: '100', sortable: 'custom', formatter: (row) => (row.imBadRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'changePlatformRate', label: '转平台服务率', width: '120', sortable: 'custom', formatter: (row) => (row.changePlatformRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'conversationCentreCommentRate', label: '中评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationCentreCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'conversationBadCommentRate', label: '差评率（会话）', width: '130', sortable: 'custom', formatter: (row) => (row.conversationBadCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'inviteConversationCount', label: '邀评会话数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'inviteCommentRate', label: '邀评率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.inviteCommentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'commentRate', label: '评价率（人维度）', width: '130', sortable: 'custom', formatter: (row) => (row.commentRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'commentConversationCount', label: '评价会话数', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'badCommentCount', label: '差评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'centreComment', label: '中评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'niceCommentCount', label: '好评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'askPrice', label: '询单客单价', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'askOrderRate', label: '询单转化率', width: '100', sortable: 'custom', formatter: (row) => (row.askOrderRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'salePrice', label: '客服销售额', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'commentCount', label: '评价人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'placeOrderCount', label: '下单人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'payOrderCount', label: '支付人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'inviteCommentCount', label: '邀评人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'askOrderCount', label: '询单人数', width: '80', sortable: 'custom'},
    { istrue: true, prop: 'sdate', label: '日期', width: '95', sortable: 'custom', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'createdTime', label: '导入时间', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') },
    { istrue: true, prop: 'batchNumber', label: '导入批次', width: '170', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "115",
        btnList: [
            { label: "删除", handle: (that, row) => that.deleteBatch(row, 0) },
            { label: "删除批次", handle: (that, row) => that.deleteBatch(row, 1) }
        ]
    }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            filter: {
                inquirsType: 1,
            },
            shopList: [],
            userList: [],
            inquirslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "sdate", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            improtGroupForm: {
            },

            dialogVisibleData: false, //导入
            uploadLoading: false,
        };
    },
    async mounted() {
        await this.getAllShopList();
    },
    methods: {
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 14)
                    this.shopList.push(f);
            });
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsList();
        },
        async getinquirsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getKuaiShouInquirsPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async deleteBatch(row, type) {
            var that = this;
            this.$confirm("此操作将删除此" + (type == 1 ? "批次" : "") + "客服人员咨询的数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let del = await deleteKuaiShouInquirsAsync({ idOrBatchNum: (type == 1 ? row.batchNumber : row.id), type: type });
                if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onSearch();
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
        },
        onImportSyjModel() {
            window.open("/static/excel/customerservice/快手咨询数据导入模板.xlsx", "_blank");
        },
        async onImportSyj() {
            this.dialogVisibleData = true;

        },

        async uploadFile () {
            if (this.fileList.length ==0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            form.append("inquirsType", 1);
            form.append("groupType",1);
            var res = await importKuaiShouInquirsAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleData = false;
            this.fileList = [];
        },
        async uploadChange (file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove (file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
