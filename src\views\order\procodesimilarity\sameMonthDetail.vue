<template>
  <my-container v-loading="pageLoading" style="height:80%;">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
          <el-date-picker style="width:220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" :clearable="false"
            :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="filter.platform" style="width:110px;" placeholder="请选择" :clearable="true"
            :collapse-tags="true" filterable>
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select style="width:180px;" v-model="filter.shopCode" placeholder="请选择" @change="onSearch"
            :clearable="true" :collapse-tags="true" filterable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select v-model="filter.groupId" style="width:120px;" placeholder="请选择" :clearable="true"
            :collapse-tags="true" filterable @change="onSearch">
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品ID:">
          <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width:150px;" @change="onSearch" />
        </el-form-item>
        <el-tag type="danger" size="large" effect="light"
          style="margin-left:30px;height:30px;line-height:30px;border:none;">
          提示：红色字体的行是主链接（即销量最高的链接）
        </el-tag>
      </el-form>

    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
      :summaryarry="summaryarryShow" :tableData='list' :tableCols='tableCols' :isSelection="false" :tablefixed="true"
      :tableHandles='tableHandles' :isSelectColumn="true" @select="selsChange" :loading="listLoading"
      :customRowStyle="customRowStyle" :headerCellStyle="headerCellStyle" @cellclick="cellclick">
      <el-table-column v-if="checkPermission('productnewpermission')" sortable prop="orderCount" :width="500" label="表格" fixed="right">
        <template slot-scope="props">
          <el-table :data="props.row.series" :show-header="false" border style="width: 100%">
            <el-table-column prop="name" label="姓名" width="80"></el-table-column>
            <el-table-column label="7天数据" width="auto">
              <template slot-scope="scope">
                <el-col :span="3" v-for="(ii,num) in scope.row.data" :key="num">
                  <template>
                    <div v-if="scope.row['name']=='广告访客量'" @click="clicknum(scope.row,ii)" style="color: red;">{{ii}}
                    </div>
                    <div v-else> {{ii}} </div>
                  </template>
                </el-col>
                <!-- <span v-for="(ii,num) in scope.row.data" :key="num">
                                    <span v-if="num <= 2" >{{ii}}</span>
                                    <el-button :span="3" type="text" size="mini" style="padding: 8px;" @click="clicknum(ii)">{{ii}}</el-button>
                                </span> -->

              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermission('productnewpermission')" width="auto" label="图表" fixed="right">
        <template slot-scope="scope">
          <div style="height: 120px;width:100%;margin-left: -20px;" :ref="'echarts'+scope.row.proCode"
            v-loading="echartsLoading"></div>
        </template>
      </el-table-column>

    </ces-table>



    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" append-to-body width="80%"
      v-dialogDrag>
      <span>
        <template>
          <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
            <el-row>
              <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                <el-form-item label="日期:">
                  <el-date-picker style="width: 260px" v-model="detailfilter.timerange" type="daterange"
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" :clearable="false"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-form-item>
                  <el-button type="primary" @click="getecharts">刷新</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </span>
      <span>
        <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>


    <el-dialog :visible.sync="detailPro.visible" :show-close="false" width="45%" v-dialogDrag height="700"
      @close="detailPro.pagerStyle={};" append-to-body>
      <el-button type="primary" @click="onSearchDetailPro">查询</el-button>
      <el-button type="primary" @click="showNextPro">下一个</el-button>
      <div style="margin-bottom:10px;margin-top:5px;">
        <el-descriptions :column="3" size="mini" border>
          <el-descriptions-item label="平台">{{myformatPlatform(detailPro.selRow.platform)}}</el-descriptions-item>
          <el-descriptions-item label="店铺">{{detailPro.selRow.shopName}}</el-descriptions-item>
          <el-descriptions-item label="运营组">{{ selGroupName }}</el-descriptions-item>
          <el-descriptions-item label="产品ID">
            <div v-html="myformatLinkProCode(detailPro.selRow.platform,detailPro.selRow.proCode)">
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="近30天销量">{{ detailPro.selRow.salesQty }}</el-descriptions-item>
          <el-descriptions-item label="编码数">{{ detailPro.selRow.goodsCodeQty }}</el-descriptions-item>
          <el-descriptions-item label="相似编码数">{{ detailPro.selRow.goodsCodeQtySame }}</el-descriptions-item>
          <el-descriptions-item label="相似度(%)">{{ detailPro.selRow.similarity }}</el-descriptions-item>
          <el-descriptions-item label="相似编码库存资金">{{ detailPro.selRow.invAmountSame }}</el-descriptions-item>
          <el-descriptions-item label="非相似编码库存资金">{{ detailPro.selRow.invAmountDiff }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <ces-table ref="tableDetailPro" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchangeDetailPro'
        :tableData='detailPro.list' :tableCols='detailPro.tableCols' :isSelection="false"
        :summaryarry="summaryarryDetailPro" :tableHandles='detailPro.tableHandles' :isSelectColumn="false"
        @select="selsChangeDetailPro" style="height:360px;" :loading="detailPro.listLoading">
      </ces-table>

      <my-pagination ref="pagerDetailPro" :total="detailPro.total" :checked-count="detailPro.sels.length"
        @get-page="getlistDetailPro" :style="detailPro.pagerStyle" />
    </el-dialog>
  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import checkPermission from '@/utils/permission';
import dayjs from "dayjs";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, formatLinkProCode, formatmoney } from "@/utils/tools";
import { queryGuardProductNewsis } from '@/api/operatemanage/base/product'
import {
  getProCodeSimilarityDetailSummary,
  exportProCodeSimilarityDetailList,
  pageProCodeSimilarityForMonthDetail
} from "@/api/order/procodesimilarity";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import * as echarts from 'echarts'
import buschar from '@/components/Bus/buschar'

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
  return formatmoney(Math.abs(value) > 1 ? Math.round(value) : Math.round(value, 1));
};

//日报列
const dayReportCols = [
  { istrue: true, prop: 'orderCountDayReport', label: '订单量', sortable: 'custom', permission: "prosameprofit", width: '70', formatter: (row) => !row.orderCountDayReport ? " " : row.orderCountDayReport },
  { istrue: true, prop: 'allMarketingCost', label: '总广告费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.allMarketingCost ? " " : row.allMarketingCost?.toFixed(2) },
];

//商品客户咨询列
const customerCols = [
  { istrue: true, prop: 'inquiries', label: '咨询量', type: 'custom', tipmesg: '链接在查询日期范围内的顾客咨询量之和', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiries ? " " : row.inquiries },
  { istrue: true, prop: 'inquiriesSuccessRate', label: '转化率', type: 'custom', tipmesg: '成功的咨询量/总咨询量', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiriesSuccessRate ? " " : (row.inquiriesSuccessRate * 100).toFixed(2) + '%' },

];

const tableCols = [
  { istrue: true, fixed: true, prop: 'platform', label: '平台', width: '80', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, fixed: true, prop: 'shopCode', label: '店铺', width: '100', sortable: 'custom', formatter: (row) => row.shopName },
  { istrue: true, fixed: true, prop: 'groupId', label: '运营组', width: '90', sortable: 'custom', formatter: (row) => row.groupName },
  { istrue: true, fixed: true, prop: 'proCode', label: '产品ID', width: '150', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, fixed: true, prop: 'onTime', label: '上架时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.onTime, "YYYY-MM-DD") },
].concat(dayReportCols).concat(customerCols);

const tableColsProGoods = [
  { istrue: true, prop: 'isSame', label: '是否相似', width: '100', sortable: 'custom', type: 'switch', isDisabled: (row) => true },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '130', sortable: 'custom' },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'goodsImage', label: '图片', width: '60', sortable: 'custom', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
  { istrue: true, prop: 'invAmount', label: '编码库存资金', width: '100', sortable: 'custom', formatter: (row) => myformatmoney(row.invAmount) },
  { istrue: true, prop: 'invAmountPredict', label: '编码库存资金预估', width: '140', sortable: 'custom', formatter: (row) => myformatmoney(row.invAmountPredict) },
];

const tableHandles = [
  // { label: "导出", handle: (that) => that.onExport() }
];
const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");

export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton, buschar },
  props: {
    filter: {
      parentId: null,
      proCode: null,
      status: null,
      platform: null,
      shopId: "",
      groupId: null,
      similarity: null,
      startDate: null,
      endDate: null,
      timerange: [startDate, endDate],
    },
    platformList: { type: Array, default: () => [] },
    groupList: { type: Array, default: () => [] }
  },
  data() {
    return {
      that: this,
      shopList: [],
      list: [],
      summaryarry: {},
      pager: { OrderBy: "orderCountDayReport", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      customRowStyle: function (data) {
        if (data.row.isMain) {
          return { color: 'red' };
        }
      },
      detailPro: {
        list: [],
        pager: { OrderBy: "isSame", IsAsc: false },
        tableCols: tableColsProGoods,
        tableHandles: [],
        total: 0,
        sels: [],
        listLoading: false,
        visible: false,
        filter: {
          detailParentId: null,
          goodsCode: null,
          isNewProductReport:true,
        },
        selRow: {},
        summaryarry: {},
        pagerStyle: {
          "margin-top": "33px"
        }
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      echartsLoading: false,
      detailfilter: {
        procode: null,
        platform: null,
        startTime: null,
        endTime: null,
        timerange: [star, endDate],
        isNewProductReport:true,
      },
      buscharDialog: { visible: false, title: "", data: [] },
    }
  },
  async mounted() {
    if (this.filter.platform)
      await this.onchangeplatform(this.filter.platform);
    else
      await this.getlist();
  },
  methods: {
    //设置店铺下拉
    async onchangeplatform(val) {
      const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 1000 });
      this.shopList = res.data.list || [];
      this.filter.shopCode = "";
      await this.onSearch();
    },
    clearFilter() {
      this.filter = {
        parentId: null,
        proCode: null,
        status: null,
        platform: null,
        shopId: "",
        similarity: null,
        timerange: this.filter.timerange,
        startDate: null,
        endDate: null
      };
    },
    selsChange: function (sels) {
      this.sels = sels;
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      this.filter.startDate = null;
      this.filter.endDate = null;
      this.filter.isNewProductReport=true;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      this.listLoading = true;
      var res = await pageProCodeSimilarityForMonthDetail(params);

      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      this.summaryarry = res.data.summary;
      const data = res.data.list || [];
      data.forEach(d => {
        d._loading = false
      })
      this.list = data;
      await this.getSummary();
      this.getEcharts()
    },
    getEcharts() {
      setTimeout(_ => {
        this.list.forEach(e => {
          let myChart = echarts.init(this.$refs['echarts' + e.proCode]);
          var series = []
          this.echartsLoading = true
          e.series.forEach(s => {
            if (s.name != '日期')
              series.push({ smooth: true, showSymbol: false, ...s })
          })
          this.echartsLoading = false
          myChart.setOption({
            legend: {
              show: false
            },
            grid: {
              left: "0",
              top: "6",
              right: "0",
              bottom: "0",
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              //不显示x轴线
              show: false,
              data: e.xAxis
            },
            yAxis: {
              type: 'value',
              show: false,
            },
            series: series
          });
          window.addEventListener("resize", () => {
            myChart.resize();
          });
        })
      }, 1000)
    },
    async cellclick(row, column, cell, event) {
      if (column.label == '图表') {
        this.detailfilter.procode = row.proCode
        this.detailfilter.platform = row.platform
        this.getecharts()
      }
    },
    async getecharts() {

      this.detailfilter.startTime = null;
      this.detailfilter.endTime = null;
      if (this.detailfilter.timerange) {
        this.detailfilter.startTime = this.detailfilter.timerange[0];
        this.detailfilter.endTime = this.detailfilter.timerange[1];
      }
      var params = { ...this.detailfilter }
      let that = this;
      const res = await queryGuardProductNewsis(params).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      })
      await this.$refs.buschar.initcharts()
    },
    async getSummary() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      debugger;
      var res = await getProCodeSimilarityDetailSummary(params);;
      if (!res?.success) {
        return
      }
      this.summaryarry = res.data;
    },
    myformatLinkProCode(platform, proCode) {
      return formatLinkProCode(platform, proCode);
    },
    myformatPlatform(platform) {
      return formatPlatform(platform);
    },
    ///==产品明细 Start==========================================
    showDetailPro(row) {
      this.detailPro.visible = true;
      this.detailPro.selRow = row;
      this.detailPro.filter.detailParentId = row.id;
      setTimeout(async () => {
        await this.onSearchDetailPro();
      }, 500);
    },
    selsChangeDetailPro: function (sels) {
      this.detailPro.sels = sels;
    },
    async sortchangeDetailPro(column) {
      if (!column.order)
        this.detailPro.pager = {};
      else {
        var orderBy = column.prop;
        this.detailPro.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearchDetailPro();
    },
    //获取查询条件
    getConditionDetailPro() {
      var pager = this.$refs.pagerDetailPro.getPager();
      var page = this.detailPro.pager;
      const params = {
        ...pager,
        ...page,
        ...this.detailPro.filter
      }

      return params;
    },
    //查询第一页
    async onSearchDetailPro() {
      this.$refs.pagerDetailPro.setPage(1)
      await this.getlistDetailPro()
    },
    //分页查询
    async getlistDetailPro() {
      var params = this.getConditionDetailPro();
      if (params === false) {
        return;
      }
      this.detailPro.listLoading = true;
      var res = await pageProCodeSimilarityForMonthDetail(params);
      this.detailPro.listLoading = false;
      if (!res?.success) {
        return
      }
      this.detailPro.total = res.data.total;
      const data = res.data.list || [];
      data.forEach(d => {
        d._loading = false
      })
      this.detailPro.list = data;
      this.detailPro.summaryarry = res.data.summary;
    },
    showNextPro() {
      if (this.list && this.list.length > 0) {
        var nextRow = this.list[0];
        var findCur = false;
        this.list.forEach(item => {
          if (findCur) {
            findCur = false;
            nextRow = item;
          }
          if (item.id == this.detailPro.selRow.id) {
            findCur = true;
          }
        });
        this.detailPro.selRow = nextRow;
        this.showDetailPro(nextRow);
      }
    },
    //导出
    async onExport() {
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = {
        ...this.filter
      }
      if (params === false) {
        return;
      }
      var res = await exportProCodeSimilarityDetailList(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "系列相似产品数据_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    ///==产品明细 End  ==========================================
    //表头样式
    headerCellStyle(data) {
      if (data && data.column) {
        var isDayReportCol = dayReportCols.find(a => a.prop == data.column.property);
        if (isDayReportCol) {
          return { color: '#F56C6C' }
        }
      }
      return null;
    },
  },
  computed: {
    selGroupName() {
      var name = this.groupList?.find(a => a.key == this.detailPro.selRow.groupId)?.value;
      return name || "未知";
    },
    selPlatformName() {
      var name = this.platformList?.find(a => a.value == this.detailPro.selRow.platform)?.label;
      return name;
    },
    summaryarryShow() {
      var sum = {};
      if (this.summaryarry) {
        for (const key in this.summaryarry) {
          sum[key] = myformatmoney(this.summaryarry[key])
        }
      }
      return sum;
    },
    summaryarryDetailPro() {
      var sum = {};
      if (this.summaryarry) {
        for (const key in this.detailPro.summaryarry) {
          sum[key] = myformatmoney(this.detailPro.summaryarry[key])
        }
      }
      return sum;
    },
  },
  watch: {
    async "filter.platform"() {
      console.log("this.filter.platform", this.filter.platform)
      await this.onchangeplatform(this.filter.platform);
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}

::v-deep .el-table__fixed {
  pointer-events: auto;
}
</style>
