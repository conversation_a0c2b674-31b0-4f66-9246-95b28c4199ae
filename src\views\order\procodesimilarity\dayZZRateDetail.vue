<template>
    <my-container>
        <template #header>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :tablefixed="true"
            :tableHandles='tableHandles' :isSelectColumn="false" @select="selsChange" :loading="listLoading">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue"
import { getDayZZRateDetail } from "@/api/order/procodesimilarity"

export default {
    name: 'YunhanAdminDaysturnoverdetail',
    components: { MyContainer, MyConfirmButton, cesTable },
    props: {
        filter: {
            type: 1,
            seriesCode: null
        }
    },
    data() {
        return {
            that: this,
            list: [],
            pager: { OrderBy: "goodsCode", IsAsc: true },
            tableCols: this.gettableCols(),
            tableHandles: [],
            summaryarry: {},
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
    },

    methods: {
        gettableCols() {
            return [
                { istrue: true, prop: 'goodsCode', label: '商品编码', width: '130', sortable: 'custom' },
                { istrue: true, prop: 'goodsName', label: '商品名称', width: '230', formatter: (row) => !row.goodsName ? " " : row.goodsName },
                { istrue: true, prop: 'invAmount', label: '库存资金', width: 'auto', sortable: 'custom' },
                { istrue: true, prop: 'masterStock', label: '库存数量', width: 'auto', sortable: 'custom' },
                { istrue: true, prop: 'salesDay1', label: '近1天销量', width: 'auto', sortable: 'custom', display: () => this.showClo(1), },
                { istrue: true, prop: 'salesDay3', label: '近3天销量', width: 'auto', sortable: 'custom', display: () => this.showClo(3), },
                { istrue: true, prop: 'oneDayZZRate', label: '近1天周转天数', width: 'auto', sortable: 'custom', formatter: (row) => row.oneDayZZRate.toFixed(2), display: () => this.showClo(1), },
                { istrue: true, prop: 'threeDayZZRate', label: '近3天周转天数', width: 'auto', sortable: 'custom', formatter: (row) => row.threeDayZZRate.toFixed(2), display: () => this.showClo(3), },
            ];
        },
        showClo(type){
            return type==this.filter.type
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...this.pager, ...this.filter }
            this.listLoading = true
            var res = await getDayZZRateDetail(params)
            this.listLoading = false
            if (!res?.success) return;
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data;
            this.summaryarry=res.data.summary;
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                var orderBy = column.prop;
                this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>