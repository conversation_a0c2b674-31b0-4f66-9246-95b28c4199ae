<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-date-picker style="width: 100px" v-model="filter.useMonth" type="month" format="yyyyMM"
                    value-format="yyyyMM" placeholder="月份"></el-date-picker>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-select filterable clearable v-model="filter.feeType" placeholder="数据类型" style="width: 160px;">
                    <el-option label="推广返还红包" value="拼多多推广返还红包" />
                    <el-option label="全站推广" value="拼多多全站推广" />
                    <el-option label="标准推广" value="拼多多标准推广" />
                    <el-option label="智能托管" value="拼多多智能托管" />
                    <el-option label="明星店铺" value="拼多多明星店铺" />
                </el-select>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <!-- <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopCode"></el-option>
                </el-select> -->
                <el-input v-model.trim="filter.shopName" placeholder="店铺名称" style="width:160px;" maxlength="40" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.productID" placeholder="商品ID" style="width:120px;" maxlength="40" />
            </el-button>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="importProps('推广返还红包')">导入推广返还红包</el-button>
            <el-button type="primary" @click="importProps('全站推广')">导入全站推广</el-button>
            <el-button type="primary" @click="importProps('标准推广')">导入标准推广</el-button>
            <el-button type="primary" @click="importProps('智能托管')">导入智能托管</el-button>
            <el-button type="primary" @click="importProps('明星店铺')">导入明星店铺</el-button>
            <el-button type="primary" @click="onComputPdd('v1')">计算拼多多推广费-工资</el-button>
            <el-button type="primary" @click="onComputPdd('v2')">计算拼多多推广费-参考</el-button>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='list'
            :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>



        <el-dialog :title="importTitle" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading"
            :close-on-click-modal="false">
            <div style="display: flex;flex-direction: column;justify-content: center;">

                <el-date-picker style="width: 120px;margin-top: 20px; margin-bottom: 20px;" v-model="importUseMonth"
                    type="month" format="yyyyMM" value-format="yyyyMM" placeholder="导入月份"></el-date-picker>

                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>

            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { formatWarehouse, formatTime, formatYesornoBool, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { Loading } from 'element-ui';
import { importPingduoduo23Async, getPingduoduoList, computePddTuiGuangFei } from '@/api/financial/yyfy'
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'feeType', label: '数据类型', sortable: 'custom', width: '100' },
    { istrue: true, fixed: true, prop: 'useMonth', label: '年月', sortable: 'custom', width: '100' },
    { istrue: true, fixed: true, prop: 'useTime', label: '数据时间', sortable: 'custom', width: '150' },
    { istrue: true, fixed: true, prop: 'shopCode', label: '店铺编号', sortable: 'custom', width: '100' },
    { istrue: true, fixed: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', width: '180' },
    { istrue: true, fixed: true, prop: 'productID', label: '商品ID', sortable: 'custom', width: '150' },
    { istrue: true, fixed: true, prop: 'useMoney', label: '花费(元)/交易金额', sortable: 'custom', width: '180' },
    { istrue: true, fixed: true, prop: 'conputeEndTime', label: '计算完成时间', sortable: 'custom', width: '150' },

];
export default {
    name: "yunyingfeepdd",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, },
    data() {
        return {
            that: this,
            filter: {
                useMonth: null,
                shopCode: null,
                productID: null,
            },
            list: [],
            shopList: [],
            userList: [],
            grouplist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: "useMoney", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            onExportLoading: false,

            fileList: [],
            importTitle: "",
            importFeeType: "",
            importUseMonth: null,
            importLoading: false,
            importVisible: false,
        };
    },
    async mounted() {
        //await this.onSearch()
        //await this.getShopList();
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            var res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
        },
        getParams() {
            if (!this.filter.useMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };

            return params;
        },
        async getList() {
            var that = this;
            const params = this.getParams();
            if (!params) {
                return false;
            }
            startLoading();
            const res = await getPingduoduoList(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.list = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            const params = this.getParams();
            if (!params) {
                return false;
            }
            this.onExportLoading = true;
            var res = await ExportMonthBusinessWiseManReport(params);
            this.onExportLoading = false;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音达人报表_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },

        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importUseMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            if (this.file == null) return this.$message.error('请上传文件')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("yearmonth", this.importUseMonth);
            form.append("feetype", this.importFeeType);
            this.importLoading = true
            let res = await importPingduoduo23Async(form)
            this.importLoading = false
            if (res?.success) {
                this.$message.success('正在后台导入中,请稍后刷新界面查看....')
                this.importVisible = false
            }
        },
        importProps(feetype) {
            this.importTitle = "导入-" + feetype
            this.importFeeType = feetype;
            this.fileList = []
            this.file = null
            //this.importUseMonth = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },


        async onComputPdd(version) {
            if (!this.filter.useMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }

            this.$confirm('确定要计算吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {

                const form = new FormData();
                form.append("useMonth", this.filter.useMonth);
                form.append("version", version);

                await computePddTuiGuangFei(form).then(({ success }) => {
                    if (success) {
                        this.$message.success('正在后台计算中,请稍后在各平台月报种查看....')
                    }
                }).catch(err => {
                    this.$message.error('计算失败，请刷新后重试')
                })

            }).catch(() => {

            });

        },

    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
