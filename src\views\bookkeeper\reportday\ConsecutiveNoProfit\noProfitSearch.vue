<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 160px" v-model="filter.yearMonthDay" type="date" placeholder="选择日期"
              :clearable="false" value-format="yyyyMMdd" @change="onSearch">
            </el-date-picker>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.platform" placeholder="平台" style="width: 120px"  @change="onchangeplatform">
              <el-option label="天猫" :value="1"> </el-option>
              <el-option label="拼多多" :value="2"> </el-option>
              <el-option label="阿里巴巴" :value="4"> </el-option>
              <el-option label="抖音" :value="6"> </el-option>
              <el-option label="京东" :value="7"> </el-option>
              <el-option label="淘工厂" :value="8"> </el-option>
              <el-option label="淘宝" :value="9"> </el-option>
              <el-option label="苏宁" :value="10"> </el-option>
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺" style="width: 160px" @change="onSearch">
              <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
              </el-option>
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 120px" @change="onSearch">
              <el-option key="无运营组" label="无运营组" :value="0"></el-option>
              <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>

          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
              style="width: 120px" @change="onSearch">
              <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button type="primary" style="margin-left: 10px; " @click="onSearch">查 询</el-button>
          <el-button v-if="filter.platform > 0 && checkPermission('api:bookkeeper:ContinuousProfitAnalysis:ContinuousNoProfitNoticeSaveAsync')" type="warning" style="margin-left: 30px;" @click="onSure">确 认</el-button>
        </el-button-group>
        <p></p>
        <el-radio-group v-model="filter.groupType" @change="changeCheck">
          <el-radio label="platform">平台</el-radio>
          <el-radio label="shopcode">店铺</el-radio>
          <el-radio label="groupid">运营组</el-radio>
          <el-radio label="operatespecialuserid">运营专员</el-radio>
        </el-radio-group>
      </div>
    </template>
    <el-tabs v-model="activeName" style="height: 94%" @tab-click="handleClick">
      <el-tab-pane label="图表展示" name="first1" style="height: 100%">
        <showChar ref="showChar" :filter="filter" style="height: 100%"></showChar>
      </el-tab-pane>
      <el-tab-pane label="记录展示" name="first2" style="height: 100%">
        <showTable ref="showTable" :filter="filter" style="height: 100%"></showTable>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import showChar from "./showChar.vue";
import showTable from "./showTable.vue";
import { getList as getshop  } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { continuousNoProfitNoticeSaveAsync } from "@/api/bookkeeper/continuousprofitanalysis"
import { formatTime } from "@/utils/tools";

export default {
  name: "ConsecutiveNoProfitSearchIndex",
  components: {
    MyContainer, showChar, showTable
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
      filter: {
        yearMonthDay: null,
        shopCode: null,
        groupId: null,
        operateSpecialUserId: null,
        groupType: 'platform',
        platform: null,
        currentPage: 1,
        pageSize: 50
      },
      shopList: [],
      grouplist: [],
      directorlist: []
    };
  },
  async mounted() {
    this.filter.yearMonthDay= formatTime(new Date(),"YYYYMMDD");
    await this.getShopList();
  },
  methods: {
    async getShopList() {

      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    onSearch() {
      if (this.activeName == "first1") {
        this.$refs.showChar.showChar();
      } else {
        this.$refs.showTable.getlist();
      }
    },
    changeCheck() {
      this.onSearch();
    },
    handleClick() {
      this.onSearch();
    },
    onSure() {
      let that = this;
      this.$confirm('是否确认?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(async () => {
        let params = { platform: that.filter.platform, yearMonthDay: that.filter.yearMonthDay };
        const res = await continuousNoProfitNoticeSaveAsync(params)
        if (!res?.success) { return }
        this.$message({ type: 'success', message: '已确认' });
      })
    },
    async onchangeplatform (val) { 
      if(val == ''){ 
        this.filter.shopCode=null;
        this.shopList=[]; 
        this.onSearch();
        return;
      }

      this.filter.platform = val;
      const res1 = await getshop({ platform: val, CurrentPage: 1, PageSize: 10000 });
      this.shopList = res1.data.list;

      this.filter.shopCode=null;
      this.onSearch();
    }
  },
};
</script>

<style lang="scss" scoped></style>
