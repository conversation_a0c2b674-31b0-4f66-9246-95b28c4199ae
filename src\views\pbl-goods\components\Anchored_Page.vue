<template>
    <MyContainer v-loading="loading">
        <template #header>
            <el-tooltip class="item" effect="dark" content="所修改--对应列头名称" placement="top-start">
                <el-select v-model="ruleForm.type" placeholder="请选择" @change="changeType" clearable>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-tooltip>
            <div>
                <el-button @click="addProps" style="margin:10px 0">新增一行</el-button>
            </div>
        </template>
        <div style="height: 400px;">
            <el-table :data="ruleForm.items" style="width: 100%" max-height="400">
                <el-table-column fixed prop="goodsCode" label="商品编码">
                    <template #default="{ row }">
                        <el-input v-model="row.goodsCode" placeholder="商品编码" maxlength="100" clearable />
                    </template>
                </el-table-column>
                <el-table-column prop="originalValue" label="原数据">
                    <template #default="{ row }">
                        <el-input v-model="row.originalValue" placeholder="原数据" maxlength="100" clearable />
                    </template>
                </el-table-column>
                <el-table-column prop="newValue" label="需修改成">
                    <template #default="{ row }">
                        <el-input v-model="row.newValue" placeholder="需修改成" maxlength="100" clearable />
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作">
                    <template #default="{ row, $index }">
                        <el-button type="text" @click="ruleForm.items.splice($index, 1)">删除</el-button>
                        <el-button type="text" @click="copyProps(row, $index)">复制</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- <uploadimgFile :accepttyes="accepttyes" :isImage="true" :uploadInfo="picture" :keys="[1, 1]"
            v-if="uploadimgFileVisable" @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
        </uploadimgFile> -->
        <div style="display: flex;justify-content: end;margin-top: 10px;">
            <el-button @click="close">取消</el-button>
            <el-button @click="submit" v-throttle="2000" type="primary">确认</el-button>
        </div>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import request from '@/utils/request'
import { log } from "mathjs";
export default {
    name: "Anchored_Page",
    components: {
        MyContainer, uploadimgFile
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableData: [],
            total: 0,
            loading: false,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            ruleForm: {
                type: '商品分类',
                items: [
                    {
                        goodsCode: '',
                        originalValue: '',
                        newValue: '',
                    }
                ],
            },
            // uploadimgFileVisable: false,
            options: [
                {
                    value: '商品名称+商品简称',
                    label: '商品名称+商品简称'
                },
                {
                    value: '商品分类',
                    label: '商品分类'
                },
                {
                    value: '款式编码',
                    label: '款式编码'
                },
            ],
            value: '',
            picture: []
        }
    },
    async mounted() {
        if (this.styleCode) {
            this.loading = true
            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/GetGoodCodesByStyleCode', { styleCode: this.styleCode })
            if (success) {
                data.forEach(item => {
                    item.originalValue = item.groupName
                    item.newValue = item.newValue || ''
                })
                this.ruleForm.items = data
            } else {
                this.$message.error('获取信息失败')
            }
            this.loading = false
        }
        // this.uploadimgFileVisable = true
    },
    methods: {
        changeType(e) {
            const map = {
                '商品名称+商品简称': 'goodsName+shortName',
                '商品分类': 'groupName',
                '款式编码': 'styleCode',
            }
            if (e && this.styleCode) {
                this.ruleForm.items.forEach((item, i) => {
                    if (e == '商品名称+商品简称') {
                        this.$set(item, 'originalValue', item.goodsName + '+' + item.shortName)
                    } else {
                        this.$set(item, 'originalValue', item[map[e]])
                    }
                })
            }
            this.$forceUpdate()
        },
        close() {
            this.$emit('close')
        },
        copyProps(row, i) {
            const val = JSON.parse(JSON.stringify(row))
            //在i的位置插入row
            this.ruleForm.items.splice(i + 1, 0, val)
        },
        addProps() {
            this.ruleForm.items.push({
                goodsCode: '',
                originalValue: '',
                newValue: '',
            })
        },
        async submit() {
            const arr = ['goodsCode', 'originalValue', 'newValue']
            const map = {
                goodsCode: '商品编码',
                originalValue: '原数据',
                newValue: '需修改成',
            }
            const flag = this.ruleForm.items.every((item, i) => {
                return arr.every(key => {
                    if (!item[key]) {
                        this.$message.error(`第${i + 1}行${map[key]}不能为空`)
                        return false
                    }
                    return true
                })
            })
            if (!flag) return
            const { success } = await request.post('/api/bookkeeper/pblGoodPdd/GoodsInfoChangeApply', this.ruleForm)
            if (success) {
                this.$message.success('提交成功')
                this.$emit('close')
                this.$emit('getList')
            }
        },
    }
}
</script>

<style scoped lang="scss"></style>