<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss"
                    startPlaceholder="调拨/归档时间" endPlaceholder="调拨/归档时间" />
                <el-select v-model="ListInfo.types" placeholder="状态" class="publicCss" clearable multiple
                    style="width: 130px;" collapse-tags>
                    <el-option label="调拨" :value="1" />
                    <el-option label="归档" :value="2" />
                </el-select>
                <el-select v-model="ListInfo.allotTypes" placeholder="调拨类型" class="publicCss" clearable multiple
                    style="width: 150px;" collapse-tags>
                    <el-option label="本调云" :value="0" />
                    <el-option label="云调本" :value="1" />
                    <el-option label="本调本" :value="2" />
                    <el-option label="云调云" :value="3" />
                </el-select>
                <el-select v-model="ListInfo.approveStatues" placeholder="审批状态" class="publicCss" clearable multiple
                    style="width: 140px;" collapse-tags>
                    <el-option label="待审批" value="待审批" />
                    <el-option label="同意" value="同意" />
                    <el-option label="拒绝" value="拒绝" />
                </el-select>
                <inputYunhan ref="styleCodes" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="系列编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 1)" title="系列编码" width="150px"
                    style="margin: 0 10px 0 0">
                </inputYunhan>
                <inputYunhan ref="goodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 2)" title="商品编码" width="150px"
                    style="margin: 0 10px 0 0">
                </inputYunhan>
                <chooseWareHouse v-model="ListInfo.allotOutWmsIds" class="publicCss" placeholder="调出仓" multiple
                    style="width: 170px;" />
                <chooseWareHouse v-model="ListInfo.allotInWmsIds" class="publicCss" placeholder="调入仓" multiple
                    style="width: 170px;" />
                <el-select v-model="ListInfo.brandIds" clearable filterable placeholder="请选择采购员" class="publicCss"
                    collapse-tags multiple style="width: 170px;">
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" id="20241213113316" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
            :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            :is-selection="false" :is-select-column="true" :is-index-fixed="false"
            style="width: 100%; margin: 0;height: 500px;" :isNeedExpend="false"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/GoodsAllot/'
import { mergeTableCols } from '@/utils/getCols'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { getAllProBrand, } from '@/api/inventory/warehouse'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, chooseWareHouse
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            brandlist: []
        }
    },
    async mounted() {
        this.init();
        await this.getCol();
        await this.getList()
    },
    methods: {
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        proCodeCallback(e, val) {
            if (val == 1) {
                this.ListInfo.styleCodes = e
            } else {
                this.ListInfo.goodsCodes = e
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                const arr = ['preAllotQty', 'preAllotTime', 'type', 'allotType']
                data.forEach(item => {
                    if (!arr.includes(item.prop)) {
                        item.width = 'auto'
                    }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                }
            } catch (error) {
                console.log(error);
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;
    flex-wrap: wrap;

    .publicCss {
        width: 190px;
        margin: 0 5px 5px 0;
    }
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}
</style>
