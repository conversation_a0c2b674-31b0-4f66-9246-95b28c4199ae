<template>
    <MyContainer>

        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="阳光隔热膜报价明细" name="first" style="height: 100%;"
                v-if="checkPermission('yggrmQuoteDetails')">
                <yggrmQuoteDetails />
            </el-tab-pane>
            <el-tab-pane label="透明桌垫报价明细" name="second" style="height: 100%;" :lazy="true"
                v-if="checkPermission('tmzdQuoteDetails')">
                <tmzdQuoteDetails />
            </el-tab-pane>
            <el-tab-pane label="皮桌垫报价明细" name="third" style="height: 100%;" :lazy="true"
                v-if="checkPermission('pzdQuoteDetails')">
                <pzdQuoteDetails />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import yggrmQuoteDetails from './components/yggrmQuoteDetails.vue';
import tmzdQuoteDetails from './components/tmzdQuoteDetails.vue';
import pzdQuoteDetails from './components/pzdQuoteDetails.vue';
export default {
    components: {
        MyContainer, yggrmQuoteDetails, tmzdQuoteDetails,pzdQuoteDetails
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>