<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="预调拨" name="first" style="height: 100%" lazy>
                <preallocationPage @close="close" @getList="getList" />
            </el-tab-pane>
            <el-tab-pane label="记录" name="fifth" style="height: 100%" lazy>
                <preallocationRecord @close="close" @getList="getList" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import preallocationPage from "./preallocationPage.vue";
import preallocationRecord from "./preallocationRecord.vue";
export default {
    components: {
        MyContainer, preallocationPage, preallocationRecord
    },
    data() {
        return {
            activeName: 'first',
        };
    },
    mounted() {

    },
    methods: {
        close() {
            this.$emit('close')
        },
        getList() {
            this.$emit('getList')
        }
    },
};
</script>

<style lang="scss" scoped></style>
