<template>
    <!-- <my-container> -->
    <div class="panel-group">
        <el-row type="flex" justify="flex-start">
            <el-col :span="3" class="card-panel-col" :style="styleall">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('shoppings')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">全平台</span>
                            </el-tooltip>
                        </div>
                        <div>￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontAll"
                                :duration="2000" class="card-panel-num" /></div>
                    </div>
                </div>
            </el-col>
            <el-col :span="3" class="card-panel-col" :style="styletian">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmonttian')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">天猫</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontTian"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="styletao">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmonttao')">
                    <!-- <div class="card-panel-icon-wrapper icon-people">
          <i class='el-icon-user-solid' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">淘宝</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontTao"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="stylepin">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('messages')">
                    <!-- <div class="card-panel-icon-wrapper icon-message">
          <i class='el-icon-s-opportunity' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">拼多多</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" content="未知" :start-val="0" :end-val="daySum.saleAmontPin"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="stylegong">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('purchases')">
                    <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">淘工厂</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontGong"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="styledy">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontDY')">
                    <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">抖音</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontDY" :duration="2000"
                            class="card-panel-num" />
                    </div>
                </div>
            </el-col>
            <el-col :span="3" class="card-panel-col" :style="styleJD">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontJD')">
                    <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">京东</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontJD" :duration="2000"
                            class="card-panel-num" />
                    </div>
                </div>
            </el-col>
            <el-col :span="3" class="card-panel-col" :style="styleJingXi">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontJingXi')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">京喜</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontJingXi" :duration="2000"
                            class="card-panel-num" />
                    </div>
                </div>
            </el-col>
            <el-col :span="3" class="card-panel-col" :style="styleJingXiNN">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontJingXiNN')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">京喜NN</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontJingXiNN" :duration="2000"
                            class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="styleAlibaba">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontAlibaba')">
                    <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">阿里巴巴</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontAlibaba"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>


            <el-col :span="3" class="card-panel-col" :style="styleSuNing">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontSuNing')">
                    <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">苏宁</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontSuNing"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="styleFenXiao">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontFenXiao')">
                    <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">分销</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontFenXiao"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <el-col :span="3" class="card-panel-col" :style="styleDYGX">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontDYGX')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">抖音供销</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontDYGX"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>
            <el-col :span="3" class="card-panel-col" :style="styleKWaiShop">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontKWaiShop')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">快手</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontKWaiShop"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>
            <el-col :span="3" class="card-panel-col" :style="styleVideo">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontWeChat')">
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">视频号</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontWeChat"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <!-- <el-col :span="3" class="card-panel-col" :style="styleSheIn">
                <div v-if="checkPermission('homepermission')" class="card-panel"
                    @click="handleSetLineChartData('saleAmontSheIn')">

                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            <el-tooltip class="item" effect="dark" content="近30天总销售金额" placement="top-start">
                                <span style="margin-right: 5px;">希音</span>
                            </el-tooltip>
                        </div>
                        ￥<count-to style="margin-right: 3px;" :start-val="0" :end-val="daySum.saleAmontSheIn"
                            :duration="2000" class="card-panel-num" />
                    </div>
                </div>
            </el-col> -->

            <!-- <el-col :span="3" class="card-panel-col"> -->

                <!-- <div class="card-panel-icon-wrapper icon-money">
          <i class='el-icon-s-data' style=""></i>
        </div> -->
                <!-- <span>
                                    <template>
                                        <el-form class="ad-form-query" :model="filterdetail" @submit.native.prevent>
                                            <el-row>
                                                <el-col :xs="24" :sm="24" :lg="24">
                                                    <el-form-item>
                                                        <el-date-picker style="width: 210px"
                                                            v-model="filterdetail.timerange" type="daterange"
                                                            format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                                            range-separator="至" start-placeholder="开始日期"
                                                            end-placeholder="结束日期" :clearable="false"
                                                            @change="getPie()"></el-date-picker>
                                                    </el-form-item>
                                                </el-col>
                                            </el-row>
                                        </el-form>
                                    </template>
                                </span> -->
                <!-- <div class="card-panel1">
                    <el-date-picker style="width: 205px;margin-left:5px; margin-top: 5px;" v-model="timerange" type="daterange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :clearable="false" @change="getChatInfos()"></el-date-picker>
                    <el-select v-model="isIgnoreSpecialProCode" placeholder="请选择" style="width: 205px;margin-left:5px; margin-top: 5px;" filterable @change="getChatInfos()">
                      <el-option v-for="item in hearlist" :key="item.label" :label="item.label" :value="item.value" />
                    </el-select>
                </div> -->
            <!-- </el-col> -->
        </el-row>
        <div class="groupClass" v-show="groupList.length > 0">
            <!-- <div class="btn-list">
                <div class="groupitem" v-for="item in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16]">
                        <el-link :type="item%11 == 0?'primary':'info'" :underline="false">主要链接
                            <br/>
                            15366
                        </el-link>
                </div>
            </div> -->
            <div class="card-container">
                <template v-if="!isloading">
                    <div class="controls left" v-show="scrollPosition > 0">
                        <!-- <el-button icon="el-icon-arrow-left" @click="scrollCards('left')"></el-button> -->
                        <el-link :underline="false" icon="el-icon-arrow-left" @click="scrollCards('left')"
                            style="font-size: 20px;"></el-link>
                    </div>
                    <div class="controls right" v-show="scrollPosition < maxScrollPosition">
                        <!-- <el-button icon="el-icon-arrow-right" @click="scrollCards('right')"></el-button> -->
                        <el-link :underline="false" icon="el-icon-arrow-right" @click="scrollCards('right')"
                            style="font-size: 20px;"></el-link>
                    </div>
                    <div class="cards-wrapper" ref="cardsWrapper">
                        <div class="cards" :style="{ transform: `translateX(-${scrollPosition * cardWidth}px)` }"
                            ref="cards">

                            <div v-for="(card, index) in groupList" class="card" :key="index"
                                :style="{ width: `${cardWidth}px` }">
                                <el-link :type="index == activeItme ? 'primary' : 'default'" :underline="false"
                                    style="text-align: center;" @click="changeGroup(index, card.groupId)">
                                    <div style="font-weight: 600;">{{ card.groupName }}</div>
                                    <!-- <div>￥{{ card.saleAmont }}</div> -->
                                    ￥<count-to style="margin-right: 10px;" :start-val="0" :end-val="card.saleAmont"
                                        :duration="2000" class="card-panel-num" />
                                </el-link>
                            </div>

                        </div>
                    </div>
                </template>
                <template v-else>
                    <div style="display: flex; width: 100%; height:60px; justify-content: center; align-items: center;">
                        <i class="el-icon-loading" />
                    </div>
                </template>

            </div>
        </div>
    </div>

    <!-- </my-container> -->
</template>

<script>
import MyContainer from "@/components/my-container";
import countTo from 'vue-count-to'
import dayjs from "dayjs";
import { getDayReportGroupSaleAmontAsync } from '@/api/bookkeeper/reportday'//日报趋势图
import { formatTime } from "@/utils";
const hearlist = [{ label: '查看全部', value: null }, { label: '查看普通ID', value: 1 }, { label: '查看特殊ID/店铺', value: 2 }];
const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'YunhanAdminIndex',
    components: { MyContainer, countTo },
    props: {
        daySum: {},
        // groupList:[],
    },
    data() {
        return {
            hearlist,
            isIgnoreSpecialProCode: null,
            isloading: true,
            pttyep: 'shoppings',
            activeItme: 0,
            timerange: [startTime, endTime],
            list: [],
            styleall: 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;',
            styletao: '',
            styletian: '',
            stylepin: '',
            stylegong: '',
            styledy: '',
            styleSheIn: '',
            styleJD: '',
            styleJingXi: '',
            styleJingXiNN: '',
            styleAlibaba: '',
            styleSuNing: '',
            styleFenXiao: '',
            styleDYGX: '',
            styleKWaiShop: '',
            styleVideo: '',
            stylegoodscodenum: '',
            stylegoodnumcurrentnum: '',
            styleamountpaid: '',
            stylecountprocode: '',
            stylerefundamont: '',
            styleordercount: '',
            styleguestunitprice: '',
            styleAverageExpressPrice: '',
            cards: this.groupList, // 卡片数据
            cardWidth: 120, // 卡片宽度
            visibleCount: 0, // 可见卡片数量
            maxScrollPosition: 0, // 最大滚动位置
            scrollPosition: 0, // 当前滚动位置
            groupList: []
        };
    },
    computed: {
        visibleCards() {
            return this.groupList.slice(this.scrollPosition, this.scrollPosition + this.visibleCount);
        }
    },
    async mounted() {
        let param = {
            reportType: 1,
            platform: 1,
            column: 'profit3',
            isHome: true,
            isIgnoreSpecialProCode: this.isIgnoreSpecialProCode,
            startTime: startTime,
            endTime: endTime
        }
        await this.getgroupList(param)
        // this.calculateVisibleCards(); // 初始化计算可见卡片
        // window.addEventListener('resize', this.calculateVisibleCards); // 监听窗口大小变化
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.calculateVisibleCards); // 移除窗口大小变化监听
    },
    methods: {
        changeGroup(i, id) {
            this.activeItme = i;
            this.$emit('handleSetLineChartData', this.pttyep, id)
        },
        async getgroupList(params) {
            this.scrollPosition = 0;
            this.isloading = true
            if (params) {
                await getDayReportGroupSaleAmontAsync(params).then(res => {
                    this.isloading = false
                    this.activeItme = 0;
                    if (res.success) {
                        this.groupList = res.data
                    }
                })
            }
            this.calculateVisibleCards();
            window.addEventListener('resize', this.calculateVisibleCards); // 监听窗口大小变化
        },
        calculateVisibleCards() {
            const wrapperWidth = this.$refs.cardsWrapper.offsetWidth; // 获取外层容器宽度
            this.visibleCount = Math.floor(wrapperWidth / this.cardWidth); // 计算可见卡片数量
            this.maxScrollPosition = Math.max(this.groupList.length - this.visibleCount, 0); // 计算最大滚动位置
            if (this.scrollPosition > this.maxScrollPosition) {
                this.scrollPosition = this.maxScrollPosition; // 修正当前滚动位置
            }
        },
        scrollCards(direction) {
            if (direction === 'left' && this.scrollPosition > 0) {
                if (this.scrollPosition < 3) {
                    this.scrollPosition = 0;

                } else {
                    this.scrollPosition = this.scrollPosition - 3;
                }

            } else if (direction === 'right' && this.scrollPosition < this.maxScrollPosition) {
                if ((this.maxScrollPosition - this.scrollPosition) < 3) {
                    this.scrollPosition = this.scrollPosition + (this.maxScrollPosition - this.scrollPosition);
                } else {
                    this.scrollPosition = this.scrollPosition + 3;
                }
            }
        },
        getChatInfos() {
            this.$emit("getChatInfos", this.timerange, this.isIgnoreSpecialProCode);
            let param = {
                reportType: 1,
                platform: 1,
                column: 'profit3',
                isHome: true,
                isIgnoreSpecialProCode: this.isIgnoreSpecialProCode,
                startTime: dayjs(this.timerange[0]).format('YYYY-MM-DD'),
                endTime: dayjs(this.timerange[1]).format('YYYY-MM-DD')
            }
            this.getgroupList(param)
        },
        async handleSetLineChartData(type) {
            this.pttyep = type;
            this.$emit('handleSetLineChartData', type, null)
                    this.styleall =''
                    this.styletao = '',
                    this.styletian = ''
                    this.stylepin = ''
                    this.stylegong = ''
                    this.styledy = ''
                    this.styleSheIn = ''
                    this.styleJD = ''
                    this.styleJingXi = ''
                    this.styleJingXiNN = ''
                    this.styleAlibaba = ''
                    this.styleSuNing = ''
                    this.styleFenXiao = ''
                    this.styleDYGX = ''
                    this.styleKWaiShop = ''
                    this.styleVideo = ''

                    this.stylegoodscodenum = ''
                    this.stylegoodnumcurrentnum = ''
                    this.styleamountpaid = ''
                    this.stylecountprocode = ''
                    this.stylerefundamont = ''
                    this.styleordercount = ''
                    this.styleguestunitprice = ''
                    this.styleAverageExpressPrice = ''

            if (type == 'shoppings') {
                this.styleall = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmonttian') {
                this.styletian = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmonttao') {
                this.styletao = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'messages') {
                    this.stylepin = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'purchases') {
                    this.stylegong = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontDY') {
                    this.styledy = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontJD') {
                    this.styleJD = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontJingXi') {
                    this.styleJingXi = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontJingXiNN') {
                    this.styleJingXiNN = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontAlibaba') {
                    this.styleAlibaba = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontSuNing') {
                    this.styleSuNing = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontFenXiao') {
                    this.styleFenXiao = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontDYGX') {
                    this.styleDYGX = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontSheIn') {
                    this.styleSheIn = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontKWaiShop') {
                    this.styleKWaiShop = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }
            else if (type == 'saleAmontWeChat') {
                    this.styleVideo = 'background-color: #409EFF;color:#fff;border-radius: 10px 10px 0px 0px;'
            }

        },
    },
};
</script>

<style lang="scss" scoped>
.timecard {
    width: 90%;
    height: 520px;
    overflow: auto;

}

.demo-image__lazy {
    margin: 10px;
    height: 520px;
    overflow-y: auto;
}

.kedanjiacss {
    background-color: #34bfa3;
}

.panel-group {
    // margin-top: 18px;


    .card-panel-col {
        // margin-bottom: 15px;
        color: rgba(51, 51, 51, 1);

    }

    .card-panel1 {
        box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
        border-color: rgba(0, 0, 0, .05);
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .card-panel {
        // height: 108px;
        cursor: pointer;
        //font-size: 12px;
        position: relative;
        //overflow: hidden;
        // color: #666;
        // background: #fff;
        box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
        border-color: rgba(0, 0, 0, .05);
        text-align: center;
        padding: 10px 0;

        &:hover {
            .card-panel {
                color: #fff;
            }

            .el-col-1 {
                width: 8.33333%;
            }

            transform: scale(1.05);
            overflow: hidden;
            color: #fff;
            background-color: #409EFF;
            border-radius: 10px 10px 0px 0px;
        }

        .icon-people {
            color: #40c9c6;
            font-size: 48px;
        }

        .icon-message {
            color: #36a3f7;
            font-size: 48px;
        }

        .icon-money {
            color: #f4516c;
            font-size: 48px;
        }

        .icon-shopping {
            color: #34bfa3;
            font-size: 48px;
        }

        .icon-warning {
            color: #f4516c;
            font-size: 48px;
        }

        .card-panel-icon-wrapper {
            float: left;
            margin: 14px 0 0 12px;
            padding: 16px;
            transition: all 0.38s ease-out;
            border-radius: 6px;
        }

        .card-panel-icon {
            float: left;
            font-size: 48px;
        }

        .card-panel-description {
            // color: rgba(0, 0, 0, 0.45);
            // float: right;
            font-weight: bold;
            // margin: 26px 0;
            // margin-left: 0px;

            .card-panel-text {
                line-height: 18px;
                font-size: 16px;
                margin-bottom: 12px;
            }

            .card-panel-num {
                font-size: 13px;
            }
        }
    }
}

@media (max-width:550px) {
    .card-panel-description {
        display: none;
    }

    .card-panel-icon-wrapper {
        float: none !important;
        width: 100%;
        height: 100%;
        margin: 0 !important;

        .svg-icon {
            display: block;
            margin: 14px auto !important;
            float: none !important;
            //background-color: #409EFF;
        }
    }
}

// .marginleft{
//   margin-left: -20px;
// }
.groupClass {
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);
    height: 100%;
    width: 100%;
    overflow-x: scroll;
    margin-top: 2px
}

.groupClass::-webkit-scrollbar {
    display: none;
}

// .groupClass:hover::-webkit-scrollbar{
// 	display:block;
//     height: 4px;
//     color: #dfe1e2;
// }
.btn-list {
    display: flex;
    white-space: nowrap;
}

.groupitem {
    margin: 10px 15px;
}

.card-container {
    position: relative;
    overflow: hidden;
    padding: 0 20px;
}

.controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    z-index: 1;
}

.left {
    left: 0;
}

.right {
    right: 0;
}

.cards-wrapper {
    position: relative;
    display: flex;
    overflow: hidden;
}

.cards {
    padding: 10px;
    display: flex;
    transition: transform 0.5s;
}

.card {
    text-align: center;
    width: 100%;
    // background-color: #ccc;
    // margin-right: 10px;
}

// ::v-deep .el-link.el-link--default{
//     color: #999999;
//     font-family: PingFang SC;
// }</style>
