<template>
  <my-container v-loading="pageLoading">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="汇总" name="first1">
        <purchasefundscollect ref="purchasefundscollect" @update-templatepageclose="handleUpdate"
          @platform-templatepageclose="platformUpdate">
        </purchasefundscollect>
      </el-tab-pane>
      <el-tab-pane label="明细" name="first2">
        <purchasefundsdetail ref="purchasefundsdetail">
        </purchasefundsdetail>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import purchasefundscollect from "@/views/inventory/purchasefundscollect";
import purchasefundsdetail from "@/views/inventory/purchasefundsdetail";

export default {
  name: 'purchasefundsindex',
  components: {
    MyContainer, purchasefundscollect, purchasefundsdetail
  },

  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },

  async mounted() {

  },

  methods: {
    //仓库汇总跳转明细页
    handleUpdate(value, timerange) {
      this.activeName = "first2";
      this.$nextTick(() => {
        this.$refs.purchasefundsdetail.updateFilterMonthDay(value, timerange);
      });
    },
    //平台汇总跳转明细页
    platformUpdate(val, timerange) {
      this.activeName = "first2";
      this.$nextTick(() => {
        this.$refs.purchasefundsdetail.performanFilterMonthDay(val, timerange);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
