<template>
  <my-container>
        <!--顶部操作-->
        <template #header>
          <el-row type="flex" >
           <el-col :span="4">


            <el-select v-model="fliter.userId" :loading="selloading" filterable
                   :remote-method="remoteMethod" fliterable placeholder="请输入姓名" remote :clearable="true">
                   <el-option v-for="(item) in selname" :key="'userSelector'+item.value+ item.extData.defaultDeptId" :label="item.label"
                       :value="item.value">
                       <span>{{item.label}}</span>
                      <span  style=" color: #8492a6; ">({{item.extData.position}},{{item.extData.empStatusText}}{{item.extData.jstUserName ? ","+item.extData.jstUserName:""}})</span>
                      <span style=" color: #8492a6; "> {{item.extData.deptName}}</span>
                   </el-option>
               </el-select>


           </el-col>

           <el-col :span="4">
            <el-select v-model="fliter.prmType" placeholder="权限类型" clearable>
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
           </el-col>


           <el-col :span="4">
            <el-input v-model="fliter.keyWords" placeholder="关键字查询" clearable  maxlength="40" />
           </el-col>
           <el-col>


            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="success" @click="openAddForm">新增</el-button>
           </el-col>
          </el-row>
         </template>
         <vxetablebase ref="table" :that='that'  :isIndex='true' :hasexpand='true' :isRemoteSort="true"
          :hasexpandRight='true' :tableData='dataList'  @sortchange='sortchange'
            :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading">
            <template slot="right">
                <vxe-column width="320" title="操作" fixed="right">
                    <template  #default="{ row }">
                         <my-confirm-button type="delete" @click="delrow(row)" />
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
  </my-container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import MyContainer from "@/components/my-container";
import { getAllUser } from '@/api/inventory/packagesprocess';
import MyConfirmButton from '@/components/my-confirm-button'
import { delErpDataPermissions, addErpDataPermissions, allErpDataPermissions,PageAllErpDataPermissions } from '@/api/admin/user'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
const tableCols = [
        { istrue: true, prop: 'userName', label: '用户', width: '110', sortable: 'custom'  },
        { istrue: true, prop: 'userDeptFullName', label: '组织', props: '1', width: '500', sortable: 'custom' },
        { istrue: true, prop: 'platformNames', label: '平台', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'prmType', label: '权限类型', props: 'platf2ormSaleInfos', width: '150', sortable: 'custom' ,  formatter: (row) => options.filter(item=>item.value == row.prmType)[0]['label']},
        { istrue: true, prop: 'prmDataName', label: '权限名', props: '2', width: 'auto', sortable: 'custom' },
    ];
const options = [
    { value: 1, label: '运营组' },
    { value: 2, label: '运营专员或助理' },
    { value: 3, label: '采购'}
];
export default {
 components: {
     MyContainer,  vxetablebase, MyConfirmButton
  },
  data() {
    return {
     that: this,
     total: 0,
     sels: [],
     dataList: [],
     fliter: {
      keyWords: '',
      userId: null,
      prmType: null
         // "currentPage": 1,
         // "pageSize": 50,
         // "isAsc": true,
     },
     selloading: false,
     pager: { OrderBy: "", IsAsc: false },
     listLoading: false,
     tableCols: tableCols,
     options: options,
     selname: []
    };
  },

  mounted() {
    this.getlist();
  },

  methods: {
    onSearch(){
        this.$refs.pager.setPage(1);
        this.getlist();
    },
   async remoteMethod(query) {
       this.loading = true;
       if (query !== '') {
           let rlt= await QueryAllDDUserTop100({ keywords: query });
           if (rlt && rlt.success) {
            this.selname = rlt.data?.map(item => {
                   return { label: item.userName, value: item.userId, extData:item }
               });
           }
       } else {
        this.selname = [...this.orgOptions];
       }
       this.loading = false;
   },
   async changepage(){
       this.listLoading = true;
       this.pager = this.$refs.pager.getPager();
       this.getlist();
   },
   async getlist(){
    this.pager = this.$refs.pager.getPager();
    // this.fliter.prmType = options.filter(item => item.value==this.fliter.prmDataId).length>0?options.filter(item => item.value==this.fliter.prmDataId)[0]['label']:'';
    let params = {
         ...this.fliter,
         ...this.pager,
     };
     // let params = { ...this.fliter };

     this.listLoading = true;
     let res = await PageAllErpDataPermissions(params);
     this.listLoading = false;
     this.dataList = res?.data.list;
     this.total = res?.data?.total;
   },
   sortchange({ order, prop }) {
      if (prop) {
        this.fliter.orderBy = prop
        this.fliter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getlist()
      }
    },
   openAddForm(){
    let _this = this;
    this.$showDialogform({
        path: `@/views/admin/datapermission/dialogadd.vue`,
        title: '新增数据权限',
        autoTitle: false,
        args: { files:{name: 1}, mode: 3 },
        height: 300,
        width: '600px',
        callOk:_this.getlist
    })
   },
   async delrow(row){
      this.rows = [row];

      let idsarr = [];
      this.rows.map((item)=>{
       idsarr.push(item.id)
      })
      let params = idsarr.join(',');
      const res = await delErpDataPermissions(params);
      if (!res?.success) {
          return
      }
      this.$message.success('删除成功！');
      this.getlist();
   },
  },
};
</script>
