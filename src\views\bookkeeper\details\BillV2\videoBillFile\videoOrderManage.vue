<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="下单开始日期"
          end-placeholder="下单结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div style="width: 150px;margin-right: 5px;">
          <inputYunhan ref="productCode2" :inputt.sync="ListInfo.OrderNo" v-model="ListInfo.OrderNo"
              class="publicCss" placeholder="线上单号/多条请按回车" :clearable="true" :clearabletext="true"
              :maxRows="200" :maxlength="3000" @callback="orderNoInnerBack" title="线上单号">
          </inputYunhan>
        </div>
        <div style="width: 210px;margin-right: 5px;">
          <inputYunhan ref="productCode2" :inputt.sync="ListInfo.GiftNumber" v-model="ListInfo.GiftNumber"
              class="publicCss" placeholder="礼物单号/多条请按回车" :clearable="true" :clearabletext="true"
              :maxRows="200" :maxlength="3000" @callback="orderNoInnerBack2" title="礼物单号">
          </inputYunhan>
        </div>
        <div style="width: 120px;margin-left:50px;">
          <YhShopSelector :names="['所有店铺']" :values="[-1]"  platform="20" :checkboxOrRadio="'checkbox'" :addAllWarehouseConcept="true" @onChange="(v)=>{ListInfo.shopCode=v[0].join(','); }">
                </YhShopSelector>
        </div>
        <div style="width: 150px;margin-right: 5px;">
          <el-select filterable v-model="ListInfo.DeliveryFeeChannel" clearable placeholder="带货费用渠道" style="width: 150px">
              <el-option label="技术服务费" value="技术服务费" />
              <el-option label="达人佣金" value="达人佣金" />
              <el-option label="运费险预计投保费用" value="运费险预计投保费用" />
              <el-option label="带货机构服务费" value="带货机构服务费" />
            </el-select>
        </div>
        <div style="width: 200px;margin-right: 10px;">
          <inputYunhan ref="productCode2" :inputt.sync="ListInfo.ProCode" v-model="ListInfo.ProCode"
              placeholder="商品ID/多条请按回车" :clearable="true" :clearabletext="true"
              :maxRows="200" :maxlength="3000" @callback="orderNoInnerBack3" title="商品ID">
          </inputYunhan>
        </div>
            <el-button type="primary" @click="getList('search')">搜索</el-button>
            <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'breachPaymentVideo202412021559'" :tablekey="'breachPaymentVideo202412021559'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { formatLinkProCode } from "@/utils/tools";
import { getWeChatOrderManageList ,importWeChatOrderManageAsync} from '@/api/bookkeeper/reportdayV2'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhShopSelector from "@/components/YhCom/YhShopSelector";

import dayjs from 'dayjs'
const tableCols = [
{ sortable: 'custom', width: '130px', align: 'center', prop: 'shop', label: '店铺' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'orderNumber', label: '订单编号' },
        { sortable: 'custom', width: '100px', align: 'center', prop: 'proCode', label: '商品ID',  type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'orderPlacementTime', label: '订单下单时间' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'orderShippingTime', label: '订单发货时间' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'orderConfirmationTime', label: '订单确认收货时间' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'orderCompletionTime', label: '订单完成时间' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'orderStatus', label: '订单状态' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'shippingMethod', label: '发货方式' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'recipientName', label: '收件人姓名' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'recipientAddress', label: '收件人地址' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'province', label: '省份' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'city', label: '城市' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'district', label: '区' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'recipientPhone', label: '收件人手机' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'buyerRemark', label: '买家备注' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'sellerRemark', label: '商家备注' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'totalProductPrice', label: '商品总价' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'actualOrderPayment', label: '订单实际支付金额' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'actualOrderReceipt', label: '订单实际收款金额' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'orderShippingFee', label: '订单运费' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'productDiscount', label: '商品优惠' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'crossStoreDiscount', label: '跨店优惠' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'productPriceChange', label: '商品改价' },
        // { sortable: 'custom', width: '130px', align: 'center', prop: 'pointDeduction', label: '积分抵扣' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'paymentMethod', label: '支付方式' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'paymentTime', label: '支付时间' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'transactionNumber', label: '交易单号' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'logisticsCompany', label: '物流公司' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'expressNumber', label: '快递单号' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'technicalServiceFee', label: '技术服务费' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'technicalServiceFeeToBeReturnedAsPopularityCard', label: '技术服务费（将以人气卡形式返还）' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'shippingInsuranceEstimatedFee', label: '运费险预计投保费用' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'deliveryMethod', label: '带货方式' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'deliveryAccountType', label: '带货账号类型' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'deliveryAccountNickname', label: '带货账号昵称' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'deliveryFeeChannel', label: '带货费用渠道' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'deliveryFeeType', label: '带货费用类型' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'deliveryFee', label: '带货费用' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'giftNumber', label: '礼物单号' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productName', label: '商品名称' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productCodePlatform', label: '商品编码(平台)' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productCodeCustom', label: '商品编码(自定义)' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'sKUCodeCustom', label: 'SKU 编码(自定义)' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productAttributes', label: '商品属性' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productPrice', label: '商品价格' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'isPreSale', label: '是否预售' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productQuantity', label: '商品数量' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productShipping', label: '商品发货' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productAfterSales', label: '商品售后' },
        { sortable: 'custom', width: '130px', align: 'center', prop: 'productRefundedAmount', label: '商品已退款金额' }
]
export default {
  name: "breachPaymentVideo",
  components: {
    MyContainer, vxetablebase,YhShopSelector,inputYunhan
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startUseDate: null,//开始时间
        endUseDate: null,//结束时间
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    // await this.getList()
    if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startUseDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endUseDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startUseDate, this.ListInfo.endUseDate]
      }
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startUseDate = e ? e[0] : null
      this.ListInfo.endUseDate = e ? e[1] : null
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    // async exportProps() {
    //     const { data } = await exportStatData(this.ListInfo)
    //     const aLink = document.createElement("a");
    //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
    //     aLink.href = URL.createObjectURL(blob)
    //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
    //     aLink.click()
    // },
    orderNoInnerBack(val) {
      this.ListInfo.OrderNo = val;
    },
    orderNoInnerBack2(val) {
      this.ListInfo.GiftNumber = val;
    },
    orderNoInnerBack3(val) {
      this.ListInfo.ProCode = val;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startUseDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endUseDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startUseDate, this.ListInfo.endUseDate]
      }
      this.loading = true
      const { data, success } = await getWeChatOrderManageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
     //上传文件
     onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importWeChatOrderManageAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
