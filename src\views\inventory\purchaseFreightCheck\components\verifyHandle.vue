<template>
  <div style="width: 100%; min-height: 300px;padding: 5px 0;">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" height="200" label-width="100px" class="demo-ruleForm">
      <el-form-item label="问题归类" prop="problemGroup" v-if="!this.distinguish">
          <el-select v-model="ruleForm.problemGroup">
            <el-option key="厂家问题" label="厂家问题" value="厂家问题"></el-option>
            <el-option key="采购问题" label="采购问题" value="采购问题"></el-option>
            <el-option key="仓库问题" label="仓库问题" value="仓库问题"></el-option>
            <el-option key="其他" label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" placeholder="请输入备注" v-model="ruleForm.remark" :autosize="{ minRows: 5, maxRows: 5 }"
          resize="none" maxlength="200" show-word-limit>
        </el-input>
      </el-form-item>
      <el-form-item label="图片" prop="picture">
        <div class="chatPicUrl">
          <uploadimgFile ref="uploadimgFile" :disabled="isView" :ispaste="!isView" :noDel="isView"
            :accepttyes="accepttyes" :isImage="true" :uploadInfo="ruleForm.picture" :keys="[1, 1]" @callback="getImg"
            :imgmaxsize="9" :limit="9" :multiple="true">
          </uploadimgFile>
          <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
        </div>
      </el-form-item>
    </el-form>
    <div style="display: flex;justify-content: center;gap: 20px;margin-top: 40px;">
      <el-button @click="resetForm">取消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { getPurchaseCostVerifyPurSetList, confirmPurchaseCostVerifyPurOrder, handlePurchaseCostVerifyPurOrder, handlePurchaseCostVerifyPurOrderForParent, confirmPurchaseCostVerifyPurOrderForParent } from '@/api/inventory/purchaseCostVerify'
export default {
  name: "verifyHandle",
  props: {
    editid: {
      type: String,
      default() {
        return '';
      }
    },
    isView: {
      type: Boolean,
      default() {
        return false;
      }
    },
    distinguish: {
      type: Boolean,
      default() {
        return false;
      }
    },
    isParent: {
      type: Boolean,
      default() {
        return false;
      }
    },
  },
  components: {
    MyContainer, vxetablebase, uploadimgFile
  },
  data() {
    return {
      rules: {
        problemGroup: [
          { required: true, message: '请选择问题归类', trigger: 'blur' }
        ],
        picture: [
          { required: true, message: '请上传图片', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入备注', trigger: 'blur' }
        ],
      },
      ruleForm: {
        picture: [],
        remark: '',
        id: '',
        problemGroup: null
      },
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      that: this,
      tableData: [],
      loading: false,
    }
  },
  async mounted() {
    this.ruleForm.id = this.editid
  },
  methods: {
    getImg(data) {
      if (data) {
        this.ruleForm.picture = data.map(item => {
          return {
            url: item.url,
            name: item.fileName
          }
        })
        // this.ruleForm.freightVoucher = data.map(item => item.url).join(',')
      }
    },
    async submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let success = true;
          let result;
          let params = {
            id: this.ruleForm.id,
            picture: JSON.stringify(this.ruleForm.picture),
            remark: this.ruleForm.remark,
            problemGroup: this.ruleForm.problemGroup
          }
          if (!this.distinguish) {
            if (this.isParent) {
              result = await handlePurchaseCostVerifyPurOrderForParent(params);
            } else {
              result = await handlePurchaseCostVerifyPurOrder(params);
            }
            success = result.success;
          } else {
            if (this.isParent) {
              result = await confirmPurchaseCostVerifyPurOrderForParent(params);
            } else {
              result = await confirmPurchaseCostVerifyPurOrder(params);
            }
            success = result.success;
          }
          if (success) {
            this.$message.success('操作成功');
            this.$emit('verifyHandleClose');
          }
        } else {
          return false;
        }
      });
    },
    //重置关闭表单
    resetForm(formName) {
      this.$emit('verifyClose')
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
}

.publicCss {
  width: 260px;
}

.chatPicUrl {
  position: relative;
  height: 100px;

  .picTips {
    position: absolute;
    top: 0;
    left: 150px;
    color: #ff0000;
    font-size: 16px;
  }
}
</style>
