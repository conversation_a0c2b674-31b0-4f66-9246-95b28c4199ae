<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <div style="height: 40px; margin-top: 10px;">
              <span>
                <el-input v-model="filter.ProductCode" placeholder="商品编码" style="width: 200px;" clearable></el-input>
              </span>
              <span>
                <el-input v-model="filter.StyleCode" placeholder="款式编码" style="width: 200px;" clearable></el-input>
              </span>
              <span>
                <el-input v-model="filter.ProductName" placeholder="商品名称" style="width: 200px;" clearable></el-input>
              </span>
              <span>
                <el-input v-model="filter.ProductSimpleName" placeholder="商品简称" style="width: 200px;" clearable></el-input>
              </span>
              <span>
                商品重量
                <el-input-number v-model="filter.ProductWeightMin" placeholder="最小值" :min=0 :max=9999 @change="handleMinChange" style="width: 130px;"></el-input-number>
                <el-input-number v-model="filter.ProductWeightMax" placeholder="最大值" :min=0 :max=9999 @change="handleMaxChange" style="width: 130px;"></el-input-number>
              </span>
              <span>
                <el-select v-model="filter.ProductStatus" placeholder="商品状态" style="width: 200px;" filterable clearable multiple collapse-tags>
                  <el-option label="商品状态（暂无）" value="暂无"></el-option>
                </el-select>
              </span>

              <span style="margin-left:5px;">
                <el-button type="primary" @click="onSearch" style="width: 60px;">搜索</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onReset" style="width: 60px;">重置</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onImport" style="width: 60px;">导入</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onExport" style="width: 60px;">导出</el-button>
              </span>
              <span style="margin-left:5px;">
                <el-button type="primary" @click="onAdd" style="width: 60px;">新增</el-button>
              </span>
            </div>
        </template>

        <vxetablebase ref="table" :tableData="tableData" :tableCols="tableCols" :is-index="true" :that="that" :id="'DistributionProductCodeManagement202409111544'" 
          :showsummary="false" style="width: 100%; height: 100%; margin: 0" @sortchange='sortchange'
          :treeProp="{ rowField: 'id', parentField: 'parentId' }" :loading="listLoading" class="already">
        </vxetablebase>
        
        <template #footer>
          <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" :height="'10px'"/>
        </template>

        <!-- 增加新增按钮，除商品编码和供应商ID都可手动输入 -->
        <el-dialog title="新增分销商品数据" :visible.sync="addVisible" width="600px" height="60%" @closeDialog="closeAddDialog" v-dialogDrag
          @close="addCancle">
          <el-form ref="fromAdd" :model="fromAdd" label-position="right" label-width="100px" >
            <el-row>
            <el-col :span="12">
            <el-form-item label="商品主图:">
              <el-input v-model="fromAdd.ProductMainPicture" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="商品名称:">
              <el-input v-model="fromAdd.ProductName" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="款式编码:">
              <el-input v-model="fromAdd.StyleCode" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="分销商品编码:">
              <el-input v-model="fromAdd.DistributionProductCode" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="商品简介:">
              <el-input v-model="fromAdd.ProductBriefIntroduction" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="成本价:">
              <el-input v-model="fromAdd.CostPrice" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="重量:">
              <el-input v-model="fromAdd.ProductWeight" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="长(cm):">
              <el-input v-model="fromAdd.ProductLength" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="宽(cm):">
              <el-input v-model="fromAdd.ProductWidth" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="高(cm):">
              <el-input v-model="fromAdd.ProductHeight" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="体积(c㎡):">
              <el-input v-model="fromAdd.ProductVolume" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="商品状态:">
              <el-input v-model="fromAdd.ProductStatus" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="修改时间:">
              <el-date-picker v-model="fromAdd.ModifyTime" placeholder=" 修改时间 " type="datetime" :clearable="true"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 170px">
              </el-date-picker>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="创建时间:">
              <el-date-picker v-model="fromAdd.CreateTime" placeholder=" 创建时间 " type="datetime" :clearable="true"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 170px">
              </el-date-picker>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>
          
          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <el-row>
            <el-col :span="12">
            <el-form-item label="供销商ID:">
              <el-input v-model="fromAdd.VendorID" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item label="供销商名称:">
              <el-input v-model="fromAdd.VendorName" style="width: 170px;"></el-input>
            </el-form-item>
            </el-col>
            </el-row>
          <!-- </el-form>

          <el-form ref="fromAdd" :inline="true" :model="fromAdd" > -->
            <div style="text-align: center;">      
              <el-button type="primary" @click="addCancle">取消</el-button>
              <el-button type="primary" @click="addSubmit">保存</el-button>
            </div>
          </el-form>
        </el-dialog>

        <el-dialog title="编辑分销商品编码/商品编码映射关系" :visible.sync="showDialog" width="450px" height="auto" @closeDialog="closeSetDialog" v-dialogDrag
          @close="setCancle">
          <el-form ref="formInfo" :model="formInfo" label-width="100px">
            <el-form-item label="分销商品编码">
              <el-input v-model="formInfo.DistributionProductCode" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="商品编码">
              <el-input v-model="formInfo.ProductCode"></el-input>
            </el-form-item>
            <el-form-item label="供销商ID">
              <el-input v-model="formInfo.VendorID"></el-input>
            </el-form-item>
            <el-form-item label="供销商名称">
              <el-input v-model="formInfo.VendorName"></el-input>
            </el-form-item>
            <div style="text-align: center;">      
              <el-button type="primary" @click="setCancle">取消</el-button>
              <el-button type="primary" @click="setSubmit">保存</el-button>
            </div>
          </el-form>
        </el-dialog>
        
        <el-dialog title="导入分销商品编码/商品编码映射关系" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" :auto-upload="false" :multiple="false" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getDistributionProductCodeStatusList, getDistributionProductCodePageList, importDistributionProductCode, 
  exportDistributionProductCode, updateDistributionProductCode, deleteDistributionProductCode, addDistributionProductCode } from "@/api/inventory/DistributionProductCodeManagement"

const tableCols = [
  { istrue: true, width: '60', align: 'center', type: "checkbox" },
  { istrue: true, width: '100', align: 'center', prop: 'productMainPicture', label: '商品主图' },
  { istrue: true, width: '100', align: 'center', prop: 'productName', label: '商品名称' },
  { istrue: true, width: '100', align: 'center', prop: 'styleCode', label: '款式编码' },
  { istrue: true, width: '100', align: 'center', prop: 'distributionProductCode', label: '分销商品编码' },
  { istrue: true, width: '100', align: 'center', prop: 'productCode', label: '商品编码' },
  { istrue: true, width: '100', align: 'center', prop: 'productBriefIntroduction', label: '商品简介' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'costPrice', label: '成本价' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'productWeight', label: '重量' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'productLength', label: '长(cm)' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'productWidth', label: '宽(cm)' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'productHeight', label: '高(cm)' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'productVolume', label: '体积(c㎡)' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'productStatus', label: '商品状态' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'modifyTime', label: '修改时间' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'createTime', label: '创建时间' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'vendorID', label: '供销商ID' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'vendorName', label: '供销商名称' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'supplierID', label: '供应商ID' },
  { istrue: true, sortable: 'custom', width: '100', align: 'center', prop: 'supplierName', label: '供应商名称' },
  {
    istrue: true, type: 'button', width: '100', label: '操作', btnList: [
      { label: "编辑", handle: (that, row) => that.onSet(row) },
      { label: "删除", handle: (that, row) => that.onDelete(row) }
    ]
  },
]

export default {
  name: "DistributionProductCodeManagement",
  components: { MyContainer, vxetablebase },
  data() {
    return {
      pageLoading: false,
      dialogVisible: false,//上传对话框
      uploadLoading: false,//上传过程
      addVisible: false,//新增对话框
      showDialog: false,//编辑对话框
      that: this,
      listLoading: false,//查询过程
      total: 0,
      tableData: [],
      tableCols: tableCols,
      pager: { orderBy: 'modifyTime', isAsc: true },
      // 过滤条件
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        // 过滤条件
        ProductCode: null,//商品编码
        StyleCode: null,//款式编码
        ProductName: null,//商品名称
        ProductSimpleName: null,//商品简称
        ProductWeightMin: undefined,//商品重量最小值
        ProductWeightMax: undefined,//商品重量最大值
        ProductStatus: [],//商品状态
      },
      // 新增
      fromAdd: {
        ProductMainPicture: null,//商品主图
        ProductName: null,//商品名称
        StyleCode: null,//款式编码
        DistributionProductCode: null,//分销商品编码
        ProductBriefIntroduction: null,//商品简介
        CostPrice: null,//成本价
        ProductWeight: null,//商品重量
        ProductLength: null,//长(cm)
        ProductWidth: null,//宽(cm)
        ProductHeight: null,//高(cm)
        ProductVolume: null,//体积(cm)
        ProductStatus: null,//商品状态
        ModifyTime: null,//修改时间
        CreateTime: null,//创建时间
        VendorID: null,//供销商ID
        VendorName: null,//供销商名称
      },
      // 编辑
      formInfo: {
        DistributionProductCode: null,//分销商品编码
        ProductCode: null,//商品编码
        VendorID: null,//供销商ID
        VendorName: null,//供销商名称
        // SuppliersName: null,//供应商名称
      },
      statusList: [],//状态选择器
      selectList: [],//复选框选中数据
      fileList: [],//导入文件
    };
  },
  mounted() {
    this.init();
    this.onSearch();
  },
  methods: {
    async init() {
      // 状态
      var { data } = await getDistributionProductCodeStatusList();
      this.statusList = data.map(item => { return { value: item, label: item }});
    },
    // 排序查询
    async sortchange(column) {
      if (column.order) {
        this.filter.orderBy = column.prop;
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
    // 复选框数据
    selectchange:function(rows,row) {
        this.selectList = [];
        rows.forEach(f => {
            this.selectList.push(f);
        })
    },
    // 商品重量最小值
    handleMinChange(value) {
      if (value === '') {
        this.filter.ProductWeightMin = undefined; // 或 null，根据需求
      } else {
        this.filter.ProductWeightMin = value;
      }
    },
    // 商品重量最大值
    handleMaxChange(value) {
      if (value === '') {
        this.filter.ProductWeightMax = undefined; // 或 null，根据需求
      } else {
        this.filter.ProductWeightMax = value;
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList()
    },
    // 查询列表
    async onSearch() {
      // 点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1)
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      const { data, success } = await getDistributionProductCodePageList(this.filter);
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
        this.listLoading = false;
      } else {
        this.$message.error('获取列表失败')
      }
    },
    // 重置过滤条件
    async onReset() {
      this.filter = {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        ProductCode: null,//商品编码
        StyleCode: null,//款式编码
        ProductName: null,//商品名称
        ProductSimpleName: null,//商品简称
        ProductWeightMin: undefined,//商品重量最小值
        ProductWeightMax: undefined,//商品重量最大值
        ProductStatus: [],//商品状态
      }
    },
    // 打开上传弹窗
    onImport() {
      this.dialogVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
          if (this.$refs.upload) {
              this.$refs.upload.clearFiles();
          }
      });
      this.fileList.splice(0, 1);
    },
    // 上传文件
    async uploadFile(item) {
      this.uploadLoading = true;
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importDistributionProductCode(form);
      if (res?.success) {
        this.$message({message: '上传成功,正在导入中...', type: "success"});
        // this.uploadLoading = false;
        // this.dialogVisible = false;
      }
      // this.getList();
    },
    // 更改上传文件
    async uploadChange(file, fileList) {
      if (fileList.length == 2) {
          fileList.splice(1, 1);
          this.$message({ message: "只允许单文件导入", type: "warning" });
          return false;
      }
      this.fileList.push(file);
    },
    // 移除上传文件
    uploadRemove() {
        this.fileList.splice(0, 1);
    },
    // 提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
          this.$message.warning('您没有选择任何文件！');
          return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.dialogVisible = false;
      this.getList();
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportDistributionProductCode(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement('a');
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute('download', '分销商品编码与商品编码_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    // 显示新增对话框
    async onAdd() {
      this.addVisible = true;
    },
    // 取消新增
    addCancle() {
      this.addVisible = false;
      this.fromAdd = {
        productMainPicture: null,//商品主图
        ProductName: null,//商品名称
        StyleCode: null,//款式编码
        distributionProductCode: null,//分销商品编码
        productBriefIntroduction: null,//商品简介
        costPrice: null,//成本价
        productLength: null,//长(cm)
        productWidth: null,//宽(cm)
        productHeight: null,//高(cm)
        productVolume: null,//体积(cm)
        productStatus: null,//商品状态
        modifyTime: null,//修改时间
        createTime: null,//创建时间
        VendorID: null,//供销商ID
        VendorName: null,//供销商名称
      };
    },
    // 关闭新增对话框
    closeAddDialog() {
      this.addVisible = false;
      this.fromAdd = {
        productMainPicture: null,//商品主图
        ProductName: null,//商品名称
        StyleCode: null,//款式编码
        distributionProductCode: null,//分销商品编码
        productBriefIntroduction: null,//商品简介
        costPrice: null,//成本价
        productLength: null,//长(cm)
        productWidth: null,//宽(cm)
        productHeight: null,//高(cm)
        productVolume: null,//体积(cm)
        productStatus: null,//商品状态
        modifyTime: null,//修改时间
        createTime: null,//创建时间
        VendorID: null,//供销商ID
        VendorName: null,//供销商名称
      };
    },
    // 提交新增
    async addSubmit() {
      this.$refs.fromAdd.validate(async valid => {
        if (valid) {
          const res = await addDistributionProductCode(this.fromAdd);
          if (res.success) {
            this.$message({ message: '添加成功', type: "success" });
            this.addVisible = false;
            this.getList();
          } else {
            this.$message.error('添加失败')
          }
        }
      })
    },
    // 显示编辑对话框
    async onSet(row) {
      this.showDialog = true;
      this.formInfo = {
        DistributionProductCode: row.distributionProductCode,
        ProductCode: row.productCode,
        VendorID: row.vendorID,//供销商ID
        VendorName: row.vendorName,//供销商名称
        // SuppliersName: row.supplierName//供应商名称
      };
      // this.formInfo.DistributionProductCode = row.DistributionProductCode;
      // this.formInfo.ProductCode = row.ProductCode;
    },
    // 取消编辑操作
    setCancle() {
      this.showDialog = false; // 关闭对话框
    },
    // 提交编辑操作
    async setSubmit() {
      this.$refs.formInfo.validate(async valid => {
        if (valid) {
          const res = await updateDistributionProductCode(this.formInfo);
          if (res.success) {
            this.$message({ message: '编辑成功', type: "success" });
            this.showDialog = false;
            this.getList();
          } else {
            this.$message({ message: res.msg, type: "error" });
          }
        }
      })
    },
    // 关闭编辑对话框
    closeSetDialog() {
      this.showDialog = false;
    }, 
    // 删除
    onDelete(row) {
        this.formInfo = {
          DistributionProductCode: row.distributionProductCode,
          ProductCode: row.productCode,
          VendorID: row.vendorID,//供销商ID
          VendorName: row.vendorName,//供销商名称
          // SuppliersName: row.supplierName//供应商名称
        };
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除后执行删除操作
        deleteDistributionProductCode(this.formInfo)
          .then(response => {
            if (response.data) {
              // 删除成功的处理逻辑
              this.$message({ type: 'success', message: '删除成功!' });
            // 成功后刷新列表
            this.getList(); // 调用重新获取数据的方法
            }
            else {
              this.$message({ type: 'error', message: '删除失败!' });
            }
          })
      }).catch(() => {
        // 用户取消删除的处理逻辑
        this.$message({ type: 'info', message: '已取消删除!' });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

// 控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 100px;
}

// 新增、编辑el-input行间距
.custom-form .el-form-item {
  margin-bottom: 120px;
  /* 自定义行间距 */
}
</style>