<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <!-- <el-select v-model="ListInfo.isStock" placeholder="地区" class="publicCss" clearable>
          <el-option :key="'是'" label="是" :value="0" />
          <el-option :key="'否'" label="否" :value="1" />
        </el-select> -->
        <el-input v-model.trim="ListInfo.SKU" placeholder="SKU" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.ProductName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-dropdown style="margin: 0 2px;" @command="handleCommand">
          <el-button type="primary">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">按照SKU汇总</el-dropdown-item>
            <el-dropdown-item :command="2">按照SPU汇总</el-dropdown-item>
            <el-dropdown-item :command="3">按照店铺汇总</el-dropdown-item>
            <el-dropdown-item :command="4">明细</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </template>
    <vxetablebase :id="'allianceDeductionJdRefund202411282236'" :tablekey="'allianceDeductionJdRefund202411282236'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <!-- <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker> -->
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { importJdSelfWarehousOut, getJdSelfWarehousOutList, exportJdSelfWarehousOutList } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
{ sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '时间' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productName', label: '商品名称' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sku', label: ' SKU编码 ' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺名称' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'spuCode', label: 'SPU编码' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productPrice', label: '商品价格' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'costPrice', label: '成本价' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'productCostPrice', label: '商品成本' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nationalPurchasePrice', label: '全国采购价' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nationalYesterdayOutgoingQuantity', label: '全国昨日出库商品件数' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'nationalYesterdayOutgoingAmount', label: '全国昨日出库金额' }
]
export default {
  name: "JdSelfWarehousOut",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      yearMonthDay: '',
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopGoodsCode: '',//商品编号
        orderNo: '',//订单号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async handleCommand(value) {
      this.loading = true
      const { data } = await exportJdSelfWarehousOutList({...this.ListInfo, exportType: value})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', `${value == 1 ? '按照SKU汇总' : value == 2 ? '按照SPU汇总' : value == 3 ? '按照店铺汇总' : value == 4 ? '明细' : '京东仓自营入仓出库' }数据` + dayjs().format('MM-DD HH:mm:ss') + '.xlsx')
      aLink.click()
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("dataType", 2);
     // form.append("yearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      var res = await importJdSelfWarehousOut(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getJdSelfWarehousOutList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach((item, index) => {
          item.yearMonthDay = item.yearMonthDay ? dayjs(item.yearMonthDay).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
