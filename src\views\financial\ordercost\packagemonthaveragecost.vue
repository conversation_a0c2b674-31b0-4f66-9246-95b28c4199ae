<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' :tablekey="packagemonthaveragecost"
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="true"
              :loading="listLoading">
         <template slot='extentbtn'>
            <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter.specialType" placeholder="特殊类别" style="width: 200px" :clearable="true"  @change="onSearch">
                 <el-option v-for="item in specialTypeList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-button>             
              <el-button style="padding: 0;margin: 0;">
                <el-select v-model="filter.auditStatus" placeholder="审核状态" style="width: 200px" :clearable="true"  @change="onSearch">
                 <el-option v-for="item in yesnoList" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </el-button>
            </el-button-group>
         </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

    <el-drawer
                      :title="formtitle"
                      :modal="false"
                      :wrapper-closable="true"
                      :modal-append-to-body="false"
                      :visible.sync="addFormVisible"
                      direction="btt"
                      size="'auto'"
                      class="el-drawer__wrapper"
                      style="position:absolute;"
                    >
                    <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" style="margin-top:10px;"/>
                      <div class="drawer-footer">
                        <el-button @click.native="addFormVisible = false">取消</el-button>
                        <my-confirm-button type="submit"  :loading="addLoading" @click="onAddSubmit" />
                      </div>
    </el-drawer> 
  </container>
</template>
<script>
import {
  pagePackageMonthAverageCost,
  addPackageMonthAverageCost,
  updatePackageMonthAverageCost,
  deletePackageMonthAverageCost,
  changePackageMonthAverageCostAuditStatus,
} from '@/api/financial/ordercost'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatWarehouse,formatTime,formatYesornoBool} from "@/utils/tools";
import MyConfirmButton from '@/components/my-confirm-button'
const tableCols =[
      
      {istrue:true,prop:'settMonth',label:'结算月份', width:'120',sortable:'custom',},
      {istrue:true,prop:'specialType',label:'特殊类别', width:'230',sortable:'custom',},  
      {istrue:true,prop:'averageCost',label:'包装均值', width:'230',sortable:'custom',}, 
      {istrue:true,prop:'auditStatus',label:'审核状态', width:'120',sortable:'custom',type:'switch',
        isDisabled:(row,that)=>row.auditStatus,
        change:(row,that)=>that.changeAuditStatus(row)  
      }, 
      {istrue:true,prop:'computeStatus',label:'计算状态', width:'110',sortable:'custom',formatter:(row)=>{return row.computeStatus==0?'未计算':row.computeStatus==1?'已计算':'预估'}},
      {istrue:true,prop:'createdUserName',label:'创建人', width:'120'},
      {istrue:true,prop:'createdTime',label:'创建时间', width:'160',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,prop:'modifiedUserName',label:'修改人', width:'120'},
      {istrue:true,prop:'modifiedTime',label:'修改时间', width:'160',sortable:'custom',formatter:(row)=>formatTime(row.modifiedTime,'YYYY-MM-DD HH:mm:ss')},
      {istrue:true,type:'button', label:'操作',width:'90',btnList:[
          {label:"编辑",handle:(that,row)=>that.onEditRow(row)},
          {label:"删除",handle:(that,row)=>that.onDeleteRow(row)}
        ]
      },
    ];
const tableHandles=[
        {label:"新增", handle:(that)=>that.onAddRow()},
        {label:"刷新", handle:(that)=>that.getlist()},
      ];
export default {
  name: 'Roles',
  components: {cesTable, container,MyConfirmButton },
   props:{
       filter: { }
     },
  data() {
    return {
      shareFeeType:0,
      that:this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      //自动表单 - Start
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      autoform:{
               fApi:{},
               rule:[],
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
      },
      addFormVisible: false,
      addLoading: false,     
      formtitle:"",
      isAdd:true,
      //自动表单 -End
      specialTypeList:[
        {label:"普通",value:"普通"},
        {label:"护墙角",value:"护墙角"},
        {label:"节能罩",value:"节能罩"},
        {label:"纸管",value:"纸管"},
        {label:"鞋拔子",value:"鞋拔子"},
      ],
      yesnoList:[
        {label:"已审核",value:true},
        {label:"未审核",value:false},
      ],     
    }
  },
  mounted() {
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.shareFeeType=this.shareFeeType;
      const params = {...pager, ...this.pager, ... this.filter}
      this.listLoading = true
      const res = await pagePackageMonthAverageCost(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    //==自动表单Start===========================
    //加载特殊类别
    async setSpecialType(){
      let rule= {validate: [{type: 'string', required: true, message:'请选择'}],options: [{value:null, label:'请选择'}]}
      rule.options=this.specialTypeList;
      return rule;
    },
    async onAddRow() {
      this.formtitle='新增';
      this.isAdd=true;
      this.addFormVisible = true;
      var that=this;
      this.autoform.rule=[
                {type:'hidden',field:'id',title:'id',value: ''},
                {type:'DatePicker',field:'settMonth',title:'结算月份',props:{type:'month',valueFormat:'yyyyMM',format:'yyyyMM'}, validate: [{type: 'string', required: true, message:'结算月份必填'}],value: "",
                    options: []
                },
                {type:'select',field:'specialType',title:'特殊类别', validate: [{type: 'string', required: true, message:'请选择特殊类别'}],value: "",
                    options: [],...await this.setSpecialType(),
                },
                {type:'number',field:'averageCost',title:'包装均值', validate: [{type: 'number', required: true, message:'请填写包装均值'}],value: "",
                    options: [],
                },   
      ];

      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields();       
    },
    async onAddSubmit() {
      this.addLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          formData.id=formData.id?formData.id:0;
          formData.Enabled=true;
          if(this.isAdd){
            const res = await addPackageMonthAverageCost(formData);
            if(res.code==1){
              await this.getlist();
              this.addFormVisible=false;
              this.$message({
                message: '新增成功',
                type: 'success'
              });
            }
          }
          else{
            const res = await updatePackageMonthAverageCost(formData);
            if(res.code==1){
              await this.getlist();
              this.addFormVisible=false;
              this.$message({
                message: '修改成功',
                type: 'success'
              });
            }
          }
        }else{
          //todo 表单验证未通过
        }
     })
     this.addLoading=false;
    },
    async onEditRow(row) {
      this.onAddRow();
      this.formtitle='修改';
      this.isAdd=false;
         
      setTimeout(async () => {
        await this.autoform.fApi.setValue({id:row.id});
        await this.autoform.fApi.setValue({settMonth:row.settMonth.toString()});
        await this.autoform.fApi.setValue({specialType:row.specialType});
        await this.autoform.fApi.setValue({averageCost:row.averageCost});
      }, 100);
      
    },
    async onDeleteRow(row){
       var params ={id:row.id||0};
       this.$confirm('确认删除?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deletePackageMonthAverageCost(params);
            if(res.code==1){
                await this.getlist();
                this.$message({message: '修改成功',type: 'success'});
            }
        }).catch(() => {});       
    },
    //==自动表单End  ===========================
    async changeAuditStatus(row){        
        this.$confirm('审核后将无法修改及删除，是否继续?', '提示', {confirmButtonText: '是',cancelButtonText: '否',type: 'warning'
        }).then(async () => {
            var params={
              id:row.id,
              auditStatus:row.auditStatus,
            };
            const res = await changePackageMonthAverageCostAuditStatus(params);
            if (!res?.success) return ;
            this.$message({message:"审核成功",type:"success"});
        }).catch(() => {
           row.auditStatus=false;
        });         
    },
  }
}
</script>
