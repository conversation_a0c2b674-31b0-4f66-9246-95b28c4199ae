<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div class="top">
        <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" clearable :maxlength="200" class="publicCss" />
        <el-select v-model="filter.packWarehouseNames" placeholder="加工仓" clearable
          style="width: 220px;margin-right: 10px;" multiple collapse-tags>
          <el-option v-for="item in processingBin" :key="item.wms_co_id" :label="item.name" :value="item.name">
          </el-option>
        </el-select>
        <el-select v-model="filter.sendWarehouseNames" placeholder="发货仓" clearable
          style="width: 220px;margin-right: 10px;" multiple collapse-tags>
          <el-option v-for="item in shippingWarehouse" :key="item.wms_co_id" :label="item.name" :value="item.name">
          </el-option>
        </el-select>
        <!-- <el-form-item label="商品名称:">
                <el-input v-model.trim="filter.goodsName" placeholder="商品名称" clearable :maxlength="200" @change="onSearch"/>         
          </el-form-item>
          <el-form-item label="款式编码:">
                <el-input v-model.trim="filter.styleCode" placeholder="款式编码" clearable :maxlength="200" @change="onSearch"/>         
          </el-form-item>
          <el-form-item label="运营组:
            <el-select style="width:130px;" v-model="filter.groupId" placeholder="请选择" :clearable="true" :collapse-tags="true"   @change="onSearch" >
              <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="采购组:">
            <el-select style="width:130px;" v-model="filter.brandId" placeholder="请选择" :clearable="true" :collapse-tags="true"   @change="onSearch" >
              <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
              </el-option>
            </el-select>
          </el-form-item> -->
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="createrow">新增</el-button>
        <el-button type="primary" @click="dialogVisible = true">导入</el-button>
        <!-- <el-form-item>
              <el-button type="primary" @click="weightDialogVisible = true">导入重量</el-button>
          </el-form-item>
          <el-form-item>
              <el-button type="primary" @click="preGoodsInventoryDialogVisible = true">导入预包编码库存</el-button>
          </el-form-item> -->
        <el-button type="primary" @click="openmuban">下载基础数据模板</el-button>
        <el-button type="primary" @click="openSet">批量设置加工、发货仓</el-button>
      </div>

    </template>

    <vxetablebase ref="table" :id="'YunHanAdminGoods20230808'" :that='that' :isIndex='true' :hasexpand='true'
      @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols'
      @select='selectchange' :isSelection.sync="ischoice" :border='true' @checkbox-range-end="callback"
      :tableHandles='tableHandles' :isSelectColumn="!ischoice" :loading="listLoading">
      <!-- <template slot='extentbtn'>
          <el-button-group>
            <el-button style="margin: 0;" type="primary" v-if="!ischoice" @click="startImport">导入</el-button>
          </el-button-group>
        </template> -->
      <template slot="right">
        <vxe-column title="操作" :field="'col_opratorcol'" width="120" fixed="right">
          <template #default="{ row }">
            <template>
              <el-button type="text" @click="onEdit(row)">编辑</el-button>
              <!-- <el-button type="text" @click="onDelete(row)">删除</el-button> -->
              <my-confirm-button type="delete" @click="onDelete(row)" />
            </template>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog :title="isadd ? '新增' : '编辑'" :visible.sync="addshow" width="50%" v-dialogDrag>
      <el-form ref="addForm" :model="addForm" label-width="190px" :rules="addFormRules">
        <el-form-item label="商品编码" prop="goodsCode">
          <el-input v-model="addForm.goodsCode" maxlength="50" auto-complete="off" :disabled="!isadd" />
        </el-form-item>
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="addForm.goodsName" maxlength="50" auto-complete="off" />
        </el-form-item>
        <el-form-item label="对应自编码数量的重量/克" prop="childWeight">
          <el-input-number v-model="addForm.childWeight" :min="0" :max="999999" :precision="2" auto-complete="off" />
        </el-form-item>
        <el-form-item label="对应子编码包装" prop="childPackage">
          <el-input v-model="addForm.childPackage" maxlength="50" auto-complete="off" />
        </el-form-item>
        <el-form-item label="打包耗材" prop="packageConsumables">
          <el-input v-model="addForm.packageConsumables" maxlength="50" auto-complete="off" />
        </el-form-item>
        <el-form-item label="预包编码" prop="prePackCode">
          <el-input v-model="addForm.prePackCode" maxlength="50" auto-complete="off" />
        </el-form-item>
        <el-form-item label="是否认领" prop="isProcessClaim">
          <el-select v-model="addForm.isProcessClaim" clearable filterable placeholder="是否认领" style="width: 140px">
            <el-option label="已认领" :value="true" />
            <el-option label="未认领" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="加工仓:">
          <el-select v-model="addForm.packWarehouseId" placeholder="加工仓" clearable
            @change="changeWarehouse($event, '加工仓', 'dialog')">
            <el-option v-for="item in processingBin" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发货仓:">
          <el-select v-model="addForm.sendWarehouseId" placeholder="发货仓" clearable
            @change="changeWarehouse($event, '发货仓', 'dialog')">
            <el-option v-for="item in shippingWarehouse" :key="item.wms_co_id" :label="item.name"
              :value="item.wms_co_id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click.native="addshow = false">取消</el-button>
          <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSubmit" />
        </div>
      </template>
    </el-dialog>

    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :on-change="uploadChange" :on-remove="uploadRemove" :http-request="uploadFile"
              :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入重量" :visible.sync="weightDialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload ref="uploadWeight" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :on-change="uploadChangeWeightFile" :on-remove="uploadRemoveWeightFile"
              :http-request="uploadWeightFile" :data="fileparm">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="weightFileUploadLoading"
                @click="submitUploadWeightFile">{{ (weightFileUploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="weightDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 批量设置加工仓发货仓 -->
    <el-dialog title="批量设置加工仓发货仓" :visible.sync="openSetVisible" width="20%" v-dialogDrag>
      <div style="display: flex;flex-direction: column;align-items: center;">
        <el-select v-model="setWarehouse.packWarehouseId" placeholder="加工仓" clearable style="width: 200px;"
          @change="changeWarehouse($event, '加工仓', 'set')">
          <el-option v-for="item in processingBin" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id">
          </el-option>
        </el-select>
        <el-select v-model="setWarehouse.sendWarehouseId" placeholder="发货仓" clearable
          style="width: 200px;margin-top: 20px;" @change="changeWarehouse($event, '发货仓', 'set')">
          <el-option v-for="item in shippingWarehouse" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id">
          </el-option>
        </el-select>
      </div>
      <template #footer>
        <div style="display: flex;justify-content: center;">
          <el-button @click="openSetVisible = false">关闭</el-button>
          <el-button type="primary" @click="openSetSubmit" style="margin-right: 10px;" v-throttle="1000">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getAllProBrand } from '@/api/inventory/warehouse';
import {
  //分页查询店铺商品资料
  pageGetBaseDataAsync,
  // 删除
  deleteBaseDataAsync,
  //更新
  updateBaseDataAsync,
  //创建
  createBaseDataAsync,
  pageGetTbWarehouseAsync,
  batchUpdateWarehouse,
} from "@/api/inventory/prepack.js"

import {
  //导入
  importBaseDatasAsync,
  importBaseDatasUpdateAsync
} from "@/api/inventory/prepackImport.js"

// import { 
//   //分页查询店铺商品资料
//   getList,
//   //导入
//   importData,
// } from "@/api/inventory/basicgoods"
const tableCols = [
  { istrue: true, label: '商品编码', type: "checkbox" },
  { istrue: true, prop: 'goodsCode',align: 'left', label: '商品编码', sortable: 'custom', },
  { istrue: true, prop: 'goodsName',align: 'left', label: '商品名称', },
  { istrue: true, prop: 'childWeight', label: '对应子编码数量的重量/克', sortable: 'custom', },
  { istrue: true, prop: 'childPackage', label: '对应子编码包装', sortable: 'custom', },
  { istrue: true, prop: 'packageConsumables', label: '打包耗材', sortable: 'custom' },
  { istrue: true, prop: 'prePackCode',align: 'left', label: '预包编码', sortable: 'custom' },
  { istrue: true, prop: 'packWarehouseName',align: 'left', label: '加工仓', sortable: 'custom' },
  { istrue: true, prop: 'sendWarehouseName',align: 'left', label: '发货仓', sortable: 'custom' },
  //{istrue:true,prop:'isProcessClaim',label:'加工认领', sortable:'custom',formatter:(row)=>row.isProcessClaim?"已认领":"未认领" },
  //  {
  //     istrue: true, type: 'button',  label: '编辑', btnList: [
  //         { label: "编辑", display: (row) => { return false; }, handle: (that, row) => that.onEdit(row.id) },
  //         { label: "删除", display: (row) => { return false; }, handle: (that, row) => that.onDelete(row) }]
  // },

  //  {istrue:true,prop:'costPrice',label:'成本价', width:'80',sortable:'custom',},
  //  {istrue:true,prop:'groupId',label:'运营组', width:'80',sortable:'custom',formatter:(row)=>row.groupId==0?" ":row.groupName},
  //  {istrue:true,prop:'modified',label:'修改时间', width:'150',sortable:'custom',formatter:(row)=>formatTime(row.modified,'YYYY-MM-DD HH:mm:ss')},      
  //  {istrue:true,prop:'isEnabled',label:'是否启用', width:'80',sortable:'custom',formatter:(row)=>row.isEnabled==0?"备用":(row.isEnabled==1?"启用":(row.isEnabled==-1?"禁用":""))},

];
const tableHandles1 = [
  // {label:"导入", handle:(that)=>that.startImport()},
  // {label:"确定选择", handle:(that)=>that.onselected()},
];
export default {
  name: 'goods',
  components: { cesTable, vxetablebase, MyContainer, MyConfirmButton },
  props: {
    ischoice: { type: Boolean, default: false },
  },
  data() {
    return {
      value: '',
      that: this,
      addLoading: false,
      isadd: true,
      addForm: {},
      fileparm: {},
      addFormRules: {
        goodsCode: [{ required: true, message: '请输入商品编码', trigger: 'change' }],
        goodsName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        // childWeight: [{ required: true, message: '请输入对应子编码数量的重量', trigger: 'blur' }],
        // childPackage: [{ required: true, message: '请输入对应子编码包装', trigger: 'blur' }],
        // packageConsumables: [{ required: true, message: '请输入打包耗材', trigger: 'blur' }],
        // prePackCode: [{ required: true, message: '请输入预包编码', trigger: 'blur' }],
        // isProcessClaim: [{ required: true, message: '请选择是否加工认领', trigger: 'blur' }],
      },
      filter: {
        styleCode: null,
        goodsCode: null,
        goodsName: null,
        groupId: null,
        brandId: null,
        packWarehouseNames: [],
        sendWarehouseNames: [],
      },
      addshow: false,
      list: [],
      summaryarry: {},
      pager: { orderBy: "", isAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      platformList: [],
      shopList: [],
      groupList: [],
      brandList: [],
      dialogVisible: false,
      weightDialogVisible: false,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      uploadLoading: false,
      weightFileUploadLoading: false,
      fileList: [],
      weightFileList: [],
      yesnoList: [
        { value: true, label: "是" },
        { value: false, label: "否" }
      ],
      selrows: [],
      processingBin: [],//加工仓
      shippingWarehouse: [],//发货仓
      openSetVisible: false,
      setWarehouse: {
        goodsCodes: [],
        packWarehouseId: null,//加工仓
        packWarehouseName: null,//加工仓
        sendWarehouseId: null,//发货仓
        sendWarehouseName: null,//发货仓
      }
    }
  },
  async mounted() {
    await this.init()
    await this.setGroupSelect();
    await this.setBandSelect();
    await this.getlist();
  },
  methods: {
    changeWarehouse(e, type, type1) {
      if (type1 == 'dialog') {
        this.addForm[type == '加工仓' ? 'packWarehouseId' : 'sendWarehouseId'] = e;
        this.addForm[type == '加工仓' ? 'packWarehouseName' : 'sendWarehouseName'] = e ? (type == '加工仓' ? this.processingBin.find(item => item.wms_co_id == e).name : this.shippingWarehouse.find(item => item.wms_co_id == e).name) : null
      } else {
        this.setWarehouse[type == '加工仓' ? 'packWarehouseId' : 'sendWarehouseId'] = e;
        this.setWarehouse[type == '加工仓' ? 'packWarehouseName' : 'sendWarehouseName'] = e ? (type == '加工仓' ? this.processingBin.find(item => item.wms_co_id == e).name : this.shippingWarehouse.find(item => item.wms_co_id == e).name) : null
      }
    },
    async openSetSubmit() {
      const { success } = await batchUpdateWarehouse(this.setWarehouse);
      if (success) {
        this.openSetVisible = false
        this.$message.success('设置成功')
        this.onSearch()
        this.setWarehouse = {
          goodsCodes: [],
          packWarehouseId: null,//加工仓
          packWarehouseName: null,//加工仓
          sendWarehouseId: null,//发货仓
          sendWarehouseName: null,//发货仓
        }
      }
    },
    openSet() {
      if (this.setWarehouse.goodsCodes.length == 0) return this.$message.error('请选择数据')
      this.openSetVisible = true
    },
    async init() {
      const { data, success } = await pageGetTbWarehouseAsync({ currentPage: 1, pageSize: 1000 });
      if (success) {
        this.shippingWarehouse = data.list.filter(item => item.isSendWarehouse == '是')
        this.processingBin = data.list.filter(item => item.isPrePack == 1)
      }
    },
    submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选择文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先选择文件", type: "warning" });
        this.uploadLoading = false
        return false;
      }
      const form = new FormData();
      form.append("file", item.file);
      // form.append("yearmonth", this.importfilter.yearmonth);
      var res = await importBaseDatasAsync(form);

      this.uploadLoading = false

      this.fileList = [];
      this.$refs.upload.clearFiles();
      if (!res?.success) {
        return
      }
      this.dialogVisible = false;
      this.$message.success("上传成功,正在导入中...")

      this.onSearch();
    },
    uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList) {
      this.fileList = fileList;
      // this.uploadChange(file, fileList);
    },

    submitUploadWeightFile() {
      if (!this.weightFileList || this.weightFileList.length == 0) {
        this.$message({ message: "请先选择文件", type: "warning" });
        return false;
      }
      this.weightFileUploadLoading = true
      this.$refs.uploadWeight.submit();
    },
    async uploadWeightFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先选择文件", type: "warning" });
        this.weightFileUploadLoading = false
        return false;
      }
      const form = new FormData();
      form.append("file", item.file);
      // form.append("yearmonth", this.importfilter.yearmonth);
      var res = await importBaseDatasUpdateAsync(form);

      this.weightFileUploadLoading = false

      this.weightFileList = [];
      this.$refs.uploadWeight.clearFiles();
      if (!res?.success) {
        return
      }
      this.weightFileUploadLoading = false;
      this.$message.success("上传成功,正在导入中...")

      this.onSearch();
    },
    uploadChangeWeightFile(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.weightFileList = list;
      }
    },
    uploadRemoveWeightFile(file, fileList) {
      this.weightFileList = fileList;
      // this.uploadChange(file, fileList);
    },

    addFormValidate() {
      let isValid = false
      this.$refs.addForm.validate(valid => {
        isValid = valid
      })
      return isValid
    },
    async onAddSubmit() {
      this.addLoading = true
      if (this.isadd) {
        const res = await createBaseDataAsync(this.addForm);
        this.addLoading = false
        if (!res.success) {
          return
        }
        await this.onSearch();
        this.$message.success("保存成功")
        this.addshow = false;
      } else {
        const res = await updateBaseDataAsync(this.addForm);
        this.addLoading = false
        if (!res.success) {
          return
        }
        await this.onSearch();
        this.$message.success("保存成功")
        this.addshow = false;
      }

      // this.groupList=res.data;
    },
    async onEdit(row) {
      // let res = await deleteBaseDataAsync({id: row.id});
      // if(!res.success){
      //   return
      // }
      this.isadd = false;
      this.addForm = {
        id: row.id,
        isProcessClaim: row.isProcessClaim ? true : false,
        prePackCode: row.prePackCode,
        packageConsumables: row.packageConsumables,
        childPackage: row.childPackage,
        childWeight: row.childWeight,
        goodsName: row.goodsName,
        goodsCode: row.goodsCode,
        packWarehouseId: row.packWarehouseId,
        sendWarehouseId: row.sendWarehouseId,
        packWarehouseName: row.packWarehouseName,
        sendWarehouseName: row.sendWarehouseName,

      };
      await this.$nextTick(() => {
        this.addshow = true;
      });
      // this.$message.success("删除成功！")
    },
    async onDelete(row) {
      let res = await deleteBaseDataAsync(row.goodsCode);
      if (!res.success) {
        return
      }
      await this.onSearch();
      this.$message.success("删除成功！")
    },
    createrow() {
      this.isadd = true;
      this.addForm = {};
      this.$nextTick(() => {
        this.addshow = true;
      });
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async setBandSelect() {
      var res = await getAllProBrand();
      if (!res?.success) return;
      this.brandList = res.data;
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = { ...pager, ...page, ... this.filter }
      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    onShowChoice() {
      // this.ischoice=true;
      this.$refs.table.clearSelection();
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.pageLoading = true;
      var res = await pageGetBaseDataAsync(params);
      this.pageLoading = false;
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      data.forEach(d => {
        d._loading = false;
        // d.id = d.goodsCode;
      })
      this.list = data
    },

    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else {
        var orderBy = column.prop;
        this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels
    },
    selectchange: function (rows, row) {
      this.setWarehouse.goodsCodes = rows.map(item => item.goodsCode);
      rows.forEach(f => {
        this.selrows.push(f);
      })
      let _this = this;
      if (rows.length > 0) {
        var a = [];
        rows.forEach(element => {
          let b = _this.list.indexOf(element);
          a.push(b + 1);
        });

        let d = _this.list.indexOf(row);

        var b = Math.min(...a)
        var c = Math.max(...a)

        a.push(d);
        if (d < b) {
          var b = _this.list.indexOf(row);
          var c = Math.max(...a)
        } else if (d > c) {
          var b = Math.min(...a) - 1
          var c = Math.max(...a)
        } else {
          var b = Math.min(...a) - 1
          var c = _this.list.indexOf(row) + 1;
        }

        let neww = [b, c];
        _this.selectarray = neww;
      }
    },
    callback(val) {
      this.selrows = [...val];
    },
    async getchoicelist() {
      if (!this.selrows || this.selrows.length == 0)
        this.$message({ message: "你还没有选择", type: "warning", });
      return this.selrows
    },
    async getchoicelistOnly() {
      if (!this.selrows || this.selrows.length == 0)
        this.$message({ message: "请选择一条数据，", type: "warning", });
      if (!this.selrows || this.selrows.length > 1)
        this.$message({ message: "只能选择一条数据", type: "warning", });
      return this.selrows
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    openmuban() {
      window.open("../../static/excel/base/预包基础数据模板.xlsx", "_self");
    },
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}

.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 5px;
  }
}
</style>
