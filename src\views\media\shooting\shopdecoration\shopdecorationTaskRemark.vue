<template>
    <my-container v-loading="pageLoading">
       <el-form   :disabled="islook">
            <el-row   style="height: 200px;">
                <el-col :span="1">
                    &nbsp;
                </el-col>
                    <el-col :span="22" >
                        <el-row>
                            <el-form-item label="图片"> 
                            </el-form-item> 
                                <uploadfile    
                                ref = "uploadimg" 
                                :uploadInfo="photoUpfiles"
                                :islook="islook" 
                                :limit="10000" 
                                :accepttyes="'.image/jpg,image/jpeg,image/png'" 
                                :boxsize="{ width: '500px', height: '80px' }"
                                :delfunction="deluplogimg"/>
                        </el-row>
                    </el-col>  
                    <el-col :span="1">
                        &nbsp;
                    </el-col>
            </el-row>
            <el-row  >
                <el-col :span="1">
                    &nbsp;
                </el-col>
                <el-col :span="22" >
                <el-card class="box-card" style="width:100% ;height: 45px; overflow: hidden;">
                        <div slot="header" class="clearfix" style="">
                            <span> 备注信息</span>
                            <span style="float:right">
                                <el-button type="primary" @click="onAddMarks()">增加一行</el-button>
                            </span>
                        </div>
                </el-card>
                <el-card class="box-card" style="width:100% ;height: 255px; overflow: auto;"> 
                    <div v-for="(item,index) in remarks " :key="index">
                        <el-row style="margin-top:15px">
                            <el-col :span="1"><span style="float: right; padding: 3px 0"> 备注:&nbsp;&nbsp;</span></el-col>
                            <el-col :span="15"><el-input v-model="item.remark" placeholder="请输入内容"></el-input></el-col>
                            <el-col :span="6"><span style="font-size: 12px;"> {{item.createdTimeType}} &nbsp;&nbsp;&nbsp;{{item.createdUserName}}</span></el-col>
                            <el-col :span="2"> &nbsp;<el-button  type="danger"   @click="onDelMarks(item.shopDecorationRemarkId,index)">删除<i class="el-icon-remove-outline"></i></el-button></el-col>
                        </el-row> 
                    </div>
                </el-card>
            </el-col>
            <el-col :span="1">
                    &nbsp;
                </el-col>
            </el-row>
       </el-form>  
   </my-container>
</template>
<script> 
import MyContainer from "@/components/my-container";
import uploadfile from '@/views/media/shooting/uploadfile' 
import { saveShootingTaskMarkAsync,delShootingTaskMarkAsync,getShootingTaskMarkAsync,delShootingTploadFileTaskAsync}
 from '@/api/media/shopdecoration';
import { formatTime} from "@/utils";
export default {
   components: { MyContainer,uploadfile},
   props:["rowinfo","islook"],
   data() {
       return {
           photoUpfiles:[], 
           pageLoading:false,
           showGoodsImage: false,
           remarks:[]
       };
   }, 
   async mounted() {
      await this.onload();
   }, 
   methods: {
        async  onsubmit()
        {
            this.pageLoading= true;
            var res = this.$refs.uploadimg.getReturns(); 
            if(!res.success){return;}
            this.photoUpfiles = res.data;
           
            var res  =  await saveShootingTaskMarkAsync({shopDecorationTaskId:this.rowinfo,uploadinfo:this.photoUpfiles,remakes:this.remarks});
            if(res?.success)
            {
                this.$message({ message: '操作成功', type: "success" });
                this.onload();
            }
            this.pageLoading= false;
        },
        async onload() {
           //获取拍摄上传的附件
            this.pageLoading= true;
            var res  =  await getShootingTaskMarkAsync({shopDecorationTaskId:this.rowinfo});
            this.photoUpfiles=[];
            this.remarks=[]; 
            if(res?.success){
                res.data.imagesinfo.forEach(element => { 
                    this.photoUpfiles.push(element); 
                }); 
                res.data.markinfos.forEach(element => { 
                    element.createdTimeType = formatTime( element.createdTime, 'YY-MM-DD HH') 
                    this.remarks.push(element); 
                });  
            } 
            this.$refs.uploadimg.setData(this.photoUpfiles); 
            this.pageLoading= false;
        },

        onAddMarks(){
            this.remarks.push({shopDecorationRemarkId:0,createdTime:new Date(),createdTimeType :formatTime(new Date(), 'YY-MM-DD HH') }); 
        },
        async onDelMarks(shopDecorationRemarkId,index )
        {
            if(shopDecorationRemarkId > 0){
                this.$confirm('此操作将会彻底删除该记录，是否执行')
                .then(async() => {
                   var res= await delShootingTaskMarkAsync({shopDecorationRemarkId:shopDecorationRemarkId});
                   if(res?.success)
                        this.remarks.splice(index,1);
                })
                .catch(_ => {
                    this.pageLoading =false;
                });
            }else{
                this.remarks.splice(index,1);
            }  
           
        },
          //删除上传图片操作
        async deluplogimg(ret)
        {
            this.pageLoading =true; 
            await delShootingTploadFileTaskAsync({upLoadPhotoId:ret.upLoadPhotoId,type:3});
            this.pageLoading =false;
        },
        async editorClick(e) {
            if (e.target.nodeName.toLocaleLowerCase() == 'img') {
                this.showImg(e.target.src);
            }
        },
          //完成表单界面关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        },
        async showImg(e) {
            this.showGoodsImage = true;
            this.imgList = [];
            if (e) {
                this.imgList.push(e);
            }
            else {
                this.imgList.push(this.imagedefault);
            }
        },
        //批量下载
        async pldownfile(index){
            this.pageLoading= true;
            if(index == 1){
                for(let num in this.pxeclUpfiles){ 
                    await this.downfile(this.pxeclUpfiles[num]);
                }
            }else{
                for(let num in this.photoUpfiles){
                   await this.downfile(this.photoUpfiles[num]);
                } 
            }
            this.pageLoading= false;
        },
        //下载文件
        async downfile(file)
        {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let blob = this.response;
                    console.log(blob);
                    // 转换一个blob链接
                    // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                    // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                    let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                    // 视频的type是video/mp4，图片是image/jpeg
                    // 01.创建a标签
                    let a = document.createElement('a');
                    // 02.给a标签的属性download设定名称
                    a.download = file.fileName;
                    // 03.设置下载的文件名
                    a.href = downLoadUrl;
                    // 04.对a标签做一个隐藏处理
                    a.style.display = 'none';
                    // 05.向文档中添加a标签
                    document.body.appendChild(a);
                    // 06.启动点击事件
                    a.click();
                    // 07.下载完毕删除此标签
                    a.remove();
                };
            };
            xhr.send();
        },
    },
};
</script>
<style  lang="scss" scoped> 
.linecontent{
//   background: rgb(238, 236, 236);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    overflow-x: auto;
    white-space: nowrap;
    margin-top: 10px;
}
.linerow{
    display: inline-block;
//   background: #fff;
    margin: 5px;
//   border: 1px solid rgba(219, 217, 217, 0.781);
//   width: 100px;
//   height: 100px;
}
.img-hover {
    position: relative;
}

.img-hover:hover {
    cursor: pointer;
    .close-img,
    .close-img-dask {
        display: block;
    }
}

.close-img {
    display: none;
    position: absolute;
    right: 0px;
    top: 0px;
    color: rgb(255, 0, 0);
    font-size: 18px;
    z-index: 99999;
}
.flexrow{
    width: 220px;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    color: rgb(50,50,50);
}
.rightflex{
    // width: 100%;
    // background-color: red;
    margin-right: 10px;
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
}
.outline{
    width: 300px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}
</style>