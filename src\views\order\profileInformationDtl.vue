<template>
    <MyContainer>
        <vxetablebase ref="vxetable" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='tableData' :tableCols="type == 'main' ? tableCols1 : tableCols"
            :isSelection="false" :isSelectColumn="false" style="width: 100%; height:600px; margin: 0"
            :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <vxe-modal v-model="dialogVisible" :width="1200" marginSize='-500' :title="title">
            <profileInformationStyleCodeDtl :filter="dialogFilter" v-if="dialogVisible" />
        </vxe-modal>
    </MyContainer>

</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { pageStyleCodeDirectorFullInfoWarDtlList } from '@/api/bookkeeper/styleCodeRptData'
import decimal from '@/utils/decimal.js'
import profileInformationStyleCodeDtl from './profileInformationStyleCodeDtl.vue' 
const computedNum = (val) => {
    return val !== null ? decimal(val, 10000, 1, '/') + '万' : ''
}
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory', label: '经营类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory1', label: '一级类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory2', label: '二级类目', type: "click", handle: (that, row, column, cell) => that.openStyleCodeDetail(row) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '订单量', formatter: (row) => row.orderCount !== null ? computedNum(row.orderCount) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleAmt', label: '销售金额', formatter: (row) => row.saleAmt !== null ? computedNum(row.saleAmt) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjzy', label: '占用资金', formatter: (row) => row.zjzy !== null ? computedNum(row.zjzy) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit33', label: '毛四利润', formatter: (row) => row.profit33 != null ? computedNum(row.profit33) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit5', label: '毛五利润', formatter: (row) => row.profit5 !== null ? computedNum(row.profit5) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit6', label: '毛六利润', formatter: (row) => row.profit6 !== null ? computedNum(row.profit6) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit6Index', label: '个人排名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzProfit6Index', label: '类目排名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjhbl', label: '回报率', formatter: (row) => row.zjhbl !== null ? row.zjhbl + '%' : '' },
]

const tableCols1 = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory', label: '经营类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory1', label: '一级类目', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzCategory2', label: '二级类目', type: "click", handle: (that, row, column, cell) => that.openStyleCodeDetail(row) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '订单量', formatter: (row) => row.orderCount !== null ? computedNum(row.orderCount) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'saleAmt', label: '销售金额', formatter: (row) => row.saleAmt !== null ? computedNum(row.saleAmt) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjzy', label: '占用资金', formatter: (row) => row.zjzy !== null ? computedNum(row.zjzy) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit33', label: '毛四利润', formatter: (row) => row.profit33 != null ? computedNum(row.profit33) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit5', label: '毛五利润', formatter: (row) => row.profit5 !== null ? computedNum(row.profit5) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'profit6', label: '毛六利润', formatter: (row) => row.profit6 !== null ? computedNum(row.profit6) : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zyzjIndex', label: '个人排名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'bzProfit6Index', label: '类目排名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'zjhbl', label: '回报率', formatter: (row) => row.zjhbl !== null ? row.zjhbl + '%' : '' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange,profileInformationStyleCodeDtl
    },
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        type: {
            type: String,
            default: ''
        },
        groupType: {
            type: Number,
            default: null
        },
        platform: {
            type: Number,
            default: null
        },
        versionId: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: this.type == 'main' ? 'zyzjIndex' : 'profit6Index',
                isAsc: true,
                versionId: this.versionId
            },
            timeRanges: [],
            tableCols,
            tableCols1,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible:false,
            dialogFilter:{},
            title:""
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const params = this.groupType == 4 ? { groupType: this.groupType, platform: this.platform, ...this.ListInfo } : { groupType: this.groupType, userId: this.item.userId, ...this.ListInfo }
                const { data, success } = await pageStyleCodeDirectorFullInfoWarDtlList(params)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                console.log(error);
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        openStyleCodeDetail(row){ 
            if (this.groupType == 4) {
                this.dialogFilter = { groupType: this.groupType, platform: this.platform,  versionId: this.versionId,
                    bzCategory: row.bzCategory, bzCategory1: row.bzCategory1, bzCategory2: row.bzCategory2 };
            }
            else{
                this.dialogFilter =  { groupType: this.groupType, userId: this.item.userId, versionId: this.versionId,
                    bzCategory: row.bzCategory, bzCategory1: row.bzCategory1, bzCategory2: row.bzCategory2 }; 
            }  
            this.title=row.bzCategory + "-" + row.bzCategory1 + "-" + row.bzCategory2;
            this.dialogVisible=true;
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
