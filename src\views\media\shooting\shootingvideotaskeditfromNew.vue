<template>
    <!-- 编辑操作 -->
    <my-container v-loading="pageLoading">
        <span class="drawer-header" style="z-index: 99;" v-if="addForm.shootingTaskId > 0">
            <div class="bjcz">
                <span class="bjczbt"> 编辑/操作 </span>
                <span class="bjcztb">
                    <i @click="onEndRestartAction"
                        v-if="checkPermission('api:media:shootingvideo:EndRestartActionAsync') && addForm.isend == 1"
                        style="margin-left: 6px;color:#c9c9c9" class="el-icon-document-delete hovericon" title="点击重启"></i>
                    <i @click="OnendShootingTaskAction"
                        v-if="checkPermission('api:media:shootingvideo:EndShootingTaskActionAsync') && addForm.isend == 0"
                        style="margin-left: 6px;color:#409eff" class="el-icon-document-checked hovericon" title="点击终止"></i>
                    <i @click="OnSignShootingTaskAction"
                        v-if="checkPermission('shootingtaskUrgencyEdit') && addForm.isTopOldNum == 0"
                        style="margin-left: 6px" class="el-icon-news hovericon" title="点击标记"></i>
                    <i @click="onUnSignShootingTaskActionShared"
                        v-if="checkPermission('shootingtaskUrgencyEdit') && addForm.isTopOldNum != 0"
                        style="margin-left: 6px;color:#409eff" class="el-icon-news hovericon" title="取消标记"></i>
                    <i style="margin-left: 6px" class="el-icon-odometer hovericon" title="催办任务(待开发)"></i>
                    <i @click="OnDeleteShootingTaskAction"
                        v-if="checkPermission('api:media:shootingvideo:deleteshootingtaskActionasync')"
                        style="margin-left: 6px" class="el-icon-delete hovericon" title="删除任务"></i>
                    <a href=""><i style="margin-left: 6px" class="el-icon-more hovericon" title="更多操作"></i></a>
                </span>
                <div class="rwmc">
                    <div class="xh" style="width: 50px">{{ addForm.shootingTaskId == 0 ? "" : addForm.shootingTaskId }}
                    </div>
                    <div class="mc" style="height: 70px;">|</div>
                    <div class="mc" style="width: 365px">
                        <div style="padding: 0;">
                            <el-tooltip v-if="inputshow" class="item" effect="dark"
                                :content="addForm.productShortName ? addForm.productShortName : ''" placement="top">
                                <div style="margin: 0; width: 100%; height: 50px;" class="linecs" @click="inputshowfunc">
                                    <span>{{ addForm.productShortName ? addForm.productShortName : '请点击输入产品简称' }}</span>
                                </div>
                            </el-tooltip>
                            <el-input v-else-if="!inputshow" @blur="inputshow = true" size="medium"
                                style="margin: 0; width: 100%; margin-top: -3px; font-size: 18px;"
                                v-model="addForm.productShortName" v-model.trim="addForm.productShortName" :maxlength=100
                                placeholder="产品简称" clearable />
                        </div>
                    </div>
                    <div v-if="!islook" class="icon"
                        style="width: 80px;height: 70px; line-height: 70px; float: right;margin-right: 60px;">
                        <el-button type="primary" @click="onSubmit">&nbsp;保&nbsp;存&nbsp;</el-button>
                    </div>
                </div>
            </div>
        </span>
        <el-form :model="addForm" ref="addForm" label-width="120px" :rules="calcAddFormRules" :disabled="islook"
            :class="addForm.shootingTaskId == 0 ? 'addform' : 'editform'">
            <el-row v-if="addForm.shootingTaskId == 0">
                <el-form-item prop="productShortName" label="产品简称">
                    <el-input style="width:300px" :clearable="true" v-model="addForm.productShortName"
                        v-model.trim="addForm.productShortName" :maxlength=100></el-input>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item prop="platform" label="平台">
                    <!-- :disabled="checkPermission('shootingUploadOutComFile') && addForm.shootingTaskId > 0" -->
                    <el-select style="width:120px;" v-model="addForm.platform" :clearable="true" :collapse-tags="true"
                        filterable @change="onchangeplatform">
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item prop="shopName" label="店铺">
                    <el-select style="width:260px;" v-model="addForm.shopName" :clearable="true" :collapse-tags="true"
                        @change="selShopInfoChange(addForm.shopName, index)" filterable>
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item prop="operationGroup" label="运营小组">
                    <el-select style="width:100px;" v-model="addForm.operationGroup" :clearable="true"
                        @change="selOpergroupInfoChange(addForm.operationGroup, index)" filterable>
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item prop="dockingPeople" label="对接人">
                    <el-input style="width:100px" :clearable="true" v-model="addForm.dockingPeople"
                        v-model.trim="addForm.dockingPeople" :maxlength=100></el-input>
                </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="4">
                <el-form-item label="紧急程度" style="padding-left: 11px;">
                  <el-select style="margin-left: -11px; width: 100px;" v-model="addForm.taskUrgency" :disabled="!checkPermission('shootingtaskUrgencyEdit')" filterable>
                    <el-option v-for="item in taskUrgencyList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <el-form-item style="padding-left: 11px; margin-left: 3px;">
                    <el-form :disabled="ischecklook" v-if="addForm.shootingTaskId != 0">
                      <el-checkbox  style="margin-left: -11px; width: 90px;"  v-model="addForm.fineStyle" :false-label="3" :true-label="1" border @change="onchangefineStyle">精品</el-checkbox>
                    </el-form>
                    <el-form :disabled="ischecklook" v-else>
                      <el-checkbox  style="margin-left: -11px; width: 90px;"  v-model="addForm.fineStyle" :false-label="3" :true-label="1" border>精品</el-checkbox>
                    </el-form>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
                <el-form-item prop="warehouse" label="拍摄样品">
                    <el-select v-model="addForm.warehouse" placeholder="请选择方式" style="width:200px;"
                        @change="selwarehouseChange()">
                        <el-option v-for="item in cwarehouselist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row
                v-if="addForm.warehouseStr == '运营寄样' || ((addForm.warehouseStr == '美工自购' || addForm.warehouseStr == '美工自购（测试）') && addForm.shootingTaskId > 0)">
                <el-form-item prop="yyOrderNum" label="订单号" style="padding-left: 11px;">
                    <el-input style="width:191px;margin-left: -11px;" :clearable="true" v-model.trim="addForm.yyOrderNum"
                        :maxlength=40></el-input>
                </el-form-item>
            </el-row>
            <el-row
                v-if="addForm.warehouseStr == '运营寄样' || ((addForm.warehouseStr == '美工自购' || addForm.warehouseStr == '美工自购（测试）') && addForm.shootingTaskId > 0)">
                <el-form-item prop="yyExpressNum" label="快递单号">
                    <el-input style="width:191px" :clearable="true" v-model.trim="addForm.yyExpressNum"
                        :maxlength=40></el-input>
                </el-form-item>
            </el-row>

            <el-row>
                <el-col :span="24">
                    <el-form-item label="拍摄任务" prop="shootingTaskPickList">
                        <el-checkbox-group v-model="addForm.shootingTaskPickList" @change="taskPickChange">
                            <el-checkbox label="1" :disabled="addForm.photoIsOver == 1" border>照片拍摄</el-checkbox>
                            <el-checkbox style="margin-left: -8px;" label="2" :disabled="addForm.vedioIsOver == 1"
                                border>主图视频</el-checkbox>
                            <el-checkbox style="margin-left: -8px;" label="3" :disabled="addForm.microDetailIsOver == 1"
                                border>微。视频</el-checkbox>
                            <el-checkbox style="margin-left: -8px;" label="4" :disabled="addForm.detailIsOver == 1"
                                border>详情排版</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row v-if="checkPermission('shootModelphoto')">
                <el-col :span="24">
                    <el-form-item label="建模任务" prop="shootingTaskPickList">
                        <el-checkbox-group v-model="addForm.shootingTaskPickList" @change="taskPickChange">
                            <el-checkbox label="5" :disabled="addForm.modelPhotosIsOver == 1" border>照片建模 </el-checkbox>
                            <el-checkbox style="margin-left: -8px;" label="6" :disabled="addForm.modelVideoIsOver == 1"
                                border>视频建模</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row v-if="checkPermission('shootFpNoTask')">
                <el-form-item prop="fpPhotoLqName" label="排除人员" style="padding-left: 11px;">
                    <el-select style="width:109px; margin-left: -11px;"
                        :disabled="addForm.shootingTaskId > 0 || fpPhotoLqNameEnable || addForm.photoIsOver == 1"
                        placeholder="排除照片" v-model="addForm.fpNoPhotoLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true" :multiple-limit="3">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('摄影') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                    <el-select style="width:109px; margin-left: 8px;"
                        :disabled="addForm.shootingTaskId > 0 || fpVideoLqNameEnable || addForm.vedioIsOver == 1"
                        placeholder="排除视频" v-model="addForm.fpNoVideoLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true" :multiple-limit="3">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('摄像') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                    <el-select style="width:109px;margin-left: 8px;"
                        :disabled="addForm.shootingTaskId > 0 || fpDetailLqNameEnable || addForm.detailIsOver == 1"
                        placeholder="排除详情" v-model="addForm.fpNoDetailLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true" :multiple-limit="3">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('美工') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                    <el-select style="width:109px;margin-left: 8px;"
                        :disabled="addForm.shootingTaskId > 0 || fpModelLqNameEnable || addForm.modelPhotosIsOver == 1 || addForm.modelVideoIsOver == 1"
                        placeholder="排除建模" v-model="addForm.fpNoModelLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true" :multiple-limit="3">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('建模') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row v-if="checkPermission('xppszdry')">
                <el-form-item prop="fpAssignPhotoLqName" label="指定人员" style="padding-left: 11px;">
                    <el-select style="width:109px; margin-left: -11px;"
                        :disabled="addForm.shootingTaskId > 0 || fpPhotoLqNameEnable || addForm.photoIsOver == 1"
                        placeholder="指定照片" v-model="addForm.fpAssignPhotoLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('摄影') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                    <el-select style="width:109px; margin-left: 8px;"
                        :disabled="addForm.shootingTaskId > 0 || fpVideoLqNameEnable || addForm.vedioIsOver == 1"
                        placeholder="指定视频" v-model="addForm.fpAssignVideoLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('摄像') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                    <el-select style="width:109px;margin-left: 8px;"
                        :disabled="addForm.shootingTaskId > 0 || fpDetailLqNameEnable || addForm.detailIsOver == 1"
                        placeholder="指定详情" v-model="addForm.fpAssignDetailLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('美工') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                    <el-select style="width:109px;margin-left: 8px;"
                        :disabled="addForm.shootingTaskId > 0 || fpModelLqNameEnable || addForm.modelPhotosIsOver == 1 || addForm.modelVideoIsOver == 1"
                        placeholder="指定建模" v-model="addForm.fpAssignModelLqName" :clearable="true" :collapse-tags="false"
                        filterable :multiple="true">
                        <el-option
                            v-for="item in userList.filter(item => item.workPositionStr && item.workPositionStr.indexOf('建模') != -1)"
                            :key="item.userName" :label="item.userName" :value="item.userName" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row v-if="checkPermission('shootFpTask') && addForm.shootingTaskId > 0">
                <el-form-item prop="fpPhotoLqName" label="分配任务" style="padding-left: 11px;">
                    <el-select style="width:109px; margin-left: -11px;"
                        :disabled="fpPhotoLqNameEnable || addForm.photoIsOver == 1" placeholder="分配照片"
                        v-model="addForm.fpPhotoLqName" :clearable="true" :collapse-tags="false" filterable>
                        <el-option v-for="item in userList" :key="item.userName" :label="item.userName"
                            :value="item.userName" :disabled="addForm.fpNoPhotoLqName?addForm.fpNoPhotoLqName.indexOf(item.userName) > -1:false" />
                            <!-- :value="item.userName" :disabled="item.userName == addForm.fpNoPhotoLqName" /> -->
                    </el-select>
                    <el-select style="width:109px; margin-left: 8px;"
                        :disabled="fpVideoLqNameEnable || addForm.vedioIsOver == 1" placeholder="分配视频"
                        v-model="addForm.fpVideoLqName" :clearable="true" :collapse-tags="false" filterable>
                        <el-option v-for="item in userList" :key="item.userName" :label="item.userName"
                            :value="item.userName" :disabled="addForm.fpNoVideoLqName?addForm.fpNoVideoLqName.indexOf(item.userName) > -1:false" />
                            <!-- :value="item.userName" :disabled="item.userName == addForm.fpNoVideoLqName" /> -->
                    </el-select>
                    <el-select style="width:109px; margin-left: 8px;"
                        :disabled="fpDetailLqNameEnable || addForm.detailIsOver == 1" placeholder="分配详情"
                        v-model="addForm.fpDetailLqName" :clearable="true" :collapse-tags="false" filterable>
                        <el-option v-for="item in userList" :key="item.userName" :label="item.userName"
                            :value="item.userName" :disabled="addForm.fpNoDetailLqName?addForm.fpNoDetailLqName.indexOf(item.userName) > -1:false" />
                            <!-- :value="item.userName" :disabled="item.userName == addForm.fpNoDetailLqName" /> -->
                    </el-select>
                    <el-select style="width:109px; margin-left: 8px;"
                        :disabled="fpModelLqNameEnable || addForm.modelPhotosIsOver == 1 || addForm.modelVideoIsOver == 1"
                        placeholder="分配建模" v-model="addForm.fpModelLqName" :clearable="true" :collapse-tags="false"
                        filterable>
                        <el-option v-for="item in userList" :key="item.userName" :label="item.userName"
                            :value="item.userName" :disabled="addForm.fpNoModelLqName?addForm.fpNoModelLqName.indexOf(item.userName) > -1:false" />
                            <!-- :value="item.userName" :disabled="item.userName == addForm.fpNoModelLqName" /> -->
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item label="参考方案" prop="referenceName" style="padding-left: 11px;">
                    <el-input style="width:260px; margin-left: -11px;" placeholder="请选择" v-model="addForm.referenceName"
                        :clearable="true"> {{ addForm.referenceName }}
                        <el-button style="padding-left: -11px" class="flexcenter" slot="append" icon="el-icon-plus"
                            @click="reference"></el-button>
                    </el-input>
                </el-form-item>
            </el-row>
            <el-row style="height: 150px; display: flex; flex-direction: row;margin-top: 5px;">
                <div class="flexrow">
                    <span style="margin-left: 10px; width: 80px; margin-right: 30px;" class="textsize">参考图片</span>
                    <div style="width: 190px;">
                        <uploadfile :minisize="false" v-if="addForm.photoUpfiles" ref="uploadimg" :islook="islook"
                            :uploadInfo="addForm.photoUpfiles" :limit="10000"
                            :accepttyes="'.image/jpg,image/jpeg,image/png'" :delfunction="deluplogimg" />
                    </div>
                </div>
                <div class="flexrow">
                    <span style="margin-right: 10px;  width: 60px;" class="textsize">参考附件</span>
                    <div style="width: 190px;">
                        <el-form :model="addForm" ref="addForm" label-width="190px" :rules="calcAddFormRules"
                        style="height: 150px;">
                            <uploadfile :minisize="false" v-if="addForm.pxeclUpfiles" ref="uploadexl" :islook="islook"
                                :isdown="true" :uploadInfo="addForm.pxeclUpfiles" :limit="10000" :accepttyes="'.xlsx,.mp4'"
                                :delfunction="deluplogexl" />
                        </el-form>
                    </div>
                </div>
            </el-row>
            <el-row>
                <el-col :span="24" style="margin-bottom: 25px;">
                    <el-input type="textarea" :rows="3" v-model="addForm.taskRemark" :maxlength="800" show-word-limit
                        placeholder="请输入备注" />
                </el-col>
            </el-row>
            <div  v-if="addForm.shootingTaskId == 0">
              <el-row>
                <el-col :span="24" style="margin-bottom: 25px;">
                  <el-button type="primary" style="width: 615px;height: 40px;" size="small" :disabled="false" @click="toResultmatt">人才资料</el-button>
                </el-col>
            </el-row>
            </div>
            <!-- ///////////////更改/////////////////////////////////// -->
            <div v-if="addForm.shootingTaskId > 0">
                <el-row v-if="checkPermission('shootingProductId')">
                    <el-form>
                        <div style="margin-bottom: 10px; height: 40px;">
                            <el-input :clearable="true" size="medium" v-model.trim="addForm.productID" :disabled="false"
                                :maxlength=40 placeholder="产品上架后请填写ID">
                                <el-button slot="append" size="medium" type="primary"
                                    @click="saveProductId">保存ID</el-button>
                            </el-input>
                        </div>

                    </el-form>
                </el-row>

                <!-- 操作选项 star -->
                <div class="bzczxx">
                    <el-menu class="el-menu-demo" mode="horizontal" collapse-transition active-text-color="#409eff">
                        <el-menu-item style="margin-left: 36px; line-height: 65px" index="1" @click="menuview = 1"><i
                                class="el-icon-set-up"></i>操作任务</el-menu-item>
                        <el-menu-item style="line-height: 65px" index="2" @click="menuview = 2">
                            <i class="el-icon-chat-line-round"></i> 驳回意见 <el-badge style="margin-top: -22px;"
                                v-if="bhinfoList.length > 0" :value="bhinfoList.length" /></el-menu-item>
                        <el-menu-item  v-if="checkPermission('alllistsRatinges') || contactPerson == addForm.dockingPeople" style="line-height: 65px" index="3" @click="menuview = 3"><i
                                class="el-icon-star-off"></i>评分</el-menu-item>
                    </el-menu>
                </div>
                <div class="bzbjfgx" v-show="menuview == 1">
                    <el-row v-if="checkPermission('shootingUploadOutComFile') && addForm.shootingTaskId > 0" style="margin-bottom: 30px;">
                        <el-button style="width:100%; height: 40px;font-size: 14px ;" size="small" type="primary"
                            :disabled="false" @click="toResultmatter($event, addForm.platform)">+上传成果文件</el-button>
                    </el-row>
                    <el-row class="flexrow textsize">
                        <span style="margin-right: 20px; width: 80px; line-height: 40px;">照片拍摄</span>
                        <div style="width: 100%;" class="flexroww textsize">
                            <div class="flexrow textsize">
                                <el-button size="mini" :type="(addForm.photoIsOver == 0 || (addForm.photoIsOver == 1 && addForm.photoLqBtnStr.indexOf('补改完成') > -1 )) && fpPhotoLqShow ? 'primary' : ''"
                                    :disabled="!fpPhotoLqShow || (islook && addForm.photoLqBtnStr !== '补改完成')"
                                    @click="pickTask(addForm.photoLqBtnStr, 1, addForm.shootingTaskId)">{{ fpPhotoLqShow ?
                                        addForm.photoLqBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px; margin-left: 10px;">{{ fpPhotoLqShow ? addForm.photoLqNameStr : ''
                                }}
                                </div>
                                <div style="width:80px; color: #999;">{{ fpPhotoLqShow ? addForm.photoOverTimeStr : '' }}
                                </div>
                                <div class="flexrow textsize">
                                  <el-button size="mini"
                                      :type="addForm.photoConfirmIsOver == 0 && fpPhotoLqShow ? 'primary' : ''"
                                      :disabled="!fpPhotoLqShow || islook || !checkPermission(['shootConfirmPhoto'])"
                                      @click="ConfirmTaskInfo(addForm.photoConfirmBtnStr, 1, addForm.shootingTaskId)">
                                      {{ fpPhotoLqShow ? addForm.photoConfirmBtnStr : '无此任务' }}
                                  </el-button>
                                  <div style="width:60px;margin-left: 10px;">{{ fpPhotoLqShow ? addForm.photoConfirmNameStr :
                                      ''
                                  }}
                                  </div>
                                  <div style="width:80px; color: #999;">
                                      <span>{{ fpPhotoLqShow ? addForm.photoConfirmTimeStr : '' }}</span>
                                  </div>
                                </div>
                                <div style="width:56px;">
                                  <el-button size="mini" type="primary" v-show="addForm.photoIsOver == 1"
                                    :disabled="addForm.photoLqBtnStr == '补改完成'" @click="onbhmarkclick(1)">补改</el-button>
                                </div>
                            </div>

                        </div>
                    </el-row>
                    <el-row class="flexrow textsize">
                        <span style="margin-right: 20px; width: 80px; line-height: 40px;">主图视频</span>
                        <div style="width: 100%;" class="flexroww textsize">
                            <div class="flexrow textsize">
                                <el-button size="mini" :type="(addForm.vedioIsOver == 0 || (addForm.vedioIsOver == 1  && addForm.vedioLqBtnStr.indexOf('补改完成') > -1 ) ) && fpVideoLqShow ? 'primary' : ''"
                                    :disabled="!fpVideoLqShow || (islook && addForm.vedioLqBtnStr !== '补改完成')"
                                    @click="pickTask(addForm.vedioLqBtnStr, 2, addForm.shootingTaskId)">{{ fpVideoLqShow ?
                                        addForm.vedioLqBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px; margin-left: 10px;">{{ fpVideoLqShow ? addForm.vedioLqNameStr : ''
                                }}
                                </div>
                                <div style="width:80px; color: #999;">{{ fpVideoLqShow ? addForm.vedioOverTimeStr : '' }}
                                </div>
                            </div>
                            <div class="flexrow textsize">
                                <el-button size="mini"
                                    :type="addForm.vedioConfirmIsOver == 0 && fpVideoLqShow ? 'primary' : ''"
                                    :disabled="!fpVideoLqShow || islook"
                                    @click="ConfirmTaskInfo(addForm.vedioConfirmBtnStr, 2, addForm.shootingTaskId)">
                                    {{ fpVideoLqShow ? addForm.vedioConfirmBtnStr : '无此任务' }}
                                </el-button>
                                <div style="width:60px;margin-left: 10px;">{{ fpVideoLqShow ? addForm.vedioConfirmNameStr :
                                    ''
                                }}
                                </div>
                                <div style="width:80px; color: #999;">
                                    <span>{{ fpVideoLqShow ? addForm.vedioConfirmTimeStr : '' }}</span>
                                </div>
                            </div>
                            <!-- v-if="addForm.detailIsOver==1"   :disabled="addForm.vedioConfirmIsOver==1"  -->
                            <div style="width:56px;">
                                <el-button size="mini" type="primary" v-show="addForm.vedioIsOver == 1"
                                    :disabled="addForm.vedioLqBtnStr == '补改完成'" @click="onbhmarkclick(2)">补改</el-button>
                            </div>
                        </div>
                    </el-row>
                    <el-row class="flexrow textsize">
                        <span style="margin-right: 20px; width: 80px; line-height: 40px;">微。视频</span>
                        <div style="width: 100%;" class="flexroww textsize">
                            <div class="flexrow textsize">
                                <el-button size="mini" :disabled="!fpMicroDetailLqShow || (islook && addForm.microDetailBtnStr !== '补改完成')"
                                    :type="(addForm.microDetailIsOver == 0 || (addForm.microDetailIsOver == 1 && addForm.microDetailBtnStr.indexOf('补改完成') > -1 ) ) && fpMicroDetailLqShow ? 'primary' : ''"
                                    @click="pickTask(addForm.microDetailBtnStr, 3, addForm.shootingTaskId)">{{
                                        fpMicroDetailLqShow ? addForm.microDetailBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px; margin-left: 10px;">
                                    {{ fpMicroDetailLqShow ? addForm.microDetailLqNameStr : '' }}</div>
                                <div style="width:80px; color: #999;">{{ fpMicroDetailLqShow ?
                                    addForm.microDetailOverTimeStr :
                                    '' }}
                                </div>
                            </div>
                            <div class="flexrow textsize">
                                <el-button size="mini" :disabled="!fpMicroDetailLqShow || islook"
                                    :type="addForm.microDetailConfirmIsOver == 0 && fpMicroDetailLqShow ? 'primary' : ''"
                                    @click="ConfirmTaskInfo(addForm.microDetailConfirmBtnStr, 3, addForm.shootingTaskId)">{{
                                        fpMicroDetailLqShow ? addForm.microDetailConfirmBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px;margin-left: 10px;">
                                    {{ fpMicroDetailLqShow ? addForm.microDetailConfirmNameStr : '' }}</div>
                                <div style="width:80px; color: #999;">
                                    <span>{{ fpMicroDetailLqShow ? addForm.microDetailConfirmTimeStr : '' }}</span>
                                </div>
                            </div>
                            <div style="width:56px;">
                                <el-button size="mini" type="primary" v-show="addForm.microDetailIsOver == 1"
                                    :disabled="addForm.microDetailBtnStr == '补改完成'" @click="onbhmarkclick(3)">补改</el-button>
                            </div>
                        </div>
                    </el-row>
                    <el-row class="flexrow textsize">
                        <span style="margin-right: 20px; width: 80px; line-height: 40px;">详情排版</span>
                        <div style="width: 100%;" class="flexroww textsize">
                            <div class="flexrow textsize">
                                <el-button size="mini" :disabled="!fpDetailLqShow || (islook && addForm.detailLqBtnStr !== '补改完成')"
                                    :type="(addForm.detailIsOver == 0 || (addForm.detailIsOver == 1  && addForm.detailLqBtnStr.indexOf('补改完成') > -1 )) && fpDetailLqShow ? 'primary' : ''"
                                    @click="pickTask(addForm.detailLqBtnStr, 4, addForm.shootingTaskId)">{{ fpDetailLqShow ?
                                        addForm.detailLqBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px; margin-left: 10px;">{{ fpDetailLqShow ? addForm.detailLqNameStr : ''
                                }}
                                </div>
                                <div style="width:80px; color: #999;">{{ fpDetailLqShow ? addForm.detailOverTimeStr : '' }}
                                </div>
                            </div>
                            <div class="flexrow textsize">
                                <el-button size="mini" :disabled="!fpDetailLqShow || islook"
                                    :type="addForm.detailConfirmIsOver == 0 && fpDetailLqShow ? 'primary' : ''"
                                    @click="ConfirmTaskInfo(addForm.detailConfirmBtnStr, 4, addForm.shootingTaskId)">{{
                                        fpDetailLqShow ? addForm.detailConfirmBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px;margin-left: 10px;">{{ fpDetailLqShow ? addForm.detailConfirmNameStr
                                    : ''
                                }}
                                </div>
                                <div style="width:80px; color: #999;">
                                    <span>{{ fpDetailLqShow ? addForm.detailConfirmTimeStr : '' }}</span>
                                </div>
                            </div>
                            <div style="width:56px;">
                                <el-button size="mini" type="primary" v-show="addForm.detailIsOver == 1"
                                    :disabled="addForm.detailLqBtnStr == '补改完成'" @click="onbhmarkclick(4)">补改</el-button>
                            </div>
                        </div>
                    </el-row>
                    <el-row class="flexrow textsize">
                        <span style="margin-right: 20px; width: 80px; line-height: 40px;">建模照片</span>
                        <div style="width: 100%;" class="flexroww textsize">
                            <div class="flexrow textsize">
                                <el-button size="mini" :disabled="!fpModelPhotoLqShow || (islook && addForm.modelPhotoBtnStr !== '补改完成')"
                                    :type="(addForm.modelPhotosIsOver == 0 || (addForm.modelPhotosIsOver == 1 && addForm.modelPhotoBtnStr.indexOf('补改完成') > -1 ) ) && fpModelPhotoLqShow ? 'primary' : ''"
                                    @click="pickTask(addForm.modelPhotoBtnStr, 5, addForm.shootingTaskId)">{{
                                        fpModelPhotoLqShow
                                        ? addForm.modelPhotoBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px; margin-left: 10px;">
                                    {{ fpModelPhotoLqShow ? addForm.modelPhotosLqNameStr : '' }}</div>
                                <div style="width:80px; color: #999;">{{ fpModelPhotoLqShow ? addForm.modelPhotosOverTimeStr
                                    :
                                    '' }}
                                </div>
                                <div class="flexrow textsize">
                                  <el-button size="mini"
                                      :type="addForm.modelPhotosConfirmIsOver == 0 && fpModelPhotoLqShow ? 'primary' : ''"
                                      :disabled="!fpModelPhotoLqShow || islook || !checkPermission(['shootConfirmPhoto'])"
                                      @click="ConfirmTaskInfo(addForm.modelPhotosConfirmBtnStr, 5, addForm.shootingTaskId)">
                                      {{ fpModelPhotoLqShow ? addForm.modelPhotosConfirmBtnStr : '无此任务' }}
                                  </el-button>
                                  <div style="width:60px;margin-left: 10px;">{{ fpModelPhotoLqShow ? addForm.modelPhotosConfirmNameStr : '' }}
                                  </div>
                                  <div style="width:80px; color: #999;">
                                      <span>{{ fpModelPhotoLqShow ? addForm.modelPhotosConfirmTimeStr : '' }}</span>
                                  </div>
                                </div>
                                <div style="width:56px;">
                                  <el-button size="mini" type="primary" v-show="addForm.modelPhotosIsOver == 1"
                                    :disabled="addForm.modelPhotoBtnStr == '补改完成'" @click="onbhmarkclick(5)">补改</el-button>
                                </div>
                            </div>
                        </div>
                    </el-row>
                    <el-row class="flexrow textsize">
                        <span style="margin-right: 20px; width: 80px; line-height: 40px;">建模视频</span>
                        <div style="width: 100%;" class="flexroww textsize">
                            <div class="flexrow textsize">
                                <el-button size="mini" :disabled="!fpModelVideoLqShow || (islook && addForm.modelVideoBtnStr !== '补改完成')"
                                    :type="(addForm.modelVideoIsOver == 0 || (addForm.modelVideoIsOver == 1 && addForm.modelVideoBtnStr.indexOf('补改完成') > -1 )) && fpModelVideoLqShow ? 'primary' : ''"
                                    @click="pickTask(addForm.modelVideoBtnStr, 6, addForm.shootingTaskId)">{{
                                        fpModelVideoLqShow
                                        ? addForm.modelVideoBtnStr : '无此任务' }}</el-button>
                                <div style="width:60px; margin-left: 10px;">
                                    {{ fpModelVideoLqShow ? addForm.modelVideoLqNameStr : '' }}</div>
                                <div style="width:80px; color: #999;">{{ fpModelVideoLqShow ? addForm.modelVideoOverTimeStr
                                    :
                                    '' }}
                                </div>
                                <div class="flexrow textsize">
                                  <el-button size="mini"
                                      :type="addForm.modelVideoConfirmIsOver == 0 && fpModelVideoLqShow ? 'primary' : ''"
                                      :disabled="!fpModelVideoLqShow || islook || !checkPermission(['shootConfirmVido'])"
                                      @click="ConfirmTaskInfo(addForm.modelVideoConfirmBtnStr, 6, addForm.shootingTaskId)">
                                      {{ fpModelVideoLqShow ? addForm.modelVideoConfirmBtnStr : '无此任务' }}
                                  </el-button>
                                  <div style="width:60px;margin-left: 10px;">{{ fpModelVideoLqShow ? addForm.modelVideoConfirmNameStr :
                                      ''
                                  }}
                                  </div>
                                  <div style="width:80px; color: #999;">
                                      <span>{{ fpModelVideoLqShow ? addForm.modelVideoConfirmTimeStr : '' }}</span>
                                  </div>
                                </div>
                                <div style="width:56px;">
                                  <el-button size="mini" type="primary" v-show="addForm.modelVideoIsOver == 1"
                                    :disabled="addForm.modelVideoBtnStr == '补改完成'" @click="onbhmarkclick(6)">补改</el-button>
                                </div>
                            </div>

                        </div>
                    </el-row>
                    <!-- <el-row style="margin-top: 50px;">
                        <el-col :span="24" v-if="addForm.shootingTaskId > 0">
                            <el-form-item label="" label-width="0" prop="loginfo">
                                <el-input type="textarea" :rows="5" v-model="loginfo" :disabled="true" placeholder="操作日志" />
                            </el-form-item>
                        </el-col>
                    </el-row> -->
                </div>
                       <!-- 操作日志 star -->
                       <div class="bzbjfgx" v-show="menuview == 1" style="min-height:160px">
                        <div class="bzczrzx" v-for="(item,index ) in loginfo " :key="index+'log'">
                            <div>
                            <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="item.avatar"></el-avatar>
                            </div>
                            <div class="rzmz">{{ item.name }}</div>
                            <div class="rzxgx">
                                {{ item.changeinfo }}
                            </div>
                            <div class="rzxgsj">  {{ item.time }}</div>
                            </div>
                            <div>
                            <div class="rzbox">
                            <div class="rzxgq">修改后：</div>
                            <div class="rzxgnr">{{ item.after }}</div>
                            </div>
                            </div>
                            <div>
                            <div class="rzbox">
                                <div class="rzxgq">修改前：</div>
                                <div class="rzxgnr">{{ item.before }}</div>
                            </div>
                            </div>
                        </div>

                        </div>
            <!-- 操作日志 end -->
                <div class="bzbjfgx" v-show="menuview == 2" style="min-height:300px">
                    <!-- 驳回信息 -->
                    <div class="bzczrzx" v-for="(item, index ) in bhinfoList " :key="index">
                        <div>
                            <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="item.avatar"></el-avatar>
                            </div>
                            <div class="rzmz">{{ item.name }}</div>
                            <div class="rzxgx">
                                {{ item.typemsg}}
                            </div>
                            <div class="rzxgsj"> {{ item.time }}</div>
                        </div>
                        <div class="rzbox">
                            <div class="rzxgq">意见：</div>
                            <div class="rzxgnr">{{ item.info }}</div>
                        </div>
                    </div>
                    <!-- 驳回信息end -->
                </div>
                <!-- 评分 -->
                <el-form>
                    <div class="pfx" v-show="menuview == 3">
                      <div style="color: black;font-size: 18px;font-weight: bold; padding: 5px 0px;">质量评分</div>
                        <div v-show="fpPhotoLqShow">
                            <div class="rwlx">照片拍摄</div>
                            <div class="czpf">
                                <el-rate v-model="photoScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="photoScore.typeid != null && !photoScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="photoScore.typeid!=null? photoScore.canUp :true" :plain="true" size="mini" @click="saveScore(1,photoScore.score)">提交</el-button>
                            <span v-if="photoScore.typeid" style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="photoScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ photoScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpVideoLqShow">
                            <div class="rwlx">主图视频</div>
                            <div class="czpf">
                                <el-rate v-model="vedioScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="vedioScore.typeid != null && !vedioScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="vedioScore.typeid!=null? vedioScore.canUp :true" :plain="true" size="mini" @click="saveScore(2,vedioScore.score)">提交</el-button>
                            <span v-if="vedioScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="vedioScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ vedioScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpMicroDetailLqShow">
                            <div class="rwlx">微。视频</div>
                            <div class="czpf">
                                <el-rate v-model="microDetailScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="microDetailScore.typeid != null && !microDetailScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="microDetailScore.typeid!=null? microDetailScore.canUp :true" :plain="true" size="mini" @click="saveScore(3,microDetailScore.score)">提交</el-button>
                            <span v-if="microDetailScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="microDetailScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ microDetailScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpDetailLqShow">
                            <div class="rwlx">详情排版</div>
                            <div class="czpf">
                                <el-rate v-model="detailScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="detailScore.typeid != null && !detailScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="detailScore.typeid!=null? detailScore.canUp :true" :plain="true" size="mini" @click="saveScore(4,detailScore.score)">提交</el-button>
                            <span v-if="detailScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="detailScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ detailScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpModelPhotoLqShow">
                            <div class="rwlx">照片建模</div>
                            <div class="czpf">
                                <el-rate v-model="modelPhotoScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="modelPhotoScore.typeid != null && !modelPhotoScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="modelPhotoScore.typeid!=null? modelPhotoScore.canUp :true" :plain="true" size="mini" @click="saveScore(5,modelPhotoScore.score)">提交</el-button>
                            <span v-if="modelPhotoScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="modelPhotoScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ modelPhotoScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpModelVideoLqShow">
                            <div class="rwlx">视频建模</div>
                            <div class="czpf">
                                <el-rate v-model="modelVideoScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="modelVideoScore.typeid != null && !modelVideoScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="modelVideoScore.typeid!=null? modelVideoScore.canUp :true" :plain="true" size="mini" @click="saveScore(6,modelVideoScore.score)">提交</el-button>
                            <span v-if="modelVideoScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="modelVideoScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ modelVideoScore.time }}</div>
                            </span>
                        </div>
                    </div>
                </el-form>
                <el-form>
                    <div class="pfx" v-show="menuview == 3">
                      <div style="color: black;font-size: 18px;font-weight: bold; padding: 5px 0px;">配合度评分</div>
                        <div v-show="fpPhotoLqShow">
                            <div class="rwlx">照片拍摄</div>
                            <div class="czpf">
                                <el-rate v-model="phdPhotoScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="phdPhotoScore.typeid != null && !phdPhotoScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="phdPhotoScore.typeid!=null? phdPhotoScore.canUp :true" :plain="true" size="mini" @click="saveScore(11,phdPhotoScore.score)">提交</el-button>
                            <span v-if="phdPhotoScore.typeid" style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="phdPhotoScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ phdPhotoScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpVideoLqShow">
                            <div class="rwlx">主图视频</div>
                            <div class="czpf">
                                <el-rate v-model="phdVedioScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="phdVedioScore.typeid != null && !phdVedioScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="phdVedioScore.typeid!=null? phdVedioScore.canUp :true" :plain="true" size="mini" @click="saveScore(12,phdVedioScore.score)">提交</el-button>
                            <span v-if="phdVedioScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="phdVedioScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ phdVedioScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpMicroDetailLqShow">
                            <div class="rwlx">微。视频</div>
                            <div class="czpf">
                                <el-rate v-model="phdMicroDetailScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="phdMicroDetailScore.typeid != null && !phdMicroDetailScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="phdMicroDetailScore.typeid!=null? phdMicroDetailScore.canUp :true" :plain="true" size="mini" @click="saveScore(13,phdMicroDetailScore.score)">提交</el-button>
                            <span v-if="phdMicroDetailScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="phdMicroDetailScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ phdMicroDetailScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpDetailLqShow">
                            <div class="rwlx">详情排版</div>
                            <div class="czpf">
                                <el-rate v-model="phdDetailScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="phdDetailScore.typeid != null && !phdDetailScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="phdDetailScore.typeid!=null? phdDetailScore.canUp :true" :plain="true" size="mini" @click="saveScore(14,phdDetailScore.score)">提交</el-button>
                            <span v-if="phdDetailScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="phdDetailScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ phdDetailScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpModelPhotoLqShow">
                            <div class="rwlx">照片建模</div>
                            <div class="czpf">
                                <el-rate v-model="phdModelPhotoScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="phdModelPhotoScore.typeid != null && !phdModelPhotoScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="phdModelPhotoScore.typeid!=null? phdModelPhotoScore.canUp :true" :plain="true" size="mini" @click="saveScore(15,phdModelPhotoScore.score)">提交</el-button>
                            <span v-if="phdModelPhotoScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="phdModelPhotoScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ phdModelPhotoScore.time }}</div>
                            </span>
                        </div>
                        <div v-show="fpModelVideoLqShow">
                            <div class="rwlx">视频建模</div>
                            <div class="czpf">
                                <el-rate v-model="phdModelVideoScore.score" show-score text-color="#ff9900" score-template="{value}" :allow-half="true" :disabled="phdModelVideoScore.typeid != null && !phdModelVideoScore.canUp">
                                </el-rate>
                            </div>
                            <el-button type="primary" v-if="phdModelVideoScore.typeid!=null? phdModelVideoScore.canUp :true" :plain="true" size="mini" @click="saveScore(16,phdModelVideoScore.score)">提交</el-button>
                            <span v-if="phdModelVideoScore.typeid"  style="margin-left:100px">
                                <div class="rztx">
                                <el-avatar :size="25" fit="cover" :src="phdModelVideoScore.avatar"></el-avatar>
                            </div>
                            <div class="rzxgsj">  {{ phdModelVideoScore.time }}</div>
                            </span>
                        </div>
                    </div>
                </el-form>
                <!-- 评分 -->
            </div>
        </el-form>
        <el-dialog title="参考列表" :visible.sync="dialogVisible" width="35%" :append-to-body="true">
            <shootingreferencelist ref="shootingreferencelist" style="z-index:10000;height:400px"></shootingreferencelist>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关 闭</el-button>
                    <el-button type="primary" @click="selreference()">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog title="驳回意见" :visible.sync="bhmarkVisible" width='30%'   v-dialogDrag append-to-body>
          <el-input  type="textarea"  :rows="12"
           :maxlength="800" show-word-limit   placeholder="请输入内容"
            v-model.trim="bhmark">  </el-input>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="bhmarkVisible = false">取 消</el-button>
                    <el-button type="primary" :disabled="bhsumbit" @click="bhmarkclick()">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import uploadfile from '@/views/media/shooting/uploadfile';
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {
    addOrUpdateShootingVideoTaskAsync, getShootingTaskFliesAsync, delShootingTploadFileTaskAsync, pickShootingTaskAsync, saveProductId,
    signShootingTaskActionAsync, deleteShootingTaskActionAsync, endShootingTaskActionAsync, endRestartActionAsync, unSignShootingTaskActionAsync,
    unPickShootingTaskAsync, confrimShootingTaskAsync, unConfrimShootingTaskAsync, saveShootingBhInfo,saveShootingScoreInfo, fineStyleSet
} from '@/api/media/ShootingVideo';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatWarehouse } from "@/utils/tools";
import shootingreferencelist from '@/views/media/shooting/fuJianmanage/shootingreferencelistSel';
import checkPermission from '@/utils/permission'
import { getCurrentUser } from '@/api/inventory/packagesprocess'
export default {
    props: {
        taskUrgencyList: { type: Array, default: [] },
        userList: { type: Array, default: [] },
        groupList: { type: Array, default: [] },
        platformList: { type: Array, default: [] },
        warehouselist: { type: Array, default: [] },
        onCloseAddForm: { type: Function, default: null },
        islook: { type: Boolean, default: false },
        ischecklook: { type: Boolean, default: false },
    },
    components: { MyContainer, MyConfirmButton, uploadfile, shootingreferencelist },
    watch: {},
    data () {
        return {
            contactPerson: null,
            photoScore: {score:0},
            vedioScore: {score:0},
            microDetailScore: {score:0},
            detailScore: {score:0},
            modelPhotoScore: {score:0},
            modelVideoScore: {score:0},
            phdPhotoScore: {score:0},
            phdVedioScore: {score:0},
            phdMicroDetailScore: {score:0},
            phdDetailScore: {score:0},
            phdModelPhotoScore: {score:0},
            phdModelVideoScore: {score:0},
            menuview: 1,
            bhmark: null,
            bhmarkVisible: false,
            typeid:null,
            bhinfoList: [],
            bhsumbit:false,
            that: this,
            inputshow: true,
            dialogVisible: false,
            pageLoading: false,
            formatWarehouse: formatWarehouse,
            shopList: [],
            taskPhotofileList: [],
            taskExeclfileList: [],
            cwarehouselist: [],
            addForm: {
                fineStyle: 3,
                shootingTaskId: 0,
                photoIsOver: 0,
                vedioIsOver: 0,
                microDetailIsOver: 0,
                detailIsOver: 0,
                modelPhotosIsOver: 0,
                modelVideoIsOver: 0,
                fpPhotoLqName: null,
                fpVideoLqName: null,
                fpDetailLqName: null,
                fpModelLqName: null,
                fpNoPhotoLqName: null,
                fpNoVideoLqName: null,
                fpNoDetailLqName: null,
                fpNoModelLqName: null,
                fpAssignDetailLqId: null,
                fpAssignDetailLqName: null,
                fpAssignModelLqId: null,
                fpAssignModelLqName: null,
                fpAssignPhotoLqId: null,
                fpAssignPhotoLqName: null,
                fpAssignVideoLqId: null,
                fpAssignVideoLqName: null,
                shopName: null,
                shopNameStr: null,
                platform: null,
                productShortName: null,
                operationGroup: null,
                operationGroupstr: null,
                taskUrgency: 9,
                dockingPeople: null,
                warehouse: null,
                warehouseStr: null,
                taskDate: null,
                isReissue: null,
                isDelivered: null,
                taskRemark: null,
                shootingTaskPick: 0,
                shootingTaskPickList: [],
                photoUpfiles: [],
                pxeclUpfiles: [],
                referenceName: null,
                referenceId: 0,
                yyOrderNum: null,
                isYYJY: 0,
                yyExpressNum: null,
                productID: null,
                isTopOldNum: 0,
                isend: 0
            },
            loginfo: null,
            fpPhotoLqShow: true,
            fpVideoLqShow: true,
            fpMicroDetailLqShow: true,
            fpDetailLqShow: true,
            fpModelPhotoLqShow: true,
            fpModelVideoLqShow: true,
            fpPhotoLqNameEnable: false,
            fpVideoLqNameEnable: false,
            fpDetailLqNameEnable: false,
            fpModelLqNameEnable: false,
            addFormRules: {
                productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                referenceName: [{ required: true, message: '请选择', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],

            },
            extBzTypeArgs: null,
        };
    },
    watch: {},
    async created () { },
    async mounted () {
        const { data,success } = await getCurrentUser();
        if (success) {
            this.contactPerson = data.userName;
        }
        for (let num in this.warehouselist) {
            this.cwarehouselist.push(this.warehouselist[num]);
        }
    },
    computed: {
        calcAddFormRules () {
            return {
                productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                /* referenceName: [{ required: true, message: '请选择', trigger: 'blur' }],   */
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],
                isYYJY: [{ required: true, message: '请选择', trigger: 'blur' }],
                yyExpressNum: [{ required: true, message: '请填写', trigger: 'blur' }],
            }
          },
    },
    methods: {
        async onchangefineStyle(){
            let _this = this;
            const params = { taskId: this.addForm.shootingTaskId, fineStyle: this.addForm.fineStyle }
            const { success } = await fineStyleSet(params)
            if (success) {
                this.$message({ message: '修改成功', type: 'success' });
                _this.onCloseAddForm(1);
            }
        },
    //人才资料网页
    toResultmatt(){
          let routeUrl = this.$router.resolve({
              path: '/packdesgintalentmanage',
          });
          window.open(routeUrl.href, '_blank');
      },
        saveScore (typeid, score) {
            saveShootingScoreInfo({ taskid: this.addForm.shootingTaskId, score: score, typeid: typeid }).then(res => {
                if (res?.success) {
                    //  this.getNewTaskInfo();
                 this.$message({ message: '评分成功', type: "success" });
                 this.editTask({ shootingTaskId: this.addForm.shootingTaskId });

            }
           })
        },
        //驳回意见
      onbhmarkclick(type){
            this.bhmarkVisible = true;
            this.bhmark = null;
            this.typeid = type;
        },
        //驳回意见填写
        async bhmarkclick () {
            if (!this.bhmark) {
                this.$message({ message: '请填写驳回意见', type: "warning" });
                return;
            }
            this.bhsumbit = true;
            var res = await saveShootingBhInfo({ taskid: this.addForm.shootingTaskId, bhinfo: this.bhmark, typeid: this.typeid });
            if (res?.success) {
                // await this.getNewTaskInfo();
                await this.editTask({ shootingTaskId: this.addForm.shootingTaskId });
                this.bhmark = null;
                this.bhmarkVisible = false;
            }
            this.onCloseAddForm(0);
            this.bhsumbit = false;
        },
        async getNewTaskInfo () {
            this.pageLoading = true;
            var res = await getShootingTaskFliesAsync({ taskid: this.addForm.shootingTaskId });
            if (res?.success) {
                this.loginfo = res.data.loginfo;
                this.bhinfoList = res.data.bhinfo;
                let index1 = res.data.scoreinfo.findIndex(item => item.typeid == 1);
                if (index1 > -1) {
                    this.photoScore = res.data.scoreinfo[index1];
                }
                let index2 = res.data.scoreinfo.findIndex(item => item.typeid == 2);
                if (index2 > -1) {
                    this.vedioScore = res.data.scoreinfo[index2];
                }
                let index3 = res.data.scoreinfo.findIndex(item => item.typeid == 3);
                if (index3 > -1) {
                    this.microDetailScore = res.data.scoreinfo[index3];
                }
                let index4 = res.data.scoreinfo.findIndex(item => item.typeid == 4);
                if (index4 > -1) {
                    this.detailScore = res.data.scoreinfo[index4];
                }
                let index5 = res.data.scoreinfo.findIndex(item => item.typeid == 5);
                if (index5 > -1) {
                    this.modelPhotoScore = res.data.scoreinfo[index5];
                }
                let index6 = res.data.scoreinfo.findIndex(item => item.typeid == 6);
                if (index6 > -1) {
                    this.modelVideoScore = res.data.scoreinfo[index6];
                }
            }
            this.pageLoading = false;
        },
        selwarehouseChange () {
            for (let num in this.warehouselist) {
                if (this.warehouselist[num].value == this.addForm.warehouse) {
                    this.addForm.warehouseStr = this.warehouselist[num].label;
                }
            }
        },
        //保存产品ID
        async saveProductId() {
            var res = await saveProductId({ taskId: this.addForm.shootingTaskId, productId: this.addForm.productID });
            if (res?.success) {
                this.$message({ message: '保存成功', type: "success" });
            }
        },
        selShopInfoChange (val) {
            let resultArr = this.shopList.find((item) => {
                return item.shopCode == val;
            });
            this.addForm.shopNameStr = resultArr.shopName;
        },
        selOpergroupInfoChange (val) {
            var resultArr = this.groupList.find((item) => {
                return item.value == val;
            });
            this.addForm.operationGroupstr = resultArr.label;
        },
        inputshowfunc () {
            if (this.islook) return;
            this.inputshow = false;
        },
        toResultmatter (row, mid) {
            window["platform"] = mid;
            if (mid == 2) {
                if (row != null) {
                    let routeUrl = this.$router.resolve({
                        path: '/resultmatter',
                        query: { id: this.addForm.shootingTaskId, platform: mid }
                    });
                    window.open(routeUrl.href, '_blank');
                }
            } else {
                if (row != null) {
                    let routeUrl = this.$router.resolve({
                        path: '/resultmatter',
                        query: { id: this.addForm.shootingTaskId, platform: mid }
                    });
                    window.open(routeUrl.href, '_blank');
                }
            }

        },
        async selreference () {
            await this.$nextTick(function () {
                var ret = this.$refs.shootingreferencelist.getSelectRow();
                this.addForm.referenceName = ret.taskName;
                this.addForm.referenceId = ret.referenceManageTaskId;
                this.dialogVisible = false;
            })
        },
        reference () {
            this.dialogVisible = true;
        },
        //编码
        taskPickChange (value) {
            if (!value)
                return;
            //未选择，只读，且清空
            if (value.indexOf("1") < 0) {
                this.fpPhotoLqNameEnable = true;
            } else {
                this.fpPhotoLqNameEnable = false;
            }
            if (value.indexOf("2") < 0) {
                this.fpVideoLqNameEnable = true;
            } else {
                this.fpVideoLqNameEnable = false;
            }
            if (value.indexOf("3") < 0) {
            } else {
            }
            if (value.indexOf("4") < 0) {
                this.fpDetailLqNameEnable = true;
            } else {
                this.fpDetailLqNameEnable = false;
            }
            if (value.indexOf("5") < 0 && value.indexOf("6") < 0) {
                this.fpModelLqNameEnable = true;
            } else {
                this.fpModelLqNameEnable = false;
            }
        },
        async onchangeplatform (val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 10000 });
            this.addForm.shopName = "";
            this.shopList = res1.data.list;
        },
        //确认Or取消任务
        async ConfirmTaskInfo (btnstr, index, shootingTaskId) {
            var that = this;
            if(index==1 || index==5){
              if (!checkPermission(['shootConfirmPhoto'])) {
                this.$message({
                      message: '你没有操作权限信息!',
                      type: 'warning'
                    });
                  return;
            }
            }
            if(index==6){
              if (!checkPermission(['shootConfirmVido'])) {
                this.$message({
                      message: '你没有操作权限信息!',
                      type: 'warning'
                    });
                  return;
            }
            }
            switch (btnstr) {
                case "摄影确认":
                case "运营确认":
                case "美工确认":
                    this.$confirm("确认完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(async () => {
                            var res = await confrimShootingTaskAsync({ taskid: shootingTaskId, index: index });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await that.editTask({ shootingTaskId: shootingTaskId });
                                await this.onCloseAddForm(0);
                            }
                        });
                    break;
                case "取消确认":
                    this.$confirm("确认取消, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(async () => {
                            var res = await unConfrimShootingTaskAsync({ taskid: shootingTaskId, index: index });
                            if (res?.success) {
                                that.$message({ message: '操作成功', type: "success" });
                                await that.editTask({ shootingTaskId: shootingTaskId });
                                await this.onCloseAddForm(0);
                            }
                        });
                    break;
            }

        },
        //完成任务
        async pickTask (btnstr, index, shootingTaskId) {
            var that = this;
            switch (btnstr) {
                case "补改完成":
                this.$confirm("确认完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        var res = await pickShootingTaskAsync({ taskid: shootingTaskId, index: index ,makeup:1});
                        if (res?.success) {
                            that.$message({ message: '完成成功', type: "success" });
                            await that.editTask({ shootingTaskId: shootingTaskId });
                            await that.onCloseAddForm(0);
                        }
                    });
                    break;
                case "标记完成":
                    this.$confirm("确认完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        var res = await pickShootingTaskAsync({ taskid: shootingTaskId, index: index});
                        if (res?.success) {
                            that.$message({ message: '完成成功', type: "success" });
                            await that.editTask({ shootingTaskId: shootingTaskId });
                            await that.onCloseAddForm(0);
                        }
                    });
                    break;
                case "取消完成":
                    this.$confirm("确认取消完成, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(async () => {
                            var res = await unPickShootingTaskAsync({ taskid: shootingTaskId, index: index})
                            if (res?.success) {
                                that.$message({ message: '取消成功', type: "success" });
                                await that.editTask({ shootingTaskId: shootingTaskId });
                                await that.onCloseAddForm(0);
                            }
                        });
                    break;
            }
        },
        //编辑任务
        async editTask (row) {
            this.pageLoading = true;
            await this.onchangeplatform(row.platform);
            this.taskPickChange(row.shootingTaskPickList);
            //获取拍摄上传的附件
            var res = await getShootingTaskFliesAsync({ taskid: row.shootingTaskId });
            this.addForm.photoPlay = 0
            this.addForm.vedioPlay = 0;
            this.addForm.microDetailPlay = 0;
            this.addForm.detailPlay = 0;
            this.addForm.modelPhotosPlay = 0;
            this.addForm.referenceName = row.referenceName;
            this.addForm.referenceId = row.referenceId;
            this.addForm.photoUpfiles = [];
            this.addForm.pxeclUpfiles = [];
            this.addForm.isYYJY = 0;
            this.loginfo = null;
            if (res?.success) {
                res.data.data.forEach(element => {
                    if (element.upLoadType == 1) {
                        this.addForm.photoUpfiles.push(element);
                    }
                    if (element.upLoadType == 2) {
                        this.addForm.pxeclUpfiles.push(element);
                    }
                });
                this.$nextTick(function () {
                    this.$refs.uploadimg.setData(this.addForm.photoUpfiles);
                    this.$refs.uploadexl.setData(this.addForm.pxeclUpfiles);
                })
                this.loginfo = res.data.loginfo;
                let index1 = res.data.scoreinfo.findIndex(item => item.typeid == 1);
                if (index1 > -1) {
                    this.photoScore = res.data.scoreinfo[index1];
                }
                let index2 = res.data.scoreinfo.findIndex(item => item.typeid == 2);
                if (index2 > -1) {
                    this.vedioScore = res.data.scoreinfo[index2];
                }
                let index3 = res.data.scoreinfo.findIndex(item => item.typeid == 3);
                if (index3 > -1) {
                    this.microDetailScore = res.data.scoreinfo[index3];
                }
                let index4 = res.data.scoreinfo.findIndex(item => item.typeid == 4);
                if (index4 > -1) {
                    this.detailScore = res.data.scoreinfo[index4];
                }
                let index5 = res.data.scoreinfo.findIndex(item => item.typeid == 5);
                if (index5 > -1) {
                    this.modelPhotoScore = res.data.scoreinfo[index5];
                }
                let index6 = res.data.scoreinfo.findIndex(item => item.typeid == 6);
                if (index6 > -1) {
                    this.modelVideoScore = res.data.scoreinfo[index6];
                }
                let index11 = res.data.scoreinfo.findIndex(item => item.typeid == 11);
                if (index11 > -1) {
                    this.phdPhotoScore = res.data.scoreinfo[index11];
                }
                let index12 = res.data.scoreinfo.findIndex(item => item.typeid == 12);
                if (index12 > -1) {
                    this.phdVedioScore = res.data.scoreinfo[index12];
                }
                let index13 = res.data.scoreinfo.findIndex(item => item.typeid == 13);
                if (index13 > -1) {
                    this.phdMicroDetailScore = res.data.scoreinfo[index13];
                }
                let index14 = res.data.scoreinfo.findIndex(item => item.typeid == 14);
                if (index14 > -1) {
                    this.phdDetailScore = res.data.scoreinfo[index14];
                }
                let index15 = res.data.scoreinfo.findIndex(item => item.typeid == 15);
                if (index15 > -1) {
                    this.phdModelPhotoScore = res.data.scoreinfo[index15];
                }
                let index16 = res.data.scoreinfo.findIndex(item => item.typeid == 16);
                if (index16 > -1) {
                    this.phdModelVideoScore = res.data.scoreinfo[index16];
                }
                this.bhinfoList = res.data.bhinfo; //驳回意见
                this.addForm.photoLqBtnStr = res.data.task.photoLqBtnStr;
                this.addForm.photoLqNameStr = res.data.task.photoLqNameStr;
                this.addForm.photoOverTimeStr = res.data.task.photoOverTimeStr;
                this.addForm.photoConfirmIsOver = res.data.task.photoConfirmIsOver;
                this.addForm.photoConfirmBtnStr = res.data.task.photoConfirmBtnStr;
                this.addForm.photoConfirmNameStr = res.data.task.photoConfirmNameStr;
                this.addForm.photoConfirmTimeStr = res.data.task.photoConfirmTimeStr;


                this.addForm.vedioLqBtnStr = res.data.task.vedioLqBtnStr;
                this.addForm.vedioLqNameStr = res.data.task.vedioLqNameStr;
                this.addForm.vedioOverTimeStr = res.data.task.vedioOverTimeStr;
                this.addForm.vedioConfirmBtnStr = res.data.task.vedioConfirmBtnStr;
                this.addForm.vedioConfirmNameStr = res.data.task.vedioConfirmNameStr;
                this.addForm.vedioConfirmTimeStr = res.data.task.vedioConfirmTimeStr;
                this.addForm.vedioConfirmIsOver = res.data.task.vedioConfirmIsOver;

                this.addForm.microDetailBtnStr = res.data.task.microDetailBtnStr;
                this.addForm.microDetailLqNameStr = res.data.task.microDetailLqNameStr;
                this.addForm.microDetailOverTimeStr = res.data.task.microDetailOverTimeStr;
                this.addForm.microDetailConfirmBtnStr = res.data.task.microDetailConfirmBtnStr;
                this.addForm.microDetailConfirmNameStr = res.data.task.microDetailConfirmNameStr;
                this.addForm.microDetailConfirmTimeStr = res.data.task.microDetailConfirmTimeStr;
                this.addForm.microDetailConfirmIsOver = res.data.task.microDetailConfirmIsOver;

                this.addForm.detailLqBtnStr = res.data.task.detailLqBtnStr;
                this.addForm.detailLqNameStr = res.data.task.detailLqNameStr;
                this.addForm.detailOverTimeStr = res.data.task.detailOverTimeStr;
                this.addForm.detailConfirmBtnStr = res.data.task.detailConfirmBtnStr;
                this.addForm.detailConfirmNameStr = res.data.task.detailConfirmNameStr;
                this.addForm.detailConfirmTimeStr = res.data.task.detailConfirmTimeStr;
                this.addForm.detailConfirmIsOver = res.data.task.detailConfirmIsOver;

                this.addForm.modelPhotoBtnStr = res.data.task.modelPhotoBtnStr;
                this.addForm.modelPhotosLqNameStr = res.data.task.modelPhotosLqNameStr;
                this.addForm.modelPhotosOverTimeStr = res.data.task.modelPhotosOverTimeStr;
                this.addForm.modelPhotosConfirmIsOver = res.data.task.modelPhotosConfirmIsOver;
                this.addForm.modelPhotosConfirmBtnStr = res.data.task.modelPhotosConfirmBtnStr;
                this.addForm.modelPhotosConfirmNameStr = res.data.task.modelPhotosConfirmNameStr;
                this.addForm.modelPhotosConfirmTimeStr = res.data.task.modelPhotosConfirmTimeStr;


                this.addForm.modelVideoBtnStr = res.data.task.modelVideoBtnStr;
                this.addForm.modelVideoLqNameStr = res.data.task.modelVideoLqNameStr;
                this.addForm.modelVideoOverTimeStr = res.data.task.modelVideoOverTimeStr;
                this.addForm.modelVideoConfirmIsOver = res.data.task.modelVideoConfirmIsOver;
                this.addForm.modelVideoConfirmBtnStr = res.data.task.modelVideoConfirmBtnStr;
                this.addForm.modelVideoConfirmNameStr = res.data.task.modelVideoConfirmNameStr;
                this.addForm.modelVideoConfirmTimeStr = res.data.task.modelVideoConfirmTimeStr;

                this.addForm.shootingTaskId = res.data.task.shootingTaskId;
                this.addForm.platform = res.data.task.platform;
                this.addForm.photoIsOver = res.data.task.photoIsOver;
                this.addForm.vedioIsOver = res.data.task.vedioIsOver;
                this.addForm.microDetailIsOver = res.data.task.microDetailIsOver;
                this.addForm.detailIsOver = res.data.task.detailIsOver;
                this.addForm.modelPhotosIsOver = res.data.task.modelPhotosIsOver;
                this.addForm.modelVideoIsOver = res.data.task.modelVideoIsOver;
                this.addForm.fpVideoLqName = res.data.task.fpVideoLqName;
                this.addForm.fpPhotoLqName = res.data.task.fpPhotoLqName;
                this.addForm.fpModelLqName = res.data.task.fpModelLqName;
                this.addForm.fpDetailLqName = res.data.task.fpDetailLqName;
                this.addForm.fpNoVideoLqName = res.data.task.fpNoVideoLqName?.split(',');
                this.addForm.fpNoPhotoLqName = res.data.task.fpNoPhotoLqName?.split(',');
                this.addForm.fpNoModelLqName = res.data.task.fpNoModelLqName?.split(',');
                this.addForm.fpNoDetailLqName = res.data.task.fpNoDetailLqName?.split(',');
                //指定人员回显
                this.addForm.fpAssignDetailLqName = res.data.task.fpAssignDetailLqName?.split(',');
                this.addForm.fpAssignModelLqName = res.data.task.fpAssignModelLqName?.split(',');
                this.addForm.fpAssignPhotoLqName = res.data.task.fpAssignPhotoLqName?.split(',');
                this.addForm.fpAssignVideoLqName = res.data.task.fpAssignVideoLqName?.split(',');

                this.addForm.shopName = res.data.task.shopName;
                this.addForm.productShortName = res.data.task.productShortName;
                this.addForm.operationGroup = res.data.task.operationGroup;
                this.addForm.taskUrgency = res.data.task.taskUrgency;
                this.addForm.dockingPeople = res.data.task.dockingPeople;
                this.addForm.warehouse = res.data.task.warehouse;
                this.addForm.taskDate = res.data.task.taskDate;
                this.addForm.isDelivered = res.data.task.isDelivered;
                this.addForm.taskRemark = res.data.task.taskRemark;
                this.addForm.shootingTaskPickList = res.data.task.shootingTaskPickList;
                this.addForm.productID = res.data.task.productID;
                this.addForm.referenceName = res.data.task.referenceName;
                this.addForm.referenceId = res.data.task.referenceId;
                this.addForm.isend = res.data.task.isend;
                this.addForm.isTopOldNum = res.data.task.isTopOldNum;

                this.addForm.operationGroupstr = res.data.task.operationGroupstr;
                this.addForm.shopNameStr = res.data.task.shopNameStr;

                this.addForm.yyOrderNum = res.data.task.yyOrderNum;
                this.addForm.isYYJY = res.data.task.isYYJY;
                this.addForm.yyExpressNum = res.data.task.yyExpressNum;
                this.addForm.canSelPlatform = res.data.task.canSelPlatform;
                // this.addForm = res.data.task
                this.addForm.fineStyle = res.data.task.fineStyle;

                if (res.data.task.isComplate == 1) {
                    this.islook = true;
                }

                if (res.data.task.shootingTaskPick.indexOf("1") < 0) { this.fpPhotoLqShow = false; }
                if (res.data.task.shootingTaskPick.indexOf("2") < 0) { this.fpVideoLqShow = false; }
                if (res.data.task.shootingTaskPick.indexOf("3") < 0) { this.fpMicroDetailLqShow = false; }
                if (res.data.task.shootingTaskPick.indexOf("4") < 0) { this.fpDetailLqShow = false; }
                if (res.data.task.shootingTaskPick.indexOf("5") < 0) { this.fpModelPhotoLqShow = false; }
                if (res.data.task.shootingTaskPick.indexOf("6") < 0) { this.fpModelVideoLqShow = false; }
            }
            if (this.addForm.isYYJY == 0) {
                for (let num in this.cwarehouselist) {
                    if (this.cwarehouselist[num].label == '美工自购' || this.cwarehouselist[num].label == '美工自购（测试）') {
                        this.cwarehouselist.splice(num, 1)
                    }
                }
            }

            this.pageLoading = false;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        //提交保存
        async onSubmit () {
            if (!this.onSubmitValidate()) {
                return;
            }
            var res = this.$refs.uploadimg.getReturns();
            if (!res.success) { return; }
            this.addForm.photoUpfiles = res.data;
            res = this.$refs.uploadexl.getReturns();
            if (!res.success) return;
            if (res.data.length == 0) {
                this.$message({ message: this.$t('请上传附件'), type: 'error' });
                return;
            }

            this.addForm.execlUpfiles = res.data;

            //未选择，只读，且清空
            var value = this.addForm.shootingTaskPickList;
            if (value.indexOf("1") < 0) { this.addForm.fpPhotoLqName = null; this.addForm.fpNoPhotoLqName = null;this.addForm.fpAssignPhotoLqName = null; }
            if (value.indexOf("2") < 0) { this.addForm.fpVideoLqName = null; this.addForm.fpNoVideoLqName = null; this.addForm.fpAssignVideoLqName = null;}
            if (value.indexOf("4") < 0) { this.addForm.fpDetailLqName = null; this.addForm.fpNoDetailLqName = null; this.addForm.fpAssignDetailLqName = null;}
            if (value.indexOf("5") < 0 && value.indexOf("6") < 0) { this.addForm.fpModelLqName = null; this.addForm.fpNoModelLqName = null; this.addForm.fpAssignModelLqName = null;}
            if (this.addForm.referenceName == null || this.addForm.referenceName == "") {
                this.addForm.referenceId = 0;
            }
            // let para = _.cloneDeep(this.addForm);
            let noname = {
            }
            if (this.addForm.fpNoPhotoLqName?.length > 0) {
                noname.fpNoPhotoLqName = this.addForm.fpNoPhotoLqName.join(',')
            } else {
                noname.fpNoPhotoLqName = null;
            }
            if (this.addForm.fpNoVideoLqName?.length > 0) {
                noname.fpNoVideoLqName = this.addForm.fpNoVideoLqName.join(',')
            } else {
                noname.fpNoVideoLqName = null;
            }
            if (this.addForm.fpNoDetailLqName?.length > 0) {
                noname.fpNoDetailLqName = this.addForm.fpNoDetailLqName.join(',')
            } else {
                noname.fpNoDetailLqName = null;
            }
            if (this.addForm.fpNoModelLqName?.length > 0) {
                noname.fpNoModelLqName = this.addForm.fpNoModelLqName.join(',')
            } else {
                noname.fpNoModelLqName = null;
            }
            //指定人员数组转字符串
            if (this.addForm.fpAssignDetailLqName?.length > 0 && this.addForm.fpAssignDetailLqName!=null) {
              noname.fpAssignDetailLqName=this.addForm.fpAssignDetailLqName.join(',')
            }
            else {
                noname.fpAssignDetailLqName = null;
            }
            if (this.addForm.fpAssignModelLqName?.length > 0 && this.addForm.fpAssignModelLqName!=null) {
              noname.fpAssignModelLqName=this.addForm.fpAssignModelLqName.join(',')
            }
             else {
                noname.fpAssignModelLqName = null;
            }
            if (this.addForm.fpAssignPhotoLqName?.length > 0 && this.addForm.fpAssignPhotoLqName!=null) {
              noname.fpAssignPhotoLqName=this.addForm.fpAssignPhotoLqName.join(',')
            }else {
                noname.fpAssignPhotoLqName = null;
            }
            if (this.addForm.fpAssignVideoLqName?.length > 0 && this.addForm.fpAssignVideoLqName!=null) {
              noname.fpAssignVideoLqName=this.addForm.fpAssignVideoLqName.join(',')
            } else {
                noname.fpAssignVideoLqName = null;
            }
            let params = { ...this.addForm, ...noname }
            let para = _.cloneDeep(this.addForm);
            if (this.extBzTypeArgs && this.extBzTypeArgs.extBzType) {
                para.shootingTaskExtBzList = [{
                    extBzType: this.extBzTypeArgs.extBzType,
                    extBzIdOne: this.extBzTypeArgs.extBzIdOne
                }];
            }
            this.pageLoading = true;
            var res = await addOrUpdateShootingVideoTaskAsync(params);
            this.pageLoading = false;
            var that = this;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
            if (that.addForm.shootingTaskId > 0) {
                that.onCloseAddForm(0);
            } else {
                that.onCloseAddForm(1);
            }
        },
        //删除上传附件操作
        async deluplogexl (ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        //删除上传图片操作
        async deluplogimg (ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 1 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        initaddform (initData) {
            this.addForm.shootingTaskId = 0;
            this.addForm.platform = null;
            this.addForm.photoIsOver = 0;
            this.addForm.vedioIsOver = 0;
            this.addForm.microDetailIsOver = 0;
            this.addForm.detailIsOver = 0;
            this.addForm.modelPhotosIsOver = 0;
            this.addForm.modelVideoIsOver = 0;
            this.addForm.fpVideoLqName = null;
            this.addForm.fpPhotoLqName = null;
            this.addForm.fpModelLqName = null;
            this.addForm.fpDetailLqName = null;
            this.addForm.shopName = null;
            this.addForm.productShortName = null;
            this.addForm.operationGroup = null;
            this.addForm.taskUrgency = 9;
            this.addForm.dockingPeople = null;
            this.addForm.warehouse = null;
            this.addForm.taskDate = null;
            this.addForm.isDelivered = null;
            this.addForm.taskRemark = null;
            this.addForm.shootingTaskPickList = [];
            this.addForm.photoUpfiles = [];
            this.addForm.pxeclUpfiles = [];
            this.addForm.photoPlay = 0;
            this.addForm.vedioPlay = 0;
            this.addForm.microDetailPlay = 0;
            this.addForm.detailPlay = 0;
            this.addForm.modelPhotosPlay = 0;
            this.addForm.referenceName = null;
            this.addForm.referenceId = null;

            if (initData) {
                if (initData.productShortName) this.addForm.productShortName = initData.productShortName;
                if (initData.platform) this.addForm.platform = initData.platform;
                if (initData.operationGroup) this.addForm.operationGroup = initData.operationGroup;
                if (initData.dockingPeople) this.addForm.dockingPeople = initData.dockingPeople;

                if (initData.extBzTypeArgs && initData.extBzTypeArgs.extBzType) {
                    this.extBzTypeArgs = initData.extBzTypeArgs;

                    if (initData.extBzTypeArgs.extBzType == "建编码") {

                        if (initData.extBzTypeArgs.extBzInWarehouse) this.addForm.isDelivered = 1;

                    }
                } else {
                    this.extBzTypeArgs = null;
                }

                this.onchangeplatform(initData.platform);
                this.addForm.isYYJY = 1;
            } else {
                this.addForm.productShortName = null;
                this.addForm.platform = null;
                this.addForm.operationGroup = null;
                this.addForm.dockingPeople = null;
                this.addForm.isYYJY = 0;
            }
            if (this.addForm.isYYJY == 0) {
                for (let num in this.cwarehouselist) {
                    if (this.cwarehouselist[num].label == '美工自购' || this.cwarehouselist[num].label == '美工自购（测试）') {
                        this.cwarehouselist.splice(num, 1)
                    }
                }
            }
            if (this.addForm.dockingPeople == null)
                this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
        },
        //终止重启
        async onEndRestartAction () {
            if (this.islook) return;
            this.$confirm("选中的任务将会重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endRestartActionAsync([this.addForm.shootingTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.addForm.isend = 0;
                    await this.onCloseAddForm(0);
                }
            });
        },
        //终止
        async OnendShootingTaskAction () {
            if (this.islook) return;
            this.$confirm("任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endShootingTaskActionAsync([this.addForm.shootingTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.addForm.isend = 1;
                    await this.onCloseAddForm(0);
                }
            });
        },
        //标记任务
        async OnSignShootingTaskAction () {
            if (this.islook) return;
            this.$confirm("任务将会进行标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await signShootingTaskActionAsync([this.addForm.shootingTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.addForm.isTopOldNum = 2;
                    await this.onCloseAddForm(0);
                }
            });
        },
        //取消标记任务
        async onUnSignShootingTaskActionShared () {
            if (this.islook) return;
            this.$confirm("将会取消标记，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unSignShootingTaskActionAsync([this.addForm.shootingTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.addForm.isTopOldNum = 0;
                    await this.onCloseAddForm(0);
                }
            });
        },
        // 删除操作
        async OnDeleteShootingTaskAction () {
            if (this.islook) return;
            this.$confirm("任务会移动到回收站，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteShootingTaskActionAsync([this.addForm.shootingTaskId]);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    await this.onCloseAddForm(1);
                }
            });
        }
    },
};
</script>
<style lang="scss" scoped>
.editform {
    padding: 0 60px;
    height: 700px;
    overflow-y: auto;
}

.editform::-webkit-scrollbar {
    display: none;
}

.addform::-webkit-scrollbar {
    display: none;
}

.addform {
    padding: 0 60px;
    height: 700px;
    overflow-y: auto;
}

.textsize {
    font-size: 14px;
    color: #606266;
    line-height: 28px;
}

.flexrow {
    display: flex;
    // flex: 1;
    flex-direction: row;
}

::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 5px !important;
}

::v-deep .el-form-item__label {
    text-align: left !important;

}

::v-deep .el-form .el-form-item__error {
    top: 21%;
    right: 1% !important;
    left: unset;
}

::v-deep .el-container.is-vertical ::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    // display: none;
}

::v-deep .el-container.is-vertical ::-webkit-scrollbar-thumb {
    background-color: #909399;
}

::-webkit-scrollbar-thumb {
    background-color: #909399 !important;
}

.flexcenter {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flexroww {
    display: flex;
    flex-direction: row;
    height: 40px;
    align-items: center;
}

.flexend {
    display: flex;
    justify-content: flex-end;
}

.bjcz {
    width: 100%;
    background-color: rgb(255, 255, 255);
    /* position:fixed; */
    float: left;



}

.linecs {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-break: anywhere;
}

.bjcz .bjczbt {
    height: 30px;
    font-size: 18px;
    line-height: 30px;
    color: #666;
    box-sizing: border-box;
    float: left;
    margin: 0 30px;

}

.bjcz .bjcztb {
    height: 30px;
    font-size: 18px;
    line-height: 30px;
    box-sizing: border-box;
    float: right;
    color: #999;
    font-weight: bold;
    margin: 0 35px;
}

.rwmc {
    width: 101%;
    margin-left: -5px;
    height: 70px;
    background-color: rgb(255, 255, 255);
    float: left;
    box-shadow: 0px 3px 5px #eeeeee;
    margin-top: 15px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-bottom: 0px;
    border-right: 0px;
    border-left: 0px;
}

.rwmc .xh {
    height: 70px;
    font-size: 18px;
    line-height: 68px;
    box-sizing: border-box;
    margin-left: 66px;
    padding: 0 2px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
}

.rwmc .mc,
.icon {
    height: 70px;
    font-size: 18px;
    line-height: 68px;
    box-sizing: border-box;
    margin-left: 10px;
    padding: 0 2px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
}

.rwmc .tj {
    height: 70px;
    font-size: 18px;
    line-height: 70px;
    box-sizing: border-box;
    margin-left: 78px;
    padding: 0 2px;
    color: #409eff;
    overflow: hidden;
    display: inline-block;
}

.hovericon:hover {
    color: #409eff !important;
    position: relative !important;
    top: -2px !important;
}

::v-deep .bzczxx {
    width: 100%;
    box-sizing: border-box;
    /* padding: 0 60px; */
    text-align: center;
    border: 1px solid #dcdfe6;
    border-right: 0px;
    border-bottom: 0px;
    border-left: 0px;
    font-size: 12px;
    margin: 0 0;
}

.bzbjfgx {
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    font-size: 12px;
    padding: 25px 0;
}

.bzczrzx {
    box-sizing: border-box;
    padding: 10px 60px;
}

::v-deep .rztx,
.rzmz,
.rzxgx,
.rzxgsj {
    // height: 30px;
    display: inline-block;
    font-size: 14px;
    line-height: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.rztx {
    width: 25px;
    height: 25px;
    background-color: #f5f5f5;
    border-radius: 50%;
    margin-right: 15px;
    position: relative;
    top: 5px;
}

.rzmz {
    width: 50px;
    margin-right: 5px;
}

.rzxgx {
    max-width: 200px;
    margin-right: 10px;
    color: #999;
}

.rzxgsj {
    max-width: 200px;
    color: #999;
}

.bzbjrw .rzxgq,
.rzxgnr {
    max-width: 450px;
    line-height: 15px;
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.rzbox{
    display: flex;
}

.rzxgq {
    width: 50px;
    margin-left: 43px;
    margin-right: 2px;
}

.bzczksps {
    width: 15%;
    display: inline-block;
}

.bzczkz {
    width: 38%;
    /* background-color: aqua; */
    display: inline-block;
}

.bzczky {
    width: 42%;
    /* background-color: aqua; */
    display: inline-block;
}
.czpfdh {
  width: 620px;
  text-align: center;
}

.pfx {
  width: 620px;
  /* height: 300px; */
  background-color: rgb(255, 255, 255);
  padding: 10px 0;
  font-size: 14px;
  color: #606266;
}

.pfx .rwlx {
  width: 90px;
  /* height: 30px; */
  /* height: 600px; */
  /* background-color: rgb(0, 104, 104); */
  display: inline-block;
  line-height: 40px;
  margin-left: 2px;
}

.pfx .czpf {
  width: 160px;
  display: inline-block;
  /* background-color: rgb(0, 104, 104); */
  position: relative;
  top: -1px;
}


// ::v-deep .el-input__inner{
//     height: 40px !important;
// }
</style>
