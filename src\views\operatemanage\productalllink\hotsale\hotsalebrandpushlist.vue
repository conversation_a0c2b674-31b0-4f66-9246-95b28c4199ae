<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-date-picker style="width:220px" v-model="filter.sdate" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始创建日期" end-placeholder="结束创建日期"
                    :picker-options="pickerOptions"></el-date-picker>
                <el-date-picker style="width:220px" v-model="filter.sdate2" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始添加日期" end-placeholder="结束添加日期"
                    :picker-options="pickerOptions"></el-date-picker>
                <!-- <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.createdUserPost" filterable placeholder="岗位" style="width:120px;"
                            clearable>
                            <el-option key="1" label="1" value="1" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.createdUserBu" filterable placeholder="分公司" style="width:120px;"
                            clearable>
                            <el-option key="义乌" label="义乌" value="义乌" />
                            <el-option key="南昌" label="南昌" value="南昌" />
                        </el-select>
                    </el-button> -->
                <el-input v-model.trim="filter.createdUserPost" placeholder="岗位" style="width:100px;" clearable
                    :maxlength="10" />
                <el-input v-model.trim="filter.createdUserBu" placeholder="分公司" style="width:100px;" clearable
                    :maxlength="10" />
                <el-input v-model.trim="filter.createdUserName" placeholder="创建人" style="width:100px;" clearable
                    :maxlength="10" />
                <el-input v-model.trim="filter.addUserName" placeholder="添加人" style="width:100px;" clearable
                    :maxlength="10" />
                <el-select v-model="filter.isStepPrice" filterable placeholder="是否阶梯价格" style="width:120px;" clearable>
                    <el-option :value="true" label="是" />
                    <el-option :value="false" label="否" />
                </el-select>
                <el-select v-model="filter.isPatent" filterable placeholder="是否专利资质" style="width:120px;" clearable>
                    <el-option :value="true" label="是" />
                    <el-option :value="false" label="否" />
                </el-select>
                <el-select v-model="filter.status" clearable placeholder="最近状态" style="width: 100px" filterable>
                    <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filter.platform" clearable placeholder="平台" style="width: 100px" filterable>
                    <el-option v-for="item in somePlatFormList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filter.isNewProduct" filterable placeholder="是否新品" style="width:120px;" clearable>
                    <el-option :value="true" label="是" />
                    <el-option :value="false" label="否" />
                </el-select>
                <el-input v-model.trim="filter.addUserGroupName" placeholder="小组" style="width:100px;" clearable
                    :maxlength="10" />
                <el-input v-model.trim="filter.goodsName" placeholder="品名" style="width:100px;" clearable
                    :maxlength="100" />
                <el-date-picker  v-if="checkPermission('QueryDayReportdateToPurchase')" style="width:220px" v-model="filter.dayReportdate" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="日报数据日期" end-placeholder="日报数据日期"
                    :picker-options="pickerOptions"></el-date-picker>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onImportPushModel">下载模板</el-button>
                <el-button type="primary" @click="onImportPush">导入</el-button>
                <!-- <el-button type="primary" @click="onExportPush">导出</el-button> -->
            </el-form>
        </template>
        <!--列表-->
        <!-- <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='tablelist' @select='selectchange' :isSelection='false' :tableCols='tableCols'
            :loading="listLoading">
            <template slot='extentbtn'>
            </template>
        </ces-table> -->
        <vxetablebase ref="personTable" :id="'hotsalebrandpushlist20230715'" :tableData='tablelist' :tableCols='tableCols' :that="that"
            :treeProp="{ rowField: 'dtlId', parentField: 'parnetId' }" @sortchange='sortchange' :loading="listLoading">
            <template slot="right">
                <template slot="right">
                    <vxe-column title="操作" :field="'col' + (tableCols.length + 1)" width="90" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" v-if="!(row.id == '0')" @click="onEditShow(row)">编辑</el-button>
                            <el-button type="text" v-if="!(row.id == '0')" @click="onDelete(row)">删除</el-button>
                        </template>
                    </vxe-column>
                    <vxe-column title="选品" :field="'col' + (tableCols.length + 2)" width="100" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" v-if="!(row.id == '0')" @click="onAddChooseShow(row)">添加到已选品</el-button>
                        </template>
                    </vxe-column>
                    <vxe-column title="日志" :field="'col' + (tableCols.length + 3)" width="140" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" v-if="!(row.id == '0')" @click="onShowChooseLog(row)">被选记录</el-button>
                            <el-button type="text" v-if="!(row.id == '0')" @click="onShowLog(row)">查看日志</el-button>
                        </template>
                    </vxe-column>
                </template>
            </template>

        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getPageList" />
        </template>

        <el-dialog title="编辑" :visible.sync="editDialogInfo.visible" width="50%" :close-on-click-modal="false" v-dialogDrag
            v-loading="editDialogInfo.vLoading" element-loading-text="拼命加载中">
            <el-form ref="editFormData" :model="editDialogInfo.editFormData" :rules="editDialogInfo.editFormRules"
                label-width="100px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="goodsImgUrl" label="图片">
                            <yh-img-upload :value.sync="editDialogInfo.editFormData.goodsImgUrl" :multiple="true"
                                :limit="9" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="goodsName" label="品名">
                            <el-input v-model.trim="editDialogInfo.editFormData.goodsName" :maxlength="100"
                                style="width:98%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="goodsColor" label="颜色">
                            <el-input v-model.trim="editDialogInfo.editFormData.goodsColor" :maxlength="20"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="goodsSize" label="产品尺寸">
                            <el-input v-model.trim="editDialogInfo.editFormData.goodsSize" :maxlength="50"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="goodsWeight" label="重量±5g">
                            <el-input v-model.trim="editDialogInfo.editFormData.goodsWeight" :maxlength="10"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="goodsMaterial" label="材质">
                            <el-input v-model.trim="editDialogInfo.editFormData.goodsMaterial" :maxlength="40"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="packCount" label="装箱数">
                            <el-input-number v-model="editDialogInfo.editFormData.packCount" :min="0" :max="10000000"
                                style="width:95%" auto-complete="off" :precision="0">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="packMethod" label="包装方式">
                            <el-input v-model.trim="editDialogInfo.editFormData.packMethod" :maxlength="20"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="packSpec" label="箱规">
                            <el-input v-model.trim="editDialogInfo.editFormData.packSpec" :maxlength="20"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="packWeight" label="整箱重">
                            <el-input v-model.trim="editDialogInfo.editFormData.packWeight" :maxlength="10"
                                style="width:95%"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="goodsBigPrice" label="大货价">
                            <el-input-number v-model="editDialogInfo.editFormData.goodsBigPrice" :min="0" :max="10000000"
                                style="width:95%" auto-complete="off" :precision="4">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="isStepPrice" label="是否阶梯价格">
                            <el-switch v-model="editDialogInfo.editFormData.isStepPrice" active-text="是" inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="isPatent" label="专利资质">
                            <el-switch v-model="editDialogInfo.editFormData.isPatent" active-text="是" inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="isCanHelpDelivery" label="是否支持代发">
                            <el-switch v-model="editDialogInfo.editFormData.isCanHelpDelivery" active-text="是"
                                inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="isHaveOriginalImage" label="是否有原图">
                            <el-switch v-model="editDialogInfo.editFormData.isHaveOriginalImage" active-text="是"
                                inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model.trim="editDialogInfo.editFormData.remark" :maxlength="500" style="width:98%"
                                show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- TODO:增加结构 -->
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="isNewProduct" label="是否新品">
                            <el-switch v-model="editDialogInfo.editFormData.isNewProduct" active-text="是" inactive-text="否">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-show="!editDialogInfo.editFormData.isNewProduct">
                    <el-col :span="24">
                        <el-form-item label="拼多多">
                            <div style="display: flex;align-items: center;">
                                <el-form-item label="价格" prop="pddNewProductPrice" style="width: 220px;margin-right: 20px;">
                                    <el-input-number :precision="2" v-model="editDialogInfo.editFormData.pddNewProductPrice"
                                        placeholder="价格" clearable :min="0" :max="9999999"
                                        :controls="false"></el-input-number>
                                </el-form-item>
                                <el-form-item label="竞品ID" prop="pddNewCompetitorPId"
                                    style="width: 220px;margin-right: 20px;">
                                    <el-input v-model="editDialogInfo.editFormData.pddNewCompetitorPId" placeholder="竞品ID"
                                        clearable maxlength="50"></el-input>
                                </el-form-item>
                                <el-form-item label="是否新品" prop="isPddNewProduct">
                                    <el-switch v-model="editDialogInfo.editFormData.isPddNewProduct" active-text="是"
                                        inactive-text="否">
                                    </el-switch>
                                </el-form-item>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-show="!editDialogInfo.editFormData.isNewProduct">
                    <el-col :span="24">
                        <el-form-item label="天猫">
                            <div style="display: flex;align-items: center;">
                                <el-form-item label="价格" prop="tmNewProductPrice" style="width: 220px;margin-right: 20px;">
                                    <el-input-number :precision="2" v-model="editDialogInfo.editFormData.tmNewProductPrice"
                                        placeholder="价格" clearable :min="0" :max="9999999"
                                        :controls="false"></el-input-number>
                                </el-form-item>
                                <el-form-item label="竞品ID" prop="tmNewCompetitorPId"
                                    style="width: 220px;margin-right: 20px;">
                                    <el-input v-model="editDialogInfo.editFormData.tmNewCompetitorPId" placeholder="竞品ID"
                                        clearable maxlength="50"></el-input>
                                </el-form-item>
                                <el-form-item label="是否新品" prop="isTmNewProduct">
                                    <el-switch v-model="editDialogInfo.editFormData.isTmNewProduct" active-text="是"
                                        inactive-text="否">
                                    </el-switch>
                                </el-form-item>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-show="!editDialogInfo.editFormData.isNewProduct">
                    <el-col :span="24">
                        <el-form-item label="淘工厂">
                            <div style="display: flex;align-items: center;">
                                <el-form-item label="价格" prop="tgcNewProductPrice" style="width: 220px;margin-right: 20px;">
                                    <el-input-number :precision="2" v-model="editDialogInfo.editFormData.tgcNewProductPrice"
                                        placeholder="价格" clearable :min="0" :max="9999999"
                                        :controls="false"></el-input-number>
                                </el-form-item>
                                <el-form-item label="竞品ID" prop="tgcNewCompetitorPId"
                                    style="width: 220px;margin-right: 20px;">
                                    <el-input v-model="editDialogInfo.editFormData.tgcNewCompetitorPId" placeholder="竞品ID"
                                        clearable maxlength="50"></el-input>
                                </el-form-item>
                                <el-form-item label="是否新品" prop="isTgcNewProduct">
                                    <el-switch v-model="editDialogInfo.editFormData.isTgcNewProduct" active-text="是"
                                        inactive-text="否">
                                    </el-switch>
                                </el-form-item>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form> <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editDialogInfo.visible = false">取消</el-button>
                    &nbsp;
                    <el-button type="primary" @click="onEditSave" :loading="editDialogInfo.vLoading">保 存</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="导入" :visible.sync="improtDialogInfo.visible" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-form ref="improtBrandPushForm" :model="improtDialogInfo.improtBrandPushForm" label-width="55px"
                label-position="left">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="uploadBrandPush" class="upload-demo" :auto-upload="false" :multiple="false"
                            :limit="1" action accept=".xlsx" :http-request="onUploadFile" :on-success="onUploadSuccess"
                            :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="improtDialogInfo.uploadLoading" @click="onSubmitUpload">{{
                                    (improtDialogInfo.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="improtDialogInfo.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="添加到已选品" :visible.sync="addChooseDialogInfo.visible" width="50%" :close-on-click-modal="false"
            v-dialogDrag v-loading="addChooseDialogInfo.vLoading" element-loading-text="拼命加载中">
            <el-form class="ad-form-query" :model="addChooseDialogInfo.addChooseFormData" ref="addChooseFormData"
                @submit.native.prevent label-width="100px" :rules="addChooseDialogInfo.addChooseFormRules">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="类型" prop="chooseType">
                            <el-select v-model="addChooseDialogInfo.addChooseFormData.chooseType" placeholder="类型"
                                @change="chooseTypeChange">
                                <el-option label="竞品" :value="3"></el-option>
                                <el-option label="新品" :value="4"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="竞品平台" prop="platform">
                            <el-select v-model="addChooseDialogInfo.addChooseFormData.platform" placeholder="请选择竞品原平台"
                                :disabled="addChooseDialogInfo.platformdisabled">
                                <el-option v-for="item in platformlist" :key="'p2f-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="运营平台" prop="newPlatform">
                            <el-select v-model="addChooseDialogInfo.addChooseFormData.newPlatform" placeholder="请选择要做的平台">
                                <el-option v-for="item in platformlist" :key="'np2f-' + item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="竞品ID" prop="goodsCompeteId">
                            <el-input v-model.trim="addChooseDialogInfo.addChooseFormData.goodsCompeteId" :maxlength="50"
                                :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="竞品标题" prop="goodsCompeteName">
                            <el-input v-model.trim="addChooseDialogInfo.addChooseFormData.goodsCompeteName" :maxlength="100"
                                :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="竞品图片" prop="goodsCompeteImgUrl">
                            <YhImgUpload :value.sync="addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="15">
                        <!-- <el-button type="primary" @click="onUpdateImg1">上一张</el-button> -->
                        <el-button type="primary" @click="onUpdateImg" v-if="updateImgInfo.maxselindex > 1">更换主图</el-button>
                        <!-- <el-button type="primary" @click="onUpdateImg2">下一张</el-button> -->
                        <el-select v-model="updateImgInfo.selindex" style="width:80px;margin-left: 5px;"
                            v-if="updateImgInfo.visible" @change="onupdateImgInfo">
                            <el-option :value="0" label="第1张" />
                            <el-option :value="1" label="第2张" v-if="updateImgInfo.maxselindex > 1" />
                            <el-option :value="2" label="第3张" v-if="updateImgInfo.maxselindex > 2" />
                            <el-option :value="3" label="第4张" v-if="updateImgInfo.maxselindex > 3" />
                            <el-option :value="4" label="第5张" v-if="updateImgInfo.maxselindex > 4" />
                            <el-option :value="5" label="第6张" v-if="updateImgInfo.maxselindex > 5" />
                            <el-option :value="6" label="第7张" v-if="updateImgInfo.maxselindex > 6" />
                            <el-option :value="7" label="第8张" v-if="updateImgInfo.maxselindex > 7" />
                            <el-option :value="8" label="第9张" v-if="updateImgInfo.maxselindex > 8" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <!-- v-if="updateImgInfo.visible" -->
                    <el-col :span="24">
                        <el-form-item>
                            <YhImgUpload :value.sync="updateImgInfo.goodsImgUrl" :multiple="true" :limit="9"
                                :disabled="true" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注" prop="chooseRemark">
                            <el-input v-model.trim="addChooseDialogInfo.addChooseFormData.chooseRemark" :maxlength="100"
                                show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addChooseDialogInfo.visible = false">取消</el-button>
                    &nbsp;
                    <el-button type="primary" @click="onAddChooseSave"
                        :loading="addChooseDialogInfo.vLoading">添加选品</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag>
            <div>
                <!-- <span>
                    <template>
                        <el-date-picker style="width: 410px" v-model="dialogMapVisible.filter.timerange" type="daterange"
                            @change="similarityDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions"></el-date-picker>
                    </template>
                </span> -->
                <span>
                    <buschar ref="dialogMapVisibleBuschar" v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import { platformlist, formatLinkProCode } from "@/utils/tools";
import {
    hotSaleBranPushStatusKeyValue, getHotSaleBrandPushPageList, getHotSaleBrandPushById,
    importHotSaleBrandPushAsync, updateHotSaleBrandPush, deleteHotSaleBrandPush, addChoose,getHotSaleBrandPushProfit3Map
} from '@/api/operatemanage/productalllink/alllink'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import YhImgUpload from '@/components/upload/yh-img-upload.vue';
import checkPermission from '@/utils/permission'
import {
    pickerOptions
} from "@/utils/tools";
import buschar from '@/components/Bus/buschar' 
const tableCols = [
    { istrue: true, prop: 'goodsName', label: '品名', width: '150', treeNode: true, sortable: 'custom', fixed: "left" },
    { istrue: true, prop: 'goodsImgUrl', label: '商品图片', width: '80', type: 'images' },
    { istrue: true, prop: 'goodsColor', label: '颜色', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'goodsSpec', label: '规格', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'goodsSize', label: '产品尺寸', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'goodsWeight', label: '重量±5g', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'goodsMaterial', label: '材质', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'packCount', label: '装箱数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'packMethod', label: '包装方式', width: '95', sortable: 'custom', },
    { istrue: true, prop: 'packSpec', label: '箱规', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'packWeight', label: '整箱重', width: '80', sortable: 'custom' },
    {
        istrue: true, prop: 'isPddNewProduct', label: '拼多多', width: '80', formatter: (row) => {
            if (row.isParent) {
                return ''
            } else {
                if(row.pddNewProductPrice != null){
                    return row.isPddNewProduct == true ? "新品" : String(row.pddNewProductPrice)
                }else{
                    return ''
                }
            }
        }, type: 'click', handle: (that, row) => that.toLink(row, 'pdd')
    },
    {
        istrue: true, prop: 'isTmNewProduct', label: '天猫', width: '80', formatter: (row) => {
            if (row.isParent) {
                return ''
            } else {
                if(row.tmNewProductPrice != null){
                    return row.isTmNewProduct == true ? "新品" : String(row.tmNewProductPrice)
                }else{
                    return ''
                }
            }
        }, type: 'click', handle: (that, row) => that.toLink(row, 'tm')
    },
    {
        istrue: true, prop: 'isTgcNewProduct', label: '淘工厂', width: '80', formatter: (row) => {
            if (row.isParent) {
                return ''
            } else {
                if(row.tgcNewProductPrice != null){
                    return row.isTgcNewProduct == true ? "新品" : String(row.tgcNewProductPrice)
                }else{
                    return ''
                }
            }
        }, type: 'click', handle: (that, row) => that.toLink(row, 'tgc')
    },
    { istrue: true, prop: 'goodsBigPrice', label: '大货价', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'goodsExpressFee', label: '预估快递费', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'isStepPrice', label: '是否阶梯价格', width: '80', formatter: (row) => row.isStepPrice == true ? "是" : "否" },
    { istrue: true, prop: 'isPatent', label: '专利资质', width: '80', formatter: (row) => row.isPatent == true ? "是" : "否" },
    { istrue: true, prop: 'isCanHelpDelivery', label: '是否支持代发', width: '80', formatter: (row) => row.isCanHelpDelivery == true ? "是" : "否" },
    { istrue: true, prop: 'isHaveOriginalImage', label: '是否有原图', width: '80', formatter: (row) => row.isHaveOriginalImage == true ? "是" : "否" },
    { istrue: true, prop: 'createdUserName', label: '创建人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'createdUserPost', label: '岗位', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'createdUserBu', label: '分公司', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'status', label: '最近状态', width: '95', formatter: (row) => row.statusName, sortable: 'custom' },
    { istrue: true, prop: 'lastAddUserName', label: '最新添加人', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lastAddUserGroupName', label: '添加人小组', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lastAddTime', label: '最新添加时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'remark', label: '备注', width: '150', sortable: 'custom' },
    {istrue:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'100',tipmesg:'毛三利润',type:'click', handle: (that, row) => that.showProfit3chart(row)},
    {istrue:true,prop:'profit3Rate',label:'毛三利润率', width:'120',tipmesg:'毛三利润/销售金额',type:'click',formatter: (row) =>!row.profit3Rate?"0":row.profit3Rate+'%',handle: (that, row) => that.showProfit3chart(row)},
    // {
    //     istrue: true, type: "button", label: '操作', width: "90", fixed: "right", btnList: [
    //         { label: "编辑", handle: (that, row) => that.onEditShow(row) },
    //         { label: "删除", handle: (that, row) => that.onDelete(row) },
    //     ]
    // },
    // {
    //     istrue: true, type: "button", label: '选品', width: "110", fixed: "right", btnList: [
    //         { label: "添加到已选品", handle: (that, row) => that.onAddChooseShow(row) },
    //     ]
    // },
    // {
    //     istrue: true, type: "button", label: '日志', width: "160", fixed: "right", btnList: [
    //         { label: "被选记录", handle: (that, row) => that.onShowChooseLog(row) },
    //         { label: "查看日志", handle: (that, row) => that.onShowLog(row) },
    //     ]
    // }
];
const somePlatFormList = [
    {
        label: '拼多多',
        value: 1
    },
    {
        label: '天猫',
        value: 2
    },
    {
        label: '淘工厂',
        value: 3
    }
]

const endTime = formatTime(new Date(), "YYYY-MM-DD");
const _2DayAgoTime = formatTime(dayjs().subtract(3, 'day'), "YYYY-MM-DD");
export default {
    name: "hotsalebrandpushlist",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, YhImgUpload, vxetablebase ,buschar,checkPermission},
    data() {
        return {
            dialogMapVisible: {
                visible: false,
                title: "",
                data: [] ,
            },
            that: this,
            somePlatFormList,
            platformlist: platformlist,
            pickerOptions: pickerOptions,
            filter: {
                sdate: [_2DayAgoTime, endTime],
                startDate: null,
                endDate: null,
                sdate2: [],
                startDate2: null,
                endDate2: null,
                dayReportdate:[],
                dayReportStartTime: null,
                dayReportEndTime: null,
                goodsName:null,
                GoodsCode:null
            },
            tableCols: tableCols,
            tablelist: [],
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdtime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            fileList: [],
            statusList: [],
            editDialogInfo: {
                visible: false,
                vLoading: false,
                editFormData: {
                    goodsImgUrl: null,
                },
                editFormRules: {
                    goodsImgUrl: [{ required: true, message: '请输入图片', trigger: 'blur' }],
                    goodsName: [{ required: true, message: '请输入品名', trigger: 'blur' }],
                    goodsColor: [{ required: true, message: '请输入颜色', trigger: 'blur' }],
                    goodsSize: [{ required: true, message: '请输入产品尺寸', trigger: 'blur' }],
                    goodsMaterial: [{ required: true, message: '请输入材质', trigger: 'blur' }],
                    goodsBigPrice: [{ required: true, message: '请输入大货价', trigger: 'blur' }],
                    // pddNewProductPrice: [{ required: true, message: '请输入拼多多新品价格', trigger: 'blur' }],
                    // pddNewCompetitorPId: [{ required: true, message: '请输入拼多多新品id', trigger: 'blur' }],
                    isPddNewProduct: [{ required: true, message: '请选择是否拼多多新品', trigger: 'blur' }],
                    // tmNewProductPrice: [{ required: true, message: '请输入天猫新品价格', trigger: 'blur' }],
                    // tmNewCompetitorPId: [{ required: true, message: '请输入天猫新品id', trigger: 'blur' }],
                    isTmNewProduct: [{ required: true, message: '请选择是否天猫新品', trigger: 'blur' }],
                    // tgcNewProductPrice: [{ required: true, message: '请输入淘工厂新品价格', trigger: 'blur' }],
                    // tgcNewCompetitorPId: [{ required: true, message: '请输入淘工厂新品id', trigger: 'blur' }],
                    isTgcNewProduct: [{ required: true, message: '请选择是否淘工厂新品', trigger: 'blur' }],
                },
            },
            improtDialogInfo: {
                visible: false,
                uploadLoading: false,
                improtBrandPushForm: {},
            },
            addChooseDialogInfo: {
                visible: false,
                vLoading: false,
                platformdisabled: true,
                addChooseFormData: {
                    chooseType: 4,
                    platform: 0,
                    goodsCompeteId: "",
                    goodsCompeteImgUrl: null
                },
                addChooseFormRules: {
                    chooseType: [{ required: true, message: '请输入类型', trigger: 'blur' }],
                    platform: [{ required: true, message: '请输入竞品平台', trigger: 'blur' }],
                    newPlatform: [{ required: true, message: '请输入运营平台', trigger: 'blur' }],
                    goodsCompeteId: [{ required: true, message: '请输入竞品ID', trigger: 'blur' }],
                    goodsCompeteName: [{ required: true, message: '请输入竞品标题', trigger: 'blur' }],
                    goodsCompeteImgUrl: [{ required: true, message: '请输入竞品图片', trigger: 'blur' }],
                },
                changeImgs: "",
            },
            updateImgInfo: {
                visible: false,
                selindex: 0,
                maxselindex: 1,
                goodsImgUrl: null,
                goodsImgUrlList: [],
            },
            formatLinkProCode,
        };
    },
    async mounted() {
        var that=this;
        if (checkPermission('HotSaleBrandPushProfit3')==false) {
                    this.$nextTick(() => {
                    this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('profit3'))
                    this.$refs.personTable.$refs.xTable.hideColumn(this.$refs.personTable.$refs.xTable.getColumnByField('profit3Rate'))
                })
            } 
           
        console.log(this, 'this');
        await this.getStatusKeyValue();
        await this.onSearch();
    },
    methods: {
       
       async showProfit3chart(row){
        if (this.filter.sdate && this.filter.sdate.length == 2) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.$message({ message: '必须输入要查询的时间', type: "error" });
                return;
            }
            if (this.filter.sdate2 && this.filter.sdate2.length == 2) {
                this.filter.startDate2 = this.filter.sdate2[0];
                this.filter.endDate2 = this.filter.sdate2[1];
            }
            else {
                this.filter.startDate2 = null;
                this.filter.endDate2 = null;
            }
            if (this.filter.dayReportdate && this.filter.dayReportdate.length == 2) {
                this.filter.dayReportStartTime = this.filter.dayReportdate[0];
                this.filter.dayReportEndTime = this.filter.dayReportdate[1];
            }
            else {
                this.filter.dayReportStartTime = null;
                this.filter.dayReportEndTime = null;
            }
            this.filter.goodsName=row.goodsName;
            this.filter.GoodsCode=row.goodsCode;
            const params = {
                ...this.filter
            };
            let that = this;
            const res = await getHotSaleBrandPushProfit3Map(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res
                that.dialogMapVisible.title = ""
                });
                await this.$refs.dialogMapVisibleBuschar.initcharts();
                this.filter.goodsName=null;
       },
        pbulicLink(link) {
            let urlMatch = link.match(/href="([^"]*)"/);
            if (urlMatch) {
                let url = new URL(urlMatch[1]);
                let path = url.href;
                window.open(path, '_blank');
            }
        },
        toLink(row, type) {
            if (type == 'pdd') {
                if (!row.isPddNewProduct && row.pddNewCompetitorPId) {
                    const pdd = this.formatLinkProCode(2, row.pddNewCompetitorPId)
                    this.pbulicLink(pdd)
                }
            }
            if (type == 'tm') {
                if (!row.isTmNewProduct && row.tmNewCompetitorPId) {
                    const tm = this.formatLinkProCode(1, row.tmNewCompetitorPId)
                    this.pbulicLink(tm)
                }
            }
            if (type == 'tgc') {
                if (!row.isTgcNewProduct && row.tgcNewCompetitorPId) {
                    const tgc = this.formatLinkProCode(8, row.tgcNewCompetitorPId)
                    this.pbulicLink(tgc)
                }
            }
        },
        async getStatusKeyValue() {
            this.statusList = [];
            const res = await hotSaleBranPushStatusKeyValue();
            Object.keys(res).forEach(key => {
                this.statusList.push({ value: key, label: res[key] });
            });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getPageList();
        },
        async getPageList() {
            await this.getStatusKeyValue();
            if (this.filter.sdate && this.filter.sdate.length == 2) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.$message({ message: '必须输入要查询的时间', type: "error" });
                return;
            }
            if (this.filter.sdate2 && this.filter.sdate2.length == 2) {
                this.filter.startDate2 = this.filter.sdate2[0];
                this.filter.endDate2 = this.filter.sdate2[1];
            }
            else {
                this.filter.startDate2 = null;
                this.filter.endDate2 = null;
            }
            if (this.filter.dayReportdate && this.filter.dayReportdate.length == 2) {
                this.filter.dayReportStartTime = this.filter.dayReportdate[0];
                this.filter.dayReportEndTime = this.filter.dayReportdate[1];
            }
            else {
                this.filter.dayReportStartTime = null;
                this.filter.dayReportEndTime = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            this.listLoading = true;
            const res = await getHotSaleBrandPushPageList(params);
            this.listLoading = false;
            this.total = res?.data?.total
            this.tablelist = res?.data?.list;
            //this.summaryarry=res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onEditShow(row) {
            this.editDialogInfo.editFormData = {};
            this.editDialogInfo.visible = true;
            this.editDialogInfo.vLoading = true;
            const params = {
                currentPage: 1,
                pageSize: 1,
                Id: row.id,
            };
            const res = await getHotSaleBrandPushById(params);
            if (res?.data) {
                this.editDialogInfo.editFormData = res?.data;
            }
            this.editDialogInfo.vLoading = false;
        },
        async onEditSave() {
            const pddNewProductPrice = this.editDialogInfo.editFormData.pddNewProductPrice;
            const tmNewProductPrice = this.editDialogInfo.editFormData.tmNewProductPrice;
            const tgcNewProductPrice = this.editDialogInfo.editFormData.tgcNewProductPrice;
            if (!this.editDialogInfo.editFormData.isNewProduct) {
                if (!this.editDialogInfo.editFormData.isPddNewProduct && pddNewProductPrice <= 0) {
                    this.$message({ message: '拼多多新品价格必须大于0', type: "error" });
                    return;
                }
                if (!this.editDialogInfo.editFormData.isTmNewProduct && tmNewProductPrice <= 0) {
                    this.$message({ message: '天猫新品价格必须大于0', type: "error" });
                    return;
                }
                if (!this.editDialogInfo.editFormData.isTgcNewProduct && tgcNewProductPrice <= 0) {
                    this.$message({ message: '淘工厂新品价格必须大于0', type: "error" });
                    return;
                }
            }
            this.editDialogInfo.vLoading = true;
            let res = await updateHotSaleBrandPush(this.editDialogInfo.editFormData);
            this.editDialogInfo.vLoading = false;
            if (res?.success) {
                this.$message({ message: '保存成功', type: "success" });
                this.editDialogInfo.visible = false;
                this.onSearch();
            }
        },
        async onDelete(row) {
            var deleteid = row.id;
            this.$confirm('确认要执行删除操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteHotSaleBrandPush({ id: deleteid })
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    this.onSearch();
                }
            }).catch(() => { });
        },
        async onAddChooseShow(row) {
            this.addChooseDialogInfo.visible = true;
            this.addChooseDialogInfo.vLoading = true;
            this.addChooseDialogInfo.platformdisabled = true;
            this.addChooseDialogInfo.changeImgs = row.goodsImgUrl;
            let mygoodsImgUrl = "";
            let obj = [];
            try {
                obj = JSON.parse(row.goodsImgUrl);
                mygoodsImgUrl = obj[0].url;
            }
            catch {
                obj = [];
                mygoodsImgUrl = row.goodsImgUrl;
            }
            console.log(obj, "ImgJsonObj");

            this.updateImgInfo = {
                visible: false,
                selindex: 0,
                goodsImgUrl: null,
                goodsImgUrlList: obj,
                maxselindex: obj.length,
            };
            let self = this;
            self.addChooseDialogInfo.addChooseFormData = {
                hotSaleBrandPushId: row.id,
                chooseType: 4,
                platform: 0,
                goodsCompeteId: "CGTX_" + (new Date().valueOf()).toString(),
                goodsCompeteName: row.goodsName,
                chooseRemark: row.remark,
                goodsCompeteImgUrl: null,
            };
            _.delay(function () {
                self.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = mygoodsImgUrl;
            }, 200)

            this.addChooseDialogInfo.vLoading = false;
            console.log(this.addChooseDialogInfo.addChooseFormData, "this.addChooseDialogInfo.addChooseFormData");
        },
        async chooseTypeChange(value) {
            if (this.addChooseDialogInfo.addChooseFormData.chooseType == 3) {
                this.addChooseDialogInfo.platformdisabled = false;
            }
            else {
                this.addChooseDialogInfo.addChooseFormData.platform = 0;
                this.addChooseDialogInfo.platformdisabled = true;
            }
            if (!this.addChooseDialogInfo.addChooseFormData.goodsCompeteId) {
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteId = "CGTX_" + (new Date().valueOf()).toString();
            }
        },
        async onAddChooseSave() {
            console.log(this.addChooseDialogInfo.addChooseFormData, "addChooseFormData");
            if (!this.addChooseDialogInfo.addChooseFormData.chooseType) {
                this.$message({ message: '类型必填', type: "error" });
                return;
            }
            if (!this.addChooseDialogInfo.addChooseFormData.newPlatform) {
                this.$message({ message: '运营平台必填', type: "error" });
                return;
            }
            if (!this.addChooseDialogInfo.addChooseFormData.goodsCompeteId) {
                this.$message({ message: '竞品ID必填', type: "error" });
                return;
            }
            if (!this.addChooseDialogInfo.addChooseFormData.goodsCompeteName) {
                this.$message({ message: '竞品名称必填', type: "error" });
                return;
            }
            if (!this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl) {
                this.$message({ message: '图片必填，可联系采购上传，也可自行添加', type: "error" });
                return;
            }
            if (this.addChooseDialogInfo.addChooseFormData.chooseType == 4 && this.addChooseDialogInfo.addChooseFormData.goodsCompeteId.indexOf("CGTX_") != 0) {
                this.$message({ message: '类型为新品时，竞品ID必须【CGTX_】起头', type: "error" });
                return;
            }
            if (this.addChooseDialogInfo.addChooseFormData.chooseType == 3 && this.addChooseDialogInfo.addChooseFormData.platform <= 0) {
                this.$message({ message: '类型为竞品时，竞品平台必须明确', type: "error" });
                return;
            }
            this.$confirm('确认要执行选品操作吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.addChooseDialogInfo.vLoading = true;
                let res = await addChoose(this.addChooseDialogInfo.addChooseFormData);
                this.addChooseDialogInfo.vLoading = false;
                if (res?.success) {
                    this.$message({ message: '添加选品成功', type: "success" });
                    this.addChooseDialogInfo.visible = false;
                    this.onSearch();
                }
            }).catch(() => { });
        },
        async onShowChooseLog(row) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/hotsalebrandpushchooselog.vue`,
                title: '选品日志',
                args: { ...row },
                height: '600px',
                width: '80%',
            })
        },
        async onShowLog(row) {
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/hotsalebrandpusheditlog.vue`,
                title: '操作日志',
                args: { ...row },
                height: '600px',
                width: '80%',
            })
        },
        async onUpdateImg() {
            this.updateImgInfo.visible = !this.updateImgInfo.visible;
            let self = this;
            let img = this.addChooseDialogInfo.changeImgs;
            _.delay(function () {
                if (!self.updateImgInfo.visible)
                    self.updateImgInfo.goodsImgUrl = "";
                else
                    self.updateImgInfo.goodsImgUrl = img;
            }, 200);
            console.log(this.updateImgInfo, "updateImgInfo");
        },
        async onupdateImgInfo() {
            this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = this.updateImgInfo.goodsImgUrlList[this.updateImgInfo.selindex].url;
        },
        async onUpdateImg1() {
            if (this.updateImgInfo.goodsImgUrlList && this.updateImgInfo.goodsImgUrlList.length > 0) {
                this.updateImgInfo.selindex = this.updateImgInfo.selindex - 1;
                if (this.updateImgInfo.selindex < 0)
                    this.updateImgInfo.selindex = 0;
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = this.updateImgInfo.goodsImgUrlList[this.updateImgInfo.selindex].url;
            }
        },
        async onUpdateImg2() {
            if (this.updateImgInfo.goodsImgUrlList && this.updateImgInfo.goodsImgUrlList.length > 0) {
                this.updateImgInfo.selindex = this.updateImgInfo.selindex + 1;
                if (this.updateImgInfo.selindex > (this.updateImgInfo.goodsImgUrlList.length - 1))
                    this.updateImgInfo.selindex = (this.updateImgInfo.goodsImgUrlList.length - 1);
                this.addChooseDialogInfo.addChooseFormData.goodsCompeteImgUrl = this.updateImgInfo.goodsImgUrlList[this.updateImgInfo.selindex].url;
            }
        },

        onImportPushModel() {
            window.open("/static/excel/productalllink/采购推新商品信息导入模板.xlsx", "_blank");
        },
        async onImportPush() {
            this.improtDialogInfo.visible = true;
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        async onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.uploadBrandPush.submit();
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            this.improtDialogInfo.uploadLoading = true;
            const res = await importHotSaleBrandPushAsync(form);
            this.improtDialogInfo.uploadLoading = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.improtDialogInfo.visible = false;
            }
        },
        async onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
    margin-bottom: 15px;
}
</style>
