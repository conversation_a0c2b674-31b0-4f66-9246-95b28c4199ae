<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期">
                    <el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :clearable="false"></el-date-picker>
                </el-form-item>
                <el-form-item label="采购单号:">
                    <el-button style="padding: 0;margin-left: 0;">
                        <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="childBuyNo"
                            v-model.trim="filter.buyNo" :maxRows="200" :inputt.sync="filter.buyNo"
                            placeholder="采购单号" :clearable="true" @callback="callbackBuyNo" title="采购单号">
                        </inputYunhan>
                    </el-button>
                </el-form-item>
                <el-form-item label="Erp编号:">
                    <el-button style="padding: 0;margin-left: 0;">
                        <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref="childIndexNo"
                            :inputt.sync="filter.indexNo" v-model.trim="filter.indexNo" placeholder="Erp编号"
                            :clearable="true" @callback="callback" title="Erp编号">
                        </inputYunhan>
                    </el-button>
                </el-form-item>
                <el-form-item label="仓库:">
                    <el-button style="padding: 0;margin-left: 0;">
                        <el-select v-model="filter.warehouse" clearable filterable placeholder="请选择仓库"
                            style="width: 200px">
                            <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                :value="item.wms_co_id" />
                        </el-select>
                    </el-button>
                </el-form-item>
                <!-- <el-form-item label="商品编码:">
                    <el-input v-model.trim="filter.goodsCode" maxlength="100" placeholder="商品编码" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <el-tabs v-model="activeName" style="height: 94%;">
            <el-tab-pane label="汇总" name="first" style="height: 100%;">
                <collect ref="goodsCostPriceChgSum" :filter="filter"></collect>
            </el-tab-pane>
            <el-tab-pane label="明细" name="second" style="height: 100%;">
                <detail ref="goodsCostPriceChgCode" :filter="filter"></detail>
            </el-tab-pane>
            <el-tab-pane label="各仓新增编码数" name="thirdly" style="height: 100%;">
                <newproduct ref="newproduct" :filter="filter"></newproduct>
            </el-tab-pane>
        </el-tabs>

    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getAllWarehouse, } from '@/api/inventory/warehouse'
import collect from "./timearrival/collect.vue"
import detail from "./timearrival/detail.vue"
import newproduct from "./timearrival/newproduct.vue"  
import inputYunhan from "@/components/Comm/inputYunhan";

const startDate = formatTime(new Date(), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(-7, 'day'), "YYYY-MM-DD");


export default {
    name: 'YunHanAdminGoodsCostPriceTab',
    components: {container,cesTable,MyConfirmButton, collect, detail, newproduct, inputYunhan},
    data() {
        return {
            that: this,
            activeName: 'first',
            warehouselist: [],
            filter: {
                brandId: null,
                goodsCode: null,
                buyNo:null,
                indexNo: null,
                warehouse: null,
                timerange: [startDate, endDate],
                startDate: null,
                endDate: null,
            },
            brandlist: [],
            pageLoading: false
        };
    },

    async mounted() {
        var res3 = await getAllWarehouse();
        this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);

        await this.onSearch();
    },

    methods: {
            async callbackBuyNo(val) {
                // this.inputedit = true;
                this.filter.buyNo = val;
                //this.onSearch();
            },
            async callback(val) {
                this.filter.indexNo = val;
                //this.onSearch();
            },
            async onSearch() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message.warning("请选择时间！")
                return;
            }
            if (this.activeName=='first') this.$refs.goodsCostPriceChgSum.onSearch();
            if (this.activeName=='second') this.$refs.goodsCostPriceChgCode.onSearch();
            if (this.activeName=='thirdly') this.$refs.newproduct.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>

</style>