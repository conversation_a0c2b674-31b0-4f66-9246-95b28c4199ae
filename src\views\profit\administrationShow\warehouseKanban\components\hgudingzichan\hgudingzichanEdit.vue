<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">水费</div>
        <el-form-item label="用水吨数：" prop="waterConsumption">
          <inputNumberYh v-model="ruleForm.waterConsumption" :placeholder="'用水吨数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="水费单价：" prop="waterPricePerTon">
          <inputNumberYh v-model="ruleForm.waterPricePerTon" :fixed="2" :placeholder="'水费单价'" class="publicCss" />
        </el-form-item>
        <div style="font-size: 15px; font-weight: 600; margin-left: 20px">电费</div>
        <el-form-item label="用电度数：" prop="electricityConsumption">
          <inputNumberYh v-model="ruleForm.electricityConsumption" :fixed="2" :placeholder="'用电度数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="电费单价：" prop="electricityPricePerKwh">
          <inputNumberYh v-model="ruleForm.electricityPricePerKwh" :fixed="2" :placeholder="'电费单价'" class="publicCss" />
        </el-form-item>
        <el-form-item label="其他公用均摊电费金额(元)：" prop="otherSharedElectricityFee">
          <inputNumberYh v-model="ruleForm.otherSharedElectricityFee" :fixed="2" :placeholder="'请输入'"
            class="publicCss" />
        </el-form-item>

      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { warehouseUtilityBillsSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
  name: 'hgudingzichanEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ruleForm: {
        waterConsumption: null,
        waterPricePerTon: null,
        electricityConsumption: null,
        electricityPricePerKwh: null,
        otherSharedElectricityFee: null
      },
      rules: {
        waterConsumption: [
          { required: true, message: '请输入用水吨数', trigger: 'blur' }
        ],
        waterPricePerTon: [
          { required: true, message: '请输入水费单价', trigger: 'blur' }
        ],
        electricityConsumption: [
          { required: true, message: '请输入用电度数', trigger: 'blur' }
        ],
        electricityPricePerKwh: [
          { required: true, message: '请输入电费单价', trigger: 'blur' }
        ],
        otherSharedElectricityFee: [
          { required: true, message: '请输入其他公用均摊电费金额', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
    console.log(this.editInfo, 'editInfo');

  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },

    async submitForm(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing");
          const { success, msg } = await warehouseUtilityBillsSubmit(this.ruleForm);

          if (success) {
            this.$message.success(msg || '保存成功');
            this.$emit("search");
          } else {
            this.$message.error(msg || '保存失败');
          }
        }
      } catch (error) {
        console.error('表单提交失败:', error);
        this.$message.error('保存失败，请重试');
      }
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
