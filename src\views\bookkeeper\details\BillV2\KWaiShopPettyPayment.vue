<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" :clearable="false"
          start-placeholder="数据开始时间" end-placeholder="数据结束时间" :picker-options="pickerOptions"
          style="width: 300px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'KWaiShopPettyPayment202408041359'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getPettyPayment_KWai } from '@/api/bookkeeper/reportdayV2'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs'

const tableCols = [
  { sortable: 'custom', width: '80', align: 'center', prop: 'shop', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'billItem', label: '账单项目', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'onlineOrderNumber', label: '线上单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNumber', label: '订单编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'microPaymentSerialNumber', label: '小额打款流水号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyerKuaishouId', label: '买家快手id', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentType', label: '打款类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'applyTime', label: '申请时间', formatter: (row) => formatTime(row.applyTime, 'YYYY-MM-DD') },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentStatus', label: '打款状态', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'message', label: '留言', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'incomeAmount', label: '收入金额', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expenseAmount', label: '支出金额', },
]
export default {
  name: "KWaiShopPettyPayment",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      summaryarry: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        shopName: null,//店铺
        billType: null,//补扣款分类
        debitSlipNumber: null,//补扣款单号
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给昨天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getPettyPayment_KWai(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
