<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height:95%;"  @tab-click="tabclick"  :before-leave="beforeleave" >
           <el-tab-pane name="tab0" label="任务列表"  style="height: 100%;" v-loading="addLoading" lazy>
               <packagetask  ref="shootingvideotask" @refresh="refresh"  :role="currole" style="height: 100%;"  tablekey="shootingvideotaskgrid"  :lazy="true"/>
           </el-tab-pane>
           <el-tab-pane name="tab4" label="已完成"  style="height: 100%;" v-loading="addLoading" lazy>
               <finishlist  ref="reffinishlist" :allsellist="allsellist" @refresh="refresh"  :role="currole" style="height: 100%;"  tablekey="reffinishlistid"  :lazy="true"/>
           </el-tab-pane>
           <el-tab-pane name="tab2" label="统计列表" v-if="checkPermission('api:Inventory:PackagesProcessing:StatList')"  style="height: 100%;" v-loading="addLoading" lazy>
               <statisticslist ref="refstatisticslist" :allsellist="allsellist" @refresh="refresh"  :role="currole" style="height: 100%;"  tablekey="refstatisticslistgrid"  :lazy="true" :type="type"/>
           </el-tab-pane>
           <el-tab-pane name="tab3" label="存档" v-if="checkPermission('api:Inventory:PackagesProcessing:ArchiveList')" style="height: 100%;" v-loading="addLoading" lazy>
               <archivelist ref="refarchive" @refresh="refresh" :allsellist="allsellist"  :role="currole" style="height: 100%;"  tablekey="refarchive"  :lazy="true"/>
           </el-tab-pane>
           <el-tab-pane name="tab7" label="统计" v-if="checkPermission('api:Inventory:PackagesProcessing:Stat')"  style="height: 100%;" v-loading="addLoading" lazy>
               <statistics  ref="datastatistics" @refresh="refresh"  :role="currole" style="height: 100%;"  tablekey="datastatistics"  :lazy="true"/>
           </el-tab-pane>
           <el-tab-pane name="tab1" label="设置"  style="height: 100%;"  lazy>
               <packageset ref="refpackageset" @getPriceTemplateListfuc="getPriceTemplateListfuc"></packageset>
           </el-tab-pane>
           <el-tab-pane name="tab9" label="每日订单"  style="height: 100%;"  lazy>
               <statdailyorder ref="datastatdailyorder"></statdailyorder>
           </el-tab-pane>
           <!-- <el-tab-pane name="tab8" label="薪资核算"   style="height: 100%;" v-if="checkPermission('api:Inventory:PackagesProcessing:PayrollAccounting')"  lazy>
               <moneysum ref="refmoneysum" @getPriceTemplateListfuc="getPriceTemplateListfuc"></moneysum>
           </el-tab-pane>  -->
       </el-tabs>
   </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import packagetask from '@/views/media/packagework/packagetask.vue';

// Vue.component('packageset', ()=>import('@/views/media/packagework/packageset.vue'))
// Vue.component('statisticslist', ()=>import('@/views/media/packagework/statisticslist.vue'))
// Vue.component('statistics', ()=>import('@/views/media/packagework/statistics.vue'))
// Vue.component('archivelist', ()=>import('@/views/media/packagework/archivelist.vue'))



// import packageset from '@/views/media/packagework/packageset.vue';
// import statisticslist from '@/views/media/packagework/statisticslist.vue';
// import statistics from '@/views/media/packagework/statistics.vue';
// import archivelist from '@/views/media/packagework/archivelist.vue';
import finishlist from '@/views/media/packagework/finishlist.vue';
import statdailyorder from '@/views/media/packagework/statdailyorder.vue';
// import moneysum from '@/views/media/packagework/moneysum.vue';
import { getShootingSetData, } from '@/api/media/shootingset'
import {  getCreateUserList, getRecordUser, getPriceTemplateList,getPackagesTypeListAsync
} from '@/api/inventory/packagesprocess';//包装加工
import { getPersonnelPositionAsync} from '@/api/inventory/packagesSetProcessing.js';



import { getShootOperationsGroup,getOperationsGroup,getErpUserInfoView} from '@/api/media/mediashare';
import { getUserRoleList,getShootingViewPersonAsync} from '@/api/media/ShootingVideo';
export default {
   name: "Users",
   components: { MyContainer , packagetask, finishlist,statdailyorder,
    'packageset': function(resolve) {
      import('@/views/media/packagework/packageset.vue').then(function(module) {
        resolve(module.default);
      });
    },
    'statisticslist': function(resolve) {
      import('@/views/media/packagework/statisticslist.vue').then(function(module) {
        resolve(module.default);
      });
    },
    'statistics': function(resolve) {
      import('@/views/media/packagework/statistics.vue').then(function(module) {
        resolve(module.default);
      });
    },
    'archivelist': function(resolve) {
      import('@/views/media/packagework/archivelist.vue').then(function(module) {
        resolve(module.default);
      });
    },
},
   data() {
       return {
           type:false,
           that: this,
           pageLoading: false,
           mdindex: 0,
           filter: {
           },
           shootingvideotaskover:"shootingvideotaskover",
           shootingvideotaskoverShop:"shootingvideotaskoverShop",
           shootingvideotaskoverBack:"shootingvideotaskoverBack",
           shopList: [],
           currole: 'tz',
           userList: [],
           groupList: [],
           warehouseList:[],
           taskUrgencyList:[],
           platformList:[],
           selids: [],
           activeName: 'tab0',
           addLoading: false,
           isedit: false,
           tab0isfirst:true,
           tab1isfirst:true,
           tab2isfirst:true,
           tab3isfirst:true,
           tab4isfirst:true,
           tab5isfirst:true,
           tab6isfirst:true,
           tab7isfirst:true,
           tab8isfirst:true,
           createUser: null,
           typeStrList:null,
           allsellist: {},
           getPriceTemplateListdata: [],
           alluserlist: []
       };
   },
   provide () {
       return {
           allsellist: ()=>this.allsellist,
           alluserlist: ()=>this.alluserlist,
       }
   },

   async mounted() {
    await this.gettabmsg();
    await this.getuser();
    await this.gettypeStrList();
    //    await this.getPriceTemplateListfuc();
    //    await this.getcrepeople();
   },
   methods: {
        async getPriceTemplateListfuc(){
            // this.$refs.refpackageset.getPriceTemplateListfuc();
            await this.gettabmsg();
        },
        async getuser(){
            const params = {
                packagesProcessId: 1,
                typeId: 3
            }
             const res = await getRecordUser();
            // const pra = {
            //     pageSize: 1000,
            //     currentPage: 1,
            // }
            // const res = await getPersonnelPositionAsync(pra);
            if (!res?.success) {
                return
            }
            this.alluserlist=res.data;
            // this.alluserlist.forEach(element => {
            //     element.userId = element.userId;
            // });
            this.$nextTick(()=>{
                this.$refs.shootingvideotask.newgetuser(this.alluserlist);
            });
        },
        async gettabmsg(){
            await this.getDataSetList(14);
            await this.getDataSetList(15);
            await this.getDataSetList(16);
            await this.getDataSetList(17);
            await this.getcrepeople();
            await this.gettypeStrList();
        },
        async getcrepeople(){
            const res = await getCreateUserList();
            if (!res?.success) {
                return
            }
            // this.createUser = res.data;
            this.allsellist.createUser = res.data;
        },
        async gettypeStrList() {
            const res = await getPackagesTypeListAsync();
            if (!res?.success) {
                return
            }
            this.allsellist.typeStrList = res.data;
        },
        async getDataSetList(index) { //14包装加工-品牌，15包装加工-包装方式，16包装加工-机型，17包装加工-尺寸
            this.listLoading = true;
            const res = await getShootingSetData({ setType: index });
            if (!res?.success) {
                return
            }
            switch (index) {
                case 14:
                    this.allsellist.packingMaterialList = res?.data?.data;
                    break;
                case 15:
                    this.allsellist.brandList = res?.data?.data;
                    break;
                case 16:
                    this.allsellist.machineTypeList = res?.data?.data;
                    break;
                case 17:
                    this.allsellist.packageSizeList = res?.data?.data;
                    break;
            }

        //    this.allsellist.packingMaterialList = this.packingMaterialList;
        //    this.allsellist.brandList = this.brandList;
        //    this.allsellist.machineTypeList = this.machineTypeList;
        //    this.allsellist.packageSizeList = this.packageSizeList;

            this.listLoading = false;
        },
        refresh(val){
            switch(val){
               //任务列表
               case 'tab0' :
                   this.$nextTick(()=>{
                    this.$refs.shootingvideotask.getTaskList(1);
                   })
                   break;
               //设置
               case 'tab1' :
                   this.mdindex = 1;
                   if(this.tab1isfirst){
                       this.tab1isfirst =false;
                   }
                   break;
               //统计列表
               case 'tab2' :
                   if(this.tab2isfirst){
                        this.$nextTick(() =>{
                            this.$refs.refstatisticslist.getTaskList(2);
                       })
                   }
                   break;
               //存档
               case 'tab3' :
                   if(this.tab3isfirst){
                        this.$refs.refarchive.getTaskList(3);
                   }
                   break;
               //已完成
               case 'tab4' :
                   if(this.tab4isfirst){
                       this.$nextTick(() =>{
                           this.$refs.reffinishlist.getTaskList(4);
                       })
                       this.tab4isfirst =false;
                   }
                   break;
               //打包进度
               case 'tab5' :
                   if(this.tab5isfirst){
                       this.tab5isfirst =false;
                   }
                   break;
               //核算
               case 'tab6' :
                   if(this.tab6isfirst){
                       this.tab6isfirst =false;
                   }
                   break;
               //统计
               case 'tab7' :
                   if(this.tab3isfirst){
                        this.$refs.datastatistics.getallmsg();
                   }
                   break;
               //设置
               case 'tab8' :
                   if(this.tab8isfirst){
                       this.tab8isfirst =false;
                   }
                   break;
           }

        },
       toResultmatter(){
           this.$router.push({path: '/media/index/homepage'})
       },
       beforeleave(visitName, currentName ){

           if(this.isedit){
               this.$message("请保存后再操作")
           }
           if(visitName== "tab99")
               return false;
           return !this.isedit;
       },
       editclosed(val){
           this.isedit = val;
       },
       async getShootingViewPerson () {
           var res = await getShootingViewPersonAsync();
           if(res?.success)
           {
               return res.data;
           }else{
              return null;
           }
       },

       async getrole() {
           var res = await getUserRoleList();
           if(res?.success)
           {
              if(res.data == null){
                   this.currole ="tz";
              }else if (res.data.indexOf("视觉部经理") >-1){
                   debugger;
                   this.currole ="b";
              }

           }else{
               this.currole ="tz";
           }

       },
        tabclick(){
           switch(this.activeName){
               //任务列表
               case 'tab0' :

                // if(this.tab0isfirst){
                //        this.tab1isfirst =false;
                // }
                   this.$nextTick(()=>{
                    // this.$refs.shootingvideotask.gettabmsg(1);
                    // this.$refs.shootingvideotask.getallmsg(this.allsellist);
                    // this.$refs.shootingvideotask.newgetuser(this.alluserlist);
                   })

                   this.mdindex = 0;
                   break;
               //设置
               case 'tab1' :
                   this.mdindex = 1;
                   if(this.tab1isfirst){
                       this.tab1isfirst =false;
                   }
                   break;
               //统计列表
               case 'tab2' :
                   if(this.tab2isfirst){
                        this.$nextTick(() =>{
                            this.$refs.refstatisticslist.getallmsg(this.allsellist);
                            this.$refs.refstatisticslist.newgetuser(this.alluserlist);
                       })
                       this.tab2isfirst =false;

                   }
                   break;
               //存档
               case 'tab3' :
                   if(this.tab3isfirst){
                      this.$nextTick(() =>{
                        this.$refs.refarchive.getallmsg(this.allsellist);
                        this.$refs.refarchive.newgetuser(this.alluserlist);
                       })
                       this.tab3isfirst =false;


                   }
                   break;
               //已完成
               case 'tab4' :
                   if(this.tab4isfirst){
                       this.$nextTick(() =>{
                        this.$refs.reffinishlist.getallmsg(this.allsellist);
                        this.$refs.reffinishlist.newgetuser(this.alluserlist);
                       })

                       this.tab4isfirst =false;
                   }
                   break;
               //打包进度
               case 'tab5' :
                   if(this.tab5isfirst){
                       this.tab5isfirst =false;
                   }
                   break;
               //核算
               case 'tab6' :
                   if(this.tab6isfirst){
                       this.tab6isfirst =false;
                   }
                   break;
               //统计
               case 'tab7' :
                   if(this.tab7isfirst){
                      this.$nextTick(() =>{
                           this.$refs.datastatistics.getallmsg(7);
                       })
                       this.tab7isfirst =false;
                   }
                   break;
               //薪资核算
               case 'tab8' :
                   if(this.tab8isfirst){
                       this.tab8isfirst =false;
                   }
                   break;
           }
       }
   },
};
</script>
<style lang="scss" scoped>
 ::v-deep .el-form-item--mini.el-form-item,
   .el-form-item--small.el-form-item {
       margin-bottom: 20px;
   }
   ::v-deep .vxe-table--render-default .vxe-header--column{
       line-height: 18px !important;
   }

//    @import './common/print-lock.css'
::v-deep .mycontainer{
    overflow: hidden !important;
}

</style>

