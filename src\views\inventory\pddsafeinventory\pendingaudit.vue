<template>
  <container>
    <template #header>
      <div style="display:flex;flex-direction: column;">
        <el-button-group>
          <el-button style="margin-top:0px">
            <button style="padding: 0;width: 410px; border: none;">
              <el-date-picker style="width: 410px" v-model="filter.timerange" @change="changeDate()" type="daterange"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                clearable></el-date-picker>
            </button>

            <button style="padding: 0;width: 200px; border: none;">
              <inputYunhan title="宝贝ID" placeholder="宝贝ID" :maxlength="200" :inputt.sync="filter.productCodes"
                :clearable="true" @callback="callbackProCode"></inputYunhan>
            </button>
            <button style="padding: 0;width: 150px; border: none;">
              <el-input style="width: 150px;" v-model="filter.goodsCode" v-model.trim="filter.goodsCode" :maxlength=100
                placeholder="商品编码" @keyup.enter.native="onSearch" clearable />
            </button>
            <el-select v-model="filter.skuSealStatus" placeholder="预售类型" clearable>
              <el-option label="所有" value></el-option>
              <el-option label="上架" value='1'></el-option>
              <el-option label="非预售" value='2'></el-option>
              <el-option label="规格预售" value="3"></el-option>
              <el-option label="时段预售" value="4"></el-option>
            </el-select>
            <el-button type="primary" @click="onSearch()" style="margin-left: 10px;">查询</el-button>
          </el-button>
        </el-button-group>
        <div style="margin-top:5px">
          <el-button type="primary" @click="batchAudit(1)"
            style="margin-left: 10px; width:100px; float:left;">批量同意</el-button>
          <el-button type="warning" @click="batchAudit(-1)"
            style=" margin-left: 10px; width:100px; float:left;">批量拒绝</el-button>
        </div>
      </div>

    </template>
    <vxetablebase :id="'pendingaudit202408041600'" :tableHandles='tableHandles' :tableData='list' :tableCols='tableCols'
      :tablefixed='true' :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange'
      @checkbox-range-end="callback" :isIndexFixed="false" height="100%">
      <template slot="right">
        <vxe-column title="操作" :field="'col_opratorcol'" width="90" fixed="right">
          <template #default="{ row }">
            <template>
              <el-button type="text" @click="audit(row, 1)">同意</el-button>
              <el-button type="text" @click="audit(row, -1)">拒绝</el-button>
            </template>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
    <el-dialog :title="dialog.title" :visible.sync="dialog.visible" width="50%" v-dialogDrag>
    <div>
      <span>
        <purchasedetail style="height: 600px;" ref="purchasedetail" :filter="dialog.filter" v-if="dialog.visible">
        </purchasedetail>
      </span>
    </div>
  </el-dialog>
  </container>
 
</template>
<script>
import { pagePddSafeInventoryAsync, pddSafeInventoryAudit, pddSafeInventoryBatchAudit, validateGoodsCodesHasCombinedCode } from '@/api/inventory/pddSafeInventorys'
import container from "@/components/my-container";
import { Loading } from 'element-ui';
import purchasedetail from '@/views/inventory/pddsafeinventory/purchasedetail'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import inputYunhan from '@/components/Comm/inputYunhan.vue'

let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};

const tableCols = [
  { istrue: true, type: 'checkbox' },
  { istrue: true, prop: 'shopCode', sortable: 'custom', label: '店铺', width: '180', type: 'custom', formatter: (row) => row.shopName },
  { istrue: true, prop: 'proCode', sortable: 'custom', label: '宝贝ID', width: '100' },
  { istrue: true, prop: 'goodsCode', sortable: 'custom', label: '商品编码', width: '120' },
  { istrue: true, prop: 'images', label: '图片', type: 'images', width: '100', align: 'center' },
  { istrue: true, prop: 'goodsName', label: '商品名称' },
  { istrue: true, prop: 'purchaseCount', sortable: 'custom', label: '采购数量', width: '80', align: 'center', type: 'click', handle: (that, row) => that.showDialog(row) },
  {
    istrue: true, summaryEvent: true, rop: '', label: `ID销售数量`, merge: true, prop: 'mergeField',
    cols: [
      { istrue: true, prop: 'salesYesterDay', sortable: 'custom', label: '昨日', width: '60' },
      { istrue: true, prop: 'salesThreeDay', sortable: 'custom', label: '3日', width: '60' },
      { istrue: true, prop: 'salesFiveDay', sortable: 'custom', label: '5日', width: '60' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `销量（昨日）`, merge: true, prop: 'mergeField1',
    cols: [
      { istrue: true, prop: 'salesYesterday_ALL', sortable: 'custom', label: '总', width: '80' },
      { istrue: true, prop: 'salesYesterday_PDD', sortable: 'custom', label: '拼多多', width: '80' },
      { istrue: true, prop: 'salesYesterday_TX', sortable: 'custom', label: '淘系', width: '80' },
      { istrue: true, prop: 'salesYesterday_DY', sortable: 'custom', label: '抖音', width: '80' },
      { istrue: true, prop: 'salesYesterday_JD', sortable: 'custom', label: '京东', width: '80' },
      { istrue: true, prop: 'salesYesterday_ALBB', sortable: 'custom', label: '阿里巴巴', width: '80' },
    ]
  },
  { istrue: true, prop: 'skuSealStatus', sortable: 'custom', label: '预售类型', width: '100' }
];
const tableHandles = [
];

export default {
  name: "Pddsafeinventory",
  components: { container, vxetablebase, purchasedetail, inputYunhan },
  data() {
    return {
      that: this,
      dialog: {
        visible: false,
        title: "采购单明细",
        filter: {
          goodsCode: null,
        }
      },
      filter: {
        goodsCode: null,
        startTime: null,
        endTime: null,
        timerange: null,
        skuSealStatus: null,
        productCodes: null
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      list: [],
      total: 0,
      pager: { OrderBy: "goodsCode", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      summaryarry: {},
      selids: [],
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
            window.setshowprogress(false);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate() - 1);
            const date2 = new Date(); date2.setDate(date2.getDate() - 1);
            picker.$emit('pick', [date1, date2]);
            window.setshowprogress(false);
          }
        }]
      }
    };
  },
  async mounted() {
    this.onSearch();
  },
  async created() {

  },
  methods: {
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      let pager = this.$refs.pager.getPager()
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.listLoading = true;
      let params = { ...pager, ...this.pager, ... this.filter }
      let res = await pagePddSafeInventoryAsync(params);
      this.listLoading = false;
      this.list = res.data?.list;
      this.total = res.data?.total;
    },
    callback(val) {
      this.sels = [];
      val.forEach(f => {
        let ob = new Object();
        ob.proCode = f.proCode;
        ob.goodsCode = f.goodsCode;
        this.sels.push(ob);
      })
    },
    async audit(row, type) {
      let audits = new Object();
      let detials = [];
      let detail = new Object();
      detail.ProCode = row.proCode;
      detail.GoodsCode = row.goodsCode;
      detials.push(detail);
      audits.details = detials;
      audits.IsPass = type;
      let validateParams = { ...audits }
      let validateRes = await validateGoodsCodesHasCombinedCode(validateParams)
      if (type == -1) {
        let params = { ProCode: row.proCode, GoodsCode: row.goodsCode, IsPass: type, IsContainsZH: true }
        let res = await pddSafeInventoryAudit(params);
        if (res.success) {
          if (res.data) {
            this.$message.success('操作成功！')
            this.onSearch();
          } else {
            this.$message.error('操作失败！')
          }
        } else {
          this.$message.error('操作失败！')
        }
      } else {
        if (validateRes.success) {
          //成功
          if (validateRes.data) {
            this.$confirm("对应存在组合编码，机器人将按照实时预售状态调整对应组合编码调整，是否需要操作组合编码？", "提示", {
              confirmButtonText: "是",
              cancelButtonText: "否",
              type: "warning",
              distinguishCancelAndClose: true
            }).then(async () => {
              let params = { ProCode: row.proCode, GoodsCode: row.goodsCode, IsPass: type, IsContainsZH: true }
              let res = await pddSafeInventoryAudit(params);
              if (res.success) {
                if (res.data) {
                  this.$message.success('操作成功！')
                  this.onSearch();
                } else {
                  this.$message.error('操作失败！')
                }
              } else {
                this.$message.error('操作失败！')
              }
            }).catch(async (error) => {
              if (error == 'cancel') {
                let params = { ProCode: row.proCode, GoodsCode: row.goodsCode, IsPass: type, IsContainsZH: false }
                let res = await pddSafeInventoryAudit(params);
                if (res.success) {
                  if (res.data) {
                    this.$message.success('操作成功！')
                    this.onSearch();
                  } else {
                    this.$message.error('操作失败！')
                  }
                } else {
                  this.$message.error('操作失败！')
                }
              }
            })
          } else {
            let params = { ProCode: row.proCode, GoodsCode: row.goodsCode, IsPass: type, IsContainsZH: false }
            let res = await pddSafeInventoryAudit(params);
            if (res.success) {
              if (res.data) {
                this.$message.success('操作成功！')
                this.onSearch();
              } else {
                this.$message.error('操作失败！')
              }
            } else {
              this.$message.error('操作失败！')
            }
          }
        } else {
          this.$message.error('校验失败!')
        }
      }
    },
    async batchAudit(type) {
      if (this.sels.length <= 0) {
        this.$message.error('请选择数据！');
        return;
      }
      let audits = new Object();
      let detials = [];
      this.sels.forEach(f => {
        let detail = new Object();
        detail.ProCode = f.proCode;
        detail.GoodsCode = f.goodsCode;
        detials.push(detail);
      })
      audits.details = detials;
      audits.IsPass = type;
      let validateParams = { ...audits }
      if (type == -1) {
        let params = { ...validateParams, IsContainsZH: true }
        let res = await pddSafeInventoryBatchAudit(params);
        if (res.success) {
          if (res.data) {
            this.$message.success('操作成功！')
            this.onSearch();
          } else {
            this.$message.error('操作失败！')
          }
        } else {
          this.$message.error('操作失败！')
        }
      } else {
        let validateRes = await validateGoodsCodesHasCombinedCode(validateParams)
        if (validateRes.success) {
          //成功
          if (validateRes.data) {
            this.$confirm("对应存在组合编码，机器人将按照实时预售状态调整对应组合编码调整，是否需要操作组合编码？", "提示", {
              confirmButtonText: "是",
              cancelButtonText: "否",
              type: "warning",
              distinguishCancelAndClose: true
            }).then(async () => {
              let params = { ...validateParams, IsContainsZH: true }
              let res = await pddSafeInventoryBatchAudit(params);
              if (res.success) {
                if (res.data) {
                  this.$message.success('操作成功！')
                  this.onSearch();
                } else {
                  this.$message.error('操作失败！')
                }
              } else {
                this.$message.error('操作失败！')
              }
            }).catch(async (error) => {
              if (error == 'cancel') {
                let params = { ...validateParams, IsContainsZH: false }
                let res = await pddSafeInventoryBatchAudit(params);
                if (res.success) {
                  if (res.data) {
                    this.$message.success('操作成功！')
                    this.onSearch();
                  } else {
                    this.$message.error('操作失败！')
                  }
                } else {
                  this.$message.error('操作失败！')
                }
              }
            })
          } else {
            let params = { ...validateParams, IsContainsZH: false }
            let res = await pddSafeInventoryBatchAudit(params);
            if (res.success) {
              if (res.data) {
                this.$message.success('操作成功！')
                this.onSearch();
              } else {
                this.$message.error('操作失败！')
              }
            } else {
              this.$message.error('操作失败！')
            }
          }
        } else {
          this.$message.error('校验失败!')
        }
      }
    },
    async showDialog(row) {
      this.dialog.title = "【" + row.goodsCode + "】采购单明细";
      this.dialog.filter.goodsCode = row.goodsCode;
      this.dialog.visible = true;
    },
    async callbackProCode(val) {
      this.filter.productCodes = val
    }
  }
}

</script>
<style></style>