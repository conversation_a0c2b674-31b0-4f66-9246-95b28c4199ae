<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <template #header>
      <videotaskfilter @topSearch="topSearch" @onAddTask="onAddTask" @onExport="onExport" :platformList="platformList"
        :warehouselist="warehouselist" :taskUrgencyList="taskUrgencyList" :islook="false" :groupList="groupList"
        :fpPhotoLqNameList="fpPhotoLqNameList" :erpUserInfoList="erpUserInfoList" @onAddOrder="onAddOrder"
        @ShowHideonSearch="ShowHideonSearch" @handleCommand="handleCommand" :listtype="1"></videotaskfilter>
    </template>
    <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <videotaskTable ref="vediomaintask" :id="'vediomaintask'" :tableData='tasklist' :tableCols='tableCols' :that='that'
      :height="'100%'" :showsummary='false' :summaryarry='summaryarry' @summaryClick='onsummaryClick'
      :loading='listLoading' @sortchange='sortchange' checkboxall @checkboxall="selectchangeevent"
      @selectchangeevent="selectchangeevent" @rowChange="rowChange" @shootUrgencyCilck="shootUrgencyCilck"
      @pickTask="pickTask" @openTaskRmarkInfo="openTaskRmarkInfo" @videotaskuploadfileDetal="videotaskuploadfileDetal"
      @editTask="editTask" @openComputOutInfo="openComputOutInfo" @onShowExproessHttp="onShowExproessHttp"
      @onShowOrderDtl="onShowOrderDtl">
    </videotaskTable>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" :sizes="[50, 100, 200, 300, 800, 1000, 2000]"
        :page-size="1000" @get-page="getTaskList" />
    </template>
    <!--创建任务-->
    <div class="dialog1">
      <el-dialog title="" :visible.sync="addTask" style="position: fixed; top: -60px;" width="700px" :show-close="false"
        :before-close="handleClose" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
        <videoaddform ref="videotaskaddfrom" v-if="addTask" :platformList="platformList" :onCloseAddForm="onCloseAddForm"
          :erpUserInfoList="erpUserInfoList" :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList"
          :warehouselist="warehouselist" :groupList="groupList">
        </videoaddform>
      </el-dialog>
    </div>
    <!-- 编辑任务 -->
    <div class="dialog1">
      <!--编辑任务-->
      <el-drawer :visible.sync="editTaskshow" :close-on-click-modal="false" direction="rtl" :size="717"
        element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
        <videoeditform v-if="editTaskshow" ref="videotaskeditform" :platformList="platformList"
          :erpUserInfoList="erpUserInfoList" :onCloseAddForm="onCloseAddForm" :taskUrgencyList="taskUrgencyList"
          :warehouselist="warehouselist" :fpPhotoLqNameList="fpPhotoLqNameList" :groupList="groupList" :islook="false" />
      </el-drawer>
    </div>
    <!--加急审核------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="加急审核" :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false"
      v-if="taskUrgencyAproved" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;">
        <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
        <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
          <my-confirm-button type="submit" @click="taskUrgencyApp" />
        </span>
      </template>
    </el-dialog>
    <!--下单记录------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag
      :close-on-click-modal="false">
      <vediotaskorderrecord ref="packdesginorderrecord" v-if="dialogOrderDtlVisible"></vediotaskorderrecord>
    </el-dialog>
    <!--订单日志信息------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="订单日志信息" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag
      :close-on-click-modal="false">
      <orderLogPage v-if="dialogHisVisible" ref="orderLogPage" :orderNoInner="sendOrderNoInner"
        style="z-index:10000;height:600px" />
    </el-dialog>
    <!--下单发货------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="下单发货" :visible.sync="dialogAddOrderVisible" v-loading="dialogAddOrderLoading" width="50%"
      v-dialogDrag :close-on-click-modal="false">
      <videotaskorderdown ref="packdesginorderdown" :warehouselist="warehouselist" :selids="selids" :orderType="1"
        :closedlg="closedlg" v-if="dialogAddOrderVisible"></videotaskorderdown>
      <template #footer>
        <span class="dialog-footer">
          <span style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
          <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
          <my-confirm-button type="submit" :loading="dialogAddOrderSubmitLoding" @click="onAddOrderSave"> 提交
          </my-confirm-button>
        </span>
      </template>
    </el-dialog>
    <!--批量分配-->
    <el-dialog title="分配拍摄" :visible.sync="dialogfppcVisible" width='30%' height='200px' v-dialogDrag
      :close-on-click-modal="false">
      <template>
        <el-form class="ad-form-query" label-position="right" label-width="120px">
          <el-form-item label="已选任务编号：">
            <div>
              <span v-for="(item, index) in selVideoTaskIdSpanList" :key="index" v-html="item"></span>
            </div>
          </el-form-item>
          <el-row>
            <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="24">
              <el-form-item label="分配拍摄:">
                <el-select style="width:100%" v-model="plAssignshootingName" filterable :clearable="true">
                  <el-option v-for="item in erpUserInfoList" :key="item.id + 'tee'" :label="item.label"
                    :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogfppcVisible = false">取 消</el-button>
          <my-confirm-button type="submit" :validate="addAssignshootingValidate" :loading="dialogAddOrderSubmitLoding"
            @click="onAssignshootingSave"> 提交 </my-confirm-button>
        </span>
      </template>
    </el-dialog>
          <!--批量到货-->
          <el-dialog title="批量到货" :visible.sync="dialogpldhVisible" width='30%' height='200px' v-dialogDrag :close-on-click-modal="false">
            <template>
                <el-form class="ad-form-query" label-position="right" label-width="120px">
                    <el-form-item label="已选任务编号：">
                        <div>
                            <span v-for="(item, index) in selVideoTaskIdSpanList" :key="index" v-html="item"></span>
                        </div>
                    </el-form-item>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="24">
                            <el-form-item  label="到货日期">
                                <el-date-picker v-model="dhArrivalDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                    type="date" size="mini" style="width:76%" placeholder="到货日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogpldhVisible = false">取 消</el-button>
                    <my-confirm-button type="submit"  :loading="dialogAddOrderSubmitLoding" @click="onArrivalShootingSave">
                        提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>
    <!--查看详情-->
    <el-dialog title="查看备注" :key="markopentime" :visible.sync="viewReferenceRemark" width="60%"
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <shootingTaskRemark ref="shootingTaskRemark" :rowinfo="selectRowKey" :islook="islook"></shootingTaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
  </my-container>
</template>
<script>
const tableCols = [];
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
  pageViewTaskAsync, pickVideoTaskAsync, unPickVideoTaskAsync, getVedioTaskOrderListById,
  overTaskActionAsync, shootUrgencyTaskSqAsync, shootUrgencyTaskTgAsync
  , unCaclTaskActionAsync, unShopTaskActionAsync, shopTaskActionAsync
  , caclTaskActionAsync, deleteTaskActionAsync, unSignTaskActionAsync
  , signTaskActionAsync, unEndTaskActionAsync, endTaskActionAsync, assignshootingSave,arrivalShootingSave
} from "@/api/media/vediotask";
import { getUserRoleList } from '@/api/media/packdesgin';
import videoeditform from "@/views/media/video/videoeditform.vue";
import videoaddform from "@/views/media/video/videoaddform.vue";
import videotaskTable from "@/views/media/video/maintable/videotaskTable.vue";
import videotaskfilter from "@/views/media/video/maintable/videotaskfilter.vue";
import videotaskorderdown from '@/views/media/video/maintable/videotaskorderdown';
import vediotaskorderrecord from '@/views/media/video/maintable/vediotaskorderrecord';
import checkPermission from '@/utils/permission'
import shootingTaskRemark from '@/views/media/video/VideoTaskRemark'
export default {
  name: "Users",
  inject: ["reload"],
  props: {
    fpPhotoLqNameList: { type: Array, default: () => { return []; } },
    erpUserInfoList: { type: Array, default: () => { return []; } },
    taskUrgencyList: { type: Array, default: () => { return []; } },
    groupList: { type: Array, default: () => { return []; } },
    platformList: { type: Array, default: () => { return []; } },
    warehouselist: { type: Array, default: () => { return []; } },
    filter: { type: Object, default: { isAudioComplate: 0, isShop: 0 } },//
  },
  components: {
    shootingTaskRemark,
    MyContainer,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
    videotaskTable,
    videoeditform,
    videoaddform,
    videotaskfilter,
    videotaskorderdown,
    vediotaskorderrecord,
  },
  data() {
    return {
      dialogpldhVisible:false,
      dhArrivalDate:null,
      that: this,
      viewReferenceRemark: false,
      shootingTaskRemarkrawer: false,
      pageLoading: false,
      //列表相关
      selids: [],
      tasklist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: {},
      sels: [], // 列表选中列
      listLoading: false,
      seafilter: {},
      //新编辑页面
      editTaskshow: false,
      addTask: false,
      //
      taskUrgencyAproved: false,
      taskUrgencyStatus: "1",
      selmicroVedioTaskId: 0,
      addLoading: false,
      //下单记录
      dialogAddOrderLoading: false,
      dialogAddOrderVisible: false,
      dialogHisVisible: false,
      dialogOrderDtlVisible: false,
      //
      dialogfppcVisible: false,
      dialogAddOrderSubmitLoding: false,
      selVideoTaskIdSpanList: [],
      plAssignshootingName: null,
      islook: false,
      markopentime: new Date().getTime() + 3,
            //选中的行
            selectRowKey: null
    };
  },
  watch: {},
  async created() { },
  async mounted() {
    this.seafilter = this.filter;
    await this.onSearch();
    await this.getrole();
    // if (this.role == "b") {
      await this.ShowHideonSearch("b");
    // }
  },
  methods: {
    async getrole() {
      var res = await getUserRoleList();
      if (res?.success) {
        if (res.data == null) {
          this.role = "tz";
        } else if (res.data.indexOf("视觉部经理") > -1) {
          this.role = "b";
        }

      } else {
        this.role = "tz";
      }

    },
    closedlg() {
      this.dialogAddOrderVisible = false;
      this.dialogAddOrderLoading = false;
    },
    //拍摄完成任务
    async pickTask(row, index) {
      var that = this;
      if (row.cuteLqName == "" || row.cuteLqName == null) {
        this.$confirm("确认领取, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          var res = await pickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
          if (res?.success) {
            that.$message({ message: '领取成功', type: "success" });
            that.onSearch();
          }
        });
      } else {
        this.$confirm("确认取消领取, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          var res = await unPickVideoTaskAsync({ videoTaskId: row.videoTaskId, index: index })
          if (res?.success) {
            that.$message({ message: '取消成功', type: "success" });
            that.onSearch();
          }
        });
      }
    },
    handleClose() {
      this.$confirm("是否确定关闭窗口", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.addTask = false;
      });
    },
    //
    async topSearch(searchfilter) {
      this.seafilter = {};
      this.seafilter = {
        ...searchfilter,
        ...this.filter
      }
      await this.onSearch();
    },
    //查询
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getTaskList();
      this.selids = [];
    },
    //刷新当前页
    async onRefresh() {
      await this.getTaskList();
    },
    //获取数据
    async getTaskList() {
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...this.seafilter
      };
      this.listLoading = true;
      let res = await pageViewTaskAsync(params);
      this.listLoading = false;
      if (res?.success) {
        this.total = res.data.total
        this.tasklist = res.data.list;
        this.summaryarry = res.data.summary;
      }
    },
    //列表排序
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async selectchangeevent(records) {
      this.selids = [];
      records.forEach(f => {
        this.selids.push(f.videoTaskId);
      })
    },
    async rowChange(row) {
      await this.editTask(row);
    },
    async onsummaryClick(property) {
      /*  this.fpcharttype = property;
       this.$nextTick(function () { 
           this.$refs.shootingchartforfp.showviewMain();
       });
       this.shootingchartforfpvisible = true; */
    },
    //紧急程度按钮点击
    async shootUrgencyCilck(row) {
      if (this.islook) {
        return;
      }
      var that = this;
      switch (row.taskUrgencyStr) {
        //申请加急
        case "正常":
          if (!checkPermission('api:media:vediotask:ShootUrgencyTaskSqAsync')) return;
          this.$confirm("是否确认加急?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            var res = await shootUrgencyTaskSqAsync({ taskid: row.videoTaskId, index: 0 });
            if (res?.success) {
              that.$message({ message: '操作成功', type: "success" });
              await that.onRefresh();
            }
          });
          break;
        //确认加急
        case "次急":
          if (!checkPermission('api:media:vediotask:ShootUrgencyTaskTgAsync')) return;
          this.selmicroVedioTaskId = row.videoTaskId;
          this.taskUrgencyAproved = true;
          break;
      }
    },
    async taskUrgencyApp() {
      var res = await shootUrgencyTaskTgAsync({ taskid: this.selmicroVedioTaskId, index: this.taskUrgencyStatus });
      if (res?.success) {
        this.taskUrgencyAproved = false;
        this.$message({ message: '操作成功', type: "success" });
        await this.onRefresh();
      }
    },
    //查看成果文件
    openComputOutInfo(row) {
      this.selectRowKey = row.videoTaskId;
      this.successfileshow = true;
    },
    //查看参考附件
    videotaskuploadfileDetal(row) {
      let routeUrl = this.$router.resolve({
        path: '/VedioCutInfo',
        query: { id: row.videoTaskId }
      });
      window.open(routeUrl.href, '_blank');
    },
    async sumbitshootingTaskRemark() {
        this.shootingTaskRemarkrawer = true;
        await this.$refs.shootingTaskRemark.onsubmit();
        this.shootingTaskRemarkrawer = false;
        this.onSearch();
      },
        //查看详情备注页
    openTaskRmarkInfo(row) {
        this.markopentime = this.markopentime + 1;
        this.selectRowKey = row.videoTaskId;
        this.viewReferenceRemark = true;
    },
    //新增编辑窗口关闭
    async onCloseAddForm(type) {
      if (type == 1) {
        this.addTask = false;
      } else if (type == 2) {
        this.addTask = false;
        this.onSearch();
      } else {
        this.editTaskshow = false;
        this.onSearch();
      }
    },
    //导出事件
    async onExport() {
      this.$refs.vediomaintask.exportData("短视频列表导出");
    },
    //新增任务
    async onAddTask() {

      this.addTask = true;
    },
    //下单
    async onAddOrder() {
      if (this.selids.length <= 0) {
        this.$message({ message: '请勾选任务', type: "warning" });
        return;
      }
      this.dialogAddOrderVisible = true;
    },
    async onAddOrderSave() {
      this.dialogAddOrderLoading = true;
      await this.$nextTick(function () {
        this.$refs.packdesginorderdown.onAddOrderSave();
      });
      this.dialogAddOrderLoading = false;
    },
    //下单记录
    async onShowOrderDtl(row) {
      this.dialogOrderDtlVisible = true;
      var ret = await getVedioTaskOrderListById({ videoTaskId: row.videoTaskId });
      if (ret?.success && ret.data.length > 0) {
        ret.data.forEach(f => f.videoTaskId = row.videoTaskId);
        this.xdfhmainlist = ret.data;
        this.xdfhdtllist = ret.data[0].dtlEntities;
      }
    },
    async onShowExproessHttp(row) {
      this.drawervisible = true;
      let expressNo = row.expressNo;
      if (!expressNo) {
        expressNo = row.sampleExpressNo;
      }
      this.$nextTick(function () {
        this.$refs.logistics.showlogistics("", expressNo);
      })
    },
    //编辑任务
    async editTask(row) {
      this.editTaskshow = true;
      await this.$nextTick(function () {
        this.$refs.videotaskeditform.editTask(row);
      });
    },
    //批量操作相关------------------------------------------------------------------------------------
    async handleCommand(command) {
      if (this.selids.length == 0 && command != 'x') {
        this.$message({ type: 'warning', message: "请选择任务" });
        return;
      }
      switch (command) {
        case 'a'://批量终止重启
          await this.unEndTaskAction(this.selids);
          break;
        case 'b'://批量终止
          await this.endTaskAction(this.selids);
          break;
        case 'c'://批量标记
          await this.signTaskAction(this.selids);
          break;
        case 'd'://取消标记
          await this.unSignTaskAction(this.selids);
          break;
        case 'e'://批量删除
          await this.deleteTaskAction(this.selids);
          break;

        case 'g'://批量统计
          await this.caclTaskAction(this.selids);
          break;

        case 'j'://批量存档
          await this.shopTaskAction(this.selids);
          break;
        case 'k'://取消存档
          await this.unShopTaskAction(this.selids);
          break;
        case 'l'://取消统计
          await this.unCaclTaskAction(this.selids);
          break;
        case 'm'://批量完成
          await this.overTaskAction(this.selids);
          break;

        case 'n'://批量分配
          await this.onAssignShooting();
          break;
        case "o"://批量到货
          this.onArrivalShooting();
          break;
      }
    },
    //显示-默认 b默认。a全部
    async ShowHideonSearch(com) {
      this.listLoading = true;
      var checkedColumnsFora = [];
      switch (com) {
        //显示全部 ,部门经理，超管
        case "a":
          checkedColumnsFora = [];
          break;
        //显示默认
        case "b":
          // checkedColumnsFora = [];
          checkedColumnsFora =
            ['Divisionline',
              , 'claimDay1', 'claimTime1Str'
              , 'claimDay2', 'claimTime2Str'
              , 'claimDay3', 'claimTime3Str'
              , 'claimDay4', 'claimTime4Str'
              , 'claimDay5', 'claimTime5Str'
              , 'arrivalTimeDays', 'deliverTimeDays', 'applyTimeDays'
              , 'applyTime'];
          break;
        default:
          break;
      }
      await this.$refs.vediomaintask.ShowHidenColums(checkedColumnsFora);
      this.listLoading = false;
    },
    addAssignshootingValidate() {
      return true;
    },
    //打开分配加工表
    onAssignShooting() {

      this.plAssignshootingName = null;
      this.selVideoTaskIdSpanList = [];
      this.selids.forEach(videoTaskId => {
        //this.addOrderForm.vedioTaskIds += (videoTaskId.toString() + ";");
        this.selVideoTaskIdSpanList.push((videoTaskId.toString() + ";&nbsp"));
      });
      this.dialogfppcVisible = true;

    },  
     //批量到货
     onArrivalShooting() {
          if (this.selids.length <= 0) {
              this.$message({ message: '请勾选任务', type: "warning" });
              return;
          }
          this.dhArrivalDate = null;
          this.selVideoTaskIdSpanList = [];
          this.selids.forEach(videoTaskId => {
              this.selVideoTaskIdSpanList.push((videoTaskId.toString() + ";&nbsp"));
          });
          this.dialogpldhVisible = true;

      },

    //批量修改分配拍摄人
    async onAssignshootingSave() {
      this.dialogAddOrderSubmitLoding = true;
      /*  this.$confirm("此操作将会覆盖之前的负责人, 是否继续?", "提示", {
           confirmButtonText: "确定",
           cancelButtonText: "取消",
           type: "warning",
       }).then(async () => { */
      var res = await assignshootingSave({ taskids: this.selids, SetName: this.plAssignshootingName });
      if (res?.success) {
        this.$message({ type: 'success', message: "操作成功" });
        this.onRefresh();
        this.dialogAddOrderSubmitLoding = false;
        this.dialogfppcVisible = false;
        this.selids=[];
      }
      //});
      this.dialogAddOrderSubmitLoding = false;
    },
     //批量到货
     async onArrivalShootingSave() {
                this.dialogAddOrderSubmitLoding = true;
                var res = await arrivalShootingSave({ taskids: this.selids, arrivalDate: this.dhArrivalDate });
                if (res?.success) {
                    this.$message({ type: 'success', message: "操作成功" });
                    this.onRefresh();
                    this.dialogAddOrderSubmitLoding = false;
                    this.dialogpldhVisible = false;
                    this.selids=[];
                }
                this.dialogAddOrderSubmitLoding = false;
    },
    //批量终止重启
    async endTaskAction(array) {
      this.$confirm("终止任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await endTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量终止
    async unEndTaskAction(array) {
      this.$confirm("重启任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unEndTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量标记
    async signTaskAction(array) {
      this.$confirm("标记任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await signTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //取消标记
    async unSignTaskAction(array) {
      this.$confirm("取消标记，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unSignTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量删除
    async deleteTaskAction(array) {
      this.$confirm("删除任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await deleteTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量统计
    async caclTaskAction(array) {
      this.$confirm("批量统计，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await caclTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量存档
    async shopTaskAction(array) {
      this.$confirm("任务存档，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await shopTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //取消存档
    async unShopTaskAction(array) {
      this.$confirm("取消存档，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unShopTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //取消统计
    async unCaclTaskAction(array) {
      this.$confirm("取消统计，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await unCaclTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
    //批量完成
    async overTaskAction(array) {
      this.$confirm("重启任务，是否确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        var res = await overTaskActionAsync(array);
        if (res?.success) {
          this.$message({ message: '操作成功', type: "success" });
          this.onSearch();
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog1 ::v-deep .el-dialog__body {
  padding: 0 !important;
  // background-color: red;
}

.dialog1 ::v-deep .el-drawer__header {
  display: none !important;
}

.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 15px;
}

::v-deep .myheader {
  padding: 5px 0px 0px 5px;
}

::v-deep .el-tag--dark.el-tag--info {

  cursor: pointer;
}
</style>
