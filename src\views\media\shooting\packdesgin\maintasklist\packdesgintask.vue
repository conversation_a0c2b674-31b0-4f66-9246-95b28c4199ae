<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template #header>
            <packdesginfilter :key="tabkey" @topSearch="topSearch" @onAddTask="onAddTask" @onAddOrder="onAddOrder"
                @ShowHideonSearch="ShowHideonSearch" @handleCommand="handleCommand" @onExport="onExport"
                :platformList="platformList" :warehouselist="warehouselist" :dockingPeopleList="dockingPeopleList"
                :fpPhotoLqNameList="fpPhotoLqNameList" :taskUrgencyList="taskUrgencyList" :islook="islook"
                :groupList="groupList" :packclasslist="packclasslist" :brandList="brandList" :listtype="listtype">
            </packdesginfilter>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <packdesgintaskTable ref="packDesgintask" :id="tabkey" :tableData='tasklist' :tableCols='tableCols' :that='that'
            :height="'100%'" :showsummary='true' :summaryarry='summaryarry' :showCacle="listtype==5" @summaryClick='onsummaryClick'
            :loading='listLoading' @sortchange='sortchange' checkboxall @checkboxall="selectchangeevent"
            @selectchangeevent="selectchangeevent" @rowChange="rowChange" @shootUrgencyCilck="shootUrgencyCilck"
            @openTaskRmarkInfo="openTaskRmarkInfo" @videotaskuploadfileDetal="videotaskuploadfileDetal" @editTask="editTask"
            @openComputOutInfo="openComputOutInfo" >
        </packdesgintaskTable>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" :sizes="[50, 100, 200, 300, 800, 1000, 2000]"
                :page-size="1000" @get-page="getTaskList" />
        </template>
        <!--创建任务-->
        <div class="dialog1">
            <el-dialog title="" :visible.sync="addTask" style="position: fixed; top: -60px;" width="727px"
                :show-close="false" :before-close="handleClose" element-loading-text="拼命加载中" v-dialogDrag
                v-loading="addLoading">
                <packdesgintaskaddfrom ref="microVediotaskaddfrom" v-if="addTask" :onCloseAddForm="onCloseAddForm"
                    :taskUrgencyList="taskUrgencyList" :groupList="groupList" :packclasslist="packclasslist"
                    :brandList="brandList" :warehouselist="warehouselist" :userList="fpPhotoLqNameList"
                    :platformList="platformList" :islook='islook'></packdesgintaskaddfrom>
            </el-dialog>
        </div>
        <!--编辑任务-->
        <div class="dialog1">
            <el-drawer :visible.sync="editTaskshow" :close-on-click-modal="false" direction="rtl" size="767px"
                element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
                <packdesgintaskeditfrom ref="microVediotaskeditfrom" v-if="editTaskshow" :onCloseAddForm="onCloseAddForm"
                    style="height: 100%;width:100%" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :packclasslist="packclasslist" :warehouselist="warehouselist" :userList="fpPhotoLqNameList"
                    :brandList="brandList" :platformList="platformList" :islook='islook' :listtype="listtype">
                </packdesgintaskeditfrom>
                <span slot="title" style="height: 0px;"></span>
            </el-drawer>
        </div>
        <!--加急审核------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="加急审核" :visible.sync="taskUrgencyAproved" width="20%" :close-on-click-modal="false"
            v-if="taskUrgencyAproved" element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <div style="vertical-align: middle; margin-top: 20px;margin-left: 80px;">
                <el-radio v-model="taskUrgencyStatus" label="1" border>同意</el-radio>
                <el-radio v-model="taskUrgencyStatus" label="9" border>驳回</el-radio>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="(taskUrgencyAproved = false)">取 消</el-button>
                    <my-confirm-button type="submit" @click="taskUrgencyApp" />
                </span>
            </template>
        </el-dialog>
        <!--查看上传文件并打分----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-drawer title="查看设计稿" :visible.sync="successfileshow" direction="ltr" :wrapperClosable="true"
            :close-on-press-escape="false" size="40%">
            <packdesgintaskuploadsuccessfilesocre v-if="successfileshow" ref="packdesginoutcomfilelook"
                :rowinfo="selectRowKey" :islook="islook" :isOverList="false" :listtype="listtype"
                style="height: 92%;width:100%"></packdesgintaskuploadsuccessfilesocre>
            <div class="drawer-footer">
                <el-button @click="successfileshow = false">关 闭</el-button>
            </div>
        </el-drawer>
        <!--查看上传附件---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看参考" :visible.sync="viewReference" width="60%" :close-on-click-modal="true"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <packdesgintaskuploadfile ref="packdesgintaskuploadfile" v-if="viewReference" :rowinfo="selectRowKey">
            </packdesgintaskuploadfile>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReference = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!--查看备注------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="查看备注" :visible.sync="viewReferenceRemark" width="60%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
            <packdesgtaskRemark ref="packdesgtaskRemark" :rowinfo="selectRowKey" v-if="viewReferenceRemark"
                :islook="islook"></packdesgtaskRemark>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="viewReferenceRemark = false">关 闭</el-button>
                    <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark"
                        v-show="!islook" />
                </span>
            </template>
        </el-dialog>
        <!--下单记录------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="任务下单记录" :visible.sync="dialogOrderDtlVisible" width='88%' v-dialogDrag
            :close-on-click-modal="false">
            <shootorderrecord ref="packdesginorderrecord" :orderType="1" :taskid="selectRowKey" v-if="dialogOrderDtlVisible"></shootorderrecord>
        </el-dialog>
        <!--订单日志信息------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="订单日志信息" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag
            :close-on-click-modal="false">
            <orderLogPage v-if="dialogHisVisible" ref="orderLogPage" :orderNoInner="sendOrderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>
        <!--下单发货------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <el-dialog title="下单发货" :visible.sync="dialogAddOrderVisible" v-loading="dialogAddOrderLoading" width="50%"
            v-dialogDrag :close-on-click-modal="false">
            <shootorderdown ref="packdesginorderdown" :warehouselist="warehouselist" :selids="selids" :orderType="1"
                :closedlg="closedlg" v-if="dialogAddOrderVisible"></shootorderdown>
            <template #footer>
                <span class="dialog-footer">
                    <span
                        style="font-size:10px;color:red;">点击提交按钮将发起钉钉审批，自提单审批通过即代表到样，非自提单审批通过则自动同步订单到聚水潭。&nbsp;&nbsp;</span>
                    <el-button @click="dialogAddOrderVisible = false">取 消</el-button>
                    <my-confirm-button type="submit" :loading="dialogAddOrderSubmitLoding" @click="onAddOrderSave"> 提交
                    </my-confirm-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {
    pageShootingViewTaskAsync, shootUrgencyCilckAsync
    , endTaskActionAsync, unEndTaskActionAsync, signTaskActionAsync, unSignTaskActionAsync, useTaskActionAsync,
    caclTaskActionAsync, unCaclTaskActionAsync, shopInfoTaskActionAsync, unShopInfoTaskActionAsync, shopTaskActionAsync, unShopTaskActionAsync,
    deleteTaskActionAsync, delTaskReActionAsync, dropTaskActionAsync, shootUrgencyTaskAsync,getUserRoleList
} from '@/api/media/packdesgin';
import { pagePackDesginCaclTaskAsync ,getHistoryPackageDesignTaskInfo} from '@/api/media/shootingset';
import packdesginfilter from '@/views/media/shooting/packdesgin/publicsource/packdesginfilter';
import packdesgintaskaddfrom from '@/views/media/shooting/packdesgin/packdesgintaskaddfrom';
import packdesgintaskeditfrom from '@/views/media/shooting/packdesgin/packdesgintaskeditfrom';
import packdesgintaskuploadsuccessfilesocre from '@/views/media/shooting/packdesgin/packdesgintaskuploadsuccessfilesocre'
import packdesgtaskRemark from '@/views/media/shooting/packdesgin/packdesginTaskRemark';
import shootorderdown from '@/views/media/shooting/shared/shootorderdown';
import shootorderrecord from '@/views/media/shooting/shared/shootorderrecord';
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
import packdesgintaskuploadfile from '@/views/media/shooting/packdesgin/packdesgintaskuploadfile'
import packdesgintaskTable from '@/views/media/shooting/packdesgin/publicsource/packdesgintaskTable'
import packdesginoutcomfilelook from '@/views/media/shooting/packdesgin/packdesginoutcomfilelook'
const tableCols = [
    { istrue: true, prop: 'packageDesignTaskId', label: '编号', width: '50', fixed: 'left' },
    { istrue: true, prop: 'productShortName', label: '产品简称', width: '160', fixed: 'left', type: "click", handle: (that, row) => that.openComputOutInfo(row) },
    { istrue: true, type: "urgencyediticon", width: "38", fixed: 'left', label: '' },
    {
        istrue: true, prop: 'taskUrgencyName', label: '紧急程度', width: '80', fixed: 'left', type: 'UrgencyButton'
        , handle: (that, row) => that.shootUrgencyCilck(row.taskUrgencyName, row.packageDesignTaskId)
    },

    { istrue: true, prop: 'warehouseStr', label: '大货仓', width: '100', fixed: 'left' },
    //---
    { istrue: true, prop: 'packClassStr', label: '包装类型', align: 'center', width: '100', fixed: 'left' },

    { istrue: true, prop: 'beizhu', label: '', width: '38', type: "clickflag", fixed: 'left', handle: (that, row) => that.openTaskRmarkInfo(row) },
    { istrue: true, prop: 'cankao', type: "fileicon", width: "38", label: '', fixed: 'left', handle: (that, row) => that.videotaskuploadfileDetal(row) },
    {
        istrue: true, permission: "api:media:shootingvideo:AddOrUpdateShootingVideoTaskAsync",
        prop: 'caozoulie', type: "editicon", fixed: 'left', width: "40", label: '', handle: (that, row) => that.editTask(row)
    },
    //详情页 
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)' },
    { istrue: true, prop: 'detailLqNameStr', label: '设计师', width: "70", align: 'center' },
    { istrue: true, prop: 'detailDaysStr', label: '天数', width: '53', align: 'center' },
    { istrue: true, prop: 'detailOverTimeStr', width: '80', label: '完成日期', align: 'center' },
    { istrue: true, prop: 'detailConfirmNameStr', label: '确认人', width: "75", align: 'center' },
    //---确认天数
    { istrue: true, prop: 'detailDaysStr', label: '天数', width: '53', align: 'center' },
    { istrue: true, permission: 'shootConfirmationDate', prop: 'detailConfirmTimeStr', label: '确认日期', width: '80', align: 'center' },
    //分配
    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)' },
    { istrue: true, prop: 'fpDetailLqNameStr', width: '80', align: 'center', label: '分配设计' },

    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)' },
    //---
    { istrue: true, prop: '', width: '80', align: 'center', label: '产品ID' },
    { istrue: true, prop: 'operationGroupstr', align: 'left', width: '90', label: '运营小组' },
    { istrue: true, prop: 'dockingPeople', align: 'left', width: '90', label: '对接人' },
    { istrue: true, prop: 'platformStr', align: 'left', width: '100', label: '平台' },
    { istrue: true, prop: 'shopNameStr', align: 'left', width: '180', label: '店铺' },

    { istrue: true, prop: 'arrivalTimeStr', width: '80', label: '到货日期' },
    { istrue: true, prop: 'arrivalTimeDays', width: '50', label: '到货天数' },
    { istrue: true, prop: 'deliverTimeStr', width: '80', label: '发货日期' },
    { istrue: true, prop: 'deliverTimeDays', width: '50', label: '发货天数' },
    { istrue: true, prop: 'applyTimeStr', width: '80', label: '申请日期' },
    { istrue: true, prop: 'applyTimeDays', width: '50', label: '申请天数' },
    { istrue: true, prop: 'createdTime', width: '100', label: '创建日期', sortable: 'custom', formatter: (row) => row.createdTimeStr },

    { istrue: true, type: 'color', backgroudColor: 'color: rgb(255, 255, 255,0)', label: '' },
    { istrue: true, prop: 'orderNoInner', width: '100', label: '内部单号', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row) },
    { istrue: true, prop: 'expressNo', width: '135', label: '快递单号', type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
    { istrue: true, prop: 'shootOrderTrack', width: '80', label: '拿样跟踪', type: 'click', handle: (that, row, column) => that.onShowOrderDtl(row) },

];
export default {
    components: {
        MyContainer, MyConfirmButton, packdesginfilter, packdesgintaskaddfrom, packdesgintaskeditfrom
        , packdesgintaskuploadsuccessfilesocre, packdesgtaskRemark, shootorderrecord, packdesginoutcomfilelook,
        packdesgintaskuploadfile, orderLogPage, shootorderdown, packdesgintaskTable
    },
    props: {
        taskUrgencyList: { type: Array, default: [] }, //平台
        platformList: { type: Array, default: [] }, //平台
        warehouselist: { type: Array, default: [] }, //仓库
        packclasslist: { type: Array, default: [] }, //仓库 
        dockingPeopleList: { type: Array, default: [] }, //对接人
        fpPhotoLqNameList: { type: Array, default: [] }, //分配查询
        groupList: { type: Array, default: [] }, //运营组
        role: { type: String, default: "tz" },//用户角色
        tablekey: { type: String, default: "" },// 
        islook: { type: Boolean, default: true }, //平台
        filter: { type: Object, default: { isShop: 0, isdel: 0, isComplate: 0 } },//
        tabkey: { type: String, default: "packdesgintask" }, //平台 
        listtype: { type: Number, default: 1 },
        brandList: { type: Array, default: [] }, // 
        versionId: { type: String, default: "0" },
    },
    watch: {},
    data() {
        return {
            that: this,
            pageLoading: false,
            //列表相关
            selids: [],
            tasklist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: {},
            sels: [], // 列表选中列
            listLoading: false,
            //选中的行id
            //编辑新增

            addTask: false,
            editTaskshow: false,
            addLoading: false,
            //编辑新增
            //审核相关
            taskUrgencyAproved: false,
            taskUrgencyStatus: "1",
            selmicroVedioTaskId: 0,
            //审核end
            selectRowKey: 0,
            //打分相关
            successfileshow: false,
            //打分相关end
            viewReferenceRemark: false,
            shootingTaskRemarkrawer: false,
            //下单记录
            dialogAddOrderLoading: false,
            dialogAddOrderVisible: false,
            dialogHisVisible: false,
            dialogAddOrderSubmitLoding: false,
            dialogOrderDtlVisible: false,

            viewReference: false,
            seafilter: {}
        };
    },
    async created() { },
    async mounted() {
        this.seafilter = this.filter;
        await this.onSearch();
        await this.getrole();
        if (this.role == "b") { 
            await this.ShowHideonSearch("b");
        }
    },
    methods: {
        async getrole() {
            var res = await getUserRoleList();
            if (res?.success) {
                if (res.data == null) {
                    this.role = "tz";
                } else if (res.data.indexOf("视觉部经理") > -1) {
                    this.role = "b";
                }

            } else {
                this.role = "tz";
            }

        },
        closedlg() {
            this.dialogAddOrderVisible = false;
            this.dialogAddOrderLoading = false;
        },
        handleClose() {
            this.$confirm("是否确定关闭窗口", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.addTask = false;
            });
        },

        async topSearch(searchfilter) {
            this.seafilter = {};
            this.seafilter = {
                ...searchfilter,
                ...this.filter
            }
            await this.onSearch();
        },
        //查询
        async onSearch() {
            this.$refs.pager.setPage(1);
            this.getTaskList();
            this.selids = [];
        },
        //刷新当前页
        async onRefresh() {
            await this.getTaskList();
        },
        //获取数据
        async getTaskList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.seafilter
            };
            this.listLoading = true;
            let res ={};
            if(this.listtype == 5){ 
                if (this.versionId != "0") {
                    res = await getHistoryPackageDesignTaskInfo(params);
                } else {
                    res = await pagePackDesginCaclTaskAsync(params);
                }
            }else{
              res = await pageShootingViewTaskAsync(params);
            }
            this.listLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.tasklist = res.data.list;
                this.summaryarry = res.data.summary;
            }
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async selectchangeevent(records) {
            this.selids = [];
            records.forEach(f => {
                this.selids.push(f.packageDesignTaskId);
            })
        },
        async rowChange(row) {
            await this.editTask(row);
        },
        async onsummaryClick(property) {
            this.fpcharttype = property;
            this.$nextTick(function () {
                this.$refs.shootingchartforfp.showviewMain();
            });
            this.shootingchartforfpvisible = true;
        },
        //紧急程度按钮点击
        async shootUrgencyCilck(row) {
            if (this.islook) {
                return;
            }
            var that = this;
            switch (row.taskUrgencyName) {
                //申请加急
                case "正常":
                    this.$confirm("是否确认加急?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(async () => {
                        var res = await shootUrgencyCilckAsync({ taskid: row.packageDesignTaskId, index: 0 });
                        if (res?.success) {
                            that.$message({ message: '操作成功', type: "success" });
                            await that.onRefresh();
                        }
                    });
                    break;
                //确认加急
                case "审核":
                    this.selmicroVedioTaskId = row.packageDesignTaskId;
                    this.taskUrgencyAproved = true;
                    break;
            }
        },
        async taskUrgencyApp() {
            var res = await ({ taskid: this.selmicroVedioTaskId, index: this.taskUrgencyStatus });
            if (res?.success) {
                this.taskUrgencyAproved = false;
                this.$message({ message: '操作成功', type: "success" });
                await this.onRefresh();
            }
        },
        //显示-默认 b默认。a全部
        async ShowHideonSearch(com) {
            this.listLoading = true;
            var checkedColumnsFora = [];
            switch (com) {
                //显示全部 ,部门经理，超管
                case "a":
                    checkedColumnsFora = []; 
                    break;
                //显示默认
                case "b": 
                    // checkedColumnsFora = [];
                    checkedColumnsFora =
                        ['Divisionline', 'cankao'
                            , 'photoDaysStr', 'photoOverTimeStr'
                            , 'vedioDaysStr', 'vedioOverTimeStr', 'vedioConfirmNameStr', 'vedioConfirmTimeStr'
                            , 'microDetailDaysStr', 'microDetailOverTimeStr', 'microDetailVedioCounts', 'microDetailConfirmNameStr'
                            , 'microDetailConfirmTimeStr'
                            , 'detailDaysStr', 'detailOverTimeStr', 'detailConfirmTimeStr','detailConfirmDaysStr'
                            , 'modelPhotosDaysStr', 'modelPhotosOverTimeStr', 'modelPhotoCounts'
                            , 'modelVideoDaysStr', 'modelVideoOverTimeStr', 'modelVedioCounts'
                            , 'productID' 
                            ,'applyTimeDays','applyTimeStr'
                            , 'arrivalTimeDays', 'deliverTimeStr', 'deliverTimeDays',]; 
                    break; 
                default:
                    break;
            } 
            await   this.$refs.packDesgintask.ShowHidenColums(checkedColumnsFora);
            this.listLoading = false;
        },
        //新增任务
        async onAddTask() {
            this.addTask = true;
        },
        //编辑任务
        async editTask(row) {
            this.addLoading = true;
            this.editTaskshow = true;
            await this.$nextTick(function () {
                this.$refs.microVediotaskeditfrom.editTask(row);
            });
            this.addLoading = false;
        },
        //提交保存
        async onSubmit() {
            this.addLoading = true;
            await this.$nextTick(function () {
                this.$refs.microVediotaskaddfrom.onSubmit();
            });
            this.addLoading = false;
        },
        //
        async onAddOrderSave() {
            this.dialogAddOrderLoading = true;
            await this.$nextTick(function () {
                this.$refs.packdesginorderdown.onAddOrderSave();
            });
            this.dialogAddOrderLoading = false;
        },
        //批量操作操作事件
        async handleCommand(command) {
            if (this.selids.length == 0 && command != 'x') {
                this.$message({ type: 'warning', message: "请选择任务" });
                return;
            }
            switch (command) {
                case 'a'://批量终止重启
                    await this.unEndTaskAction(this.selids);
                    break;
                case 'b'://批量终止
                    await this.endTaskAction(this.selids);
                    break;
                case 'c'://批量标记
                    await this.signTaskAction(this.selids);
                    break;
                case 'd'://取消标记
                    await this.unSignTaskAction(this.selids);
                    break;
                case 'e'://批量删除
                    await this.deleteTaskAction(this.selids);
                    break;
                case 'f'://批量使用
                    await this.useTaskAction(this.selids);
                    break;
                case 'g'://批量统计
                    await this.caclTaskAction(this.selids);
                    break;
                case 'h'://信息存档
                    await this.shopInfoTaskAction(this.selids);
                    break;
                case 'i'://信息取消存档
                    await this.unShopInfoTaskAction(this.selids);
                    break;
                case 'j'://批量存档
                    await this.shopTaskAction(this.selids);
                    break;
                case 'k'://取消存档
                    await this.unShopTaskAction(this.selids);
                    break;
                case 'l'://取消统计
                    await this.unCaclTaskAction(this.selids);
                    break;
                case 'm'://彻底删除
                    await this.dropTaskAction(this.selids);
                    break;
                case 'n'://任务重启
                    await this.delTaskReAction(this.selids);
                    break;
            }
        },

        //批量终止重启
        async endTaskAction(array) {
            this.$confirm("终止任务，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量终止
        async unEndTaskAction(array) {
            this.$confirm("重启任务，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unEndTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量标记
        async signTaskAction(array) {
            this.$confirm("标记任务，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await signTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //取消标记
        async unSignTaskAction(array) {
            this.$confirm("取消标记，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unSignTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量删除
        async deleteTaskAction(array) {
            this.$confirm("删除任务，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量使用
        async useTaskAction(array) {
            this.$confirm("批量使用，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await useTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量统计
        async caclTaskAction(array) {
            this.$confirm("批量统计，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await caclTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //信息存档
        async shopInfoTaskAction(array) {
            this.$confirm("信息存档，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await shopInfoTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //信息取消存档
        async unShopInfoTaskAction(array) {
            this.$confirm("取消存档，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unShopInfoTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量存档
        async shopTaskAction(array) {
            this.$confirm("任务存档，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await shopTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //取消存档
        async unShopTaskAction(array) {
            this.$confirm("取消存档，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unShopTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //取消统计
        async unCaclTaskAction(array) {
            this.$confirm("取消统计，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unCaclTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },
        //批量重启
        async delTaskReAction(array) {
            this.$confirm("重启任务，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delTaskReActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },

        async dropTaskAction(array) {
            this.$confirm("彻底删除，是否确认", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await dropTaskActionAsync(array);
                if (res?.success) {
                    this.$message({ message: '操作成功', type: "success" });
                    this.onSearch();
                }
            });
        },

        //导出事件
        async onExport(type) {
            this.$refs.packDesgintask.exportData("包装设计导出", type);
        },
        //新增编辑窗口关闭
        async onCloseAddForm(type) {
            if (type == 1) {
                this.addTask = false;
            } else if (type == 2) {
                this.addTask = false;
                this.onSearch();
            } else {
                this.editTask = false;
                this.onSearch();
            }
        },
        //查看成果文件
        openComputOutInfo(row) {
            this.selectRowKey = row.packageDesignTaskId;
            this.successfileshow = true;
        },
        //查看参考附件
        videotaskuploadfileDetal(row) {
            this.selectRowKey = row.packageDesignTaskId;
            this.viewReference = true;
        },
        //查看详情备注页
        openTaskRmarkInfo(row) {
            this.selectRowKey = row.packageDesignTaskId;
            this.viewReferenceRemark = true;
        },
        //独立备注提交-代码
        async sumbitshootingTaskRemark() {
            this.shootingTaskRemarkrawer = true;
            await this.$refs.packdesgtaskRemark.onsubmit();
            this.shootingTaskRemarkrawer = false;
        },
        //下单
        async onAddOrder() {
            if (this.selids.length <= 0) {
                this.$message({ message: '请勾选任务', type: "warning" });
                return;
            }
            this.dialogAddOrderVisible = true;
        },
        //下单记录
        async onShowOrderDtl(row) {
            this.dialogOrderDtlVisible = true;
            
            this.selectRowKey = row.packageDesignTaskId;
            // var ret = await getShootingTaskOrderListById({ shootingTaskId: row.shootingTaskId });
            // if (ret?.success && ret.data.length > 0) {
            //     ret.data.forEach(f => f.shootingTaskId = row.shootingTaskId);
            //     this.xdfhmainlist = ret.data;
            //     this.xdfhdtllist = ret.data[0].dtlEntities;
            // }
        },
        async onShowExproessHttp(row) {
            this.drawervisible = true;
            let expressNo = row.expressNo;
            if (!expressNo) {
                expressNo = row.sampleExpressNo;
            }
            this.$nextTick(function () {
                this.$refs.logistics.showlogistics("", expressNo);
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.dialog1 ::v-deep .el-dialog__body {
    padding: 0 !important;
    // background-color: red;
}

.dialog1 ::v-deep .el-drawer__header {
    display: none !important;
}

::v-deep .myheader {
    padding: 5px 0px 0px 5px;
}
</style>
