<template>
    <div v-loading="loading">
        <el-form ref="ruleForm" :model="ruleForm" status-icon :rules="rules" label-width="180px" class="demo-ruleForm">
            <el-row>
                <el-col :span="8">
                    <fieldset style="height: 440px;width: 400px;overflow: auto;">
                        <legend>调拨设置:</legend>
                        <el-form-item label="调拨数据源:" prop="allotType">
                            <el-select v-model="ruleForm.allotType" clearable placeholder="请选择调拨数据源" class="publicCss">
                                <el-option label="全仓销量" :value="0" />
                                <el-option label="义乌出货量" :value="1" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="统计销量天数:" prop="saleDay">
                            <el-input-number v-model="ruleForm.saleDay" placeholder="统计销量天数" :min="0" :max="100000"
                                :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="安全库存天数:" prop="safeDay">
                            <el-input-number v-model="ruleForm.safeDay" placeholder="安全库存天数" :min="0" :max="100000"
                                :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="最小安全天数:" prop="minSafeDay">
                            <el-input-number v-model="ruleForm.minSafeDay" placeholder="最小安全天数" :min="0" :max="100"
                                :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="调拨批次时间:" prop="allotTimesRange">
                            <el-select v-model="ruleForm.allotTimesRange" clearable filterable multiple collapse-tags
                                placeholder="请选择调拨批次时间" class="publicCss">
                                <el-option v-for="item in timeList" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="滞销调出最大编码数:" prop="unsalableMaxCount">
                            <el-input-number v-model="ruleForm.unsalableMaxCount" placeholder="滞销调拨最大编码数" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="调回热销最大编码数:" prop="hotSaleMaxCount">
                            <el-input-number v-model="ruleForm.hotSaleMaxCount" placeholder="调回热销最大编码数" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="热销补货最大编码数:" prop="stockMaxCount">
                            <el-input-number v-model="ruleForm.stockMaxCount" placeholder="热销补货最大编码数" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="热销备货最大编码数:" prop="hotStockMaxCount">
                            <el-input-number v-model="ruleForm.hotStockMaxCount" placeholder="热销备货最大编码数" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="配件调拨最大编码数:" prop="fittingsMaxCount">
                            <el-input-number v-model="ruleForm.fittingsMaxCount" placeholder="配件调拨最大编码数" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="配件调拨方式:" prop="fittingsAllotType">
                            <el-select v-model="ruleForm.fittingsAllotType" clearable filterable collapse-tags
                                placeholder="请选择配件调拨方式" class="publicCss">
                                <el-option label="仓库占比" :value="0" />
                                <el-option label="仓库出货量" :value="1" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="配件调拨缓冲小时:" prop="fittingsBufferHour">
                            <el-input-number v-model="ruleForm.fittingsBufferHour" placeholder="配件调拨缓冲小时" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="热销滞销调拨缓冲小时:" prop="hotUaeBufferHour">
                            <el-input-number v-model="ruleForm.hotUaeBufferHour" placeholder="热销滞销调拨缓冲小时" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>

                    </fieldset>
                </el-col>
                <el-col :span="8">
                    <fieldset style="height: 440px;width: 400px;overflow: auto;">
                        <legend>白名单:</legend>
                        <div style="background-color: #f2f2f2;padding: 10px;margin-bottom: 5px;">
                            加入白名单编码，将默认常驻在主仓，不会做滞销处理，会做补货处理。</div>
                        <el-form-item label="采购白名单:">
                            <el-select v-model="ruleForm.brandIds" clearable filterable multiple collapse-tags
                                placeholder="请选择采购员" class="publicCss">
                                <el-option v-for="item in brandlist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="款式编码白名单:">
                            <el-input placeholder="款式编码白名单" v-model="ruleForm.whitelistStyleCodesStr" clearable
                                type="textarea" maxlength="500" class="publicCss" :rows="7" resize="none">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="商品编码白名单:">
                            <el-input placeholder="商品编码白名单" v-model="ruleForm.whitelistGoodsCodesStr" clearable
                                type="textarea" maxlength="500" class="publicCss" :rows="7" resize="none">
                            </el-input>
                        </el-form-item>

                    </fieldset>
                </el-col>
                <el-col :span="8">
                    <fieldset style="height: 440px;width: 400px;overflow: auto;">
                        <legend>仓库设置:</legend>
                        <el-form-item label="主仓库:" prop="mainWmsId">
                            <chooseWareHouse v-model="ruleForm.mainWmsId" class="publicCss"
                                @chooseWms="chooseWms($event, 1)" />
                        </el-form-item>
                        <el-form-item label="主仓仓位数:" prop="mainWmsBinCount">
                            <el-input-number v-model="ruleForm.mainWmsBinCount" placeholder="主仓仓位数" :min="0"
                                :max="100000" :controls="false" class="publicCss" />
                        </el-form-item>
                        <el-form-item label="滞销仓:" prop="unsalableWmsId">
                            <chooseWareHouse v-model="ruleForm.unsalableWmsId" class="publicCss"
                                @chooseWms="chooseWms($event, 2)" />
                        </el-form-item>
                        <el-form-item label="备货仓库:" prop="stockWmsId">
                            <chooseWareHouse v-model="ruleForm.stockWmsId" class="publicCss"
                                @chooseWms="chooseWms($event, 3)" />
                        </el-form-item>
                        <el-form-item label="默认主仓负责人:" prop="mainWmsHeadId">
                            <YhUserelector :value.sync="ruleForm.mainWmsHeadId" :text.sync="ruleForm.mainWmsHeader"
                                class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="默认滞销仓负责人:" prop="unsalableWmsHeadId">
                            <YhUserelector :value.sync="ruleForm.unsalableWmsHeadId"
                                :text.sync="ruleForm.unsalableWmsHeader" class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="默认备货仓负责人:" prop="stockWmsHeadId">
                            <YhUserelector :value.sync="ruleForm.stockWmsHeadId" :text.sync="ruleForm.stockWmsHeader"
                                class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="滞销调出发起人:">
                            <YhUserelector :value.sync="ruleForm.unsalableApplicant"
                                :text.sync="ruleForm.unsalableApplicantName" class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="滞销调出审批人:">
                            <yhUserselectors class="publicCss" :value.sync="ruleForm.unsalableApprover"
                                :text.sync="ruleForm.unsalableApproverName" clearable v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="调回热销发起人:">
                            <YhUserelector :value.sync="ruleForm.hotSaleApplicant"
                                :text.sync="ruleForm.hotSaleApplicantName" class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="调回热销审批人:">
                            <yhUserselectors class="publicCss" :value.sync="ruleForm.hotSaleApprover"
                                :text.sync="ruleForm.hotSaleApproverName" clearable v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="热销补货发起人:">
                            <YhUserelector :value.sync="ruleForm.stockApplicant"
                                :text.sync="ruleForm.stockApplicantName" class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="热销补货审批人:">
                            <yhUserselectors class="publicCss" :value.sync="ruleForm.stockApprover"
                                :text.sync="ruleForm.stockApproverName" clearable v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="热销备货发起人:">
                            <YhUserelector :value.sync="ruleForm.hotStockApplicant"
                                :text.sync="ruleForm.hotStockApplicantName" class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="热销备货审批人:">
                            <yhUserselectors class="publicCss" :value.sync="ruleForm.hotStockApprover"
                                :text.sync="ruleForm.hotStockApproverName" clearable v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="配件调拨发起人:">
                            <YhUserelector :value.sync="ruleForm.fittingsApplicant"
                                :text.sync="ruleForm.fittingsApplicantName" class="publicCss" v-if="isVisible" />
                        </el-form-item>
                        <el-form-item label="配件调拨审批人:">
                            <yhUserselectors class="publicCss" :value.sync="ruleForm.fittingsApprover"
                                :text.sync="ruleForm.fittingsApproverName" clearable v-if="isVisible" />
                        </el-form-item>
                    </fieldset>
                </el-col>
            </el-row>

            <div class="btnGroup">
                <el-button @click="$emit('close')">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="2000">提交</el-button>
            </div>
        </el-form>
    </div>
</template>

<script>
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import yhUserselectors from '@/components/YhCom/yh-userselectors.vue'
// import YhUserselectormulti from "@/components/YhCom/yh-userselectormulti.vue";
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import dayjs from 'dayjs'
import request from '@/utils/request'
const api = '/api/verifyOrder/WmsAllot/'
const timeList = []
for (let i = 0; i < 24; i++) {
    for (let j = 0; j < 60; j += 30) {
        timeList.push(dayjs().hour(i).minute(j).format('HH:mm'))
    }
}
export default {
    components: {
        chooseWareHouse, YhUserelector, yhUserselectors
    },
    data() {
        return {
            api,
            timeList,
            loading: false,
            ruleForm: {
                whitelistBrands: null,//采购白名单
                whitelistStyleCodes: null,//款式编码白名单
                whitelistGoodsCodes: null,//商品编码白名单
                saleDay: undefined,//统计销量天数
                safeDay: undefined,//安全库存天数
                minSafeDay: undefined,//最小安全天数
                mainWmsId: null,//主仓库
                mainWmsName: null,//主仓库名称
                mainWmsHeader: null,//主仓负责人
                mainWmsBinCount: undefined,//主仓仓位数
                mainWmsHeadId: null,//主仓负责人
                unsalableWmsId: null,//滞销仓
                unsalableWmsName: null,//滞销仓名称
                unsalableWmsHeadId: null,//滞销仓负责人
                stockWmsId: null,//备货仓库
                stockWmsName: null,//备货仓库名称
                stockWmsHeadId: null,//备货仓负责人
                allotTimes: null,//调拨批次时间
                allotTimesRange: [],//调拨批次时间
                unsalableMaxCount: undefined,//滞销调拨最大编码数
                hotSaleMaxCount: undefined,//热销调拨最大编码数
                stockMaxCount: undefined,//滞销调拨最大款式编码数
                whitelistStyleCodesStr: '',//款式编码白名单
                whitelistGoodsCodesStr: '',//商品编码白名单
                brandIds: [],
                unsalableWmsHeader: null,//滞销仓负责人
                stockWmsHeader: null,//备货仓负责人
                allotType: null,//调拨数据源
                hotStockMaxCount: undefined,//备货调拨最大编码数
                fittingsAllotType: null,//配件调拨方式
                fittingsBufferHour: undefined,//配件调拨缓冲小时
                hotUaeBufferHour: undefined,//热销滞销调拨缓冲小时
                fittingsMaxCount: undefined,//调拨最大编码数
                unsalableApplicant: null,//滞销调出发起人
                unsalableApprover: null,//滞销调出审批人
                hotSaleApplicant: null,//调回热销发起人
                hotSaleApprover: null,//调回热销审批人
                stockApplicant: null,//热销补货发起人
                stockApprover: null,//热销补货审批人
                hotStockApplicant: null,//热销备货发起人
                hotStockApprover: null,//热销备货审批人
                fittingsApplicant: null,//配件调拨发起人
                fittingsApprover: null,//配件调拨审批人
                unsalableApplicantName: null,//滞销调出发起人
                unsalableApproverName: null,//滞销调出审批人
                hotSaleApplicantName: null,//调回热销发起人
                hotSaleApproverName: null,//调回热销审批人
                stockApplicantName: null,//热销补货发起人
                stockApproverName: null,//热销补货审批人
                hotStockApplicantName: null,//热销备货发起人
                hotStockApproverName: null,//热销备货审批人
                fittingsApplicantName: null,//配件调拨发起人
                fittingsApproverName: null,//配件调拨审批人
            },
            rules: {
                saleDay: [{ required: true, message: '请输入统计销量天数', trigger: 'blur' }],
                safeDay: [{ required: true, message: '请输入安全库存天数', trigger: 'blur' }],
                minSafeDay: [{ required: true, message: '请输入最小安全天数', trigger: 'blur' }],
                allotTimesRange: [{ required: true, message: '请选择调拨批次时间', trigger: 'change' }],
                unsalableMaxCount: [{ required: true, message: '请输入滞销调拨最大编码数', trigger: 'blur' }],
                hotSaleMaxCount: [{ required: true, message: '请输入热销调拨最大编码数', trigger: 'blur' }],
                stockMaxCount: [{ required: true, message: '请输入热销补货最大编码数', trigger: 'blur' }],
                mainWmsId: [{ required: true, message: '请选择主仓库', trigger: 'change' }],
                mainWmsBinCount: [{ required: true, message: '请输入主仓仓位数', trigger: 'blur' }],
                mainWmsHeadId: [{ required: true, message: '请选择主仓负责人', trigger: 'change' }],
                unsalableWmsId: [{ required: true, message: '请选择滞销仓', trigger: 'change' }],
                unsalableWmsHeadId: [{ required: true, message: '请选择滞销仓负责人', trigger: 'change' }],
                stockWmsId: [{ required: true, message: '请选择备货仓库', trigger: 'change' }],
                stockWmsHeadId: [{ required: true, message: '请选择备货仓负责人', trigger: 'change' }],
                hotStockMaxCount: [{ required: true, message: '请输入备货调拨最大编码数', trigger: 'blur' }],
                fittingsAllotType: [{ required: true, message: '请选择配件调拨方式', trigger: 'change' }],
                fittingsBufferHour: [{ required: true, message: '请输入配件调拨缓冲小时', trigger: 'blur' }],
                hotUaeBufferHour: [{ required: true, message: '请输入热销滞销调拨缓冲小时', trigger: 'blur' }],
                fittingsMaxCount: [{ required: true, message: '请输入配件调拨最大编码数', trigger: 'blur' }],
                unsalableApplicant: [{ required: true, message: '请选择滞销调出发起人', trigger: 'change' }],
                unsalableApprover: [{ required: true, message: '请选择滞销调出审批人', trigger: 'change' }],
                hotSaleApplicant: [{ required: true, message: '请选择调回热销发起人', trigger: 'change' }],
                hotSaleApprover: [{ required: true, message: '请选择调回热销审批人', trigger: 'change' }],
                stockApplicant: [{ required: true, message: '请选择热销补货发起人', trigger: 'change' }],
                stockApprover: [{ required: true, message: '请选择热销补货审批人', trigger: 'change' }],
                hotStockApplicant: [{ required: true, message: '请选择热销备货发起人', trigger: 'change' }],
                hotStockApprover: [{ required: true, message: '请选择热销备货审批人', trigger: 'change' }],
                fittingsApplicant: [{ required: true, message: '请选择配件调拨发起人', trigger: 'change' }],
                fittingsApprover: [{ required: true, message: '请选择配件调拨审批人', trigger: 'change' }],
            },
            brandlist: [],  // 采购员列表
            isVisible: false
        }
    },
    async mounted() {
        await this.init()
        await this.getSetting()
    },
    methods: {
        clear() {
            this.ruleForm = {
                whitelistBrands: null,//采购白名单
                whitelistStyleCodes: null,//款式编码白名单
                whitelistGoodsCodes: null,//商品编码白名单
                saleDay: undefined,//统计销量天数
                safeDay: undefined,//安全库存天数
                minSafeDay: undefined,//最小安全天数
                mainWmsId: null,//主仓库
                mainWmsName: null,//主仓库名称
                mainWmsHeader: null,//主仓负责人
                mainWmsBinCount: undefined,//主仓仓位数
                mainWmsHeadId: null,//主仓负责人
                unsalableWmsId: null,//滞销仓
                unsalableWmsName: null,//滞销仓名称
                unsalableWmsHeadId: null,//滞销仓负责人
                stockWmsId: null,//备货仓库
                stockWmsName: null,//备货仓库名称
                stockWmsHeadId: null,//备货仓负责人
                allotTimes: null,//调拨批次时间
                allotTimesRange: [],//调拨批次时间
                unsalableMaxCount: undefined,//滞销调拨最大编码数
                hotSaleMaxCount: undefined,//热销调拨最大编码数
                stockMaxCount: undefined,//滞销调拨最大款式编码数
                whitelistStyleCodesStr: '',//款式编码白名单
                whitelistGoodsCodesStr: '',//商品编码白名单
                brandIds: [],
                unsalableWmsHeader: null,
                stockWmsHeader: null,
                allotType: null,
                hotStockMaxCount: undefined,
                fittingsAllotType: null,
                fittingsBufferHour: undefined,
                hotUaeBufferHour: undefined,
                fittingsMaxCount: undefined,
                unsalableApplicant: null,
                unsalableApprover: null,
                hotSaleApplicant: null,
                hotSaleApprover: null,
                stockApplicant: null,
                stockApprover: null,
                hotStockApplicant: null,
                hotStockApprover: null,
                fittingsApplicant: null,
                fittingsApprover: null,
                unsalableApplicantName: null,
                unsalableApproverName: null,
                hotSaleApplicantName: null,
                hotSaleApproverName: null,
                stockApplicantName: null,
                stockApproverName: null,
                hotStockApplicantName: null,
                hotStockApproverName: null,
                fittingsApplicantName: null,
                fittingsApproverName: null,
            }
        },
        async getSetting() {
            this.clear()
            this.loading = true
            const { data, success } = await request.post(`${this.api}GetSetting`)
            if (success) {
                this.$nextTick(() => {
                    data.allotTimesRange = data.allotTimes ? data.allotTimes.split(',') : []
                    data.brandIds = data.whitelistBrands ? data.whitelistBrands.split(',') : []
                    data.whitelistStyleCodesStr = data.whitelistStyleCodes ? data.whitelistStyleCodes : ''
                    data.whitelistGoodsCodesStr = data.whitelistGoodsCodes ? data.whitelistGoodsCodes : ''
                    data.unsalableApprover = data.unsalableApprover ? data.unsalableApprover.split(',') : []
                    data.hotSaleApprover = data.hotSaleApprover ? data.hotSaleApprover.split(',') : []
                    data.stockApprover = data.stockApprover ? data.stockApprover.split(',') : []
                    data.hotStockApprover = data.hotStockApprover ? data.hotStockApprover.split(',') : []
                    data.fittingsApprover = data.fittingsApprover ? data.fittingsApprover.split(',') : []
                    data.unsalableApproverName = data.unsalableApproverName ? data.unsalableApproverName.split(',') : []
                    data.hotSaleApproverName = data.hotSaleApproverName ? data.hotSaleApproverName.split(',') : []
                    data.stockApproverName = data.stockApproverName ? data.stockApproverName.split(',') : []
                    data.hotStockApproverName = data.hotStockApproverName ? data.hotStockApproverName.split(',') : []
                    data.fittingsApproverName = data.fittingsApproverName ? data.fittingsApproverName.split(',') : []
                    this.ruleForm = data ? data : this.ruleForm
                    this.isVisible = true
                    this.loading = false
                })
            } else {
                this.isVisible = true
                this.loading = false
            }
        },
        chooseWms(e, val) {
            if (val == 1) {
                this.ruleForm.mainWmsName = e ? e.name : ''
            } else if (val == 2) {
                this.ruleForm.unsalableWmsName = e ? e.name : ''
            } else if (val == 3) {
                this.ruleForm.stockWmsName = e ? e.name : ''
            }
        },
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        submitForm(formName) {
            console.log(this.ruleForm, 'this.ruleForm');
            // return
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const params = JSON.parse(JSON.stringify(this.ruleForm))
                    console.log(this.ruleForm, 'this.ruleForm');
                    params.whitelistBrands = params.brandIds ? params.brandIds.join(',') : ''
                    params.allotTimes = params.allotTimesRange ? params.allotTimesRange.join(',') : ''
                    params.whitelistStyleCodes = params.whitelistStyleCodesStr ? params.whitelistStyleCodesStr.replace(/[\uff0c,\s,\n]/g, ',') : ''
                    params.whitelistGoodsCodes = params.whitelistGoodsCodesStr ? params.whitelistGoodsCodesStr.replace(/[\uff0c,\s,\n]/g, ',') : ''
                    params.unsalableApprover = params.unsalableApprover ? params.unsalableApprover.join(',') : ''
                    params.hotSaleApprover = params.hotSaleApprover ? params.hotSaleApprover.join(',') : ''
                    params.stockApprover = params.stockApprover ? params.stockApprover.join(',') : ''
                    params.hotStockApprover = params.hotStockApprover ? params.hotStockApprover.join(',') : ''
                    params.fittingsApprover = params.fittingsApprover ? params.fittingsApprover.join(',') : ''
                    params.unsalableApproverName = params.unsalableApproverName ? params.unsalableApproverName.join(',') : ''
                    params.hotSaleApproverName = params.hotSaleApproverName ? params.hotSaleApproverName.join(',') : ''
                    params.stockApproverName = params.stockApproverName ? params.stockApproverName.join(',') : ''
                    params.hotStockApproverName = params.hotStockApproverName ? params.hotStockApproverName.join(',') : ''
                    params.fittingsApproverName = params.fittingsApproverName ? params.fittingsApproverName.join(',') : ''
                    const { success } = await request.post(`${this.api}Setting`, params)
                    if (success) {
                        this.$message.success('设置成功')
                        this.$emit('close')
                    }
                } else {
                    return false;
                }
            });
        }
    }
}
</script>

<style scoped lang="scss">
.publicCss {
    width: 200px;
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

::v-deep .el-input__inner {
    text-align: left;
}
</style>