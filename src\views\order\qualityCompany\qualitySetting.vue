<template>
 <container v-loading="pageLoading">
     <template #header>
      
     </template>

     
    <el-tabs v-model="tabindex" :tab-position="tabPosition" style="height: 100%;" @tab-click="onSearch">
        <el-row>
         <el-button-group>
             <el-button style="padding:0;margin:0;">

                 <el-input v-model.trim="filter.qualityCheckCompanyName" placeholder="质检报告公司" style="width: 160px" :maxlength="100" clearable></el-input>

             </el-button>
             <el-button type="primary" @click="onSearch">查询</el-button>

         

         </el-button-group>
         <el-button type="primary" style="margin-left: 10px" @click="addnew">新增</el-button>
         <!-- <el-button type="primary" @click="onSearch">刷新</el-button> -->
       </el-row>
        <el-tab-pane label="质检报告公司" style="height: 100%;" >
            <div id="YunHanAdminGoods20231018" style="width: 100%; height: 92%">
                <yhVxetable :id="'qualitySetting202408041827'" ref="tabletwowee"  :resizable="true" :somerow="'dateStr'" :hasSeq='false' :xgt="-1" :ygt="-1" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
                :tableData='tableData' :tableCols='tableCols' :border="true" :cellClassName="cellClassName"
                :isSelectColumn="false" :loading="listLoading">
                </yhVxetable>
            </div>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="zerogetList" />
        </el-tab-pane>
    </el-tabs>

    <el-dialog :title="isedit==1?'新增':'编辑'" :visible.sync="dialogEdit" width="600px" v-dialogDrag>
         <span>
             <el-form ref="refform" :model="editForm" label-width="100px" max-height="600">
                 <el-form-item label="请输入名称:" :rules="[{ required: true, message: '请输入名称', trigger: 'blur' }]" prop="qualityCheckCompanyName">
                  <el-input v-model.trim="editForm.qualityCheckCompanyName" placeholder="请输入名称" style="width: 160px" :maxlength="100" clearable></el-input>
                 </el-form-item>
             </el-form>
         </span>

         <span slot="footer" class="dialog-footer">
             <el-button type="primary" @click="saveSetting"  v-throttle="3000">保存</el-button>
             <el-button @click="dialogEdit = false">关闭</el-button>
         </span>
     </el-dialog>
     
     
     

 </container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from "@/components/my-container";
import yhVxetable from "@/components/VxeTable/yh_vxetable.vue";
import exportExecl from "@/utils/exportExecl.js";

import { formatLinkProCode, platformlist } from "@/utils/tools";
// import { getOrderFoodMenuList, exportOrderMenuManageAsync,exportOrderMenuGroupAsync,exportOrderMenuAsync, getOrderFoodMenuProvier, getOrderFoodMenuStatisList, getAreaSetList   } from '@/api/profit/orderfood';

import {
  getQualityCheckCompanyList,
  saveQualityCheckCompany,
  delQualityCheckCompanyById,
} from "@/api/operatemanage/qualityCompany.js";

const tableCols = [
  { istrue: true, prop: "qualityCheckCompanyName", label: "公司名称", width: "auto" },
  {
    istrue: true,
    type: "button",
    label: "操作",
    width: "200",
    btnList: [
      { label: "编辑", handle: (that, row) => that.editBaseMenu(row) },
      { label: "删除", type: "danger", handle: (that, row) => that.delBaseMenu(row, 1) },
    ],
  },
];

const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
  name: "Users",
  components: { container, yhVxetable },
  data() {
    return {
      that: this,
      tabindex: 0,
      // pagertwo: { OrderBy: "createdTime", IsAsc: true },
      isedit: 1, //1新增 2编辑
      tabPosition: "left",
      editForm: { qualityCheckCompanyName: "" },
      daochushow: false,
      dialogEdit: false,
      // tableshow: true,
      gysList: [],
      pickerOptions: {
        disabledDate(time) {
          const currentDate = new Date();
          const currentTime = currentDate.getTime();
          return time.getTime() > currentTime;
        },
      },
      filter: {
       qualityCheckCompanyName: "",
      },
      platformlist: platformlist,
      tableCols: tableCols,
      // tableCols1: [],

      tableHandles: null,
      // tableData: [],
      total: 0,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      sels: [],
      quyuList: [],
      allAlign: "center",
      tableData: [{}],
      tableData1: [],
      mergeCells: [
        { row: 1, col: 1, rowspan: 3, colspan: 3 },
        { row: 5, col: 0, rowspan: 2, colspan: 2 },
      ],
    };
  },
  mounted() {
    this.onSearch();
  },
  methods: {
    zerogetList(){
      this.filter.currentPage = 1;
      this.getList();
    },
    async saveSetting() {
      let isValid = false;
      this.$refs.refform.validate((valid) => {
        isValid = valid;
      });
      if (!isValid) {
        return;
      }
      if (this.isedit == 1) {
        //保存新增
        let par = { ...this.editForm };
        const res = await saveQualityCheckCompany(par);
        if (!res.success) {
         return
        }
        this.$message({ type: "success", message: "保存成功!" });
        await this.getList();
        this.dialogEdit = false;
      } else if (this.isedit == 2) {
        //保存编辑
        let par = { ...this.editForm };
        const res = await saveQualityCheckCompany(par);
        if (!res.success) {
         return
        }
        this.$message({ type: "success", message: "编辑成功!" });
        await this.getList();
        this.dialogEdit = false;
        
      }
    },
    addnew() {
      this.isedit = 1; //新增
      this.editForm = { qualityCheckCompanyName: "" };
      this.$nextTick(() => {
        this.dialogEdit = true;
      });
    },
    editBaseMenu(row) {
      this.isedit = 2; //编辑
      this.editForm = {
        qualityCheckCompanyName: row.qualityCheckCompanyName,
        id: row.id,
      };
      this.$nextTick(() => {
        this.dialogEdit = true;
      });
    },
    async delBaseMenu(row, index) {
      this.$confirm("确定删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async (_) => {
          let par = {
            id: row.id,
          };
          const res = await delQualityCheckCompanyById(par);
          if (!res?.success) {
           return
          }
            this.$message({ type: "success", message: "删除成功!" });
            await this.getList();
          
        })
        .catch((_) => {});
    },
    cellClassName({ row, column }) {
      return null;
    },
    async onSearch() {
      await this.getList();
    },
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      await this.onSearch();
    },
    async getList() {
      var that = this;
      this.listLoading = true;
      this.pageLoading = true;
  

      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };

      const res = await getQualityCheckCompanyList(params);
      if(!res?.success){
       return
      }
      this.tableData = res.data.list;
      this.total = res.data.total;

      this.pageLoading = false;
      this.listLoading = false;
    },
    removeDuplicatesByName(array, name) {
      return array.filter((item, index) => {
        return array.findIndex((obj) => obj[name] === item[name]) === index;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
  color: blue;
}
// ::v-deep .vxe-table--body-wrapper{
//     overflow-x: hidden;
// }
.vxetable202212161323 {
  border: none !important;
}
// ::v-deep .vxe-table--body-wrapper{
//     display: none;
// }
</style>

