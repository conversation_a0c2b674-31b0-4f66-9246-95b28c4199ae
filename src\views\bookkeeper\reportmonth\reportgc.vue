<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
            <el-button-group>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
                        <el-option label="工资月报" value="v1"></el-option>
                        <el-option label="参考月报" value="v2"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.proCode" placeholder="产品ID" style="width:120px;" />
                </el-button> -->
                <el-button style="padding: 0;margin: 0;">
                    <inputYunhan ref="productCode2" :inputt.sync="filter.proCode" v-model="filter.proCode"
                        class="publicCss" placeholder="产品ID/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="proCodeBack" title="产品ID">
                    </inputYunhan>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.ProductName" placeholder="产品名称" style="width:160px;" />
                </el-button>
                <!-- <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.platform" placeholder="平台" style="width:120px;">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button> -->
                <el-button style="padding: 0;">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                        style="width: 90px">
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable clearable v-model="filter.isPinPai" placeholder="是否品牌" style="width:120px;">
                        <el-option label="是" :value=1></el-option>
                        <el-option label="否" :value=0></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.pinPai" placeholder="品牌" style="width:120px;" />
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" :loading="exportloading">导出</el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' @summaryClick='onsummaryClick' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='financialreportlist' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <vxe-modal title="明细" v-model="calcdetails.visible" width="80%" v-dialogDrag>
            <calcdetails ref="calcdetails" style="height:600px;"></calcdetails>
        </vxe-modal>

        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getFinancialReportList } from '@/api/financial/yyfy'
import { exportFinancialReport } from '@/api/monthbookkeeper/financialreport'
import { formatWarehouse, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import calcdetails from "@/views/bookkeeper/reportmonth/calcdetails";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
import inputYunhan from "@/components/Comm/inputYunhan";
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'proCode', fix: true, label: '产品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, fixed: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName },
     { istrue: true, fixed: true, prop: 'operateSpecialUserName', label: '专员', sortable: 'custom', width: '70', formatter: (row) => row.operateSpecialUserName },
    { istrue: true, fixed: true, prop: 'assistantUserName', label: '助理', sortable: 'custom', width: '70', formatter: (row) => row.assistantUserName },
    {
        istrue: true, prop: '', label: `账单`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '65' },
            { istrue: true, prop: 'createdTime', label: '统计日期', sortable: 'custom', width: '150' },
            { istrue: true, prop: 'proCode', label: '产品名称', sortable: 'custom', width: '150', formatter: (row) => row.proName },
            { istrue: true, prop: 'goodsCodes', label: '商品编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '150', formatter: (row) => row.shopName },
            { istrue: true, prop: 'pinPai', label: '品牌', width: '80', sortable: 'custom' },
            { istrue: true, prop: 'orderCount', label: '订单数', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'count', label: 'ID数', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'amountSettlement', label: '结算收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountSettlement_1', label: '结算收入-1', sortable: 'custom', width: '95', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountSettlement_2', label: '2月之前月份收入', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthIn', label: '跨月收入', width: '80' },
            { istrue: true, prop: 'amountOut', label: '退款', sortable: 'custom', width: '70', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthOut', label: '跨月退款', sortable: 'custom', width: '80' },

            { istrue: true, prop: 'amountSettlementWx_1', label: '微信结算收入-1', sortable: 'custom', width: '95', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountSettlementWx_2', label: '微信2月之前月份收入', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthInWx', label: '微信跨月收入', width: '80' },
            { istrue: true, prop: 'amountOutWx', label: '微信退款', sortable: 'custom', width: '70', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCrossMonthOutWx', label: '微信跨月退款', sortable: 'custom', width: '80' },

            { istrue: true, prop: 'amountTaoKeNot', label: '淘客不计', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountShare', label: '参与公摊金额', sortable: 'custom', width: '110' },
            { istrue: true, prop: 'amountCost', label: '结算成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountCost_1', label: '结算成本_1', sortable: 'custom', width: '80' },

            { istrue: true, prop: 'pinPaiAmount1', label: '品牌费用1', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount1Avg', label: '品牌费用1分摊', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount2', label: '品牌费用2', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount2Avg', label: '品牌费用2分摊', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount1_yc', label: '异常品牌费用1', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount2_yc', label: '异常品牌费用2', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount3', label: '品牌费用3', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmount3Avg', label: '品牌费用3分摊', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },
            { istrue: true, prop: 'pinPaiAmountTotal', label: '品牌费用合计', sortable: 'custom', width: '80', permission: "monthpinpaifeeseepermission" },

            { istrue: true, prop: 'amountOutCost', label: '退款成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountEmptyId', label: '空白链接ID成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountReSendCost', label: '补发成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'amountExceptionCost', label: '异常成本', sortable: 'custom', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'agentCost', label: '代发成本差', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'cbcamont', label: '定制护墙角差额', width: '80', sortable: 'custom' },
            { istrue: true, prop: 'amontFreightfee', label: '采购运费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'xiaoTuiFanHuan', label: '销退仓返还', sortable: 'custom', width: '100' },
            { istrue: true, prop: 'saleProfit', label: '销售毛利', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'orderCount_FX', label: '分销订单数', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'count_FX', label: '分销ID数', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountSettlement_FX', label: '分销结算收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountCost_FX', label: '分销结算成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanTotalCost', label: '定制款成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanAvgCost', label: '定制款分摊成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'dingZhiKuanExceptionCost', label: '定制款异常成本', sortable: 'custom', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `账单费用`, merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'deposit', sortable: 'custom', label: '保证金', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'insurance', sortable: 'custom', label: '保险承保', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'villageServiceFee', sortable: 'custom', label: '村淘平台服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'withholdingPoints', sortable: 'custom', label: '代扣返点积分', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'withholdingReturned', sortable: 'custom', label: '代扣交易退回积分', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'publicDonation', sortable: 'custom', label: '公益捐款', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'hbServiceFee', sortable: 'custom', label: '花呗服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            //{ istrue: true, prop: 'enjoymentFee', sortable: 'custom', label: '品牌新享新品直降礼金软件服务费', width: '100', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'cashRed_Tx', sortable: 'custom', label: '淘宝现金红包', width: '110', type: 'click', handle: (that, row) => that.showCashRedTXDetail(row) },
            { istrue: true, prop: 'taobaoAllianceRefund', sortable: 'custom', label: '淘宝联盟推广佣金返还', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'tmallCommission', sortable: 'custom', label: '天猫佣金', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'creditCardServiceFee', sortable: 'custom', label: '信用卡服务费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'quickPaymentServiceFee', sortable: 'custom', label: '极速回款担保服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'enjoymentFirstPlan', sortable: 'custom', label: '品牌新享首单拉新计划', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            //{ istrue: true, prop: 'enjoymentFirstPlan11',  sortable: 'custom', label: '品牌新享双11激励', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'taobaoSpecial', sortable: 'custom', label: '淘宝特价版联合营销计划系统服务费', width: '100', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'yxkk', sortable: 'custom', label: '营销扣款', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'directJointPromotion', sortable: 'custom', label: '直营联营营促销', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'c2mReturn', sortable: 'custom', label: 'C2M退货包运费代扣', width: '95' },
            { istrue: true, prop: 'dayBuy', sortable: 'custom', label: '每日必买', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            //{ istrue: true, prop: 'enjoymentYJ',  sortable: 'custom', label: '品牌新享佣金扣费', width: '80', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'overseaCalc', sortable: 'custom', label: '海外零售计划', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'bbFarmSoftServiceFee', sortable: 'custom', label: '芭芭农场T62软件服务', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'waiveCommissionFee', sortable: 'custom', label: '减免单佣金扣费', width: '90' },
            //{ istrue: true, prop: 'brandNewProductSoft',  sortable: 'custom', label: '品牌新享新品孵化软件服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            //{ istrue: true, prop: 'brandNewManPlan',  sortable: 'custom', label: '品牌新享-智能拉新计划', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'daySepcialSaelSoft', sortable: 'custom', label: '新天天特卖软件服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'tbNewManGiftTechnology', sortable: 'custom', label: '淘宝新客礼金技术服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'tmAbroadBackInsure', sortable: 'custom', label: '天猫海外退货险保费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'ttGearCommissionDeduct', sortable: 'custom', label: '淘特拍档佣金扣费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'settlementCpsDeduct', sortable: 'custom', label: '结算价CPS扣费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'tbFreeCommissionDeduct', sortable: 'custom', label: '淘免单佣金扣费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },

            //{ istrue: true, prop: 'suprPlusServiceFee',  sortable: 'custom', label: '品牌新享-超级流量加速软件服务费', width: '100', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'billionSubsidyServiceFee', sortable: 'custom', label: '百亿补贴软件服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'typeNewCusServeceFee', sortable: 'custom', label: '品类新客礼金软件服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'advanceBackRepairFee', sortable: 'custom', label: '垫资退款补缴费用', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            //{ istrue: true, prop: 'timedPlusServiceFee',  sortable: 'custom', label: '品牌新享-限时加速服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'costEffectiveCommission', sortable: 'custom', label: '聚划算佣金', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },

            { istrue: true, prop: 'kuaJingTrustTechnology', sortable: 'custom', label: '跨境托管技术服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'kuaJingPopularizeService', sortable: 'custom', label: '跨境供货平台推广服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'saleAfterPay', sortable: 'custom', label: '售后支付', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'taoBaoPopularizeService', sortable: 'custom', label: '淘宝内容推广服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'baBaNongChangCpsCommission', sortable: 'custom', label: '芭芭农场供应商cps佣金', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'byOrderService', sortable: 'custom', label: '按订单收费服务费', width: '90' },

            { istrue: true, prop: 'techServiceFeeBefor2', sortable: 'custom', label: '先用后付技术服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'spanSellerSingleBuyGive', sortable: 'custom', label: '跨商单品买赠收费', width: '90' },
            { istrue: true, prop: 'buyGiveGiftMarket', sortable: 'custom', label: '买赠赠品营销费用', width: '90' },
            { istrue: true, prop: 'sellerConcentrateTransfer', sortable: 'custom', label: '商家集运中转操作费', width: '90' },
            { istrue: true, prop: 'sellerConcentrateLogistics', sortable: 'custom', label: '商家集运物流服务费', width: '90' },
            { istrue: true, prop: 'billionSubsidyFreightInsure', sortable: 'custom', label: '百亿补贴运费险保费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },

            { istrue: true, prop: 'otherDeDuctionFee', sortable: 'custom', label: '其他账单', width: '90' },

            { istrue: true, prop: 'jiChuRuanJianFuWuFei', sortable: 'custom', label: '基础软件服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'tiShengJiHuaFuWuFei', sortable: 'custom', label: '消费者体验提升计划服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'baiYiDingXiangFeiYong', sortable: 'custom', label: '百亿补贴定向营销费用', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'daCuRuanJianFuWuFei', sortable: 'custom', label: '淘宝大促软件技术服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'lianMengYongJinDaiKou', sortable: 'custom', label: '淘宝联盟佣金代扣', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },

            { istrue: true, prop: 'nnProGrowCpsFee', sortable: 'custom', label: 'NN商品成长CPS费用', width: '90' },
            { istrue: true, prop: 'backCompensate', sortable: 'custom', label: '退款赔付', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'redSignSupplierCpsCommission', sortable: 'custom', label: '红包签到供应商cps佣金', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'gfjjSoftwareServiceFee', sortable: 'custom', label: '官方竞价软件服务费', width: '90', type: 'click', handle: (that, row, column) => that.showcalcdetails(row, column.prop) },
            { istrue: true, prop: 'tbNewGiftServiceFee', sortable: 'custom', label: '淘宝新品礼金技术服务费', width: '90' },
            { istrue: true, prop: 'merchantInviolableRed', sortable: 'custom', label: '商家权益红包', width: '90' },
            { istrue: true, prop: 'c2MKfServiceFee', sortable: 'custom', label: 'C2M客服服务费', width: '90' },
            { istrue: true, prop: 'xinJiangWuLiuJiYun', sortable: 'custom', label: '新疆物流集运', width: '90' },
            { istrue: true, prop: 'c2MTechServiceFee', sortable: 'custom', label: 'C2M-技术服务费', width: '90' },
            { istrue: true, prop: 'selfUndertakePaid', sortable: 'custom', label: '自承接赔付', width: '90' },
            { istrue: true, prop: 'appealReturn', sortable: 'custom', label: '申诉返还', width: '90' },
            { istrue: true, prop: 'productValuePaid', sortable: 'custom', label: '货值赔付', width: '90' },
            { istrue: true, prop: 'priceProtectReturn', sortable: 'custom', label: '价保货款返还', width: '90' },
            { istrue: true, prop: 'priceProtectDuct', sortable: 'custom', label: '价保扣款', width: '90' },
            { istrue: true, prop: 'c2MMainTVServerFee', sortable: 'custom', label: 'C2M官方直播推广服务费', width: '90' },
            { istrue: true, prop: 'c2MTgcActivityRefund', sortable: 'custom', label: 'C2M淘工厂活动返款', width: '90' },
            { istrue: true, prop: 'cooperationExpenses', sortable: 'custom', label: '淘工厂补贴', width: '90' },
            { istrue: true, prop: 'cooperationFee', sortable: 'custom', label: '合作费用扣费', width: '90' },
            { istrue: true, prop: 'crossAddValueServerFee', sortable: 'custom', label: '跨境增值服务费', width: '90' },

            { istrue: true, prop: 'membershipPurchaseCommission', sortable: 'custom', label: '会员购佣金', width: '90' },
            { istrue: true, prop: 'taoTeMarketingHosting', sortable: 'custom', label: '淘特营销托管', width: '90' },
            { istrue: true, prop: 'c2MBusinessPenalty', sortable: 'custom', label: 'C2M-商家处罚', width: '90' },
            { istrue: true, prop: 'evaluationGiftServiceFee', sortable: 'custom', label: '评价有礼服务费', width: '90' },

            { istrue: true, prop: 'hzAmount_wx', sortable: 'custom', label: '微信-淘工厂补贴', width: '90' },
            { istrue: true, prop: 'yxAmount_wx', sortable: 'custom', label: '微信-营销费用', width: '90' },
         //   { istrue: true, prop: 'yjAmount_wx', sortable: 'custom', label: '微信-佣金', width: '90' },
            { istrue: true, prop: 'jyyjAmount_wx', sortable: 'custom', label: '微信-交易佣金', width: '90' },
            { istrue: true, prop: 'avgFee', sortable: 'custom', label: '分摊费用', width: '80' },
            { istrue: true, prop: 'dkTotalAmont', sortable: 'custom', label: '总费用', width: '80' }
        ]
    },
    {
        istrue: true, prop: '', label: `订单费用合计`, merge: true, prop: 'mergeField2',
        cols: [
            { istrue: true, prop: 'freightFee', label: '快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'freightFeeAvg', label: '快递费均摊', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'withholdfee', label: '快递违规扣款', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFee', label: '包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFeeAuct', label: '真实包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFeePredict', label: '预估包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'packageFeeShare', label: '分摊包装费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'totalOrderCost', label: '订单费用合计', sortable: 'custom', width: '80' },

            { istrue: true, prop: 'wcOrderCount', label: '外仓订单量', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcMinExpressFee', label: '外仓最低快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcActuExpressFee', label: '外仓快递费-实际快递费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcOrderCountYunYing', label: '外仓订单量(运)', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcOrderRateYunYing', label: '外仓率(运)', sortable: 'custom', width: '80', formatter: (row) => row.wcOrderRateYunYing + '%' },
            { istrue: true, prop: 'wcJunExpressFeeYunYing', label: '外仓快递费(运)', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'wcPackageFee', label: '外仓包装费(运)', sortable: 'custom', width: '80' },
        ]
    },
    {
        istrue: true, prop: '', label: `运营费用`, merge: true, prop: 'mergeField3',
        cols: [
            { istrue: true, prop: 'cuishou', sortable: 'custom', label: '催收', width: '80' },
            { istrue: true, prop: 'tesudanyongjin', sortable: 'custom', label: '特殊单费用', width: '70' },
            { istrue: true, prop: 'shizhitou', sortable: 'custom', label: '一站式智投', width: '80' },
            { istrue: true, prop: 'zhitongcheamont', sortable: 'custom', label: '直通车', width: '80' },
            { istrue: true, prop: 'tuijian', sortable: 'custom', label: '直播短视频', width: '80' },
            { istrue: true, prop: 'taobaoke', sortable: 'custom', label: '淘宝客', width: '80' },
            { istrue: true, prop: 'wanxiangtai', sortable: 'custom', label: '无界', width: '80' },
            { istrue: true, prop: 'shangpintuoguanyugu', sortable: 'custom', label: '商品托管预估', width: '80' },
            { istrue: true, prop: 'taolijing', sortable: 'custom', label: '淘礼金', width: '80' },
            { istrue: true, prop: 'tesudanyongjin', sortable: 'custom', label: '特殊单佣金', width: '70' },
            { istrue: true, prop: 'tesudanben', sortable: 'custom', label: '特殊单成本', width: '70' },
            { istrue: true, prop: 'taotetuiguang', sortable: 'custom', label: '淘特推广', width: '80' },

            { istrue: true, prop: 'pinPaiXinXiang', sortable: 'custom', label: '品牌新享', width: '90' },
            { istrue: true, prop: 'deductionHand', sortable: 'custom', label: '手淘直播佣金扣费', width: '90' },
            { istrue: true, prop: 'taoCustomDeductCommission', sortable: 'custom', label: '淘客扣佣', width: '90' },
            { istrue: true, prop: 'sellerDepositPromotionService', sortable: 'custom', label: '投流推广佣金', width: '90' },
            { istrue: true, prop: 'sellerMoneySingleSubSidy', sortable: 'custom', label: '商家出资单品补贴', width: '90' },
            { istrue: true, prop: 'merchantSubsidy', sortable: 'custom', label: '商家出资补贴-货款', width: '90' },
            { istrue: true, prop: 'merchantSubsidy_sepcial', sortable: 'custom', label: '商家出资补贴-特殊单', width: '90' },
            { istrue: true, prop: 'proPromCommission', sortable: 'custom', label: '微信-投流推广佣金', width: '90' },
            { istrue: true, prop: 'tkPromCommission', sortable: 'custom', label: '微信-淘客推广佣金', width: '90' },
            { istrue: true, prop: 'zbPromCommission', sortable: 'custom', label: '微信-直播推广佣金', width: '90' },

            { istrue: true, prop: 'advFee', sortable: 'custom', label: '运营费合计', width: '80' },
            { istrue: true, prop: 'advRate', sortable: 'custom', label: '广告费占比', width: '80', formatter: (row) => { return row.advRate?.toFixed(2) + "%" } },
        ]
    },
    { istrue: true, fixed: false, prop: 'cbcamont', fix: true, label: '护墙角成本差', width: '80', sortable: 'custom', children: [] },
    {
        istrue: true, prop: '', label: `产品费用`, merge: true, prop: 'mergeField4', tipmesg: '可在"财务管理/月报/产品费用"中查看明细',
        cols: [
            { istrue: true, prop: 'amontPick', label: '产品运费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amontSampleBX', label: '样品费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amontSampleGrop', label: '运营拿样', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'amontSampleMG', label: '美工拿样', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'amontShoot', label: '美工拍摄费用', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'amontFreightfee', label: '采购运费', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'totalProductCost', sortable: 'custom', label: '产品费用合计', width: '95' }
        ]
    },
    {
        istrue: true, prop: '', label: `工资`, merge: true, prop: 'mergeField5', tipmesg: '可在"财务管理/月报/工资费用"中查看明细',
        cols: [
            { istrue: true, prop: 'amontCommissionMG', label: '美工提成', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amontCommissionXMT', label: '新媒体提成', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'amontCommissionCG', label: '采购提成', sortable: 'custom', width: '90' },
            { istrue: true, prop: 'amontMachineGZ', label: '加工工资', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amontWagesGroup', label: '运营工资', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'totalWagesCost', label: '工资合计', sortable: 'custom', width: '80' }
        ]
    },
    { istrue: true, prop: 'amontOffLinefee', label: '运营下架', sortable: 'custom', width: '90', tipmesg: '可在"财务管理/月报/产品费用"中查看明细' },
    { istrue: true, prop: 'amontStoreLossfee', label: '仓储损耗', sortable: 'custom', width: '90', tipmesg: '可在"财务管理/月报/产品费用"中查看明细' },
    { istrue: true, prop: 'grossProfit', label: '产品利润', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'chuCangYg', label: '预估出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangZs', label: '真实出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCang', label: '出仓成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'chuCangYunYing', label: '出仓成本（运）', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit4', label: '毛四', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wareWages', label: '仓库薪资', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'wareWagesYunYing', label: '仓库薪资（运）', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit5', label: '毛五', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'profit6', label: '毛六', sortable: 'custom', width: '120' },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, calcdetails, buschar,inputYunhan },
    data() {
        return {
            that: this,
            filter: {
                proCode: null,
                platform: 8,
                yearMonth: null,
                shopCode: null,
                groupId: null,
                productName: null,
                version: "v1"
            },
            platformlist: platformlist,
            shopList: [],
            userList: [],
            grouplist: [],
            financialreportlist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: " AmountSettlement ", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            dialogVisibleSyj: false,
            calcdetails: { visible: false },
            fileList: [],
            exportloading: false,
            analysisFilter: {
                searchName: "View_FinancialMonthReport_GC",
                extype: 5,
                selectColumn: "Count",
                filterTime: "YearMonth",
                isYearMonthDay: false,
                filter: null,
                columnList: [{ columnNameCN: '订单数', columnNameEN: 'Count' }]
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },
    async mounted() {
        //await this.onSearch()
        await this.getShopList();
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [8] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            let res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
            // loading.close();
        },
        async getList() {
            let that = this;
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };
            startLoading();
            const res = await getFinancialReportList(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.financialreportlist = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.exportloading = true;
            let res = await exportFinancialReport(params);
            this.exportloading = false;
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '财务账单数据' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async showcalcdetails(row, column) {
            this.calcdetails.visible = true;
            console.log('this.$refs', this.$refs)
            console.log('this.$refs.calcdetails;', this.$refs.calcdetails)
            let calc = this.$refs.calcdetails;
            console.log('calcdetails1', calc)
            let that = this;
            await this.$nextTick(async () => {
                console.log('calcdetails2', calc)
                await that.$refs.calcdetails.onSearch(column, this.filter.version, row.shopCode, row.yearMonth, row.proCode)
            });
        },
        async onsummaryClick(property) {
            let that = this;
            this.analysisFilter.filter = {
                proCode: [that.filter.proCode, 0],
                //platform: [that.filter.platform, 0],
                yearMonth: [that.filter.yearMonth, 0],
                shopCode: [that.filter.shopCode, 0],
                groupId: [that.filter.groupId, 0],
                productName: [that.filter.productName, 5],
                version: [that.filter.version, 0],
            };
            this.analysisFilter.selectColumn = property;
            let cn = "金额";
            if (property == "orderCount" || property == "count") {
                cn = "数量";
            }
            this.analysisFilter.columnList = [{ columnNameCN: cn, columnNameEN: property }];

            const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        },
        proCodeBack(val) {
            let that = this;
            that.filter.proCode = val;
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
