<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-switch v-model="ListInfo.switch" active-text="售后" inactive-text="售前" @change="changeSwitch">
                </el-switch>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 600px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dateRange from "@/components/date-range/index.vue";
import { getCustomerService } from '@/api/operatemanage/base/product'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupname', label: '分组', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'groupManager', label: '组长', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sname', label: '姓名', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'snick', label: '昵称', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        customerInfo: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                groupType: 0,
                switch: false
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        changeSwitch() {
            this.ListInfo.groupType = this.ListInfo.switch ? 1 : 0
            this.getList('search')
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getCustomerService({ ...this.ListInfo, ...this.customerInfo })
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
