<template>
  <MyContainer>
    <vxetablebase :id="'productldModule202408041523'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title='店铺名称' :visible.sync="shopnamepopup" width="70%" v-dialogDrag append-to-body>
      <div style="height: 400px;">
        <shopNameModule ref="shopNameModule" v-if="shopnamepopup" />
      </div>
    </el-dialog>

    <el-dialog title='打款类型' :visible.sync="typepaymentpopup" width="50%" v-dialogDrag append-to-body>
      <div style="height: 400px;">
        <remitTypeModule ref="remitTypeModule" v-if="typepaymentpopup" />
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageGetChangeLogs } from "@/api/inventory/basicgoods"
import shopNameModule from "./shopNameModule.vue"
import remitTypeModule from "./remitTypeModule.vue"
import { getPettyPaymentShop_DYPageList } from '@/api/customerservice/douyinrefund'
const tableCols = [
  { width: 'auto', align: 'center', prop: 'shopName', label: '店铺名称', type: 'click', handle: (that, row) => that.onShopname(row), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remitAmount', label: '打款金额(元)', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remitAmountRate', label: '金额占比', formatter: (row) => row.remitAmountRate ? (row.remitAmountRate * 100).toFixed(2) + '%' : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCount', label: '单量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderCountRate', label: '单量占比', formatter: (row) => row.orderCountRate ? (row.orderCountRate * 100).toFixed(2) + '%' : '', },
  { width: 'auto', align: 'center', prop: 'remitTypeCount', label: '打款类型', type: 'click', handle: (that, row) => that.onTypepayment(row), },
]
export default {
  name: "productIdModule",
  components: {
    MyContainer, vxetablebase, shopNameModule, remitTypeModule
  },
  data() {
    return {
      typepaymentpopup: false,
      shopnamepopup: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {

  },
  methods: {
    onTypepayment(row) {
      const params = { ...this.ListInfo, ...row }
      this.typepaymentpopup = true
      this.$nextTick(() => {
        this.$refs.remitTypeModule.getList('search', params)
      })
    },
    onShopname(row) {
      const params = { ...this.ListInfo, ...row }
      this.shopnamepopup = true
      this.$nextTick(() => {
        this.$refs.shopNameModule.getList('search', params)
      })
    },
    async getList(type, params) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.ListInfo = Object.assign(this.ListInfo, params)
      console.log(this.ListInfo, 'this.ListInfo');
      const theChildparams = { ...this.ListInfo, startDate: this.ListInfo.startDate, endDate: this.ListInfo.endDate, proCode: this.ListInfo.proCode, shopCode: this.ListInfo.shopCode }
      this.loading = true
      const { data, success } = await getPettyPaymentShop_DYPageList(theChildparams)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        //获取列表失败
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
