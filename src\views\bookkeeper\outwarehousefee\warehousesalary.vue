<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>

                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 280px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-button>
             
                <!-- <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.shopName" style="width: 240px" size="mini" clearable>
                        <el-option v-for="item in myWarehouseList" :key="item.label" :label="item.label"
                            :value="item.label" />
                    </el-select>
                </el-button> -->
                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.shopName" placeholder="仓库" style="width:160px;" clearable
                        maxlength="40" />
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <!-- <el-button type="primary" @click="onSearch">计算出仓费</el-button> -->
            </el-button-group>

        </template>

        <vxetablebase :id="'warehousesalary20241115'" :border="true" :align="'center'"
            :tablekey="'warehousesalary20241115'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0"   :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

      

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist, formatPlatform, formatTime, pickerOptions,warehouselist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import inputYunhan from "@/components/Comm/inputYunhan";


import { GetyWarehouseSalaryPageList} from '@/api/bookkeeper/outwarehousefee'

const tableCols = [
    { istrue: true, prop: 'warehouseName', label: '仓库', sortable: 'custom', width: '260', formatter: (row) => row.warehouseName },
    { istrue: true, prop: 'calculateTime', label: '操作时间', sortable: 'custom', width: '150', formatter: (row) => formatTime(row.calculateTime,"YYYY-MM-DD")  },
    { istrue: true, prop: 'addtime', label: '同步时间', sortable: 'custom', width: '150'  },
    { istrue: true, prop: 'totalSalary', label: '薪资', sortable: 'custom', width: '80' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: "WarehouseSalary",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
        buschar, vxetablebase,inputYunhan
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                feeType: "拣货_订单数",
                orderNoInners:null,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            fileList: [],
            fileparm: {},

            importFeeType: "",
            myWarehouseList: warehouselist,

            dialogUploadData: {
                isSuccess: true,
                title: "",
                visible: false,
                uploadLoading: false,
                importWarehouseCode: 0,
                importFeeType: "",
            },
        };
    },
    async mounted() {
        //await this.onSearch();
    },
    async created() {
    },
    methods: {
       
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList() {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.daterange) {
                this.filter.startTime = this.filter.daterange[0];
                this.filter.endTime = this.filter.daterange[1];
            }
            else {
                this.$message({ type: 'error', message: '请输入日期!' });
                return;
            }
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.listLoading = true;
            const res = await GetyWarehouseSalaryPageList(params);
            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            this.listLoading = false;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        async onImport(feeType) {
            this.dialogUploadData.importWarehouseCode = this.filter.warehouseCode;
            this.dialogUploadData.importFeeType = feeType;
            this.dialogUploadData.title = "导入" + feeType;
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("dataType", this.dialogUploadData.importFeeType);
            form.append("warehouseCode", this.dialogUploadData.importWarehouseCode.toString());
            form.append("warehouseName", this.myWarehouseList.find(f => f.value == this.dialogUploadData.importWarehouseCode)?.label);
            console.log(form, 'form')
            var res = await ImportOutWarehouseFee(form);
            if (res?.success) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            }
            this.dialogUploadData.uploadLoading = false;
            this.dialogUploadData.isSuccess = res?.success;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.isSuccess)
                this.dialogUploadData.visible = false;
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            if (!this.dialogUploadData.importWarehouseCode) {
                this.$message({ message: "请先选择仓库", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        orderNoInnerBack(val) {
            this.filter.orderNoInners = val;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
