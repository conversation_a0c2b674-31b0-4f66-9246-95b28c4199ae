<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
        <el-form-item label="南昌" prop="nc">
          <inputNumberYh v-model="ruleForm.nc" :placeholder="'请输入'" class="publicCss" v-if="!isText" />
          <el-input v-model.trim="ruleForm.nc" placeholder="请输入" maxlength="50" clearable class="publicCss" v-else />
        </el-form-item>
        <el-form-item label="义乌" prop="yw">
          <inputNumberYh v-model="ruleForm.yw" :placeholder="'请输入'" class="publicCss" v-if="!isText" />
          <el-input v-model.trim="ruleForm.yw" placeholder="请输入" maxlength="50" clearable class="publicCss" v-else />
        </el-form-item>
        <el-form-item label="武汉" prop="wh">
          <inputNumberYh v-model="ruleForm.wh" :placeholder="'请输入'" class="publicCss" v-if="!isText" />
          <el-input v-model.trim="ruleForm.wh" placeholder="请输入" maxlength="50" clearable class="publicCss" v-else />
        </el-form-item>
        <el-form-item label="深圳" prop="sz">
          <inputNumberYh v-model="ruleForm.sz" :placeholder="'请输入'" class="publicCss" v-if="!isText" />
          <el-input v-model.trim="ruleForm.sz" placeholder="请输入" maxlength="50" clearable class="publicCss" v-else />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { regionalMonitoringSelfInspectionSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      isText: false,
      ruleForm: {
        nc: '',
        yw: '',
        wh: '',
        sz: '',
      },
      rules: {
        nc: [
          { required: true, message: '请输入南昌', trigger: 'blur' },
        ],
        wh: [
          { required: true, message: '请输入武汉', trigger: 'blur' },
        ],
        yw: [
          { required: true, message: '请输入义乌', trigger: 'blur' },
        ],
        sz: [
          { required: true, message: '请输入深圳', trigger: 'blur' },
        ],
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
    if (this.editInfo.type == '关键成果（说明）') {
      this.isText = true;
    } else {
      this.isText = false;
    }
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await regionalMonitoringSelfInspectionSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}
</style>
