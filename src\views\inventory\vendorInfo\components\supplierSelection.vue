<template>
  <MyContainer>
    <template #header>
      <div class="top" style="display: flex; flex-direction: row; align-items: center; flex-wrap: wrap;">
        <!-- v-show="selectedTitles.includes('供应商名称')" -->
        <el-input v-model.trim="ListInfo.nameManufacturer" v-show="selectedTitles.includes('供应商名称')" placeholder="供应商名称"
          maxlength="50" clearable class="publicCss" />
        <el-select v-show="selectedTitles.includes('类目')" v-model="ListInfo.catroyType" placeholder="类目" clearable
          filterable class="publicCss">
          <el-option v-for="item in categoryList" :key="item" :label="item" :value="item" />
        </el-select>
        <div class="publicCss" v-show="selectedTitles.includes('商品名称')" style="width: 165px;">
          <inputYunhan ref="productproductName" :inputt.sync="ListInfo.productName" v-model="ListInfo.productName"
            width="165px" placeholder="商品名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'productName')" title="商品名称">
          </inputYunhan>
        </div>
        <div class="publicCss" v-show="selectedTitles.includes('商品编号')" style="width: 165px;">
          <inputYunhan ref="productgoodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
            width="165px" placeholder="商品编号/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsCode')" title="商品编号">
          </inputYunhan>
        </div>
        <div class="publicCss" v-show="selectedTitles.includes('公司商品编码')" style="width: 165px;">
          <inputYunhan ref="productyhGoodsCode" :inputt.sync="ListInfo.yhGoodsCode" v-model="ListInfo.yhGoodsCode"
            width="165px" placeholder="公司商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'yhGoodsCode')" title="公司商品编码">
          </inputYunhan>
        </div>
        <div class="publicCss" v-show="selectedTitles.includes('款式名称')" style="width: 165px;">
          <inputYunhan ref="productstyleCode" :inputt.sync="ListInfo.styleCode" v-model="ListInfo.styleCode"
            width="165px" placeholder="款式名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'styleCode')" title="款式名称">
          </inputYunhan>
        </div>
        <el-input v-show="selectedTitles.includes('供应商商品编码')" v-model.trim="ListInfo.gysGoodsCode" placeholder="供应商商品编码"
          maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('供应商1688商品链接')" v-model.trim="ListInfo.oppositeLink"
          placeholder="供应商1688商品链接" maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('1688选品中心链接')" v-model.trim="ListInfo.competitorLink"
          placeholder="1688选品中心链接" maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('商品ID')" v-model.trim="ListInfo.proCode" placeholder="商品ID"
          maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('产地')" v-model.trim="ListInfo.origin" placeholder="产地" maxlength="50"
          clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('货号')" v-model.trim="ListInfo.articleNumber" placeholder="货号"
          maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('材质')" v-model.trim="ListInfo.material" placeholder="材质"
          maxlength="100" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('包装')" v-model.trim="ListInfo.packagingSpecification" placeholder="包装"
          maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('联系人')" v-model.trim="ListInfo.contactPerson" placeholder="联系人"
          maxlength="20" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('联系方式')" v-model.trim="ListInfo.contactInformation" placeholder="联系方式"
          maxlength="20" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('热销链接')" v-model.trim="ListInfo.hotLink" placeholder="热销链接"
          maxlength="50" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('快递单号')" v-model.trim="ListInfo.expressNo" placeholder="快递单号"
          maxlength="40" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('对接人')" v-model.trim="ListInfo.yhContactPerson" placeholder="对接人"
          maxlength="20" clearable class="publicCss" />
        <el-select v-show="selectedTitles.includes('对接人架构')" v-model.trim="ListInfo.deptId" clearable filterable
          placeholder="对接人架构" class="publicCss">
          <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-show="selectedTitles.includes('供应商创建人')" v-model.trim="ListInfo.createdUserName"
          placeholder="供应商创建人" maxlength="20" clearable class="publicCss" />
        <div class="publicCss" v-show="selectedTitles.includes('展位')" style="width: 165px;">
          <inputYunhan ref="productboothInformation" :inputt.sync="ListInfo.boothInformation"
            v-model="ListInfo.boothInformation" width="165px" placeholder="展位/Enter输入多条" :clearable="true"
            :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackGoodsCode($event, 'boothInformation')" title="展位">
          </inputYunhan>
        </div>
        <el-select v-show="selectedTitles.includes('是否控价')" v-model="ListInfo.isPriceControl" placeholder="是否控价"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('是否同步商品详情')" v-model="ListInfo.isUse" placeholder="是否同步商品详情"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('是否已打印')" v-model="ListInfo.isDaYin" placeholder="是否已打印" clearable
          class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('商品编号是否有值')" v-model="ListInfo.isGoodsCode" placeholder="商品编号是否有值"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('是否公司有编码')" v-model="ListInfo.isYhGoodsCode" placeholder="是否公司有编码"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('供应商商品链接是否有值')" v-model="ListInfo.isOppositeLink"
          placeholder="供应商商品链接是否有值" clearable class="publicCss" style="width: 170px;">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('1688选品中心链接是否有值')" v-model="ListInfo.isCompetitorLink"
          placeholder="1688选品中心链接是否有值" clearable class="publicCss" style="width: 185px;">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('选品中心链接运营是否有值')" v-model="ListInfo.isOperateId"
          placeholder="选品中心链接运营是否有值" clearable class="publicCss" style="width: 185px;">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('选品中心链接运营')" v-model="ListInfo.operateIdList" placeholder="选品中心链接运营"
          clearable filterable multiple collapse-tags class="publicCss">
          <el-option v-for="item in referrerOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('竞争力')" v-model="ListInfo.isCompetitivePowerH5List" placeholder="竞争力"
          clearable filterable multiple collapse-tags class="publicCss">
          <el-option label="空" :value="0" />
          <el-option v-for="item in competeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('参考价有无')" v-model="ListInfo.isCompetitivePowerH5Price"
          placeholder="参考价有无" clearable class="publicCss">
          <el-option label="有" :value="1" />
          <el-option label="无" :value="2" />
        </el-select>
        <el-select v-show="selectedTitles.includes('是否有产品图片')" v-model="ListInfo.isImage" placeholder="是否有产品图片"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('是否对接打单平台')" v-model="ListInfo.isDuiJieDaDanPingtai"
          placeholder="是否对接打单平台" clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input v-show="selectedTitles.includes('打单平台1')" v-model.trim="ListInfo.daDanPing1" placeholder="打单平台1"
          maxlength="100" clearable class="publicCss" />
        <el-input v-show="selectedTitles.includes('打单平台2')" v-model.trim="ListInfo.daDanPing2" placeholder="打单平台2"
          maxlength="100" clearable class="publicCss" />
        <el-select v-show="selectedTitles.includes('是否所有商品对接打单平台')" v-model="ListInfo.isAllDuiJieDaDanPingtai"
          placeholder="是否所有商品对接打单平台" clearable class="publicCss" style="width: 181px;">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('供应商来源')" v-model="ListInfo.isSupplierSource" placeholder="供应商来源"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="publicCss" v-show="selectedTitles.includes('供应商来源')" style="width: 180px;">
          <inputYunhan ref="productsupplierSource" :inputt.sync="ListInfo.supplierSource"
            v-model="ListInfo.supplierSource" width="180px" placeholder="供应商来源/Enter输入多条" :clearable="true"
            :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackGoodsCode($event, 'supplierSource')" title="供应商来源">
          </inputYunhan>
        </div>
        <el-select v-show="selectedTitles.includes('是否能代发')" v-model="ListInfo.isDaiFa" placeholder="是否能代发" clearable
          class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-show="selectedTitles.includes('价格是否校验')" v-model="ListInfo.isJiaoYanJiaGe" placeholder="价格是否校验" clearable
          class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="publicCss" v-show="selectedTitles.includes('打单平台')" style="width: 165px;">
          <inputYunhan ref="productdaDanPingTai" :inputt.sync="ListInfo.daDanPingTai" v-model="ListInfo.daDanPingTai"
            width="165px" placeholder="打单平台/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'daDanPingTai')" title="打单平台">
          </inputYunhan>
        </div>
        <!-- <div style="width: 250px;margin: 0 0 5px 5px;" v-show="selectedTitles.includes('供应商1688链接一件代发售价(包邮)')">
          <number-range :min.sync="ListInfo.minDaFaPriceNoExpress" maxNumber="9999.9999"
            :max.sync="ListInfo.maxDaFaPriceNoExpress" min-label="最小供应商1688链接一件代发售价(包邮)" max-label="最大供应商1688链接一件代发售价(包邮)" />
        </div> -->
        <el-select v-show="selectedTitles.includes('展位是否有值')" v-model="ListInfo.isBoothInformation" placeholder="展位是否有值"
          clearable class="publicCss">
          <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
	  <el-select v-show="selectedTitles.includes('属性')" v-model="ListInfo.shuXing" placeholder="属性" clearable filterable multiple collapse-tags class="publicCss" style="width: 160px">
          <el-option v-for="item in statusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div class="publicCss" style="width: 165px;">
          <inputYunhan ref="productyhGoodsCode" :inputt.sync="ListInfo.goodsCreatedUserName" v-model="ListInfo.goodsCreatedUserName"
            width="165px" placeholder="添加人/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsCreatedUserName')" title="添加人">
          </inputYunhan>
        </div>
        <el-date-picker class="publicCss" v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="添加起始时间" end-placeholder="添加结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-button type="primary" class="top_button" style="margin-left: 5px;"
        @click="getList('search')">搜索</el-button>
        <el-button class="top_button" icon="vxe-icon-custom-column" @click="clickToolbar" />
        <div style="margin-bottom: 5px;">
          <el-button type="primary" class="top_button" @click="importProps"
            v-if="checkPermission('importSupplier')">导入</el-button>
          <el-button type="primary" class="top_button" @click="exportProps" :disabled="isExport">导出</el-button>
          <el-button type="primary" class="top_button" @click="handleAddBedore">新增</el-button>
          <el-button type="primary" class="top_button" @click="onGeneratePrintQrCode">生成打印二维码</el-button>
          <!-- <el-button type="primary" @click="onPrintMethod(4)">批量打印</el-button> -->
          <!-- <el-button type="primary" class="top_button" v-if="separateCheck" @click="onControls">批量操作</el-button>
          <el-button type="primary" class="top_button" @click="onControlsEdit">批量修改</el-button> -->
          <el-dropdown style="margin: 0 2px;" @command="handleCommand">
            <el-button type="primary">
              批量操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">id批量维护</el-dropdown-item>
              <el-dropdown-item command="b">批量编辑</el-dropdown-item>
              <el-dropdown-item command="c" v-if="checkPermission('batchUnbindingPoint')">批量解绑货架</el-dropdown-item>
              <el-dropdown-item command="d"
                v-if="checkPermission('batchDeleteSampleProduct')">批量删除展会商品</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="primary" class="top_button" @click="onInitProductList">同步新增商品到微信小程序</el-button>
          <el-button type="primary" class="top_button" @click="onGetOrdinaryGoodsData">获取普通商品资料</el-button>
        </div>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
      id="20250218161914" @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @select="selectchange"
      @checkbox-range-end="selectchange">
      <template slot="right">
        <vxe-column title="操作" width="170" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" @click="handleEditBefore(row)">编辑</el-button>
              <el-button type="text" @click="handleMaintenanceId(row)">ID维护</el-button>
              <el-button type="text" @click="handLinePrinting(row, 2)">打印</el-button>
              <el-button type="text" @click="handOperationLog(row)">日志</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <!-- <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span> -->
      </div>
      <div style="display: flex;justify-content: space-between;">
        <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
          :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
          <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-tooltip>
        </el-upload>
      </div>
      <div class="btnGroup">
        <el-button @click="importVisible = false">取消</el-button>
        <el-button type="primary" @click="sumbit" v-throttle="1000">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="isEditPi ? '批量编辑' : isEdit ? '编辑' : '新增'" :visible.sync="logVisible" width="90%" v-dialogDrag
      :close-on-click-modal="false" @close="handleClose" style="margin-top: -10vh;">
      <div slot="title" class="header-title">
        <span class="title-text"><span>{{ isEditPi ? '批量编辑' : isEdit ? '编辑' : '新增' }}</span></span>
        <span class="title-close" v-if="!isEditPi"> <el-button type="primary" @click="handLinePrinting(form, 3)"
            style="margin-right: 30px;">打印</el-button>
        </span>
      </div>
      <div v-loading="editloading">
        <el-form label-width="130px" :model="form" class="logForm" ref="ruleForm" :rules="rules">
          <el-row :gutter="10" style="height: 47px;">
            <el-col :span="8">
              <el-form-item label="供应商名称:" :prop="isEditPi ? '' : 'supplierId'" key="supplierId">
                <el-select v-model="form.supplierId" clearable placeholder="请选择供应商名称" filterable @clear="onClearMthod"
                  style="width: 100%;">
                  <el-option v-for="item in supplierList" :label="item.label" :value="item.value" :key="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="商品名称:" :prop="isEditPi ? '' : 'productName'">
                <el-input v-model.trim="form.productName" clearable maxlength="50" placeholder="请输入商品名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="款式名称:" :prop="isEditPi ? '' : 'styleCode'">
                <el-input v-model.trim="form.styleCode" clearable maxlength="50" placeholder="请输入款式名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="商品编号:">
                <el-input v-model.trim="form.goodsCode" clearable maxlength="20" placeholder="请输入商品编号"
                  disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="公司商品编码:">
                <el-input v-model.trim="form.yhGoodsCode" clearable maxlength="20" placeholder="请输入公司商品编码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="height: 47px;">
            <el-col :span="4">
              <el-form-item label="供应商商品编码:">
                <el-input v-model.trim="form.gysGoodsCode" clearable maxlength="20" placeholder="请输入供应商商品编号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="产地:" :prop="isEditPi ? '' : 'origin'">
                <el-input v-model.trim="form.origin" clearable maxlength="20" placeholder="请输入产地"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="包装:" :prop="isEditPi ? '' : 'packagingSpecification'">
                <el-input v-model.trim="form.packagingSpecification" clearable maxlength="20"
                  placeholder="请输入包装"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="标准装箱数量:"  :prop="isEditPi ? '' : 'boxSpecification'">
                <el-input v-model.trim="form.boxSpecification" clearable maxlength="100"
                  placeholder="请输入标准装箱数量"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="材质:"  :prop="isEditPi ? '' : 'material'">
                <el-input v-model.trim="form.material" clearable maxlength="100" placeholder="请输入材质"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="起订量:">
                <el-input-number v-model="form.minimumOrderQuantity" :min="1" :max="999999" :controls="false"
                  :precision="0" placeholder="请输入起订量" style="width: 100%;"
                  @blur="handleMinOrderBlur($event, form.minimumOrderQuantity, '起订量', 0)"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="height: 47px;">
            <el-col :span="4">
              <el-form-item label="日均产能:">
                <el-input-number v-model="form.averageDailyCapacity" :min="0" :max="9999999" :controls="false"
                  :precision="0" placeholder="请输入日均产能" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="日均现货:">
                <el-input-number v-model="form.dailySpot" :min="0" :max="9999999" :controls="false" :precision="0"
                  placeholder="请输入日均现货" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="4">
              <el-form-item label="价格系数:">
                <el-input-number v-model="form.price" :min="1" :max="999999" :controls="false" :precision="4"
                  placeholder="请输入价格系数" style="width: 100%;"
                  @blur="handleMinOrderBlur($event, form.price, '价格系数', 4)"></el-input-number>
              </el-form-item>
            </el-col> -->
            <el-col :span="4">
              <el-form-item label="供货成本价:" :prop="isEditPi ? '' : 'minimumSellingPrice'">
                <el-input-number v-model="form.minimumSellingPrice" :min="0.0001" :max="999999" :controls="false"
                  :precision="4" placeholder="请输入供货成本价" style="width: 100%;"  @change="changeMinimumSellingControlPrice"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="货号:">
                <el-input v-model.trim="form.articleNumber" clearable maxlength="20" placeholder="请输入货号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="展位:">
                <el-input v-model.trim="form.boothInformation" clearable disabled maxlength="20"
                  placeholder="请输入展位"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="height: 47px;">
            <el-col :span="4">
              <el-form-item label="热销链接:">
                <el-input v-model.trim="form.hotLink" clearable maxlength="500" placeholder="请输入热销链接"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="4">
              <el-form-item label="1688选品中心链接:">
                <el-input v-model.trim="form.competitorLink" clearable maxlength="500" placeholder="请输入1688选品中心链接"
                  :disabled="current.operateId == 41071 ? false : true && !verification"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="4">
              <el-form-item label="类目:">
                <el-select v-model="form.catroyType" placeholder="类目" clearable filterable>
                  <el-option v-for="item in categoryList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="产品重量(g):"  :prop="isEditPi ? '' : 'weight'">
                <el-input-number v-model="form.weight" :min="0" :max="9999999.99" :precision="2" :controls="false"
                  placeholder="产品重量(g)" style="width: 100%;">
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="颜色:" :prop="isEditPi ? '' : 'colour'">
                <el-input v-model.trim="form.colour" clearable maxlength="40" placeholder="请输入颜色"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="对接人:" >
                <el-select v-model="form.yhContactPerson" placeholder="请模糊输入并选择对接人" clearable filterable remote
                  :filter-method="(query) => searchReferrer(query, 2)" @change="handleContactChange" value-key="value">
                  <el-option v-for="item in contactPersonList"
                    :key="'userSelector' + item.value + item.ddUserId + item.extData.defaultDeptId"
                    :label="`${item.name} - (${item.deptName}) - ${item.position} - ${item.empStatusText}`"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="对接人架构:">
                <el-select v-model="form.deptId" placeholder="请选择对接人架构" clearable filterable disabled>
                  <el-option label="无匹配架构" :value="0" />
                  <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="添加人:">
                <el-input v-model.trim="form.createdUserNameRead" clearable disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="快递单号:" :prop="isEditPi ? '' : 'expressNo'">
                <el-input v-model.trim="form.expressNo" clearable maxlength="40" placeholder="请输入快递单号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="选品中心链接运营:">
                <el-select v-model="form.operateId" placeholder="请模糊输入并选择用户" clearable filterable
                  :disabled="current.operateId == 41071 ? false : true && !verification">
                  <el-option v-for="item in referrerOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="产品尺寸(长*宽*高cm):"  :prop="isEditPi ? '' : 'size'" label-width="170px">
                <el-input v-model.trim="form.size" clearable maxlength="200" placeholder="请输入产品尺寸(长*宽*高cm)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="竞争力:">
                <el-select v-model="form.isCompetitivePowerH5" placeholder="请选择竞争力" clearable filterable disabled>
                  <el-option v-for="item in competeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="是否竞争力:" >
                <el-select v-model="form.isCompetitivePower" placeholder="是否竞争力" clearable
                  @change="changeCompetitivePower">
                  <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>


          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="供应商来源:">
                <el-input v-model.trim="form.supplierSource" clearable maxlength="20" placeholder="请输入供应商来源"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="是否控价:" >
                <el-select v-model="form.isPriceControl" placeholder="是否控价" clearable @change="changePriceControl">
                  <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" v-if="form.isPriceControl === 1">
              <el-form-item label="供货成本价(控价价格):" label-width="160px" >
                <el-input-number v-model="form.minimumSellingControlPrice" :min="0" :max="9999999" :controls="false"
                  :precision="4" placeholder="供货成本价(控价价格)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="是否能代发:">
                <el-select v-model="form.isDaiFa" placeholder="是否能代发" clearable>
                  <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="打单平台:">
                <el-input v-model.trim="form.daDanPingTai" clearable maxlength="50" placeholder="请输入打单平台"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="供应商1688商品链接:"  label-width="160px">
                <el-input v-model.trim="form.oppositeLink" clearable maxlength="500"
                  placeholder="请输入供应商商品链接"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="供应商1688链接售价:" label-width="150px">
                <el-input-number v-model="form.saleProce1688" :min="0" :max="9999999" :controls="false" :precision="4"
                  placeholder="请输入供应商1688链接售价" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- <el-col :span="6">
              <el-form-item label="供应商1688链接一件代发售价(包邮):" label-width="240px">
                <el-input-number v-model="form.daFaPriceNoExpress" :min="0" :max="9999.9999" :controls="false"
                  :precision="4" placeholder="请输入代发单价" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col> -->
          </el-row>
          <!-- <el-row v-if="form.isCompetitivePower === 0">
            <el-col :span="4">
              <el-form-item label="对手价格:">
                <el-input-number v-model="form.counterpartyPrice" :min="0" :max="9999999" :controls="false"
                  :precision="4" placeholder="请输入对手价格" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="对手链接:">
                <el-input v-model.trim="form.counterpartyProCode" clearable maxlength="500" placeholder="请输入对手链接"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row>
            <!-- <el-col :span="4">
              <el-form-item label="售价:" :prop="isEditPi ? '' : 'salePrice'">
                <el-input-number v-model="form.salePrice" :min="0" :max="9999999" :controls="false" :precision="4"
                  placeholder="请输入售价" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col> -->

            <!-- <el-col :span="4">
              <el-form-item label="净重(g):">
                <el-input-number v-model="form.netWeight" :min="0" :max="9999999" :controls="false" :precision="2"
                  placeholder="请输入净重(g)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="箱规(长):">
                <el-input-number v-model="form.boxGaugeLength" :min="0" :max="9999999" :controls="false" :precision="2"
                  placeholder="请输入箱规(长)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="箱规(宽):">
                <el-input-number v-model="form.boxGaugeWidth" :min="0" :max="9999999" :controls="false" :precision="2"
                  placeholder="请输入箱规(宽)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="箱规(高):">
                <el-input-number v-model="form.boxGaugeHeight" :min="0" :max="9999999" :controls="false" :precision="2"
                  placeholder="请输入箱规(高)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <!-- <el-col :span="6">
              <el-form-item label="是否有[MADEINCHINA]标识:" label-width="200px">
                <el-select v-model="form.isMadeInChina" placeholder="是否有[MADEINCHINA]标识" clearable style="width: 100%;">
                  <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="5">
              <el-form-item label="标准装箱尺寸(长*宽*高cm):" label-width="200px"  :prop="isEditPi ? '' : 'standardPackingSize'">
                <el-input v-model.trim="form.standardPackingSize" clearable maxlength="100"
                  placeholder="请输入标准装箱尺寸(长*宽*高cm)"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="产品包装是否有生产地址信息:" label-width="210px"  :prop="isEditPi ? '' : 'isHasProductionAddress'">
                <el-select v-model="form.isHasProductionAddress" placeholder="产品包装是否有生产地址信息" clearable filterable>
                  <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="4">
              <el-form-item label="H5小程序售价:" label-width="120px">
                <el-input-number v-model="form.smallProgramLatestPrice" :min="0" :max="9999999" :controls="false"
                  :precision="4" placeholder="请输入H5小程序售价" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col> -->
            <el-col :span="5">
              <el-form-item label="供应商1688链接一件代发售价(包邮):" label-width="240px">
                <el-input-number v-model="form.issueUnitPriceHasExpress" :min="0" :max="9999999" :controls="false"
                  :precision="4" placeholder="请输入供应商1688链接一件代发售价(包邮)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="供应商供货一件代发价格(包邮):" label-width="220px"
                :prop="isEditPi ? '' : 'issueUnitPriceHasExpressToMe'">
                <el-input-number v-model="form.issueUnitPriceHasExpressToMe" :min="0" :max="9999999" :controls="false"
                  :precision="4" placeholder="请输入供应商供货一件代发价格(包邮)" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5">
              <el-form-item label="是否授权（品牌、ip、专利）:" label-width="220px" :prop="isEditPi ? '' : 'isAuthoPP'">
                <el-select v-model="form.isAuthoPP" placeholder="是否授权（品牌、ip、专利）" clearable style="width: 100%;">
                  <el-option label="是"  value="是" />
                  <el-option label="否"  value="否" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="属性:" :prop="isEditPi ? '' : 'shuXing'">
                <el-select v-model="form.shuXing" placeholder="属性" clearable multiple collapse-tags filterable style="width: 100%;">
                  <el-option v-for="item in statusOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item label="产品图片:" label-width="85px" :prop="isEditPi ? '' : 'image'">
                <uploadimgFile ref="imageuploadimgFile" v-if="logVisible" :disabled="isView" :ispaste="!isView"
                  :noDel="isView" :reveal="true" :accepttyes="accepttyes" :isImage="true" :uploadInfo="form.image"
                  :keys="[1, 1]" @callback="getImg($event, 'image')" @beforeUpload="beforeUpload($event, 'image')"
                  :imgmaxsize="10" :limit="10" :multiple="true">
                </uploadimgFile>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="资质:" label-width="78px">
                <uploadimgFile ref="zizhiImageuploadimgFile" v-if="logVisible" :disabled="isView" :ispaste="!isView"
                  :noDel="isView" :reveal="true" :accepttyes="accepttyes" :isImage="true" :uploadInfo="form.zizhiImage"
                  :keys="[1, 1]" @callback="getImg($event, 'zizhiImage')"
                  @beforeUpload="beforeUpload($event, 'zizhiImage')" :imgmaxsize="4" :limit="4" :multiple="true">
                </uploadimgFile>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="专利:" label-width="78px">
                <uploadimgFile ref="zhuanliImageuploadimgFile" v-if="logVisible" :disabled="isView" :ispaste="!isView"
                  :noDel="isView" :reveal="true" :accepttyes="accepttyes" :isImage="true"
                  :uploadInfo="form.zhuanliImage" :keys="[1, 1]" @callback="getImg($event, 'zhuanliImage')"
                  @beforeUpload="beforeUpload($event, 'zhuanliImage')" :imgmaxsize="4" :limit="4" :multiple="true">
                </uploadimgFile>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="height: 200px;" v-if="!isEditPi">
            <el-col :span="11">
              <el-button type="text" @click="addtTableProps">新增一行</el-button>
              <el-table :data="form.priceRangList" style="width: 100%;margin-bottom: 20px" max-height="160"
                v-loading="tableLoading">
                <el-table-column type="index" width="50" />
                <!-- @clear="clearInfo($index)"  @change="changeGoodsCode($index)"  -->
                <el-table-column prop="goodsNum" label="起订量" width="230">
                  <template #default="{ row, $index }">
                    <!-- <el-input v-model.trim="row.goodsNum" placeholder="起订量" style="width: 100%" clearable maxlength="50"
                       /> -->
                    <el-input-number v-model="row.goodsNum" :min="0" :max="9999999" :controls="false" :precision="0"
                      placeholder="请输入起订量" style="width: 100%;"
                      @blur="searchGoodsName(row.goodsNum, $index, '起订量')"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="goodsPrice" label="价格">
                  <template #default="{ row, $index }">
                    <el-input-number v-model="row.goodsPrice" placeholder="价格" :min="0" :max="999999" :precision="4"
                      :controls="false" @blur="searchGoodsName(row.goodsPrice, $index, '价格')" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column prop="" label="操作" width="70" v-if="!isView">
                  <template #default="{ row, $index }" v-if="!isView">
                    <el-button type="danger" @click="form.priceRangList.splice($index, 1)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="13" style="height: 167px;" v-if="isEdit">
              <div style="margin-bottom: 10px;">展会ID图片：</div>
              <el-scrollbar style="height: 100%;">
                <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
                  <el-image v-for="(item, index) in pictureData" :key="index"
                    style="width: 30px; height: 30px; object-fit: contain;" :src="item" :preview-src-list="pictureData">
                  </el-image>
                </div>
              </el-scrollbar>
            </el-col>
          </el-row>
          <div style="height: 80px;" v-else></div>
          <div style="display: flex;justify-content: center;align-items: center;margin-top: 10px;">
            <el-button @click="logVisible = false; editloading = false;">取消</el-button>
            <el-button type="primary" @click="submit" v-throttle="1000">确定</el-button>
          </div>
        </el-form>
      </div>

      <el-dialog title="打印" :visible.sync="innerLayerprintVisible" width="35%" v-dialogDrag @close="printCloseMethod"
        append-to-body>
        <div style="height: 500px;">
          <el-select v-model="printSize" placeholder="请选择打印尺寸" clearable style="width: 130px;margin-right: 5px;"
            @change="changePrintSize($event, 3)">
            <el-option v-for="size in printSizeList" :key="size" :label="size + '*' + size" :value="size" />
          </el-select>
          高(厘米):
          <el-input-number v-model="qrCodeHeight" :min="1" :max="999" :controls="false" :precision="1"
            placeholder="请输入高度(厘米)" style="width: 100px;margin-right: 5px;" @blur="onPrintMethod(1)" />
          长(厘米):
          <el-input-number v-model="qrCodeWidth" :min="1" :max="999" :controls="false" :precision="1"
            placeholder="请输入长度(厘米)" style="width: 100px;margin-right: 10px;" @blur="onPrintMethod(1)" />
          <el-button type="primary" @click="printQRCode('#printid')">打印</el-button>
          <el-button type="primary" @click="onStorageMethodDebounced">打印确认</el-button>
          <el-switch @change="onPrintMethod(6)" style="margin-left: 30px;" v-model="switchshow" active-text="显示id"
            inactive-text="隐藏id">
          </el-switch>
          <el-scrollbar style="height: 95%;margin-top: 10px;">
            <div id="printid" v-if="innerLayerprintVisible" v-loading="printLoading" :style="qrcodeContainerStyle">
              <div v-for="(item, index) in goodsCodeList" :key="index" class="qrcode-item" style="margin-top: 7px;">
                <canvas :id="`qrcode${index}`" :style="{
                  width: `${qrCodeWidth * 37.795}px`,
                  height: `${qrCodeHeight * 37.795}px`
                }"></canvas>
                <div v-show="switchshow" class="qrcode-id">{{ item }}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </el-dialog>
    </el-dialog>

    <el-dialog title="打印" :visible.sync="printVisible" width="40%" v-dialogDrag @close="printCloseMethod">
      <div style="height: 500px;display: flex;flex-direction: column;">
        <div>
          <el-select v-model="printSize" placeholder="请选择打印尺寸" clearable style="width: 110px;margin-right: 5px;"
            @change="changePrintSize($event, 2)">
            <el-option v-for="size in printSizeList" :key="size" :label="size + '*' + size" :value="size" />
          </el-select>
          高(厘米):
          <el-input-number v-model="qrCodeHeight" :min="1" :max="999" :controls="false" :precision="1"
            placeholder="请输入高度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
          长(厘米):
          <el-input-number v-model="qrCodeWidth" :min="1" :max="999" :controls="false" :precision="1"
            placeholder="请输入长度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
          <el-input-number v-model="numQRCode" :min="1" :max="2000" placeholder="二维码张数"
            style="width: 20%;margin-right: 5px;" v-if="generatePrint" :precision="0"></el-input-number>
          <el-button type="primary" v-if="generatePrint" @click="generateQRCode">生成</el-button>
          <el-button type="primary" @click="printQRCode('#printid')">打印</el-button>
          <el-button type="primary" @click="onStorageMethodDebounced" v-if="!generatePrint">打印确认</el-button>
        </div>
        <div style="margin-top: 5px;">
          <el-switch @change="onPrintMethod(6)" v-model="switchshow" active-text="显示id" inactive-text="隐藏id">
          </el-switch>
          <!-- <el-input-number v-if="generatePrint || (goodsCodeList && goodsCodeList.length > 1)"
            v-model.number="rowDisplayNum" :min="1" :max="10" placeholder="行二维码个数" :precision="0"
            style="margin-left: 10px;width: 150px;"></el-input-number> -->
        </div>
        <el-scrollbar ref="refscrollbar" style="height: 95%;margin-top: 10px;">
          <div id="printid" v-if="printVisible" v-loading="printLoading" :style="qrcodeContainerStyle">
            <div v-for="(item, index) in goodsCodeList" :key="index" class="qrcode-item" style="margin-top: 7px;">
              <canvas :id="`qrcode${index}`"></canvas>
              <div v-show="switchshow" class="qrcode-id">{{ item }}</div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>

    <el-dialog title="ID维护" :visible.sync="encodingVisible" width="25%" v-dialogDrag>
      <div class="encoding-container" v-loading="editloading">
        <div class="encoding-row">
          <span class="encoding-label">ID:</span>
          <el-input v-model.trim="encoding.proCode" clearable maxlength="40" placeholder="请输入ID"
            class="encoding-input" />
        </div>
        <div class="encoding-row">
          <span class="encoding-label">选品中心链接运营:</span>
          <el-select v-model="encoding.operateId" placeholder="请选择选品中心链接运营" clearable filterable class="encoding-select"
            :disabled="current.operateId == 41071 ? false : true && !verification">
            <el-option v-for="item in referrerOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="encoding-row">
          <span class="encoding-label">价格是否校验:</span>
            <el-select v-model="encoding.isJiaoYanJiaGe" placeholder="请选择价格是否校验" filterable class="encoding-input">
              <el-option v-for="item in yesOrNoOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </div>
        <div class="encoding-row">
          <span class="encoding-label">H5小程序售价:</span>
            <el-input-number v-model="encoding.smallProgramLatestPrice" :min="0" :max="9999999" :controls="false"
            :precision="2" placeholder="请输入H5小程序售价" style="width: 100%;"></el-input-number>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="encodingVisible = false">取 消</el-button>
        <el-button type="primary" @click="dencodingMethod">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="批量操作" :visible.sync="batchOperationVisible" width="25%" v-dialogDrag style="margin-top: 18vh;"
      @close="handleClose">
      <div class="encoding-container" v-loading="editloading">
        <div class="encoding-row">
          <span class="encoding-label">ID:</span>
          <el-input v-model.trim="batchOperation.proCode" clearable maxlength="40" placeholder="请输入ID"
            class="encoding-input" />
        </div>
        <div class="encoding-row">
          <span class="encoding-label">选品中心链接运营:</span>
          <el-select v-model="batchOperation.operateId" placeholder="请选择选品中心链接运营" clearable filterable
            class="encoding-select" :disabled="current.operateId == 41071 ? false : true && !verification">
            <el-option v-for="item in referrerOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchOperationVisible = false">取 消</el-button>
        <el-button type="primary" @click="batchOperationMethod">保 存</el-button>
      </span>
    </el-dialog>

    <el-dialog title="日志" :visible.sync="operationLog.visible" width="45%" v-dialogDrag>
      <selectionLog v-if="operationLog.visible" ref="refselectionLog" :sampleGoodsId="operationLog.sampleGoodsId" />
    </el-dialog>

    <el-dialog title="查询条件" :visible.sync="dailyNewspaperToolbar" width="50%" v-dialogDrag>
      <template #title>
        <div class="dialog-header">
          查询条件设置
          <el-button style="margin-left: 10px;" @click="selectAll">全选/取消全选</el-button>
        </div>
      </template>
      <el-scrollbar>
        <div style="height: 200px;">
          <el-checkbox-group v-model="colOptions" @change="changeOptions">
            <el-checkbox v-for="(item, i) in colSelect" :label="item" :key="i"></el-checkbox>
          </el-checkbox-group>
        </div>
      </el-scrollbar>
      <div style="margin-top: 40px;display: flex;justify-content: end;">
        <el-button @click="dailyNewspaperToolbar = false">取消</el-button>
        <el-button type="primary" @click="verifyOptions" v-throttle="3000">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="批量解绑" :visible.sync="untieShelvesVisible" width="50%" v-dialogDrag :close-on-click-modal="false">
      <batchUnbinding v-if="untieShelvesVisible" ref="refbatchUnbinding" />
    </el-dialog>
    <el-dialog title="获取普通商品资料" top="10vh" :visible.sync="ordinaryGoodsVisible" width="90%" v-dialogDrag>
      <div slot="title" class="header-title">
        <span class="title-text"><span>获取普通商品资料</span></span>
        <el-button type="primary" size="small" @click="openSelectGoodsCodeDialog" style="margin-left: 15px;">添加商品</el-button>
      </div>
      <div class="ordinary-goods-content">
        <el-table :data="ordinaryGoodsPageList" height="500" border style="width: 100%">
          <el-table-column type="index" label="#" width="50"></el-table-column>
          <el-table-column prop="image" label="图片" width="40" class-name="goods-img-cell">
            <template slot-scope="scope">
              <el-image v-if="scope.row.image" :src="scope.row.image" fit="cover" style="width: 40px; height:40px; display: block;"></el-image>
              <span v-else>无图片</span>
            </template>
          </el-table-column>
          <el-table-column prop="goodsCode" label="商品编码" ></el-table-column>
          <el-table-column prop="goodsName"  label="商品名称" ></el-table-column>
          <el-table-column prop="styleCode" label="款式编码" ></el-table-column>
          <el-table-column prop="costPrice" width="80" label="成本价" ></el-table-column>
          <el-table-column prop="operateGroup" label="运营组" width="140"></el-table-column>
          <el-table-column prop="purchaseGroup" label="采购组"></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="text" @click="deleteOrdinaryGoods(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container" style="margin-top: 10px; text-align: right;">
          <el-pagination
            @size-change="handleOrdinaryGoodsSizeChange"
            @current-change="handleOrdinaryGoodsPageChange"
            :current-page="ordinaryGoodsPageInfo.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="ordinaryGoodsPageInfo.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="ordinaryGoodsList.length">
          </el-pagination>
        </div>
      </div>
      <div class="btnGroup">
        <el-button @click="ordinaryGoodsVisible = false">取消</el-button>
        <el-button type="primary" @click="submitOrdinaryGoods">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="选择商品编码" top="1vh" :visible.sync="selectGoodsCodeVisible" width="80%" height="100%" v-dialogDrag>
      <div slot="title" class="header-title">
        <span class="title-text"><span>选择商品编码</span></span>
      </div>
      <div class="search-area" style="margin-bottom: 10px;">
          <el-form :inline="true" @submit.native.prevent>
            <el-form-item label="商品编码">
              <!-- <el-input v-model="goodsCodeQuery" placeholder="商品编码" clearable style="width: 160px;"></el-input> -->
              <div class="publicCss"  style="width: 165px;">
          <inputYunhan ref="goodsCodeQuery" :inputt.sync="goodsCodeQuery" v-model="goodsCodeQuery"
            width="165px" placeholder="商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="500"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsCodeQuery')" title="商品编码">
          </inputYunhan>
        </div>

            </el-form-item>

            <el-form-item label="商品名称">
              <div class="publicCss"  style="width: 165px;">
          <inputYunhan ref="goodsNameQuery" :inputt.sync="goodsNameQuery" v-model="goodsNameQuery"
            width="165px" placeholder="商品名称/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="500"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsNameQuery')" title="商品名称">
          </inputYunhan>
        </div>
      </el-form-item>

            <el-form-item label="款式编码:">
              <inputYunhan ref="goodsStyleCodeQuery" :inputt.sync="goodsStyleCodeQuery" v-model="goodsStyleCodeQuery"
            width="165px" placeholder="款式编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="500"
            :maxlength="2000" @callback="callbackGoodsCode($event, 'goodsStyleCodeQuery')" title="款式编码">
          </inputYunhan>
            </el-form-item>
            <el-form-item label="运营组:">
              <el-select style="width:160px;" v-model="goodsGroupQuery" placeholder="请选择" :clearable="true" multiple
                filterable :collapse-tags="true">
                <el-option v-for="item in goodsGroupOptions" :key="item.key" :label="item.value" :value="item.key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="采购组:">
              <el-select style="width:160px;" v-model="goodsBrandQuery" placeholder="请选择" :clearable="true" multiple
                filterable :collapse-tags="true">
                <el-option v-for="item in goodsBrandOptions" :key="item.key" :label="item.value" :value="item.key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="selectGoodsPageInfo.currentPage=1;searchGoodsCode()">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      <div class="select-goods-code-content">
        <el-table height="500"
          ref="selectGoodsTable"
          :data="selectGoodsList"
          border
          style="width: 100%;"
          :row-style="{height: 'auto'}"
          :cell-style="{padding: '0 0'}"
          @selection-change="handleSelectionChange"
          @sort-change="handleGoodsSortChange">
          <el-table-column type="selection" width="50"></el-table-column>
          <el-table-column type="index" label="#" width="50"></el-table-column>
          <el-table-column prop="image" label="图片" class-name="goods-img-cell" width="40">
            <template slot-scope="scope">
              <el-image v-if="scope.row.image" :src="scope.row.image" fit="cover" style="width: 40px; height: 40px; display: block;"></el-image>
              <span v-else>无图片</span>
            </template>
          </el-table-column>
          <el-table-column prop="goodsCode" label="商品编码" sortable="custom"></el-table-column>
          <el-table-column prop="goodsName" label="商品名称" sortable="custom"></el-table-column>
          <el-table-column prop="styleCode" label="款式编码" sortable="custom"></el-table-column>
          <el-table-column prop="costPrice" label="成本价" width="90" sortable="custom"></el-table-column>
          <el-table-column prop="operateGroup" label="运营组" width="140" sortable="custom"></el-table-column>
          <el-table-column prop="purchaseGroup" label="采购组" sortable="custom"></el-table-column>
        </el-table>
      </div>
      <div class="pagination-container" style="margin-top: 10px; text-align: right;">
          <el-pagination ref="goodscodepage"
            @size-change="handleGoodsSizeChange"
            @current-change="handleGoodsPageChange"
            :current-page="selectGoodsPageInfo.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="selectGoodsPageInfo.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="selectGoodsPageInfo.total">
          </el-pagination>
        </div>
      <div class="btnGroup">
        <el-button @click="selectGoodsCodeVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectGoods">确定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import { GetDeptList } from '@/api/inventory/Demerit.js'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
// import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode,formatProCodeStutas3 } from "@/utils/tools";
import { pickerOptions, formatLinkProCode, formatProCodeStutas3 } from '@/utils/tools'
import { SetVxeTableColumnCacheAsync, GetVxeTableColumnCacheAsync } from '@/api/admin/business'
import dayjs from 'dayjs'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
  getSampleGoodsList,
  updateSampleGoods,
  getCrmEfficiencyDept,
  getAlbbSupplierExhibitionlist,
  getSampleGoodPriceRangList, ImportSampleGoods, ExportSampleGoods, getSampleProCodeImages, updateSampleGoods2DaYin, getSuiJiGoodsCodeBath, batchUpdateSampleGoodsToOperateId, batchDeleteSampleGoods, initProductList
} from '@/api/customerservice/albbinquirs'
import inputYunhan from "@/components/Comm/inputYunhan";
import dateRange from "@/components/date-range/index.vue";
import printQRCode from "@/utils/printQRCode";
import decimal from '@/utils/decimal'
import { QueryAllDDUserTop100 } from '@/api/admin/deptuser'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink';
import { getLoginInfo } from '@/api/admin/auth'
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { getBrandDeptList } from '@/api/inventory/purchaseordernew'
import selectionLog from "@/views/inventory/vendorInfo/components/selectionLog.vue";
import batchUnbinding from "@/views/inventory/vendorInfo/components/batchUnbinding.vue";
import numberRange from "@/components/number-range/index.vue";
import QRCode from "qrcode";
import Print from 'print-js'
import _ from 'lodash'
import { saveSampleGoods } from '@/api/customerservice/albbinquirs'
import { getList } from '@/api/inventory/basicgoods'
import { getGroupKeyValue } from '@/api/operatemanage/base/product'
import { getAllProBrand } from '@/api/inventory/warehouse'
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { sortable: 'custom', width: '250', align: 'center', prop: 'nameManufacturer', label: '供应商名称', },
  //   { sortable: 'custom', width: '100', align: 'center', prop: 'proCode', label: '商品ID', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'catroyType', label: '类目', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'productName', label: '商品名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商品编号', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'styleCode', label: '款式名称', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'gysGoodsCode', label: '供应商商品编码', },
  { width: '100', align: 'center', prop: 'image', label: '产品图片', type: "images", },
  { sortable: 'custom', width: '130', align: 'center', prop: 'oppositeLink', label: '供应商1688商品链接', type: "click", handle: (that, row, column, cell) => that.canclick(1, row, column, cell) },
  { sortable: 'custom', width: '150', align: 'center', prop: 'competitorLink', label: '1688选品中心链接', type: "click", handle: (that, row, column, cell) => that.canclick(2, row, column, cell) },
  // { sortable: 'custom', width: '150', align: 'center', prop: 'competitorLink', label: '1688选品中心链接', type: 'html', formatter: (row) => row.status == 3 ? formatProCodeStutas3(row.proCode) : formatLinkProCode(4, row.proCode), },
  { istrue: true, prop: 'proCode', fix: true, label: '商品ID', width: '150', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(4, row.proCode) },
  { sortable: 'custom', width: '100', align: 'center', prop: 'competitivePowerH5Price', label: '参考价', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'operateName', label: '选品中心链接运营', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'expressNo', label: '快递单号', },

  { sortable: 'custom', width: '100', align: 'center', prop: 'origin', label: '产地', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'articleNumber', label: '货号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'material', label: '材质', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'colour', label: '颜色', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'size', label: '产品尺寸(长*宽*高cm)', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'packagingSpecification', label: '包装', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'standardPackingSize', label: '标准装箱尺寸(长*宽*高cm)', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'isHasProductionAddress', label: '产品包装是否有生产地址信息', formatter: (row) => row.isHasProductionAddress == 1 ? '是' : row.isHasProductionAddress == 0 ? '否' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'boxSpecification', label: '标准装箱数量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'weight', label: '产品重量', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'dailySpot', label: '日均现货', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'averageDailyCapacity', label: '日均产能', },
  // { sortable: 'custom', width: '50', align: 'center', prop: 'isZizhi', label: '是否有资质', },
  { width: '100', align: 'center', prop: 'zizhiImage', label: '资质', type: "images", },
  // { sortable: 'custom', width: '50', align: 'center', prop: 'isZhuanli', label: '是否有专利', },
  { width: '100', align: 'center', prop: 'zhuanliImage', label: '专利', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'contactPerson', label: '联系人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'contactInformation', label: '联系方式', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'yhContactPerson', label: '对接人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'supplierSource', label: '供应商来源', },
  { sortable: 'custom', width: '240', align: 'center', prop: 'deptName', label: '对接人架构', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '供应商创建人', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'price', label: '价格系数', },
  // { sortable: 'custom', width: '150', align: 'center', prop: 'totalQuantity', label: '价格1、起订量1', },
  // { sortable: 'custom', width: '150', align: 'center', prop: 'totalQuantity', label: '价格2、起订量2', },
  // { sortable: 'custom', width: '150', align: 'center', prop: 'totalQuantity', label: '价格3、起订量3', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isPriceControl', label: '是否控价', formatter: (row) => row.isPriceControl == 1 ? '是' : row.isPriceControl == 0 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isUse', label: '是否同步商品详情', formatter: (row) => row.isUse == 1 ? '是' : row.isUse == 0 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isDaYin', label: '是否已打印', formatter: (row) => row.isDaYin == 1 ? '是' : '否', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'minimumSellingPrice', label: '供货成本价', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'saleProce1688', label: '供应商1688链接售价', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'smallProgramLatestPrice', label: 'H5小程序售价', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isCompetitivePower', label: '是否竞争力', formatter: (row) => row.isCompetitivePower == 1 ? '是' : row.isCompetitivePower == 0 ? '否' : '', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'isCompetitivePowerH5String', label: '竞争力', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'boothInformation', label: '展位', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'hotLink', label: '热销链接', type: "click", handle: (that, row, column, cell) => that.canclick(3, row, column, cell) },
  { sortable: 'custom', width: '130', align: 'center', prop: 'isDuiJieDaDanPingtai', label: '是否对接打单平台', formatter: (row) => row.isDuiJieDaDanPingtai == 1 ? '是' : row.isDuiJieDaDanPingtai == 0 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'daDanPing1', label: '打单平台1', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'daDanPing2', label: '打单平台2', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'isAllDuiJieDaDanPingtai', label: '是否所有商品对接打单平台', formatter: (row) => row.isAllDuiJieDaDanPingtai == 1 ? '是' : row.isAllDuiJieDaDanPingtai == 0 ? '否' : '', },
  { sortable: 'custom', width: '90', align: 'center', prop: 'housekeeperAccountNumber', label: '店管家账号', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'isDaiFa', label: '是否能代发', formatter: (row) => row.isDaiFa == 1 ? '是' : row.isDaiFa == 0 ? '否' : '', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'isJiaoYanJiaGe', label: '价格是否校验', formatter: (row) => row.isJiaoYanJiaGe == 1 ? '是' : row.isJiaoYanJiaGe == 0 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'daDanPingTai', label: '打单平台', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'daFaPriceNoExpress', label: '供应商1688链接一件代发售价(包邮)', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'minimumOrderQuantity', label: '起订量', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'salePrice', label: '售价', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'issueUnitPriceHasExpress', label: '供应商1688链接一件代发售价(包邮)', },//原代发单价(含运)
  { sortable: 'custom', width: '150', align: 'center', prop: 'issueUnitPriceHasExpressToMe', label: '供应商供货一件代发价格(包邮)', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'isAuthoPP', label: '是否授权（品牌、ip、专利）', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'netWeight', label: '净重(g)', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'isMadeInChina', label: '是否有[MADEINCHINA]标识', formatter: (row) => row.isMadeInChina == 1 ? '是' : row.isMadeInChina == 0 ? '否' : '', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'boxGaugeLength', label: '箱规(长)', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'boxGaugeWidth', label: '箱规(宽)', },
  // { sortable: 'custom', width: '100', align: 'center', prop: 'boxGaugeHeight', label: '箱规(高)', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shuXing', label: '属性', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdTime', label: '添加时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'a.CreatedUserName', label: '添加人',  formatter: (row) => row.createdUserNameRead },

]
const competeList = [
  { label: '无竞争力(平台售价≤商品成本)', value: 1 },
  { label: '有竞争力-普通款(平台售价＞商品成本)', value: 2 },
  { label: '有竞争力-竞争力款(平台售价＞商品成本)', value: 3 },
  { label: '无竞价产品', value: 4 },
]
const yesOrNoOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]
const statusOptions = [
  '一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月', '四季款'
]
const categoryList = ['3C数码配件、电器', '艺术收藏用品', '运动户外', '载具用品', '仪器仪表', '五金建材工具', '居家布艺', '配饰专区', '饰品装饰', '美妆美容美发美体用品', '服饰专区', '玩具动漫周边', '日用餐厨饮具', '生活工具', '收纳清洁用具', '孕产妇/婴童用品', '宠物用品', '办公文化', '节庆用品礼品']
export default {
  name: "supplierSelection",
  components: {
    MyContainer, vxetablebase, dateRange, uploadimgFile, inputYunhan, selectionLog, numberRange, batchUnbinding
  },
  data() {
    return {
      untieShelvesVisible: false,
      dailyNewspaperToolbar: false,
      colOptions: [],
      selectedTitles: [],
      storeid: 'vendorInfocomponentssupplierSelection',
      colSelect: ["供应商名称", "类目",
        "商品名称", '商品编号',
        '公司商品编码', '款式名称', '供应商商品编码', '供应商1688商品链接',
        '1688选品中心链接', '商品ID', '产地',
        '货号', '材质', '包装',
        '联系人', '联系方式', '热销链接',
        '快递单号', '对接人',
        '对接人架构', '供应商创建人', '展位', '是否控价',
        '是否同步商品详情', '是否已打印', '商品编号是否有值', '是否公司有编码', '供应商商品链接是否有值', '1688选品中心链接是否有值',
        '选品中心链接运营是否有值', '选品中心链接运营',
        '竞争力', '参考价有无', '是否有产品图片', '是否对接打单平台', '打单平台1',
        '打单平台2', '是否所有商品对接打单平台', '供应商来源', '是否能代发', '价格是否校验', '打单平台', '展位是否有值', '属性'
      ],
      operationLog: {
        visible: false,
        sampleGoodsId: null,
      },
      isEditPi: false,
      yesOrNoOptions,
      statusOptions,
      contactPersonList: [],
      categoryList,
      loginMessage: {},
      purchasegrouplist: [],
      competeList,
      specialVerify: true,
      separateCheck: false,
      current: {
        operateId: '',
        operateName: '',
      },
      batchloading: false,
      batchOperation: {
        operateId: '',
        proCode: '',
        ids: [],
      },
      batchOperationVisible: false,
      verification: false,
      referrerOptions: [],
      scrollbarWidth: 0,
      generateList: [],
      numQRCode: undefined,
      rowDisplayNum: undefined,
      generatePrint: false,
      printSizeList: [10, 8, 5, 4, 3, 2.5, 1.5],
      pictureData: [],
      encoding: {
        proCode: '',
        operateId: '',
        isJiaoYanJiaGe: 0,
      },
      encodingVisible: false,
      printSize: 1.5,
      qrCodeWidth: 1.5,
      qrCodeHeight: 1.5,
      switchshow: true,
      printVisible: false,
      innerLayerprintVisible: false,
      printLoading: false,
      codelist: [],
      goodsCodeList: [],
      selectList: [],
      printData: [],
      tableLoading: false,
      editloading: false,
      isView: false,
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        nameManufacturer: null,
        contactPerson: null,
        contactInformation: null,
        yhContactPerson: null,
        createdUserName: null,
        yhGoodsCode: null,
        goodsCode: null,
        proCode: null,//商品ID
        oppositeLink: null,//供应商1688商品链接
        competitorLink: null,//1688选品中心链接
        origin: null,//产地
        articleNumber: null,//货号
        material: null,//材质
        packagingSpecification: null,//包装
        isUse: null,//是否同步商品详情
        isDaYin: null,//是否已打印
        gysGoodsCode: null,//供应商商品编码
        boothInformation: null,//展位
        isPriceControl: null,//是否控价
        hotLink: null,//热销链接
        productName: null,//商品名称
        expressNo: null,//快递单号
        isGoodsCode: null,//是否有公司商品编码
        isYhGoodsCode: null,//是否有供应商公司商品编码
        isOppositeLink: null,//是否有供应商商品链接
        isCompetitorLink: null,//是否有1688选品中心链接
        isOperateId: null,//是否有选品中心链接运营
        isCompetitivePowerH5List: [],//竞争力
        isCompetitivePowerH5Price: null,//参考价
        deptId: null,//对接人架构
        styleCode: null,//款式名称
        operateIdList: [],
        catroyType: null,//类目
        isImage: null,//是否有产品图片
        isDuiJieDaDanPingtai: null,//是否对接打单平台
        daDanPing1: null,//打单平台1
        daDanPing2: null,//打单平台2
        isAllDuiJieDaDanPingtai: null,//是否所有商品对接打单平台
        supplierSource: null,//供应商来源
        isSupplierSource: null,//供应商来源是否有值
        isDaiFa: null,//是否能代发
        isJiaoYanJiaGe: null,//价格是否校验
        daDanPingTai: null,//打单平台
        minDaFaPriceNoExpress: null,//最小代发单价（不含运）
        maxDaFaPriceNoExpress: null,//最大代发单价（不含运）
        isBoothInformation: null,//展位是否有值
        shuXing: [],//属性
        goodsCreatedUserName:null,
        createdTimeStart:null,
        createdTimeEnd:null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      fileList: [],
      importLoading: false,
      importVisible: false,
      file: null,
      isEdit: false,
      logVisible: false,
      supplierList: [],//供应商列表
      form: {
        productName: null,//商品名称
        goodsCode: null,//商品编号
        yhGoodsCode: null,//公司商品编码
        gysGoodsCode: null,//供应商商品编码
        image: [],//产品图片
        oppositeLink: null,//供应商1688商品链接
        competitorLink: undefined,//1688选品中心链接
        origin: null,//产地
        articleNumber: null,//货号
        material: null,//材质
        packagingSpecification: null,//包装
        boxSpecification: null,//标准装箱数量
        dailySpot: undefined,//日均现货
        averageDailyCapacity: undefined,//日均产能
        zizhiImage: [],//资质/
        zhuanliImage: [],//专利
        //price: 1,//价格系数
        isPriceControl: null,//是否控价
        minimumSellingPrice: undefined,//供货成本价
        isCompetitivePower: null,//是否竞争力
        boothInformation: null,//展位
        hotLink: null,//热销链接
        priceRangList: [],//价格、起订量
        id: null,
        counterpartyProCode: null,//对手链接
        counterpartyPrice: undefined,//对手价格
        minimumSellingControlPrice: undefined,//最低控价价格
        isZhuanli: null,//是否转让
        isZizhi: null,//是否资质
        expressNo: null,//快递单号
        colour: null,//颜色
        size: null,//产品尺寸(长*宽*高cm)
        yhContactPerson: null,//对接人
        operateId: null,//选品中心链接运营
        isCompetitivePowerH5: null,//竞争力
        deptId: null,//对接人架构
        deptName: null,//对接人架构
        styleCode: null,//款式名称
        weight: undefined,//产品重量
        catroyType: null,//类目
        supplierSource: null,//供应商来源
        daFaPriceNoExpress: undefined,//供应商1688链接一件代发售价(包邮)
        isDaiFa: null,//是否能代发
        daDanPingTai: null,//打单平台
        batchType: null,//批量操作类型
        minimumOrderQuantity: undefined,//起订量
        salePrice: undefined,//售价
        issueUnitPriceHasExpress: undefined,//单件运费
        issueUnitPriceHasExpressToMe: undefined,//供应商供货一件代发价格(包邮)
        boxGaugeLength: undefined,//箱规(长)
        boxGaugeWidth: undefined,//箱规(宽)
        boxGaugeHeight: undefined,//箱规(高)
        isMadeInChina: null,//是否有[MADEINCHINA]标识
        netWeight: undefined,//净重(g)
        saleProce1688: undefined,//供应商1688链接售价
        smallProgramLatestPrice: undefined,//H5小程序售价
        isHasProductionAddress: null,//产品包装是否有生产地址信息
        standardPackingSize: null,//标准装箱尺寸(长*宽*高cm)
        isAuthoPP:null,//是否授权（品牌、ip、专利）
        shuXing: [],//属性
      },
      batchType: null,
      rules: {
        productName: [
          { required: true, message: '请输入商品名称', trigger: 'blur' },
        ],
        goodsCode: [
          { required: true, message: '请输入商品编号', trigger: 'blur' },
        ],
        yhGoodsCode: [
          { required: true, message: '请输入公司商品编码', trigger: 'blur' },
        ],
        gysGoodsCode: [
          { required: true, message: '请输入供应商商品编号', trigger: 'blur' },
        ],
        image: [
          { required: true, message: '请上传产品图片', trigger: 'blur' },
        ],
        // oppositeLink: [
        //   { required: true, message: '请输入供应商商品链接', trigger: 'blur' },
        // ],
        competitorLink: [
          { required: true, message: '请输入1688选品中心链接', trigger: 'blur' },
        ],
        // origin: [
        //   { required: true, message: '请输入产地', trigger: 'blur' },
        // ],
        // articleNumber: [
        //   { required: true, message: '请输入货号', trigger: 'blur' },
        // ],
        material: [
          { required: true, message: '请输入材质', trigger: 'blur' },
        ],
        packagingSpecification: [
          { required: true, message: '请输入包装', trigger: 'blur' },
        ],
        boxSpecification: [
          { required: true, message: '请输入标准装箱数量', trigger: 'blur' },
        ],
        dailySpot: [
          { required: true, message: '请输入日均现货', trigger: 'blur' },
        ],
        averageDailyCapacity: [
          { required: true, message: '请输入日均产能', trigger: 'blur' },
        ],
        zizhiImage: [
          { required: true, message: '请上传资质', trigger: 'blur' },
        ],
        zhuanliImage: [
          { required: true, message: '请上传专利', trigger: 'blur' },
        ],
        // price: [
        //   { required: true, message: '请输入价格系数', trigger: 'blur' },
        // ],
        // isPriceControl: [
        //   { required: true, message: '请输入是否控价', trigger: 'blur' },
        // ],
        minimumSellingPrice: [
          { required: true, message: '请输入供货成本价', trigger: 'blur' },
        ],
        size: [
          { required: true, message: '请输入产品尺寸(长*宽*高cm)', trigger: 'blur' },
        ],
        // isCompetitivePower: [
        //   { required: true, message: '请输入是否竞争力', trigger: 'blur' },
        // ],
        boothInformation: [
          { required: true, message: '请输入展位', trigger: 'blur' },
        ],
        hotLink: [
          { required: true, message: '请输入热销链接', trigger: 'blur' },
        ],
        image: [
          { required: true, type: 'array', min: 1, message: '请上传产品图片', trigger: 'blur' }
        ],
        supplierId: [
          { required: true, message: '请选择供应商', trigger: 'change' },
        ],
        // yhContactPerson: [
        //   { required: true, message: '请输入对接人', trigger: 'blur' },
        // ],
        // expressNo: [
        //   { required: true, message: '请输入快递单号', trigger: 'blur' },
        // ],
        colour: [
          { required: true, message: '请输入颜色', trigger: 'blur' },
        ],
        weight: [
          { required: true, message: '请输入产品重量', trigger: 'blur' },
        ],
        styleCode: [
          { required: true, message: '请输入款式名称', trigger: 'blur' },
        ],
        salePrice: [
          { required: true, message: '请输入售价', trigger: 'blur' },
        ],
        // issueUnitPriceHasExpressToMe: [
        //   { required: true, message: '请输入供应商供货一件代发价格(包邮)', trigger: 'blur' },
        // ],
        isAuthoPP: [
          { required: true, message: '请选择是否授权（品牌、ip、专利）', trigger: 'blur' },
        ],
        standardPackingSize: [
          { required: true, message: '请输入标准装箱尺寸（长*宽*高cm）', trigger: 'blur' },
        ],
        isHasProductionAddress: [
          { required: true, message: '请选择产品包装是否有生产地址信息', trigger: 'blur' },
        ],
        shuXing: [
          { required: true, message: '请选择属性', trigger: 'blur' },
        ],
      },
      // 获取普通商品资料相关数据
      ordinaryGoodsVisible: false,
      ordinaryGoodsList: [],
      selectGoodsCodeVisible: false,
      selectGoodsList: [],
      selectedGoods: [],
      goodsCodeQuery: '',
      goodsNameQuery: '',
      goodsStyleCodeQuery: '',
      goodsGroupQuery: null,
      goodsBrandQuery: null,
      goodsGroupOptions: [],
      goodsBrandOptions: [],
      // 商品编码弹窗分页
      selectGoodsPageInfo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      selectGoodsSortInfo: {
        orderBy: 'modified',
        isAsc: false
      },
      existingGoodsCodes: [],
      canceledGoodsCodes: [],
      ordinaryGoodsPageInfo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
    }
  },
  // 计算属性
  computed: {
    qrcodeContainerStyle() {
      const containerWidth = this.scrollbarWidth
      const qrCodeWidthWithMargin = this.qrCodeWidth * 37.795 + 20; // 200px宽度 + 20px的间距
      const maxRowDisplayNum = Math.floor(containerWidth / qrCodeWidthWithMargin); // 最大能显示的二维码个数
      // 计算每行显示的二维码个数，如果二维码宽度超出总宽度，则自动换行
      const rowDisplayNum = this.rowDisplayNum > maxRowDisplayNum ? maxRowDisplayNum : this.rowDisplayNum;
      return {
        width: `${decimal(decimal(this.qrCodeWidth, 37.795, 0, '*'), 120, 0, '+')}px`,
        height: `${decimal(decimal(this.qrCodeHeight, 37.795, 0, '*'), 40, 0, '+')}px`,
        display: 'grid',
        // gridTemplateColumns: `repeat(${rowDisplayNum}, 1fr)`,
        // gap: '20px',
        justifyContent: 'center',
        alignItems: 'center',
        pageBreakInside: 'avoid',
      };
    },
    ordinaryGoodsPageList() {
      const start = (this.ordinaryGoodsPageInfo.currentPage - 1) * this.ordinaryGoodsPageInfo.pageSize;
      const end = start + this.ordinaryGoodsPageInfo.pageSize;
      return this.ordinaryGoodsList.slice(start, end);
    },
    that() {
      return this
    },
    loginUserId() {
      return this.loginMessage?.userId || 0
    },
    loginUserName() {
      return this.loginMessage?.username || ""
    },
  },
  watch: {
    printVisible(val) {
      if (val) {
        this.getScrollbarWidth()
      }
    },
  },
  async mounted() {
    await this.oninitializeEcho();
    setTimeout(async () => {
      const { data } = await getUserInfo();
      this.current.operateId = data.id || ''
      this.current.operateName = data.nickName || ''
      const { data: directorGroupList } = await getLoginInfo()
      this.loginMessage = data
      this.loginMessage.roles = directorGroupList.roles
      this.checkAuthority(data.fullName, directorGroupList.roles);
      if (this.current.operateId == 41071 && this.current.operateName == '陈鑫河') {
        this.specialVerify = false
        this.separateCheck = true
      }
    }, 500)
    await this.getList()
    await this.init(null)
    //对接人架构
    let originalArr = await GetDeptList();
    this.purchasegrouplist = originalArr.map(({ id, name }) => ({ value: Number(id), label: name }));
    const { data: data1 } = await getCrmEfficiencyDept()
    this.referrerOptions = (data1 || []).map(item => ({
      label: item.userName,
      value: item.userId
    }))
  },
  methods: {
    async onInitProductList() {
      this.$confirm('是否同步新增商品到微信小程序?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await initProductList()
      })
    },
    handleMinOrderBlur(e, val, name, precision) {
      let parsed = Number(val); // 转换为数字
      const minValue = 1; // 最小值设定
      // 目标字段映射
      const target = name === '起订量' ? 'minimumOrderQuantity' : 'price';
      // 如果转换后的值无效（NaN）或小于最小值，则赋值为 minValue
      if (isNaN(parsed) || parsed < minValue) {
        parsed = minValue;
      }
      // 根据精度进行取整或保留小数位
      if (precision === 0) {
        parsed = Math.floor(parsed); // 起订量必须是整数
      } else {
        parsed = Number(parsed.toFixed(precision)); // 价格系数保留指定的小数位
      }
      // 赋值回表单
      this.form[target] = parsed;
    },
    //初始化
    selectAll() {
      if (this.colOptions.length != this.colSelect.length) {
        this.colOptions = this.colSelect.map(i => i);
      } else {
        this.colOptions = [];
      }
    },
    changeOptions(valArr) {
      this.colOptions = valArr;
    },
    //点击设置
    clickToolbar() {
      this.oninitializeEcho();
      this.dailyNewspaperToolbar = true;
    },
    //数据初始化回显
    async oninitializeEcho() {
      const { data, success } = await GetVxeTableColumnCacheAsync({ tableId: this.storeid });
      if (success) {
        let storeData = data ? JSON.parse(data) : [];
        this.colOptions = this.colSelect.filter(i => storeData.includes(i));
      } else {
        this.colOptions = [];
      }
      this.selectedTitles = this.colOptions
    },
    //点击确定
    async verifyOptions() {
      await SetVxeTableColumnCacheAsync({ tableId: this.storeid, ColumnConfig: JSON.stringify(this.colOptions) });
      var arr = this.colSelect.filter(i => this.colOptions.indexOf(i) < 0); // 未选中的
      this.selectedTitles = this.colSelect.filter(i => {
        //定义某项必须显示
        if (i !== '日期') {
          return !arr.includes(i)
        }
      });
      this.dailyNewspaperToolbar = false;
    },

    async handleCommand(value) {
      if (value == 'a') {
        this.batchType = 1
        this.onControls();
      } else if (value == 'b') {
        if (!this.selectList || (this.selectList && this.selectList.length == 0)) {
          this.$message.error('请选择要操作的行数据')
          return
        }
        this.batchType = 2
        this.isEditPi = true;
        this.handleEdit({})
      } else if (value == 'c') {
        this.untieShelvesVisible = true
      } else if (value == 'd') {
        if (!this.selectList || (this.selectList && this.selectList.length == 0)) {
          this.$message.error('请选择要操作的行数据')
          return
        }
        this.$confirm('是否批量删除所选展会商品?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const { success } = await batchDeleteSampleGoods({ ids: this.selectList.map(item => item.id) })
          if (success) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {
        });
      }
    },
    async handOperationLog(row) {
      this.operationLog.sampleGoodsId = row.id ? row.id : null
      this.operationLog.visible = true
    },
    handleContactChange(val) {
      const selectedItem = this.contactPersonList.find(item => item.value === val);
      if (selectedItem) {
        this.changeContactPerson(selectedItem); // 传递完整的选中对象
      }
    },
    changeContactPerson(selectedItem) {
      if (selectedItem) {
        const isValid = this.purchasegrouplist?.some(group => group.value == selectedItem.deptId);
        this.form.deptId = isValid ? selectedItem.deptId : 0;
        this.form.deptName = isValid ? selectedItem.deptName : null;
        this.form.yhContactPerson = selectedItem.name; // 赋值 name 但保持 value 绑定
      } else {
        this.form.deptId = null;
        this.form.deptName = null;
        this.form.yhContactPerson = null;
      }
    },
    handleClose() {
      this.contactPersonList = []
    },
    checkAuthority(fullName, roles) {
      const is1688Operator = fullName.includes('1688运营');
      const isSuperAdmin = roles.some(role => role.name === '超级管理员');
      this.verification = is1688Operator || isSuperAdmin;
      this.separateCheck = is1688Operator || isSuperAdmin;
    },
    batchOperationMethod() {
      if (!this.batchOperation.operateId && !this.batchOperation.proCode) {
        this.$message.error('请选择选品中心链接运营或者输入ID')
        return
      }
      this.$confirm('是否执行批量操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.batchloading = true
        const { success, data } = await batchUpdateSampleGoodsToOperateId({ ...this.batchOperation, batchType: this.batchType })
        this.batchloading = false
        if (success) {
          if (data.success) {
            this.$message.success('设置成功')
            this.getList()
            this.batchOperationVisible = false
          } else {
            this.$message.error(data.msg)
          }
        } else {
          this.$message.error('设置失败')
        }
      }).catch(() => {
      });
    },
    onControls() {
      if (!this.selectList || (this.selectList && this.selectList.length == 0)) {
        this.$message.error('请选择要操作的行数据')
        return
      }
      this.batchOperation.operateId = ''
      this.batchOperation.proCode = ''
      this.batchOperation.ids = this.selectList.map(item => item.id)
      if (this.selectList[0].proCode) {
        this.batchOperation.proCode = this.selectList[0].proCode
      }
      this.batchOperationVisible = true
    },
    async searchReferrer(e, val) {
      //如果e的长度大于200,就提示
      if (e.length > 200) {
        this.$message.error('最多输入200个字符')
        return
      }
      // 如果输入为空，清空下拉框
      if (e === '' || e === null || e === undefined) {
        this.contactPersonList = []
        return
      }
      const { data } = await QueryAllDDUserTop100({ keywords: e });
      if (!data) return;
      const targetData = data.map(item => ({
        value: item.userId,
        label: `${item.userName} - (${item.deptName})`,
        name: item.userName,
        ddUserId: item.ddUserId,
        extData: item,
        ...(val === 2 && { deptId: item.defaultDeptId, deptName: item.deptName, position: item.position, empStatusText: item.empStatusText }) // 动态添加字段
      }));
      val === 1 ? this.referrerOptions = targetData : this.contactPersonList = targetData;
    },
    getScrollbarWidth() {
      this.$nextTick(() => {
        if (this.$refs.refscrollbar && this.$refs.refscrollbar.$el) {
          this.scrollbarWidth = this.$refs.refscrollbar.$el.offsetWidth;
        } else {
          console.warn('Scrollbar ref not found.');
        }
      });
    },
    async generateQRCode() {
      if (this.numQRCode == undefined || this.numQRCode == null || this.numQRCode == '') {
        this.$message.error('请输入二维码张数')
        return
      }
      this.printLoading = true
      const { data, success } = await getSuiJiGoodsCodeBath({ num: this.numQRCode })
      this.printLoading = false
      if (success) {
        this.generateList = data
        this.onPrintMethod()
      } else {
        this.$message.error('生成失败')
      }
    },
    onGeneratePrintQrCode() {
      this.goodsCodeList = []
      this.generatePrint = true
      this.switchshow = true
      this.printVisible = true
    },
    callbackGoodsCode(val, type) {
      const map = {
        productName: () => (this.ListInfo.productName = val),
        goodsCode: () => (this.ListInfo.goodsCode = val),
        yhGoodsCode: () => (this.ListInfo.yhGoodsCode = val),
        styleCode: () => (this.ListInfo.styleCode = val),
        supplierSource: () => (this.ListInfo.supplierSource = val),
        daDanPingTai: () => (this.ListInfo.daDanPingTai = val),
        boothInformation: () => (this.ListInfo.boothInformation = val),
        goodsCreatedUserName: () => (this.ListInfo.goodsCreatedUserName = val),
        goodsCodeQuery: () => (this.goodsCodeQuery= val),
        goodsStyleCodeQuery: () => (this.goodsStyleCodeQuery= val),
        goodsNameQuery: () => (this.goodsNameQuery= val),
      };
      map[type]?.();
    },
    onStorageMethodDebounced: _.debounce(function (param) {
      this.editloading = true
      this.printedMthod(param);
    }, 1000),
    async printedMthod() {
      let goodsCodeList = this.printData.map(item => item.goodsCode);
      this.printLoading = true
      const { success } = await updateSampleGoods2DaYin({ goodsCodeList })
      this.printLoading = false
      this.editloading = false
      if (success) {
        this.$message.success('打印确认成功')
        // this.onPrintMethod()
      } else {
        this.$message.error('打印确认失败')
      }
    },
    prepareParams() {
      const { operateIdList, isCompetitivePowerH5List, ...rest } = this.ListInfo;
      return {
        ...rest,
        operateIdList: operateIdList?.length ? operateIdList.join(',') : '',
        isCompetitivePowerH5List: isCompetitivePowerH5List?.length ? isCompetitivePowerH5List.join(',') : '',
      };
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    async exportProps() {
      this.isExport = true
      const params = this.prepareParams();
      await ExportSampleGoods(params).then(({ data }) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '阿里巴巴展会供应商商品列表' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    handleMaintenanceId(row) {
      this.encoding = JSON.parse(JSON.stringify(row))
      this.encoding.image = this.encoding.image && this.encoding.image.length > 0 ? JSON.parse(this.encoding.image) : ''
      this.encoding.zizhiImage = this.encoding.zizhiImage && this.encoding.zizhiImage.length > 0 ? JSON.parse(this.encoding.zizhiImage) : ''
      this.encoding.zhuanliImage = this.encoding.zhuanliImage && this.encoding.zhuanliImage.length > 0 ? JSON.parse(this.encoding.zhuanliImage) : ''
      this.encoding.proCode = row.proCode ? row.proCode : ''
      this.encoding.operateId = row.operateId ? row.operateId : ''
      this.batchType = null
      this.encoding.isJiaoYanJiaGe = row.isJiaoYanJiaGe ? row.isJiaoYanJiaGe : 0
      this.encodingVisible = true
    },
    async dencodingMethod() {
      // if (!this.encoding.proCode) {
      //   this.$message.error('请输入完整ID')
      //   return
      // }


      this.editloading = true
      if (this.encoding.zizhiImage.length && this.encoding.zizhiImage.length > 0) {
        this.encoding.isZizhi = 1
      } else {
        this.encoding.isZizhi = null
      }
      if (this.encoding.zhuanliImage.length && this.encoding.zhuanliImage.length > 0) {
        this.encoding.isZhuanli = 1
      } else {
        this.encoding.isZhuanli = null
      }
      const params = {
        ...this.encoding,
        image: this.encoding.image.map(item => item.url).filter(Boolean).join(','),
        zizhiImage: this.encoding.zizhiImage.map(item => item.url).filter(Boolean).join(','),
        zhuanliImage: this.encoding.zhuanliImage.map(item => item.url).filter(Boolean).join(','),
      }
      const { success } = await updateSampleGoods(params)
      this.editloading = false
      if (!success) return
      this.$message.success('操作成功')
      this.encodingVisible = false
      this.getList()
    },
    canclick(val, row, column, cell) {
      let url = ''
      if (val == 1) {
        url = row.oppositeLink
      } else if (val == 2) {
        url = row.competitorLink
      } else if (val == 3) {
        url = row.hotLink
      }
      if (url) {
        window.open(url, '_blank');
      }
    },
    printQRCode(val) {
      if (this.goodsCodeList && this.goodsCodeList.length == 0) {
        this.$message.error('暂无可打印的二维码');
        return;
      }

      Print({
        printable: 'printid',
        type: 'html',
        scanStyles: true,
        targetStyles: ['*'],
        onCancel: function () {
          console.log('');
        },
        onPrint: function () {
          console.log('');
        },
      });
    },
    changePrintSize(e, val) {
      this.qrCodeWidth = e
      this.qrCodeHeight = e
      this.onPrintMethod(val)
    },
    printCloseMethod() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection()
        this.selectList = []
        this.printData = []
        this.printSize = 1.5
        this.qrCodeWidth = 1.5
        this.qrCodeHeight = 1.5
        this.printVisible = false
        this.generatePrint = false
        this.goodsCodeList = []
        this.numQRCode = undefined
        this.rowDisplayNum = undefined
        this.switchshow = true
      })
    },
    handLinePrinting(row, val) {
      // this.selectList = [row]
      this.printData = [row]
      if ((val == 3 || val == 2) && !row.goodsCode) {
        this.$message.error('暂无商品编号，无法打印')
        return
      }
      this.onPrintMethod(val)
    },
    onPrintMethod(val) {
      let listData = []
      if (this.generatePrint) {
        if (this.goodsCodeList?.length === 0 && val === 6) return;
        if (!this.generateList?.length) {
          this.$message.error('请先输入二维码张数以生成二维码');
          return;
        }
        this.goodsCodeList = this.generateList;
      } else {
        if (val == 2 || val == 3 || val == 1 || val == 6) {
          listData = this.printData
        } else {
          listData = this.selectList
        }
        if (!listData?.length) {
          this.$message.error('请选择需要打印的商品');
          return;
        }
        if (val === 1) this.printSize = null;
        this.goodsCodeList = listData.map(item => item.goodsCode);
      }
      this.printLoading = true;
      if (val == 4 || val == 2) {
        this.printVisible = true;
        this.innerLayerprintVisible = false;
      } else if (val == 3) {
        this.innerLayerprintVisible = true;
        this.printVisible = false;
      }
      // 定义厘米到像素的转换因子（1厘米 = 37.795像素）
      const cmToPx = 40;
      setTimeout(async () => {
        for (let i = 0; i < this.goodsCodeList.length; i++) {
          if (document.getElementById(`qrcode${i}`)) {
            const element = document.getElementById(`qrcode${i}`);
            const options = {
              width: decimal(this.qrCodeWidth, cmToPx, 0, '*'),   // 宽度（像素）
              height: decimal(this.qrCodeHeight, cmToPx, 0, '*'),  // 高度（像素）
              margin: 2                           // 边距
            };
            const url = 'http://xp.gylyh.com/approvalform/html/codenummsg.html?id=' + this.goodsCodeList[i];
            QRCode.toCanvas(element, url, options, (error) => {
              if (error) console.error(error);
            });
          }
        }
      }, 300);
      setTimeout(() => {
        this.printLoading = false;
        this.editloading = false;
      }, 1000);
    },
    selectchange(val) {
      this.selectList = val;
    },
    searchGoodsName(value, index, type) {
      if (type === '起订量') {
        if (index > 0) {
          const previousGoodsNum = Number(this.form.priceRangList[index - 1].goodsNum);
          if (value >= previousGoodsNum) {
            this.$message.error('起订量必须小于上一行的起订量');
            this.form.priceRangList[index].goodsNum = undefined;
          }
        } else if (this.form.priceRangList.length > 1) {
          const nextGoodsNum = Number(this.form.priceRangList[index + 1].goodsNum);
          if (value < nextGoodsNum) {
            this.$message.error('起订量必须大于下一行的起订量');
            this.form.priceRangList[index].goodsNum = undefined;
          }
        }
      }
      if (type === '价格') {
        if (index > 0) {
          const previousPrice = Number(this.form.priceRangList[index - 1].goodsPrice);
          if (value <= previousPrice) {
            this.$message.error('价格必须大于上一行的价格');
            this.form.priceRangList[index].goodsPrice = undefined;
          }
        } else if (this.form.priceRangList.length > 1) {
          const nextPrice = Number(this.form.priceRangList[index + 1].goodsPrice);
          if (value > nextPrice) {
            this.$message.error('价格必须小于下一行的价格');
            this.form.priceRangList[index].goodsPrice = undefined;
          }
        }
      }
    },
    addtTableProps() {
      // 检查每行数据的起订量和价格是否已经填写
      for (let i = 0; i < this.form.priceRangList.length; i++) {
        const row = this.form.priceRangList[i];
        if (!row.goodsNum || !row.goodsPrice) {
          this.$message.error('所有行的起订量和价格必须填写完整');
          return;
        }
      }
      // 如果所有必填项都已填写，新增一行
      this.form.priceRangList.push({ goodsNum: undefined, goodsPrice: undefined });
    },
    async init(val) {
      const params = {
        isSupplierId: val
      }
      const { data, success } = await getAlbbSupplierExhibitionlist(params)
      if (success) {
        this.supplierList = []
        this.supplierList = data.map(item => {
          return { value: item.supplierId, label: item.nameManufacturer }
        })
        if (val == 1) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.form.supplierId = this.supplierList && this.supplierList.length == 1 ? this.supplierList[0].value.toString() : this.form.supplierId
            }, 100)
          })
        }
      }
    },
    changeCompetitivePower(e) {
      this.form.counterpartyProCode = null
      this.form.counterpartyPrice = undefined
    },
    changePriceControl(e) {
      this.form.minimumSellingControlPrice = undefined
    },
    changeMinimumSellingControlPrice(e) {
      this.form.smallProgramLatestPrice = decimal(decimal(this.form.minimumSellingPrice, 1.2, 4, '*'),1.4,4,'+')
    },
    async submit() {
      await this.$nextTick()
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let flag = true
          this.form.priceRangList.forEach((item, index) => {
            if (!item.goodsNum || !item.goodsPrice) {
              flag = false
              this.$message.error(`第${index + 1}行的信息未填写完整`)
            }
          })
          if (!flag) return
          this.editloading = true
          if (this.form.zizhiImage.length && this.form.zizhiImage.length > 0) {
            this.form.isZizhi = 1
          } else {
            this.form.isZizhi = null
          }
          if (this.form.zhuanliImage.length && this.form.zhuanliImage.length > 0) {
            this.form.isZhuanli = 1
          } else {
            this.form.isZhuanli = null
          }
          const params = {
            ...this.form,
            image: this.form.image.map(item => item.url).filter(Boolean).join(','),
            zizhiImage: this.form.zizhiImage.map(item => item.url).filter(Boolean).join(','),
            zhuanliImage: this.form.zhuanliImage.map(item => item.url).filter(Boolean).join(','),
            shuXing: this.form.shuXing ? this.form.shuXing.join(',') : '',
            deptId: this.form.deptId === null || this.form.deptId === undefined ? 0 : Number(this.form.deptId)
          }

          //批量编辑
          if (this.isEditPi) {
            this.$confirm('是否执行批量操作?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              this.batchloading = true
              let ids = this.selectList.map(item => item.id);
              let params = {
                ...this.form,
                ids: ids,
                image: this.form.image.length > 0 ? this.form.image.map((item) => item.url).join(',') : '',
                zhuanliImage: this.form.zhuanliImage.length > 0 ? this.form.zhuanliImage.map((item) => item.url).join(',') : '',
                zizhiImage: this.form.zizhiImage.length > 0 ? this.form.zizhiImage.map((item) => item.url).join(',') : '',
                shuXingstr: this.form.shuXing ? this.form.shuXing.join(',') : '',
              }
              delete params.shuXing
              const { success, data } = await batchUpdateSampleGoodsToOperateId({ ...params, batchType: 2 })
              this.batchloading = false
              this.editloading = false
              if (success) {
                if (data.success) {
                  this.$message.success('操作成功')
                  this.logVisible = false
                  this.batchOperationVisible = false
                  this.getList()
                } else {
                  this.$message.error(data.msg)
                }
              } else {
                this.$message.error('设置失败')
              }
            }).catch(() => {
              this.batchloading = false
              this.editloading = false;
            });
            return;
          }


          const { success, data } = await updateSampleGoods(params)
          this.editloading = false
          if (success) {
            if (data.success) {
              this.$message.success('操作成功')
              this.logVisible = false
              this.getList()
            } else {
              this.$message.error(data.msg)
            }
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    beforeUpload(data, type) {
      if (data.length > 0) {
        const target = type == 'zizhiImage' ? 'zizhiImage' : type == 'zhuanliImage' ? 'zhuanliImage' : 'image';
        this.form[target] = this.form[target].concat(
          data.map(item => ({
            url: item.url,
            name: item.name
          }))
        );
      }
    },
    getImg(data, type) {
      if (data) {
        const target = type == 'zizhiImage' ? 'zizhiImage' : type == 'zhuanliImage' ? 'zhuanliImage' : 'image';
        this.form[target] = data.map(item => ({
          url: item.url,
          name: item.fileName
        }));
      }
    },
    handleEditBefore(row) {
      this.isEditPi = false;
      this.handleEdit(row);
    },
    async handleEdit(row) {
      this.loading = true
      // 使用对象解构缓存常用属性
      const {
        id,
        proCode,
        image = [],
        zizhiImage = [],
        zhuanliImage = [],
        deptId,
        operateName,
        deptName,
        operateId,
        ...restRow
      } = row;
      // 合并表单初始化操作
      this.form = {
        ...restRow,
        id: id || null,
        image: image.length ? JSON.parse(image) : [],
        zizhiImage: zizhiImage.length ? JSON.parse(zizhiImage) : [],
        zhuanliImage: zhuanliImage.length ? JSON.parse(zhuanliImage) : [],
        deptId: deptId === "0" ? null : Number(deptId) || null,
        deptName: deptName || null,
        createdUserNameRead: row.createdUserNameRead || null,
        // 使用短路运算简化数字字段处理
        minimumSellingPrice: row.minimumSellingPrice || undefined,
        dailySpot: row.dailySpot || undefined,
        averageDailyCapacity: row.averageDailyCapacity || undefined,
        counterpartyPrice: row.counterpartyPrice || undefined,
        minimumSellingControlPrice: row.minimumSellingControlPrice || undefined,
        daFaPriceNoExpress: row.daFaPriceNoExpress || undefined,
        minimumOrderQuantity: row.minimumOrderQuantity || undefined,
        weight: row.weight || undefined,
        boxGaugeHeight: row.boxGaugeHeight || undefined,
        boxGaugeWidth: row.boxGaugeWidth || undefined,
        boxGaugeLength: row.boxGaugeLength || undefined,
        netWeight: row.netWeight || undefined,
        issueUnitPriceHasExpress: row.issueUnitPriceHasExpress || undefined,
        issueUnitPriceHasExpressToMe: row.issueUnitPriceHasExpressToMe || undefined,
        salePrice: row.salePrice || undefined,
        saleProce1688: row.saleProce1688 || undefined,
        smallProgramLatestPrice: row.smallProgramLatestPrice || undefined,
        supplierId: row.supplierId?.toString() || null,
        operateId: operateId || null,
        proCode: proCode || null,
        batchType: this.batchType,
        isAuthoPP: row.isAuthoPP || undefined,
        shuXing: row.shuXing ? row.shuXing.split(',') : [],
      };

      // 并行执行异步请求
      const [priceRes, imagesRes] = await Promise.all([
        this.isEditPi ? '' : getSampleGoodPriceRangList({ id }),
        getSampleProCodeImages({ proCode })
      ]);
      // 处理价格范围数据
      this.form.priceRangList = priceRes.data?.list || [];
      // 优化图片数据处理管道
      this.pictureData = (imagesRes.data?.list || [])
        .flatMap(item => {
          try {
            const parsed = JSON.parse(item.images || '[]');
            return Array.isArray(parsed)
              ? parsed.map(img => img.url)
              : [];
          } catch {
            return (item.images || '')
              .split(',')
              .map(url => url.trim());
          }
        })
        .filter(Boolean);
      // 批量执行初始化操作
      await this.$nextTick(async () => {
        this.isEdit = true;
        // await this.init(null);
        // 重置状态使用对象批量赋值
        Object.assign(this, {
          contactPersonList: [],
          loading: false,
          logVisible: true
        });
        this.batchType = null
        this.editloading = false;
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate()
        })
        // 并行执行搜索操作
        await Promise.all([
          // deptName && this.searchReferrer(deptName, 2)
        ]);
        // 权限校验后置处理
        this.checkAuthority(this.loginMessage.fullName, this.loginMessage.roles);
      });
    },
    verifyonMethod(row) {
      return !(row.operateId && row.operateId !== this.current.operateId && row.operateName && row.operateName !== this.current.operateName);
    },
    handleAddBedore() {
      this.isEditPi = false;
      this.handleAdd();
    },
    async handleAdd() {
      // 初始化供应商列表
      await this.init(1)
      // 等待DOM更新后执行表单重置
      await this.$nextTick()
      // 重置编辑状态
      this.isEdit = false
      // 重置表单对象,使用默认值初始化所有字段
      const defaultForm = {
        productName: null,//商品名称
        goodsCode: null,//商品编号
        yhGoodsCode: null,//公司商品编码
        gysGoodsCode: null,//供应商商品编码
        image: [],//产品图片
        oppositeLink: null,//供应商1688商品链接
        competitorLink: undefined,//1688选品中心链接
        origin: null,//产地
        articleNumber: null,//货号
        material: null,//材质
        packagingSpecification: null,//包装
        boxSpecification: null,//标准装箱数量
        dailySpot: undefined,//日均现货
        averageDailyCapacity: undefined,//日均产能
        zizhiImage: [],//资质/
        zhuanliImage: [],//专利
        //price: 1,//价格系数
        isPriceControl: null,//是否控价
        minimumSellingPrice: undefined,//供货成本价
        isCompetitivePower: null,//是否竞争力
        boothInformation: null,//展位
        hotLink: null,//热销链接
        priceRangList: [],//价格、起订量
        id: null,
        supplierId: null,//供应商
        counterpartyProCode: null,//对手链接
        counterpartyPrice: undefined,//对手价格
        minimumSellingControlPrice: undefined,//最低控价价格
        isZhuanli: null,//是否转让
        isZizhi: null,//是否资质
        expressNo: null,//快递单号
        colour: null,//颜色
        size: null,//产品尺寸(长*宽*高cm)
        yhContactPerson: null,//对接人
        operateId: null,//选品中心链接运营
        isCompetitivePowerH5: null,//竞争力
        deptId: null,//对接人架构
        styleCode: null,//款式名称
        weight: undefined,//产品重量
        catroyType: null,//类目
        supplierSource: null,//供应商来源
        daFaPriceNoExpress: undefined,//供应商1688链接一件代发售价(包邮)
        minimumOrderQuantity: 1,//起订量
        isDaiFa: null,//是否能代发
        daDanPingTai: null,//打单平台
        issueUnitPriceHasExpress: undefined,//单件运费
        issueUnitPriceHasExpressToMe: undefined,//供应商供货一件代发价格(包邮)
        salePrice: undefined,//售价
        boxGaugeLength: undefined,//箱规(长)
        boxGaugeWidth: undefined,//箱规(宽)
        boxGaugeHeight: undefined,//箱规(高)
        isMadeInChina: null,//是否有[MADEINCHINA]标识
        netWeight: undefined,//净重(g)
        saleProce1688: undefined,//供应商1688链接售价
        smallProgramLatestPrice: undefined,//H5小程序售价
        isHasProductionAddress: null,//产品包装是否有生产地址信息
        standardPackingSize: null,//标准装箱尺寸(长*宽*高cm)
        isAuthoPP:null,//是否授权（品牌、ip、专利）
        shuXing: [],//属性
      }
      // 使用Object.assign完全重置form对象
      this.form = Object.assign({}, defaultForm)
      // 重置其他相关数据
      this.pictureData = []
      this.contactPersonList = []
      this.logVisible = true
      this.verification = true
      // 确保表单验证器被重置
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.resetFields()
        this.$refs.ruleForm.clearValidate()
      }
      // 重置所有表单组件的状态
      const formComponents = this.$refs.ruleForm?.$children || []
      formComponents.forEach(component => {
        if (component.resetField) {
          component.resetField()
        }
        if (component.reset) {
          component.reset()
        }
      })
      // 如果只有一个供应商则自动选中
      if (this.supplierList?.length === 1) {
        this.form.supplierId = this.supplierList[0].value.toString()
      }
      this.$forceUpdate()
    },
    async onClearMthod() {
      await this.init(null)
    },
    downLoadFile() {
      window.open("../../../static/excel/1688选品中心供应商导入模版.xlsx", "_self");
    },
    async uploadFile(data) {
      this.file = data.file
    },
    async sumbit() {
      //没有时间就提示
      if (this.file == null) return this.$message.error('请上传文件')
      this.$message.info('正在导入中,请稍后...')
      const form = new FormData();
      form.append("upfile", this.file);
      this.importLoading = true
      await ImportSampleGoods(form).then(({ success, data }) => {
        if (success) {
          this.$message.success(data)
          this.importVisible = false
          this.getList()
        }
        this.importLoading = false
      }).catch(err => {
        this.importLoading = false
        this.$message.error('导入失败')
      })
    },
    importProps() {
      this.fileList = []
      this.file = null
      this.importVisible = true
    },
    removeFile(file, fileList) {
      this.file = null
    },
    validateField(value, fieldName) {
      const items = value.split(',')
        .map(item => item.trim())
        .filter(item => item !== '');
      // 单值校验（无逗号分隔）
      if (items.length === 1 && value.indexOf(',') === -1) {
        if (value.length > 50) {
          return {
            valid: false,
            message: `${fieldName} 长度超过50字符限制（当前：${value.length}）`
          };
        }
        return { valid: true };
      }
      // 多值校验
      const invalidItems = items
        .map((item, index) => ({ item, index: index + 1 }))
        .filter(({ item }) => item.length > 50);
      if (invalidItems.length > 0) {
        const errorList = invalidItems.map(({ item, index }) =>
          `第${index}项【${item}】(${item.length}字符)`
        ).join('，');
        return {
          valid: false,
          message: `${fieldName}中存在超长内容：${errorList}`
        };
      }
      return { valid: true };
    },
    async changeTime(e) {
      this.ListInfo.createdTimeStart = e ? e[0] : null
      this.ListInfo.createdTimeEnd = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      const validationFields = [
        { key: 'productName', name: '商品名称' },
        { key: 'goodsCode', name: '商品编号' },
        { key: 'yhGoodsCode', name: '公司商品编码' },
        { key: 'supplierSource', name: '供应商来源' },
        { key: 'daDanPingTai', name: '打单平台' },
        { key: 'styleCode', name: '款式名称' }
      ];
      // for (const { key, name } of validationFields) {
      //   const value = this.ListInfo[key];
      //   if (value) {
      //     const validation = this.validateField(value, name);
      //     if (!validation.valid) {
      //       this.$message.error(validation.message);
      //       return; // 终止执行
      //     }
      //   }
      // }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const params = this.prepareParams();
        const { data, success } = await getSampleGoodsList(params)
        if (success) {
          this.tableData = data.list
          this.tableData.forEach(item => {
            item.image = this.handleImage(item, 'image');
            item.zizhiImage = this.handleImage(item, 'zizhiImage');
            item.zhuanliImage = this.handleImage(item, 'zhuanliImage');
          });
          this.total = data.total
          this.selectList = []
          this.loading = false
        } else {
          //获取列表失败
          this.loading = false
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.loading = false
      }
    },
    handleImage(item, field) {
      const images = item[field];
      if (images) {
        return JSON.stringify(images.split(',').map(url => ({ url, name: '' })));
      }
      return JSON.stringify([]);
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    // 获取普通商品资料
    onGetOrdinaryGoodsData() {
      this.ordinaryGoodsVisible = true;
      // 初始化空列表
      this.ordinaryGoodsList = [];
      // 重置分页
      this.ordinaryGoodsPageInfo.currentPage = 1;
    },

    // 打开选择商品编码对话框
    async openSelectGoodsCodeDialog() {
      this.selectGoodsCodeVisible = true;
      this.selectedGoods = [];
      this.selectGoodsList = []; // 清空现有列表
      this.selectGoodsPageInfo.currentPage = 1; // 重置页码
      this.canceledGoodsCodes = []; // 重置取消选择的记录

      // 清空查询条件
      this.goodsCodeQuery = '';
      this.goodsStyleCodeQuery = '';
      this.goodsGroupQuery = null;
      this.goodsBrandQuery = null;
      this.goodsNameQuery = '';
      // 重置排序设置
      this.selectGoodsSortInfo = {
        orderBy: 'modified',
        isAsc: false
      };

      // 初始化运营组和采购组下拉选项
      if (this.goodsGroupOptions.length === 0) {
        await this.loadGoodsGroupOptions();
      }
      if (this.goodsBrandOptions.length === 0) {
        await this.loadGoodsBrandOptions();
      }

      // 记录已选商品的编码，用于后续自动选中
      this.existingGoodsCodes = this.ordinaryGoodsList.map(item => item.goodsCode);

      this.searchGoodsCode(); // 加载商品数据
    },

    // 加载运营组下拉选项
    async loadGoodsGroupOptions() {
      try {
        const res = await getGroupKeyValue({});
        if (res && res.data) {
          this.goodsGroupOptions = res.data;
        }
      } catch (error) {
        console.error('获取运营组选项失败', error);
      }
    },

    // 加载采购组下拉选项
    async loadGoodsBrandOptions() {
      try {
        const res = await getAllProBrand();
        if (res && res.success && res.data) {
          this.goodsBrandOptions = res.data;
        }
      } catch (error) {
        console.error('获取采购组选项失败', error);
      }
    },

    // 搜索商品编码
    searchGoodsCode() {
      //this.selectGoodsPageInfo.currentPage=1;
      // 使用API获取商品列表
      const params = {
        currentPage: this.selectGoodsPageInfo.currentPage,
        pageSize: this.selectGoodsPageInfo.pageSize,
        goodsCode: this.goodsCodeQuery,
        goodsNameList: this.goodsNameQuery,
        styleCode: this.goodsStyleCodeQuery,
        groupId: this.goodsGroupQuery,
        brandId: this.goodsBrandQuery,
        isEnabled: 1, // 默认只查询启用的商品
        orderBy: this.selectGoodsSortInfo.orderBy,
        isAsc: this.selectGoodsSortInfo.isAsc
      };


      this.$loading({ lock: true, text: '加载中...' });

      getList(params)
        .then(res => {
          if (res.code === 200 || res.success) {
            const data = res.data && res.data.list ? res.data.list : [];
            // 处理数据，确保包含所需字段
            this.selectGoodsList = data.map(item => ({
              ...item,
              image: item.pictureBig || item.image,
              goodsCode: item.goodsCode,
              goodsName: item.goodsName,
              styleCode: item.styleCode,
              cost: item.costPrice,
              operateGroup: item.groupName,
              purchaseGroup: item.brandName
            }));
            this.selectGoodsPageInfo.total = res.data.total || 0;

            // 根据已选商品自动选中对应的行
            this.$nextTick(() => {
              if (this.existingGoodsCodes && this.existingGoodsCodes.length > 0) {
                // 获取表格实例
                const tableRef = this.$refs.selectGoodsTable;
                if (tableRef) {
                  // 遍历商品列表，选中已存在的商品
                  this.selectGoodsList.forEach(row => {
                    if (this.existingGoodsCodes.includes(row.goodsCode)) {
                      tableRef.toggleRowSelection(row, true);
                    }
                  });
                }
              }
            });
          } else {
            this.$message.error(res.msg || '获取商品列表失败');
          }
        })
        .catch(err => {
          console.error('获取商品列表失败', err);
          this.$message.error('获取商品列表失败');
        })
        .finally(() => {
          this.$loading().close();
        });
    },

    // 商品列表页码变更
    handleGoodsPageChange(page) {
      this.selectGoodsPageInfo.currentPage = page;
      this.searchGoodsCode();
    },

    // 商品列表每页数量变更
    handleGoodsSizeChange(size) {
      this.selectGoodsPageInfo.pageSize = size;
      this.selectGoodsPageInfo.currentPage = 1;
      this.searchGoodsCode();
    },

    // 处理商品选择变化
    handleSelectionChange(selection) {
      // 跟踪已存在于ordinaryGoodsList中但在当前选择中被取消的商品
      this.canceledGoodsCodes = [];

      // 检查当前页面上存在于ordinaryGoodsList中的商品是否被取消选择
      const currentPageGoodsCodes = this.selectGoodsList.map(item => item.goodsCode);

      // 当前页上的商品编码与已有普通商品的交集
      const intersectionCodes = currentPageGoodsCodes.filter(code =>
        this.existingGoodsCodes.includes(code)
      );

      // 找出已有但在selection中被取消的
      intersectionCodes.forEach(code => {
        // 如果在交集中但不在当前selection中，说明被取消了
        if (!selection.some(item => item.goodsCode === code)) {
          this.canceledGoodsCodes.push(code);
        }
      });

      // 更新选中的商品列表
      const currentSelectedGoodsCodes = new Set(this.selectedGoods.map(item => item.goodsCode));

      // 将之前没有的新选择添加到已选列表中
      selection.forEach(item => {
        if (!currentSelectedGoodsCodes.has(item.goodsCode)) {
          this.selectedGoods.push(item);
        }
      });

      // 如果之前选择的某项现在不在selection中，说明被取消选择，需要从selectedGoods中移除
      this.selectedGoods = this.selectedGoods.filter(item =>
        // 当前页面上的商品：如果在selection中保留，不在selection中移除
        this.selectGoodsList.some(listItem => listItem.goodsCode === item.goodsCode)
          ? selection.some(selItem => selItem.goodsCode === item.goodsCode)
          // 不在当前页面上的商品：保持原样
          : true
      );

      console.log('被取消选择的商品编码:', this.canceledGoodsCodes);
    },

    // 确认选择商品
    confirmSelectGoods() {
      if (this.selectedGoods.length === 0 && !this.canceledGoodsCodes.length) {
        this.$message({
          message: '请至少选择一个商品',
          type: 'warning'
        });
        return;
      }

      // 先处理取消选择的商品，将其从ordinaryGoodsList中移除
      if (this.canceledGoodsCodes && this.canceledGoodsCodes.length > 0) {
        // 从普通商品列表中过滤掉被取消选择的商品
        this.ordinaryGoodsList = this.ordinaryGoodsList.filter(item =>
          !this.canceledGoodsCodes.includes(item.goodsCode)
        );

        if (this.canceledGoodsCodes.length > 0) {
          this.$message({
            message: `已移除${this.canceledGoodsCodes.length}个取消选择的商品`,
            type: 'info'
          });
        }
      }

      // 将选中的商品添加到普通商品资料列表中，避免重复添加
      // 使用商品编码(goodsCode)作为唯一标识进行去重
      const existingGoodsCodes = new Set(this.ordinaryGoodsList.map(item => item.goodsCode));

      let newGoods = [];
      let duplicateCount = 0;

      this.selectedGoods.forEach(item => {
        if (!existingGoodsCodes.has(item.goodsCode)) {
          newGoods.push(item);
          existingGoodsCodes.add(item.goodsCode);
        } else {
          duplicateCount++;
        }
      });

      // 将不重复的商品添加到列表中
      this.ordinaryGoodsList = [...this.ordinaryGoodsList, ...newGoods];

      // 清空取消选择记录
      this.canceledGoodsCodes = [];

      this.selectGoodsCodeVisible = false;
    },

    // 删除普通商品
    deleteOrdinaryGoods(index) {
      // 计算实际索引
      const actualIndex = (this.ordinaryGoodsPageInfo.currentPage - 1) * this.ordinaryGoodsPageInfo.pageSize + index;
      this.ordinaryGoodsList.splice(actualIndex, 1);

      // 检查当前页是否还有数据，如果没有且不是第一页，则返回上一页
      const totalPages = Math.ceil(this.ordinaryGoodsList.length / this.ordinaryGoodsPageInfo.pageSize);
      if (this.ordinaryGoodsPageInfo.currentPage > totalPages && this.ordinaryGoodsPageInfo.currentPage > 1) {
        this.ordinaryGoodsPageInfo.currentPage = totalPages || 1;
      }
    },

    // 提交普通商品资料
    submitOrdinaryGoods() {
      if (this.ordinaryGoodsList.length === 0) {
        this.$message({
          message: '请至少添加一个商品',
          type: 'warning'
        });
        return;
      }

      // 构建提交数据
      const submitSampleData = this.ordinaryGoodsList.map(item => ({
        // 基本商品信息 - 必填
        goodsCode: '',
        styleCode: item.styleCode || '',
        productName: item.goodsName || '',

        // 供应商信息
        supplierId: '1916309602146209792',
        nameManufacturer: item.nameManufacturer || '',

        // 商品编码相关
        yhGoodsCode: item.goodsCode|| null,
        gysGoodsCode: item.gysGoodsCode || null,
        proCode: item.proCode || null,

        // 链接信息
        oppositeLink: item.oppositeLink || '',
        competitorLink: item.competitorLink || null,
        hotLink: item.hotLink || null,

        // 产品属性
        shuXing: null,
        origin: item.origin || '',
        articleNumber: item.articleNumber || '',
        material: item.material || '',
        colour: item.colour || '',
        size: item.size || '',
        weight: item.weight || 9,

        // 包装信息
        packagingSpecification: item.packagingSpecification || '',
        boxSpecification: item.packCount || '9',
        standardPackingSize: item.standardPackingSize || '',
        isHasProductionAddress: null,

        // 供应信息
        dailySpot: 0,
        averageDailyCapacity: item.averageDailyCapacity || 0,
        minimumOrderQuantity: item.minimumOrderQuantity || 0,

        // 价格相关
        price: item.price || 0,
        isPriceControl: null,
        minimumSellingPrice: item.costPrice || 0,
        saleProce1688: item.saleProce1688 || 0,
        smallProgramLatestPrice: item.smallProgramLatestPrice || 0,
        issueUnitPriceHasExpress: item.issueUnitPriceHasExpress || 0,
        issueUnitPriceHasExpressToMe: item.issueUnitPriceHasExpressToMe || 0,

        // 竞争力相关
        isCompetitivePower: null,
        isCompetitivePowerH5: item.isCompetitivePowerH5 || null,
        isCompetitivePowerH5String: item.isCompetitivePowerH5String || '',
        competitivePowerH5Price: item.competitivePowerH5Price || null,

        // 知识产权相关
        isZhuanli: item.isZhuanli || null,
        isZiZhi: item.isZiZhi || null,
        isZizhi: item.isZizhi || null,
        isAuthoPP: item.isAuthoPP || '',
        isMadeInChina: item.isMadeInChina || null,

        // 联系人信息
        contactPerson: item.contactPerson || '',
        contactInformation: item.contactInformation || '',
        yhContactPerson: item.yhContactPerson || '',

        // 展位信息
        boothInformation: item.boothInformation || null,

        // 平台对接信息
        isDuiJieDaDanPingtai: item.isDuiJieDaDanPingtai || 0,
        daDanPing1: item.daDanPing1 || '',
        daDanPing2: item.daDanPing2 || '',
        isAllDuiJieDaDanPingtai: item.isAllDuiJieDaDanPingtai || 0,
        isDaiFa: null,
        daDanPingTai: item.daDanPingTai || '',
        housekeeperAccountNumber: item.housekeeperAccountNumber || null,

        // 其它信息
        isUse: item.isUse || 0,
        isDaYin: item.isDaYin || null,
        expressNo: item.expressNo || null,
        spId: item.spId || 0,

        // 部门信息
        //deptId: item.deptId || 0,
        //deptName: item.deptName || null,

        // 创建人信息
        //createdUserName: item.createdUserName || this.current.operateName || '',
        //createdUserNameRead: item.createdUserNameRead || this.current.operateName || '',
        //operateId: null,

        // 来源信息
        supplierSource: item.supplierSource || '',
        catroyType: item.catroyType || null,

        // 内部属性
        //createdTime: item.createdTime || dayjs().format('YYYY-MM-DD HH:mm:ss'),
        priceRangList: item.priceRangList || [],
        counterpartyProCode: item.counterpartyProCode || null,
        batchType: item.batchType || null,
        children: item.children || null,

        // 图片相关
        image: item.image || '',
        zizhiImage: item.zizhiImage || '',
        zhuanliImage: item.zhuanliImage || '',
      }));

      this.$loading({ lock: true, text: '保存中...' });

      // 调用API提交商品数据
      saveSampleGoods(submitSampleData)
        .then(res => {
          if (res?.success) {
            this.$message({
              message: '保存成功',
              type: 'success'
            });
            this.ordinaryGoodsVisible = false;
            this.getList();
          } else {
            this.$message.error(res.msg || '保存失败');
          }
        })
        .catch(err => {
          console.error('保存普通商品资料失败', err);
          this.$message.error('保存失败');
        })
        .finally(() => {
          this.$loading().close();
        });
    },
    // 表格排序变更处理
    handleGoodsSortChange(column) {
      if (!column.order) {
        this.selectGoodsSortInfo = { orderBy: 'modified', isAsc: false };
      } else {
        // 根据后端API要求可能需要调整参数名称
        // 转换为后端期望的参数格式
        this.selectGoodsSortInfo = {
          orderBy: column.prop, // 使用prop而不是property，确保与后端字段匹配
          isAsc: column.order === 'ascending'
        };

        // 为运营组排序添加groupId参数
        if (column.prop === 'operateGroup') {
          this.selectGoodsSortInfo.orderBy = 'groupId';
        }

        // 为采购组排序添加brandId参数
        if (column.prop === 'purchaseGroup') {
          this.selectGoodsSortInfo.orderBy = 'brandId';
        }

        console.log('排序变更:', column, '处理后的排序参数:', this.selectGoodsSortInfo);
      }
      this.searchGoodsCode();
    },
    handleOrdinaryGoodsPageChange(page) {
      this.ordinaryGoodsPageInfo.currentPage = page;
    },
    handleOrdinaryGoodsSizeChange(size) {
      this.ordinaryGoodsPageInfo.pageSize = size;
      this.ordinaryGoodsPageInfo.currentPage = 1;
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 146px;
    // margin: 0 1px 5px 0px;
    margin-bottom: 5px;
    margin-left: 5px;
  }
}

.btnGroup {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}

.logForm ::v-deep .el-input__inner {
  text-align: left;
}


/* 添加样式 */
#printid {
  page-break-inside: avoid;

  .qrcode-item {
    display: flex;
    flex-direction: column;
    // align-items: center;
    page-break-inside: avoid;
    // margin-top: 5px;
  }

  .qrcode-id {
    font-size: 9px;
    display: flex;
    justify-content: center;
  }
}

.qrcode-canvas {
  display: block;
  max-width: 100%;
  /* 防止溢出 */
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 1px;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}

.encoding-container {
  padding: 20px;
  min-height: 120px;
}

.encoding-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.encoding-label {
  min-width: 120px;
  text-align: right;
  padding-right: 12px;
  color: #606266;
}

.encoding-input {
  flex: 1;
  max-width: 300px;
}

.encoding-select {
  flex: 1;
  max-width: 300px;
}

.select-goods-code-content {
  max-height: 85vh;
  overflow-y: auto;
}

.ordinary-goods-content {
  max-height: 85vh;
  overflow-y: auto;
}

/* 为表格设置紧凑样式 */
.select-goods-code-content ::v-deep .el-table {
  font-size: 13px;
}

.select-goods-code-content ::v-deep .el-table td,
.select-goods-code-content ::v-deep .el-table th {
  padding: 5px 0;
}
.select-goods-code-content .goodsimg{
  padding:0 0;
}

/* 为图片单元格设置样式 */
::v-deep .goods-img-cell {
  padding: 0 !important;
}

::v-deep .goods-img-cell .cell {
  padding: 0 !important;
  height: 40px;
  line-height: 40px;
}

// ::v-deep .el-table .goods-img-cell .el-image {
//   height: 50px;
//   width: 50px;
//   display: block;
//   margin: 0;
// }

::v-deep .ordinary-goods-content .goods-img-cell,
::v-deep .select-goods-code-content .goods-img-cell {
  padding: 0 !important;
}

// ::v-deep .ordinary-goods-content .goods-img-cell .cell,
// ::v-deep .select-goods-code-content .goods-img-cell .cell {
//   padding: 0 !important;
//   height: 50px;
//   line-height: 50px;
// }
</style>
