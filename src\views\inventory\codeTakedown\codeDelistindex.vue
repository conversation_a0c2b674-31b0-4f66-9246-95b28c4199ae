<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="汇总" name="first1" style="height: 99%" v-if="checkPermission('viewGoodsBanSummary')">
        <codeDelistCollect ref="refcodeDelistCollect" @ChangeActiveName="ChangeActiveName" />
      </el-tab-pane>
      <el-tab-pane label="通知下架" name="first2" style="height: 99%">
        <codeNoticeCollect ref="refcodeNoticeCollect" />
      </el-tab-pane>
      <el-tab-pane label="明细" name="first3" style="height: 99%" lazy v-if="checkPermission('viewGoodsBanDetail')">
        <codeDelistDetail ref="refcodeDelistDetail" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import codeDelistCollect from "./codeDelistCollect.vue";
import codeNoticeCollect from "./codeNoticeCollect.vue";
import codeDelistDetail from "./codeDelistDetail.vue";
export default {
  name: "codeDelistindex",
  components: {
    MyContainer, codeDelistCollect, codeNoticeCollect, codeDelistDetail
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {
    ChangeActiveName(row) {
      this.activeName = "first2";
      this.$nextTick(() => {
        this.$refs.refcodeNoticeCollect.Jumpinit(row);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
