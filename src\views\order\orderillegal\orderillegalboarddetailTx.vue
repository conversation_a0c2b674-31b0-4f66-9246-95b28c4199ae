<template>
    <my-container>
        <template #header>
          <el-form class="ad-form-query" :inline="true" :model="filter1" @submit.native.prevent>      
                <el-form-item label="日期:">
                    <el-date-picker style="width:240px" v-model="filter1.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                    start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>            
          </el-form>
        </template>   
        <el-row :gutter="0" class="row-condition">
          <el-col :span="2" :offset="1">
            <div class="my-title">数据概况</div>
          </el-col>
        </el-row>    

          <el-row style="margin-top: 30px">
          <el-col :span="3" :offset="1" >
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">总扣款 
                <el-tooltip class="item" effect="dark" content="总扣款" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.sumAmountPaid">
                <span class="canclick" @click="onShowDetail(0)">{{dataSum.sumAmountPaid}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">缺货 
                <el-tooltip class="item" effect="dark" content="缺货" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.stockout">
                <span class="canclick" @click="onShowDetail(1)">{{dataSum.stockout}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">虚假轨迹 
                <el-tooltip class="item" effect="dark" content="虚假轨迹" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.falseTrack">
                <span class="canclick" @click="onShowDetail(2)">{{dataSum.falseTrack}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">延迟发货 
                <el-tooltip class="item" effect="dark" content="延迟发货" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text"  v-loading="loadingDataSum.falsedelay">
                <span class="canclick" @click="onShowDetail(3)">{{dataSum.falsedelay}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">其他扣款 
                <el-tooltip class="item" effect="dark" content="其他扣款" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text"  v-loading="loadingDataSum.otherwithhold">
                <span class="canclick" @click="onShowDetail(7)">{{dataSum.otherwithhold}}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>  

        <el-row :gutter="0" class="row-condition">
          <el-col :span="2" :offset="1">
            <div class="my-title">运营组</div>
          </el-col>
        </el-row>

        <el-row style="margin-top: 30px">
          <el-col :span="3" :offset="1" v-for="item in groupSumList" :key="item.groupId">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header" v-loading="loadingDataSum.groupName">{{item.groupName}} 
                <el-tooltip class="item" effect="dark" content="未知" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.groupSumAmountPaid" >
                <span class="canclick" @click="onShowDetailGroup(item.groupId)">{{item.groupSumAmountPaid}}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="0" class="row-condition">
          <el-col :span="2" :offset="1">
            <div class="my-title">发货仓库</div>
          </el-col>
        </el-row> 

        <el-row style="margin-top: 30px">
          <el-col :span="3" :offset="1" >
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">未知  
                <el-tooltip class="item" effect="dark" content="未知" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid1">
                <span class="canclick" @click="onShowDetailWarehouse(0)">{{dataSum.WarehouseAmountPaid1}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1" >
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">（本仓）  
                <el-tooltip class="item" effect="dark" content="（本仓）" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid2">
                <span class="canclick" @click="onShowDetailWarehouse(1)">{{dataSum.WarehouseAmountPaid2}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">南昌昌东
                <el-tooltip class="item" effect="dark" content="南昌昌东" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid4">
                <span class="canclick" @click="onShowDetailWarehouse(3)">{{dataSum.WarehouseAmountPaid4}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">安徽 
                <el-tooltip class="item" effect="dark" content="罗兵安徽仓" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid5">
                <span class="canclick" @click="onShowDetailWarehouse(7)">{{dataSum.WarehouseAmountPaid5}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">上海 
                <el-tooltip class="item" effect="dark" content="上海暖锦贸易有限公司" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid6">
                <span class="canclick" @click="onShowDetailWarehouse(8)">{{dataSum.WarehouseAmountPaid6}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">
                南昌定制仓
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="南昌定制仓"
                  placement="top-end"
                  ><span><i class="el-icon-question"></i></span
                ></el-tooltip>
              </div>
              <div
                class="grid-text"
                v-loading="loadingDataSum.WarehouseAmountPaid12"
              >
              金额
                <span class="canclick" @click="onShowDetailWarehouse(12)">{{
                  dataSum.WarehouseAmountPaid12
                }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="0" class="row-condition">
          <el-col :span="2" :offset="1">
            <div class="my-title">快递公司</div>
          </el-col>
        </el-row>

        <el-row style="margin-top: 30px">
          <el-col :span="3" :offset="1" >
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">（义乌）圆通(返0.1) 
                <el-tooltip class="item" effect="dark" content="圆通(返0.1)" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid1">
                <span class="canclick" @click="onShowDetailCompany(1)">{{dataSum.ExpressAmountPaid1}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">（义乌）申通 
                <el-tooltip class="item" effect="dark" content="申通" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid2">
                <span class="canclick" @click="onShowDetailCompany(2)">{{dataSum.ExpressAmountPaid2}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">（义乌）圆通(不返0.1) 
                <el-tooltip class="item" effect="dark" content="圆通(不返0.1)" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid3">
                <span class="canclick" @click="onShowDetailCompany(3)">{{dataSum.ExpressAmountPaid3}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">百世 
                <el-tooltip class="item" effect="dark" content="百世" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid4">
                <span class="canclick" @click="onShowDetailCompany(4)">{{dataSum.ExpressAmountPaid4}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">德邦 
                <el-tooltip class="item" effect="dark" content="德邦" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid5">
                <span class="canclick" @click="onShowDetailCompany(5)">{{dataSum.ExpressAmountPaid5}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">邮政 
                <el-tooltip class="item" effect="dark" content="邮政" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text"  v-loading="loadingDataSum.ExpressAmountPaid6">
                <span class="canclick" @click="onShowDetailCompany(6)">{{dataSum.ExpressAmountPaid6}}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>

          <el-row style="margin-top: 30px">
          <el-col :span="3" :offset="1" >
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">中通 
                <el-tooltip class="item" effect="dark" content="中通" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid7">
                <span class="canclick" @click="onShowDetailCompany(7)">{{dataSum.ExpressAmountPaid7}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">顺丰次晨 
                <el-tooltip class="item" effect="dark" content="顺丰次晨" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid8">
                <span class="canclick" @click="onShowDetailCompany(8)">{{dataSum.ExpressAmountPaid8}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">顺丰标快 
                <el-tooltip class="item" effect="dark" content="顺丰标快" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid9">
                <span class="canclick" @click="onShowDetailCompany(9)">{{dataSum.ExpressAmountPaid9}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">顺丰特惠 
                <el-tooltip class="item" effect="dark" content="顺丰特惠" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid10">
                <span class="canclick" @click="onShowDetailCompany(10)">{{dataSum.ExpressAmountPaid10}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">百世汇通 
                <el-tooltip class="item" effect="dark" content="百世汇通" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid11">
                <span class="canclick" @click="onShowDetailCompany(11)">{{dataSum.ExpressAmountPaid11}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">优速 
                <el-tooltip class="item" effect="dark" content="优速" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text"  v-loading="loadingDataSum.ExpressAmountPaid12">
                <span class="canclick" @click="onShowDetailCompany(12)">{{dataSum.ExpressAmountPaid12}}</span>
              </div>
            </el-card>
          </el-col>

          </el-row>
        <el-row style="margin-top: 30px">
          <el-col :span="3" :offset="1" >
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">韵达 
                <el-tooltip class="item" effect="dark" content="韵达" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid13">
                <span class="canclick" @click="onShowDetailCompany(13)">{{dataSum.ExpressAmountPaid13}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">京广 
                <el-tooltip class="item" effect="dark" content="京广" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid14">
                <span class="canclick" @click="onShowDetailCompany(14)">{{dataSum.ExpressAmountPaid14}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">速尔 
                <el-tooltip class="item" effect="dark" content="速尔" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.ExpressAmountPaid15">
                <span class="canclick" @click="onShowDetailCompany(15)">{{dataSum.ExpressAmountPaid15}}</span>
              </div>
            </el-card>
          </el-col>
          <!-- <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">顺丰特惠 
                <el-tooltip class="item" effect="dark" content="顺丰特惠" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid4">
                <span>{{dataSum.WarehouseAmountPaid4}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">百世汇通 
                <el-tooltip class="item" effect="dark" content="百世汇通" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text" v-loading="loadingDataSum.WarehouseAmountPaid5">
                <span>{{dataSum.WarehouseAmountPaid5}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '20px' }" shadow="always">
              <div class="grid-header">优速 
                <el-tooltip class="item" effect="dark" content="优速" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text"  v-loading="loadingDataSum.WarehouseAmountPaid6">
                <span>{{dataSum.WarehouseAmountPaid6}}</span>
              </div>
            </el-card>
          </el-col> -->
        </el-row>
        
    </my-container>
</template>

<script>
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { rulePlatform} from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { getWithholdTxSum, getWitholdCompanyTx as  getWitholdCompany, getWithGroupTxSum as getWithGroupSum} from "@/api/order/orderdeductmoney"
import orderIllgalsearch from './OrderIllgalsearch.vue'
export default {
    name: 'YunhanAdminOrderillegalboard',
    components: {cesTable, MyContainer, MyConfirmButton, orderIllgalsearch },
    data() {
        return {
      that:this,
      activeName: 'first',
      directorList:[],
      directorGroupList:[],
      groupSumList: [],
      filter1: {
        startDate:null,
        endDate:null,
        platform:null,
        shopId:null,
        groupId:null,
        proCode:null,
        sendWarehouse:null,
        illegalType:null,
        expressCompany:null,
        operateSpecialId:null,
        occurrenceTime:null,
        timerange:[formatTime(dayjs().subtract(7,"day"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
      },
      filter2: {
          proCode:null
      },
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
      dataSum:{
              WarehouseAmountPaid1:null,
              WarehouseAmountPaid2:null,
              WarehouseAmountPaid3:null,
              WarehouseAmountPaid4:null,
              WarehouseAmountPaid5:null,
              WarehouseAmountPaid6:null,
              WarehouseAmountPaid12:null,
              ExpressAmountPaid1:null,
              ExpressAmountPaid2:null,
              ExpressAmountPaid3:null,
              ExpressAmountPaid4:null,
              ExpressAmountPaid5:null,
              ExpressAmountPaid6:null,
              ExpressAmountPaid7:null,
              ExpressAmountPaid8:null,
              ExpressAmountPaid9:null,
              ExpressAmountPaid10:null,
              ExpressAmountPaid11:null,
              ExpressAmountPaid12:null,
              ExpressAmountPaid13:null,
              ExpressAmountPaid14:null,
              ExpressAmountPaid15:null,
              sumAmountPaid:null,
              notTransfer:null,
              stockout:null,
              falseTrack:null,
              falseCollect:null,
              falsedelay:null,
              otherwithhold:null,
              deduction1:null,
              deduction2:null,
              groupName:null,
              groupSumAmountPaid:null
            },
            loadingDataSum:{
              WarehouseAmountPaid1:false,
              WarehouseAmountPaid2:false,
              WarehouseAmountPaid3:false,
              WarehouseAmountPaid4:false,
              WarehouseAmountPaid5:false,
              WarehouseAmountPaid6:false,
              WarehouseAmountPaid12:false,
              ExpressAmountPaid1:false,
              ExpressAmountPaid2:false,
              ExpressAmountPaid3:false,
              ExpressAmountPaid4:false,
              ExpressAmountPaid5:false,
              ExpressAmountPaid6:false,
              ExpressAmountPaid7:false,
              ExpressAmountPaid8:false,
              ExpressAmountPaid9:false,
              ExpressAmountPaid10:false,
              ExpressAmountPaid11:false,
              ExpressAmountPaid12:false,
              ExpressAmountPaid13:false,
              ExpressAmountPaid14:false,
              ExpressAmountPaid15:false,
              sumAmountPaid:false,
              notTransfer:false,
              stockout:false,
              falseTrack:false,
              falseCollect:false,
              falsedelay:false,
              otherwithhold:false,
              deduction1:false,
              deduction2:false,
              groupName:false,
              groupSumAmountPaid:false,
              
            },
      platformList: [],
      dialogVisible: false,
      showDetailVisible: false,
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,     
      deleteLoading: false,
      uploadLoading:false,
      fileHasSubmit:false,
      formtitle:"新增",
      fileList:[],
      selids:[],//选择的id
        };
    },

    async mounted() {
        
    },

    methods: {
        async onSearch() {
            if (!this.filter1.timerange) {this.$message({message: "请选择日期",type: "warning",});return;}
            if (this.filter1.timerange&&this.filter1.timerange.length>1) {
                this.filter1.startDate = this.filter1.timerange[0];
                this.filter1.endDate = this.filter1.timerange[1];
            }
                this.getpinwithholdsum()
                this.getexpress()
                this.getGroupSum()
        },   
        async onSearch1(para) {
            this.filter1.timerange = para.timerange,
            this.filter1.proCode = para.proCode
            await this.onSearch()
        }, 
        async getpinwithholdsum(){      
            this.loadingDataSum.sumAmountPaid = true
            this.loadingDataSum.notTransfer = true
            this.loadingDataSum.stockout = true
            this.loadingDataSum.falseTrack = true
            this.loadingDataSum.falseCollect = true
            this.loadingDataSum.falsedelay = true
            this.loadingDataSum.otherwithhold = true
            this.loadingDataSum.deduction1 = true
            this.loadingDataSum.deduction2 = true
            this.loadingDataSum.WarehouseAmountPaid1 = true
            this.loadingDataSum.WarehouseAmountPaid2 = true
            this.loadingDataSum.WarehouseAmountPaid3 = true
            this.loadingDataSum.WarehouseAmountPaid4 = true
            this.loadingDataSum.WarehouseAmountPaid5 = true
            this.loadingDataSum.groupName = true,
            this.loadingDataSum.groupSumAmountPaid = true
            this.loadingDataSum.groupName = true
            this.loadingDataSum.groupSumAmountPaid = true
            var para = {...this.filter1,...this.filter2}
            var res = await getWithholdTxSum(this.filter1);
            if (!res?.code) {
                return false
            }
            
            this.dataSum.sumAmountPaid = res.data.sumAmountPaid
            this.dataSum.notTransfer = res.data.notTransfer
            this.dataSum.stockout = res.data.stockout
            this.dataSum.falseTrack = res.data.falseTrack
            this.dataSum.falseCollect = res.data.falseCollect
            this.dataSum.falsedelay = res.data.falsedelay
            this.dataSum.otherwithhold = res.data.otherWithhold
            this.dataSum.deduction1 = res.data.deduction1
            this.dataSum.deduction2 = res.data.deduction2
            this.dataSum.WarehouseAmountPaid1 = res.data.warehouseAmountPaid1
            this.dataSum.WarehouseAmountPaid2 = res.data.warehouseAmountPaid2
            this.dataSum.WarehouseAmountPaid3 = res.data.warehouseAmountPaid3
            this.dataSum.WarehouseAmountPaid4 = res.data.warehouseAmountPaid4
            this.dataSum.WarehouseAmountPaid5 = res.data.warehouseAmountPaid5
            this.dataSum.WarehouseAmountPaid6 = res.data.warehouseAmountPaid6
            this.dataSum.WarehouseAmountPaid12 = res.data.warehouseAmountPaid12
            this.dataSum.groupName = res.data.groupName
            this.dataSum.groupSumAmountPaid = res.data.groupSumAmountPaid

            this.loadingDataSum.sumAmountPaid = false
            this.loadingDataSum.notTransfer = false
            this.loadingDataSum.stockout = false
            this.loadingDataSum.falseTrack = false
            this.loadingDataSum.falseCollect = false
            this.loadingDataSum.falsedelay = false
            this.loadingDataSum.otherwithhold = false
            this.loadingDataSum.deduction1 = false
            this.loadingDataSum.deduction2 = false
            this.loadingDataSum.WarehouseAmountPaid1 = false
            this.loadingDataSum.WarehouseAmountPaid2 = false
            this.loadingDataSum.WarehouseAmountPaid3 = false
            this.loadingDataSum.WarehouseAmountPaid4 = false
            this.loadingDataSum.WarehouseAmountPaid5 = false
            this.loadingDataSum.WarehouseAmountPaid6 = false
            this.loadingDataSum.WarehouseAmountPaid12 = false
            this.loadingDataSum.groupName = false,
            this.loadingDataSum.groupSumAmountPaid = false
        
        },        
        async getexpress(){
            this.loadingDataSum.ExpressAmountPaid1 = true
            this.loadingDataSum.ExpressAmountPaid2 = true
            this.loadingDataSum.ExpressAmountPaid3 = true
            this.loadingDataSum.ExpressAmountPaid4 = true
            this.loadingDataSum.ExpressAmountPaid5 = true
            this.loadingDataSum.ExpressAmountPaid6 = true
            this.loadingDataSum.ExpressAmountPaid7 = true
            this.loadingDataSum.ExpressAmountPaid8 = true
            this.loadingDataSum.ExpressAmountPaid9 = true
            this.loadingDataSum.ExpressAmountPaid10 = true
            this.loadingDataSum.ExpressAmountPaid11 = true
            this.loadingDataSum.ExpressAmountPaid12 = true
            this.loadingDataSum.ExpressAmountPaid13 = true
            this.loadingDataSum.ExpressAmountPaid14 = true
            this.loadingDataSum.ExpressAmountPaid15 = true
            
            var para = {...this.filter1,...this.filter2}
            var res = await getWitholdCompany(this.filter1);
            if (!res?.code) {
                return false
            }

            this.dataSum.ExpressAmountPaid1 = res.data.expressAmountPaid1 
            this.dataSum.ExpressAmountPaid2 = res.data.expressAmountPaid2 
            this.dataSum.ExpressAmountPaid3 = res.data.expressAmountPaid3 
            this.dataSum.ExpressAmountPaid4 = res.data.expressAmountPaid4 
            this.dataSum.ExpressAmountPaid5 = res.data.expressAmountPaid5 
            this.dataSum.ExpressAmountPaid6 = res.data.expressAmountPaid6 
            this.dataSum.ExpressAmountPaid7 = res.data.expressAmountPaid7 
            this.dataSum.ExpressAmountPaid8 = res.data.expressAmountPaid8 
            this.dataSum.ExpressAmountPaid9 = res.data.expressAmountPaid9 
            this.dataSum.ExpressAmountPaid10 = res.data.expressAmountPaid10
            this.dataSum.ExpressAmountPaid11 = res.data.expressAmountPaid11
            this.dataSum.ExpressAmountPaid12 = res.data.expressAmountPaid12
            this.dataSum.ExpressAmountPaid13 = res.data.expressAmountPaid13
            this.dataSum.ExpressAmountPaid14 = res.data.expressAmountPaid14
            this.dataSum.ExpressAmountPaid15 = res.data.expressAmountPaid15


            this.loadingDataSum.ExpressAmountPaid1 = false
            this.loadingDataSum.ExpressAmountPaid2 = false
            this.loadingDataSum.ExpressAmountPaid3 = false
            this.loadingDataSum.ExpressAmountPaid4 = false
            this.loadingDataSum.ExpressAmountPaid5 = false
            this.loadingDataSum.ExpressAmountPaid6 = false
            this.loadingDataSum.ExpressAmountPaid7 = false
            this.loadingDataSum.ExpressAmountPaid8 = false
            this.loadingDataSum.ExpressAmountPaid9 = false
            this.loadingDataSum.ExpressAmountPaid10 = false
            this.loadingDataSum.ExpressAmountPaid11 = false
            this.loadingDataSum.ExpressAmountPaid12 = false
            this.loadingDataSum.ExpressAmountPaid13 = false
            this.loadingDataSum.ExpressAmountPaid14 = false
            this.loadingDataSum.ExpressAmountPaid15 = false
        },
        async getGroupSum(){
            var para = {...this.filter1,...this.filter2}
            const res = await getWithGroupSum(this.filter1)
            
            this.groupSumList = res.data
            },
        async onShowDetail(val){
            this.showDetailVisible = true
            this.filter1.groupId = null
            this.filter1.sendWarehouse = null
            this.filter1.expressCompany = null
            if(val == 1) this.filter1.illegalType = 1
            else if(val == 2) this.filter1.illegalType = 2
            else if(val == 3) this.filter1.illegalType = 3
            else if(val == 7) this.filter1.illegalType = 7
            else if(val == 0) this.filter1.illegalType = null
            this.$nextTick( async () =>{
                //await this.$refs.orderIllgalsearch.onSearch()
                this.$emit("onSearchDetail",this.filter1)
            })       
        },
        async onShowDetailGroup(val){
            this.showDetailVisible = true
            this.filter1.illegalType = null
            this.filter1.sendWarehouse = null
            this.filter1.expressCompany = null
            if (val >= 0) this.filter1.groupId = val
            else this.filter1.groupId = null
            this.$nextTick( async () =>{
                //await this.$refs.orderIllgalsearch.onSearch()
                this.$emit("onSearchDetail",this.filter1)
            })       
        },
        async onShowDetailWarehouse(val){
            this.showDetailVisible = true
            this.filter1.illegalType = null
            this.filter1.groupId = null
            this.filter1.expressCompany = null
            if (val>=0) this.filter1.sendWarehouse = val
            else this.filter1.sendWarehouse = null
            this.$nextTick( async () =>{
                //await this.$refs.orderIllgalsearch.onSearch()
                this.$emit("onSearchDetail",this.filter1)
            })
            
        },
        async onShowDetailCompany(val){
        this.showDetailVisible = true
        this.filter1.illegalType = null
        this.filter1.groupId = null
        this.filter1.sendWarehouse = null
        if (val>=0) this.filter1.expressCompany = val
        else this.filter1.expressCompany = null
        //this.$refs.orderIllgalsearch.onSearch()
        this.$emit("onSearchDetail",this.filter1)
    },  
    onSearchOrders(proCode) {
      this.filter1.proCode = proCode
      this.getpinwithholdsum()
      this.getexpress()
      this.getGroupSum()
    },
    },
};
</script>

<style lang="scss" scoped>
.row-condition {
  margin-top: 10px;
  margin-bottom: 20px;
  color: #606266;
  font-weight: bold;
} 
.grid-header {
  color: #606266;
  font-size: 12px;
  margin-top: 10px;
  font-weight: bold;
}
.grid-text{
  color: #606266;
  font-size: 12px;
  margin-top: 10px;
  padding: 10px;
  font-weight: bold;
}
.el-col {
  text-align: center;
}
.el-row {
  margin-right: 80px;
}
.el-icon-question {
  color: #909399;
}
.abnormalcard {
   padding: 0;
  }
.canclick{
  color:#409EFF;
  cursor: pointer;
}
</style>