<template>
    <div>
        <div class="sydh">
            <div class="sydhsx">
                <div>
                    <div style="width: 326px; margin: 0px auto">
                        <el-checkbox-group v-model="mainviewcheckList">
                            <el-checkbox style="margin: 5px 6px" label="任务列表">任务列表</el-checkbox>
                            <el-checkbox style="margin: 5px 6px" label="已完成">已完成</el-checkbox>
                            <el-checkbox style="margin: 5px 6px" label="存档">存档</el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <div style="width: 286px; margin: 0px auto">
                        <el-checkbox-group v-model="mainviewTaskcheckList">
                            <el-checkbox style="margin: 5px 6px" label="微。视频">微。视频</el-checkbox>
                            <el-checkbox style="margin: 5px 6px" label="视频建模">视频建模</el-checkbox>
                        </el-checkbox-group>
                    </div>
                </div>
                <div style="width: 620px; margin: 8px auto">
                    <el-select size="mini" style="width: 120px" filterable v-model="mainviewfilter.platform"
                        :clearable="true" placeholder="平台" @change="onchangeplatfor">
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                    <el-select size="mini" style="width: 200px" filterable v-model="mainviewfilter.shopName"
                        :clearable="true" placeholder="店铺">
                        <el-option v-for="item in shopListmain" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"> </el-option>
                    </el-select>
                    <el-date-picker size="mini" style="position: relative; top: 1px; width: 35%" type="daterange"
                        align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="yyyy-MM-dd" :picker-options="pickerOptions" v-model="mainviewfilter.timerange">
                    </el-date-picker>
                    <span> <el-button type="primary" @click="initsearch">查询</el-button></span>
                </div>
            </div>
        </div>
        <div class="sybj">
            <!--任务状态start-->
            <div class="tjnrk1">
                <div class="tjnrnk">
                    <div class="tjbt">  <span>任务状态</span> </div>
                    <div class="nrqk" style="height: 230px">
                        <div  style="  width: 625px;  margin: 0 auto; position: relative; top: 6px;  background-color: rgb(255, 0, 0);  ">
                            <div class="sztjk">
                                <div class="tjsz">{{ maintasktotal }}</div>
                                <div class="tjmc">任务条数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ maintaskovertotal }}</div>
                                <div class="tjmc">完成条数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ maintasklasttotal }}</div>
                                <div class="tjmc">剩余条数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ maintaskconfirmtotal }}</div>
                                <div class="tjmc">确认条数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ maintaskawaitconfirmtotal }}</div>
                                <div class="tjmc">待确认条数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ tasktotal }}</div>
                                <div class="tjmc">任务总数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ taskovertotal }}</div>
                                <div class="tjmc">完成总数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ tasklasttotal }}</div>
                                <div class="tjmc">剩余总数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ taskconfirmtotal }}</div>
                                <div class="tjmc">确认总数</div>
                            </div>
                            <div class="sztjk">
                                <div class="tjsz">{{ taskawaitconfirmtotal }}</div>
                                <div class="tjmc">待确认总数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--任务状态end-->
            <!--部门公告start-->
            <div class="tjnrk2">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>部门公告</span>
                    </div>
                    <div style="height: 230px" class="nrqk">
                        <div
                            style="  width: 500px;  margin: 0 auto;  position: relative;  top: 15px;  background-color: rgb(255, 0, 0); ">
                        </div>
                    </div>
                </div>
            </div>
            <!--部门公告end-->
            <!--平台上新estart-->
            <div class="tjnrk3">
                <div class="tjnrnk">
                    <div class="tjbt"> <span>平台上新</span> </div>
                    <div style="height: 360px" class="nrqk">
                        <div style="width: 100%; float: left; margin: 20px auto">
                            <div class="ptsx">
                                <div style="width: 65%; display: inline-block">
                                    <span>淘系</span>
                                </div>
                                <div style="width: 35%; text-align: right; display: inline-block">
                                    <span>{{ taoxisxtotal }}</span>
                                </div>
                            </div>
                            <div class="ptsx">
                                <div style=" width: 65%;   display: inline-block; ">
                                    <span>拼多多</span>
                                </div>
                                <div style="   width: 35%;  text-align: right; display: inline-block;  ">
                                    <span>{{ pddsxtotal }}</span>
                                </div>
                            </div>
                            <div class="ptsx">
                                <div style="  width: 65%;  display: inline-block; ">
                                    <span>其他</span>
                                </div>
                                <div style="    width: 35%;   text-align: right; display: inline-block; ">
                                    <span>{{ othersxtotal }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--平台上新end-->
            <!--上新统计start-->
            <div class="tjnrk4">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>上新统计</span>
                    </div>
                    <div style="height: 360px" class="nrqk">
                        <div style="width: 100%; float: left; margin: 5px 0"></div>
                        <div id="microVediosxtjtb" style="width: 60vw; height: 355px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--上新统计end-->
            <!--任务统计start-->
            <div class="tjnrk5">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>任务统计</span>
                    </div>
                    <div style="height: 360px" class="nrqk">
                        <div style="width: 100%; float: left; margin: 5px 0"></div>
                        <div style="padding: 8px 25px; color: #666">
                            <span>照片总数：{{ taskzptoal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>视频总数：{{ tasksptotal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>详情总数：{{ taskxqtotal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>建模总数：{{ taskjmtotal }}</span>
                            <span style="margin-right: 20px"></span>
                            <span>工作时长：{{ taskgztoal }}</span>
                            <span style="margin-right: 20px"></span>
                        </div>
                        <div id="microVediorwtjtb" style="width: 87vw; height:360px;margin: 0 auto" ></div>
                    </div>
                </div>
            </div>
            <!--任务统计end-->
            <!--人员-任务汇总start-->
            <div class="tjnrk6">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>人员-任务汇总</span>
                    </div>
                    <div style="height: 220px" class="nrqk">
                        <div style="width: 100%; float: left">
                            <div style="width: 100%; float: left; margin-top: -5px">
                                <vxetablebase ref="microVediolist" :height="'200px'" :id="'microVediodfg'"
                                    :tableData='tasklist' :tableCols='tableCols' :that='that' @sortchange='sortchange'
                                    :loading='listLoading' :hascheck='false' :hasSeq='false'>
                                </vxetablebase>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--人员-任务汇总end-->
            <!--区域对比start-->
            <div class="tjnrk7">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>区域对比</span>
                    </div>
                    <div style="height: 220px" class="nrqk">
                        <div style="width: 100%; float: left; margin: 5px 0"></div>
                        <div id="microVedioqydbtb" style="width: 25vw; height: 215px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--区域对比end-->
            <!--类型统计start-->
            <div class="tjnrk8">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>类型统计</span>
                    </div>
                    <div style="height: 360px" class="nrqk">
                        <div style="width: 100%; float: left; margin: 5px 0"></div>
                        <div id="microVediolxtjtb" style="width: 25vw; height: 355px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--类型统计end-->
            <!--建模统计start-->
            <div class="tjnrk9">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>建模统计</span>
                    </div>
                    <div style="height: 360px" class="nrqk">
                        <div style="width: 100%; float: left;"></div>
                        <div style="padding: 8px 25px; color: #666">
                            <span>完成款数：</span>
                            <span style="margin-right: 20px">{{ jmtaskwctotal }}</span>
                            <!-- <span>图片张数：</span>
                            <span style="margin-right: 20px">{{ jmtaskxytotal }}</span> -->
                            <span>视频个数：</span>
                            <span style="margin-right: 20px">{{ jmtasksptotal }}</span>
                        </div>
                        <div id="microVediojmtjtb" style="width: 60vw; height: 355px; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--建模统计end-->
            <!--评分统计start-->
            <div class="tjnrk10">
                <div class="tjnrnk">
                    <div class="tjbt">
                        <span>评分统计</span>
                    </div>
                    <div style="height: 360px; background-color: #fff" class="nrqk">
                        <div style="width: 100%; float: left;"></div>
                        <div style="padding: 8px 25px; color: #666">
                            <span>出款天数：</span>
                            <span style="margin-right: 20px">-</span>
                            <span>综合评分：</span>
                            <span style="margin-right: 20px">-</span>
                            <span>质量评分：</span>
                            <span style="margin-right: 20px">-</span>
                            <span>时效评分：</span>
                            <span style="margin-right: 20px">-</span>
                            <span>工作时长：</span>
                            <span style="margin-right: 20px">-</span>
                        </div>
                        <div id="microVediopftjtb" style="width: 100%; height: 100%; margin: 0 auto"></div>
                    </div>
                </div>
            </div>
            <!--评分统计end-->
        </div>
        <el-dialog title="个人数据趋势" :visible.sync="view5show" width="80%" element-loading-text="拼命加载中"
            :close-on-click-modal="true" :append-to-body="true">
            <div style="display:inline-block">
                <el-button style="padding: 0;">
                    <el-select filterable v-model="view5filter.groupName" placeholder="人员" clearable :collapse-tags="true">
                        <el-option v-for="item in groupNames" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-button>
                <el-date-picker style="width:310px;margin-left:13px; margin-top: 10px " type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    v-model="view5filter.timerange" :picker-options="pickerOptions" />
                <el-button type="primary" @click="showViewforper">查询</el-button>
            </div>
            <div>
                <buschar ref="view5" :analysisData="viewData5" :thisStyle="thisStyleView5" :gridStyle="gridStyleView5">
                </buschar>
            </div>
        </el-dialog>
    </div>
</template>
<script>


import * as echarts from "echarts";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform } from "@/utils/formruletools";
import {
    taskStatusStatistics, taskByPlatformStatistics, taskByGroupStatisticsMain, taskByGroupStatistics
    , taskByFpTaskStatisticsMain, taskByFpTaskStatistics, taskByFpTaskForUserStatistics, taskByPersionStatistics
    , taskRegionalComparisonStatistics, taskTypeStatistics, taskModelingStatisticsMain
    , tasScoreStatisticsMain, tasScoreForUserStatistics ,taskByFpTaskStatisticsDetail
} from '@/api/media/microvedio';
import {  getErpUserInfoView as getShootingViewPersonAsync} from '@/api/media/mediashare';

import buschar from '@/components/Bus/buscharforShooting.vue';
const tableCols = [
    { istrue: true, prop: 'name', label: '负责人' },
    { istrue: true, prop: 'yfpNum', label: '已分配', sortable: 'custom' },
    { istrue: true, prop: 'overNum', label: '已完成', sortable: 'custom' },
    { istrue: true, prop: 'overRate', label: '完成率（%）', sortable: 'custom' },
    { istrue: true, prop: 'lastTask', label: '剩余任务', sortable: 'custom' },
    { istrue: true, prop: 'yqr', label: '已确认', sortable: 'custom' },
    { istrue: true, prop: 'dqr', label: '待确认', sortable: 'custom' },
    { istrue: true, prop: 'cqts', label: '出勤天数', sortable: 'custom' }
];
export default {
    components: { buschar, vxetablebase },
    data() {
        return {
            //所有任务区域
            maintasktotal: null,
            maintaskovertotal: null,
            maintasklasttotal: null,
            maintaskconfirmtotal: null,
            maintaskawaitconfirmtotal: null,
            tasktotal: null,
            taskovertotal: null,
            tasklasttotal: null,
            taskconfirmtotal: null,
            taskawaitconfirmtotal: null,
            //所有任务区域end
            listLoading: false,
            tableCols: tableCols,
            tasklist: [],
            groupNames: [],
            viewData5: [],
            thisStyleView5: { width: '100%', height: '400px', 'box-sizing': 'border-box', 'line-height': '240px' },
            gridStyleView5: { left: '1%', right: 15, bottom: 20, top: '10%', containLabel: true },
            view5filter: {
                startTime: null,
                endTime: null,
                timerange: [],
                groupName: null
            },
            addLoading: false,
            view5show: false,
            that: this,
            taoxisxtotal: null,//淘系上新
            pddsxtotal: null,//拼多多上新
            othersxtotal: null,//其他上新
            taskzptoal: null,//照片总数：
            tasksptotal: null,//视频总数：
            taskxqtotal: null,//详情总数：
            taskjmtotal: null,//建模总数：
            taskgztoal: null,//工作时长：
            pager: {},
            //建模统计
            jmtaskwctotal: null,//完成款数：
            jmtaskxytotal: null,//图片张数：
            jmtasksptotal: null,//视频个数：

            expressData: [],//上新统计数据
            sxDataDetail: [],//上新统计数据详情
            taskFpData: [],//上新统计数据
            taskFpDataDetail: [],//上新统计数据详情

            moedlqyData: [],//类型统计
            moedlperData: [],//人员类型统计
            moedlperDataDetail: [],//人员类型统计详情
            sxData: [],
            tableData: [],
            platformList: [],
            shopListmain: [],
            mainviewcheckList: ['任务列表', '已完成'],
            mainviewTaskcheckList: ['微。视频', '视频建模'],
            mainviewfilter: {
                startTime: null,
                endTime: null,
                timerange: [],
                shopName: null,
                platform: null,
            },
            pickerOptions: { disabledDate(time) { return time.getTime() > Date.now() } },
        }
    },
    async mounted() {
        await this.onGetdrowList();
        await this.getShootingViewPer();
        this.initsearch();
    },
    methods: {
        async initsearch() {
            this.getshangxinInfo();
            this.gettotaltaskInfo();
            this.onechart();
            this.twochart();
            this.getconfirmListInfo();
            this.threechart();
            this.fourchart();
            this.fivechart();
            // this.sixchart();
        },
         // 格式化数值的函数
         formatNumber(value){
            const absNumber = Math.abs(value);
            const isInteger =Number.isInteger(value);
            const options ={
            minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
            useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value);     
                },       
        //获取下拉数据
        async onGetdrowList() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        //切换平台
        async onchangeplatfor(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.mainviewfilter.shopName = null;
            this.shopListmain = res1.data.list;
        },
        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.getconfirmListInfo();
        },

        //获取分配人下拉，对接人下啦
        async getShootingViewPer() {
            var res = await getShootingViewPersonAsync();
            if (res) {
                this.groupNames = res.map(item => { return item.label });
            }
        },
        //获取确认列表信息
        async gettotaltaskInfo() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            this.listLoading = true;
            var res = await taskStatusStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
                ...this.pager,
            });
            this.listLoading = false;
            if (res.success) {
                this.maintasktotal = this.formatNumber(res.data.maintasktotal);
                this.maintaskovertotal = this.formatNumber(res.data.maintaskovertotal);
                this.maintasklasttotal = this.formatNumber(res.data.maintasklasttotal);
                this.maintaskconfirmtotal = this.formatNumber(res.data.maintaskconfirmtotal);
                this.maintaskawaitconfirmtotal = this.formatNumber(res.data.maintaskawaitconfirmtotal);
                this.tasktotal = this.formatNumber(res.data.tasktotal);
                this.taskovertotal = this.formatNumber(res.data.taskovertotal);
                this.tasklasttotal = this.formatNumber(res.data.tasklasttotal);
                this.taskconfirmtotal = this.formatNumber(res.data.taskconfirmtotal);
                this.taskawaitconfirmtotal = this.formatNumber(res.data.taskawaitconfirmtotal);
            }
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }           
        },
        
        //获取确认列表信息
        async getconfirmListInfo() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            this.listLoading = true;
            var res = await taskByPersionStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
                ...this.pager,
            });
            this.listLoading = false;
            if (res.success) {
                this.tasklist = res.data;
            }
        },
        //获取淘系，拼多多，其他上新数据
        async getshangxinInfo() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res = await taskByPlatformStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
            });
            if (res.success) {
                this.taoxisxtotal = this.formatNumber(res.data.taoxinum);
                this.pddsxtotal = this.formatNumber(res.data.pddnum);
                this.othersxtotal = this.formatNumber(res.data.allnum);
            }
        },
        //上新统计图表-组数据详情
        async OperationGroupForPerson(params) {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await taskByGroupStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
                "groupName": params.name
            })
            this.sxDataDetail = res1;
        },
        // 上新统计图表
        async onechart() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await taskByGroupStatisticsMain({
                "viewName": "viewMain", "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform
            });
            this.expressData = res1;
            var myChart = echarts.init(document.getElementById("microVediosxtjtb"));
            const option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.expressData.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value}",
                        },
                    },
                ],
                grid: {
                    top: "40px",
                    bottom: "50px",
                    left: "60",
                    right: "65",
                },
                series: [
                    {
                        name: "上新款数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                               return formatValue(params.value);
                         }                            
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: this.expressData.series[0].data,
                    },

                ],
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    }
                ],
            };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }          
            myChart.on("click", async params => {
                if (params && params.name != "" && this.expressData.xAxis.includes(params.name)) {
                    //
                    await this.OperationGroupForPerson(params);
                    if (this.sxDataDetail.length == 0) {
                        return;
                    }
                    myChart.setOption({
                        legend: {
                            top: 5,
                            data: ["每日总上新", "每日组上新"],
                        },
                        xAxis: {
                            data: this.sxDataDetail.xAxis,
                        },
                        series: [
                            {
                                name: "每日总上新",
                                label: {
                                    show: true,
                                    position: "top",
                                },
                                type: "line",
                                id: "sales",
                                data: this.sxDataDetail.series[1].data,
                                universalTransition: {
                                    enabled: true,
                                    divideShape: "clone",
                                },
                            },
                            {
                                name: "每日组上新",
                                type: "line",
                                id: "sales2",
                                label: {
                                    show: true,
                                    position: "top",
                                },
                                data: this.sxDataDetail.series[0].data,
                                universalTransition: {
                                    enabled: true,
                                    divideShape: "clone",
                                },
                            },
                        ],
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.setOption(option, true);
                                },
                            },

                        ],
                    });
                }
            });
            myChart.setOption(option);
        },
        //上新统计图表

        //任务统计图表-组数据详情
        async fpTaskDetails(params) {
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }           
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var check = [params.name];
            var res1 = await taskByFpTaskStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": check,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
            })
            this.taskFpDataDetail = res1;
            this.taskFpData.series.forEach((item, index) => {
                item.id = "sales" + index;
                item.label = { show: true, position: "top",
                formatter: function(params){
                    return formatValue(params.value)
                }      
                 };
                item.universalTransition = { enabled: true, divideShape: "clone", };
                if (item.name.indexOf("率") > -1) {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' } } };
                } else {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 } ,formatter: function(params){
                        return formatValue(params.value)}} } };
                }
            });
        },
        //任务统计图表
        async twochart() {
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }           
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await taskByFpTaskStatisticsDetail({
                "viewName": "viewMain", "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform
            });
            this.taskFpData = res1;
            this.taskFpData.series.forEach((item, index) => {
                item.id = "sales" + index;
                item.barGap = "5%";
                item.label = { show: true, position: "top",
                formatter: function(params){
                    return formatValue(params.value)
                }
                 };
                item.emphasis = { focus: "series", };
                item.tooltip = { valueFormatter: function (value) { return value + ""; }, }
                if (item.name.indexOf("率") > -1) {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' } } };
                } else {
                    item.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 },formatter: function(params){
                        return formatValue(params.value)} } } };
                }
            });
            this.taskFpData.summuydata.forEach((item, index) => {
                switch (item.seriesName) {
                    case "照片拍摄":
                        this.taskzptoal = this.formatNumber(item.seriestotal);
                        break;
                    case "视频拍摄":
                        this.tasksptotal = this.formatNumber(item.seriestotal);
                        break;
                    case "详情排版":
                        this.taskxqtotal = this.formatNumber(item.seriestotal);
                        break;
                    case "建模任务":
                        this.taskjmtotal = this.formatNumber(item.seriestotal);
                        break;
                    case "工作时长":
                        this.taskgztoal = this.formatNumber(item.seriestotal);
                        break;
                }
            });
            var myChart = echarts.init(document.getElementById("microVediorwtjtb"));

            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                legend: {
                    top: 5,
                    selected: { "工作时长": false, "完成率": false },
                    data: ["分配数", "已完成", "剩余任务", "工作时长", "完成率"],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.taskFpData.xAxis,
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value}%",
                        },
                        splitLine: {
                            show: false // 不显示网格线
                        },
                    },
                ],
                grid: {
                    top: "55px",
                    bottom: "80px",
                    left: "60",
                    right: "65",
                },
                series: this.taskFpData.series,
                graphic: [
                    {
                        type: "text",
                        left: "right",
                        top: 12,
                        style: {
                            text: "",
                            fontSize: 14,
                        },

                    },
                    {
                        type: "text",
                        left: "left",
                        top: 12,
                        style: {
                            text: '',
                            fontSize: 14,
                        },
                    },
                ],
            };
            myChart.on("click", async params => {
                if (params && this.taskFpData.xAxis.includes(params.name)) {
                    await this.OpenDetail(params);
                }
            });
            myChart.setOption(option);
        },
        async OpenDetail(params) {
            this.view5filter.timerange = this.mainviewfilter.timerange;
            this.view5filter.groupName = params.name;
            this.view5filter.shopName = this.mainviewfilter.shopName;
            this.view5filter.platform = this.mainviewfilter.platform;
            await this.showViewforper();
        },
        async showViewforper() {
            //获取数据
            if (this.mainviewcheckList.length == 0) {
                this.$message({ message: '必须选择一种任务列表', type: "error" });
                return;
            }
            if (this.mainviewTaskcheckList.length == 0) {
                this.$message({ message: '必须选择一种拍摄任务', type: "error" });
                return;
            }
            //获取数据
            if (this.view5filter.timerange) {
                this.view5filter.startTime = this.view5filter.timerange[0];
                this.view5filter.endTime = this.view5filter.timerange[1];
            } else {
                this.view5filter.startTime = null;
                this.view5filter.endTime = null;
            }
            this.addLoading = true;
            this.view5show = true;
            var res1 = await taskByFpTaskForUserStatistics({
                "startTime": this.view5filter.startTime,
                "endTime": this.view5filter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "groupName": this.view5filter.groupName
            })
            this.setOptions(res1);
            this.viewData5 = res1;
            await this.$refs.view5.initcharts();
            this.addLoading = false;
        },
        setOptions(element) {
            element.series.forEach(s => {
                s.barMaxWidth = '50';
                if (s.name.indexOf("率") > -1) {
                    s.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 }, formatter: '{c}%' }, color: s.backColor } };
                } else {
                    s.itemStyle = { normal: { label: { show: true, position: 'top', textStyle: { fontSize: 14 } } } };
                }
                s.emphasis = { focus: 'series' };
                s.smooth = false;
            })
        },

        //  任务统计图表

        //  区域对比图表

        async threechart() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res = await taskRegionalComparisonStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
            })
            var myChart = echarts.init(document.getElementById("microVedioqydbtb"));
            var option = {
                tooltip: {
                    trigger: "axis",
                    orient: 'vertical',
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    orient: 'vertical',
                    left: "right",
                    top: 'center',
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                xAxis: [
                    {
                        type: "category",
                        data: ["义乌", "南昌"],
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },

                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],
                grid: { top: "55px", bottom: "48px", left: "60", right: "60" },
                legend: { top: '5', data: ["完成数量", "占比"] },
                series: [
                    {
                        name: "完成数量",
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: res.series[0].data,
                    },

                    {
                        name: "占比",
                        type: "line",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }                          
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "%";
                            },
                        },
                        data: res.series[1].data,
                    },
                ],
            };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }
            myChart.setOption(option);
        },

        //  区域对比图表

        //  类型统计图表
        async fourchart() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await taskTypeStatistics({
                "viewName": "viewMain", "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform
            });
            this.moedlqyData = res1;
            var myChart = echarts.init(document.getElementById("microVediolxtjtb"));
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    left: "right",
                    orient: 'vertical',
                    top: 'center',
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ["line", "bar"] },
                        restore: { show: true },
                        saveAsImage: { show: true },
                    },
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.moedlqyData.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],
                grid: { top: "55px", bottom: "48px", left: "60", right: "60" },
                legend: { top: '5', selected: { "完成款数": false, "完成占比": false }, data: ["款式数量", "完成款数", "款式占比", "完成占比"] },
                series: [
                    {
                        name: "款式数量",
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },

                        data: this.moedlqyData.series[0].data,
                    },

                    {
                        name: "完成款数",
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }

                        },

                        data: this.moedlqyData.series[2].data,
                    },
                    {
                        name: "款式占比",
                        type: "line",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "%";
                            },
                        },
                        data: this.moedlqyData.series[1].data,
                    },
                    {
                        name: "完成占比",
                        type: "line",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value) + "%";
                            },
                        },
                        data: this.moedlqyData.series[3].data,
                    },
                ],
            };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }

            myChart.setOption(option);
        },

        //  类型统计图表

        //  建模统计图表

        async fivechart() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await taskModelingStatisticsMain({
                "viewName": "viewMain", "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform
            });
            this.moedlperData = res1;

            this.moedlperData.summuydata.forEach((item, index) => {
                switch (item.seriesName) {
                    case "参与款数":
                        this.jmtaskwctotal = this.formatNumber(item.seriestotal);
                        break;
                    case "图片张数":
                        this.jmtaskxytotal = this.formatNumber(item.seriestotal);
                        break;
                    case "视频个数":
                        this.jmtasksptotal = this.formatNumber(item.seriestotal);
                        break;

                }
            });
            var myChart = echarts.init(document.getElementById("microVediojmtjtb"));
            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                legend: {
                    top: 5,
                    data: ["完成款数", "视频个数"],
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.moedlperData.xAxis,
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",

                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        splitLine: {
                            show: false // 不显示网格线
                        },
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],
                dataGroupId: "",
                animationDurationUpdate: 500,

                grid: {
                    top: "55px",
                    bottom: "80px",
                    left: "60",
                    right: "65",
                },

                series: [
                    {
                        name: "完成款数",
                        type: "bar",
                        id: "sales",
                        barGap: "5%",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: this.moedlperData.series[0].data
                    },

                    {
                        name: "视频个数",
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: this.moedlperData.series[1].data
                    },

                ],
            };

            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }           
            myChart.on("click", async params => {
                if (params && params.name != "" && this.moedlperData.xAxis.includes(params.name)) {
                    //弹出新的窗口，获取人员的工作时长和对应的
                    await this.OpenDetail(params);
                }
            });
            myChart.setOption(option);
        },

        //  建模统计图表

        //  评分统计图表
        async sixchartdetail(params) {
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var check = [params.name];
            var res1 = await getTaskScoreStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": check,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
            })
            return res1
        },
        async sixchart() {
            //获取数据
            if (this.mainviewfilter.timerange) {
                this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
            } else {
                this.mainviewfilter.startTime = null;
                this.mainviewfilter.endTime = null;
            }
            var res1 = await getTaskScoreGroupStatistics({
                "startTime": this.mainviewfilter.startTime,
                "endTime": this.mainviewfilter.endTime,
                "checkTaskList": this.mainviewcheckList,
                "checkTaskType": this.mainviewTaskcheckList,
                "shopName": this.mainviewfilter.shopName,
                "platform": this.mainviewfilter.platform,
            })
            var xtable = ["照片拍摄", "视频拍摄", "详情排版", "建模任务"];
            var myChart = echarts.init(document.getElementById("microVediopftjtb"));

            var option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        crossStyle: {
                            color: "#999",
                        },
                    },
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar', 'stack'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                legend: {
                    top: 5,
                    data: ["工作时长", "出款天数", "排除次数",],
                },
                xAxis: [
                    {
                        type: "category",
                        data: ["照片拍摄", "视频拍摄", "详情排版", "建模任务"],
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                    {
                        type: "value",
                        name: "",
                        axisLabel: {
                            formatter: "{value} ",
                        },
                    },
                ],

                grid: {
                    top: "55px",
                    bottom: "80px",
                    left: "60",
                    right: "65",
                },

                series: [
                    {
                        name: "工作时长",
                        type: "bar",
                        yAxisIndex: "1",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: res1.data.bretlist1,
                    },

                    {
                        name: "出款天数",
                        type: "bar",
                        yAxisIndex: "1",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: res1.data.bretlist2,
                    },

                    {
                        name: "排除次数",
                        yAxisIndex: "1",
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                            formatter: function (params) {
                                 return formatValue(params.value);
                            }
                        },
                        emphasis: {
                            focus: "series",
                        },
                        tooltip: {
                            valueFormatter: function (value) {
                                return formatValue(value);
                            },
                        },
                        data: res1.data.bretlist3,
                    },
                ],
            };
            // 格式化函数
            function formatValue(value) {
                const absNumber = Math.abs(value);
                const isInteger =Number.isInteger(value);
                const options ={
                minimumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                maximumFractionDigits:(absNumber<100 && !isInteger) ? 2 : 0,
                useGrouping:absNumber>=100
                         };
           return new Intl.NumberFormat('zh-CN',options).format(value); 
            }
            myChart.on("click", async params => {
                debugger
                if (params && xtable.includes(params.name)) {
                    var resdata = await this.sixchartdetail(params);
                    debugger
                    myChart.setOption({
                        xAxis: {
                            data: resdata.data.xAxis
                        },
                        series: resdata.data.series,
                        graphic: [
                            {
                                type: "text",
                                left: "right",
                                top: 12,
                                style: {
                                    text: "Back",
                                    fontSize: 14,
                                },
                                onclick: function () {
                                    myChart.setOption(option);
                                },
                            },
                        ],
                    });
                }
            });

            myChart.setOption(option);
        },

        //  评分统计图表

    },
};
</script>

<style lang="scss" scoped>
.sybj {
    min-width: 1100px;
    background-color: #f3f4f6;
    padding: 5px;
    height: calc(100vh - 280px);
    overflow-y: auto;
}

.tjldh {
    width: 785px;
    margin: 0 auto;
    box-shadow: 0px 3px 8px #cacaca;
    /* position: fixed; */
    z-index: 999;
}

.el-menu-demo {
    text-align: center;
}

.rqxz {
    width: 1000px;
    height: 80px;
    background-color: rgb(0, 54, 36);
    margin: 0 auto;
    line-height: 50px;
}

.tjbt {
    /* background-color: aquamarine; */
    /* font-weight: bold; */
    color: #333;
    line-height: 30px;
    font-size: 14px;
}

.sztjk {
    min-width: 75px;
    height: 50px;
    background-color: #f5faff;
    padding: 20px;
    text-align: center;
    float: left;
    margin: 5px;
    border-radius: 8px;
}

.sztjk .tjsz {
    font-size: 22px;
    color: #409eff;
}

.sztjk .tjmc {
    font-size: 14px;
    color: #409eff;
}

.tjnrk1 {
    width: 65%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk2 {
    width: 35%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk3 {
    width: 30%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk4 {
    width: 70%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk5 {
    width: 100%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk6 {
    width: 70%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk7 {
    width: 30%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk8 {
    width: 30%;
    /* background-color: rgb(255, 187, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk9 {
    width: 70%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrk10 {
    width: 100%;
    /* background-color: rgb(255, 0, 0); */
    box-sizing: border-box;
    padding: 3px 5px;
    display: inline-block;
}

.tjnrnk {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 15px 20px;
    float: left;
    border-radius: 6px;
}

.ptsx {
    width: 85%;
    height: 90px;
    background-color: #f7f7f7;
    border-radius: 8px;
    margin: 10px auto;
    box-sizing: border-box;
    padding: 0 35px;
    line-height: 90px;
}

.ptsx span {
    font-size: 16px;
    color: #555;
}

/* .nrqk{
    background-color: #000;

  } */

.sydh {
    width: 100%;
    min-width: 1100px;
    height: 125px;

    z-index: 999;
}

.sydhfl {
    width: 120px;
    height: 50px;
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #555;
    text-decoration: none;
}

.sydhfl :hover {
    width: 120px;
    height: 50px;
    background: linear-gradient(#f5f5f5 96%, #409eff 96%);
    display: inline-block;
    /* margin: 2px; */
    text-align: center;
    line-height: 50px;
    color: #409eff;
}

.sydhfl i {
    font-size: 19px;
    color: #409eff;
    margin-right: 5px;
    position: relative;
    top: 1px;
    line-height: 50px;
}

.sydh .fgf {
    margin: 0 5px;
    color: #a6a6a6;
    line-height: 50px;
}

.sydhsx {
    width: 100%;
    height: 125px;
    background-color: #f3f4f6;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 15px;
}
</style>
