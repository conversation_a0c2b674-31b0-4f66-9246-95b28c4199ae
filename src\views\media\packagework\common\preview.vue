<template>
  <div :visible="visible" :maskClosable="false"
           @cancel="hideModal" :width="width+'mm'">
    <div style="display: flex; flex-direction: row; padding-left: 20px;">
      <!-- <div style="margin-right: 20px">打印预览</div> -->
      <el-button :loading="waitShowPrinter" type="primary" icon="printer" @click.stop="print">打印</el-button>
      <!-- <el-button type="primary" icon="printer" @click.stop="toPdf">pdf</el-button> -->
    </div>
    <div :spinning="spinning" style="min-height: 100px">
      <div id="preview_content_design"></div>
    </div>
    <!-- <template slot="title"> -->

    <!-- </template> -->
    <template slot="footer">
      <el-button key="close" type="info" @click="hideModal">
        关闭
      </el-button>
    </template>
  </div>
</template>

<script>
export default {
  name: "printPreview",
  props: {},
  data() {
    return {
      visible: false,
      spinning: true,
      waitShowPrinter: false,
      // 纸张宽 mm
      width: 0,
      // 模板
      hiprintTemplate: {},
      // 数据
      printData: {}
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
  },
  methods: {
    hideModal() {
      this.visible = false
    },
    show(hiprintTemplate, printData, width = '210') {
      this.visible = true
      this.spinning = true
      this.width = hiprintTemplate.editingPanel ? hiprintTemplate.editingPanel.width : width;
      this.hiprintTemplate = hiprintTemplate
      this.printData = printData
      console.log("打印数据",[hiprintTemplate,printData])
      setTimeout(() => {
        // eslint-disable-next-line no-undef
        $('#preview_content_design').html(hiprintTemplate.getHtml(printData))
        this.spinning = false
      }, 500)
    },
    print() {
      this.waitShowPrinter = true
      this.hiprintTemplate.print(this.printData, {}, {
        callback: () => {
          console.log('callback')
          this.waitShowPrinter = false
        }
      })
    },
    toPdf() {
      this.hiprintTemplate.toPdf({}, '打印预览');
    },
  }
}

</script>
<style lang="scss" scoped>
::v-deep .ant-modal-body {
  padding: 0px;
}

::v-deep .ant-modal-content {
  margin-bottom: 24px;
}
</style>
