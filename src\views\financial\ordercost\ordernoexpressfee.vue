<template>
    <container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' tablekey="ordernoexpressfee" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="true" @summaryClick='onsummaryClick' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.platform" placeholder="平台" style="width: 100px" @change="changePlatform" :clearable="true" :collapse-tags="true" filterable>
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter.shopCode" v-if="storeConcealment" @change="onSearch" placeholder="店铺编码" style="width: 120px" :clearable="true" :collapse-tags="true" filterable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopCode" :value="item.shopCode" />
                        </el-select>
                        <el-input v-else v-model.trim="filter.shopCode" placeholder="平台" style="width: 250px" maxlength="50" clearable/>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input style="width: 200px" v-model="filter.shopName" placeholder="店铺名称" @change="onSearch" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="导入时间" end-placeholder="导入时间" :picker-options="pickOptions" @change="onSearch"></el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input style="width: 160px" v-model="filter.batchNumber" placeholder="批次号" @change="onSearch" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input style="width: 160px" v-model="filter.orderNo" placeholder="原始订单号" @change="onSearch" />
                    </el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
    import { pageOrderNoExpressFee, exportOrderNoExpressFeeShop } from '@/api/financial/ordercost'
    import container from '@/components/my-container/noheader'
    import cesTable from "@/components/Table/table.vue";
    import { formatPlatform, formatTime } from "@/utils/tools";
    import buschar from '@/components/Bus/buschar'
    import { getAnalysisCommonResponse } from '@/api/admin/common'
    import { getExpressFee_FenXiaoAsync } from '@/api/monthbookkeeper/financialDetail'
    const tableCols = [
        { istrue: true, prop: 'settMonth', label: '结算月份', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
        { istrue: true, prop: 'shopCode', label: '店铺编码', width: '120', sortable: 'custom'},
        { istrue: true, prop: 'shopName', label: '店铺名称', width: '250'},
        { istrue: true, prop: 'orderNo', label: '原始订单号', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'overWeightFee', label: '续重费', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'faceSheetFee', label: '面单费', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'sjTotalFee', label: '运费合计', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'computeStatus', label: '工资月报计算状态', width: '110', sortable: 'custom', formatter: (row) => { return row.computeStatus == 0 ? '未计算' : row.computeStatus == 1 ? '已计算' : '预估' } },
        { istrue: true, prop: 'referComputeStatus', label: '参考月报计算状态', width: '110', sortable: 'custom', formatter: (row) => { return row.referComputeStatus == 0 ? '未计算' : row.referComputeStatus == 1 ? '已计算' : '预估' } },
        { istrue: true, prop: 'createdTime', label: '导入时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'batchNumber', label: '批次号', width: '180', sortable: 'custom' }
    ];
    const tableHandles = [
        { label: "导入", handle: (that) => that.onimport() },
        { label: "导出", handle: (that) => that.onExport() },
        { label: "汇总导出", handle: (that) => that.onExportShop() },
        { label: "删除", handle: (that) => that.onbatchDelete() },
        { label: "单店删除", handle: (that) => that.onShopDelete() },
        { label: "刷新", handle: (that) => that.getlist() },
    ];
    export default {
        name: 'Roles',
        components: { cesTable, container, buschar },
        props: {
            filter: {},
            shopList: [],
            platformList: [],
        },
        data() {
            return {
                storeConcealment: true,
                shareFeeType: 0,
                that: this,
                list: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: "id", IsAsc: false },
                summaryarry: {},
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false,
                pickOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now()
                    }
                },
                analysisFilter: {
                    searchName: "OrderNoExpressFee",
                    extype: 6,
                    selectColumn: "overWeightFee",
                    filterTime: "SettMonth",
                    isYearMonthDay: false,
                    filter: null,
                    columnList: [{ columnNameCN: '续重费', columnNameEN: 'overWeightFee' }]
                },
                buscharDialog: { visible: false, title: "", data: [] },
            }
        },
        mounted() {
            this.onSearch()
        },
        beforeUpdate() { },
        methods: {
            onSearch() {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist() {
                if (!this.filter.settMonth) {
                    this.$message({ message: "请选择结算月份", type: "warning" });
                    return false;
                }
                this.filter.startTime = null;
                this.filter.endTime = null;
                if (this.filter.timerange) {
                    this.filter.startTime = this.filter.timerange[0];
                    this.filter.endTime = this.filter.timerange[1];
                }
                var pager = this.$refs.pager.getPager()
                this.filter.shareFeeType = this.shareFeeType;
                const params = { ...pager, ...this.pager, ... this.filter }
                this.listLoading = true
                let res = {}
                if(this.filter.platform == 11){
                    params.yearMonth = this.filter.settMonth;
                    res = await getExpressFee_FenXiaoAsync(params)
                } else {
                    res = await pageOrderNoExpressFee(params)
                }
                this.listLoading = false
                if (!res?.success) return
                this.total = res.data.total
                const data = res.data.list
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
                this.summaryarry = res.data.summary;
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async onbatchDelete() {
                await this.$emit('ondeleteByBatch', this.shareFeeType);
            },
            async onShopDelete() {
                await this.$emit('ondeleteByShop', this.shareFeeType);
            },
            async oncomput() {
                this.$emit('onstartcomput', this.shareFeeType);
            },
            async onimport() {
                await this.$emit('onstartImport', this.shareFeeType);
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            onDownloadTemplate() {
                window.open("../static/excel/financial/ordercost/订单快递费导入模板.xlsx", "_self");
            },
            changePlatform() {
                this.$emit("changePlatform", this.filter.platform);
                if(this.filter.platform == 11){
                    this.storeConcealment = false;
                }else{
                    this.storeConcealment = true;
                }
                this.filter.shopCode = null;
                this.onSearch();
            },
            onExport() {
                if (!this.filter.shopCode) {
                    this.$message({ message: "请先选择店铺", type: "warning" });
                    return false;
                }
                this.$emit("onExport");
            },
            async onExportShop() {
                if (!this.filter.settMonth) {
                    this.$message({ message: "请选择结算月份", type: "warning" });
                    return false;
                }
                if (!this.filter.platform) {
                    this.$message({ message: "请选择平台", type: "warning" });
                    return false;
                }
                this.filter.startTime = null;
                this.filter.endTime = null;
                if (this.filter.timerange) {
                    this.filter.startTime = this.filter.timerange[0];
                    this.filter.endTime = this.filter.timerange[1];
                }
                this.$confirm('数据量较大，请耐心等待，确认导出吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    var params = {
                        ...this.filter
                    };
                    let res = await exportOrderNoExpressFeeShop(params);
                    if (!res?.data) return;
                    const aLink = document.createElement("a");
                    let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '原始订单快递费汇总_' + this.filter.settMonth + '_' + new Date().toLocaleString() + '.xlsx')
                    aLink.click();
                }).catch(() => {
                });
            },
            async onsummaryClick(property) {
                let that = this;
                var fstartTime = null;
                var fendTime = null;
                if (that.filter.timerange) {
                    fstartTime = that.filter.timerange[0];
                    fendTime = that.filter.timerange[1];
                }
                this.analysisFilter.filter = {
                    platform: [that.filter.platform, 0],
                    shopCode: [that.filter.shopCode, 0],
                    batchNumber: [that.filter.batchNumber, 0],
                    orderNo: [that.filter.orderNo, 0],
                    createdTime: [fstartTime, 3],
                    createdTime: [fendTime, 4],
                    settMonth: [that.filter.settMonth, 0],
                    computeStatus: [that.filter.computeStatus, 0],
                };
                this.analysisFilter.selectColumn = property;
                this.analysisFilter.columnList = [{ columnNameCN: "金额", columnNameEN: property }];

                const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                    that.buscharDialog.visible = true;
                    that.buscharDialog.data = res.data
                    that.buscharDialog.title = res.data.legend[0]
                });
            }
        }
    }
</script>
