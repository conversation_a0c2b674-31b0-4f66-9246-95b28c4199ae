<template>
  <container>
       <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='true' :summaryarry='summaryarry'
              :loading="listLoading">
         <template slot='extentbtn'>
          <el-button-group>
            <!-- <el-button style="padding: 0;margin: 0;">
               <el-date-picker style="width: 110px" v-model="filter1.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
            </el-button> -->
            <el-button style="padding: 0;margin: 0;">
                <el-select filterable v-model="filter1.version" placeholder="类型" style="width: 100px">
                  <el-option label="工资月报" value="v1"></el-option>
                  <el-option label="参考月报" value="v2"></el-option>
               </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.proCode"  placeholder="产品ID"/></el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.platform"  placeholder="平台"/></el-button>
            <el-button style="padding: 0;margin: 0;"><el-input style="width: 100px" v-model="filter1.platform"  placeholder="源Id"/></el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.groupId" filterable clearable placeholder="运营组" style="width: 110px">
                 <el-option label="全部" value/>
                 <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter1.shopCode" filterable clearable placeholder="店铺" style="width: 240px">
                <el-option label="全部" value/>
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-button-group>
        </template>
       </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </container>
</template>
<script>
import {pageWagesMonthReport} from '@/api/financial/wages'
import {getList as getshopList } from '@/api/operatemanage/base/shop'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import {formatWarehouse,formatTime,formatYesornoBool} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'yearMonth',label:'年月', width:'70',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'宝贝ID', width:'150',sortable:'custom'},
      {istrue:true,prop:'amontCommissionMG',label:'美工提成', width:'100',sortable:'custom'},
      {istrue:true,prop:'amontCommissionXMT',label:'新媒体提成', width:'100',sortable:'custom'},
      {istrue:true,prop:'amontCommissionCG',label:'采购提成', width:'100',sortable:'custom'},
      {istrue:true,prop:'amontCornerWall',label:'护墙角工资', width:'100',sortable:'custom'},
      {istrue:true,prop:'amontMachineGZ',label:'加工部工资', width:'100',sortable:'custom'},
      {istrue:true,prop:'amontMachineGZAvg',label:'加工工资分摊', width:'110',sortable:'custom'},          
      {istrue:true,prop:'amontWagesGroup',label:'运营工资', width:'100',sortable:'custom'},      
      //{istrue:true,prop:'totalAmont',label:'实际合计', width:'100',sortable:'custom'},
     ];
const tableHandles=[ ];
export default {
  name: 'Roles',
  components: {cesTable, container},
   props:{
       filter: { }
     },
  data() {
    return {
      filter1: {
        proCode:null,
        platform:null,
        fKId:null,
        groupId:null,
        shopCode :null,
        type :null,
        shareOper :null
       },
      grouplist:[],
      shoplist:[],
      that: this,
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"YearMonth",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false,
      dialogExmainPurchaseFreightVisible:false
    }
  },
  async mounted() {
     await this.init()
     this.onSearch()
  },
  beforeUpdate() { },
  methods: {
    async init(){
        this.shoplist =[]
        const res1 = await getshopList({isOpen:1,platform:1,CurrentPage:1,PageSize:100});
        res1?.data?.list.forEach(f=>{this.shoplist.push(f)})
       
        const res11 = await getshopList({isOpen:1,platform:2,CurrentPage:1,PageSize:100});
        res11?.data?.list.forEach(f=>{this.shoplist.push(f)})

        var res2= await getDirectorGroupList();
        this.grouplist = res2.data.map(item => {return { value: item.key, label: item.value };}); 
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()

      const params = {...pager, ...this.pager,... this.filter, ... this.filter1}
      this.listLoading = true
      const res = await pageWagesMonthReport(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbatchDelete() {
       await this.$emit('ondeleteByBatch',this.shareFeeType);
    },
   async oncomput(){
      this.$emit('onstartcomput',this.shareFeeType);
   },
   async onimport(){
     await this.$emit('onstartImport',this.shareFeeType);
   },
    selsChange: function(sels) {
      this.sels = sels
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  }
}
</script>
