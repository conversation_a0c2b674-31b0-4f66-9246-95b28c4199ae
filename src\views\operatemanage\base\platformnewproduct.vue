<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="上架时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" clearable :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>        
                <el-form-item label="宝贝ID:">
                    <el-input v-model="filter.procode" style="width: 150px" placeholder="宝贝ID" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item label="上新模式:">
                <el-select v-model="filter.newPattern" placeholder="请选择" clearable :collapse-tags="true" filterable style="width: 100px">
                    <el-option label="未知" value="未知" />
                    <el-option v-for="item in productnewList" :key="item.state" :label="item.state" :value="item.state" />
                </el-select>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopId" placeholder="请选择店铺" clearable style="width: 130px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="组长:">
                <el-select filterable v-model="filter.groupId" placeholder="请选择组长" style="width: 100px" clearable>
                    <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="运营专员:">
                <el-select filterable v-model="filter.operateSpecialId" placeholder="请选择负责人" clearable style="width: 100px">
                    <el-option label="所有" value=""/>
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="运营助理:">
                <el-select filterable v-model="filter.user1Id"  placeholder="请选择负责人" clearable style="width: 100px">
                    <el-option label="所有" value=""/>
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="备用:">
                    <el-select filterable v-model="filter.user3Id" placeholder="请选择负责人" clearable style="width: 100px">
                        <el-option label="所有" value=""/>
                        <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                    </el-select>
                </el-form-item>
                <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="true" :loading="listLoading" @cellclick="cellclick">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import { getProductStateName} from '@/api/operatemanage/base/product'
import { platformlist, formatLinkProCode, formatPlatform} from '@/utils/tools'
import {getAllProBrand} from '@/api/inventory/warehouse'
import {getProductNewList, } from '@/api/operatemanage/base/product'

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

const tableCols =[
       {istrue:true,prop:'onTime',label:'上架时间', width:'90', sortable:'custom',formatter:(row) => formatTime(row.onTime, "YYYY-MM-DD")},
       {istrue:true,prop:'proCode',label:'产品ID', width:'105', sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
       {istrue:true,prop:'platform',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
       {istrue:true,prop:'images',label:'产品图片', width:'80',type:"image"},
       {istrue:true,prop:'title',label:'产品名称', width:'260', sortable:'custom'},
       {istrue:true,prop:'shopName',label:'店铺', width:'180', sortable:'custom'},
       {istrue:true,prop:'newPattern',label:'上新模式',sortable:'custom', width:'100',type:'custom',},
       {istrue:true,prop:'groupId',label:'组长', width:'70',sortable:'custom',formatter:(row)=>row.groupName||' '},
       {istrue:true,prop:'operateSpecialUserId',label:'运营专员', width:'80',sortable:'custom',formatter:(row)=> row.operateSpecialUserName||' '},
       {istrue:true,prop:'userId',label:'运营助理', width:'80',sortable:'custom',formatter:(row)=> row.userRealName||' '},
       //{istrue:true,prop:'userId2',label:'车手', width:'70',sortable:'custom',permission:"productpermis",formatter:(row)=> row.userRealName2||' '},
       {istrue:true,prop:'userId3',label:'备用', width:'auto',sortable:'custom',formatter:(row)=>row.userRealName3||' '},
       
       
    ];
const tableHandles1=[];

export default {
    name: 'YunhanAdminPlatformnewproduct',
    components: {MyContainer, cesTable},

    data() {
        return {
            that:this,
            filter: {
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                groupId:null,
                operateSpecialId:null,
                user1Id:null,
                user3Id:null,
                shopId:null,
                platform:null,
                newPattern:null
            },
            list: [],
            summaryarry:{},
            pager:{OrderBy:"onTime",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles1,
            platformlist:platformlist,
            platformList: [],
            grouplist: [],    
            brandlist: [],
            directorList:[], 
            productnewList:[],
            shopList:[],
            directorGroupList:[],
            total: 0,
            sels: [],
            listLoading: false, 
            pickerOptions: {
                disabledDate(time) {
                return time.getTime() > Date.now();
                },
            },
        };
    },

    async mounted() {
        await this.getlist()
        await this.getDirectorlist()
        await this.init()
        await this.getPruductNewState()
    },

    methods: {
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getPruductNewState() {
            var res = await getProductStateName();
            if (res?.code){
                this.productnewList = res.data.map(function (item) {
                var ob = new Object();
                ob.state = item;
                return ob;
                })
            } 
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})     
            const res3 = await getProductBrandPageList()
            
            this.directorList = res1.data
            this.directorGroupList =[{key:'0',value:'未知'}].concat(res2.data ||[]);
            this.bandList = res3.data?.list
        },
        async onchangeplatform(val){
            this.categorylist =[]
            const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
            this.shopList=res1.data.list
        },
        async init(){
            var res= await getAllProBrand();
            this.brandlist = res.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        async getlist(){
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            this.listLoading = true
            var res = await getProductNewList(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        async cellclick(row, column, cell, event){

        },
        sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.onSearch();
        },
        selsChange: function(sels) {
            this.sels = sels
        },
        selectchange:function(rows,row) {
            this.selids=[];
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>