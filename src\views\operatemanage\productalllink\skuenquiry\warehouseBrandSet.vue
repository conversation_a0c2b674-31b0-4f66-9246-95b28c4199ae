<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable collapse-tags>
                    <el-option label="启用" value="启用" />
                    <el-option label="未启用" value="未启用" />
                </el-select>
                <chooseWareHouse v-model="ListInfo.warehouseIds" class="publicCss" multiple />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="batchEdit">批量修改</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @select="select"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 500px;" :loading="loading">
        </vxetablebase>
        <div style="margin-top: 10px;">共{{ tableData.length ? tableData.length : 0 }}条</div>

        <el-dialog title="批量编辑" :visible.sync="batchEditVisible" width='20%' :close-on-click-modal="false" v-dialogDrag
            append-to-body>
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="品牌:">
                    <el-select v-model="ruleForm.brandId" placeholder="品牌" style="width: 200px;" clearable filterable>
                        <el-option v-for="item in brandList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="标签:">
                    <el-select v-model="ruleForm.labelId" placeholder="标签" style="width: 200px;" clearable filterable multiple collapse-tags 
                    @change="handleLabelChange" >
                        <el-option v-for="item in labelList" :key="item.key" :label="item.value" :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <div style="display: flex;justify-content: center;margin-top: 10px;">
                    <el-button @click="close">关闭</el-button>
                    <el-button type="primary" @click="submitForm" v-throttle="1000">保存</el-button>
                </div>
            </el-form>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { getAllProBrand } from '@/api/inventory/warehouse';
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {
    getWarehouseBrandCorrespondingNexusList,
    saveWarehouseBrandCorrespondingNexus
} from '@/api/operatemanage/productalllink/alllink'
import { row } from "mathjs";
import { getConfigByParentTitle, getConfigByParentKey } from '@/api/admin/publicConfig';
const tableCols = [
    { type: 'checkbox', label: '', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandId', label: '品牌', formatter: (row) => row.brandName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'label', label: '标签', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, chooseWareHouse
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            ruleForm: {},
            batchEditVisible: false,
            labelList: [],
        }
    },
    async mounted() {
        await this.getList()
        this.setBandSelect();
        this.setLabelSelect();
    },
    methods: {
        close() {
            this.batchEditVisible = false
        },
        async setBandSelect() {
            var res = await getAllProBrand();
            if (!res?.success) return;
            this.brandList = res.data;
        },
        async setLabelSelect() {
            var res = await getConfigByParentKey({key: 'AllPurchaseNewPlanGoodsLabel'})
            if (!res?.success)
                return;
            
            this.labelList = res.data.map(item => {
                return { key: item.id, value: item.key }
            });
        },
        handleLabelChange(value) {
            if (value && value.length > 5) {
            this.$message.warning('最多只能选择 5 个标签');
            this.ruleForm.labelId = value.slice(0, 5); // 截取前5个
            }
        },
        async submitForm() {
            if (!this.ruleForm.brandId) return this.$message.error('请选择品牌')
            const { success } = await saveWarehouseBrandCorrespondingNexus({ warehouseIds: this.selectList.map(item => item.warehouseId), brandId: this.ruleForm.brandId, labelId: this.ruleForm.labelId, label: this.ruleForm.label })
            if (!success) return this.$message.error('批量修改失败')
            this.$message.success('批量修改成功')
            this.batchEditVisible = false
            this.getList()
            this.selectList = []
        },
        batchEdit() {
            if (this.selectList.length == 0) return this.$message.error('请至少选择一条数据')
            this.ruleForm = {
                brandId: null,
            }
            this.batchEditVisible = true
        },
        select(val) {
            this.selectList = val
        },
        async getList(type) {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getWarehouseBrandCorrespondingNexusList(this.ListInfo)
                if (success) {
                    this.tableData = data
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
