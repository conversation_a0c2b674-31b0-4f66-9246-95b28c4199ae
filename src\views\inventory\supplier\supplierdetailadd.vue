<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form :model="detail" :rules="rules" ref="elForm">
        <el-descriptions title="供应商信息" :column="5" border >
          <el-descriptions-item label="供应商编码" :required="true" prop="code" labelStyle="width: 100px">
            <el-input v-model.trim="detail.code" maxlength="20" :disabled="true" :show-word-limit="true" style="width:96%"/>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称" :required="true" :span="1" prop="name" labelStyle="width: 100px">
            <el-input v-model.trim="detail.name" maxlength="40" :show-word-limit="true" style="width:96%"/>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="发货地址/省" prop="province" :required="true" labelStyle="width: 100px">
            <el-select v-model="detail.province" placeholder="请选择" @change="SelectProvinces" style="width:96%">
              <el-option v-for="item in provinces" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="发货地址/市" prop="city" :required="true" labelStyle="width: 100px">
            <el-select v-model="detail.city" placeholder="请选择" @change="SelectCity" style="width:96%">
              <el-option v-for="item in cities" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="发货地址/区" prop="districts" :required="true" labelStyle="width: 100px">
            <el-select v-model="detail.districts" placeholder="请选择" style="width:96%">
              <el-option v-for="item in districtss" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="付款方式" prop="payType" :required="true">
            <el-select v-model="detail.payType" style="width:96%">
              <el-option label="转账" value="转账" />
              <el-option label="1688" value="1688" />
              <el-option label="月结" value="月结" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="增值税发票" prop="vatInvoice" :required="true">
            <el-select v-model="detail.vatInvoice" style="width:96%">
              <el-option label="开不了" value="开不了" />
              <el-option label="可开加税" value="可开加税" />
              <el-option label="可开免税" value="可开免税" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2" prop="address">
            <el-input v-model="detail.address" maxlength="40" :show-word-limit="true" />
          </el-descriptions-item>
          <el-descriptions-item label="成立时间" prop="clTime">
            <el-date-picker v-model="detail.clTime" type="date" placeholder="选择日期"></el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item label="1688网址" :span="2" prop="address1688">
            <el-input v-model="detail.address1688" maxlength="100" :show-word-limit="true" />
          </el-descriptions-item>
          <el-descriptions-item label="配合度" prop="fitDegree">
            <el-select v-model="detail.fitDegree" style="width: 100%">
              <el-option label="很好" value="很好" />
              <el-option label="一般" value="一般" />
              <el-option label="差" value="差" />
              <el-option label="极差" value="极差" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="主营材质" prop="mainMaterials">
            <el-input v-model="detail.mainMaterials" maxlength="100" :show-word-limit="true" />
          </el-descriptions-item>
          <el-descriptions-item label="企业诚信" prop="integrity">
            <el-input v-model="detail.integrity" maxlength="100" :show-word-limit="true" />
          </el-descriptions-item>
          <el-descriptions-item label="员工人数" prop="employeesRang">
            <el-select v-model="detail.employeesRang" style="width: 100%">
              <el-option label="≤10" :value="0" />
              <el-option label="10-50" :value="1" />
              <el-option label="50-100" :value="2" />
              <el-option label="100-300" :value="3" />
              <el-option label="≥300" :value="4" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="年产能/万" prop="annualCapacity">
            <el-select v-model="detail.annualCapacity" style="width: 100%">
              <el-option label="≤100" :value="0" />
              <el-option label="100-300" :value="1" />
              <el-option label="300-500" :value="2" />
              <el-option label="500-1000" :value="3" />
              <el-option label="≥1000" :value="4" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="工厂面积/㎡" prop="factoryArea">
            <el-select v-model="detail.factoryArea" style="width: 100%">
              <el-option label="≤500" :value="0" />
              <el-option label="500-1000" :value="1" />
              <el-option label="1000-3000" :value="2" />
              <el-option label="3000-5000" :value="3" />
              <el-option label="≥5000" :value="4" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="是否能开票">
            <el-select v-model="detail.isOpenInvoice" clearable filterable placeholder="是否能开票" style="width: 96%" @change="changeInvoiceType">
              <el-option value="是" label="是" />
              <el-option value="否" label="否" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="货款对公/对私" v-if="detail.isOpenInvoice == '是'">
            <el-select v-model="detail.supplierIsOfPublicPay" clearable filterable placeholder="货款对公/对私" style="width: 96%">
              <el-option value="货款对公" label="货款对公" />
              <el-option value="货款对私" label="货款对私" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="发票类型" v-if="detail.isOpenInvoice == '是'">
            <el-select v-model="detail.invoiceType" clearable filterable placeholder="发票类型" style="width: 96%">
              <el-option value="普票" label="普票" />
              <el-option value="专票" label="专票" />
            </el-select>
            <i style="color:red">*</i>
          </el-descriptions-item>
          <el-descriptions-item label="税点(%)" v-if="detail.isOpenInvoice == '是'">
            <div style="display: flex; align-items: center; width: 100%">
            <el-input-number :controls="false" :min="0" :max="999" :precision="1"
                placeholder="税点(%)" style="flex: 1;" v-model="detail.supplierTaxRate" />
            <span style="margin-left: 5px;">%</span>
            <i style="color:red;">*</i>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="4" prop="reMark">
            <el-input type="textarea" v-model="detail.reMark" maxlength="500" :show-word-limit="true" />
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </template>

    <el-tabs v-model="activeName">
      <el-tab-pane label="联系人" name="first" style="height: 280px;">
        <div style="width: 1300px;float: left;">
          <el-table ref="contacts" :data="detail.contactsList" style="width: 100%;" max-height="300">
            <el-table-column type="index" align="center" width="40"></el-table-column>
            <el-table-column prop="position" label="职位/关系" width="120">
              <template slot-scope="scope">
                <el-input v-model="scope.row.position" maxlength="10" :show-word-limit="false" />
              </template>
            </el-table-column>
            <el-table-column prop="contacts" label="联系人" width="120">
              <template slot-scope="scope">
                <el-input v-model="scope.row.contacts" maxlength="10" :show-word-limit="false" />
              </template>
            </el-table-column>
            <el-table-column prop="mobile" label="联系电话" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.mobile" maxlength="20" :show-word-limit="false" />
              </template>
            </el-table-column>
            <el-table-column prop="weChat" label="微信号" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.weChat" maxlength="20" :show-word-limit="false" />
              </template>
            </el-table-column>
            <el-table-column prop="snick" label="旺旺名" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.snick" maxlength="20" :show-word-limit="false" />
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark" maxlength="100" :show-word-limit="false" />
              </template>
            </el-table-column>
            <el-table-column prop="businessUserName" label="添加人" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.businessUserName" maxlength="100" :show-word-limit="false" disabled />
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="" width="180">
              <template slot-scope="scope">
                <el-button type="success" icon="el-icon-delete" size="mini" @click="deletecontacts(scope.$index)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" style="margin-left:1000px" @click="addcontacts">新增</el-button>
        </div>
        <div style="margin: 0;padding: 0;float: left;">

        </div>
      </el-tab-pane>
      <el-tab-pane label="商品" name="second" style="height: 320px;">
        <el-table :data="detail.goodsList" style="width:100%;" height="90%">
          <el-table-column prop="goodsCode" label="商品编码" width="150"></el-table-column>
          <el-table-column prop="goodsName" label="商品名" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="price" label="成本价" width="70"></el-table-column>
          <el-table-column prop="brandName" label="采购" width="70"></el-table-column>
          <el-table-column prop="groupName" label="运营" width="70"></el-table-column>
          <el-table-column prop="hsaPatent" label="专利" width="90">
            <template slot-scope="scope">
              <el-select v-model="scope.row.hsaPatent" style="width: 100%">
                <el-option label="有" :value="true" />
                <el-option label="无" :value="false" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="totalAmont" label="缺货/次数" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.abnormalQty }}</span>/<span>{{ scope.row.purchaseCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="lastInTransitTime" label="最近到货时长" width="100">
            <template slot-scope="scope">
              <span>{{ formatSecondToHour(scope.row.lastInTransitTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="avgInTransitTime" label="平均到货时长" width="100">
            <template slot-scope="scope">
              <span>{{ formatSecondToHour(scope.row.avgInTransitTime) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </container>
</template>
<script>
import { getSupplierDetail, getNameSimilarity, addSupplier } from '@/api/inventory/supplier'
import { getRegion } from '@/api/admin/business'
import { formatTime, formatYesornoBool, formatSecondToHour, formatNoLink } from "@/utils/tools";
import container from '@/components/my-container/nofooter'
import { verifyPurchaseSupplierShipmentPlace } from '@/api/inventory/purchase'
export default {
  name: "Users",
  components: { container },
  data() {
    return {
      activeName: 'first',
      that: this,
      filter: {
        code: null,
        name: null,
        province: null,
        city: null,
        districts: null,
        payType: null,
      },
      formatSecondToHour: formatSecondToHour,
      detail: {
        isOpenInvoice: null,
        supplierIsOfPublicPay: null,
        invoiceType: null,
        supplierTaxRate: undefined,
        code: null,
        name: null,
        province: null,
        city: null,
        districts: null,
        clTime: null,
        address1688: null,
        address: null,
        payType: null,
        vatInvoice: null,
        fitDegree: null,
        mainMaterials: null,
        integrity: null,
        employeesRang: null,
        annualCapacity: null,
        factoryArea: null,
        reMark: null,
        goodsList: [],
        contactsList: []
      },
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      total: 0,
      sels: [],
      selids: [],
      provinces: [],
      cities: [],
      districtss: [],
      listLoading: false,
      pageLoading: false,
      rules: {
        name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' },],
        code: [{ required: true, message: '请输入供应商编码', trigger: 'blur' }],
        province: [{ required: true, message: '请输入发货地址/省', trigger: 'blur' }],
        city: [{ required: true, message: '发货地址/市', trigger: 'blur' }],
        districts: [{ required: true, message: '发货地址/区', trigger: 'blur' }],
        payType: [{ required: true, message: '请输入付款方式', trigger: 'blur' }],
        vatInvoice: [{ required: true, message: '请输入增值税发票', trigger: 'blur' }],
      }
    };
  },
  async mounted() {
    await this.init();
  },
  methods: {
    changeInvoiceType(value) {
      if (value !== '是') {
        this.detail.supplierIsOfPublicPay = null
        this.detail.invoiceType = null
        this.detail.supplierTaxRate = undefined
      }
    },
    onclear() {
      //this.$refs["elForm"].resetFields();
      this.detail = {
        code: null,
        name: null,
        province: null,
        city: null,
        districts: null,
        clTime: null,
        address1688: null,
        address: null,
        payType: null,
        vatInvoice: null,
        fitDegree: null,
        mainMaterials: null,
        integrity: null,
        employeesRang: null,
        annualCapacity: null,
        factoryArea: null,
        reMark: null,
        goodsList: [],
        contactsList: []
      }
    },
    async init() {
      var res = await getRegion({ parentcode: 0 })
      this.provinces = res.data
    },
    async SelectProvinces(code) {
      this.detail.districts = null;
      this.districtss = []
      this.detail.city = null;
      this.cities = []
      if (code) {
        var res = await getRegion({ parentcode: code })
        this.cities = res.data
      }
    },
    async SelectCity(code) {
      this.detail.districts = null;
      this.districtss = []
      if (code) {
        var res = await getRegion({ parentcode: code })
        this.districtss = res.data
      }
    },
    async getDtail(supplierId) {
      this.listLoading = true
      const res = await getSupplierDetail({ supplierId: supplierId })
      this.listLoading = false
      if (!res?.success) return
      this.detail = res.data
      if (this.detail.contactsList.length == 0)
        await this.addcontacts()
      if (this.detail.province > 0) {
        await this.SelectProvinces(this.detail.province)
      }
      if (this.detail.city > 0) {
        await this.SelectCity(this.detail.city)
      }

    },
    async addcontacts() {
      if (this.detail.contactsList == undefined)
        this.detail.contactsList = new Array();
      let obj = {};
      obj.position = "";
      obj.contacts = "";
      obj.mobile = "";
      obj.weChat = "";
      obj.businessUserName = this.$store.getters.userName?.split("-")[0].trim();
      obj.remark = "";
      this.detail.contactsList.push(obj);
      this.$nextTick(() => { this.$refs.contacts.doLayout(); });
    },
    async deletecontacts(index) {
      this.$confirm('确定删除吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
        .then(async () => {
          console.log(index)
          this.detail.contactsList.splice(index, 1);
        }).catch(() => {
          this.$message({ type: 'info', message: '已取消' });
        });
    },
    configSave() {
      let that = this;
      that.$confirm('确定保存吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
        .then(() => {
          that.pageLoading = true;
          addSupplier(that.detail)
            .then(function (res) {
              if (!res?.success) {
                that.$message({ type: 'warn', message: '保存失败' });
                that.pageLoading = false;
              }
              else {
                that.$message({ type: 'success', message: '保存成功' });
                that.pageLoading = false;
                that.$emit("addcallback", res.data);
              }
            })
            .catch(function (err) { console.error(err); });
        }).catch(() => {
          that.$message({ type: 'info', message: '已取消' });
        });
    },
    async verifyPurchaseSupplierShipmentPlaceAsync() {
      let that = this;
      if (!that.detail.name || that.detail.name == "" ||
        !that.detail.province || that.detail.province == "" ||
        !that.detail.city || that.detail.city == "" ||
        !that.detail.districts || that.detail.districts == "" ||
        !that.detail.payType || that.detail.payType == "" ||
        !that.detail.vatInvoice || that.detail.vatInvoice == "") {
          that.$message({ type: 'error', message: '供应商名称 | 发货地址/省/市/区 | 付款方式 | 增值税发票，以上字段必填！' });
        return;
      }

      if (!this.detail.isOpenInvoice || this.detail.isOpenInvoice == "") {
        this.$message({ type: 'error', message: '是否能开票字段必填！' });
        return;
      }
      if (this.detail.isOpenInvoice == '是') {
        if (!this.detail.supplierIsOfPublicPay || this.detail.supplierIsOfPublicPay == "") {
          this.$message({ type: 'error', message: '货款对公/对私字段必填！' });
          return;
        }
        if (!this.detail.invoiceType || this.detail.invoiceType == "") {
          this.$message({ type: 'error', message: '发票类型字段必填！' });
          return;
        }
        if (this.detail.supplierTaxRate === undefined || this.detail.supplierTaxRate === null || this.detail.supplierTaxRate === "") {
          this.$message({ type: 'error', message: '税点字段必填！' });
          return;
        }
      }

      let pcdArr = [that.detail.province, that.detail.city, that.detail.districts];

      let param = {
        supplier: that.detail.name,
        provinceCityDistrict: JSON.stringify(pcdArr)
      };

      var res = await verifyPurchaseSupplierShipmentPlace(param);
      if (!res?.success) {
        that.$confirm('供应商名称省市与发货地省市不一致，是否继续?','提示',{ confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
          .then(() => {
            that.onSave();
          });
      } else {
        that.onSave();
      }
    },
    async onSave() {
      let that = this;
      if (!this.detail.name || this.detail.name == "" ||
        !this.detail.province || this.detail.province == "" ||
        !this.detail.city || this.detail.city == "" ||
        !this.detail.districts || this.detail.districts == "" ||
        !this.detail.payType || this.detail.payType == "" ||
        !this.detail.vatInvoice || this.detail.vatInvoice == "") {
        this.$message({ type: 'error', message: '供应商名称 | 发货地址/省/市/区 | 付款方式 | 增值税发票，以上字段必填！' });
        return;
      }
      //校验名称是否有相似的
      await getNameSimilarity({ supplierName: this.detail.name }).then(function (res) {
        if (res?.success && res.data) {
          that.$confirm("存在相似的供应商名：" + res.data + ",是否继续添加？", '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
            .then(function () {
              that.configSave();
              that.$emit('onSearch');
            })
            .catch(() => {
              //取消操作
              return;
            });
        } else {
          that.configSave();
        }
      });
    },
  },
};
</script>
<style scoped>
.el-card__body {
  padding: 0;
}

.my-label-bitian {
  color: #FDE2E2;
}
</style>
