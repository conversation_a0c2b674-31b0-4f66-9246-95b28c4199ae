<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <!--  -->
            <el-form class="ad-form-query" :model="chooseFormData" ref="chooseForm"  @submit.native.prevent
                label-width="100px">
                <el-row>
                    <el-col  :span="8">
                        <el-form-item label="产品简称" prop="goodsCompeteShortName" :rules="[
                            { required: true, message: '请填写产品简称', trigger: 'blur', type: 'string' },
                            { max: 50, message: '长度50 个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.goodsCompeteShortName" style="width: 250px" :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col  :span="8">
                        <el-form-item label="产品标题" prop="goodsCompeteName" :rules="[
                            { required: true, message: '请填写产品标题', trigger: 'blur', type: 'string' },
                            { max: 50, message: '长度50 个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.goodsCompeteName" style="width: 250px" :maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col  :span="8">
                        <el-form-item label="竞品ID" 
                            prop="goodsCompeteId" :rules="[
                                { required: true, message: '请填写竞品ID', trigger: 'blur', type: 'string' },
                                { min: 4,  max: 30, message: '长度4到30 个字符内', trigger: 'blur', type: 'string' }
                            ]">
                            <el-input style="width: 250px" v-model.trim="chooseFormData.goodsCompeteId" :maxlength="30" :minlength="4"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="产品类目" prop="goodsCategorys" :rules="[
                            { required: true, message: '请选择产品类目', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.goodsCategorys" placeholder="请选择"  @change="setSelectCategorys" style="width: 250px" clearable filterable :collapse-tags="true">
                                <el-option v-for="(item) in categoryall.categoryone" 
                                :key="item.mainCategory+'-'+item.categoryLevel1+'-'+item.categoryLevel2" 
                                :label="item.mainCategory+'-'+item.categoryLevel1+'-'+item.categoryLevel2"  
                                :value="item.mainCategory+'-'+item.categoryLevel1+'-'+item.categoryLevel2">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="是否支持一件代发" prop="isOneDistribution" label-width="150px" :rules="[
                            { required: true, message: '请选择是否支持一件代发', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.isOneDistribution" placeholder="请选择" style="width: 190px; margin-left: 10px;" clearable filterable :collapse-tags="true">
                                <el-option
                                label="是"  
                                :value="1">
                                </el-option>
                                <el-option
                                label="否"  
                                :value="0">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="国内/跨境" prop="internationalType" :rules="[
                            { required: true, message: '请选择国内/跨境', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.internationalType" placeholder="请选择" @change="onInternationalTypeChange" disabled
                            style="width: 250px" clearable filterable :collapse-tags="true">
                                <el-option  :key="0" :value="0" label="国内"  />
                                <el-option  :key="1" :value="1" label="跨境"  />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
 
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="资质"  prop="qualificationLists" :rules="[
                            { required: true, message: '请选择资质', trigger: 'blur', }]">
                            <el-select :collapse-tags="true" v-model="chooseFormData.qualificationLists" multiple placeholder="类型" style="width: 250px" >
                                <el-option label="无" value="无"></el-option>
                                <el-option label="质检报告" value="质检报告"></el-option>
                                <el-option label="食品级质检报告" value="食品级质检报告"></el-option>
                                <el-option label="全国工业生产许可证" value="全国工业生产许可证"></el-option>
                                <el-option label="化妆品生产许可证" value="化妆品生产许可证"></el-option>
                                <el-option label="化妆品备案信息" value="化妆品备案信息"></el-option>
                                <el-option label="农业生产许可证" value="农业生产许可证"></el-option>
                                <el-option label="农药登记证书" value="农药登记证书"></el-option>
                                <el-option label="肥料生产许可证" value="肥料生产许可证"></el-option>
                                <el-option label="3c认证报告" value="3c认证报告"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="是否专利" prop="isPatent" :rules="[
                            { required: true, message: '是否专利', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.isPatent" @change="chooseFormData.isPatent==0?chooseFormData.patentType='':''" placeholder="是否专利" style="width: 250px" >
                                <el-option label="是" :value="1"></el-option>
                                <el-option label="否" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="专利类型" prop="patentType" v-if="chooseFormData.isPatent==1" :rules="[
                            { required: true, message: '专利类型', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.patentType" placeholder="专利类型" style="width: 250px" >
                                <el-option label="专利" :value="'专利'"></el-option>
                                <el-option label="品牌" :value="'品牌'"></el-option>
                                <el-option label="著作" :value="'著作'"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="成本价" prop="costPrice" :rules="[
                            { required: true, message: '请填写成本价', trigger: 'blur',  }]">
                            <!-- <el-input v-model="chooseFormData.costPrice" @blur="chooseFormData.costPrice = chooseFormData.costPrice.slice(0,10)" type="number" :max="9999999" style="width: 250px" :maxlength="8"></el-input> -->

                            <el-input-number v-model="chooseFormData.costPrice" type="number" placeholder="请填写成本价"
                                clearable :controls="false" :precision="3" :max="9999999" :min="0"  style="width: 250px" />
                        </el-form-item>
                    </el-col>

                    <el-col   :span="8">
                        <el-form-item label="售价" prop="salePrice" :rules="[
                            { required: true, message: '请填写售价', trigger: 'blur',  }]">
                            <!-- <el-input v-model="chooseFormData.salePrice" type="number" @blur="chooseFormData.salePrice = chooseFormData.salePrice.slice(0,10)" :max="9999999" style="width: 250px" :maxlength="8"></el-input> -->
                            
                            <el-input-number v-model="chooseFormData.salePrice" type="number" placeholder="请填写售价"
                                clearable :controls="false" :precision="3" :max="9999999" :min="0"  style="width: 250px" />
                        </el-form-item>
                    </el-col>

                    <el-col   :span="8">
                        <el-form-item label="商品图片" prop="goodsCompeteImgUrl" :rules="[
                            { required: true, message: '请上传', trigger: 'blur' }]">
                            <!-- <el-input v-model=" chooseFormData.goodsCompeteImgUrl " style="width: 250px" :maxlength="100"></el-input> -->
                            <yh-img-upload :limit="1" :value.sync="chooseFormData.goodsCompeteImgUrl" />
                        </el-form-item> 
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="是否可开票"  prop="isInvoicing" :rules="[
                            { required: true, message: '请选择是否可开票', trigger: 'blur', }]">
                            <el-select v-model="chooseFormData.isInvoicing" placeholder="是否可开票" style="width: 250px" >
                                <el-option label="是" :value="1"></el-option>
                                <el-option label="否" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item v-if="chooseFormData.isInvoicing==1" label="税点%"  prop="isInvoicingRate" :rules="[
                            { required: true, message: '请填写税点%', trigger: 'blur', }]">
                            <el-input-number v-model="chooseFormData.isInvoicingRate" type="number" placeholder="请填写税点%"
                                clearable :controls="false" :precision="2" :max="100" :min="0"  style="width: 250px" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col   :span="8">
                        <!-- :rules="[
                            { required: true, message: '请填写竞品标题', trigger: 'blur' },
                            { min: 4, max: 100, message: '长度在 4 到 100 个字符', trigger: 'blur' }]" -->
                        <el-form-item label="供应商名称" prop="supplierName" :rules="[
                            { required: true, message: '请填写供应商名称', trigger: 'blur', type: 'string' },
                            { max: 50, message: '长度50个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.supplierName" style="width: 250px" :maxlength="50"></el-input>
                        </el-form-item> 
                    </el-col>

                    <el-col   :span="8">
                        <el-form-item label="供应商链接" prop="supplierLink" :rules="[
                            { required: true, message: '请填写供应商链接', trigger: 'blur', type: 'string' },
                            { max: 100, message: '长度100个字符内', trigger: 'blur', type: 'string' }]">
                            <el-input v-model="chooseFormData.supplierLink" style="width: 250px" :maxlength="100"></el-input>
                        </el-form-item> 
                    </el-col>

                    <el-col   :span="8">
                        <!-- :rules="[
                        { min: 0, max: 100, message: '长度不能超过100个字符', trigger: 'blur' }]" -->
                        <el-form-item label="备注" prop="remark" >
                            <el-input v-model="chooseFormData.remark" :maxlength="100" ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            
                <el-row>
                    <el-col   :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择资质图片', trigger: 'blur' }]" -->
                        <el-form-item label="质检报告" prop="inspectionReportImgUrl" >
                            <yh-img-upload :limit="1" :value.sync="chooseFormData.inspectionReportImgUrl" />
                        </el-form-item>
                    </el-col>

                    <el-col   :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择资质图片', trigger: 'blur' }]" -->
                        <el-form-item label="质检报告文件" prop="inspectionReportPdfUrls" >
                            <YhImgUpload :value.sync="chooseFormData.inspectionReportPdfUrls" :isImg="false" accept=".pdf,.xlsx,.docx" :limit="11"></YhImgUpload>
                        </el-form-item>
                    </el-col>

                    <el-col   :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择专利图片', trigger: 'blur' }]" -->
                        <el-form-item label="专利资质" prop="patentQualificationImgUrls" >
                            <yh-img-upload :limit="10" :value.sync="chooseFormData.patentQualificationImgUrls" />
                        </el-form-item>
                    </el-col>

                    <el-col   :span="4">
                        <!-- :rules="[
                        { required: true, message: '请选择专利PDF', trigger: 'blur' }]" -->
                        <el-form-item label="专利资质文件" prop="patentQualificationPdfUrls" >
                            <YhImgUpload :value.sync="chooseFormData.patentQualificationPdfUrls" :isImg="false" accept=".pdf,.xlsx,.docx" :limit="11" ></YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col   :span="4">
                        <el-form-item label="包装图片" prop="packingImgUrls" >
                            <YhImgUpload :value.sync="chooseFormData.packingImgUrls"  :limit="10" ></YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    SKU规格
                </el-row>
                <vxetablebase :id="'HotSaleBrandPushNewKJSelEdit202408041709'" ref="table1" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :border="true"
                    :tableData='chooseFormData.tableData1' :tableCols='tableCols1'
                    :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" height='300'>
                </vxetablebase>
            </el-form>
        </template>

        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right; margin-top: 5px;">
                    <el-button  @click="onClose">取消</el-button>
                    <el-button  type="primary" v-show="mode==1"  @click="onSave()">确认推新</el-button>
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>


import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import YhImgUpload from '@/components/upload/yh-img-upload.vue';
import {
    SaveHotSaleBrandPushNewByKJSel,GetHotSaleBrandPushNewKJSelById,GetHotSaleBrandPushNewKJSelDtlById
} from '@/api/operatemanage/productalllink/alllink'
import { getAllBrandProductCategorys,getHotSaleBrandPushNewById } from '@/api/operatemanage/productalllink/LogisticsAnalyse.js';
const tableCols1 = [
    { width: '150', prop: 'goodsCode', label: 'Sku编码'},
    { width: '300', prop: 'goodsName', label: 'Sku名称'},
    { width: '300', prop: 'goodsSpecifications', label: 'Sku规格'},
    { width: '300', prop: 'goodsDescription', label: 'Sku说明'},
    { width: '80', prop: 'goodsImgUrl', label: 'Sku图片' ,type: 'images'},
    { width: '80', prop: 'goodsPrice', label: 'Sku单价($)', formatter: (row) =>row.goodsPrice+"$"},
];
export default {
    name: "HotSaleBrandPushNewKJSelEdit",
    components: { MyContainer, YhImgUpload, vxetablebase },
    data() {
        return {
            that: this,
            mode:3,
            categoryall: {
                categoryone: [],
                categorytwo: [],
                categorythr: [],
            },
            chooseFormData: {
                goodsCompeteId:null,
                goodsCompeteShortName:null,
                goodsCompeteName:null,
                goodsCategorys:null,
                isOneDistribution:null,
                internationalType:1,
                qualificationLists:null,
                isPatent:null,
                patentType:null,
                goodsCompeteImgUrl:null,
                isInvoicing:null,
                supplierName:null,
                supplierLink:null,
                remark:null,
                inspectionReportImgUrl:null,
                inspectionReportPdfUrls:null,
                patentQualificationImgUrls:null,
                patentQualificationPdfUrls:null,
                packingImgUrls:null,
                tableData1:[],
            },
            pageLoading: false,
            tableCols1:tableCols1,
        };
    },
    created() {

    },
    async mounted() { 
        
    }, 
    methods: {
        clearForm(){
            this.chooseFormData.goodsCompeteId=null;
            this.chooseFormData.goodsCompeteShortName=null;
            this.chooseFormData.goodsCompeteName=null;
            this.chooseFormData.goodsCategorys=null;
            this.chooseFormData.isOneDistribution=null;
            this.chooseFormData.internationalType=1;
            this.chooseFormData.qualificationLists=null;
            this.chooseFormData.isPatent=null;
            this.chooseFormData.patentType=null;
            this.chooseFormData.goodsCompeteImgUrl=null;
            this.chooseFormData.isInvoicing=null;
            this.chooseFormData.supplierName=null;
            this.chooseFormData.supplierLink=null;
            this.chooseFormData.remark=null;
            this.chooseFormData.inspectionReportImgUrl=null;
            this.chooseFormData.inspectionReportPdfUrls=null;
            this.chooseFormData.patentQualificationImgUrls=null;
            this.chooseFormData.patentQualificationPdfUrls=null;
            this.chooseFormData.packingImgUrls=null;
            this.chooseFormData.tableData1=[];
        },
        async loadData(val) {
            this.pageLoading=true;
            if(this.mode)
                this.mode=val.mode;
            if(!val.hotSaleBrandPushNewId)
                val.hotSaleBrandPushNewId=0;
            console.log(val,'val');

            this.clearForm();
            this.chooseFormData.refType = 1;
            this.chooseFormData.refId = val.id;

            this.getcategoryallview();

            let res = await GetHotSaleBrandPushNewKJSelById({ id: val.id });
            this.chooseFormData.goodsCompeteId = "KJ"+res.data.goodsCompeteId;
            this.chooseFormData.goodsCompeteShortName = res.data.goodsCompeteShortName;
            this.chooseFormData.goodsCompeteName = res.data.goodsCompeteName;
            this.chooseFormData.goodsCompeteImgUrl = res.data.goodsCompeteImgUrl;

            let resPushNew = await getHotSaleBrandPushNewById({ id: val.hotSaleBrandPushNewId });
            if (resPushNew?.success&&resPushNew.data) {
                this.chooseFormData.goodsCompeteId = resPushNew.data.goodsCompeteId;
                this.chooseFormData.goodsCompeteShortName = resPushNew.data.goodsCompeteShortName;
                this.chooseFormData.goodsCompeteName = resPushNew.data.goodsCompeteName;
                this.chooseFormData.goodsCompeteImgUrl = resPushNew.data.goodsCompeteImgUrl;
                this.chooseFormData.goodsCategorys = resPushNew.data.goodsCategory + "-" + 
                                                     resPushNew.data.categoryLeve1 + "-" + 
                                                     resPushNew.data.categoryLeve2;
                this.chooseFormData.costPrice=resPushNew.data.costPrice;
                this.chooseFormData.salePrice=resPushNew.data.salePrice;
                this.chooseFormData.isOneDistribution=resPushNew.data.isOneDistribution;
                this.chooseFormData.internationalType=resPushNew.data.internationalType;
                this.chooseFormData.qualificationLists=resPushNew.data.qualificationLists;
                this.chooseFormData.isPatent=resPushNew.data.isPatent;
                this.chooseFormData.patentType=resPushNew.data.patentType;
                this.chooseFormData.isInvoicing=resPushNew.data.isInvoicing;
                this.chooseFormData.isInvoicingRate=resPushNew.data.isInvoicingRate;
                this.chooseFormData.supplierName=resPushNew.data.supplierName;
                this.chooseFormData.supplierLink=resPushNew.data.supplierLink;
                this.chooseFormData.remark=resPushNew.data.remark;
                this.chooseFormData.inspectionReportImgUrl=resPushNew.data.inspectionReportImgUrl;
                this.chooseFormData.inspectionReportPdfUrls=resPushNew.data.inspectionReportPdfUrls;
                this.chooseFormData.patentQualificationImgUrls=resPushNew.data.patentQualificationImgUrls;
                this.chooseFormData.patentQualificationPdfUrls=resPushNew.data.patentQualificationPdfUrls;
                this.chooseFormData.packingImgUrls=resPushNew.data.packingImgUrls;
            }

            let resdtl = await GetHotSaleBrandPushNewKJSelDtlById({ id: val.id });
            this.chooseFormData.tableData1=resdtl.data;
            this.pageLoading=false;
        },
        async getcategoryallview(){
            let res = await getAllBrandProductCategorys();
            if (res?.success) {
                this.categoryall.categorytwo = [];
                this.categoryall.categorythr = [];
                this.categoryall.categoryone = res.data;
            }
        },
        setSelectCategorys(data){
            if(data == null || data == ""){ 
                this.chooseFormData.goodsCategory = "";
                this.chooseFormData.categoryLeve1 = "";
                this.chooseFormData.categoryLeve2 = "";
            }
            else{
                let obj ={}
                obj = this.categoryall.categoryone.find(function(item){
                    return (item.mainCategory+'-'+item.categoryLevel1+'-'+item.categoryLevel2) === data;
                })
                this.chooseFormData.goodsCategory = obj.mainCategory;
                this.chooseFormData.categoryLeve1 = obj.categoryLevel1;
                this.chooseFormData.categoryLeve2 = obj.categoryLevel2;
            }
        },
        onInternationalTypeChange(value){
            if(this.chooseFormData.internationalType==1){
                if(this.chooseFormData.goodsCompeteId.toLowerCase().indexOf("kj")!=0){
                    this.chooseFormData.goodsCompeteId=("KJ"+this.chooseFormData.goodsCompeteId);
                }
            }
            else{
                if(this.chooseFormData.goodsCompeteId.toLowerCase().indexOf("kj")==0){
                    this.chooseFormData.goodsCompeteId=(this.chooseFormData.goodsCompeteId.substring(2));
                }
            }
        },
        onClose() {
            this.$emit('close');
        },
        onSaveSuccess() {
            this.$emit('afterSave');
            this.$emit('close');
        },
        async onSave() {
            await this.save();
        },
        async save() {
            let me=this;
            this.pageLoading = true;
            //处理跨境的品 加KJ前缀
            this.onInternationalTypeChange();
            let saveData = { ...this.chooseFormData };

            //处理发票税点
            if(saveData.isInvoicing==1&&!saveData.isInvoicingRate){
                this.$message({ message: '请填写税点%且大于0', type: "error" });
                this.pageLoading = false;
                return false;
            }
            if(!saveData.isInvoicing){
                saveData.isInvoicingRate=null;
            }
            try {
                let valid = await this.$refs["chooseForm"].validate();
                if (!valid) {
                    this.pageLoading = false;
                    return false;
                } 
            } catch (error) {
                this.pageLoading = false;
                return false;
            }

            this.$confirm('确认要执行此操作吗，推新后将无法退回？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let reqRlt = await SaveHotSaleBrandPushNewByKJSel(saveData);
                this.pageLoading = false;
                if (reqRlt && reqRlt.success) {
                    this.$message({ message: '操作成功，已推送到【采购推新(新版)-跨境】', type: "success" });
                    me.onSaveSuccess();
                }
            }).catch(() => {
                this.pageLoading = false;
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.flexrow{
    display: flex;
    flex-direction: row;
}
</style>

