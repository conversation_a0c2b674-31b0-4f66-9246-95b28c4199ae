<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-row>
                <!-- <el-input v-model.trim="filter.keyWord" placeholder="关键字搜索" style="width: 160px" :maxlength="50" clearable>
                <el-tooltip slot="suffix" class="item" effect="dark" content="支持搜索的内容：菜名，菜单类型名称，价格，积分，标签"
                    placement="bottom">
                        <i class="el-input__icon el-icon-question"></i>
                    </el-tooltip>
                </el-input> -->
                <!-- <el-date-picker v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    style="width:230px;">
                </el-date-picker> -->
                <!-- <el-date-picker v-model="filter.timerange" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期"
                    style="width:150px;">
                </el-date-picker> -->
                <!-- <el-select v-model="filter.weekTime" placeholder="时间" clearable filterable style="width: 160px">
                    <el-option label="周一" value="周一" />
                    <el-option label="周二" value="周二" />
                    <el-option label="周三" value="周三" />
                    <el-option label="周四" value="周四" />
                    <el-option label="周五" value="周五" />
                    <el-option label="周六" value="周六" />
                    <el-option label="周日" value="周日" />
                </el-select> -->
                <!-- <el-select v-model="filter.gysName" placeholder="供应商" clearable filterable style="width: 160px">
                    <el-option label="小红" value="小红" />
                    <el-option label="小白" value="小白" />
                    <el-option label="小黄" value="小黄" />
                </el-select> -->
                <el-select v-model="filter.gysName" placeholder="供应商类型" clearable filterable style="width: 160px">
                    <el-option :label="item" :value="item" v-for="(item,index) in gysList" :key="index" />
                </el-select>
                <el-select v-model="filter.zoneName" placeholder="地区搜索" filterable style="width: 160px">
                    <el-option :label="item" :value="item" v-for="(item,index) in quyuList" :key="index" />
                </el-select>
                <!-- <el-input v-model.trim="filter.zoneName" placeholder="地区搜索" style="width: 160px" :maxlength="50" clearable></el-input> -->
                <el-select v-model="filter.menuStatus" placeholder="点餐状态" clearable filterable style="width: 160px">
                    <el-option label="在售" value="在售" />
                    <el-option label="下架" value="下架" />
                </el-select>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="dialogVisibleData = true">导入菜单</el-button>
                <!-- <el-button type="primary" @click="openDialogMenuType">供应商名称</el-button> -->
                <el-button type="primary" @click="onExport">下载导入模板</el-button>

            </el-row>



        </template>
        <container v-show="onetab == 'two'">
            <template>
                <yhVxetable ref="table" :id="'YunHanAdminGoods20231219'" :somerow="'zoneName,menuLabel,orderMenuDate,gysName,menuType'" showoverflow="title" :that='that' :isIndex='true'  @sortchange='sortchange' :summaryarry="summaryarry"
                :tableData='tableDatatwo' :tableCols='tableColstwo' :border='true'
                :isSelectColumn="false" :loading="listLoading">
                </yhVxetable>
            </template>

            <!-- <template #footer>
                <my-pagination ref="pagertwo" :total="total" :checked-count="sels.length" @get-page="getListtwo" />
            </template> -->
        </container>
        <container v-show="onetab == 'one'">
            <template>
                <yhVxetable ref="tabletwo" :id="'YunHanAdminGoods20231220'" :that='that' :isIndex='true'  @sortchange='sortchange' :summaryarry="summaryarry"
                :tableData='tableData' :tableCols='tableCols' :border='true'
                :isSelectColumn="false" :loading="listLoading">
                </yhVxetable>
            </template>

            <template #footer>
                <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
            </template>
        </container>



        <el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="600px" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false"  action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleData = false">关闭</el-button>
            </span>
        </el-dialog>

        <!-- <el-dialog title="菜单名称类型" :visible.sync="dialogMenuType"  width="480px" v-dialogDrag>
            <span>
                <el-form ref="form" :model="form" label-width="10px" max-height="600">
                    <el-form-item label="">
                        <span>
                            <el-button @click="addMenuType">新增</el-button>
                        </span>
                    </el-form-item>
                    <div style="max-height: 400px; overflow-y: auto; width: 25rem;">
                        <el-form-item label="" v-for="(item, index) in form.menuTypeList" :key="index">
                            <span>
                                类型
                                <el-input placeholder="类型" style="width: 80px; " v-model="item.menuType" maxlength="50"></el-input>
                                &nbsp;&nbsp;
                                排序
                                <el-input-number placeholder="排序"
                                v-model="item.orderId" :step="1" step-strictly :max="999999" style="width: 120px; "></el-input-number>
                                &nbsp;&nbsp;
                                <el-button @click="removeMenuType(index)">删除</el-button>
                            </span>
                        </el-form-item>
                    </div>
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveMenuType">保存</el-button>
                <el-button @click="dialogMenuType = false">关闭</el-button>
            </span>
        </el-dialog> -->

        <el-dialog title="编辑菜单" :visible.sync="dialogEdit" width="600px" v-dialogDrag>
            <span>
                <el-form ref="form" :model="form" label-width="80px" max-height="600">
                    <el-form-item label="菜单名称:">
                        <el-input placeholder="菜单名称" v-model="editForm.menuName" maxlength="50"></el-input>
                    </el-form-item>
                    <el-form-item label="价格:">
                        <el-input-number placeholder="价格"
                                v-model="editForm.price" :precision="2" :min="0" :max="99999999" style="width: 120px; "></el-input-number>
                    </el-form-item>
                    <el-form-item label="成本价:">
                        <el-input-number placeholder="成本价"
                                v-model="editForm.costPrice" :precision="2" :min="0" :max="99999999" style="width: 120px; "></el-input-number>
                    </el-form-item>
                    <el-form-item label="库存:">
                        <el-input-number placeholder="库存"
                                v-model="editForm.stock" :step="1" step-strictly :max="999999"></el-input-number>
                    </el-form-item>
                    <el-form-item label="状态:">
                        <el-select v-model="editForm.menuStatus" placeholder="状态" clearable filterable
                            style="width: 100%; ">
                            <el-option label="在售" value="在售"></el-option>
                            <el-option label="下架" value="下架"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="图片:" v-show="onetab == 'two'">
                        <yh-img-upload :value.sync="editForm.imageUrl" ref="supplier_id" :limit="1" keys="one"></yh-img-upload>
                    </el-form-item> -->
                    <!-- <el-form-item label="排序:">
                        <el-input-number placeholder="排序"
                                v-model="editForm.orderId" :step="1" step-strictly :max="999999"></el-input-number>
                    </el-form-item> -->
                </el-form>
            </span>

            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="updateBaseMenu">保存</el-button>
                <el-button @click="dialogEdit = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
import YhImgUpload from "@/components/upload/yh-img-upload1.vue";
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import container from '@/components/my-container';
import yhVxetable from '@/components/VxeTable/yh_vxetable.vue';
import { formatLinkProCode, platformlist } from '@/utils/tools'
import { importBaseMenuAsync, getPageBaseMenuAsync, updateBaseMenuAsync, delBaseMenuAsync, getOrderMenuTypeAsync,
     saveMenuTypeAsync, getBaseManagerDetailAsync, getOrderFoodMenuProvier, getAreaSetList } from '@/api/profit/orderfood';

const tableCols = [
    { istrue: true, prop: 'gysName', label: '供应商名称', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'menuName', label: '菜名', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'price', label: '价格', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'salesvolume', label: '销量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'menuStatus', label: '状态', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'menuLabel', label: '标签', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'stock', label: '库存', width: '100', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "200",
        btnList: [
            { label: "编辑", handle: (that, row) => that.editBaseMenu(row, 1),  },
            { label: "删除", handle: (that, row) => that.delBaseMenu(row, 1),  }
        ]
    }
];

const tableColstwo = [
    { istrue: true, prop: 'zoneName', label: '地区',  width: '120', },
    { istrue: true, prop: 'orderMenuDate', label: '时间',  width: '160', formatter: (row) => formatTime(row.orderMenuDate, 'YYYY.MM.DD') },
    { istrue: true, prop: 'menuLabel', label: '餐别', width: '160' },

    { istrue: true, prop: 'gysName', label: '供应商名称', width: '160' },

    { istrue: true, prop: 'menuType', label: '类型',  width: '160' },
    { istrue: true, prop: 'menuName', label: '菜单',  width: '160' },
    { istrue: true, prop: 'price', label: '价格', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'costPrice', label: '成本价', width: '100', sortable: 'custom', },

    { istrue: true, prop: 'stock', label: '库存', width: '100', sortable: 'custom' },

    // { istrue: true, prop: 'salesvolume', label: '销量', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'menuStatus', label: '状态', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'menuLabel', label: '标签', width: '100', sortable: 'custom' },
    {
        istrue: true, type: "button", label: '操作', width: "200",
        btnList: [
            { label: "编辑", handle: (that, row) => that.editBaseMenu(row,2), permission: 'foodmenu:edit', },
            { label: "删除", handle: (that, row) => that.delBaseMenu(row,2), permission: 'foodmenu:del', }
        ]
    }
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = startDate;
//const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name: "Users",
    components: { container, yhVxetable, YhImgUpload },
    data () {
        return {
            onetab: 'two',
            fileList:[],
            dialogEdit: false,
            editForm: {},
            form: {
                menuTypeList: [],
            },
            dialogMenuType: false,
            uploadLoading: false,
            dialogVisibleData: false,
            that: this,
            filter: {
                // timerange: endDate,
                // startTime: startDate,
                // endTime: endDate,
                keyWord: null,
                Enabled: true,
                menuStatus: '在售',
                zoneName: ''
            },
            platformlist: platformlist,
            tableCols: tableCols,
            tableColstwo: tableColstwo,
            tableDatatwo: [],
            quyuList: [],
            tableHandles: null,
            tableData: [],
            total: 0,
            pager: { OrderBy: "", IsAsc: false },
            pagertwo: { OrderBy: "orderMenuDate", IsAsc: true },
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            sels: [],
            gysList: [],
        };
    },
    mounted () {
        // this.getOrderMenuType();
        this.onSearch();
    },
    methods: {
        async getgys(){
            const res = await getOrderFoodMenuProvier();
            if(!res.success){
                return
            }
            this.gysList = res.data;
        },
        async getquyu(){
            const res = await getAreaSetList({getLevel: 1});
            if(!res.success){
                return
            }
            this.quyuList = res.data;
            if(!this.filter.zoneName){
                this.filter.zoneName = this.quyuList[0];
            }
        },
        async onExport() {
            window.open("/static/excel/profit/导入模板.xlsx", "_blank");
        },
        // async openDialogMenuType () {
        //     this.dialogMenuType = true;
        //     await this.getOrderMenuType();
        // },
        async updateBaseMenu () {
            let p = this.editForm;
            if(this.onetab == 'one'){
                const res = await updateBaseMenuAsync(p);
                if (res.data) {
                    this.$message({ type: 'success', message: '保存成功!' });
                    this.dialogEdit = false;
                    await this.getList();
                }
            }else if(this.onetab == 'two'){
                const res = await updateBaseMenuAsync(p);
                if (res.data) {
                    this.$message({ type: 'success', message: '保存成功!' });
                    this.dialogEdit = false;
                    await this.getListtwo();
                }
            }

        },
        // async saveMenuType () {
        //     let par = this.form.menuTypeList;
        //     const res = await saveMenuTypeAsync(par);
        //     if (res.data) {
        //         this.$message({ type: 'success', message: '保存成功!' });
        //         this.dialogMenuType = false;
        //         await this.getOrderMenuType();
        //         await this.getList();
        //     }
        // },
        addMenuType () {
            this.form.menuTypeList.push({
                menuType: '',
                orderId: null
            });
        },
        removeMenuType (index) {
            this.form.menuTypeList.splice(index, 1)
        },
        editBaseMenu (row) {
            if(this.onetab == 'two'){
                this.getbasedetail(row)
                return
            }
            // this.getOrderMenuType();
            this.editForm = {
                id: row.id,
                menuName: row.menuName,
                menuType: row.menuType,
                menuLabel: row.menuLabel,
                price: row.price,
                integral: row.integral,
                salesvolume: row.salesvolume,
                menuStatus: row.menuStatus,
                orderId: row.orderId,
                stock: row.stock,
            }
            this.dialogEdit = true;
        },
        async getbasedetail(row){
            const res = await getBaseManagerDetailAsync(row.id);
            this.editForm = res?.data;
            this.dialogEdit = true;
        },
        // async getOrderMenuType () {
        //     const res = await getOrderMenuTypeAsync();
        //     console.log("11111111111111", res)
        //     this.form.menuTypeList = res?.data;
        //     console.log(this.form.menuTypeList, 'dasda')
        // },
        async delBaseMenu (row, index) {
            this.$confirm('确定删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async _ => {
                    let par = {
                        id: row.id
                    }
                    if(this.onetab=='two'){
                        const res = await delBaseMenuAsync(par)
                        if (res.data) {
                            this.$message({ type: 'success', message: '删除成功!' });
                            await this.getListtwo();
                        }
                    }else if(this.onetab=='one'){
                        const res = await delBaseMenuAsync(par)
                        if (res.data) {
                            this.$message({ type: 'success', message: '删除成功!' });
                            await this.getList();
                        }
                    }

                })
                .catch(_ => { });

        },
        async onSearch () {
            await this.getquyu();
            await this.getgys();
            if(this.onetab=='two'){
                // this.$refs.pagertwo.setPage(1);
                await this.getListtwo();
            }else if(this.onetab=='one'){
                this.$refs.pager.setPage(1);
                await this.getList();
            }
        },
        async sortchange (column) {

            if(this.onetab=='one'){
                if (!column.order){
                this.pager = {};
                }else{
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                this.$refs.pager.setPage(1);

            }else if(this.onetab == 'two'){
                if (!column.order){
                    this.pagertwo = {};
                }else{
                    this.pagertwo = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                // this.$refs.pagertwo.setPage(1);
            }
            await this.onSearch();
        },
        async getListtwo () {
            // if (this.filter.timerange) {
            //     this.filter.startTime = this.filter.timerange;
            //     this.filter.endTime = this.filter.timerange;
            // }
            var that = this;
            this.listLoading = true;
            // var pager = this.$refs.pagertwo.getPager();
            const params = {  ...this.pagertwo, ...this.filter, };
            const res = await getPageBaseMenuAsync(params).then(res => {
                that.total = res.data?.total;
                that.tableDatatwo = res.data?.list;
            });
            this.listLoading = false;
        },
        async getList () {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange;
                this.filter.endTime = this.filter.timerange;
            }
            var that = this;
            this.listLoading = true;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, ...this.myfilter };
            const res = await getPageBaseMenuAsync(params).then(res => {
                that.total = res.data?.total;
                that.tableData = res.data?.list;
            });
            this.listLoading = false;
        },
        async uploadFile () {
            if (this.fileList.length ==0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", this.fileList[0].raw);
            //form.append("platform", 1);
            var res = await importBaseMenuAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
            this.dialogVisibleData = false;
            this.fileList = [];
        },
        async uploadChange (file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
            // console.log(this.$refs.upload,'dsad',fileList)
            // if (fileList && fileList.length > 0) {
            //     var list = [];
            //     for (var i = 0; i < fileList.length; i++) {
            //         if (fileList[i].status == "success")
            //             list.push(fileList[i]);
            //         else
            //             list.push(fileList[i].raw);
            //     }
            //     this.fileList = list;
            // }
        },
        uploadRemove (file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload () {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
    }
})
</script>
<style scoped>
::v-deep .el-table__fixed-footer-wrapper tbody td {
    color: blue;
}
</style>

