<template>
  <MyContainer>
    <template>
      <el-tabs v-model="activeName" style="height: 94%" @tab-click="tabSwitching">
        <el-tab-pane label="统计" name="first" style="height: 100%" :lazy="true">
          <lossDataStatisticsOne res="reslossDataStatisticsOne" @changeTab="changeTab" @updateCondition="updateCondition"></lossDataStatisticsOne>
        </el-tab-pane>
        <!-- <el-tab-pane label="责任" name="second" style="height: 100%" :lazy="true">
          <summaryResponsibilityTwo :firstTabProps="firstTabProps" v-if="activeName == 'second'"></summaryResponsibilityTwo>
        </el-tab-pane> -->
        <el-tab-pane label="责任" name="second" style="height: 100%" :lazy="true">
          <summaryResponsibilityTwo ref="summaryResponsibilityTwo"></summaryResponsibilityTwo>
        </el-tab-pane>
        <el-tab-pane label="审核" name="third" style="height: 100%" :lazy="true">
          <managementAccountabThr></managementAccountabThr>
        </el-tab-pane>
        <el-tab-pane label="图片" name="forth" style="height: 100%" :lazy="true">
          <imageDetailInfo></imageDetailInfo>
        </el-tab-pane>
        <el-tab-pane label="聊天记录核查" name="fifth" style="height: 100%" :lazy="true">
          <chatRecordCheck></chatRecordCheck>
        </el-tab-pane>
      </el-tabs>
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import lossDataStatisticsOne from '@/views/order/LossOrderCostStatistics/childPages/lossDataStatisticsOne.vue'
import summaryResponsibilityTwo from '@/views/order/LossOrderCostStatistics/childPages/summaryResponsibilityTwo.vue'
import managementAccountabThr from '@/views/order/LossOrderCostStatistics/childPages/managementAccountabThr.vue'
import imageDetailInfo from '@/views/order/LossOrderCostStatistics/childPages/imageDetailInfo.vue'
import chatRecordCheck from '@/views/order/LossOrderCostStatistics/childPages/chatRecordCheck.vue'

export default {
  name: 'Vue2demoIndex',
  components: { lossDataStatisticsOne, summaryResponsibilityTwo, managementAccountabThr, MyContainer, imageDetailInfo, chatRecordCheck },
  data() {
    return {
      activeName: 'first',
      dataFromFirstTab: null,
      firstTabProps:{},
    };
  },

  mounted() {

  },

  methods: {
    updateCondition(params){
      this.firstTabProps = params
    },
    tabSwitching(tab){
      this.$nextTick(() => {
        if(this.activeName == 'second'){
          this.$refs.summaryResponsibilityTwo.tabSwitch(this.firstTabProps);
        }
      })
      this.activeName = tab.name;
    },
    changeTab(val) {
      this.activeName = val
    }
  },
};
</script>

<style lang="scss" scoped></style>
