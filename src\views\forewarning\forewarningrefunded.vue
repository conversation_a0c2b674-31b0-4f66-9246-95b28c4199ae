<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;width: 160px;">
            <el-input v-model.trim="filter.orderNoInner" clearable placeholder="内部订单号"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px;">
            <el-input v-model.trim="filter.orderNo" clearable placeholder="线上订单号"/>
          </el-button>
          <el-button style="padding: 0; border: none;float: left;">
            揽收剩余
            <el-input v-model.trim="filter.remainingTime" style="width:60px" :controls="false" :precision="0"  :min="1" clearable maxlength="1"  @input="(v)=>(filter.remainingTime=v.replace(/^(0+)|[^\d]+/g, ''))"></el-input>
            时间
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.orderStatus" placeholder="订单状态" style="width: 120px">
                    <el-option label="已发货" :value="1" ></el-option>
                    <el-option label="发货中" :value="2" ></el-option>
                    <el-option label="异常" :value="3" ></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 250px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                              start-placeholder="开始发货时间" end-placeholder="结束发货时间"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 280px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                              start-placeholder="计划发货时间" end-placeholder="计划结束发货时间"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.sendWarehouse" placeholder="发货仓" style="width: 135px">
                    <el-option label="义乌市昀晗供应链管理有限公司" :value="1" ></el-option>
                    <el-option label="【南昌全品类仓】" :value="2" :key="2"></el-option>
                    <el-option label="【南昌定制仓】" :value="3" :key="3"></el-option>
                    <el-option label="【义乌圆通爆款仓】" :value="4" :key="4"></el-option>
                    <el-option label="【义乌圆通5楼】" :value="5" :key="5"></el-option>
                    <el-option label="【南昌裁剪仓】" :value="6" :key="6"></el-option>
                    <el-option label="【杭州分仓】" :value="7" :key="7"></el-option>
                    <el-option label="【义乌邮政仓】" :value="8" :key="8"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-input v-model.trim="filter.logisticsCompany" clearable placeholder="物流公司" style="width: 135px"/>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px;">
             <el-input v-model.trim="filter.logisticsOrder" clearable placeholder="请输入物流单号"/>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.platform" placeholder="平台" style="width: 135px">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" @click.native="getType(item.value)">
              </el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺" style="width: 135px">
              <el-option v-for="item in shopList" :key="item.value"  :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
              <el-date-picker style="width: 250px" v-model="filter.timerange3" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                              start-placeholder="开始支付时间" end-placeholder="结束支付时间"></el-date-picker>
          </el-button>
          <el-button type="primary" @click="onSearch">搜索</el-button>
        </el-button-group>
      </div>
    </template>
    <template>
      <ces-table :id="'forewarningrefunded202408041530'" ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading">
      </ces-table>
    </template>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
  </my-container>
</template>

<script>
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import { formatTime } from "@/utils/tools";
import { listToTree } from '@/utils'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
//查询，导出前的分组，导出，导入，通知对应负责人
import { getLogisticsWarningPageList, getLogisticsWarningGroupWareData, exportLogisticsWarningDataAyync, importLogisticsWarningDataAsync, noticeLogisticsWarning} from '@/api/warning/Warning'
import { arrayEach } from "xe-utils";

const tableCols = [
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', width: '100', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner' },
  { istrue: true, prop: 'orderNo', label: '线上订单号', width: '100', sortable: 'custom'},
  { istrue: true, prop: 'triggerSize', label: '触发规格', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'remainingTimeStr', label: '剩余揽收时间', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'orderStatus', label: '订单状态', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'sendGoodsTime', label: '发货时间', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'planSendGoodsTime', label: '计划发货时间', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'sendWarehouse', label: '发货仓', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'logisticsCompany', label: '物流公司', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'logisticsOrderNo', label: '物流单号', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'logisticsStatus', label: '物流状态', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'platform', label: '平台', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'shopName', label: '店铺名称', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'payTime', label: '支付时间', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'weight', label: '重量', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'isDingCrowdNotice', label: '是否群通知', width: '100',sortable: 'custom', formatter: (row) => row.isDingCrowdNotice ? "是" : "否" },
  { istrue: true, prop: 'isDingNotice', label: '是否钉钉通知', width: '100',sortable: 'custom', formatter: (row) => row.isDingNotice ? "是" : "否" },
  { istrue: true, prop: 'pretendStatus', label: '揽收状态', width: '100',sortable: 'custom', formatter: (row) => row.pretendStatus},
  { istrue: true, prop: 'dingCrowdNoticeTime', label: '群通知时间', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'dingNoticeTime', label: '钉钉通知时间', width: '100',sortable: 'custom' },
  { istrue: true, prop: 'pretendTime', label: '假揽时间', width: '100',sortable: 'custom' },
];

export default {
name: 'Users',
components: {MyContainer, cesTable, buschar, MySearch,MySearchWindow,MyConfirmButton,inputYunhan},
data() {
  return {
    filter:{
      //4为对应标签已退款
      searchType:4,
      orderNoInner: null,
      orderNo: null,
      remainingTime: null,
      orderStatus: null,
      logisticsCompany: null,
      platform: null,
      shopCode: null,
      sendWarehouse:null,
      logisticsOrder:null,
      timerange:[],
      sendStartTime:null,
      sendEndTime:null,
      timerange2:[],
      planSendStartTime:null,
      planSendEndTime:null,
      timerange3:[],
      payStartTime:null,
      payEndTime:null,
    },
    tableCols: tableCols,
    pageLoading: false,
    total: 0,
    warehouseWagesWarehouseList: [],
    pager: { orderBy:'',isAsc: true},
    summaryarry: {},
    that:this,
    list: [],
    sels : [],
    listLoading: false,
    uploadLoading:false,
    dialogVisible: false,
    shopList:[],
    warehouseList:[],
    internalnumber:null,
    onlinenumber:null,
    options1:[],
    options2:[],
    options:[{
      value:'天猫',
      label:'天猫'
    },{
      value:'拼多多',
      label:'拼多多'
    },{
      value:'阿里巴巴',
      label:'阿里巴巴'
    },{
      value:'抖音',
      label:'抖音'
    },{
      value:'京东',
      label:'京东'
    },{
      value:'淘工厂',
      label:'淘工厂'
    },{
      value:'淘宝',
      label:'淘宝'
    },{
      value:'苏宁',
      label:'苏宁'
    },{
      value:'1688',
      label:'1688'
    },{
      value:'分销',
      label:'分销'
    },{
      value:'快手',
      label:'快手'
    }
  ]
  };
},

async mounted() {
    this.onSearch()
  },
  //可异步获取数据
async created() {

  },

methods: {
  //搜索
  onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist();
    },
  //分页查询
  async getlist(){
      this.sels = [];
      this.selids = [];
      //开始发货时间
      this.filter.sendStartTime = null;
      this.filter.sendEndTime = null;
      if (this.filter.timerange && this.filter.timerange.length > 1) {
          this.filter.sendStartTime = this.filter.timerange[0];
          this.filter.sendEndTime = this.filter.timerange[1];
      }
      //计划发货时间
      this.filter.planSendStartTime = null;
      this.filter.planSendEndTime = null;
      if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
          this.filter.planSendStartTime = this.filter.timerange2[0];
          this.filter.planSendEndTime = this.filter.timerange2[1];
      }
      //开始支付时间
      this.filter.payStartTime = null;
      this.filter.payEndTime = null;
      if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
          this.filter.payStartTime = this.filter.timerange3[0];
          this.filter.payEndTime = this.filter.timerange3[1];
      }
      let num = parseInt(this.filter.remainingTime);
      if (isNaN(num)) {
        this.filter.remainingTime = null;
      } else {
        this.filter.remainingTime = num;
      }
      // let num1 = parseInt(this.filter.orderNoInner);
      // if (isNaN(num1)) {
      //   this.filter.orderNoInner = null;
      // } else {
      //   this.filter.orderNoInner = num1;
      // }
      if(this.filter.shopCode == ""){
        this.filter.shopCode = null
      }
      if(this.filter.platform == ""){
        this.filter.platform = null
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      switch(this.filter.sendWarehouse){
        case 1:this.filter.sendWarehouse = "义乌市昀晗供应链管理有限公司"; break;
        case 2:this.filter.sendWarehouse = "【南昌全品类仓】"; break;
        case 3:this.filter.sendWarehouse = "【南昌定制仓】"; break;
        case 4:this.filter.sendWarehouse = "【义乌圆通爆款仓】"; break;
        case 5:this.filter.sendWarehouse = "【义乌圆通5楼】"; break;
        case 6:this.filter.sendWarehouse = "【南昌裁剪仓】"; break;
        case 7:this.filter.sendWarehouse = "【杭州分仓】"; break;
        case 8:this.filter.sendWarehouse = "【义乌邮政仓】"; break;
      }
      switch(this.filter.orderStatus){
        case 1:this.filter.orderStatus = "已发货"; break;
        case 2:this.filter.orderStatus = "发货中"; break;
        case 3:this.filter.orderStatus = "异常"; break;
      }
      const params = {
        ...pager,
        ...page,
        ...this.filter
      }
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getLogisticsWarningPageList(params)
      this.listLoading = false;
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      // data.forEach(d => {
      //   d._loading = false;
      // })
      this.list = data;
      this.ordernumber();
      // this.summaryarry = res.data.summary;
  },
  //内部线上订单号数据
  async ordernumber(){
    this.options1=[];
    this.options2=[];
    let internalnumber = this.list.map(item => ({ orderNoInner: item.orderNoInner }));
    let onlinenumber = this.list.map(item => ({ orderNo: item.orderNo }));
    internalnumber.forEach(f=>{
      this.options1.push({value:f.orderNoInner,label:f.orderNoInner})
    });
    onlinenumber.forEach(f=>{
      this.options2.push({value:f.orderNo,label:f.orderNo})
    });
  },
  async getType(value){
    this.filter.shopCode = null
    switch(value){
        case "天猫": var val = 1; break;
        case "拼多多": var val = 2; break;
        case "阿里巴巴":var val = 4 ; break;
        case "抖音":var val = 6 ; break;
        case "京东":var val = 7 ; break;
        case "淘工厂":var val = 8 ; break;
        case "淘宝":var val = 9 ; break;
        case "苏宁":var val = 10 ; break;
        case "分销":var val = 11 ; break;
        case "1688":var val = 4 ; break;
        case "快手":var val = 20 ; break;
      }
    const res1 = await getAllShopList({platforms:[val]});
    this.shopList=res1.data?.map(item=>{return{value:item.shopCode,label:item.shopName};});
  },
    //排序查询
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
        this.onSearch();
    },
 },
};
</script>

<style lang="scss" scoped>

</style>
