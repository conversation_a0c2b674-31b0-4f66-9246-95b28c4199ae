import request from '@/utils/request'
const apiPrefix = `${process.env.VUE_APP_BASE_API_OperateManage}/operate/`

//查询订单处理信息
export const getOrderProcess = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderProcess', params, config)
}

//导出订单处理信息
export const exportOrderProcess = (params, config = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'ExportOrderProcess', params, config)
}

//展示规则
export const getOrderProcessRule = (params, config = {}) => {
    return request.post(apiPrefix + 'GetOrderProcessRule', params, config)
}

//删除规则
export const deleteOrderProcessRule = (params, config = {}) => {
    return request.post(apiPrefix + 'DeleteOrderProcessRule', params, config)
}

//新建规则
export const setOrderProcessRule = (params, config = {}) => {
    return request.post(apiPrefix + 'SetOrderProcessRule', params, config)
}

//编辑规则
export const editOrderProcessRule = (params, config = {}) => {
    return request.post(apiPrefix + 'EditOrderProcessRule', params, config)
}
