<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form
          class="ad-form-query"
          :inline="true"
          :model="Filter"
          @submit.native.prevent>
        </el-form>
      </template>
      <!--列表-->
      <ces-table ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='dahuixionglist'
                @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true'
           :tableCols='tableCols' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
        </el-table-column>
         <template slot='extentbtn'>
              <el-button-group>
              <el-button type="primary" @click="onSearch">刷新</el-button>
              <el-button type="primary" @click="addButton">添加店铺</el-button>
            </el-button-group>
          </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>


      <el-drawer title="编辑店铺" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit"  @click="onEditSubmit" />
      </div>
    </el-drawer>
    <el-drawer title="新增店铺" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="addVisible"
        direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
        <form-create :rule="autoformAdd.rule1" v-model="autoformAdd.fApi" :option="autoformAdd.options"/>
        <div class="drawer-footer">
            <el-button @click.native="addVisible = false">取消</el-button>
            <my-confirm-button type="submit"  @click="onaddSubmit" />
        </div>
    </el-drawer>
    </my-container>
</template>
<script>

import { getExcludingPackingAndWarehouseCost, addExcludingPackingAndWarehouseCost, deleteExcludingPackingAndWarehouseCost } from '@/api/bookkeeper/reportdayV2'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { ruleShop } from '@/utils/formruletools'
const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺', width: '500', sortable: 'custom' },
    { istrue: true, prop: 'excludingPacking', label: '是否计算包装费', width: '300', sortable: 'custom', 
        formatter: (row) => row.excludingPacking == '0' ? '否' : row.excludingPacking == '1' ? '是' : ''  },
    { istrue: true, prop: 'excludingWarehouseCost', label: '是否计算出仓成本', width: '300', sortable: 'custom', 
        formatter: (row) => row.excludingWarehouseCost == '0' ? '否' : row.excludingWarehouseCost == '1' ? '是' : '' },
    { istrue: true, prop: 'qyDate', label: '启用时间', width: '300', sortable: 'custom' },
    { istrue: true, type: "button", label: '操作', width: "auto", btnList: [{ label: "编辑", handle: (that, row) => that.onEditMethod(row) }, { label: "删除", handle: (that, row) => that.deleteButton(row) }] }
];
export default {
name: "ExcludingPackagingAndWarehouseCosts",
components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
data() {
    return {
        that:this,
        editLoading:false,
        addVisible:false,
        Filter: {
            currentPage: 1,
            pageSize: 50,
            orderBy: "shopName",
            isAsc: false,
        },
        shopList:[],
        dahuixionglist: [],
        tableCols:tableCols,
        total: 0,
        listLoading: false,
        pageLoading: false,
        selids:[],
        editVisible:false,
        autoform:{
                fApi:{},
                options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                rule:[]
        },
        autoformAdd:{
                fApi:{},
                options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
                rule:[]
        },
    };
},
async mounted() {
    await this.initform();
    this.onSearch();
},
methods: {
    onEditMethod(row) {
        this.EditButton(row);
    },
    addButton(){
        this.addVisible=true;
    },
    //新增-提交
    async onaddSubmit(){
        await this.autoformAdd.fApi.validate(async (valid, fail) => {
        if(valid){
            const formData = this.autoformAdd.fApi.formData();
            const res = await addExcludingPackingAndWarehouseCost(formData);
            await this.autoformAdd.fApi.resetFields()
            if(res.success){
                this.$message.success('添加成功！');
                this.getList();
                this.addVisible=false;
            }
            }else{}
        })
    },
        //编辑
    EditButton(row){
        this.editVisible = true
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length >0)
            this.autoform.fApi.resetFields()
            this.$nextTick(async() =>{
        await this.autoform.fApi.setValue(row)
        })
    },
    //编辑-提交
    async onEditSubmit() {
        await this.autoform.fApi.validate(async (valid, fail) => {
            if(valid){
                const formData = this.autoform.fApi.formData();
                const res = await addExcludingPackingAndWarehouseCost(formData);
                if(res.code==1){
                this.$message.success('修改成功！');
                    this.getList();
                this.editVisible=false;
                }
            }else{}
        })
    },
    //表单初始化
    async initform() {
        this.autoform.rule = [
            { type: 'select', field: 'shopId', title: '店铺', value: '', ...await ruleShop(), props: { filterable: true, clearable: true } },
            { type: 'select', field: 'excludingPacking', title: '是否计算包装费', validate: [{ required: true, message: "请选择是否计算包装费", trigger: "change" }],  value: '', options: [{value:'1', label:'是'},{value:'0', label:'否'}], props: { filterable: true, clearable: true } },
            { type: 'select', field: 'excludingWarehouseCost', title: '是否计算出仓成本', validate: [{ required: true, message: "请选择是否计算出仓成本", trigger: "change" }], value: '', options: [{value:'1', label:'是'},{value:'0', label:'否'}], props: { filterable: true, clearable: true } },
            {
            type: "DatePicker",
            field: "qyDate",
            title: "启用时间",
            value: "", // 单选日期默认值为字符串
            props: {
                type: "date", // 修改为单选日期
                format: "yyyy-MM-dd", // 日期格式
                placeholder: "请选择启用时间",
            },
            validate: [
                { required: true, message: "请选择启用时间", trigger: "change" }
            ]
            }
        ],
        this.autoformAdd.rule1 = [
            { type: 'select', field: 'shopId', title: '店铺', value: '', ...await ruleShop(), props: { filterable: true, clearable: true } },
            { type: 'select', field: 'excludingPacking', title: '是否计算包装费', validate: [{ required: true, message: "请选择是否计算包装费"}], value: '', options: [{value:'1', label:'是'},{value:'0', label:'否'}], props: { filterable: true, clearable: true } },
            { type: 'select', field: 'excludingWarehouseCost', title: '是否计算出仓成本', 
            validate: [{ required: true, message: "请选择是否计算出仓成本", trigger: "change" }], value: '', options: [{value:'1', label:'是'},{value:'0', label:'否'}], props: { filterable: true, clearable: true } },
            {
            type: "DatePicker",
            field: "qyDate",
            title: "启用时间",
            value: "", // 单选日期默认值为字符串
            props: {
                type: "date", // 修改为单选日期
                format: "yyyy-MM-dd", // 日期格式
                placeholder: "请选择启用时间",
            },
            validate: [
                { required: true, message: "请选择启用时间", trigger: "change" }
            ]
            }
        ]
    },
    //删除
    async deleteButton(row){
        var that=this;
        this.$confirm("此操作将删除此数据?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(async() => {
        let del= await deleteExcludingPackingAndWarehouseCost({shopId:row.shopId})
        if (del?.success) {
                    that.$message({ message: '已删除', type: "success" });
                    that.onRefresh()
                }
                else {
                    that.$message({ message: '发生异常，请刷新后重试', type: "error" });
                }
            });
    },
    //排序
    sortchange({ order, prop }) {
        if (prop) {
          this.Filter.orderBy = prop
          this.Filter.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
    },
    //刷新
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
        this.$refs.pager.setPage(1);
        this.getList();
    },
    //查询
    async getList(){
        const params = {
            ...this.Filter,
            isCrossBorder: 0
        };
        this.listLoading = true;
        const res = await getExcludingPackingAndWarehouseCost(params);
        this.listLoading = false;
        this.total = res.data.total
        this.dahuixionglist = res.data.list;
        this.dahuixionglist.forEach(f=>{
            f.qyDate = dayjs(f.qyDate).format('YYYY-MM-DD')
        })
    },
    selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
            this.selids.push(f.id);
        })
    },
    //每页数量改变
    Sizechange(val) {
        this.Filter.currentPage = 1;
        this.Filter.pageSize = val;
        this.getList()
      },
    //当前页改变
    Pagechange(val) {
    this.Filter.currentPage = val;
    this.getList()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
background-color: #fff;
}
</style>
