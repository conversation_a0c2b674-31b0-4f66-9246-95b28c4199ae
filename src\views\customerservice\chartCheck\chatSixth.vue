<template>
  <my-container>
    <!--顶部操作-->
    <div class=".top">
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="创建时间:">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content"  clearable>
            <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="Filter.shopNameList" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in shopList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="聊天账号:">
         <el-select v-model="Filter.chatAccountList" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in cAccountList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="账号使用人:">
          <el-select v-model="Filter.userNameList" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in uNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组:">
          <el-select v-model="Filter.groupNameList" placeholder="请选择" class="el-select-content" filterable multiple clearable collapse-tags>
            <el-option v-for="item in gNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="线上订单号:">
          <el-input maxlength="50" v-model.trim="Filter.orderNo" clearable />
        </el-form-item>
        <el-form-item label="宝贝ID:">
          <el-input maxlength="20" v-model.trim="Filter.proId" clearable />
        </el-form-item>
        <el-form-item label="标识:">
          <!-- <el-select v-model="Filter.likes" placeholder="请选择" class="el-select-content" clearable >
            <el-option v-for="item in likeList" :key="item.vlaue" :label="item.label" :value="item.vlaue" /> -->


            <el-select v-model="Filter.likes" placeholder="请选择" class="el-select-content" clearable>
              <el-option
                  v-for="item in likeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
           </el-select>

        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
            <el-dropdown style="box-sizing: border-box; margin-left:6px;" v-if="checkPermission(['api:Customerservice:UnPayOrder:AddQualityChatInfoAsync'])" size="mini" split-button type="primary" icon="el-icon-share"
                          @command="handleCommand"> 导出 
                        <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">列表数据</el-dropdown-item>
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="b">聊天数据</el-dropdown-item>
                        <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="c">提取话术</el-dropdown-item>
                        <el-dropdown-item class="Batcoperation" style="height: 10px"></el-dropdown-item>
                        </el-dropdown-menu>
                  </el-dropdown>
          <!-- <el-button type="primary" @click="onExport" v-if="checkPermission(['api:Customerservice:UnPayOrder:AddQualityChatInfoAsync'])">导出</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <div class="uptime">数据更新时间: {{ upDataTime }}</div>

    <!--列表-->
    <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="true" :summaryarry="summaryarry" :tableCols="tableCols" :loading="listLoading" style="width: 100%;
    height: calc(100% - 15%); margin: 0">
    </Ces-table>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>

<!-- 聊天记录 -->
<ChartQualityDialog :v-if="qualityDialogVisible" :isShow="qualityDialogVisible" @closeDialog="qualityDialogVisible = false" ref="recordithRef"></ChartQualityDialog>

<!-- 话术提取 -->
<ExtractDialogueDialog :v-if="extractDialogueVisible" :isShow="extractDialogueVisible" @closeDialog="extractDialogueVisible = false" ref="auditChartithRef"></ExtractDialogueDialog>

<!-- 查看提取话术 -->
<ViewDialogueDialog :v-if="viewDialogVisible" :isShow="viewDialogVisible" @closeDialog="viewDialogVisible = false" ref="viewithRef"></ViewDialogueDialog>
    <!-- 订单日志信息 -->
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" />
    </el-dialog>

  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

import dayjs from "dayjs";
import { formatTime } from "@/utils";

import {getQualityChatOrderPageList,getQualityChatOrderShopNameList,getQualityChatOrderChatAccountList,getQualityChatOrderUserNameList,getQualityChatOrderGroupNameList,qualityChatOrderExport,
} from "@/api/customerservice/chartCheck";
import ChartQualityDialog from "@/views/customerservice/chartCheck/SalesDialog/ChartQualityDialog"
import ExtractDialogueDialog from "@/views/customerservice/chartCheck/SalesDialog/ExtractDialogueDialog"
import ViewDialogueDialog from "@/views/customerservice/chartCheck/SalesDialog/ViewDialogueDialog"
import { formatLinkProCode } from "@/utils/tools";



//平台下拉
 const platformTypeList=[
        { name: "天猫", value: 1 }
 ];

const tableCols = [
  {
    istrue: true,
    prop: "platform",
    label: "平台",
    width: "70",
     formatter:(row)=>
      {
        return  platformTypeList.filter(item=>item.value==row.platform)[0].name 
      },
  },
  {
    istrue: true,
    prop: "shopName",
    label: "店铺",
    width: "150",
  },
  // {
  //   istrue: true,
  //   prop: "insertTime",
  //   label: "创建时间",
  //   width: "150",
  //   sortable: "custom",
  //    formatter: (row) => {
  //     return row.insertTime?formatTime(row.insertTime, "YYYY-MM-DD"):"";
  //   },
  // },
  {
    istrue: true,
    prop: "orderTime",
    label: "创建时间",
    width: "150",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "orderNo",
    label: "线上订单号",
    width: "180",
     type: 'click',
    handle: (that, row) => that.showLogDetail(row)
  },
  {
    istrue: true,
    prop: "proId",
    label: "宝贝ID",
    width: "130",
      type: "html",
    formatter: (row) => formatLinkProCode(row.platform, row.proId),
  },
  {
    istrue: true,
    prop: "chatAccount",
    label: "聊天账号",
    width: "150",
  },
    {
    istrue: true,
    prop: "userName",
    label: "使用账号人",
    width: "100",
    sortable: "custom",
  },
    {
    istrue: true,
    prop: "groupName",
    label: "分组",
    width: "120",
    sortable: "custom",
  },
   {
    istrue: true,
    prop: "groupManager",
    label: "组长",
    width: "100",
    sortable: "custom",
  },
   {
    istrue: true,
    prop: "likes",
    label: "标识",
    width: "80",
    sortable: "custom",
    formatter: (row) => {
      return row.likes > 0 ? '已提取' : "";
    },
  },
  {
    istrue: true,
    type: "button",
    label: "操作",
    align: "center",
    fixed: "right",
    width:"300",
    btnList: [
      { label: "查看聊天记录", handle: (that, row) => that.showDetail(row) },
      { label: "提取话术", handle: (that, row) => that.auditChart(row),permission: "api:Customerservice:UnPayOrder:QualityChatOrderExport"},
      { label: "查看提取话术", handle: (that, row) => that.viewAuditChart(row),},
    ],
  },
];
export default {
  name: "chatSixth",
  components: {
    MyContainer,
    CesTable,
    OrderActionsByInnerNos,
    ChartQualityDialog, //正向聊天聊天记录
    ExtractDialogueDialog,//敷衍审核
    ViewDialogueDialog,
  },
  data() {
    return {
      that: this,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        platform:null,
        shopName:null,
        chatAccount:null,
        orderNo:null,
        proId:null,
        userName:null,
        groupName:null,
        likes: null
      },
      platformTypeList:platformTypeList,
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      shopList: [],
      uNameList:[],
      gNameList:[],
      cAccountList:[],
      likeList:[
        {label:'已提取',value:1},
        {label:'未提取',value:0},
      ],
      qualityDialogVisible:false,//正向聊天的聊天记录
      extractDialogueVisible:false,//提取话术
      viewDialogVisible:false, //查看提取话术
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      upDataTime: "",
      dialogHisVisible: false,
      orderNo: '',
    };
  },
  async mounted() {
         await this.init(); 
          await this.onSearch();
  },
  methods: {
      datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-4);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.Filter.timerange=[];
        this.Filter.timerange[0]=this.datetostr(date1);
        this.Filter.timerange[1]=this.datetostr(date2);
      },
     //查看线上订单号记录
    showLogDetail (row) {
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },
    showDetail(row) {     //查看聊天记录
      this.$refs.recordithRef.dataJson=row
      this.$refs.recordithRef.tableData=this.tableData
      // this.recorddialogVisible = true;
      this.qualityDialogVisible = true;
    },
    auditChart(row) {  //提取话术 
      this.$refs.auditChartithRef.dataJson=row
      this.$refs.auditChartithRef.tableData=this.tableData
      this.extractDialogueVisible = true;
    }, 
    viewAuditChart(row) {  //查看提取话术 
      this.$refs.viewithRef.dataJson=row
      this.$refs.viewithRef.tableData=this.tableData
      this.viewDialogVisible = true;
    }, 
    

    // 查询
    onSearch() {
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.gettrainplanList();
        this.getSelectOptionList();
      });
    },
     //头部下拉列表
    async getSelectOptionList()
    {
      const para = { ...this.Filter };
        if (this.Filter.timerange) {
          para.timeStart = this.Filter.timerange[0];
          para.timeEnd = this.Filter.timerange[1];
        }
          const param={
            platform:para.platform,
            timeStart:para.timeStart,
            timeEnd:para.shopList
          }
        const resShop=await getQualityChatOrderShopNameList(param); //获得店铺下拉信息
        this.shopList=resShop.data;
      
        const resAccount=await getQualityChatOrderChatAccountList(param) //获取聊天账号信息
        this.cAccountList=resAccount.data;

        const resUserName=await getQualityChatOrderUserNameList(param) //获取正向聊天账号使用人
        this.uNameList=resUserName.data;
        
        const resGroupName=await getQualityChatOrderGroupNameList(param) //获取正向聊天分、分组
        this.gNameList=resGroupName.data;

    },
 
    getCondition() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.timeStart = this.Filter.timerange[0];
        para.timeEnd = this.Filter.timerange[1];
      }
      // if (this.Filter.shopName) para.shopName = this.Filter.shopName;
      // else para.shopName = null;
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async gettrainplanList() {
      var params = this.getCondition();
      console.log(params)
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getQualityChatOrderPageList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const resData = res.data.list;
      this.upDataTime = res.data.extData.dataUpdateTime;
      resData.forEach((d) => {
        d._loading = false;
      });
      this.tableData = resData;
      this.summaryarry = res.data.summary;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    async onExport(type) {
          var params = this.getCondition();
          if (params === false) {
            return;
          }
           params.dataType= type;  // 0：默认，1：带聊天记录
          var res = await qualityChatOrderExport(params);
          if(res?.success){
            this.$message({ message: res.msg, type: "success" });
          }
          // const aLink = document.createElement("a");
          // let blob = new Blob([res], { type: "application/vnd.ms-excel" });
          // aLink.href = URL.createObjectURL(blob);
          // aLink.setAttribute(
          //   "download",
          //   "正向聊天_" + new Date().toLocaleString() + ".xlsx"
          // );
          // aLink.click();
    },
    async handleCommand(command) {
            // if (this.selids.length == 0 && command != 'x') {
            //     this.$message({ type: 'warning', message: "请选择任务" });
            //     return;
            // }
            switch (command) {
                //列表数据
                case 'a':
                    await this.onExport(0)
                    break;
                //聊天数据
                case 'b':
                   await this.onExport(1)
                    break;
                //提取话术
                case 'c':
                   await this.onExport(2)
                    break;
            }
        },
  },
};
</script>

<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
