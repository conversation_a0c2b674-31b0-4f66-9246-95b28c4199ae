<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <el-date-picker v-model="paymentTimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions" :clearable="false"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 1)">
        </el-date-picker>
        <el-date-picker v-model="dataTimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="付款开始时间" end-placeholder="付款结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime($event, 2)">
        </el-date-picker>
        <el-select v-model="ListInfo.platForm" placeholder="平台" class="publicCss" clearable>
          <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.shopCode" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <div class="publicCss">
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="150px"
            placeholder="线上单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
            @callback="callbackGoodsCode" title="线上单号">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-select v-model.trim="styleCode" multiple collapse-tags filterable remote reserve-keyword
          placeholder="款式编码" @paste.native="handlePaste" clearable :remote-method="remoteMethod"
          class="publicCss" style="width: 165px;" v-loading="searchloading">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.proCode" placeholder="产品ID" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'customizationCost202408041417'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, platformlist, formatPlatform, formatTime } from '@/utils/tools'
import { pageCodeSalesThemeAnalysis_CustomCostAsync, exportCodeSalesThemeAnalysis_CustomCostAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getListByStyleCode } from "@/api/inventory/basicgoods"
const formatOrderType = (row) => {
  let type = '';
  switch (row) {
    case 0:
      type = '普通订单';
      break;
    case 1:
      type = '补发订单';
      break;
    case 2:
      type = '供销plus';
      break;
    case 3:
      type = '其他';
      break;
  }
  return type;
};
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '日期', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: '80', type: 'orderLogInfo', orderType: 'orderNoInner' },
  { istrue: true, prop: 'orderType', label: '订单类型', sortable: 'custom', width: '100', formatter: (row) => formatOrderType(row.orderType) },
  { istrue: true, prop: 'orderNo', label: '线上单号', sortable: 'custom', width: '150' },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '130', },
  { istrue: true, prop: 'platform', fix: true, label: '平台', sortable: 'custom', width: '100', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, prop: 'timeSend', label: '发货日期', sortable: 'custom', width: '100', formatter: (row) => formatTime(row.timeSend, 'YYYY-MM-DD'), },
  { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: '100', formatter: (row) => formatTime(row.timePay, 'YYYY-MM-DD'), },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'styleCode', label: '款式编码', sortable: 'custom', width: '100', formatter: (row) => !row.styleCode ? " " : row.styleCode },
  { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'cost', label: '成本价', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'qty', label: '销售数量', sortable: 'custom', width: '100', },
  { istrue: true, prop: 'giftQty', label: '赠品数量', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'actualAmount', label: '实发金额', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'salesAmount', label: '销售金额', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'isCost', label: '商品成本价（正确）', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'isCostV2', label: '商品成本价（正确）2', sortable: 'custom', width: '100', tipmesg: '第二次更改的成本价' },
]
export default {
  name: "customizationCost",
  components: {
    MyContainer, vxetablebase, inputYunhan,
  },
  data() {
    return {
      styleCode: [],
      searchloading: false,
      options: [],
      summaryarry: {},
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        payStartDate: null,
        payEndDate: null,
        dataStartDate: null,
        dataEndDate: null,
        platForm: null,
        shopCode: null,
        orderNo: null,
        goodsCode: null,
        styleCode: null,
        proCode: null,
      },
      paymentTimeRanges: [],
      dataTimeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.platformlist = this.platformlist.filter(item => item.value != 0)
    await this.getList()
  },
  methods: {
    //款式编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading = true
        this.options = [];
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    handlePaste(event) {
      event.preventDefault();
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');
      const trimmedText = pastedText.replace(/\s/g, '');
      document.execCommand('insertText', false, trimmedText);
    },
    //线上单号
    callbackGoodsCode(val) {
      this.ListInfo.orderNo = val;
    },
    async exportProps() {
      this.loading = true
      this.ListInfo.styleCode = this.styleCode ? this.styleCode.join() : ''
      const { data } = await exportCodeSalesThemeAnalysis_CustomCostAsync(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '定制成本' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async changeTime(e, val) {
      if (val == 1) {
        this.ListInfo.payStartDate = e ? e[0] : null
        this.ListInfo.payEndDate = e ? e[1] : null
      } else {
        this.ListInfo.dataStartDate = e ? e[0] : null
        this.ListInfo.dataEndDate = e ? e[1] : null
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.paymentTimeRanges && this.paymentTimeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.payStartDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.payEndDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.paymentTimeRanges = [this.ListInfo.payStartDate, this.ListInfo.payEndDate]
      }
      this.loading = true
      this.ListInfo.styleCode = this.styleCode ? this.styleCode.join() : ''
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await pageCodeSalesThemeAnalysis_CustomCostAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
        this.tableData.forEach(item => {
          if (item.timeSend == "1970-01-01 00:00:00") {
            item.timeSend = ""
          }
          if (item.timePay) {
            item.timePay = dayjs(item.timePay).format('YYYY-MM-DD')
          }
        })
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 35px;
}
</style>
