<template>
  <MyContainer>
    <template #header>
     <el-row style="margin-bottom: 10px;">
      <el-input class="marginleft" v-model="filter.styleCode" placeholder="系列编码" clearable style="width: 250px" maxlength="50"></el-input>
      <el-select v-model="filter.dockUserId" placeholder="请选择对接人" style="width: 220px;"
          @change="changeAssignment" clearable filterable>
          <el-option v-for="item in procurementList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
      </el-select>
      <el-select class="marginleft" v-model="filter.wxAccountName" clearable style="width: 250px" filterable  placeholder="客服账号" >
        <el-option v-for="(item,i) in keserlist" :key="i" :label="item.wxAccountName" :value="item.wxAccountName" />
      </el-select>
      <el-button class="marginleft"  type="primary" @click="onSearch">查询</el-button>
      <el-button class="marginleft"  type="primary" @click="onebind">一键绑定</el-button>
      <el-button class="marginleft"  type="primary" @click="onebindno">一键解绑</el-button>
      <el-button class="marginleft"  type="primary" @click="onCreateSer">创建客服账号</el-button>
     </el-row>
    </template>
    <template>
     <cesTable :id="'wecharbind202408041629'" ref="detailTable" :tableData="list" :tableCols="tableCols" :is-index="true" :that="that"
         :showsummary="true" style="width: 100%; height: 650px" @sortchange='sortchange' @checkbox-range-end="checkboxrange" class="detail">
     </cesTable>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <vxe-modal title="创建客服账号" v-model="serviceAccountshow" :width="1300" mask-closable draggable	>
      <template #default>
        <el-button class="marginleft"  type="primary" @click="creoneser">新增一行</el-button>

        <vxe-table
          border
          ref="diaxTable"
          resizable
          show-overflow
          keep-source
          :data="keserlist"
          height="500px"
          :edit-rules="validRules"
          :edit-config="{trigger: 'click', mode: 'row', showStatus: true}">
          <vxe-column type="seq" width="60"></vxe-column>

          <vxe-column field="wxAccountName" title="客服账号名称" :edit-render="{}" width="160">
            <template #edit="{ row }">
              <vxe-input v-model="row.wxAccountName" maxlength="50" type="text" placeholder="客服账号名称" @change="$refs.diaxTable.updateStatus({row})"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column field="dockUserId" title="对接人" :edit-render="{}" width="140">
            <template #default="{ row }">
              <span>{{ idchangeAssignment(row.dockUserId, keserlist.indexOf(row)) }}</span>
            </template>
            <template #edit="{ row }">
              <vxe-select v-model="row.dockUserId" placeholder="请选择对接人"
                     clearable filterable transfer @change="idtoname(row)">
                <vxe-option v-for="item in procurementList" :key="item.value" :label="item.label" :value="item.value"></vxe-option>
              </vxe-select>
            </template>
          </vxe-column>
          <vxe-column field="url" title="对接链接" :edit-render="{}" width="700">
           <template #edit="{ row }">
              <vxe-input v-model="row.url" type="text" maxlength="100" placeholder="对接链接"></vxe-input>
            </template>
          </vxe-column>

          <vxe-column title="操作"  #default="{ row }">
           <el-button class="marginleft"  type="danger" @click="delserlist(keserlist.indexOf(row))">删除</el-button>
          </vxe-column>
        </vxe-table>
        <div style="display: flex; width: 100%; margin-top: 10px">
         <el-button style="margin-left: auto"  @click="serviceAccountshow = false">取消</el-button>
         <el-button   type="primary" @click="saveserlist">保存</el-button>
        </div>
        <!-- <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="nameTotal"
            @page-change="namePagechange" @size-change="nameSizechange" style="margin-top: 40px;" /> -->
      </template>
    </vxe-modal>

    <el-dialog title="一键绑定" :visible.sync="showkefusel" width="30%" v-dialogDrag>
     <span>
      <el-select class="marginleft" v-model="wxAccountId" clearable style="width: 250px" filterable  placeholder="客服账号" >
        <el-option v-for="(item,i) in keserlist" :key="i" :label="item.wxAccountName" :value="item.id" />
      </el-select>
     </span>
     <span slot="footer" class="dialog-footer">
       <el-button @click="showkefusel = false">关闭</el-button>
       <el-button class="marginleft"  type="primary" @click="subkefutrue">确认</el-button>
     </span>
  </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import VXETable from 'vxe-table'
import {
    getBrandUsers,
    getProviderStyleWXAccountList,
    saveProviderStyleWXAccount,
    getProviderStyleWXBindPageList,
    setProviderStyleWXBind
}
    from '@/api/openPlatform/ProviderQuotation'
const tableCols = [
    { istrue: true, type: 'checkbox' },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom' },
    { istrue: true, prop: 'wxAccountName', label: '客服账号', sortable: 'custom' },
    { istrue: true, prop: 'dockUserName', label: '对接人', },
    // { istrue: true, prop: 'dockUserId', label: '对接人', type:'click',
    // handle:(that,row)=>{return that.idchangeAssignment(row.dockUserId, null)},  sortable: 'custom' },
    { istrue: true, prop: 'mobile', label: '手机号', sortable: 'custom' },
]
export default {
  name: "Vue2demoWechatbind",
  components: { MyContainer, cesTable },
  data() {
    return {
     that: this,
     tableCols: tableCols,
     procurementList: [],
     sellist: [],
     platformList: [],
     keserlist: [],
     TableData: [],
     list: [],
     styleCodes: [],
     wxAccountId: null,

     filter: {
      styleCode: '',
      wxAccountName: '',
      dockUserId: null
     },
     total: null,
     serviceAccountshow: false,
     showkefusel: false,
     sels: {},
     validRules: {
       url: [
       { required: true, message: '接入链接' }
       ],
       dockUserId: [
         { required: true, message: '对接人' }
       ],
       wxAccountName: [
       { required: true, message: '客服名称' }
       ],
     }
    };
  },

  mounted() {
   this.onSearch();
   this.onCreateSer("noshow");
  },

  methods: {
   idtoname(row){
    let index = this.keserlist.indexOf(row)
    let name = this.idchangeAssignment(row.dockUserId,null)
    this.keserlist[index].dockUserName = name;
   },
   async subkefutrue(){
     let params = {
      styleCodes: this.styleCodes,
      wxAccountId: this.wxAccountId
     }
     let res = await setProviderStyleWXBind(params);
     if(!res.success){
      return
     }
     this.sellist = []
     this.$message.success("绑定成功！")
     this.showkefusel = false;
     this.getlist();

   },
   onebindno(){
    let res = this.issel();
    if(!res.istrue){
     return
    }
    this.$confirm("选中的系列编码将取消绑定客服，是否确定完成", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      }).then(async () => {
          let params = {
           styleCodes: this.styleCodes,
           wxAccountId: null
          }
          let res = await setProviderStyleWXBind(params);
          if(!res.success){
           return
          }
          this.sellist = []
          this.$message({ message: '操作成功', type: "success" });
          this.getlist();
      });
   },
   onebind(){
    this.getkeser();
    let res = this.issel();
    if(!res.istrue){
     return
    }
    this.showkefusel = true;

   },
   issel(){
    if(this.sellist.length == 0){
     this.$message("请选择至少一条数据！")
     return {istrue:false, styleCodes: null}
    }
    let styleCodes = []
    this.sellist.map((item)=>{
     styleCodes.push(item.styleCode)
    })
    this.styleCodes = styleCodes;
    return {istrue:true, styleCodes: styleCodes}
   },
   onSearch() {
    this.$refs.pager.setPage(1)
    this.getlist()
  },
   delserlist(index){
    this.keserlist.splice(index,1)
    this.saveserlist("noshow")
   },
   creoneser(){
    this.keserlist.push({
     url: '',
     dockUserId: null,
     dockUserName: '',
     wxAccountName: ''
    })
   },
   //排序
  sortchange({ order, prop }) {
      this.listLoading = true
      if (prop) {
          this.filter.orderBy = prop
          this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getlist()
      }
  },
  // 获取列表
  async getlist() {
    let _this = this;

    const pager = this.$refs.pager.getPager()
    // this.filter.startDate = this.filter.timerange[0];
    // this.filter.endDate = this.filter.timerange[1];

    const params = {
      ...pager,
      ...this.filter
    }
    this.listLoading = true
    let newObj = JSON.parse(JSON.stringify(params))

    const res = await getProviderStyleWXBindPageList(newObj)
    this.listLoading = false

    if (!res?.success) {
      return
    }

    this.total = res.data.total
    _this.list = res.data.list;
    _this.$forceUpdate();
  },
   sortchange2({ order, prop }) {
       if (prop) {
           this.RecordsInfo.orderBy = prop
           this.RecordsInfo.isAsc = order.indexOf("descending") == -1 ? true : false
           this.dockingRecords(this.RecordsInfo)
       }
   },
   checkboxrange(rows){
    console.log("打印行数据",rows)
    this.sellist =  rows;
   },
   handleClose(){

   },
   async onCreateSer(name){
    const { data, success } = await getBrandUsers({ userName: null })
    if (success) {
        this.procurementList = data.map(item => {
            return {
                label: item.value,
                value: item.key
            }
        })
    }
    this.getkeser();
    if(name == 'noshow'){
     return
    }
    this.serviceAccountshow = true;
   },
   async getkeser(){
    let res = await getProviderStyleWXAccountList({})
    if(!res.success){
     return
    }
    this.keserlist = res.data;
   },
   idchangeAssignment(e,index){
    if(e){
     return this.procurementList.filter(item => item.value == e)[0]?.label
    }
   },
   // changesel(e){
   //  if(e){
   //   this.filter.dockUserName =  this.keserlist.filter(item => item.id == e)[0].wxAccountName
   //  }
   // },
   changeAssignment(e) {
    if(e){
     this.filter.dockUserName = this.procurementList.filter(item => item.value == e)[0]?.label
    }else{
     this.filter.dockUserName = ""
    }
   },
   async saveserlist(name){
    const $table = this.$refs.diaxTable
    const errMap = await $table.validate(true).catch(errMap => errMap)
    if (errMap) {
      VXETable.modal.message({ status: 'error', message: '校验不通过！' })
      return
    }
    let res = await saveProviderStyleWXAccount(this.keserlist)
    if(!res.success){
     return
    }
    this.$message.success("操作成功！")
    if(name == 'noshow'){
     return
    }
    this.serviceAccountshow = false;
   }
  },
};
</script>

<style lang="scss" scoped></style>
