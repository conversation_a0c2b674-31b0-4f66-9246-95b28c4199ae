<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.proCode" placeholder="广告名称与ID" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'liveBroadcastPromotionSph202505161159'" :tablekey="'liveBroadcastPromotionSph202505161159'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'"
      :border="true">
      <template slot="right">
        <vxe-column title="操作" width="80" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="handleBatchDeletion(row)">批次删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions, downloadLink } from '@/utils/tools'
import { getzhiBoTuiGuang_WeChatList, importzhiBoTuiGuang_WeChatsync, deletezhiBoTuiGuang_WeChatBatchAsync, exportzhiBoTuiGuang_WeChatList } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: '广告名称与ID', },
  { width: 'auto', align: 'center', prop: 'promotedVideoAccountAndCreateTime', label: '推广视频号与创建时间', },
  { width: 'auto', align: 'center', prop: 'target', label: '目标', },
  { width: 'auto', align: 'center', prop: 'adStatus', label: '广告状态', },
  { width: 'auto', align: 'center', prop: 'budget', label: '预算', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'useMoney', label: '花费', },
  { width: 'auto', align: 'center', prop: 'liveViewCount', label: '直播间观看人数', },
  { width: 'auto', align: 'center', prop: 'conversionVolume', label: '转化量', },
  { width: 'auto', align: 'center', prop: 'conversionCost', label: '转化成本', },
  { width: 'auto', align: 'center', prop: 'transactionAmount', label: '成交金额', },
  { width: 'auto', align: 'center', prop: 'transactionROI', label: '成交ROI', },
]
export default {
  name: "liveBroadcastPromotionSph",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      downloadLink,
      dialogVisible: false,
      fileList: [],
      fileparm: {},
      uploadLoading: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startUseDate: null,//开始时间
        endUseDate: null,//结束时间
        shopName: null,//店铺名称
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250516/1923265946806829057.xlsx', '视频号运营费用-直播推广导入模板.xlsx');
    },
    handleBatchDeletion(row) {
      this.$confirm('此操作将删除此批次数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deletezhiBoTuiGuang_WeChatBatchAsync({ batchNumber: row.batchNumber }).then(res => {
          if (res.success) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      })
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importzhiBoTuiGuang_WeChatsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startUseDate = e ? e[0] : null
      this.ListInfo.endUseDate = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportzhiBoTuiGuang_WeChatList(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', `视频号运营费用-直播推广数据${dayjs().format('MMDD')}.xlsx`)
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startUseDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endUseDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startUseDate, this.ListInfo.endUseDate]
      }
      this.loading = true
      const { data, success } = await getzhiBoTuiGuang_WeChatList(this.ListInfo)
      this.loading = false
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
