<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent
      >
        <el-form-item label="培训计划下达日期:">
          <el-date-picker
            style="width: 320px"
            v-model="Filter.timerange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            :default-value="defaultDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select
            v-model="Filter.platform"
            placeholder="请选择"
            class="el-select-content"
            @change="changePlatform"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="店铺:">
          <el-select
            v-model="Filter.shopid"
            placeholder="请选择"
            class="el-select-content"
            clearable
          >

            <el-option
              v-for="item in shopList"
              :key="item.id"
              :label="item.shopName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <div>
        <el-form-item label="运营组:">
          <el-select
            v-model="Filter.groupId"
            placeholder="请选择"
            class="el-select-content"
            clearable
          >

            <el-option
              v-for="item in groupList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态:">
          <el-select
            v-model="Filter.status"
            placeholder="请选择"
            style="width: 130px"
          >
            <el-option label="所有" value="0" />
            <el-option label="已培训" value="2" />
            <el-option label="未培训" value="1" />
          </el-select>
        </el-form-item>

         <el-form-item label="培训导师:">
		          <el-input v-model.trim="Filter.Coach" style="width: 110px"/>
        </el-form-item>
         <el-form-item label="被培训人:">
          <el-input v-model.trim="Filter.trainees" style="width: 110px"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        </div>
      </el-form>

    </template>

    <!--列表-->
    <ces-table
      ref="table"
      :that="that"
      :isIndex="true"
      :hasexpand="false"
      @sortchange="sortchange"
      :tableData="OrderList"
      :summaryarry="summaryarry"
      :tableCols="tableCols"
      :tableHandles="tableHandles"
      :loading="listLoading"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column
                v-for="col in props.row.detailcols"
                :prop="col.prop"
                :label="col.label"
                :key="col"
              >
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <!-- <template slot="extentbtn">
        <el-button-group>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-button-group>
      </template> -->
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="gettrainplanList"
      />
    </template>
    <el-dialog title="被培训人" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <span>
        {{ trainees }}
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>


      <el-dialog title="历史场次" :visible.sync="hisdialogVisible" width="50%" height="80%" v-dialogDrag>
      <span>
        <hisdatatbl ref="hisdata">

        </hisdatatbl>


      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closehistorytrainview">关闭</el-button>
      </span>
    </el-dialog>




 <el-dialog title="培训资料" :visible.sync="resourcedialogVisible" width="50%"  height="80%" v-dialogDrag>
      <span>
  <resourcedatatbl ref="trainress">

        </resourcedatatbl>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeresourceview">关闭</el-button>
      </span>
    </el-dialog>




  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";

import hisdatatbl from "@/views/customerservice/histraindatatbl.vue"
import resourcedatatbl from "@/views/customerservice/trainresource.vue"


import { formatTime } from "@/utils";

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import {
  pageOrders,
  importOrderAndGoodsData,
  syncNewOrderMonitAsync,
  exportOrder,
} from "@/api/order/ordergoods";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import { getList as getcategorylist } from "@/api/operatemanage/base/category";
import { listToTree } from "@/utils";
import {
  formatExpressCompany,
  formatLink,
  formatYesornoBool,
  formatLinkProCode
} from "@/utils/tools";

import { rulePlatform, ruleSendWarehouse } from "@/utils/formruletools";

import {
  addtrainplan,
  gettrainplandata,
  settrainstatus,
} from "@/api/customerservice/trainplan";

const tableCols = [
  {
    istrue: true,
    prop: "shopid",
    label: "店铺",
    width: "110",
    sortable: "custom",
    formatter: (row) => {
      return row.shopname;
    },
  },

  {
    istrue: true,
    prop: "groupid",
    label: "运营组",
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.oprateManager;
    },
  },

  {
    istrue: true,
    display: true,
    prop: "productID",
    label: "商品ID",
    width: "120",
    sortable: "custom",
    type: "html",
     formatter: (row) => formatLinkProCode(row.platform, row.productID),
    // formatter: (row) => {
    //   var proBaseUrl = "";
    //   switch (row.Platform) {
    //     case "1":
    //       proBaseUrl = "https://detail.tmall.com/item.htm?id=" + row.productID;
    //       break;
    //     case "2":
    //       proBaseUrl =
    //         "https://mobile.yangkeduo.com/goods2.html?goods_id=" +
    //         row.productID;
    //       break;
    //   }
    //   if (row.productID && proBaseUrl)
    //     return formatLink(row.productID, proBaseUrl);
    //   return row.productID;
    // },
  },

  {
    istrue: true,
    prop: "productName",
    label: "商品名",
    width: "100"
  },
  {
    istrue: true,
    prop: "coach",
    label: "培训导师",
    width: "140",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "trainDate",
    label: "任务下达日期",
    width: "150",
    sortable: "custom", formatter: (row) => {


        return formatTime(row.trainDate, "YYYY-MM-DD");


    },
  },

  {
    istrue: true,
      prop: "tDate",
    label: "最近已培训日期",
    width: "150",
    sortable: "custom",
     formatter: (row) => {

       if(row.tDate>formatTime(new Date(2018, 11, 24), "YYYY-MM-DD"))

        return formatTime(row.tDate, "YYYY-MM-DD");
        else
        {

         return "";

        }

    },
  },
  {
    istrue: true,
    prop: "trainstatus",
    label: "已执行状态",
    width: "110",
    sortable: "custom",
    formatter: (row) => {
      if (row.trainstatus == 0) {
        return "未培训";
      } else {
        return "已培训";
      }
    },
  },
  {
    istrue: true,
    type: "button",
    label: "操作",
    width: "430",
    btnList: [
      { label: "查看历史场次", handle: (that, row) => that.showhis(row) },
      {
        label: "被培训人明细",
        handle: (that, row) => that.showtrainee(row, 1),
      },
      {
        istrue: false,
        label: "设置为已培训",
        handle: (that, row) => that.settrainstatusOK(row),
      },
      { label: "培训资料下载", handle: (that, row) => that.downloadresource(row, 2) },
    ],
  },
];
export default {
  name: "Users",
  components: {
   MyContainer,
    MyConfirmButton,
    MySearch,
    MySearchWindow,


    hisdatatbl,
    resourcedatatbl,
    cesTable,
  },
  data() {
    return {
      date: '',
      that: this,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        status:"0",
        startCreatedTime: null,
        endCreatedTime: null,
        platform: null,
        shopid: null,
        groupId: null,
        productCategoryId: null,
        goodsCode: null,
        proCode: null,
        brandId: null,
        expressCompany: null,
        orderNo: null,
        orderSource: null,
        hisdialogVisible1:false,
        rowStartPayTime: null,
        rowEndPayTime: null,
        statusName: null, //状态名称  拆分，补发，退货
         Coach:''
      },
      trainhisdata:[],
      trainees: "",
      OrderList: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
       hispager: {
        OrderBy: "traindate",
        pageSize: 10,
        total: 1,
        pageIndex: 1,
        IsAsc: true,
      },
      pager: { OrderBy: "traindate", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      userNameReadonly: true,
      fileList: [],
      importFilte: { companyid: null, warehouse: null },
      batchNumber: "",
      tableHandles: [],
      platformList: [],
      shopList: [],
      groupList: [],
      categorylist: [],
      brandlist: [],

      sendWarehouseList: [],
      hisdialogVisible:false,
      resourcedialogVisible:false,
      pickerOptions: {
          disabledDate(date) {
          // 设置禁用日期
          const start = new Date('1970/1/1');
          const end = new Date('9999/12/31');
          return date < start || date > end;
          }
        },
        defaultDate: new Date('1970/1/1')
    };
  },
  async mounted() {
    await this.onSearch();
    await this.setGroupSelect();
    await this.changePlatform();
    await this.setPlatform();


  },
  methods: {

    closehistorytrainview(){


       this.$refs.hisdata.clear();

      this.hisdialogVisible=false;


    },

    closeresourceview(){


         this.$refs.trainress.clear();;

         this.resourcedialogVisible = false

    },
    downloadresource(row){


    var that=this;

      this.resourcedialogVisible=true;

      setTimeout(() => {
         that.$refs.trainress.init(row.productID);
      }, 200);



    },




    showhis(row){


      this.hisdialogVisible=true;
      var that=this;

      setTimeout(function(){


  that.$refs.hisdata.init(row.productID);

      },200);






    },
    settrainstatusOK(row) {
      var that=this;
      this.$confirm("此操作将任务状态改为已培训, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          settrainstatus({ id: row.id }).then((res) => {
              that.onSearch();
          });
        })
        .catch(() => {
          //几点取消的提示
        });
    },
    showtrainees(trainees) {
      this.trainees = trainees;
      this.dialogVisible = true;
    },
    showtrainee(row) {
      this.trainees = row.trainees;
      this.dialogVisible = true;
    },

    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },

    formatCreatedTime(row, column, time) {
      return formatTime(time, "YYYY-MM-DD HH:mm");
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    uploadFile(item) {
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = importOrderAndGoodsData(form);
      this.$message({
        message: "上传成功,正在导入中...",
        type: "success",
      });
    },
    // 查询
    onSearch() {
      this.$refs.pager.setPage(1);
      this.gettrainplanList();
    },
    getCondition() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.startDate = this.Filter.timerange[0];
        para.endDate = this.Filter.timerange[1];
      }
      if (!(para.startDate && para.endDate)) {
        this.$message({ message: "请先选择日期！", type: "warning" });
        return false;
      }

      if (this.Filter.groupId) {
        para.groupId = this.Filter.groupId;
      } else {
        para.groupId = 0;
      }
      para.trainstatus = this.Filter.status;
      if (this.Filter.shopid) para.shopid = this.Filter.shopid;
      else para.shopid = 0;

      if (this.Filter.categoryId) {
        para.productCategoryId =
          this.Filter.categoryId[this.Filter.categoryId.length - 1];
      }
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };

      console.log("params", params);

      return params;
    },
    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;

if(!params.OrderBy)
{

  params.OrderBy="tDate";
}


      const res = await gettrainplandata(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.OrderList = data;
      this.summaryarry = res.data.summary || {};
    },
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var d1 = dayjs(params.startCreatedTime);
      var d2 = dayjs(params.endCreatedTime);
      if (d2.diff(d1, "day") > 5) {
        this.$message({ message: "最多只能导出5天的数据", type: "warning" });
        return false;
      }
      var loadingInstance = this.$loading({
        text: "正在导出，请稍后",
        fullscreen: false,
      });
      var res = await exportOrder(params);
      loadingInstance.close();
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "订单_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
    async changePlatform(val) {
      const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 1000,
      });
      this.shopList = res1.data.list;
      if (val) this.getcategorylist(val);
    },
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    async getcategorylist(platform) {
      const res = await getcategorylist({ platform: platform });
      if (!res?.code) {
        return;
      }
      const list = [];
      res.data.forEach((f) => {
        f.label = f.categoryName;
        list.push(f);
      });
      this.categorylist = listToTree(_.cloneDeep(list), {
        id: "",
        parentId: "",
        label: "所有",
      });
    },




    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      if (this.pager) {
        this.pager.OrderBy =
          this.pager.OrderBy == "brandName" ? "brandId" : this.pager.OrderBy;
        this.pager.OrderBy =
          this.pager.OrderBy == "platformDesc"
            ? "platform"
            : this.pager.OrderBy;
        this.pager.OrderBy =
          this.pager.OrderBy == "proCountTypeName"
            ? "proCountType"
            : this.pager.OrderBy;
      }
      this.onSearch();
    },


  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
