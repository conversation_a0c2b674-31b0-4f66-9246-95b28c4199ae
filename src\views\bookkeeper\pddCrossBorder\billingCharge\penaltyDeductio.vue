<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :clearable="false" :value-format="'yyyy-MM-dd'" @change="changeTime" clearable>
        </el-date-picker>
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名" maxlength="50" clearable class="publicCss" />

        <el-button-group style="margin-left: 10px;">
          <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="ListInfo.violationIDs"
            v-model.trim="ListInfo.violationIDs" placeholder="违规ID/多条输入请按回车" :clearable="true" @callback="callback"
            title="违规ID" @entersearch="entersearch">
          </inputYunhan>
        </el-button-group>
        <el-input v-model="ListInfo.goodsCode" style="margin-left: 10px;" maxlength="50" placeholder="商品编码" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.skc" style="margin-left: 5px;" maxlength="50" placeholder="SKC" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList()">搜索</el-button>
        <el-button type="primary" @click="startImport()">导入</el-button>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('temu_penaltyDeductio_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'derelict202408041402'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" style="width: 100%;margin: 0" :loading="loading" :height="'100%'" :summaryarry='summaryarry' :showsummary='true'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入弃货数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 75px;">
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs'
import { getBillingCharge_Wgkk_PddTemuPageList, billingCharge_Wgkk_PddTemu_Export, import_BillingCharge_Wgkk_PddTemu } from '@/api/bookkeeper/crossBorderV2'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { istrue: true, prop: 'shopCode', label: '店铺ID', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'shopName', label: '店铺名称', width: '170', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'violationID', label: '违规ID', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'inventoryNumber', label: '备货单号', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'skc', label: 'SKC', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'expenditureType', label: '支出类型', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'amount', label: '支出金额', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'currency', label: '币种', width: 'auto', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'financialTime', label: '账务时间', width: 'auto', align: 'center', sortable: 'custom', formatter: (row) => formatTime(row.financialTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'violationType', label: '违规类型', width: '违规类型', align: 'center', sortable: 'custom', },

]
export default {
  name: "derelict",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      yearMonthDay: null,//导入日期
      fileparm: {},//上传文件参数
      dialogVisible: false,//导入弹窗
      uploadLoading: false,//上传loading
      fileList: [],//上传文件列表
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      summaryarry: {},
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {

    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.yearMonthDay);
      var res = await import_BillingCharge_Wgkk_PddTemu(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async getList() {
      if (this.timeRanges != null && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      } 
      this.loading = true
      const { data, success } = await getBillingCharge_Wgkk_PddTemuPageList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjBillCharges/TEMU全托弃货.xlsx", "_blank");
    },
    async onExport() {//导出列表数据；
      if (this.timeRanges != null && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      } 
      var res = await billingCharge_Wgkk_PddTemu_Export(this.ListInfo);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
    async callback(val) {
      this.ListInfo.violationIDs = val;
    },
    async entersearch(val) {
      this.getList();
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>