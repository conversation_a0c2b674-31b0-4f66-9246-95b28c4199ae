<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <el-scrollbar style="height: 100%">
        <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">
          <el-form-item label="诚信仓：" prop="integrityWareNum">
              <inputNumberYh @input="computedone" v-model="ruleForm.integrityWareNum" :placeholder="'诚信仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="圆通仓：" prop="yuantongWareNum">
              <inputNumberYh @input="computedone" v-model="ruleForm.yuantongWareNum" :placeholder="'圆通仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="邮政仓：" prop="mailWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.mailWareCount" :placeholder="'邮政仓'" class="publicCss" />
          </el-form-item>

          <el-form-item label="1688发货仓：" prop="send1688WareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.send1688WareCount" :placeholder="'1688发货仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="西安仓：" prop="xaWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.xaWareCount" :placeholder="'西安仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="南昌仓：" prop="ncWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.ncWareCount" :placeholder="'南昌仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="东莞仓：" prop="dgWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.dgWareCount" :placeholder="'东莞仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="集包场地：" prop="packageSiteCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.packageSiteCount" :placeholder="'集包场地'" class="publicCss" />
          </el-form-item>
          <el-form-item label="加工仓：" prop="processWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.processWareCount" :placeholder="'加工仓'" class="publicCss" />
          </el-form-item>

          <el-form-item label="跨境仓：" prop="crossWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.crossWareCount" :placeholder="'跨境仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="昀晗云仓：" prop="yunhanWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.yunhanWareCount" :placeholder="'昀晗云仓'" class="publicCss" />
          </el-form-item>
          <el-form-item label="首力仓储部：" prop="shouliWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.shouliWareCount" :placeholder="'首力仓储部'" class="publicCss" />
          </el-form-item>
          <el-form-item label="仓库行政：" prop="administrationWareCount">
              <inputNumberYh @input="computedone" v-model="ruleForm.administrationWareCount" :placeholder="'仓库行政'" class="publicCss" />
          </el-form-item>


          <!-- <el-form-item label="部门：">
            {{ '部门' }}
          </el-form-item> -->
          <!-- <el-form-item label="总人数：" prop="regionName">
            <inputNumberYh v-model="ruleForm.regionName" :placeholder="'总人数'" class="publicCss" />
          </el-form-item> -->

          <!-- <el-form-item label="培训人：" prop="trainer">
            <el-input style="width:80%;" v-model.trim="ruleForm.trainer" :maxlength="50" placeholder="培训人" clearable />
          </el-form-item> -->

          <!-- <el-form-item label="考试平均分：" prop="avgExamScore">
            <inputNumberYh v-model="ruleForm.avgExamScore" :placeholder="'考试平均分'" class="publicCss" :fixed="2" />
          </el-form-item> -->
          <!-- <el-form-item label="培训满意度：" prop="trainingSatisfaction">
            <inputNumberYh v-model="ruleForm.trainingSatisfaction" :placeholder="'培训满意度'" class="publicCss" :fixed="2"/>
          </el-form-item> -->

          <!-- <el-form-item label="1年以上离职人数：" prop="yearCount">
            <inputNumberYh v-model="ruleForm.yearCount" :placeholder="'1年以上离职人数'" class="publicCss" />
          </el-form-item> -->

  <!--
          <el-form-item label="辞职：" prop="resignCount">
              <el-input style="width:80%;" v-model.trim="ruleForm.resignCount" :maxlength="50" placeholder="辞职" clearable />
          </el-form-item>
          <el-form-item label="劝退：" prop="quitCount"> -->
            <!-- <inputNumberYh v-model="ruleForm.quitCount" :placeholder="'劝退'" class="publicCss" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.quitCount" :maxlength="50" placeholder="劝退" clearable />
          </el-form-item> -->
          <!-- <el-form-item label="自离：" prop="selfDeparture"> -->
            <!-- <inputNumberYh v-model="ruleForm.selfDeparture" :placeholder="'自离'" class="publicCss" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.selfDeparture" :maxlength="50" placeholder="自离" clearable />

          </el-form-item>
          <el-form-item label="试用期离职：" prop="probationQuitCount"> -->
            <!-- <inputNumberYh v-model="ruleForm.probationQuitCount" :placeholder="'试用期离职'" class="publicCss" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.probationQuitCount" :maxlength="50" placeholder="试用期离职" clearable />

          </el-form-item>
          <el-form-item label="正式工离职：" prop="regularQuitCount"> -->
            <!-- <inputNumberYh v-model="ruleForm.regularQuitCount" :placeholder="'正式工离职'" class="publicCss" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.regularQuitCount" :maxlength="50" placeholder="正式工离职" clearable />

          </el-form-item> -->

          <!-- <el-form-item label="经理人数：" prop="managerCount">
            <inputNumberYh v-model="ruleForm.managerCount" :placeholder="'经理人数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="总监人数：" prop="directorCount">
            <inputNumberYh v-model="ruleForm.directorCount" :placeholder="'总监人数'" class="publicCss" />
          </el-form-item>
          <el-form-item label="总经理/区负责人：" prop="generalManager">
            <inputNumberYh v-model="ruleForm.generalManager" :placeholder="'总经理/区负责人'" class="publicCss" />
          </el-form-item>
          <el-form-item label="总裁：" prop="ceoCount">
            <inputNumberYh v-model="ruleForm.ceoCount" :placeholder="'总裁'" class="publicCss" />
          </el-form-item> -->

        </el-form>
      </el-scrollbar>
      <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div>
    </div>
  </template>

  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { warehouseFixedAssetsSubmit } from '@/api/people/peoplessc.js';
  import checkPermission from '@/utils/permission'
  import decimal from '@/utils/decimal'
  export default {
    name: 'departmentEdit',
    components: {
      inputNumberYh, MyConfirmButton
    },
    props: {
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      districtList: {
        type: Object,
        default: () => {
          return {}
        }
      },
      typeList: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: ''
        },
        rules: {
            administrationWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          shouliWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
            ],
            yunhanWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          crossWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
            ],
            processWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          xaWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          ncWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          dgWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          packageSiteCount: [
            { required: true, message: '请输入', trigger: 'blur' }
            ],
            send1688WareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          mailWareCount: [
            { required: true, message: '请输入', trigger: 'blur' }
            ],
            yuantongWareNum: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          integrityWareNum: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
        }
      }
    },

    async mounted() {
      this.$nextTick(() => {
        this.$refs.refruleForm.clearValidate();
      });
      this.ruleForm = { ...this.editInfo };
    },
      methods: {
      computedone(){
          let a = this.ruleForm.totalNumWorkers ? this.ruleForm.totalNumWorkers :  0;
          let b = this.ruleForm.usedWorkstations ? this.ruleForm.usedWorkstations :  0;

          this.ruleForm.remainingWorkstations = decimal(a, b, 2, '-').toFixed(0);
          return decimal(a, b, 2, '-').toFixed(0);
      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
          console.log(this.ruleForm.label, 'this.ruleForm.label');
        if (this.ruleForm.remainingWorkstations<0) {
          return this.$message.warning('剩余工位不能为负数');
        }
        this.$refs[formName].validate(async(valid) => {
            if (valid) {
              this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
              const { data, success } = await warehouseFixedAssetsSubmit(this.ruleForm)
              if(!success){
                  return
              }
              await this.$emit("search");

            } else {
              console.log('error submit!!');
              return false;
            }
          });
      //   this.$confirm('是否保存?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(async () => {
      //     this.$refs[formName].validate(async(valid) => {
      //       if (valid) {
      //         const { data, success } = await warehouseFixedAssetsSubmit(this.ruleForm)
      //         if(!success){
      //             return
      //         }
      //         await this.$emit("search");

      //       } else {
      //         console.log('error submit!!');
      //         return false;
      //       }
      //     });
      //   }).catch(() => {
      //   });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
