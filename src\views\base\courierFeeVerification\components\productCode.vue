<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startReceiveDate" :endDate.sync="ListInfo.endReceiveDate"
                    class="publicCss" startPlaceholder="收寄时间" endPlaceholder="收寄时间" :clearable="false" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <number-range :min.sync="ListInfo.totalCountMin" :max.sync="ListInfo.totalCountMax"
                    min-label="订单量 - 最小值" max-label="订单量 - 最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.feeMin" :max.sync="ListInfo.feeMax" min-label="快递费 - 最小值"
                    max-label="快递费 - 最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax" min-label="重量 - 最小值"
                    max-label="重量 - 最大值" class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange='sortchange'
            :tableData='data.list' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
            style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog :visible.sync="dialogVisible1" width="90%" v-dialogDrag>
            <billReview :info="info" v-if="dialogVisible1" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import request from '@/utils/request'
import numberRange from "@/components/number-range/index.vue";
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import billReview from './billReview.vue'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', summaryType: 'Sum', prop: 'totalQty', label: '商品数量', type: 'click', handle: (that, row) => that.openDetails(row) },
    { sortable: 'custom', width: 'auto', align: 'center', summaryType: 'Sum', prop: 'totalCount', label: '订单量', type: 'click', handle: (that, row) => that.openDetails(row) },
    { sortable: 'custom', width: 'auto', align: 'center', summaryType: 'Sum', prop: 'maxWeight', label: '重量最高', type: 'click', handle: (that, row) => that.openDetails(row) },
    { sortable: 'custom', width: 'auto', align: 'center', summaryType: 'Sum', prop: 'minWeight', label: '重量最低', type: 'click', handle: (that, row) => that.openDetails(row) },
    { sortable: 'custom', width: 'auto', align: 'center', summaryType: 'Sum', prop: 'maxFee', label: '快递费最高', type: 'click', handle: (that, row) => that.openDetails(row) },
    { sortable: 'custom', width: 'auto', align: 'center', summaryType: 'Sum', prop: 'minFee', label: '快递费最低', type: 'click', handle: (that, row) => that.openDetails(row) },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, numberRange, inputYunhan, billReview
    },
    data() {
        return {
            that: this,
            api: '/api/express/Express/StatExpress/',
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                type: 2,
                startReceiveDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),//开始时间
                endReceiveDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),//结束时间
            },
            data: {},
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            dialogVisible1: false
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        proCodeCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        openDetails(row) {
            this.info = {
                startReceiveDate: this.ListInfo.startReceiveDate,
                endReceiveDate: this.ListInfo.endReceiveDate,
                goodsCode: row.goodsCode
            }
            this.$emit('linkToDetail', { params: this.info })
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
