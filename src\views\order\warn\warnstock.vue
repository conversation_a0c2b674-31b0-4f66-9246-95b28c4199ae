<template>
  <container v-loading="pageLoading">
     <ces-table ref="table" :that='that' :isIndex='true' :isSelectColumn='true' @sortchange='sortchange'
                           tablekey='warnstock' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;">
               <el-input v-model.trim="filter.orderNoInner" placeholder="内部订单号" style="width:193px;"/>         
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.status" placeholder="状态" clearable style="width: 100px" @change='onchangeplatform'>
                <el-option label="审单" :value="0"></el-option>
                <el-option label="加急订单" :value="1"></el-option>
                <el-option label="配货订单" :value="2"></el-option>
                <el-option label="打包订单" :value="3"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.platform" placeholder="平台" clearable style="width: 100px" @change='onchangeplatform'>
                <el-option label="淘系" :value="1"></el-option>
                <el-option label="拼多多" :value="2"></el-option>
                <el-option label="工厂店" :value="8"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 140px">
                <el-option v-for="item in shoplist" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
              </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
       </template>
     </ces-table>
     <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>

    <el-dialog title="导入仓库订单" :visible.sync="dialogVisible" width="40%">
      <span>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx"
                               :http-request="uploadFile" :file-list="fileList" :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container/noheader";
import { formatPlatform,formatYesornoBool} from "@/utils/tools";
import { pageOrderSaleOutStock,importOrderSaleOutStock} from "@/api/order/ordererror";
import {getList as getshopList} from '@/api/operatemanage/base/shop'
const tableCols =[
     {istrue:true,prop:'outWarehouseNo',label:'出库单号', width:'80',sortable:'custom'},
     {istrue:true,prop:'outStockNo',label:'出仓单号', width:'80',sortable:'custom'}, 
     {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'90',sortable:'custom',type:'orderLogInfo',orderType:'orderNoInner' },
     {istrue:true,prop:'orderNo',label:'线上订单号', width:'120',sortable:'custom'},     
     {istrue:true,prop:'platform',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
     {istrue:true,prop:'shopCode',label:'店铺', width:'120',sortable:'custom',formatter:(row)=>row.shopName},
     {istrue:true,prop:'status',label:'状态', width:'80',sortable:'custom',formatter:(row)=>row.status==0?'审单':row.status==1?'加急订单': row.status==2?'配货订单':row.status==3?'打包订单':' '},
     {istrue:true,prop:'stockBatchNumber',label:'批次号', width:'80',sortable:'custom'},
     {istrue:true,prop:'isOrderPrinted',label:'订单已打印', width:'90',sortable:'custom',formatter:(row)=>formatYesornoBool(row.isOrderPrinted)},
     {istrue:true,prop:'isExpressOrderPrinted',label:'快递单已打印', width:'105',sortable:'custom',formatter:(row)=>formatYesornoBool(row.isExpressOrderPrinted)},
     {istrue:true,prop:'expressCompany',label:'快递公司', width:'90',sortable:'custom'},
     {istrue:true,prop:'expressNo',label:'快递单号', width:'100',sortable:'custom'},
     {istrue:true,prop:'isSend',label:'是否已发货', width:'90',sortable:'custom',formatter:(row)=>formatYesornoBool(row.isSend)},
     {istrue:true,prop:'label',label:'标记多标签', width:'100',sortable:'custom'},
     {istrue:true,prop:'editTime',label:'编辑时间', width:'120',sortable:'custom'},
     {istrue:true,prop:'createdTime',label:'创建时间', width:'120',sortable:'custom'},
    ];
    const tableHandles1=[{label:"导入", handle:(that)=>that.onstartImport()}];
export default {
  name: "warnorder",
  components: {cesTable, container },
  data() {
    return {
      that:this,
      filter: {orderNoInner: null,shopCode:null,platform:null,status:null},
      list: [],
      listLoading: false,
      pageLoading: false,
      shoplist:[],
      tableCols:tableCols,
      tableHandles:tableHandles1,
      pager:{OrderBy:" id ",IsAsc:false},
      total:0,
      sels: [],
      selids: [], 
      fileList:[],
      fileparm:{},
      listLoading: false,
      pageLoading: false,
      dialogVisible:false,
      uploadLoading:false,
    };
  },
  async mounted() {
    //await this.onSearch();
  },
  methods: {
    async onchangeplatform(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:300});
      this.shoplist=res.data.list
    },
    sortchange(column){
      if(!column.order) this.pager={OrderBy:"id",IsAsc:false}
      else this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getList();
    },
    onSearchWarnStock(status) {
      this.filter.status=status
      this.$refs.pager.setPage(1)
      this.getList();
    },
    async getList() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager, ...this.filter,...this.pager};
      this.listLoading = true;
      const res = await pageOrderSaleOutStock(params);
      this.listLoading = false;
      if (!res?.success) return; 
      this.total = res.data.total
      const data = res.data.list
      data.forEach((d) => {d._loading = false;});
      this.list = data;
    },
    async onstartImport(){
      this.dialogVisible=true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() { 
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file); 
      var res = await importOrderSaleOutStock(form);
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
    selsChange: function(sels) {
      this.sels = sels
    },
  },
};
</script>
 
