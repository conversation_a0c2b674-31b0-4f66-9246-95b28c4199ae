<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="filter"
        @submit.native.prevent
      >
        <el-form-item label="采购日期:">
          <el-date-picker
            style="width: 260px"
            v-model="filter.timerangecg"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="采购单号:"><el-input v-model="filter.buyNo" clearable/></el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>     
    </template>

    <!-- :tableHandles='tableHandles' -->
    
    <ces-table
      ref="table"
      :that="that"
      :isIndex="true"
      @sortchange="sortchange"
      @select="selectchange"
      @cellclick="cellclick"
      :hasexpand="true"
      :tableData="list"
      :tableCols="tableCols"
      :loading="listLoading"
      :showsummary="true"
      :summaryarry="summaryarry"
    >
      <template slot="extentbtn">
        <el-button-group>
          <el-button style="margin: 0">
            {{ lastUpdateTime }}
            
          </el-button>
        </el-button-group>
      </template>
    </ces-table>
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getlist"
      />
    </template>
    
    
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%">
      <span>
        <el-upload
          ref="upload"
          class="upload-demo"
          :auto-upload="false"
          :multiple="false"
          action
          accept=".xlsx"
          :http-request="uploadFile"
          :on-change="uploadChange"
          :on-remove="uploadRemove"
        >
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button
            style="margin-left: 10px"
            size="small"
            type="success"
            :loading="uploadLoading"
            @click="submitUpload"
            >{{ uploadLoading ? "上传中" : "上传" }}
          </el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer
      title="处理"
      :modal="false"
      :wrapper-closable="true"
      :modal-append-to-body="false"
      :visible.sync="editVisible"
      direction="btt"
      size="'auto'"
      class="el-drawer__wrapper"
      style="position: absolute"
    >
      <form-create
        :rule="autoform.rule"
        v-model="autoform.fApi"
        :option="autoform.options"
      />
      <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button
          type="submit"
          :loading="editLoading"
          @click="onEditSubmit"
        />
      </div>
    </el-drawer>
    
    <el-dialog ref="editPopover" :visible.sync="visiblepopover" v-dialogDrag>
      <goodscoderecord
        ref="goodscoderecord"
        :filter="goodscoderecordfilter"
        style="height: 400px"
      ></goodscoderecord>
    </el-dialog>

    <el-dialog
      :visible.sync="dialoganalysisVisible"
      v-dialogDrag
      :show-close="false"
    >
      <purchasehistorytjanalysis
        ref="purchasehistorytjanalysis"
        style="height: 450px"
      ></purchasehistorytjanalysis>
    </el-dialog>

    <!-- 三十天数据看板 -->
    <el-dialog
      ref="detailPopover"
      placement="bottom-end"
      :visible.sync="visiblepopoverdetail"
      direction="btt"
      :key="'detail' + popperFlagdetail.toString()"
      width="85%"
    >
    <purorderindex2 ref="purorderindex2" :fileter="purorderindefilter"></purorderindex2>   
    </el-dialog>
    <div
      class="imgDolg"
      v-show="imgPreview.show"
      @click.stop="imgPreview.show = false"
    >
      <i
        class="el-icon-close"
        id="imgDolgClose"
        @click.stop="imgPreview.show = false"
      ></i>
      <img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
    </div>

    <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl">
      <logistics ref="logistics"></logistics>
    </el-drawer>
  </container>
</template>
<script>
import {
  editPurchaseOrder,
  getPurchaseOrder,
  queryPurchaseOrderDetail,
  PagePurOrder,
  importPurchaseOrder,
  getLastUpdateTimeyPurchase,
  exportPurchaseOrder,
  getOrderChartsOrderCount,
} from "@/api/inventory/purorder";
import { getAllProBrand } from "@/api/inventory/warehouse";
import { upLoadImage } from "@/api/upload/file";
import {
  formatTime,
  formatYesornoBool,
  formatWarehouseArea,
  formatNoLink,
  formatIsError,
  formatIsOutStock,
  formatSecondToHour,
} from "@/utils/tools";
import { ruleExpressComanycode } from "@/utils/formruletools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import logistics from "@/components/Comm/logistics";
import goodscoderecord from "@/views/inventory/goodscoderecord";
import { throttle } from "throttle-debounce";
import formCreate from "@form-create/element-ui";
import FcEditor from "@form-create/component-wangeditor";
import purchasehistorytjanalysis from "@/views/inventory/components/purchasehistorytjanalysis";
import * as echarts from "echarts";
import dayjs from "dayjs";
import purorderindex2  from '@/views/inventory/purorderindex2'
const tableCols = [
  { istrue: true,prop: "buyNo",label: "采购单号",width: "80",sortable: "custom",type: "html",formatter: (row) => formatNoLink(row.buyNo),},
  { istrue: true,prop: "brandName", label: "采购员", width: "63" },
  { istrue: true,prop: "purchaseDate",label: "采购日期",width: "105",sortable: "custom",formatter: (row) => formatTime(row.purchaseDate, "MM-DD HH:mm:ss"),},
  // {istrue:true,prop:'checker',label:'审核人', width:'65',},
  { istrue: true,prop: "checkDate",label: "审核日期",width: "80",sortable: "custom",formatter: (row) => formatTime(row.checkDate, "MM-DD HH:mm:ss"),},
  { istrue: true,prop: "status",label: "状态",width: "60",sortable: "custom",},
  { istrue: true,prop: "receivStatus",label: "收货状态",width: "80",sortable: "custom",},
  { istrue: true,prop: "nonInCount",label: "未入库数",width: "80",sortable: "custom",},
  { istrue: true,prop: "nonInAmont",label: "未入库金额",width: "95",sortable: "custom",},
  { istrue: true,prop: "totalAmont",label: "总金额",width: "80",sortable: "custom",},
  { istrue: true,prop: "isError",label: "进货仓",width: "70",sortable: "custom",formatter: (row) => formatIsError(row.isError),},
  { istrue: true,prop: "lastWarehousingDate",label: "入库时间",width: "80",sortable: "custom",formatter: (row) => formatTime(row.lastWarehousingDate, "YYYY-MM-DD"),},
  { istrue: true,prop: "lastInTransitTime",label: "在途时长",width: "85",sortable: "custom",formatter: (row) => formatSecondToHour(row.lastInTransitTime),},
  { istrue: true,prop: "isOutStock",label: "缺货状态",width: "75",type: "html",formatter: (row) => formatIsOutStock(row.isOutStock),},
  { istrue: true,prop: "supplier",label: "供应商",width: "*",sortable: "custom",},
  { istrue: true, prop: "boxCount", label: "箱数", width: "55" },
  { istrue: true, prop: "reMark", label: "备注", width: "160", type: "editor" },
];
const tableHandles = [
  { label: "导入", handle: (that) => that.startImport() },
  { label: "导出", handle: (that) => that.onExport() },
  { label: "查看历史", handle: (that) => that.onShowHistory() },
];
export default {
  name: "Users",
  components: {
    purorderindex2,
    container,
    cesTable,
    MyConfirmButton,
    logistics,  
    goodscoderecord,
    purchasehistorytjanalysis,
    
  },
  data() {
    return {
      orderDetail: {
        title: "订单",
        visible: false,
        tableCols: tableCols,
        tableData: [],
        total: 0,
        total1: 0,
        sels: [],
        listLoading: false,
        pager: { OrderBy: "payTime", IsAsc: false },
        pageLoading: false,
        that: this,
        filter: {
          startDate: null,
          endDate: null,
        },
        showRow: {}, //明细的行
        enableSelect: true, //允许选择
      },
      that: this,
      formatWarehouseArea: formatWarehouseArea,
      formatYesornoBool: formatYesornoBool,
      formatTime: formatTime,
      formatIsOutStock: formatIsOutStock,
      formatSecondToHour: formatSecondToHour,
      //图表配置 Start
      charts: {
        myChart: null,
        chartsType: null,
        Ylist: [{ value: 0, unit: "" }],
        loading: false,
        visible: false,
        title: null,
        filter: {
          GoodsCode: null,
          startDate: null,
          endDate: null,
          timeRange: [
            formatTime(dayjs().subtract(29, "day"), "YYYY-MM-DD"),
            formatTime(new Date(), "YYYY-MM-DD"),
          ],
        },
      },
      filter: {
        timerangecg: null,
        buyNo: null,
      },
      goodscoderecordfilter: { goodsCode: "", buyNo: "" },
      purorderindefilter:{ buyNo: "" },
      imgPreview: { img: "", show: false },
      lastUpdateTime: "",
      brandlist: [],
      list: [],
      detaillist: [],
      oderDetailView: {},
      drawervisible: false,
      dialoganalysisVisible: false,
      visiblepopover: false,
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopoverdetail: false,
      dialogOrderDetailVisible: false,
      popperFlagdetail: false,
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      total: 0,
      total1: 0,
      sels: [],
      selids: [],
      fileList: [],
      listLoading: false,
      dialogVisible: false,
      pageLoading: false,
      editVisible: false,
      editLoading: false,
      uploadLoading: false,
      hackReset: false,
      goodscoderecord1id: +new Date(),
      autoform: {
        fApi: {},
        options: {
          submitBtn: false,
          global: { "*": { props: { disabled: false }, col: { span: 6 } } },
        },
        rule: [],
      },
      //图表配置 End
      platformList: [],
      shopList: [],
      groupList: [],
      brandlist: [],
      sendWarehouseList: [],
      collapseActiveNames: [], //折叠面板激活页面
      orderDetailActiveName: "tabOrderDetailTable",
    };
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log("this.$refs.table--->", this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n); // 监听滚动，用于编辑框的滚动移除
      }
    },
  },
  async mounted() {
    if (this.$route.query && this.$route.query.buyNo) {
      this.filter.buyNo = this.$route.query.buyNo;
      this.filter.status = "";
    } else if (this.$route.query && this.$route.query.goodscodes) {
      this.filter.goodsCode = this.$route.query.goodscodes;
      this.filter.status = "";
    }

    formCreate.component("editor", FcEditor);
    await this.initform();
    await this.init();
    await this.onSearch();
  },
  methods: {
    //明细查询条件
    getOrderDetailCondition() {
      var pager = this.$refs.orderDetailPager.getPager();
      var page = this.orderDetail.pager;
      this.orderDetail.filter.startDate = null;
      this.orderDetail.filter.endDate = null;
      var backupDate = formatTime(this.filter.backupDate, "yyyy-MM-dd");
      if (
        this.orderDetail.filter.timerange &&
        this.orderDetail.filter.timerange.length > 1
      ) {
        this.orderDetail.filter.startDate =
          this.orderDetail.filter.timerange[0];
        this.orderDetail.filter.endDate = this.orderDetail.filter.timerange[1];
      }
      var params = { ...params, ...pager, ...page, ...this.filter, ...this.orderDetail.filter, backupDate,};
      return params;
    },
    async initform() {
      let that = this;
      this.autoform.rule = [
        { type: "hidden", field: "id", title: "id", value: "" },
        { type: "input",field: "buyNo",title: "采购单号",value: "",props: { readonly: true },col: { span: 6 },},
        { type: "input",field: "brandName",title: "采购负责人",value: "",props: { maxlength: 10, readonly: true },col: { span: 18 },},
        { type: "InputNumber",field: "boxCount",title: "箱数",value: 0,props: { min: 0, precision: 0 },},
        { type: "DatePicker",field: "planArrivalTime",title: "预计到货日期",value: "",
          validate: [{ type: "string", required: true, message: "请输入预计到货日期" },],
          props: { type: "datetime",format: "yyyy-MM-dd",placeholder: "预计到货日期",},col: { span: 6 },},
        { type: "input",field: "expressNo",title: "物流单号",value: "",col: { span: 6 },},
        { type: "select",field: "companyCode",title: "物流公司",value: "",
          ...(await ruleExpressComanycode()),
          validate: [{ type: "string", required: false }],
          col: { span: 6 },
        },
        { type: "editor",field: "reMark",title: "备注",value: "",col: { span: 24 },
          props: {
            maxlength: 400,
            init: async (editor) => {
              await that.initeditor(editor);
            },
          },
        },
      ];
    },
    async init() {
      var res2 = await getAllProBrand();
      this.brandlist = res2.data.map((item) => {
        return { value: item.key, label: item.value };
      });
      var res3 = await getLastUpdateTimeyPurchase();
      this.lastUpdateTime = "最晚更新时间:" + res3.data;
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      this.getlist();
    },
    async getlist() {
      if (!this.pager.OrderBy) this.pager.OrderBy = "";
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      if (params.timerangecg) {
        params.startPurchaseDate = params.timerangecg[0];
        params.endPurchaseDate = params.timerangecg[1];
      }
      if (params.timerangesh) {
        params.startCheckDate = params.timerangesh[0];
        params.endCheckDate = params.timerangesh[1];
      }
      console.log("params", params);
      this.listLoading = true;
      const res = await PagePurOrder(params);
      this.listLoading = false;
      if (!res?.success) return;
      this.total = res.data.total;
      const data = res.data.list;
      data.forEach((d) => {
        d._loading = false;
      });
      this.list = data;
      this.summaryarry = res.data.summary;
    },
    async cellclick(row, column, cell, event) {
      if (column.property == "buyNo") {
        this.visiblepopoverdetail = true;
         await this.getdetaillist(row.buyNo);
      }      
    },
    async getdetaillist(buyNo) {
      this.purorderindefilter = { buyNo: buyNo };
      this.$nextTick( ()=>{
        this.$refs.purorderindex2.onSearch(buyNo);
      })
    },
    async getrecordlist(buyno) {
      this.goodscoderecordfilter = { buyNo: buyno, goodsCode: "" };
      this.$nextTick(() => {
        this.$refs.goodscoderecord.onSearch("", buyno);
      });
    },
    async onHand(row) {
      this.formtitle = "编辑";
      this.editVisible = true;
      const res = await getPurchaseOrder({ buyno: row.buyNo });
      var arr = Object.keys(this.autoform.fApi);
      if (arr.length > 0) this.autoform.fApi.resetFields();
      await this.autoform.fApi.setValue(res.data);
    },
    onDisPlay(row) {
      return row.isHandle == true;
    },
    async onEditSubmit() {
      this.editLoading = true;
      await this.autoform.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoform.fApi.formData();
          const res = await editPurchaseOrder(formData);
          if (res.code == 1) {
            this.getlist();
            this.editVisible = false;
          }
        } else {
        }
      });
      this.editLoading = false;
    },
    startImport() {
      this.dialogVisible = true;
    },
    cancelImport() {
      this.dialogVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      const res = await importPurchaseOrder(form);
      if (res.code == 1)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false;
    },
    async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for (var i = 0; i < fileList.length; i++) {
          if (fileList[i].status == "success") list.push(fileList[i]);
          else list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
    },
    uploadRemove(file, fileList) {
      this.uploadChange(file, fileList);
    },
    async onExport() {
      if (this.onExporting) return;
      try {
        const params = { ...this.pager, ...this.filter };
        var res = await exportPurchaseOrder(params);
        if (!res?.data) return;
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute(
          "download",
          "采购单导出_" + new Date().toLocaleString() + ".xlsx"
        );
        aLink.click();
      } catch (err) {
        console.log(err);
        console.log(err.message);
      }
      this.onExporting = false;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach((f) => {
        this.selids.push(f.proBianMa);
      });
    },
    doCopy: function (val) {
      let that = this;
      this.$copyText(val).then(
        function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
        },
        function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
        }
      );
    },
    showImg(e) {
      if (e.target.tagName == "IMG") {
        this.imgPreview.img = e.target.src;
        this.imgPreview.show = true;
      }
    },
    async showlogistics(companycode, number) {
      this.drawervisible = true;
      this.$refs.logistics.showlogistics(companycode, number);
    },
    async onShowHistory() {
      this.dialoganalysisVisible = true;
      this.$nextTick(() => {
        this.$refs.purchasehistorytjanalysis.onSearch();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(140, 134, 134, 0.6);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  img {
    width: 80%;
  }
}
#myChartOrderCount {
  width: 99%;
  height: 500px;
}
</style>

