<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="申请开始时间" endPlaceholder="申请结束时间" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="待审批" :value="0" />
                    <el-option label="同意" :value="1" />
                    <el-option label="拒绝" :value="2" />
                </el-select>
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.productId" placeholder="宝贝ID" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="handleAgree(ids, 1, 'batch')">批量同意</el-button>
                <el-button type="primary" @click="handleAgree(ids, 2, 'batch')">批量拒绝</el-button>
                <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'product-Taken202408041836'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
            @onTrendChart="trendChart" @select="checkboxRangeEnd">
            <template slot="right">
                <vxe-column title="操作" width="200" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleAgree(row.id, 1)" v-if="row.status == 0">同意</el-button>
                            <el-button type="text" @click="handleAgree(row.id, 2)" v-if="row.status == 0">拒绝</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt" :modal="false">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: "status asc,applyTime",
                type: 1,
                isAsc: false,
                styleCode: this.styleCode,
                summarys: [],
                startTime: dayjs().format('YYYY-MM-DD'), //开始时间
                endTime: dayjs().format('YYYY-MM-DD'), //结束时间
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
    },
    methods: {
        checkboxRangeEnd(row) {
            this.ids = row.map((item) => item.id);
        },
        handleAgree(ids, status, type) {
            if (type == 'batch' && this.ids.length == 0) return this.$message.error('请选择要操作的数据')
            this.$confirm(`是否${status == 1 ? '同意' : '拒绝'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post('/api/bookkeeper/PblGoodPdd/Product/Operate', {
                    ids: type == 'batch' ? ids : [ids],
                    status,
                })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    this.getList()
                }
                if (type == 'batch') {
                    this.ids = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true;
            await request.post('/api/bookkeeper/pblGoodPdd/Product/ExportData', this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            let { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Product/GetColumns')
            if (success) {
                data.unshift({
                    label: "",
                    type: "checkbox",
                });
                data = data.filter(item => item.prop !== 'origGroupName' && item.prop !== 'newGroupName')
                data.forEach(item => {
                    if (item.prop == 'productId') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.productId)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Product/PageGetData', this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        async trendChart(option) {
            var endDate = null;
            var startDate = null;

            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });

            option.startDate = startDate;
            option.endDate = endDate;

            this.chatProp.chatTime = [startDate, endDate];

            this.chatProp.chatLoading = true;
            const { data, success } = await request.post('/api/bookkeeper/pblGoodPdd/Product/GetTrendChart', option)
            if (success) {
                this.chatProp.data = data;
            }

            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;

            this.chatPropOption = option;
        },
        async changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            await this.getList()
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
