<template>
    <container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane v-if="checkPermission('api:profit:purchasepostwages:GetPurchaseWagesComputeAsync')" lazy label="薪资统计" name="first" style="height: 100%;">
                <purchasepostwages ref="purchasepostwages"></purchasepostwages>
            </el-tab-pane>
            <el-tab-pane v-if="checkPermission('api:profit:purchasepostwages:GetPurchasePostWagesAsync')" lazy label="岗位薪资设置" name="second" style="height: 100%;">
                <purchasepost ref="purchasepost"></purchasepost>
            </el-tab-pane>
            <el-tab-pane lazy label="人员" name="third" style="height: 100%;">
                <purchasepostuser ref="purchasepostuser"></purchasepostuser>
            </el-tab-pane> 
            <el-tab-pane lazy v-if="checkPermission('SemiAndFinishTab')" label="成品转半成品进货" name="fourth" style="height: 100%;">
                <SemiAndFinish ref="SemiAndFinish"></SemiAndFinish>
            </el-tab-pane> 
            <el-tab-pane v-if="checkPermission('DeptComplaint')" lazy label="部门投诉" name="complain" style="height: 100%;">
                <DeptComplaint ref="DeptComplaint" />
            </el-tab-pane>
            <el-tab-pane v-if="checkPermission('purchaseNoFreePostageToFreePostage')" lazy label="不包邮转包邮" name="postage" style="height: 100%;">
                <purchaseNoFreePostageToFreePostage ref="purchaseNoFreePostageToFreePostage" />
            </el-tab-pane>
            <el-tab-pane v-if="checkPermission('redEnvelopeIncome')" lazy label="红包收入" name="income" style="height: 100%;">
                <redEnvelopeIncome ref="redEnvelopeIncome" />
            </el-tab-pane>
            <el-tab-pane v-if="checkPermission('Demerit')" lazy label="行为扣分" name="demerit" style="height: 100%;">
                <Demerit ref="Demerit" />
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>

import container from "@/components/my-container";
import purchasepostwages from './purchasepostwages.vue';
import purchasepost from "./purchasepost.vue";
import purchasepostuser from './purchasepostuser.vue';
import SemiAndFinish from "./SemiAndFinish.vue";
import DeptComplaint from '@/views/inventory/DeptComplaint.vue';
import purchaseNoFreePostageToFreePostage from '@/views/inventory/purchaseNoFreePostageToFreePostage.vue';
import redEnvelopeIncome from './redEnvelopeIncome.vue';
import Demerit from "@/views/inventory/Demerit.vue";

export default { 
    name: 'YunHanAdminGoodsFinishedpart',
    components: { container, purchasepostwages, purchasepost, purchasepostuser, SemiAndFinish, DeptComplaint, purchaseNoFreePostageToFreePostage, redEnvelopeIncome,Demerit },
    data() {
        return {
            that: this,
            activeName: 'first',
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        //根据权限来判断显示哪个tab
        if (this.checkPermission('api:profit:purchasepostwages:GetPurchaseWagesComputeAsync')) {
            this.activeName = 'first';
        } else if (this.checkPermission('api:profit:purchasepostwages:GetPurchasePostWagesAsync')) {
            this.activeName = 'second';
        } else {
            this.activeName = 'third';
        }
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$nextTick(() => {
                // if (this.activeName == 'first') this.$refs.index.onSearch();
                // if (this.activeName == 'second') this.$refs.finishedpartVue.onSearch();
            })
        },

    },
};
</script>


</style>
