<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="拍摄人:">
          <el-input v-model="filter.userName" placeholder="拍摄人"/>
        </el-form-item>
        <el-form-item label="时间:">
          <el-date-picker style="width:320px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" 
            start-placeholder="开始" end-placeholder="结束" clearable :picker-options="pickerOptions"></el-date-picker>
         </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :isSelectColumn='true' @sortchange='sortchange'
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
  </my-container>
</template>

<script>
import {pageImagesNotFund} from '@/api/storehouse/images'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatWarehouse} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'url',label:'图片', width:'60',type:'image'},
      {istrue:true,prop:'createdUserName',label:'拍摄人', width:'90',sortable:'custom'},
      {istrue:true,prop:'createdTime',label:'拍摄时间', width:'150',sortable:'custom'},
     ];
const tableHandles1=[];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,
      filter: {
         userName:null,
         timerange:null,
         beginTime:null,
         endTime:null,
      },
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
      list: [],
      pager:{OrderBy:"id",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      imageUrl: '',
      dialogVisible: false,
      disabled: false
    }
  },
  async mounted() {
    this.getlist();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.beginTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.beginTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pageImagesNotFund(params)
      this.listLoading = false
      if (!res?.success) {return }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>