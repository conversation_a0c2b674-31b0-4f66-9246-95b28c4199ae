<template>
    <div>
        <!-- 新增一行 -->
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" class="formCss">
            <el-form-item label="数据库名称:" prop="dbName">
                <el-select v-model="ruleForm.dbName" placeholder="请选择数据库" class="publicCss" clearable>
                    <el-option v-for="item in nameList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="是否支持导出:" prop="sql">
                <el-select v-model="ruleForm.canQuery" placeholder="是否支持导出" class="publicCss">
                    <el-option key="是" label="是" :value="true" />
                    <el-option key="否" label="否" :value="false" />
                </el-select>
            </el-form-item>
            <el-form-item label="请选择用户:" prop="userIds">
                <el-select v-model="ruleForm.userIds" filterable placeholder="请选择用户" multiple collapse-tags
                    :filter-method="searchReferrer" clearable :remote-method="changeSelectValue" @change="changeUser">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="标题:" prop="title">
                <el-input v-model="ruleForm.title" placeholder="标题" class="publicCss" clearable maxlength="200" />
            </el-form-item>
            <el-form-item label="描述:" prop="remark">
                <el-input v-model="ruleForm.remark" placeholder="描述" class="publicCss" clearable maxlength="200" />
            </el-form-item>
            <el-form-item label="sql:" prop="sql">
                <el-tooltip effect="dark" content="条件站位符：{where}，如： where 1=1 and {where}" placement="top-start">
                    <el-input type="textarea" :rows="2" placeholder="sql语句" v-model="ruleForm.sql" />
                </el-tooltip>
            </el-form-item>
            <el-button type="text" @click="addRow">新增一行</el-button>
            <el-table :data="ruleForm.columns" style="width: 100%" max-height="200">
                <el-table-column prop="name" label="字段名" width="180">
                    <template #default="{ row }">
                        <el-input v-model="row.name" placeholder="字段名" class="publicCss" clearable maxlength="200" />
                    </template>
                </el-table-column>
                <el-table-column prop="label" label="显示名" width="180">
                    <template #default="{ row }">
                        <el-input v-model="row.label" placeholder="显示名" class="publicCss" clearable maxlength="200" />
                    </template>
                </el-table-column>
                <el-table-column prop="conditionType" label="查询类型">
                    <template #default="{ row }">
                        <el-select v-model="row.conditionType" placeholder="查询类型" class="publicCss">
                            <el-option v-for="item in conditionTypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="dataType" label="显示类型">
                    <template #default="{ row }">
                        <el-select v-model="row.dataType" placeholder="显示类型" class="publicCss">
                            <el-option key="文本" label="文本" :value="1" />
                            <el-option key="数字" label="数字" :value="2" />
                            <el-option key="时间" label="时间" :value="3" />
                            <el-option key="是否" label="是否" :value="4" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="conditionRequired" label="是否查询必填项" width="150">
                    <template #default="{ row }">
                        <el-select v-model="row.conditionRequired" placeholder="是否查询必填项" class="publicCss">
                            <el-option key="是" label="是" :value="true" />
                            <el-option key="否" label="否" :value="false" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="conditionDS" label="查询数据源" width="200">
                    <template #default="{ row }">
                        <el-input v-model="row.conditionDS" placeholder="JSON对象数组" class="publicCss" clearable
                            maxlength="2000" />
                    </template>
                </el-table-column>
                <el-table-column prop="" label="操作" width="200">
                    <template #default="{ row, $index }">
                        <div style="display: flex;">
                            <el-button type="primary" @click="doCopy" class="copyButton">复制数据源格式</el-button>
                            <el-button type="danger" @click="ruleForm.columns.splice($index, 1)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="btnGroup">
                <el-button @click="close">取消</el-button>
                <el-button type="primary" @click="submit('ruleForm')" v-throttle="2000">保存</el-button>
            </div>
        </el-form>
    </div>
</template>

<script>
import { mergeReporter } from '@/api/bookkeeper/reporter'
import { getUserListPage } from '@/api/admin/user'
import { getShootingViewPersonAsync } from '@/api/media/changeimg'
const conditionTypeList = [
    { label: '日期范围', value: 1 },
    { label: '时间范围', value: 2 },
    { label: '单选', value: 3 },
    { label: '多选', value: 4 },
    { label: '精确查询', value: 5 },
    { label: '模糊查询', value: 6 },
    { label: '数字范围', value: 7 },
    { label: '是否选择', value: 8 },
    { label: '多行或选', value: 9 },
]
export default {
    name: 'SelfServiceTable',
    props: {
        nameList: {
            type: Array,
            default: () => []
        },
        close: {
            type: Function,
            default: () => { }
        },
        editPropsList: {
            type: Object,
            default: () => { }
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            rules: {
                dbName: [
                    { required: true, message: '请选择数据库', trigger: 'change' }
                ],
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' }
                ],
                userIds: [
                    { required: true, message: '请选择用户', trigger: 'change' }
                ],
                sql: [
                    { required: true, message: '请输入sql语句', trigger: 'blur' }
                ]
            }
            ,//抽屉验证规则
            ruleForm: {//抽屉表单
                title: null,//标题
                remark: null,//备注
                dbName: null,//数据库名称
                sql: null,//sql语句z
                canQuery: true,//是否支持查询条件
                canExport: true,//是否支持导出
                userIds: [],//用户id
                userNames: null,//用户名称
                names: [],
                columns: []
            },
            ReferrerOptions: [],//推荐人下拉框选项tuijianrenId
            options: [{ value: '0', label: '所有人' }],
            conditionTypeList
        }
    },
    mounted() {
        if (this.isEdit) {
            this.ruleForm = this.editPropsList
            this.ruleForm.userNames = this.editPropsList.userNames ? this.editPropsList.userNames.split(',') : ''
            this.ruleForm.userIds = this.editPropsList.userIds ? this.editPropsList.userIds.split(',') : []
            if (this.ruleForm.userIds.length > 0) {
                const index = this.ruleForm.userIds.findIndex(item => item === '0')
                if (index != -1) {
                    this.options.splice(0, 1)
                }
                const userList = this.ruleForm.userIds.map((item, index) => {
                    return {
                        value: item,
                        label: item == '0' ? '所有人' : this.ruleForm.userNames[index]
                    }
                })
                this.options = [...this.options, ...userList]
            }
        }
    },
    methods: {
        doCopy: function () {
            let val = JSON.stringify([
                { "value": 1, "label": "张三" },
                { "value": 2, "label": "李四" },
            ]);
            let that = this;
            this.$copyText(val).then(function (e) {
                that.$message({ message: "内容已复制到剪切板！", type: "success" });
            }, function (e) {
                that.$message({ message: "抱歉，复制失败！", type: "warning" });
            })
        },
        changeUser(e) {
            if (e.length > 0) {
                const index = e.findIndex(item => item == 0)
                if (index != -1) {
                    if ((e.length > 1) && index == 0) {
                        e.splice(1, 1)
                        this.$message.error('选择所有人时，无法选择其他人员')
                    }
                    if (e.length > 1 && index != 0) {
                        e.splice(index, 1)
                        this.$message.error('选择其他人员时，无法选择所有人')
                    }
                }
                this.ruleForm.names = [...new Set(this.options.filter(item => e.includes(item.value)))]
            } else {
                this.ruleForm.names = []
            }
        },
        changeSelectValue(e) {
            if (e.length > 0) {
                this.ruleForm.userIds = e
            } else {
                this.ruleForm.userIds = null
                this.options = []
            }
        },
        async searchReferrer(parm) {
            if (!parm) {
                return;
            }
            let dynamicFilter = { field: 'nickName', operator: 'Contains', value: parm }
            const { data } = await getUserListPage({ currentPage: 1, pageSize: 50, dynamicFilter })
            this.$nextTick(() => {
                //合并查询的结果和现有结果,并且去除掉重复的用户
                this.options = this.options.concat(data.list.filter(item => !this.options.some(t => t.value == item.id)).map(f => {
                    return { value: f.id, label: f.nickName }
                }))
            })
        },
        submit(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.userIds = this.ruleForm.userIds ? this.ruleForm.userIds.join(',') : ''
                    this.ruleForm.userNames = this.ruleForm.names?.map(item => item.label)?.join(',')
                    const { success } = await mergeReporter(this.ruleForm)
                    if (success) {
                        this.$emit('getList', 'true')
                        this.close()
                        this.$message.success('保存成功')
                    }
                } else {
                    this.$message.error('请输入完整信息')
                    return false;
                }
            });

        },
        clear() {
            this.$refs.ruleForm.resetFields()
        },
        //新增一行
        addRow() {
            this.ruleForm.columns.push({
                name: null,//字段名
                label: null,//显示名
                dataType: null,//显示类型
                conditionType: null,//查询类型
                conditionRequired: true,//是否查询必填项
                conditionDS: null,//查询数据源
                inCondition: true
            })
        },
    }
}
</script>

<style scoped lang="scss">
.formCss {
    padding: 20px;
    box-sizing: border-box;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;

    .el-button {
        margin: 0 10px;
    }
}
</style>