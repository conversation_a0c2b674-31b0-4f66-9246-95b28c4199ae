<template>
  <MyContainer>
    <template #header>
      <div >
        <!-- <el-date-picker v-model="ListInfo.yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker> -->
        <el-button style="padding: 0;margin: 0;width: 250px; border: none;">
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
      </el-button>
      <el-button style="padding: 0;margin: 0;width: 500px; border: none;">
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </el-button>
        <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
          <el-input v-model.trim="ListInfo.province" placeholder="省份" maxlength="50" clearable class="" />
        </el-button>
        <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="" />
          </el-button>
          
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.mailNumberList" v-model="ListInfo.mailNumberList" width="160px"
              placeholder="邮件号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="mailNumberCallback" title="邮件号">
            </inputYunhan>
            </el-button>
            <el-button style="padding: 0;margin: 0;border: none;" >
            <el-select filterable v-model="ListInfo.platform" placeholder="请选择平台" multiple collapse-tags
              clearable style="width: 160px">
              <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 200px; border: none;">
                <!-- <el-input v-model.trim="ListInfo.originalOnlineOrderNo" placeholder="原始线上订单号" maxlength="50" clearable class="" /> -->
                <div  class="publicCss" >
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.originalOnlineOrderNo" v-model="ListInfo.originalOnlineOrderNo" width="200px"
              placeholder="原始线上订单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="原始线上订单号">
            </inputYunhan>
         </div>
          </el-button>

          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                  <!-- <el-input v-model.trim="ListInfo.orderNo" placeholder="内部单号" maxlength="50" clearable class="" /> -->
                  <inputYunhan ref="productCode" :inputt.sync="ListInfo.orderNo" v-model="ListInfo.orderNo" width="200px"
              placeholder="内部单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback2" title="内部单号">
            </inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                    <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                      <el-input v-model.trim="ListInfo.labels" placeholder="标签" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
                        <el-input v-model.trim="ListInfo.status" placeholder="状态" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名称" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-select  ref="dFShopNameInput" v-model="ListInfo.dFShopName" multiple collapse-tags clearable filterable placeholder="店铺归属"  >
              <el-option v-for="(item,index) in dFShopNameList" :key="index" :label="item" :value="item" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.distributorCode" placeholder="分销商编码" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.outWareHouseName" placeholder="真实发货仓库" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 60px; border: none;">
            <el-checkbox v-model.trim="ListInfo.isempty"  maxlength="50" clearable class="" />空数据
          </el-button>
          <el-button style="padding: 0;margin: 0;width: 60px; border: none;">
            <el-checkbox v-model="ListInfo.noUseCatch">非缓存</el-checkbox>
          </el-button>

        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="lockExpressFee(1)">数据锁定</el-button>
        <el-button type="primary" @click="lockExpressFee(0)">数据解锁</el-button>

        <!-- <el-button type="primary" @click=startImport>导入</el-button> -->


    </div>
    </template>
    <vxetablebase :id="'erpExport202410131003'" :tablekey="'erpExport202410131003'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="90" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" @click="onDeleteOperation(row)"><span style="color: red;">批次删除</span></el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="数据锁定/解锁" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 50px;">
            <el-date-picker v-model="yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="onLockData()">提交确认</el-button>
      </span>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { warehouselist, formatWarehouse,formatTime,pickerOptions ,formatWarehouseNew,platformlist} from "@/utils/tools";
import { getExpressComanyAll,getExpressComanyStationName, getExpressInfoData_Month, exportExpressInfoData_Month ,importExpressFeeCalculateReview
  ,deleteMonthExpressReview,epressFreeLock,geDaiFaShopConfigurationList} from "@/api/express/express";
import dayjs from 'dayjs'
import queryCondition from "../../dailyCourierFee/components/queryCondition.vue";
import inputYunhan from "@/components/Comm/inputYunhan";

const dFShopNameList = [
  '昀晗',
  '分销',
  '其他',
  '独立站',
]

const tableCols = [
{ sortable: 'custom', width: '80', align: 'center', prop: 'inportDate', label: '导入日期', formatter: (row) => formatTime(row.inportDate,"YYYY-MM-DD") },
{ sortable: 'custom', width: '80', align: 'center', prop: 'expressCompanyName', label: '快递公司', },
{ sortable: 'custom', width: '80', align: 'center', prop: 'prosimstateId', label: '快递站点',  formatter: (row) => row.prosimstate, type: 'custom' },
{ sortable: 'custom', width: '80', align: 'center', prop: 'warehouseId', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouseId)  },
{ sortable: 'custom', width: '100', align: 'center', prop: 'outWareHouseName', label: '真实发货仓库', },
{ sortable: 'custom', width: '110', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'jsCount', label: '计数', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'waybill', label: '面单', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'totalFreight', label: '运费合计', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'province', label: '省份', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'originalOnlineOrderNo', label: '原始线上订单号 ', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderNo', label: '内部单号',  formatter: (row) => row.orderNo ? row.orderNo : '', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'platform', label: '平台',  formatter: (row) => row.platformStr  },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName1', label: '店铺名称1', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopCode', label: '店铺编号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'dfShopName', label: '店铺归属', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'distributorCode', label: '分销商编码', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'goodsWeight', label: '商品重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'orderStatus', label: '订单状态', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'shopStatus', label: '店铺状态', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'customerPayment', label: '客户实付', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'label', label: '标签', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'distributor', label: '分销商', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'quantity', label: '数量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'billingWeight', label: '快递重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'auditAmount', label: 'ERP核算运费', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注留言',  formatter: (row) => row.remark+row.buyer_message },
]
export default {
  name: "erpExport",
  components: {
    MyContainer, vxetablebase,queryCondition,inputYunhan
  },
  data() {
    return {
      dFShopNameList,
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      platformlist: platformlist,
      dialogVisible: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      that: this,
      formatWarehouseNew:formatWarehouseNew,
      expressCompanyId: null,
      warehouselist: warehouselist,
      warehouse: null,
      pickerOptions,
      expresscompanylist: [],
      dialogVisible2:false,
      prosimstate: null,
      prosimstatelist: [],
      ListInfo: {
        DataType:6,
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        yearmonth:null,
        startTime: null,//开始时间
        endTime: null,//结束时间
        mailNumberList: null,
        noUseCatch: false,//非缓存
        batchNumber: null,//批次号
        originalOnlineOrderNo: null,//原始线上订单号
        orderNo: null,//内部单号
        goodsCode: null,//商品编码
        labels: null,//标签
        status: null,//状态
        dFShopName:null,//店铺归属
        distributorCode:null,
      },
      yearmonth:null,
      lockType:1,
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      expresscompanylist: [],
      creatTime: null,
      formatWarehouseNew,
      warehouselist: warehouselist,
      warehouse: null,
    }
  },
  async mounted() {
   // this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
     if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    // await this.getList()
    const res =  await geDaiFaShopConfigurationList({pageSize:10000,currentPage:1});
    if(res.success){
      res.data.list.forEach(x=>{
        this.dFShopNameList.push(x.shopName1)
      })
    }
    this.dFShopNameList = [...new Set(this.dFShopNameList)];
    await this.init()
  },
  methods: {
     async getprosimstatelist (val) {

      var id;
      if (val == 1)
         {
          id = this.expressCompanyId
          this.prosimstate=null
         }
      else if (val == 2) {
          id = this.ListInfo.expressName
          this.ListInfo.prosimstate = null
      }

      var res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
          this.prosimstatelist = res.data
      }
  },
  lockExpressFee(val){
    this.lockType=val;
this.dialogVisible2=true;

  },
  
  async onLockData() {
    if(!this.yearmonth){
        this.$message.error('请选择月份')
        return
      }

      this.$confirm('确认提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await epressFreeLock({ yearMonth: this.yearmonth ,lockType:this.lockType })
        if (success) {
          this.dialogVisible2=false
          this.$message.success('提交成功')
        } else {
          this.$message.error('提交失败')
        }
      }).catch(() => {
        this.$message.info('已取消提交')
      });
    },
  async onDeleteOperation(row) {
      this.$confirm('是否删除该批次数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteMonthExpressReview({ batchNumber: row.batchNumber ,inportDate:row.inportDate })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    productCodeCallback(val) {
      this.ListInfo.originalOnlineOrderNo = val;
    },
    productCodeCallback2(val) {
      this.ListInfo.orderNo = val;
    },
    mailNumberCallback(val) {
      this.ListInfo.mailNumberList = val;
    },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressInfoData_Month({...this.ListInfo,...this.topfilter})
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', 'ERP导出月账单数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressInfoData_Month({...this.ListInfo,...this.topfilter})
      if (success) {

        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary

        let summary = data.summary || {}

const resultsum = {};
Object.entries(summary).forEach(([key, value]) => {
    resultsum[key] = formatNumber(value);
});
function formatNumber(number) {
    const options = {
        useGrouping: true,
    };
    return new Intl.NumberFormat('zh-CN', options).format(number);
}
this.summaryarry = resultsum
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    productSelect(e,val) {
      this.$nextTick(() => {
      let value = e.target.value; // 输入框值
        if(value) { // 你输入才有这个值 不为空，如果你下拉框选择的话 这个值为空
          if(val == 1) {
            this.ListInfo.goodsCode = value
          } else if(val == 2) {
            this.ListInfo.labels = value
          } else if(val == 3) {
            this.ListInfo.status = value
          } else if(val == 4) {
            this.ListInfo.remark = value
          } else if(val == 5) {
            this.ListInfo.dFShopName = value
          }
        }
      });
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("creatTime", this.creatTime);
      // form.append("yearMonthDay", this.yearMonthDay);
      var res = await importExpressFeeCalculateReview(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
    },
    onSubmitUpload() {
      // if (!this.yearMonthDay) {
      //   this.$message({ message: "请选择日期", type: "warning" });
      //   return false;
      // }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
        //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
