<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="90px">        
          <el-date-picker v-model="pickvalue" type="daterange" align="right" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" class="rightMar"
                    :clearable="true" @change="changeDate">
          </el-date-picker>
          <el-input style="width:150px;" v-model.trim="filter.goodsCode" placeholder="商品编码" clearable :maxlength="200"/>
          <el-input style="width:150px;" v-model.trim="filter.goodsName" placeholder="商品名称" clearable :maxlength="200"/>
          <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-form>
    </template>
 
    <vxetablebase ref="table" :id="'PrePackGoodsStreateRecordTable'" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :tableData='list' :showsummary='true' :tableCols='tableCols' :border='true' :loading="listLoading">  
    </vxetablebase>
    
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template> 
  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue"; 
import dayjs from 'dayjs'
import { 
  //分页查询预包加工记录
  pageGetPrePackGoodsTreateRecordAsync
} from "@/api/inventory/prepack.js"
  
const tableCols =[ 
    { istrue: true, prop: 'createdTime', label: '开单日期', width: '100', sortable: 'custom', formatter:(row)=> formatTime(row.createdTime, 'YYYY-MM-DD') },
    { istrue: true, prop: 'goodsCode', align: 'center', label: '商品编码', width: '105', sortable: 'custom', },
    { istrue: true, prop: 'goodsName', label: '商品名称', }, 
    { istrue: true, prop: 'combineDetailGoodsCount', align: 'center', label: '包含子商品数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'avgWeight', align: 'center', label: '快递重量', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'childWeight', align: 'center', label: '对应子编码数量的重量/克', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'childPackage', label: '对应子编码包装', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'packageConsumables', label: '打包耗材', width: '120', sortable: 'custom', },
    //{ istrue: true, prop: 'isProcessClaim', label: '加工认领', width: '100', sortable: 'custom', formatter:(row)=>row.isProcessClaim?"已认领":"未认领" },
    { istrue: true, prop: 'prePackCode', label: '预包编码', width: '105', sortable: 'custom', },
    { istrue: true, prop: 'prePackInventory', label: '预包编码库存', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'isCombineGoods', label: '是否组合装', width: '80', sortable: 'custom', formatter:(row)=> {return row.isCombineGoods == 1? "是":"否"}  },
    { istrue: true, prop: 'wareHouseId', label: '加工仓', width: '110', sortable: 'custom', formatter:(row)=> {return row.wareHouseName}},
    { istrue: true, prop: 'weekAvgQuantity', label: '应加工数量', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'treateCount', label: '加工数量', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'treateStatus', label: '加工状态', width: '80', sortable: 'custom', }
  ]; 
export default {
  name: 'goods',
  components: {cesTable, vxetablebase, MyContainer, MyConfirmButton },
  data() {
    return {
      that:this,   
      filter: {
        styleCode:null,
        goodsCode:null,
        goodsName:null,
        groupId:null,
        brandId:null
     }, 
      addshow: false,
      list: [],
      summaryarry:{},
      pager:{orderBy:"",isAsc:false},
      tableCols:tableCols,   
      total: 0,
      pickvalue: null,
      sels: [], 
      listLoading: false,
      pageLoading: false, 
      pickerOptions: {
        shortcuts: [{
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
        // 自定义日期禁用函数
        disabledDate(date) {
          // 获取当前日期
          const currentDate = new Date();
          // 如果选中日期在当前日期之后，则禁用它
          return date > currentDate;
        }
      },
    }
  },
  async mounted() { 
    await this.getlist();   
  },
  methods: {  
    changeDate(e) {
            //如果e为空或者e为null或者为undefined就清空时间
        if (!e) {
            this.filter.beginDate = null
            this.filter.endDate = null
        }
        //如果e不为空就赋值
        if (e) {
            this.filter.beginDate = dayjs(e[0]).format('YYYY-MM-DD')
            this.filter.endDate = dayjs(e[1]).format('YYYY-MM-DD')
        }
    },
    //获取查询条件
    getCondition(){
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = { ...pager, ...page, ... this.filter}
      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist()
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
        return;
      }
      this.pageLoading = true;
      var res = await pageGetPrePackGoodsTreateRecordAsync(params);
      this.pageLoading = false;
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false; 
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        var orderBy =column.prop;
        this.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    }, 
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
