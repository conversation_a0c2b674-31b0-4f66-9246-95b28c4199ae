<template>
    <my-container v-loading="pageLoading">
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='datalist' @select='selectchange' :tableCols='tableCols' :isSelectColumn='true'
            :customRowStyle="customRowStyle" :loading="listLoading" :summaryarry="summaryarry" :selectColumnHeight="'0px'"
            :isBorder="false" style="height: 100%;">
            <template slot='extentbtn'> </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 新增岗位/编辑岗位 -->
        <el-dialog title="查看岗位" :visible.sync="showDialog" width="30%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <postionDialogform v-if="showDialog" :isCheck="true" ref="postionDialogform"></postionDialogform>
            <!-- <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDialog = false">取 消</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" v-show="!islook" />
                </span>
            </template> -->
        </el-dialog>
        <!-- 完成 -->
        <el-dialog :title="'招聘人数('+count+')'" :visible.sync="showHeadcount" width="60%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <headcount :planId="planId" v-if="showHeadcount"></headcount>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import headcount from "@/views/profit/PersonnelRecruiting/headcount";
import { getPageRecruitmentPlan } from '@/api/profit/hr'
import { formatTime } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'department', label: '招聘部门',sortable: 'custom', },
    { istrue: true, prop: 'positionName', align: 'left', label: '岗位名称',sortable: 'custom',width: "280"  },
    { istrue: true, prop: 'jobRequirements', align: 'left', label: '岗位要求',sortable: 'custom', },
    {
        istrue: true, prop: 'recruitmentCount', align: 'left', label: '招聘人数',width: "100" , type: "click", handle: (that, row) => {
            that.showHeadcount = true, that.planId = row.planId,
                that.count = row.onboardCount + '/' + row.recruitmentCount
        },
        formatter: (row) => row.onboardCount + '/' + row.recruitmentCount,sortable: 'custom',
    },
    {
        istrue: true, prop: 'recruitmentEndDate', align: 'left', label: '招聘时间', width: "200", formatter: (row) => formatTime(row.recruitmentStartDate, 'YYYY-MM-DD')
            + '至' + formatTime(row.recruitmentEndDate, 'YYYY-MM-DD'),sortable: 'custom',
    },
    { istrue: true, prop: 'recruiters', align: 'left', label: '招聘专员' ,sortable: 'custom',},
    { istrue: true, prop: 'addedDate', align: 'left', label: '添加时间' ,sortable: 'custom',},
    {
        istrue: true, prop: 'planStatus', align: 'left', label: '完成原因', formatter: (row) => row.planStatus == 21 ? '逾期关闭' : row.planStatus == 22 ? '手动关闭' :
            row.planStatus == 23 ? '满员关闭':'',sortable: 'custom', },
    { istrue: true, prop: 'closeDate', align: 'left', label: '完成时间',sortable: 'custom', },
    {
        istrue: true, type: "button", label: '操作',
        btnList: [
            { label: "查看", permission: "", handle: (that, row) => that.oncheck(row.planId) },
            { label: "重新发布", permission: "", handle: (that, row) => that.republish(row.planId) },
        ]
    }
];
import { getRecruitmentPlan, createRecruitmentPlan } from '@/api/profit/hr'
export default {
    name: "completeRecruitment",//完成招聘
    components: {
        MyContainer, postionDialogform, MyConfirmButton
        , cesTable, headcount,
    },
    // props: {
    //     showDialog: {
    //         type: Boolean,
    //         default: () => { return false; }
    //     },
    //     diologTitle: {
    //         type: String,
    //         default: () => { return ''; }
    //     },
    // },
    props: {
        ddDeptId:{ type: String, default: '' },
    },
    watch: {
    },
    data () {
        return {
            count:'',
            planId:0,
            showDialog: false,
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            islook: false,
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            finishReason: null,//完成原因
            showFinishDialog: false,//显示完成弹窗
            showHeadcount: false,//显示招聘人数列表
            filter: {
                closeStatus: 1,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: null,//招聘部门
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                closeReason: null,//完成原因
            }
        };
    },
    watch: {
    },
    async created () {

    },
    async mounted () {
        this.onSearch()
    },
    methods: {
        // 重新发布
        republish (planId) {
            this.$confirm('重新发布会新建另外一条招聘计划，进入新岗位的编辑页面，系统将自动填写被复制岗位的全部信息。', '确认重新发布该岗位招聘计划吗？', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                if (planId) {
                    let ruleForm = {}
                    //获取岗位信息
                    getRecruitmentPlan({ planId: planId }).then(res => {
                        if (res.success) {
                            ruleForm = res.data;
                            ruleForm.planId = null;
                            //新增
                            createRecruitmentPlan(ruleForm).then(res => {
                                if (res.success) {
                                    this.$message({ message: '重新发布成功', type: "success" });
                                    this.onSearch()
                                }
                            })

                        }
                    })
                }
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消重新发布' });
            });
        },
        onSearch (filter) {
            if (filter) {
                this.filter = filter
            }
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        // 查看
        oncheck (planId) {
            setTimeout(() => {
                this.$refs.postionDialogform.getInfo(planId);
            }, 1);
            this.showDialog = true;
        },

        //获取数据
        async getDataList () {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
                ddDeptId: this.ddDeptId
            };
            this.listLoading = true;
            const res = await getPageRecruitmentPlan(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.shopDecorationTaskId);
            })
        },
        //多选事件
        selectchange: function (rows, row) {
            this.sels = [];
            rows.forEach(f => {
                this.sels.push(f.v1);
            })
        },
        //批量
        async onAll () {
            if (this.sels.length == 0) {
                this.$message({ type: 'warning', message: "请选择一个岗位" });
                return;
            }
            this.$confirm("选中的岗位将会重新发布，是否确定", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                const res = await moveTaskToOver(this.sels)// 重新发布到方法
                if (res?.success) {
                    this.$message({ message: '发布成功', type: "success" });
                    this.selids = [];
                    this.onSearch();
                    var self = this;
                    setTimeout(() => { self.reload(); }, 100);
                }
            });
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
    },
};
</script>