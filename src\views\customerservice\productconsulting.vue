<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent
      >

<div></div>
<div>
        <el-link :underline="false">人员转换率：</el-link>

        <el-form-item prop="renmin">
          <el-input v-model="Filter.MinPersonRate" style="width: 100px" />
        </el-form-item>
        ~
        <el-form-item prop="renmax">
          <el-input v-model="Filter.MaxPersonRate" style="width: 100px" />
        </el-form-item>

        <el-link :underline="false">机器转换率：</el-link>

        <el-form-item prop="renmin">
          <el-input v-model="Filter.MinAiRate" style="width: 100px" />
        </el-form-item>
        ~
        <el-form-item prop="renmax">
          <el-input v-model="Filter.MaxAiRate" style="width: 100px" />
        </el-form-item>

        <el-form-item label="时间:">


          <el-date-picker
            v-model="Filter.timerange"
            type="datetimerange"
             :picker-options="pickerOptions"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-value="defaultDate"
          ></el-date-picker>
        </el-form-item>

 <el-form-item label="包含机器人:">
        <el-switch :width="40" @change="changecontainjiqi"
          v-model="Filter.containjiqistatus"
          inactive-color="#228B22"
          active-text="是"
          inactive-text="否">
        </el-switch>
        </el-form-item>

</div>
<el-form-item label="平台:">
          <el-select
             v-model="Filter.p"
            placeholder="请选择"
            class="el-select-content"
            @change="changePlatform"
            style="width: 150px"
            clearable
          >
            <el-option

              v-for="item in platformList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="店铺:">
          <el-select
            v-model="Filter.shopid"
            placeholder="请选择"
            class="el-select-content"
            clearable
          >

            <el-option
              v-for="item in shopList"
              :key="item.id"
              :label="item.shopName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="商品标题:">
          <el-input v-model="Filter.productName" style="width: 190px" />
        </el-form-item>


           <el-form-item label="商品ID:">
          <el-input v-model="Filter.ProductID" style="width: 320px" />
        </el-form-item>



        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>

        <!-- <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>

        <el-form-item>
          <a href="../static/excel/快递费用规则导入模板(每个快递公司一个文件).xlsx">
            <el-button type="primary">下载导入魔板</el-button>
          </a>
        </el-form-item> -->
        <el-form-item>
          <el-button v-if="checkPermission(['api:customerservice:productconsulting:ImportProductconsultingMultipleAsync'])" size="small" type="primary" @click="startImport"
            >导入商品咨询明细</el-button
          >
        </el-form-item>

        <el-form-item>
          <el-button  size="small" type="primary" @click="downloadTemplate"
            >商品咨询明细导入模板</el-button
          >
        </el-form-item>

        <el-form-item>
          <el-button  v-if="checkPermission(['api:customerservice:productconsulting:ExportConversionRateAsync'])"  size="small" type="primary" @click="startExport"
            >导出</el-button
          >
        </el-form-item>
      </el-form>


    </template>
    <!--列表-->

    <ces-table
      ref="table"
      :that="that"
      :isIndex="true"
      :hasexpand="true"
      @sortchange="sortchange"
      :isSelection="true"
      :tableData="ExpressList"
      :tableCols="tableCols"
      :tableHandles="tableHandles"
      :loading="listLoading"
    >
    </ces-table>

    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="ratelisttotal"
        @get-page="getExpressList"
      />
    </template>

    <!--新增快递费规则-->

    <el-dialog
      title="导入商品咨询明细"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload
              ref="upload"
              class="upload-demo"
              :auto-upload="false"
              :multiple="false"
              :limit="1"
              action
              accept=".xlsx"
              :http-request="uploadFile"
              :on-change="uploadchange"
              :file-list="fileList"
              :data="fileparm"
            >
              <template #trigger>
                <el-button size="small" type="primary"
                  >选取商品咨询文件</el-button
                >
              </template>
              <el-button
                style="margin-left: 10px"
                size="small"
                type="success"
                @click="submitUpload"
                >上传</el-button
              >
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="每人转化统计"
      :visible.sync="everyPersonVisible"
      width="60%"
    >
      <span>
        <personstatistics ref="personstatistics" :Filter="Filter" :personpager="personpager" ></personstatistics>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="everyPersonVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="detailType" :visible.sync="detailVisible" width="70%" v-dialogDrag>
      <span>


 <el-button  v-if="checkPermission(['api:customerservice:productconsulting:ExportConversionRateAsync'])"  size="small" type="primary" @click="startExportDetail"
            >导出所有</el-button>

  <el-tabs v-model="activeName" @tab-click="changeemp">
    <el-tab-pane v-bind:key="name" v-for="name in emplist" :label="name" :name="name"></el-tab-pane>

  </el-tabs>



        <el-table
          v-loading="listLoading"
          :data="detaildata"
          highlight-current-row
          :lazy="true"
          style="width: 100%; height: 100%"
          @selection-change="onSelsChange"
          :default-expand-all="false"
          :row-class-name="getRowClass"
        >
          <!-- <el-table-column type="selection" width="40" /> -->
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column prop="productID" label="商品ID" width="120" />
          <el-table-column prop="seller" label="人员" width="120">
            <!-- <template #default="{ row }">
          <el-tag
            :type="row.enabled ? 'success' : 'danger'"
            disable-transitions
            >{{ row.enabled ? "正常" : "禁用" }}</el-tag>
        </template> -->
          </el-table-column>

          <el-table-column prop="productName" label="商品名称" width="600">
          </el-table-column>

          <el-table-column prop="buyer" label="咨询者" width="180">
            <template #default="{ row }">
              <el-link :underline="true" @click="copyinfo(row.buyer)">{{
                row.buyer
              }}</el-link>
            </template>
          </el-table-column>

          <el-table-column prop="chatdate" label="咨询日期" width="100">
            <template #default="{ row }">
              {{ formatdate(row.chatdate) }}
            </template>
          </el-table-column>

          <el-table-column prop="createdTime" label="是否成交" width="80">
            <template #default="{ row }">
              <div v-if="row.result == 2" style="color: red">未成交</div>

              <div v-if="row.result == 1" style="color: black">已成交</div>
            </template>
          </el-table-column>
        </el-table>

        <div class="block">
          <el-pagination
            layout="prev, pager, next"
            @current-change="changePage"
            :total="detailpager.total"

          >
          </el-pagination>
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </span>
    </el-dialog>

     <el-dialog title="培训资料" :visible.sync="resourcedialogVisible" width="30%"  height="50%" v-dialogDrag>
      <span>
  <trainresourceupload ref="trainres">

        </trainresourceupload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resourcedialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>


  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import checkPermission from '@/utils/permission'
import { getList as getshopList } from "@/api/operatemanage/base/shop";

import trainresourceupload from "@/views/customerservice/trainresourceupload.vue"
import { rulePlatform } from "@/utils/formruletools";

import cesTable from "@/components/Table/table.vue";
import {
  formatPlatform,
  formatYesorno,
  formatYesornoBool,
  formatbianmastatus,
  formatproducmask,
  formatLink,
  formatLinkProCode,
} from "@/utils/tools";
import {
  ruleExpressComany,
  ruleWarehouse,
  ruleYesornoBool,
} from "@/utils/formruletools";

import {
  importProductconsulting,
  getRateList,
  getRateDetailList,
  getConsultingList,
  exportRateList,
  getEmployeeListByProductList,
  exportProductConsultDetailList
} from "@/api/customerservice/productconsulting";
import {
addtrainplan
} from "@/api/customerservice/trainplan";

import personstatistics from "./personstatistics.vue"


const tableCols = [
  {
    istrue: true,
    prop: "productID",
    label: "商品ID",
    width: "180",
    sortable: "custom",
    type: "html",
    type: "html",
    formatter: (row) => formatLinkProCode(row.platform, row.productID),
  },
  { istrue: true, prop: "productName", label: "标题", width: "260" },
  {
    istrue: true,
    prop: "allren",
    label: "人工咨询合计",
    width: "120",
    sortable: "custom",

  },
   {
    istrue: true,
    prop: "alljiqi",
    label: "机器咨询合计",
      sortable: "custom",
    width: "120",

  },
  { istrue: true, prop: "shopname", label: "店铺", width: "120" },
  { istrue: true, prop: "oprater", label: "运营人", width: "100" },
  { istrue: true, prop: "oprateManager", label: "组长", width: "100" },

  {
    istrue: true,
    prop: "ren",
    label: "人工转化率",
    width: "120",
    sortable: "custom",
    formatter: (row) =>
     //return parseInt(row.ren * 100) + "%";
    !row.ren?" ": (row.ren*100).toFixed(2)+'%'

  },

  {
    istrue: true,
    prop: "jiqi",
    label: "机器转化率",
    width: "120",
    sortable: "custom",
    formatter: (row) => {
      return parseInt(row.jiqi * 100) + "%";
    },
  },
  {
    istrue: true,
    prop: "jiqicha",
    label: "机器vs人工均值",
     sortable: "custom",
    width: "160",
    type: "html",
    formatter: (row) => {
      if (row.ren - row.jiqi >= 0.03) {
        return (
          "<div style='color:red;'>" +
          parseInt((row.jiqi-row.ren) * 1000) / 10 +
          "%" +
          "</div>"
        );
      } else {
        return parseInt((row.jiqi-row.ren) * 1000) / 10 + "%";
      }
    },
  },
  {
    istrue: true,
    prop: "highrate",
    label: "高转化均值",
    width: "120",
    sortable: "custom",
    formatter: (row) => {
      return parseInt(row.highrate * 1000) / 10 + "%";
    },
  },
  {
    istrue: true,
    prop: "gaocha",
    label: "高转化vs人工均值",
    width: "140",
    sortable: "custom",
    formatter: (row) => {
      return parseInt((row.highrate-row.ren) * 1000) / 10 + "%";
    },
  },


  {
    istrue: true,
    type: "button",
    label: "操作",
    width: "430",
    btnList: [
      { label: "按人统计", handle: (that, row) => that.showperson(row) },
      { label: "成交明细", handle: (that, row) => that.showdetail(row, 1) },
      { label: "未成交明细", handle: (that, row) => that.showdetail(row, 2) },
    ],
  },








];
const tableHandles1 = [];

export default {
  name: "Users",
  components: {
    MyContainer,
    cesTable,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
    trainresourceupload,
    personstatistics
  },

  data() {
    return {
      date: '',
      that: this,
      Filter: {
        StartCreatedTime: "",
        EndCreatedTime: "",
        Warehouse: null,
        Province: null,
        Enabled: null,
        productName:'',
        p:null,

        FreightRuleType: null,
        timerange: "",
        yearmonth: null,
      },
      platformid:0,
      tableCols: tableCols,
      tableHandles: tableHandles1,
      pager: { OrderBy: "rensuccess", IsAsc: false },
      dynamicFilter: null,
      ExpressList: [],
      ratelisttotal:0,
      platformList:[],
      shopList:[],
activeName:'',
      detailpager: {
        OrderBy: "chatdate",
        pageSize: 10,
        total: 1,
        pageIndex: 1,
        IsAsc: true,
      },

          pickerOptions: {
          shortcuts: [{
            text: '近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },{
            text: '近十五天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }],
          disabledDate(date) {
          // 设置禁用日期
          const start = new Date('1970/1/1');
          const end = new Date('9999/12/31');
          return date < start || date > end;
          }
        },
        defaultDate: new Date('1970/1/1'),


      detailVisible: false,
      detailType: "成交明细",
      emplist:[],

      personpager: {
        OrderBy: "rensuccess",
        pageSize: 200,
        pageIndex: 1,
        IsAsc: false,
      },
      everyPersonVisible: false,

      persondata: [],
      detaildata: [],

      showpersonrow: {},

      summaridata:{},
      seller:'',




      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      addDialogFormVisible: false,
      editFormVisible: false, // 编辑界面是否显示
      editLoading: false,

      editFormRules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
      },
      userNameReadonly: true,
      // 编辑界面数据
      editForm: {},
      addFormVisible: false, // 新增界面是否显示
      addLoading: false,
      addFormRules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      // 新增界面数据
      addForm: {},
      deleteLoading: false,
      importFilte: { companyid: null, warehouse: null, yearmonth: null },
      expresscompanylist: [],
      dialogVisible: false,
      dialogbatchNumberVisible: false,
      dialogdeletebatchNumberVisible: false,
      dialogCloneVisible: false,

      fileList: [],
      fileparm: {},
        resourcedialogVisible:false,
      cloneparm: {
        oldBatchnumber: "",
        newYearMonth: "",

      },
    };
  },

  async mounted() {

    console.log("checkPermission")
if(checkPermission(['api:customerservice:trainplan:AddTrainDataAsync']))
{
 this.tableCols[this.tableCols.length-1].btnList.push( { label: "添加培训计划", handle: (that, row) => that.addtrainplan(row) });
}
if(checkPermission(['api:customerservice:trainplan:TrainfilesUpload']))
{
 this.tableCols[this.tableCols.length-1].btnList.push( { label: "管理培训资料", handle: (that, row) => that.manageresource(row, 4) });
}


   this.getDate();

 await this.setPlatform();
  await this.onSearch();
 await this.changePlatform();
  },
  methods: {

//下载生意参谋平台导入模板
   downloadTemplate(){
        window.open("../../static/excel/customerservice/商品咨询明细导入模板.xlsx","_self");
    },
manageresource(row){
var that=this;



that.resourcedialogVisible=true;

setTimeout(function(){

that.$refs.trainres.init(row.productID);
},200);


//that.$refs.trainres.init(row.productID);


}
,
 async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
async  changePlatform(val){


this.Filter.p=val;
 const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 1000,
      });
      this.shopList = res1.data.list;
      this.$forceUpdate();

  },
    addtrainplan(row){



var that = this;


      if (this.Filter.timerange) {
        this.personpager.StartDate = this.Filter.timerange[0];
        this.personpager.EndDate = this.Filter.timerange[1];
      } else {
        this.personpager.StartDate = null;
        this.personpager.EndDate = null;
      }

      console.log(row);

      const params = {
        ...this.personpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };
      params.ProductID = row.productID;

      var listtrainee="";

      var maxseller="";
      var maxrate=0;

      getRateDetailList(params).then((res) => {
        that.persondata = res.data.list;
        that.persondata.forEach(function (item, index) {
          var success = item.rensuccess + item.jiqisuccess;
          var fail = item.renfail + item.jiqifail;

          item.rate = success / (fail + success);
          if (item.rate < row.ren) {
           listtrainee+=item.seller+","
          }

          if(item.rate>maxrate)
          {
            maxrate=item.rate;
            maxseller=item.seller;

          }

        });
      var traindata={};
      traindata.productID=row.productID;
      traindata.coach=maxseller;
      traindata.trainees=listtrainee;

 this.$confirm("此操作将发布培训指令, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {



addtrainplan(traindata).then(res=>{

if(res.data==true)
{
   that.$message({
            type: "success",
            message: "已添加成功",
          });

}

});

  })
        .catch(() => {
          //几点取消的提示
        });
      });
    },
getDate() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              this.Filter.timerange=[start, end]
  },

    changecontainjiqi(){


    },
    async startExport() {
      var that = this;
      const para = {};
      if (that.Filter.Enabled == "true") para.enabled = true;
      else if (that.Filter.Enabled == "false") para.enabled = false;
      para.warehouse = that.Filter.Warehouse;

      console.log(that.Filter);
      //debugger
      if (that.Filter.timerange) {
        para.StartDate = that.Filter.timerange[0];
        para.EndDate = that.Filter.timerange[1];
      }

      if (that.Filter.MinPersonRate) {
        para.MinPersonRate = that.Filter.MinPersonRate / 100;
      }
         if(that.Filter.containjiqistatus)
      {
           para.MinAiRate = 0.001;

      }
      if (that.Filter.MinAiRate) {
        para.MinAiRate = that.Filter.MinAiRate / 100;
      }
      if (that.Filter.MaxPersonRate) {
        para.MaxPersonRate = that.Filter.MaxPersonRate / 100;
      }


           if (that.Filter.MaxAiRate) {
        para.MaxAiRate = that.Filter.MaxAiRate / 100;
      }


         if(!that.Filter.containjiqistatus)
      {

          if(para.MinAiRate)
           para.MinAiRate = 0;
           para.MaxAiRate = 0;

      }




      if (that.Filter.ProductID) {
        para.ProductID = that.Filter.ProductID;
      }


      var pager = this.$refs.pager.getPager();
      var filter = that.Filter.filter;

      console.log(pager)

      const params = {
        ...this.pager,
        ...pager,

        ...para,
        //dynamicFilter: this.dynamicFilter
      };

      var res = await exportRateList(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "商品咨询分析_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },



async startExportDetail() {
         var that = this;

      if (this.Filter.timerange) {
        this.detailpager.StartDate = this.Filter.timerange[0];
        this.detailpager.EndDate = this.Filter.timerange[1];
      }

      //this.detailpager.CurrentPage = e;

      const params = {
        ...this.detailpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };
      if(this.seller && this.seller.length>0)
          params.seller=null;

      // getConsultingList(params).then((res) => {
      //   that.detaildata = res.data.list;
      //   that.detailpager.total = res.data.total;
      //   that.detailVisible = true;
      // });

      var res = await exportProductConsultDetailList(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "商品咨询明细_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },


    startImport() {
      this.dialogVisible = true;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    persondatasortchange(column) {
      if (!column.order)
        this.personpager = { OrderBy: column.prop, IsAsc: false };
      else
        this.personpager = {
          OrderBy: column.prop,
          pageSize: 200,
          pageIndex: 1,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.showperson(this.showpersonrow);
    },
    copyinfo(e1) {
      var that = this;

      this.$copyText(e1).then(
        function (e) {
          that.$message({
            type: "success",
            message: "已复制",
          });
        },
        function (e) {
          alert("Can not copy");
          console.log(e);
        }
      );
    },
    changeemp(tab,event){

      console.log(tab)
      console.log(event)
      this.seller=tab.name
      this.changePage(1)


    },
    changePage(e) {
      var that = this;

      if (this.Filter.timerange) {
        this.detailpager.StartDate = this.Filter.timerange[0];
        this.detailpager.EndDate = this.Filter.timerange[1];
      }

      this.detailpager.CurrentPage = e;

      const params = {
        ...this.detailpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };
      if(this.seller && this.seller.length>0)
          params.seller=this.seller;

      getConsultingList(params).then((res) => {
        that.detaildata = res.data.list;
        that.detailpager.total = res.data.total;
        that.detailVisible = true;
      });
    },
    showperson(row) {
      var that = this;
      setTimeout(() => {
        this.$refs.personstatistics.OnSearch(row)
      }, 15);

        that.everyPersonVisible = true;

    },
    showdetail(row, type) {
      var that = this;
      if (type == 1) this.detailType = "成交明细";
      if (type == 2) this.detailType = "未成交明细";

      this.detailpager.ResultType = type;

      if (this.Filter.timerange) {
        this.detailpager.StartDate = this.Filter.timerange[0];
        this.detailpager.EndDate = this.Filter.timerange[1];
      } else {
        this.detailpager.StartDate = null;
        this.detailpager.EndDate = null;
      }

      this.detailpager.ProductID = row.productID;
      this.detailpager.CurrentPage = 1;

      const params = {
        ...this.detailpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };

getEmployeeListByProductList(params).then((res) => {
         that.emplist = res.data.list;
      });


      getConsultingList(params).then((res) => {
        that.detaildata = res.data.list;
        that.detailpager.total = res.data.total;
        that.detailVisible = true;
      });
    },
    formatCreatedTime(row, column, time) {
      return formatTime(time, "YYYY-MM-DD HH:mm");
    },
    formatdate(time) {
      return formatTime(time, "YYYY-MM-DD");
    },
    getRowClass({ row, rowIndex }) {
      return "row-expand-cover";
      // if (row.ruleDetails.length == 0) {
      // return 'row-expand-cover';
      // } else {
      // return '';
      //    }
    },

    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
        //状态码为200时则上传成功
      } else {
        //状态码不是200时上传失败 从列表中删除
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    uploadchange() {
      // this.$refs.upload.clearFiles();
    },
    submitUpload() {
      this.$refs.upload.submit();
      // this.$confirm("确定导入吗?, 是否继续?", "提示", {
      // confirmButtonText: "确定",
      // cancelButtonText: "取消",
      // type: "warning",
      // })
      // .then(() => {
      //   debugger
      //     this.$refs.upload.submit();
      // })
      // .catch(() => {});
    },
    uploadFile(item) {
      var that = this;
      const form = new FormData();
      form.append("token", this.token);

      form.append("upfile", item.file);
      const res = importProductconsulting(form).then((res) => {
        console.log(res);

        that.dialogVisible = false;
        that.fileList = [];

        this.$message({
          message: "上传成功,正在导入中...",
          type: "success",
        });
      });
    },

    // 查询
    async onSearch(dynamicFilter) {
      this.$refs.pager.setPage(1);





      this.dynamicFilter = dynamicFilter;
      this.getExpressList();
    },

    async getExpressList() {
      var that = this;
      const para = {};
      if (that.Filter.Enabled == "true") para.enabled = true;
      else if (that.Filter.Enabled == "false") para.enabled = false;
      para.warehouse = that.Filter.Warehouse;

      console.log(that.Filter);
      //debugger
      if (that.Filter.timerange) {
        para.StartDate = that.Filter.timerange[0];
        para.EndDate = that.Filter.timerange[1];
      }

      if (that.Filter.MinPersonRate) {
        para.MinPersonRate = that.Filter.MinPersonRate / 100;
      }


//用于过滤是否包含机器人，不包含机器人则最低AI rate 为0.1%
      if(that.Filter.containjiqistatus)
      {
           para.MinAiRate = 0.001;

      }
      if (that.Filter.MinAiRate) {
        para.MinAiRate = that.Filter.MinAiRate / 100;
      }
      if (that.Filter.MaxPersonRate) {
        para.MaxPersonRate = that.Filter.MaxPersonRate / 100;
      }

      if (that.Filter.MaxAiRate) {
        para.MaxAiRate = that.Filter.MaxAiRate / 100;
      }



         if (that.Filter.productName) {
        para.productName = that.Filter.productName;
      }
   if (that.Filter.shopid) {
        para.shopid = that.Filter.shopid;
      }


            if(!that.Filter.containjiqistatus)
      {


           if(para.MinAiRate)
           para.MinAiRate = 0;
           para.MaxAiRate = 0;

      }



      if (that.Filter.ProductID) {
        para.ProductID = that.Filter.ProductID;
      }

      var pager = this.$refs.pager.getPager();
      var filter = that.Filter.filter;

      const params = {
        ...this.pager,
        ...pager,

        ...para,
        //dynamicFilter: this.dynamicFilter
      };
      this.listLoading = true;

      if(!params.OrderBy)
      {

        params.OrderBy="rensuccess";
      }


      if(params.MinPersonRate>params.MaxPersonRate)
       {
        this.$message.error('转换率左边不能大于右边');

       }
      getRateList(params).then((res) => {
        that.listLoading = false;
        //if (!res?.success) {
        //return;
        //}
        that.ratelisttotal = res.data.total;
        const data = res.data.list;
        var i = 1;
        data.forEach((d) => {
          d.enabled = true;
          d.id = i;
          i++;
          // d._loading = false;
        });
        that.ExpressList = data;
        that.$forceUpdate();

      });

      // const res = await getRateList(params);
    },
    async startEdit(row) {
      row.edit = true;
    },
    cancelEdit(row) {
      row.edit = false;
      if (row.id === undefined) {
        // 重新加载该页面
      }
    },

    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
