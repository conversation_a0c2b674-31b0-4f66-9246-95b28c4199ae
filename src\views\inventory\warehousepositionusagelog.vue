<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='inquirslist' @select='selectchange' :isSelection='false' :tableCols='tableCols' style="height:90%"
            :isSelectColumnCols="false" :loading="listLoading">
            <template slot='extentbtn'>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getdataList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import { getWarehousePositionUsageLogPageList } from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
    { istrue: true, prop: 'wms_co_id', label: '仓库名称', width: '260', sortable: 'custom', formatter: (row) => row.name },
    { istrue: true, prop: 'modifiedColName', label: '被修改列名称', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'modifiedBeforeValue', label: '修改前值', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'modifiedAfterValue', label: '修改后值', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'modifiedResult', label: '修改结果', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'modifiedUserName', label: '操作人', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'modifiedTime', label: '操作时间', width: '150', sortable: 'custom' }
];
export default {
    name: "warehousepositionusagelog",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            wms_co_id: 0,
            filter: {
            },
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "modifiedTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
        };
    },
    async mounted() {
    },
    methods: {
        async loadData(rowinfo) {
            this.wms_co_id = rowinfo.wms_co_id;
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getdataList();
        },
        async getdataList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
            };
            params.wms_co_id = this.wms_co_id
            console.log(params)
            this.listLoading = true;
            const res = await getWarehousePositionUsageLogPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirslist = res.data.list;
            //this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
