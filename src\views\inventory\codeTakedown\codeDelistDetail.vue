<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="publicCss">
          <inputYunhan ref="goodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="150px"
            placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackGoodsCode" title="商品编码">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple collapse-tags>
          <el-option :key="'待操作'" label="待操作" :value="0" />
          <el-option :key="'操作中'" label="操作中" :value="1" />
          <el-option :key="'删除失败'" label="删除失败" :value="-1" />
          <el-option :key="'删除成功'" label="删除成功" :value="2" />
          <el-option :key="'确认删除'" label="确认删除" :value="3" />
          <el-option :key="'下架处理'" label="下架处理" :value="4" />
        </el-select>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.isNotify" placeholder="是否通知" class="publicCss" clearable>
          <el-option :key="'是'" label="是" :value="true" />
          <el-option :key="'否'" label="否" :value="false" />
        </el-select>
        <div class="publicCss">
          <inputYunhan ref="proCode" :inputt.sync="ListInfo.proCode" v-model="ListInfo.proCode" width="150px"
            placeholder="ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
            @callback="callbackID" title="ID">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="exportNoticeLog">导出通知日志</el-button>
      </div>
    </template>
    <vxetablebase :id="'codeDelistDetail20241029'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" :border="true">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <el-button type="text" v-if="row.status && row.status == 3"
                @click="onDeletionMethod(row)">确认删除</el-button>
              <el-button type="text" @click="onAcquireLogs(row)">日志</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="日志" :visible.sync="logsdialogVisible" width="50%" v-dialogDrag>
      <div style="height: 400px;">
        <detailLog ref="detailLog" v-if="logsdialogVisible" :logsid="logsid" />
      </div>
    </el-dialog>

    <el-dialog title="通知日志" :visible.sync="noticeLogsdialogVisible" width="50%" v-dialogDrag>
      <div style="height: 400px;">
        <noticeLog ref="noticeLog" v-if="noticeLogsdialogVisible" :logsid="logsid" />
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import dayjs from 'dayjs'
import detailLog from "./detailLog.vue";
import noticeLog from "./noticeLog.vue";
import { getGoodsBanDetailPage, exportGoodsBanDetail, confirmBanGoodsCode, exportGoodsBanNoticeData } from "@/api/inventory/basicgoods"
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'syncTime', label: '导入时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'platformStr', label: '平台', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'combineCode', label: '组合装商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'combineEntityCode', label: '其他编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: 'ID', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'statusStr', label: '状态', },
  { width: '300', align: 'left', prop: 'execResult', label: '执行结果' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'isNotify', label: '是否通知', formatter: (row) => row.isNotify ? '是' : '否' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'lastNotifier', label: '最后通知人' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'lastNoticeTime', label: '最后通知时间', type:'click',handle:(that, row)=>that.showNoticeLog(row) },
]
export default {
  name: "codeDelistDetail",
  components: {
    MyContainer, vxetablebase, detailLog, noticeLog, inputYunhan
  },
  data() {
    return {
      timeCheck: false,//时间选择器是否默认时间
      logsid: null,
      logsdialogVisible: false,
      noticeLogsdialogVisible: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        goodsCode: null,//商品编码
        status: [],//状态
        isNotify: null,//是否通知
        proCode: null,//ID
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    callbackGoodsCode(val) {
      this.ListInfo.goodsCode = val;
    },
    callbackID(val) {
      this.ListInfo.proCode = val;
    },
    async onAcquireLogs(row) {
      this.logsid = row.id
      this.logsdialogVisible = true
    },
    async showNoticeLog(row){
      console.log(row,'row');
      this.logsid = row.id;
      this.noticeLogsdialogVisible = true;
    },
    Jumpinit(row) {
      let that = this
      that.ListInfo.startDate = row.startDate
      that.ListInfo.endDate = row.endDate
      that.ListInfo.goodsCode = row.goodsCode
      that.ListInfo.status = null
      that.ListInfo.isNotify = null
      that.ListInfo.proCode = null
      if (row.startDate && row.endDate) {
        that.timeRanges = [row.startDate, row.endDate]
      } else {
        that.timeRanges = []
        that.timeCheck = true
      }
      that.ListInfo.goodsCode = row.goodsCode
      that.getList()
    },
    async onDeletionMethod(row) {
      this.$confirm('是否确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = { proCode: row.proCode, platform: row.platform, platformShopId: row.platformShopId }
        const { success } = await confirmBanGoodsCode(params)
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportGoodsBanDetail(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '编码下架明细数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async exportNoticeLog(){
      this.loading = true;
      const res = await exportGoodsBanNoticeData(this.ListInfo);
      this.loading = false;
      if(res && res.data && res.data.success){
        this.$message.success(res.data.msg);
      }else{
        this.$message.error(res && res.data && res.data.msg?res.msg:'导出失败！');
      } 
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0 && !this.timeCheck) {
        //默认给近7天时间
        this.ListInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await getGoodsBanDetailPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
