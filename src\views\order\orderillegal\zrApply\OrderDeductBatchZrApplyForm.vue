<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right">
                <el-row>
                    <el-col :span="23" :offset="1">
                        <span style="color:red">
                            申诉须知：
                            <br/>
1、申诉基础步骤：发起申诉 --> 提交申诉凭证 --> 审核凭证 --> 判定申诉结果 --> 申诉通过后调整新责任部门，原部门责任及相关事宜剔除
<br/>
2、申诉机会仅一次，请您使用好申诉权益。
<br/>
3、申诉时间为(非扣款时间)责任计算时间起当天17:30至次日10:00，超时导致申诉入口关闭，无法支持再次申诉。
                        </span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-row>
                            <el-col :span="24" style="height:442px">
                                <div style="font-weight: bold;">已选列表：</div>
                                <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist' :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' :loading="sellistLoading">
                                </ces-table>
                            </el-col>
                        </el-row>
                    </el-col>
                    <el-col :span="16">
                        <div style="font-weight: bold; ">&nbsp;&nbsp;&nbsp;&nbsp;申诉信息：</div>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="新责任类型：" prop="newZrType1" :rules="[
                                                        { required: true, message: '请选择新责任类型', trigger: ['blur', 'change'] }
                                                        ]">
                                    <el-select v-if="formEditMode" v-model="form.newZrType1" @change="newZrType1Change">
                                        <el-option v-for="(v,key) in deductOrderZrType12Tree" :key="key" :label="key" :value="key"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrType1 }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="新责任原因：" prop="newZrType2" :rules="[
                                                        { required: true, message: '请选择新责任原因', trigger: ['blur', 'change'] }
                                                        ]">
                                    <el-select v-if="formEditMode" v-model="form.newZrType2" >
                                        <el-option v-for="item in deductOrderZrType12Tree[form.newZrType1]" :key="item.zrType2" :label="item.zrType2"  :value="item.zrType2"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrType2 }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="新责任部门：" prop="newZrDeptAction" :rules="[
                                                        { required: true, message: '请选择新责任部门', trigger: ['blur', 'change'] }
                                                        ]">
                                    <el-select v-if="formEditMode" v-model="form.newZrDeptAction" @change="newZrDeptActionChange">
                                        <el-option v-for="item in zrDeptActions" :value="item.zrDeptAction" :label="item.zrDeptAction"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrDeptAction }}</span>
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="12">
                                <el-form-item label="新责任原因：" prop="newZrReason" :rules="[
                                { required: true, message: '请选择新责任原因', trigger: [ 'change'] }
                                ]">
                                    <el-select  v-if="formEditMode "  v-model="form.newZrReason">
                                        <el-option v-for="item in zrReasons" :value="item" :label="item"></el-option>
                                    </el-select>
                                    <span v-else>{{ form.newZrReason }}</span>
                                </el-form-item>
                            </el-col> -->
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="新责任人：" prop="newMemberName" >
                                    <template  v-if="formEditMode " >
                                        <el-select v-if="form.newZrDept=='采购'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                            <el-option v-for="item in brandlist" :key="item.key" :label="item.value"  :value="item.key"/>
                                        </el-select>
                                        <el-select v-else-if="form.newZrDept=='运营'" v-model="form.newMemberId" filterable @change="newMemberIdChange">
                                            <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key" />
                                        </el-select>
                                        <YhUserelector v-else-if="form.newZrDept!='机器人' && form.newZrDept!='外部'  && form.newZrDept!='快递'"
                                            :value.sync="form.newMemberDDUserId" :text.sync="form.newMemberDDUserName"
                                        ></YhUserelector>
                                        <el-input  v-else  v-model="form.newMemberName" clearable maxlength="10"  />
                                    </template>
                                    <span v-else>{{ form.newMemberName }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="申诉理由：" prop="newZrConditionFullName" :rules="[
                                                        { required: true, message: '请填写新责任部门对应申诉理由', trigger: ['blur'] }
                                                        ]">
                                    <el-input v-if="formEditMode" type="textarea" v-model="form.newZrConditionFullName" clearable maxlength="100" show-word-limit />
                                    <span v-else>{{ form.newZrConditionFullName }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24" style="height:400px;overflow:auto">
                                <el-form-item label="定责资料：" prop="applyContent" :rules="[
                        { required: true, message: '请填写新定责资料', trigger: ['blur'] }
                        ]">
                                    <yh-quill-editor :value.sync="form.applyContent" v-if="formEditMode" style="height:300px;"></yh-quill-editor>
                                    <div v-else v-html="form.applyContent" class="tempdiv"></div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button @click="onClose">关闭</el-button>
                    <el-button type="primary" @click="onSave(true)">批量提交申诉</el-button>
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons ,DeductOrderZrType12,DeductOrderZrType12Tree } from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { BatchDeductZrAppeal } from "@/api/order/orderdeductmoney";
    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import YhUserelector from '@/components/YhCom/yh-userselector.vue'


    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'
    import {
        getDirectorList,
        getDirectorGroupList,
        getProductBrandPageList,
        getList as getshopList,
    } from "@/api/operatemanage/base/shop";

    import store from '@/store'


    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState = (v) => {
        switch (v) {
            case 0: return '下架';
            case 1: return '已查询';
            case -1: return '失败';
        }
        return ' ';
    };

    const fmtApplyState = function (val) {
        if (val == -1) return "已拒绝";
        else if (val == 0) return "待申请";
        else if (val == 1) return "申请中";
        else if (val == 2) return "申请中";
        return val;
    }

    const tableCols = [
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120' },
        { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '84', },
        { istrue: true, prop: 'orderNo', label: '线上订单号', width: '180', },
    ]
    export default {
        name: "OrderDeductBatchZrApplyForm",
        components: { cesTable, MyContainer, MyConfirmButton, YhQuillEditor ,YhUserelector },
        data() {
            return {
                that: this,
                platform: 0,
                sellist: [],
                sellistLoading: false,
                tableCols: tableCols,
                mode: 3,
                zrDeptActions: DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,

                deductOrderZrType12:DeductOrderZrType12,
                deductOrderZrType12Tree:DeductOrderZrType12Tree,
                deductOrderZrType12TreeChild:[],

                brandlist: [],
                directorList: [],

                form: {
                    newZrType1:"",
                    newZrType2:"",
                    newZrDeptAction:null,
                    newZrReason:null,
                    newMemberId:null,
                    newMemberName:"",
                    newMemberDDUserId:"",
                    newMemberDDUserName:""
                },
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式
                dialogHisVisible: false,
                isTx: false,
            };
        },
        async mounted() {
            await this.setBandSelect();
            await this.getDirectorlist();


            this.deductOrderZrType12TreeChild=[];

            for(let key in this.deductOrderZrType12Tree)
            {
                this.deductOrderZrType12TreeChild=this.deductOrderZrType12TreeChild.concat(this.deductOrderZrType12Tree[key]);
            }



            //console.log(store);
        },
        computed: {

        },
        methods: {
            //设置新责任类型原因
            newZrType1Change(){
                this.form.newZrType2="";

            },
            async getDirectorlist () {
                const res1 = await getDirectorList({});
                const res2 = await getDirectorGroupList({});

                this.directorList = res1.data;
                this.directorGroupList = [{ key: "0", value: "未知" }].concat(
                    res2.data || []
                );
            },
            async setBandSelect(){
                var res= await  getAllProBrand();
                if (!res?.success) return;
                this.brandlist = res.data;
            },
            fmtApplyState: fmtApplyState,
            fmtJqrNoticeState: fmtJqrNoticeState,
            newZrDeptActionChange() {
                if(this.form.newZrDeptAction){
                    let opt=this.zrDeptActions.find(x=>x.zrDeptAction==this.form.newZrDeptAction);
                    if(opt){
                        if(this.form.newZrDept!=opt.zrDept){
                            this.form.newMemberName="";
                            this.form.newMemberId=null;
                            this.form.newMemberDDUserId="";
                            this.form.newMemberDDUserName="";
                        }
                        this.form.newZrAction=opt.zrAction;
                        this.form.newZrDept=opt.zrDept;
                    }else{
                        this.form.newMemberName="";
                        this.form.newMemberId=null;
                        this.form.newMemberDDUserId="";
                        this.form.newMemberDDUserName="";
                    }
                }
            },
            newMemberIdChange(){
                let arr=null;
                if(this.form.newZrDept=="采购"){
                    arr=[...this.brandlist];
                }
                else if(this.form.newZrDept=="运营"){
                    arr=[...this.directorList];
                }

                if(arr!=null && arr && this.form.newMemberId){
                    let opt=arr.find(x=>x.key==this.form.newMemberId);
                    if(opt){
                        this.form.newMemberName=opt.value;
                    }
                }
            },
            async loadData({ selRows, platform }) {
                this.platform = platform;
                this.sellist = selRows;
                if(selRows && selRows.length>0)
                {
                    this.form.newZrType1= selRows[0].zrType1;
                    this.form.newZrType2= selRows[0].zrType2;
                }

            },
            onClose() {
                this.$emit('close');
            },
            async onSave(isClose) {
                if (await this.save()) {
                    this.$emit('afterSave');
                    if (isClose)
                        this.$emit('close');
                }
            },
            async save() {

                this.pageLoading = true;
                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                }
                let myOrders = [];
                this.sellist.forEach(f => {
                    myOrders.push({ orderNo: f.orderNo, deductOccurTime: f.occurrenceTime, illegalType: f.illegalType });
                });
                let saveData ={...this.form};
                saveData.newZrReason=saveData.newZrType2;
                saveData.deductPlatform = this.platform;
                saveData.orderList=myOrders;

                let rlt = await BatchDeductZrAppeal(saveData);
                if (rlt && rlt.success) {
                    this.$message.success(rlt.data.rltMsg);
                }
                this.pageLoading = false;
                return (rlt && rlt.success);
            }
        },
    };
</script>
<style lang="scss" scoped>
    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
</style>
