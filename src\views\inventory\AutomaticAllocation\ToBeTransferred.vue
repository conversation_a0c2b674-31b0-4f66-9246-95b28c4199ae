<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent></el-form>
      </template>
      <!--列表-->
      <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" :hasexpand="false" @sortchange="sortchange"
        :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
        :loading="listLoading" :summaryarry='summaryarry'>
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column v-for="col in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>

            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.GoodsCode" maxlength="50" clearable placeholder="商品编码" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;">
            <el-select v-model="Filter.Rwarehouse" clearable  placeholder="待调入仓" >
                <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            </el-button>
            <el-button style="padding: 0;">
                <el-select v-model="Filter.Cwarehouse" clearable placeholder="待调出仓" >
                    <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                </el-button>
            <el-button style="padding: 0;">
                    <el-select v-model="Filter.AllocationState" clearable placeholder="调拨状态" >
                        <el-option key="-1" label="拒绝" value="-1"></el-option>
                        <el-option key="0" label="待调拨" value="0"></el-option>
                        <el-option key="1" label="调拨中" value="1"></el-option>
                        <el-option key="2" label="已调拨" value="2"></el-option>

                    </el-select>
            </el-button>
            <el-button style="padding: 0;">

            <el-date-picker style="width: 260px"
            v-model="Filter.Timerange"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="创建开始日期"
                end-placeholder="创建结束日期"
                :picker-options="pickerOptions"
                :default-value="defaultDate"
           ></el-date-picker>


            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.Remark" maxlength="50" clearable placeholder="备注" style="width:150px;" />
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <!-- <el-button type="primary" @click="onstartImport">导入</el-button>-->
            <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template>
    </my-container>
  </template>
  <script>

  import { getSaleAfterTxList ,exportSaleAfterTxList} from '@/api/bookkeeper/reportday'
  import { importSaleAfterTx } from '@/api/bookkeeper/import'
  import dayjs from "dayjs";
  import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import { formatPlatform, formatLinkProCode, formatTime ,formatwarehouseIdList} from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
  import { warehouseTransferDetails,exportAllocateList } from "@/api/inventory/allocate"
  import {getWarehouses} from "@/api/storehouse/storehouse";

  const startDate = formatTime(dayjs().subtract(6,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");

  export default {
    name: "AfterSaleTx",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
    },
    data() {
      return {
        date: '',
        onExportDialogVisible: false,
        importfilter: {
          version: ''
        },
        that: this,
        Filter: {
           GoodsCode: null,
           Timerange:null,
           StartAllocationDate: startDate,
           EndAllocationDate: endDate,
           Remark:null
        },
        shopList: [],
        styleCode: null,
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: this.gettableCols(),
        total: 0,
        summaryarry: {},
        pager: { OrderBy: "GoodsCode", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        showtable: true,
        Warehouseslist:[],
        pickerOptions: {
          disabledDate(date) {
          // 设置禁用日期
          const start = new Date('1970/1/1');
          const end = new Date('9999/12/31');
          return date < start || date > end;
          }
        },
        defaultDate: new Date('1970/1/1')
      };
    },
    async created() {
        await this.getWarehousesList();
    },
    mounted() {
      this.Filter.Timerange = [startDate,endDate]
  },
    methods: {
      async getWarehousesList() {
      var res3 = await getWarehouses();
      this.Warehouseslist = res3.data?.map(item => { return { value: item.wms_co_id, label: item.name }; });
    },
      gettableCols() {
        return [
        { istrue: true,  prop: 'goodsCode', label: '调拨编码', sortable: 'custom', width: '100' },
        { istrue: true,  prop: 'goodsCodeName',  label: '编码名称', width: '110',sortable: 'custom' },
        { istrue: true,  prop: 'brandId', label: '采购', sortable: 'custom', width: '70' ,formatter:(row)=>row.brandName},
        { istrue: true,  prop: 'createTime', label: '创建时间', sortable: 'custom', width: '120' },
        { istrue: true,  prop: 'completeTime', label: '完成时间', sortable: 'custom', width: '120'},
        { istrue: true,  prop: 'rwarehouseName', label: '调入仓', sortable: 'custom', width: '110'},
        { istrue: true,  prop: 'cwarehouseName', label: '调出仓', sortable: 'custom', width: '110'},
        { istrue: true,  prop: 'qty', label: '调拨数', sortable: 'custom', width: '70' },
        { istrue: true,  prop: 'rwarehouseInventoryDay', label: '入库仓周转天数', sortable: 'custom', width: '120',},
        { istrue: true,  prop: 'rwarehouseSafeDayDown', label: '入库仓安全天数', sortable: 'custom', width: '120', },
        { istrue: true,  prop: 'rwarehouseWarehouseStock', label: '入库仓库存数', sortable: 'custom', width: '110', },
        { istrue: true,  prop: 'rwarehouseSafeStockDown', label: '入库仓安全库存数', sortable: 'custom', width: '130', },
        { istrue: true,  prop: 'cwarehouseSafeStockDown', label: '出库仓安全库存数', sortable: 'custom', width: '130', },
        { istrue: true,  prop: 'cwarehouseInventoryDay', label: '出库仓周转天数', sortable: 'custom', width: '120', },
        { istrue: true,  prop: 'cwarehouseSafeDayDown', label: '出库仓安全天数', sortable: 'custom', width: '120', },
        { istrue: true,  prop: 'cwarehouseWarehouseStock', label: '出库仓库存数', sortable: 'custom', width: '110', },
        { istrue: true,  prop: 'allocationState', label: '调拨状态', sortable: 'custom', width: '90' ,formatter:(row)=>row.allocationState=="-1"?"拒绝":row.allocationState=="0"?"待调拨":row.allocationState=="1"?"申请中":row.allocationState=="2"?"已调拨":row.allocationState=="3"?"调拨成功":row.allocationState=="4"?"调拨部分成功":row.allocationState=="5"?"确认调拨失败":row.allocationState=="6"?"创建调拨失败":row.allocationState=="7"?"撤销":""},
        { istrue: true,  prop: 'remark', label: '备注', sortable: 'custom', width: '120', },
       ]
      },
      showClo(){
        return this.Filter.startTime==this.Filter.endTime;
      },
      changeSelectType() {
        this.getList();
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
         if (this.Filter.Timerange && this.Filter.Timerange.length > 0) {
                this.Filter.StartAllocationDate = this.Filter.Timerange[0];
                this.Filter.EndAllocationDate = this.Filter.Timerange[1];
           }else
           {
                this.Filter.StartAllocationDate=null;
                this.Filter.EndAllocationDate=null
           }
        const para = { ...this.Filter };
        let pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await warehouseTransferDetails(params);
        if (!res.success) {
          this.listLoading = false;
          return;
        }
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry = res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
      async onExport()
      {
        if (this.Filter.Timerange && this.Filter.Timerange.length > 0) {
                this.Filter.StartAllocationDate = this.Filter.Timerange[0];
                this.Filter.EndAllocationDate = this.Filter.Timerange[1];
           }else
           {
                this.Filter.StartAllocationDate=null;
                this.Filter.EndAllocationDate=null
           }
          var res = null;
          const para = { ...this.Filter };
          let pager = this.$refs.pager.getPager();
          const params = {
            ...pager,
            ...this.pager,
            ...para,
          };
          this.pageLoading = true;
          var res = await exportAllocateList(params);
          var fileName="自动调拨_调拨记录_"
          this.pageLoading = false;
          const aLink = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
          aLink.click()
      },
    },
  };


  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>

