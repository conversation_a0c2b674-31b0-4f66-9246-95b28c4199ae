<template>
    <my-container>
      <el-tabs v-model="activeName" style="height:95%;">
        <el-tab-pane label="模型配置" name="first4" style="height: 100%;" lazy>
          <amoxingpeizhi ref="amoxingpeizhi" />
        </el-tab-pane>
        <el-tab-pane label="数据库配置" name="first5" style="height: 100%;" lazy>
          <bshujukupeizhi ref="bshujukupeizhi" />
        </el-tab-pane>
        <el-tab-pane label="表字段配置" name="first6" style="height: 100%;" lazy>
          <cbiaoziduanpeizhi ref="cbiaoziduanpeizhi" />
        </el-tab-pane>
        <el-tab-pane label="工具配置" name="first7" style="height: 100%;" lazy>
          <dgongjupeizhi ref="dgongjupeizhi" />
        </el-tab-pane>
        <el-tab-pane label="关键字配置" name="first8" style="height: 100%;" lazy>
          <fgjzpeizhi ref="fgjzpeizhi" />
        </el-tab-pane>
        
      </el-tabs>
    </my-container>
  </template>
  <script>
    import MyContainer from "@/components/my-container";
    
    import amoxingpeizhi from './amoxingpeizhi/index.vue'
    import bshujukupeizhi from './bshujukupeizhi/index.vue'
    import cbiaoziduanpeizhi from './cbiaoziduanpeizhi/index.vue'
    import dgongjupeizhi from './dgongjupeizhi/index.vue'
    import fgjzpeizhi from './fgjzpeizhi/index.vue'

  export default {
    name: "officeDashboardIndex",
    components: {
        MyContainer, amoxingpeizhi, bshujukupeizhi,
        cbiaoziduanpeizhi, dgongjupeizhi,fgjzpeizhi
    },
    data() {
      return {
        activeName: 'first4',
      };
    },
    methods: {
  
    },
  };
  </script>
  <style lang="scss" scoped></style>
  