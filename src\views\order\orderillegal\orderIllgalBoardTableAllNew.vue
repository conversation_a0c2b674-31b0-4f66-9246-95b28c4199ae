<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <div style="text-align: center;" >
                <p>
                    <el-radio-group v-model="filterInner.zrType1" @change="zrType1Chg">
                        <el-radio key="" label="">所有类型       
                        </el-radio>
                        <el-radio key="缺货" label="缺货"  value="缺货">缺货</el-radio>
                        <el-radio key="虚假轨迹" label="虚假轨迹"  value="虚假轨迹">虚假轨迹</el-radio>
                        <el-radio key="延迟发货" label="延迟发货"  value="延迟发货">延迟发货</el-radio>

<!-- 
                        <el-option v-for="(v,key) in deductOrderZrType12Tree" :key="key" :label="key"
                :value="key"></el-option> -->
                    </el-radio-group>
                </p>
                <p>
                    <el-radio-group v-model="filterInner.zrType2" @change="onSearch()">
                        <el-radio key="" label="">所有原因       
                        </el-radio>
                        <el-radio
                                v-for="item in deductOrderZrType12Tree[filterInner.zrType1]"
                                :key="item.zrType2"
                                :label="item.zrType2">{{item.zrType2}}             
                        </el-radio>
                    </el-radio-group>
                </p>
            </div>
            <el-select filterable  v-model="filterInner.platforms" placeholder="全部平台"  clearable multiple  collapse-tags
              style="width: 180px">
              <!-- <el-option label="全部平台" value =""></el-option> -->
              <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select filterable  style="width: 180px" v-model="filterInner.expressTags"  clearable  placeholder="物流标记"  multiple  collapse-tags>
              <el-option  key="一条" label="一条"  value="一条"></el-option>
              <el-option  key="多条" label="多条"  value="多条"></el-option>
              <el-option  key="签收" label="签收"  value="签收"></el-option>
              <el-option  key="断点" label="断点"  value="断点"></el-option>
            </el-select>
            <el-select v-model="filterInner.wmsCoId"  clearable placeholder="请选择发货仓" style="width: 250px" key="filter.wmsCoId">
              <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
            </el-select>
            <el-input  v-model="filterInner.expressCompanyName" clearable  placeholder="快递公司" style="width: 120px" maxLength="20"></el-input>
            <el-button type="primary" @click="onSearch()">搜索</el-button>
        </template>
        <template>
       
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' style="height:94%" :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="true" @select="selectchange" :isSelectColumn='true' :tableHandles='tableHandles' @cellclick="cellclick" @summaryClick='onsummaryClick' :loading="listLoading">
                <template slot="extentbtn">
                    <span style="color:red">申诉开放时间为(非扣款时间)责任计算时间起当天17:30至次日10:00，其他时间或者过期均不再开放</span>
                </template>
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :visible.sync="showDetailVisible.visible" width="72%" append-to-body>
            <span>
                <template>
                    <el-date-picker v-model="filter.timerange" type="datetimerange" :picker-options="pickerOptions1" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                    <el-button type="primary" @click="onSearch1()">查询</el-button>
                </template>
            </span>
            <span>
                <buschar v-if="showDetailVisible.visible" ref="buschar" :analysisData="showDetailVisible.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showDetailVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" append-to-body v-dialogDrag >
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="isTx" style="z-index:10000;height:600px" />
        </el-dialog>
        <el-dialog title="内部订单日志信息" v-if="dialogHisVisible1" :visible.sync="dialogHisVisible1" width="70%" height="600px" append-to-body v-dialogDrag >
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos1" :orderNoInner="orderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog :title="OrderIllegaldetailForYunYingTitle" v-if="OrderIllegaldetailForYunYingVisible" :visible.sync="OrderIllegaldetailForYunYingVisible" width="65%" height="600px" v-dialogDrag append-to-body>
            <OrderIllegaldetailForYunYing ref="OrderIllegaldetailForYunYing" style="z-index:10000;height:650px" :yyId="yyId" />
        </el-dialog>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime, } from "@/utils";
    import buschar from "@/components/Bus/buschar";
    import { formatLinkProCode, formatSendWarehouse, formatPlatform, formatExpressCompany } from "@/utils/tools";
    import {
        GetOrderDeductAllViewListAsync,
        getWithholdSumTable as getOrderWithholdList, getOrderWithholdListChart,
        getOrderWithholdTbaleTXList,
        exportOrderWithhold, importPinOrderIllegal, exportOrderWithholdTbalePddList, exportOrderWithholdTbaleTXList,ExportOrderDeductAllViewList
    } from "@/api/order/orderdeductmoney";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import * as echarts from "echarts";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    import OrderIllegaldetailForYunYing from "@/views/order/orderillegal/OrderIllegaldetailForYunYing.vue";
    import {getExpressComanyAll,getExpressComanyStationName
    } from "@/api/express/express";

    import { warehouselist,platformlist ,DeductOrderZrType12Tree} from "@/utils/tools";
    import {getAllWarehouse} from '@/api/inventory/warehouse'

    const tableCols = [
        //产品ID、内部订单号、线上订单号、扣款金额、扣款日期，付款日期、发货日期、扣款原因、是否预售、责任、发货仓、物流公司、平台、店铺、运营组、采购组、
        { istrue: true, prop: 'proCode', label: '宝贝ID', width: '110', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
        { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '84', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail1(row) },
        { istrue: true, prop: 'orderNo', label: '线上订单号', width: '180', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
        { istrue: true, prop: 'amountPaid', label: '扣款金额', width: '54', sortable: 'custom', summaryEvent: false, },
        { istrue: true, prop: 'occurrenceTime', label: '扣款日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.occurrenceTime, "YYYY-MM-DD") },
        { istrue: true, prop: 'payTime', label: '付款日期', width: '124', sortable: 'custom', formatter: (row) => !row.payTime ? " " : formatTime(row.payTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'sendTime', label: '发货日期', width: '124', sortable: 'custom', formatter: (row) => !row.sendTime ? " " : formatTime(row.sendTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'planDeliveryDate', label: '预计发货时间', width: '124', sortable: 'custom', formatter: (row) => !row.planDeliveryDate ? " " : formatTime(row.planDeliveryDate, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'deliveryTotalMinsTxt', label: '发货时长', width: '90', sortable: 'custom'  },
        { istrue: true, prop: 'illegalType', label: '平台原因', width: '70', sortable: 'custom', formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName },
        { istrue: true, prop: 'zrType1', label: '扣款类型', width: '80', sortable: 'custom'},
        { istrue: true, prop: 'zrType2', label: '扣款原因', width: '120', sortable: 'custom'},

        { istrue: true, prop: 'ysLx', label: '是否预售', width: '60', sortable: 'custom' },
        { istrue: true, prop: 'zrDeptAction', label: '责任部门', width: '64', sortable: 'custom' },
        { istrue: true, prop: 'zrSetTime', label: '责任计算时间', width: '130', sortable: 'custom', formatter: (row) => !row.zrSetTime ? " " : formatTime(row.zrSetTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'sendWarehouseName', label: '发货仓', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'expressNo', label: '快递单号', width: '125', sortable: 'custom', type: 'click', handle: (that, row) => that.onShowLogistics(row) },
        { istrue: true, prop: 'expressCompanyName', label: '快递公司', width: '100', sortable: 'custom' },
          
        { istrue: true, prop: 'expressCollectionMins', label: '揽收时长', width: '90', sortable: 'custom', formatter: (row) => row.expressCollectionTxt   },
        { istrue: true, prop: 'expressTags', label: '物流标记', width: '90', sortable: 'custom'  },

        { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
        { istrue: true, prop: 'shopName', label: '店铺', minwidth: '120', sortable: 'custom', },
        { istrue: true, prop: 'groupName', label: '运营组', width: '62', sortable: 'custom' },
        { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', formatter: (row) => !row.operateSpecialUserName ? " " : row.operateSpecialUserName, type: 'click', handle: (that, row) => that.showYunYing(row) },
        { istrue: true, prop: 'brandName', label: '采购组', width: '62', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '商品名称', width: '180', sortable: 'custom' },
        { istrue: true, prop: 'zrConditionFullName', label: '划责规则', width: '180', sortable: 'custom', formatter: (row) => (row.zrConditionFullName ?? "") },
        { istrue: true, prop: 'memberName', label: '责任人', width: '70', sortable: 'custom' },
        { istrue: true, prop: 'memberDeptFullName', label: '钉钉组织', width: '160', sortable: 'custom' },        
        { istrue: true, prop: 'zrAppealState', label: '申诉状态', width: '80', formatter: (row) => !row.zrAppealStateText ? " " : row.zrAppealStateText },
        //fixed: 'right',
        {
            istrue: true, label: '功能', width: '80',fixed:'right', type: 'button', btnList: [
                {
                    label: '申诉',
                    handle: (that, row) => that.zrApply(row),
                    ishide: (that, row) => { return !row.showZrAppealBtn ; }
                },
                {
                    label:'指派',
                    permission:'api:order:orderdeductmoney:SetZrMemberCustomize',
                    handle:(that,row)=>that.onSetZrMember(row),
                    ishide: (that, row) => { return  row.zrAppealState ==1  ; }
                },    
            ]
        }
    ]

    const tableHandles = [
        { label: "导出", handle: (that) => that.onExport() },
        { label: "批量申诉", handle: (that) => that.batchZrApply(),
        ishide: (that) => { return  !(that.filter.platform && that.filter.platform>0) ; }
     },
     { label: "批量指派", handle: (that) => that.onSetZrMemberBatch(), 
     permission:'api:order:orderdeductmoney:SetZrMemberCustomize',
     ishide: (that) => { return  !(that.filter.platform && that.filter.platform>0) ; }
     },
    ];



    export default {
        name: 'YunhanAdminOrderillegaldetail',
        components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase, buschar, OrderActionsByInnerNos, OrderIllegaldetailForYunYing },
        props: {
            filter: {

            }
        },
        data() {
            return {
                deductOrderZrType12Tree:DeductOrderZrType12Tree,
                deductOrderZrType12TreeChild:[],
                that: this,
                dialogHisVisible1: false,
                orderNoInner: '',
                orderNo: '',
                isTx: false,
                dialogHisVisible: false,
                list: [],
                platformlist:platformlist,
                chatType: '',
                chatVal: '',
                illegalTypeList: [],
                summaryarry: {},
                pager: { OrderBy: "OccurrenceTime", IsAsc: false },
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
                pickerOptions1: {
                    shortcuts: [{
                        text: '近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近十五天',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                tableCols: tableCols,
                tableHandles: tableHandles,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                dialogVisible: false,
                uploadLoading: false,
                uploadLoading: false,
                showDetailVisible: { visible: false, title: "", data: [] },
                selids: [],
                selrows: [],
                yyId: 0,
                OrderIllegaldetailForYunYingTitle: "",
                OrderIllegaldetailForYunYingVisible: false,
                typeList:[],
                expresscompanylist: [],
                warehouselist: warehouselist,
                newWareHouseList:[],   
                showradio:false,
                platformList:[],
                filterInner:{                    
                    zrType1:"",
                    zrType2:"",
                    wmsCoId:null,
                    platforms:[],
                    expressCompanyName:"",
                }
            };
        },

        async mounted() {
            //await this.setPlatform()
            //await this.onSearch()
            await this.setForm();           
        },

        methods: {
            async zrType1Chg(){
                this.filterInner.zrType2="";
                this.onSearch();
            },
            //切换平台
            async onPlatformRadioChange() {
                this.onSearch();
            },
            //导出
            async onExport() {
                let para = { ...this.filter,...this.filterInner };
                let res =await ExportOrderDeductAllViewList(para);

                return;
                    

                if (!res?.data) {
                    this.$message({ message: "没有数据", type: "warning" });
                    return
                }

                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '扣款详情看板列表_' + new Date().toLocaleString() + '_.xlsx')
                aLink.click()
            },
            //申诉
            async zrApply(row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyForm.vue`,
                    title: '责任申诉',
                    autoTitle: false,
                    args: { id: 0, orderNo: row.orderNo, occTime: row.occurrenceTime, platform: (row.platform == 2 ? 2 : (row.platform == 6 ? 6 : 1)), mode: 1 },
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
            },
            //批量申诉
            async batchZrApply() {
                let self = this;
                if (!self.selrows || self.selrows.length <= 0) {
                    this.$message({ message: "请勾选至少一行数据", type: "warning" });
                    return;
                }

                if(!(this.filter.platform && this.filter.platform>0)){
                    this.$message({ message: "批量申诉功能需要选择具体平台", type: "warning" });
                    return;
                }


                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductBatchZrApplyForm.vue`,
                    title: '批量责任申诉',
                    autoTitle: false,
                    args: { selRows: self.selrows, platform: (this.filter.platform == 2 ? 2 : (this.filter.platform == 6 ? 6 : 1)) },
                    height: 600,
                    width: '80%',
                    callOk: self.onSearch
                })
            },
            //指派 
            async onSetZrMember(row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrSetMemberForm.vue`,
                    title: '责任指派',
                    autoTitle: false,
                    args: { ...row,  mode: 1, platform: (row.platform == 2 ? 2 : (row.platform == 6 ? 6 : 1))  },
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
            },
            //指派 
            async onSetZrMemberBatch() {
                let self=this;      
                if(!self.selrows||self.selrows.length<=0)
                {
                    this.$message({ message: "请勾选至少一行数据", type: "warning" });
                    return;
                }
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrSetMemberForm.vue`,
                    title: '责任指派',
                    autoTitle: false,
                    args: { ...self.selrows[0], platform: (self.selrows[0].platform == 2 ? 2 : (self.selrows[0].platform == 6 ? 6 : 1)), mode: 1 ,orderList:[...self.selrows]},
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
            },
            showLogDetail1(row) {
                this.dialogHisVisible1 = true;
                this.orderNoInner = row.orderNoInner;
            },
            showLogDetail(row) {                
                this.orderNo = row.orderNo;
                if (this.filter.platform == 2 || this.filter.platform == 6) {
                    this.isTx = false;
                }
                else {
                    this.isTx = true;
                }
                this.dialogHisVisible = true;
            },
            onShowLogistics(row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                    title: '物流明细',
                    args: { expressNos: row.expressNo },
                    height: 300,
                });
            },
            //查询第一页
            async onSearch() {
                //if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
               
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            async onSearch2() {
                this.resetForm();
              //  if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
               
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startPayDate = null
                this.filter.endPayDate = null
                this.filter.startSendDate = null
                this.filter.endSendDate = null
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择日期", type: "warning" });
                    return false;
                }

                if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                    this.filter.startPayDate = this.filter.timerange2[0];
                    this.filter.endPayDate = this.filter.timerange2[1];
                }

                if (this.filter.timerange3 && this.filter.timerange3.length > 1) {
                    this.filter.startSendDate = this.filter.timerange3[0];
                    this.filter.endSendDate = this.filter.timerange3[1];
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter,
                    ...this.filterInner,
                }

              //  params.platforms = this.Platforms;

                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                console.log("部门看板最终参数", params);
                if (params.dutyDept == "未知") {
                    params.dutyDept = ""
                }
                this.selids=[];
                this.selrows=[];
                this.listLoading = true
                let res =  res = await GetOrderDeductAllViewListAsync(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                if (this.summaryarry)
                    this.summaryarry.amountPaid_sum = parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                console.log(rows)
                this.selrows = rows;

                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            cellclick(row, column, cell, event) {

            },
            async onSearch1() {
                this.showchart();
                //this.filter1.timerange1=[];

            },
            // 趋势图
            async showchart() {
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                } else {
                    this.$message({ message: "请先选择日期", type: "warning" });
                    return false;
                }


                var paras = { ...this.filter };

                let that = this;
                this.$nextTick(async () => {
                    const res = await getOrderWithholdListChart(paras).then((res) => {
                        that.showDetailVisible.visible = true;
                        that.showDetailVisible.data = res.data;
                        that.showDetailVisible.title = res.data.legend[0];
                    });
                    await this.$refs.buschar.initcharts()
                });

            },
            //汇总趋势图
            async onsummaryClick(property) {
                //console.log("我是汇总趋势图传递的参数呀",property)
                if (property && property == "amountPaid") {
                    await this.showchart();
                }
            },
            async showYunYing(row) {
                this.yyId = row.operateSpecialUserId;
                this.OrderIllegaldetailForYunYingTitle = row.operateSpecialUserName + "-历史责任扣款";
                this.OrderIllegaldetailForYunYingVisible = true;
            },
            async getExpressComanyList () {
                const res = await getExpressComanyAll({});
                if (!res?.success) {
                    return;
                }
                const data = res.data;
                this.expresscompanylist = data;
            },
        //获取状态信息
        async getprosimstatelist (val) {
                var id;
                if (val == 1)
                    id = this.importFilte.companyid
                else if (val == 2) {
                    id = this.filter.companyId
                    this.filter.prosimstate = null
                }

                var res = await getExpressComanyStationName({ id: id });
                if (res?.code) {
                    // this.prosimstatelist = res.data.map(function (item) {
                    // var ob = new Object();
                    // ob.state = item;
                    // ob.isshow = false;
                    // ob.selectedicon = "";
                    // ob.selecttype = "fail";
                    // return ob;
                    // })
                    this.prosimstatelist = res.data
                }
            },
           async setForm(){
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;

               // await this.getExpressComanyList();

                var res = await getAllWarehouse();
                this.newWareHouseList = res.data.filter((x) => { return x.name.indexOf('代发') < 0; });

                if(this.filter.platform)
                {
                    console.log(this.filter.platform)
                    this.filterInner.platforms= [this.filter.platform];
                }
                else{
                    this.filterInner.platforms= [];
                }

            },
            // 重置表单
        resetForm() {
            this.platforms = [];
            this.filterInner.zrType1=this.filter.zrType1;
            this.filterInner.zrType2=this.filter.zrType2;
            this.filterInner.wmsCoId = null;
            this.filterInner.expressTags = [];
            this.filterInner.expressCompanyName = "";
            //this.setForm();
        },
    }
    };
</script>

<style lang="scss" scoped></style>
