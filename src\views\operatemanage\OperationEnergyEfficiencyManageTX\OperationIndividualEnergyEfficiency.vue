<template>
    <my-container v-loading="pageLoading">




         <el-row>
            <el-col :span="24"><div class="div1">
              <div id="app" style="height: 100px; width: 100%; display: flex; flex-direction: row;">

                <div class="right-menu-item" style="margin-left: 150px;margin-top: 5px;">
                  <img :src="avatar" style="width: 80px; height: 80px; border-radius: 40px;" alt="">
                </div>
                <div style="display: flex; flex-direction: column; margin-left: 10px;">
                  <div style="font-size:35px;">{{userName}}</div>
                  <div style="font-size:15px;margin-top: 5px;">{{pddcontributeinfolist.groupName}}</div>
                </div>
                <div style="display: flex; flex-direction: column; margin-left: 150px;">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.yesterdaySaleAmont" :duration="2000" />
                    <!-- {{pddcontributeinfolist.yesterdaySaleAmont}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">昨日销售额</div>
                </div>
                <div style="display: flex; flex-direction: column; margin-left: 150px;">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.yesterdayProfit3" :duration="2000" />
                    <!-- {{pddcontributeinfolist.yesterdayProfit3}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">昨日毛三</div>
                </div>

                <div style="display: flex; flex-direction: column; margin-left: 150px; color: blue;" @click="JumpProductManage">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.proCodeCount" :duration="2000" />
                    <!-- {{pddcontributeinfolist.proCodeCount}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">上架ID数</div>
                </div>

                <div style="display: flex; flex-direction: column; margin-left: 150px; color: blue;" @click="JumpGoodsCodeDetail">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.goodsCodeCount" :duration="2000" />
                    <!-- {{pddcontributeinfolist.goodsCodeCount}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">建立编码数</div>
                </div>

                <!-- <div style="display: flex; flex-direction: column; margin-left: 150px;">
                  <div style="font-size:35px">1218.02</div>
                  <div style="font-size:15px;margin-top: 5px;">加班时长</div>
                </div> -->

              </div>

            </div></el-col>
            
          </el-row>
          <el-row>
            <el-col :span="24"><div class="div2">
                <div class="div2-1">
                    <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-button>
                    <el-button style="padding: 0;margin-left: 10px;" >
                        <el-select filterable v-model="filter.Platform" collapse-tags clearable placeholder="平台"
                          style="width: 90px"> 
                          <el-option label="淘系" :value="1" />
                          <el-option label="淘工厂" :value="8" />
                        </el-select>
                      </el-button>
                    <el-button style="padding: 0;margin-left: 10px;" v-if="checkPermission('NXOperationGroup')">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                      style="width: 90px">
                      <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                      <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0; margin-left: 10px;" v-if="checkPermission('NXOperationoperateSpecialUserId')">
                    <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员"
                      style="width: 90px">
                      <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button style="padding: 0;  margin-left: 10px;" v-if="checkPermission('NXOperationuserId')">
                    <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
                      style="width: 90px">
                      <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-button>
                  <el-button class="button" style="padding: 0;  margin-left: 10px;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                  </el-button>
                 
                </el-button-group>
                </div>
                <div class="div2-3">
                    <el-button style=" margin-left: 800px;" type="primary" @click="showchart(1)">日</el-button>
                    <el-button  type="warning" @click="showchart(2)">周</el-button>
                    <el-button type="success" @click="showchart(3)">月</el-button>
                    <el-button type="danger" @click="showchart(4)">季</el-button>
                </div>
                <div class="div2-2">
                  <buschar  ref="buschar" :analysisData="charData"></buschar>
                  
                </div>
            </div></el-col>
            
          </el-row>
          <el-row>
            <el-col :span="24"><div class="div3">
              <div style="height: 50px; width: 100%; display: flex; flex-direction: row;">
                <div style="display: flex; flex-direction: column; margin-left: 425px;">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.planSumSaleAmont" :duration="2000" />
                    <!-- {{pddcontributeinfolist.planSumSaleAmont}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">计划销售额</div>
                </div>
                <div style="display: flex; flex-direction: column; margin-left: 150px;">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.sumSaleAmont" :duration="2000" />
                    <!-- {{pddcontributeinfolist.sumSaleAmont}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">合计销售额&nbsp;<span style="font-size:15px;margin-left: 5px; color: red;">完成:
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.planSumSaleAmontRate" :duration="2000" />
                    <!-- {{pddcontributeinfolist.planSumSaleAmontRate}} -->
                    %</span></div>
                </div>

                <div style="display: flex; flex-direction: column; margin-left: 150px;">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.planSumProfit3" :duration="2000" />
                    <!-- {{pddcontributeinfolist.planSumProfit3}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">计划毛三</div>
                </div>

                <div style="display: flex; flex-direction: column; margin-left: 150px;">
                  <div style="font-size:35px">
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.sumProfit3" :duration="2000" />
                    <!-- {{pddcontributeinfolist.sumProfit3}} -->
                  </div>
                  <div style="font-size:15px;margin-top: 5px;">合计毛三&nbsp;<span style="font-size:15px;margin-left: 5px; color: red;">完成:
                    <count-to  :start-val="0" :end-val="pddcontributeinfolist.planSumProfit3Rate" :duration="2000" />
                    <!-- {{pddcontributeinfolist.planSumProfit3Rate}} -->
                    %</span></div>
                </div>
              </div>
            </div></el-col>
            
          </el-row>
          <el-dialog title="编码详情" :visible.sync="dialogGetGoodsCodeDetailVisible" height="300px" v-dialogDrag>
            <el-row>
            
            </el-row>
             <el-row>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="24">
                 
                   <GoodsCodeDetail ref="GoodsCodeDetail" :filter="GoodsCodeDetail.filter" style="height:600px;"></GoodsCodeDetail>
                </el-col>
             
            </el-row>
           
        </el-dialog>
    </my-container>
   
  </template>
  <script>
  import countTo from 'vue-count-to'
  import dayjs from "dayjs";
  import { mapGetters } from 'vuex'
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import { getEnergyEfficiencyYesterdayTX ,getEnergyEfficiencyTX_Chart} from '@/api/bookkeeper/pddstaticsreport'
  import * as echarts from 'echarts'
  import buschar from '@/components/Bus/buscharOpeation'
  import MyContainer from "@/components/my-container/nofooter.vue";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import {getDirectorGroupList,getDirectorList } from '@/api/operatemanage/base/shop'
  import GoodsCodeDetail from "./GoodsCodeDetail.vue";

  
  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      buschar
      ,countTo,
      GoodsCodeDetail
    },
    data() {
      return {
        GoodsCodeDetail:{
         filter:{}

        },
        grouplist: [],
        dialogGetGoodsCodeDetailVisible: false,
        pageLoading: false,
        charData:[],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        directorlist: [],
        avatarDefault: require('@/assets/images/avatar.png'),
        
        pddcontributeinfolist: [],
        filter: {
        startTime: null,
        endTime: null,
        Platform:null,
        timerange: null,
        // 运营助理id
        userId: null,
        //组id
        groupId: null,
        // 运营专员 ID
        operateSpecialUserId: null,
       },
       filter1:{
            DateType:null,
            startTime: null,
            endTime: null,
            Platform:null,
            timerange: null,
            // 运营助理id
            userId: null,
            //组id
            groupId: null,
            // 运营专员 ID
            operateSpecialUserId: null,
          },
          pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近半个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                    picker.$emit('pick', [start, end]);
                    }
                },{
                    text: '近一个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                    }
                }]
            },
      };
    },
    async mounted() {
      
    },
    async created() {
     await this.init()
     await this.showchart();
     await this.getGroupList();
    // await this.getShopList();
    
  },
  computed:{
    ...mapGetters([
      'menus',
      'userName',
      'avatar'
    ]),

  
  

  },
 
    methods: {
      async JumpGoodsCodeDetail(){
        this.$router.push({path: '/operatemanage/OperationEnergyEfficiencyManageTX/GoodsCodeDetail', query: {Platform: this.filter.Platform}})
        

      },
     async JumpProductManage(){

      this.$router.push({path: '/operatemanage/base/product', query: {Platform: this.filter.Platform,groupId:this.filter.groupId,operateSpecialUserId:this.filter.operateSpecialUserId,userId:this.filter.userId}})
      

     },
      // startplan(){
      //   window.open("http://localhost:8002/operatemanage/OperationEnergyEfficiencyManageTX/OperationPlan");
      // },
      async getGroupList() {
      
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

     
    },
      async showchart(datatype){
      this.filter1.DateType=datatype;
      this.filter1.startTime = null;
      this.filter1.endTime = null;
      if (this.filter.timerange) {
        this.filter1.startTime = this.filter.timerange[0];
        this.filter1.endTime = this.filter.timerange[1];
      }
      this.filter1.Platform=this.filter.Platform;
      this.filter1.groupId=this.filter.groupId;
      this.filter1.operateSpecialUserId=this.filter.operateSpecialUserId;
      this.filter1.userId=this.filter.userId;
         var params = {
          ... this.filter1
        };
         let that = this;
         
         
          const res =  getEnergyEfficiencyTX_Chart(params).then(res=>{                    
             
             that.charData=res.data
            //  res.data.series.map((item)=>{
            //   item.barWidth = '50px'
            //  })
                this.$nextTick(() => {
                  this.$refs.buschar.initcharts()
                });
            });
       
         
           

       
         
   
      },
      onSearch() {

        this.getEnergyEfficiencyYesterdayTX();
        this.showchart();

      },
      async getEnergyEfficiencyYesterdayTX() {
        this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      const para = { ...this.filter };
        const params = {
          ...para,
        };
        this.listLoading = true;
        const res = await getEnergyEfficiencyYesterdayTX(params);
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data;
      },


    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 60);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
    },
     
    }
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }

  .div1 {
    margin-top: 0px;
    background: #d7dfeb;
    height: 100px;
  }
  .div2 {
    background: #e0e5f0;
    height: 63vh;
  }
  .div3 {
    margin-top: 0px;
    background: #d7dfeb;
    height: 80px;
  }
  .div2-1{
    margin-top: 0px;
    background: #bed1ec;
    height: 50px;
  }
  .div2-2{
    margin-top: 0px;
    /* background: #9ba6b4; */
    /* height: 460px;
    width: 98%; */
    width: 87vw;
    height: 55vh;
  }
  .div2-3{
    margin-top: 0px;
    background: #98bef1;
    height: 40px;
  }
  .span1{
   margin-top: 0px;
   margin-left: 100px;
   width: 80px;
   color: red;
  }
  .span2{
   margin-top: 0px;
   margin-left: 100px;
   color: red;
  }
  .span3{
   margin-top: 0px;
   margin-left: 100px;
   color: red;
  }
  .span4{
   margin-top: 0px;
   margin-left: 270px;
   width: 80px;
   color: red;
  }
  .span5{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }
  .span6{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }
  .span7{
   margin-top: 0px;
   margin-left: 200px;
   color: red;
  }


  
  </style>
  