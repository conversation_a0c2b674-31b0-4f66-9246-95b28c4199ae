<template>
  <div style="height: 100%;">
     <template>
      <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
         <el-row>
              <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                 <el-form-item label="左边轴显示:">
                    <el-select v-model="filter1.leftY" placeholder="请选择" class="el-select-content">
                        <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
              </el-col> -->
              <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="右边轴显示:">
                  <el-select v-model="filter1.rightY" placeholder="请选择" class="el-select-content">
                      <el-option v-for="item in Ylist" :key="item.value" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
              </el-col> -->
           </el-row>
        <!-- <el-form-item>
          <el-button type="primary" @click="onfresh">刷新</el-button>
        </el-form-item> -->
      </el-form>
      </template> 
      <br>
       <div id="echarthotwordsmonit" style="width: 100%;height: 589px; box-sizing:border-box; line-height: 589px;">     
      </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {hotWordsMonitorAnalysis} from '@/api/operatemanage/operate'
export default {
  name: 'Roles',
  components: { },
   props:{
       filter: { }
     },
  data() {
    return {
      filter1:{
        jpProCodes:[],
        leftY:0,
        //rightY:1
      },
   Ylist:[
          {value:0,unit:"",label:"交易金额"},
          {value:1,unit:"",label:"访客人数"},
          {value:2,unit:"",label:"搜索人数"},
          {value:4,unit:"%",label:"支付人数"},
          {value:6,unit:"",label:"收藏人数"},
          {value:8,unit:"",label:"加购人数"},
          {value:11,unit:"",label:"uv价值"}
         ],
      jpprocodes:[],
      pageLoading: false
    }
  },
  mounted() {
  },
  beforeUpdate() {
  },
methods: {
  async onSearch() {
     if (!this.filter.selfProCode) {
        this.$message({message: "请先选择产品！",type: "warning",});
        return;
      }
      this.getdata()
    },
  async onfresh() {
     if (!this.filter.selfProCode) {
        this.$message({message: "请先选择产品！",type: "warning",});
        return;
      }
      this.getdata()
    },
  async getdata() {
      var parm={...this.filter, ...this.filter1};
      if (!parm.selfProCode) return;
      var hasparm=false;
      var arry= Object.keys(parm)
      if (arry.length==0)  return;
      for (let key of Object.keys(parm)) {
        if(parm[key])
            hasparm=true;
      }
      if(!hasparm) return;
      const res = await hotWordsMonitorAnalysis(parm);      
      if (!res?.code)  return;       
      var chartDom = document.getElementById('echarthotwordsmonit');
      var myChart = echarts.init(chartDom);
      myChart.clear();
      if (!res.data) {
         this.$message({message: "没有数据!",type: "warning",});
         return;
       }
      var option = this.Getoptions(res.data);
      //console.log(JSON.stringify(option))
      option && myChart.setOption(option); 
    },
  Getoptions(element){
     var series=[]
     element.series.forEach(s=>{
       series.push({  smooth: true, ...s})
     })
     var yAxis=[{ type: 'value',name: "指标",axisLabel: {formatter: '{value}'}},{ type: 'value',name: "占比",axisLabel: {formatter: '{value}'}}]
     this.Ylist.forEach(s=>{
       if (s.value==this.filter1.rightY)
          yAxis.push({ type: 'value',name: s.label,axisLabel: {formatter: '{value}'+s.unit}})
     })      
     var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
            data: element.legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series:  series
    };
    return option;
   },
  }
}
</script>

