<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="monthrange" style="width: 250px; margin-right: 5px;" :clearable="false"
          :value-format="'yyyy-MM'" />
        <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable>
          <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.warehouseName" placeholder="仓库" class="publicCss" clearable collapse-tags>
          <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.post" placeholder="岗位" class="publicCss" clearable collapse-tags>
          <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>

    <vxetablebase :id="TABLE_ID" border :tablekey="TABLE_ID" ref="table" :that="that" :isIndex="true" :hasexpand="true"
      :tablefixed="true" @sortchange="sortchange" :tableData="tableData" :tableCols="tableCols" :isSelection="false"
      :isSelectColumn="false" :summaryarry="summaryarry" :footerDataArray="summaryarry" :showsummary="true"
      style="width: 100%; margin: 0" :loading="loading" :height="'100%'" :somerow="somerow"
      :cellClassName="cellClassName" :mergeColumn="mergeColumn">
      <template slot="right">
        <vxe-column title="操作" width="90" fixed="right">
          <template #default="{ row }">
            <div style="display: flex;justify-content: center;width: 100%;">
              <el-button v-if="row.post.indexOf('小计') == -1" type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" v-if="row.post.indexOf('小计') == -1" size="mini" style="color: red;"
                @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="handlePageChange" @size-change="handleSizeChange" />
    </template>

    <!-- 导入对话框 -->
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
        accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
        :on-change="onUploadChange" :on-remove="onUploadRemove">
        <template #trigger>
          <el-button size="small" type="primary">选取文件</el-button>
        </template>
        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
          @click="onSubmitUpload">
          {{ uploadLoading ? '上传中' : '上传' }}
        </el-button>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 编辑抽屉 -->
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="40%">
      <hResignationRankingEdit v-if="dialogVisibleEdit" :editInfo="editInfo" @cancellationMethod="cancellationMethod" />
    </el-drawer>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/views/profit/sscManager/ePeoplefx/yh_vxetable.vue";
import dayjs from 'dayjs'
import hResignationRankingEdit from "./hResignationRankingEdit.vue";
import { downloadLink } from "@/utils/tools.js";
import {
  rankingOfResignationPositionsInWarehousingPage,
  rankingOfResignationPositionsInWarehousingRemove,
  rankingOfResignationPositionsInWarehousingImport,
  rankingOfResignationPositionsInWarehousingExport,
  temporaryLaborCostsListValue,
} from '@/api/people/peoplessc.js';

// 常量定义
const TABLE_ID = 'hResignationRankingIndex202507031520';
const TEMPLATE_URL = 'https://nanc.yunhanmy.com:10010/media/video/20250711/1943596836229455872.xlsx';
const TEMPLATE_NAME = '仓储离职岗位排名_导入模板.xlsx';

const TABLE_COLS = [
  { width: 'auto', align: 'center', prop: 'calculateMonth', label: '月份' },
  { width: 'auto', align: 'center', prop: 'type', label: '类型' },
  { width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库' },
  { width: 'auto', align: 'center', prop: 'post', label: '岗位' },
  { width: 'auto', align: 'center', prop: 'peopleNumber', label: '人数' },
  { width: 'auto', align: 'center', prop: 'accountFor', label: '占比', formatter: (row) => row.accountFor ? row.accountFor + '%' : '0%' },
];

export default {
  name: "hResignationRankingIndex",
  components: {
    MyContainer,
    vxetablebase,
    hResignationRankingEdit
  },
  data() {
    const currentMonth = dayjs().format('YYYY-MM');

    return {
      mergeColumn: {
        column: ['calculateMonth', 'type', 'warehouseName', 'post'], // 需要合并的列
        default: 'calculateMonth' // 默认显示字段
      },
      // 常量
      TABLE_ID,
      somerow: 'warehouseName,type,calculateMonth',
      downloadLink,

      // 下拉列表数据
      districtList: [],
      sectionList: [],
      typeList: [],

      // 编辑相关
      editInfo: {},
      dialogVisibleEdit: false,

      // 上传相关
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],

      // 表格相关
      that: this,
      tableCols: TABLE_COLS,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,

      // 查询参数
      ListInfo: {
        calculateMonthArr: [currentMonth, currentMonth],
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        postArr: [],
        warehouseNameArr: [],
        type: null,
        warehouseName: null,
        post: null,
      }
    }
  },
  async mounted() {
    await this.getList()
  },

  methods: {
    // 表格样式
    cellClassName(val) {
      if (val.row.post.indexOf("小计") > -1) {
        return 'coloryellow'
      }
    },

    // 搜索
    handleSearch() {
      this.getList('search')
    },

    // 下载模板
    downExcel() {
      downloadLink(TEMPLATE_URL, TEMPLATE_NAME);
    },

    // 编辑相关
    cancellationMethod(val) {
      this.dialogVisibleEdit = false
      if (val === 1) {
        this.getList('search')
      }
    },

    handleEdit(row) {
      this.editInfo = JSON.parse(JSON.stringify(row))
      this.dialogVisibleEdit = true
    },

    async handleDelete(row) {
      try {
        await this.$confirm('是否删除！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        this.loading = true
        const { success } = await rankingOfResignationPositionsInWarehousingRemove({ ids: row.id })
        this.loading = false

        if (success) {
          this.$message.success('删除成功')
          this.getList();
        } else {
          this.$message.error('删除失败')
        }
      } catch (error) {
        // 用户取消删除
      }
    },

    // 上传相关
    onUploadRemove() {
      this.fileList = []
    },

    onUploadChange(_, fileList) {
      this.fileList = fileList;
    },

    onUploadSuccess() {
      this.fileList = [];
      this.dialogVisible = false;
    },

    async onUploadFile(item) {
      if (!item?.file?.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }

      this.uploadLoading = true
      try {
        const form = new FormData();
        form.append("file", item.file);
        const res = await rankingOfResignationPositionsInWarehousingImport(form);

        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
          await this.getList()
        }
      } catch (error) {
        this.$message({ message: "上传失败", type: "error" });
      } finally {
        this.uploadLoading = false
        this.dialogVisible = false;
      }
    },

    onSubmitUpload() {
      if (this.fileList.length === 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },

    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    // 导出
    async exportProps() {
      this.loading = true
      try {
        const params = this.buildQueryParams();
        const data = await rankingOfResignationPositionsInWarehousingExport(params)

        const aLink = document.createElement("a");
        const blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', `仓储离职岗位排名_${new Date().toLocaleString()}.xlsx`)
        aLink.click()
      } catch (error) {
        this.$message.error('导出失败')
      } finally {
        this.loading = false
      }
    },

    // 构建查询参数
    buildQueryParams() {
      const params = {
        ...this.ListInfo,
        calculateMonthStart: null,
        calculateMonthEnd: null,
      };

      // 处理日期范围
      if (this.ListInfo.calculateMonthArr?.length > 0) {
        params.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
        params.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
      }

      // 处理数组参数
      // params.post = this.ListInfo.postArr.join(',');
      // params.warehouseName = this.ListInfo.warehouseNameArr.join(',');

      return params;
    },

    // 获取列表数据
    async getList(type) {
      if (type === 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }

      this.loading = true
      try {
        const params = this.buildQueryParams();
        const { data, success } = await rankingOfResignationPositionsInWarehousingPage(params)

        if (success) {
          this.tableData = data.list
          this.total = data.total

          // 更新下拉列表选项
          this.updateDropdownOptions();

          // 处理汇总数据
          this.processSummaryData(data.summary);
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },

    // 更新下拉列表选项
    updateDropdownOptions() {
      // 从表格数据中提取唯一值
      const extractUniqueValues = (field) => {
        return Array.from(new Set(
          this.tableData
            .map(item => item[field])
            .filter(value => value !== undefined && value !== null)
        ));
      };

      this.typeList = Array.from(new Set([...this.typeList, ...extractUniqueValues('type')]));
      this.districtList = Array.from(new Set([...this.districtList, ...extractUniqueValues('warehouseName')]));
      this.sectionList = Array.from(new Set([...this.sectionList, ...extractUniqueValues('post')]));
    },
    // 处理汇总数据
    processSummaryData(summary) {
      if (!Array.isArray(summary)) return;
      summary.forEach(item => {
        Object.keys(item).forEach(key => {
          if (item[key] !== undefined && item[key] !== null) {
            if (key === 'accountFor') {
              item[key] = item[key] + '%';
            } else {
              item[key] = item[key] + ' ';
            }
          }
        });
      });
      this.summaryarry = summary;
    },

    // 分页相关
    handleSizeChange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },

    handlePageChange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },

    // 排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = !order.includes("descending")
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;

  .publicCss {
    width: 160px;
  }
}

:deep(.el-select__tags-text) {
  max-width: 35px;
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.vxe-footer--row) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.coloryellow) {
  background: #fff2cc;
  color: black;
  font-weight: 600;
}
</style>
