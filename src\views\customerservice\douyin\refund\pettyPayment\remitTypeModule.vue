<template>
  <MyContainer>
    <vxetablebase :id="'remitTypeModule202408041524'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageGetChangeLogs } from "@/api/inventory/basicgoods"
import { getPettyPaymentTypeGroup_DYPageList } from '@/api/customerservice/douyinrefund'

const tableCols = [
  { width: 'auto', align: 'center', prop: 'remitType', label: '打款类型', },
  { istrue: true, prop: 'remitAmount', label: '金额-占比', width: '200', align: 'center', sortable: 'custom',
    formatter: (row) => { return row.remitAmount + '/' + (row.remitAmountRate * 100).toFixed(2) + '%' } },
  { istrue: true, prop: 'orderCount', label: '单量-占比', width: '200', align: 'center', sortable: 'custom',
    formatter: (row) => { return row.orderCount + '/' + (row.orderCountRate * 100).toFixed(2) + '%' }},
]
export default {
  name: "remitTypeModule",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {

  },
  methods: {
    async getList(type, params) {
      console.log(type, params, 'type, params');
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.ListInfo = Object.assign(this.ListInfo, params)
      const theChildparams = { startDate: this.ListInfo.startDate, endDate: this.ListInfo.endDate, proCode: this.ListInfo.proCode, shopCode: this.ListInfo.shopCode, ...this.ListInfo }
      this.loading = true
      const { data: { list, total }, success } = await getPettyPaymentTypeGroup_DYPageList(theChildparams)
      if (success) {
        this.tableData = list
        this.total = total
        this.loading = false
      } else {
        //获取列表失败
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
