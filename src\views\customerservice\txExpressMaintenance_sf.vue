<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" @click="getList('search')" style="width: 80px;">刷新</el-button>
        </div>
      </template>
      <vxetablebase :id="'txExpressMaintenance_sf202408211421'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
        :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
        :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
        :loading="loading" :height="'100%'">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
      <el-dialog title="导入顺丰快递维护数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag append-to-body>
        <span>
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import dayjs from 'dayjs'
  import {getTianMaoComplainExpress_ShunFengList as getTianMaoComplainExpressList,importTianMaoComplainExpress_ShunFengAsync as  importTianMaoComplainExpressAsync } from '@/api/customerservice/taobaoshouhou'
  
  const tableCols = [
    { width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库名称', },
    { width: 'auto', align: 'center', prop: 'correspondingAreaExpress_SF', label: '对应地区顺丰', sortable: 'custom' },
  ]
  export default {
    name: "txExpressMaintenance",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        dialogVisible: false,
        fileList: [],
        uploadLoading: false,
        fileparm: {},
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
        },
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        var res = await importTianMaoComplainExpressAsync(form);
        if (res?.success)
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      onSubmitUpload() {
        if (this.fileList.length == 0) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.dialogVisible = true;
      },
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await getTianMaoComplainExpressList(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          //获取列表失败
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }
  </style>
  