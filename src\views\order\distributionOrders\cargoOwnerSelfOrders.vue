<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="付款开始日期" end-placeholder="付款结束日期" :picker-options="pickerOptions"
          style="width: 230px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <dateRange :startDate.sync="ListInfo.sendTimeStart" :endDate.sync="ListInfo.sendTimeEnd"
          style="width: 230px;margin-right: 5px;" class="publicCss" startPlaceholder="发货时间" endPlaceholder="发货时间" />
        <el-select class="publicCss" v-model="ListInfo.platform" placeholder="平台" style="width: 100px" clearable
          :collapse-tags="true" filterable>
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- <el-input v-model.trim="ListInfo.orderInnerNo" placeholder="内部订单号" maxlength="20" clearable class="publicCss" /> -->
        <el-input-number v-model="ListInfo.orderInnerNo" class="publicCss" :min="0" :max="999999999999999"
          placeholder="内部订单号" :precision="0" :controls="false"></el-input-number>
        <div class="publicCss" style="display: flex;">
          <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="150px"
            placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
            @callback="callbackGoodsCode" title="商品编码">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.shipperFxName" placeholder="货主分销" class="publicCss" clearable>
          <el-option v-for="item in fxUserNames" :key="'2' + item" :label="item" :value="item" />
        </el-select>
        <el-input v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="20" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.productCode" placeholder="产品ID" maxlength="20" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" size="mini" :disabled="isExport" @click="onExport">导出</el-button>
      </div>
    </template>

    <vxetablebase :id="'cargoOwnerSelfOrders202412141512'" :tablekey="'cargoOwnerSelfOrders202412141512'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      border :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry'
      :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatPlatform } from '@/utils/tools'
import { getShipperFxNameList,getShipperFxSelfOrderList,ExportShipperFxSelfOrderList} from '@/api/order/shipperFxOrder';
import inputYunhan from "@/components/Comm/inputYunhan";
import dayjs from 'dayjs'
import { orderDayRpt_OrderTypes_fmtFunc } from '@/api/bookkeeper/reportdayV2'
import { rulePlatform } from "@/utils/formruletools";
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
  { istrue: true, prop: 'shipperFxName', label: '货主分销', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'yearMonthDay', label: '日期', sortable: 'custom', width: '90', formatter: (row) => row.yearMonthDay ? dayjs(row.yearMonthDay).format('YYYY-MM-DD') : '' },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: '90', type: 'orderLogInfo', orderType: 'orderNoInner' },
  { istrue: true, prop: 'orderType', label: '订单类型', width: '80', type: 'custom', formatter: (row) => orderDayRpt_OrderTypes_fmtFunc(row.orderType), sortable: 'custom', },
  { istrue: true, prop: 'orderNo', label: '线上单号', width: '180', sortable: 'custom' },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '180', },
  { istrue: true, prop: 'platform', fix: true, label: '平台', width: '90', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  {
    istrue: true, prop: 'timeSend', label: '发货日期', width: '140', sortable: 'custom', formatter: (row) => {
      if ((row.timeSend && row.timeSend == '2106-02-07 06:28:16') || !row.timeSend) {
        return ''
      } else {
        return row.timeSend
      }
    }
  },
  { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: '130', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'styleCode', label: '款式编码', sortable: 'custom', width: '90', formatter: (row) => !row.styleCode ? " " : row.styleCode },
  { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'cost', label: '成本价', type: 'custom', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'qty', label: '销售数量', sortable: 'custom', width: '90', },
  { istrue: true, prop: 'giftQty', label: '赠品数量', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'actualAmount', label: '实发金额', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'salesAmount', label: '销售金额', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', width: '90' },
]
export default {
  name: "cargoOwnerSelfOrders",
  components: {
    MyContainer, vxetablebase, inputYunhan, dateRange
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      fxUserNames:[],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        payTimeStart: null,//开始时间
        payTimeEnd: null,//结束时间
        // orderInnerNo: null,//内部订单号
        shipperFxName: null,//货主分销
        goodsCode: null,//商品编码
        platform: null//平台
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      platformList: [],
    }
  },
  async mounted() {
    await this.getList()
    var pfrule = await rulePlatform();
    this.platformList = pfrule.options;

    await getShipperFxNameList()
      .then(({ data }) => {
        this.fxUserNames = data;
      })
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.payTimeStart = e ? e[0] : null
      this.ListInfo.payTimeEnd = e ? e[1] : null
    },
    callbackGoodsCode(val) {
      this.ListInfo.goodsCode = val
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.payTimeStart = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.payTimeEnd = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.payTimeStart, this.ListInfo.payTimeEnd]
      }
      this.loading = true
      const { data, success } = await getShipperFxSelfOrderList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },

    async onExport(val) {
      this.loading = true;
      await ExportShipperFxSelfOrderList(this.ListInfo)
        .then((data) => {
          if (!data.success) {
            return;
          }
          window.$message.success(data.msg);
        })
        .catch(() => {
          this.loading = false;
        });
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
