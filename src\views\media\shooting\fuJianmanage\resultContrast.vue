<template>
    <my-container v-loading="pageLoading">
        <div class="rowstyle" >
            <div class="marleftrig" ref="oneboxx">
                <div class="aligncenter">
                    <div class="flexrow">
                        <el-button-group>
                        <el-button :type="num==1?'primary':''" @click="whactclick(1)">直通车图</el-button>
                        <el-button :type="num==2?'primary':''" @click="whactclick(2)">主图</el-button>
                        <el-button :type="num==3?'primary':''" @click="whactclick(3)">sku</el-button>
                        <el-button :type="num==4?'primary':''" @click="whactclick(4)">详情页</el-button>
                        <el-button :type="num==5?'primary':''" @click="whactclick(5)">主图视频</el-button>
                        <el-button :type="num==6?'primary':''" @click="whactclick(6)">微详情视频</el-button>
                        </el-button-group>
                    </div>
                    <div style="margin-top: 10px;"><el-button type="primary" style="width:200px;" @click="tocreateimg()">点击保存</el-button></div>
                </div>

                <div class="content">
                    <div v-if="num==1&&listall.data1!=null" style="display: flex; flex-direction: row; ">
                        <!-- <div style="flex:1;">
                            <slideshow ref="createindex1" @changefuc="changefuc" @getlist="getlist" :key="num" :list="listall.data1" :bannum="num" :name="'indeximg'"></slideshow>
                        </div> -->
                        <shootingcreateindex1 v-if="num==1&&listall.data1!=null" ref="createindex1" @getlist="getlist" :refname="'createindex1'"  :duimoudle="true" :num="num" :key="num" :alllist="listall.data1" :bannum="num" :main="mainlist" :name="'indeximg'" :resultshow="true" :btnshow="false" :disabled="true"  :ispaste = "false" />
                    </div>
                    <div v-else-if="num==2&&listall.data2!=null" style="display: flex; flex-direction: row;">
                        <!-- <div style="flex:1;">
                            <slideshow ref="createindex2" @changefuc="changefuc" @getlist="getlist" :list = "listall.data2" :imglist = "skuimg" :key="num" :bannum="num" :name="'indeximg'"></slideshow>
                        </div> -->
                        <shootingcreateindex2 ref="createindex2" @getlist="getlist" :refname="'createindex2'"  :duimoudle="true" :num="num" :key="num" :alllist="listall.data2" :bannum="num" :main="mainlist" :name="'indeximg'" :resultshow="true" :btnshow="false" :disabled="true"  :ispaste = "false" />
                    </div>
                    <div v-else-if="num==3&&listall.data3!=null" style="display: flex; flex-direction: row;">
                        <!-- <div style="flex:1;">
                            <slideshow ref="createindex3" @changefuc="changefuc" @getlist="getlist" :list = "listall.data3" :imglist = "msglist" :key="num" :bannum="num" :name="'skuimg'"></slideshow>
                        </div> -->
                        <shootingcreateindex3 ref="createindex3" @getlist="getlist" :refname="'createindex3'"  :duimoudle="true" :num="num" :key="num" :alllist="listall.data3" :bannum="num" :main="mainlist" :name="'indeximg'" :resultshow="true" :btnshow="false" :disabled="true"  :ispaste = "false" />
                    </div>
                    <div v-else-if="num==4&&listall.data4!=null" style="display: flex; flex-direction: row;">
                        <!-- <div style="flex:1;">
                            <slideshow ref="createindex4" @changefuc="changefuc" @getlist="getlist" :key="num" :list = "listall.data4" :imglist = "videolist" :bannum="num" :name="'indexvideo'"></slideshow>
                        </div> -->
                        <shootingcreateindex4 ref="createindex4" @getlist="getlist" :refname="'createindex4'"  :duimoudle="true" :num="num" :key="num" :alllist="listall.data4" :bannum="num" :main="mainlist" :name="'indexvideo'" :resultshow="true" :btnshow="false" :disabled="true"  :ispaste = "false" />
                    </div>
                    <div v-else-if="num==5&&listall.data5!=null" style="display: flex; flex-direction: row;">
                        <!-- <div style="flex:1;">
                            <slideshow ref="createindex5" @changefuc="changefuc" @getlist="getlist" :key="num" :type="'video'" :list = "listall.data5" :imglist = "weilist" :bannum="num" :name="'weivideo'"></slideshow>
                        </div>
                        <div style="flex:3;"><shootingcreateindex5 :duimoudle="true" :refname="'createindex5'" :key="num" :alllist="listall.data5" :main="mainlist" :name="'weivideo'" :resultshow="true" :isroll="true" :btnshow="false" :disabled="true" :ispaste = "false" :showbottom="true"/></div> -->
                        <shootingcreateindex5 ref="createindex5" @getlist="getlist" :refname="'createindex5'"  :duimoudle="true" :num="num" :key="num" :alllist="listall.data5" :bannum="num" :main="mainlist" :name="'weivideo'" :resultshow="true" :btnshow="false" :disabled="true"  :ispaste = "false" :showbottom="true" />

                    </div>
                    <div v-else-if="num==6&&listall.data6!=null" style="display: flex; flex-direction: row;">
                        <!-- <div style="flex:1;">
                            <slideshow ref="createindex6" @changefuc="changefuc" @getlist="getlist" :key="num" :type="'video'" :list = "listall.data6" :imglist = "weilist" :bannum="num" :name="'weivideo'"></slideshow>
                        </div>
                        <div style="flex:3;"><shootingcreateindex6 :duimoudle="true" :refname="'createindex6'" :key="num" :alllist="listall.data6" :main="mainlist" :name="'weivideo'" :resultshow="true" :isroll="true" :btnshow="false" :disabled="true" :ispaste = "false" :showbottom="true"/></div> -->
                       <shootingcreateindex6 ref="createindex6" @getlist="getlist" :refname="'createindex6'"  :duimoudle="true" :num="num" :key="num" :alllist="listall.data6" :bannum="num" :main="mainlist" :name="'weivideo'" :resultshow="true" :btnshow="false" :disabled="true"  :ispaste = "false" :showbottom="true" />

                    </div>
                </div>

                <el-dialog title="海报生成" :visible.sync="dialogTableVisible">
                    <div class="border">
                        <img
                        style="width: auto; height: auto; max-width: 100%; max-height: 100%;"
                        :src="imgUrl"/>
                    </div>
                </el-dialog>
            </div>
        </div>
    </my-container>
</template>

<script>
import html2canvas from 'html2canvas'
import MyContainer from "@/components/my-container";
import slideshow from '@/views/media/shooting/fuJianmanage/slideshow';
import shootingcreateindex1 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex2 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex3 from '@/views/media/shooting/fuJianmanage/shootingcreateindex2';
import shootingcreateindex4 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex5 from '@/views/media/shooting/fuJianmanage/shootingcreateindex3';
import shootingcreateindex6 from '@/views/media/shooting/fuJianmanage/shootingcreateindex3';

import {getUploadSuccessAttachment,getShootRefenceInfo  } from '@/api/media/ShootingVideo';

export default {
    name: 'DEMOResultContrast',
    components: {MyContainer,shootingcreateindex1,shootingcreateindex2,shootingcreateindex3,shootingcreateindex4,shootingcreateindex5,shootingcreateindex6,slideshow},
    data() {
        return {
            pageLoading:false,
            num: 1,
            imgUrl: '',
            dialogTableVisible: false,
            shootingTaskId: '',
            indexList: [],
            imglistt: [],
            skulistt: [],
            skuimg: [],
            msglistt: [],
            msglist: [],
            weilist: [],
            weilistt: [],
            videolist: [],
            videolistt: [],
            listall: [],
            mainlist: null,
            ischange: false,
        };
    },

    async mounted() {
        await this.$nextTick(()=>{
            this.shootingTaskId = this.$route.query.id;
        })
        await this.getlist()
        // await this.getvideo()
        

    },

    methods: {
        changefuc(val){
            this.ischange = val;
        },
        whactclick(num){
            let _this = this;
            if(this.ischange){
                this.$message({
                    message: "请先保存再进行操作！",
                    offset: 150,
                    duration: 2000
                })
                return
            }
            _this.num = num;
        },
        tocreateimg(){
            // html2canvas(this.$refs.oneboxx).then((canvas) => {
			// 	let dataURL = canvas.toDataURL('image/png')
			// 	this.imgUrl = dataURL
            //     this.dialogTableVisible = true;
			// })

            let _this = this;
            _this.ischange = false;
            // this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.submitty();
            }else if(_this.num == 2){
                this.$refs.createindex2.submitty();
            }else if(_this.num == 3){
                this.$refs.createindex3.submitty();
            }else if(_this.num == 4){
                this.$refs.createindex4.submitty();
            }else if(_this.num == 5){
                this.$refs.createindex5.submitty();
            }else if(_this.num == 6){
                this.$refs.createindex6.submitty();
            }
        },
        async getlist(){
            let _this = this;
                
            _this.pageLoading = true;
            var res = await getShootRefenceInfo(_this.shootingTaskId);
            if(res?.success){
            _this.listall = res.data;
            _this.mainlist = res.mainTask;
            _this.pageLoading = false;




            }
        },
        async getvideo(){
            this.pageLoading = true;
            var res = await getUploadSuccessAttachment({uploadType:3,shootingTaskId:this.shootingTaskId,actionType:1});
            if(res?.success){
                let weilist = [];
                let weilistt = [];
                let videolist = [];
                let videolistt = [];

                let arraylist = res.data;
                arraylist.map((item,i)=>{
                if(item.fileType == 1){
                        videolist.push(item)
                    }
                })
                //视频
                this.videolist = videolist;


                arraylist.map((item,i)=>{
                    if(item.fileType == 1){
                        videolistt.push(item.url)
                    }
                })


                var a = ['','','','']
                videolistt.map((item,i)=>{
                    videolistt[i] = [...a]
                    videolistt[i][a.length] = item;
                })






            }
            _this.pageLoading = false;
        }
    },
};
</script>

<style lang="scss" scoped>
.rowstyle{
    margin: 0;
    height: 100%;
    // display: flex;
    // justify-content: center;
    // align-items: center;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: auto;
    // border: 1px solid #eee;
}
.aligncenter{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    height: auto;
    flex-direction: column;
    // border-bottom: 1px solid #eee;
    padding: 10px;
    // border: 1px solid blue;
}
.content{
    // margin-top: 30px;
    border: 1px solid #eee;

    height: 820px;
    overflow-y: auto;
    // border: 1px solid #eee;
}
.border{
    border: 2px solid #409EFF;
}
.flexx{
    flex: 1;
}
.marleftrig{
    margin: 0;
//    width: 1000px;
}
</style>