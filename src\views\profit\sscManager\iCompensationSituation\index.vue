<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" type="daterange" style="width: 280px; margin-right: 5px;" clearable
          :value-format="'yyyy-MM-dd'" />
        <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable @change="changeType">
          <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.region" placeholder="区域" class="publicCss" clearable collapse-tags>
          <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.dept" placeholder="部门" class="publicCss" clearable collapse-tags>
          <el-option v-for="item in sectionList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model.trim="ListInfo.name" placeholder="姓名" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.workInjuriesType" placeholder="工伤类型" class="publicCss" clearable collapse-tags>
          <el-option v-for="item in injuredList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>

    <vxetablebase :id="'iCompensationSituationIndex202507031520'" border
      :tablekey="'iCompensationSituationIndex202507031520'" ref="table" :that="that" :isIndex="true" :hasexpand="true"
      :tablefixed="true" @sortchange="sortchange" :tableData="tableData" :tableCols="tableCols" :isSelection="false"
      :isSelectColumn="false" :summaryarry="summaryarry" :footerDataArray="summaryarry" :showsummary="true"
      style="width: 100%; margin: 0" :loading="loading" :height="'100%'" :somerow="somerow"
      :cellClassName="cellClassName" :mergeColumn="mergeColumn">
      <template #processPhotos="{ row }">
        <div class="image-container">
          <template v-if="row.processPhotos && getImageList(row.processPhotos).length > 0">
            <div class="image-wrapper">
              <el-image :src="getImageList(row.processPhotos)[0]" :preview-src-list="getImageList(row.processPhotos)"
                fit="cover" class="process-image" :lazy="true">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div class="image-count-badge" v-if="getImageList(row.processPhotos).length > 1">
                {{ getImageList(row.processPhotos).length }}
              </div>
            </div>
          </template>
          <div v-else class="no-image">
          </div>
        </div>
      </template>
      <template #workInjuryIdentificationResolution="{ row }">
        <div class="image-container">
          <template
            v-if="row.workInjuryIdentificationResolution?.indexOf('.doc') > -1 || row.workInjuryIdentificationResolution?.indexOf('.docx') > -1 && getImageList(row.workInjuryIdentificationResolution).length > 0">
            <div class="image-wrapper">
              <el-image src="https://nanc.yunhanmy.com:10010/media/video/20250709/1942761545499385857.jpg" fit="cover"
                class="process-image" :lazy="true" @click="downExecl(row.workInjuryIdentificationResolution)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </template>
        </div>
      </template>
      <template slot="right">
        <vxe-column title="操作" width="90" fixed="right">
          <template #default="{ row }">
            <div style="display: flex;justify-content: center;width: 100%;">
              <el-button v-if="!row.dept || row.dept.indexOf('小计') == -1" type="text"
                @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" v-if="!row.dept || row.dept.indexOf('小计') == -1" size="mini" style="color: red;"
                @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>

    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="handlePageChange" @size-change="handleSizeChange" />
    </template>

    <!-- 导入对话框 -->
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
        accept=".xlsx" :file-list="fileList" :http-request="onUploadFile" :on-success="onUploadSuccess"
        :on-change="onUploadChange" :on-remove="onUploadRemove">
        <template #trigger>
          <el-button size="small" type="primary">选取文件</el-button>
        </template>
        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
          @click="onSubmitUpload">
          {{ uploadLoading ? '上传中' : '上传' }}
        </el-button>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 编辑抽屉 -->
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="40%">
      <iCompensationSituationEdit v-if="dialogVisibleEdit" :editInfo="editInfo"
        @cancellationMethod="cancellationMethod" />
    </el-drawer>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/views/profit/sscManager/ePeoplefx/yh_vxetable.vue";
import dayjs from 'dayjs'
import iCompensationSituationEdit from "./iCompensationSituationEdit.vue";
import { downloadLink } from "@/utils/tools.js";
import {
  workInjuryCompensationSituationInEachDistrictPage,
  workInjuryCompensationSituationInEachDistrictRemove,
  workInjuryCompensationSituationInEachDistrictImport,
  workInjuryCompensationSituationInEachDistrictExport,
  temporaryLaborCostsListValue,
} from '@/api/people/peoplessc.js';

// 常量定义
const TABLE_ID = 'hResignationRankingIndex202507031520';
const TEMPLATE_URL = 'https://nanc.yunhanmy.com:10010/media/video/20250711/1943595521709416449.xlsx';
const TEMPLATE_NAME = '各区工伤赔付情况_导入模板.xlsx';

const TABLE_COLS = [
  { width: '100', align: 'center', prop: 'type', label: '类型' },
  { width: '100', align: 'center', prop: 'region', label: '区域' },
  { width: '100', align: 'center', prop: 'dept', label: '部门' },
  { width: '100', align: 'center', prop: 'accidentDate', label: '事故日期' },
  { width: '100', align: 'center', prop: 'name', label: '姓名' },
  { width: '100', align: 'center', prop: 'workInjuriesType', label: '工伤类型' },
  { width: '100', align: 'center', prop: 'isInsurance', label: '是否参险' },
  { width: '100', align: 'center', prop: 'injuredArea', label: '受伤部位' },
  { width: '100', align: 'center', prop: 'injuryTime', label: '受伤时间' },
  { width: '100', align: 'center', prop: 'injuryCauseProcess', label: '受伤原因及经过' },
  { width: '100', align: 'center', prop: 'processPhotos', label: '过程照片' },
  { width: '100', align: 'center', prop: 'isFracture', label: '是否骨折' },
  { width: '100', align: 'center', prop: 'involvingEquipmentProcessName', label: '涉及设备及工艺名称' },
  { width: '120', align: 'center', prop: 'accidentAlarmTime', label: '事故报警时间' },
  { width: '100', align: 'center', prop: 'restDays', label: '休息天数' },
  { width: '100', align: 'center', prop: 'hospitalizationDays', label: '住院天数' },
  { width: '100', align: 'center', prop: 'medicalExpenses', label: '医疗费用' },
  { width: '100', align: 'center', prop: 'insuranceCompensationLostWages', label: '保险赔偿误工费' },
  { width: '100', align: 'center', prop: 'totalExpensesIncurred', label: '合计产生费用' },
  { width: '100', align: 'center', prop: 'totalCostBorneInsurance', label: '保险公司承担费用合计' },
  { width: '100', align: 'center', prop: 'companyCompensatesLostWorkExpenses', label: '公司赔付误工费(6月起，按150元/天/人计算)' },
  { width: '100', align: 'center', prop: 'totalExpensesBorneCompany', label: '公司承担费用合计' },
  { width: '100', align: 'center', prop: 'workInjuryAccidentAcceptanceNumber', label: '工伤事故受理编号' },
  { width: '100', align: 'center', prop: 'workInjuryIdentificationResolution', label: '工伤认定决议书' },
  { width: '100', align: 'center', prop: 'disabilityAssessmentConclusionAndTime', label: '伤残鉴定结论及时间' },
]
export default {
  name: "iCompensationSituationIndex",
  components: {
    MyContainer,
    vxetablebase,
    iCompensationSituationEdit
  },
  data() {
    const currentDate = dayjs().format('YYYY-MM-DD');
    const firstDayOfMonth = dayjs().startOf('month').format('YYYY-MM-DD');

    return {
      mergeColumn: {
        column: ['type', 'region', 'dept', 'accidentDate', 'name', 'workInjuriesType', 'isInsurance', 'injuredArea', 'injuryTime', 'injuryCauseProcess', 'processPhotos', 'isFracture', 'involvingEquipmentProcessName', 'accidentAlarmTime'], // 需要合并的列
        default: 'type' // 默认显示字段
      },
      // 常量
      TABLE_ID,
      somerow: 'type,dept,region',
      downloadLink,

      // 下拉列表数据
      districtList: [],
      allDistrictList: [],
      sectionList: [],
      typeList: [],
      injuredList: ['骨折', '夹伤', '砸伤', '压伤', '撞伤', '扭伤', '摔伤'],

      // 编辑相关
      editInfo: {},
      dialogVisibleEdit: false,

      // 上传相关
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],

      // 表格相关
      that: this,
      tableCols: TABLE_COLS,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,

      // 查询参数
      ListInfo: {
        calculateMonthArr: [firstDayOfMonth, currentDate],
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        type: null,
        // region: null,
        dept: null,
      }
    }
  },
  async mounted() {
    await this.getList()
  },

  methods: {
    changeType(e) {
      if (!e) {
        this.districtList = [...this.allDistrictList];
        return;
      }
      if (e === '办公室') {
        this.districtList = this.allDistrictList.filter(item => !item.includes('仓'));
      } else {
        this.districtList = this.allDistrictList.filter(item => item.includes('仓'));
      }
    },
    // 处理图片列表
    getImageList(imageString) {
      if (!imageString) return [];
      return imageString.split(',').filter(url => url.trim());
    },

    // 表格样式
    cellClassName(val) {
      if (val.row.dept && val.row.dept.indexOf("小计") > -1) {
        return 'coloryellow'
      }
    },

    // 搜索
    handleSearch() {
      this.getList('search')
    },
    downExecl(url) {
      downloadLink(url, '工商认定决议书文件');
    },

    // 下载模板
    downExcel() {
      downloadLink(TEMPLATE_URL, TEMPLATE_NAME);
    },

    // 编辑相关
    cancellationMethod(val) {
      this.dialogVisibleEdit = false
      if (val === 1) {
        this.getList('search')
      }
    },

    handleEdit(row) {
      this.editInfo = JSON.parse(JSON.stringify(row))
      this.dialogVisibleEdit = true
    },

    async handleDelete(row) {
      try {
        await this.$confirm('是否删除！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        this.loading = true
        const { success } = await workInjuryCompensationSituationInEachDistrictRemove({ ids: row.id })
        this.loading = false

        if (success) {
          this.$message.success('删除成功')
          this.getList();
        } else {
          this.$message.error('删除失败')
        }
      } catch (error) {
        // 用户取消删除
      }
    },

    // 上传相关
    onUploadRemove() {
      this.fileList = []
    },

    onUploadChange(_, fileList) {
      this.fileList = fileList;
    },

    onUploadSuccess() {
      this.fileList = [];
      this.dialogVisible = false;
    },

    async onUploadFile(item) {
      if (!item?.file?.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }

      this.uploadLoading = true
      try {
        const form = new FormData();
        form.append("file", item.file);
        const res = await workInjuryCompensationSituationInEachDistrictImport(form);

        if (res?.success) {
          this.$message({ message: res.msg, type: "success" });
          await this.getList()
        }
      } catch (error) {
        this.$message({ message: "上传失败", type: "error" });
      } finally {
        this.uploadLoading = false
        this.dialogVisible = false;
      }
    },

    onSubmitUpload() {
      if (this.fileList.length === 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },

    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    // 导出
    async exportProps() {
      this.loading = true
      try {
        const params = this.buildQueryParams();
        const data = await workInjuryCompensationSituationInEachDistrictExport(params)

        const aLink = document.createElement("a");
        const blob = new Blob([data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', `仓储离职岗位排名_${new Date().toLocaleString()}.xlsx`)
        aLink.click()
      } catch (error) {
        this.$message.error('导出失败')
      } finally {
        this.loading = false
      }
    },

    // 构建查询参数
    buildQueryParams() {
      const params = {
        ...this.ListInfo,
        calculateMonthStart: null,
        calculateMonthEnd: null
      };

      // 处理日期范围
      if (this.ListInfo.calculateMonthArr?.length > 0) {
        params.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
        params.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
      }

      return params;
    },

    // 获取列表数据
    async getList(type) {
      if (type === 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }

      this.loading = true
      try {
        const params = this.buildQueryParams();
        const { data, success } = await workInjuryCompensationSituationInEachDistrictPage(params)

        if (success) {
          this.tableData = data.list
          // this.getxiala('typeList', 'type');
          // this.getxiala('districtList', 'region');
          // this.getxiala('sectionList', 'dept');
          this.total = data.total

          // 更新下拉列表选项
          this.updateDropdownOptions();

          // 处理汇总数据
          this.processSummaryData(data.summary);
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },

    // 更新下拉列表选项
    updateDropdownOptions() {
      // 从表格数据中提取唯一值
      const extractUniqueValues = (field) => {
        return Array.from(new Set(
          this.tableData
            .map(item => item[field])
            .filter(value => value !== undefined && value !== null)
        ));
      };

      this.typeList = Array.from(new Set([...this.typeList, ...extractUniqueValues('type')]));
      this.allDistrictList = Array.from(new Set([...this.allDistrictList, ...extractUniqueValues('region')]));
      this.sectionList = Array.from(new Set([...this.sectionList, ...extractUniqueValues('dept')]));
      this.injuredList = Array.from(new Set([...this.injuredList, ...extractUniqueValues('workInjuriesType')]));
      if (!this.ListInfo.type) {
        this.districtList = [...this.allDistrictList];
      }
    },
    async getxiala(that, val) {
      let res = await temporaryLaborCostsListValue({
        'fieldName': val
      })
      if (!res.success) {
        return
      }
      this[that] = res.data;
    },

    // 处理汇总数据
    processSummaryData(summary) {
      if (!summary) return;
      summary.forEach(key => {
        if (summary[key] !== undefined && summary[key] !== null) {
          if (key === 'totalSalary_sum' || key === 'hourSalary_sum') {
            summary[key] = summary[key].toFixed(2) + ' '
          } else {
            summary[key] = summary[key] + ' '
          }
        }
      });

      this.summaryarry = summary;
    },

    // 分页相关
    handleSizeChange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },

    handlePageChange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },

    // 排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = !order.includes("descending")
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;

  .publicCss {
    width: 160px;
  }
}

:deep(.el-select__tags-text) {
  max-width: 35px;
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.vxe-footer--row) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

// 图片展示样式
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  .image-wrapper {
    position: relative;
    display: inline-block;

    .process-image {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid #dcdfe6;

      :deep(.el-image__inner) {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        color: #909399;
        font-size: 14px;
      }
    }

    .image-count-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #f56c6c;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      font-size: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      border: 2px solid white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
  }

  .no-image {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    color: #909399;
    font-size: 12px;

    i {
      font-size: 16px;
      margin-bottom: 2px;
    }

    span {
      font-size: 10px;
    }
  }
}

:deep(.coloryellow) {
  background: #fff2cc;
  color: black;
  font-weight: 600;
}
</style>
