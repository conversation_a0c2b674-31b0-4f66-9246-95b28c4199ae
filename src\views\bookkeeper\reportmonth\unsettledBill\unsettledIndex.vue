<template>
  <my-container>
    <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane label="淘系未结算" name="first1" style="height: 98%;">
        <unsettledTx />
      </el-tab-pane>
      <el-tab-pane label="拼多多未结算" name="first2" :lazy="true" style="height: 98%;">
        <unsettledPdd />
      </el-tab-pane>
      <el-tab-pane label="抖音未结算" name="first3" :lazy="true" style="height: 98%;">
        <unsettledDy />
      </el-tab-pane>
    </el-tabs>
  </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import unsettledTx from './unsettledTx.vue'
import unsettledPdd from './unsettledPdd.vue'
import unsettledDy from './unsettledDy.vue'

export default {
  name: "settlementKWaiShopIndex",
  components: { MyContainer, unsettledTx, unsettledPdd, unsettledDy },
  data() {
    return {
      activeName: 'first1'
    };
  },
};
</script>
<style lang="scss" scoped></style>
