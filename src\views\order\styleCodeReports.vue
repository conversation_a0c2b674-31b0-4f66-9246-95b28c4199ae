<template>
    <my-container v-loading="pageLoading" style="height: 100%">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="日期:">
                    <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台">
                    <el-select v-model="filter.platform" placeholder="请选择" :clearable="true" :collapse-tags="true"
                        filterable @change="onchangeplatformMain" style="width: 100px">
                        <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="经营大类:">
                    <el-select style="width: 175px;" v-model="filter.bzCategory" placeholder="经营大类"
                        :collapse-tags="true" remote :remote-method="remoteMethodBusinessCategory" clearable filterable>
                        <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="i" :label="item"
                            :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="一级类目:">
                    <el-select style="width: 175px;" v-model="filter.bzCategory1" placeholder="一级类目"
                        :collapse-tags="true" remote :remote-method="remoteMethodCategoryName1s" clearable filterable>
                        <el-option v-for="(item, i) in filterList.categoryName1s" :key="i" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="二级类目:">
                    <el-select style="width: 175px;" v-model="filter.bzCategory2" placeholder="二级类目"
                        :collapse-tags="true" remote :remote-method="remoteMethodCategoryName2s" clearable filterable>
                        <el-option v-for="(item, i) in filterList.categoryName2s" :key="i" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺:">
                    <el-select v-model="filter.shopCode" style="width: 150px" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable>
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="filter.groupId" style="width: 110px" placeholder="请选择" :clearable="true"
                        :collapse-tags="true" filterable>
                        <el-option v-for="item in groupList" :key="item.key" :label="item?.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="采购组:">
                    <el-select v-model="filter.brandId" clearable filterable placeholder="请选择采购员" style="width: 140px"
                        :collapse-tags="true">
                        <el-option v-for="item in brandlist" :key="item?.value" :label="item.label"
                            :value="item?.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="系列编码:">
                    <el-input v-model.trim="filter.goodsCode" placeholder="系列编码" style="width: 130px" clearable />
                </el-form-item>
                <el-form-item label="产品ID:">
                    <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width: 130px" clearable />
                </el-form-item>
                <el-form-item label="毛二正负利润:" v-if="checkPermission('prosameprofit')">
                    <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable placeholder="毛利2"
                        style="width: 100px">
                        <el-option label="全部" />
                        <el-option label="正利润" :value="false" />
                        <el-option label="负利润" :value="true" />
                    </el-select>
                </el-form-item>
                <el-form-item label="毛三正负利润:" v-if="checkPermission('prosameprofit')">
                    <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3"
                        style="width: 100px">
                        <el-option label="全部" />
                        <el-option label="正利润" :value="false" />
                        <el-option label="负利润" :value="true" />
                    </el-select>
                </el-form-item>
                <el-form-item label="毛四正负利润:" v-if="checkPermission('prosameprofit')">
                    <el-select filterable v-model="filter.profit33UnZero" collapse-tags clearable placeholder="毛利4"
                        style="width: 100px">
                        <el-option label="全部" />
                        <el-option label="正利润" :value="false" />
                        <el-option label="负利润" :value="true" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态:" v-if="checkPermission('prosameprofit')">
                    <el-select v-model="filter.state" placeholder="请选择" clearable :collapse-tags="true" filterable
                        style="width: 100px">
                        <el-option v-for="item in prosimstatelist" :key="item.state" :label="item.state"
                            :value="item.state" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onAdd">公司类目新增</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table"  :id="'styleCodeReports_240804111600'"  :that="that" :isIndex="true" :hasexpand="true" @sortchange="sortchange"
            :iscompanyclick="true" :summaryarry="summaryarryMain" :tableData.sync="list" :tableCols="tableCols"
            :tablefixed="true" :tableHandles="tableHandles" :headerCellStyle="headerCellStyle" :border="true"
            @summaryClick='onsummaryClick' :loading="listLoading">
            <template slot="extentbtn">
                <el-button-group v-if="lastUpdateTime != null">
                    <el-button style="margin: 0" @click="getimportlist">
                        {{ lastUpdateTime }}
                    </el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :visible.sync="detail.visible" :show-close="false" width="90%" v-dialogDrag>
            <el-button type="primary" @click="onSearchDetail">查询</el-button>
            <el-button type="primary" @click="showNext">下一个</el-button>
            <div style="margin-bottom: 10px">
                <el-row>
                    <el-col :span="20">
                        <el-descriptions :column="3" size="mini" border>
                            <el-descriptions-item label="系列编码">{{ detail.selRow.goodsCode }}</el-descriptions-item>
                            <el-descriptions-item label="系列名称">{{ detail.selRow.goodsName }}</el-descriptions-item>
                            <el-descriptions-item label="运营组">{{ selGroupName }}</el-descriptions-item>
                            <el-descriptions-item label="主链接">
                                <div v-html="myformatLinkProCode(detail.selRow.platform, detail.selRow.proCode)"></div>
                            </el-descriptions-item>
                            <el-descriptions-item label="近30天销量">{{ detail.selRow.salesQty }}</el-descriptions-item>
                            <el-descriptions-item label="编码数">{{ detail.selRow.goodsCodeQty }}</el-descriptions-item>
                        </el-descriptions>
                    </el-col>
                    <el-col :span="3" :offset="1">
                        <el-image style="width: 60px; height: 60px" :src="detail.selRow.goodsImage"
                            :preview-src-list="detail.srcList">
                            <template #error>
                                <div class="image-slot">
                                    <el-icon>
                                        <picture />
                                    </el-icon>
                                </div>
                            </template>
                        </el-image>
                    </el-col>
                </el-row>
            </div>
            <el-tabs type="card" style="margin-bottom: 0px" @tab-click="onTabClick" v-model="detail.tabName">
                <el-tab-pane label="相似产品" name="tabSame">
                    <same-pro-detail :filter="detail.filter" ref="sameProDetail" style="height: 480px"
                        :platformList="platformList" :groupList="groupList">
                    </same-pro-detail>
                </el-tab-pane>

                <el-tab-pane label="系列编码" name="tabSeries" v-if="checkPermission('prosameprofit')">
                    <series-goods :filter="detail.filter" ref="seriesGoods" style="height: 480px">
                    </series-goods>
                </el-tab-pane>

                <el-tab-pane label="主链接编码" name="tabMain" v-if="checkPermission('prosameprofit')">
                    <main-pro-goods :filter="detail.filter" ref="mainProGoods" style="height: 480px">
                    </main-pro-goods>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>

        <div v-show="isshowstate">
            <procodesimilaritystate ref="procodesimilaritystate" @changelist="changelist" @onSearch="onSearch">
            </procodesimilaritystate>
        </div>

        <!-- 回款天数弹窗 -->
        <el-dialog :visible.sync="openPaymentVisible" width="40%" v-dialogDrag>
            <vxetablebase :id="'styleCodeReports202408041738_2'" v-if="openPaymentVisible" ref="table" :that='that' :isIndex='true' :hasexpand='true'
                :tablefixed='true' :tableData='openPaymentData' :tableCols='paymentTableCols' :isSelection="false"
                :toolbarshow="false" :isSelectColumn="false" style="width: 100%; margin: 30px 0 0 0 ;height: 200px;"
                v-loading="loading" />
        </el-dialog>

        <!-- 订单量图表 -->
        <el-dialog :visible.sync="dialoganalysisVisible" width="80%" v-dialogDrag :show-close="false">
            <proCodeSimilarityAnalysis ref="proCodeSimilarityAnalysis" style="height: 550px">
            </proCodeSimilarityAnalysis>
        </el-dialog>

        <!-- 系列编码趋势图 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" v-dialogDrag width="80%">
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span v-if="buessness.visible">
                <buschar v-if="buscharDialog.visible" :analysisData="buessness.data"></buschar>
                <!-- <ProCodeBusinessStaffPlatForm v-if="buessness.visible" ref="ProCodeBusinessStaffPlatForm"></ProCodeBusinessStaffPlatForm> -->
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>


        <!-- 周转天数详情 -->
        <el-dialog :visible.sync="dialodayssisVisible" width="50%" v-dialogDrag :show-close="false">
            <Daysturnoverdetail ref="Daysturnoverdetail" :filter="detail.filter" style="height: 550px">
            </Daysturnoverdetail>
        </el-dialog>

        <el-drawer title="耗材费用" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="editparmVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
            <div class="drawer-footer">
                <el-button @click.native="editparmVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="editparmLoading" @click="onSetEditParm" />
            </div>
        </el-drawer>

        <!-- <el-dialog :visible.sync="isshowmonth" width="50%" v-dialogDrag :show-close="false">
           <el-checkbox-group v-model="checkboxGroup1">
            <el-checkbox-button v-for="city in cities" :label="city" :key="city">{{city}}</el-checkbox-button>
          </el-checkbox-group>
      </el-dialog> -->

        <!-- 周转天数详情 -->
        <el-dialog :visible.sync="dialogDayZZRatedetailVisible" width="60%" v-dialogDrag :show-close="false">
            <DayZZRateDetail ref="DayZZRatedetail" :filter="dayZZRatedetail.filter" style="height: 550px">
            </DayZZRateDetail>
        </el-dialog>

        <!-- 时间线弹框 -->
        <el-dialog title="" :visible.sync="importtimedialogVisible" v-dialogDrag width="30%">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span></span>
                </div>
                <div style="height:400px;overflow-y:auto" class="text item">
                    <el-alert v-for="item in importtimelist" :key="item" title="" type="success" :closable="false">
                        计算时间 : {{ item }}
                    </el-alert>
                </div>
            </el-card>
        </el-dialog>

        <vxe-modal v-model="seriesCodingCategory" transfer :zIndex="2001" :width="1300" marginSize='-500' :height="600"
            center draggable :show-close="false">
            <template #default>
            <template slot=title>
                系列编码类目
                <i class="el-icon-close" style="float:right;border:0;font-size:20px;cursor:pointer;"
                    @click="customihandleClose"></i>
            </template>
            <div class="father">
                <div class="main">
                    <div class="father_top">
                        <div class="father_top_left">
                            <div class="father_top_left_top">
                                <el-input placeholder="请输入系列编码" v-model="seriesName" clearable class="input-with-select"
                                    maxlength="100">
                                    <el-button slot="append" icon="el-icon-search"
                                        @click="bindingSeriescoding(1)"></el-button>
                                </el-input>
                            </div>
                            <div class="father_top_left_bottom">
                                <div class="father_top_left_bottom_top">
                                    <div :class="['father_top_left_bottom_top_left', index == 0 ? 'active' : '']"
                                        @click="getProps(0)">待绑定
                                    </div>
                                    <div :class="['father_top_left_bottom_top_right', index == 1 ? 'active' : '']"
                                        @click="getProps(1)">
                                        已绑定
                                    </div>
                                </div>
                                <div class="father_top_left_bottom_bottom">
                                    <vxe-table :scroll-y="{ gt: 200, enabled: true }" v-show="index == 0"
                                        :column-config="{ resizable: true }" :loading="seriesloading"
                                        :scroll-x="{ gt: 200, enabled: true }" ref="multipleTable" :data="unBindData"
                                        tooltip-effect="dark" height="370px"
                                        style="width: 100%;  overflow-y: auto !important;"
                                        @checkbox-change="handleSelectionChange" @checkbox-all="handleSelectionChange">
                                        <vxe-column type="checkbox" width="55">
                                        </vxe-column>
                                        <vxe-column field="styleCode" title="系列编码" show-overflow="title" width="120">
                                        </vxe-column>
                                        <vxe-column field="styleCategory" title="系统类目" show-overflow="title">
                                        </vxe-column>
                                    </vxe-table>
                                    <vxe-table :scroll-y="{ gt: 200, enabled: true }" v-show="index == 1"
                                        :loading="seriesloading" :column-config="{ resizable: true }"
                                        :scroll-x="{ gt: 200, enabled: true }" ref="bindmultipleTable" :data="bindData"
                                        tooltip-effect="dark" height="370px"
                                        style="width: 100%;  overflow-y: auto !important;"
                                        @checkbox-change="bindhandleSelectionChange"
                                        @checkbox-all="bindhandleSelectionChange">
                                        <vxe-column type="checkbox" width="55">
                                        </vxe-column>
                                        <vxe-column field="styleCode" title="系列编码" show-overflow="title" width="120"
                                            :tooltip="true">
                                        </vxe-column>
                                        <vxe-column field="styleCategory" title="系统类目" show-overflow="title" width="130"
                                            :tooltip="true">
                                        </vxe-column>
                                        <vxe-column title="已绑类目" show-overflow="title" :tooltip="true">
                                            <template #default="{ row }">
                                                {{ row.mainCategory + (row.categoryLevel1 ? ('-' + row.categoryLevel1) :
                                                    '') + (row.categoryLevel2 ? ('-' + row.categoryLevel2) : '') }}
                                            </template>
                                        </vxe-column>
                                    </vxe-table>
                                </div>
                            </div>
                        </div>
                        <div class="father_top_right">
                            <div class="father_top_right_one">
                                <div class="father_top_right_one_top">一级类目</div>
                                <div class="father_top_right_one_bottom">
                                    <div v-for="(category, index) in categoryall.categoryone" :key="category"
                                        :class="{ 'father_top_right_one_bottom_one': true, 'activered': isActiveone(1, category) }"
                                        @click="getcategoryall(1, category)">{{ category }}</div>
                                </div>
                            </div>
                            <div class="father_top_right_two">
                                <div class="father_top_right_two_top">二级类目</div>
                                <div class="father_top_right_two_bottom">
                                    <div v-for="(category, index) in categoryall.categorytwo" :key="category"
                                        :class="{ 'father_top_right_two_bottom_one': true, 'activered': isActiveone(2, category) }"
                                        @click="getcategoryall(2, category)">{{ category }}</div>
                                </div>
                            </div>
                            <div class="father_top_right_three">
                                <div class="father_top_right_three_top">三级类目</div>
                                <div class="father_top_right_three_bottom">
                                    <div v-for="(category, index) in categoryall.categorythr" :key="category"
                                        :class="{ 'father_top_right_three_bottom_one': true, 'activered': isActiveone(3, category) }"
                                        @click="getcategoryall(3, category)">{{ category }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="father_bottom">
                        <div class="father_bottom_topWord">已选择系列编码</div>
                        <div style="width: 1176px">
                            <el-tag v-for="(item, index) in multipleSelection" :key="index" closable type=""
                                style="margin-right: 5px;margin-left: 5px;" @close="handleTagClose(item, index)">{{
                                    item.styleCode
                                }}</el-tag>
                        </div>
                    </div>
                </div>
                <div class="footer">
                    <el-button @click="customihandleClose()">取 消</el-button>
                    <el-button type="primary" @click="onSubmit">确 定</el-button>
                </div>
            </div>

            </template>
        </vxe-modal>

        <vxe-modal :loading="categorylistLoading" :title=categoryPopupname v-model="categoryPopupWindow" :width="1500"
            :height="600" draggable center transfer :close-on-click-modal="true" :mask-closable="true" marginSize='-500'
            @hide="categoryPopup">
            <template #default>
            <my-container style="height: 98%">

                <template #header>
                    <div style="height: 40px; margin-top: 5px; display: flex; align-items: center;margin-bottom: 30px;">
                        <el-date-picker style="width: 320px;margin-right:20px;" v-model="popupwindow.timerange"
                            type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                            start-placeholder="开始" end-placeholder="结束" :clearable="false"
                            :picker-options="pickerOptions" @change="imilarity"></el-date-picker>
                        <span style="margin-right:15px;">{{ activeName }}</span>
                        <span style="margin-right:20px;">
                            <el-select v-model="popupwindow.popupcategory" placeholder="请选择类目" @change="imilarity"
                                clearable filterable :collapse-tags="true">
                                <el-option v-for="(item) in activecategory" :key="item" :label="item" :value="item">
                                </el-option>
                            </el-select>
                        </span>
                        <span style="margin-right:15px;">平台</span>
                        <span style="margin-right:20px;">
                            <el-select v-model="popupwindow.platform" placeholder="请选择平台" :clearable="true"
                                :collapse-tags="true" filterable @change="imilarity" style="width: 100px">
                                <el-option v-for="item in platformList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </span>
                        <span style="margin-right:15px;">运营组</span>
                        <span style="margin-right:20px;">
                            <el-select v-model="popupwindow.groupId" style="width: 110px" placeholder="请选择运营组"
                                :clearable="true" @change="imilarity" :collapse-tags="true" filterable>
                                <el-option v-for="item in groupList" :key="item.key" :label="item.value"
                                    :value="item.key" />
                            </el-select>
                        </span>
                    </div>
                </template>
                <div style="height: 510px;">
                    <vxetablebase :id="'styleCodeReports202408041738_3'" ref="categorytable" v-if="isshowsummary" :that='that' :isIndex='false'
                        :hasexpand='false' :summaryarry='categorysummaryarry' :showsummary='isshowsummary'
                        :tableData='categorylist' :tableCols='categorytableCols' :isSelection="false" :border="true"
                        @sortchange='categorysortchange' :isSelectColumn="false" style="width:98%;margin: 0">
                    </vxetablebase>
                </div>

                <template #footer>
                    <my-pagination ref="pagerr" :total="categorytotal" @page-change="Pagechange"
                        @size-change="Sizechange" />
                </template>
            </my-container>
            </template>
        </vxe-modal>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import checkPermission from '@/utils/permission';
import dayjs from "dayjs";
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatLinkProCode, formatmoney } from "@/utils/tools";
import { rulePlatform } from "@/utils/formruletools";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import { batchAddProCodeSimilarity, getListByStyleCodeCost } from '@/api/operatemanage/base/product'
import { queryProCodeSimilarityAnalysis } from "@/api/order/procodesimilarity"
import buschar from '@/components/Bus/buschar'
import ProCodeBusinessStaffPlatForm from '@/views/order/ProCodeBusinessStaffPlatForm'
import { getProCodeBusinessStaffPlatForm } from '@/api/bookkeeper/reportday'
import { VXETable, VxeModalDefines } from 'vxe-table'
import {
    getLastUpdateTime,
    pageProCodeSimilarity,
    exportProCodeSimilarity,
    exportProCodeSimilarity1,
    exportProCodeSimilarityListNew,
    getProCodeSimilaritySummary,
    getProCodeSimilarityStateName,
    addTbProCodeSimilarityGrowSet,
    getProCodeSimilarityImportLogTime,
    getStyleCodePlatformHKDayCount
} from "@/api/order/procodesimilarity"
import SeriesGoods from './procodesimilarity/SeriesGoods.vue';
import MainProGoods from './procodesimilarity/MainProGoods.vue';
import SameProDetail from './procodesimilarity/SameProDetail.vue';
import proCodeSimilarityAnalysis from './procodesimilarity/ProCodeSimilarityAnalysis.vue';
import Daysturnoverdetail from './procodesimilarity/daysTurnoverDetail.vue'
import DayZZRateDetail from './procodesimilarity/dayZZRateDetail.vue'
import procodesimilaritystate from './procodesimilarity/procodesimilaritystate.vue'
import { getAllProBrand } from '@/api/inventory/warehouse'
// import yhcolumnmergeVue from '../../components/Table/yhcolumnmerge.vue';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllTbProCodeSimilarityByName, setStylesBrandProductCategory } from "@/api/order/procodesimilarity"
import { getBrandProductCategory } from '@/api/operatemanage/productalllink/LogisticsAnalyse.js'
import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
    var money = formatmoney(
        Math.abs(value) > 1 ? Math.round(value, 2) : Math.round(value, 1)
    );
    return money
};

var getPlatfromStr = function (row) {
    let str = "淘宝:" + (row.taoEarningsRate * 100).toFixed(2) + "%，"
        + "拼多多:" + (row.pinEarningsRate * 100).toFixed(2) + "%，"
        + "京东:" + (row.jdEarningsRate * 100).toFixed(2) + "%，"
        + "抖音:" + (row.dyEarningsRate * 100).toFixed(2) + "%，"
        + "苏宁:" + (row.snEarningsRate * 100).toFixed(2) + "%，"
        + "阿里巴巴:" + (row.aliEarningsRate * 100).toFixed(2) + "%"
    return str;
}

//日报列
const dayReportCols = [
    { istrue: true, summaryEvent: true, prop: 'orderCountDayReport', label: '订单量', sortable: 'custom', width: '70', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReport ? " " : row.orderCountDayReport, handle: (that, row) => that.Pananysis(row) },
    { istrue: true, summaryEvent: true, prop: 'wcOrderRate', label: '外仓率', sortable: 'custom', width: '70', permission: "prosameprofit", formatter: (row) => !row.wcOrderRate ? " " : (row.wcOrderRate * 100).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'orderCountDayReportSort', label: '订单排序', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReportSort ? " " : row.orderCountDayReportSort },
    { istrue: true, summaryEvent: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', permission: "prosameprofit", width: '100', formatter: (row) => !row.saleAmont ? " " : row.saleAmont.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit1', label: '订单毛利', sortable: 'custom', permission: "prosameprofit", width: '120', type: 'custom', tipmesg: '销售金额-销售成本', formatter: (row) => !row.profit1 ? " " : row.profit1.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit1Rate', label: '毛利率', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '订单毛利/销售金额', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate * 100).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'dkAmont', label: '平台扣点', sortable: 'custom', width: '120', type: 'custom', permission: "prosameprofit", tipmesg: '支付宝账单费用扣点，新上链接天猫-7.5%；C店2%；已有ID-上月月报百分比', formatter: (row) => !row.dkAmont ? " " : row.dkAmont.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'refundAmont', label: '总退款金额', sortable: 'custom', width: '140', permission: "prosameprofit", tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.refundAmont ? " " : row.refundAmont.toFixed(2) },

    { istrue: true, summaryEvent: true, prop: 'allMarketingCost', label: '总广告费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.allMarketingCost ? " " : row.allMarketingCost?.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'packageFee', label: '包装材料', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.packageFee ? " " : row.packageFee?.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'freightFeeTotal', label: '快递费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal?.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight?.toFixed(2) },

    {
        istrue: true, summaryEvent: true, prop: 'profit2', label: '毛二利润', sortable: 'custom', permission: "prosameprofit", width: '100', type: 'custom', tipmesg: '销售金额-销售成本-平台扣点-延迟发货扣款-营销费用-淘宝客-首单礼金-特殊单(刷单、补单、大灰熊)-包装材料-快递费',
        formatter: (row) => !row.profit2 ? " " : row.profit2?.toFixed(2)
    },
    { istrue: true, summaryEvent: true, prop: 'profit2Rate', label: '毛二利润率', sortable: 'custom', permission: "prosameprofit", width: '130', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.profit2Rate ? " " : (row.profit2Rate * 100).toFixed(2) + '%' },
    {
        istrue: true, summaryEvent: true, prop: 'profit3PredictRate', label: '毛三预估比例', type: 'custom', permission: "prosameprofit", tipmesg: '(空白链接ID成本+异常成本+补发成本+代发成本+采购运费+产品费用+工资+损耗)/销售金额',
        sortable: 'custom', width: '130', formatter: (row) => !row.profit3PredictRate ? " " : (row.profit3PredictRate * 100).toFixed(2) + '%'
    },
    { istrue: true, summaryEvent: true, prop: 'profit3PredictFee', label: '预估费用', type: 'custom', tipmesg: '毛三预估比例*销售金额', permission: "prosameprofit", sortable: 'custom', width: '130', formatter: (row) => !row.profit3PredictFee ? " " : row.profit3PredictFee?.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', width: '130', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "prosameprofit", formatter: (row) => !row.profit3 ? " " : row.profit3?.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit3Sort', label: '毛三排序', sortable: 'custom', width: '130', permission: "prosameprofit", formatter: (row) => !row.profit3Sort ? " " : row.profit3Sort },
    { istrue: true, summaryEvent: true, prop: 'earningsRate', label: '盈亏平衡率', width: '130', sortable: 'custom', type: 'custom', tipmesg: '销售成本/毛三利润', permission: "prosameprofit", formatter: (row) => !row.earningsRate ? " " : (row.earningsRate).toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'growRate', label: '系列增长率', type: 'custom', tipmesg: '系列增长率由人工手动设置，毛三利润率小于设定的增长率则会同步到系列编码增长率模块', sortable: 'custom', width: '130', permission: "prosameprofit", formatter: (row) => !row.growRate ? " " : (row.growRate).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'profit3Rate', label: '毛三利润率', type: 'custom', tipmesg: '毛三利润/销售金额', sortable: 'custom', width: '130', permission: "prosameprofit", formatter: (row) => !row.profit3Rate ? " " : (row.profit3Rate * 100).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '130', permission: "prosameprofit", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'shareFee', label: '公摊费', type: 'custom', tipmesg: '公摊费(%)*销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.shareFee ? " " : row.shareFee.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit33', label: '毛四利润', type: 'custom', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit33 ? " " : row.profit33.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit33Rate', label: '毛四利润率', type: 'custom', sortable: 'custom', width: '130', permission: "prosameprofit", formatter: (row) => !row.profit33Rate ? " " : (row.profit33Rate * 100).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate * 100).toFixed(2) + '%' },


];

const categorytableCols = [
    { istrue: true, fixed: 'left', prop: 'goodsCode', label: '系列编码', width: '200', sortable: 'custom', type: 'custom' },
    { istrue: true, fixed: 'left', prop: 'styleCategory', label: '类目', width: '150', sortable: 'custom', type: 'custom', formatter: (row) => row.mainCategory + row.categoryLevel1 + row.categoryLevel2 },
    { istrue: true, fixed: 'left', prop: 'platform', label: '平台', width: '200', type: 'custom', tipmesg: '淘宝、拼多多盈利率', sortable: 'custom', formatter: (row) => "淘宝:" + (row.taoEarningsRate * 100).toFixed(2) + "%，" + "拼多多:" + (row.pinEarningsRate * 100).toFixed(2) + "%" },
    { istrue: true, fixed: 'left', prop: 'groupId', label: '运营组', type: 'custom', tipmesg: '主链接ID的所属运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'orderCount', label: '近30天订单数', permission: "prosameprofit", width: '110', sortable: 'custom', formatter: (row) => myformatmoney(row.orderCount) },
    { istrue: true, prop: 'salesQty', label: '近30天销量', permission: "prosameprofit", width: '90', sortable: 'custom', formatter: (row) => myformatmoney(row.salesQty) },
    { istrue: true, prop: 'amount', label: '近30天销售额', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.amount) },
    { istrue: true, prop: 'cost', label: '近30天成本', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.cost) },
    { istrue: true, prop: 'profit', label: '近30天毛利润', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.profit) },
    { istrue: true, prop: 'createdTime', label: '计算时间', permission: "prosameprofit", width: '160', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'invAmountTotal', label: '库存资金', permission: "prosameprofit", type: 'custom', tipmesg: '系列编码所有商品编码的库存资金之和。商品库存资金=采购建议的编码库存数（即实际可用数+采购在途+进货仓库存） * 商品的成本价', width: '100', sortable: 'custom', formatter: (row) => myformatmoney(row.invAmountTotal) },
    { istrue: true, prop: 'invAmountTotalSort', label: '库存资金占用排序', permission: "prosameprofit", type: 'custom', width: '130', sortable: 'custom', formatter: (row) => !row.invAmountTotalSort ? " " : row.invAmountTotalSort },
    { istrue: true, prop: 'orderCountDayReport', label: '订单量', sortable: 'custom', width: '70', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReport ? " " : row.orderCountDayReport, handle: (that, row) => that.Pananysis(row) },
    { istrue: true, prop: 'orderCountDayReportSort', label: '订单排序', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReportSort ? " " : row.orderCountDayReportSort },
    { istrue: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', permission: "prosameprofit", width: '100', formatter: (row) => !row.saleAmont ? " " : row.saleAmont.toFixed(2) },
    { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost.toFixed(2) },
    { istrue: true, prop: 'profit1', label: '订单毛利', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '销售金额-销售成本', formatter: (row) => !row.profit1 ? " " : row.profit1.toFixed(2) },
    { istrue: true, prop: 'profit1Rate', label: '毛利率', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '订单毛利/销售金额', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'dkAmont', label: '平台扣点', sortable: 'custom', width: '80', type: 'custom', permission: "prosameprofit", tipmesg: '支付宝账单费用扣点，新上链接天猫-7.5%；C店2%；已有ID-上月月报百分比', formatter: (row) => !row.dkAmont ? " " : row.dkAmont.toFixed(2) },
    { istrue: true, prop: 'refundAmont', label: '总退款金额', sortable: 'custom', width: '70', permission: "prosameprofit", tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.refundAmont ? " " : row.refundAmont.toFixed(2) },
    { istrue: true, prop: 'expressDeductAmount', label: '违规总扣款', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'custom', tipmesg: '违规总扣款', formatter: (row) => !row.expressDeductAmount ? " " : row.expressDeductAmount.toFixed(2) },
    { istrue: true, prop: 'allMarketingCost', label: '总广告费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.allMarketingCost ? " " : row.allMarketingCost?.toFixed(2) },
    { istrue: true, prop: 'packageFee', label: '包装材料', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.packageFee ? " " : row.packageFee?.toFixed(2) },
    { istrue: true, prop: 'freightFeeTotal', label: '快递费', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.freightFeeTotal ? " " : row.freightFeeTotal?.toFixed(2) },
    { istrue: true, prop: 'freightAvgWeight', label: '快递均重', sortable: 'custom', permission: "prosameprofit", width: '80', formatter: (row) => !row.freightAvgWeight ? " " : row.freightAvgWeight?.toFixed(2) },
    {
        istrue: true, prop: 'profit2', label: '毛二利润', sortable: 'custom', permission: "prosameprofit", width: '100', type: 'custom', tipmesg: '销售金额-销售成本-平台扣点-延迟发货扣款-营销费用-淘宝客-首单礼金-特殊单(刷单、补单、大灰熊)-包装材料-快递费',
        formatter: (row) => !row.profit2 ? " " : row.profit2?.toFixed(2)
    },
    { istrue: true, prop: 'profit2Rate', label: '毛二利润率', sortable: 'custom', permission: "prosameprofit", width: '130', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.profit2Rate ? " " : (row.profit2Rate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'profit3', label: '毛三利润', sortable: 'custom', width: '100', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "prosameprofit", formatter: (row) => !row.profit3 ? " " : row.profit3?.toFixed(2) },
    { istrue: true, prop: 'profit3Sort', label: '毛三排序', sortable: 'custom', width: '100', permission: "prosameprofit", formatter: (row) => !row.profit3Sort ? " " : row.profit3Sort },
    { istrue: true, prop: 'profit3Rate', label: '毛三利润率', type: 'custom', tipmesg: '毛三利润/销售金额', sortable: 'custom', width: '130', permission: "prosameprofit", formatter: (row) => !row.profit3Rate ? " " : (row.profit3Rate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'shareRate', label: '公摊费率', type: 'custom', tipmesg: '明细2/销售金额', sortable: 'custom', width: '100', permission: "prosameprofit", formatter: (row) => !row.shareRate ? " " : (row.shareRate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'shareFee', label: '公摊费', type: 'custom', tipmesg: '公摊费(%)*销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.shareFee ? " " : row.shareFee.toFixed(2) },
    { istrue: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
    { istrue: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '80', permission: "prosameprofit", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate * 100).toFixed(2) + '%' },
]

//商品客户咨询列
const customerCols = [
    { istrue: true, summaryEvent: true, prop: 'inquiries', label: '咨询量', type: 'custom', tipmesg: '系列编码所有链接在查询日期范围内的顾客咨询量之和', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiries ? " " : row.inquiries },
    { istrue: true, summaryEvent: true, prop: 'inquiriesSuccessRate', label: '咨询量转化率', type: 'custom', tipmesg: '成功的咨询量/总咨询量', sortable: 'custom', permission: "", width: '140', formatter: (row) => !row.inquiriesSuccessRate ? " " : (row.inquiriesSuccessRate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'sendOrderCount', label: '发货订单数', type: 'custom', width: '80', },
    { istrue: true, prop: 'damagedOrderCount', label: '破损订单数', type: 'custom', width: '80', },
    { istrue: true, prop: 'damagedOrderRatio', label: '损耗订单比', type: 'custom', width: '80' },
    { istrue: true, prop: 'goodsCostPrice', label: '商品单价', type: 'custom', width: '80', },
    { istrue: true, prop: 'damagedAmount', label: '损耗总费用', type: 'custom', width: '80', },
];

const paymentTableCols = [
    { istrue: true, fixed: true, prop: 'platform', label: '平台', formatter: (row) => row.platformName ? row.platformName : '' },
    { istrue: true, fixed: true, prop: 'styleCode', label: '系列编码', },
    { istrue: true, fixed: true, prop: 'hkDayCount', label: '回款天数', formatter: (row) => row.hkDayCount !== null ? row.hkDayCount.toFixed(2) : '' },
]

const tableCols = [
    { istrue: true, fixed: 'left', prop: 'goodsCode', label: '系列编码', width: '200', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, "tabSame") },
    // { isTrue: true, fixed: true, prop: 'brandCategory', label: '公司类目', width: '200', sortable: 'custom', type: 'companyclick', seriesList: ['mainCategory', 'categoryLevel1', 'categoryLevel2'], formatter: (row) => { return row.profit + '-' + row.invAmountTotalPredict + '-' + row.invAmountTotal }, handle: (that, row, field, val) => that.classofcompanyClick(row, field, val), },
    // { istrue: true, fixed: true, prop: 'styleCategory', label: '类目', width: '150', sortable: 'custom', type: 'custom' },
    { istrue: true,  prop: 'bzCategory', label: '经营大类', width: '150', sortable: 'custom', },
    { istrue: true,  prop: 'bzCategory1', label: '一级类目', width: '150', sortable: 'custom', },
    { istrue: true,  prop: 'bzCategory2', label: '二级类目', width: '150', sortable: 'custom', },
    //{istrue:true,prop:'goodsName',label:'系列名称', width:'100'},主链接ID的所属平台
    { istrue: true,  prop: 'goodsImage', label: '图片', width: '60', type: 'images', goods: { code: 'goodsCode', name: 'goodsName' } },
    { istrue: true,  prop: 'platform', label: '平台', width: '200', type: 'custom', tipmesg: '盈利率', sortable: 'custom', formatter: (row) => getPlatfromStr(row) },
    { istrue: true,  prop: 'proCode1', fix: true, label: '趋势图', permission: "prosameprofit", style: "color:red;cursor:pointer;", width: '70', formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showprchart(row.goodsCode) },
    //{istrue:true,prop:'shopCode',label:'店铺', width:'200',sortable:'custom',formatter:(row)=>row.shopName},
    { istrue: true,  prop: 'groupId', label: '运营组', type: 'custom', tipmesg: '主链接ID的所属运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true,  prop: 'brandName', label: '采购', type: 'custom', tipmesg: '对应产品下的采购人员', width: '100', formatter: (row) => row.brandName },
    //{istrue:true,prop:'proCode',label:'产品ID', width:'130',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
    { istrue: true, prop: 'customerGroups', label: '客服组', sortable: 'custom', permission: "setcusservicegroup", width: '80', type: 'custom', tipmesg: '选择客服组', type: 'click', handle: (that, row) => that.setcustomergroup(row) },
    { istrue: true, prop: 'state', label: '系列状态', sortable: 'custom', width: '80', type: 'custom', type: 'click', handle: (that, row) => that.setprostate(row) },

    { istrue: true, prop: 'zjhbRate', label: '资金回报率', sortable: 'custom', permission: "prosameprofit", tipmesg: '毛三利润/(在仓资金+采购在途资金+销售在途)', width: '110', formatter: (row) => row.zjhbRate !== null ? row.zjhbRate.toFixed(2) + '%' : '' },
    { istrue: true, prop: 'zjzzRate', label: '资金周转率', sortable: 'custom', permission: "prosameprofit", tipmesg: '系列编码所有链接是否确认收货、确认收货日期(付款到收货)天数、细分到平台', width: '110', formatter: (row) => row.zjzzRate !== null ? row.zjzzRate.toFixed(2) + '%' : '' },
    { istrue: true, prop: 'hkDayCount', label: '回款天数', sortable: 'custom', permission: "prosameprofit", width: '80', type: 'click', formatter: (row) => row.hkDayCount.toFixed(2), handle: (that, row) => that.openPaymentDialog(row) },
    { istrue: true, summaryEvent: true, prop: 'expressDeductAmount', label: '违规总扣款', sortable: 'custom', permission: "prosameprofit", width: '130', type: 'custom', tipmesg: '违规总扣款', formatter: (row) => !row.expressDeductAmount ? "0" : row.expressDeductAmount.toFixed(2) },
    { istrue: true, summaryEvent: true, prop: 'refundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', permission: "prosameprofit", width: '130', formatter: (row) => !row.refundAmontBeforeRate ? "0.00%" : (row.refundAmontBeforeRate * 100).toFixed(2) + '%' },
    { istrue: true, summaryEvent: true, prop: 'refundAmontAfterRate', label: '发货后退款率', sortable: 'custom', permission: "prosameprofit", width: '130', formatter: (row) => !row.refundAmontAfterRate ? "0.00%" : (row.refundAmontAfterRate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'mainStyleCodeCostRate', label: '主卖占比', sortable: 'custom', permission: "prosameprofit", width: '100', formatter: (row) => !row.mainStyleCodeCostRate ? "0.00%" : (row.mainStyleCodeCostRate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'notMainStyleCodeCostRate', label: '辅卖占比', sortable: 'custom', permission: "prosameprofit", width: '100', formatter: (row) => !row.notMainStyleCodeCostRate ? "0.00%" : (row.notMainStyleCodeCostRate * 100).toFixed(2) + '%' },
    { istrue: true, prop: 'leaseSaleStyleCodeCostRate', label: '45天成本借卖比例', sortable: 'custom', permission: "prosameprofit", width: '150', formatter: (row) => row.leaseSaleStyleCodeCostRate !== null ? row.leaseSaleStyleCodeCostRate.toFixed(4) : '' },

    { istrue: true, prop: 'stateCreatedTime', label: '状态绑定时间', sortable: 'custom', tipmesg: '系列状态最晚绑定时间', width: '150', type: 'custom', formatter: (row) => !row.stateCreatedTime ? " " : row.stateCreatedTime },
    //{istrue:true,prop:'state',label:'销售月份',sortable:'custom', width:'80',type:'custom',type:'click',handle:(that,row)=>that.setpromonth(row)},
    { istrue: true, label: '毛三利润走势', type: 'star',width:'130', permission: "prosameprofit", tipmesg: '毛三利润近四周走势，红色代表正利润，绿色代表负利润，十万以上则单位为萬，X则代表暂未利润' },
    { istrue: true, label: '毛四利润走势', type: 'star4',width:'130', permission: "prosameprofit", tipmesg: '毛四利润近四周走势，红色代表正利润，绿色代表负利润，十万以上则单位为萬，X则代表暂未利润' },
    { istrue: true, prop: 'oneDayZZRate', label: '1天周转天数',width:'130', type: 'click', permission: "prosameprofit", tipmesg: '', formatter: (row) => row.oneDayZZRate.toFixed(2), handle: (that, row) => that.showDayZZRateDetail(row, 1) },
    { istrue: true, prop: 'threeDayZZRate', label: '3天周转天数',width:'130', type: 'click', permission: "prosameprofit", tipmesg: '', formatter: (row) => row.threeDayZZRate.toFixed(2), handle: (that, row) => that.showDayZZRateDetail(row, 3) },
    { istrue: true, prop: 'orderCount', label: '近30天订单数', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.orderCount) },
    { istrue: true, prop: 'salesQty', label: '近30天销量', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.salesQty) },
    { istrue: true, prop: 'amount', label: '近30天销售额', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.amount) },
    { istrue: true, prop: 'cost', label: '近30天成本', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.cost) },
    { istrue: true, prop: 'goodsCodeQty', label: '编码数', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'invAmount', label: '产品编码库存资金', width: '160', sortable: 'custom', formatter: (row) => myformatmoney(row.invAmount) },
    { istrue: true, prop: 'profit', label: '近30天毛利润', permission: "prosameprofit", width: '130', sortable: 'custom', formatter: (row) => myformatmoney(row.profit) },
    { istrue: true, prop: 'createdTime', label: '计算时间', permission: "prosameprofit", width: '160', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'inventoryFundsAmount', label: '库存资金(新)', permission: "prosameprofit", type: 'custom', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'procureInTransit', label: '采购在途', permission: "prosameprofit", type: 'custom', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'saleInTransit', label: '销售在途', permission: "prosameprofit", type: 'custom', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'invAmountTotal', label: '库存资金', permission: "prosameprofit", type: 'custom', tipmesg: '系列编码所有商品编码的库存资金之和。商品库存资金=采购建议的编码库存数（即实际可用数+采购在途+进货仓库存） * 商品的成本价', width: '100', sortable: 'custom', formatter: (row) => myformatmoney(row.invAmountTotal) },
    { istrue: true, prop: 'invAmountTotalSort', label: '库存资金占用排序', permission: "prosameprofit", type: 'custom', width: '140', sortable: 'custom', formatter: (row) => !row.invAmountTotalSort ? " " : row.invAmountTotalSort },
    { istrue: true, prop: 'invAmountTotalPredict', label: '库存资金预估', permission: "prosameprofit", type: 'custom', tipmesg: '系列编码所有商品编码的预估库存资金之和。商品预估库存资金=采购建议中商品的预计日均销量 * 15天 * 商品成本价', width: '140', sortable: 'custom', formatter: (row) => myformatmoney(row.invAmountTotalPredict) },
    //{istrue:true,prop:'turnoverRate',label:'周转天数', width:'100',sortable:'custom',formatter:(row)=> !row.turnoverRate?"0" : row.turnoverRate.toFixed(2),type:'click',handle:(that,row)=>that.showTurnover(row)},
].concat(dayReportCols).concat(customerCols);
const tableHandles1 = [
    { label: "导出（旧）", handle: (that) => that.onExport() },
    { label: "导出（新）", handle: (that) => that.newonExport() },
    { label: "导出（与列表一致）", handle: (that) => that.allListExport() },
    { label: "批量设置耗材费用", handle: (that) => that.onHand(1) },
    { label: "批量设置增长率", handle: (that) => that.onHand(2) },
];

const cityOptions = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
export default {
    name: 'Roles',
    components: { cesTable, MyContainer, MyConfirmButton, SeriesGoods, MainProGoods, SameProDetail, proCodeSimilarityAnalysis, buschar, Daysturnoverdetail, procodesimilaritystate, ProCodeBusinessStaffPlatForm, DayZZRateDetail, vxetablebase },
    data() {
        return {
            paymentTableCols,
            loading: false,
            openPaymentData: [],
            categorytableCols,
            isshowsummary: false,
            tobebound: [],
            bound: [],
            activeTab: '0',
            categorypager: { OrderBy: "salesQty", IsAsc: false, currentPage: 1, pageSize: 50, },
            categorysummaryarry: {},
            categorylistLoading: true,
            categorylist: [],
            categorytotal: 0,
            categorysels: [],
            popupwindow: {
                popupcategory: null,
                timerange: [],
                groupId: null,
                platform: null,
            },
            popupcategoryone: null,
            popupcategorytwo: null,
            popupcategorythr: null,
            activecategory: [],
            goryalldataone: [],
            goryalldatatwo: [],
            goryalldatathree: [],
            seriesloading: false,
            currentData: [],
            unBindData: [],//未绑定
            bindData: [],//已绑定
            categoryall: {
                categoryone: [],
                categorytwo: [],
                categorythr: [],
            },
            modelcategoryall: {
                modelcategoryone: null,
                modelcategorytwo: null,
                modelcategorythr: null,
            },
            seriesName: '',
            index: 0,
            categoryPopupname: null,
            categoryPopupWindow: false,
            multipleSelection: [],
            activeName: null,
            seriesCodingCategory: false,
            buessness: { visible: false, title: "", data: [] },
            that: this,
            formtitle: "耗材费用",
            filter: {
                platform: null,
                shopCode: "",
                proCode: null,
                goodsCode: null,
                groupId: null,
                brandId: null,
                similarity: 0,
                days: null,
                profit2UnZero: null,
                profit3UnZero: null,
                profit33UnZero: null,
                styleCategory: null,
                state: null,
                startDate: null,
                endDate: null,
                timerange: [],
                bzCategory: null,
                bzCategory1: null,
                bzCategory2: null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "salesQty", IsAsc: false },
            tableCols: tableCols,
            tableHandles: tableHandles1,
            cities: cityOptions,
            checkboxGroup1: [],
            platformList: [],
            prosimstatelist: [],
            shopList: [],
            groupList: [],
            brandlist: [],
            importtimelist: [],
            total: 0,
            sels: [],
            deletegroupdialogVisible: false,
            importtimedialogVisible: false,
            listLoading: false,
            pageLoading: false,
            dialogDrVisible: false,
            dialoganalysisVisible: false,
            dialodayssisVisible: false,
            addFormVisible: false,
            addLoading: false,
            editparmVisible: false,
            editparmLoading: false,
            isshowstate: false,
            isshowtime: false,
            isshowmonth: false,
            lastUpdateTime: null,
            drparamProCode: '',
            handletype: 0,
            detail: {
                visible: false,
                filter: {
                    parentId: null,
                    proCode: null,
                    status: null,
                    platform: null,
                    shopId: "",
                    similarity: null,

                    seriesCode: null,
                    goodsCode: null,
                    startDate: null,
                    endDate: null,
                    timerange: [],
                    styleCodeStartDate: null,
                    styleCodeEndDate: null,
                    styleCodeTimerange: []
                },
                selRow: {},
                srcList: [],
                tabName: "tabSame",
                customRowStyle: function (data) {
                    if (data.row.isMain) {
                        return { color: 'red' };
                    }
                },
            },
            filterList: {
                bussinessCategoryNames: [],
                categoryName1s: [],
                categoryName2s: []
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            // autoform:{
            //          fApi:{},
            //          options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
            //          rule:[{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
            //                {type:'select',field:'styleCode',title:'系列编码', validate: [{type: 'string', required: true, message:'请选择系列编码'}],value: "",options: [],
            //                                           props:{filterable:true,allowCreate:false,clearable:true,remote:true,remoteMethod:(parm) => this.remoteSearchUser(parm)}
            //                                           },
            //                 {type:'select',field:'packCost1',title:'包装费', validate: [{type: 'string', required: true, message:'请选择包装费'}],value: "",options: [],
            //                                           props:{filterable:true,allowCreate:false,clearable:true,remote:true,remoteMethod:(parm) => this.remoteSearchStyleCode(parm)}
            //                                           },
            //                //{type:'InputNumber',field:'packCost',title:'耗材费用',value: null,props:{min:0,precision:3,step:0.1},col:{span:6}},
            //               //  {type: "DatePicker",field: "date",title: "具体日期(二选一)", props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},},
            //               //  {type: "DatePicker",field: "beginDate",title: "开始日期(二选一)", props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},},
            //               //  {type:'DatePicker',field:'endDate',title:'结束日期(二选一)',props: {type: "date",format: "yyyy-MM-dd",placeholder:"请选择时间",},}
            //               ]
            //     },
            autoform: {
                fApi: {},
                rule: [],
                options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } }
            },
            buscharDialog: { visible: false, title: "", data: [] },
            dialogDayZZRatedetailVisible: false,
            dayZZRatedetail: {
                filter: {
                    type: 1,
                    seriesCode: null
                }
            },
            isShowFiled: null,
            openPaymentVisible: false
        };
    },
    async mounted() {
        if (this.$route.query && this.$route.query.styleCode) {
            this.filter.goodsCode = this.$route.query.styleCode
        }
        this.init()
        await this.setPlatform();
        await this.setGroupSelect();
        await this.getLastUpdateTime();
        // await this.getlist();
        await this.getprosimstatelist()
    },
    methods: {
        init() {
            this.filter.startDate = formatTime(dayjs().subtract(31, 'day'), "YYYY-MM-DD");
            this.filter.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filter.timerange = [this.filter.startDate, this.filter.endDate];
        },
        // changeCategory(e,val){
        //   if (e) {
        //     if (val == 1) {
        //       this.filterList.categoryName1s = []
        //       this.filterList.categoryName2s = []
        //       this.filter.bzCategory1 = null
        //       this.filter.bzCategory2 = null
        //     }else if(val == 2){
        //       this.filterList.categoryName1s = []
        //       this.filterList.categoryName2s = []
        //     }else{
        //       this.filter.bzCategory2 = e
        //     }
        //   }else{
        //     if (val == 1) {
        //       this.filterList.bussinessCategoryNames = []
        //       this.filterList.categoryName1s = []
        //       this.filterList.categoryName2s = []
        //       this.filter.bzCategory1 = null
        //       this.filter.bzCategory2 = null
        //     }else if(val == 2){
        //       this.filterList.categoryName1s = []
        //       this.filterList.categoryName2s = []
        //       this.filter.bzCategory2 = null
        //     }
        //   }
        // },
        async openPaymentDialog(row) {
            this.loading = true
            const params = {
                startDate: this.filter.startDate,
                endDate: this.filter.endDate,
                goodsCode: row.goodsCode,
            }
            const { data, success } = await getStyleCodePlatformHKDayCount(params)
            if (success) {
                this.openPaymentData = data
                this.openPaymentVisible = true
            } else {
                this.$message.error('获取数据失败')
            }
            this.loading = false
        },
        remoteMethodBusinessCategory(query) {
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            this.options = [];
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
                this.searchloading = false
                this.filterList.bussinessCategoryNames = res.data
            }, 200)
        },
        remoteMethodCategoryName1s(query) {
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            this.options = [];
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
                this.searchloading = false
                this.filterList.categoryName1s = res.data
            }, 200)
        },
        remoteMethodCategoryName2s(query) {
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            this.options = [];
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
                this.searchloading = false
                this.filterList.categoryName2s = res.data
            }, 200)
        },
        categoryPopup() {
            this.popupwindow.groupId = null
            this.popupwindow.platform = null
        },
        handleTagClose(val, tag) {
            this.bound = this.bound.filter(item => item.styleCode !== val.styleCode);
            this.tobebound = this.tobebound.filter(item => item.styleCode !== val.styleCode);
            this.multipleSelection = this.multipleSelection.filter(item => item.styleCode !== val.styleCode);

            // 取消当前表格中的选择
            let _this = this;
            this.unBindData.forEach((item) => {
                if (item.styleCode == val.styleCode) {
                    _this.$refs.multipleTable.setCheckboxRow(item, false);
                }
            });

            this.bindData.forEach((item) => {
                if (item.styleCode === val.styleCode) {
                    _this.$refs.bindmultipleTable.setCheckboxRow(item, false);
                }
            });
        },
        customihandleClose() {
            if (this.multipleSelection.length > 0) {
                this.$confirm("类目已修改，点击确定则修改，点击取消则放弃修改", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    appendToBody: true,
                    zIndex: 999999
                }).then(async () => {
                    this.customihandle()
                }).catch(() => {
                    this.seriesCodingCategory = true;
                });
            } else {
                this.customihandle()
            }
        },
        customihandle() {
            this.seriesName = ''
            this.currentData = []
            this.multipleSelection = []
            this.categoryall.categoryone = []
            this.categoryall.categorytwo = [];
            this.categoryall.categorythr = [];
            this.seriesCodingCategory = false
            this.$forceUpdate();
        },
        //确定保存
        async onSubmit(val) {
            if (this.multipleSelection.length == 0) {
                const h = this.$createElement;
                this.$message({
                    message: h('p', null, [
                        h('span', null, '请选择数据编码 '),
                        h('i', { style: 'z-index: 3000; color: grey' })
                    ])
                });
                return
            }
            if (this.multipleSelection.length != 0 && this.modelcategoryall.modelcategoryone == null) {
                const h = this.$createElement;
                this.$message({
                    message: h('p', null, [
                        h('span', null, '请先选择类目 '),
                        h('i', { style: 'z-index: 3000; color: grey' })
                    ])
                });
                return
            }
            if (val == 1) {
                this.handleSave()
            } else {
                this.$confirm("是否修改类目，点击确定即为修改成功", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    appendToBody: true,
                    zIndex: 999999
                })
                    .then(async () => {
                        this.handleSave()
                    })
            }
        },
        // 将保存逻辑单独封装成一个方法
        async handleSave() {
            var multipleSelec = [];
            this.multipleSelection.forEach((item) => {
                if (item.styleCode) {
                    multipleSelec.push(item.styleCode);
                }
            });
            const params = {
                styleCodes: multipleSelec,
                mainCategory: this.modelcategoryall.modelcategoryone,
                categoryLevel1: this.modelcategoryall.modelcategorytwo,
                categoryLevel2: this.modelcategoryall.modelcategorythr
            };
            const { data, success } = await setStylesBrandProductCategory(params);
            if (success) {
                this.$message({ message: '保存成功!', type: 'success' });
                this.customihandle();
                this.onSearch();
            }
        },
        //类目选中颜色
        isActiveone(type, name) {
            if (type == 1) {
                return this.goryalldataone[0] === type && this.goryalldataone[1] === name;
            } else if (type == 2) {
                return this.goryalldatatwo[0] === type && this.goryalldatatwo[1] === name;
            } else if (type == 3) {
                return this.goryalldatathree[0] === type && this.goryalldatathree[1] === name;
            }
        },
        //类目数据
        async getcategoryall(type, name) {
            if (type == 1) {
                this.modelcategoryall.modelcategoryone = null
                this.modelcategoryall.modelcategoryone = name
                this.goryalldataone = [type, name]
            } else if (type == 2) {
                this.modelcategoryall.modelcategorytwo = null
                this.modelcategoryall.modelcategorytwo = name
                this.goryalldatatwo = [type, name]
            } else if (type == 3) {
                this.modelcategoryall.modelcategorythr = null
                this.modelcategoryall.modelcategorythr = name
                this.goryalldatathree = [type, name]
            }
            let res = await getBrandProductCategory({ level: type, parentName: name });
            if (res?.success) {
                if (type == 0) {
                    this.categoryall.categorytwo = [];
                    this.categoryall.categorythr = [];
                    this.categoryall.categoryone = res.data;
                } else if (type == 1) {
                    this.categoryall.categorythr = [];
                    this.categoryall.categorytwo = res.data;
                } else if (type == 2) {
                    this.categoryall.categorythr = res.data;
                }
            }
            this.$forceUpdate();
        },
        //已绑定，待绑定页签切换
        getProps(e) {
            this.seriesloading = true;
            this.index = e;
            this.seriesName = '';
            // this.currentData = [];

            // if (e == 0) {
            //   this.currentData = this.unBindData;
            //   this.setTableSelection(this.tobebound);
            // } else if (e == 1) {
            //   this.currentData = this.bindData;
            //   this.setTableSelection(this.bound);
            // }
            this.seriesloading = false;
            this.$forceUpdate();
        },
        setTableSelection(items) {
            this.$nextTick(() => {
                this.$refs.multipleTable.clearSelection(); // 清除之前的勾选状态
                items.forEach((item) => {
                    const foundItem = this.currentData.find(dataItem => dataItem.styleCode === item.styleCode);
                    if (foundItem) {
                        this.$refs.multipleTable.toggleRowSelection(foundItem, true); // 设置复选框勾选
                    }
                });
            });
        },
        //系列编码类目表格全选
        handleSelectionChange() {
            const val = this.$refs.multipleTable.getCheckboxRecords()
            const targetArray = this.index === 0 ? this.tobebound : this.bound;
            const otherArray = this.index === 0 ? this.bound : this.tobebound;
            for (let i = this.multipleSelection.length - 1; i >= 0; i--) {
                const item = this.multipleSelection[i];
                const notInTarget = !val.some(valItem => valItem.styleCode === item.styleCode);
                const notInOther = !otherArray.some(otherItem => otherItem.styleCode === item.styleCode);
                if (notInTarget && notInOther) {
                    this.multipleSelection.splice(i, 1);
                }
            }
            this.index === 0 ? this.tobebound = val : this.bound = val;
            if (this.index === 0) {
                this.tobebound.forEach((item) => {
                    if (this.multipleSelection.indexOf(item) === -1) {
                        this.multipleSelection.push(item);
                    }
                });
            } else if (this.index === 1) {
                this.bound.forEach((item) => {
                    if (this.multipleSelection.indexOf(item) === -1) {
                        this.multipleSelection.push(item);
                    }
                });
            }
        },
        bindhandleSelectionChange() {
            const val = this.$refs.bindmultipleTable.getCheckboxRecords()
            const targetArray = this.index === 0 ? this.tobebound : this.bound;
            const otherArray = this.index === 0 ? this.bound : this.tobebound;
            for (let i = this.multipleSelection.length - 1; i >= 0; i--) {
                const item = this.multipleSelection[i];
                const notInTarget = !val.some(valItem => valItem.styleCode === item.styleCode);
                const notInOther = !otherArray.some(otherItem => otherItem.styleCode === item.styleCode);
                if (notInTarget && notInOther) {
                    this.multipleSelection.splice(i, 1);
                }
            }
            this.index === 0 ? this.tobebound = val : this.bound = val;
            if (this.index === 0) {
                this.tobebound.forEach((item) => {
                    if (this.multipleSelection.indexOf(item) === -1) {
                        this.multipleSelection.push(item);
                    }
                });
            } else if (this.index === 1) {
                this.bound.forEach((item) => {
                    if (this.multipleSelection.indexOf(item) === -1) {
                        this.multipleSelection.push(item);
                    }
                });
            }
        },
        //新增
        async onAdd() {
            this.bindingSeriescoding()
            this.getcategoryall(0, '');
            this.seriesCodingCategory = true
            this.$forceUpdate();
        },
        //系列编码数据
        async bindingSeriescoding(e) {
            this.seriesloading = true
            const params = { name: this.seriesName };
            const { data, success } = await getAllTbProCodeSimilarityByName(params)
            if (success) {
                this.unBindData = []
                this.bindData = []
                this.currentData = []
                this.unBindData = data.unBindData
                this.bindData = data.bindData
                if (this.index == 0 && e == 1) {
                    this.currentData = data.unBindData;
                    this.unBindData.forEach(unBindItem => {
                        this.multipleSelection.forEach(selectionItem => {
                            if (unBindItem.styleCode == selectionItem.styleCode) {
                                this.$nextTick(() => {
                                    let _this = this;
                                    _this.$refs.multipleTable.setCheckboxRow(unBindItem, true);
                                })
                            }
                        });
                    });
                } else if (this.index == 1 && e == 1) {
                    this.currentData = data.bindData
                    this.bindData.forEach(bindData => {
                        this.multipleSelection.forEach(selectionItem => {
                            if (bindData.styleCode == selectionItem.styleCode) {
                                this.$nextTick(() => {
                                    let _this = this;
                                    _this.$refs.bindmultipleTable.setCheckboxRow(bindData, true)
                                })
                            }
                        });
                    });
                }
                this.seriesloading = false
            }
        },
        //列表类目
        async classofcompanyClick(row, field, val) {
            this.popupwindow.timerange = this.filter.timerange
            this.categoryPopupWindow = true
            this.categorylistLoading = true
            var name = null
            this.popupwindow.popupcategory = null
            this.isShowFiled = field
            if (field === 'mainCategory') {
                this.categoryPopupname = '一级类目弹窗'
                this.activeName = '一级类目'
                var type = 0
                var name = ''
                this.popupwindow.popupcategory = row.mainCategory
            } else if (field === 'categoryLevel1') {
                this.categoryPopupname = '二级类目弹窗'
                this.activeName = '二级类目'
                var type = 1
                var name = row.mainCategory
                this.popupwindow.popupcategory = row.categoryLevel1
            } else if (field === 'categoryLevel2') {
                this.categoryPopupname = '三级类目弹窗'
                this.activeName = '三级类目'
                var type = 2
                var name = row.categoryLevel1
                this.popupwindow.popupcategory = row.categoryLevel2
            }
            this.popupcategoryone = row.mainCategory
            this.popupcategorytwo = row.categoryLevel1
            this.popupcategorythr = row.categoryLevel2
            // this.popupwindow.popupcategory = name
            const { data, success } = await getBrandProductCategory({ level: type, parentName: name })
            if (success) {
                this.activecategory = data
            }
            this.imilarity()
        },
        //每页数量改变
        Sizechange(val) {
            this.categorypager.currentPage = 1;
            this.categorypager.pageSize = val;
            this.imilarity()
        },
        //当前页改变
        Pagechange(val) {
            this.categorypager.currentPage = val;
            this.imilarity()
        },
        async imilarity() {
            var param = this.getCondition();
            if (this.activeName == '一级类目') {
                this.popupcategoryone = this.popupwindow.popupcategory
            } else if (this.activeName == '二级类目') {
                this.popupcategorytwo = this.popupwindow.popupcategory
            } else if (this.activeName == '三级类目') {
                this.popupcategorythr = this.popupwindow.popupcategory
            }
            param.startDate = this.popupwindow.timerange[0]
            param.endDate = this.popupwindow.timerange[1]
            const params = { ...param, mainCategory: this.popupcategoryone, categoryLevel1: this.popupcategorytwo, categoryLevel2: this.popupcategorythr, ...this.popupwindow, ...this.categorypager }
            this.categorylistLoading = true
            const res = await pageProCodeSimilarity(params)
            if (!res?.success) {
                return;
            }
            this.categorytotal = res.data.total;
            const data = res.data.list;
            data.forEach((d) => {
                d._loading = false;
            });
            data.forEach(item => {
                if (this.isShowFiled == 'mainCategory') {
                    item.categoryLevel1 = ''
                    item.categoryLevel2 = ''
                } else if (this.isShowFiled == 'categoryLevel1') {
                    item.mainCategory = ''
                    item.categoryLevel2 = ''
                } else {
                    item.mainCategory = ''
                    item.categoryLevel1 = ''
                }
            })
            this.categorylist = data;

            const res1 = await getProCodeSimilaritySummary(params);
            if (!res1?.success) {
                return;
            }
            this.$nextTick(() => {
                this.isshowsummary = true;
                this.categorylistLoading = false;
                this.categorysummaryarry = res1.data;
            })
        },
        categorysortchange({ order, prop }) {
            if (prop) {
                this.categorypager.OrderBy = prop
                this.categorypager.IsAsc = order.indexOf("descending") == -1 ? true : false
                this.imilarity()
            }
        },
        //获取状态信息
        async getprosimstatelist() {
            var res = await getProCodeSimilarityStateName();
            if (res?.code) {
                this.prosimstatelist = res.data.map(function (item) {
                    var ob = new Object();
                    ob.state = item;
                    return ob;
                })
            }
        },
        async remoteSearchStyleCode(parm) {
            if (!parm) {
                //this.$message({ message: this.$t('api.sync'),type: 'warn'})
                return;
            }
            var options = [];
            const res = await getListByStyleCodeCost({ currentPage: 1, pageSize: 50, styleCode: parm })
            res?.data?.forEach(f => {
                options.push({ value: f.cost + ',' + f.goodsCode, label: f.goodsName + ' — ' + f.cost })
            })
            this.autoform.fApi.getRule('packCost1').options = options;
        },
        async setcustomergroup(e) {
            this.isshowstate = true
            await this.$refs.procodesimilaritystate.setcustomergroup(e, this.list)
        },
        async changelist(e) {
            this.list = e
        },
        //设置系列状态
        async setprostate(e) {
            this.isshowstate = true
            await this.$refs.procodesimilaritystate.OnSearch(e.goodsCode, this.list)
        },
        async setpromonth(e) {
            this.isshowmonth = true
        },
        //设置平台下拉
        async setPlatform() {
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
        },
        //设置店铺下拉
        async onchangeplatform(val) {
            const res = await getshopList({
                platform: val,
                CurrentPage: 1,
                PageSize: 1000,
            });
            this.shopList = res.data.list || [];
            this.filter.shopCode = "";
        },
        //设置店铺下拉
        async onchangeplatformMain(val) {
            await this.onchangeplatform(val);
            await this.onSearch();
        },
        //设置店铺下拉
        async onchangeplatformDetail(val) {
            await this.onchangeplatform(val);
            await this.onSearchDetail();
        },
        //设置运营组下拉
        async setGroupSelect() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;

            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        //导出
        async allListExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            var res = await exportProCodeSimilarityListNew(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "系列编码报表" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        //导出
        async onExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            var res = await exportProCodeSimilarity(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "链接相似度_" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        //导出
        async newonExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            var res = await exportProCodeSimilarity1(params);
            if (!res?.data) return;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute(
                "download",
                "系列编码报表" + new Date().toLocaleString() + ".xlsx"
            );
            aLink.click();
        },
        async remoteSearchUser(parm) {
            if (!parm) {
                //this.$message({ message: this.$t('api.sync'),type: 'warn'})
                return;
            }
            var options = [];
            const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: parm })
            res?.data?.forEach(f => {
                options.push({ value: f.styleCode, label: f.styleCode })
            })
            this.autoform.fApi.getRule('styleCode').options = options;
        },
        //修改
        async onHand(val) {
            if (val == 1) {
                this.handletype = val
                this.formtitle = "耗材费用设置"
                this.editparmVisible = true
                this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
                {
                    type: 'select', field: 'styleCode', title: '系列编码', validate: [{ type: 'string', required: true, message: '请选择系列编码' }], value: "", options: [],
                    props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
                },
                {
                    type: 'select', field: 'packCost1', title: '包装费', validate: [{ type: 'string', required: true, message: '请选择包装费' }], value: "", options: [],
                    props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchStyleCode(parm) }
                },
                ]
                var arr = Object.keys(this.autoform.fApi);
                if (arr.length > 0)
                    this.autoform.fApi.reload()

                var model = { id: '', }
                this.$nextTick(async () => {
                    var arr = Object.keys(this.autoform.fApi)
                    if (arr.length > 0)
                        await this.autoform.fApi.resetFields()
                    await this.autoform.fApi.setValue(model)
                })
            } else if (val == 2) {
                this.handletype = val
                this.formtitle = "增长率设置"
                this.editparmVisible = true
                var that = this
                this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 1 } },
                {
                    type: 'select', field: 'styleCode', title: '系列编码', value: "", options: [],
                    props: { multiple: true, filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
                },
                { type: 'radio', field: 'isAll', title: '是否设置全部', value: true, update(val) { { that.changeIsStyleCode(val) } }, options: [{ value: true, label: "批量", disabled: false }, { value: false, label: "全部" },], col: { span: 6 }, },
                { type: 'InputNumber', field: 'growRate', title: '增长率', props: { precision: 0 }, validate: [{ required: true, message: '请输入' }] },
                ]

                var arr = Object.keys(this.autoform.fApi);
                if (arr.length > 0)
                    this.autoform.fApi.reload()

                var model = { id: '', }
                this.$nextTick(async () => {
                    var arr = Object.keys(this.autoform.fApi)
                    if (arr.length > 0)
                        await this.autoform.fApi.resetFields()
                    await this.autoform.fApi.setValue(model)
                })
            }


        },
        async changeIsStyleCode(val) {
            this.$nextTick(async () => {
                var arr = Object.keys(this.autoform.fApi)
                if (arr.length > 0) {
                    if (val == false)
                        await this.autoform.fApi.hidden(true, 'styleCode')
                    else if (val == true) {
                        await this.autoform.fApi.hidden(false, 'styleCode')
                    }
                }

            })
        },
        async onSetEditParm() {
            this.editparmLoading = true;
            if (this.handletype == 1) {
                await this.autoform.fApi.validate(async (valid, fail) => {
                    if (valid) {
                        const formData = this.autoform.fApi.formData()
                        const res = await batchAddProCodeSimilarity(formData)
                        if (res.code == 1) {
                            this.editparmLoading = false;
                            this.editparmVisible = false;
                        }
                    }
                    else {
                        this.editparmLoading = false;
                    }
                })
            } else if (this.handletype == 2) {
                this.$nextTick(async () => {
                    await this.autoform.fApi.validate(async (valid, fail) => {
                        if (valid) {
                            const formData = this.autoform.fApi.formData()
                            if (formData.isAll == true) {
                                formData.styleCode = formData.styleCode.join()
                            }

                            const res = await addTbProCodeSimilarityGrowSet(formData)
                            this.editparmLoading = false;
                            this.editparmVisible = false;
                        }
                        else {
                            this.editparmLoading = false;
                        }
                    })
                })
            }
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            // this.filter.timerange = []
            if (this.filter.timerange == 0) {
                this.filter.startDate = formatTime(dayjs().subtract(31, 'day'), "YYYY-MM-DD");
                this.filter.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
                this.filter.timerange = [this.filter.startDate, this.filter.endDate];
            } else {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = {
                ...pager,
                ...page,
                ...this.filter,
            };

            return params;
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getlist();
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await pageProCodeSimilarity(params);
            this.listLoading = false;
            if (!res?.success) {
                return;
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach((d) => {
                d._loading = false;
            });
            this.list = data;
            await this.getSummary();
        },
        async getSummary() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }

            const res = await getProCodeSimilaritySummary(params);
            if (!res?.success) {
                return;
            }
            this.summaryarry = res.data;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                    OrderBy: column.prop,
                    IsAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            }
            await this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels;
        },
        async getLastUpdateTime() {
            const res = await getLastUpdateTime({});
            if (!res?.success) {
                return;
            }
            if (res.data) this.lastUpdateTime = "最晚更新时间：" + res.data;
        },
        ///==明细 Start==========================================
        async showDetail(row, tabName) {
            this.detail.visible = true;
            this.detail.selRow = row;
            this.detail.srcList = [row.goodsImage];
            this.detail.tabName = tabName;
            this.clearDetailFilter();
            if (this.detail.filter.timerange.length == 0) {
                this.detail.filter.timerange = [this.filter.startDate, this.filter.endDate]
                this.detail.filter.startDate = this.detail.filter.timerange[0];
                this.detail.filter.endDate = this.detail.filter.timerange[1];
            }
            if (this.detail.filter.styleCodeTimerange.length == 0) {
                this.detail.filter.styleCodeTimerange = [this.filter.startDate, this.filter.endDate]
                this.detail.filter.styleCodeStartDate = this.detail.filter.styleCodeTimerange[0];
                this.detail.filter.styleCodeEndDate = this.detail.filter.styleCodeTimerange[1];
            }
            this.detail.filter.parentId = row.id;
            this.detail.filter.seriesCode = row.goodsCode;
            this.detail.filter.similarity = this.filter.similarity;
            this.detail.filter.shopCode = this.filter.shopCode;
            this.detail.filter.groupId = this.filter.groupId;
            this.detail.filter.proCode = this.filter.proCode;
            this.detail.filter.platform = this.filter.platform;

            this.detail.pager = { OrderBy: "id", IsAsc: false };
            await this.onTabClick({ label: "相似产品" });
        },
        async showTurnover(row) {
            this.dialodayssisVisible = true
            this.clearDetailFilter();
            this.detail.filter.goodsCode = row.goodsCode
            this.$nextTick(async () => {
                await this.$refs.Daysturnoverdetail.onSearch()
            })

        },
        clearDetailFilter() {
            this.detail.filter = {
                parentId: null,
                proCode: null,
                status: null,
                platform: null,
                shopId: "",
                similarity: null,
                seriesCode: null,
                goodsCode: null,
                timerange: [],
                startDate: null,
                endDate: null,
                styleCodeTimerange: [],
                styleCodeStartDate: null,
                styleCodeEndDate: null
            };
        },
        //分页查询
        async onSearchDetail() {
            if (this.detail.tabName == "tabSame") {
                await this.$refs.sameProDetail.onSearch();
            } else if (this.detail.tabName == "tabSeries") {
                await this.$refs.seriesGoods.onSearch();
            } else if (this.detail.tabName == "tabMain") {
                await this.$refs.mainProGoods.onSearch();
            }
        },
        //订单量图表
        async Pananysis(row) {
            this.dialoganalysisVisible = true;
            let para = { proCode: row.proCode, timerange: this.filter.timerange }

            this.$nextTick(() => {
                this.$refs.proCodeSimilarityAnalysis.onSearch(para);
            });
        },
        //趋势图
        async showprchart(goodsCode) {
            //  window['lastseeprcodedrchart']=goodsCode
            //  this.buessness.data= window['lastseeprcodedrchart1']


            this.filter.startDate = null;
            this.filter.endDate = null;
            var startDate = null, endDate = null;
            if (this.filter.timerange) {
                startDate = this.filter.timerange[0];
                endDate = this.filter.timerange[1]
            }

            var params = { goodsCode: goodsCode, similarity: this.filter.similarity, goodsCode: goodsCode, isGroupBy: true, platform: this.filter.platform }
            let that = this;
            const res = await queryProCodeSimilarityAnalysis(params).then(res => {
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            })
            this.$nextTick(async () => {
                await getProCodeBusinessStaffPlatForm({ goodsCode: goodsCode, platform: this.filter.platform }).then(res => {
                    that.buscharDialog.visible = true;
                    that.buessness.visible = true
                    that.buessness.data = res.data
                })
            })

            //await this.$refs.ProCodeBusinessStaffPlatForm.getanalysisdata(goodsCode)
        },
        //汇总趋势图
        async onsummaryClick(property) {
            // this.$message({message:property,type:"warning"});
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...this.filter };
            params.column = property;
            let that = this;
            const res = await queryProCodeSimilarityAnalysis(params).then(res => {
                that.buessness.visible = false;
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        },
        async getimportlist() {
            var res = await getProCodeSimilarityImportLogTime();
            if (!res?.success) return
            this.importtimelist = res.data
            this.importtimedialogVisible = true;
        },
        showNext() {
            if (this.list && this.list.length > 0) {
                var nextRow = this.list[0];
                var findCur = false;
                this.list.forEach(item => {
                    if (findCur) {
                        findCur = false;
                        nextRow = item;
                    }
                    if (item.id == this.detail.selRow.id) {
                        findCur = true;
                    }
                });
                this.detail.selRow = nextRow;
                this.showDetail(nextRow, this.detail.tabName);
            }
        },
        onTabClick(tab, event) {
            setTimeout(async () => {
                await this.onSearchDetail();
            }, 500);
        },
        ///==明细 End  ==========================================
        myformatLinkProCode(platform, proCode) {
            return formatLinkProCode(platform, proCode);
        },
        myformatPlatform(platform) {
            return formatPlatform(platform);
        },
        //表头样式
        headerCellStyle(data) {
            if (data && data.column) {
                var isDayReportCol = dayReportCols.find(
                    (a) => a.prop == data.column.property
                );
                if (isDayReportCol) {
                    return { color: "#F56C6C" };
                }
            }
            return null;
        },
        showDayZZRateDetail(row, type) {
            this.dayZZRatedetail.filter.type = type;
            this.dayZZRatedetail.filter.seriesCode = row.goodsCode;
            this.dialogDayZZRatedetailVisible = true;
            this.$nextTick(async () => {
                await this.$refs.DayZZRatedetail.onSearch()
            })
        }
    },
    computed: {
        selGroupName() {
            var name = this.groupList?.find(
                (a) => a.key == this.detail.selRow.groupId
            )?.value;
            return name || "未知";
        },
        selPlatformName() {
            var name = this.platformList?.find(
                (a) => a.value == this.detail.selRow.platform
            )?.label;
            return name;
        },
        summaryarryMain() {
            return this.summaryarry;
        },
    },
};
</script>
<style scoped lang="scss">
::v-deep .el-link.el-link--primary {
    margin-right: 7px;
}

::v-deep .el-table__fixed {
    pointer-events: auto;
}

.xptcblbj2 {
    width: 100%;
    box-sizing: border-box;
    display: flex;
}

.half-screen-tabs {
    display: flex;
    height: 100vh;
}

.half-screen-content {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.el-tabs__content {
    height: 100%;
}

.father {
    height: 620px;
    display: flex;
    flex-direction: column;

    .main {
        display: flex;
        flex: 1;
        flex-direction: column;

        .father_top {
            display: flex !important;
            flex-direction: row !important;

            /* justify-content: space-between; */
            margin-bottom: 10px;

            .father_top_left {
                width: 48%;
                height: 100%;
                display: flex;
                flex-direction: column;

                .father_top_left_top {
                    width: 100%;
                    height: 40px;
                    margin-bottom: 20px;
                }

                .father_top_left_bottom {
                    border: 1px solid #ccc;
                    flex: 1;
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    .father_top_left_bottom_top {
                        display: flex !important;
                        flex-direction: row !important;
                        height: 40px;

                        .father_top_left_bottom_top_left,
                        .father_top_left_bottom_top_right {
                            width: 50%;
                            height: 100%;
                            text-align: center;
                            line-height: 40px;
                            border-radius: 5px;
                            cursor: pointer;
                        }

                    }

                    .father_top_left_bottom_bottom {
                        height: 370px;
                    }
                }
            }

            .father_top_right {
                width: 50%;
                display: flex;
                justify-content: space-between;
                margin-top: 60px;
                margin-left: 20px;
                border: 1px solid #ccc;

                .father_top_right_one {
                    width: 33%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;

                    .father_top_right_one_top {
                        width: 100%;
                        height: 40px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border: 1px solid #ccc;
                    }

                    .father_top_right_one_bottom {
                        width: 100%;
                        flex: 1;
                        border: 1px solid #ccc;
                        max-height: 366px;
                        min-height: 366px;
                        overflow-y: auto;

                        .father_top_right_one_bottom_one {
                            margin-top: 5px;
                            height: 19px;
                            text-align: center;
                            color: #409EFF;
                            cursor: pointer;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            overflow: hidden;
                        }

                    }
                }

                .father_top_right_two {
                    width: 33%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;

                    .father_top_right_two_top {
                        width: 100%;
                        height: 40px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border: 1px solid #ccc;
                    }

                    .father_top_right_two_bottom {
                        width: 100%;
                        flex: 1;
                        border: 1px solid #ccc;
                        max-height: 366px;
                        min-height: 366px;
                        overflow-y: auto;

                        .father_top_right_two_bottom_one {
                            margin-top: 5px;
                            height: 19px;
                            text-align: center;
                            color: #409EFF;
                            cursor: pointer;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            overflow: hidden;
                        }
                    }
                }

                .father_top_right_three {
                    width: 34%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;

                    .father_top_right_three_top {
                        width: 100%;
                        height: 40px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border: 1px solid #ccc;
                    }

                    .father_top_right_three_bottom {
                        width: 100%;
                        flex: 1;
                        border: 1px solid #ccc;
                        max-height: 366px;
                        min-height: 366px;
                        overflow-y: auto;

                        .father_top_right_three_bottom_one {
                            margin-top: 5px;
                            height: 19px;
                            text-align: center;
                            color: #409EFF;
                            cursor: pointer;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            overflow: hidden;
                        }
                    }
                }
            }
        }

        .father_bottom {
            height: 100px;
            overflow: auto;
            border: 1px solid #ccc;
            margin-top: 10px;

            .father_bottom_topWord {
                text-align: center;
            }
        }
    }

    .footer {
        height: 30px;
        margin-top: 10px;
        display: flex;
        justify-content: center;

        &:nth-child(1) {
            margin-right: 20px;
        }
    }
}

.active {
    background-color: #409EFF;
}

.activered {
    color: red !important;
}
</style>
