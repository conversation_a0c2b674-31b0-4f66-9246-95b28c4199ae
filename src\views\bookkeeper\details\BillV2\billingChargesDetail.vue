<template>
    <MyContainer>
      <template #header>
      </template>
      <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { replaceSpace } from '@/utils/getCols'
  import { pickerOptions } from '@/utils/tools'
  import { pageUnCalcCharBillItemDetail } from '@/api/bookkeeper/reportdayV2'
  import dayjs from 'dayjs'
  const tableCols = [
    { istrue: true, align: 'center', prop: 'yearMonthDay', label: '日期', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'orderNo', label: '线上单号', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'shopCode', label: '店铺', tipmesg: '', width: 'auto', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, align: 'center', prop: 'billingItem', label: '账单项目', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'businessType', label: '业务类型', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'inAmountIncome', label: '收入金额', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'outAmountIncome', label: '支出金额', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'amountIncome', label: '金额', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, align: 'center', prop: 'remark', label: '备注', tipmesg: '', width: 'auto', sortable: 'custom', },
  ]
  export default {
    name: "billingChargesDetail",
    components: {
      MyContainer, vxetablebase
    },
    props: {
      detailArgument: { type: Object, default: () => { } },
    },
    data() {
      return {
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          startTime: null,//开始时间
          endTime: null,//结束时间
          platform: null,
          billingItem: null,
        },
        timeRanges: [],
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      this.ListInfo.startTime = this.detailArgument.startTime
      this.ListInfo.endTime = this.detailArgument.endTime
      this.ListInfo.platform = this.detailArgument.platform
      this.ListInfo.BillType = this.detailArgument.billingItem
      await this.getList('search')
    },
    methods: {
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await pageUnCalcCharBillItemDetail(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          //获取列表失败
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }
  </style>
  