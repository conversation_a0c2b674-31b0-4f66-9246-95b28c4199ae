<template>
  <my-container v-loading="pageLoading">
    <template #header>
    <el-form :inline="true" :model="filter" @submit.native.prevent>
      <el-form-item label="分仓:">
          <el-select v-model="warehouses" multiple clearable filterable  :collapse-tags="true" placeholder="请选择分仓" style="width: 250px">
            <el-option label="全仓" :value="-1"/>
            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
      <el-form-item label="采购员:">
        <el-select v-model="brandIds" multiple collapse-tags clearable filterable placeholder="请选择采购员" style="width: 140px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="运营组:">
        <el-select v-model="groupIds" multiple collapse-tags clearable filterable placeholder="请选择运营组" style="width: 140px">
            <el-option label="所有" value=""/>
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="预警:">
        <el-select v-model="filter.warnStatus" placeholder="请选择" clearable filterable style="width: 150px">
          <el-option label="所有" value></el-option>
          <el-option label="正常" value='0'></el-option>
          <el-option label="进货" value="1"></el-option>
          <el-option label="催货" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商品编码:"><el-input v-model.trim="filter.goodsCode" /></el-form-item>
      <el-form-item label="商品名称:"><el-input v-model.trim="filter.goodsName" /></el-form-item>
       <el-form-item label="人为编辑:">
        <el-select v-model="filter.isedit"  clearable filterable placeholder="请选择" style="width: 100px">
           <el-option label="所有" value></el-option>
           <el-option label="是" value="true"></el-option>
           <el-option label="否" value='false'></el-option>
        </el-select>
      </el-form-item>
      <el-form-item><el-button type="primary" @click="onSearch">查询</el-button></el-form-item>
    </el-form>
    </template>
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
        @cellclick='cellclick' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
         <template slot='extentbtn'>
            <el-button-group>              
              <el-button style="margin: 0;" type="primary" @click="onShowEditPurchasePlan2Parm" v-if="checkPermission(['api:inventory:purchase:EditPurchasePlan2ParmAsync'])">
                参数设置
              </el-button>
              <el-button style="margin: 0;" v-if="lastUpdateTime" >
                {{lastUpdateTime}}
              </el-button>
            </el-button-group>
        </template>
     </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>

   <el-dialog title="导入数据" v-dialogDrag :visible.sync="importVisible" width="30%">
     <div>
        <el-alert type="warning" show-icon :closable="false" title="温馨提示:导入3个文件，各需要包含‘全仓’、‘义乌’、‘昌东’关键字 ！"></el-alert>
     </div>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="3" action accept=".xlsx"
          :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入数据" v-dialogDrag :visible.sync="importVisible1" width="30%">
     <div>
        <el-alert type="warning" show-icon :closable="false" title="温馨提示:导入3个文件，各需要包含‘全仓’、‘义乌’、‘昌东’关键字 ！"></el-alert>
     </div>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="3" action accept=".xlsx"
          :http-request="uploadFile1" :on-change="uploadChange" :on-remove="uploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading1" @click="submitUpload1">{{(uploadLoading1?'上传中':'上传' )}}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importVisible1 = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible" :show-close="false" v-dialogDrag width="60%">      
      <div style="margin-top:-50px;margin-bottom:-30px;margin-right:-20px;margin-left:-20px;">
        <div style="height: 160px">
          <ces-table ref="table1" :that='that'  :hasexpand='true' :isSelectColumn='false'
           :tableData='list1' :tableCols='tableCols1' :loading="listLoading"></ces-table>
        </div>
       <probianmaanalysis :filter="analysisfilter" ref="probianmaanalysis1"/>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="visiblepopover" v-dialogDrag :show-close="false">
       <goodscoderecord ref="goodscoderecord" :filter="goodscoderecordfilter" style="height: 500px"></goodscoderecord> 
    </el-dialog>
    <el-dialog :visible.sync="visibleplan2recode" v-dialogDrag :show-close="true">
       <purchaseplan2recode ref="plan2recode" style="height: 400px"></purchaseplan2recode> 
    </el-dialog>
    <el-drawer title="处理" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
       </div>
    </el-drawer>
    <el-drawer title="参数设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoformparm.rule" v-model="autoformparm.fApi" :option="autoformparm.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editparmVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onEditPurchasePlan2ParmSubmit" />
       </div>
    </el-drawer>
  </my-container>
</template>

<script>
import {pagePurchasePlan2,exportPurchasePlan2,getLastUpdateTimeyPurchasePlan2,importPurchasePlan2,editPurchasePlan2,getPurchasePlan2,getPurchasePlan2Parm,editPurchasePlan2Parm,importPurchasePlan2History} from '@/api/inventory/purchase'
import {getDirectorGroupList} from '@/api/operatemanage/base/shop'
import {getAllProBrand} from '@/api/inventory/warehouse'
import {upLoadImage} from '@/api/upload/file'
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import probianmaanalysis from '@/views/inventory/probianmaanalysis'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import purchaseplan2recode from '@/views/inventory/components/purchaseplan2recode'
import {formatYesornoBool,formatPurchasePlanError,formatmoney,formatTime,formatNoLink,formatSecondToHour,formatWarehouse as formatWarehousePublic ,warehouselist} from "@/utils/tools";
import { throttle } from 'throttle-debounce';
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
 
var formatWarehouse=function(warehouse){
    if(warehouse==-1) return "全仓";
    return formatWarehousePublic(warehouse);
};
const tableCols =[
      {istrue:true,prop:'brandId',label:'采购员', width:'65',fixed:true,type:'html',formatter:(row)=>{return `<a href="javascript:void(0);" style="font-size:small;color:#606266";">${row.brandName??''}</a>`;}},
      {istrue:true,prop:'warehouse',label:'分仓', width:'75',fixed:true,sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'80',fixed:true,sortable:'custom', type:'click',handle:(that,row)=>that.directgoodsCode(row.goodsCode)},
      {istrue:true,prop:'images',label:'图片', width:'60',fixed:true,type:'image'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'100',fixed:true,sortable:'custom'},
      {istrue:true,prop:'warnStatus',label:'预警状态', width:'70',fixed:true,type:'html',formatter:(row)=>formatPurchasePlanError(row.warnStatus),tipmesg:'正常=(建议采购量<最低库存天数*预计日均销量，并且“最低库存天数>合计周转天数”)、 进货=(建议采购量>=最低库存天数*预计日均销量，并且“最低库存天数≤合计库存周转天数”)、 催货=(实际可用数<预计日均销量*2_ 并且采购在途> 0或者进货仓库存> 0)'},
      {istrue:true,prop:'purchasePlan',label:'建议采购量', width:'70',sortable:'custom',tipmesg:'预计日均销量*建议库存天数-实际可用数-进货仓库存-采购在途'},
      {istrue:true,prop:'useableCount',label:'实际可用数', width:'70',sortable:'custom'},
      {istrue:true,prop:'orderUseableCount',label:'订单占有数', width:'70',sortable:'custom'},
      {istrue:true,prop:'sumTurnover',label:'合计库存周转天数', width:'70',sortable:'custom',tipmesg:'(实际可用数+进货仓库存+采购在途)/预计日均销量'},
      {istrue:true,prop:'useableTurnover',label:'现有库存周转天数', width:'70',sortable:'custom',tipmesg:'(实际可用数+进货仓库存)/预计日均销量'},
      {istrue:true,prop:'estimateSalesDay',label:'预计日均销量', width:'70',sortable:'custom',tipmesg:'(昨日销量+3日平均销量+7日平均销量+15日平均销量)/4',type:'html',formatter:(row)=>formatNoLink(row.estimateSalesDay)},
      {istrue:true,prop:'planStockDays',label:'建议库存天数', width:'70',sortable:'custom',type:'html',formatter:(row)=>
          {return `<a href="javascript:void(0);" style="font-size:small;color:${row.planStockDays==row.originPlanStockDays?'#606266':'red'}";">${row.planStockDays}</a>`;}},
      {istrue:true,prop:'minimumStockDays',label:'最低库存天数', width:'70',sortable:'custom',tipmesg:'最近在途时长(分钟)/1440+最低库存参数',type:'html',formatter:(row)=> 
          {return `<a href="javascript:void(0);" style="font-size:small;color:${row.minimumStockDays==row.originMinimumStockDays?'#606266':'red'}";">${row.minimumStockDays}</a>`;}},
      {istrue:true,type:'button', width:'55',btnList:[{label:"编辑",display:(row)=>{return false;},handle:(that,row)=>that.onHand(row)}]},
      {istrue:true,prop:'lastInTransitTime',label:'最近在途时长', width:'70',sortable:'custom',formatter:(row)=>formatSecondToHour(row.lastInTransitTime)},
      {istrue:true,prop:'avgInTransitTime',label:'历史平均在途', width:'70',sortable:'custom',formatter:(row)=>formatSecondToHour(row.avgInTransitTime)},
      {istrue:true,prop:'goodsLable',label:'商品标签', width:'50',sortable:'custom'},
      {istrue:true,prop:'inTransitNum',label:'采购在途', width:'90',sortable:'custom'},
      {istrue:true,prop:'stock',label:'进货仓库存', width:'70',sortable:'custom'},
      //{istrue:true,prop:'groupId',label:'运营', width:'65',type:'html',formatter:(row)=>{return row.groupName;}},
      {istrue:true,prop:'groupId',label:'运营', width:'60',type:'html',formatter:(row)=>{return `<a href="javascript:void(0);" style="font-size:small;color:#606266";">${row.groupName??''}</a>`;}},
      {istrue:true,prop:'goodsCreatedTime',label:'商品创建日期', width:'105',sortable:'custom',formatter:(row)=>formatTime(row.goodsCreatedTime,'MM-DD HH:mm:ss')},
      {istrue:true,prop:'cost',label:'成本', width:'65',sortable:'custom'},
      {istrue:true,prop:'purchasePlanAmont',label:'预计采购金额', width:'70',sortable:'custom'}, 
      {istrue:true,prop:'salesYesterday',label:'昨日销量', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay3',label:'3天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay7',label:'7天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay15',label:'15天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay30',label:'30天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay45',label:'45天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay60',label:'60天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay90',label:'90天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgSalesDay120',label:'120天日均销', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgRefundDay30',label:'月日均退货量', width:'60',sortable:'custom'},
      {istrue:true,prop:'avgRefundDay90',label:'季度日均退货', width:'60',sortable:'custom'},
     ];
    const tableCols1 =[
      {istrue:true,prop:'brandId',label:'采购员', width:'65',fixed:true,type:'html',formatter:(row)=>{return `<a href="javascript:void(0);" style="font-size:small;color:#606266";">${row.brandName??''}</a>`;}},
      {istrue:true,prop:'warehouse',label:'分仓', width:'75',fixed:true,sortable:'custom',formatter:(row)=>formatWarehouse(row.warehouse)},
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'80',fixed:true,sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.goodsCode)},
      {istrue:true,prop:'images',label:'图片', width:'60',fixed:true,type:'image'},
      {istrue:true,prop:'goodsName',label:'商品名称', width:'100',fixed:true,sortable:'custom'},
      {istrue:true,prop:'warnStatus',label:'预警状态', width:'50',fixed:true,type:'html',formatter:(row)=>formatPurchasePlanError(row.warnStatus)},
      {istrue:true,prop:'purchasePlan',label:'建议采购量', width:'70',sortable:'custom'},      
      {istrue:true,prop:'useableCount',label:'实际可用数', width:'70',sortable:'custom'},
      {istrue:true,prop:'sumTurnover',label:'合计库存周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'useableTurnover',label:'现有库存周转天数', width:'70',sortable:'custom'},
      {istrue:true,prop:'estimateSalesDay',label:'预计日均销量', width:'70',sortable:'custom',type:'html',formatter:(row)=>formatNoLink(row.estimateSalesDay)},
      {istrue:true,prop:'planStockDays',label:'建议库存天数', width:'70',sortable:'custom',type:'html',formatter:(row)=>
          {return `<a href="javascript:void(0);" style="font-size:small;color:${row.planStockDays==row.originPlanStockDays?'#606266':'red'}";">${row.planStockDays}</a>`;}},
      {istrue:true,prop:'minimumStockDays',label:'最低库存天数', width:'70',sortable:'custom',type:'html',formatter:(row)=> 
          {return `<a href="javascript:void(0);" style="font-size:small;color:${row.minimumStockDays==row.originMinimumStockDays?'#606266':'red'}";">${row.minimumStockDays}</a>`;}},
      {istrue:true,prop:'inTransitNum',label:'采购在途', width:'90',sortable:'custom'},
      {istrue:true,prop:'orderUseableCount',label:'订单占有数', width:'70',sortable:'custom'},
      {istrue:true,prop:'stock',label:'进货仓库存', width:'70',sortable:'custom'},
      {istrue:true,prop:'groupId',label:'运营', width:'60',type:'html',formatter:(row)=>{return `<a href="javascript:void(0);" style="font-size:small;color:#606266";">${row.groupName??''}</a>`;}},
     ];
const tableHandles=[{label:"导入", handle:(that)=>that.startImport(1)}, 
                    {label:"导入(历史)", handle:(that)=>that.startImport(2)},
                    {label:"导出", handle:(that)=>that.onExport()}];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton,probianmaanalysis,goodscoderecord,purchaseplan2recode},
  data() {
    return {
      that:this,
      formatTime:formatTime,
      filter: {
        newWarehouses:null,
        goodsCode:null,
        brandId:null,
        groupId:null,
        brandIds:null,
        groupIds:null,
        goodsName:null,
        warnStatus:null,
        isedit:null,
      },
      analysisfilter:{
        startDate: null,
        endDate: null,
        proBianMa:""
      },
      warehouses: [0,1,5,6],
      groupIds:[],
      brandIds:[],
      warehouselist:warehouselist,
      goodscoderecordfilter:{goodsCode:"",buyNo:""},
      list: [],
      list1: [],
      brandlist:[],
      grouplist:[],
      recodelist:[],
      tableCols1:tableCols1,
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      importVisible: false,//导入实时
      importVisible1: false,//导入历史
      prevTarget: null, // 编辑 Popover 的 Reference （参照），用于 popover.js 对齐两个元素
      popperFlag: false, // 用于编辑 Popover 的刷新
      visiblepopover: false,
      visiblepopoverdetail: false,
      dialogOrderDetailVisible:false,
      popperFlagdetail: false,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      selids: [],
      fileList:[],
      listLoading: false, 
      pageLoading: false,
      uploadLoading:false,
      uploadLoading1:false,
      lastUpdateTime:'',
      onExporting:false,
      dialogVisible:false,
      editVisible:false,
      editparmVisible:false,
      editLoading:false,
      drawervisible:false,
      visibleplan2recode:false,
      editparmLoading:false,
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
    }
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    this.init();
    this.getlist();
    this.initform();
    this.initformparm();
    formCreate.component('editor', FcEditor);
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async init(){
        var date1=new Date();
        this.analysisfilter.endDate=await this.datetostr(date1);
        var date2=await this.addDate(this.analysisfilter.endDate,-90);
        this.analysisfilter.startDate=await this.datetostr(new Date(date2));
        var res1= await getDirectorGroupList();
        this.grouplist = res1.data.map(item => {
          return { value: item.key, label: item.value };
        });

        var res2= await getAllProBrand();
        this.brandlist = res2.data.map(item => {
          return { value: item.key, label: item.value };
        });

        var res3= await getLastUpdateTimeyPurchasePlan2();
        this.lastUpdateTime= "最晚更新时间:"+res3.data
    },
    async initform(){
       let that=this;
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'input',field:'goodsCode',title:'商品编码',value: '',props:{readonly:true},col:{span:4}},
                     {type:'input',field:'goodsName',title:'商品名称',value: '',props:{readonly:true},col:{span:6}},
                     {type:'InputNumber',field:'planStockDays',title:'建议库存天数',value: 0,props:{min:0,max:99999999,precision:1},col:{span:6}},
                     {type:'InputNumber',field:'minimumStockDays',title:'最低库存天数',value: 0,props:{min:0,max:99999999,precision:1},col:{span:6}},                     
                    ]
    },
    async initformparm(){
       this.autoformparm.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'InputNumber',field:'purchasePlanParm',title:'建议采购量参数',value: null,props:{min:0,max:99999999,precision:1},col:{span:6}},
                     {type:'InputNumber',field:'minimumStockDaysParm',title:'最低库存天数参数',value: null,props:{min:0,max:99999999,precision:1},col:{span:6}},
                    ]
    },
    async removeEditPopoverListener(flag) {  // 监听滚动，用于编辑框的滚动移除
      let timer = setTimeout(() => {
        let scrollElement = this.$refs.table.$el.querySelector('.el-table__body-wrapper');
        console.log('监听滚动，用于编辑框的滚动移除', flag, scrollElement);
        let scrollHandle = () => {
          console.log('执行--->', this.visibleEditOpinions);
          if (this.visibleEditOpinions) {
            this.clearEditPopperComponent();
          }
        }
        if (flag) {
          // 滚动节流
          scrollElement.addEventListener('scroll', throttle(500, scrollHandle));
        } else {
          scrollElement.removeEventListener('scroll', scrollHandle);
        }
        clearTimeout(timer);
      }, 0);
    },
   async initeditor(editor){
     editor.config.uploadImgMaxSize = 3 * 1024 * 1024 
     editor.config.excludeMenus = ['emoticon','video']
     editor.config.uploadImgAccept = []
     editor.config.customUploadImg =async function (resultFiles, insertImgFn) {
        console.log('resultFiles',resultFiles)
        const form = new FormData();
        form.append("image", resultFiles[0]);
        const res =await upLoadImage(form);
        var url=`${res.data}`
        console.log('url',url)
        insertImgFn(url)
     }
    },
   async datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
   async addDate(date,days){ 
        var d=new Date(date); 
        d.setDate(d.getDate()+days); 
        var m=d.getMonth()+1; 
        return d.getFullYear()+'-'+m+'-'+d.getDate(); 
    },
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      this.filter.newWarehouses = this.warehouses.join();
      this.filter.brandIds = this.brandIds.join();
      this.filter.groupIds = this.groupIds.join();
      const params = {...pager,...this.pager,... this.filter}
      this.listLoading = true
      const res = await pagePurchasePlan2(params)
      this.listLoading = false
      if (!res?.success)return
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
      this.summaryarry=res.data.summary;
    },
  async cellclick(row, column, cell, event){
     if (column.property=='estimateSalesDay')  
        await this.onanalysis(row.goodsCode,row.warehouse)
     else if (column.property=='goodsCode')
         this.$router.push({path: '/order/ordergoodssales', query: {goodsCode: row.goodsCode}})
     else if (column.property=='expressNo'&&row.expressNo)
        await this.showlogistics(row.companyCode,row.expressNo);
      else if (column.property=='planStockDays'){
       if (row.planStockDays!=row.originplanStockDays) await this.showplan2recode(row.goodsCode);    
      }
     else if (column.property=='minimumStockDays'){
       if (row.minimumStockDays!=row.originMinimumStockDays) await this.showplan2recode(row.goodsCode);    
     }
     else if (column.property=='planArrivalTime'
         ||column.property=='companyCode'
         ||column.property=='count' ||column.property=='remark') {
      await this.getrecordlist(row.goodsCode)
      this.visiblepopover=true;
     }
    },
  async directgoodsCode(goodsCode){
      this.$router.push({path: '/order/ordergoodssales', query: {goodsCode: row.goodsCode}})
    },
   async getrecordlist(goodsCode){
      this.$nextTick(() => {
         this.$refs.goodscoderecord.onSearch(goodsCode,'');
      });
     },
   async onanalysis(goodsCode,warehouse){
        this.dialogVisible=true;
        this.$nextTick(() => {
           this.$refs.probianmaanalysis1.onpk(goodsCode,warehouse==-1?null:warehouse);
        });
        const res = await getPurchasePlan2({goodsCode:goodsCode,warehouse:warehouse})
        this.list1=[];
        this.list1.push(res.data)
    },
  async clearEditPopperComponent() {
      this.prevTarget = null;
      this.popperFlag = !this.popperFlag;
      this.popperFlagdetail= !this.popperFlagdetail;
      this.visiblepopover = false;
      this.visiblepopoverdetail= false;
    },
  async onShowEditPurchasePlan2Parm(){
      this.editparmVisible = true
      const res = await getPurchasePlan2Parm()
      var arr = Object.keys(this.autoformparm.fApi);
      if(arr.length >0)
         this.autoformparm.fApi.resetFields()
      await this.autoformparm.fApi.setValue(res.data)
    },
  async onEditPurchasePlan2ParmSubmit(){
      this.editparmLoading=true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoformparm.fApi.formData();
          const res = await editPurchasePlan2Parm(formData);
          if(res.success) this.editparmVisible=false;
        }else{}
     })
     this.editparmLoading=false;      
    },
   async onHand(row){
      this.editVisible = true
      const res = await getPurchasePlan2({goodsCode:row.goodsCode,warehouse:row.warehouse})
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
      await this.autoform.fApi.setValue(res.data)
    },
   async onDisPlay(row){
     return row.isHandle==true;
    },
   async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editPurchasePlan2(formData);
          if(res.success){
            this.getlist();
            this.editVisible=false;
          }
        }else{}
     })
     this.editLoading=false;
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    startImport(num) {
      if (num == 1)
        this.importVisible = true;
      else if (num == 2)
        this.importVisible1 = true;
    },
    cancelImport() {
      this.importVisible = false;
    },
    beforeRemove() {
      return false;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    
    //导入实时
   async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
    //导入历史
    async submitUpload1() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading1=true;
      this.$refs.upload.submit();
    },
   async uploadFile(item,fileList) {
      if(!this.fileHasSubmit) return false; 
      let fileyiwu = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("义乌")>-1;});
      //let filecbei = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("昌北")>-1;});
      let filecdong = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("昌东")>-1;});
      let filequancang = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("全仓")>-1;});
      //let fileshhai = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("上海分仓")>-1;});
     
      if (fileyiwu.length==0) {
        this.$message({ message: "请上传义乌仓文件", type: "warning" });
        this.uploadLoading=false; 
        this.fileList=null;
        this.$refs.upload.value.clearFiles();
        return false;
      }
      else if (filecdong.length==0) {
        this.$message({ message: "请上传昌东仓文件", type: "warning" });
        this.uploadLoading=false; 
        this.fileList=null;
        this.$refs.upload.value.clearFiles();
        return false;
      }
      else if (filequancang.length==0) {
        this.$message({ message: "请上传全仓文件", type: "warning" });
        this.uploadLoading=false; 
        this.fileList=null;
        this.$refs.upload.value.clearFiles();
        return false;
      }
      // else if (fileshhai.length==0) {
      //   this.$message({ message: "请上传上海分仓文件", type: "warning" });
      //   this.uploadLoading=false; 
      //   this.fileList=null;
      //   this.$refs.upload.value.clearFiles();
      //   return false;
      // }

      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("fileyiwu", fileyiwu[0]);     
      form.append("filecdong", filecdong[0]); 
      form.append("filequancang", filequancang[0]);
      //form.append("fileshanghai", fileshhai[0]);
      const res =await importPurchasePlan2(form);
      //const res =await importPurchasePlan2History(form);
      this.clearFiles();
      this.importVisible = false;
      if (res.success) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false; 
    },
    async uploadFile1(item) {
      if(!this.fileHasSubmit) return false; 
      let fileyiwu = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("义乌")>-1;});
      //let filecbei = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("昌北")>-1;});
      let filecdong = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("昌东")>-1;});
      let filequancang = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("全仓")>-1;});
      //let fileshhai = this.fileList.filter((item, index,arr)=>{return item.name.indexOf("上海分仓")>-1;});
     
      if (fileyiwu.length==0) {
        this.$message({ message: "请上传义乌仓文件", type: "warning" });
        this.uploadLoading1=false; 
        this.fileList=null;
        this.$refs.upload.value.clearFiles();
        return false;
      }
      else if (filecdong.length==0) {
        this.$message({ message: "请上传昌东仓文件", type: "warning" });
        this.uploadLoading1=false; 
        this.fileList=null;
        this.$refs.upload.value.clearFiles();
        return false;
      }
      else if (filequancang.length==0) {
        this.$message({ message: "请上传全仓文件", type: "warning" });
        this.uploadLoading1=false; 
        this.fileList=null;
        this.$refs.upload.value.clearFiles();
        return false;
      }
      // else if (fileshhai.length==0) {
      //   this.$message({ message: "请上传上海分仓文件", type: "warning" });
      //   this.uploadLoading1=false; 
      //   this.fileList=null;
      //   this.$refs.upload.value.clearFiles();
      //   return false;
      // }

      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("fileyiwu", fileyiwu[0]);     
      form.append("filecdong", filecdong[0]); 
      form.append("filequancang", filequancang[0]);
      //form.append("fileshanghai", fileshhai[0]);
      //const res =await importPurchasePlan2(form);
      const res =await importPurchasePlan2History(form);
      this.clearFiles();
      this.importVisible1 = false;
      if (res.success) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading1=false; 
    },
    clearFiles() {
        this.$refs['upload'].clearFiles();
    },
   async uploadChange(file, fileList) {
      if (fileList && fileList.length > 0) {
        var list = [];
        for(var i=0;i<fileList.length;i++){
          if(fileList[i].status=="success")
            list.push(fileList[i]);
          else
            list.push(fileList[i].raw);
        }
        this.fileList = list;
      }
   },
   async uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    selsChange: function(sels) {
      this.sels = sels
    },
  async onExport() {
     if (this.onExporting) return;
     try{
        const params = {...this.pager,... this.filter}
        var res= await exportPurchasePlan2(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','建议采购_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    async showlogistics(companycode,number){
      this.drawervisible=true;
       this.$nextTick(function(){
         this.$refs.logistics.showlogistics(companycode,number);
       })
    },
    async showplan2recode(goodsCode){
       this.visibleplan2recode=true;
       this.$nextTick(function(){
         this.$refs.plan2recode.onShow(goodsCode);
       })
    },
   async onShowParm(){
     
   }
  }
}
</script>
<style lang="scss" scoped>
  .el-dialog__header {
    padding: 20px 20px 10px;
    display: none;
   // margin-top: -20px;
  }
</style>


