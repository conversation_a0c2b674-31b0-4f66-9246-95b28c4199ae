<template>
  <MyContainer>
    <template #header>
      <el-form @keydown.enter.native="getList('search')">
        <div class="condition" ref="condition">
          <el-row :gutter="10" class="top">
            <el-col :span="3">
              <el-input v-model.trim="query.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
            </el-col>
            <el-col :span="3">
              <el-input v-model.trim="query.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
            </el-col>
            <el-col :span="3">
              <el-input v-model.trim="query.entityCode" placeholder="实体编码" maxlength="50" clearable class="publicCss" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.qtyMin" :max.sync="query.qtyMax" min-label="昨日销量最小值"
                max-label="昨日销量最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.usableQtyMin" :max.sync="query.usableQtyMax"
                min-label="全仓库存最小值" max-label="全仓库存最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.safeDayMin" :max.sync="query.safeDayMax"
                min-label="全仓安全天数最小值" max-label="全仓安全天数最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.sendWmsUsableQtyMin"
                :max.sync="query.sendWmsUsableQtyMax" min-label="外仓库存最小值" max-label="外仓库存最大值" />
            </el-col>
            <el-col :span="3">
              <el-button type="primary" size="mini" style="margin-left: 3px" @click="getList('search')">搜索</el-button>
              <el-button type="primary" size="mini" style="margin-left: 3px" :disabled="isExport"
                @click="exportProps">导出</el-button>
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.sendWmsUsableDayMin"
                :max.sync="query.sendWmsUsableDayMax" min-label="外仓安全天数最小值" max-label="外仓安全天数最大值" />
            </el-col>
            <el-col :span="3">
              <number-range class="publicCss" :min.sync="query.entityQtyMin" :max.sync="query.entityQtyMax"
                min-label="昨预包销量最小值" max-label="昨预包销量最大值" />
            </el-col>
          </el-row>
        </div>
      </el-form>
    </template>
    <vxetablebase :id="'unsalable202408041854'" ref="table" v-loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
      :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
      :is-select-column="true" :is-index-fixed="false" style="width: 100%;  margin: 0" :height="'100%'"
      :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange"
      @onTrendChart="trendChart">
      <template #usableQty="{ row }">
        <inventoryPopover :goods-codes="[row.goodsCode, row.entityCode]" :width="800" height="400"
          :columns="['wmsName', 'goodsCode', 'quantity', 'usableQty', 'orderUseQty', 'wmsWaitSendQty', 'inWmsInventory', 'allotQty', 'jstUsableQty', 'modifyTime']">
          {{ row.usableQty }}</inventoryPopover>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    <el-drawer title="趋势图" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false" direction="btt">
      <div v-if="!chatProp.chatLoading">
        <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="margin: 10px;"
          @change="
            trendChart({
              ...chatPropOption,
              startDate: $event[0],
              endDate: $event[1],
            })
          " />
        <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data" />
      </div>
      <div v-else v-loading="chatProp.chatLoading" />
    </el-drawer>
  </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import numberRange from '@/components/number-range/index.vue'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import inventoryPopover from './../inventory/popover.vue'
import { pickerOptions } from '@/utils/tools'
import { download } from '@/utils/download'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs'
import {
  getColumns_Unsalable, pageGetData_Unsalable, exportData_Unsalable, getTrendChart_Unsalable
} from '@/api/vo/prePack'
export default {
  name: 'ScanCodePage',
  components: {
    MyContainer, vxetablebase, numberRange, inventoryPopover, buschar
  },
  data() {
    return {
      chatPropOption: {},
      rules: {
      },
      that: this,
      query: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false
      },
      data: { total: 0, list: [], summary: {} },
      tableCols: [],
      loading: true,
      pickerOptions,
      isExport: false,
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: []// 趋势图数据
      }
    }
  },
  updated() {
    this.$refs.condition.scrollTop = 0
  },
  async mounted() {
    await this.getCol()
    this.getList()
  },
  methods: {
    async trendChart(option) {
      var endDate = null;
      var startDate = null;

      if (option.startDate && option.endDate) {
        startDate = option.startDate;
        endDate = option.endDate;
      } else {
        endDate = option.date;
        startDate = new Date(option.date);
        startDate.setDate(option.date.getDate() - 30);

        startDate = dayjs(startDate).format("YYYY-MM-DD");
        endDate = dayjs(endDate).format("YYYY-MM-DD");
      }
      option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
      option.filter.filters.push({
        field: option.dateField,
        operator: "GreaterThanOrEqual",
        value: startDate,
      });
      option.filter.filters.push({
        field: option.dateField,
        operator: "LessThanOrEqual",
        value: endDate,
      });

      option.startDate = startDate;
      option.endDate = endDate;

      this.chatProp.chatTime = [startDate, endDate];

      this.chatProp.chatLoading = true;

      const { data, success } = await getTrendChart(option);
      if (success) {
        this.chatProp.data = data;
      }

      this.chatProp.chatLoading = false;
      this.chatProp.chatDialog = true;

      this.chatPropOption = option;
    },
    async getCol() {
      const { data, success } = await getColumns_Unsalable()
      if (success) {
        this.tableCols = data
        this.query.summarys = data.filter(a => a.summaryType).map(a => { return { column: a['sort-by'], summaryType: a.summaryType } })
      }
    },
    // 导出数据,这里前端可以封装一个方法
    async exportProps() {
      this.isExport = true
      await exportData_Unsalable(this.query).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type === 'search') {
        this.query.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await pageGetData_Unsalable(this.query)
        if (success) {
          this.data = data
        } else {
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.$message.error('获取列表失败')
      } finally {
        this.loading = false
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.query.currentPage = 1
      this.query.pageSize = val
      this.getList()
    },
    // 当前页改变
    Pagechange(val) {
      this.query.currentPage = val
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.query.orderBy = prop
        this.query.isAsc = order.indexOf('descending') === -1
        this.getList()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.condition {
  height: 35px;
  max-height: 35px;
  overflow: hidden;
  position: relative;

  &:hover {
    overflow: visible;

    .top {
      box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.2);
    }
  }

  .top {
    position: absolute;
    background: white;
    z-index: 100;
    top: 0;

    .el-col {
      height: 30px;
      margin-bottom: 5px;

      .publicCss {
        width: 100%;
      }
    }
  }
}
</style>
