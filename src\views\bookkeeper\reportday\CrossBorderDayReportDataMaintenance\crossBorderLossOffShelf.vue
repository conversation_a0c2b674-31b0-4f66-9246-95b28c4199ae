<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='dahuixionglist' @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true'
      :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <!-- <el-button style="padding: 0;margin: 0;">
                  <el-input v-model.trim="Filter.ProName" clearable maxlength="100" placeholder="商品名称" style="width:120px;"/>
              </el-button>
              <el-button style="padding: 0;margin: 0;">
                  <el-input v-model.trim="Filter.SerialCoding"   clearable maxlength="100" placeholder="系列编码" style="width:120px;"/>
              </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button> -->
          <el-button type="primary" @click="onSearch">刷新</el-button>
          <el-button type="primary" @click="addButton">添加损耗下架</el-button>
          <!-- <el-button type="primary" @click="onImportSyj">导入</el-button>
            <el-button type="primary" @click="downloadTemplate">导入模板</el-button>   -->
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>

    <el-dialog title="导入产品运费" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2" :file-list="fileList"
          :on-change="onsuccess">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px;" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitupload2">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          <!-- <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button> -->
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-drawer title="编辑损耗下架" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" @click="onEditSubmit" />
      </div>
    </el-drawer>
    <el-drawer title="新增损耗下架" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="addVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <form-create :rule="autoformAdd.rule1" v-model="autoformAdd.fApi" :option="autoformAdd.options" />
      <div class="drawer-footer">
        <el-button @click.native="addVisible = false">取消</el-button>
        <my-confirm-button type="submit" @click="onaddSubmit" />
      </div>
    </el-drawer>
  </my-container>
</template>
<script>

import { getLossOffShelf, editLossOffShelf } from '@/api/bookkeeper/reportdayV2'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { formatPlatformkj } from "@/utils/tools";
const tableCols = [
  { istrue: true, prop: 'platForm', label: '平台', width: '400', sortable: 'custom', formatter: (row) => formatPlatformkj(row.platForm) },
  { istrue: true, prop: 'scale', label: '占比', width: '400', sortable: 'custom', tipmesg: '输入2是2%', formatter: (row) => !row.scale ? " " : row.scale + '%' },
  { istrue: true, prop: 'remark', label: '备注', width: '400', sortable: 'custom' },
  { istrue: true, type: "button", label: '操作', width: "450", btnList: [{ label: "编辑", handle: (that, row) => that.EditButton(row) }] }
];
export default {
  name: "crossBorderLossOffShelf",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      editLoading: false,
      addVisible: false,
      Filter: {


      },
      shopList: [],
      userList: [],
      groupList: [],
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 0,
      yearMonth: "",
      editVisible: false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      autoformAdd: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
    };
  },
  async mounted() {
    await this.initform();
    this.onSearch();
  },
  methods: {
    addButton() {
      this.addVisible = true;
    },
    async onaddSubmit() {
      await this.autoformAdd.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformAdd.fApi.formData();
          const res = await editLossOffShelf(formData);
          await this.autoformAdd.fApi.resetFields()
          if (res.success) {
            this.$message.success('添加成功！');
            this.getjSpeedDriveList();
            this.addVisible = false;
          }
        } else { }
      })
    },
    EditButton(row) {
      this.editVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if (arr.length > 0)
        this.autoform.fApi.resetFields()
      this.$nextTick(async () => {
        await this.autoform.fApi.setValue(row)
      })
    },
    async onEditSubmit() {
      await this.autoform.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoform.fApi.formData();
          const res = await editLossOffShelf(formData);
          if (res.code == 1) {
            this.$message.success('修改成功！');
            this.getjSpeedDriveList();
            this.editVisible = false;
          }
        } else { }
      })
    },
    async initform() {
      this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
      { type: 'select', field: 'platForm', title: '平台', value: '', col: { span: 6 }, options: [{ value: 12, label: '希音(全托)' }, { value: 16, label: '希音(自营)' },{ value: 13, label: '拼多多跨境(全托)' }, { value: 15, label: '拼多多跨境（半托)' }], props: { clearable: true, disabled: true }, validate: [{ type: 'number', required: true, message: '请选择平台' }] },
      { type: 'input', field: 'scale', title: '占比', value: '', props: { min: 0, precision: 0, maxlength: 7 }, validate: [{ pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/, message: "输入占比的格式不正确！" }, { required: true, message: '请输入占比' }] },
      { type: 'input', field: 'remark', title: '备注', value: '', props: { readonly: false, maxlength: 200 }, col: { span: 11 } },
      ],
        this.autoformAdd.rule1 = [
          { type: 'select', field: 'platForm', title: '平台', value: '', col: { span: 6 }, options: [{ value: 12, label: '希音(全托)' }, { value: 16, label: '希音(自营)' }, { value: 13, label: '拼多多跨境(全托)' }, { value: 15, label: '拼多多跨境（半托)' }], props: { clearable: true, disabled: false }, validate: [{ type: 'number', required: true, message: '请选择平台' }] },
          { type: 'input', field: 'scale', title: '占比', value: '', props: { min: 0, precision: 0, maxlength: 7 }, validate: [{ pattern: /(^-?[1-9]\d*\.\d+$|^-?0\.\d+$|^-?[1-9]\d*$|^0$)/, message: "输入占比的格式不正确！" }, { required: true, message: '请输入占比' }] },
          { type: 'input', field: 'remark', title: '备注', value: '', props: { readonly: false, maxlength: 200 }, col: { span: 11 } },
        ]
    },
    //下载加工费导入模板
    downloadTemplate() {
      window.open("../../static/excel/financial/加工费模板.xlsx", "_self");
    },
    setplatform(platform) {
      this.platform = platform;
    },
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入费用数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deletejSpeedDriveBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()

        });

    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    onsuccess(file, fileList) {
      this.fileList = fileList;
    },
    async onSubmitupload2() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true;
      this.$refs.upload2.submit()
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      // form.append("yearMonth",this.yearMonth);
      const res = importProcessingCost(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
      this.dialogVisibleSyj = false;
      this.uploadLoading = false
      this.$refs.upload2.clearFiles();
      this.fileList = [];
    },


    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {
      this.Filter.proCode = null;

      this.Filter.promotePlan = null;
      this.Filter.startAccountDate = null;
      this.Filter.endAccountDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0];
        this.Filter.endAccountDate = this.Filter.UseDate[1];
      }

      if (/^\d+$/.test(this.Filter.BatchNumber) == false && this.Filter.BatchNumber != null && this.Filter.BatchNumber != "") {
        this.$message.error('请输入正确的批次号！！！！！');
        this.Filter.BatchNumber = null;
        return;
      }
      this.Filter.platform = this.platform;
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
        isCrossBorder: 1,
      };
      this.listLoading = true;
      const res = await getLossOffShelf(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
