<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-row>
                <el-col :span="24" style="height:442px">
                    <ces-table ref="table" :that='that' :isIndex='false' :hasexpandRight="true" @cellclick='cellclick'
                        :hasexpand='true' :tableData='sellist' :tableCols='tableCols' :isSelection="true"
                        :isSelectColumn='true' :loading="sellistLoading">
                        <template slot="right">
                            <el-table-column label="日常进货量" prop="stockInQtyDaily" width="220" fixed="right">
                                <template slot="header" slot-scope="scope">
                                    <div style="display: flex; flex-direction: row; align-items: center;">
                                        <div style="margin-top: 7px;">
                                            <el-tooltip class="item" effect="dark" content="选择后，将批量同步列表中商品" placement="top">
                                                <i class="el-icon-question">
                                                    日常进货量
                                                    <br />
                                                    <el-select v-model="batchSelectIndex" style="width:139px;"
                                                        placeholder="日常进货选择" @change="batchSelectIndexChg">
                                                        <el-option :value="'1'" label="日常进货量1日销量"></el-option>
                                                        <el-option :value="'3'" label="日常进货量3日销量"></el-option>
                                                        <el-option :value="'7'" label="日常进货量7日销量"></el-option>
                                                        <el-option :value="'15'" label="日常进货量15日销量"></el-option>
                                                        <el-option :value="'30'" label="日常进货量30日销量"></el-option>
                                                    </el-select>
                                                </i>
                                            </el-tooltip>
                                        </div>

                                        <div style="margin-top: 20px;">
                                            <el-select v-model="dayvalue" filterable placeholder="请选择"
                                                @change="neiSelectIndexChg">
                                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>


                                </template>
                                <template slot-scope="{row}">
                                    <!-- <el-select v-model="row.qtyDaily">
                                        <el-option v-for="item in row.dailyQties" :value="item.value" :label="item.label">
                                        </el-option>
                                    </el-select> -->
                                    <span>{{ row.qtyDaily }}</span>
                                    <el-slider v-model="row.value2" :step="1" :max="15" :key="row.goodsCode"
                                        @change="slidechange(sellist.indexOf(row), $event)" show-stops>
                                    </el-slider>
                                </template>
                            </el-table-column>
                            <el-table-column label="活动进货量" prop="stockInQtyPromotion" width="130" fixed="right">
                                <template slot="header" slot-scope="scope">
                                    <el-tooltip class="item" effect="dark" content="例如双11、双12、618大促。活动进货需要走流程审批。"
                                        placement="top">
                                        <i class="el-icon-question">
                                            活动进货量
                                        </i>
                                    </el-tooltip>
                                    <br>
                                    <el-tooltip class="item" effect="dark" content="点批量将同步数量给列表中所有行" placement="top">
                                        <i>
                                            <el-input-number v-model="batchStockInQtyPromotion" style="width:70px"
                                                :controls="false" :min="0" :max="1000000" :precision="0" :disabled="isKJ">
                                            </el-input-number>
                                            <el-button type="text" size="mini"
                                                @click="batchStockInQtyPromotionSync" :disabled="isKJ">批量</el-button>
                                        </i>
                                    </el-tooltip>
                                </template>
                                <template slot-scope="{row}">
                                    <el-input-number v-model="row.stockInQtyPromotion"
                                        @change="changehdjhl(sellist.indexOf(row), $event)" style="width:100%"
                                        :controls="false" :min="0" :max="1000000" :precision="0" :disabled="isKJ"></el-input-number>
                                </template>
                            </el-table-column>
                            <!-- //总量 -->
                            <el-table-column label="活动进货量" prop="mysummary" width="130" fixed="right">
                                <template slot="header" slot-scope="scope">
                                    <!-- <el-tooltip class="item" effect="dark" content="例如双11、双12、618大促。活动进货需要走流程审批。" placement="top">
                                       <i class="el-icon-question">
                                        活动总量
                                        </i>
                                    </el-tooltip>
                                    <br>    -->
                                    活动总量
                                    <!-- <el-tooltip class="item" effect="dark" content="点批量将同步数量给列表中所有行" placement="top">
                                        <i>
                                        <el-input-number v-model="batchStockInQtyPromotion" style="width:70px" :controls="false" :min="0" :max="1000000" :precision="0" >
                                        </el-input-number>
                                        <el-button  type="text"  size="mini"  @click="batchStockInQtyPromotionSync">批量</el-button>
                                    </i>
                                    </el-tooltip> -->
                                </template>
                                <template slot-scope="{row}">
                                    {{ row.mysummary }}
                                    <!-- <el-input-number v-model="row.stockInQtyPromotion" style="width:100%" :controls="false" :min="0" :max="1000000" :precision="0"></el-input-number> -->
                                </template>
                            </el-table-column>
                            <el-table-column label="仓库" width="130" fixed="right">
                                <template slot="header" slot-scope="scope">
                                    仓库
                                    <br/>
                                    <el-select v-model="allWarehouse" placeholder="请选择仓库" filterable clearable style="width: 250px">
                                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                            :value="item.wms_co_id" />
                                    </el-select>
                                    <el-button type="text" size="mini"
                                                @click="batchStockInWarehouseSync">批量</el-button>
                                </template>
                                <template slot-scope="{row}">
                                    <el-select v-model="row.warehouse" placeholder="请选择仓库" filterable clearable style="width: 250px">
                                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                                            :value="item.wms_co_id" />
                                    </el-select>
                                </template>
                            </el-table-column>
                        </template>
                    </ces-table>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px;">
                    <el-button @click="onClose">关闭</el-button>
                    <el-button type="primary" @click="onSave(true)">确认申报</el-button>
                </el-col>
            </el-row>
        </template>

        <!-- 编码明细 -->
        <el-dialog :visible.sync="dialogVisible" title="详情信息" width="1200" v-dialogDrag append-to-body>
            <procodedetail :filter="filterdetail" ref="procodedetail"></procodedetail>
        </el-dialog>

        <vxe-modal v-model="visiblepopoverdetail" :width="750" :position="{ top: 380, left: '100px' }" resize mask-closable>
          <template #default>
            <template>
                <el-table :data="detaillist" border max-height="300" :loading="popoverdetailloding">
                    <el-table-column width="auto" property="goodsCode" label="商品编码"></el-table-column>
                    <el-table-column width="auto" property="price" label="单价">
                        <template slot-scope="scope">
                            <span>{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="60" property="count" label="数量">
                        <template slot-scope="scope">
                            <span>{{ scope.row.count }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
          </template>
        </vxe-modal>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatSecondNewToHour } from "@/utils/tools";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import codedpurchase from './codedpurchase.vue'
import { ApplyStockGoods, ApplyStockGoodsSetMoreInfo, getPurchaseOrderDetail, getPurOrderAnalysisForOperate,IsCrossBorderDept } from "@/api/inventory/goodscodestock"
import { formatNoLink } from "@/utils/tools";
import procodedetail from './procodedetail.vue'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import {GetCodeSalesThemeanalysis_TikTokPageList} from '@/api/kjerp/ThirdPartyServices'
import store from '@/store'

var formatSecondToHour1 = function (time) {
    return formatSecondNewToHour(time);
}
const tableCols = [
    { istrue: true, prop: 'picture', label: '图片', width: '70', type: 'images', fixed: 'left' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', fixed: 'left', type: 'html', formatter: (row) => formatNoLink(row.goodsCode) },
    { istrue: true, prop: 'goodsName', label: '商品名称', minwidth: '140', },

    { istrue: true, prop: 'styleCode', label: '系列编码', width: '120', },
    { istrue: true, prop: 'personUsableQty', label: '个人可用数', width: '70', },
    { istrue: true, prop: 'purchaseInTransitQty', label: '采购在途数', width: '70', type: 'html', formatter: (row) => formatNoLink(row.purchaseInTransitQty) },
    { istrue: true, prop: 'purchaseInTransitAvgMin', label: '平均在途时长', width: '70', formatter: (row) => formatSecondToHour1(row.purchaseInTransitAvgMin) },
    { istrue: true, prop: 'warehouseSpace', label: '仓位信息',type: 'html', width: '140'  },
   //{ istrue: true, label: '趋势图', width: '80', type: 'click', handle: (that, row) => that.getbirchart(row.goodsCode, 30, 0) },
    { istrue: true, prop: 'salesDay', label: '日销量', width: '80', },
    { istrue: true, prop: 'salesDay3', label: '3天销量', width: '80', },
    { istrue: true, prop: 'salesDay3', label: '3天均销量', width: '80', formatter: (row) => (row.salesDay3 / 3).toFixed(0) },
    { istrue: true, prop: 'salesDay7', label: '7天销量', width: '80', },
    { istrue: true, prop: 'salesDay7', label: '7天均销量', width: '80', formatter: (row) => (row.salesDay7 / 7).toFixed(0) },
    { istrue: true, prop: 'salesDay15', label: '15天销量', width: '80', },
    { istrue: true, prop: 'salesDay15', label: '15天均销量', width: '80', formatter: (row) => (row.salesDay15 / 15).toFixed(0) },
    { istrue: true, prop: 'salesDay30', label: '30天销量', width: '80', },
    { istrue: true, prop: 'salesDay30', label: '30天均销量', width: '80', formatter: (row) => (row.salesDay30 / 30).toFixed(0) },
    // { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: '80',  formatter: (row) => (row.turnoverDays).toFixed(2) },
    // { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: '80',  formatter: (row) => (row.turnoverDays3).toFixed(2) },
    // { istrue: true, prop: 'salesDay3', label: '3天均销量', width: '80',  formatter: (row) => row.salesDay3   },
    // { istrue: true, prop: 'salesDay7', label: '7天均销量', width: '80',  formatter: (row) => row.salesDay7  },
    // { istrue: true, prop: 'salesDay15', label: '15天均销量', width: '80',  formatter: (row) => row.salesDay15  },
    // { istrue: true, prop: 'salesDay30', label: '30天均销量', width: '80', formatter: (row) => row.salesDay30  },
    { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: '80', formatter: (row) => (row.turnoverDays).toFixed(2) },
    { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: '80', formatter: (row) => (row.turnoverDays3).toFixed(2) },

    // { istrue: true, prop: 'stockInQtyDaily', label: '日常进货量', width: '100', fixed:'right' },
    // { istrue: true, prop: 'stockInQtyPromotion', label: '活动进货量', width: '100', fixed:'right'  },
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");


export default {
    name: "ApplyStockGoodsForm",
    components: { cesTable, MyContainer, MyConfirmButton, codedpurchase, procodedetail },
    data() {
        return {
            that: this,
            options: [{
                value: '1',
                label: '1天'
            }, {
                value: '3',
                label: '3天'
            }, {
                value: '7',
                label: '7天'
            }, {
                value: '15',
                label: '15天'
            }],
            dayvalue: '',
            platform: 0,
            allWarehouse: null,
            batchSelectIndex: '',
            batchStockInQtyPromotion: null,
            sellist: [],
            detaillist: [],
            warehouselist: [],
            sellistLoading: false,
            visiblepopoverdetail: false,
            popoverdetailloding: false,
            tableCols: tableCols,
            mode: 3,
            filterdetail: {
                goodsCode: null,
                startDate: null,
                endDate: null,
                timerange: [startDate, endDate]
            },
            filterchart: {
                startDate: null,
                endDate: null,
                timerange: [startDate, endDate]
            },
            form: {
            },
            value2: 0,
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式
            dialogHisVisible: false,
            dialogVisible: false,
            selindex: 0,
            isKJ:false
        };
    },
    async mounted() {
        this.getUser()
        await this.init();
    },
    computed: {

    },
    methods: {
        async getUser() {
            const { data } = await IsCrossBorderDept()
            this.isKJ = data
        },
        async init() {
            var res3 = await getAllWarehouse();
            this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
        },
        changehdjhl(i, e) {
            this.sellist.forEach((item, index) => {
                if (i == index) {
                    item.mysummary = item.qtyDaily + e
                }
            })

        },

        slidechange(index, newval) {
            if (newval >= 0) {
                this.newval = newval;
            }
            if (index >= 0) {
                this.selindex = index;
            }

            this.sellist.forEach((item, i) => {
                if (i == index) {
                    item.newval = newval;
                    item.qtyDaily = newval * (this.batchSelectIndex == 1 ? item.salesDay : this.batchSelectIndex == 3 ? (item.salesDay3 / 3).toFixed(0) : this.batchSelectIndex == 7 ? (item.salesDay7 / 7).toFixed(0) : this.batchSelectIndex == 15 ? (item.salesDay15 / 15).toFixed(0) : this.batchSelectIndex == 30 ? (item.salesDay30 / 30).toFixed(0) : 0);

                    item.mysummary = item.stockInQtyPromotion + newval * (this.batchSelectIndex == 1 ? item.salesDay : this.batchSelectIndex == 3 ? (item.salesDay3 / 3).toFixed(0) : this.batchSelectIndex == 7 ? (item.salesDay7 / 7).toFixed(0) : this.batchSelectIndex == 15 ? (item.salesDay15 / 15).toFixed(0) : this.batchSelectIndex == 30 ? (item.salesDay30 / 30).toFixed(0) : 0);

                }
            })
        },
        batchStockInQtyPromotionSync() {
            this.sellist.forEach((row) => {
                row.stockInQtyPromotion = this.batchStockInQtyPromotion;
                row.mysummary = row.qtyDaily + row.stockInQtyPromotion
            });
        },
        batchStockInWarehouseSync() {
            console.log('仓库细腻', this.allWarehouse)
            this.sellist.forEach((row) => {
                row.warehouse = Number(this.allWarehouse)
                console.log('赋值仓库', row.warehouse)
            });
        },
        async batchSelectIndexChg(a) {
            let goodsCodes = []
            goodsCodes = this.sellist.map((item) => {
                return item.goodsCode
            })
            const res = await GetCodeSalesThemeanalysis_TikTokPageList({goodsCodes})
            res.data.sort((a, b) => {
                if (a.goodsCode === b.goodsCode) {
                    return new Date(a.date) - new Date(b.date);
                }
                return a.goodsCode.localeCompare(b.goodsCode);
            });
            let arr = []
            goodsCodes.forEach(item=>{
                const filterData = res.data.filter((data) => data.goodsCode === item);
                let salesDay = 0
                let salesDay3 = 0
                let salesDay7 = 0
                let salesDay15 = 0
                let salesDay30 = 0
                filterData.forEach(data =>{
                    let date = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
                    let diff = dayjs(date).diff(dayjs(data.date), 'day')
                    if(diff == 0){
                        salesDay += data.qty
                    }
                    if(diff <= 3){
                        salesDay3 += data.qty
                    }
                    if(diff <= 7){
                        salesDay7 += data.qty
                    }
                    if(diff <= 15){
                        salesDay15 += data.qty
                    }
                    if(diff <= 30){
                        salesDay30 += data.qty
                    }
                })
                arr.push({
                    goodsCode: item,
                    salesDay: salesDay,
                    salesDay3: salesDay3,
                    salesDay7: salesDay7,
                    salesDay15: salesDay15,
                    salesDay30: salesDay30
                })
            })
            this.sellist.forEach((item) => {
                let filterItem = arr.find((data) => data.goodsCode === item.goodsCode);
                this.$set(item, 'salesDay', filterItem.salesDay);
                this.$set(item, 'salesDay3', filterItem.salesDay3);
                this.$set(item, 'salesDay7', filterItem.salesDay7);
                this.$set(item, 'salesDay15', filterItem.salesDay15);
                this.$set(item, 'salesDay30', filterItem.salesDay30);
            })
            let v = parseInt(a);
            this.sellist.forEach((item) => {
                let c = item.newval ? item.newval : item.value2 ? item.value2 : 0;
                let d = v == 1 ? item.salesDay : v == 3 ? (item.salesDay3 / 3).toFixed(0) : v == 7 ? (item.salesDay7 / 7).toFixed(0) : v == 15 ? (item.salesDay15 / 15).toFixed(0) : v == 30 ? (item.salesDay30 / 30).toFixed(0) : 0;
                item.qtyDaily = c * d;
                item.mysummary = item.stockInQtyPromotion + (item.newval ? item.newval : item.value2 ? item.value2 : 0) * (v == 1 ? item.salesDay : v == 3 ? (item.salesDay3 / 3).toFixed(0) : v == 7 ? (item.salesDay7 / 7).toFixed(0) : v == 15 ? (item.salesDay15 / 15).toFixed(0) : v == 30 ? (item.salesDay30 / 30).toFixed(0) : 0);

                if (!item.qtyDaily) {
                    item.qtyDaily = 0;
                }
            });
            console.log(a,'a');

        },
        neiSelectIndexChg(a) {
            let v = parseInt(a);
            this.sellist.forEach((item) => {
                item.value2 = parseInt(v);
                item.qtyDaily = v * (this.batchSelectIndex == 1 ? item.salesDay : this.batchSelectIndex == 3 ? (item.salesDay3 / 3).toFixed(0) : this.batchSelectIndex == 7 ? (item.salesDay7 / 7).toFixed(0) : this.batchSelectIndex == 15 ? (item.salesDay15 / 15).toFixed(0) : this.batchSelectIndex == 30 ? (item.salesDay30 / 30).toFixed(0) : 0);
                // debugger
                item.mysummary = item.stockInQtyPromotion + item.qtyDaily;

                if (!item.qtyDaily) {
                    item.qtyDaily = 0;
                }
            });

        },
        async loadData({ selRows }) {
            this.pageLoading = true;
            let dts = selRows.map((item) => {
                let d = { ...item, stockInQtyDaily: 0, stockInQtyPromotion: 0, };

                d.salesDay = d.salesDay ? d.salesDay : 0;
                d.salesDay3 = d.salesDay3 ? d.salesDay3 : 0;
                d.salesDay7 = d.salesDay7 ? d.salesDay7 : 0;
                d.salesDay15 = d.salesDay15 ? d.salesDay15 : 0;
                d.salesDay30 = d.salesDay30 ? d.salesDay30 : 0;

                d.dailyQties = [];
                //d.dailyQties.push({value:0,label:'-'});
                d.dailyQties.push({ value: '1', label: `1日销量(${d.salesDay})` });
                d.dailyQties.push({ value: '3', label: `3日销量(${d.salesDay3})` });
                d.dailyQties.push({ value: '7', label: `7日销量(${d.salesDay7})` });
                d.dailyQties.push({ value: '15', label: `15日销量(${d.salesDay15})` });
                d.dailyQties.push({ value: '30', label: `30日销量(${d.salesDay30})` });

                return d;
            });

            let res = await ApplyStockGoodsSetMoreInfo({ goodsDetails: dts });
            if (res && res.success) {
                dts = res.data.goodsDetails;
                dts.forEach((d) => {
                    d.qtyDaily = null;
                    d.dailyQties = [];
                    d.warehouse = null;
                    //d.dailyQties.push({value:0,label:'-'});
                    d.dailyQties.push({ value: '1', label: `1日销量(${d.salesDay})` });
                    d.dailyQties.push({ value: '3', label: `3日销量(${d.salesDay3})` });
                    d.dailyQties.push({ value: '7', label: `7日销量(${d.salesDay7})` });
                    d.dailyQties.push({ value: '15', label: `15日销量(${d.salesDay15})` });
                    d.dailyQties.push({ value: '30', label: `30日销量(${d.salesDay30})` });
                });
            }
            this.pageLoading = false;
            this.sellist = dts;
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        },
        async save() {



            let saveData = {
                goodsDetails: [...this.sellist]
            };
            try {
                saveData.goodsDetails.forEach(row => {
                    let v = row.qtyDaily;
                    console.log("vvv", v)
                    if (v == null || v == "") {
                        //this.$message.warning(row.goodsCode + "请选择日常进货量！");
                        //throw "校验失败";
                        row.stockInQtyDaily = 0;
                    } else {
                        row.stockInQtyDaily = v;
                    }
                    // if(v != '1') row.stockInQtyDaily=row['salesDay']+v
                    // else  row.stockInQtyDaily=row['salesDay']
                });
            } catch (e) {
                console.log(e);
                return;
            }
            this.pageLoading = true;
            let rlt = await ApplyStockGoods(saveData);
            if (rlt && rlt.success) {
                if (!rlt.msg) {
                    this.$message.success("申报成功！");
                }
                else {
                    this.$message.warning(rlt.msg);
                }
            }
            this.pageLoading = false;
            return (rlt && rlt.success);
        },
        async clickProfit(row) {
            this.dialogVisible = true;
            this.filterdetail.goodsCode = row.goodsCode
            this.filterdetail.startDate = null;
            this.filterdetail.endDate = null;
            if (this.filterdetail.timerange) {
                this.filterdetail.startDate = this.filterdetail.timerange[0];
                this.filterdetail.endDate = this.filterdetail.timerange[1];
            }
            this.$nextTick(() => {
                this.$refs.procodedetail.clearData();
                this.$refs.procodedetail.onSearch();
            })
        },
        async cellclick(row, column, cell, event) {
            if (column?.property == 'goodsCode')
                await this.clickProfit(row)
            if (column?.property == 'purchaseInTransitQty')
                await this.getPurchaseOrderDetail(row.goodsCode)
        },
        async getPurchaseOrderDetail(goodsCode) {
            this.visiblepopoverdetail = true;
            this.popoverdetailloding = true;
            var para = { goodsCode: goodsCode };
            var res = await getPurchaseOrderDetail(para);
            if (!(res.code == 1 && res.data)) return
            this.detaillist = res.data;
            this.popoverdetailloding = false;
        },
        async getbirchart(goodsCode, number, type) {
            this.startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            this.endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filterchart.timerange = [this.startDate, this.endDate];
            this.filterchart.startDate = null;
            this.filterchart.endDate = null;
            if (this.filterchart.timerange) {
                this.filterchart.startDate = this.startDate;
                this.filterchart.endDate = this.endDate;
            }
            this.goodsCode = goodsCode;
            this.timeNum = number;
            this.timeType = type;
            let loadingInstance = Loading.service();
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: type, ...this.filterchart };
            //console.log('数据来了', params);
            await getPurOrderAnalysisForOperate(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            await this.$refs.buschar.initcharts()
            loadingInstance.close();
        },
    },
};
</script>
