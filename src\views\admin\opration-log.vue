<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 94%;">
      <el-tab-pane label="页面日志" name="first" style="height: 100%;">
        <PageReqLogSumList></PageReqLogSumList>
      </el-tab-pane>
      <el-tab-pane label="页面操作日志" name="controls" style="height: 100%;" :lazy="true">
        <pageOperationLogl />
      </el-tab-pane>
      <el-tab-pane label="接口日志" name="second" style="height: 100%;" :lazy="true">
        <OprationLogOrg></OprationLogOrg>
      </el-tab-pane>
      <el-tab-pane label="宝贝ID日志" name="forth" style="height: 100%;" :lazy="true">
        <productIdLog></productIdLog>
      </el-tab-pane>
      <el-tab-pane label="AI对话日志" name="fifth" style="height: 100%;" :lazy="true">
        <AiConversationsLog></AiConversationsLog>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>

import MyContainer from '@/components/my-container'
import OprationLogOrg from "@/views/admin/opration-log-org"
import PageReqLogSumList from "@/views/admin/PageReqLogSumList"
import pageOperationLogl from "@/views/admin/pageOperationLogl"
import productIdLog from './productIdLog.vue'
import AiConversationsLog from './AiConversationsLog.vue'
export default {
  name: 'OprationLog',
  components: { MyContainer, OprationLogOrg, PageReqLogSumList, pageOperationLogl,productIdLog,AiConversationsLog },//, ElRow
  data() {
    return {
      pageLoading: false,
      activeName: "first"
    };
  },
  mounted() {
  },
  methods: {
  }
}
</script>
