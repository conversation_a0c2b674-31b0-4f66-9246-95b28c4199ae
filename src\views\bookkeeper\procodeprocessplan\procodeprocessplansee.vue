<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form ref="addForm" :model="addForm" label-width="120px">
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="processPlanName" label="方案名称">
                            <el-input v-model="addForm.processPlanName" auto-complete="off" placeholder="请填写方案名称"
                                disabled maxlength="20" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="startDate" label="观察开始日期">
                            <el-date-picker v-model="addForm.startDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                disabled type="date" placeholder="请选择观察开始日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="curDate" label="日报参考日期">
                            <el-date-picker v-model="addForm.curDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                disabled type="date" placeholder="请选择日报参考日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                        <el-form-item prop="endDate" label="观察结束日期">
                            <el-date-picker v-model="addForm.endDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                disabled type="date" placeholder="请选择观察结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="procodeProcessPlanColCheck1" label="被观察指标">
                            <el-checkbox-group v-model="procodeProcessPlanColCheckList" size="mini">
                                <el-checkbox v-for="item in procodeProcessPlanColList" :key="item.value" disabled
                                    :label="item.label" :value="item.value" border></el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row v-if="permissionDelDtl">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-button type="primary" @click="onBatchDeleteDtl" :loading="deldtlLoading">批量删除明细</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <template>
            <vxetablebase :id="'procodeprocessplanadd_see'" :border="true" :align="'center'" :isRemoteSort="false"
                :tablekey="'procodeprocessplanadd_see'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
                :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
                :tableData='addForm.proList' :tableCols='tableCols' :loading="listLoading" :tableHandles='tableHandles'
                @select='onSelectDtl' :showheaderoverflow="false" style="width:100%;height:95%;margin: 0" :xgt="9999">
                <template v-slot:operateProcessPlan="{ row }">
                    <div v-if="!operateProcessPlanVisable">
                        {{ row.operateProcessPlan }}
                    </div>
                    <div v-else>
                        <el-select filterable v-model="row.operateProcessPlanValue" placeholder="请选择运营处理方案" clearable>
                            <el-option v-for="item in operateProcessPlanList" :key="item" :label="item" :value="item" />
                        </el-select>
                    </div>
                </template>
            </vxetablebase>
        </template>
        <template #footer>
            <div class="dialog-footer">
                <my-confirm-button type="submit" :loading="addLoading" @click="onProcessPlan">
                    提交运营处理方案
                </my-confirm-button>
            </div>
        </template>
    </my-container>
</template>
<script>
import { platformlist, formatPlatform, formatTime, } from "@/utils/tools";
import dayjs from 'dayjs';
import MyContainer from '@/components/my-container';
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetProcodeProcessPlanColList, GetProcodeProcessPlanById2, WriteOperateProcessPlan,
    DeleteProcodeProcessPlanProByIds
} from '@/api/bookkeeper/procodeprocessplan'

const tableCols = [
];

const tableHandles1 = [

];

const operateProcessPlanList = [
    "正常售卖",
    "提升利润",
    "战略产品",
    "3元3件店铺",
    "活动低价",
    "下架链接",
    "虚拟链接（赠品、邮费）",
];

export default {
    name: 'procodeprocessplansee',
    components: { MyContainer, MyConfirmButton, vxetablebase },
    props: {
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            platformlist: platformlist,
            tableCols: [],
            tableHandles: tableHandles1,
            procodeProcessPlanColKeys: [],
            procodeProcessPlanColList: [],
            procodeProcessPlanColCheckList: [],
            addLoading: false,
            selProCodes: [],
            permissionDelDtl: false,//是否有权限删除明细
            addForm: {
                processPlanName: "",
                curDate: null,
                startDate: null,
                endDate: null,
                procodeItemCols: "",
                procodeItemColNames: "",
                proList: [],
            },
            addFormTemp: {},
            operateProcessPlanVisable: true,
            listLoading: false,
            summaryarry: {},
            operateProcessPlanList: operateProcessPlanList,
            selectAddRows: [],
            deldtlLoading: false,
        }
    },
    async mounted() {
    },
    methods: {
        async loadData(seeparam) {
            console.log(seeparam, "seeparam");
            this.pageLoading = false;
            this.pageLoading = true;
            //获取详情主表
            let main = await GetProcodeProcessPlanById2({ id: seeparam.id });
            if (main?.success == true) {
                this.addFormTemp = main.data;
                this.permissionDelDtl = main.data.permissionDelDtl;
                //拼指标勾选
                this.procodeProcessPlanColKeys = main.data.procodeItemCols.split(',')
                this.getProcodeProcessPlanColList();
                this.procodeProcessPlanColCheckList = main.data.procodeItemColNames.split(',')

                //拼明细列头
                await this.onSeeDtl();

                //拼数据源
                this.addForm = this.addFormTemp;
                this.selProCodes = this.addForm.proList.map(item => item.proCode);
            }
            //获取详情明细列头
            this.selectAddRows = [];
            this.pageLoading = false;
        },
        async getProcodeProcessPlanColList() {
            this.procodeProcessPlanColList = [];
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit1Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit1Rate", label: "毛一利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit2Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit2Rate", label: "毛二利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit3Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit3Rate", label: "毛三利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit4Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit4Rate", label: "毛四利率" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit1AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit1AfterRate", label: "毛一利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit2AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit2AfterRate", label: "毛二利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit3AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit3AfterRate", label: "毛三利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "yyProfit4AfterRate") != null) {
                this.procodeProcessPlanColList.push({ value: "yyProfit4AfterRate", label: "毛四利率(减退款)" });
            }
            if (this.procodeProcessPlanColKeys.find(f => f == "Profit4Rate") != null) {
                this.procodeProcessPlanColList.push({ value: "Profit4Rate", label: "净利率" });
            }
        },
        async onSeeDtl() {
            this.listLoading = true;
            let procodeProcessPlanColCheckList_Order = [];//用于指标按照顺序，避免指标是乱的
            this.procodeProcessPlanColList.forEach(f => {//循环的目的就是保持指标按照顺序
                let find = this.procodeProcessPlanColCheckList.find(s => s == f.label);
                if (find) {
                    procodeProcessPlanColCheckList_Order.push(f);
                }
            });

            let curymd = dayjs(this.addFormTemp.curDate).format('YYYY-MM-DD');
            let curymd_3 = dayjs(this.addFormTemp.curDate).add(-3, 'day').format('YYYY-MM-DD');
            let curymd_7 = dayjs(this.addFormTemp.curDate).add(-7, 'day').format('YYYY-MM-DD');

            this.tableCols = [];
            let newcols = [
                { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
                { istrue: true, prop: 'platform', label: '平台', width: '40', sortable: 'custom', formatter: row => formatPlatform(row.platform) },
                { istrue: true, prop: 'shopName', label: '店铺名称', width: '80', sortable: 'custom' },
                { istrue: true, prop: 'styleCode', label: '系列编码', width: '80', sortable: 'custom' },
                { istrue: true, prop: 'proCode', label: '产品ID', width: '100', sortable: 'custom' },
                { istrue: true, prop: 'proName', label: '产品名称', width: '80', sortable: 'custom' },
                { istrue: true, prop: 'groupId', label: '运营组', width: '55', sortable: 'custom', formatter: (row) => row.groupName },
                { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '55', sortable: 'custom', formatter: (row) => row.operateSpecialUserName },
                { istrue: true, prop: 'userId', label: '运营助理', width: '55', sortable: 'custom', formatter: (row) => row.userName },
                { istrue: true, prop: 'onTime', label: '上架时间', width: '80', sortable: 'custom' },
                { istrue: true, prop: 'onTimeCount', label: '上架天数', width: '40', sortable: 'custom' },
                { istrue: true, prop: 'productCategoryId', label: '类目', width: '80', sortable: 'custom', formatter: (row) => row.productCategoryName },
                { istrue: true, prop: 'operateProcessPlan', label: '运营处理方案', width: '150' }
            ];

            let newcols2 = [
                { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '60', formatter: (row) => !row.orderCount ? " " : row.orderCount },
                { istrue: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '80', formatter: (row) => !row.payAmont ? " " : row.payAmont.toFixed(2) },
                { istrue: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv?.toFixed(2) },
                { istrue: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '60', formatter: (row) => !row.advratio ? " " : row.advratio.toFixed(2) + "%" },

                { istrue: true, prop: 'yyProfit1', label: '毛一利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '销售金额-总商品成本-采购运费-代发成本', formatter: (row) => !row.yyProfit1 ? " " : row.yyProfit1.toFixed(2) },
                { istrue: true, prop: 'yyProfit1Rate', label: '毛一利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.yyProfit1Rate ? " " : row.yyProfit1Rate.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit2', label: '毛二利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛一利润-平台扣点-包装费-快递费', formatter: (row) => !row.yyProfit2 ? " " : row.yyProfit2?.toFixed(2) },
                { istrue: true, prop: 'yyProfit2Rate', label: '毛二利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.yyProfit2Rate ? " " : row.yyProfit2Rate.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit3', label: '毛三利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3 ? " " : row.yyProfit3?.toFixed(2) },
                { istrue: true, prop: 'yyProfit3Rate', label: '毛三利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3Rate ? " " : row.yyProfit3Rate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit4', label: '毛四利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛三利润-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4 ? " " : row.yyProfit4?.toFixed(2) },
                { istrue: true, prop: 'yyProfit4Rate', label: '毛四利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛四利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4Rate ? " " : row.yyProfit4Rate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit1After', label: '毛一(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit1AfterRate', label: '毛一利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1AfterRate ? " " : row.yyProfit1AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit2After', label: '毛二(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit2AfterRate', label: '毛二利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2AfterRate ? " " : row.yyProfit2AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit3After', label: '毛三(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit3AfterRate', label: '毛三利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3AfterRate ? " " : row.yyProfit3AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'yyProfit4After', label: '毛四(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛四-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After?.toFixed(2) },
                { istrue: true, prop: 'yyProfit4AfterRate', label: '毛四利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛四（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4AfterRate ? " " : row.yyProfit4AfterRate?.toFixed(2) + '%' },
                { istrue: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', sortable: 'custom', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
                { istrue: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate).toFixed(2) + '%' },
                {
                    istrue: true, rop: '', label: `退款`, width: '120', merge: true, prop: 'mergeField',
                    cols: [
                        { istrue: true, prop: 'yyRefundAmontBefore', label: '发货前退款', sortable: 'custom', width: '100', type: 'custom' },
                        { istrue: true, prop: 'yyRefundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontBeforeRate ? " " : (row.yyRefundAmontBeforeRate).toFixed(2) + '%' },
                        { istrue: true, prop: 'yyRefundAmontAfter', label: '发货后退款', sortable: 'custom', width: '100', type: 'custom' },
                        { istrue: true, prop: 'yyRefundAmontAfterRate', label: '发货后退款率', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontAfterRate ? " " : (row.yyRefundAmontAfterRate).toFixed(2) + '%' },
                        { istrue: true, prop: 'yyRefundAmont', label: '总退款金额', sortable: 'custom', width: '100', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.yyRefundAmont ? " " : row.yyRefundAmont.toFixed(2) },
                    ]
                },
            ];

            let c1 = { align: 'center', prop: '', label: curymd, merge: true, width: '80', cols: [] }
            let c2 = { align: 'center', prop: '', label: "前3天", merge: true, width: '80', cols: [] }
            let c3 = { align: 'center', prop: '', label: "前7天", merge: true, width: '80', cols: [] }
            //当天，前3天，前7天
            procodeProcessPlanColCheckList_Order.forEach(w => {
                //拼列头
                if (w.value.indexOf("Rate") > 0) {
                    c1.cols.push({ istrue: true, prop: curymd + w.value, label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[curymd + w.value] + '%') });
                    c2.cols.push({ istrue: true, prop: curymd_3 + w.value, label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[curymd_3 + w.value] + '%') });
                    c3.cols.push({ istrue: true, prop: curymd_7 + w.value, label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[curymd_7 + w.value] + '%') });
                }
                else {
                    c1.cols.push({ istrue: true, prop: curymd + w.value, label: w.label, width: '80', sortable: 'custom' });
                    c2.cols.push({ istrue: true, prop: curymd_3 + w.value, label: w.label, width: '80', sortable: 'custom' });
                    c3.cols.push({ istrue: true, prop: curymd_7 + w.value, label: w.label, width: '80', sortable: 'custom' });
                }
                //循环数据源
                this.addFormTemp.proList.forEach(f => {
                    //拼数据源
                    f[(curymd + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd && x.itemKey == w.value)?.itemValue ?? 0;
                    f[(curymd_3 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_3 && x.itemKey == w.value)?.itemValue ?? 0;
                    f[(curymd_7 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_7 && x.itemKey == w.value)?.itemValue ?? 0;
                })
            });
            newcols.push(c1);
            newcols.push(c2);
            newcols.push(c3);

            //循环观察日期
            for (let i = dayjs(this.addFormTemp.startDate); i <= dayjs(this.addFormTemp.endDate); i = i.add(1, 'day')) {
                let mycols = [];
                //循环指标
                procodeProcessPlanColCheckList_Order.forEach(w => {
                    //拼列头
                    if (w.value.indexOf("Rate") > 0) {
                        mycols.push({ istrue: true, prop: (i.format('YYYY-MM-DD') + w.value), label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[i.format('YYYY-MM-DD') + w.value] + '%') });
                    }
                    else {
                        mycols.push({ istrue: true, prop: (i.format('YYYY-MM-DD') + w.value), label: w.label, width: '80', sortable: 'custom' });
                    }

                    //拼数据源
                    this.addFormTemp.proList.forEach(f => {
                        f[(i.format('YYYY-MM-DD') + w.value)] = f.proItemList.find(x => x.itemDateStr == i.format('YYYY-MM-DD') && x.itemKey == w.value)?.itemValue ?? 0;
                    });

                });
                newcols.push({ align: 'center', prop: '', label: i.format('YYYY-MM-DD'), merge: true, prop: 'mergeField', width: '80', cols: mycols });
            }

            newcols = newcols.concat(newcols2);

            this.$nextTick(() => {
                this.tableCols = newcols;
            });
            this.listLoading = false;
        },
        //仅保存处理方案
        async onProcessPlan() {
            this.addLoading = true;
            let dtos = [];
            this.addForm.proList.forEach(f => {
                if (f.operateProcessPlanValue) {
                    dtos.push({ id: f.id, operateProcessPlan: f.operateProcessPlanValue });
                }
            });
            let res = await WriteOperateProcessPlan(dtos);
            if (res?.success == true) {
                this.$message.success('提交成功')
            }
            this.addLoading = false;
        },
        onSelectDtl(rows) {
            this.selectAddRows = rows;
        },
        //批量删除
        async onBatchDeleteDtl() {
            if (this.selectAddRows.length <= 0) {
                this.$message.warning('请至少勾选一行');
                return;
            }

            this.$confirm('删除后将不可恢复，确定要删除吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.deldtlLoading = true;
                this.addLoading = true;
                let proIds = this.selectAddRows.map(item => item.id);
                let res = await DeleteProcodeProcessPlanProByIds(proIds);
                if (res?.success == true) {
                    this.$message.success('删除成功');
                    this.selectAddRows.forEach(row => {
                        this.addForm.proList.splice(this.addForm.proList.indexOf(row), 1);
                    });
                    this.selectAddRows = [];
                }
                this.deldtlLoading = false;
                this.addLoading = false;
            }).catch(() => {
                //this.$message({ type: 'info', message: '已取消计算' });
                this.deldtlLoading = false;
                this.addLoading = false;
            });



        }
    }
}
</script>
<style scoped></style>
