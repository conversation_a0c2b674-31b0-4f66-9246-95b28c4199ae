<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-descriptions title="供应商信息" :column="4" border>
        <el-descriptions-item label="供应商编码"> <el-input v-model="detail.code" maxlength="20" :show-word-limit="true" :disabled="true"/></el-descriptions-item>
        <el-descriptions-item label="供应商名称" :span="1"><el-input v-model="detail.name" maxlength="40" :show-word-limit="true" :disabled="true"/></el-descriptions-item>
        <el-descriptions-item label="发货地址/省">
          <el-select v-model="detail.province" placeholder="请选择" :disabled="true">
             <el-option label="" :value="0"/>
             <el-option v-for="item in provinces" :key="item.code" :label="item.name" :value="item.code"/>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="发货地址/市">
          <el-select v-model="detail.city" placeholder="请选择" :disabled="true">
            <el-option label="" :value="0"/>
            <el-option v-for="item in cities" :key="item.code" :label="item.name" :value="item.code"/>
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="累计采购次数">
          <span>{{detail.totalCS}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="累计采购金额">
          <span>{{detail.totalAmont}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="未入库金额">
          <span>{{detail.nonInAmont}}</span>
        </el-descriptions-item>
      </el-descriptions>
    </template>
    <template>
     <el-table :data="detail.purchaseList" style="width: 700px;" height="'94%'">
      <el-table-column type="index" align="center" width="40"></el-table-column>
      <el-table-column prop="buyNo" label="采购单号" width="120"/>
      <el-table-column prop="purchaseDate" label="采购日期" width="150"/>
      <el-table-column prop="checkDate" label="审核日期" width="150"/>
      <el-table-column prop="totalAmont" label="总金额" width="180"/>
      <el-table-column prop="inAmont" label="已入库金额" width="180"/>
      <el-table-column prop="nonInAmont" label="未入库金额" width="180"/>
    </el-table>
    </template>

  </container>
</template>
<script>
import {getSupplierPurchaseList} from '@/api/inventory/supplier'
import {getRegion} from '@/api/admin/business'
import {formatTime,formatYesornoBool,formatSecondToHour,formatNoLink} from "@/utils/tools";
import container from '@/components/my-container/nofooter'
export default {
  name: "Users",
  components: {container},
  data() {
    return {
      filter: {
        code:null,
        name:null,
        province:null,
        city:null,
        payType:null,
      },
      detail: {purchaseList:[]},
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [],
      provinces: [],
      cities: [],
      listLoading: false,
      pageLoading: false
    };
  },
 async mounted() {
      await this.init();
  },
 methods: {
   async init() {
      var res = await getRegion({parentcode:0})
      this.provinces = res.data
    },
    async SelectProvinces(code) {
       if (code>0) {
         this.cities=[]
         var res = await getRegion({parentcode:code})
         this.cities = res.data
       }
    },
   async getDtail(supplierId,filter) {
      this.listLoading = true
      const res = await getSupplierPurchaseList({
        supplierId:supplierId,
        purchaseStartTime: (filter==null ?  null : filter.purchaseStartTime),
        purchaseEndTime: (filter==null ?  null : filter.purchaseEndTime),
        brandId: (filter==null ?  null : filter.brandId)
      })
      this.listLoading = false
      if (!res?.success) return
      this.detail = res.data
      if (this.detail.province>0) {
       await this.SelectProvinces(this.detail.province)
      }
      this.detail.province=this.detail.province==0?"":this.detail.province
      this.detail.city=this.detail.city==0?"":this.detail.city
    },
  },
};
</script>
<style scoped>
.el-card__body{
    padding: 0;
}
</style>
<style lang="scss" scoped>
.el-table th > .cell {
   padding-left: 8px;
}
.el-table .caret-wrapper {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 34px;
    width: 0;
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
}
.el-table__footer-wrapper {
    margin-top: -2px;
    font-size: 9px;
    padding: 0px;
}
.el-table .cell {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 23px;
    padding-right: 1px;
}
.el-table__footer-wrapper tbody td, .el-table__header-wrapper tbody td {
    background-color: #f1f1f1;
    color: #0756f5;
}
.ces-table-require::before{
  content:'*';
  color:red;
}
.table-wrapper {
    width: 100%;
    height: calc(100% - 35px);
    margin: 0;
 }
.el-table{
   overflow:visible !important;
   height: 99.9% !important;
   width: 99.9% !important;
}
.el-table__empty-block {
      height:550px;
      border:none;
    }
/* .el-table {
  width: 99.9% !important;
} */
 p img{ max-width: 100px}
 /* .wendang{

    img{
          max-width: 100px;
          }
} */
</style>
