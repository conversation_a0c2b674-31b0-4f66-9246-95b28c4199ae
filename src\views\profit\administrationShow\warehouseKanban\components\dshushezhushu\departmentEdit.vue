<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
      <el-scrollbar style="height: 100%">
        <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="160px" class="demo-ruleForm">
          <el-form-item label="废纸/纸箱变卖：" prop="wastePaperBoxIncome">
              <inputNumberYh @input="computedone" v-model="ruleForm.wastePaperBoxIncome" :placeholder="'废纸/纸箱变卖'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="固定资产报废卖收入：" prop="fixedAssetScrapIncome">
              <inputNumberYh @input="computedone" v-model="ruleForm.fixedAssetScrapIncome" :placeholder="'固定资产报废卖收入'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="乐捐收入：" prop="donationIncome">
              <inputNumberYh @input="computedone" v-model="ruleForm.donationIncome" :placeholder="'乐捐收入'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="其他收入：" prop="otherIncome">
              <inputNumberYh @input="computedone" v-model="ruleForm.otherIncome" :placeholder="'其他收入'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="备注：" >
            <el-input style="width:80%;" v-model.trim="ruleForm.remarks" :maxlength="100" placeholder="备注" clearable />
              <!-- <inputNumberYh @input="computedone" v-model="ruleForm.remark" :placeholder="'备注'" class="publicCss"  :fixed="2" /> -->
          </el-form-item>
  
  
          <!-- <el-form-item label="部门：">
            {{ '部门' }}
          </el-form-item> -->
          <!-- <el-form-item label="总人数：" prop="regionName">
            <inputNumberYh v-model="ruleForm.regionName" :placeholder="'总人数'" class="publicCss"  :fixed="2" />
          </el-form-item> -->

          <!-- <el-form-item label="培训人：" prop="trainer">
            <el-input style="width:80%;" v-model.trim="ruleForm.trainer" :maxlength="50" placeholder="培训人" clearable />
          </el-form-item> -->
  
          <!-- <el-form-item label="考试平均分：" prop="avgExamScore">
            <inputNumberYh v-model="ruleForm.avgExamScore" :placeholder="'考试平均分'" class="publicCss"  :fixed="2" :fixed="2" />
          </el-form-item> -->
          <!-- <el-form-item label="培训满意度：" prop="trainingSatisfaction">
            <inputNumberYh v-model="ruleForm.trainingSatisfaction" :placeholder="'培训满意度'" class="publicCss"  :fixed="2" :fixed="2"/>
          </el-form-item> -->
  
          <!-- <el-form-item label="1年以上离职人数：" prop="yearCount">
            <inputNumberYh v-model="ruleForm.yearCount" :placeholder="'1年以上离职人数'" class="publicCss"  :fixed="2" />
          </el-form-item> -->
  
  <!--
          <el-form-item label="辞职：" prop="resignCount">
              <el-input style="width:80%;" v-model.trim="ruleForm.resignCount" :maxlength="50" placeholder="辞职" clearable />
          </el-form-item>
          <el-form-item label="劝退：" prop="quitCount"> -->
            <!-- <inputNumberYh v-model="ruleForm.quitCount" :placeholder="'劝退'" class="publicCss"  :fixed="2" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.quitCount" :maxlength="50" placeholder="劝退" clearable />
          </el-form-item> -->
          <!-- <el-form-item label="自离：" prop="selfDeparture"> -->
            <!-- <inputNumberYh v-model="ruleForm.selfDeparture" :placeholder="'自离'" class="publicCss"  :fixed="2" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.selfDeparture" :maxlength="50" placeholder="自离" clearable />
  
          </el-form-item>
          <el-form-item label="试用期离职：" prop="probationQuitCount"> -->
            <!-- <inputNumberYh v-model="ruleForm.probationQuitCount" :placeholder="'试用期离职'" class="publicCss"  :fixed="2" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.probationQuitCount" :maxlength="50" placeholder="试用期离职" clearable />
  
          </el-form-item>
          <el-form-item label="正式工离职：" prop="regularQuitCount"> -->
            <!-- <inputNumberYh v-model="ruleForm.regularQuitCount" :placeholder="'正式工离职'" class="publicCss"  :fixed="2" /> -->
            <!-- <el-input style="width:80%;" v-model.trim="ruleForm.regularQuitCount" :maxlength="50" placeholder="正式工离职" clearable />
  
          </el-form-item> -->
  
          <!-- <el-form-item label="经理人数：" prop="managerCount">
            <inputNumberYh v-model="ruleForm.managerCount" :placeholder="'经理人数'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="总监人数：" prop="directorCount">
            <inputNumberYh v-model="ruleForm.directorCount" :placeholder="'总监人数'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="总经理/区负责人：" prop="generalManager">
            <inputNumberYh v-model="ruleForm.generalManager" :placeholder="'总经理/区负责人'" class="publicCss"  :fixed="2" />
          </el-form-item>
          <el-form-item label="总裁：" prop="ceoCount">
            <inputNumberYh v-model="ruleForm.ceoCount" :placeholder="'总裁'" class="publicCss"  :fixed="2" />
          </el-form-item> -->
  
        </el-form>
      </el-scrollbar>
      <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
        <el-button @click="cancellationMethod">取消</el-button>
        <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
      </div>
    </div>
  </template>
  
  <script>
  import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
  import MyConfirmButton from '@/components/my-confirm-button'
  import { warehouseIncomeDetailsSubmit } from '@/api/people/peoplessc.js';
  import checkPermission from '@/utils/permission'
  import decimal from '@/utils/decimal'
  export default {
    name: 'departmentEdit',
    components: {
      inputNumberYh, MyConfirmButton
    },
    props: {
      editInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      districtList: {
        type: Object,
        default: () => {
          return {}
        }
      },
      typeList: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data() {
      return {
        selectProfitrates: [],
        ruleForm: {
          label: '',
          name: ''
        },
        rules: {
            checkinPersonCount: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          bedSpaceNum: [
            { required: true, message: '请输入', trigger: 'blur' }
            ],
            roomNum: [
            { required: true, message: '请输入', trigger: 'blur' }
          ]
        }
      }
    },
  
    async mounted() {
      this.$nextTick(() => {
        this.$refs.refruleForm.clearValidate();
      });
      this.ruleForm = { ...this.editInfo };
    },
      methods: {
          computedone() {
              return;
          let a = this.ruleForm.totalNumWorkers ? this.ruleForm.totalNumWorkers :  0;
          let b = this.ruleForm.usedWorkstations ? this.ruleForm.usedWorkstations :  0;
  
          this.ruleForm.remainingWorkstations = decimal(a, b, 2, '-').toFixed(0);
          return decimal(a, b, 2, '-').toFixed(0);
      },
      cancellationMethod() {
        this.$emit('cancellationMethod');
      },
      submitForm(formName) {
          console.log(this.ruleForm.label, 'this.ruleForm.label');
        if (this.ruleForm.remainingWorkstations<0) {
          return this.$message.warning('剩余工位不能为负数');
        }
        this.$refs[formName].validate(async(valid) => {
            if (valid) {
              this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
              const { data, success } = await warehouseIncomeDetailsSubmit(this.ruleForm)
              if(!success){
                  return
              }
              await this.$emit("search");
  
            } else {
              console.log('error submit!!');
              return false;
            }
          });
      //   this.$confirm('是否保存?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(async () => {
      //     this.$refs[formName].validate(async(valid) => {
      //       if (valid) {
      //         const { data, success } = await warehouseIncomeDetailsSubmit(this.ruleForm)
      //         if(!success){
      //             return
      //         }
      //         await this.$emit("search");
  
      //       } else {
      //         console.log('error submit!!');
      //         return false;
      //       }
      //     });
      //   }).catch(() => {
      //   });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .publicCss {
    width: 80%;
  }
  </style>
  