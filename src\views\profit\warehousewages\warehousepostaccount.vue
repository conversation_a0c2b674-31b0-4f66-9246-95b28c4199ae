<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="仓库">
                    <el-select v-model="filter.warehouseName" style="width: 100px" size="mini" @change="onSearch">
                        <el-option label="诚信仓" value="诚信仓"></el-option>
                        <el-option label="爆款仓" value="爆款仓"></el-option>
                        <el-option label="圆通5楼" value="圆通5楼"></el-option>
                        <el-option label="邮政仓" value="邮政仓"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.accountPostCode" placeholder="一级岗位编码" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.accountPostName" placeholder="一级岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.accountTwoPostName" placeholder="二级岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.account" placeholder="账号" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onAdd">新增</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog title="岗位" :visible.sync="dialogVisibleAdd" width="50%" v-dialogDrag :close-on-click-modal="false"
            v-loading="addFromLoading">
            <template>
                <el-form ref="addForm" :model="addFormData" :rules="addFormRules" label-width="140px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="warehouseName" label="仓库名称" auto-complete="off">
                                <el-select v-model="addFormData.warehouseName" style="width:100%;">
                                    <el-option label="诚信仓" value="诚信仓"></el-option>
                                    <el-option label="爆款仓" value="爆款仓"></el-option>
                                    <el-option label="圆通5楼" value="圆通5楼"></el-option>
                                    <el-option label="邮政仓" value="邮政仓"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="account" label="账号">
                                <el-input v-model="addFormData.account" auto-complete="off" @blur="onAccountBlur"
                                    maxlength="20" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="accountUserName" label="真实姓名">
                                <el-input v-model="addFormData.accountUserName" auto-complete="off" maxlength="20" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="accountPostCode" label="一级岗位编码">
                                <el-input v-model="addFormData.accountPostCode" auto-complete="off" maxlength="20" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="accountPostName" label="一级岗位名称">
                                <el-input v-model="addFormData.accountPostName" auto-complete="off" maxlength="20" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="accountTwoPostName" label="二级岗位名称">
                                <el-input v-model="addFormData.accountTwoPostName" auto-complete="off" maxlength="20" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="dayPrice" label="白班工价">
                                <el-input-number v-model="addFormData.dayPrice" :min="0" :max="100000" placeholder="白班工价"
                                    :precision="4" style="width: 180px;">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="nightPrice" label="晚班工价">
                                <el-input-number v-model="addFormData.nightPrice" :min="0" :max="100000" placeholder="晚班工价"
                                    :precision="4" style="width: 180px;">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="getValueType" label="取值类型">
                                <el-select v-model="addFormData.getValueType" style="width: 180px;">
                                    <el-option label="从订单日志取值" value="从订单日志取值"></el-option>
                                    <el-option label="从聚水潭工作量报表取值" value="从聚水潭工作量报表取值"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="addFormData.getValueType == '从订单日志取值'">
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="orderLogGetValueColNames" label="订单日志取值名称">
                                <el-input v-model="addFormData.orderLogGetValueColNames" auto-complete="off"
                                    style="width: 85%;" disabled />
                                <el-button style="float: right ; font-size:14px" type="text" @click="onSel(1)">选择
                                </el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="orderLogGetValueType" label="计件类型">
                                <el-select v-model="addFormData.orderLogGetValueType" style="width: 180px;">
                                    <el-option label="按订单计件" value="按订单计件"></el-option>
                                    <el-option label="按商品计件" value="按商品计件"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="addFormData.getValueType == '从聚水潭工作量报表取值'">
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="jstWorkGetValueColNames" label="工作量取值列名称">
                                <el-input v-model="addFormData.jstWorkGetValueColNames" auto-complete="off"
                                    style="width: 85%;" disabled />
                                <el-button style="float: right ; font-size:14px" type="text" @click="onSel(2)">选择
                                </el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <!-- <el-button @click="dialogVisibleAdd = false">取 消</el-button> -->
                    <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSave" />
                </span>
            </template>
        </el-dialog>
        <el-dialog title="选择" :visible.sync="dialogVisibleAdd_Sel" width="25%" v-dialogDrag :close-on-click-modal="false">
            <template>
                <el-row v-if="addFormData.getValueType == '从订单日志取值'">
                    <el-col>
                        <el-select v-model="addFormDataSel" placeholder="" :clearable="true" filterable multiple
                            style="width:80%;">
                            <el-option v-for="item in workItemLogNameList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row v-if="addFormData.getValueType == '从聚水潭工作量报表取值'">
                    <el-col>
                        <el-select v-model="addFormDataSel" placeholder="" :clearable="true" filterable multiple
                            style="width:80%;">
                            <el-option v-for="item in workItemJstWorkList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisibleAdd_Sel = false">取 消</el-button>
                    <el-button type="primary" @click="onSelOk()">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog title="日志" :visible.sync="dialogVisibleLog" width="45%" height="540" v-dialogDrag
            :close-on-click-modal="false" :loading="dialogVisibleLogLoading">
            <template>
                <div style="height:540px;">
                    <el-table :data="dialogVisibleLogData" :height="'540px'">
                        <el-table-column prop="modifiedUserName" label="操作人" width="100" />
                        <el-table-column prop="modifiedTime" label="操作时间" width="150" />
                        <el-table-column prop="modifiedContext" label="操作内容" />
                    </el-table>
                </div>
            </template>
        </el-dialog>

        <el-dialog title="计算个人人效" :visible.sync="dialogVisibleCompute2" width="20%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-date-picker style="width: 280px" v-model="computeWorkDate2" type="daterange" format="yyyy-MM-dd"
                            :picker-options="pickOptions" value-format="yyyy-MM-dd" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="copmuteLoading2" @click="onComputeAccountSave" /> &nbsp;
                <el-button @click="dialogVisibleCompute2 = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehouseWagesAccountPageList, getWarehousePostWorkItemColList,
    getWarehouseWagesAccountById, deleteWarehouseWagesAccountById,
    saveWarehouseWagesAccount, getWarehouseWagesAccountLogList, computeWarehouseUserWorkData
} from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'account', label: '账号', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'accountUserName', label: '真实姓名', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'accountPostCode', label: '一级岗位编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'accountPostName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'accountTwoPostName', label: '二级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'dayPrice', label: '白班工价', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'nightPrice', label: '晚班工价', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'getValueType', label: '取值类型', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'orderLogGetValueCols', label: '订单日志取值名称', width: '180', sortable: 'custom', formatter: (row) => row.orderLogGetValueColNames },
    { istrue: true, prop: 'orderLogGetValueType', label: '计件类型', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'jstWorkGetValueCols', label: '工作量取值列名称', width: '180', sortable: 'custom', formatter: (row) => row.jstWorkGetValueColNames },
    {
        istrue: true, type: 'button', label: '操作', width: '160',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEdit(row.id) },
            { label: "删除", handle: (that, row) => that.onDelete(row.id) },
            { label: "计算", handle: (that, row) => that.onComputeAccount(row) },
            { label: "日志", handle: (that, row) => that.onLog(row.id) }
        ]
    },
]
const tableHandles1 = [
    //{ label: "导入", handle: (that) => that.onImport2() },
];
export default {
    name: 'warehousepostaccount',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: {

    },
    data() {
        return {
            that: this,
            nowDate: formatTime(new Date(), "YYYY-MM-DD"),
            filter: {
                warehouseName: "诚信仓",
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "warehouseName", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            tableHandles1: tableHandles1,
            dialogVisibleAdd: false,
            addFromLoading: false,
            workItemLogNameList: [],
            workItemJstWorkList: [],
            addLoading: false,
            addFormData: {
                id: 0,
                warehouseName: null,
                account: null,
                accountUserName: null,
                accountPostCode: null,
                accountPostName: null,
                accountTwoPostName: null,
                dayPrice: 0,
                nightPrice: 0,
                getValueType: null,
                orderLogGetValueCols: null,
                orderLogGetValueColNames: null,
                orderLogGetValueType: null,
                jstWorkGetValueCols: null,
                jstWorkGetValueColNames: null,
            },
            addFormRules: {
                warehouseName: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
                account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
                accountUserName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
                accountPostCode: [{ required: true, message: '请输入一级岗位编码', trigger: 'blur' }],
                accountPostName: [{ required: true, message: '请输入一级岗位名称', trigger: 'blur' }],
                accountTwoPostName: [{ required: true, message: '请输入二级岗位名称', trigger: 'blur' }],
                getValueType: [{ required: true, message: '请输入取值类型', trigger: 'blur' }],
            },
            addFormDataSel: [],
            dialogVisibleAdd_Sel: false,

            dialogVisibleLogLoading: false,
            dialogVisibleLog: false,
            dialogVisibleLogData: [],

            dialogVisibleCompute2: false,
            copmuteLoading2: false,
            computeWorkDate2: [],
            computeParams: {
                bWorkDate: null,
                eWorkDate: null,
            },

            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
        };
    },
    async mounted() {
        await this.getWorkItemList();
        await this.onSearch()
    },
    methods: {
        async onSel() {
            this.addFormDataSel = [];
            this.dialogVisibleAdd_Sel = true;
        },
        async onSelOk() {
            if (!this.addFormDataSel || this.addFormDataSel.length <= 0) {
                this.addFormDataSel = [];
            }
            if (this.addFormData.getValueType == '从订单日志取值') {
                this.addFormData.orderLogGetValueCols = "";
                this.addFormData.orderLogGetValueColNames = "";
                if (this.addFormDataSel.length > 0) {
                    let tIndex = 0;
                    this.addFormDataSel.forEach(f => {
                        tIndex++;
                        this.addFormData.orderLogGetValueCols += (f + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                        this.addFormData.orderLogGetValueColNames += (f + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                    });
                }
            }
            else if (this.addFormData.getValueType == '从聚水潭工作量报表取值') {
                this.addFormData.jstWorkGetValueCols = "";
                this.addFormData.jstWorkGetValueColNames = "";
                if (this.addFormDataSel.length > 0) {
                    let tIndex = 0;
                    this.addFormDataSel.forEach(f => {
                        tIndex++;
                        let def = this.workItemJstWorkList.find(w => w.value == f);
                        if (def) {
                            this.addFormData.jstWorkGetValueCols += (def.value + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                            this.addFormData.jstWorkGetValueColNames += (def.label + ((tIndex < this.addFormDataSel.length) ? "," : ""));
                        }
                    });
                }
            }
            this.dialogVisibleAdd_Sel = false;
        },
        async onAccountBlur() {
            if (this.addFormData.account) {
                let split = this.addFormData.account.split('-');
                if (split.length >= 2) {
                    if (split[0]) {
                        this.addFormData.accountPostCode = split[0].replace(/\d+/g, '');
                    }
                    this.addFormData.accountUserName = split[1];
                }
            }
        },
        async onClearAddForm() {
            this.addFormData.id = 0;
            this.addFormData.warehouseName = null;
            this.addFormData.account = null;
            this.addFormData.accountUserName = null;
            this.addFormData.accountPostCode = null;
            this.addFormData.accountPostName = null;
            this.addFormData.accountTwoPostName = null;
            this.addFormData.dayPrice = 0;
            this.addFormData.nightPrice = 0;
            this.addFormData.getValueType = null;
            this.addFormData.orderLogGetValueCols = null;
            this.addFormData.orderLogGetValueColNames = null;
            this.addFormData.orderLogGetValueType = null;
            this.addFormData.jstWorkGetValueCols = null;
            this.addFormData.jstWorkGetValueColNames = null;
        },
        async getWorkItemList() {
            const data = await getWarehousePostWorkItemColList();
            this.workItemLogNameList = [];
            this.workItemJstWorkList = [];
            if (data) {
                data.forEach(f => {
                    if (f.workItemColType == "从订单日志取值")
                        this.workItemLogNameList.push({ value: f.workItemColName, label: f.workItemColDes });
                    if (f.workItemColType == "从聚水潭工作量报表取值")
                        this.workItemJstWorkList.push({ value: f.workItemColName, label: f.workItemColDes });
                });
            }
        },
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseWagesAccountPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onAdd() {
            await this.onClearAddForm();
            this.dialogVisibleAdd = true;
        },
        addFormValidate: function () {
            let isValid = false;
            this.$refs.addForm.validate(valid => {
                isValid = valid;
            });
            return isValid;
        },
        async onAddSave() {
            let chkError = false;
            if (this.addFormData.postUserList && this.addFormData.postUserList.length > 0) {
                this.addFormData.postUserList.forEach(f => {
                    if (!f.userName)
                        chkError = true;
                    if (!f.userWagesBeginDate)
                        chkError = true;
                    if (!f.userWages)
                        chkError = true;
                });
                if (chkError) {
                    this.$message({ type: 'error', message: '个人工价中【姓名、日期、工价】必填!' });
                    return;
                }
            }
            if (this.addFormData.getValueType == "从订单日志取值") {
                if (!this.addFormData.orderLogGetValueColNames || !this.addFormData.orderLogGetValueCols || !this.addFormData.orderLogGetValueType) {
                    this.$message({ type: 'error', message: '请输入[订单日志取值名称]和[计件类型]' });
                    return;

                }
            }
            if (this.addFormData.getValueType == "从聚水潭工作量报表取值") {
                if (!this.addFormData.jstWorkGetValueCols || !this.addFormData.jstWorkGetValueColNames) {
                    this.$message({ type: 'error', message: '请输入[工作量取值列名称]' });
                    return;
                }
            }
            this.addLoading = true;
            let res = await saveWarehouseWagesAccount(this.addFormData);
            this.addLoading = false;
            if (res.success) {
                this.$message({ type: 'success', message: '保存成功!' });
                this.dialogVisibleAdd = false;
                this.onSearch();
            }
        },
        async onEdit(mainId) {
            this.dialogVisibleAdd = true;
            this.addFromLoading = true;
            let res = await getWarehouseWagesAccountById({ id: mainId });
            this.addFromLoading = false;
            if (res?.success && res?.data) {
                this.addFormData = res?.data;
            }
            console.log(this.addFormData);
        },
        async onDelete(mainId) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteWarehouseWagesAccountById({ id: mainId });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    this.onSearch()
                }
            }).catch(() => {
            });
        },
        async onLog(mainId) {
            this.dialogVisibleLog = true;
            this.dialogVisibleLogLoading = true;
            const res = await getWarehouseWagesAccountLogList({ accountId: mainId });
            this.dialogVisibleLogLoading = false;
            console.log(res);
            if (res?.success) {
                this.dialogVisibleLogData = res.data;
            }
            else {
                this.dialogVisibleLogData = [];
            }
        },
        async onComputeAccount(row) {
            if (!row || !row.getValueType || !row.account) {
                this.$message({ type: 'error', message: '获取账号失败' });
                return;
            }
            this.computeParams.getValueType = row.getValueType;
            this.computeParams.computeAccount = row.account;
            this.dialogVisibleCompute2 = true;
        },
        async onComputeAccountSave() {
            console.log(this.computeParams, "computeParams");
            if (!this.computeWorkDate2 || this.computeWorkDate2.length <= 0) {
                this.$message({ type: 'error', message: '请选择要计算的日期!' });
                return;
            }
            else {
                if (!this.computeParams || !this.computeParams.getValueType || !this.computeParams.computeAccount) {
                    this.$message({ type: 'error', message: '获取账号失败' });
                    return;
                }
                this.computeParams.bWorkDate = formatTime(this.computeWorkDate2[0], "YYYY-MM-DD");
                this.computeParams.eWorkDate = formatTime(this.computeWorkDate2[1], "YYYY-MM-DD");
                this.copmuteLoading2 = true;
                console.log(this.computeParams, "computeParams");
                const res = await computeWarehouseUserWorkData(this.computeParams);
                this.copmuteLoading2 = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '计算个人人效中..请稍后在[人效统计]页签刷新查看!' });
                    this.dialogVisibleCompute2 = false;
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__body-wrapper {
    overflow-y: auto;
}
</style>
