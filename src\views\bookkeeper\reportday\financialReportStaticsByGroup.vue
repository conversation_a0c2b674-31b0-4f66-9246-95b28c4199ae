<template>
<my-container>
<div>
     <ces-table ref="table" :that='that'  
          :tableData='financialreportlist'  
          :tableCols='tableCols' :tableHandles='tableHandles' :summaryarry='summaryarry'  :loading="listLoading"  style="width:100%;height:1650px;margin: 0">
    </ces-table> 
<el-dialog :title="detailName" :visible="dialogVisible"   @close='closeDialog' width="90%" v-dialogDrag>
      <span>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button  >确定</el-button>
      </span>
    </el-dialog>
<!-- <el-dialog title="明细" :style="dialogVisible?'display:block;':'display:none;'" height="600px" width="600px"  @close='closeDialog'>
      <span>   <el-input value="sss"></el-input>
       <financialReportDetailByOneUser height="600px" width="600px"   ref="financialReportDetailByOneUser" />  
         </span> <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog> -->
  </div>
   </my-container>
</template>
<script>
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
import {exportFinancialStaticticsByUser,getParm,setParm,getFinancialStaticticsByUser,getPerformanceStaticticsByGroup,getFinancialStaticticsByGroup} from '@/api/bookkeeper/reportday' 
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
}; 
const tableCols =[
        {istrue:true,fixed:true,prop:'groupId',label:'小组',sortable:'custom', width:'60',formatter:(row)=> row.groupName},
         {istrue:true,prop:'payAmont',label:'付款金额',sortable:'custom',width:'100',formatter:(row)=> !row.payAmont?" ": row.payAmont.toFixed(2)},      
        {istrue:true,prop:'saleAmont',label:'销售金额',width:'120',formatter:(row)=> !row.saleAmont?" ": row.saleAmont.toFixed(2)},         
        {istrue:true, prop:'saleCost',label:'销售成本',width:'120',formatter:(row)=> row.saleCost==0?" ": row.saleCost?.toFixed(2)}, 
        {istrue:true,prop:'profit1',label:'毛一利润',width:'120',formatter:(row)=> !row.profit1?" ": row.profit1.toFixed(2)},    
        {istrue:true,prop:'profit1Rate',label:'毛一利润率',width:'90',formatter:(row)=> !row.profit1Rate?" ": (row.profit1Rate*100).toFixed(2)+"%"},    
        {istrue:true,prop:'alladv',label:'广告费',width:'100',formatter:(row)=> row.alladv==0?" ": row.alladv?.toFixed(2)},
        {istrue:true,prop:'profit3',label:'毛三利润',width:'120',type:'custom',formatter:(row)=> row.profit3==0?" ": row.profit3?.toFixed(2)},
        {istrue:true,prop:'profit4',label:'净利润',width:'80',formatter:(row)=> row.profit4==0?" ": row.profit4?.toFixed(2)}, 
             ];
const tableHandles=[
      ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,financialReportDetailByOneUser},
  data() {
    return {
      that:this,
      filter: {
        platform:null,
        shopCode:null,
        proCode:null,
        productName:null,
        groupId:null,
        startTime: null,
        endTime: null,
        timerange:null,
        // 运营助理
        userId :null,
        // 车手
        userId2:null,
        // 备用
        userId3:null,
        // 运营专员 ID
        operateSpecialUserId:null,    
      }, 
      onimportfilter:{
        yearmonthday:null,
      },
      shopList:[],
      userList:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:" saleAmont ",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      detailName:'业绩明细',
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      dialogDrVisible:false,
      drparamProCode:'',
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      freightDetail:{
        visible:false,
        filter:{
          proCode:null,
          timeRange:[]
        }
      }
    };
  },
  async mounted() {
    this.init();
    var that=this;
    window.setFinancialFilterTime=function(startTime,endTime,userId,operateSpecialUserId,platform){
      //  var date1 = new Date(); date1.setDate(startTime);
      //  var date2 = new Date(); date2.setDate(endTime);
      if(userId>0)
      {
        that.tableCols[0].display=true;
        that.tableCols[1].display=false;
      }
      if(operateSpecialUserId>0)
      {
        that.tableCols[1].display=true;
        that.tableCols[0].display=false;
      }
        console.log(startTime)
        console.log(endTime)
        that.filter.startTime=startTime;
        that.filter.endTime=endTime;
        that.filter.userId=userId;
        that.filter.platform=platform;
        that.filter.operateSpecialUserId=operateSpecialUserId;
        that.onSearch();
    }
  },
  async created() {
    console.log("created")
    await this.getShopList();
  },
  methods: {    
    closeDialog(){
      this.dialogVisible=false;
    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-10);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timerange=[];
        this.filter.timerange[0]=this.datetostr(date1);
        this.filter.timerange[1]=this.datetostr(date2);
        console.log(this.filter)
      },
   async showprchart2(prcode){
      window['lastseeprcodedrchart']=prcode
      this.drparamProCode=prcode
      this.dialogDrVisible=true
   } ,  
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode&&f.platform==2)
              this.shopList.push(f);
        });
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 
        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };}); 
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){      
      await this.getList().then(res=>{  });
    },
    async getList(){
      //this.filter.startTime =null;
      // this.filter.endTime =null;
      // if (this.filter.timerange) {
      //   this.filter.startTime = this.filter.timerange[0];
      //   this.filter.endTime = this.filter.timerange[1];
      // }
      var that=this;
      this.pager.pageSize=100;
      this.pager.currentPage=1;       
      const params = {...this.pager,...this.filter};
     // this.listLoading = true;
      startLoading(); 
      const res = await getPerformanceStaticticsByGroup(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee =" ";               
              }
            }
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   onRefresh(){
        this.onSearch()
    },
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
