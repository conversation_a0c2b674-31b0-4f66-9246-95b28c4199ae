<template>
    <div class="dashboard-editor-container">
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper555">
                    <div class="chart-wrapper556">
                        <line-kpi ref="LineKpi" />
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import LineKpi from '@/views/xingzheng/lineKpi'

    export default {
        name: 'PersonnelEfficiency',
        components: {
            LineKpi
        },
        data () {
            return {

            }
        }, async mounted () {

        }, methods: {

        }
    };
</script>

<style lang="scss" scoped>
    .dashboard-editor-container {
        padding: 20px;
        background-color: rgb(240, 242, 245);
        position: relative;

        .github-corner {
            position: absolute;
            top: 0px;
            border: 0;
            right: 0;
        }

        .chart-wrapper555 {
            background: rgb(240, 242, 245);
            padding: 10px 1px 0;
            margin-bottom: 1px;
            height: 100%;
        }

        .chart-wrapper556 {
            background: #fff;
            padding: 10px 16px 0;
            height: 100%;
        }
    }
</style>