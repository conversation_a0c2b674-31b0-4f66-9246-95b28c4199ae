<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <el-tabs v-model="activeName" style="height:94%;">
      
    <el-tab-pane   label="特殊单费用" name="tab6" style="height: 100%;">
        <dahuixiong :filter="filter" ref="dahuixiong" style="height: 100%;"/>
    </el-tab-pane>
    
     <el-tab-pane    label="淘宝客" name="tab7" style="height: 100%;">
        <pddtaobaoke :filter="filter" :tablekey="tablekey1" ref="pddtaobaoke" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane    label="营销费用" name="tab8" style="height: 100%;">
        <yingxiao :filter="filter" :tablekey="tablekey2" ref="yingxiao" style="height: 100%;"/>
    </el-tab-pane> 
    <el-tab-pane     label="拼多搜索推广"  name="tab11" style="height: 100%;">
        <pingduoduo :filter="filter" :tablekey="tablekey3" ref="pingduoduotuiguang" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane      label="拼多场景展示" name="tab12" style="height: 100%;">
        <pingduoduo :filter="filter"  :tablekey="tablekey4" ref="pingduoduochangjing" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane     label="拼多明星店铺" name="tab13" style="height: 100%;">
        <pingduoduo :filter="filter" :tablekey="tablekey5" ref="pingduoduofangxintui" style="height: 100%;"/>
    </el-tab-pane>
        <el-tab-pane     label="推广返还红包" name="tab14" style="height: 100%;">
        <pingduoduoTuiGuangFanHai :filter="filter" :tablekey="tablekey6" ref="pingduoduoTuiGuangFanHai" style="height: 100%;"/>
    </el-tab-pane>
  <el-tab-pane     label="拼多多全站推广" name="tab15" style="height: 100%;">
        <pingduoduo :filter="filter" :tablekey="tablekey7" ref="quanzhan" style="height: 100%;"/>
    </el-tab-pane>
    <el-tab-pane     label="拼多多标准推广" name="tab16" style="height: 100%;">
      <pingduoduoBiaoZhun :filter="filter" :tablekey="tablekey8" ref="pingduoduoBiaoZhun" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane     label="拼多多智能托管" name="tab17" style="height: 100%;">
    <pingduoduoZhiNeng :filter="filter" :tablekey="tablekey9" ref="pingduoduoZhiNeng" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane     label="拼多多评价有礼" name="tab18" style="height: 100%;">
  <pingduoduoEvaluationCourtesy :filter="filter" :tablekey="tablekey10" ref="pingduoduoEvaluationCourtesy" style="height: 100%;"/>
</el-tab-pane>
  
  </el-tabs>
  </my-container >

 </template>
<script>
import MyContainer from "@/components/my-container";

 import shizhitou from '@/views/financial/yyfydayreport/shizhitou'

 import tuijian from '@/views/financial/yyfydayreport/tuijian'

 import taobaoke from '@/views/financial/yyfydayreport/taobaoke'

 import pingduoduoBiaoZhun from '@/views/financial/yyfydayreport/pingduoduoBiaoZhun'
 import pingduoduoZhiNeng from '@/views/financial/yyfydayreport/pingduoduoZhiNeng'
 import pingduoduoEvaluationCourtesy from '@/views/financial/yyfydayreport/pingduoduoEvaluationCourtesy'
import pingduoduoTuiGuangFanHai from '@/views/financial/yyfydayreport/pingduoduoTuiGuangFanHai'
 
 
 import yinglimofangday from '@/views/financial/yyfydayreport/yinglimofangday'

 import wanxiangtaiday from '@/views/financial/yyfydayreport/wanxiangtaiday'


 import taotetuiguang from '@/views/financial/yyfydayreport/taotetuiguang'

 import zhitongche from '@/views/financial/yyfydayreport/zhitongche'
 
 import dahuixiong from '@/views/financial/yyfydayreport/dahuixiong'

 import taolijing from '@/views/financial/yyfydayreport/taolijing'

 import yingxiao from '@/views/financial/yyfydayreport/yingxiao'

 
import accountresult from '@/views/financial/yyfydayreport/accountresult'

 
 import pingduoduo from '@/views/financial/yyfydayreport/pingduoduo'

 import unusual from '@/views/financial/yyfydayreport/unusual'
 
 import pddtaobaoke from '@/views/financial/yyfydayreport/pddtaobaoke'
 import shoudanlijingday from '@/views/financial/yyfydayreport/shoudanlijingday'

 import checkPermission from '@/utils/permission'

export default {
  name: "Users",
  components: {pddtaobaoke, MyContainer,shoudanlijingday,shizhitou,tuijian,taobaoke,taotetuiguang,zhitongche,dahuixiong,taolijing,yingxiao,accountresult,pingduoduo,unusual,yinglimofangday,wanxiangtaiday,pingduoduoBiaoZhun,pingduoduoZhiNeng,pingduoduoEvaluationCourtesy,pingduoduoTuiGuangFanHai},
  data() {
    return {
      that:this,
      activeName:'',
      filter: {
      },
      shopList:[],
      userList:[],
      groupList:[],
      selids:[],
      dialogVisibleSyj:false,
      pageLoading:false,
      fileList:[],
      tablekey1:"tablekey1",
      tablekey2:"tablekey2",
      tablekey3:"tablekey3",
      tablekey4:"tablekey4",
      tablekey5:"tablekey5",
      tablekey6:"tablekey6",
      tablekey7:"tablekey7",
    };
  },
  mounted() {
this.$refs.pingduoduotuiguang.setfeetype('拼多多搜索推广');
this.$refs.pingduoduochangjing.setfeetype('拼多多场景展示');
this.$refs.pingduoduofangxintui.setfeetype('拼多多放心推');
this.$refs.pingduoduoTuiGuangFanHai.setfeetype('拼多多推广返还红包');
this.$refs.quanzhan.setfeetype('拼多多全站推广');
this.$refs.pingduoduoBiaoZhun.setfeetype('拼多多标准推广');
this.$refs.pingduoduoZhiNeng.setfeetype('拼多多智能托管');
this.$refs.pingduoduoEvaluationCourtesy.setfeetype('拼多多评价有礼');
this.$refs.dahuixiong.setplatform(1);
this.$refs.yingxiao.setplatform(1);




  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
