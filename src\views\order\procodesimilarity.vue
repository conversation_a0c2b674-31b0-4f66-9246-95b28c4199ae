<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%;">
      <el-tab-pane v-if="checkPermission('api:bookkeeper:styleCodeRptData:PageStyleCodeRptListAsync')" label="系列编码报表"
        name="first" style="height: 100%;" lazy>
        <newStryleCodeReports />
      </el-tab-pane>
      <!-- <el-tab-pane label="系列编码报表" name="first" style="height: 100%;" lazy>
        <styleCodeReports />
      </el-tab-pane> -->
      <el-tab-pane v-if="checkPermission('api:bookkeeper:styleCodeRptData:PageSimilarityAsync')" label="系列资金报表"
        name="lossqty" style="height: 100%;" lazy>
        <styleCodeReports2 />
      </el-tab-pane>

      <el-tab-pane label="系列订单趋势（临时）" name="three" style="height: 100%;" lazy>
        <styleCodeOrderCountRpt />
      </el-tab-pane>
      <el-tab-pane label="资料信息" name="forth" style="height: 100%;" lazy v-if="checkPermission('profileInformation')">
        <profileInformation />
      </el-tab-pane>
      <el-tab-pane v-if="checkPermission('styleCodeReports_yw')" label="系列资金报表(运维版)" name="fifth" style="height: 100%;"
        lazy>
        <styleCodeReports_yw />
      </el-tab-pane>
      <!-- <el-tab-pane label="运维优化产品统计" name="sixth" style="height: 100%;" lazy>
        <yw_PersonalOptimization />
      </el-tab-pane> -->
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import styleCodeReports from './styleCodeReports.vue'
import styleCodeReports2 from './styleCodeReports2.vue'
import styleCodeOrderCountRpt from '@/views/bookkeeper/styleCodeOrderCountRpt.vue'
import newStryleCodeReports from './newStryleCodeReports.vue'
import checkPermission from '@/utils/permission'
import profileInformation from './profileInformation.vue'
import styleCodeReports_yw from './styleCodeReports_yw.vue'
import yw_PersonalOptimization from './yw_PersonalOptimization.vue'
export default {
  name: 'procodesimilarity',
  components: {
    MyContainer, styleCodeReports, styleCodeReports2, newStryleCodeReports, styleCodeOrderCountRpt, profileInformation, styleCodeReports_yw, yw_PersonalOptimization
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>
