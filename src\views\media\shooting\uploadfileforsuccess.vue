<template>
<el-row>
      <el-row >
        <el-col :xs="14" :sm="14" :lg="14">
            <el-upload ref="upload"
                    action ="#"
                    :auto-upload="true" 
                    :multiple="true" 
                    :limit="limit" 
                    :show-file-list ="false"
                    :accept ="accepttyes"
                    :http-request="UpSuccessload"
                    :on-exceed ="exceed"
                    :file-list="retdata"
                    :disabled="islook"
                    >
                    <el-button size="small" type="primary"    :disabled="islook" :v-loading="uploading"   >点击上传<i class="el-icon-upload el-icon--right"></i></el-button>
            </el-upload> 
           
        </el-col>
        
        <el-col :xs="10" :sm="10" :lg="10">
            <span>数量：{{retdata.length}}</span>
            <img src="@/static/images/downflieicon.png" fit="contain" @click="downfileall()"  style="width: 20px;height: 20px;margin-left: 80px; "/>
        </el-col>
    </el-row>
    <div class="contentup">
        <el-row >
        <el-col :xs="17" :sm="17" :lg="17">
            <template>
                <draggablevue v-model="retdata" :scroll="true" animation="300"  @update="dataUpdate">
                    <transition-group> 
                            <div v-for="(i,index) in retdata"  :key="i.uid">
                                <div class="flexroww">
                                    <el-tooltip class="item" effect="dark" :content="i.fileName" placement="left">
                                        <div class="outline" @click="imgclick(i.url,retdata[index],index)">{{ i.fileName }}</div>
                                    </el-tooltip>
                                    <div class="flexrow">
                                        <el-link :disabled="islook" v-show="i.filestaus==2" type="primary" @click="downfile(i)"><i class="el-icon-success"></i></el-link>
                                        <el-link :disabled="islook" v-show="i.filestaus!=2" type="warning"><i class="el-icon-warning"></i></el-link>
                                        <el-link :disabled="islook" type="danger"  @click="removefile(i)"><i class="el-icon-error"></i></el-link>
                                    </div>
                                </div> 
                            </div>
                    </transition-group>
                </draggablevue>
            </template>
        </el-col>
        <el-col :xs="4" :sm="4" :lg="4">
        </el-col>
    </el-row> 
    </div>
    <el-image-viewer v-if="showGoodsImage" :initialIndex="imgindex" :url-list="imgList"  :wrapperClosable="false" :on-close="closeFunc" style="z-index:9999;" />
         <!--视频播放-->
    <el-dialog title="视频播放" :visible.sync="dialogVisible" width="50%" @close="closeVideoPlyer"  :append-to-body="true" >
        <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeVideoPlyer">关闭</el-button>
        </span>
    </el-dialog>
</el-row>
</template>
<script>
import draggablevue from 'vuedraggable' 
import MyContainer from "@/components/my-container";
import ElImageViewer from './imageviewer.vue';
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
function pause(msec) {
    return new Promise(
        (resolve, reject) => {
            setTimeout(resolve, msec || 500);
        }
    );
}
export default {
    components: { MyContainer,draggablevue,ElImageViewer,videoplayer}, 
    props:{
        uploadInfo:{ type: Array, default:[] }, 
        accepttyes:{ type: String, default:"*" },
        uptype:{ type: String, default:"other" },
        limit:{ type: Number, default: 100000  },
        delfunction:{ type: Function, default: null},
        islook:{ type: Boolean, default:false },
    },
    data() {
        return {
            retdata:[],
            deldata:[], 
            uploading:false,
            imgindex:0,
            icontype: 'primary',
            imgList: [],
            showGoodsImage: false,
            IsChang:false,
            dialogVisible:false,
            videoplayerReload:false
        };
    },
    async mounted() {  
        this.retdata = this.uploadInfo; 
        this.deldata = [];  
    },  
    methods: {
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.dialogVisible = true;
            this.videoUrl = videoUrl;
        },
        async closeVideoPlyer() {
            this.dialogVisible = false;
            this.videoplayerReload = false;
        },
        async downfileall(){
            for(let num in this.retdata)
            { 
                if(this.retdata[num].filestaus ==2  ){
                    await pause(500);
                    await this.downfile(this.retdata[num]);
                }  
            }
        },
        dataUpdate(){
            this.IsChang =true;
        },
        getChange(){ 
            return this.islook ? false:this.IsChang;
        },
        //获取返回值
        getReturns(){ 
            var curdata= [];
            this.retdata.forEach(function(item){
                //filestaus 0 新增，1移除，2原来的文件
                curdata.push({ 
                    fileName:item.fileName
                    ,url:item.url
                    ,OutComeId:item.outComeId
                    ,uid:item.uid
                    ,filestaus:item.filestaus
                    ,file: item.file,
                })
            });

            this.deldata.forEach(function(item){
                //filestaus 0 新增，1移除，2原来的文件
                curdata.push({ 
                    fileName:item.fileName
                    ,url:item.url
                    ,OutComeId:item.outComeId
                    ,uid:item.uid
                    ,filestaus:item.filestaus
                    ,file: item.file,
                })
            });

            return {success:true ,data :curdata};
        },
        //下载文件
        async downfile(file)
        { 
            var xhr = new XMLHttpRequest();
            xhr.open('GET', file.url, true);
            xhr.responseType = 'arraybuffer'; // 返回类型blob
            xhr.onload = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    let blob = this.response;
                    console.log(blob);
                    // 转换一个blob链接
                    // 注: URL.createObjectURL() 静态方法会创建一个 DOMString(DOMString 是一个UTF-16字符串)，
                    // 其中包含一个表示参数中给出的对象的URL。这个URL的生命周期和创建它的窗口中的document绑定
                    let downLoadUrl = window.URL.createObjectURL(new Blob([blob], {type: 'video/mp4'}));
                    // 视频的type是video/mp4，图片是image/jpeg
                    // 01.创建a标签
                    let a = document.createElement('a');
                    // 02.给a标签的属性download设定名称
                    a.download = file.fileName;
                    // 03.设置下载的文件名
                    a.href = downLoadUrl;
                    // 04.对a标签做一个隐藏处理
                    a.style.display = 'none';
                    // 05.向文档中添加a标签
                    document.body.appendChild(a);
                    // 06.启动点击事件
                    a.click();
                    // 07.下载完毕删除此标签
                    a.remove();
                };
            };
            xhr.send();
        },
        //移除文件
        async removefile(file){
            this.IsChang =true;
            //发出父级页面移除请求
            if(this.delfunction){
                await this.delfunction(file);
            }
            for(let num in this.retdata)
            {
                if(this.retdata[num].uid==file.uid){
                    //原上传文件，移除到删除列表
                    if(this.retdata[num].outComeId > 0){
                        //打算删除标记
                        this.retdata[num].filestaus = 1;
                        this.deldata.push(this.retdata[num]);
                    }
                    this.retdata.splice(num,1)
                }
            }
        },
        //上传超出提出
        exceed(){
            this.$message({ message: "超出上传数量限制", type: "warning" });
        },
        //上传方法
        async UpSuccessload(file){
            this.IsChang =true;
            this.uploading = true; 
            this.retdata.push({
                fileName:file.file.name
                ,url:""
                ,outComeId:0
                ,uid:file.file.uid
                ,filestaus:0
                ,file: file.file
            });
            this.uploading = false; 
        }, 
        imgclick(e,data,index){ 
            //判断当前是 图片，还是视频，还是不可预览文件 
            if(this.uptype =="imgtype" ){   
                this.imgList = [];
                for(let num in this.retdata)
                {  
                    this.imgList.push(this.retdata[num].url); 
                }  
                this.imgindex=index;
                this.showGoodsImage = true;
            }
            else if(this.uptype =="rartype" ){
                //不可预览
            }
            else if(this.uptype =="psdtype" ){
                 //不可预览
            }
            else if(this.uptype =="vediotype" ){
                 //视频预览
                 this.playVideo(data.url);
            } 
            return; 
        }, 
        
        //完成表单界面关闭图片
        async closeFunc() {
            this.showGoodsImage = false;
        }, 
    },
};
</script>
<style  lang="scss" scoped>
.infinite-list-item {
    list-style:none;
    margin: 0;
    padding: 0;
}
.infinite-list-item  span{
    width: 50px !important;
    white-space: nowrap!important;
    overflow: hidden!important;
    text-overflow: ellipsis!important;
}
.outline{
    width: 370px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}
.contentup{
    height: 250px;
    // width: 80%;
    width: 380px;
    margin-top: 10px;
    overflow:scroll;
}
.flexrow{
    z-index: 999;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    color: rgb(50,50,50);
    justify-content: center;
    align-items: center;
}
.flexroww{
    // background-color: red;
    width: 365px;
    display: flex;
    flex-direction: row;
    font-size: 14px;
    color: rgb(50,50,50);
    position: relative;
}
.rightflex{
    // width: 100%;
    // background-color: red;
    display: flex;
    justify-content: flex-end;
    margin-left: auto;
}
.rowclass{
    height: 300px;
    min-width: 500px;
    // background-color: green;
}
.hoversign:hover {
    background-color: red;
    cursor: move;
}
</style>