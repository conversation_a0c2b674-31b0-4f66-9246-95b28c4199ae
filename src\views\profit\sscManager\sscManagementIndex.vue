<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="首页" name="first10" style="height: 100%">
        <Aashouye ref="Aashouye" />
      </el-tab-pane>

      <el-tab-pane label="招聘数据汇总" name="first1" style="height: 100%">
        <recruitment ref="recruitment" />
      </el-tab-pane>
      <el-tab-pane label="招聘部门分析" name="first2" style="height: 100%" lazy>
        <department ref="department" />
      </el-tab-pane>
      <el-tab-pane label="岗位职级分布" name="first3" style="height: 100%" lazy>
        <AllRegionalPersonnel ref="AllRegionalPersonnel" />
      </el-tab-pane>
      <el-tab-pane label="SSC异动数据" name="first4" style="height: 100%" lazy>
        <BsscData ref="BsscData" />
      </el-tab-pane>
      <el-tab-pane label="离职数据分析" name="first5" style="height: 100%" lazy>
        <CleaveManagement ref="CleaveManagement" />
      </el-tab-pane>
      <!-- <el-tab-pane label="培训成效分析--" name="first6" style="height: 100%" lazy>
        <CmtrainingData ref="CmtrainingData" />
      </el-tab-pane> -->

      <el-tab-pane label="人员分析表" name="first7" style="height: 100%" lazy>
        <ePeoplefx ref="refePeoplefx" />
      </el-tab-pane>
      <!-- <el-tab-pane label="各仓月度档案分析--" name="first8" style="height: 100%" lazy>
        <fMonthlyArchive ref="fMonthlyArchive" />
      </el-tab-pane> -->
      <el-tab-pane label="各仓临时工成本" name="first9" style="height: 100%" lazy>
        <gWarehouseTemporary ref="gWarehouseTemporary" />
      </el-tab-pane>
      <el-tab-pane label="仓储离职岗位排名" name="first11" style="height: 100%" lazy>
        <hResignationRanking ref="hResignationRanking" />
      </el-tab-pane>
      <el-tab-pane label="各区工伤赔付情况" name="first12" style="height: 100%" lazy>
        <iCompensationSituation ref="iCompensationSituation" />
      </el-tab-pane>

    </el-tabs>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import recruitment from "./recruitment/index.vue";
import department from "./department/index.vue";
import Aashouye from "./Aashouye/index.vue";


import ePeoplefx from "@/views/profit/sscManager/ePeoplefx/ePeoplefx.vue";
import fMonthlyArchive from "@/views/profit/sscManager/fMonthlyArchive/fMonthlyArchive.vue";
import gWarehouseTemporary from "@/views/profit/sscManager/gWarehouseTemporary/gWarehouseTemporary.vue";
import hResignationRanking from "@/views/profit/sscManager/hResignationRanking/index.vue";
import iCompensationSituation from "@/views/profit/sscManager/iCompensationSituation/index.vue";


import AllRegionalPersonnel from "@/views/profit/sscManager/AllRegionalPersonnel/index.vue";
import BsscData from "@/views/profit/sscManager/BsscData/index.vue";
import CleaveManagement from "@/views/profit/sscManager/CleaveManagement/index.vue";
import CmtrainingData from "@/views/profit/sscManager/CmtrainingData/index.vue";

export default {
  name: "sscManagementIndex",
  components: {
    MyContainer, recruitment, department, AllRegionalPersonnel, BsscData, CleaveManagement, CmtrainingData,
    fMonthlyArchive, ePeoplefx, gWarehouseTemporary, Aashouye, hResignationRanking, iCompensationSituation
  },
  data() {
    return {
      activeName: "first10",
    };
  },

};
</script>
<style lang="scss" scoped></style>
