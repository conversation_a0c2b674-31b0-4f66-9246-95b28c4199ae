<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>

        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'expressLockList123'" :tablekey="'expressLockList123'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageExpressFreeLock} from "@/api/express/express";
import { formatTime} from "@/utils/tools";

const tableCols = [
{ width: '110', align: 'center', prop: 'yearMonth', label: '月份'},
  { width: '210', align: 'center', prop: 'createdUserName', label: '操作人', },
  { width: '210', align: 'center', prop: 'lockType', label: '操作类型' ,  formatter: (row) => row.lockType==1?"锁定": row.lockType==0?"解锁" :"同步进月报"},
  {  width: '210', align: 'center', prop: 'createdTime', label: '操作时间', },
]
export default {
  name: "expressLockList",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      timeRanges: [],
      that: this,
      ListInfo: {
        yearmonth:null,
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
       
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      platform:null,
      yearmonth:null,
      dialogVisible2:false,
    }
  },
  async mounted() {
    this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    //await this.getList()
    await this.init()
  },
  methods: {
    startClac() {
 this.dialogVisible2 = true;
},
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })

    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await pageExpressFreeLock({...this.ListInfo})
        if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
   
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
