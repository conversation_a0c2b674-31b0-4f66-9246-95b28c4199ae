<template>
    <my-container v-loading="pageLoading">
        <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules" :disabled="islook">
            <div class="bzjzcjrw">
                <div class="bt">
                    <span style="float: left">创建加工</span>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">成品名称</div>
                    <div class="lxwz2 formtop">
                        <el-form-item prop="finishedProductName"  label=" " label-width="12px">
                            <!-- <el-input style="width:100%" :clearable="true" v-model="addForm.finishedProductName"
                                :maxlength=100 key="spmc"></el-input> -->
                            <vxe-input :maxlength="100" disabled style="height: 28px; width: 320px; font-size: 12px;" v-model="addForm.finishedProductName" placeholder="成品名称" clearable></vxe-input>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">成品编码</div>
                    <div class="lxwz2">
                        <el-form-item label=" " label-width="12px">
                            <!-- <el-select v-model="addForm.finishedProductCode" :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform" >
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select> -->
                            <div style="display: flex; flex-direction: row;">
                                <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                                <div style="width:65%">
                                    <el-input :clearable="true" disabled @change="changecode" v-model="finishedProductCode"
                                         :maxlength=100 key="cpbm">
                                        <el-button slot="append" icon="el-icon-plus" @click="onSelctCp(0)"></el-button>
                                    </el-input>
                                </div>
                                <!-- <div style="margin-left: 20px;text-align: right;">
                                    <span><el-button type="primary"
                                            @click="tofinishedProductCode">暂无编码</el-button></span>
                                </div> -->
                            </div>

                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">品牌</div>
                    <div class="lxwz2">
                        <el-form-item prop="brandName" label=" " label-width="12px">
                            <el-select style="width:30%" v-model="addForm.brandCode" :clearable="true" :collapse-tags="true"
                                filterable>
                                <el-option v-for="item in allsellistfuc.brandList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx" v-if="createprocessinghide">
                    <div class="lxwz">包装材料</div>
                    <div class="lxwz2">
                        <el-form-item prop="packingMaterialName" label=" " label-width="12px">
                            <el-select style="width:50%" v-model="addForm.packingMaterialCode" :clearable="true" filterable>
                                <el-option v-for="item in allsellistfuc.packingMaterialList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx" v-if="createprocessinghide">
                    <div class="lxwz">机型</div>
                    <div class="lxwz2">
                        <el-form-item prop="machineTypeName" label=" " label-width="12px">
                            <el-select style="width:40%" v-model="addForm.machineTypeCode" filterable>
                                <el-option v-for="item in allsellistfuc.machineTypeList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx" v-if="createprocessinghide">
                    <div class="lxwz">包装尺寸</div>
                    <div class="lxwz2">
                        <el-form-item label=" " label-width="12px">
                            <el-select style="width:45%" v-model="addForm.packageSizeCode" filterable>
                                <el-option v-for="item in allsellistfuc.packageSizeList" :key="item.setId" :label="item.sceneCode"
                                    :value="item.setId" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>

                <div class="bzccjlx">
                    <div class="lxwz">成品数量</div>
                    <div class="lxwz2">

                        <el-form-item label=" " label-width="12px">
                            <div style="display: flex; flex-direction: row;">
                                <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                                <el-input-number :step="1" controls-position="right" style="width:250px" :clearable="true" v-model="addForm.finishedProductQuantity"
                                    v-model.trim="addForm.finishedProductQuantity" :min="1" :max="1000000"></el-input-number>
                            </div>
                        </el-form-item>

                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">半成品加工仓</div>
                    <div class="lxwz2">
                        <el-form-item prop="halfProcessWarehouse" label=" " label-width="12px">
                            <el-select style="width:45%" v-model="addForm.halfProcessWarehouse" :clearable="true" :collapse-tags="true"
                                filterable>
                                 <el-option v-for="item in wareList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">成品调入仓</div>
                    <div class="lxwz2">
                        <el-form-item prop="finishTranWarehouse" label=" " label-width="12px">
                            <el-select style="width:45%" v-model="addForm.finishTranWarehouse" :clearable="true" :collapse-tags="true"
                                filterable>
                                 <el-option v-for="item in wareList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
                <div class="bzccjlx">
                    <div class="lxwz">计划完成日期</div>
                    <div class="lxwz2">
                        <el-form-item prop="pfDate" label=" " label-width="12px">
                            <el-date-picker v-model="addForm.pfDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                type="date" style="width:35%" placeholder="结束时间">
                            </el-date-picker>
                        </el-form-item>
                    </div>
                </div>

                <div class="box-card">
                    <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
                        <div style="width:50%;line-height:28px;font-size:16px;"><span style="color: #F56C6C;">*</span>半成品编码</div>
                        <div style="width:50%;text-align: right;"><el-button type="primary"
                                    @click="onSelctCp(1)">选择半成品编码</el-button></div>
                    </div>
                    <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
                        <el-table :data="addForm.detialPackagesProcess"  header-row-class-name="bcpb">
                            <el-table-column label="序号" width="50" align="center">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column prop="id" label="id" v-if="false" />
                            <el-table-column prop="halfProductCode" label="半成品编码" width="120" />
                            <el-table-column prop="halfProductName" label="半成品名称" width="200"/>
                            <el-table-column prop="halfProductQuantity" label="组合数量" width="160">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.halfProductQuantity" :min="0" :max="10000"
                                        placeholder="数量" :precision="4">
                                    </el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column lable="操作">
                                <template slot-scope="scope">
                                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">移除 <i
                                            class="el-icon-remove-outline"></i>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>

            </div>

        </el-form>
        <!--选择商品-->
        <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag append-to-body>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="参考列表" :visible.sync="dialogVisible" width="35%" :append-to-body="true">
            <shootingreferencelist ref="shootingreferencelist" style="z-index:10000;height:400px"></shootingreferencelist>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关 闭</el-button>
                    <el-button type="primary" @click="selreference()">创建任务</el-button>
                </span>
            </template>
        </el-dialog>
    </my-container>
</template>
<script>
import uploadfile from '@/views/media/shooting/uploadfile'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { addOrUpdateShootingVideoTaskAsync, getShootingTaskFliesAsync, delShootingTploadFileTaskAsync } from '@/api/media/ShootingVideo';
import { editPackagesProcess,replaceTemplatesWorkPrice,getAllWarehouse } from '@/api/inventory/packagesprocess';
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatWarehouse } from "@/utils/tools";
import shootingreferencelist from '@/views/media/shooting/fuJianmanage/shootingreferencelistSel';
import semifinishedtable from '@/views/media/packagework/semifinishedtable.vue';
import goodschoice from "@/views/base/goods/goods2.vue";
import { getShootingSetDataById, getShootingSetData, saveShootingSet, deleteShootingSet, saveDataOrderListDataAsync } from '@/api/media/shootingset'
export default {
    props: ["taskUrgencyList", 'groupList', 'platformList', 'islook', 'onCloseAddForm'],
    inject: ['allsellist'],
    components: { MyContainer, MyConfirmButton, uploadfile, shootingreferencelist, semifinishedtable, goodschoice },
    data() {
        return {
            createprocessinghide: false,
            that: this,
            addLoading: true,
            dialogFormVisible: false,
            dialogVisible: false,
            pageLoading: false,
            endworktime: '',
            formatWarehouse: formatWarehouse,
            //选择商品窗口
            goodschoiceVisible: false,
            allAlign1: null,
            tableData1: [
            ],
            shopList: [],
            userList: [],
            taskPhotofileList: [],
            taskExeclfileList: [],

            packageSizeList: [],
            machineTypeList: [],
            packingMaterialList: [],
            brandList: [],
            wareList:[],
            finishedProductCode: '',
            addForm: {
                packagesProcessId: 0,
                finishedProductName: "",
                finishedProductCode: "",
                finishedProductImg: "",
                brandCode: "",
                brandName: "",
                packingMaterialCode: "",
                packingMaterialName: "",
                machineTypeCode: "",
                machineTypeName: "",
                packageSizeCode: "",
                packageSizeName: "",
                finishedProductQuantity: 0,
                pfDate: "",
                urgencyDegree: "",
                quantityRequired: 0,
                certificateInfo: "",
                remark: "",
                detialPackagesProcess: [],
                finishTranWarehouse: null,
                halfProcessWarehouse: "",
            },
            loginfo: null,
            fpPhotoLqNameEnable: false,
            fpVideoLqNameEnable: false,
            fpDetailLqNameEnable: false,
            fpModelLqNameEnable: false,
            addFormRules: {
                finishedProductName: [{ required: true, message: '请填写', trigger: 'blur' }],
                finishedProductCode: [{ required: true, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                isDelivered: [{ required: true, message: '请选择', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],

            },
            extBzTypeArgs: null,
        };
    },
    async created() {
        // this.gettabmsg();
    },
    async mounted() {
        this.addForm.dockingPeople = this.$store.getters.userName?.split("-")[0].trim();
        this.setWare();
        // this.gettabmsg();
    },
    computed: {
        calcAddFormRules() {
            return {
                finishedProductName: [{ required: true, message: '请填写', trigger: 'blur' }],
                // finishedProductCode: [{ required: false, message: '请选择', trigger: 'blur' }],
                operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                isDelivered: [{ required: true, message: '请选择', trigger: 'blur' }],
                shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                platform: [{ required: true, message: '请选择', trigger: 'blur' }],
                isYYJY: [{ required: true, message: '请选择', trigger: 'blur' }],
                yyExpressNum: [{ required: this.addForm.isYYJY == 1, message: '请填写', trigger: 'blur' }],
            }
        },
        allsellistfuc(){
            return this.allsellist()
        }
    },
    methods: {
        tofinishedProductCode(){
            this.finishedProductCode = '暂无编码';
        },
        changecode(val){
            this.addForm.finishedProductCode = val;
        },
        // gettabmsg(data) { //14包装加工，15-品牌，16包装加工-机型，17包装加工-尺寸
        //     this.packingMaterialList = data.packingMaterialList;
        //     this.brandList = data.brandList;
        //     this.machineTypeList = data.machineTypeList;
        //     this.packageSizeList = data.packageSizeList;
        // },
        // getallmsg(data){
        //     this.packingMaterialList = data.packingMaterialList;
        //     this.brandList = data.brandList;
        //     this.machineTypeList = data.machineTypeList;
        //     this.packageSizeList = data.packageSizeList;
        // },
        //移除明细
        async onDelDtlGood(index) {
            this.addForm.detialPackagesProcess.splice(index, 1);
        },
        //选择商品确定
        async onQueren() {
            if (this.seltype == 0) {
                //选择成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.addForm.finishedProductCode = f.goodsCode;
                        this.finishedProductCode = f.goodsCode;
                        this.addForm.finishedProductName = f.goodsName;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 2) {
                //完成界面选择商品
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.finishForm.consumeGoodsCode = f.goodsCode;
                        this.finishForm.consumeGoodsName = f.goodsName;
                        this.finishForm.consumePicture = f.picture;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 10) {
                //完成界面选择商品
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.updateUserGoodsAmountData.consumeGoodsCode = f.goodsCode;
                        this.updateUserGoodsAmountData.consumeGoodsName = f.goodsName;
                        this.updateUserGoodsAmountData.consumePicture = f.picture;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else {
                //选择半成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                    //反填数据,
                    // if (this.addForm.detialPackagesProcess) {
                        if(!this.addForm.detialPackagesProcess){
                            this.addForm.detialPackagesProcess = []
                        }
                        //已存在的不添加
                        var temp = this.addForm.detialPackagesProcess;
                        var isNew = true;
                        choicelist.forEach(f => {
                            isNew = true;
                            if(temp?.length>0){
                                temp.forEach(old => {
                                    if (old.halfProductCode == f.goodsCode) {
                                        isNew = false;
                                    }
                                });
                            }

                            //
                            if (isNew) {
                                this.addForm.detialPackagesProcess.push({ halfProductCode: f.goodsCode, halfProductName: f.goodsName, halfProductQuantity: 0, dtlActualGoodsAmount: 0 });

                            } else {
                                this.addForm.detialPackagesProcess.forEach(told => {
                                    if (told.halfProductCode == f.goodsCode) {
                                        told.halfProductName = f.goodsName;
                                    }
                                });
                            }
                        })

                    // }
                    this.goodschoiceVisible = false;
                }
            }
        },
        //新增/编辑/完成界面的【选择商品】按钮
        onSelctCp(type) {
            this.seltype = type;
            this.goodschoiceVisible = true;
            this.$nextTick(() => {
                this.$refs.goodschoice.removeSelData();
            })
        },
        async OpenAdd() {
            this.addDialogTitle = "创建加工表单";
            this.dialogFormVisible = true;
        },
        async selreference() {
            var res = await editPackagesProcess(this.addForm);
            this.addLoading = false;
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' }); return; }
            this.$message({ message: '创建任务成功！', type: 'success' });
            this.finishedProductCode = "";
        },
        reference() {
            this.dialogVisible = true;
        },
       async setWare() {
            const res = await getAllWarehouse();
            if (!res?.success) {
                return
            }
            this.wareList = res?.data;
        },
        //编码
        taskPickChange(value) {
            //未选择，只读，且清空
            if (value.indexOf("1") < 0) {
                this.fpPhotoLqNameEnable = true;
            } else {
                this.fpPhotoLqNameEnable = false;
            }
            if (value.indexOf("2") < 0) {
                this.fpVideoLqNameEnable = true;
            } else {
                this.fpVideoLqNameEnable = false;
            }
            if (value.indexOf("3") < 0) {
            } else {
            }
            if (value.indexOf("4") < 0) {
                this.fpDetailLqNameEnable = true;
            } else {
                this.fpDetailLqNameEnable = false;
            }
            if (value.indexOf("5") < 0 && value.indexOf("6") < 0) {
                this.fpModelLqNameEnable = true;
            } else {
                this.fpModelLqNameEnable = false;
            }
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.addForm.shopName = "";
            this.shopList = res1.data.list;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        //提交保存
        async onSubmit() {
            this.pageLoading = true;
            if(!this.finishedProductCode){
                this.pageLoading = false;
                this.$message({ message: '请选择成品编码', type: 'error' }); return;
            }
            if(this.finishedProductCode=='暂无编码'){
                this.addForm.finishedProductCode='暂无编码';
            }
            var res = await editPackagesProcess(this.addForm);
            //
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' });this.pageLoading = false; return; }
            if (res.data.templateId<=0||!res.data.templateId) {
                this.$message({ message: '创建任务成功！', type: 'success' });
                this.finishedProductCode = "";
                this.addLoading = false;
                this.pageLoading = false;
                this.onCloseAddForm(1);
            }else{

            this.$confirm("当前任务存在历史工价，是否覆盖？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                distinguishCancelAndClose: true,
            })
                .then(async () => {
                let params = {
                    ...res.data
                }
                var ress = await replaceTemplatesWorkPrice(params);
                if (ress?.success) {

                    this.$message({ message: '创建任务成功！', type: "success" });
                    await this.$emit('getTaskList');
                    this.onCloseAddForm(1);
                    this.addLoading = false;
                    this.pageLoading = false;
                }
                }).catch(async (e) => {
                    this.$message({ message: '未沿用工价，创建任务成功！', type: 'success' });
                    this.onCloseAddForm(1);
                    this.addLoading = false;
                    this.pageLoading = false;
                    // if (e == 'cancel') {
                    //     let params = {
                    //     processId: row.packagesProcessingId,
                    //     degreeType: 2
                    //     }
                    //     var res = await updateUrgencyDegree(params);
                    //     if (res?.success) {
                    //     that.$message({ message: '更新紧急程度成功！', type: "success" });
                    //     await this.$emit('getTaskList');
                    //     }

                    // } else if (e == 'close') {

                    // }
                });
                ////////////////////////////////
            }
            //////////////
            // this.onCloseAddForm(1);
        },
        //删除上传附件操作
        async deluplogexl(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 2 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        //删除上传图片操作
        async deluplogimg(ret) {
            this.addLoading = true;
            await delShootingTploadFileTaskAsync({ upLoadPhotoId: ret.upLoadPhotoId, type: 1 }).catch(_ => {
                this.addLoading = false;
            });
            this.addLoading = false;
        },
        initaddform(initData){
            this.addForm = {};
            this.finishedProductCode = "";
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep [class*=el-col-] {
    height: 35px !important;
}

::v-deep .bzjzcjrw {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
    height: 40px;
    /* background-color: aquamarine; */
    font-size: 18px;
    color: #666;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    padding: 0 35px;
}

::v-deep .bzjzcjrw .bzccjlx {
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
    width: 100%;
    height: 35px;
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
    width: 20%;
    font-size: 14px;
    color: #666;
    vertical-align: top;
    line-height: 26px;
    /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
    width: 80%;
}

::v-deep .box-card {
    margin-top: 20px;
    box-sizing: border-box;
    padding: 0 60px;

}
::v-deep .bcpb {
    height:50px !important;
    color:#909399 !important;
}
::v-deep .el-form{
    margin-top: -18px !important;
}

</style>

