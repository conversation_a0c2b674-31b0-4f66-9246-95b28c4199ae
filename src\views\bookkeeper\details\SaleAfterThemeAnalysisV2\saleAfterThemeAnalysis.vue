<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>

                <el-form-item label="数据日期">
                    <el-date-picker style="width: 220px" v-model="filter.dataTime" type="datetimerange" :clearable="true"
                      range-separator="至" start-placeholder="日期开始时间" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      end-placeholder="日期结束时间"></el-date-picker>
                </el-form-item>
                <el-form-item label="确认日期">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="true"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="线上订单号:">
                    <el-input v-model.trim="filter.OrderNo" :clearable="true" maxlength="20" placeholder="线上订单号" style="width:130px;"/>
                </el-form-item>
                <el-form-item label="店铺款式编码:">
                    <el-input v-model.trim="filter.ProCode" :clearable="true" maxlength="20" placeholder="店铺款式编码" style="width:130px;"/>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.Platform" placeholder="请选择平台"  style="width:130px;">
                        <el-option label="淘系" value="1"></el-option>
                        <el-option label="拼多多" value="2"></el-option>
                        <el-option label="阿里巴巴" value="4"></el-option>
                        <el-option label="抖音" value="6"></el-option>
                        <el-option label="京东" value="7"></el-option>
                        <el-option label="淘工厂" value="8"></el-option>
                        <el-option label="苏宁" value="10"></el-option>
                        <el-option label="分销" value="11"></el-option>
                        <el-option label="快手" value="14"></el-option>
                        <el-option label="视频号" value="20"></el-option>
                        <el-option label="小红书" value="21"></el-option>
                      </el-select>
                </el-form-item>

                <el-form-item label="店铺:">
                    <el-input v-model.trim="filter.ShopName" :clearable="true" maxlength="20" placeholder="店铺" style="width:130px;"/>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" @close="closediolag"  width="40%" v-dialogDrag>
            <span :gutter="20">
                <el-row >
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                      <el-date-picker style="width: 100%" v-model="importfilter.YearMonthDay" type="date" format="yyyyMMdd"
                      value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
                    </el-col>
                    <el-col :xs="4" :sm="6" :md="8" :lg="6">
                      <el-select  filterable v-model="importfilter.PlatForm" placeholder="请选择平台" clearable>
                        <el-option label="淘系" value="1"></el-option>
                        <el-option label="拼多多" value="2"></el-option>
                        <el-option label="阿里巴巴" value="4"></el-option>
                        <el-option label="抖音" value="6"></el-option>
                        <el-option label="京东" value="7"></el-option>
                        <el-option label="淘工厂" value="8"></el-option>
                        <el-option label="苏宁" value="10"></el-option>
                        <el-option label="分销" value="11"></el-option>
                        <el-option label="快手" value="14"></el-option>
                        <el-option label="视频号" value="20"></el-option>
                        <el-option label="小红书" value="21"></el-option>
                      </el-select>
                    </el-col>
                  <el-col :xs="4" :sm="6" :md="8" :lg="6">
                      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false"  action
                          accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                          >
                          <template #trigger>
                              <el-button size="small" type="primary">选取文件</el-button>
                          </template>
                          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                          @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                      </el-upload>
                  </el-col>
              </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { importAfterSalesSubjectAnalysis as importBillFee, } from '@/api/bookkeeper/reportdayV2'
import {getAfterSalesSubjectAnalysis as getNewPddBillingCharge} from '@/api/bookkeeper/reportday'
const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '日期', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'afterOrderNumber', label: '售后单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderNoInner', label: '内部订单号', tipmesg: '', width: '100', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
    { istrue: true, prop: 'orderNo', label: '线上订单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'receiptNumber', label: '进仓单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderStatus', label: '订单状态', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'shopName', label: '店铺', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'commodityAbbreviation', label: '商品简称', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'proCode', label: '店铺款式编码', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'productName', label: '商品名称', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'orderPaymentDate', label: '订单支付日期', tipmesg: '', width: '100', sortable: 'custom', },
   // { istrue: true, prop: 'receiptDate', label: '进仓时间', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'confirmationDate', label: '确认日期', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'originalOrderLabel', label: '原订单标签', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'originalOrderType', label: '原订单类型', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'quantityReturned', label: '退货数量', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'retractedQuantity', label: '实退数量', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'returnAmount', label: '退货金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'refundableAmount', label: '实退金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'returnCostAmount', label: '退货成本金额', tipmesg: '', width: '100', sortable: 'custom', },
]

const tableHandles = [
    { label: "导入", handle: (that) => that.startImport() },
];

const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton },

    data() {
        return {
            that: this,
            importfilter: {
             PlatForm: null,
             YearMonthDay:null
            },
            filter: {
                startTime: null,
                endTime: null,
                timerange: null,
                shopCode: null,
                dataTime:null,
                dataStartDate: null,
                dataEndDate: null,
                Platform:"2"
            },
            platformlist:platformlist,
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    platform: null
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "ConfirmationDate", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: []
        };
    },

    async mounted() {
        await this.initit();
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
      datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async initit(){
      var date1 = new Date(); date1.setDate(date1.getDate()-1);
      var date2 = new Date(); date2.setDate(date2.getDate()-1);
      this.filter.dataTime=[];
      this.filter.dataTime[0]=this.datetostr(date1);
      this.filter.dataTime[1]=this.datetostr(date2);
    },
        //关闭Dilog
        closediolag(){
            this.$refs.upload.clearFiles();
                    this.dialogVisible = false;
                    this.fileList = [];
                    this.uploadLoading = false;
                    this.importfilter.YearMonthDay=null;
                    this.importfilter.PlatForm=null;
		        },
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100 });
            this.filter.shopCode = null
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            this.filter.dataStartDate = null;
            this.filter.dataEndDate = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            if (this.filter.dataTime) {
                this.filter.dataStartDate = this.filter.dataTime[0];
                this.filter.dataEndDate = this.filter.dataTime[1];
            }
            if (!this.filter.Platform || this.filter.Platform == null) {
                this.$message({ message: "请先选择平台", type: "warning" });
                return false;
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getNewPddBillingCharge(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        //字体颜色
        // renderRefundStatus(row) {
        //     if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
        //         return "color:red;cursor:pointer;";
        //     } else return "";
        // },
        //开始导入
        startImport() {
            this.fileList = []
            this.uploadLoading=false
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (!this.importfilter.PlatForm || this.importfilter.PlatForm == null) {
        this.$message({ message: "请先选择平台", type: "warning" });
        return false;
           }
           if (!this.importfilter.YearMonthDay || this.importfilter.YearMonthDay == null) {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
           }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$nextTick(()=>{
                this.uploadFile(this.fileList[0].raw);
            })
        },
        clearFiles(){
            this.$refs['upload'].clearFiles();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item);
            form.append("YearMonthDay", this.importfilter.YearMonthDay);
            form.append("PlatForm", this.importfilter.PlatForm);
            let res = await importBillFee(form);
                if (res.code == 1) {
                    this.$message({ message: "上传成功,正在导入中...", type: "success" });
                    this.$refs.upload.clearFiles();
                    this.dialogVisible = false;
                    this.fileList = [];
                    this.uploadLoading = false;
                    this.importfilter.YearMonthDay=null;
                    this.importfilter.PlatForm=null;
                }
                else
                {
                 this.uploadLoading = false;
                }
        },
        async uploadChange(file, fileList) {
            let files=[];
            files.push(file)
            this.fileList = files;
        },
        async uploadRemove(file, fileList) {
            this.fileList = []
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

</style>
