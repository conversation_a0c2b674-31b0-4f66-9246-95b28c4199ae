<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        </el-form>
      </template>
      <!--列表-->
      <template tyle="height: 99%;">
        <vxetablebase ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
          @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false'
          :tableCols='tableCols' :loading="listLoading">

          <el-table-column type="expand">
            <template slot-scope="props">
              <div>
                <el-table :data="props.row.detaildata" style="width: 100%">
                  <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                  </el-table-column>
                </el-table>
              </div>
            </template>
            
          <template slot='extentbtn'></template>
          
          </el-table-column>
        </vxetablebase>

        <!-- <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
          @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false'
          :tableCols='tableCols' :loading="listLoading">
          <el-table-column type="expand">
            <template slot-scope="props">
              <div>
                <el-table :data="props.row.detaildata" style="width: 100%">
                  <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <template slot='extentbtn'>
  
          </template>
        </ces-table> -->
        <!--分页-->
      </template>
  
      <template #footer>
        <my-pagination ref="myPager" :total="total" @get-page="onRefresh" />
      </template>
    </my-container>
  </template>
  <script>
  import { getInquirsStatisticsByShopListByUser } from '@/api/customerservice/pddInquirsnew'
  import cesTable from "@/components/Table/table.vue";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  
  import Decimal from 'decimal.js';
  function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
  }
  
  const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '80' },
  
    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.successpayrate ? row.successpayrate : 0) + "%" } 
        // formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100) : 0) + "%" }
    },
  
    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', 
        formatter: (row) => { return (row.threeSecondReplyRate ? row.threeSecondReplyRate : 0) + "%" } 
        // formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100) : 0) + "%" }
    },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', 
        formatter: (row) => { return (row.thirtySecondResponseRate ? row.thirtySecondResponseRate : 0) + "%" } 
        // formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100) : 0) + "%" }
    },
  
    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },
  
    // { istrue: true, prop: 'inquireCost', label: '接待成本（元）', width: '130', sortable: 'custom' },
    // { istrue: true, prop: 'inquireCostPrice', label: '接待成本单价', width: '130', sortable: 'custom' },
    // { istrue: true, prop: 'ipsCostPrice', label: '询单成本单价', width: '130', sortable: 'custom' },
    // { istrue: true, prop: 'receiveCostPrice', label: '成团成本单价', width: '130', sortable: 'custom' },
  
    { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },
  
  ];
  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase },
    data() {
      return {
        that: this,
        Filter: {
        },
        shopList: [],
        userList: [],
        groupList: [],
        inquirsstatisticslist: [],
        tableCols: tableCols,
        total: 0,
        summaryarry: { count_sum: 10 },
        pager: { OrderBy: "ipscount", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        //
        selids: [],
        dialogVisibleSyj: false,
        fileList: [],
        params: {},
      };
    },
    async mounted() {
      // this.$nextTick(() => {
      //   // 确保子组件DOM已经更新完成
      //   this.$refs.child.$nextTick(() => {
      //     // 子组件的内部DOM更新也已完成，可以进行后续操作
  
  
      //   });
      // });
      this.$refs.myPager.setPage(1);
      this.loadParam();
      this.onRefresh();
    },
    created() {
  
  
    },
    methods: {
  
      async canclick(row, column, cell) {
  
        //  if(this.filter.userId>0)
        //  this.detailName=row.userName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
        // if(this.filter.operateSpecialUserId>0)
        //  this.detailName=row.operateSpecialUserName+"的各商品业绩明细，统计周期："+this.filter.startTime+"--"+this.filter.endTime;
        //   window.financialReportDetailByOneUser={startTime:this.filter.startTime,endTime:this.filter.endTime,userId:this.filter.userId,operateSpecialUserId:this.filter.operateSpecialUserId,shopCode:row.shopCode}
        //  this.dialogVisible=true;
        //  if(this.$refs.financialReportDetailByOneUser)
        //  {
        //    this.$refs.financialReportDetailByOneUser.onSearch();
        //  } 
      },
  
  
      async deleteBatch(row) {
        var that = this;
        this.$confirm("此操作将删除此批次个人效率统计数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            await deleteInquirsStatisticsBatch({ batchNumber: row.batchNumber })
            that.$message({ message: '已删除', type: "success" });
            that.onRefresh()
  
          });
  
      },
      sortchange(column) {
        if (!column.order)
          this.pager = {};
        else
          this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
        this.onRefresh();
      },
      onImportSyj() {
        this.dialogVisibleSyj = true
      },
      async loadData() {
        const res = await getInquirsStatisticsByShopListByUser(this.params);
        this.total = res.total
        this.inquirsstatisticslist = res.list;
        this.summaryarry = res.summary;
      },
      loadParam() {
        this.Filter.startSdate = localStorage.getItem("startsdate")
        this.Filter.endSdate = localStorage.getItem("endsdate")
        this.Filter.sname = localStorage.getItem("sname")
        this.Filter.EnmPddGroupType = 0;
  
      },
      async onRefresh() {
  
        const para = { ...this.Filter };
        var pager = this.$refs.myPager.getPager();
        this.params = {
          ...pager,
          ...this.pager,
          ...para,
        }
  
        this.listLoading = true;
        await this.loadData();
        this.listLoading = false;
      },
  
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach(f => {
          this.selids.push(f.id);
        })
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  