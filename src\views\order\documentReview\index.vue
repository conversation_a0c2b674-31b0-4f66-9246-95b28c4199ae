<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="利润分析" name="seven" :lazy="true" style="height: 100%;">
                <ProfitAnalysis />
            </el-tab-pane>
            <el-tab-pane label="汇总数据" name="first" :lazy="true" style="height: 100%;">
                <summaryVue />
            </el-tab-pane>
            <el-tab-pane label="计算中" name="second" :lazy="true" style="height: 100%;">
                <computeVue />
            </el-tab-pane>
            <el-tab-pane label="已完成" name="third" :lazy="true" style="height: 100%;">
                <finishedVue />
            </el-tab-pane>
            <el-tab-pane label="已同步" name="fourth" :lazy="true" style="height: 100%;">
                <synchronousVue />
            </el-tab-pane>
            <el-tab-pane label="二次组团" name="fifth" :lazy="true" style="height: 100%;">
                <secondaryGrouping />
            </el-tab-pane>
            <el-tab-pane label="蓄单设置" name="sixth" :lazy="true" style="height: 100%;">
                <chargeOrder />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import computeVue from "./component/compute.vue";
import finishedVue from "./component/finished.vue";
import synchronousVue from "./component/synchronous.vue";
import summaryVue from "./component/summary.vue";
import chargeOrder from "./component/chargeOrder.vue";
import secondaryGrouping from "./component/secondaryGrouping.vue";
import ProfitAnalysis from "./component/ProfitAnalysis.vue";
export default {
    components: {
        MyContainer, computeVue, finishedVue, synchronousVue, summaryVue, chargeOrder, secondaryGrouping, ProfitAnalysis
    },
    data() {
        return {
            activeName: 'seven'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>