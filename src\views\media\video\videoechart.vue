<template>
    <div class="dashboard-editor-container" style="height:100%;">
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="18">
                <div class="chart-wrapper">
                    <div class="chart-wrapper11">
                        <el-date-picker style="width:310px;margin-left:13px;"
                         type="daterange" format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd" 
                          range-separator="至" start-placeholder="开始日期"
                           end-placeholder="结束日期"  
                            v-model="mainviewfilter.timerange"
                            :picker-options="pickerOptions"
                             @change="showviewMain" />
                        <el-button type="primary" @click="showviewMain">查询</el-button>
                        <span style="margin-left:50px ;font-size:small;">{{topSumInfo1}} </span>
                        <span style="margin-left:10px ;font-size:small;">{{topSumInfo2}} </span>
                        <span style="margin-left:10px ;font-size:small;">{{topSumInfo3}} </span>
                        <span style="margin-left:10px ;font-size:small;">{{topSumInfo4}} </span>
                        <span style="margin-left:10px ;font-size:small;">{{topSumInfo5}} </span>
                        <span style="margin-left:10px ;font-size:small;">{{topSumInfo6}} </span>
                        <el-checkbox-group v-model="topcheckList" style="float: right;margin-top: 5px;">
                            <el-checkbox label="已拍摄" ></el-checkbox>
                            <el-checkbox label="已完成"></el-checkbox>
                            <el-checkbox label="存档"></el-checkbox>
                        </el-checkbox-group>
                        <buschar ref="viewMain" :analysisData="expressData" :thisStyle="thisStyleMain" :gridStyle="gridStyleMain" />
                    </div>
                </div>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6" >
                <div class="chart-wrapper">
                    <div class="chart-wrapper11">
                    <div  style=" height:30px ;font-size:small;">
                        <!-- <el-date-picker style="width:310px;margin-left:13px;" type="daterange" format="yyyy-MM-dd"   value-format="yyyy-MM-dd" 
                          range-separator="至" start-placeholder="开始日期"  end-placeholder="结束日期"   v-model="selectMonth.timerange" :picker-options="pickerOptions"
                         @change="statisticalperformance" /> -->
                      <span style="margin-left:5px ;font-size:small;">统计已完成的拍摄条数 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span> 
                       <el-button type="primary"  style="float:right;margin-right: 10px;  " @click="statisticalperformance">刷新</el-button>
                    </div>
                    <ces-table ref="table" 
                    :that='that' 
                    :isIndex='false' 
                    :hasexpand='false'
                    :isSelectColumn='false'  
                    :tableData='tasklist' 
                    :tableCols='tableCols' 
                    :loading="listLoading" > 
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <div>
                                <el-table :data="props.row.detaildata" style="width: 100%">
                                    <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template> 
                    </el-table-column>
                    </ces-table> 
                </div>
                </div>
            </el-col>
        </el-row>
        <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :lg="24">
                <div class="chart-wrapper2">
                    <div class="chart-wrapper22">
                        <el-date-picker style="margin-left:13px;" 
                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                         range-separator="至" 
                         start-placeholder="开始日期" 
                         end-placeholder="结束日期" 
                         
                          v-model="view1filter.timerange"
                          :picker-options="pickerOptions"
                           @change="showView1()" />
                        <el-button type="primary" @click="showView1">查询</el-button>
                        <span style="margin-left:50px;font-size:small;">{{cuteSumInfo1}} </span>
                        <span style="margin-left:10px;font-size:small;">{{cuteSumInfo2}} </span>
                        <span style="margin-left:10px;font-size:small;">{{cuteSumInfo3}} </span>
                        <span style="margin-left:10px;font-size:small;">{{cuteSumInfo4}} </span>
                        <span style="margin-left:10px;font-size:small;">{{cuteSumInfo5}} </span>
                        <span style="margin-left:10px;font-size:small;">{{cuteSumInfo6}} </span>

                        <el-checkbox-group v-model="cutecheckList" style="float: right;margin-top: 5px;">
                            <el-checkbox label="已拍摄" ></el-checkbox>
                            <el-checkbox label="已完成"></el-checkbox>
                            <el-checkbox label="存档"></el-checkbox>
                        </el-checkbox-group>
                        <buschar ref="view1" :analysisData="viewData1" :thisStyle="thisStyle" :gridStyle="gridStyle"></buschar>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>

    import cesTable from "@/components/Table/table.vue";
    import buschar from '@/components/Bus/buschar.vue'; 
    import { getVideoViewAsync,getstatisticalperformance } from '@/api/media/video';
    const tableCols = [
        { istrue: true, prop: 'name', label: '姓名'  },
        { istrue: true, prop: 'shootNum', label: '条数'   },
        { istrue: true, prop: 'merits', label: '绩效' ,type:'inputnumber' ,min:'0',max:'100',precision:'2',change: meritsChange },
        { istrue: true, prop: 'total', label: '合计'  }, 
    ];   
    function meritsChange(row){
        row.total =row.merits ==null ?null: (row.merits * row.shootNum).toFixed(2);
    };
    export default {
        name: "videoechart",
        components: { buschar,cesTable },
        data() {
            return {
                that:this,
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    },    
                },
                tasklist:[],
                tableCols: tableCols,
                topSumInfo1:null,
                topSumInfo2:null,
                topSumInfo3:null,
                topSumInfo4:null,
                topSumInfo5:null,
                topSumInfo6:null,
                topSumInfo7:null,
                cuteSumInfo1:null,
                cuteSumInfo2:null,
                cuteSumInfo3:null,
                cuteSumInfo4:null,
                cuteSumInfo5:null,
                cuteSumInfo6:null,
                cuteSumInfo7:null,
                topcheckList:['已拍摄','已完成'],
                cutecheckList:['已拍摄','已完成'],
                monthcheckList:['已拍摄','已完成'], 
                beforehbalfMonth: null,
                afterhbalfMonth: null,
                totalMonth: null,
                thisStyleMain: { width: '1250px', height: '380px', 'box-sizing': 'border-box', 'line-height': '240px' },
                gridStyleMain: {
                    left: '1%',
                    right: 15,
                    bottom: 20,
                    top: '12%',
                    containLabel: true
                },
                thisStyle: { width: '1650px', height: '300px', 'box-sizing': 'border-box', 'line-height': '120px' },
                gridStyle: {
                    left: '1%',
                    right: 10,
                    bottom: 20,
                    top: '15%',
                    containLabel: false
                },
                pageLoading: true,
                expressData: {},
                viewData1: {},
                viewData2: {},
                viewData3: {},
                viewData4: {},
                viewData5: {},
                mainviewfilter: {
                    startTime: null,
                    endTime: null,
                    timerange: [],
                  
                },
                view1filter: {
                    startTime: null,
                    endTime: null,
                    timerange: [],
                  
                },
                selectMonth:{
                    startTime: null,
                    endTime: null,
                    timerange: []
                },
                listLoading :false
            };
        },
        async mounted() {
            await this.voidInitCharts();
        },
        methods: {
            meritsChange:meritsChange,
           
            async voidInitCharts() {
                await this.showviewMain();
                await this.statisticalperformance();
                await this.showView1();
            },
            async showviewMain() {
                //获取数据
                if (this.mainviewfilter.timerange) {
                    this.mainviewfilter.startTime = this.mainviewfilter.timerange[0];
                    this.mainviewfilter.endTime = this.mainviewfilter.timerange[1];
                }else{

                    this.mainviewfilter.startTime =  null;
                    this.mainviewfilter.endTime = null;
                }
                var res1 = await getVideoViewAsync({ "viewName": "viewMain", "startTime": this.mainviewfilter.startTime, "endTime": this.mainviewfilter.endTime 
                ,"checkdata":   this.topcheckList 
                 })

                this.setOptions(res1);
                this.expressData = res1;
                await this.$refs.viewMain.initcharts();
                this.topSumInfo1 =null;
                this.topSumInfo2 =null;
                this.topSumInfo3 =null;
                this.topSumInfo4 =null;
                this.topSumInfo5 =null;
                this.topSumInfo6 =null;
                for(var i =0; i < res1.summuydata.length;i++){
                    if(res1.summuydata[i].seriesName  =="拍摄条数"){
                        this.topSumInfo1 = "拍摄合计：" + res1.summuydata[i].seriestotal;
                    }else  
                    if(res1.summuydata[i].seriesName  =="待拍条数"){
                        this.topSumInfo2 = "待拍合计：" + res1.summuydata[i].seriestotal;
                    }else  
                    if(res1.summuydata[i].seriesName  =="补拍条数"){
                        this.topSumInfo3 = "补拍合计：" + res1.summuydata[i].seriestotal;
                    }else     
                    if(res1.summuydata[i].seriesName  =="补拍次数"){
                        this.topSumInfo4= "补拍次数合计：" + res1.summuydata[i].seriestotal;
                    }else     
                    if(res1.summuydata[i].seriesName  =="待拍条数"){
                        this.topSumInfo5 = "待拍合计：" + res1.summuydata[i].seriestotal;
                    }else     
                    if(res1.summuydata[i].seriesName  =="通过条数"){
                        this.topSumInfo6 = "通过合计：" + res1.summuydata[i].seriestotal;
                    }           
                }  
            },
            
            async showView1() {
                //获取数据
                if (this.view1filter.timerange) {
                    this.view1filter.startTime = this.view1filter.timerange[0];
                    this.view1filter.endTime = this.view1filter.timerange[1];
                }else{
                    this.view1filter.startTime =  null;
                    this.view1filter.endTime = null;
                }
                var res1 = await getVideoViewAsync({ "viewName": "view1", "startTime": this.view1filter.startTime, "endTime": this.view1filter.endTime 
                ,"checkdata":   this.cutecheckList })
                this.setOptions(res1);
                this.viewData1 = res1;
                await this.$refs.view1.initcharts();
                this.cuteSumInfo1 =null;
                this.cuteSumInfo2 =null;
                this.cuteSumInfo3 =null;
                this.cuteSumInfo4 =null;
                this.cuteSumInfo5 =null;
                this.cuteSumInfo6 =null;
                for(var i =0; i < res1.summuydata.length;i++){
                    if(res1.summuydata[i].seriesName  =="剪辑条数"){
                        this.cuteSumInfo1 = "剪辑合计：" + res1.summuydata[i].seriestotal;
                    }else  
                    if(res1.summuydata[i].seriesName  =="待审条数"){
                        this.cuteSumInfo2 = "待审合计：" + res1.summuydata[i].seriestotal;
                    }else  
                    if(res1.summuydata[i].seriesName  =="待剪条数"){
                        this.cuteSumInfo3 = "待剪合计：" + res1.summuydata[i].seriestotal;
                    }      
                        
                }  
            },
            //获取统计数据
            async statisticalperformance()
            {   
                 //获取数据
                if (this.selectMonth.timerange) {
                    this.selectMonth.startTime = this.selectMonth.timerange[0];
                    this.selectMonth.endTime = this.selectMonth.timerange[1];
                }else{
                    this.selectMonth.startTime =  null;
                    this.selectMonth.endTime = null;
                }
                this.listLoading= true;
                var res = await getstatisticalperformance({  "startTime": this.selectMonth.startTime, "endTime": this.selectMonth.endTime});
          
                if(res?.success){
                    this.tasklist=res?.data;
                }
                this.listLoading= false;
            },
            //导出统计数据
            exportStatisticalperformance()
            {

            },
            setOptions(element) {
                element.series.forEach(s => {
                    s.barMaxWidth = '50';
                    if(s.name.indexOf("率")>-1){
                        s.itemStyle = {   normal: { label: { show: true, position: 'top', textStyle: { color:  s.backColor, fontSize: 14 }, formatter: '{c}%'  },color: s.backColor }   }; 
                  
                    }else{

                        s.itemStyle = {   normal: { label: { show: true, position: 'top', textStyle: { color:  s.backColor, fontSize: 14 }  },color: s.backColor }   }; 
                    }
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .indexnew {
        background-color: #fff;
    }
    .timecard {
        margin: 10px;
        height: 280px;
        overflow-y: auto;
    }
    .updateindex {
        margin: 10px;
        height: 300px;
        overflow-y: auto;
    }
    .dashboard-editor-container {
        padding: 10px;
        background-color: rgb(240, 242, 245);
        position: relative;

        .github-corner {
            position: absolute;
            top: 0;
            border: 0;
            right: 0;
        }

        .chart-wrapper {
            background: rgb(240, 242, 245);
            padding: 10px 1px 0;
            margin-bottom: 1px;
            height: 410px;
        }

        .chart-wrapper11 {
            background: #fff;
            padding: 10px 1px 0;
            //margin-bottom: 10px;

            height: 400px;
        }

        .chart-wrapper2 {
            background: rgb(240, 242, 245);
            padding: 10px 1px 0;
            margin-bottom: 1px;
            height: 360px;
        }
        .chart-wrapper22 {
            background: #fff;
            padding: 10px 1px 0;
            //margin-bottom: 10px;
            height: 350px;
        }

    }

    @media (max-width: 1024px) {
        .chart-wrapper {
            padding: 8px;
        }
    }
</style>

