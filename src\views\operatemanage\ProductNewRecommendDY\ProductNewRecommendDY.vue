<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form  class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent ></el-form>
      </template>
      <!--列表-->
      <ces-table      ref="table"
        :that="that"
        :isIndex="true"
        :hasexpand="true"
        @sortchange="sortchange"
        :tableData="pddcontributeinfolist"
        @select="selectchange"
        :isSelection="false"
        :tableCols="tableCols"
        :loading="listLoading"
        :tableHandles='tableHandles'
        :summaryarry='summaryarry'
        :showsummary = 'false'
      >
        <el-table-column width="680px" label="点赞数表格"  fixed="right">
          <template slot-scope="scope">
            <table borderColor="#000" style="height: 10px; width:680px; border-color: white; margin-left: -10px;" :cellspacing="0">
              <tr>
                  <td style=" width: 300px; text-align: left;" v-for="(item,i) in scope.row.childListt" :key="i">{{item.likeNumberDate}}</td>
              </tr>
              <tr>
                <td style=" width: 300px; text-align: left;" v-for="(item,i) in scope.row.childListt" :key="i">{{item.likeNumber?item.likeNumber:0}}</td>
            </tr>
          </table>
          </template>
        </el-table-column>

        <template slot="extentbtn">
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.ProName" maxlength="50" clearable placeholder="商品名称" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.AccountNumberDY" maxlength="10" clearable placeholder="抖音号" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.NumberNameDY" maxlength="10" clearable placeholder="抖音号名称" style="width:150px;" />
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.CreatedUserName" maxlength="5" clearable placeholder="抖音号创建人" style="width:150px;" />
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onSearch">刷新</el-button>
          </el-button-group>
        </template>



      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getProductNewAccountNumberDY"
        />
      </template>
  <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%">

  <span>
     <buschar v-if="dialogMapVisible.visible" ref="buschar" :analysisData="dialogMapVisible.data"></buschar>
 </span>

 <span slot="footer" class="dialog-footer">
   <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
 </span>
</el-dialog>
    </my-container>
  </template>
  <script>
  import { getProductNewAccountNumberDYList,queryProductNewLikeNumberDYChart,changeProductNewAccountNumberDYStatus  } from '@/api/bookkeeper/reportday'
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import buschar from '@/components/Bus/buschar'
  const tableCols = [

  { istrue: true,  prop: 'videoShelfTime', label: '视频上架时间', sortable: 'custom', width: '150', },
  { istrue: true,  prop: 'proName', label: '商品名称', sortable: 'custom', width: '100', },
  { istrue: true,  prop: 'proImage', label: '产品图片', sortable: 'custom', width: '100', type: "image" },
  { istrue: true, prop: 'proImage', videoprop: 'videoAddress', label: '视频', width: '80', type: 'imgvideo' },
  { istrue: true,  prop: 'accountNumberDY', label: '抖音号',  width: '100' },
  { istrue: true,  prop: 'numberNameDY', label: '抖音号名称',  width: '140' },
  { istrue: true,  prop: 'accountNumberDYCreateName', label: '抖音号创建人',  width: '100' },
  {istrue:true,prop:'isUpdate',label:'是否监控', width:'80',sortable:'custom',type:'switch',
        change:(row,that)=>that.changeStatus(row)},
  {istrue:true,display:true,label:'点赞数趋势图', style:"color:red;cursor:pointer;",width:100,formatter:(row)=>'点赞数趋势图', type:'click'
  ,handle:(that,row)=>that.showchart(row)
},

  ];
  const tableHandles1 = [];
  export default {
    name: "Users",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
      buschar,

    },
    data() {
      return {
        dialogMapVisible:{visible:false,title:"",data:[]},
        onExportDialogVisible: false,
        searchloading:false,
        tableHandles: tableHandles1,
        that: this,
        Filter: {
          AccountNumberDY: null,
          CreatedUserName: null,
          ProName: null,
          NumberNameDY: null


      },
      filter1:{
            NumberNameDY:null,
            AccountNumberDY:null,
            ProName:null

          },
        arrlist: [],
        options: [],
        pddcontributeinfolist: [],
        tableCols: tableCols,
        total: 0,
        summaryarry: {  },
        pager: { OrderBy: "", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        originData: []
        // fileList: [],
      };
    },
    async mounted() {
      await this.getProductNewAccountNumberDY();
    },
    async created() {

  },
    methods: {


      async changeStatus(row){
      this.$confirm('将改变抖音号的监控状态，是否继续?', '提示', {confirmButtonText: '是',cancelButtonText: '否',type: 'warning'
        }).then(async () => {
            var params={
              accountNumberDY:row.accountNumberDY,
              numberNameDY:row.numberNameDY,
              isUpdate:row.isUpdate,
              proName:row.proName
            };
            const res= await changeProductNewAccountNumberDYStatus(params);
            if (!res?.success) return ;
            this.$message({message:"重置成功",type:"success"});
        }).catch(() => {
           row.isUpdate=!row.isUpdate;
        });
    },
      async showchart(row){
         this.filter1.NumberNameDY=row.numberNameDY;
         this.filter1.AccountNumberDY=row.accountNumberDY;
         this.filter1.ProName=row.proName;
         var params = {
          ... this.filter1
        };
         let that = this;

         const res = await queryProductNewLikeNumberDYChart(params).then(res=>{
             that.dialogMapVisible.visible=true;
             that.dialogMapVisible.data=res.data
             that.dialogMapVisible.title=res.data.legend[0]
         })


      },

      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },

      onSearch() {
        this.$refs.pager.setPage(1);
        this.getProductNewAccountNumberDY();
      },
      async getProductNewAccountNumberDY() {

        const para = { ...this.Filter };
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getProductNewAccountNumberDYList(params);
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;

        let _this = this;
        var newdata = res.data.list;
        let temp = []
        for (const item of newdata) {

          let a = [];
          for(var i of item.childList){

            if(i.likeNumberDate=='1小时内'){
              i.id = 1;
            }else if(i.likeNumberDate=='3小时内'){
              i.id = 2;
            }else if(i.likeNumberDate=='5小时内'){
              i.id = 5;

            }else if(i.likeNumberDate=='8小时内'){
              i.id = 8;

            }else if(i.likeNumberDate=='1天内'){
              i.id = 10;

            }else if(i.likeNumberDate=='2天内'){
              i.id = 20;

            }else if(i.likeNumberDate=='3天内'){
              i.id = 30;

            }else if(i.likeNumberDate=='5天内'){
              i.id = 50;

            }
            else if(i.likeNumberDate=='7天内'){
              i.id = 70;

            }else if(i.likeNumberDate=='15天内'){
              i.id = 150;

            }else if(i.likeNumberDate=='30天内'){
              i.id = 300;

            }else if(i.likeNumberDate=='超过30天'){
              i.id = 310;

            }

            a.push(i.likeNumberDate)
          }

          item.childListt = [{likeNumberDate:'1小时内',id:1},{likeNumberDate:'3小时内',id:3},{likeNumberDate:'5小时内',id:5},{likeNumberDate:'8小时内',id:8},{likeNumberDate:'1天内',id:10},{likeNumberDate:'2天内',id:20},{likeNumberDate:'3天内',id:30},
          {likeNumberDate:'5天内',id:50},{likeNumberDate:'7天内',id:70},{likeNumberDate:'15天内',id:150},{likeNumberDate:'30天内',id:300},{likeNumberDate:'超过30天',id:310},];

          for(let i of item.childList){
            item.childListt.unshift({...i})
          }



          item.childListt = _this.duplicateRemoval(item.childListt,'likeNumberDate')

          item.childListt.sort(function (a,b) {
            return a.id-b.id
          })
        }
        this.summaryarry=res.data.summary;


      },
      duplicateRemoval(data, field) {
          var fields = [];
          var arr = [];
          for (var i = 0; i < data.length; i++) {
              if (fields.indexOf(data[i][field]) == -1) {
                  fields.push(data[i][field]);
                  arr.push(data[i]);
              }
          }
          return arr;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
