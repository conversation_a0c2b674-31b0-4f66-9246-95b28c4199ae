<template>
  <my-container v-loading="pageLoading" class="box">
    <!--顶部操作-->
    <div class=".top">
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent
      >
        <el-form-item label="下单时间:">
          <el-date-picker
            style="width: 320px"
            v-model="Filter.timerange"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            :default-value="defaultDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="订单号:">
          <el-input v-model.trim="Filter.orderNo" />
        </el-form-item>
        <el-form-item label="宝贝ID:">
          <el-input v-model.trim="Filter.proId" style="width: 110px" />
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select
            v-model="Filter.shopName"
            placeholder="请选择"
            class="el-select-content"
            clearable
          >
            <el-option
              v-for="item in shopList"
              :key="item.id"
              :label="item.shopName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="聊天账号:">
          <el-input v-model.trim="Filter.chatAccount" />
        </el-form-item>
        <el-form-item label="是否有聊天记录:">
          <el-select
            v-model="Filter.isChat"
            placeholder="请选择"
            class="el-select-content"
            clearable
          >
            <el-option
              v-for="item in [
                { name: '是', isChart: true },
                { name: '否', isChart: false },
              ]"
              :key="item.isChart"
              :label="item.name"
              :value="item.isChart"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="uptime" v-show="upDataTime">数据更新时间: {{ upDataTime }}</div>
    <!--列表-->
    <ces-table
      ref="table"
      :that="that"
      :isIndex="true"
      @sortchange="sortchange"
      :tableData="OrderList"
      :showsummary="true"
      :summaryarry="summaryarry"
      :tableCols="tableCols"
      :tableHandles="tableHandles"
      :loading="listLoading"
      style="width: 100%; height: 90%; margin: 0"
    >
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>
    <el-dialog
      title="聊天记录"
      :visible.sync="resourcedialogVisible"
      width="50%"
      v-dialogDrag
    >
      <div class="system-box">
        <div class="system">
          <div>系统消息</div>
          <div style="margin-left: 20px">2024-05 10:05:50:25</div>
        </div>
        <div style="color: #333">[常见问题列表]</div>
      </div>
      <div style="height: 500px; overflow: auto">
        <div
          class="message-container"
          v-for="(message, index) in chartList"
          :key="index"
          :class="getMessageClass(message.userType)"
        >
          <div v-if="message.userType != 0">
            <div class="" style="text-align: right; margin: 5px 0">
              {{ message.userName }}
            </div>
            <div
              class="avatar"
              style="display: flex; align-items: center; text-align: right"
            >
              <div
                style="margin-right: 10px; text-align: right; color: #333"
                class="message"
                v-html="message.content"
              ></div>
              <img
                src="@/static/images/vedio.jpg"
                alt="Avatar"
                class="avatar-image"
              />
            </div>
          </div>
          <div v-if="message.userType == 0" class="message-container">
            <div class="avatar">
              <img
                src="@/static/images/vedio.jpg"
                alt="Avatar"
                class="avatar-image"
              />
            </div>
            <div class="bubble">
              <div style="display: flex; color: #999">
                <div>
                  {{ message.userName }}
                </div>
                <div style="margin-left: 10px">
                  {{ message.recordTime }}
                </div>
              </div>
              <div
                class="message"
                style="text-align: left"
                v-html="message.content"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <my-pagination
        slot="footer"
        ref="chartPager"
        :total="chartTotal"
        @get-page="getChartList"
      />
    </el-dialog>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import resourcedatatbl from "@/views/customerservice/trainresource.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import { formatLinkProCode } from "@/utils/tools";

import {
  getUnpayOrderList,
  getChartList,
} from "@/api/customerservice/unpaidorder";

const tableCols = [
  {
    istrue: true,
    prop: "orderNo",
    label: "订单号",
    sortable: "custom",
  },
  {
    istrue: true,
    display: true,
    prop: "proId",
    label: "宝贝ID",
    sortable: "custom",
    type: "html",
    width: "150",
    formatter: (row) => formatLinkProCode(row.platform, row.proId),
  },
  {
    istrue: true,
    prop: "goodsCount",
    label: "商品数量",
    width: "150",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "amount",
    label: "商品总价",
    width: "150",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "orderTime",
    label: "下单时间",
    width: "150",
    sortable: "custom",
    formatter: (row) => {
      return formatTime(row.orderTime, "YYYY-MM-DD");
    },
  },
  {
    istrue: true,
    prop: "shopId",
    label: "店铺ID",
    width: "150",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "shopName",
    label: "店铺名称",
    width: "150",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "isChat",
    label: "是否有聊天记录",
    width: "150",
    sortable: "custom",
    formatter: (row) => {
      if (row.isChat) {
        return "查看聊天记录";
      } else {
        return " ";
      }
    },
    type: "click",
    handle: (that, row) => that.showDetail(row),
    style: "color: rgb(72, 132, 243);cursor:pointer;",
  },
  {
    istrue: true,
    prop: "chatAccount",
    label: "聊天账号",
    sortable: "custom",
  },
];
export default {
  name: "Unpaidorder",
  components: {
    MyContainer,
    resourcedatatbl,
    cesTable,
  },
  data() {
    return {
      activeName: "first1",
      that: this,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        orderNo: "",
        proId: "",
        shopName: null,
        chatAccount: "",
        isChat: null,
        platform: 1,
      },
      OrderList: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      userNameReadonly: true,
      tableHandles: [],
      shopList: [],
      resourcedialogVisible: false,
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date("1970/1/1"),
      upDataTime: "",
      chartTotal: 0,
      chartList: [],
    };
  },
  async mounted() {
    await this.onSearch();
    await this.changePlatform();
  },
  methods: {
    closeresourceview() {
      this.resourcedialogVisible = false;
    },
    showDetail(row) {
      this.resourcedialogVisible = true;
      this.$nextTick(() => {
        this.$refs.chartPager.setPage(1);
        this.getChartList();
      });
    },
    async getChartList() {
      var pager = this.$refs.chartPager.getPager();
      let data = {
        platform: 1,
        ...pager,
      };
      const res = await getChartList(data);
      this.chartTotal = res.data.total;
      this.chartList = res.data.list;
    },
    //消息框样式动态选择
    getMessageClass(isSent) {
      return isSent != 0 ? "message-container-right" : "message-container-left";
    },
    // 查询
    onSearch() {
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.gettrainplanList();
      });
    },
    getCondition() {
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.orderTimeStart = this.Filter.timerange[0];
        para.orderTimeEnd = this.Filter.timerange[1];
      }
      if (!(para.orderTimeStart && para.orderTimeEnd)) {
        this.$message({ message: "请先选择日期！", type: "warning" });
        return false;
      }
      if (this.Filter.shopName) para.shopName = this.Filter.shopName;
      else para.shopName = null;
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      console.log("params", params);
      return params;
    },
    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getUnpayOrderList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.upDataTime = res.data.extData.dataUpdateTime;
      data.forEach((d) => {
        d._loading = false;
      });
      this.OrderList = data;
      this.summaryarry = res.data.summary;
    },
    async changePlatform(val) {
      const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 1000,
      });
      this.shopList = res1.data.list;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .inner-container::-webkit-scrollbar {
  display: none;
}

.system-box {
  padding: 10px 20px 10px 10px;
  box-sizing: border-box;
  background-color: #fafafa;
}

.system {
  display: flex;
  margin-bottom: 4px;
}

.message-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.avatar {
  margin-left: 10px;
  /* 修改这里将头像放在消息框的右边 */
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.bubble {
  color: #000;
  padding: 10px;
  border-radius: 5px;
}

.message {
  text-align: left;
  margin: 0;
  width: 280px;
}

.message-container-right {
  justify-content: flex-end;
}

.message-container-left {
  justify-content: flex-start;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}
</style>
