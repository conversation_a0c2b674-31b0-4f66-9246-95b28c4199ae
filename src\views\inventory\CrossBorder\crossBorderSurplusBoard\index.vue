<template>
    <MyContainer>
        <el-tabs v-model="levelOne" style="height: 95%;">
            <el-tab-pane label="跨境结余看板" name="levelOne1" :lazy="true" style="height: 100%;">
                <el-tabs v-model="levelTwo1" style="height: 95%;">
                    <el-tab-pane label="产品费用" name="levelTwo1A" style="height: 98%;">
                        <ProductFare style="height: 100%;" />
                    </el-tab-pane>
                    <el-tab-pane label="采购费用" name="levelTwo1B" :lazy="true" style="height: 98%;">
                        <PurchaseFare style="height: 100%;" />
                    </el-tab-pane>
                    <el-tab-pane label="头程-运费" name="levelTwo1C" :lazy="true" style="height: 98%;">
                        <HeadFareShippingCost style="height: 100%;" />
                    </el-tab-pane>
                    <el-tab-pane label="头程-卸货上架费" name="levelTwo2C" :lazy="true" style="height: 98%;">
                        <HeadFareUnloadingShelvingFee style="height: 100%;" />
                    </el-tab-pane>
                    <el-tab-pane label="头程-装车费" name="levelTwo3C" :lazy="true" style="height: 98%;">
                        <HeadFareLoadingFee style="height: 100%;" />
                    </el-tab-pane>
                    <el-tab-pane label="头程-包装成本" name="levelTwo4C" :lazy="true" style="height: 98%;">
                        <HeadFarePackagingCost style="height: 100%;" />
                    </el-tab-pane>
                </el-tabs>
            </el-tab-pane>
            <el-tab-pane label="跨境消耗看板" name="levelOne2" :lazy="true" style="height: 100%;">
                <el-tabs v-model="levelTwo2" style="height: 95%;">
                    <el-tab-pane label="产品费用" name="levelOne2A" style="height: 98%;">
                        <ConsumeProductFare style="height: 100%;" />
                    </el-tab-pane>
                    <el-tab-pane label="采购费用" name="levelOne2B" :lazy="true" style="height: 98%;">
                        <ConsumePurchaseFare style="height: 100%;" />
                    </el-tab-pane>
                </el-tabs>
            </el-tab-pane>

    
         </el-tabs>
       
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import ProductFare from './tab/ProductFare.vue';
import PurchaseFare from './tab/PurchaseFare.vue';
import HeadFare from './tab/HeadFare.vue';
import ConsumeProductFare from './tab/ConsumeProductFare.vue';
import ConsumePurchaseFare from './tab/ConsumePurchaseFare.vue';

import HeadFareShippingCost from './tab/HeadFareShippingCost.vue';
import HeadFareUnloadingShelvingFee from './tab/HeadFareUnloadingShelvingFee.vue';
import HeadFareLoadingFee from './tab/HeadFareLoadingFee.vue';
import HeadFarePackagingCost from './tab/HeadFarePackagingCost.vue';


export default {
    components: {
        MyContainer, ProductFare,PurchaseFare,HeadFare,ConsumeProductFare,ConsumePurchaseFare,HeadFareShippingCost,HeadFareUnloadingShelvingFee,HeadFareLoadingFee,HeadFarePackagingCost
    },
    data() {
        return {
            levelOne:'levelOne1',
            levelTwo1:'levelTwo1A',
            levelTwo2:'levelOne2A',
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>