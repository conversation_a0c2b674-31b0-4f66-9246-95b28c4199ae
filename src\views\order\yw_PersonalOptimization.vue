<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    :clearable="false" style="width: 200px;" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" style="width: 180px;" clearable
                    filterable multiple collapse-tags>
                    <el-option :label="item.label" :value="item.value" v-for="item in statusList" :key="item.value" />
                </el-select>
                <el-select v-model="ListInfo.goodsTypes" placeholder="类型" class="publicCss" clearable>
                    <el-option label="老品优化" value="老品优化" />
                    <el-option label="新建编码" value="新建编码" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 1)" title="款式编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 2)" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.yWUserNames" v-model="ListInfo.yWUserNames"
                    placeholder="运维/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="proCodeCallback($event, 3)" title="运维"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <dateRange :startDate.sync="ListInfo.goodsCreateStartTime" :endDate.sync="ListInfo.goodsCreateEndTime"
                    class="publicCss" start-placeholder="编码创建开始" end-placeholder="结束时间" style="width: 200px;" />
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>订单数</div>
                    <el-select v-model="ListInfo.orderCountType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.orderCountMax = undefined
                            ListInfo.orderCountMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '订单数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.orderCountMin" placeholder="订单数"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="0"
                        :controls="false" v-if="ListInfo.orderCountType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.orderCountMin" :maxNumber="999999999" :precision="0"
                        :max.sync="ListInfo.orderCountMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>销售数量</div>
                    <el-select v-model="ListInfo.saleCountType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.saleCountMax = undefined
                            ListInfo.saleCountMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '销售数量'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.saleCountMin" placeholder="销售数量"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="0"
                        :controls="false" v-if="ListInfo.saleCountType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.saleCountMin" :maxNumber="999999999" :precision="0"
                        :max.sync="ListInfo.saleCountMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>销售金额</div>
                    <el-select v-model="ListInfo.saleAmountType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.saleAmountMax = undefined
                            ListInfo.saleAmountMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '销售金额'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.saleAmountMin" placeholder="销售金额"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.saleAmountType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.averageInTransitMin" :maxNumber="999999999" :precision="2"
                        :max.sync="ListInfo.saleAmountMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>销售成本</div>
                    <el-select v-model="ListInfo.saleCostType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {

                            ListInfo.saleCostMax = undefined
                            ListInfo.saleCostMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '销售成本'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.saleCostMin" placeholder="销售成本"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.saleCostType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.saleCostMin" :maxNumber="999999999" :precision="2"
                        :max.sync="ListInfo.saleCostMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <el-select v-if="checkPermission('viewAllProps') || isYw" v-model="ListInfo.optimizeWays"
                    placeholder="请选择优化方向" class="publicCss" filterable multiple collapse-tags>
                    <el-option v-for="item in optimizeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>新成本价</div>
                    <el-select v-model="ListInfo.newCostType" placeholder="符号" style="width: 60px;" class="publicCss"
                        clearable @change="e => {
                            ListInfo.newCostMax = undefined
                            ListInfo.newCostMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '新成本价'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.newCostMin" placeholder="新成本价"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="4"
                        :controls="false" v-if="ListInfo.newCostType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.newCostMin" :maxNumber="999999999" :precision="4"
                        :max.sync="ListInfo.newCostMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <el-select v-model="ListInfo.isAdjustJstJiangjia" placeholder="是否更新聚水潭成本价" class="publicCss" clearable
                    v-if="checkPermission('viewAllProps') || isYw">
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
                <el-select v-model="ListInfo.isSendToOperateUser" placeholder="是否内推运营" class="publicCss" clearable
                    v-if="checkPermission('viewAllProps') || isYw">
                    <el-option label="是" :value="1" />
                    <el-option label="否" :value="0" />
                </el-select>
                <el-input v-model.trim="ListInfo.remark" placeholder="备注" maxlength="1000" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.optimizeResult" placeholder="优化结果" class="publicCss" clearable filterable
                    multiple collapse-tags v-if="checkPermission('viewAllProps') || isYw">
                    <el-option v-for="item in optimizeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <dateRange :startDate.sync="ListInfo.approvedStartTime" :endDate.sync="ListInfo.approvedEndTime"
                    class="publicCss" start-placeholder="审批开始时间" end-placeholder="结束时间" style="width: 200px;"
                    v-if="checkPermission('viewAllProps') || isYw" />
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>降价比例</div>
                    <el-select v-model="ListInfo.jiangjiaRateType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            ListInfo.jiangjiaRateMax = undefined
                            ListInfo.jiangjiaRateMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '降价比例'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.jiangjiaRateMin" placeholder="降价比例"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="2"
                        :controls="false" v-if="ListInfo.jiangjiaRateType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.jiangjiaRateMin" :maxNumber="999999999"
                        :max.sync="ListInfo.jiangjiaRateMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div class="serchDay" v-if="checkPermission('viewAllProps') || isYw">
                    <div>累计优化次数</div>
                    <el-select v-model="ListInfo.optimizeCountType" placeholder="符号" style="width: 60px;"
                        class="publicCss" clearable @change="e => {
                            ListInfo.optimizeCountMax = undefined
                            ListInfo.optimizeCountMin = undefined
                        }">
                        <el-option :label="item.label" :value="item.value" v-for="item in operatorList"
                            :key="item.value + '累计优化次数'" />
                    </el-select>
                    <el-input-number style="width: 60px;" v-model.trim="ListInfo.optimizeCountMin" placeholder="累计优化次数"
                        maxlength="50" clearable :max="999999999" :min="0" class="publicCss" :precision="0"
                        :controls="false" v-if="ListInfo.optimizeCountType != '介于'" />
                    <number-range v-else :min.sync="ListInfo.optimizeCountMin" :maxNumber="999999999"
                        :max.sync="ListInfo.optimizeCountMax" min-label=" 最小值" max-label="最大值" class="publicCss" />
                </div>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="handleApproval(null, 'agree', true)"
                        v-if="checkPermission('AgreeToApprove')">批量同意</el-button>
                    <el-button type="primary" @click="handleApproval(null, 'refuse', true)"
                        v-if="checkPermission('ApprovalIsDenied')">批量拒绝</el-button>
                    <el-button type="primary" @click="handleEdit(null, true)" v-if="isYw">批量编辑</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @select="select"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border
            rowHeight="60" :showheaderoverflow="false" id="20250702092848" :isSelectColumn="false" showsummary
            :summaryarry="summary" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right" v-if="isYw">
                <vxe-column title="操作" width="300" align="center" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;flex-wrap:wrap">
                            <el-button type="text" @click="viewProgress(row)">查看优化进度</el-button>
                            <el-button type="text" @click="viewVariation(row)">查看优化后变化</el-button>
                            <el-button type="text" @click="viewProductStandards(row)">查看产品标准</el-button>
                            <el-button type="text" @click="viewCompetitiveProfits(row)">查看竞品利润</el-button>
                            <el-button type="text" @click="viewShelvesData(row)">查看上架数据</el-button>
                            <el-button type="text" @click="handleEdit(row, false)"
                                :disabled="row.status == '待审核'">编辑</el-button>
                            <el-button type="text" @click="handleApproval(row, 'agree', false)"
                                :disabled="row.status != '待审核'" v-if="checkPermission('AgreeToApprove')">同意</el-button>
                            <el-button type="text" @click="handleApproval(row, 'refuse', false)"
                                :disabled="row.status != '待审核'"
                                v-if="checkPermission('ApprovalIsDenied')">拒绝</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog v-dialogDrag :title="isBatch ? '批量编辑' : '编辑'" :visible.sync="editVisable" width="75%">
            <yw_PersonalOptimization_edit v-if="editVisable" @close="() => {
                editVisable = false
                selectList = []
            }" :editRow="editRow" @getList="getList" :selectList="selectList" :isBatch="isBatch" />
        </el-dialog>

        <el-dialog v-dialogDrag :title="`${this.editRow?.goodsCode}优化进度`" :visible.sync="optimizeProgressVisable"
            width="75%">
            <optimizeProgress v-if="optimizeProgressVisable" @close="optimizeProgressVisable = false"
                :editRow="editRow" />
        </el-dialog>

        <el-dialog v-dialogDrag :title="`${this.editRow?.goodsCode}优化后变化`" :visible.sync="optimizeChangesVisible"
            width="75%">
            <optimizeChanges v-if="optimizeChangesVisible" @close="optimizeChangesVisible = false" :editRow="editRow" />
        </el-dialog>

        <el-dialog v-dialogDrag :title="`${this.editRow?.goodsCode}产品标准`" :visible.sync="productStandardsVisible"
            width="75%">
            <productStandards v-if="productStandardsVisible" @close="productStandardsVisible = false"
                :editRow="editRow" />
        </el-dialog>

        <el-dialog v-dialogDrag :title="`${this.editRow?.goodsCode}上架`" :visible.sync="listingDataVisible" width="75%">
            <listingData v-if="listingDataVisible" @close="listingDataVisible = false" :editRow="editRow" />
        </el-dialog>

        <el-dialog v-dialogDrag :title="`${this.editRow?.goodsCode}竞品利润`" :visible.sync="competitiveProfitsVisible"
            width="75%">
            <competitiveProfits v-if="competitiveProfitsVisible" @close="competitiveProfitsVisible = false"
                :editRow="editRow" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import yw_PersonalOptimization_edit from './yw_PersonalOptimization_edit.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
import numberRange from "@/components/number-range/index.vue";
import optimizeProgress from './optimizeProgress.vue'
import optimizeChanges from './optimizeChanges.vue'
import productStandards from './productStandards.vue'
import listingData from './listingData.vue'
import competitiveProfits from './competitiveProfits.vue'
import { QueryYWOptimizeGoodsRecordListAsync, BatchAgreen, BatchReject } from '@/api/inventory/YWOptimizeGoods';
const operatorList = [
    { label: '大于', value: '大于' },
    { label: '小于', value: '小于' },
    { label: '等于', value: '等于' },
    { label: '介于', value: '介于' },
]
const statusList = [
    { label: '待优化', value: '待优化' },
    { label: '优化中', value: '优化中' },
    { label: '优化完结', value: '优化完结' },
    { label: '待审核', value: '待审核' },
    { label: '拒绝', value: '拒绝' },
]
const optimizeList = [
    { label: "降本", value: "降本" },
    { label: "提质", value: "提质" },
    { label: "优化库位", value: "优化库位" },
    { label: "更换包材", value: "更换包材" },
    { label: "优化售前", value: "优化售前" },
    { label: "优化售后", value: "优化售后" },
    { label: "更换包装方式", value: "更换包装方式" },
    { label: "新建编码", value: "新建编码" },
    { label: "优化链接", value: "优化链接" },
    { label: "推给运营上架", value: "推给运营上架" }
]
const tableCols = [
    { label: '', type: 'checkbox' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'goodsType', label: '类型', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'styleCode', label: '款式编码', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'ywUserName', label: '运维', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsImage', label: '商品图片', type: 'images' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'goodsCreateTime', label: '创建日期', formatter: (row) => row.goodsCreateTime ? dayjs(row.goodsCreateTime).format('YYYY-MM-DD HH:mm:ss') : row.goodsCreateTime },
    {
        sortable: 'custom', width: '80', align: 'center', prop: 'orderCount', label: '订单数',
        formatter: (row) => {
            if (row.orderCount == '-') {
                return '-'
            } else if (row.orderCount == '0.0000') {
                return 0
            } else {
                return row.orderCount !== null ? row.orderCount.split('.')[0] : row.orderCount
            }
        }
    },
    {
        sortable: 'custom', width: '90', align: 'center', prop: 'saleCount', label: '销售数量',
        formatter: (row) => {
            if (row.saleCount == '-') {
                return '-'
            } else if (row.saleCount == '0.0000') {
                return 0
            } else {
                return row.saleCount !== null ? row.saleCount.split('.')[0] : row.saleCount
            }
        }
    },
    {
        sortable: 'custom', width: '100', align: 'center', prop: 'saleAmount', label: '销售金额',
        formatter: (row) => {
            if (row.saleAmount == '-') {
                return '-'
            } else if (row.saleAmount == '0.0000') {
                return 0.00
            } else {
                return row.saleAmount !== null ? row.saleAmount : row.saleAmount
            }
        }
    },
    {
        sortable: 'custom', width: '80', align: 'center', prop: 'saleCost', label: '销售成本',
        formatter: (row) => {
            if (row.saleCost == '-') {
                return '-'
            } else if (row.saleCost == '0.0000') {
                return 0.00
            } else {
                return row.saleCost !== null ? row.saleCost : row.saleCost
            }
        }
    },
    {
        sortable: 'custom', width: '70', align: 'center', prop: 'cost', label: '成本价',
        formatter: (row) => {
            if (row.cost == '-') {
                return '-'
            } else if (row.cost == '0.0000') {
                return 0.00
            } else {
                return row.cost !== null ? row.cost : row.cost
            }
        }
    },
    { sortable: 'custom', width: '90', align: 'center', prop: 'optimizeWay', label: '优化方向', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'newCost', label: '新成本价', },
    { sortable: 'custom', width: '80', align: 'center', prop: 'isAdjustJstJiangjia', label: '是否更新聚水潭成本价', formatter: (row) => row.isAdjustJstJiangjia === '1' ? '是' : row.isAdjustJstJiangjia === '0' ? '否' : row.isAdjustJstJiangjia },
    { sortable: 'custom', width: '60', align: 'center', prop: 'isSendToOperateUser', label: '是否内推运营', formatter: (row) => row.isSendToOperateUser === '1' ? '是' : row.isSendToOperateUser === '0' ? '否' : row.isSendToOperateUser },
    { sortable: 'custom', width: '60', align: 'center', prop: 'operaterUserRemark', label: '运维备注(运营)', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'saleBeforeUserRemark', label: '运维备注(售前)', },
    { sortable: 'custom', width: '60', align: 'center', prop: 'saleAfterUserRemark', label: '运维备注(售后)', },
    { sortable: 'custom', width: '90', align: 'center', prop: 'optimizeResult', label: '优化结果', },
    { sortable: 'custom', width: '120', align: 'center', prop: 'approvedTime', label: '审批时间', formatter: (row) => row.approvedTime ? dayjs(row.approvedTime).format('YYYY-MM-DD HH:mm:ss') : row.approvedTime },
    {
        sortable: 'custom', width: '90', align: 'center', prop: 'jiangJiaRate', label: '降价比例',
        formatter: (row) => {
            if (row.jiangJiaRate == '-') {
                return '-'
            } else if (row.jiangJiaRate == '0.0000') {
                return 0.00 + '%'
            } else if (row.jiangJiaRate !== null && row.jiangJiaRate !== '') {
                return (row.jiangJiaRate !== null && row.jiangJiaRate !== '') ? row.jiangJiaRate + '%' : row.jiangJiaRate
            }
        }
    },
    {
        sortable: 'custom', width: '50', align: 'center', prop: 'optimizeCount', label: '累计优化次数',
        formatter: (row) => {
            if (row.optimizeCount == '-') {
                return '-'
            } else if (row.optimizeCount == '0.0000') {
                return 0
            } else {
                return row.optimizeCount !== null ? row.optimizeCount.split('.')[0] : row.optimizeCount
            }
        }
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer,
        vxetablebase,
        dateRange,
        yw_PersonalOptimization_edit,
        inputYunhan,
        numberRange,
        optimizeProgress,
        optimizeChanges,
        productStandards,
        listingData,
        competitiveProfits
    },
    data() {
        return {
            optimizeProgressVisable: false,
            optimizeChangesVisible: false,
            productStandardsVisible: false,
            listingDataVisible: false,
            competitiveProfitsVisible: false,
            optimizeList,
            that: this,
            operatorList,
            statusList,
            isYw: false,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                endTime: dayjs().format('YYYY-MM-DD'),
                status: ['优化中']
            },
            summary: {},
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            editVisable: false,
            isBatch: false, // 是否批量编辑
            editRow: null,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        proCodeCallback(val, type) {
            if (type == 1) {
                this.ListInfo.styleCodes = val
            } else if (type == 2) {
                this.ListInfo.goodsCodes = val
            } else if (type == 3) {
                this.ListInfo.yWUserNames = val
            }
        },
        viewVariation(row) {
            this.editRow = row
            this.optimizeChangesVisible = true
        },
        viewProgress(row) {
            this.editRow = row
            this.optimizeProgressVisable = true
        },
        viewProductStandards(row) {
            this.editRow = row
            this.productStandardsVisible = true
        },
        viewCompetitiveProfits(row) {
            this.editRow = row
            this.competitiveProfitsVisible = true
        },
        viewShelvesData(row) {
            this.editRow = row
            this.listingDataVisible = true
        },
        handleEdit(row, type) {
            if (type && this.selectList?.length == 0) return this.$message.error('请至少选择一条数据');
            this.editRow = row ?? null
            this.isBatch = type
            this.editVisable = true
        },
        select(val) {
            this.selectList = val
        },
        handleApproval(row, type, isBatch) {
            if (isBatch && this.selectList?.length == 0) return this.$message.error('请至少选择一条数据');
            const flag = this.selectList.every(item => item.status == '待审核')
            if (!flag) return this.$message.error('只能操作待审核的数据')
            this.$confirm(`此操作将${type === 'agree' ? '同意' : '拒绝'}该审批, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let goodsCodes = this.selectList.map(item => item.goodsCode) || []//批量同意
                if (type == 'agree') {
                    if (row) {
                        goodsCodes = [row.goodsCode]//单条同意
                    }
                    const { success } = await BatchAgreen({ goodsCodes });
                    if (!success) return
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                } else {
                    if (row) {
                        goodsCodes = [row.goodsCode]//单条拒绝
                    }
                    const { success } = await BatchReject({ goodsCodes });
                    if (!success) return
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
                await this.getList()
                if (!row) {
                    this.selectList = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await QueryYWOptimizeGoodsRecordListAsync(this.ListInfo)
                if (success) {
                    this.isYw = data.extData.isYW
                    this.tableData = data.list
                    this.total = data.total
                    this.summary = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                console.log('获取列表失败', error);

                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 150px;
        margin: 0 5px 5px 0px;
    }

    .serchDay {
        display: flex;
        align-items: center;
        font-size: 14px;
        gap: 5px;
    }
}
</style>
