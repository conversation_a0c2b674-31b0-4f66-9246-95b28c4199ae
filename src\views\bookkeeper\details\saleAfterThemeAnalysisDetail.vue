<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="年月:">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width:100px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:" label-position="right">
                    <el-select v-if="shopCheck" filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 200px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
                    </el-select>
                    <el-input v-else v-model.trim="filter.shopCode" placeholder="所属店铺" maxlength="50" clearable style="width: 200px" />
                </el-form-item>
                <el-form-item label="订单编号:" label-position="right">
                    <el-input v-model.trim="filter.serialNumberOrder" clearable placeholder="商品订单编号" style="width:133px;" />
                </el-form-item>
                <el-form-item label="宝贝Id:" label-position="right">
                    <el-input v-model.trim="filter.proCode" clearable placeholder="宝贝Id" style="width:133px;" />
                </el-form-item>
                <el-form-item label="空白ID:" label-position="right">
                    <el-select filterable clearable v-model="filter.isNullProCode" placeholder="请选择" style="width:100px;">
                        <el-option label="空白ID" :value="true"></el-option>
                        <el-option label="非空白ID" :value="false"></el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="类型" label-position="right" >
           <el-select filterable clearable v-model="filter.OrderType" placeholder="请选择"  style="width:80px;">
            <el-option label="非补发/换货单" :value="1"></el-option>
            <el-option label="补发" :value="2"></el-option>
            <el-option label="换货" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" label-position="right" >
           <el-select filterable clearable v-model="filter.Status" placeholder="请选择"  style="width:80px;">
            <el-option label="非取消" :value="1"></el-option>
            <el-option label="已取消" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否特殊" label-position="right" >
           <el-select filterable clearable v-model="filter.IsSpecial" placeholder="请选择"  style="width:80px;">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item> -->
                <el-form-item label="分组" label-position="right">
                    <el-select filterable clearable v-model="filter.QueryType" placeholder="请选择" style="width:100px;">
                        <el-option label="订单分组" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='true' :tableHandles='tableHandles'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
    import cesTable from "@/components/Table/table.vue";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
    import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
    import { getSaleAfterThemeAnalysisDetail as getPageList, exportGatherSaleAfterThemeAnalysisList, getFenXiaoSaleAfterThemeAnalysisDetail } from '@/api/monthbookkeeper/financialDetail'
    const tableCols = [
        { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'numberOnlineOrderOrigin', label: '线上订单号', sortable: 'custom', width: '245', type: 'html' },
        { istrue: true, prop: 'proCode', label: '宝贝ID', sortable: 'custom', width: '245' },//原： type: 'html'，20250530去掉了type: 'html'
        { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '245' },//原： type: 'html'，20250530去掉了type: 'html'
        { istrue: true, prop: 'shopCode', label: '店铺编码', width: '245', sortable: 'custom' },
        //   {istrue:true,prop:'typeOrder',label:'订单类型', width:'80',sortable:'custom',type:'html'},
        //   {istrue:true,prop:'statusOrder',label:'订单状态',sortable:'custom', width:'80',type:'html'},
        //{istrue:true,prop:'nameWarehouse',label:'发货仓',sortable:'custom', width:'80',type:'html'},
        //   {istrue:true,prop:'timeSend',label:'发货日期',sortable:'custom', width:'80',type:'html'},
        //   {istrue:true,prop:'timePay',label:'支付时间',sortable:'custom', width:'120',type:'html'},
        // {istrue:true,prop:'nameExpressCompany',label:'快递公司',sortable:'custom', width:'80',type:'html'},
        // {istrue:true,prop:'numberExpress',label:'快递单号',sortable:'custom', width:'80',type:'html'},
        //   {istrue:true,prop:'nameBrand',label:'品牌',sortable:'custom', width:'80',type:'html'},
        //   {istrue:true,prop:'countSale',label:'销售数量',sortable:'custom', width:'80',type:'html'},
        //   {istrue:true,prop:'amountSaleJe',label:'销售金额',sortable:'custom', width:'80',type:'html',formatter:(row)=>{return row.amountSaleJe?.toFixed(0)}},
        //   {istrue:true,prop:'amountCoustJe',label:'销售成本',sortable:'custom', width:'80',type:'html',formatter:(row)=>{return row.amountCoustJe?.toFixed(0)}},
        //   {istrue:true,prop:'amountSaleSf',label:'实发金额',sortable:'custom', width:'80',type:'html',formatter:(row)=>{return row.amountSaleSf?.toFixed(0)}},
        //   {istrue:true,prop:'amountCoustSf',label:'实发成本',sortable:'custom', width:'80',type:'html',formatter:(row)=>{return row.amountCoustSf?.toFixed(0)}},
        { istrue: true, prop: 'amountRefundCostSJ', label: '实退成本金额', sortable: 'custom', width: '150', type: 'html', formatter: (row) => { return row.amountRefundDQ?.toFixed(0) } },
        //{istrue:true,prop:'weightOrder',label:'订单重量',sortable:'custom', width:'80',type:'html'},
        //{istrue:true,prop:'nameProductBianmaGroup',label:'组合编码',sortable:'custom', width:'80',type:'html'},
        //{istrue:true,prop:'typeVirtualProduct',label:'虚拟分类',sortable:'custom', width:'80',type:'html'},
    ];
    const tableHandles = [
        { label: "导出店铺汇总", handle: (that) => that.onShopGatherExport() },
    ]
    export default {
        name: "Users",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                tableHandles: tableHandles,
                filter: {
                    platform: null,
                    yearMonth: null,
                    shopCode: null,
                    isNullProCode: null
                },
                shopList: [],
                userList: [],
                groupList: [],
                platformlist: platformlist,
                ZTCKeyWordList: [],
                tableCols: tableCols,
                summaryarry: {},
                total: 0,
                pager: { OrderBy: "id", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                shopCheck: true,
                pageLoading: false,
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
            };
        },
        async mounted() {
        },
        methods: {
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.onSearch();
            },
            async onchangeplatform(val) {
                this.filter.shopCode=null;
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res1.data.list
                if(this.filter.platform == 11){
                  this.$nextTick(()=>{
                  this.shopCheck = false;
                    this.tableCols.unshift({ istrue: true, prop: 'numberInternalOrder', label: '分销商', width: '90', sortable: 'custom' });
                  })
                } else {
                  this.shopCheck = true;
                  this.tableCols = this.tableCols.filter(f => f.label != '分销商');
                }
            },
            // async getShopList(){
            //   const res1 = await getAllShopList();
            //   this.shopList=[];
            //     res1.data?.forEach(f => {
            //       if(f.isCalcSettlement&&f.shopCode)
            //           this.shopList.push(f);
            //     });
            // },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.getList();
            },
            async getList() {
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...this.filter,
                };
                this.listLoading = true;
                let res = {}
                if (this.filter.platform == 11) {
                    res = await getFenXiaoSaleAfterThemeAnalysisDetail(params);
                } else {
                    res = await getPageList(params);
                }
                this.listLoading = false;
                this.total = res.data?.total
                this.ZTCKeyWordList = res.data?.list;
                this.summaryarry = res.data?.summary;
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async onShopGatherExport() {
                if (!this.filter.yearMonth || !this.filter.platform) {
                    this.$message({ message: "请输入年月和平台", type: "warning" });
                    return
                }
                this.$confirm('确认要导出吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    var pager = this.$refs.pager.getPager();
                    const params = {
                        ...pager,
                        ...this.pager,
                        ...this.filter,
                    };
                    var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                    var res = await exportGatherSaleAfterThemeAnalysisList(params);
                    loadingInstance.close();
                    if (!res?.data) {
                        this.$message({ message: "没有数据", type: "warning" });
                        return
                    }
                    const aLink = document.createElement("a");
                    let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '销售主题分析店铺汇总_' + new Date().toLocaleString() + '.xlsx')
                    aLink.click();
                }).catch(() => {
                });
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
