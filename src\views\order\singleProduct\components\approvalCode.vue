<template>
  <MyContainer>
    <vxetablebase :id="'approvalCode202501151902'" ref="table" :loading="loading" :that="that" :is-index="true"
      :hasexpand="true" :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list"
      :table-cols="tableCols" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
      style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
const api = '/api/verifyOrder/GoodAllot/Inv/'
import { mergeTableCols } from '@/utils/getCols'
export default {
  name: "approvalCode",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange,
  },
  props: {
    approvalListInfo: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      api,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        instanceId: null,
      },
      data: {},
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
    }
  },
  async mounted() {
    console.log(this.approvalListInfo, 'this.approvalListInfo');
    this.ListInfo.instanceId = this.approvalListInfo.instanceId
    await this.getCol();
    await this.getList()
  },
  methods: {
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
          }
        })
        this.tableCols = mergeTableCols(data)
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss"></style>
