<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="商品推广" name="first1" style="height: 100%">
        <productPromotionSph />
      </el-tab-pane>
      <el-tab-pane label="直播推广" name="first2" style="height: 100%" lazy>
        <liveBroadcastPromotionSph />
      </el-tab-pane>
      <el-tab-pane label="推广流水" name="first3" style="height: 100%" lazy>
        <promotionFlowSph />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import productPromotionSph from "./components/productPromotionSph.vue";
import liveBroadcastPromotionSph from "./components/liveBroadcastPromotionSph.vue";
import promotionFlowSph from "./components/promotionFlowSph.vue";
export default {
  name: 'operatingExpensesIndexSph',
  components: {
    MyContainer, productPromotionSph, liveBroadcastPromotionSph, promotionFlowSph
  },
  data() {
    return {
      that: this,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
