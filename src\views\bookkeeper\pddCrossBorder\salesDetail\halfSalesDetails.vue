<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>

        <div>
          <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="ListInfo.orderNo"
            v-model.trim="ListInfo.orderNo" placeholder="订单号/若输入多条请按回车" :clearable="true" @callback="callbackOrderNo"
            title="订单号" @entersearch="entersearch">
          </inputYunhan>
        </div>

        <div>
          <inputYunhan :key="'2'" :keys="'two'" :width="'150px'" ref="" :inputt.sync="ListInfo.subOrderNo"
            v-model.trim="ListInfo.subOrderNo" placeholder="子订单/若输入多条请按回车" :clearable="true"
            @callback="callbackSubOrderNo" title="子订单" @entersearch="entersearch">
          </inputYunhan>
        </div>
        <div>
          <inputYunhan :key="'4'" :keys="'four'" :width="'150px'" ref="" :inputt.sync="ListInfo.trackingNumbers"
            v-model.trim="ListInfo.trackingNumbers" placeholder="运单号/若输入多条请按回车" :clearable="true"
            @callback="callbackTrackingNumbers" title="运单号" @entersearch="entersearch">
          </inputYunhan>
        </div>


        <div>
          <inputYunhan :key="'3'" :keys="'three'" :width="'150px'" ref="" :inputt.sync="ListInfo.goodsCode"
            v-model.trim="ListInfo.goodsCode" placeholder="商品编码/若输入多条请按回车" :clearable="true"
            @callback="callbackgoodsCode" title="商品编码" @entersearch="entersearch">
          </inputYunhan>
        </div>


        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.orderStatus" placeholder="订单状态" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.region" style="width: 150px;" clearable placeholder="区域">
          <el-option
            v-for="item in regionList"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>

        <el-button type="primary" @click="getList('search')">搜索</el-button>

        <el-dropdown style="box-sizing: border-box; margin-left:6px;" size="mini" split-button @click="startImport"
          type="primary" icon="el-icon-share" @command="handleCommand"> 导入
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="Batcoperation" style="padding: 0 25px" command="a">下载模版</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" @click="onExport" style="margin-left: 5px;" v-if="checkPermission('salesDetail_kj_export')">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'halfSalesDetails202408041405'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
      :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
      :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'">
      
      <template v-slot:postalCode="{ row }">
        <div>{{ row.postalCode }}</div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">
        <el-date-picker style="width: 150px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { pageHalfSalesDetails_TemuAsync, importHalfSalesDetails_TemuAsync } from '@/api/bookkeeper/reportdayV2'
import { codeSalesThemeAnalysis_BanTuo_Temu_Export } from '@/api/bookkeeper/crossBorderV2'

import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', prop: 'yearMonthDay', label: '年月日', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopCode', label: '店铺ID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName', label: '店铺名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderNo', label: '订单号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'subOrderNo', label: '子订单号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderStatus', label: '订单状态', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'sku', label: '商品SKU', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'quantity', label: '商品件数', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productSKUID', label: '货品SKUID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productQuantity', label: '货品件数', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productSKCID', label: '货品SKCID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productSPUID', label: '货品SPUID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'skuCode', label: '商品编码', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productName', label: '商品名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'costPrice', label: '成本价', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'declaredPrice', label: '申报价格', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'activitiesPrice', label: '活动价', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'salesAmount', label: '销售金额', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'salesCost', label: '销售成本', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'recipientName', label: '收货人姓名', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'recipientContact', label: '收货人联系方式', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'address1', label: '详细地址1', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'address2', label: '详细地址2', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'address3', label: '详细地址3', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'district', label: '区县', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'city', label: '城市', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'province', label: '省份', },
  { sortable: 'custom', width: '60', align: 'center', prop: 'region', label: '区域', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'postalCode', label: '收货地址邮编',},
  { sortable: 'custom', width: '110', align: 'center', prop: 'country', label: '国家', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'orderCreateTime', label: '订单创建时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'requiredShipTime', label: '要求最晚发货时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'trackingNumber', label: '运单号', },
]

const regionList =['美东','美中','美西']


export default {
  name: "halfSalesDetails",
  components: {
    MyContainer, vxetablebase, inputYunhan
  },
  data() {
    return {
      regionList:regionList,
      yearMonthDay: null,
      dialogVisible: false,//导入弹窗
      fileList: [],//上传文件列表
      uploadLoading: false,//上传按钮loading
      fileparm: {},//上传文件参数
      timeRanges: [],//时间范围
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        orderNo: null,//线上单号
        orderStatus:null, //订单状态
      },
      tableCols,
      summaryarry: {},
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", dayjs(this.yearMonthDay).format('YYYYMMDD'));
      var res = await importHalfSalesDetails_TemuAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = null
      this.dialogVisible = true;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges != null && this.timeRanges.length == 0) {
          //默认给近1天时间
          this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
          this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await pageHalfSalesDetails_TemuAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async handleCommand(command) {
      switch (command) {
        //下载模版
        case 'a':
          await this.downLoadFile()
          break;
      }
    },
    async downLoadFile() {
      window.open("/static/excel/KjSalesDetails/TEMU半托-销售明细.xlsx", "_blank");
    },
    async entersearch(val) {
      this.getList();
    },
    async callbackOrderNo(val) {
      this.ListInfo.orderNo = val;
    },
    async callbackSubOrderNo(val) {
      this.ListInfo.subOrderNo = val;
    },
    async callbackTrackingNumbers(val) {
      this.ListInfo.trackingNumbers = val;
    },
    async callbackgoodsCode(val) {
      this.ListInfo.goodsCode = val;
    },
    async onExport() {//导出列表数据；
      var res = await codeSalesThemeAnalysis_BanTuo_Temu_Export(this.ListInfo);
      if (res?.success) {
        this.$message({ message: res.msg, type: "success" });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
