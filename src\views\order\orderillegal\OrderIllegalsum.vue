<template>
  <my-container v-loading="pageLoading">
    <template>
      <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
        :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" @select="selectchange"
        :isSelectColumn='false' :tableHandles='tableHandles' :loading="listLoading">
      </ces-table>
    </template>
    <!-- <template slot='extentbtn'>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
               <el-checkbox v-model="filter1.isgroupbyreson">原因分组</el-checkbox>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
        </el-button-group>
       </template> -->


    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <!-- 订单量图表 -->
    <el-dialog :visible.sync="dialoganalysisVisible" width="80%" v-dialogDrag :show-close="false">
      <orderIllAnalysis ref="orderIllAnalysis" style="height: 550px"></orderIllAnalysis>
    </el-dialog>

    <el-dialog :visible.sync="dialosisboardVisible" width="85%" v-dialogDrag :show-close="false">
      <el-dialog :visible.sync="showDetailVisible" width="72%" :show-close="false" append-to-body>
        <div style="height:600px;">
          <orderIllgalsearch :filter="filter2" ref="orderIllgalsearch" style="height:100%;"></orderIllgalsearch>
        </div>
      </el-dialog>
      <div>
        <orderillegalboard ref="orderillegalboard"></orderillegalboard>
      </div>
    </el-dialog>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { formatLinkProCode, formatSendWarehouse } from "@/utils/tools";
import { getOrderWithholdList, exportOrderWithhold, importPinOrderIllegal, getWithProCodeSum } from "@/api/order/orderdeductmoney"
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import orderIllAnalysis from './OrderIllAnalysis.vue'
import orderillegalboard from './orderillegalboard.vue'
import orderIllgalsearch from './OrderIllgalsearch.vue'

const tableCols = [
  { istrue: true, prop: 'proCode', label: '宝贝ID',tipmesg:'查询生效条件：扣款时间、宝贝ID、扣款原因', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(2, row.proCode) },
  { istrue: true, prop: 'goodsName', label: '商品名称', formatter: (row) => !row.goodsName ? "" : row.goodsName },
  { istrue: true, prop: 'groupId', label: '小组', width: '60', formatter: (row) => !row.groupName ? " " : row.groupName },
  { istrue: true, prop: 'shopId', label: '店铺', formatter: (row) => !row.shopName ? " " : row.shopName },
  //{istrue:true,fixed:true,prop:'proCode',fix:true,label:'商品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
  // {istrue:true,prop:'illegalType',label:'扣款原因', width:'auto',sortable:'custom',formatter:(row)=> !row.illegalTypeName?" " : row.illegalTypeName},
  { istrue: true, prop: 'amountPaid', label: '扣款金额', width: '100', sortable: 'custom', formatter: (row) => parseFloat(row.amountPaid.toFixed(6)) },
  { istrue: true, prop: 'proCode1', fix: true, label: '趋势图', text: '趋势图', style: "color:red;cursor:pointer;", width: '70', formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showprchart(row.proCode) },
  // {istrue:true,prop:'proCode2',fix:true,label:'看板', text:'看板',style:"color:red;cursor:pointer;",width:'70', formatter:(row)=>'看板',type:'click',handle:(that,row)=>that.showpboard(row.proCode)},
]
const tableHandles = [];
export default {
  name: 'YunhanAdminOrderillegaldetail1',
  components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, orderIllAnalysis, orderIllgalsearch, orderillegalboard },
  props: {
    filter: {}
  },
  data () {
    return {
      that: this,
      filter1: { isgroupbyreson: null },
      filter2: {},
      list: [],
      platformList: [],
      illegalTypeList: [],
      summaryarry: {},
      pager: { OrderBy: "AmountPaid", IsAsc: false },
      filterImport: {
        platform: 1,
        occurrenceTime: formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now();
        }
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      dialogVisible: false,
      uploadLoading: false,
      dialoganalysisVisible: false,
      dialosisboardVisible: false,
      showDetailVisible: false
    };
  },
  async mounted () {
    await this.onSearch()
  },
  methods: {
    async onSearch () {
      if (!this.filter.timerange) { this.$message({ message: "请选择日期", type: "warning", }); return; }
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    //获取查询条件
    getCondition () {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter,
      }
      return params;
    },
    //分页查询
    async getlist () {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true
      const res = await getWithProCodeSum(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      // if(this.summaryarry)
      //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange (column) {
      if (!column.order)
        this.pager = {};
      else {
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
      }
      await this.onSearch();
    },
    async showprchart (row) {
      this.dialoganalysisVisible = true;
      let para = { proCode: row, timerange: this.filter.timerange, platform: 2, reportType: 0 }
      this.$nextTick(() => {
        this.$refs.orderIllAnalysis.onSearchPddKkhz(para);
      });
    },
    //父子传值
    async showpboard (row) {
      //this.$emit('nOnsearch', row);
      //let para ={proCode : row, timerange : this.filter.timerange}
      this.filter.proCode = row;
      this.filter.shopId = this.filter?.shopId ?? null;
      let para = { ...this.filter }
      this.dialosisboardVisible = true
      this.$nextTick(async () => {
        
        await this.$refs.orderillegalboard.showIllboard(para)
      })
    },
    async onSearchDetail (para) {
      this.filter2 = para
      console.log('输出', this.filter2)
      this.filter2.groupId = null
      this.showDetailVisible = true
      this.$nextTick(async () => {
        await this.$refs.orderIllgalsearch.onSearch()
      })
    },
    selectchange: function (rows, row) {
      this.selids = []; console.log(rows)
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  }
};
</script>

<style lang="scss" scoped></style>
