<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-date-picker style="width: 280px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false"
        :picker-options="pickerOptions"></el-date-picker>
      <el-input v-model.trim="filter.proCode" clearable placeholder="商品ID" style="width:140px" maxlength="50" />

      <el-select v-model="filter.shopCode" filterable clearable placeholder="店铺" style="width:160px">
        <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <!-- <el-input v-model.trim="filter.businessMan" clearable placeholder="商务" style="width:140px" maxlength="50"/> -->
      <el-select v-model.trim="filter.businessMan" filterable clearable placeholder="商务" style="width:140px"
        :disabled="businessManDisabled">
        <el-option label="无商务" value="无商务"></el-option>
        <el-option v-for="item in businessManList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <!-- <el-select v-model.trim="filter.wiseManAccountCode" filterable clearable placeholder="达人快手号" remote
        reserve-keyword :remote-method="remoteMethodWiseManAccountCode" :loading="wiseManAccountCodeloading"
        style="width:200px">
        <el-option v-for="item in accountCodeList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select> -->
      <el-input v-model.trim="filter.wiseManAccountCode" placeholder="达人快手号" maxlength="20" clearable
        style="width:200px" />

      <el-input v-model.trim="filter.keywords" clearable placeholder="关键字查询 商品/店铺/达人" style="width:240px"
        maxlength="50" />

      <el-button-group>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </el-button-group>
    </template>


    <vxetablebase :id="'KWaiShopBusinessDaily202501162030'" :border="true" :align="'center'"
      :tablekey="'KWaiShopBusinessDaily202501162030'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
      :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
      style="width:100%;height:100%;margin: 0" :xgt="9999">
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>


    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div>
        <el-date-picker style="width: 200px;margin-right: 10px;margin-bottom: 10px;" v-model="yearMonthDay" type="date"
          placeholder="选择日期" :clearable="false" format="yyyyMMdd" value-format="yyyyMMdd">
        </el-date-picker>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import { getBusinessOrderDayReport_AllList, importBusinessOrderInfoAsync } from '@/api/bookkeeper/reportdayV2'
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop';

import {
  GetBusinessDayReportDY, GetAllWiseManDetailList, GetAllWiseManDetailListPage,
  GetAllBusinessDetailList, GetBusinessManInfo
} from "@/api/bookkeeper/reportdayDouYin";


import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
const tableCols = [
  { istrue: true, prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '80' },
  //{ istrue: true,  prop: 'platform', fix: true, label: '平台', width: '150', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', width: '120' },
  { istrue: true, label: '小组头像', width: '70', type: 'ddAvatar', ddInfo: { type: 1, prop: 'groupId' } },
  { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '80', type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, },
  { istrue: true, prop: 'businessMan', label: '商务', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'cpsDarenNickname', label: '达人', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'wiseManAccountCode', label: '达人快手号', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'proCode', label: '商品ID', sortable: 'custom', width: '160', type: 'html', formatter: (row) => formatLinkProCode(6, row.proCode,) },
  { istrue: true, prop: 'proName', label: '商品名称', sortable: 'custom', minwidth: '120' },
  { istrue: true, prop: 'onTime', label: '上架时间', sortable: 'custom', width: '140' },
  { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '80' },
  { istrue: true, prop: 'payAmount', label: '付款金额', sortable: 'custom', width: '80', formatter: (row) => row.payAmount },
  { istrue: true, prop: 'saleAmount', label: '销售金额', sortable: 'custom', width: '80', formatter: (row) => row.saleAmount },
  { istrue: true, prop: 'commissionAmount', label: '佣金', sortable: 'custom', width: '80', formatter: (row) => row.commissionAmount },
  { istrue: true, prop: 'commissionRate', label: '佣金率', sortable: 'custom', width: '80', formatter: (row) => row.commissionRate + "%" },
  //{ istrue: true, prop: 'yearMonthDay', label: '预估提成', sortable: 'custom', width: '150' },
];
const tableHandles = [
  //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(3, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
  name: "KWaiShopBusinessDaily",
  components: {
    MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
    buschar, vxetablebase
  },
  data() {
    return {
      yearMonthDay: '',
      dialogVisible: false,
      uploadLoading: false,
      pageLoading: false,
      that: this,
      filter: {
        daterange: [startDate, endDate],
        startTime: null,
        endTime: null,
        updateTime: null,
        proCode: null,
        productName: null,
        shopCode: null,
        businessMan: null,
        wiseManAccountName: null,
        wiseManAccountCode: null,
        groupType: null,
        platForm: 14,
      },
      wiseManAccountCodeloading: false,
      businessManDisabled: false,
      pickerOptions: pickerOptions,
      options: [],
      businessManList: [],
      shopList: [],
      accountCodeList: [],
      wiseManList: [],
      grouplist: [],
      directorlist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      datalist: [],
      pager: { OrderBy: "", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      fileparm: {},
    };
  },
  async mounted() {
    await this.getBusinessManInfo();
    await this.onSearch();
  },
  async created() {
    await this.getShopList();
    //await this.getWiseManAccountCodeList();
    await this.getBusinessNameList();
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonthDay", this.yearMonthDay);
      var res = await importBusinessOrderInfoAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = ''
      this.dialogVisible = true;
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [14] });
      this.shopList = res1.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });

      const res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      const res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    async getWiseManAccountCodeList() {
      //获取全部达人快手号列表
      const res4 = await GetAllWiseManDetailList();
      this.accountCodeList = res4.data?.map(item => { return { value: item.wiseManAccountCode, label: item.wiseManAccountName + '（' + item.wiseManAccountCode + '）' }; });
    },
    async getBusinessNameList() {
      const resBusiness = await GetAllBusinessDetailList();
      this.businessManList = resBusiness.data?.filter(item1 => item1.businessMan != '无商务')?.map(item => { return { value: item.businessMan, label: item.businessMan }; });
    },
    async getBusinessManInfo() {
      const res = await GetBusinessManInfo();
      if (res.success && res.data) {
        this.businessManDisabled = true;
        this.filter.businessMan = res.data;
      }
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getList();
    },
    async getList() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.daterange) {
        this.filter.startTime = this.filter.daterange[0];
        this.filter.endTime = this.filter.daterange[1];
      }
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      this.pageLoading = true;
      const res = await getBusinessOrderDayReport_AllList(params);
      that.total = res.data?.total;
      that.datalist = res.data?.list;
      that.summaryarry = res.data?.summary;
      for (var key in that.summaryarry) {
        that.summaryarry[key] = that.summaryarry[key] ? that.summaryarry[key].toString() : '';
      }
      this.pageLoading = false;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    onRefresh() {
      this.onSearch()
    },
    async remoteMethodWiseManAccountCode(value) {
      const res4 = await GetAllWiseManDetailListPage({ str: value });
      this.accountCodeList = res4.data?.map(item => {
        return { value: item.wiseManAccountCode, label: item.wiseManAccountName + '（' + item.wiseManAccountCode + '）' };
      });
    }

  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
