<template>
  <my-container>
    <template #header>
      限制开始时间
      <el-date-picker style="width: 280px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" 
        @change="changeTime" unlink-panels>
      </el-date-picker>

      <el-select v-model="filterStyleCode.groupId" placeholder="小组" multiple clearable collapse-tags filterable style="width:190px;" >
        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
        </el-option>
      </el-select>

      <el-tooltip class="item" effect="dark" content="输入“空”，可查询系列编码为空的数据" placement="top">
        <el-select v-if="boardType == 1" v-model="filterStyleCode.styleCode" multiple collapse-tags filterable remote reserve-keyword placeholder="系列编码"
          clearable :remote-method="remoteMethod" style="width: 190px">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-tooltip>
      
      <el-select v-model="filterStyleCode.BelongToGroupId" placeholder="归属小组" multiple clearable collapse-tags filterable style="width:190px;" >
        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key">
        </el-option>
      </el-select>
      
      <el-button type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
      <el-button type="primary" icon="el-icon-download" @click="onExport">导出</el-button>
    </template>
    
    <vxetablebase :id="'violatStatisticsStyleCode202412071358'" ref="table" :that='that' :isIndex='false' 
      @sortchange='sortchangeStyleCode' :tableData='tableData' :tableCols='tableColsStyleCode' style="width: 100%; margin: 0"
      :loading="listLoading" :height="'100%'" :treeProp="{ rowField: 'id', parentField: 'parentId' }" :hasSeq="true">
    </vxetablebase>
    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablebase.vue";
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getViolatStatisticsStyleCodeData, exportViolatStatisticsStyleCodeData, getViolatStatisticsStyleCode } from '@/api/operatemanage/OperationalMiddleOfficeManage/ViolatStatistics.js';

const tableColsStyleCode = [
  { sortable: 'custom', istrue: true, width: '320', align: "center", prop: "styleCode", label: "系列编码" },
  { sortable: 'custom', istrue: true, width: '320', align: "center", prop: "groupName", label: "小组" },
  { sortable: 'custom', istrue: true, width: '320', align: "center", prop: "seriesNameScore", label: "系列编码DSR", tipmesg: "输入“空”，可查询系列编码为空的数据" },
  { sortable: 'custom', istrue: true, width: '320', align: "center", prop: "belongToGroupName", label: "编码归属小组" },
  { sortable: 'custom', istrue: true, width: '320', align: "center", prop: "num", label: "出现次数", treeNode: true },
];

export default {
  name: "ViolatStatisticsStyleCode",
  components: { MyContainer, vxetablebase },
  data() {
    return {
      that: this,
      listLoading: false,
      timeRange: [],
      groupList: [],//运营组ID-小组
      shopList: [],//店铺编码-店铺(拼多多)

      filterStyleCode: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startPunishBeginTime: null,//起始限制开始时间
        endPunishBeginTime: null,//结束限制开始时间
        groupId: [],//运营组ID-小组
        styleCode: [],//系列编码
        BelongToGroupId: [],//运营组Id-商品归属小组
      },
      
      total: 0,
      tableData: [],
      tableColsStyleCode: tableColsStyleCode,
      pickerOptions: {
        shortcuts: [{
          text: '前一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            end.setTime(end.getTime());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近一个月',
          onClick(picker) {
            const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
            const date2 = new Date(); date2.setDate(date2.getDate());
            picker.$emit('pick', [date1, date2]);
          }
        }]
      },
      options: [],
    }
  },
  async mounted() {
    this.init();
    this.getList();
  },
  methods: {
    async init() {
      //获取运营组ID-小组
      const group = await getDirectorGroupList({});
      this.groupList = [{ key: 0, value: '空' }].concat(group.data || []);
      //获取店铺编码-店铺(拼多多)
      const { data: data } = await getAllShopList({ platforms: [2] });
      data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode != null) {
            this.shopList.push(f);
        }
      })
    },
    async changeTime(e) {
      this.filterStyleCode.startPunishBeginTime = e? e[0] : null;
      this.filterStyleCode.endPunishBeginTime = e? e[1] : null;
    },
    // 每页数量改变
    Sizechange(val) {
      this.listLoading = true;
      this.filterStyleCode.currentPage = 1;
      this.filterStyleCode.pageSize = val;
      this.getList();
      this.listLoading = false;
    },
    // 当前页改变
    Pagechange(val) {
      this.filterStyleCode.currentPage = val;
      this.getList();
    },
    // 排序
    sortchangeStyleCode(column) {
      if (!column.order) {
        this.pagerStyleCode = {};
      } else {
        this.pagerStyleCode = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      this.filterStyleCode.orderBy = column.prop;
      this.filterStyleCode.isAsc = column.order.indexOf("descending") == -1 ? true : false;
      this.onSearch();
    },
    // 查询
    onSearch() {
      //点击查询时才将页数重置为1
      this.filterStyleCode.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let data, success;
      // let val1 = this.filterStyleCode.styleCode ? this.filterStyleCode.styleCode.replace(/[^a-zA-Z0-9()\u4e00-\u9fa5]/g, ',') : "";
      // let val2 = val1.split(',');
      // this.filterStyleCode.styleCode = val2.join(',');
      ({ data, success } = await getViolatStatisticsStyleCodeData(this.filterStyleCode));
      this.listLoading = false;
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    styleCodeCallback(val) {
      let val1 = val.replace(/[^a-zA-Z0-9()\u4e00-\u9fa5]/g, ',');
      let val2 = val1.split(',');
      this.filterStyleCode.styleCode = val2.join(',');
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.options = [];
        setTimeout(async () => {
          // const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query });
          const res = await getViolatStatisticsStyleCode({ styleCode: query });
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportViolatStatisticsStyleCodeData(this.filterStyleCode);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '违规数据统计系列编码ID维度导出_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    }
  }
}
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend
  {
      background-color: #fff;
  }

  ::v-deep .inner-container::-webkit-scrollbar
  {
      display: none;
  }

  ::v-deep .mycontainer
  {
      position: relative;
  }

  .uptime
  {
      font-size: 14px;
      position: absolute;
      right: 30px;
  }

  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text
  {
      max-width: 45px;
  }
</style>