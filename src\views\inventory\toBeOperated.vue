<template>
    <MyContainer>
        <template #header>
            <div class="top">
            <el-select v-model="ListInfo.title" placeholder="降价部门" class="publicCss" clearable>
                <el-option key="采购员" label="采购员" value="采购员" />
                <el-option key="核价员" label="核价员" value="核价员" />
                <el-option key="外采" label="外采" value="外采" />
                <el-option key="运营部-运营降价" label="运营部-运营降价" value="运营部-运营降价" />
            </el-select>
            <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                <el-option key="待审批" label="待审批" value="待审批"></el-option>
                <el-option key="审批中" label="审批中" value="审批中"></el-option>
                <el-option key="已完成" label="已完成" value="已完成"></el-option>
                <el-option key="已拒绝" label="已拒绝" value="已拒绝"></el-option>
                <el-option key="已撤销" label="已撤销" value="已撤销"></el-option>
            </el-select>
            <el-select v-model="ListInfo.supplierType" placeholder="供应商类型" class="publicCss" clearable>
                <el-option key="原供应商降价" label="原供应商降价" value="原供应商降价"></el-option>
                <el-option key="新开发供应商降价" label="新开发供应商降价" value="新开发供应商降价"></el-option>
            </el-select>
            <el-input v-model="ListInfo.seriesName" placeholder="款式编码" class="publicCss" maxlength="50" clearable />
            <el-input v-model="ListInfo.goodsCode" placeholder="商品编码 末尾加*模糊搜索" class="publicCss" maxlength="50" clearable />
            <el-input v-model="ListInfo.goodsName" placeholder="商品名称" class="publicCss" maxlength="50" clearable />
            <el-input v-model="ListInfo.userName" placeholder="添加人" class="publicCss" maxlength="50" clearable />
            <el-select v-model="ListInfo.isFreeShipping" placeholder="是否包邮" class="publicCss" maxlength="50" clearable>
                <el-option key="是" label="是" value="是"></el-option>
                <el-option key="否" label="否" value="否"></el-option>
            </el-select>
            <el-button type="primary" @click="getList(true)">搜索</el-button>
            <el-button type="primary" @click="exportProps">导出</el-button>
            <el-button type="primary" @click="addProps">新增</el-button>
            <el-button type="primary" @click="auditProps(false)">发起审批</el-button>
        </div>
        <el-date-picker v-model="addTimeRanger" type="daterange" unlink-panels range-separator="至"
            start-placeholder="添加开始日期" end-placeholder="添加结束日期" :picker-options="pickerOptions"
            style="width: 260px;" @change="changeTime($event, 'addTime')" />
        <el-tooltip class="item" effect="dark" content="添加日期" placement="top-start">
            <i class="el-icon-question" style="margin-right: 10px;"></i>
        </el-tooltip>
        <el-date-picker v-model="applyTimeRanger" type="daterange" unlink-panels range-separator="至"
            start-placeholder="完成审批开始日期" end-placeholder="完成审批结束日期" :picker-options="pickerOptions"
            style="width: 280px;" @change="changeTime($event, 'applyTime')" />
        <el-tooltip class="item" effect="dark" content="完成审批时间" placement="top-start">
            <i class="el-icon-question" style="margin-right: 10px;"></i>
        </el-tooltip>
        <el-select v-model="ListInfo.isFailed" placeholder="是否失败" class="publicCss" maxlength="50" clearable>
            <el-option key="是" label="是" value="是"></el-option>
            <el-option key="否" label="否" value="否"></el-option>
        </el-select>
        </template>
        
        <vxetablebase :id="'toBeOperated202408041550'" @select="checkboxRangeEnd" ref="table" :that='that'
            :isIndex='true' :hasexpand='true' :column-config="{ resizable: true }" :tree-config="{}" :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" v-loading="loading" style="width: 100%;" height="100%">
            <template slot="right">
                <vxe-column title="操作" width="180">
                    <template #default="{ row, $index }">
                        <div v-if="row.isRoot">
                            <el-button type="text" v-if="row.status == '审批中' || row.status == '已完成'"
                                @click="viewProps(row)">查看</el-button>
                            <el-button type="text" v-if="row.status == '已完成' && row.isFailed != true && checkPermission('SetGoodsCostChgFail')" 
                                @click="failedProps(row)">失败</el-button>
                            <el-button type="text" @click="eidtProps(row)"
                                v-if="row.status != '审批中' && row.status != '已完成'">编辑</el-button>
                            <el-button type="text" @click="delProps(row.id)"
                                v-if="row.status != '审批中' && row.status != '已完成'">删除</el-button>
                            <el-button type="text" @click="auditProps(true, row)"
                                v-if="row.status != '审批中' && row.status != '已完成'">发起审批</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer :title="title" :visible.sync="drawer" direction="rtl" :append-to-body="true" size="50%">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" style="padding:  20px;">
                <el-form-item label="降价部门" prop="title">
                    <el-select v-model="ruleForm.title" placeholder="降价部门" clearable style="width: 400px;">
                        <el-option key="采购员" label="采购员" value="采购员" />
                        <el-option key="核价员" label="核价员" value="核价员" />
                        <el-option key="外采" label="外采" value="外采" />
                        <el-option key="运营部-运营降价" label="运营部-运营降价" value="运营部-运营降价" />
                    </el-select>
                </el-form-item>
                <el-form-item label="降价供应商" prop="supplierType">
                    <el-select v-model="ruleForm.supplierType" placeholder="降价供应商" clearable style="width: 400px;">
                        <el-option key="原供应商降价" label="原供应商降价" value="原供应商降价" />
                        <el-option key="新开发供应商降价" label="新开发供应商降价" value="新开发供应商降价" />
                    </el-select>
                </el-form-item>
                <div class="el-form-item el-form-item--mini">
                    <label class="el-form-item__label" style="width: 150px;">核价截图or聊天截图</label>
                    <div class="el-form-item__content" style="margin-left: 150px;">
                        <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="!formEditMode"
                            :noDel="!formEditMode" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls"
                            :keys="[1, 1]" @callback="getImg" :imgmaxsize="19" :limit="19" :multiple="true">
                        </uploadimgFile>
                    </div>
                </div>
                <el-form-item label="款式编码" prop="seriesName">
                    <el-input v-model="ruleForm.seriesName" placeholder="款式编码" style="width: 400px;" clearable
                        maxlength="100" />
                </el-form-item>
                <el-form-item label="一键复制区域">
                    <el-input type="textarea" placeholder="请按照规定格式粘贴复制内容" v-model.trim="textarea" maxlength="1000"
                        :show-word-limit="false" tabindex="1" class="textarea" @input="changeIpt" :rows="1" />
                </el-form-item>
                <el-form-item label="是否包邮" prop="isFreeShipping">
                    <el-select v-model="ruleForm.isFreeShipping" placeholder="是否包邮" clearable style="width: 80px;">
                        <el-option key="是" label="是" value="是" />
                        <el-option key="否" label="否" value="否" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否摘品">
                    <el-switch v-model="ruleForm.isPicking" active-color="#13ce66" inactive-color="#c0c0c0"></el-switch>
                </el-form-item>
                <el-form-item label="原采购" v-if="ruleForm.isPicking">
                    <el-select v-model="ruleForm.originalBrand" placeholder="原采购" clearable style="width: 400px;"
                        filterable>
                        <el-option v-for="item in brandList" :key="item.id" :label="item.brandName" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="半成品转成品" v-if="ruleForm.isPicking">
                    <el-switch v-model="ruleForm.isSemiFinished" active-color="#13ce66"
                        inactive-color="#c0c0c0"></el-switch>
                </el-form-item>
                <!-- 表格部分 -->
                <el-button type="text" @click="addtTableProps">新增一行</el-button>
                <el-table :data="ruleForm.items" style="width: 100%;margin-bottom: 20px" max-height="250">
                    <el-table-column type="index" width="50" />
                    <el-table-column prop="goodsCode" label="商品编码" width="180">
                        <template #default="{ row }">
                            <el-input v-model="row.goodsCode" placeholder="商品编码" style="width: 100%" clearable
                                maxlength="50" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="goodsName" label="商品名称" width="180">
                        <template #default="{ row }">
                            <el-input v-model="row.goodsName" placeholder="商品名称" style="width: 100%" clearable
                                maxlength="50" />
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="prevPrice" label="原成本(元)">
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.prevPrice" placeholder="原成本(元)" style="width: 100%"
                                :max="999999" :precision="4" clearable :controls="false" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="新成本(元)">
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.price" placeholder="新成本(元)" style="width: 100%" :max="999999"
                                :precision="4" clearable :controls="false" />
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="prevPrice" label="原成本(元)">
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.prevPrice" placeholder="原成本(元)" style="width: 100%"
                                :max="999999" :precision="4" clearable :controls="false" @change="changePrice" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="price" label="新成本(元)">
                        <template #default="{ row, $index }">
                            <el-input-number v-model="row.price" placeholder="新成本(元)" style="width: 100%" :max="999999"
                                :precision="4" clearable :controls="false" @change="changePrice" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="decreaseRatio" label="降价比例">
                        <template #default="{ row }">
                            {{ (row.decreaseRatio !== null && row.decreaseRatio !== undefined) ? row.decreaseRatio + '%'
                                : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="70">
                        <template #default="{ row, $index }">
                            <el-button type="danger" @click="delItemsProps($index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="fileRemove" :file-list="fileList" accept=".xlsx,.xls" :on-success="handleSuccess"
                    :on-change="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
                <el-form-item class="btnCss">
                    <el-button type="info" @click="drawer = false">关闭</el-button>
                    <el-button type="primary" @click="downLoadFiles" v-throttle="2000">下载附件</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm', false)" :disabled="isView"
                        v-throttle="2000">保存</el-button>
                    <el-button type="primary" @click="submitForm('ruleForm', true)" :disabled="isView"
                        v-throttle="5000">保存&发起审批</el-button>
                </el-form-item>
                <el-timeline v-if="activities.length > 0">
                    <el-timeline-item v-for="(activity, index) in activities" :key="index"
                        :timestamp="activity.approveTime">
                        {{ activity.approveName + '-' + activity.result }}
                    </el-timeline-item>
                </el-timeline>
            </el-form>
        </el-drawer>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import { replaceSpace } from '@/utils/getCols'
import dayjs from 'dayjs'
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { 
    pageGetApplyData, exportApplyData, mergeGoodsCostApply, 
    sendApply, deleteGoodsCostApply, getApplyData, 
    getApproveLogDetailAsync, getBrandList, 
    setGoodsCostApplyFailedMarker
} from '@/api/inventory/goodscostpricechg'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'status', label: '状态', sortable: 'custom', width: 'auto', treeNode: true, },
    { istrue: true, prop: 'isFailedStr', label: '是否失败', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'seriesName', label: '款式编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'prevPrice', label: '聚水潭成本', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'price', label: '新成本', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'isFreeShipping', label: '是否包邮', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'userName', label: '添加人', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'title', label: '降价部门', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'supplierType', label: '供应商类型', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '添加日期', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'applyTime', label: '发起审批时间', sortable: 'custom', width: 'auto', },
    { istrue: true, prop: 'cptTime', label: '完成审批时间', sortable: 'custom', width: 'auto', },
]

export default {
    name: "toBeOperated",
    components: {
        MyContainer, vxetablebase, uploadimgFile
    },
    data() {
        return {
            tableCols,
            pickerOptions,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                userName: null,//添加人
                userId: null,
                title: null,
                goodsCode: null,//商品编码
                goodsName: null,//商品名称
                seriesName: null,//系列编码
                supplierType: null,//供应商类型
                status: null,//状态
                beginCreated: null,//添加开始日期
                endCreated: null,//添加结束日期
                beginApply: null,//发起审批开始日期
                endApply: null,//发起审批结束日期
            },
            total: 0,
            loading: false,
            tableData: [],
            addTimeRanger: [],
            applyTimeRanger: [],
            ids: [],
            drawer: false,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            editPriceVisible: false,
            formEditMode: true,
            chatUrls: [],
            title: '',
            ruleForm: {
                sendApply: false,
                title: null,
                userName: null,
                supplierType: null,
                seriesName: null,
                pictures: null,
                items: [],
                files: null,
                isFreeShipping: null
            },
            fileList: [],
            rules: {
                title: [
                    { required: true, message: '请选择降价部门', trigger: 'blur change' },
                ],
                supplierType: [
                    { required: true, message: '请选择降价供应商', trigger: 'change blur' }
                ],
                seriesName: [
                    { required: true, message: '请输入降价编码', trigger: 'change blur' }
                ],
                isFreeShipping: [
                    { required: true, message: '请选择是否包邮', trigger: 'change' },
                ]
            },
            isView: false,
            isEdit: false,
            activities: [],
            name: null,
            textarea: '',
            brandList: []
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        changePrice() {
            this.ruleForm.items.forEach((item, i) => {
                if (item.price === null || item.price === undefined || item.price === '' || item.price === 0 || item.prevPrice === null || item.prevPrice === undefined || item.prevPrice === '' || item.prevPrice === 0) {
                    item.decreaseRatio = 0
                } else {
                    item.decreaseRatio = ((1 - (item.price / item.prevPrice)) * 100).toFixed(3)
                }
            })
        },
        async getBrand() {
            const { data } = await getBrandList()
            this.brandList = data
        },
        changeIpt(e) {
            if (e != null && e != undefined && e != '' && (encodeURI(e) != '%0A' && encodeURI(e) != '%20')) {
                //根据空格和换行切割
                let arr = e.split(/[\s\n]/)
                arr.forEach((item, i) => {
                    //如果为空则删除
                    if (item == '' || item == null || item == undefined || item == ' ') {
                        arr.splice(i, 1)
                    }
                })
                //将arr切割成四个一组
                let newArr = []
                for (let i = 0; i < arr.length; i += 4) {
                    newArr.push(arr.slice(i, i + 4))
                }
                for (let i = 0; i < newArr.length; i++) {
                    this.ruleForm.items.push({ goodsCode: newArr[i][0], goodsName: newArr[i][1], prevPrice: newArr[i][2], price: newArr[i][3] })
                }
                this.textarea = null
            }
        },
        downLoadFiles() {
            if (this.ruleForm.files) {
                const a = JSON.parse(this.ruleForm.files)
                const aLink = document.createElement("a");
                aLink.href = a.url
                aLink.setAttribute('download', a.name)
                aLink.click()
            } else {
                this.$message.error('暂无附件')
            }
        },
        fileRemove() {
            this.ruleForm.files = null
        },
        uploadFile(data) {
            this.name = data.name
        },
        async viewProps(row) {
            this.title = '查看'
            this.ruleForm = {
                sendApply: false,
                title: null,
                userName: null,
                supplierType: null,
                pictures: null,
                seriesName: null,
                items: [],
                files: null,
                isFreeShipping: null
            }
            this.ruleForm = JSON.parse(JSON.stringify(row))
            this.getBrand()
            await this.getTableData(row)
            this.isView = true
            this.drawer = true
        },
        async getTableData({ id }) {
            this.fileList = []
            this.activities = []
            this.editPriceVisible = false
            const { data, success } = await getApplyData(id)
            if (success) {
                if (data.pictures) {
                    this.chatUrls = data.pictures.split(',').map((item, i) => {
                        return {
                            url: item,
                            name: `聊天截图${i + 1}`
                        }
                    })
                }
                if (data.files) {
                    const a = JSON.parse(data.files)
                    this.fileList.push({ name: a.name, url: a.url })
                    console.log(this.fileList, 'this.fileList');
                }
                if (data.instanceId) {
                    let { data: data1, success: success1 } = await getApproveLogDetailAsync(`"${data.instanceId}"`)
                    if (data1) {
                        data1.forEach(item => {
                            item.approveTime = item.approveTime ? dayjs(item.approveTime).format('YYYY-MM-DD HH:mm') : null
                        })
                    } else {
                        data1 = []
                    }
                    this.activities = data1
                }
                this.ruleForm.items = data.items
                this.editPriceVisible = true
            }
        },
        async eidtProps(row) {
            this.getBrand()
            this.ruleForm = {
                sendApply: false,
                title: null,
                userName: null,
                supplierType: null,
                pictures: null,
                seriesName: null,
                items: [],
                files: null,
                isFreeShipping: null
            }
            this.title = '编辑'
            console.log('row',row);
            this.ruleForm = JSON.parse(JSON.stringify({...row}))
            await this.getTableData(row)
            this.isEdit = true
            this.isView = false
            this.drawer = true
        },
        delItemsProps(index) {
            this.ruleForm.items.splice(index, 1)
        },
        delProps(id) {
            this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteGoodsCostApply(id)
                if (success) {
                    await this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });

                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        auditProps(isTbale, row) {
            if (isTbale) {
                this.ids = [row.id]
            }
            if (this.ids.length == 0) return this.$message.error('请选择数据')
            //根据id找出tableData中对应的数据
            const data = this.tableData.filter(f => this.ids.includes(f.id))
            // 如果有已完成的数据
            data.forEach(item => {
                if (item.status == '已完成' || item.status == '审批中') {
                    this.$message.error('已完成或审批中的数据不能发起审批')
                    throw new Error('已完成或审批中的数据不能发起审批')
                }
            })
            this.$confirm('此操作将审批该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await sendApply(this.ids)
                if (success) {
                    await this.getList()
                    this.ids = []
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });

                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消审批'
                });
            });
        },
        async submitForm(formName, sendApply) {
            this.$refs.ruleForm.validate(async(valid) => {
                if (valid) {
                    this.ruleForm.sendApply = sendApply
                    if (!this.ruleForm.title) {
                        return this.$message.error('请选择降价部门')
                    }
                    if (!this.ruleForm.supplierType) {
                        return this.$message.error('请选择降价供应商')
                    }
                    if (!this.ruleForm.seriesName) {
                        return this.$message.error('请输入降价编码')
                    }
                    if (this.chatUrls.length == 0) {
                        return this.$message.error('请上传聊天截图')
                    }
                    if (this.ruleForm.isPicking && !this.ruleForm.originalBrand) {
                        return this.$message.error('请选择原采购')
                    }
                    this.ruleForm.items.forEach((item, index) => {
                        if (!item.goodsCode || !item.goodsName || !item.prevPrice || !item.price) {
                            this.$message.error(`第${index + 1}条数据不完整`)
                            throw new Error(`第${index + 1}条数据不完整`)
                        }
                        if (item.prevPrice <= 0 || item.price <= 0) {
                            this.$message.error(`第${index + 1}条数据原成本或新成本小于等于0`)
                            throw new Error(`第${index + 1}条数据原成本或新成本小于等于0`)
                        }
                    })
                    const { success } = await mergeGoodsCostApply(this.ruleForm)
                    if (success) {
                        this.$message.success('操作成功')
                        this.drawer = false;
                        this.getList();
                    } else {
                        this.$message.error('操作失败')
                    }
                }
            })
        },
        addtTableProps() {
            this.ruleForm.items.push({ goodsCode: null, goodsName: null, decreaseRatio: null })
        },
        async handleSuccess({ data }) {
            this.ruleForm.files = JSON.stringify({ url: data.url, name: this.name })
        },
        getImg(data) {
            console.log(data, 'data');
            if (data) {
                this.chatUrls = data
                this.ruleForm.pictures = data.map(item => item.url).join(',')
            }
        },
        addProps() {
            this.ruleForm = {
                sendApply: false,
                title: null,
                userName: null,
                supplierType: null,
                pictures: null,
                seriesName: null,
                items: [],
                files: null,
                isFreeShipping: null
            }
            this.activities = []
            this.chatUrls = []
            this.editPriceVisible = false
            this.isEdit = false
            this.isView = false
            this.fileList = []
            setTimeout(() => {
                this.editPriceVisible = true
            }, 50);
            this.getBrand()
            this.title = '新增'
            this.drawer = true
        },
        //点击复选框
        checkboxRangeEnd(row) {
            this.ids = row.map(f => f.id)
        },
        async exportProps() {
            const { data } = await exportApplyData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '待操作数据' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        changeTime(e, type) {
            e = e ? e.map(f => dayjs(f).format('YYYY-MM-DD')) : null
            if (type == 'addTime') {
                this.ListInfo.beginCreated = e ? e[0] : null
                this.ListInfo.endCreated = e ? e[1] : null
            }
            if (type == 'applyTime') {
                this.ListInfo.beginApply = e ? e[0] : null
                this.ListInfo.endApply = e ? e[1] : null
            }
            this.getList()
        },
        init() {
            //添加日期
            if (this.addTimeRanger.length == 0) {
                this.addTimeRanger = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.beginCreated = this.addTimeRanger[0]
                this.ListInfo.endCreated = this.addTimeRanger[1]
            }
            //完成审批日期
            if (this.applyTimeRanger.length == 0) {
                this.applyTimeRanger = [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
                this.ListInfo.beginApply = this.applyTimeRanger[0]
                this.ListInfo.endApply = this.applyTimeRanger[1]
            }
        },
        async getList(isSearch) {
            isSearch ? this.ListInfo.currentPage = 1 : null
            const replaceArr = ['goodsCode', 'goodsName', 'seriesName', 'userName']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await pageGetApplyData(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async failedProps(row) {
            var param = { applyId: row.id };
            this.$confirm('此操作将设置该数据失败, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                var res = await setGoodsCostApplyFailedMarker(param);
                if (res?.success){
                    await this.getList();
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnCss {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
}
</style>
