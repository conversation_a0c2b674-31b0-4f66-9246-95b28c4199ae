<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='groupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <!-- <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.groupNameList" placeholder="分组"   multiple clearable filterable :collapse-tags="true">
                            <el-option v-for="item in filterGroupList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button> -->
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopNameList" placeholder="店铺"  multiple clearable filterable :collapse-tags="true">
                            <el-option v-for="item in shopList" :key="item" :label="item"
                                :value="item">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;" v-if="checkPermission(['api:Customerservice:OutSource:ExportDouYinByShop'])" >导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getShopInquirsStatisticsList" />
        </template>
        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';

import {
    getOSGroupByList,
    getOSDouYinShopEfficiencyPageList,
    getOSDouYinShopEfficiencyChat,
    exportDouYinByShop
} from "@/api/customerservice/waibao";
  import {getOSStoreNameList} from "@/api/customerservice/waibaocustomer";


const tableCols = [
    { istrue: true, prop: 'shopName', label: '店名', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'receiveds', label: '人工已接待人数', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '人工已接待会话量', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '100', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate).toFixed(2) + "%" },
    { istrue: true, prop: 'threeResponseRate', label: '3分钟人工回复率',width: '130', sortable: 'custom', formatter: (row) => (row.threeResponseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '130',sortable: 'custom' },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '100', sortable: 'custom', formatter: (row) => (row.satisfactionRate).toFixed(2) + "%" },
    { istrue: true, prop: 'noSatisfactionCount', label: '不满意人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'satisfactionCount', label: '满意人数', width: '90', sortable: 'custom'},
    { istrue: true, prop: 'threeResponseCount', label: '3分钟回复人数', width: '120', sortable: 'custom'},
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'payers', label: '支付人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '90', sortable: 'custom', formatter: (row) => (row.ipsRate * 100).toFixed(2) + "%" },
    //  { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    //   { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '90', sortable: 'custom' },
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
               groupNameList:[],
            },
            shopList: [],
            filterGroupList: [],
            userList: [],
            groupList: [],
            groupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "inquirs", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        await this.getGroupNameList();
        await this.getAllShopList();
    },
    methods: {
       async getGroupNameList() {
            let groups = await getOSGroupByList({ platform: 6 });
            this.filterGroupList=groups.data
        },
        async getAllShopList() {
            let shops = await getOSStoreNameList({platform:6});
            this.shopList = shops.data
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getShopInquirsStatisticsList();
        },
        getParam() {
            if (this.filter.sdate) {
                this.filter.timeStart = this.filter.sdate[0];
                this.filter.timeEnd = this.filter.sdate[1];
            }
            else {
                this.filter.timeStart = null;
                this.filter.timeEnd = null;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getShopInquirsStatisticsList() {//分页列表
            let params = this.getParam();
            this.listLoading = true;
            const res = await getOSDouYinShopEfficiencyPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.groupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async showchart(row) {
            let params = this.getParam();
            params.shopName = row.shopName;
            const res = await getOSDouYinShopEfficiencyChat(params).then(res => {
                if (res) {
                    this.dialogMapVisible.visible = true;
                    this.dialogMapVisible.data = res;
                    this.dialogMapVisible.title = res.title;
                    res.title = "";
                }
            })
            this.dialogMapVisible.visible = true
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportDouYinByShop(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音店效率统计(外包)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 55px;
}
</style>
