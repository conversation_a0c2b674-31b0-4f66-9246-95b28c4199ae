<template>
  <div>
        <!-- <span>
            <template>
                <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                            <el-form-item label="日期:">
                              <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                                   value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                            <el-form-item>
                                <el-button type="primary" @click="getechart">刷新</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
        </template>
        </span>
        <span>
            <buschar  ref="buschar" :analysisData="buscharDialog.data"></buschar>
        </span> -->

        <el-row>
      <el-col>
        <div>
          <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
                    <el-row>
                        <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                            <el-form-item label="日期:">
                              <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                                   value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                            <el-form-item>
                                <el-button type="primary" @click="getechart">刷新</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
        </div>
      </el-col>
    </el-row>
      <el-row>
      <el-col>
        <!-- <div style="scroll:false;" :scroll="no">
          <buschar  ref="buschar" :analysisData="buscharDialog.data" style="height:330px,width:1775px"></buschar>
        </div> style="height:320px;width:100%"-->
        <div id="charsid" :style="{height:height,width:width}">
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {getOrderNodesTimeAnalysishz} from '@/api/order/ordernodes'
import buschar from '@/components/Bus/buschar'
import pieChart from '@/views/admin/homecomponents/PieChart'
import * as echarts from 'echarts';
  export default {
    components: {buschar,pieChart},
    props:{
      width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '322px'
    },
      filter:{}
    },
    data() {
      return {
         selectrow:{},
         buscharDialog:{visible:false,title:"",data:[]},
         myChart1:null
      };
    },
  async created() {
    // await this.onSearch()
   },
    methods: {
      async onSearch() {
          const params = {...this.filter};
          await this.getechart();
      },
      async getechart(){
       await this.showprchart()
      },
      async showprchart(){
          this.filter.startTime = null
          this.filter.endTime =  null
          if (this.filter.timerange&&this.filter.timerange.length>1) {
              this.filter.startTime = this.filter.timerange[0];
              this.filter.endTime = this.filter.timerange[1];
          }
          else {
              this.$message({message:"请先选择日期",type:"warning"});
              return false;
          }
          const params = {...this.filter};
          const res = await getOrderNodesTimeAnalysishz(params);
         var chardata= this.Getoptions(res.data);
          await this.initchartsline(chardata);
        },
        async initchartsline(analysisData) {
          var that=this;
            this.$nextTick(() => {
                var chartDom1 = document.getElementById('charsid');
                if(that.myChart1!=null){
                  that.myChart1.dispose(); //销毁
                }
                that.myChart1 = echarts.init(chartDom1);
                that.myChart1.clear();
                var option1 = analysisData;
                that.myChart1.setOption(option1);
              });
    },
    Getoptions(element){
       var series=[]
       element.series.forEach(s=>{
         series.push({smooth: true, ...s})
       })
     var yAxis=[]
     element.yAxis.forEach(s=>{
       yAxis.push({type: 'value',minInterval:10,offset:s.offset,splitLine:s.splitLine,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
     })
     var selectedLegend={};//{};
     if(element.selectedLegend){
       element.legend.forEach(f=>{
          //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
          if(!element.selectedLegend.includes(f)) selectedLegend[f]=false
        })
     }
      var option = {
        title: {text: element.title},
        tooltip: {trigger: 'axis'},
        legend: {
           selected:selectedLegend,
           data: element.legend
         },
        grid: {
            top: '20%',
            left:'5%',
            right: '4%',
            bottom: '20%',
            containLabel: false
        },
        toolbox: {feature: {
            magicType: {show: true, type: ['line', 'bar']},
            //restore: {show: true},
        }},
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis:  yAxis,
        series:  series
    };
    return option;
   },
    }
  };
</script>
