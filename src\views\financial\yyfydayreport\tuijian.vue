<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->

    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tablekey='tablekey'
      :tableData='tuijianlist' @summaryClick='onsummaryClick' @select='selectchange' :isSelection='false'
      :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.sceneName" placeholder="场景名字" style="width:120px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.ShopName" placeholder="店铺" style="width:120px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.ProductName" placeholder="计划名称" style="width:120px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model="Filter.ProductID" placeholder="计划id" style="width:120px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width:280px" v-model="Filter.UseDate" type="datetimerange"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>

          </el-button>
          <!-- <el-button type="text" size="medium" disabled style="color: red;">周转对比:</el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input-number v-model="filter1.startdiff"></el-input-number>至<el-input-number v-model="filter1.enddiff"></el-input-number>
            </el-button> -->
          <el-button type="primary" @click="onSearch">查询</el-button>

          <el-button type="primary" @click="onImportSyj">导入</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettuijianList" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisibleSyj" width="30%">
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传
          </my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import { importTuijianAsync, getTuijianList, deleteTuijianBatch, queryTuiJianAnalysis } from '@/api/financial/yyfyday'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar'
const tableCols = [
  { istrue: true, prop: 'useDate', label: '日期', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'shopName', label: '店铺', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'sceneId', label: '场景ID', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'sceneName', label: '场景名字', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'originalSceneId', label: '原二级场景ID', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'originalSceneName', label: '原二级场景名字', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'productID', label: '计划id', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'productName', label: '计划名称', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'useMoney', label: '花费', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'viewCount', label: '观看次数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'viewCost', label: '观看成本', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'showCount', label: '展现量', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'viewRate', label: '观看率', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'costPerThousandShows', label: '千次展现花费', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'totalTransactionCount', label: '总成交笔数', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'totalTransactionAmount', label: '总成交金额', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'directTransactionAmount', label: '直接成交金额', width: '100', sortable: 'custom' },
  { istrue: true, summaryEvent: true, prop: 'directTransactionCount', label: '直接成交笔数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'createdTime', label: '导入时间', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'batchNumber', label: '导入批次', width: '100', sortable: 'custom' },
  { istrue: true, type: "button", width: "70",label: '操作',  btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) }] }
];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar },
  props: {
    tablekey: { type: String, default: '' }//tab下多个pane下标
  },
  data() {
    return {
      that: this,
      Filter: {
        UseDate:[startDate, endDate],
      },
      shopList: [],
      userList: [],
      groupList: [],
      tuijianlist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      buscharDialog: { visible: false, title: "", data: [] },
    };
  },
  async mounted() {
  },
  methods: {
    async onsummaryClick(property) {
      this.Filter.startAccountDate = null;
      this.Filter.endAccountDate=null;
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0];
        this.Filter.endAccountDate = this.Filter.UseDate[1];
      }
      this.Filter.startUseDate = null;
        this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.Filter };
      params.column = property;
      let that = this;
      await queryTuiJianAnalysis(params).then(res => {
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data;
        that.buscharDialog.title = res.data.legend[0];
      });
    },
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入超级推荐汇总数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteTuijianBatch({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()

        });

    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importTuijianAsync(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.gettuijianList();
    },
    async gettuijianList() {
      this.Filter.startAccountDate=null;
      this.Filter.endAccountDate=null;
      if (this.Filter.UseDate) {
        this.Filter.startAccountDate = this.Filter.UseDate[0];
        this.Filter.endAccountDate = this.Filter.UseDate[1];
      }
      this.Filter.startUseDate = null;
        this.Filter.endUseDate = null;
      if (this.Filter.UseDate) {
        this.Filter.startUseDate = this.Filter.UseDate[0];
        this.Filter.endUseDate = this.Filter.UseDate[1];
      }
      const para = { ...this.Filter };
      let pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getTuijianList(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)
      //console.log(res.data.summary)

      this.total = res.data.total
      this.tuijianlist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
