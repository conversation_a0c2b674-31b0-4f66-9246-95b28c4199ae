<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%" @tab-click="onToggle">
      <el-tab-pane label="ERP核算" name="first4" style="height: 99%" lazy>
        <erpExport ref="referpExport" />
      </el-tab-pane>
      <el-tab-pane label="已复核" name="first7" style="height: 99%" lazy>
        <erpExportReview ref="referperpExportReview" />
      </el-tab-pane>

      <el-tab-pane label="锁定记录" name="first17" style="height: 99%" lazy>
        <expressLockList ref="refexpressLockList" />
      </el-tab-pane>
      <el-tab-pane label="已复核全量数据" name="first71" style="height: 99%" lazy>
        <erpExportALL ref="refererpExportALL" />
      </el-tab-pane>
      <el-tab-pane label="编码重量段" name="first72" style="height: 99%" lazy>
        <expressGoodsWeight ref="referexpressGoodsWeight" />
      </el-tab-pane>
      <el-tab-pane label="重量差" name="first73" style="height: 99%" lazy>
        <erpExportALLWeight ref="refererpExportALLWeight" />
      </el-tab-pane>
      <!-- <el-tab-pane label="账单明细" name="first1" style="height: 99%">
        <billingDetails ref="refbillingDetails" />
      </el-tab-pane> -->
      <el-tab-pane label="区域汇总" name="first2" style="height: 99%" lazy>
        <regionalRollup ref="refregionalRollup" />
      </el-tab-pane>
      <el-tab-pane label="店铺汇总" name="first3" style="height: 99%" lazy>
        <storeSummary ref="refstoreSummary" />
      </el-tab-pane>
      <el-tab-pane label="代发店铺汇总" name="first19" style="height: 99%" lazy>
        <agentStoreSummary ref="refagentStoreSummary" />
      </el-tab-pane>
      <el-tab-pane label="店铺按月汇总" name="first9" style="height: 99%" lazy>
        <storeSummaryByMonth ref="refstoreSummaryByMonth" />
      </el-tab-pane>
      <el-tab-pane label="分销商按月汇总" name="first10" style="height: 99%" lazy>
        <fenxiaoSummaryByMonth ref="reffenxiaoSummaryByMonth" />
      </el-tab-pane>
      <el-tab-pane label="万俊" name="first5" style="height: 99%" lazy>
        <wanData ref="refwanData" />
      </el-tab-pane>
      <el-tab-pane label="取消单" name="first6" style="height: 99%" lazy>
        <cancellationOrder ref="refcancellationOrder" />
      </el-tab-pane>
      <el-tab-pane label="分销商管理" name="first88" style="height: 99%" lazy>
        <expressDistributor ref="refexpressDistributor" />
      </el-tab-pane>
      <el-tab-pane label="店铺配置" name="first8" style="height: 99%" lazy>
        <shopConfiguration ref="refshopConfiguration" />
      </el-tab-pane>
      <el-tab-pane label="改名店铺" name="first888" style="height: 99%" lazy>
        <shopChangeHistory ref="refshopChangeHistory" />
      </el-tab-pane>
      <el-tab-pane label="代发快递店铺配置" name="first18" style="height: 99%" lazy>
        <agentExpressDeliveryDisposition ref="refagentExpressDeliveryDisposition" />
      </el-tab-pane>
      <el-tab-pane label="快递公司汇总" name="first11" style="height: 99%" lazy>
        <courierCompaniesCollect ref="refcourierCompaniesCollect" />
      </el-tab-pane>
      <el-tab-pane label="快递余额表" name="first16" style="height: 99%" lazy>
        <ExpressCompanyMonthlySummary ref="refExpressCompanyMonthlySummary" />
      </el-tab-pane>
      <el-tab-pane label=" 南昌及西安快递汇总" name="first15" style="height: 99%" lazy>
        <courierCompaniesCollectNoYiWuCang ref="refcourierCompaniesCollectNoYiWuCang" />
      </el-tab-pane>
      <el-tab-pane label="仓区汇总" name="first12" style="height: 99%" lazy>
        <warehouseAreaSummary ref="refwarehouseAreaSummary" />
      </el-tab-pane>
      <el-tab-pane label="仓区配置" name="first13" style="height: 99%" lazy>
        <warehouseAreaDisposition ref="refwarehouseAreaDisposition" />
      </el-tab-pane>
      <el-tab-pane label="趋势图" name="first14" style="height: 99%">
        <warehouseTrendChart ref="refwarehouseTrendChart" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import billingDetails from "./components/billingDetails.vue";
import regionalRollup from "./components/regionalRollup.vue";
import storeSummary from "./components/storeSummary.vue";
import storeSummaryByMonth from "./components/storeSummaryByMonth.vue";
import fenxiaoSummaryByMonth from "./components/fenxiaoSummaryByMonth.vue";
import erpExport from "./components/erpExport.vue";
import erpExportALL from "./components/erpExportALL.vue";
import erpExportALLWeight from "./components/erpExportALLWeight.vue";
import expressGoodsWeight from "./components/expressGoodsWeight.vue";
import ExpressCompanyMonthlySummary from "./components/ExpressCompanyMonthlySummary.vue";
import erpExportReview from "./components/erpExportReview.vue";
import wanData from "./components/wanData.vue";
import expressLockList from "./components/expressLockList.vue";
import cancellationOrder from "./components/cancellationOrder.vue";
import shopConfiguration from "./components/shopConfiguration.vue";
import expressDistributor from "./components/expressDistributor.vue";
import courierCompaniesCollect from "./components/courierCompaniesCollect.vue";
import courierCompaniesCollectNoYiWuCang from "./components/courierCompaniesCollectNoYiWuCang.vue";
import warehouseAreaSummary from "./components/warehouseAreaSummary.vue";
import warehouseAreaDisposition from "./components/warehouseAreaDisposition.vue";
import warehouseTrendChart from "./components/warehouseTrendChart.vue";
import agentExpressDeliveryDisposition from "./components/agentExpressDeliveryDisposition.vue";
import agentStoreSummary from "./components/agentStoreSummary.vue";
import shopChangeHistory from "./components/shopChangeHistory.vue";

export default {
  name: "processingIndex",
  components: {
    MyContainer, billingDetails, regionalRollup, storeSummary, erpExport, erpExportReview, wanData, cancellationOrder, shopConfiguration, storeSummaryByMonth
    , fenxiaoSummaryByMonth, courierCompaniesCollect,courierCompaniesCollectNoYiWuCang, warehouseAreaSummary, warehouseAreaDisposition
    , warehouseTrendChart,expressLockList,erpExportALL,expressGoodsWeight,erpExportALLWeight,ExpressCompanyMonthlySummary, agentExpressDeliveryDisposition, agentStoreSummary
    ,expressDistributor,shopChangeHistory
  },
  data() {
    return {
      activeName: "first4",
    };
  },
  methods: {
    onToggle(e) {
      this.$nextTick(() => {
        if (e.name === 'first14') {
          this.$refs.refwarehouseTrendChart.getList()
        }
      })
    },
  },

};
</script>

<style lang="scss" scoped></style>
