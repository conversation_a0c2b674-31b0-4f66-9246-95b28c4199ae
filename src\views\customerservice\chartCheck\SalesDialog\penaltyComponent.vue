<template>
  <div style="border:1px #f2f4f5 solid;  border-top: none;padding:20px;">
    <div style="height: 500px; overflow: auto" v-loading="isLoading">
      <!-- <div v-if="!interfaceType">
             <el-descriptions size="middle" class="margin-top" title="" :column="3">
              <el-descriptions-item label="平台/店铺"> {{platformName}} — {{dataJson.shopName}}</el-descriptions-item>
              <el-descriptions-item label="违规单号">{{violationOrderNo}}</el-descriptions-item>
              <el-descriptions-item label="违规时间">{{violationTime }}</el-descriptions-item>
            </el-descriptions>
      </div>
      <div v-else>
             <el-descriptions size="middle" class="margin-top" title="" :column="3">
              <el-descriptions-item label="平台/店铺"> {{platformName}} — {{dataJson.shopName}}</el-descriptions-item>
              <el-descriptions-item label="线上订单号">{{violationOrderNo}}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{violationTime }}</el-descriptions-item>
            </el-descriptions>
      </div> -->
      <div class="tipColor" v-if="!interfaceType"> 违规判定： {{ platformPunishMsg }} </div>
      <br />
      <div v-if="isShow">
        <div class="message-container" v-for="(message, index) in chartList" :key="index"
          :class="getMessageClass(message.userType)">
          <div v-if="message.userType == 3">
            <div class="system-box">
              <div class="system">
                <div>系统消息{{ chartList.length }}</div>
                <div style="margin-left: 20px">{{ message.recordTime }}</div>
              </div>
              <div style="color: #333">{{ message.content }}</div>
            </div>
          </div>
          <div v-if="message.userType == 0" style=" width:95%">
            <el-row>
              <el-col :span="1">
                <img src="@/assets/images/消费者.png" />
              </el-col>
              <el-col :span="23">
                <div class="bubble">
                  <div style="display: flex; color: #999;margin-bottom: 5px;">
                    <div>
                      <span class="consumer">消费者</span>
                      <span class="username"> {{ message.userName }}</span>
                    </div>
                    <div class="recordTime" style="margin-left: 10px">
                      {{ message.recordTime }}
                    </div>
                  </div>
                  <div v-if="isProbablyImage(message.content)">
                    <el-image :src="message.content" :preview-src-list="[message.content]"></el-image>
                  </div>
                  <div v-else class="message msg_consumer" style="text-align: left" v-html="message.content"></div>
                </div>
              </el-col>
            </el-row>

          </div>
          <div v-if="message.userType == 1" style=" width:100%">
            <el-row>
              <el-col :span="1">
                <img src="@/assets/images/商家.png" />
              </el-col>
              <el-col :span="23">
                <div class="bubble">
                  <div style="display: flex; color: #999;margin-bottom: 5px;">
                    <div>
                      <span class="merchant">商家</span>
                      <span class="username"> {{ message.userName }}</span>
                    </div>
                    <div class="recordTime" style="margin-left: 10px">
                      {{ message.recordTime }}
                    </div>
                  </div>
                  <div v-if="isProbablyImage(message.content)">
                    <el-image style="object-fit: cover;" :src="message.content"
                      :preview-src-list="[message.content]"></el-image>
                  </div>
                  <div v-else class="message msg_merchant" v-html="message.content"></div>
                </div>
              </el-col>
            </el-row>
            <!-- <el-row >
                <el-col :span="23" >
                    <div class="bubble2">
                        <div style="display: flex;align-items: center; margin-bottom: 5px;justify-content: flex-end; ">
                            <div style="margin-rigth: 10px" class="recordTime">
                              {{ message.recordTime }}
                            </div>
                            <div class="name">
                              <span class="username">{{ message.userName }}</span>
                              <span class="merchant">商家</span>
                            </div>
                        </div>
                        <div class="avatar">
                            <div v-if="isProbablyImage(message.content)">
                                <el-image style="object-fit: cover;" :src="message.content" :preview-src-list="[message.content]"></el-image>
                            </div>
                            <div v-else class="message msg_merchant" v-html="message.content"></div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="1">
                    <img src="@/assets/images/商家.png" />
                </el-col>
              </el-row> -->
          </div>
          <div v-if="message.userType == 2">
          <div style="display: flex;align-items: left; color: #999;margin-bottom: 5px;">
            <div class="name">
              {{ message.userName }}
            </div>
            <div style="margin-left: 10px;line-height: 29px;">
              {{ message.recordTime }}
            </div>
          </div>
          <div class="avatar">
            <div v-if="isProbablyImage(message.content)">
              <el-image style="width: 500px; height: 500px" :src="message.content" :preview-src-list="[message.content]"></el-image>
            </div>
            <div v-else class="message" style="margin-right: 10px; text-align: left; color: #333" v-html="message.content"></div>
          </div>
        </div>

        </div>
      </div>
    </div>
    <my-pagination slot="footer" ref="chartPager" :total="chartTotal" @get-page="getChartList" />
  </div>
</template>
<script>
import { formatTime } from "@/utils";

import { getPlatformPunishChatRecordsPageList, getReplyCarelesslyChatRecordsPageList } from "@/api/customerservice/chartCheck";


export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chartList: [],
      chartTotal: 0,
      isLoading: false,
      interfaceType: false,// true：敷衍 false：平台批判
      pagerData: {},
      total: 0,
      platformPunishMsg: null,//提示
      dataJson: null,
    };
  },
  watch: {
    dataJson(newV, oldVal) {
      if (newV) {
        this.$nextTick(() => {
          this.$refs.chartPager.setPage(1);
          this.getChartList();
        });
      }
    }
  },
  mounted() {
  },
  computed: {
    platformName()//平台初始化
    {
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
      console.log(this.dataJson);
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
      } else {
        return ""
      }
    },
    violationTime() //日期转换
    {
      if (this.interfaceType) return this.dataJson.insertTime ? formatTime(this.dataJson.insertTime, "YYYY-MM-DD") : ""
      else return this.dataJson.violationTime ? formatTime(this.dataJson.violationTime, "YYYY-MM-DD") : ""
    },
    violationOrderNo() {
      if (this.interfaceType) return this.dataJson.orderNo
      else return this.dataJson.violationOrderNo
    },
  },
  methods: {
    async getChartList(a, b) {
      this.isShow = false;
      this.isLoading = true;
      var pager = this.$refs.chartPager.getPager();
      var params = {
        platform: this.dataJson.platform,
        platformPunishId: this.dataJson.id,
        replyCarelesslyId: this.dataJson.id,
        pageSize: pager.pageSize,
        currentPage: pager.currentPage,
        dataJson: null,
        orderBy: "",
        isAsc: false
      };
      const res = this.interfaceType ? await getReplyCarelesslyChatRecordsPageList(params) : await getPlatformPunishChatRecordsPageList(params);
      //console.log(111);
      this.platformPunishMsg = !this.interfaceType ? res.data.extData.platformPunishMsg : "";
      this.chartTotal = res.data.total;
      this.chartList = res.data.list;
      this.isShow = true;
      this.isLoading = false;
    },
    //消息框样式动态选择
    getMessageClass(isSent) {
      let className = ""
      switch (isSent) {
        case 0:// 用户
          className = "message-container-left"
          break;
        case 1:// 客服
          className = "message-container-right"
          break;
        case 2:// 机器人
          className = "message-container-right"
          break;
        case 3:// 系统
          className = "message-container-left"
          break;
      }
      return className
      // return isSent != 0 && isSent != 3 ? "message-container-right" : "message-container-left";
    },
    isProbablyImage(url) {
      return (url.match(/\.(jpeg|jpg|gif|png)$/) != null)
    },
    onSubmitDot() {
      this.closeDialog();
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
  },
};
</script>

<style scoped lang="scss">
.tipColor {
  color: red;
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-left: 10px !important;
  padding-bottom: 0px !important;
  line-height: 3;
}

::v-deep .el-descriptions__body .el-descriptions__table {
  background-color: rgb(242, 244, 245);
}

.msg_merchant {
  padding: 10px;
  color: white;
  border-radius: 5px;
  background-color: rgb(64, 158, 255);
}

.msg_consumer {
  padding: 10px;
  border-radius: 5px;
  background-color: rgb(240, 246, 255);
}

.system-box {
  background-color: #fafafa;
  padding: 10px;
  box-sizing: border-box;
  width: 300px;
}

.system {
  display: flex;
  margin-bottom: 4px;
  color: #999;
}

.message-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.name {
  text-align: right;
  margin: 5px 0;
  color: #999;
}

.avatar {
  float: right;
  margin-right: 0px;
  /* 修改这里将头像放在消息框的右边 */
  display: flex;
  align-items: center;
  text-align: right
}

.avatar-image {
  width: 400px;
  height: 400px;
  // object-fit: cover;
}

.bubble {
  color: #000;
  border-radius: 5px;
  padding-left: 10px;
  padding-bottom: 10px;
}

.bubble2 {
  color: #000;
  border-radius: 5px;
  padding-right: 10px;
}

.message {
  text-align: left;
  margin: 0;
  width: 400px;
}

// .message-container-right {
//   padding-left: 10px;
// }

.message-container-left {
  justify-content: flex-start;
}

.merchant {
  color: rgb(88, 170, 255) !important;
  border: 1px rgb(88, 170, 255) solid;
}

.consumer {
  color: red !important;
  border: 1px red solid;
}

.username {
  color: black !important;
  margin-right: 5px;
  margin-left: 5px;
}

.recordTime {
  color: gray !important;
}

::v-deep .el-image .el-image__inner {
  max-width: 500px !important;
  max-height: 1000px !important;
  height: auto;
  width: 500px !important;
}
</style>
