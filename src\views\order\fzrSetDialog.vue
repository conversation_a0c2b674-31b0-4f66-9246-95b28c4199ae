<template>
  <div style="width: 100%;height: 100%;">
    <div class="setBox">
      <fieldset class="setItem">
        <legend style="font-weight: 700;font-size: 16px;">负责人:</legend>
        <el-radio-group v-model="submitForm.ownerType" @input="changeRadio">
          <el-radio :label="1" style="font-weight: 700;" :disabled="!ownerTypeList?.includes(1)">运维</el-radio>
          <el-radio :label="2" style="font-weight: 700;" :disabled="!ownerTypeList?.includes(2)">负责人2</el-radio>
          <el-radio :label="3" style="font-weight: 700;" :disabled="!ownerTypeList?.includes(3)">负责人3</el-radio>
          <el-radio :label="4" style="font-weight: 700;" :disabled="!ownerTypeList?.includes(4)">负责人4</el-radio>
          <el-radio :label="5" style="font-weight: 700;" :disabled="!ownerTypeList?.includes(5)">负责人5</el-radio>
        </el-radio-group>
        <div style="display: flex;margin:10px 0">
          <el-input v-model="input" placeholder="请输入负责人姓名或手机号" style="width: 220px;margin-right: 10px;" clearable
            maxlength="50"></el-input>
          <el-button type="primary" @click="fzrSearch">搜索</el-button>
        </div>
        <el-table :data="personList" style="width: 100%" border height="510" v-loading="tableLoading"
          :sum-text="`共 ${personList.length} 条`">
          <el-table-column type="index" label="#" width="100" />
          <el-table-column prop="value1" label="负责人姓名" sortable>
          </el-table-column>
          <el-table-column prop="value2" label="负责人电话号码" sortable>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ row }">
              <el-button type="danger" @click="delFzr(row.key)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div>共{{ personList?.length }}条</div>
      </fieldset>
      <fieldset class="setItem">
        <div class="sticky-header">
          <el-input v-model="permission" clearable maxlength="50" placeholder="请输入权限"
            style="width: 220px;margin-right: 10px;"></el-input>
        </div>
        <legend style="font-weight: 700;font-size: 16px;">权限:</legend>
        <h3> 列表可视列 </h3>
        <el-tree :data="tableCols1" :props="defaultProps" show-checkbox node-key="prop" check-on-click-node
          :filter-node-method="filterProps" ref="columnPermissionListsRef" default-expand-all />
        <h3>弹窗权限 </h3>
        <el-tree :data="echartsList1" :props="defaultProps" show-checkbox node-key="value"
          :filter-node-method="filterProps" ref="dialogPermissionListsRef" default-expand-all check-on-click-node />
        <h3>搜索权限 </h3>
        <el-tree :data="searchList1" :props="defaultProps" show-checkbox node-key="prop"
          :filter-node-method="filterProps" ref="searchPermissionListsRef" default-expand-all check-on-click-node />
      </fieldset>
    </div>
    <div style="margin-top: 30px;display: flex;justify-content: center;">
      <el-button style="margin-right: 10px;" @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="submit" v-throttle="2000"
        v-if="checkPermission('styleCodeReports_yw_submit')">确定</el-button>
    </div>
  </div>
</template>

<script>
import {
  deleteSeriesMainOwnerById,
  saveSeriesCodeOwnersPermissionsSet,
  getSeriesCodeOwnersPermissionsSet,
  getSeriesMainOwnerLists,
  ValidateUserIsOwnerLeader
} from "@/api/bookkeeper/styleCodeRptData";
export default {
  props: {
    echartsList: {
      type: Array,
      default: () => []
    },
    tableCols: {
      type: Array,
      default: () => []
    },
    searchList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'cols',
        label: 'label'
      },
      permission: '',
      echartsList1: [],
      tableCols: [],
      tableCols1: [],
      searchList1: [],
      input: '',
      personList: [],
      submitForm: {
        ownerType: 1,
        columnPermissionLists: [],
        searchPermissionLists: [],
        dialogPermissionLists: []
      },
      lastType: 1,
      loading: false,
      historyData: {},
      closChecked: false,
      dialogChecked: false,
      searchChecked: false,
      ownerTypeList: []
    }
  },
  created() {
    const res = JSON.parse(JSON.stringify(this.tableCols))
    res.push(
      {
        width: '120', align: 'center', fixed: 'right', label: '操作', prop: 'operate', type: 'button', cols: [
          { label: "趋势图", prop: 'echarts', handle: (that, row) => that.showDetailAnalysis(row, 1, true) },
          { label: "备忘录", prop: 'memorandum', handle: (that, row) => that.memorandumDialog(row) }
        ]
      }
    )
    const cols = [
      {
        label: '',
        cols: res
      }
    ]
    this.$set(this, 'tableCols1', cols);
    const res1 = JSON.parse(JSON.stringify(this.searchList))
    const cols1 = [
      {
        label: '',
        cols: res1
      }
    ]
    this.$set(this, 'searchList1', cols1);
    const res2 = JSON.parse(JSON.stringify(this.echartsList))
    const cols2 = [
      {
        label: '',
        cols: res2
      }
    ]
    this.$set(this, 'echartsList1', cols2);
  },
  watch: {
    permission(val) {
      this.$refs.columnPermissionListsRef.filter(val);
      this.$refs.searchPermissionListsRef.filter(val);
      this.$refs.dialogPermissionListsRef.filter(val);
    }
  },
  async mounted() {
    this.getValidateUserIsOwnerLeader()
  },
  methods: {
    async getValidateUserIsOwnerLeader() {
      const { data, success } = await ValidateUserIsOwnerLeader();
      if (!success) return
      this.ownerTypeList = data
      this.$set(this.submitForm, 'ownerType', (data && data.length > 0) ? data[0] : 1)
      if (data?.length == 0) return
      await this.getFzrPermission()
      await this.fzrSearch()
    },
    filterProps(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    async fzrSearch() {
      this.loading = true
      const { data, success } = await getSeriesMainOwnerLists({ ownerType: this.submitForm.ownerType, userName: this.input });
      if (!success) return
      this.personList = data;
      this.loading = false
    },
    async getFzrPermission() {
      const { data, success } = await getSeriesCodeOwnersPermissionsSet({ ownerType: this.submitForm.ownerType });
      if (!success) return
      this.$set(this, 'submitForm', data);
      this.$refs.columnPermissionListsRef.setCheckedKeys(data.columnPermissionLists ? data.columnPermissionLists : []);
      this.$refs.searchPermissionListsRef.setCheckedKeys(data.searchPermissionLists ? data.searchPermissionLists : []);
      this.$refs.dialogPermissionListsRef.setCheckedKeys(data.dialogPermissionLists ? data.dialogPermissionLists : []);
      this.historyData = JSON.parse(JSON.stringify(data));
    },
    delFzr(userId) {
      this.$confirm('此操作将永久删除该负责人, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteSeriesMainOwnerById({ ownerType: this.submitForm.ownerType, userId });
        if (!success) return
        await this.fzrSearch()
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch((error) => {
        console.log(error, 'error');
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    changeRadio(e) {
      if (this.lastType != e) {
        this.$confirm('切换负责人会将之前未保存的权限清空! 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.loading = true
          this.lastType = e;
          await this.getFzrPermission()
          await this.fzrSearch()
          this.$message({
            type: 'success',
            message: '切换成功!'
          });
          this.loading = false
        }).catch((error) => {
          console.log(error, 'error');
          this.submitForm.ownerType = this.lastType
          this.$message({
            type: 'info',
            message: '已取消'
          });
          this.loading = false
        });
      }
    },
    async submit() {
      this.$set(this.submitForm, 'columnPermissionLists', this.$refs.columnPermissionListsRef.getCheckedKeys());
      this.$set(this.submitForm, 'searchPermissionLists', this.$refs.searchPermissionListsRef.getCheckedKeys());
      this.$set(this.submitForm, 'dialogPermissionLists', this.$refs.dialogPermissionListsRef.getCheckedKeys());
      const { success } = await saveSeriesCodeOwnersPermissionsSet([this.submitForm])
      if (success) {
        this.$message({
          type: 'success',
          message: '保存成功!'
        });
      }
    },
  }
}
</script>

<style scoped lang="scss">
.setBox {
  display: flex;
  width: 100%;
  height: 95%;

  .setItem {
    display: 1;
    height: 100%;
    width: 50%;
    overflow-y: auto;
    position: relative;

    .sticky-header {
      display: flex;
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      background-color: #fff;
      z-index: 99;
    }
  }
}

.checkBoxItem {
  width: 90px;
  white-space: nowrap;
  overflow: hidden;
  // text-overflow: ellipsis;
}

::v-deep .el-tree-node__label {
  font-size: 18px;
}
</style>