<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="创建时间" endPlaceholder="创建时间" />
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <el-input v-model.trim="ListInfo.internalOrderNo" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.buyUserId" placeholder="买家ID" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.createdUserName" placeholder="创建人" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.analysisError" placeholder="状态" class="publicCss" clearable>
                    <el-option label="异常" :value="true" />
                    <el-option label="正常" :value="false" />
                </el-select>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="importProps" v-if="checkPermission('yggrmImport')">导入</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                <!-- <el-button type="primary" @click="handleAdd">新增</el-button> -->
            </div>
        </template>
        <vxetablebase :id="'tmzdQuotePage202408041633'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :toolbarshow="false" @sortchange='sortchange' :tableData='tableData'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            v-loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="viewProps(row.id)">编辑</el-button>
                            <el-button type="text"
                                v-if="checkPermission('api:inventory:CustomNormsGoods:DeleteTMZDCSRecord')"
                                @click="tmzdDelete(row.id)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 校验买家id -->
        <el-dialog title="新增" :visible.sync="buyersIdVisable" width="15%" :close-on-click-modal="false" v-dialogDrag>
            <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
                <el-input v-model="buyUserInfo.buyUserId" placeholder="请输入买家ID" maxlength="50" clearable
                    style="width: 180px;" />
                <div class="btnGroup">
                    <el-button @click="buyersIdVisable = false">取消</el-button>
                    <el-button type="primary" @click="verifyBuyerId" v-throttle="2000">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 新增 -->
        <el-drawer :title="isView ? '查看' : '新增'" :visible.sync="drawer" direction="rtl" size="90%"
            :wrapperClosable="false">
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                style="padding: 16px;box-sizing: border-box;" :rules="rules">
                <el-form-item label="买家id:" prop="pass">
                    <div>{{ ruleForm.buyUserId }}</div>
                </el-form-item>
                <!-- <el-form-item label="聊天记录:" prop="chatPicUrl">
                    <div class="chatPicUrl">
                        <uploadimgFile v-if="drawer" ref="uploadimgFile" :ispaste="!isView"
                            :noDel="isView || (chatUrls.length == 10)" :accepttyes="accepttyes" :isImage="true"
                            :uploadInfo="chatUrls" :keys="[1, 1]" @callback="getImg" :imgmaxsize="10" :limit="10"
                            :multiple="true">
                        </uploadimgFile>
                        <span class="picTips" v-if="!isView">提示:点击灰框可直接贴图！！！</span>
                    </div>
                </el-form-item> -->
                <el-form-item label="订单备注:" prop="orderRemark">
                    <el-tooltip class="item" effect="dark" :content="ruleForm.orderRemark" placement="top-start">
                        <div class="orderRemark">{{ ruleForm.orderRemark }}</div>
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="规格报价明细:" prop="pass">
                    <span style="color: red;">(禁止截图给客户！！！)</span>
                    <el-button style="margin-left: 20px;" @click="addProps" type="primary">新增一行</el-button>
                </el-form-item>
                <div style="height: 400px;">
                    <el-table :data="ruleForm.dtls" style="width: 100%;height:100%" max-height="450">
                        <el-table-column prop="customType" label="款式" width="75">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>款式
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.customType" placeholder="款式"
                                    @change="changeType($event, $index)">
                                    <el-option label="定制款" :value="1" />
                                    <el-option label="常规款" :value="2" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="norms" label="规格" width="150">
                            <template #header="{ column }">
                                <span style="color: #F56C6C; margin: 0 7px 0 -11px">*</span>规格
                            </template>
                            <template #default="{ row, $index }">
                                <el-select v-model="row.norsId" placeholder="规格" @change="(val) => {
                                    if (row.customType == 1) {
                                        row.norsItem = row.normsList.find(a => a.id == val);
                                    } else if (row.customType == 2) {
                                        row.norsItem = row.normsList.find(a => a.goodsCode == val);
                                    }
                                    if (row.norsItem) {
                                        row.isEdgeGrinding = row.norsItem.isEdgeGrinding
                                        row.type = row.norsItem.type
                                    }
                                    Cpt();
                                }" filterable remote :remote-method="remoteMethod($index)" v-loading="selLoading">
                                    <!-- 定制款规格 -->
                                    <el-option v-for="item in row.normsList"
                                        :key="item.norms + '-' + item.type + '-' + (item.isEdgeGrinding ? '磨边' : '不磨边')"
                                        :label="row.customType == 1 ? item.norms + ' - ' + item.type + ' - ' + (item.isEdgeGrinding ? '磨边' : '不磨边') : item.norms"
                                        :value="row.customType == 1 ? item.id : item.goodsCode" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isDK" label="类型" width="70">
                            <template #default="{ row, $index }">
                                <el-select v-model="row.type" placeholder="类型" @change="Cpt"
                                    :disabled="row.customType == 2">
                                    <el-option label="磨砂" value="磨砂" v-show="row.norms != 1" />
                                    <el-option label="透明" value="透明" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetLength" label="长(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetLength" :min="0" :max="10000" :precision="4"
                                    :disabled="row.customType == 2" :controls="false" label="长(米)" class="iptCss"
                                    @change="Cpt" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetWidth" label="宽(米)" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetWidth" :min="0" :max="10000" :precision="4"
                                    :disabled="row.customType == 2" :controls="false" label="宽(米)" class="iptCss"
                                    @change="Cpt" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetCount" label="张" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="张" class="iptCss" @change="Cpt" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="totalAmount" label="成本" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.totalCost" :min="0" :precision="4" :controls="false"
                                    label="成本" class="iptCss" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" width="auto">
                            <template #default="{ row }">
                                <el-input v-model="row.remark" maxlength="50" clearable placeholder="备注" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderFreightBagFee" label="操作" width="140">
                            <template #default="{ row, $index }">
                                <div style="display: flex;">
                                    <el-button style="margin-left: 10px;" type="warning"
                                        @click="batchSet(row)">复制</el-button>
                                    <el-button type="danger" @click="delProps($index)">删除</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-form-item label="合计:" prop="pass">
                    <div>{{ ruleForm.totalCost }}</div>
                </el-form-item>
                <el-form-item label="实际报价:" prop="pass">
                    <el-input-number v-model="ruleForm.actualTotalAmount" :min="0" :max="9999999" :precision="2"
                        :controls="false" label="实际报价" class="iptCss" />
                </el-form-item>
                <div style="display: flex;justify-content: end;margin-top: 100px;">
                    <el-button type="primary" @click="submitForm">确认修改</el-button>
                    <!-- <el-button type="primary" @click="openOrder" >订单提交</el-button>
                    <el-button type="primary" @click="submitForm(false)" v-throttle="2000"
                        >非订单提交</el-button>
                    <el-button type="primary" @click="copyProps('bj')" :disabled="false">复制报价</el-button>
                    <el-button type="primary" @click="copyProps('bz')" :disabled="false">复制加工备注</el-button> -->
                </div>
            </el-form>
        </el-drawer>

        <el-dialog :visible.sync="copyVisable" width="15%" height="100">
            <div style="margin-left: 40px;">
                复制<el-input-number style="width: 40px;" v-model="copyRowCount" :min="0" :max="15" :precision="0"
                    :controls="false" />条
                <el-button style="margin-left: 10px;" type="primary" @click="doCopy">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="订单提交" :visible.sync="orderVisable" width="15%" :close-on-click-modal="false" v-dialogDrag>
            <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
                <el-input v-model="ruleForm.orderNo" placeholder="请输入订单号" maxlength="50" clearable
                    style="width: 180px;" />
                <div class="btnGroup">
                    <el-button @click="orderVisable = false">取消</el-button>
                    <el-button type="primary" @click="submitForm(true)" v-throttle="2000">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-date-picker v-model="importTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始时间" end-placeholder="结束时间" :picker-options="pickerOptions"
                    style="width: 250px;margin:0 10px 10px 0;" :value-format="'yyyy-MM-dd'" @change="importChangeTime">
                </el-date-picker>
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    getTMZDCSRecordPageList,
    getCostSetByTMZDCache,
    getTMZDCSRecordByBuyUserId,
    saveTMZDCSRecord,
    getTMZDCSRecordById,
    importCustomMadeMultipleAsync,
    exportTMZDCSRecordPageList,
    deleteTMZDCSRecord
} from '@/api/inventory/customNormsGoods'
import decimal from '@/utils/decimal'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyUserId', label: '买家ID', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'internalOrderNo', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '创建人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'payTime', label: '付款时间', },
    { sortable: 'custom', width: '65', align: 'center', prop: 'totalSheetCount', label: '总张数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalCost', label: '总成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualTotalAmount', label: '实际报价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'analysisError', label: '状态', formatter: (row) => row.analysisError == false ? '正常' : '异常' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipmentStatus', label: '订单状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updatedUserName', label: '修改人', },
]
const ys = {
    norsId: '规格',
    type: '类型',
    sheetLength: '长',
    sheetWidth: '宽',
    sheetCount: '张',
    totalAmount: '售价',
}
const ggList = [
    {
        label: '1.0',
        value: 0.4
    },
    {
        label: '1.5',
        value: 0.7
    },
    {
        label: '2.0',
        value: 1.1
    },
    {
        label: '3.0',
        value: 1.4
    },
    {
        label: '5.0',
        value: 2.2
    },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile, dateRange
    },
    data() {
        return {
            ggList,
            ys,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                payStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),//付款开始时间
                payEndTime: dayjs().format('YYYY-MM-DD'),//付款结束时间
                buyUserId: null,//买家ID
                orderNo: null,//订单号
                createdUserName: null,//创建人
                internalOrderNo: null,//内部订单号
            },
            timeRanges: [],
            payTimeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            buyersIdVisable: false,
            buyUserInfo: {
                buyUserId: null,
                id: null
            },
            drawer: false,
            ruleForm: {
                buyUserId: null,//买家id
                orderNo: null,//订单号
                totalAmount: null,//总价
                actualTotalAmount: null,//实际总价
                // chatPicUrl: null,//聊天记录
                orderWeight: 0,//快递重量
                totalSheetCount: 0,//总张数
                weight: 0,//重量
                cost: 0,//成本(不含快递+打包)
                totalCost: 0,//成本(含快递+打包)
                dtls: [
                    {
                        norsId: null,
                        customType: null,
                        norms: null,//规格
                        type: null,//类型
                        sheetWidth: null,//单张宽
                        sheetLength: null,//单张长
                        sheetCount: null,//张数
                        totalAmount: null,//售价
                        remark: null,//备注
                        normsList: null,
                        norsItem: null,
                    }
                ]
            },
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            isView: false,
            catchList: [],
            orderVisable: false,
            rules: {
                // chatPicUrl: [
                //     { required: true, message: '请上传聊天记录', trigger: 'change' }
                // ]
            },
            index: 0,
            normsList: [],
            selLoading: false,
            timer: null,
            importVisible: false,
            importLoading: false,
            fileList: [],
            importTimeRanges: [],
            // chatPicUrl: []
            importTime: {
                payStartTime: null,
                payEndTime: null
            },
            isExport: false,
            copyRow: {},
            copyRowCount: 0,
            copyVisable: false,
        }
    },

    async mounted() {
        await this.getList()
    },
    methods: {
        async exportProps() {
            this.isExport = true
            await exportTMZDCSRecordPageList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '透明桌垫报价测算' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        importChangeTime(e) {
            this.importTime.payStartTime = e ? e[0] : null
            this.importTime.payEndTime = e ? e[1] : null
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importTime.payStartTime || !this.importTime.payEndTime) return this.$message.error('请选择时间')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("payStartTime", this.importTime.payStartTime);
            form.append("payEndTime", this.importTime.payEndTime);
            this.importLoading = true
            await importCustomMadeMultipleAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.importTimeRanges = []
            this.importTime = {
                payStartTime: null,
                payEndTime: null
            }
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        remoteMethod(i) {
            let row = this.ruleForm.dtls[i]
            let that = this
            return (name) => {
                if (!name) return row.normsList = []
                clearTimeout(this.timer);
                this.timer = setTimeout(() => {
                    row.normsList = []
                    getCostSetByTMZDCache({ name, type: row.customType })
                        .then(res => {
                            if (res.success) {
                                row.normsList = res.data
                                that.$set(that.ruleForm.dtls, i, row)
                            }
                        })
                        .catch(err => {
                            that.$set(row, 'normsList', [])
                        });
                }, 100);
            }
        },
        changeType(val, i) {
            //清空其他值
            this.ruleForm.dtls[i].sheetLength = 0
            this.ruleForm.dtls[i].sheetWidth = 0
            this.ruleForm.dtls[i].sheetCount = 0
            this.ruleForm.dtls[i].totalAmount = 0
            this.ruleForm.dtls[i].remark = null
            this.ruleForm.dtls[i].goodsCode = null
            this.ruleForm.dtls[i].norsItem = null
            this.ruleForm.dtls[i].norms = null
            this.ruleForm.dtls[i].type = null
            this.ruleForm.dtls[i].normsList = []
            this.ruleForm.dtls[i].norsId = null
            this.Cpt()
        },
        async delProps(i) {
            this.ruleForm.dtls.splice(i, 1)
            await this.Cpt()
        },
        Cpt() {
            this.ruleForm.dtls.forEach((item, i) => {
                if (!item.norsItem) {
                    item.norsItem = {
                        sheetLength: 0,//单张长
                        sheetWidth: 0,//单张宽
                        sheetCount: 0,//张数
                        totalCost: 0,//单行成本
                        packProcessCost: 0,//打包费
                        proportion: 0,//比重
                        kgPriceCost: 0,//公斤价格
                        croppingCost: 0,//裁切费
                        actualLand: 0,//实际厚度
                        goodsCode: '',//商品编码
                        pf: 0,//平方
                        weight: 0,//重量
                        totalCost: 0,//单行成本
                        croppingCostTotal: 0,//裁切费总计
                        packProcessCostFt: 0,//打包费分摊
                        totalAmount: 0,//售价
                        cost: 0,
                    }
                }
                let res = item.norsItem
                if (res && item.customType == 1) {
                    item.packProcessCost = res.packProcessCost //打包费
                    item.proportion = res.proportion //比重
                    item.kgPriceCost = res.kgPriceCost //公斤价格
                    item.croppingCost = res.croppingCost //裁切费
                    item.actualLand = res.actualLand //实际厚度
                    item.goodsCode = res.goodsCode//商品编码
                    item.pf = decimal(decimal(item.sheetLength, item.sheetWidth, 4, '*'), item.sheetCount, 4, '*')//平方
                    item.weight = decimal(decimal(item.pf, item.actualLand, 4, '*'), item.proportion, 4, '*')//重量 
                    item.totalCost = decimal(item.weight, item.kgPriceCost, 4, '*')//单行成本
                    item.croppingCostTotal = decimal(item.sheetCount, item.croppingCost, 4, '*')//裁切费总计
                    item.totalCost = decimal(item.totalCost, item.croppingCostTotal, 4, '+')//售价
                } else if (res && item.customType == 2) {
                    item.goodsCode = res.goodsCode
                    item.totalCost = decimal(item.sheetCount, res.cost, 4, '*')
                } else {
                    item.packProcessCost = 0 //打包费
                    item.proportion = 0 //比重
                    item.kgPriceCost = 0 //公斤价格
                    item.croppingCost = 0 //裁切费
                    item.actualLand = 0 //实际厚度
                    item.goodsCode = ''//商品编码
                    item.pf = 0 //平方
                    item.weight = 0
                    item.croppingCost = 0
                    item.totalCost = 0
                }
            })
            let packProcessList = []
            this.ruleForm.dtls.forEach(item => {
                if (item.customType == 1) {
                    packProcessList.push(Number(item.packProcessCost))
                }
            })//打包费数组
            //打包费最大值
            const maxPackProcessCost = Math.max(...packProcessList);
            let costTotal = this.ruleForm.dtls.reduce((total, item) => total = item.customType == 1 ? decimal(total, item.totalCost, 4, '+') : total, 0) || 0//总成本(除去常规款)
            this.ruleForm.dtls.forEach((item, i) => {
                let packProcessCostFt = 0
                //如果定制款总成本,那就计算定制款分摊
                if (costTotal != 0 && item.customType == 1) {
                    packProcessCostFt = decimal(maxPackProcessCost, decimal(item.totalCost, costTotal, 4, '/'), 4, '*') //打包费分摊 = 明细中最大的打包费 * (单行成本 / 总成本)
                    this.ruleForm.dtls[i].packProcessCostFt = packProcessCostFt//打包费分摊
                    // this.ruleForm.dtls[i].totalCost = decimal(item.totalCost, packProcessCostFt, 4, '+')//成本(含成本+打包(分摊)+裁剪费总和(张数*单张裁剪)) 单行
                } else {
                    this.ruleForm.dtls[i].packProcessCostFt = 0
                }
            })
            //总打包费分摊
            let packProcessCostTotal = 0
            this.ruleForm.dtls.forEach(item => {
                //计算定制款的打包费分摊总和
                if (item.customType == 1) {
                    packProcessCostTotal = decimal(packProcessCostTotal, item.packProcessCostFt, 4, '+')
                }
            })
            //如果定制款打包费总和不等于最大的定制款打包费,则对最大的行的打包费分摊进行调整
            if (packProcessCostTotal != maxPackProcessCost) {
                const maxPackProcess = this.ruleForm.dtls.reduce((total, item) => total > item.packProcessCostFt ? total : item.packProcessCostFt, 0)//最大的打包费分摊
                const maxIndex = this.ruleForm.dtls.findIndex(item => item.packProcessCostFt == maxPackProcess)
                if (maxIndex >= 0) {
                    //用最大打包费减去打包费总和
                    let a = decimal(maxPackProcessCost, packProcessCostTotal, 4, '-')
                    //给最大的行的打包费分摊加上差值
                    let b = decimal(this.ruleForm.dtls[maxIndex].packProcessCostFt, a, 4, '+')
                    this.ruleForm.dtls[maxIndex].packProcessCostFt = b
                }
            }
            //计算定制款含打包费成本以及出仓成本
            this.ruleForm.dtls.forEach(item => {
                if (item.customType == 1) {
                    item.totalCost = decimal(item.totalCost, item.packProcessCostFt, 4, '+')//成本(含成本+打包+裁剪费总和(张数*单张裁剪)) 单行
                }
            })
            this.ruleForm.dtls.forEach(item => {
                item.totalAmount = decimal(item.totalCost, 1.7, 4, '*')
            })
            this.ruleForm.totalAmount = this.ruleForm.dtls.reduce((total, item) => decimal(total, item.totalAmount, 4, '+'), 0)
            this.ruleForm.totalCost = this.ruleForm.dtls.reduce((total, item) => decimal(total, item.totalCost, 4, '+'), 0)
        },
        openOrder() {
            this.ruleForm.orderNo = null
            this.orderVisable = true
        },
        async viewProps(id) {
            await this.getCacheList()
            this.buyUserInfo.id = id
            this.buyUserInfo.buyUserId = null
            const { data, success } = await getTMZDCSRecordById(this.buyUserInfo)
            if (success) {
                if (data) {
                    this.ruleForm = data
                    this.ruleForm.dtls = data.dtls.map(item => {
                        return {
                            norsId: item.customType == 1 ? (item.norsItem ? item.norsItem.id : null) : item.norsItem.goodsCode,
                            customType: item.customType ? item.customType : null,
                            norms: item.norms ? item.norms : null,//规格
                            type: item.type,//类型
                            sheetWidth: item.sheetWidth,//单张宽
                            sheetLength: item.sheetLength,//单张长
                            sheetCount: item.sheetCount,//张数
                            totalAmount: item.totalAmount,//售价
                            remark: item.remark,//备注
                            goodsCode: item.goodsCode,//商品编码
                            normsList: item.norsItem ? [item.norsItem] : [],
                            norsItem: item.norsItem,
                            totalCost: item.totalCost,
                        }
                    })
                }
                this.drawer = true
            }
        },
        copyProps(type) {
            this.validate()
            let textarea = document.createElement("textarea")
            let e = ''
            let totalSheetCount = this.ruleForm.dtls.reduce((total, item) => total + item.sheetCount, 0);
            if (type == 'bj') {
                this.ruleForm.dtls.forEach(item => {
                    e += `${item.type}${item.norms}定制#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,总共${item.sheetCount}张,售价:${item.totalAmount}\n`
                })
                e = e + `共${totalSheetCount}张,合计:${this.ruleForm.totalAmount}元,优惠后总价:${this.ruleForm.actualTotalAmount}元\n`
            } else {
                this.ruleForm.dtls.forEach(item => {
                    e += `${item.type}${item.norms}定制#${item.goodsCode ? item.goodsCode : ''}@${item.sheetLength}*${item.sheetWidth}*${item.sheetCount}*张#,总共${item.sheetCount}张,售价:${item.totalAmount},备注:${item.remark ? item.remark : ''}\n`
                })
                e = e + `共${totalSheetCount}张\n`
            }
            textarea.value = e
            textarea.readOnly = "readOnly"
            document.body.appendChild(textarea)
            textarea.select()
            let result = document.execCommand("copy")
            if (result) {
                this.$message({
                    message: '复制成功',
                    type: 'success'
                })
            }
            textarea.remove()
        },
        validate() {
            this.ruleForm.dtls.forEach((item, i) => {
                if (item.customType == 1 || item.customType == null) {
                    const arr = ['customType', 'norsId', 'type', 'sheetLength', 'sheetWidth', 'sheetCount', 'totalCost',]
                    const map = {
                        customType: '款式',
                        norsId: '规格',
                        type: '类型',
                        sheetLength: '长',
                        sheetWidth: '宽',
                        sheetCount: '张',
                        totalCost: '成本',
                    }
                    for (const key in item) {
                        if (arr.includes(key)) {
                            if (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0) {
                                this.$message.error(`第${i + 1}行${map[key]}不能为空或者小于0`)
                                throw new Error(`第${i + 1}行${map[key]}不能为空或者小于0`)
                            }
                        }
                    }
                } else if (item.customType == 2) {
                    const arr = ['customType', 'norsId', 'sheetCount', 'totalCost']
                    const map = {
                        customType: '款式',
                        norsId: '规格',
                        sheetCount: '张数',
                        totalCost: '成本',
                    }
                    for (const key in item) {
                        if (arr.includes(key)) {
                            if (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0) {
                                this.$message.error(`第${i + 1}行${map[key]}不能为空或者小于0`)
                                throw new Error(`第${i + 1}行${map[key]}不能为空或者小于0`)
                            }
                        }
                    }
                }
            })
            if (this.ruleForm.actualTotalAmount <= 0 || this.ruleForm.actualTotalAmount === null || this.ruleForm.actualTotalAmount === undefined) {
                this.$message.error('实际报价不能为空或小于等于0')
                throw new Error('实际报价不能为空或小于等于0')
            }
        },
        async submitForm() {
            if (this.ruleForm.dtls.length == 0) return this.$message.error('至少添加一条数据')
            this.validate()
            const { data, success } = await saveTMZDCSRecord(this.ruleForm)
            if (success) {
                await this.getList()
                this.$message.success('提交成功')
                this.drawer = false
            } else {
                this.$message.error('提交失败')
            }
        },
        addProps() {
            this.ruleForm.dtls.push({
                customType: null,
                norsId: null,
                norms: null,//规格
                type: null,//类型
                sheetLength: 0,//单张长
                sheetWidth: 0,//单张宽
                sheetCount: 0,//张数
                totalAmount: 0,//售价
                remark: null,//备注
            })
        },
        changePrice(i) {
            //计算价格
            const item = this.ruleForm.dtls[i]
            if ((item.sheetLength !== null && item.sheetLength !== undefined) && (item.sheetWidth !== null && item.sheetWidth !== undefined) && (item.sheetCount !== null && item.sheetCount !== undefined) && (item.sheetSquareSaleAmount !== null && item.sheetSquareSaleAmount !== undefined)) {
                item.totalAmount = (item.sheetLength * item.sheetWidth * item.sheetCount * item.sheetSquareSaleAmount).toFixed(2)
            }
            //计算折后价
            if (item.totalAmount && item.discount) {
                item.actualAmount = (item.totalAmount * item.discount).toFixed(2)
            }
        },
        getNumber(num, type) {
            num = Math.floor(num * type) / type
            //如果根据type传入的值来判断截取多少位小数,使用Math,floor
            return math.number(math.format(num, { notation: 'fixed' }))
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.chatPicUrl = data.map(item => item.url).join(',')
            }
        },
        async verifyBuyerId() {
            this.buyUserInfo.id = null
            if (!this.buyUserInfo.buyUserId) return this.$message.error('请输入买家ID')
            this.ruleForm.buyUserId = this.buyUserInfo.buyUserId
            const { data, success } = await getTMZDCSRecordByBuyUserId(this.buyUserInfo)
            if (success) {
                if (data) {
                    this.ruleForm = data
                    this.ruleForm.dtls = data.dtls.map(item => {
                        //找出跟ys中的key相同的值,并返回一个数组
                        return {
                            norms: item.norms,//规格
                            type: item.type,//类型
                            sheetWidth: item.sheetWidth,//单张宽
                            sheetLength: item.sheetLength,//单张长
                            sheetCount: item.sheetCount,//张数
                            totalAmount: item.totalAmount,//售价
                            remark: item.remark,//备注
                            goodsCode: item.goodsCode,//商品编码
                        }
                    })
                    if (data.chatPicUrl.length > 0) {
                        this.chatUrls = data.chatPicUrl.split(',').map((item, i) => {
                            return {
                                url: item,
                                name: `聊天截图${i + 1}`
                            }
                        })
                    } else {
                        this.chatUrls = []
                    }
                    setTimeout(() => {
                        if (data.isChangePrice == 1) {
                            alert('部分规格信息价格产生变动,请仔细核对')
                        }
                    }, 100);
                }
                this.buyersIdVisable = false
                this.drawer = true

            } else {
                this.$message.error('校验失败')
            }
        },
        async getCacheList() {
            const { data, success } = await getCostSetByTMZDCache()
            if (success) {
                this.catchList = data
                //获取定制款的规格,并且去重
                this.dzList = data.filter(item => item.customType == 1)
                this.dzList = this.dzList.filter((value, index, self) => {
                    return self.findIndex((t) => {
                        return t.norms === value.norms
                    }) === index
                })
                //获取常规款的规格
                this.cgList = data.filter(item => item.customType == 2)
                this.cgList = this.cgList.filter((value, index, self) => {
                    return self.findIndex((t) => {
                        return t.norms === value.norms
                    }) === index
                })
            }
        },
        async changeTime(e, type) {
            if (type == 'create') {
                this.ListInfo.startTime = e ? e[0] : null
                this.ListInfo.endTime = e ? e[1] : null
            } else if (type == 'pay') {
                this.ListInfo.payStartTime = e ? e[0] : null
                this.ListInfo.payEndTime = e ? e[1] : null
            }
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await getTMZDCSRecordPageList(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async tmzdDelete(id) {
            this.$confirm('删除后无法恢复，是否确认删除?', '删除订单信息', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteTMZDCSRecord({ Id: id })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.getList()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        batchSet(row) {
            this.copyRow = row;
            this.copyVisable = true;
        },
        async doCopy() {
            for (let index = 0; index < this.copyRowCount; index++) {

                this.ruleForm.dtls.push(Object.assign({}, this.copyRow))
            }
            await this.Cpt()
            this.copyVisable = false;
        }
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    el-button {
        margin-left: 10px;
    }
}

.iptCss {
    width: 80px;
}

::v-deep .el-table__body-wrapper {
    min-height: 300px !important;
    max-height: 340px !important;
}

::v-deep .cell {
    padding-left: 0;
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 16px;
    }
}

.orderRemark {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
