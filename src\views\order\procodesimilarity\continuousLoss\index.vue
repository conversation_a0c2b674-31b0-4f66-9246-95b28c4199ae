<template>
 <my-container v-loading="pageLoading" style="height: 100%">
   <el-tabs v-model="activeName" style="height: 94%" >
     <el-tab-pane label="持续亏损链接统计" name="first1" style="height: 100%">
       <noProfitSearch  ref="noProfitSearch" style="height: 100%"></noProfitSearch>
     </el-tab-pane>
     <el-tab-pane label="持续亏损链接分析及运营方案" name="first2" style="height: 100%">
       <analysis ref="analysis" style="height: 100%"></analysis>
     </el-tab-pane>
   </el-tabs> 
 </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import analysis from "./analysis.vue";
import noProfitSearch from "./noProfitSearch.vue";

export default {
 name: "ConsecutiveNoProfitIndex",
 components: {
   MyContainer, analysis, noProfitSearch
 },
 data() {
   return {
     that: this,
     pageLoading: false,
     activeName: "first1"
   };
 },
 async mounted() {
 },
 methods: {
  
 },
};
</script>

<style lang="scss" scoped></style>
