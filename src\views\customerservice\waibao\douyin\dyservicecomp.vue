<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' style="height:93%;" :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange'
                :isSelection='false' :tableCols='tableCols' :loading="listLoading">
                <template slot='extentbtn'>
                    <el-input v-model="filter.groupName" v-model.trim="filter.groupName" placeholder="组名" style="width:120px;" disabled :maxlength="50" />
                    <el-input v-model="filter.name" v-model.trim="filter.name" placeholder="姓名" style="width:120px;" disabled :maxlength="50" />
                    <el-input v-model="filter.timeStart" style="width:120px;" disabled />至
                    <el-input v-model="filter.timeEnd" style="width:120px;" disabled />
                </template>
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getinquirsstatisticsList" />
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import {getOSDouYinKfInquirsPageList} from "@/api/customerservice/waibao";

const tableCols = [
    { istrue: true, prop: 'huiHuaId', label: '会话ID', },
    { istrue: true, prop: 'huiHuaTime', label: '日期', sortable: 'custom', formatter: (row) => formatTime(row.sdate, 'YYYY-MM-DD')  },
    { istrue: true, prop: 'snick', label: '昵称'},
    { istrue: true, prop: 'huiHuaResult', label: '评价值'},
    { istrue: true, prop: 'huiHuaFlag', label: '评价标签'},
    { istrue: true, prop: 'huiHuaType  ', label: '评价类型'},
];

export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, datepicker, cesTable },
    data() {
        return {
            that: this,
            filter: {
                name: "",
                sdate: [],
                timeEnd: "",
                timeStart: "",
                isManYi:"",//满意/不满意
                groupName:"",
            },
            
            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "huiHuaTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {

    },
    created() {

    },
    methods: {
        async dialogOpenAfter(data) {
            this.filter.sdate[0] = data.startDate;
            this.filter.sdate[1] = data.endDate;
            this.filter.timeStart = data.startDate;
            this.filter.timeEnd = data.endDate;
            this.filter.name = data.name;
            this.filter.groupName = data.groupName;
            this.filter.isManYi=data.isManYi;
            this.onSearch();
        },
        onSearch() {
            this.getinquirsstatisticsList();
        }, 
        async getinquirsstatisticsList() {
            const para = { ...this.filter };
            const params = {
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getOSDouYinKfInquirsPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
