<template>
 <div class="allbody">
   <my-container v-loading="pageLoading">
     <div id="top">
     <div class="body">
       <div class="bgcolor">
         <div class="content">
             <div class="flexleft paddingtwo">
               <div class="left-img" style="margin-bottom: auto;">
                 <div class="relete-block">

                   <div style="height:450px;width:450px;z-index: 99; border-radius: 20px;" v-if="imglist.length>0">
                       <div class="fdj" @mousemove="mousemove"  v-show="!clickbtn" >
                         <div class="small" ref="small"  @mousemove="handerover" @mouseleave="handOut">
                             <img :src="imgshow?imgshow:imglist.length>0?imglist[1].url:''" alt=""  style="border-radius: 20px;"/>
                             <div v-show="overarea">
                               <div  class="mask" :style="{'left':left + 'px','top':top + 'px',}" ref="mask"></div>
                             </div>
                         </div>
                         <div class="big" ref="big" v-show="overarea">
                             <img ref="bigimg" :src="imgshow?imgshow:imglist.length>0?imglist[1].url:''" alt=""  :style="{'left':imgX + 'px','top':imgY + 'px',}"/>
                         </div>
                     </div>
                   </div>
                   <div v-else style="height:450px;width:450px;z-index: 99; border-radius: 20px; color: #909399; display: flex; justify-content: center; align-items: center;">
                       {{msgtext}}
                   </div>
                   <div id="divhover" v-show="clickbtn" height="450px"  class="videoshow">
                    <!-- {{ videolist }}
                    222222 -->
                       <video width="450px" height="450px" muted autoplay loop :src="videolist!=null?videolist.domainUrl:''">
                           <!-- <source :src="videolist.url" type="video/mp4"> -->
                       </video>
                       <div><i class="el-icon-close deleteicon" @click="clickbtn = false"></i></div>
                   </div>

                   <div class="square" v-show="!clickbtn&&videolist!=null" @click="clicksquare"><i class="el-icon-caret-right" style="font-size: 3rem; color:white;"></i></div>
                 </div>
               </div>

               <div class="flexrow smaslling" style="margin-bottom: auto;">
                   <div class="scroll_wrapper">
                       <div class="scroll_list" style="margin-left: 17px;">
                           <div class="content-colorimg outline" v-if="imglist.length>0">

                             <div class="centerflex" v-if="videolist!=null">
                               <div :class="[clickbtn?'hoverBg':'hoverimg']" style="margin: 5px 0 0 6.2px; display: flex; justify-content: center; align-items: center;" >
                                 <img @mouseenter="mouseover(-1,videolist!=null?videolist.domainUrl:'')" :src="imglist[1]?imglist[1].url:''" alt="" width="68px" height="68px" style="border-radius: 8px;" />
                               </div>
                             </div>

                             <div class="centerflex"  v-for="(i, idx) in imglist" :key="idx">
                               <div :class="[idx==hoverIndex?'hoverBg':'hoverimg']" style="margin: 5px 0 0 6.2px; display: flex; justify-content: center; align-items: center;" >
                                 <img v-show="idx>0" @mouseenter="mouseover(idx,i.url)" :src="i.url" :alt="i.name" width="68px" height="68px" style="border-radius: 8px;"/>
                               </div>
                             </div>
                           </div>
                           <div class="content-colorimg outline" v-else style="color: #909399; display: flex; justify-content: center; align-items: center;">
                               {{msgtext}}
                           </div>
                       </div>
                   </div>
               </div>
             </div>
             <div class="flexright paddingtwo">
               <el-row>
                 <el-col :span="24"><div class="grid-content bg-purple-dark margintop weightsize">{{ dandata.itemName }}</div></el-col>
               </el-row>
               <el-row>
                 <el-col :span="24">
                  <div class="grid-content bg-purple-dark margintop" style="display: flex; flex-direction: row;">
                    <div class="xltext">昨日销量<span class="samllred" style="margin-left: 5px;">{{dandata.saleYesterday}}</span></div>
                    <div class="xltext">近3日销量<span class="samllred" style="margin-left: 5px;">{{dandata.saleThree}}</span></div>
                    <div class="xltext">近7日销量<span class="samllred" style="margin-left: 5px;">{{dandata.saleSeven}}</span></div>
                    <div class="xltext">近15日销量<span class="samllred" style="margin-left: 5px;">{{dandata.saleFifteen}}</span></div>
                    <div class="xltext">近30日销量<span class="samllred" style="margin-left: 5px;">{{dandata.saleThirty}}</span></div>
                 </div>
                </el-col>
               </el-row>
               <div style="margin-top: 1rem; background-color: #eee; height: 60px; align-items: center; border-radius: 10px;" class="margintop flexrow">
                 <div style="margin-left: 20px;"><div class="grid-content bg-purple-dark" style="color: grey">售价<span class="color-red"><span style="font-size: 2rem;">￥{{ distributionPricePre }}</span><span v-if="isShowBegin">起</span></span></div></div>

                 <div style="margin-left: 20px;"><div class="grid-content bg-purple-dark" style="color: grey;padding: 10px 0 0 0;">零售价{{ dandata.minSalesPrice }}</div></div>

               </div>
               <el-row>
                 <el-col :span="24">
                  <div class="grid-content bg-purple-dark margintop" style="display: flex; flex-direction: row;">
                    <div style="margin-right: 60px;" class="xltext">数据</div>
                    <div class="xltext">商品详情浏览<span class="samllblack">{{dandata.visitQty}}</span></div>
                    <div class="xltext">收藏人数<span class="samllblack">{{dandata.favoriteQty}}</span></div>
                    <div class="xltext">上架次数<span class="samllblack">{{dandata.shelvesQty}}</span></div>
                 </div>
                </el-col>
               </el-row>

               <el-row>
                 <el-col :span="24">
                  <div class="grid-content bg-purple-dark margintop" style="display: flex; flex-direction: row;">
                    <div style="margin-right: 60px;" class="xltext">资料</div>
                    <div class="xltext">资料编码:<span class="samllblack">{{dandata.proCode}}</span></div>
                 </div>
                </el-col>
               </el-row>

               <el-row>
                 <el-col :span="24">
                  <div class="grid-content bg-purple-dark margintop" style="display: flex; flex-direction: row;">
                    <div style="margin-right: 60px;" class="xltext">创建</div>
                    <div class="xltext">{{dandata.createdTime}}</div>
                 </div>
                </el-col>
               </el-row>

               <div class="margintop content-colorimg-flex">
                 <div class="smalltext" style="margin-right: 60px; width: 32px;">款式</div>
                 <div class="content-colorimg outline" v-if="skuImgList.length>0">
                   <div style="width: 200px;height:30px; margin-bottom: 10px; margin-left: 10px; display: flex; flex-direction: row;" class="centerflex" :class="i==riclickIndex?'clickBg':''"   :style="imgboder" @click="isimg(itemm,i)" v-for="(itemm,i) in skuImgList" :key="i">
                    <img :src="itemm.skuImage" width="30px" height="30px">
                       <el-tooltip effect="dark" :content="itemm.colour" placement="top-start">
                    <div class="orderTitle" style="font-size: 12px; color: grey; margin-right: auto;">{{ itemm.colour }}</div>
                      </el-tooltip>
                   </div>
                 </div>
                 <div class="content-colorimg outline" v-else style="color: #909399; display: flex;  align-items: center;">
                     {{msgtext}}
                 </div>

               </div>

               <!-- <div class="margintop content-colorimg-flex">
                 <div class="smalltext" style="margin-right: 60px; width: 29px;">型号</div>
               </div> -->
             </div>
         </div>
       <goodstable :skulist="dandata.skulist"></goodstable>

       </div>
     </div>
     <div style="display: flex; flex-direction: row; justify-content: center; width: 100%;">
      <div style="width: 1200px;">
       <img :src="item" v-for="(item,ii) in dandata.itemDetailImageslist" :key="ii" alt="" width="100%" mode="aspectFit">
      </div>
     </div>

     <!-- <div style="position: absolute; bottom: 20px; right: 20px;">
        <a href="#top"><div class="el-backtop" style="right: 50px; bottom: 50px;"><i class="el-icon-caret-top"></i></div></a>
     </div> -->

     <!-- <el-backtop target=".appmain-wrap" :bottom="100" :right="40">
      <div class="el-backtop" style="right: 50px; bottom: 50px;"><i class="el-icon-caret-top"></i></div>
      shang
    </el-backtop> -->

   </div>
   <el-backtop  target=".el-main .mycontainer" :bottom="100" :visibility-height="5"></el-backtop>


  </my-container>

 </div>


</template>
<script>
import MyContainer from "@/components/my-container";
import { getPageDetailImgInfo} from '@/api/media/ShootingVideo';
import goodstable from "@/views/distribution/goodstable.vue"

export default {
  name: "Users",
  components: { MyContainer, goodstable },
  props: ['dandata'],
  data() {
      return {
       minSupplyPrice: null,
       alldata: this.dandata,
       imgboder: {border:''},
       top:'',
       left:'',
       imgX:'',
       imgY:'',
       overarea: false,
       pageLoading: false,
       num: 1,
       clickbtn: false,
       hoverIndex: -1,
       clickIndex: -1,
       riclickIndex: -1,
       imgshow: '',
       platform: null,
       distributionPricePre: 0,
       isShowBegin: true,
       imglist:[
        // {name:'',url:''}
       ],
       videolist: null,
       bottomimg: {

       },
       skuImgList: [],
       isimghover: false,
       cutImgSrc: '',
       draw: false,
       msgtext: '暂无内容，请先上传后预览！'
      };
  },
  watch: {
   alldata: 'alldatafuc'
  },
  provide () {
      return {
      }
  },
  created() {

   // this.$nextTick(() => {
   //   // 禁用右键
   //   document.oncontextmenu = new Function("event.returnValue=false");
   //   // 禁用选择
   //   document.onselectstart = new Function("event.returnValue=false");
   // });
 },
  async mounted() {
   // this.getlist();
   this.platform = this.$route.query.platform;

   this.dandata.itemMainImagelist.forEach((element,index) => {
    // this.imglist[index].url = element;
    // if(index==0){
    //   this.videolist.domainUrl =
    //   this.imglist.push
    // }

    // 不是视频才加入图片列表
    if(this.isExt(['mp4','mov'],element)){
      this.videolist = {domainUrl:element};
    } else {
      this.imglist.push({name:'',url:element})
    }
   });
   this.skuImgList = this.dandata.skulist;
   this.minSupplyPrice = this.dandata.minSupplyPrice;
   //this.distributionPricePre = this.dandata.skulist[0].distributionPricePre;
   this.distributionPricePre = this.dandata.minBaseSalePrice;

   //this.videolist.domainUrl = this.dandata.itemMainImagelist[0];

  },
  methods: {
   alldatafuc(e){
    console.log("点击变化",e)
   },
   async getlist(){
     let _this = this;
     _this.pageLoading= true;
     var res  =  await getPageDetailImgInfo({taskid: this.$route.query.id});
     if(res?.success)
     {
         _this.imglist = res.data.mainImgList;
         _this.videolist=res.data.vedioList;
         _this.bottomimg = res.data.detailImgList;
         _this.skuImgList = res.data.skuImgList;
     }
     _this.pageLoading= false;
   },
   handleChange(value) {
       console.log(value);
     },
   setActiveItem(val){
     return 2;
   },
   clicksquare(){
     this.clickbtn = true;
     this.riclickIndex = -1;
     this.hoverIndex = -1;
     // console.log("点击圈圈")
   },
   selectStyle(){
     console.log("hover事件")
   },
   leftSlide(){
       let left=this.$refs.wrapperCon.scrollLeft
       let num=0
       clearInterval(this.timer)
       this.timer=null
       this.timer=setInterval(()=>{
           if(!left||num>=300){
               // 停止滚动
               clearInterval(this.timer)
               this.timer=null
               return
           }
           this.$refs.wrapperCon.scrollLeft=left-=30
           num+=30
       },30)
       // 20：速度
   },
   rightSlide(){

       let left=this.$refs.wrapperCon.scrollLeft
       let scrollWidth=this.$refs.wrapperCon.scrollWidth
       let clientWidth=this.$refs.wrapperCon.clientWidth
       let num=0
       clearInterval(this.timer)
           this.timer=setInterval(()=>{
           if(left+clientWidth>=scrollWidth||num>=300){
               clearInterval(this.timer)
               return
           }
           this.$refs.wrapperCon.scrollLeft=left+=30
           num+=30
       },20)
     },
     mouseover(index,img){
       if(img==this.videolist?.domainUrl&&index==-1){
         this.clickbtn = true;
         this.hoverIndex = -1;
         console.log("打印数据",this.clickbtn)
         return
       }
       this.clickbtn = false;
       this.hoverIndex = index;
       this.imgshow = img;
       this.riclickIndex = -1;
     },
     isimg(val,index){
      console.log("点击sku信息",val)
      this.minSupplyPrice = val.supplyPrice;
       this.clickbtn = false;
       this.imgshow = val.skuImage;
       this.riclickIndex = index;
       this.hoverIndex = -1;
       // this.distributionPricePre  = val.distributionPricePre;
       this.distributionPricePre  = val.baseSalePrice || 0;
       this.isShowBegin = false;
     },
     changecarou(index){
       this.hoverIndex = index;
     },
     mousemove(e){
       let small = this.$refs.small
       let mask = this.$refs.mask
       let big = this.$refs.big
       let bigimg = this.$refs.bigimg

       let maskX =  e.pageX - small.offsetLeft
       let maskY = e.pageY - small.offsetTop

       maskX = maskX - mask.offsetWidth +260;
       maskY = maskY - mask.offsetHeight+300;

       maskX = maskX < 0 ? 0 : maskX;
       maskY = maskY < 0 ? 0 : maskY;


       if(maskX>450&&maskX<760){
         maskX = maskX-450
       }else if(maskX>=760){
         maskX = 310
       }else if(maskX<=450){
         maskX = 0
       }

       if(maskY>450&&maskY<760){
         maskY = maskY-450
       }else if(maskY>=760){
         maskY = 310
       }else if(maskY<=450){
         maskY = 0
       }
       // maskX = maskX > small.offsetWidth - mask.offsetWidth ? small.offsetWidth - mask.offsetWidth : maskX;
       // maskY = maskY > small.offsetHeight - mask.offsetHeight ? small.offsetHeight - mask.offsetHeight : maskY;
       // console.log("计算后msk位置",[maskX,maskY])
       let bigImgX = maskX * (big.offsetWidth - bigimg.offsetWidth) / (small.offsetWidth - mask.offsetWidth);
       let bigImgY = maskY * (big.offsetHeight - bigimg.offsetHeight) / (small.offsetHeight - mask.offsetHeight)

       this.left = maskX
       this.top = maskY
       this.imgX = bigImgX
       this.imgY = bigImgY
     },
     handerover(){
       this.overarea =true
     },
     handOut(){
       this.overarea =false
     },
     imgmouseover(val,index){
       let _this = this;
       _this.isimghover = true;

       var wrap = document.getElementById("imgid"+index);
       // var width = wrap.offsetWidth;
       // var height = wrap.offsetHeight;
       // _this.x = e.offsetX
       // _this.y = e.offsetY
     },
     tomessage(val,index){
       var thiz = this;
       thiz.draw = true;
       var wrap = document.getElementById("imgid"+index);
       var width = wrap.offsetWidth;
       var height = wrap.offsetHeight;
       console.log("打印原图宽高",[width,height])

       var clipcanvas = document.getElementById("clipcanvas"+index);
       var drawcanvas = document.getElementById("drawcanvas"+index);
       clipcanvas.width = width;
       clipcanvas.height = height;
       // clipcanvas.style.backgroundColor = 'rgba(0,0,0,0.1)';
       drawcanvas.width = width;
       drawcanvas.height = height;

       var clipCtx = drawcanvas.getContext("2d");
       var clipImg = document.createElement("img");
       clipImg.classList.add('img_anonymous');
       clipImg.crossOrigin = "anonymous";
       clipImg.src = val.url;
       clipImg.width = width+'px';
       clipImg.height = height+'px';
       // clipImg.style.width = width+'px';
       // clipImg.style.height = height+'px';
       clipImg.style.zIndex = 250;
       clipImg.mode = 'scaleToFill';
       var timg = clipImg.cloneNode();
       wrap.appendChild(clipImg);
       clipImg.onload = function(){
           var x = Math.floor((width - this.width)/2);
           var y = Math.floor((height - this.height)/2);
           // console.log("画图宽高",[timg.width,timg.height]);
           // console.log("this指向",this);
           clipCtx.drawImage(this,0,0,timg.width,timg.height,x,y,this.width,this.height);
           // clipCtx.drawImage(this,0,0,width,height,x,y,this.width,this.height);
       };

       var ctx = clipcanvas.getContext("2d");
           ctx.fillStyle = 'rgba(0,0,0,0.4)';
           ctx.strokeStyle="rgba(0,143,255,1)";
           var start = null;
           var clipArea = {};//裁剪范围

           clipcanvas.onmousedown = function(e){
               start = {
                   x:e.offsetX,
                   y:e.offsetY
               };
           };
           clipcanvas.onmousemove = function(e){
               if(start){
                   fill(start.x,start.y,e.offsetX-start.x,e.offsetY-start.y)
               }
           };
           document.addEventListener("mouseup",function(e){
               if(start){
                   start = null;
                   var url = startClip(clipArea);
                   //生成base64格式的图
                   thiz.cutImgSrc = url;


                    //添加输入框
                   let input = document.createElement('input');
                   let canvasArea = document.getElementsByClassName('img_box')[index];
                   // let canvasArea = document.getElementById("clipcanvas"+index)[0];
                   canvasArea.appendChild(input);
                   input.style.left = `${clipArea.x}px`;
                   input.style.top = `${clipArea.y+clipArea.h}px`;
                   input.style.border = "2px dashed red";
                   input.style.zIndex = 101;
                   input.style.height = '30px';
                   input.style.position = 'absolute';
                   input.focus();
                   // console.log("输入框信息",input)
               }
           });
           function fill(x,y,w,h){
               ctx.clearRect(0,0,width,height);
               ctx.beginPath();
               //遮罩层
               ctx.globalCompositeOperation = "source-over";
               ctx.fillRect(0,0,width,height);
               //画框
               ctx.globalCompositeOperation = 'destination-out';
               ctx.fillRect(x,y,w,h);
               //描边
               ctx.globalCompositeOperation = "source-over";
               ctx.moveTo(x,y);
               ctx.lineTo(x+w,y);
               ctx.lineTo(x+w,y+h);
               ctx.lineTo(x,y+h);
               ctx.lineTo(x,y);
               ctx.stroke();
               ctx.closePath();
               clipArea = {
                   x,
                   y,
                   w,
                   h
               };
           }
           function startClip(area){
               var canvas = document.createElement("canvas");
               canvas.width = area.w;
               canvas.height = area.h;

               var data = clipCtx.getImageData(area.x,area.y,area.w,area.h);

               var context = canvas.getContext("2d");
               context.putImageData(data,0,0);
               return canvas.toDataURL("image/png",1);
           }
           // 移除创建的img节点
           // var imglist = document.getElementById("clip-img-w").getElementsByTagName("img")
           // // imglist.remove();
           // for(var i=0; i<imglist.length;i++){
           //     imglist[i].remove()
           // }
     },
     imgmouseleave(){
       let _this = this;
       // _this.isimghover = false;
     },
     tomobilemsg(){
       // let routeUrl = this.$router.resolve({
       //        path: "/mobilemsg",
       //       //  query: {id:this.rowinfo}
       //   });
       // window.open(routeUrl.href, '_blank');
       this.$router.push({ path: '/mobilemsg',query: {id:this.$route.query.id,platform:  this.platform}})
     },
     tocarmsg(){
           this.$router.push({ path: '/msgshow1',query: {id:this.$route.query.id,platform:  this.platform}})
       },
     intosql(){
       let allmsg = document.getElementById('alldemo');
       // console.log("打印",allmsg)
     },
    isExt(exts, fileName){
      // 判断是否是指定后缀
      const ext = this.getExt(fileName);
      var flag = exts.indexOf(ext.toLowerCase())>-1;
      return flag;
    },
    getExt(fileName){
      if(!fileName) return 'nofilename';
      // 获取文件扩展名
      return /[^.]+$/.exec(fileName)[0];
    }
  },
};
</script>
<style lang="scss" scoped>
.allbody {
 height: 100%;
 width: 100%;
}
.whitecolor{
 background-color: white;
}
// .heard-title{
//   height: 3rem;
//   padding: 0 80px;
//   background-color: white;
// }
.body{
 height: 100%;
 // border-radius: 20px;
 padding: 10px 20px;
 background-color: #E4E7ED;
}
.heard{
 height: 4rem;
 background-color: white;
 border-radius: 30px;
}
.content{
 min-height: 36rem;
 background-color: white;
 border-radius: 20px;
 margin: 20px 0;
 display: flex;
 position: relative;
}
.rightthr{
 display: flex;
 justify-content: center;
 flex-direction: column;
 align-items: center;
 position: absolute;
 width: 150px;
 height: 300px;
 // background-color: red;
 right: 0;
 top: 50%;
 transform: translate(0,-50%);
}
.flexrow{
 display: flex;
 flex-direction: row;
}
.flexleft{
 flex: 3;
 min-height: 40rem;
 display: flex;
 justify-content: center;
 align-items: center;
 flex-direction: column;
}
.flexright{
 flex: 7;
}
.paddingtwo{
padding: 30px;
}
.margintop{
 margin-top: 2rem;
}
// .margintopbot{
//   margin: 30px 0 100px 0;
// }
.marginleft{
 margin-left: 1rem;
}
.column{
 display: flex;
 flex-direction: column;
}
.bgcolor{
 background-color: white;
}
.centerflex{
 display: flex;
 justify-content: center;
 align-items: center;
}
.columnflex{
 display: flex;
 align-items: center;
}
.left-img{
 background-color: #EBEEF5;
 border-radius: 20px;
 // height: 22rem;
 // width: 22rem;
}
.left-smallimg{
 height: 5rem;
 width: 100%;
 // margin-top: 1rem;
 background-color: #EBEEF5;
}
.content-colorimg{
 height: 100%;
 width: 100%;
 // background-color: #EBEEF5;
}
.content-colorimg-flex{
 display: flex;
 flex-direction: row;
}
.btncss{
 line-height: 2rem;
}
.button-buy{
 border-radius: 20px;
 display: flex;
 flex-direction: row;
}
.btn-left{
background-color: #e43112;
color: white;
padding: 5px;
width: 7rem;
border-radius: 20px 0 0 20px;
}
.btn-right{
 background-color: #ebb608;
 color: white;
 padding: 5px;
 width: 7rem;
 border-radius: 0 20px 20px 0;
}
.color-red{
 color: #f52f0c;
}
.btn-quan{
 margin-left: 20px;
 border-radius: 20px;
 background-color: #e43112;
 color: white;
 display: flex;
 align-items: center;
}
.btn-nei{
 margin: 0 15px;
}
.smalltext{
 color: #909399;
 font-size: 0.8rem;margin-right: 10px;
}
.sectionalizer{
 border-radius: 20px;
 padding: 2px;
 // background-color: #e43112;
 border: 1px solid #999695e5;
}
.marginlr{
 margin: 0 15px;
 color: #606266;
 font-size: 0.9rem;
 // width: 100%;
 // border-right: 1px solid #999695e5;
}
.columnline{
 height: 20px;
 width: 1px;
 background-color: #909399;
}

.btntobuy {
border: 0;
background-color: #e46852;
box-shadow: rgb(0 0 0 / 5%) 0 0 8px;
letter-spacing: 1.5px;
text-transform: uppercase;
font-size: 0.9rem;
transition: all .5s ease;
}
.btntoup {
border: 0;
background-color: #ebb608;
box-shadow: rgb(0 0 0 / 5%) 0 0 8px;
letter-spacing: 1.5px;
text-transform: uppercase;
font-size: 0.9rem;
transition: all .5s ease;
}
.btntoup:hover{
letter-spacing: 3px;
background-color: rgb(241, 168, 9);
color: hsl(0, 0%, 100%);
box-shadow: rgb(241, 168, 9) 0px 7px 29px 0px;
}

.btntoup:active{
letter-spacing: 3px;
background-color: rgb(241, 168, 9);
color: hsl(0, 0%, 100%);
box-shadow: rgb(241, 168, 9) 0px 0px 0px 0px;
transform: translateY(10px);
transition: 100ms;
}

.btntobuy:hover{
letter-spacing: 3px;
background-color: rgb(236, 46, 12);
color: hsl(0, 0%, 100%);
box-shadow: rgb(236, 46, 12) 0px 7px 29px 0px;
}

.btntobuy:active{
letter-spacing: 3px;
background-color: rgb(236, 46, 12);
color: hsl(0, 0%, 100%);
box-shadow: rgb(236, 46, 12) 0px 0px 0px 0px;
transform: translateY(10px);
transition: 100ms;
}
.smaslling{
 width: 100%;
 // justify-content: center;
 align-items: center;
 margin-top: 1rem;
}
.iconsize{
 font-size: 1.8rem;
}
.iconcover:hover{
 color: #409EFF;
 border-color: #409EFF;
}
.flexcolumn{
 display: flex;
 flex-direction: column;
}
.outline{
 display: flex;
 overflow-x: hidden;
 flex-wrap: wrap;
 flex-direction: row;
}
.imglist{
 margin: 1rem 5rem;
 display: flex;
 align-items: center;
}
::v-deep .el-main{
 background-color: #E4E7ED;
}
.relete-block{
 position: relative;
}
.square{
 background-color: rgba(157, 161, 170, 0.461);
 width: 40px;
 height:40px;
 border-radius: 40px;
 position: absolute;
 top: 86%;
 left: 0%;
 z-index: 201;
 // transform: translate(-50%,-50%);
 display: flex;
 justify-content: center;
 align-items: center;
 border: 2px solid #fff;
}
.videoshow{
 z-index: 200;
 top: 0;
 position: absolute;
}
.deleteicon{
 position: absolute;
 top: 5px;
 right: 5px;
 color: #909399;
 font-size: 2.6rem;
 display: none;
 // color: red;
}
#divhover:hover .deleteicon{
 display: block;
}
.topborder{
 border-top: 1px solid #9e9e9ea2;
 // line-height: 2rem;
 padding: 1rem 2rem;
 font-weight: 600;
}
#click-scroll-X {
   display: flex;
   align-items: center;
   .left_btn,.right_btn {
     font-size: 2.8rem;
     cursor: pointer;
   }
 .scroll_wrapper {
   width: 410px;
   overflow-x: scroll;
   padding: 20px 20px;
   overflow: hidden;
   .scroll_list{
   display: flex;
   align-items: center;
   justify-content: space-between;
   // overflow: hidden;

       .item {
       width: 100px;
       height: 100px;
       display: flex;
       align-items: center;
       justify-content: center;
       border: 1px solid rgb(223, 223, 223);
       box-sizing: border-box;
       flex-shrink: 0;
       }
   }
 }
}
.hoverBg{
 //  background: #da1f1f;
 border-radius: 5px;
  border: 1px solid #da1f1f;
  color: #fff;
 border-radius: 8px;
}
.hoverimg{
 border: 1px solid #eee;
 border-radius: 8px;
}
.clickBg{
 //  background: red;
  border: 1px solid red;
  color: #fff;
}
::v-deep .el-header{
 background-color: rgba(208, 210, 214, 0.693);
}
.small {
     width: 450px;
     height: 450px;
     position: relative;
     cursor: move;
   }
 .small img {
   width: 100%;
   height: 100%;
 }
 .big {
       width: 450px;
       height: 450px;
       position: absolute;
       top: 0;
       left: 450px;
       overflow: hidden;
   }
   .big img {
       position: absolute;
       width: (45*45/14)px;
       height: (45*45/14)px;
       left:0;
       top:0;
       z-index: 89;
   }

   .mask {
       width: 140px;
       height: 140px;
       background: rgba(0, 119, 255, 0.4);
       position: absolute;
       top: 0px;
       left: 0px;
       z-index: 150;
       cursor: move;
   }
   .fdj{
       border-radius: 20px;
       height: 450px;
       width: 450px;
       background-color: #F2F6FC;
   }
   .img_box{
   width: 790px;
   height: 100%;
   position:relative;
   }
   .img_box canvas{
     width: 790px;
     position: absolute;
     }
   .img_box #clipcanvas{
       z-index: 2;
     }
   .img_box #drawcanvas{
       z-index: 1;
   }
   .btntomsg{
     position: absolute;
     left: 790px;
   }
   .weightsize{
     font-weight: 600;
     font-size: 23px;
   }

   .xltext{
     color: #7c7c7c;
     font-weight: 400;
     font-size: 14px;
     margin-right: 10px;
    .samllred{
    color: #f04852;
    font-weight: 500;
    font-size: 14px;
    }
    .samllblack{
     font-weight: 600;
     color: black;
     font-size: 14px;
    }
   }

   .orderTitle{
    height: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    // white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    // height: 2.5rem;
    width: 170px;
    white-space: nowrap;
    display: block;
    // cursor: pointer;
    flex: 1;
  //  max-width: 60%;
  }

  .appmain-wrap {
    height: 100vh;
    background: red;
    overflow: scroll;
  }
</style>

