<template>
    <div style="padding-left:10px;overflow: auto;border: 1px solid #eee; width: 100%; border-radius: 10px; height: 500px; overflow: hidden;"
        v-loading="loading">
        {{ platname }}
        <div v-if="analysisData && analysisData.series && analysisData.series != null && analysisData.series.length > 0"
            :id="'buschar' + randrom" :style="thisStyle"></div>
        <div v-else>没有可供展示的图表数据！</div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
    name: 'buschar',
    components: {},
    props: {
        isslice: false,
        chartsdata: {},
        isselplatformlr: [],
        loading: {
            type: Boolean, default: function () {
                return false;
            }
        },
        action: { type: Function, default: null },
        parms: { type: Object, default: null },

        thisStyle: {
            type: Object,
            default: function () {
                return {
                    width: '100%', height: '470px', 'box-sizing': 'border-box', 'line-height': '470px'
                }
            }
        },
        gridStyle: {
            type: Object, default: function () {
                return {
                    top: '20%',
                    left: '5%',
                    right: '4%',
                    bottom: '9%',
                    containLabel: false
                }
            }
        },
        legendChanges: { type: Function, default: null },
    },
    data() {
        return {
            that: this,
            alignleft: false,
            randrom: "",
            period: 0,
            pageLoading: false,
            listLoading: false,
            procode: '',
            platname: '',
            index: 0,
            analysisData: {},
            isDot: null,
            sellr: [],
            datatime: '',
            clicklengend: false
        }
    },
    created() {
        var e = 10;
        var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
            a = t.length,
            n = "";
        for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
        this.randrom = n;
    },
    // watch: {
    //  isselplatformlr: {
    //     handler(newVal){
    //       if(newVal.length>0){
    //        this.sellr = newVal;
    //       }
    //     },
    //     deep: true
    //   }
    // },
    async mounted() {



        await this.$nextTick(() => {
            this.analysisData = this.chartsdata.analysis;

            this.platname = this.chartsdata.platformName;

            this.initcharts();
        });
        



    },
    methods: {
        routerpush(params) {

            let that = this;
            // debugger
            const dateString = that.datatime;


            const formattedDate = dateString.replace(/-/g, '');
            params.query.yearMonthDay = dateString;



            if (!this.clicklengend) {
                if (JSON.parse(localStorage.getItem('isselplatformlr')).length > 1) {

                    this.$message.info('请只选择一个标签负利润后才能跳转平台！');
                    return
                }
            }

            if (!this.clicklengend) {
                that.$router.push({ path: params.path, query: params.query })
            }

            this.clicklengend = false;
            // this.$router.push(...params)
        },
        initcharts() {
            let that = this;

            if (!(that.analysisData && that.analysisData.series && that.analysisData.series != null && that.analysisData.series.length > 0))
                return;

            this.$nextTick(() => {
                var chartDom = document.getElementById('buschar' + that.randrom);
                var myChart = echarts.init(chartDom);
                myChart.clear();
                if (that.analysisData.series) {
                    var option = that.Getoptions(that.analysisData);
                    option && myChart.setOption(option);
                }
                myChart.on('legendselectchanged', function (params) {
                    if (that.legendChanges != null) {
                        that.legendChanges(params.selected);
                    }
                });

                myChart.off('legendselectchanged');
                myChart.on('legendselectchanged', (e) => {
                    this.clicklengend = true;
                })

                myChart.on('mouseover', (params) => {
                    // this.isDot = params.event.target.;//获取鼠标停留拐点的数据
                    // this.classPass = this.subListClone[this.isDot].passing_rate[0]//雷达图拐点本班及格率
                    // this.gradePass = this.subListClone[this.isDot].passing_rate[1]//雷达图拐点年段及格率
                })
            });
        },
        randomString() {
            var e = 10;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                a = t.length,
                n = "";
            for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
            return n
        },
        Getoptions(element) {
            var series = []
            element.series.forEach(s => {
                series.push({ smooth: true, ...s })
            })
            let _this = this;
            var yAxis = []
            if (Array.isArray(element.yAxis)) {
                element.yAxis.forEach(s => {
                    yAxis.push({
                        type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
                            formatter: function (value) {
                                if (value >= 100000000) {
                                    value = (value / 100000000).toFixed(1) + 'Y';
                                }
                                if (value >= 10000000) {
                                    value = (value / 10000000).toFixed(1) + 'KW';
                                }
                                if (value >= 10000) {
                                    value = (value / 10000).toFixed(1) + 'W';
                                }
                                if (value >= 1000) {
                                    value = (value / 1000).toFixed(1) + 'K';
                                }
                                if (value <= -100000000) {
                                    value = (value / 100000000).toFixed(1) + 'Y';
                                }
                                if (value <= -10000000) {
                                    value = (value / 10000000).toFixed(1) + 'KW';
                                }
                                if (value <= -10000) {
                                    value = (value / 10000).toFixed(1) + 'W';
                                }
                                if (value <= -1000) {
                                    value = (value / 1000).toFixed(1) + 'K';
                                }
                                return value + s.unit;
                            }
                        }
                    })
                })
            } else {
                yAxis = { ...element.yAxis };
            }

            // yAxis[1] = {
            //     min: 0,
            //     // max: 100,
            //     interval: function (value) {
            //         if (value <= 100) {
            //             return 10
            //         } else {
            //             return 100
            //         }
            //     },
            //     axisLabel: {
            //         formatter: '{value} 个'
            //     }

            // }



            var selectedLegend = {};//{};
            if (element.selectedLegend) {
                element.legend.forEach(f => {
                    //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
                    if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
                })
            }
            var option = _this.isslice ? {
                title: { text: element.title },
                // tooltip: { trigger: 'axis' },
                legend: {
                    selected: selectedLegend,
                    data: element.legend
                },
                grid: this.gridStyle,
                toolbox: {
                    show: false,
                    feature: {
                        //  magicType: { show: true, type: ['line'] },
                        //restore: {show: true},
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    padding: [30, 0, 0, 0],
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series: series,
            } : {
                title: { text: element.title },
                // tooltip: { trigger: 'axis' },
                legend: {
                    selected: selectedLegend,
                    data: element.legend
                },
                grid: this.gridStyle,
                toolbox: {
                    show: false,
                    feature: {
                        //  magicType: { show: true, type: ['line'] },
                        //restore: {show: true},
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    padding: [30, 0, 0, 0],
                    axisPointer: {
                        type: 'shadow'
                    },
                  


                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series: series,
            };


            return option;
        },
    }
}
</script>

