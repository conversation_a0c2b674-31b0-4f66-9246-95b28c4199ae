<template>
  <div>
    <div class="bzckxg">
      <div class="bt">
        <span style="float: left">查看效果</span>
      </div>

      <div class="bzckxgdh">
        <div style="width: 140px; margin: 0 auto">
          <el-radio-group style="width: 100%" v-model="radio2" size="medium">
            <el-radio-button
              style="width: 50%"
              label="效果图"
            ></el-radio-button>
            <el-radio-button
              style="width: 50%"
              label="源文件"
            ></el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="bzckxgt">
        <div></div>
      </div>
      <div class="bzckywj">
        <div></div>
      </div>

      <div class="qxtj"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "OneHelloWorld",

  data() {
    return {};
  },

  mounted() {},

  methods: {},
};
</script>

<style>
.bzckxg {
  width: 1000px;
  background-color: #fff;
}
.bzckxg .bt {
  height: 60px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

.bzckxg .bzckxgdh {
  width: 100%;
  height: 80px;
  background-color: aqua;
  box-sizing: border-box;
  padding: 20px 50px;
}

.bzckxg .qxtj {
  box-sizing: border-box;
  padding: 20px 50px;
}

.bzckxgt {
  width: 100%;
  height: 1200px;
  background-color: bisque;
  box-sizing: border-box;
  padding: 20px 50px;
}

.bzckywj {
  width: 100%;
  height: 200px;
  background-color: rgb(196, 255, 206);
  box-sizing: border-box;
  padding: 20px 50px;
}

</style>