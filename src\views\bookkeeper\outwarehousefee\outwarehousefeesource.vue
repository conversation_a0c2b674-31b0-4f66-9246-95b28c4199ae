<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model.trim="filter.feeType" filterable placeholder="类型" style="width:160px"
                        @change="onSearch">
                        <el-option label="拣货_订单数" value="拣货_订单数" />
                        <el-option label="打包_总" value="打包_总" />
                        <el-option label="出库验货_总" value="出库验货_总" />
                        <el-option label="发货" value="发货" />
                        <el-option label="发货抽检登记" value="发货抽检登记" />
                        <el-option label="发货质检" value="发货质检" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 280px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="filter.warehouseCode" style="width: 240px" size="mini" clearable>
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;">
                    <!-- <el-input v-model.trim="filter.orderNoInner" placeholder="内部单号" style="width:120px;" clearable
                        oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}"
                        maxlength="10" /> -->

                    <inputYunhan ref="productCode2" :inputt.sync="filter.orderNoInners" v-model="filter.orderNoInners"
                        class="publicCss" placeholder="内部单号/输入0可查空内部单号" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000"  @callback="orderNoInnerBack" title="内部单号">
                    </inputYunhan>

                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                <el-checkbox v-model="filter.noClac" class="publicCss" style="width: 60px;margin-top: 5px;">未计算</el-checkbox>
                </el-button>

                   <el-button style="padding: 0;margin: 0;border: 0;">
                <el-checkbox v-model="filter.zeroAmount" class="publicCss" style="width: 80px;margin-top: 5px;">出仓为0</el-checkbox>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" :loading="isExport">导出</el-button>
                <!-- <el-button type="primary" @click="onReClac">计算出仓费</el-button> -->
            </el-button-group>

            <el-button-group>
                <!-- <el-button type="primary" @click="onImport('预包加工0.09')">导入(预包加工0.09)</el-button>
                <el-button type="primary" @click="onImport('预包加工0.13')">导入(预包加工0.13)</el-button>
                <el-button type="primary" @click="onImport('拣货批次管理')">导入(拣货批次管理)</el-button>
                <el-button type="primary" @click="onImport('加工单管理')">导入(加工单管理)</el-button> -->
                <el-button type="primary" @click="onImport('加工进仓')">导入(加工进仓)</el-button>
                <el-button type="primary" @click="onImport('拣货')">导入(拣货)</el-button>
                <el-button type="primary" @click="onImport('拣货_订单数')">导入(拣货_订单数)</el-button>
                <el-button type="primary" @click="onImport('打包_总')">导入(打包_总)</el-button>
                <el-button type="primary" @click="onImport('出库验货_总')">导入(出库验货_排除赠品)</el-button>
                <el-button type="primary" @click="onImport('发货')">导入(发货_排除赠品)</el-button>
                <el-button type="primary" @click="onImport('发货抽检登记')">导入(发货抽检登记)</el-button>
                <el-button type="primary" @click="onImport('发货质检')">导入(发货质检)</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'OutWarehouseFeeSource20231012'" :border="true" :align="'center'"
            :tablekey="'OutWarehouseFeeSource20231012'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0"   :xgt="9999">
             <template slot="right">
               <vxe-column title="操作" width="140" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;align-items: center;">
              <my-confirm-button  type="text"  :loading="row._loading" @click="onReClac(row)" >当日({{ row.feeType }})重算</my-confirm-button>
            </div>
          </template>
        </vxe-column>
        </template>
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog :title="dialogUploadData.title" :visible.sync="dialogUploadData.visible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-select v-model="dialogUploadData.importWarehouseCode"
                            style="width: 240px;margin-right: 20px;" size="mini">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <!-- <el-date-picker v-model="dialogUploadData.importWorkDate" type="date" placeholder="选择日期"
                            style="width:150px">
                        </el-date-picker> -->
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        &nbsp;
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx,.csv" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="dialogUploadData.uploadLoading" @click="onSubmitUpload">{{
        (dialogUploadData.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogUploadData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import inputYunhan from "@/components/Comm/inputYunhan";
import { getTbWarehouseList } from '@/api/profit/warehousewages';
import { GetyOutWarehouseFeePageList, ImportOutWarehouseFee,ReClacOutWarehouseFee, exportOutWarehouseFee } from '@/api/bookkeeper/outwarehousefee'

const tableCols = [
    { istrue: true, prop: 'warehouseCode', label: '仓库', sortable: 'custom', width: '260', formatter: (row) => row.warehouseName },
    { istrue: true, prop: 'operateTime', label: '操作时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'operater', label: '操作人', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'outNo', label: '出仓单号', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'orderNoInner', label: '内部单号', sortable: 'custom', width: '120', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { istrue: true, prop: 'onlineOrderNumber', label: '线上单号', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'outCount', label: '数量', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'batchType', label: '类型', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'batchStr', label: '批次号', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'taskFlag', label: '任务标记', sortable: 'custom', width: '80' },
        { istrue: true, prop: 'jsDateTime', label: '计算时间', width: '150', sortable: 'custom' },
    { istrue: true , prop: 'outFee', label: '出仓成本', width: '150', sortable: 'custom' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(3, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: "outwarehousefeesource",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
        buschar, vxetablebase,inputYunhan
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                feeType: "拣货_订单数",
                orderNoInners:null,
                noClac:false,
                zeroAmount:false,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            isExport: false,
            summaryarry: {},
            selids: [],
            fileList: [],
            fileparm: {},

            importFeeType: "",
            myWarehouseList: [],

            dialogUploadData: {
                isSuccess: true,
                title: "",
                visible: false,
                uploadLoading: false,
                importWarehouseCode: 0,
                importFeeType: "",
            },
        };
    },
    async mounted() {
        //await this.onSearch();
    },
    async created() {
        await this.getWarehouseList();
    },
    methods: {
        async getWarehouseList() {
            this.myWarehouseList = [];
            const res = await getTbWarehouseList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.myWarehouseList.push({ label: res[i].name, value: res[i].wms_co_id })
                }
            }
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList() {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.daterange) {
                this.filter.startTime = this.filter.daterange[0];
                this.filter.endTime = this.filter.daterange[1];
            }
            else {
                this.$message({ type: 'error', message: '请输入日期!' });
                return;
            }
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.listLoading = true;
            const res = await GetyOutWarehouseFeePageList(params);
            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            this.listLoading = false;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        async onReClac(row){
          var res = await ReClacOutWarehouseFee(row);
           if (res?.success) {
                this.$message({ message: "提交成功,正在重算中...", type: "success" });
            }
        },

        async onImport(feeType) {
            this.dialogUploadData.importWarehouseCode = this.filter.warehouseCode;
            this.dialogUploadData.importFeeType = feeType;
            this.dialogUploadData.title = "导入" + feeType;
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("dataType", this.dialogUploadData.importFeeType);
            form.append("warehouseCode", this.dialogUploadData.importWarehouseCode.toString());
            form.append("warehouseName", this.myWarehouseList.find(f => f.value == this.dialogUploadData.importWarehouseCode)?.label);
            console.log(form, 'form')
            var res = await ImportOutWarehouseFee(form);
            if (res?.success) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            }
            this.dialogUploadData.uploadLoading = false;
            this.dialogUploadData.isSuccess = res?.success;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.isSuccess)
                this.dialogUploadData.visible = false;
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            if (!this.dialogUploadData.importWarehouseCode) {
                this.$message({ message: "请先选择仓库", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        orderNoInnerBack(val) {
            this.filter.orderNoInners = val;
        },
        async onExport(){
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.isExport = true;
            let res = await exportOutWarehouseFee(params);
            this.isExport = false;
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '出仓成本数据源' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
