<template>
  <my-container>
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
      </el-form>
    </template>

    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true'
      :hasexpand='false' @sortchange='sortchange' :tableData='tableData'
      @select='selectchange' :isSelection='false'
      :tableCols='tableCols' :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button-group>
            <el-button style="padding: 0;margin: 0;">
              <el-select v-model="filter.userName" placeholder="姓名" multiple clearable collapse-tags filterable style="width: 180px;">
                <el-option v-for="item in userNameList" :label="item" :value="item"></el-option>
              </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model="filter.phoneNumber" v-model.trim="filter.phoneNumber" placeholder="电话" style="width:150px;" clearable="true" maxlength="15">
              </el-input>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model="filter.sDesc" v-model.trim="filter.sDesc" placeholder="描述" style="width:150px;" clearable="true" maxlength="10">
              </el-input>
            </el-button>
            <el-button type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
            <el-button type="primary" icon="el-icon-upload" @click="onImport">导入</el-button>
            <el-button type="primary" icon="el-icon-download" @click="onExport">导出</el-button>
          </el-button-group>
        </el-button-group>
      </template>
    </ces-table>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <!-- 人员维护导入 -->
    <el-dialog title="人员维护导入" :visible.sync="dialogVisible" width="40%">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadTemplateFile">下载模版</el-button></span>
      </div>
        <span>
          <el-upload ref="upload" :auto-upload="false" :multiple="true" :limit="1" action accept=".xlsx" 
            :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove" :file-list="fileList">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
          </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
    </el-dialog>
    
    <!-- 人员维护编辑 -->
    <el-dialog title="人员维护编辑" :visible.sync="editVisible" width="400px" v-dialogDrag>
      <el-form ref="formEdit" :model="formEdit" label-width="100px" :rules="rules" >
        <el-form-item label="姓名：" prop="userName">
          <el-select v-model="formEdit.userName" placeholder="姓名" clearable filterable style="width: 250px;" :disabled="true" >
            <el-option v-for="item in userNameList" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="电话：" prop="phoneNumber">
          <el-input v-model="formEdit.phoneNumber" v-model.trim="formEdit.phoneNumber" placeholder="电话" style="width:250px;" clearable maxlength="15" show-word-limit>
          </el-input>
        </el-form-item>

        <el-form-item label="描述：" prop="sDesc">
          <el-input v-model="formEdit.sDesc" v-model.trim="formEdit.sDesc" placeholder="描述" style="width:250px;" clearable maxlength="10" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>

      <div style="text-align: center;">
        <el-button type="primary" @click="editVisible=false">取消</el-button>
        <el-button type="primary" @click="createSubmit">确认</el-button>
      </div>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import { importVideoContributorAsync, getVideoContributorList, exportVideoContributorList, editVideoContributor, deleteVideoContributor, getVideoContributorSelectorList } from "@/api/operatemanage/lookboard";

const tableCols = [
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: "姓名", prop: "userName" },
  { sortable: 'custom', istrue: true, width: '150', align: 'center', label: "电话", prop: "phoneNumber" },
  { sortable: 'custom', istrue: true, width: '280', align: 'center', label: "描述", prop: "sDesc", formatter: (row) => { return row.sDesc+" " } },
  {
    istrue: true, type: 'button', width: '120', label: '操作', btnList: [
      { label: "编辑", handle: (that, row) => that.onEdit(row) },
      { label: "删除", handle: (that, row) => that.onDelete(row) }
    ]
  }
]

export default {
  name: 'VideoContributor',
  components: { MyContainer, cesTable },
  data() {
    return {
      that: this,
      listLoading: false,

      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        userName: [],//姓名
        phoneNumber: "",//电话
        sDesc: "",//描述
      },
      total: 0,
      tableData: [],
      tableCols: tableCols,
      userNameList: [],
      
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],

      editVisible: false,
      formEdit: {
        id: null,//
        userName: null,//姓名
        phoneNumber: "",//电话
        sDesc: "",//描述
      },

      rules: {
        userName: [
          { required: true, message: '请选择姓名', trigger: 'change' }
        ],
        phoneNumber: [
          { required: true, message: '请输入电话', trigger: 'change' }
        ],
        sDesc: [
          { required: true, message: '请输入描述', trigger: 'change' }
        ],
      },
    }
  },
  async mounted() {
    this.init();
    this.getList();
  },
  methods: {
    async init() {
      const { data } = await getVideoContributorSelectorList();
      this.userNameList = [...new Set(data)];
      console.log("姓名",this.userNameList);
    },
    // 排序查询
    sortchange(column) {
      if (column.order) {
        this.filter.orderBy = column.prop;
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.listLoading = true;
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
      this.listLoading = false;
    },
    // 当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList();
    },
    // 查询
    onSearch() {
      //点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      const { data, success } = await getVideoContributorList(this.filter);
      this.listLoading = false;
      if (success) {
        this.tableData = data.list;
        this.total = data.total;
      } else {
        this.$message.error("获取订单处理数据失败");
      }
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportVideoContributorList(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '人员维护_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    downLoadTemplateFile() {
      window.open("/static/excel/operateManage/拼多多内容业绩统计_人员维护导入模版.xlsx", "_blank");
    },
    // 打开导入弹窗
    onImport() {
      this.fileList = [];
      this.dialogVisible = true;
    },
    // 上传文件
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true;
      const form = new FormData();
      form.append("upfile", item.file);
      const res = await importVideoContributorAsync(form);
      if (res?.success) {
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.dialogVisible = false;
      }
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
    },
    // 更改文件
    async uploadChange(file, fileList) {
      this.fileList = fileList;
    },
    // 移除上传文件
    async uploadRemove() {
      this.fileList.splice(0, 1);
    },
    // 提交上传文件
    async submitUpload() {
      if (this.fileList.length == 0) {
        this.$message.warning('您没有选择任何文件！');
        return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
      this.importVisible = false;
    },
    // 打开人员维护编辑弹窗
    async onEdit(row) {
      this.formEdit = {
        id: row.id,
        userName: row.userName,
        phoneNumber: row.phoneNumber,
        sDesc: row.sDesc,
      }
      this.editVisible = true;
    },
    // 提交人员维护编辑
    async createSubmit() {
      if (null == this.formEdit.userName) {
        this.$message.warning('请输入姓名！');
        return;
      }
      // if (null == this.formEdit.phoneNumber) {
      //   this.$message.warning('请输入电话！');
      //   return;
      // }
      const phoneRegex = /^(1[3-9]\d{9})|(0\d{2,3}-\d{7,8})$/;
      if (!phoneRegex.test(this.formEdit.phoneNumber)) {
        this.$message.warning('电话格式不正确！');
        return;
      }
      if (null == this.formEdit.sDesc) {
        this.$message.warning('请输入描述！');
        return;
      }
      if (this.formEdit.sDesc.includes(' ')) {
        this.$message.warning('描述不能包含空格！');
        return;
      }
      if (this.formEdit.sDesc.includes(',')) {
        this.$message.warning('描述不能包含逗号！');
        return;
      }
      if (this.formEdit.sDesc.includes('，')) {
        this.$message.warning('描述不能包含中文逗号！');
        return;
      }
      if (this.formEdit.sDesc.includes('。')) {
        this.$message.warning('描述不能包含中文句号！');
        return;
      }
      this.$confirm('是否提交？', '提示', {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await editVideoContributor(this.formEdit);
        if (res?.success) {
          this.$message({ message: "提交成功", type: "success" });
          this.editVisible = false;
          this.getList();
        }
        // else {
        //   this.$message.error("提交失败");
        // }
      })
    },
    // 删除人员维护信息
    async onDelete(row) {
      this.formEdit.id = row.id;
      this.$confirm('是否删除？', '提示', {
        confirmButtonClass: '确定',
        cancelButtonClass: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteVideoContributor(this.formEdit);
        if (res?.success) {
          this.$message({ message: "删除成功", type: "success" });
          this.getList();
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    }
  }
}
</script>