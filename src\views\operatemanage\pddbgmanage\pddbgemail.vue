<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="时间">
                    <el-date-picker style="width: 230px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="运营组：">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 110px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="站内信类别:">
                    <el-input v-model="filter.emailType" v-model.trim="filter.emailType" placeholder="站内信类别" style="width: 120px" maxlength="50" clearable trim></el-input>
                </el-form-item>
                <el-form-item label="重要:">
                        <el-select  v-model="filter.isImportant" placeholder="重要"
                            :collapse-tags="true" clearable >
                            <el-option label="重要" :value="true"></el-option>
                        </el-select>
               </el-form-item>
                <el-form-item label="是否处理:">
                        <el-select v-model="filter.isHandel" placeholder="是否处理"
                            :collapse-tags="true" clearable >
                            <el-option label="未处理" :value="false"></el-option>
                            <el-option label="已处理" :value="true"></el-option>
                        </el-select>
               </el-form-item>
               <el-form-item label="处理人:">
                    <el-select filterable v-model="filter.handeler" placeholder="请选择处理人" clearable style="width: 120px">
                        <el-option v-for="(item,i) in handelerList" :key="i" :label="item.msg"
                            :value="item.haderUserId" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSetImportant">设置重要</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :loading="listLoading" :summaryarry="summaryarry">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>


        <el-dialog :visible.sync="detail.visible" :show-close="true" v-dialogDrag width="90%" append-to-body>
            <div style="height: 650px;">
                <ces-table ref="detailTable" :that='that' :isIndex='true' :hasexpand='true'
                    @sortchange='detialSortchange' :tableData='detail.list' :tableCols='detailTableCols'
                    :isSelection="false" :loading="detialListLoading">
                </ces-table>
            </div>
            <my-pagination ref="detailPager" :total="detailTotal" :checked-count="sels.length"
                @get-page="getDetialList" />
        </el-dialog>

        <el-drawer :title="detail.info.subject" :visible.sync="detail.drawerShow" direction="rtl">
            <div style="padding-left:20px;">
                <div v-html="detail.info.detailInfo"></div>
            </div>
        </el-drawer>

   <el-dialog title="设置重要" :show-close="false" :visible.sync="pddbdemailImportantVisible" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
        <div style="width:100%;margin:15px auto 70px auto;">
            <span style="margin-left:5px">站内信类别:</span>
                   <el-select filterable v-model="pddbdemailImportant.OperateManageIds" placeholder="请选择站内信类别" multiple clearable style="width: 260px">
                        <el-option v-for="(item,i) in pddEmailTyleList" :key="i" :label="item.label"
                            :value="item.value" />
                    </el-select>
          </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pddbdemailImportantVisible = false">取消</el-button>
          <el-button @click="submitSetImportant" v-throttle="3000" type="primary" v-loading="btnloading">保存</el-button>
        </span>
      </template>
    </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getPingduoduoBGEmailList, getPingduoduoBGEmailGroupList,exportPingduoduoBGEmailGroupList,
    setPddBackGroundImportant,handlePddBackGroundEmail,getPddEmailDetial,getHandelerList } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
import { getDirectorGroupList,getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
import { formatTime } from "@/utils";

const tableCols = [
    { istrue: true, prop: 'date', label: '日期', tipmesg: '', width: '150', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'emailType', label: '站内信类别', width: '400', sortable: 'custom', formatter: (row) => row.emailType },
    { istrue: true, prop: 'isImportant', label: '重要', width: '100', sortable: 'custom', formatter: (row) => {return row.isImportant?"重要":"不重要"} },
    { istrue: true, prop: 'orderCount', label: '扣款单数', width: '200', sortable: 'custom', type: 'click', handle: (that, row) => that.openDetail(row) },
    { istrue: true, prop: 'deductAmount', label: '扣款金额', sortable: 'custom'},
    {  istrue: true, type: 'isHandel', label: '是否处理', width: '100',style:(that,row)=>{return row.isHandel==true?"color:gray;":"color: blue;cursor: pointer"}, formatter: (row) =>{ return row.isHandel==true?"已处理":"未处理"},type:'click',handle: (that, row) => that.handelOp(row)},
    { istrue: true, prop: 'handlerUserName', label: '处理人', sortable: 'custom'},
    { istrue: true, prop: 'handlerTime', label: '处理时间', sortable: 'custom'}
]

const detailTableCols = [
    { istrue: true, prop: 'date', label: '日期', tipmesg: '', width: '150', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) => row.groupName },
    { istrue: true, prop: 'subject', label: '主题', width: '400', sortable: 'custom', type: 'html', formatter: (row) => row.subject, type: 'click',handle: (that, row) => that.openDetailForm(row) },
    { istrue: true, prop: 'orderCount', label: '扣款单数', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'deductAmount', label: '扣款金额', sortable: 'custom', }
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPddBGEmail',
    components: { container, cesTable, MyConfirmButton },
    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                groupId: null,
                shopCode: null,
                emailType: null,
                timerange: [startDate, endDate],
                isImportant:true,
                isHandel:false,
                handeler:null
            },
            list: [],
            shopList: [],
            directorGroupList:[],
            pddEmailTyleList:[],
            pager: { OrderBy: "date", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            tableCols: tableCols,
            detailTableCols: detailTableCols,
            total: 0,
            detailTotal: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            detialListLoading: false,
            detail: {
                visible: false,
                filter: {
                    startTime: null,
                    endTime: null,
                    emailType: null,
                    shopName: null
                },
                info:{
                   detailInfo:"",

                },
                list: [],
                pager: { OrderBy: "date", IsAsc: false },
                drawerShow:false,
            },
            summaryarry:{},
            //设置重要
            pddbdemailImportantVisible:false,
            pddbdemailImportant: {
                OperateManageIds: []
            },
            addLoading: true,
            btnloading:false,
            handelerList:[]
        };
    },

    async mounted() {
        await this.onSearch()
        await this.loadListData()
    },

    methods: {
        async openDetail(row) {
            this.detail.visible = true;
            this.detail.filter.startTime = row.date;
            this.detail.filter.endTime = row.date;
            this.detail.filter.emailType = row.pddEmailTypeId;
            this.detail.filter.shopName = row.shopName;
            this.$nextTick(async function () {
                await this.onSearchDetail();
            })
        },
        //获取店铺
        async loadListData() {
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list

            let res3 = await getDirectorGroupList({})
            this.directorGroupList = res3.data;

            var resEmailType = await getPddEmailDetial({typeId:1,flag:0});
            this.pddEmailTyleList=resEmailType?.data;
            this.pddbdemailImportant.OperateManageIds=[];
            this.pddEmailTyleList.forEach(item => {
                if (item.isImportant) {
                    this.pddbdemailImportant.OperateManageIds.push(item.value);
                }
            })
            await this.getHandelList();
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="date";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getPingduoduoBGEmailGroupList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
            this.summaryarry=res.data.summary;
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        //查询第一页
        async onSearchDetail() {
            this.$refs.detailPager.setPage(1)
            await this.getDetialList()
        },
        async getDetialList() {
            let pager = this.$refs.detailPager.getPager();
            let page = this.detail.pager;
            const params = { ...pager, ...page, ... this.detail.filter }
            if (params === false) {
                return;
            }
            this.detialListLoading = true
            const res = await getPingduoduoBGEmailList(params)
            this.detialListLoading = false
            if (!res?.success) {
                return
            }
            this.detailTotal = res.data.total;
            const data = res.data.list;
            this.detail.list = data
        },
        async detialSortchange(column) {
            if (!column.order)
                this.detail.pager = {};
            else {
                this.detail.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearchDetail();
        },
        openDetailForm(row){
            this.detail.info.subject=row.subject;
            this.detail.info.detailInfo=row.detailInfo;
            this.detail.drawerShow=true;
        },
        async onExport(){
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="date";
                this.pager.IsAsc=false;
            }
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }else {
                this.$message({ message: "请选择时间！", type: "warning" });
                return;
            }
            let pager = this.$refs.pager.getPager();
            let params = {  ...pager,   ...this.pager,   ...this.filter};
            let res = await exportPingduoduoBGEmailGroupList(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }

            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多站内信_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
       async submitSetImportant () {
         var res = await setPddBackGroundImportant({operateManageIds:this.pddbdemailImportant.OperateManageIds});
         if (!res?.success) {
            this.btnloading = false;
            return
        }
        this.$message({ type: 'success', message: "设置成功" });
        this.btnloading = false;
        this.pddbdemailImportantVisible = false;
        await  this.onSearch();

        },
        onSetImportant () {
          this.pddbdemailImportantVisible = true;
        },
        async handelOp(row)
        {
            if(row.isHandel)
            {
                this.$message({type: 'info',message: '已处理不可重复处理'});
                return;
            }
            this.$confirm('确认处理吗, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
            }).then(async () => {
                const res = await handlePddBackGroundEmail({typeId:1,pddEmailTypeId:row.pddEmailTypeId,date:row.date,shopName:row.shopName,groupId:row.groupId});
                if (!res?.success) {return }
                this.$message({type: 'success',message: '操作成功!'});
                await this.getHandelList();
                await this.onSearch()
            }).catch((x) => {
            this.$message({type: 'info',message: "取消操作"});
            });
        },
        async getHandelList()
        {
            var resHandeler = await getHandelerList({typeId:1});
            this.handelerList = resHandeler?.data;
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
