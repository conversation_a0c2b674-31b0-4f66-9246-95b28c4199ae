<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="时间:">
          <el-date-picker style="width: 210px" v-model="filter.yearMonth" type="monthrange" range-separator="至"
            start-placeholder="开始月份" end-placeholder="结束月份" format="yyyyMM" value-format="yyyyMM"
            placeholder="选择月份"></el-date-picker>
        </el-form-item>

        <el-form-item label="运营:">
          <el-input style="width: 100px" v-model.trim="filter.groupName" clearable maxlength="50" placeholder="运营小组" />
        </el-form-item>
        <el-form-item label="平台:">
          <el-select :clearable="true" :collapse-tags="true" multiple style="width: 150px" v-model="PlatformList11"
            placeholder="请选择平台">
            <!-- <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value"/> -->
            <el-option label="淘系" value='1,9'></el-option>
            <el-option label="淘工厂" value='8'></el-option>
            <el-option label="拼多多" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-input style="width: 100px" v-model.trim="filter.shopName" clearable maxlength="50" placeholder="店铺" />
        </el-form-item>
        <el-form-item label="系列编码:">
          <!-- <el-input style="width: 100px" v-model.trim="filter.SeriesCoding" maxlength="50"   placeholder="系列编码"/> -->
          <el-select v-model="filter.SeriesCoding" multiple filterable remote reserve-keyword placeholder="系列编码"
            clearable :remote-method="remoteMethod" :loading="searchloading">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="startImport">导入数据</el-button>

          <el-button type="primary" @click="ClickdownloadTemplate">下载模板</el-button>
        </el-form-item>

      </el-form>
    </template>
    <el-tabs v-model="activeName" style="height: 94%;">

      <el-tab-pane label="淘系明细1" name="Sixth" style="height: 100%;">
        <detail1Tx :filter="filter" ref="detail1Tx" />
      </el-tab-pane>
      <el-tab-pane label="淘系明细2" name="Seventh" style="height: 100%;">
        <detail2Tx :filter="filter" ref="detail2Tx" />
      </el-tab-pane>
      <el-tab-pane label="拼多多明细1" name="Eighth" style="height: 100%;">
        <detail1Pdd :filter="filter" ref="detail1Pdd" />
      </el-tab-pane>
      <el-tab-pane label="拼多多明细2" name="Ninth" style="height: 100%;">
        <detail2Pdd :filter="filter" ref="detail2Pdd" />
      </el-tab-pane>

      <el-tab-pane label="运营利润汇总" name="first" style="height: 100%;">
        <operatingProfitAnalysisSum :filter="filter" ref="operatingProfitAnalysisSum" />
      </el-tab-pane>
      <el-tab-pane label="店铺利润汇总" name="second" style="height: 100%;">
        <shopProfitAnalysisSum :filter="filter" ref="shopProfitAnalysisSum" />
      </el-tab-pane>
      <!-- <el-tab-pane label="系列编码利润汇总" name="third" style="height: 100%;">
     <operatingProfitAnalysis :filter="filter" ref="operatingProfitAnalysis"/>
    </el-tab-pane> -->
      <!-- <el-tab-pane label="店铺利润分析" name="fourth" style="height: 100%;">
      <shopProfitAnalysis :filter="filter" ref="shopProfitAnalysis"/>
    </el-tab-pane> -->
      <el-tab-pane label="系列编码利润汇总" name="fifth" style="height: 100%;">
        <productProfitAnalysis :filter="filter" ref="productProfitAnalysis" />
      </el-tab-pane>


    </el-tabs>

    <el-dialog title="下载导入模板" :visible.sync="dialogVisible" width="50%">

      <el-row>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-select filterable v-model="TemplateType.Type" placeholder="请选择您要下载的模板" clearable>
            <el-option label="拼多多明细1-模板" value="1"></el-option>
            <el-option label="拼多多明细2-模板" value="2"></el-option>
            <el-option label="淘系明细1-模板" value="3"></el-option>
            <el-option label="淘系明细2-模板" value="4"></el-option>
            <el-option label="快递费-模板" value="5"></el-option>
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-button type="primary" @click="downloadTemplate">下载</el-button>
        </el-col>

      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="ImportdialogVisible" width="50%">

      <el-row>
        <el-alert title="温馨提示：导入快递费时不需要选择类型" type="success" :closable="false" style="margin-bottom:10px;">
        </el-alert>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">

          <el-select filterable v-model="importfilter.Type" placeholder="请选择要导入的表" clearable>
            <el-option label="拼多多明细1" value="1"></el-option>
            <el-option label="拼多多明细2" value="2"></el-option>
            <el-option label="淘系明细1" value="3"></el-option>
            <el-option label="淘系明细2" value="4"></el-option>
            <el-option label="快递费" value="5"></el-option>
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-select filterable v-model="importfilter.version" placeholder="类型" clearable>
            <el-option label="工资月报" value="v1"></el-option>
            <el-option label="参考月报" value="v2"></el-option>
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
            :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove" :file-list="fileList">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
          </el-upload>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button @click="ImportdialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>
<script>

import { rulePlatform } from "@/utils/formruletools";
import { importOperatingProfitAnalysis, importShopProfitAnalysis, importShopProfitsCourierFee, importPDDProfitAnalysisDetail1, importPDDProfitAnalysisDetail2 } from '@/api/bookkeeper/import'
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import operatingProfitAnalysisSum from '@/views/bookkeeper/ProfitAnalysis/operatingProfitAnalysisSum'
import shopProfitAnalysisSum from '@/views/bookkeeper/ProfitAnalysis/shopProfitAnalysisSum'
import operatingProfitAnalysis from '@/views/bookkeeper/ProfitAnalysis/operatingProfitAnalysis'
import shopProfitAnalysis from '@/views/bookkeeper/ProfitAnalysis/shopProfitAnalysis'
import productProfitAnalysis from '@/views/bookkeeper/ProfitAnalysis/productProfitAnalysis'
import detail1Tx from '@/views/bookkeeper/ProfitAnalysis/detail1Tx'
import detail2Tx from '@/views/bookkeeper/ProfitAnalysis/detail2Tx'
import detail1Pdd from '@/views/bookkeeper/ProfitAnalysis/detail1Pdd'
import detail2Pdd from '@/views/bookkeeper/ProfitAnalysis/detail2Pdd'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import { getListByStyleCode } from "@/api/inventory/basicgoods"
export default {
  name: "Users",
  components: { container, operatingProfitAnalysisSum, shopProfitAnalysisSum, operatingProfitAnalysis, shopProfitAnalysis, productProfitAnalysis, detail1Tx, detail2Tx, detail1Pdd, detail2Pdd },
  data() {
    return {
      options: [],
      platformList: [],
      PlatformList11: [],
      uploadLoading: false,
      importfilter: {
        Type: '',
        version: ''
      },
      TemplateType: {
        Type: ''
      },
      dialogVisible: false,
      ImportdialogVisible: false,
      filter: {
        yearMonth: '',
        groupName: '',
        shopName: '',
        SeriesCoding: [],
        Platform: [],
        PlatformName: null,
        startTime: '',
        endTime: ''
      },
      activeName: 'Sixth',
      that: this,
      pageLoading: false,
      importNumber: '',
      searchloading: false,
      fileList: []
    };
  },
  watch: {
    value(n) {
      if (n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
  async mounted() {
    await this.setPlatform();
  },
  methods: {
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },

    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;

    },
    //导入
    startImport() {
      this.importfilter.Type = ''
      this.importfilter.version = ''
      this.uploadLoading = false
      this.fileList = []
      this.ImportdialogVisible = true;
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      });
    },

    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("version", this.importfilter.version);
      if (this.importfilter.Type == 1) {
        const res = await importPDDProfitAnalysisDetail1(form);
        if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.fileList = []
        this.uploadLoading = false;
        this.importfilter.Type = '';
      }
      if (this.importfilter.Type == 2) {
        const res = await importPDDProfitAnalysisDetail2(form);
        if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.fileList = []
        this.uploadLoading = false;
        this.importfilter.Type = '';
      }


      if (this.importfilter.Type == 3) {
        const res = await importShopProfitAnalysis(form);
        if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.fileList = []
        this.uploadLoading = false;
        this.importfilter.Type = '';
      }
      if (this.importfilter.Type == 4) {
        const res = await importOperatingProfitAnalysis(form);
        if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.fileList = []
        this.uploadLoading = false;
        this.importfilter.Type = '';
      }
      if (this.importfilter.Type == 5) {
        const res = await importShopProfitsCourierFee(form);
        if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.fileList = []
        this.uploadLoading = false;
        this.importfilter.Type = '';
      }

    },
    async uploadChange(file, fileList) {
      let list = [];
      list.push(file);
      this.fileList = list;
    },
    uploadRemove(file, fileList) {
      this.fileList = []
    },
    async submitUpload() {
      if (this.importfilter.Type == '') {
        this.$message({ message: "请先选择上传的表", type: "warning" });
        return false;
      }

      if (this.importfilter.version == '' && this.importfilter.Type != '5') {
        this.$message({ message: "请选择月报类型", type: "warning" });
        return false;
      }
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }

      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    downloadTemplate() {
      if (this.TemplateType.Type == '') {
        this.$message({ message: "请选择要下载的导入模板", type: "warning" });
        return false;
      }
      if (this.TemplateType.Type == 1) {
        window.open("../../static/excel/financial2/profit/拼多多明细1模板.xlsx", "_self");
        this.TemplateType.Type = '';
      }
      if (this.TemplateType.Type == 2) {
        window.open("../../static/excel/financial2/profit/拼多多明细2模板.xlsx", "_self");
        this.TemplateType.Type = '';
      }
      if (this.TemplateType.Type == 3) {
        window.open("../../static/excel/financial2/profit/淘系明细1模板.xlsx", "_self");
        this.TemplateType.Type = '';
      }
      if (this.TemplateType.Type == 4) {
        window.open("../../static/excel/financial2/profit/淘系明细2模板.xlsx", "_self");
        this.TemplateType.Type = '';
      }
      if (this.TemplateType.Type == 5) {
        window.open("../../static/excel/financial2/profit/快递费模板.xlsx", "_self");
        this.TemplateType.Type = '';
      }
    },

    ClickdownloadTemplate() {
      this.dialogVisible = true;
    },
    async onSearch() {
      if (this.filter.yearMonth) {
        this.filter.startTime = this.filter.yearMonth[0];
        this.filter.endTime = this.filter.yearMonth[1];
      }
      this.filter.Platform = [];
      this.filter.Platform = this.PlatformList11.join();
      if (this.activeName == 'first') this.$refs.operatingProfitAnalysisSum.onSearch();
      if (this.activeName == 'second') this.$refs.shopProfitAnalysisSum.onSearch();
      if (this.activeName == 'third') this.$refs.operatingProfitAnalysis.onSearch();
      if (this.activeName == 'fourth') this.$refs.shopProfitAnalysis.onSearch();
      if (this.activeName == 'fifth') this.$refs.productProfitAnalysis.onSearch();
      if (this.activeName == 'Sixth') this.$refs.detail1Tx.onSearch();
      if (this.activeName == 'Seventh') this.$refs.detail2Tx.onSearch();
      if (this.activeName == 'Eighth') this.$refs.detail1Pdd.onSearch();
      if (this.activeName == 'Ninth') this.$refs.detail2Pdd.onSearch();
    },

  },
};
</script>