<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.platforms" placeholder="平台" collapse-tags multiple filterable clearable
                    class="publicCss">
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <dateRange :startDate.sync="ListInfo.launchStartTime" :endDate.sync="ListInfo.launchEndTime"
                    class="publicCss" start-placeholder="发起时间" end-placeholder="结束时间" />
                <dateRange :startDate.sync="ListInfo.approvedStartTime" :endDate.sync="ListInfo.approvedEndTime"
                    class="publicCss" start-placeholder="审批时间" end-placeholder="结束时间" />
                <dateRange :startDate.sync="ListInfo.onStartTime" :endDate.sync="ListInfo.onEndTime" class="publicCss"
                    start-placeholder="上架时间" end-placeholder="结束时间" />
                <el-select filterable v-model="ListInfo.operateSpecialUserIds" collapse-tags clearable
                    placeholder="运营专员" multiple class="publicCss">
                    <el-option key="无运营专员" label="无运营专员" :value="0"></el-option>
                    <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0;height: 400px;" :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatLinkProCode, formatPlatform, platformlist } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { GetYWOptimizeProductOptimizeRecordList } from '@/api/inventory/YWOptimizeGoods'
import { getDirectorList, } from '@/api/operatemanage/base/shop'
const tableCols = [ 
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'platform', label: '平台', formatter: (row) => formatPlatform(row.platform) },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'directorId', label: '运营专员',formatter:(row) => row.directorName },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'launchTime', label: '发起时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'approvedTime', label: '审批时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'onTime', label: '上架时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'proCode', label: 'ID', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    props: {
        editRow: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                launchStartTime: null,
                launchEndTime: null,
                onStartTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                onEndTime: dayjs().format('YYYY-MM-DD'),
                goodsCode: this.editRow.goodsCode,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            directorlist: []
        }
    },
    async mounted() {
        this.init()
        await this.getList()
    },
    methods: {
        async init() {
            var res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口0
            try {
                const { data, success } = await GetYWOptimizeProductOptimizeRecordList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
