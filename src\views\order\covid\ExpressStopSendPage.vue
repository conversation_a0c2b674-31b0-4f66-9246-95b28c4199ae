<template>
    <my-container v-loading="pageLoading">
        <ces-table style="height: 100%;" :ref="tablekey" :key="tablekey" :that='that' :isIndex='true' :isSelectColumn='false' :hasexpand='false' @sortchange='sortchange' @cellclick="mainCellClick" :tableData='list' :tableCols='tableCols' :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination style="text-align:left;width:800px;" ref="pager" :total="total" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import cesTable from "@/components/Table/table.vue";
    import { getExpressStopSendDataAsync, setExpressEnabledAsync } from "@/api/order/covidStopSendPage";
    export default {
        name: 'ExpressStopSendPage',
        components: { cesTable, MyContainer },
        props: {
            filter: {
                type: Object,
                default: null
            }
        },
        data() {
            return {
                tablekey: "expressStopSendPageMain",
                that: this,
                tableCols: [],
                list: [],
                summaryarry: {},
                pager: {},
                importExpressName: "",
                importWarehouse: "",
                warehouselist: [{ label: "南昌", value: "南昌" }, { label: "义乌", value: "义乌" }],
                dialogVisible: false,
                dialogEportVisible: false,
                exportWarehouse: "",
                total: 0,
                listLoading: false,
                pageLoading: false,
                importLoading: false,
                fileList: [],
            }
        },
        async mounted() {
            this.initTableCols();
            await this.onSearch();
        },
        methods: {
            initTableCols() {
                var showTableCols = [];
                showTableCols.push({ istrue: true, prop: 'expressName', label: '快递公司', width: '250', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'warehouse', label: '发货仓', width: '100', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'enabled', label: '是否启用', width: '80', sortable: 'custom', type: 'click', handle: (that, row) => that.setExpressEnabled(row), formatter: (row) => row.enabled == 0 ? "启用" : "禁用" });
                this.tableCols = showTableCols;
            },
            async setExpressEnabled(row) {
                await setExpressEnabledAsync({ "expressId": row.id, "enabled": !row.enabled });
                await this.getlist()
            },
            //获取查询条件
            getCondition() {
                if (this.filter.stopSendTimerange && this.filter.stopSendTimerange.length > 1) {
                    this.filter.startTime = this.filter.stopSendTimerange[0];
                    this.filter.endTime = this.filter.stopSendTimerange[1];
                } else {
                    this.filter.startTime = null;
                    this.filter.endTime = null;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getExpressStopSendDataAsync(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            mainCellClick(row, column, cell) {
                this.$emit("changeDetailData", row.id);
            }
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
</style>
