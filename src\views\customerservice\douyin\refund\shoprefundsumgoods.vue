<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter">
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :summaryarry="summaryarry" :tableData='pagelist' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    getSumRefundGoodsDtlPageList
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '300', sortable: 'custom' },
    // { istrue: true, prop: 'backOrderCount', label: '退货退款单量', width: '110', sortable: 'custom' },
    // { istrue: true, prop: 'backAllAmount', label: '退货退款总金额', width: '120', sortable: 'custom' },
    // { istrue: true, prop: 'backExpressCount', label: '退回快递', width: '80', sortable: 'custom' },
    // { istrue: true, prop: 'backExpressComCount', label: '退货物流公司', width: '110', sortable: 'custom' },
    // { istrue: true, prop: 'onlyOrderCount', label: '仅退款单量', width: '100', sortable: 'custom' },
    // { istrue: true, prop: 'onlyAllAmount', label: '仅退款总金额', width: '110', sortable: 'custom' },
    // { istrue: true, prop: 'onlysaleAfterReasonCount', label: '仅退款售后原因', width: '120', sortable: 'custom' },

    { istrue: true, prop: 'backYesOrderCount', label: '已退货退款单量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'backNoOrderCount', label: '未退货退款单量', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'backYesOrderAmount', label: '已退货退款金额', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'backNoOrderAmount', label: '未退货退款金额', width: '90', sortable: 'custom' },

    { istrue: true, prop: 'onlyYSendOrderCount', label: '已发货仅退款单量', width: '90', sortable: 'custom'},
    { istrue: true, prop: 'onlyNSendOrderCount', label: '未发货仅退款单量', width: '90', sortable: 'custom' }, 
    { istrue: true, prop: 'onlyYSendOrderAmount', label: '已发货仅退款金额', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'onlyNSendOrderAmount', label: '未发货仅退款金额', width: '90', sortable: 'custom' },
];
export default {
    name: "shoprefundsumgoods",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                goodsCode: null,
                goodsName: null,
                shopCode: null,
            },
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "goodsCode", IsAsc: true },
            sels: [], // 列表选中列
            selids: [],
        };
    },
    async mounted() {
    },
    methods: {
        async loadData(filterparam) {
            this.filter.timerange = [filterparam.param.startDate, filterparam.param.endDate];
            this.filter.shopCode = filterparam.param.shopCode;
            if (!filterparam.param.shopCode)
                this.filter.shopCode = '未知';
            this.onSearch();
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await getSumRefundGoodsDtlPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            //this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
