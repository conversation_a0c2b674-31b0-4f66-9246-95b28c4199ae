<template>
  <el-dialog title="查看结果" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-if="isShow"  append-to-body v-dialogDrag>
    <div style="">
      <el-descriptions size="small" class="margin-top" title="" :column="3">
        <el-descriptions-item label="平台/店铺">{{ platformName }} / {{ dataJson.shopName }}</el-descriptions-item>
        <el-descriptions-item label="线上订单号">{{ dataJson.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ conversationTime }} <el-button style="margin-left:20px;" type="primary"
            @click="showOrhide">{{ this.isShowOrHide ? "收起聊天记录" : "展开聊天记录" }}</el-button></el-descriptions-item>
        <el-descriptions-item
          v-if="interfaceType && dataJson?.afterSalesRemark != '' && dataJson?.afterSalesRemark != null"
          label="售后原因">{{ dataJson?.afterSalesRemark }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 聊天记录 -->
    <div v-show="isShowOrHide">
      <chartComponent ref="chartRef" :isShow="isShow"></chartComponent>
    </div>

    <el-form ref="formDataRef" label-width="100px" style="margin-top: 20px">
      <!-- 审核 -->
      <div style="border: 1px #eeeeee solid;padding:20px;">
        <div style="display: flex">
          <el-form-item label="审核:" prop="firstStatus" class="first custom-label">
            <div style="width: 50px;">{{ filterInitialAuditType(dataJson.initialAuditType) }}</div>
          </el-form-item>
          <el-form-item label="审核内容:" style="margin-left: 80px" class="first custom-label"
            v-if="dataJson.initialAuditType == 2">
            <div style="width:300px">{{ dataJson.refuseInitialAuditType }}</div>
          </el-form-item>
        </div>
        <!-- 售后审核显示退款原因稽查，责任人 -->
        <div style="display: flex" v-if="interfaceType">
          <el-form-item label="责任人:" prop="person" class="custom-label">
            <div style='width:100px'>{{ dataJson?.person }}</div>
          </el-form-item>
          <el-form-item label="退款原因稽查:" prop="reasonForRefund" style="margin-left:30px" class="custom-label">
            <div>{{ dataJson?.reasonForRefund }}</div>
          </el-form-item>
        </div>
        <!-- 售前特有字段 -->
<!--        <div style="display: flex" v-if="!interfaceType">
          <el-form-item label="聊天条数:" prop="chatCount" class="custom-label" v-if="dataJson?.chatCount !== undefined">
            <div style='width:100px'>{{ dataJson?.chatCount }}</div>
          </el-form-item>
        </div>-->
        <!-- 责任客服和说明 -->
        <div style="display: flex">
          <el-form-item label="责任客服:" prop="initialResponsibleName" class="custom-label">
            <div style='width:100px'>{{ dataJson.initialResponsibleName }}</div>
          </el-form-item>
          <el-form-item label="说明:" prop="firstExplain" style="margin-left: 30px" class="custom-label">
            <div>{{ dataJson.initialAuditRemark }}</div>
          </el-form-item>
        </div>
        <div style="display: flex">
          <el-form-item label="审核人:" prop="firstStatus" class="custom-label">
            <div style='width:100px'>{{ dataJson.initialOperator }}</div>
          </el-form-item>
          <el-form-item label="审核凭证:" prop="firstStatus" style="margin-left: 30px" class="custom-label">
            <span v-for="(image, index) in iniImgsSplitList" :key="index">
              <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
              <span v-else>无</span>
            </span>
          </el-form-item>
        </div>


      </div>

      <el-collapse :value="activeReviewIndex" @change="(val) => activeReviewIndex = val" style="margin-top: 20px">
        <el-collapse-item :name="0" v-if="reviewList.length > 0">
          <template #title>
            <span>审核信息 1</span>
          </template>

          <div style="display: flex">
            <el-form-item label="审核:" class="first custom-label">
              <div style="width: 50px;">{{ filterInitialAuditType(reviewList[0].initialAuditType) }}</div>
            </el-form-item>
            <el-form-item label="数据编码:" style="margin-left: 50px" class="first custom-label">
              <div style="width: 180px;">{{ reviewList[0].conversationId }}</div>
            </el-form-item>
            <el-form-item label="审核类型:" style="margin-left: 20px" class="first custom-label"
                          v-if="reviewList[0].initialAuditType == 2">
              <div style="width:300px">{{ reviewList[0].refuseInitialAuditType }}</div>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="审核人:" class="custom-label">
              <div style='width:100px'>{{ reviewList[0].initialOperator }}</div>
            </el-form-item>
            <el-form-item label="审核日期:"  class="custom-label">
              <div style='width:150px'>{{ reviewList[0].initialOperatorTime }}</div>
            </el-form-item>
            <el-form-item label="审核凭证:" class="custom-label" style="margin-left: 50px">
                <span v-for="(image, imgIndex) in reviewList[0].initialAuditImgs ? reviewList[0].initialAuditImgs.split(',') : []" :key="imgIndex">
                  <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
                </span>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="责任客服:"  class="custom-label">
              <div style='width:100px'>{{ reviewList[0].initialResponsibleName }}</div>
            </el-form-item>
            <el-form-item label="说明:"  class="custom-label">
              <div>{{ reviewList[0].initialAuditRemark }}</div>
            </el-form-item>
          </div>
        </el-collapse-item>

        <el-collapse-item v-for="(review, index) in reviewList.slice(1)" :key="index" :name="(index + 1).toString()">
          <template #title>
            <span>审核信息 {{ index + 2 }}</span>
          </template>

          <div style="display: flex">
            <el-form-item label="审核:" class="first custom-label">
              <div style="width: 50px;">{{ filterInitialAuditType(review.initialAuditType) }}</div>
            </el-form-item>
            <el-form-item label="数据编码:" style="margin-left: 50px" class="first custom-label">
              <div style="width: 180px;">{{ review.conversationId }}</div>
            </el-form-item>
            <el-form-item label="审核类型:" style="margin-left: 20px" class="first custom-label"
                          v-if="review.initialAuditType == 2">
              <div style="width:300px">{{ review.refuseInitialAuditType }}</div>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="审核人:" class="custom-label">
              <div style='width:100px'>{{ review.initialOperator }}</div>
            </el-form-item>
            <el-form-item label="审核日期:"  class="custom-label">
              <div style='width:150px'>{{ review.initialOperatorTime }}</div>
            </el-form-item>
            <el-form-item label="审核凭证:" class="custom-label" style="margin-left: 50px">
                <span v-for="(image, imgIndex) in review.initialAuditImgs ? review.initialAuditImgs.split(',') : []" :key="imgIndex">
                  <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
                </span>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="责任客服:"  class="custom-label">
              <div style='width:100px'>{{ review.initialResponsibleName }}</div>
            </el-form-item>
            <el-form-item label="说明:"  class="custom-label">
              <div>{{ review.initialAuditRemark }}</div>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-form>
    <template #footer>
      <div class="dialog-footer" style="display:flex;justify-content: flex-end;margin-right:35px">
        <div style="position: relative;">
          <el-button @click="btnChange('last')" type="primary" :disabled="isLastButtonDisabled">查看上一个</el-button>
          <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;">
            <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i
                class="el-icon-question"></i></el-tooltip>
          </div>
        </div>
        <div style="position: relative;margin-left:20px;">
          <el-button @click="btnChange('next')" type="primary" :disabled="isNextButtonDisabled">查看下一个</el-button>
          <div style="position: absolute;right:-20px;top:-20px; cursor:pointer;">
            <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i
                class="el-icon-question"></i></el-tooltip>
          </div>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import chartComponent from "@/views/customerservice/chartCheck/SalesDialog/chartComponent"
import { getChartList } from "@/api/customerservice/unpaidorder";

import { formatTime } from "@/utils";
import {GetUnpayOrderSalesList} from "@/api/customerservice/chartCheck";
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    chatRecordKeywords: {
      type: String,
      default: ''
    },
    scrollTimeout: {
      type: Object,
      default: 3000
    }
  },
  components: {
    chartComponent
  },
  computed: {
    platformName() {
      console.log(this.dataJson)
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0]?.name
      } else {
        return ""
      }
    },
    iniImgsSplitList() //审核图片分割
    {
      return this.dataJson?.initialAuditImgs ? this.dataJson?.initialAuditImgs.split(",") : "";
    },
    /*finalImgsSplitList()//复审图片分割
    {
      return this.dataJson?.finalAuditImgs ? this.dataJson?.finalAuditImgs.split(",") : "";
    },
    judImgsSplitList()//评判图片分割
    {
      return this.dataJson?.judgmentAuditImgs ? this.dataJson?.judgmentAuditImgs.split(",") : "";
    },*/
    conversationTime() {//日期转换
      return this.dataJson?.conversationTime ? formatTime(this.dataJson?.conversationTime, "YYYY-MM-DD") : ""
    },
  },
  data() {
    return {
      dataJson: {},
      isShowOrHide: true,
      tableData: [],
      isLastButtonDisabled: false,
      isNextButtonDisabled: false,
      interfaceType: false,// true：售后 false：售前
      reviewList: [],
      activeReviewIndex: [0],
      allList_pre: ["回复问题","专业能力","敷衍怠慢","存在违规/过度承诺","存在违规/引导线下交易","存在违规/其他违规","态度问题/辱骂客户","态度问题/怒怼客户","态度问题/反问客户","态度问题/不耐烦"],//全部平台售前
      allList_after: ["回复问题","流程问题","敷衍怠慢","专业能力","存在违规/过度承诺","存在违规/引导线下交易","存在违规/其他违规","态度问题/辱骂客户","态度问题/怒怼客户","态度问题/反问客户","态度问题/不耐烦", "小额打款异常"],//全部平台售后
      statusList: [],
      salesType: '', // 0：售前，1：售后
    };
  },
  created() {
    document.addEventListener('keydown', this.handleArrowUp);
  },
  mounted() {
    setTimeout(() => {
      this.$nextTick(() => {
        this.$refs.chartRef.dataJson = this.dataJson;
        // 根据salesType设置interfaceType
        this.interfaceType = this.salesType == 1;
      });
    },100);
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$refs.chartRef.dataJson = this.dataJson;
          // 根据salesType设置interfaceType
          this.interfaceType = this.salesType == 1;
        });
        this.buttonDisabled();//按钮是否可用
        this.handleScrollToKeyword();
      }
      document.addEventListener('keydown', this.handleArrowUp);
    },
    chatRecordKeywords(newVal) {
      if (newVal) {
        this.handleScrollToKeyword();
      }
    },
    interfaceType: {
      immediate: true,
      handler(val) {
        // 根据interfaceType设置审核类型列表
        this.statusList = val ? this.allList_after : this.allList_pre;
      }
    }
  },
  methods: {
    handleArrowUp(event) {
      if (!this.isShow) {
        return
      }
      console.log(event.key)
      if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('last');
      }
      if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('next');
      }
    },
    filterInitialAuditType(type) {
      let name = ''
      if (type == 1) {
        name = '合格'
      } else if (type == 2) {
        name = '不合格'
      }
      return name;
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
    showOrhide() {
      if (this.isShowOrHide)
        this.isShowOrHide = false;
      else this.isShowOrHide = true
    },
    async btnChange(last) {  //查看上一个、查看下一个
      const index = this.tableData.indexOf(this.dataJson);
      if (last == 'last') {
        const info = this.tableData[index - 1]
        this.dataJson = info;
        this.keyWord = info.conversationId;
        this.platform = info.platform;
        this.$refs.chartRef.dataJson = info;
        // 更新interfaceType为当前行的销售类型
        this.interfaceType = info.salesType == 1;
        // this.$refs.chartRef.getChartList()
      } else if (last == 'next') {
        const info = this.tableData[index + 1]
        this.dataJson = info;
        this.keyWord = info.conversationId;
        this.platform = info.platform;
        this.$refs.chartRef.dataJson = info;
        // 更新interfaceType为当前行的销售类型
        this.interfaceType = info.salesType == 1;
        // this.$refs.chartRef.getChartList()
      }
      //获取是否已经审核的数据
      if (this.dataJson.conversationUserId) {
        var params = {
          conversationUserId: this.dataJson.conversationUserId,
          conversationId: this.dataJson.conversationId,
          salesType: this.salesType,
          shopId: this.dataJson.shopId,
          auditState:2,
          orderBy: "createdTime",
          isAsc: false
        }
        const res = await GetUnpayOrderSalesList(params);
        if (!res?.success) {
          return;
        }
        this.reviewList = res.data.list;
      } else {
        this.reviewList = [];
      }
      await this.buttonDisabled()//按钮是否禁用
    },
    async buttonDisabled() { //按钮是否禁用
      this.isLastButtonDisabled = false;
      this.isNextButtonDisabled = false;
      const index = this.tableData.indexOf(this.dataJson);
      if (index == 0) {
        this.isLastButtonDisabled = true;
      }
      if (index == this.tableData.length - 1) {
        this.isNextButtonDisabled = true;
      }
    },
    handleScrollToKeyword() {
      if (this.chatRecordKeywords) {
        clearTimeout(this.scrollTimeout);
        // 延迟调用，确保聊天记录已经加载完成
        this.scrollTimeout = setTimeout(() => {
          this.$refs.chartRef.scrollToKeyword(this.chatRecordKeywords);
        }, 500);
      }
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  color: #888888 !important;
  /* 更改为你想要的颜色 */
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding: 15px;
}

//顶部可点击div样式
::v-deep .el-descriptions__body .el-descriptions__table {
  background-color: rgb(242, 244, 245);
}

::v-deep .el-descriptions__body .el-descriptions-item__container {
  font-size: 14px;
}

.first ::v-deep .el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
</style>
