<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="阳光隔热膜设置" name="first" style="height: 100%;" v-if="checkPermission('yggrmSet')">
                <yggrmSet />
            </el-tab-pane>
            <el-tab-pane label="透明桌垫设置" name="second" style="height: 100%;" :lazy="true"
                v-if="checkPermission('tmzdSet')">
                <tmzdSet />
            </el-tab-pane>
            <el-tab-pane label="阳光隔热膜常规款设置" name="third" style="height: 100%;" :lazy="true">
                <yggrmcgkSet />
            </el-tab-pane>
            <el-tab-pane label="快递费" name="forth" style="height: 100%;" :lazy="true">
                <courierFee />
            </el-tab-pane>
            <el-tab-pane label="皮桌垫设置" name="fifth" style="height: 100%;" :lazy="true">
                <pzdSet v-if="checkPermission('pzdSet')"/>
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import yggrmSet from './components/yggrmSet.vue';
import tmzdSet from './components/tmzdSet.vue';
import yggrmcgkSet from './components/yggrmcgSet.vue';
import courierFee from './components/courierFee.vue';
import pzdSet from './components/pzdSet.vue';
export default {
    components: {
        MyContainer, yggrmSet, tmzdSet, yggrmcgkSet, courierFee, pzdSet
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>