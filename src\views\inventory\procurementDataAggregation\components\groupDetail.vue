<template>
  <my-container>
    <template #header>
      <el-form :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 150px" v-model="filter.rptDate" :clearable="false" type="month"
            @change="getList('search')" placeholder="请选择月份" icon="el-icon-date" format="yyyy-MM" value-format="yyyy-MM">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="组别">
          <el-select v-model="filter.deptNameList" placeholder="请选择" style="width: 170px;" clearable multiple
            collapse-tags filterable>
            <el-option label="采购1组" value="采购1组"></el-option>
            <el-option label="采购2组" value="采购2组"></el-option>
            <el-option label="采购3组" value="采购3组"></el-option>
            <el-option label="采购4组" value="采购4组"></el-option>
            <el-option label="采购一组" value="采购一组"></el-option>
            <el-option label="采购二组" value="采购二组"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select v-model="filter.areaNameList" placeholder="请选择" style="width: 170px;" clearable multiple
            collapse-tags filterable>
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="武汉" value="武汉"></el-option>
            <el-option label="深圳" value="深圳"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <!-- <el-button type="primary" @click="onCalculate(2)"
            v-if="checkPermission('api:inventory:purchaseSummary:CalPurchaseSumProfit3GroupRpt')">计算</el-button>
          <el-button type="primary" @click="onCalculate(1)"
            v-if="checkPermission('groupDetailTrialCalculation')">试算</el-button> -->
          <el-button type="primary" @click="onImport"
            v-if="checkPermission('api:inventory:purchaseSummary:ImportProfit3GroupDetail')">导入</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button type="primary" @click="onSetScore"
            v-if="checkPermission('api:inventory:purchaseSummary:AddProfit3GroupDetailScoreSet')">配置权重</el-button>
          <el-button type="primary" @click="onInterception"
            v-if="checkPermission('groupDetailDataInterception')">数据截停</el-button>
          <!-- <el-button type="primary" @click="onDownLoadTemplateFile"
            v-if="checkPermission('api:inventory:purchaseSummary:ImportProfit3GroupDetail')">下载导入模版</el-button> -->
          <el-button type="primary" @click="tocreateimgMethod">生成图片</el-button>
          <el-button @click="onSearchScoreLog">分值日志</el-button>
          <el-button style="margin-left: 5px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;"
            @click="onUpdateTime" v-if="checkPermission('Inventory_PurchaseSummary_Profit3GroupConfig')">更新时间：{{ renewTime }}
          </el-button>
          <el-button type="primary" @click="onSetGroup" >配置小组</el-button>
        </el-form-item>
      </el-form>
    </template>

    <div style="width: 100%;height: 100%;" v-loading="loadingUrl">
      <!-- 大列表 -->
      <div id="YunHanAdminGoods202501181550" v-show="!dataVisible" style="width: 100%;height: 100%;">
        <vxetablebase v-show="!dataVisible" :id="'groupDetail2025010420223'" ref="table" :that='that' :isIndex='true'
          :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
          :isSelection="false" :isSelectColumn="false" style="width: 100%;margin: 0" :loading="loading" :height="'100%'"
          :showheaderoverflow="false" @cellStyle="cellStyle" cellStyle :hasSeq="hasSeq">
        </vxetablebase>
      </div>
      <div id="YunHanAdminGoods202501181550" v-show="dataVisible" style="width: 100%;height: 100%;">
        <div v-show="dataVisible" class="dialogcss" id="oneboxxx" style="width: 100%;height: auto;">
          <vxetablebase :id="'groupDetail2025010420223'" ref="table1" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;margin: 0" :loading="loading"
            :showheaderoverflow="false" @cellStyle="cellStyle" cellStyle :hasSeq="hasSeq">
          </vxetablebase>
        </div>
      </div>
    </div>

    <!--分页-->
    <template #footer>
      <div style="display: flex;justify-content: baseline;margin: 5px 0;">
        共{{ total }}条
      </div>
    </template>

    <el-dialog :title="calculate ? '试算' : '计算'" :visible.sync="computeVisible" width="20%" v-dialogDrag
      :close-on-click-modal="false">
      <div style="height: 100px;display: flex;align-items: center;justify-content: center;gap: 15px;">
        <div>{{ calculate ? '试算' : '计算' }}日期：</div>
        <div>
          <el-date-picker style="width: 100%" v-model="startTime" :clearable="false" type="month"
            :placeholder="calculate ? '请选择试算月份' : '请选择计算月份'" icon="el-icon-date" format="yyyy-MM"
            value-format="yyyy-MM"></el-date-picker>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="computeVisible = false">取 消</el-button>
        <el-button type="primary" @click="computeMethod">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog title="导入" :visible.sync="importVisible" width="20%" v-dialogDrag>
      <template #title>
        导入<br /><br />
        <div style="display: flex;justify-content: space-between;">
          <el-date-picker style="width: 160px" v-model="importMonth" type="month" placeholder="请选择导入月份"
            icon="el-icon-date" format="yyyy-MM" value-format="yyyy-MM">
          </el-date-picker>
          <el-button type="primary" @click="onDownLoadTemplateFile">下载导入模版</el-button>
        </div>
      </template><br />
      <el-row>
        <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
          <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
            :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
            <template #trigger>
              <el-button size="medium" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="medium" type="success" :loading="uploadLoading"
              @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click="importVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 配置权重 -->
    <el-dialog title="配置权重" :visible.sync="setScoreVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <setScorecomponent v-if="setScoreVisible" ref="setScorecomponent" @onCancelMethod="onCancelMethod"
        :selectGroup="true" />
    </el-dialog>

    <!-- 分值日志 -->
    <el-dialog title="分值日志" :visible.sync="scoreLogVisible" width="75%" v-dialogDrag>
      <scoreSetLogDialog v-if="scoreLogVisible" ref="scoreSetLogDialog" :rptDate="filter.rptDate" :selectGroup="true" />
      <div style="display: flex;justify-content: end;margin-top: 20px;">
        <el-button @click="scoreLogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 更新时间 -->
    <el-dialog title="更新时间" :visible.sync="updateTimeVisible" width="30%" v-dialogDrag>
      <div style="display: flex;">
        <vxetablebase :id="'updateTime202501051635'" :tablekey="'updateTime202501051635'" ref="table2" :that='that'
          :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='updatesortchange' :isNeedExpend="false"
          :tableData='updateTableData' :tableCols='updateTableCols' :isSelection="false" :isSelectColumn="false"
          style="width: 100%;  margin: 0" :loading="updateloading" :height="'250px'">
        </vxetablebase>
      </div>
    </el-dialog>

    <el-dialog title="数据截停" :visible.sync="stopVisible" width="30%" v-dialogDrag>
      <div class="stopcontainer">
        截停时间
        <div>
          <inputNumberYh v-model="dateDay" :min="0" :max="100" style="width: 150px;" />
        </div>
        号
        <el-button type="primary" class="stopbutton" @click="onTakeEffect">生效</el-button>
      </div>
      <vxetablebase v-if="stopVisible" :id="'stop202502071336'" :tablekey="'stop202502071336'" ref="table3" :that='that'
        :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='stopsortchange' :isNeedExpend="false"
        :tableData='stopData' :tableCols='stopTableCols' :isSelection="false" :isSelectColumn="false"
        style="width: 100%;  margin: 0" :loading="stoploading" :height="'200px'">
      </vxetablebase>
    </el-dialog>

    <el-dialog title="配置小组" :visible.sync="setGroupVisible" width="40%" v-dialogDrag>
      <!-- 已选标签 -->
        <div style="
          border: 1px solid #000;  /* 黑色边框 */
          padding: 10px;
          border-radius: 4px;
          max-height: 100px;
          overflow-y: auto;
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
          gap: 5px;
          background-color: white;
        ">
        <el-tag v-for="tag in groupTagList" :key="tag.key" closable type="success" @close="handleClose(tag)" >
          {{ tag.value }}
        </el-tag>
      </div>

      <!-- 可选标签 -->
      <div style="margin-top: 10px;">
        <el-tag v-for="(item, index) in tagList" :key="index" type="success" :type="groupTagList.some(t => t.key == item.value) ? '' : 'info'" @click="onAddGroup(item.value, item.label)" >
          {{ item.label }}
        </el-tag>
      </div>
      <div style="text-align: center;margin-top: 30px;">
        <el-button @click="onCloseGroup" >取消</el-button>
        <el-button type="primary" @click="addGroup" >确定</el-button>
        <el-button type="danger" @click="clearTags" >清空</el-button>
      </div>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs';
import { formatTime } from "@/utils";
import html2canvas from 'html2canvas';
import decimal from '@/utils/decimal'
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import exportExecl from "@/utils/exportExecl.js"
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew';
import setScorecomponent from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/setScorecomponent';
import scoreSetLogDialog from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/scoreSetLogDialog';
import { importProfit3GroupDetail, getProfit3GroupDetailList, calPurchaseSumProfit3GroupStopRpt, calPurchaseSumProfit3GroupRpt, getRenewLog, getProfit3GroupDetailInitSetStop, addProfit3GroupDetailStopSet
  , getProfit3GroupConfig, setProfit3GroupConfig
 } from '@/api/inventory/purchaseSummary';
import { GetBianMaBrandPurDept } from '@/api/inventory/warehouse';

const tableCols = [
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '日期', prop: 'rptDate', formatter: (row) => formatTime(row.rptDate, "YYYY-MM") },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '组别', prop: 'deptName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '区域', prop: 'areaName' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全量扣款（专员平均值）', prop: 'deductAmountAvg', tipmesg: '取值采购部首页-(采购部)扣款分析-扣款金额，只取小组采购专员和采购预备组长' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全量得分', prop: 'deductAmountAvgScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '个人扣款（小组合计）', prop: 'deductAmount', tipmesg: '取值采购部扣款责任-采购扣款总额-合计扣款总额' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '个人得分', prop: 'deductAmountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '涨降价（小组合计）', prop: 'allotDownAmount', tipmesg: '取值采购涨价降价 - 采购汇总 - 冲抵后金额合计' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '降价得分', prop: 'allotDownAmountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '周转天数（小组合计）', prop: 'brandTurnoverDay', tipmesg: '取值采购个人周转天数-自然月平均周转天数' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '周转得分', prop: 'brandTurnoverDayScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '滞销退款金额（小组合计）', prop: 'refundAmount', tipmesg: '取值采购退货出库-汇总-公司实际收回' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款得分', prop: 'refundAmountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '滞销笔数（小组合计）', prop: 'refundAmountUnsold', tipmesg: '取值采购退货出库-汇总-滞销笔数' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '滞销笔数得分', prop: 'refundAmountUnsoldScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全仓缺货次数（小组合计）', prop: 'fullWmsMonthLackCount', tipmesg: '取产品编码快递均重报表-采购维度-全仓缺货次数' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '全仓缺货得分', prop: 'fullWmsMonthLackCountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '本仓缺货次数（小组合计）', prop: 'inWmsMonthLackCount', tipmesg: '取产品编码快递均重报表-采购维度-本仓缺货次数' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '本仓缺货得分', prop: 'inWmsMonthLackCountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '云仓缺货次数（小组合计）', prop: 'wmsMonthLackCount', tipmesg: '取产品编码快递均重报表-采购维度-云仓缺货次数' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '云仓缺货得分', prop: 'wmsMonthLackCountScore', headerBgColor: '#FFFF00' },

  { sortable: 'custom', istrue: true, width: '110', align: 'center', label: '小组日常检查扣分（小组合计）', prop: 'dayWorkDeductScore', tipmesg: '通过采购数据汇总-小组明细-导入' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '小组摘品扣分（小组合计）', prop: 'packingGoodsTaskDeductScore', tipmesg: '取核价日志-摘品任务-款式编码数量' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '小组滞销扣分（小组合计）', prop: 'brandRefundDeductScore', tipmesg: '取值同专员明细中的滞销退款扣分，只取小组采购专员和采购预备组长' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '扣分总计', prop: 'allDeductScore', headerBgColor: '#FFFF00' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '合计得分', prop: 'allScore' },
];

const updateTableCols = [
  // { sortable: 'custom', istrue: true, align: 'center', label: '更新时间', prop: 'rptDate' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '操作类型', prop: 'calType' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '操作人', prop: 'operateUserName' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '更新时间', prop: 'operateTime' },
];

const stopTableCols = [
  { istrue: true, align: 'center', label: '截停时间', prop: 'dateDay' },
  { sortable: 'custom', istrue: true, align: 'center', label: '操作时间', prop: 'createdTime', formatter: (row) => row.createdTime ? formatTime(row.createdTime, "YYYY-MM-DD HH:mm:ss") : '' },
  { istrue: true, align: 'center', label: '操作人', prop: 'createdUserName', formatter: (row) => row.createdUserName ? row.createdUserName : '' },
];

export default {
  name: "groupDetail",
  components: {
    MyContainer, vxetablebase, setScorecomponent, scoreSetLogDialog, inputNumberYh
  },
  data() {
    return {
      stopInfo: {
        currentPage: 1,
        pageSize: 10,
        orderBy: 'createdTime',
        isAsc: false,
      },
      stoploading: false,
      stopData: [],
      stopTableCols,
      dateDay: undefined,
      stopVisible: false,
      calculate: false,
      hiddenColumns: [],
      hasSeq: true,
      that: this,
      loading: false,
      total: 0,
      tableData: [],
      tableCols: tableCols,
      dataVisible: false,
      renewTime: null,//更新时间
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'rptDate',
        isAsc: false,
        //过滤条件
        rptDate: null,//月份
        deptNameList: [],//组别
        areaNameList: [],//区域
      },
      computeVisible: false,//计算
      startTime: null,//计算日期
      uploadLoading: false,//导入加载
      importVisible: false,//导入弹窗显示
      importMonth: null,//导入月份
      fileList: [],//上传文件列表
      setScoreVisible: false,//分值设置弹窗
      scoreLogVisible: false,//分值日志
      loadingUrl: false,
      updateTimeInfo: {
        type: '小组',
        orderBy: 'operateTime',
        isAsc: false,
      },
      updateTimeVisible: false,//更新时间
      updateTableData: [],//更新时间
      updateTableCols: updateTableCols,//更新时间列
      updateloading: false,//更新时间加载
      groupTagList: [], // 存储选中的 tag 列表
      tagList: [],// 存储所有可选 tag
      setGroupVisible: false,
    };
  },
  async mounted() {
    this.filter.rptDate = dayjs().format('YYYY-MM');
    await this.getList();
    const res = await GetBianMaBrandPurDept();
    if (res?.success) {
      const seenLabels = new Set();
      this.tagList = res.data
        .filter(item => {
          if (seenLabels.has(item.dept_name)) {
            return false;
          }
          seenLabels.add(item.dept_name);
          return true;
        })
        .map(item => ({
          value: item.dept_id,
          label: item.dept_name
        }));
    }
  },
  created() {
    this.debouncedCompute = _.debounce(async () => {
      let information = this.calculate ? '试算' : '计算';
      if (!this.startTime) {
        this.$message({ message: `请选择${information}日期`, type: "error" });
        return;
      }
      const calFunction = this.calculate ? calPurchaseSumProfit3GroupStopRpt : calPurchaseSumProfit3GroupRpt;
      const { success: calcSuccess } = await calFunction({ startTime: this.startTime });
      if (calcSuccess) {
        this.$message({ message: `操作成功，请稍后查看${information}结果.....`, type: "success" });
        this.computeVisible = false;
        this.getList();
      } else {
        this.$message.error('计算失败');
      }
    }, 1000);
  },
  methods: {
    onCancelMethod(val) {
      if (val == 1) {
        this.getList();
      }
      this.setScoreVisible = false;
    },
    async onExport() {
      this.loading = true;
      await new Promise(resolve => setTimeout(resolve, 500));
      this.hasSeq = false
      setTimeout(async () => {
        const endDate = formatTime(dayjs(), "YYYY-MM-DD");
        exportExecl("YunHanAdminGoods202501181550", `小组明细数据` + endDate + '.xlsx');
        this.hasSeq = true
        await new Promise(resolve => setTimeout(resolve, 500));
        this.loading = false;
      }, 1000);
    },
    async cellStyle(row, column, callback) {
      if (column.field === 'allDeductScore' || column.field === 'fullWmsMonthLackCountScore' || column.field === 'brandTurnoverDayScore' || column.field === 'allotDownAmountScore' || column.field === 'deductAmountScore' || column.field === 'deductAmountAvgScore' || column.field === 'inWmsMonthLackCountScore' || column.field === 'wmsMonthLackCountScore' || column.field === 'refundAmountScore' || column.field === 'refundAmountUnsoldScore') {
        callback({ backgroundColor: '#FFFF00' });
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.filter.currentPage = 1;
      }
      const firstDayOfMonth = this.filter.rptDate ? dayjs(this.filter.rptDate).startOf('month').format('YYYY-MM-DD') : ''
      const params = { ...this.filter, rptDate: firstDayOfMonth };
      this.loading = true;
      const { data, success } = await getProfit3GroupDetailList(params);
      if (success) {
        this.tableData = data.list;
        this.renewTime = data.extData.lastRenewTime;
        this.tableData.forEach(item => {
          item.rptDate = dayjs(item.rptDate).format('YYYY-MM');
        })
        this.total = data.total;
        let hiddenList = data.extData.hiddenRows
          .filter(item => item.includes("得分"))
          .map(item => item.replace("得分", ""));
        this.onShowupMethod(hiddenList);
        this.loading = false;
      } else {
        this.$message.error('获取列表失败');
        this.loading = false;
      }
    },
    async onShowupMethod(list) {
      this.$nextTick(() => {
        const tableRef = this.$refs.table;
        if (tableRef) {
          let takePlace = [];
          let payment = [];
          this.tableCols.forEach(item => {
            if (list.some(substring => item.label.includes(substring))) {
              if (item.cols && Array.isArray(item.cols)) {
                item.cols.forEach(col => {
                  payment.push(col.prop);
                });
              } else {
                payment.push(item.prop);
              }
            } else {
              if (item.cols) {
                item.cols.forEach(col => {
                  takePlace.push(col.prop);
                });
              } else {
                takePlace.push(item.prop);
              }
            }
          });
          this.hiddenColumns = payment
          tableRef.changecolumn(payment);
          tableRef.changecolumn_setTrue(takePlace);
          if (this.$refs.table1) {
            this.$refs.table1.changecolumn(payment);
            this.$refs.table1.changecolumn_setTrue(takePlace);
          }
        }
        this.$forceUpdate();
      });
    },
    // 排序查询
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onTakeEffect() {
      if (!this.dateDay) {
        this.$message({ message: '请输入截停时间', type: "error" });
        return;
      }
      this.stoploading = true;
      const { success } = await addProfit3GroupDetailStopSet({ dateDay: this.dateDay })
      this.stoploading = false;
      if (!success) return
      this.$message({ message: '操作成功', type: "success" });
      this.dateDay = undefined;
      this.onInterception()
    },
    async onInterception() {
      this.stoploading = true;
      const { data, success } = await getProfit3GroupDetailInitSetStop(this.stopInfo)
      this.stoploading = false;
      if (!success) return
      this.stopData = data.list;
      this.dateDay = undefined
      this.stopVisible = true;
    },
    stopsortchange({ order, prop }) {
      if (prop) {
        this.stopInfo.orderBy = prop
        this.stopInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.onInterception()
      }
    },
    // 计算
    async onCalculate(val) {
      this.startTime = dayjs().format('YYYY-MM');
      if (val == 1) {
        this.calculate = true;
      } else if (val == 2) {
        this.calculate = false;
      }
      this.computeVisible = true
    },
    async computeMethod() {
      this.debouncedCompute();
    },
    // 导入
    onImport() {
      this.importMonth = null;
      this.importVisible = true;
      this.uploadLoading = false;
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles();
        }
      });
      this.fileList.splice(0, 1);
    },
    // 上传文件
    async uploadFile(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      if (null == this.importMonth) {
        this.$message({ message: "请选择导入月份", type: "error" });
        return false;
      }
      form.append("updateMonth", JSON.stringify(this.importMonth));
      const res = await importProfit3GroupDetail(form);
      if (res.success) {
        this.$message({ message: '上传成功,正在导入中...', type: "success" });
        this.importVisible = false;
      }
    },
    // 更改上传文件
    async uploadChange(file, fileList) {
      if (file.length > 1 || fileList.length > 1) {
        fileList.splice(1, 1);
        this.$message({ message: "只允许单文件导入", type: "warning" });
        return false;
      }
      this.fileList.push(file);
    },
    // 移除上传文件
    uploadRemove() {
      this.fileList.splice(0, 1);
    },
    // 提交上传文件
    async submitupload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请选择文件", type: "warning" });
        return;
      }
      this.$refs.upload.submit();
      this.$refs.upload.clearFiles();
      this.fileList.splice(0, 1);
    },
    // 打开配置权重弹窗
    async onSetScore() {
      this.setScoreVisible = true;
    },
    // 下载模版
    onDownLoadTemplateFile() {
      window.open("/static/excel/inventory/采购数据汇总_小组明细导入.xlsx", "_blank");
    },
    // 分值日志
    async onSearchScoreLog() {
      this.scoreLogVisible = true;
    },
    tocreateimgMethod() {
      this.loadingUrl = true;
      this.dataVisible = true;
      setTimeout(() => {
        this.tocreateimg();
      }, 100);
    },
    //生成图片
    async tocreateimg() {
      setTimeout(async () => {
        //const element = this.$refs.table.$el;
        const element = document.querySelector('#oneboxxx');
        if (!element) return;
        const originalWidth = element.style.width;  // 保留原始宽度
        let computeWidth = 0;
        this.tableCols.forEach(item => {
          if (item.cols && item.cols.length > 0) {
            item.cols.forEach(subItem => {
              if (!this.hiddenColumns.includes(subItem.prop)) {
                computeWidth = decimal(computeWidth, subItem.width == 'auto' ? 40 : subItem.width ? Number(subItem.width) : 0, 0, '+');
              }
            });
          } else {
            if (!this.hiddenColumns.includes(item.prop)) {
              computeWidth = decimal(computeWidth, item.width == 'auto' ? 40 : item.width ? Number(item.width) : 0, 0, '+');
            }
          }
        });
        computeWidth = computeWidth + 30
        if (computeWidth < element.scrollWidth) {
          computeWidth = element.scrollWidth
        }
        element.style.width = `${computeWidth}px`;  // 设置宽度
        await this.$nextTick();
        // 生成图片
        html2canvas(element, {
          allowTaint: true,
          useCORS: true,
          scrollX: 0,
          scrollY: 0,
          width: computeWidth,  // 确保捕获元素的完整宽度
          height: element.scrollHeight,  // 捕获元素的完整高度
          scale: 2,  // 提高图像质量（分辨率）
        }).then(async (canvas) => {
          const imgData = canvas.toDataURL('image/png');
          // 将 base64 转换为文件对象
          const imgFile = this.dataURLtoFile(imgData, `${new Date().getTime()}.png`);
          // 上传图片到服务器
          const formData = new FormData();
          formData.append('data', imgFile);  // 文件对象
          formData.append('batchnumber', '');  // 示例：添加其他表单字段
          formData.append('fileName', imgFile.name);  // 文件名称
          formData.append('total', '1');
          formData.append('index', '1');
          try {
            const res = await xMTVideoUploadBlockAsync(formData);  // 上传接口
            if (res.success) {
              const imgUrlGenerate = res.data.url;
              this.dataURL = imgUrlGenerate;
              this.downloadUrlFile(imgUrlGenerate, res.data.fileName);
            } else {
              this.$message.error('图片下载失败');
              this.loadingUrl = false;
            }
          } catch (error) {
            this.$message.error('图片下载失败');
            this.loadingUrl = false;
          }
          // 恢复原始宽度和高度
          element.style.width = originalWidth;
          element.style.height = 'auto'; // 恢复原始高度
        });
      }, 100);
    },
    dataURLtoFile(dataurl, filename) {
      const arr = dataurl.split(',');
      const mimeMatch = arr[0].match(/^data:(.*?);base64,|^data:(.*?);/);
      if (!mimeMatch) {
        throw new Error('Invalid data URL');
      }
      const mime = mimeMatch[1];
      const byteString = atob(arr[1]);
      const arrayBufferView = new Uint8Array(byteString.length);
      for (let i = 0; i < byteString.length; i++) {
        arrayBufferView[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([arrayBufferView], { type: mime });
      const file = new File([blob], filename, { type: mime });
      return file;
    },
    downloadUrlFile(url, fileName) {
      const newFileName = `${this.filter.rptDate} 小组明细.png`;
      const url2 = url.replace(/\\/g, '/'); // 确保 URL 格式正确
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url2, true);
      xhr.responseType = 'blob';
      xhr.onload = () => {
        if (xhr.status === 200) {
          // 创建 Blob 对象，指定文件类型
          const blob = xhr.response;
          // 创建一个 URL 对象来表示该 Blob 对象
          const link = document.createElement('a');
          const url = window.URL.createObjectURL(blob); // 创建一个指向 Blob 的 URL
          // 设置下载链接的文件名
          link.href = url;
          link.download = newFileName;  // 设置下载时的文件名
          // 触发点击事件，下载文件
          link.click();
          // 释放创建的 URL 对象
          window.URL.revokeObjectURL(url);
        }
      };
      xhr.send();
      this.$message.success('图片下载成功');
      this.dataVisible = false;
      this.loadingUrl = false;
    },
    async onUpdateTime() {
      this.updateloading = true
      const { data, success } = await getRenewLog({ rptDate: this.filter.rptDate, ...this.updateTimeInfo })
      this.updateloading = false
      if (!success) return
      this.updateTableData = data.list
      this.updateTimeVisible = true;
    },
    updatesortchange({ order, prop }) {
      if (prop) {
        this.updateTimeInfo.orderBy = prop
        this.updateTimeInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.onUpdateTime()
      }
    },
    //打开小组配置弹窗
    async onSetGroup() {
      const res1 = await getProfit3GroupConfig();
      if (res1?.success) {
        this.groupTagList = res1.data.list.map(item => 
          ({ key: item.key, value: item.value })
        );
      }
      this.setGroupVisible = true;
    },
    //关闭小组配置弹窗
    async onCloseGroup() {
      this.setGroupVisible = false;
      const res1 = await getProfit3GroupConfig();
      if (res1?.success) {
        this.groupTagList = res1.data.list.map(item => 
          ({ key: item.key, value: item.value })
        );
      }
    },
    //删除标签
    handleClose(tag) {
      this.groupTagList = this.groupTagList.filter(t => t.key !== tag.key);
    },
    //添加标签
    onAddGroup(value, label) {
      // 避免重复添加
      if (!this.groupTagList.some(tag => tag.key == value)) {
        this.groupTagList.push({ key: value, value: label });
      }
    },
    //提交小组配置
    async addGroup() {
      var res = await setProfit3GroupConfig(this.groupTagList);
      if (res?.success) {
        this.$message.success('小组配置成功');
        this.setGroupVisible = false;
      }
    },
    //清空标签
    clearTags() {
      this.groupTagList.splice(0);
    },

  },
}
</script>
<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 40px;
}

.dialogcss ::v-deep .body--wrapper {
  height: auto !important;
}

.stopcontainer {
  margin-bottom: 10px;
  display: flex;
  gap: 10px;
  align-items: center;

  .stopbutton {
    margin-left: 10px;
  }
}
</style>
