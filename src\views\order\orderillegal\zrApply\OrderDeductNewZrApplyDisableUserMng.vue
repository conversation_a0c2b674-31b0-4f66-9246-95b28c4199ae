<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-row>
                <el-col :span="11">
                    限制申诉到期时间
                    <el-date-picker  style="width:130px;"
      v-model="form.disableEndTime"
      type="date" value-format="yyyy-MM-dd"
      placeholder="选择日期">
    </el-date-picker>
                </el-col>
                <el-col :span="8">
                    禁止申诉人
                    <el-select v-model="form.userId" style="width:110px;" filterable  remote  :remote-method="remoteSearchUser" @change="onDeptUserIdChg">
                        <el-option v-for="item in userOpts" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="5">
                    <el-button type="primary" @click="addUser">添加禁止申诉人</el-button>
                </el-col>
            </el-row>
            <el-row>             
                <el-col :span="24" style="height:450px">                 
                    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist' :hasexpandRight="true"
                        :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' :loading="sellistLoading">
                        <template slot="right">
                            <el-table-column  label="操作" width="100">
                            <template #default="{ $index, row }">                              
                                <my-confirm-button  type="text"   @click="delRow(row)">
                                    删除
                                </my-confirm-button>                               
                            </template>
                        </el-table-column>
                        </template>
                    </ces-table>
                </el-col>
            </el-row>
        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">
                    <el-button @click="onClose">关闭</el-button>
                </el-col>
            </el-row>
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode, DeductOrderZrDeptActions } from "@/utils/tools";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { GetAllDeductDisableApplyUserList,SaveDeductDisableApplyUser } from "@/api/order/orderdeductmoney";

import store from '@/store'
import {  getUserListPage } from '@/api/admin/user'



const tableCols = [
    { istrue: true, prop: 'userName', label: '禁止人员姓名', width: '130' , sortable: true},
    { istrue: true, prop: 'disableEndTime', label: '禁用申诉截止日期', sortable: true,formatter:(row)=>{return formatTime(row.disableEndTime,"YYYY-MM-DD")}},    
    
]
export default {
    name: "OrderDeductNewZrApplyDisableUserMng",
    components: { cesTable, MyContainer, MyConfirmButton },
    data() {
        return {
            that: this,
            platform: 0,
            depts:[],
            userOpts:[],
            form:{
                id:0,              
                userId:null,
                userName:null,
                disableEndTime:null,
            },
            sellist: [],
            sellistLoading: false,
            tableCols: tableCols,
            mode: 3,
            zrDeptActions: DeductOrderZrDeptActions,       
            //summaryarry: {},
            pageLoading: false,
            curRow: null,
            formEditMode: true,//是否编辑模式              
        };
    },
    async mounted() {
        let self=this;
        self.zrDeptActions.forEach(item=>{
            let f=self.depts.find(x=>x.zrDept==item.zrDept);
            if(!f){
                self.depts.push({zrDept:item.zrDept});
            }
        })
    },
    computed: {

    },
    methods: {  
        async addUser(){
            this.pageLoading = true;
            let dto={...this.form};
            let rsp= await SaveDeductDisableApplyUser(dto);
            if(rsp && rsp.success){
                this.$message.success('操作成功！');
            this.loadData();       
            }

            this.pageLoading = false;
        },
        onDeptUserIdChg(){
            let opt=this.userOpts.find(x=>x.value== this.form.userId);
            if(opt){
                this.form.userName=opt.label;
            }
            else{
                this.form.userId=null;
                this.form.userName=null;
            }

        },
        async remoteSearchUser (parm) {
            this.userOpts=[];
            if (!parm) { 

                return;
            }
            var dynamicFilter = { field: 'nickName', operator: 'Contains', value: parm }
            var options = [];
            const res = await getUserListPage({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter })
            res?.data?.list.forEach(f => {
                this.userOpts.push({ value: f.id, label: f.nickName })
            })
            
        },  
        async delRow(row) {
            let delDto={...row};
            delDto.id=-delDto.id;
           let rsp=  await SaveDeductDisableApplyUser(delDto);
           if(rsp && rsp.success){
            this.$message.success('操作成功！');
            this.loadData();            
           }


        },
        async loadData() {
            this.pageLoading = true;
            let rsp=await GetAllDeductDisableApplyUserList();
            if(rsp &&rsp.success){
                this.sellist=rsp.data;
            }
            this.pageLoading = false;
        },
        onClose() {
            this.$emit('close');
        },
        async onSave(isClose) {
            if (await this.save()) {
                this.$emit('afterSave');
                if (isClose)
                    this.$emit('close');
            }
        }        
    },
};
</script>
<style lang="scss" scoped>
.tempdiv ::v-deep img {
    width: auto;
    max-width: 1000px;
}
</style>