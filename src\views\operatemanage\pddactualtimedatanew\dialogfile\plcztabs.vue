<template>
 <div>
   <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="全站推广" name="first">
     <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
      预算日限额：
      
       <el-checkbox-group @change="handleCheckedChange($event,'onearr')" v-model="filter.onearr">
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
         <el-checkbox  label="1">不限额</el-checkbox>
         <el-checkbox  label="2">
          <el-input style="width: 137px;" :maxlength="200" :disabled="filter.onearr[0] == '1'" @input="inputchange('one')"
           v-model="filter.allStationExtendTotalXE" placeholder="请输入限额额度"></el-input>
          </el-checkbox>
        </div>
        
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">

         <el-select v-model="filter.allStationExtendType" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
         </el-select>
         <el-input v-model="filter.allStationExtendValue" ::maxlength="200" placeholder="请输入全站推广日限额"></el-input>


        </div>
        
      </el-checkbox-group>
     

     </div>
    
   </el-tab-pane>
    <el-tab-pane label="多多搜索" name="second">
     
      <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
      预算日限额：
      
       <el-checkbox-group @change="handleCheckedChange($event,'twoarr')" v-model="filter.twoarr">
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
         <el-checkbox  label="1">不限额</el-checkbox>
         <el-checkbox  label="2">
          <el-input style="width: 137px;" :maxlength="200" :disabled="filter.twoarr[0] == '1'" @input="inputchange('two')"
           v-model="filter.duoDuoSearchTotalXE" placeholder="请输入限额额度"></el-input>
          </el-checkbox>
        </div>
        
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">

         <el-select v-model="filter.duoDuoSearchType" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
         </el-select>


         <el-input v-model="filter.duoDuoSearchValue" :maxlength="200" placeholder="请输入全站推广日限额"></el-input>


        </div>
        
      </el-checkbox-group>
     

     </div>
    </el-tab-pane>
    <el-tab-pane label="多多场景" name="third">
      <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
      预算日限额：
      
       <el-checkbox-group @change="handleCheckedChange($event,'thrarr')" v-model="filter.thrarr">
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
         <el-checkbox  label="1">不限额</el-checkbox>
         <el-checkbox  label="2">
          <el-input style="width: 137px;" :maxlength="200" :disabled="filter.thrarr[0] == '1'"
           v-model="filter.duoDuoSceneTotalXE" placeholder="请输入限额额度" @input="inputchange('thr')"></el-input>
          </el-checkbox>
        </div>
        
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">

         <el-select v-model="filter.duoDuoSceneType" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
         </el-select>


         <el-input v-model="filter.duoDuoSceneValue" :maxlength="200" placeholder="请输入全站推广日限额"></el-input>


        </div>
        
      </el-checkbox-group>
     

     </div>
    </el-tab-pane>


    <el-tab-pane label="直播推广" name="fourth">

      <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
      预算日限额：
      
       <el-checkbox-group @change="handleCheckedChange($event,'fourarr')" v-model="filter.fourarr">
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">
         <el-checkbox  label="1">不限额</el-checkbox>
         <el-checkbox  label="2">
          <el-input style="width: 137px;" :maxlength="200" :disabled="filter.fourarr[0] == '1'" @input="inputchange('four')"
           v-model="filter.liveExtendTotalXE" placeholder="请输入限额额度"></el-input>
          </el-checkbox>
        </div>
        
        <div style="margin: 10px; display: flex; flex-direction: row; align-items: center;">

         <el-select v-model="filter.liveExtendType" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
         </el-select>


         <el-input v-model="filter.liveExtendValue" :maxlength="200" placeholder="请输入全站推广日限额"></el-input>


        </div>
        
      </el-checkbox-group>
     

     </div>
    </el-tab-pane>
  </el-tabs>
 </div>
</template>

<script>
import { batchSaveActualTimeAdvData } from '@/api/operatemanage/datapdd/actualtimedatapdd.js';
export default {
 name: 'Vue2demoPlcztabs',
 props: ['listrows'],
 data() {
  return {
   activeName: 'first',
   checked: true,
   filter: {
    onearr: [],
    twoarr: [],
    thrarr: [],
    fourarr: [],

    liveExtendXEType: null,
    duoDuoSearchXEType: null,
    duoDuoSceneXEType: null,
    allStationExtendXEType: null,

   },
   options: [{
          value: '1',
          label: '目标投产比'
        }, {
          value: '2',
          label: '出价'
        },
    ]
  };
 },

 mounted() {
  
 },

 methods: {
  handleClick(tab, event) {
    console.log(tab, event);
  },
  handleCheckedChange(e,name){
    if(name == 'onearr'){
      e.length>=2&&e[0]=='1'?this.filter.onearr = ['2']:this.filter.onearr = ['1'];
      this.filter.allStationExtendXEType = this.filter.onearr[0];
      if(this.filter.allStationExtendXEType=='1'){
        this.filter.allStationExtendTotalXE = null;
      }
    }else if(name == 'twoarr'){
      e.length>=2&&e[0]=='1'?this.filter.twoarr = ['2']:this.filter.twoarr = ['1'];
      this.filter.duoDuoSceneXEType = this.filter.onearr[0];
      if(this.filter.duoDuoSceneXEType = '1'){
        this.filter.duoDuoSearchTotalXE = null;
      }
    }else if(name == 'thrarr'){
      e.length>=2&&e[0]=='1'?this.filter.thrarr = ['2']:this.filter.thrarr = ['1'];
      this.filter.duoDuoSearchXEType = this.filter.onearr[0];
      if(this.filter.duoDuoSearchXEType = '1'){
        this.filter.duoDuoSceneTotalXE = null;
      }
    }else if(name == 'fourarr'){
      e.length>=2&&e[0]=='1'?this.filter.fourarr = ['2']:this.filter.fourarr = ['1'];
      this.filter.liveExtendXEType = this.filter.onearr[0];
      if(this.filter.liveExtendXEType = '1'){
        this.filter.liveExtendTotalXE = null;
      }
    }
  },
  inputchange(val){
    if(val == 'one'){
      this.filter.allStationExtendXEType = '2';
      this.filter.onearr = ['2']
    }else if(val == 'two'){
      this.filter.duoDuoSceneXEType = '2';
      this.filter.twoarr = ['2'];
    }else if(val == 'thr'){
      this.filter.duoDuoSearchXEType = '2';
      this.filter.thrarr = ['2'];
    }else if(val == 'four'){
      this.filter.liveExtendXEType = '2';
      this.filter.fourarr = ['2'];
    }
  },
  async submit(){
    this.filter.productCodes = [];
    this.listrows.map((item)=>{
      this.filter.productCodes.push(item.proCode)
    })

    let params = {
      ...this.filter
    }
    console.log("打印提交信息",params)

    let res = await batchSaveActualTimeAdvData(params);
    if(!res.success){
      return
    }
    this.$message.success("批量操作成功！")
  },
 },
};
</script>

<style lang="scss" scoped>

</style>