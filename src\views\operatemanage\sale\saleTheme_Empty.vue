<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 220px" v-model="filter.dataTime" type="datetimerange" :clearable="true"
              range-separator="至" start-placeholder="日期开始时间" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              end-placeholder="日期结束时间"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 220px" v-model="filter.payTime" type="datetimerange" :clearable="true"
              range-separator="至" start-placeholder="付款开始时间" value-format="yyyy-MM-dd HH:mm:ss"
              end-placeholder="付款结束时间"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-date-picker style="width: 220px" v-model="filter.timerange" type="datetimerange" :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="发货开始时间"
              end-placeholder="发货结束时间"></el-date-picker>
          </el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.platform" placeholder="请选择平台" clearable style="width:130px;">
              <el-option label="淘系" value="1"></el-option>
              <el-option label="拼多多" value="2"></el-option>
              <el-option label="阿里巴巴" value="4"></el-option>
              <el-option label="抖音" value="6"></el-option>
              <el-option label="京东" value="7"></el-option>
              <el-option label="淘工厂" value="8"></el-option>
              <el-option label="苏宁" value="10"></el-option>
              <el-option label="分销" value="11"></el-option>
              <!-- <el-option label="希音" value="12"></el-option>
              <el-option label="拼多多跨境" value="13"></el-option> -->
              <el-option label="快手" value="14"></el-option>
              <el-option label="抖音供销" value="17"></el-option>
              <el-option label="视频号" value="20"></el-option>
              <el-option label="小红书" value="21"></el-option>
            </el-select>
          </el-button>
          <el-button style="padding: 0;">
            <el-input v-model.trim="filter.shopCode"  clearable maxlength="50"  placeholder="店铺" style="width:130px;"/>
          </el-button>
          <el-button style="padding: 0;">
            <inputYunhan ref="orderNo" :inputt.sync="filter.orderNo" v-model="filter.orderNo" width="200px"
              placeholder="线上单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
              :maxlength="21000" @callback="productCodeCallback" title="线上单号">
            </inputYunhan>
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.goodsCode" maxlength="50" clearable placeholder="商品编码" style="width:130px;" />
          </el-button>
          <!-- 产品编码 -->
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="filter.ProCode" clearable maxlength="50" placeholder="产品ID" style="width:130px;" />
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button type="primary" @click="onstartImport">导入</el-button>
        </el-button-group>
      </div>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelection='false'
      :showsummary='true' :summaryarry='summaryarry' :tableData='financialreportlist' :tableCols='tableCols'
      :tableHandles='tableHandles' :loading="listLoading" style="height: calc(100% - 30px);">

    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>

    <el-drawer title="编辑" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
       <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
       <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
      </div>
    </el-drawer>
    <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag @close="closediolag">
      <span :gutter="20">
        <el-row>
         
          <el-col :xs="4" :sm="6" :md="8" :lg="6">
            <el-select filterable v-model="importfilter.PlatForm" placeholder="请选择平台" clearable>
              <el-option label="淘系" value="1"></el-option>
              <el-option label="拼多多" value="2"></el-option>
              <el-option label="阿里巴巴" value="4"></el-option>
              <el-option label="抖音" value="6"></el-option>
              <el-option label="京东" value="7"></el-option>
              <el-option label="淘工厂" value="8"></el-option>
              <el-option label="苏宁" value="10"></el-option>
              <el-option label="分销" value="11"></el-option>
              <!-- <el-option label="希音" value="12"></el-option>
              <el-option label="拼多多跨境" value="13"></el-option> -->
              <el-option label="快手" value="14"></el-option>
              <el-option label="抖音供销" value="17"></el-option>
              <el-option label="视频号" value="20"></el-option>
              <el-option label="小红书" value="21"></el-option>
            </el-select>
          </el-col>
          <el-col :xs="4" :sm="6" :md="8" :lg="6">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" action accept=".xlsx"
              :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove" :file-list="fileList">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="submitUpload">{{ uploadLoading ? "上传中" : "上传" }}
              </el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>
<script>
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatPlatform, formatTime, formatLinkProCode, platformlist } from "@/utils/tools";

import {importCodeSalesThemeAnalysisEmptyCodeAsync,orderDayRpt_OrderTypes_fmtFunc,pageCodeSalesThemeAnalysisEmptyCodeAsync,editCodeSalesThemeAnalysisEmptyCode,exportCodeSalesThemeAnalysisEmptyCodeAsync} from '@/api/bookkeeper/reportdayV2'
import { getAllProBrand } from '@/api/inventory/warehouse'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import importmodule from '@/components/Bus/importmodule'
import StyleCodeDetail from '@/views/bookkeeper/reportday/StyleCodeDetail'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getWarehouses } from "@/api/storehouse/storehouse";
import { sendwarehouselist } from "@/utils/tools";
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const formatOrderType = (row) => {
  let type = '';
  switch (row) {
    case 1:
      type = '普通订单';
      break;
    case 2:
      type = '补发订单';
      break;
    case 4:
      type = '分销Plus';
      break;
    case 8:
      type = '换货';
      break;
    case 16:
      type = '分销';
      break;
    case 32:
      type = '供销';
      break;
    case 0:
      type = '其他';
      break;
  }
  return type;
};
const tableCols = [
{istrue: true,type: "button",label:'操作',width: "60",btnList: [{ label: "编辑", handle: (that, row) => that.EditButton(row)}]},
{ istrue: true, prop: 'yearMonthDay', label: '日期', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'orderNoInner', label: '内部订单号', sortable: 'custom', width: '90',type:'orderLogInfo',orderType:'orderNoInner'  },
 // { istrue: true, prop: 'orderType', label: '订单类型', sortable: 'custom', width: '90', formatter: (row) => formatOrderType(row.orderType) },
  { istrue: true,  prop: 'orderType', label: '订单类型',  width: '90' ,type:'custom',formatter:(row)=>orderDayRpt_OrderTypes_fmtFunc(row.orderType),sortable:'custom',},
  { istrue: true, prop: 'orderNo', label: '线上单号', width: '150', sortable: 'custom' },
  // { istrue: true, prop: 'orderStatus ', label: '订单状态', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
  //{ istrue: true, prop: 'wmsName', label: '发货仓', sortable: 'custom', width: '100', },
  { istrue: true, prop: 'shopName', label: '店铺', sortable: 'custom', width: '90', },
  { istrue: true, prop: 'platform', fix: true, label: '平台', width: '90', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
  { istrue: true, prop: 'timeSend', label: '发货日期', width: '120', sortable: 'custom', formatter: (row) => {
      if ((row.timeSend && row.timeSend == '2106-02-07 06:28:16') || !row.timeSend) {
        return ''
      } else {
        return row.timeSend
      }
    }},
  { istrue: true, prop: 'timePay', label: '付款日期', sortable: 'custom', width: '105', },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100' },
  { istrue: true, prop: 'styleCode', label: '款式编码', sortable: 'custom', width: '90', formatter: (row) => !row.styleCode ? " " : row.styleCode },
  { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '90' },
 // { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '90', },
 // { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', sortable: 'custom', width: '90' },
  //{ istrue: true, prop: 'brandName', label: '采购员', sortable: 'custom', width: '110', type: 'custom' },
  { istrue: true, prop: 'cost', label: '成本价', type: 'custom', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'qty', label: '销售数量', sortable: 'custom', width: '90', },
  { istrue: true, prop: 'giftQty', label: '赠品数量', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'actualAmount', label: '实发金额', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'salesAmount', label: '销售金额', sortable: 'custom', width: '90' },
  { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', width: '90' },

  // {istrue:true,type:'button', width:'55',label:'操作', width:'100',btnList:[{label:"编辑处理方案",handle:(that,row)=>that.onHand(row)}]},
];
const tableHandles = [

];

export default {
  name: "sale",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, InputMult, importmodule, inputYunhan, StyleCodeDetail },
  data () {
    return {
      that: this,
      filter: {
        payStartDate: null,
        payEndDate: null,
        deliverGoodsStartDate: null,
        deliverGoodsEndDate: null,
        platform: "2",
        shopCode: null,
        wmsId: null,
        // 运营专员 ID
        operateSpecialUserId: null,
        proCode: null,
        goodsCode: null,
        groupId: null,
        brandId: null,
        payTime: null,
        timerange: null,
        dataTime:null,
        dataStartDate: null,
        dataEndDate: null,
        orderNo:null
      },


      importfilter: {
        PlatForm: null,
        YearMonthDay:null
      },
      warehouselist: [],
      editVisible: false,
      styleCode: null,
      profit3UnZero: null,
      options: [],
      platformlist: platformlist,
      onExporting: false,

      shopList: [],
      userList: [],
      brandlist: [],
      grouplist: [],
      directorlist: [],
      financialreportlist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      pager: { OrderBy: "", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      earchloading: false,
      pageLoading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      dialogVisibleData: false,
      dialogVisible: false,
      uploadLoading: false,
      importFilte: {},
      fileparm: {},
      editparmVisible: false,
      editLoading: false,
      editparmLoading: false,
      searchloading: false,
      /* dialogDrVisibleShengYi:false, */
      dialogDrVisible: false,
      expressfreightanalysisVisible: false,
      drparamProCode: '',
      editVisible:false,
      autoform: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      StyleCodeDetail: {
        visible: false,
        filter: {
          StyleCode: ''
        }
      },

    };
  },
  async mounted () {
    await this.initform();
    this.initit();
  },
  async created () {
    // await this.init()
    await this.getShopList();
    await this.getList();
  },
  methods: {
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async initit(){
      var date1 = new Date(); date1.setDate(date1.getDate()-1);
      var date2 = new Date(); date2.setDate(date2.getDate()-1);
      this.filter.dataTime=[];
      this.filter.dataTime[0]=this.datetostr(date1);
      this.filter.dataTime[1]=this.datetostr(date2);
    },
    productCodeCallback(val) {
      this.filter.orderNo = val;
    },
   
    async init () {
      var date1 = new Date(); date1.setDate(date1.getDate() - 10);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
      this.filter.payTime = [];
      this.filter.payTime[0] = this.datetostr(date1);
      this.filter.payTime[1] = this.datetostr(date2);
    },

    async getShopList () {
      const res1 = await getAllShopList();
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode && (f.platform == 1 || f.platform == 8))
          this.shopList.push(f);
      });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      var res4 = await getAllProBrand();
      // 采购员
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });

      // 发货仓
      var res5 = await getWarehouses();
      this.warehouselist = res5.data?.map(item => { return { value: item.wms_co_id, label: item.name }; });
    },
    async sortchange (column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    // onRefresh () {
    //   this.onSearch()
    // },
    async onSearch () {
      this.$refs.pager.setPage(1);
      await this.getList().then(res => { });
      loading.close();
    },
    async getList () {
      this.filter.payStartDate = null;
      this.filter.payEndDate = null;
      this.filter.deliverGoodsStartDate = null;
      this.filter.deliverGoodsEndDate = null;
      this.filter.dataStartDate = null;
      this.filter.dataEndDate = null;
      if (this.filter.payTime) {
        this.filter.payStartDate = this.filter.payTime[0];
        this.filter.payEndDate = this.filter.payTime[1];
      }
      if (this.filter.timerange) {
        this.filter.deliverGoodsStartDate = this.filter.timerange[0];
        this.filter.deliverGoodsEndDate = this.filter.timerange[1];
      }
      if (this.filter.dataTime) {
        this.filter.dataStartDate = this.filter.dataTime[0];
        this.filter.dataEndDate = this.filter.dataTime[1];
      }
      if (!this.filter.platform || this.filter.platform == null) {
        this.$message({ message: "请先选择平台", type: "warning" });
        return false;
      }
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...this.pager, ...pager, ...this.filter };
      // this.listLoading = true;
      startLoading();
      await pageCodeSalesThemeAnalysisEmptyCodeAsync(params).then(res => {
        loading.close();
        that.total = res.data?.total;
        // if (res?.data?.list && res?.data?.list.length > 0) {
        //   for (var i in res.data.list) {
        //     if (!res.data.list[i].freightFee) {
        //       res.data.list[i].freightFee = " ";
        //     }
        //   }
        // }
        that.financialreportlist = res.data?.list;
        that.summaryarry = res.data?.summary;

      });
    },

    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    async onExport() {
     if (this.onExporting) return;
     try{
      this.filter.payStartDate = null;
      this.filter.payEndDate = null;
      this.filter.deliverGoodsStartDate = null;
      this.filter.deliverGoodsEndDate = null;
      this.filter.dataStartDate = null;
      this.filter.dataEndDate = null;
      if (this.filter.payTime) {
        this.filter.payStartDate = this.filter.payTime[0];
        this.filter.payEndDate = this.filter.payTime[1];
      }
      if (this.filter.timerange) {
        this.filter.deliverGoodsStartDate = this.filter.timerange[0];
        this.filter.deliverGoodsEndDate = this.filter.timerange[1];
      }
      if (this.filter.dataTime) {
        this.filter.dataStartDate = this.filter.dataTime[0];
        this.filter.dataEndDate = this.filter.dataTime[1];
      }
      if (!this.filter.platform || this.filter.platform == null) {
        this.$message({ message: "请先选择平台", type: "warning" });
        return false;
      }
        this.onExporting = true;
      
        const params = {...this.pager,...this.filter}
        var res= await exportCodeSalesThemeAnalysisEmptyCodeAsync(params);
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','销售主题数据_' + new Date().toLocaleString() + '.xlsx' )
        this.uploadLoading = false;

        aLink.click()
        }catch(err){
          console.log(err)
          console.log(err.message);
        }
      this.onExporting=false;
     },
    
    EditButton(row){
      this.editVisible = true
      var arr = Object.keys(this.autoform.fApi);
      if(arr.length >0)
         this.autoform.fApi.resetFields()
          this.$nextTick(async() =>{
      await this.autoform.fApi.setValue(row)
      })
        },
        async onEditSubmit() {
      this.editLoading=true;
      await this.autoform.fApi.validate(async (valid, fail) => {
      if(valid){
          const formData = this.autoform.fApi.formData();
          const res = await editCodeSalesThemeAnalysisEmptyCode(formData);
          if(res.code==1){
            this.$message.success('修改成功！');
             this.getjSpeedDriveList(); 
            this.editVisible=false;        
          }
        }else{}
     })
     this.editLoading=false;
    },
    async initform(){
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'input',field:'proCode',title:'产品ID',value: '',col:{span:6}}
                    ];
    },
        async onstartImport () {
      this.fileList = [];
      this.uploadLoading = false;
      this.dialogVisible = true;
    },
    async submitUpload () {
      if (!this.importfilter.PlatForm || this.importfilter.PlatForm == null) {
        this.$message({ message: "请先选择平台", type: "warning" });
        return false;
      }
      if (this.fileList==null || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
     
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile (item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      const form = new FormData();

      form.append("PlatForm", this.importfilter.PlatForm);

      let filelist=this.fileList;
      form.append("token", this.token);

      if(filelist && filelist.length>1){
        form.append("upfile", filelist[0].raw);
    
      }
      else{
        form.append("upfile", item.file);
      }



      const res = await importCodeSalesThemeAnalysisEmptyCodeAsync(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.fileList = []
        this.$refs.upload.clearFiles();
        this.onSearch();
        this.dialogVisible = false;
        this.importfilter.YearMonthDay=null
        this.importfilter.PlatForm=null
      }
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false;
    },
    closediolag(){
      this.fileList = []
        this.$refs.upload.clearFiles();
        this.dialogVisible = false;
        this.importfilter.YearMonthDay=null
        this.importfilter.PlatForm=null
    },
    async uploadChange(file, fileList) {
        let files=[];
        files.push(file)
        this.fileList = fileList;
    },
    async uploadRemove(file, fileList) {
        this.fileList = []
    },
  },

};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

/* .el-table  ::v-deep  .cell{
       color: black;
  } */
::v-deep .el-table td.el-table__cell div {

  color: gray;

}
</style>

