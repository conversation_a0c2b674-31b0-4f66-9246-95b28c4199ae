<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;" @tab-click="handleClick">
            <el-tab-pane label="全平台日报数据源" name="tab1" style="height: 100%;"
                v-if="checkPermission('procodeprocessplandayreport')">
                <procodeprocessplandayreport :filter="filter" ref="procodeprocessplandayreport" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="白名单设置" name="tab3" style="height: 100%;">
                <procodeprocessplanbmd :filter="filter" ref="procodeprocessplanbmd" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="产品处理方案" name="tab2" style="height: 100%;">
                <procodeprocessplanlist :filter="filter" ref="procodeprocessplanlist" style="height: 100%;"
                    @toSee="toSee" />
            </el-tab-pane>
            <el-tab-pane label="产品处理方案详情" name="tab4" style="height: 100%;">
                <procodeprocessplansee2 :filter="filter" ref="procodeprocessplansee2" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import checkPermission from '@/utils/permission';
import MyContainer from "@/components/my-container";

import procodeprocessplandayreport from '@/views/bookkeeper/procodeprocessplan/procodeprocessplandayreport';
import procodeprocessplanlist from '@/views/bookkeeper/procodeprocessplan/procodeprocessplanlist';
import procodeprocessplanbmd from '@/views/bookkeeper/procodeprocessplan/procodeprocessplanbmd';
import procodeprocessplansee2 from '@/views/bookkeeper/procodeprocessplan/procodeprocessplansee2';

export default {
    name: "procodeprocessplanindex",
    components: {
        MyContainer, procodeprocessplandayreport, procodeprocessplanlist, procodeprocessplanbmd, procodeprocessplansee2
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            activeName: 'tab1',
        };
    },
    mounted() {
        this.getPermissionTab();
    },
    methods: {
        handleClick(tab, event) {
            this.activeName = tab.name;
            this.$nextTick(async () => {
                if (this.activeName == 'tab2') {
                    this.$refs.procodeprocessplanlist.onSearch()
                };
                if (this.activeName == 'tab4') {
                    this.$refs.procodeprocessplansee2.onPlanyMethod()
                };
            })
        },
        toSee(seeId) {
            this.activeName = 'tab4'
            this.$refs.procodeprocessplansee2.loadData2({ id: seeId })
        },
        getPermissionTab() {
            if (!checkPermission('procodeprocessplandayreport')) {
                this.activeName = "tab2"
            }
        }

    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
