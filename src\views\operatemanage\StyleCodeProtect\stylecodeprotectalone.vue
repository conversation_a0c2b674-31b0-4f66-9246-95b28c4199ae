<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin: 0;border: none;">
                <el-select filterable v-model="filter.aloneGroupIds" placeholder="独立发展小组" style="width: 170px" clearable
                    multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'dlfz2' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="addAloneShow">添加小组</el-button>
            <el-button type="primary" @click="delAlone">删除小组</el-button>
            <el-button type="primary" @click="seelog">操作记录</el-button>
        </template>

        <vxetablebase :id="'stylecodeprotectalone20231019'" :border="true" :align="'center'"
            :tablekey="'stylecodeprotectalone20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' @select='selectchange' :isSelectColumn="true" :showsummary='true'
            :tablefixed='true' :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog title="添加独立发展小组" :visible.sync="addAloneDialogData.visible" width="400px"
            :close-on-click-modal="false" v-dialogDrag>
            <span>
                <el-select filterable v-model="addAloneDialogData.addData" placeholder="独立发展小组" style="width: 300px"
                    clearable multiple collapse-tags>
                    <el-option v-for="item in directorGroupList" :key="'gk' + item.key" :label="item.value"
                        :value="item.key" />
                </el-select>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addAloneDialogData.visible = false">关闭</el-button>
                <el-button @click="addAloneSave" type="primary" :loading="addAloneDialogData.loading">确定</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { GetStyleCodeProtectAlonePageList, InsertStyleCodeProtectAlone, DtlStyleCodeProtectAlone } from '@/api/operatemanage/stylecodeprotect'

const tableCols = [
    { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
    { istrue: true, prop: 'aloneGroupId', label: '独立发展小组', sortable: 'custom', width: '160', formatter: (row) => row.aloneGroupName },
    { istrue: true, prop: 'aloneStartTime', label: '开始时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'createdUserName', label: '设置人', sortable: 'custom', width: '160' },
    //{ istrue: true, prop: 'createdTime', label: '设置时间', sortable: 'custom', width: '160' },
];
export default {
    name: "stylecodeprotectalone",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase
    },
    data() {
        return {
            that: this,
            directorGroupList: [],
            filter: {
                aloneGroupIds: [],
            },
            tableCols: tableCols,
            total: 0,
            datalist: [],
            pager: { OrderBy: "aloneStartTime", IsAsc: false },
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            sels: [], // 列表选中列
            selids: [],

            addAloneDialogData: {
                visible: false,
                loading: false,
                addData: [],
            }
        };
    },
    async mounted() {
        await this.onSearch();
        await this.getloadgroupselect();
    },
    async created() {
    },
    methods: {
        async getloadgroupselect() {
            const res1 = await getDirectorGroupList({});
            this.directorGroupList = res1.data;
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            this.sels = [];
            console.log(params);
            this.listLoading = true;
            const res = await GetStyleCodeProtectAlonePageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.aloneGroupId);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        seelog() {
            this.$showDialogform({
                path: `@/views/operatemanage/StyleCodeProtect/stylecodeprotectalonelog.vue`,
                autoTitle: false,
                title: '操作日志',
                args: {
                },
                height: '500px',
                width: '55%',
            });
        },
        addAloneShow() {
            this.addAloneDialogData.addData = [];
            this.addAloneDialogData.loading = false;
            this.addAloneDialogData.visible = true;
        },
        async addAloneSave() {
            this.addAloneDialogData.loading = true;
            if (!this.addAloneDialogData.addData || this.addAloneDialogData.addData.length <= 0) {
                this.$message({ type: 'warning', message: '请至少选择一个小组' });
                this.addAloneDialogData.loading = false;
                return;
            }
            let res = await InsertStyleCodeProtectAlone(this.addAloneDialogData.addData);
            this.addAloneDialogData.loading = false;
            if (res?.success == true) {
                this.$message({ type: 'success', message: '添加成功' });
                this.addAloneDialogData.visible = false;
                await this.onSearch();
                //更新可上架小组查询条件里面不能有独立发展小组
                this.$emit('updateksjgroup');
            }
        },
        async delAlone() {
            if (!this.selids || this.selids.length <= 0) {
                this.$message({ type: 'warning', message: '请至少勾选一行数据' });
                return;
            }
            this.$confirm('确定要删除勾选的小组吗？', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                let res = await DtlStyleCodeProtectAlone(this.selids);
                if (res?.success == true) {
                    this.$message({ type: 'success', message: '删除成功' });
                    await this.onSearch();
                    //更新可上架小组查询条件里面不能有独立发展小组
                    this.$emit('updateksjgroup');
                }
            }).catch(() => {
            });
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-select__tags-text {
    max-width: 80px;
}
</style>
