<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form
          class="ad-form-query"
          :inline="true"
          :model="Filter"
          @submit.native.prevent>
        </el-form>
      </template>
      <!--列表-->
      <ces-table ref="table" :that='that' :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='dahuixionglist'
                @select='selectchange' :isSelection='false' :showsummary='true'  :summaryarry='summaryarry'
           :tableCols='tableCols' :loading="listLoading">
        <el-table-column type="expand">
          <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
        </el-table-column>
         <template slot='extentbtn'>
            <el-button-group>
              <el-button style="padding: 0;margin: 0;">
                                  <el-date-picker v-model="Filter.yearMonth" style="width: 120px;" type="month" :clearable="true" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>
                              </el-button>
                              <el-button style="padding: 0;margin: 0;">
                                  <el-date-picker v-model="Filter.yearMonthDay" style="width: 120px;" type="date" :clearable="true" format="yyyyMMdd" value-format="yyyyMMdd" placeholder="选择日期"  ></el-date-picker>
                              </el-button>
                              <el-button style="padding: 0;margin: 0;">
                                  <inputYunhan ref="productID" :inputt.sync="Filter.productID" v-model="Filter.productID" width="200px"
              placeholder="ID(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="500" :valuedOpen="true"
              :maxlength="10000" @callback="productCodeCallback2" title="ID">
            </inputYunhan>
                              </el-button>
                              <el-button style="padding: 0;margin: 0;">
                                  <el-select filterable clearable collapse-tags v-model="shopCodeList" placeholder="店铺" style="width: 400px" multiple collapse-tags>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
              :value="item.shopCode"></el-option>
          </el-select>
                              </el-button>
                              <!-- <el-button style="padding: 0;margin: 0;">
                                  <el-input v-model.trim="Filter.BatchNumber" clearable maxlength="19" placeholder="导入批次" style="width:120px;"/>
                              </el-button> -->
                              <!-- <el-button style="padding: 0;margin: 0;">
                                <el-select v-model="Status" clearable filterable collapse-tags placeholder="状态" multiple  class="publicCss">
                                <el-option  label="推广中" value="推广中"/>
                                <el-option  label="已暂停" value="已暂停"/>
                                <el-option  label="推广待暂停" value="推广待暂停"/>
                                <el-option  label="推广异常" value="推广异常"/>
                                <el-option  label="待推广" value="待推广"/>
                              </el-select>
                            </el-button> -->
   
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button type="primary" @click="onImportSyj">导入</el-button>
            <el-button type="primary" @click="onexport">导出</el-button>

            </el-button-group>
          </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination
          ref="pager"
          :total="total"
          :checked-count="sels.length"
          @get-page="getJdExpressList"
        />
      </template>

      <el-dialog title="导入周预算" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
        <span>
            <el-upload ref="upload2" class="upload-demo"
                    :auto-upload="false"
                    :multiple="false"
                    :limit="1"
                    action
                    accept=".xlsx"
                    :http-request="uploadFile2"
                    :on-success="uploadSuccess2">
                  <template #trigger>
                      <el-button size="small" type="primary">选取文件</el-button>
                  </template>
                  <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisibleSyj = false">关闭</el-button>
        </span>
      </el-dialog>
    </my-container>
  </template>
  <script>

import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import {importMonthTuoguanYGAsync,getMonthTuoguanYGList,deleteMonthBatchAsync,exportMonthTuoguanYGAsync} from '@/api/financial/yyfyday'
  import dayjs from "dayjs";
  import cesTable from "@/components/Table/table.vue";
  import { formatTime } from "@/utils";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";

  const tableCols =[
        {istrue:true,prop:'yearMonth',label:'年月', width:'80px',sortable:'custom'},
        {istrue:true,prop:'yearMonthDay',label:'日期', width:'80px',sortable:'custom'},
        {istrue:true,prop:'shopName',label:'店铺', width:'auto',sortable:'custom'},
        {istrue:true,prop:'proCode',label:'ID', width:'auto',sortable:'custom'},
        {istrue:true,prop:'promotionDealAmount',label:'推广成交金额', width:'140px',sortable:'custom'},
        {istrue:true,prop:'promotionDealOrderCount',label:'推广成交订单量', width:'140px',sortable:'custom'},
        {istrue:true,prop:'promotionDealRate',label:'推广投产比', width:'120px',sortable:'custom'},
        {istrue:true,prop:'weeklyBudgetConsumedAmount',label:'周预算消耗金额', width:'150px',sortable:'custom'},
        {istrue:true,prop:'createdTime',label:'导入时间', width:'100px',sortable:'custom'},
        {istrue:true,prop:'batchNumber',label:'导入批次', width:'150px',sortable:'custom'},
        {istrue: true,type: "button",label:'操作',width: "80px",btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row)}]}
       ];

const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

  export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,inputYunhan},
    data() {
      return {
        that:this,
        Filter: {
          ProCode:'',
          PromotePlan:'',
          ShopName:'',
          BatchNumber:null,
          UseDate:[startDate, endDate],
          UseDstartAccountDateate:'',
          endAccountDate:'',
          feeType:3,
        },
        shopCodeList:[],
        shopList:[],
        userList:[],
        groupList:[],
        dahuixionglist: [],
        tableCols:tableCols,
        total: 0,
        summaryarry:{},
        pager:{OrderBy:"id",IsAsc:false},
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids:[],
        Status:[],
        dialogVisibleSyj:false,
        fileList:[],
        platform:0,
        yearMonth:""
      };
    },
    async mounted() {
      this.onSearch();
    },
    async created() {
    await this.getShopList();
    },
    methods: {
      setplatform(platform){
  this.platform=platform;
  },
     async deleteBatch(row){
        var that=this;
        this.$confirm("此操作将删除此批次导入费用数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async() => {
        await deleteMonthBatchAsync({batchNumber:row.batchNumber})
        that.$message({message: '已删除', type: "success"});
        that.onRefresh()
          });
      },
      sortchange(column){
        if(!column.order)
          this.pager={};
        else
          this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
        this.onSearch();
      },
      onImportSyj(){
        this.dialogVisibleSyj = true
      },
      async uploadFile2(item) {
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("feeType", 3);
        const res =  await importMonthTuoguanYGAsync(form);
        if (res.success) {
        this.$message({message: '上传成功,正在导入中...', type: "success"});
        }
        this.dialogVisibleSyj = false;
      },
      async uploadSuccess2(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
      },
      async onSubmitupload2() {
        this.$refs.upload2.submit()
      },
      onRefresh(){
          this.onSearch()
      },
      onSearch(){
         this.$refs.pager.setPage(1);
         this.getJdExpressList();
      },
      productCodeCallback2(val) {
      this.Filter.productID = val;
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [8] });
      this.shopList = res1.data;
   
    },
      async getJdExpressList(){
      
         if(/^\d+$/.test(this.Filter.BatchNumber)==false&&this.Filter.BatchNumber!=null&&this.Filter.BatchNumber!=""){
          this.$message.error('请输入正确的批次号！！！！！');
          this.Filter.BatchNumber=null;
          return;
         }
         if(this.Status)
         this.Filter.Status=this.Status.join(',');
        if(this.shopCodeList)
        this.Filter.shopCode = this.shopCodeList.join(',')
        const para = {...this.Filter};
        var pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await getMonthTuoguanYGList(params);
        this.listLoading = false;
        this.total = res.data.total
        this.dahuixionglist = res.data.list;
        this.summaryarry=res.data.summary;
      },
             //导出
     async onexport() {
            this.isExport = true
            if(/^\d+$/.test(this.Filter.BatchNumber)==false&&this.Filter.BatchNumber!=null&&this.Filter.BatchNumber!=""){
          this.$message.error('请输入正确的批次号！！！！！');
          this.Filter.BatchNumber=null;
          return;
         }
         if(this.Status)
         this.Filter.Status=this.Status.join(',');
        if(this.shopCodeList)
        this.Filter.shopCode = this.shopCodeList.join(',')

            await exportMonthTuoguanYGAsync(this.Filter).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '周预算' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
      selectchange:function(rows,row) {
        this.selids=[];
        rows.forEach(f=>{
          this.selids.push(f.id);
        })
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
