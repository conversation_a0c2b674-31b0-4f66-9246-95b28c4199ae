<template>
 <MyContainer >
    <template #header>
      <div >
        <el-radio-group v-model="radio">
          <el-radio-button  label="播种"></el-radio-button>
          <el-radio-button  label="聚集订单"></el-radio-button>
          <el-radio-button  label="工作量"></el-radio-button>
          <el-radio-button label="工价维护"></el-radio-button>
          <el-radio-button label="主控设备"></el-radio-button>
          <el-radio-button label="标签设备"></el-radio-button>
        </el-radio-group>
    </div>
    
  </template>
    <Sow v-if="radio=='播种'"/>
    <GatherOrder v-if="radio=='聚集订单'"/>
    <WorkLoad v-if="radio=='工作量'"/>
    <WagesManage v-if="radio=='工价维护'"/>
    <MainDevice v-if="radio=='主控设备'"/>
    <LabelDevice v-if="radio=='标签设备'"/>
 
 </MyContainer>
</template>

<script>

import MyContainer from "@/components/my-container";
import LabelDevice from "./components/LabelDevice.vue";
import Sow from "./components/Sow.vue";
import MainDevice from "./components/MainDevice.vue";
import WorkLoad from "./components/WorkLoad.vue";
import GatherOrder from "./components/GatherOrder.vue";
import WagesManage from "./components/WagesManage.vue";


export default {
 name: 'sowingWall',
 components: {MyContainer,Sow, LabelDevice,MainDevice,WorkLoad,GatherOrder,WagesManage},
 data() {
   return {
        radio:'播种'
   };
 },
  async mounted(){

  },
 methods:{
     
 }
};
</script>

<style lang="scss" scoped>
::v-deep .el-radio-button__inner{
  width: 80px;
}
</style>
