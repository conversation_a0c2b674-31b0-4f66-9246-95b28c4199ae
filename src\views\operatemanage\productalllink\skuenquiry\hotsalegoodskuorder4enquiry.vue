<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :inline="true" ref="orderForm" :model="form" label-width="110px" label-position="left" :disabled="!formEditMode" size="mini">
                <el-row>
                    <el-col :span="24">
                        <div style="margin-right:20px;float:left">
                            <strong>
                                竞品ID：
                            </strong>
                            <span v-html="formatLinkProCode(form.platform, form.goodsCompeteId)"></span>

                        </div>
                        <div style="margin-right:20px;float:left">
                            <strong>
                                竞品标题：
                            </strong>
                            {{ form.goodsCompeteName}}
                        </div>
                        <div style="margin-right:20px;float:left">
                            <strong>
                                SKU名称：
                            </strong>
                            {{ form.skuName}}
                        </div>
                        <div style="margin-right:20px;float:left">
                            <strong>
                                登记人：
                            </strong>
                            {{ form.sampleUserName }}
                        </div>
                        <div style="margin-right:20px;float:left">
                            <strong>
                                登记时间：
                            </strong>
                            {{ form.createdTime}}
                        </div>
                        <div style="margin-right:20px;float:left">
                            <strong>
                                采样来源：
                            </strong>
                            {{ form.sampleSource}}
                        </div>
                        <div v-if="form.sampleSource=='厂家'" style="margin-right:20px;float:left">
                            <strong>
                                厂家：
                            </strong>
                            {{ form.factoryName}}
                        </div>
                        <div v-if="form.sampleSource=='厂家'" style="margin-right:20px;float:left">
                            <!-- v-if="!!form.factorySpecification" -->
                            <strong>
                                厂家规格：
                            </strong>
                            {{ form.factorySpecification}}
                        </div>
                        <div v-if="form.sampleSource=='厂家'" style="margin-right:20px;float:left">
                            <!-- v-if="!!form.factoryUrl" -->
                            <strong>
                                厂家链接：
                            </strong>
                            {{ form.factoryUrl}}
                        </div>

                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="数量：">
                            <el-input-number type="number" align="right" clearable :precision="0" :min="0" :max="1000000" :controls="false" v-model.number="form.quantity" style="width:183px;"></el-input-number>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="价格：">
                            <el-input-number type="number" align="right" clearable :precision="2" :min="0" :max="1000000" :controls="false" v-model.number="form.price" style="width:183px;"></el-input-number>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="采样状态：">
                            <el-select v-model="form.sampleOrderState">
                                <el-option :value="10" label="已登记" />
                                <el-option :value="20" label="已下单" />
                                <el-option :value="30" label="已到货" />
                            </el-select>
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="重量(kg)：">
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="10000" :controls="false" v-model.number="form.sampleWeight" style="width:183px;"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="长 * 宽 * 高：">
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="1000" :controls="false" @change="calcSampleVolume" v-model.number="form.sampleLength" style="width:90px;"></el-input-number>cm
                            *
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="1000" :controls="false" @change="calcSampleVolume" v-model.number="form.sampleWidth" style="width:90px;"></el-input-number>cm
                            *
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="1000" :controls="false" @change="calcSampleVolume" v-model.number="form.sampleHeigdth" style="width:90px;"></el-input-number>cm
                            =
                            体积：
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="100000000" :controls="false" v-model.number="form.sampleVolume" style="width:90px;"></el-input-number>cm³

                        </el-form-item>
                    </el-col>

                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="下单时间：">
                            <el-date-picker v-model="form.sampleOrderTime" clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" style="width:183px;"></el-date-picker>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="快递公司：">
                            <yh-expressselector :value.sync="form.expressCompanyCode" :text.sync="form.expressCompanyName"></yh-expressselector>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="快递单号：">
                            <el-input v-model="form.expressNum" :maxlength="30" clearable style="width:100%;"> </el-input>
                            <el-form>
                            <el-button v-if="!!form.expressNum" type='text' @click="onShowExproessHttp(form)">查看轨迹</el-button>
                        </el-form>
                        </el-form-item>   
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="包装重量(kg)：">
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="10000" :controls="false" v-model.number="form.packingWeight" style="width:183px;"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="包装长*宽*高：">
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="1000" :controls="false" @change="calcPackingVolume" v-model.number="form.packingLength" style="width:90px;"></el-input-number>cm
                            *
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="1000" :controls="false" @change="calcPackingVolume" v-model.number="form.packingWidth" style="width:90px;"></el-input-number>cm
                            *
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="1000" :controls="false" @change="calcPackingVolume" v-model.number="form.packingHeigdth" style="width:90px;"></el-input-number>cm
                            =
                            体积：
                            <el-input-number type="number" clearable :precision="2" :min="0" :max="100000000" :controls="false" v-model.number="form.packingVolume" style="width:90px;"></el-input-number>cm³

                        </el-form-item>
                    </el-col>

                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="到货时间：">
                            <el-date-picker v-model="form.sampleArrivalTime" clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" style="width:183px;"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="样品材质：">
                            <el-input v-model="form.sampleMaterial" :maxlength="100" clearable style="width:100%;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="对样结果：">
                            <el-select v-model="form.sampleCompareState" clearable :disabled=" !isYY">
                                <el-option :value="0" label=" " />
                                <el-option :value="10" label="没问题" />
                                <el-option :value="-10" label="有问题" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item label="样品图片：">
                            <yh-img-upload :value.sync="form.sampleImgUrl" :limit="5"></yh-img-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="包装图片：">
                            <yh-img-upload :value.sync="form.packingImgUrl" :limit="5"></yh-img-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" style="">

                        <el-form-item label="对样备注1">
                            <div v-if="!formEditMode || !isYY" style="max-height:290px;overflow:auto;">
                                <div v-html="form.sampleCompareDiscription" class="tempdiv"></div>
                            </div>
                            <yh-quill-editor :value.sync="form.sampleCompareDiscription" v-if="formEditMode && isYY">
                            </yh-quill-editor>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>

        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>

    </my-container>
</template>
<script>  


    import { formatTime } from "@/utils";
    import { formatmoney, formatPercen, platformlist, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { getSkuOrderAsync, saveSkuOrderAsync } from '@/api/operatemanage/productalllink/alllink';

    import YhImgUpload from "@/components/upload/yh-img-upload.vue";
    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
    import YhExpressselector from '@/components/YhCom/yh-expressselector.vue'
    import logistics from '@/components/Comm/logistics'

    export default {
        name: "skuOrderPage4Enquiry",
        components: { MyContainer, MyConfirmButton,logistics, YhImgUpload, YhQuillEditor, YhExpressselector },
        data() {
            return {
                that: this,
                form: {
                    "id": "",
                    "createdTime": null,
                    "goodsCompeteId": null,
                    "goodsCompeteName": null,
                    "goodsCompeteImgUrl": null,
                    "skuCode": null,
                    "skuName": null,
                    "skuImgUrl": null,
                    "sampleSource": null,
                    "sampleOrderState": 10,
                    "sampleOrderTime": null,
                    "sampleArrivalTime": null,
                    "sampleUserId": null,
                    "sampleUserName": null,
                    "expressNum": null,
                    "sampleWeight": null,
                    "sampleVolume": null,
                    "sampleLength": null,
                    "sampleWidth": null,
                    "sampleHeigdth": null,
                    "sampleMaterial": null,
                    "sampleImgUrl": null,
                    "sampleCompareState": 0,
                    "sampleCompareDiscription": null,
                    "expressCompanyCode": '',
                    "expressCompanyName": '',
                },
                pageLoading: false,
                formEditMode: true,//是否编辑模式
                selfInfo: {

                },
                isYY:true,//是否为运营 
                drawervisible:false,
            };
        },
        async mounted() {

            const userInfoName = "hotsalegoods_selfuserinfo";
            let selfInfo4Store = getStore(userInfoName);
            if (selfInfo4Store) {
                this.selfInfo = selfInfo4Store;
            }

        },
        computed: {

        },
        methods: {
            formatLinkProCode: formatLinkProCode,
            formatTime: formatTime,
            //计算样品体积
            calcSampleVolume() {
                let ln = isNaN(this.form.sampleLength) ? 0 : this.form.sampleLength;
                let wd = isNaN(this.form.sampleWidth) ? 0 : this.form.sampleWidth;
                let hd = isNaN(this.form.sampleHeigdth) ? 0 : this.form.sampleHeigdth;
                this.form.sampleVolume = Number((ln * wd * hd).toFixed(2));
            },
            //计算包装体积
            calcPackingVolume() {
                let ln = isNaN(this.form.packingLength) ? 0 : this.form.packingLength;
                let wd = isNaN(this.form.packingWidth) ? 0 : this.form.packingWidth;
                let hd = isNaN(this.form.packingHeigdth) ? 0 : this.form.packingHeigdth;
                this.form.packingVolume = Number((ln * wd * hd).toFixed(2));
            },
            async getSkuOrderData(skuId, formEditMode,isYY) {
                this.isYY=!!isYY;
                let form = this.form;
                // 清空from对象

                Object.keys(form).forEach(key => (form[key] = null));

                //处理编辑模式
                this.formEditMode = formEditMode;

                this.pageLoading = true;
                const res = await getSkuOrderAsync({ skuOrderId: skuId });
                this.pageLoading = false;

                this.form = { ...res?.data };

                if (this.form.sampleOrderState === 0)
                    this.form.sampleOrderState = 10;

            },
            async saveSkuOrderData() {

                let dtoData = {
                    ...this.form
                };

                const res = await saveSkuOrderAsync(dtoData);

                if (res?.success) {
                    this.$message({ message: '保存成功', type: "success" });
                }
            },
            async onShowExproessHttp(row) {
                this.drawervisible = true;
                this.$nextTick(function () {
                    this.$refs.logistics.showlogistics("",row.expressNum);
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }

    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }

    ::v-deep .el-input-number.is-without-controls .el-input__inner {
        padding-left: 5px;
        padding-right: 5px;
    }

    ::v-deep .el-input-number .el-input__inner {
        text-align: right;
    }
</style>
