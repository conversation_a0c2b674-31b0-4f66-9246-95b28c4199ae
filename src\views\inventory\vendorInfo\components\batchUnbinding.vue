<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.boothInformation" placeholder="请输入货架号信息(首字母+数字) 例:A02" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="batchUnbindingMethod">解绑</el-button>
      </div>
    </template>
    <vxetablebase :id="'batchUnbinding202503181337'" :tablekey="'batchUnbinding202503181337'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="400" @select="selectchange"
      @checkbox-range-end="selectchange">
    </vxetablebase>
    <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { getSampleGoodsList, batchUpdateSampleGoodsToBoothInformation } from '@/api/customerservice/albbinquirs'
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { width: '150', align: 'center', prop: 'image', label: '产品图片', type: "images" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'nameManufacturer', label: '供应商名称' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productName', label: '商品名称' },
  { sortable: 'custom', width: '220', align: 'center', prop: 'boothInformation', label: '展位' },
]
export default {
  name: "batchUnbinding",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      selectList: [],
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        boothInformation: '',
        shuXing: [],
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    // await this.getList()
  },
  methods: {
    selectchange(val) {
      this.selectList = val;
    },
    async batchUnbindingMethod() {
      if (this.selectList.length == 0) {
        this.$message.error('请选择要解绑的商品')
        return
      }
      this.$confirm('确定解绑吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data, success } = await batchUpdateSampleGoodsToBoothInformation({ jiebanglist: this.selectList.map(item => ({ id: item.id, boothInformation: item.boothInformation })) })
        if (success) {
          if (data.success) {
            this.$message.success('解绑成功')
            this.getList('search')
          } else {
            this.$message.error(data.msg)
          }
        } else {
          this.$message.error('解绑失败')
        }
      }).catch(() => {
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (!this.ListInfo.boothInformation) {
        this.$message.error('请输入货架号信息')
        this.tableData = []
        this.total = 0
        this.summaryarry = {}
        return
      }
      if (!/^[A-Za-z]/.test(this.ListInfo.boothInformation)) {
        this.$message.error('货架号信息格式不正确')
        this.tableData = []
        this.total = 0
        this.summaryarry = {}
        return
      }
      this.loading = true
      const { data, success } = await getSampleGoodsList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 250px;
    margin-right: 5px;
  }
}
</style>
