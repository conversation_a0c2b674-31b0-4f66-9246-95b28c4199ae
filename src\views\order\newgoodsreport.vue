<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="日期:">
          <el-date-picker style="width: 220px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束" :clearable="false"
            :picker-options="pickerOptions" @change="onSearch" ></el-date-picker>
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="filter.platform" placeholder="请选择" :clearable="true" :collapse-tags="true" filterable
            @change="onchangeplatformMain" style="width: 100px">
            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="filter.shopCode" style="width: 150px" placeholder="请选择" @change="onSearch"
            :clearable="true" :collapse-tags="true" filterable>
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="运营组:">
          <el-select v-model="filter.groupId" style="width: 110px" placeholder="请选择" :clearable="true"
            :collapse-tags="true" filterable @change="onSearch">
            <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="系列编码:">
          <el-input v-model.trim="filter.goodsCode" placeholder="系列编码" style="width: 130px" :maxlength=100 @change="onSearch" />
        </el-form-item>
        <el-form-item label="产品ID:">
          <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width: 130px" :maxlength=100 @change="onSearch" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <ces-table ref="table" :that="that" :isIndex="true" :hasexpand="true" @sortchange="sortchange"
      :summaryarry="summaryarryMain" :tableData.sync="list" :tableCols="tableCols" :tablefixed="true"
      :tableHandles="tableHandles" :headerCellStyle="headerCellStyle" 
      @cellclick="cellclick" :loading="listLoading">
      
      <el-table-column v-if="checkPermission('productnewpermission')" width="auto" label="图表"  fixed="right">
        <template slot-scope="scope">
          <div style="height: 120px;width:100%;margin-left: -20px;" :ref="'echarts'+scope.row.goodsCode"
            v-loading="echartsLoading"></div>
        </template>
      </el-table-column>
    </ces-table>

    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>
    <!-- 系列编码趋势图 -->
    <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" append-to-body width="80%"
      v-dialogDrag>
      <span>
        <template>
          <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
            <el-row>
              <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                <el-form-item label="日期:">
                  <el-date-picker style="width: 260px" v-model="detailfilter.timerange" type="daterange" 
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" :clearable="false"></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-form-item>
                  <el-button type="primary" @click="getecharts">刷新</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </span>
      <span>
        <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>



    <el-dialog :visible.sync="detail.visible" :show-close="false" width="90%" v-dialogDrag>
      <el-button type="primary" @click="onSearchDetail">查询</el-button>
      <el-button type="primary" @click="showNext">下一个</el-button>
      <div style="margin-bottom: 10px">
        <el-row>
          <el-col :span="20">
            <el-descriptions :column="3" size="mini" border>
              <el-descriptions-item label="系列编码">{{
              detail.selRow.goodsCode
              }}</el-descriptions-item>
              <el-descriptions-item label="系列名称">{{
              detail.selRow.goodsName
              }}</el-descriptions-item>
              <el-descriptions-item label="运营组">{{
              selGroupName
              }}</el-descriptions-item>
              <el-descriptions-item label="主链接">
                <div v-html="
                  myformatLinkProCode(
                    detail.selRow.platform,
                    detail.selRow.proCode
                  )
                "></div>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-image style="width: 60px; height: 60px" :src="detail.selRow.goodsImage"
              :preview-src-list="detail.srcList">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <picture />
                  </el-icon>
                </div>
              </template>
            </el-image>
          </el-col>
        </el-row>
      </div>
      <el-tabs type="card" style="margin-bottom: 0px" @tab-click="onTabClick" v-model="detail.tabName">
        <el-tab-pane label="近一个月详情" name="tabSame">
          <same-month-detail :filter="detail.filter" ref="sameProDetail" style="height: 480px"
            :platformList="platformList" :groupList="groupList">
          </same-month-detail>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <div v-show="isshowstate">
      <procodesimilaritystate ref="procodesimilaritystate" @changelist="changelist" @onSearch="onSearch">
      </procodesimilaritystate>
    </div>

    <!-- 订单量图表 -->
    <el-dialog :visible.sync="dialoganalysisVisible" width="80%" v-dialogDrag :show-close="false">
      <proCodeSimilarityAnalysis ref="proCodeSimilarityAnalysis" style="height: 550px"></proCodeSimilarityAnalysis>
    </el-dialog>




    <!-- 周转天数详情 -->
    <el-dialog :visible.sync="dialodayssisVisible" width="50%" v-dialogDrag :show-close="false">
      <Daysturnoverdetail ref="Daysturnoverdetail" :filter="detail.filter" style="height: 550px"></Daysturnoverdetail>
    </el-dialog>

    <el-drawer title="耗材费用" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editparmVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
      style="position:absolute;">
      <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
      <div class="drawer-footer">
        <el-button @click.native="editparmVisible = false">取消</el-button>
        <my-confirm-button type="submit" :loading="editparmLoading" @click="onSetEditParm" />
      </div>
    </el-drawer>

  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import checkPermission from '@/utils/permission';
import dayjs from "dayjs";
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { formatPlatform, formatLinkProCode, formatmoney } from "@/utils/tools";
import { rulePlatform } from "@/utils/formruletools";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import { batchAddProCodeSimilarity, getListByStyleCodeCost } from '@/api/operatemanage/base/product'
import { queryProCodeSimilarityAnalysis } from "@/api/order/procodesimilarity"
import buschar from '@/components/Bus/buschar'
import ProCodeBusinessStaffPlatForm from '@/views/order/ProCodeBusinessStaffPlatForm'
import { getProCodeBusinessStaffPlatForm } from '@/api/bookkeeper/reportday'
import {
  getLastUpdateTime,
  pageProCodeSimilarityByNewReport,
  exportProCodeSimilarity,
  exportProCodeSimilarity1,
  getProCodeSimilarityForMonthSummary,
  getProCodeSimilarityStateName,
  addTbProCodeSimilarityGrowSet
} from "@/api/order/procodesimilarity"
import {  queryGuardProductNewGoods } from '@/api/operatemanage/base/product'
import sameMonthDetail from './procodesimilarity/sameMonthDetail.vue';
import proCodeSimilarityAnalysis from './procodesimilarity/ProCodeSimilarityAnalysis.vue';
import Daysturnoverdetail from './procodesimilarity/daysTurnoverDetail.vue'
import procodesimilaritystate from './procodesimilarity/procodesimilaritystate.vue'
import * as echarts from 'echarts'

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
  var money = formatmoney(
    Math.abs(value) > 1 ? Math.round(value, 2) : Math.round(value, 1)
  );
  return money
};
//日报列
const dayReportCols = [
  { istrue: true, summaryEvent: true, prop: 'orderCountDayReport', label: '订单量', sortable: 'custom', width: '100', permission: "prosameprofit", formatter: (row) => !row.orderCountDayReport ? " " : row.orderCountDayReport, handle: (that, row) => that.Pananysis(row) },
  { istrue: true, summaryEvent: true, prop: 'allMarketingCost', label: '总广告费', sortable: 'custom', permission: "prosameprofit", width: '100', formatter: (row) => !row.allMarketingCost ? " " : row.allMarketingCost?.toFixed(2) },
];

//商品客户咨询列
const customerCols = [
  { istrue: true, summaryEvent: true, prop: 'inquiries', label: '咨询量', type: 'custom', tipmesg: '系列编码所有链接在查询日期范围内的顾客咨询量之和', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiries ? " " : row.inquiries },
  { istrue: true, summaryEvent: true, prop: 'inquiriesSuccessRate', label: '咨询量转化率', type: 'custom', tipmesg: '成功的咨询量/总咨询量', sortable: 'custom', permission: "", width: '80', formatter: (row) => !row.inquiriesSuccessRate ? " " : (row.inquiriesSuccessRate * 100).toFixed(2) + '%' },
];

const tableCols = [
  { istrue: true, fixed: true, prop: 'goodsCode', label: '系列编码', width: '280', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row, "tabSame") },
  { istrue: true, fixed: true, prop: 'goodsImage', label: '图片', width: '160', type: 'imageGoodsCode', goods: { code: 'goodsCode', name: 'goodsName' } },
  { istrue: true, fixed: true, prop: 'groupId', label: '运营组', type: 'custom', tipmesg: '主链接ID的所属运营组', width: '100', sortable: 'custom', formatter: (row) => row.groupName },
  { istrue: true, prop: 'customerGroups', label: '客服组', sortable: 'custom', permission: "setcusservicegroup", width: '100', type: 'custom', tipmesg: '选择客服组', type: 'click', handle: (that, row) => that.setcustomergroup(row) },
].concat(dayReportCols).concat(customerCols);

const tableHandles1 = [

];
const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");
const cityOptions = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton, sameMonthDetail, proCodeSimilarityAnalysis, buschar, Daysturnoverdetail, procodesimilaritystate, ProCodeBusinessStaffPlatForm },
  data() {
    return {
      buessness: { visible: false, title: "", data: [] },
      that: this,
      formtitle: "耗材费用",
      filter: {
        platform: null,
        shopCode: "",
        proCode: null,
        goodsCode: null,
        groupId: null,
        similarity: 0,
        days: null,
        profit2UnZero: null,
        profit3UnZero: null,
        state: null,
        startDate: null,
        endDate: null,
        timerange: [startDate, endDate],
      },

      list: [],
      summaryarry: {},
      pager: { OrderBy: "salesQty", IsAsc: false },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      cities: cityOptions,
      checkboxGroup1: [],
      platformList: [],
      prosimstatelist: [],
      shopList: [],
      groupList: [],
      total: 0,
      sels: [],
      deletegroupdialogVisible: false,
      listLoading: false,
      pageLoading: false,
      dialogDrVisible: false,
      dialoganalysisVisible: false,
      dialodayssisVisible: false,
      addFormVisible: false,
      addLoading: false,
      editparmVisible: false,
      editparmLoading: false,
      isshowstate: false,
      isshowtime: false,
      isshowmonth: false,
      lastUpdateTime: null,
      drparamProCode: '',
      handletype: 0,
      detail: {
        visible: false,
        filter: {
          parentId: null,
          proCode: null,
          status: null,
          platform: null,
          shopId: "",
          similarity: null,

          seriesCode: null,
          goodsCode: null,
          startDate: null,
          endDate: null,
          timerange: [startDate, endDate]
        },
        selRow: {},
        srcList: [],
        tabName: "tabSame",
        customRowStyle: function (data) {
          if (data.row.isMain) {
            return { color: 'red' };
          }
        },
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      autoform: {
        fApi: {},
        rule: [],
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } }
      },
      echartsLoading: false,
      detailfilter: {
        styleCode: null,
        platform: null,
        startTime: null,
        endTime: null,
        timerange: [startDate, endDate]
      },
      buscharDialog: { visible: false, title: "", data: [] },
    };
  },
  async mounted() {
    if (this.$route.query && this.$route.query.styleCode) {
      this.filter.goodsCode = this.$route.query.styleCode
    }
    await this.setPlatform();
    await this.setGroupSelect();
    await this.getLastUpdateTime();
    await this.getlist();
    await this.getprosimstatelist()
  },
  methods: {
    //获取状态信息
    async getprosimstatelist() {
      var res = await getProCodeSimilarityStateName();
      if (res?.code) {
        this.prosimstatelist = res.data.map(function (item) {
          var ob = new Object();
          ob.state = item;
          return ob;
        })
      }
    },
    async remoteSearchStyleCode(parm) {
      if (!parm) {
        //this.$message({ message: this.$t('api.sync'),type: 'warn'})
        return;
      }
      var options = [];
      const res = await getListByStyleCodeCost({ currentPage: 1, pageSize: 50, styleCode: parm })
      res?.data?.forEach(f => {
        options.push({ value: f.cost + ',' + f.goodsCode, label: f.goodsName + ' — ' + f.cost })
      })
      this.autoform.fApi.getRule('packCost1').options = options;
    },
    async setcustomergroup(e) {
      this.isshowstate = true
      await this.$refs.procodesimilaritystate.setcustomergroup(e, this.list)
    },
    async changelist(e) {
      this.list = e
    },
    //设置系列状态
    async setprostate(e) {
      this.isshowstate = true
      await this.$refs.procodesimilaritystate.OnSearch(e.goodsCode, this.list)
    },
    async setpromonth(e) {
      this.isshowmonth = true
    },
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    //设置店铺下拉
    async onchangeplatform(val) {
      const res = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 1000,
      });
      this.shopList = res.data.list || [];
      this.filter.shopCode = "";
    },
    //设置店铺下拉
    async onchangeplatformMain(val) {
      await this.onchangeplatform(val);
      await this.onSearch();
    },
    //设置店铺下拉
    async onchangeplatformDetail(val) {
      await this.onchangeplatform(val);
      await this.onSearchDetail();
    },
    //设置运营组下拉
    async setGroupSelect() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },

    //导出
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var res = await exportProCodeSimilarity(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "链接相似度_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    //导出
    async newonExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var res = await exportProCodeSimilarity1(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "系列编码报表" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    async remoteSearchUser(parm) {
      if (!parm) {
        //this.$message({ message: this.$t('api.sync'),type: 'warn'})
        return;
      }
      var options = [];
      const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: parm })
      res?.data?.forEach(f => {
        options.push({ value: f.styleCode, label: f.styleCode })
      })
      this.autoform.fApi.getRule('styleCode').options = options;
    },
    //修改
    async onHand(val) {
      if (val == 1) {
        this.handletype = val
        this.formtitle = "耗材费用设置"
        this.editparmVisible = true
        this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
        {
          type: 'select', field: 'styleCode', title: '系列编码', validate: [{ type: 'string', required: true, message: '请选择系列编码' }], value: "", options: [],
          props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
        },
        {
          type: 'select', field: 'packCost1', title: '包装费', validate: [{ type: 'string', required: true, message: '请选择包装费' }], value: "", options: [],
          props: { filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchStyleCode(parm) }
        },
        ]
        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0)
          this.autoform.fApi.reload()

        var model = { id: '', }
        this.$nextTick(async () => {
          var arr = Object.keys(this.autoform.fApi)
          if (arr.length > 0)
            await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue(model)
        })
      } else if (val == 2) {
        this.handletype = val
        this.formtitle = "增长率设置"
        this.editparmVisible = true
        var that = this
        this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 1 } },
        {
          type: 'select', field: 'styleCode', title: '系列编码', value: "", options: [],
          props: { multiple: true, filterable: true, allowCreate: false, clearable: true, remote: true, remoteMethod: (parm) => this.remoteSearchUser(parm) }
        },
        { type: 'radio', field: 'isAll', title: '是否设置全部', value: true, update(val) { { that.changeIsStyleCode(val) } }, options: [{ value: true, label: "批量", disabled: false }, { value: false, label: "全部" },], col: { span: 6 }, },
        { type: 'InputNumber', field: 'growRate', title: '增长率', props: { precision: 0 }, validate: [{ required: true, message: '请输入' }] },
        ]

        var arr = Object.keys(this.autoform.fApi);
        if (arr.length > 0)
          this.autoform.fApi.reload()

        var model = { id: '', }
        this.$nextTick(async () => {
          var arr = Object.keys(this.autoform.fApi)
          if (arr.length > 0)
            await this.autoform.fApi.resetFields()
          await this.autoform.fApi.setValue(model)
        })
      }


    },
    async changeIsStyleCode(val) {
      this.$nextTick(async () => {
        console.log('来了', val)
        var arr = Object.keys(this.autoform.fApi)
        if (arr.length > 0) {
          if (val == false)
            await this.autoform.fApi.hidden(true, 'styleCode')
          else if (val == true) {
            await this.autoform.fApi.hidden(false, 'styleCode')
          }
        }

      })
    },
    async onSetEditParm() {
      this.editparmLoading = true;
      if (this.handletype == 1) {
        await this.autoform.fApi.validate(async (valid, fail) => {
          if (valid) {
            const formData = this.autoform.fApi.formData()
            const res = await batchAddProCodeSimilarity(formData)
            if (res.code == 1) {
              this.editparmLoading = false;
              this.editparmVisible = false;
            }
          }
          else {
            this.editparmLoading = false;
          }
        })
      } else if (this.handletype == 2) {
        this.$nextTick(async () => {
          await this.autoform.fApi.validate(async (valid, fail) => {
            if (valid) {
              const formData = this.autoform.fApi.formData()
              if (formData.isAll == true) {
                formData.styleCode = formData.styleCode.join()
              }

              const res = await addTbProCodeSimilarityGrowSet(formData)
              this.editparmLoading = false;
              this.editparmVisible = false;
            }
            else {
              this.editparmLoading = false;
            }
          })
        })
      }
    },
    //获取查询条件
    getCondition() {
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      this.filter.startDate = null;
      this.filter.endDate = null;
      if (this.filter.timerange) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      const params = {
        ...pager,
        ...page,
        ...this.filter,
      };

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1);
      await this.getlist();
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      this.listLoading = true;
      const res = await pageProCodeSimilarityByNewReport(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      //this.summaryarry = res.data.summary;
      data.forEach((d) => {
        d._loading = false;
      });
      this.list = data;
      await this.getSummary();
      this.getEcharts()
    },
    getEcharts() {
      setTimeout(_ => {
        this.list.forEach(e => {
          let myChart = echarts.init(this.$refs['echarts' + e.goodsCode]);
          var series = []
          this.echartsLoading = true
          e.series.forEach(s => {
            if (s.name != '日期')
              series.push({ smooth: true, showSymbol: false, ...s })
          })
          this.echartsLoading = false
          myChart.setOption({
            legend: {
              show: false
            },
            grid: {
              left: "0",
              top: "6",
              right: "0",
              bottom: "0",
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              //不显示x轴线
              show: false,
              data: e.xAxis
            },
            yAxis: {
              type: 'value',
              show: false,
            },
            series: series
          });
          window.addEventListener("resize", () => {
            myChart.resize();
          });
        })
      }, 1000)
    },
    async cellclick(row, column, cell, event) {
      if (column.label == '图表') {
        this.detailfilter.styleCode = row.goodsCode
        this.detailfilter.platform = row.platform
        this.getecharts()
      }
    },
    async getecharts() {
      this.detailfilter.startTime = null;
      this.detailfilter.endTime = null;
      if (this.detailfilter.timerange) {
        this.detailfilter.startTime = this.detailfilter.timerange[0];
        this.detailfilter.endTime = this.detailfilter.timerange[1];
      }
      
      var params = { ...this.detailfilter }
      let that = this;
      this.listLoading=true;
      const res = await queryGuardProductNewGoods(params).then(res => {
        this.listLoading=false;
        that.buscharDialog.visible = true;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      })
      await this.$refs.buschar.initcharts()
    },
    async getSummary() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      const res = await getProCodeSimilarityForMonthSummary(params);
      if (!res?.success) {
        return;
      }
      this.summaryarry = res.data;
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else {
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    selsChange: function (sels) {
      this.sels = sels;
    },
    async getLastUpdateTime() {
      const res = await getLastUpdateTime({});
      if (!res?.success) {
        return;
      }
      if (res.data) this.lastUpdateTime = "最晚更新时间：" + res.data;
    },
    ///==明细 Start==========================================
    async showDetail(row, tabName) {
      this.detail.visible = true;
      this.detail.selRow = row;
      this.detail.srcList = [row.goodsImage];
      this.detail.tabName = tabName;
      this.clearDetailFilter();
      this.detail.filter.parentId = row.id;
      this.detail.filter.seriesCode = row.goodsCode;
      this.detail.filter.similarity = this.filter.similarity;
      this.detail.filter.shopCode = this.filter.shopCode;
      this.detail.filter.groupId = this.filter.groupId;
      this.detail.filter.proCode = this.filter.proCode;
      this.detail.filter.platform = this.filter.platform;

      this.detail.pager = { OrderBy: "id", IsAsc: false };
      await this.onTabClick({ label: "相似产品" });
    },
    async showTurnover(row) {
      this.dialodayssisVisible = true
      this.clearDetailFilter();
      this.detail.filter.goodsCode = row.goodsCode
      this.$nextTick(async () => {
        await this.$refs.Daysturnoverdetail.onSearch()
      })

    },
    clearDetailFilter() {
      this.detail.filter = {
        parentId: null,
        proCode: null,
        status: null,
        platform: null,
        shopId: "",
        similarity: null,
        seriesCode: null,
        goodsCode: null,
        timerange: this.filter.timerange,
        startDate: null,
        endDate: null,
      };
    },
    //分页查询
    async onSearchDetail() {
      if (this.detail.tabName == "tabSame") {
        await this.$refs.sameProDetail.onSearch();
      }
    },
    //订单量图表
    async Pananysis(row) {
      this.dialoganalysisVisible = true;
      let para = { proCode: row.proCode, timerange: this.filter.timerange }

      this.$nextTick(() => {
        this.$refs.proCodeSimilarityAnalysis.onSearch(para);
      });
    },
    //趋势图
    async showprchart(goodsCode) {
      //  window['lastseeprcodedrchart']=goodsCode
      //  this.buessness.data= window['lastseeprcodedrchart1']


      this.filter.startDate = null;
      this.filter.endDate = null;
      var startDate = null, endDate = null;
      if (this.filter.timerange) {
        startDate = this.filter.timerange[0];
        endDate = this.filter.timerange[1]
      }

      var params = { goodsCode: goodsCode, similarity: this.filter.similarity, goodsCode: goodsCode, isGroupBy: true }
      let that = this;
      const res = await queryProCodeSimilarityAnalysis(params).then(res => {
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      })
      this.$nextTick(async () => {
        await getProCodeBusinessStaffPlatForm({ goodsCode: goodsCode }).then(res => {
          that.buscharDialog.visible = true;
          that.buessness.visible = true
          that.buessness.data = res.data
        })
      })

      //await this.$refs.ProCodeBusinessStaffPlatForm.getanalysisdata(goodsCode)
    },
    showNext() {
      if (this.list && this.list.length > 0) {
        var nextRow = this.list[0];
        var findCur = false;
        this.list.forEach(item => {
          if (findCur) {
            findCur = false;
            nextRow = item;
          }
          if (item.id == this.detail.selRow.id) {
            findCur = true;
          }
        });
        this.detail.selRow = nextRow;
        this.showDetail(nextRow, this.detail.tabName);
      }
    },
    onTabClick(tab, event) {
      setTimeout(async () => {
        await this.onSearchDetail();
      }, 500);
    },
    ///==明细 End  ==========================================
    myformatLinkProCode(platform, proCode) {
      return formatLinkProCode(platform, proCode);
    },
    myformatPlatform(platform) {
      return formatPlatform(platform);
    },
    //表头样式
    headerCellStyle(data) {
      if (data && data.column) {
        var isDayReportCol = dayReportCols.find(
          (a) => a.prop == data.column.property
        );
        if (isDayReportCol) {
          return { color: "#F56C6C" };
        }
      }
      return null;
    },
  },
  computed: {
    selGroupName() {
      var name = this.groupList?.find(
        (a) => a.key == this.detail.selRow.groupId
      )?.value;
      return name || "未知";
    },
    selPlatformName() {
      var name = this.platformList?.find(
        (a) => a.value == this.detail.selRow.platform
      )?.label;
      return name;
    },
    summaryarryMain() {
      return this.summaryarry;
    },
  },
};
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}

::v-deep .el-table__fixed {
  pointer-events: auto;
}
</style>
