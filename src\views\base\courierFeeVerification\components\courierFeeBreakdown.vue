<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startReceiveDate" :endDate.sync="ListInfo.endReceiveDate"
                    class="publicCss" startPlaceholder="收寄时间" endPlaceholder="收寄时间" :clearable="false" />
                <el-select v-model="ListInfo.warehouses" clearable filterable placeholder="仓库" class="publicCss"
                    multiple collapse-tags v-if="info.type == 0">
                    <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.expressCompanyIds" multiple collapse-tags clearable filterable
                    placeholder="快递公司" class="publicCss" :style="{ width: width }" v-if="info.type == 1">
                    <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
                <number-range :min.sync="ListInfo.totalCountMin" :max.sync="ListInfo.totalCountMax"
                    min-label="订单量 - 最小值" max-label="订单量 - 最大值" class="publicCss" />
                    <number-range :min.sync="ListInfo.totalFeeMin" :max.sync="ListInfo.totalFeeMax" min-label="总快递费 - 最小值"
                    max-label="总快递费 - 最大值" class="publicCss" />
                <number-range :min.sync="ListInfo.feeMin" :max.sync="ListInfo.feeMax" min-label="快递费 - 最小值"
                    max-label="快递费 - 最大值" class="publicCss" />
                    <number-range :min.sync="ListInfo.weightMin" :max.sync="ListInfo.weightMax" min-label="重量 - 最小值"
                    max-label="重量 - 最大值" class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' border
            @sortchange='sortchange' :tableData='data.list' :tableCols='tableCols' :isSelection="false"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" :isSelectColumn="false"
            style="width: 100%;  margin: 0;height:600px" :loading="loading" :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, formatWarehouseNew, warehouselist } from '@/utils/tools'
import dayjs from 'dayjs'
import { getExpressComanyAll, } from "@/api/express/express";
import dateRange from "@/components/date-range/index.vue";
import decimal from "@/utils/decimalToFixed";
import { mergeTableCols } from '@/utils/getCols'
import request from '@/utils/request'
import numberRange from "@/components/number-range/index.vue";
import { row } from "mathjs";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, numberRange
    },
    props: {
        info: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
            that: this,
            warehouselist,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startReceiveDate: this.info.startReceiveDate,
                endReceiveDate: this.info.endReceiveDate,
                type: this.info.type,
                weightType: this.info.weightType,
                summarys: [],
                warehouses: this.info.warehouseId ? this.info.warehouseId : [],
                expressCompanyIds: this.info.expressCompanyId ? this.info.expressCompanyId : [],
            },
            api: '/api/express/Express/StatExpress/',
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            data: {},
            loading: false,
            pickerOptions,
            isExport: false,
            expresscompanylist: []
        }
    },
    created() {
        this.getCol()
        this.init1()
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        linkToDetail(row, label) {
            let params = {
                startReceiveDate: this.ListInfo.startReceiveDate,
                endReceiveDate: this.ListInfo.endReceiveDate,
            }
            if (this.info.type == 0) {
                params.warehouseId = row.warehouse
            } else if (this.info.type == 1) {
                params.expressCompanyId = row.expressCompanyId
            }
            let [start, end] = label.split('-').map(item => Number(item))
            if (start > 10) {
                start = decimal(start, 1000, '/')
            }
            if (end > 10) {
                end = decimal(end, 1000, '/')
            }
            params.weightMin = start
            params.weightMax = end
            console.log(params, 'params');
            this.$emit('linkToDetail', params)
        },
        async init1() {
            const res = await getExpressComanyAll({});
            if (!res?.success) return
            this.expresscompanylist = res.data;
        },
        extractNumbers(str) {
            const matches = str.match(/\d+(\.\d+)?/g); // 匹配整数或小数
            return matches ? matches.map(Number) : []; // 转换为数字数组
        },
        getCol() {
            let step = 0//步长
            let start;//起始值
            let end;//结束值
            [start, end] = this.extractNumbers(this.info.range)
            if (end == 500 || start == 500) {
                step = 100
            } else {
                step = 0.1
            }
            if (start == 500) {
                end = 1000
            }
            const arr = [
                { label: '订单量', prop: 'count' },
                { label: '订单占比', prop: 'countRate' },
                { label: '快递费', prop: 'fee' },
                { label: '快递费占比', prop: 'feeRate' }
            ]
            let tableCols = []
            let index = 0
            for (let i = start; i < end; i = decimal(Number(i), Number(step), '+')) {
                index++
                for (let j = 0; j < arr.length; j++) {
                    tableCols.push({
                        enabled: true,
                        sortable: 'custom',
                        width: '100',
                        align: 'center',
                        summaryType: arr[j].label.includes('占比') ? null : 'Sum',
                        prop: `${arr[j].prop + index}`,
                        label: `${arr[j].label}`,
                        mergeName: `${i}-${decimal(i, step, '+')}${(this.info.range.includes('kg') && start != 500) ? 'kg' : 'g'}`,
                        type: !this.info.isDetails ? 'click' : '',
                        handle: (that, row) => that.linkToDetail(row, `${i}-${decimal(i, step, '+')}`),
                    })
                }
            }
            tableCols.forEach(item => {
                if (item.label.includes('占比')) {
                    item.formatter = (row) => {
                        return decimal(row[item.prop], 100, '*') + '%'
                    }
                }
            })
            if (this.info.list) {
                this.info.list.forEach((item, index) => {
                    tableCols.unshift({
                        enabled: true,
                        sortable: 'custom',
                        width: '100',
                        align: 'center',
                        summaryType: null,
                        prop: item.prop,
                        label: item.label,
                        formatter: (row) => {
                            if (item.prop == 'warehouse') {
                                return formatWarehouseNew(row.warehouse)
                            } else {
                                return row[item.prop]
                            }
                        },
                        summaryType: (item.label !== '仓库' && item.label !== '快递公司') ? 'Sum' : null,
                    })

                })
            }
            this.ListInfo.summarys = tableCols
                .filter((a) => a.summaryType)
                .map((a) => {
                    return { column: a["prop"].charAt(0).toUpperCase() + a["prop"].slice(1), summaryType: a.summaryType };
                });
            const res = mergeTableCols(tableCols)
            this.$set(this, 'tableCols', res)
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {

                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}

::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
