<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' style="height:93%;" :isIndex='true'
                :hasexpand='false' @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange'
                :isSelection='false' :tableCols='tableCols' :loading="listLoading">
                <template slot='extentbtn'>
                    <el-input v-model="filter.name" v-model.trim="filter.name" placeholder="姓名" style="width:120px;"
                        disabled :maxlength="50" />
                    <el-input v-model="filter.timeStart" style="width:120px;" disabled />至
                    <el-input v-model="filter.timeEnd" style="width:120px;" disabled />
                </template>
            </ces-table>
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import {
  getOSDouYinShopPersonalEfficiency
} from "@/api/customerservice/waibao";

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '150' },
    { istrue: true, prop: 'sname', label: '姓名' },
    { istrue: true, prop: 'receiveds', label: '人工已接待人数', sortable: 'custom' },
    { istrue: true, prop: 'inquirs', label: '人工已接待会话量',  sortable: 'custom' },
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '80', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate).toFixed(2) + "%" },
    { istrue: true, prop: 'threeResponseRate  ', label: '3分钟人工回复率', width: '80', sortable: 'custom', formatter: (row) => (row.threeResponseRate * 100).toFixed(2) + "%" },
    { istrue: true, prop: 'responseTime', label: '平均响应时长(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '80', sortable: 'custom', formatter: (row) => (row.satisfactionRate).toFixed(2) + "%" },
    { istrue: true, prop: 'noSatisfactionCount', label: '不满意人数', sortable: 'custom', formatter: (row) => row.noSatisfactionCount },
    { istrue: true, prop: 'satisfactionCount', label: '满意人数', width: '70', sortable: 'custom', formatter: (row) => row.satisfactionCount },
    { istrue: true, prop: 'threeResponseCount', label: '3分钟回复人数',sortable: 'custom', formatter: (row) => row.threeResponseCount },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'payers', label: '支付人数', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'ipsRate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => (row.ipsRate * 100).toFixed(2) + "%" },
    // { istrue: true, prop: 'outTimes', label: '出勤人次', width: '70', sortable: 'custom' },
    // { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '70', sortable: 'custom' },
];

const tableCols2 = [
    { istrue: true, prop: 'shopName', label: '店铺名称', width: '150'},
    { istrue: true, prop: 'sname', label: '姓名', width: '80'},
    { istrue: true, prop: 'receiveds', label: '评价数量', width: '100', sortable: 'custom'},
    { istrue: true, prop: 'noSatisfactionRate', label: '不满意率', width: '100', sortable: 'custom', formatter: (row) => (row.noSatisfactionRate ).toFixed(2) + "%" },
    { istrue: true, prop: 'noSatisfactionCount', label: '不满意人数', width: '100', sortable: 'custom', formatter: (row) => row.noSatisfactionCount },
    { istrue: true, prop: 'satisfactionRate', label: '满意率', width: '100', sortable: 'custom', formatter: (row) => (row.satisfactionRate ).toFixed(2) + "%" },
    { istrue: true, prop: 'satisfactionCount', label: '满意人数', width: '100', sortable: 'custom', formatter: (row) => row.satisfactionCount },

];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, datepicker, cesTable },
    data() {
        return {
            that: this,
            filter: {
                name: "",
                sdate: [],
                timeEnd: "",
                timeStart: ""
            },
            isService:false,//服务数据统计弹框
            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {

    },
    created() {

    },
    methods: {
        async dialogOpenAfter(data) {
            this.filter.sdate[0] = data.startDate;
            this.filter.sdate[1] = data.endDate;
            this.filter.timeStart = data.startDate;
            this.filter.timeEnd = data.endDate;
            this.filter.name = data.name;
             this.isService = data.isService;
             console.log(data.isService,'isService')
            this.onSearch();
        },
        onSearch() {
            this.getinquirsstatisticsList();
        },
        async getinquirsstatisticsList() {
            var newArr=this.isService?tableCols2:tableCols;
            this.tableCols=newArr;

            const para = { ...this.filter };
            const params = {
                ...this.pager,
                ...para,
            };
            this.listLoading = true;
            const res = await getOSDouYinShopPersonalEfficiency(params);
            this.listLoading = false;
            this.total = res.data.total
            this.inquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
