<template>
    <MyContainer class="ces-main">
        <div
            style="width: 100%; height: 100%; display: flex; flex-direction: column; padding: 20px; box-sizing: border-box;">
            <div style="height: 60px; width: 100%; align-items: center; display: flex; flex-direction: row;">
                <el-button type="primary" @click="addformfuc">新增仓库</el-button>
                <el-button type="primary" @click="onshowfile">导入快递费</el-button>
            </div>
            <div
                style="height: 60%; width: 100%; align-items: center; display: flex;flex-direction: row; overflow-x: auto; white-space: nowrap;">
                <div v-for="(item, i) in listtop" :key="i" style="box-sizing: border-box; " @click="itemclick(item)">
                    <div
                        style="width: 200px; height: 250px; margin-right: 10px; padding: 10px 5px; font-size: 13px; border: 1px solid; box-sizing: border-box; display: flex; flex-direction: column;  border-radius: 10px; ">
                        <!-- <el-row style="align-self: center;">
                        <el-col :span="6" style="text-align: center;">仓库：</el-col>
                        <el-col :span="18" style="text-align: center; font-size: 20px; font-weight: 600;">[南昌芒果仓]</el-col>
                    </el-row> -->
                        <div
                            style="align-items: center; word-break: break-word; overflow-wrap: break-word; width: 200px;  white-space: pre-wrap;">
                            <span>仓库：</span>
                            <span style="font-size: 15px; font-weight: 600;"> {{ item.warehouseName }}</span>
                        </div>
                        <div
                            style="align-items: center; word-break: break-word; overflow-wrap: break-word; width: 160px; white-space: pre-wrap;">
                            <span>特性：</span>
                        </div>
                        <div
                            style="width: 180px; height: 100%; margin-top: -10px; overflow-y: auto;box-sizing: border-box;">
                            <div
                                style="word-break: break-word; overflow-wrap: break-word; width: 160px; white-space: pre-wrap; font-size: 13px; padding-left: 15px; box-sizing: border-box;">
                                <br />{{ item.particularity }}
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div
                style="height: 100%; width: 100%; display: flex; border: 1px solid; border-radius: 10px; box-sizing: border-box;overflow: auto;">
                <div style="box-sizing: border-box; height: 100%; width: 100%; box-sizing: border-box; ">
                    <div
                        style="width: 100%; height: 100%; box-sizing: border-box; padding: 20px; display: flex; flex-direction: column;">
                        模拟计算
                        <div style="margin-top: 20px;">
                            <el-form :model="ruleForm" label-position="left" status-icon :rules="addFormRules"
                                ref="ruleForm" label-width="100px" class="demo-ruleForm">
                                <div style="display: flex;">
                                    <el-form-item label="仓库:" prop="specialWarehouseId" class="itemMR">
                                        <el-select style="width: 300px;" @change="getquyulist" filterable
                                            v-model="ruleForm.specialWarehouseId" placeholder="仓库" :collapse-tags="true"
                                            clearable>
                                            <el-option v-for="item in listtop" :key="item.id"
                                                :label="item.warehouseName" :value="item.id" />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="发货区域:" prop="province" class="itemMR">
                                        <el-select style="width: 300px;" v-model="ruleForm.province" filterable
                                            @change="$forceUpdate()" placeholder="发货区域" :collapse-tags="true" clearable>
                                            <el-option v-for="item in quyulist" :key="item" :label="item"
                                                :value="item" />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="样品重量:(kg)" prop="ypweight" label-width="120px" class="itemMR">
                                        <el-input style="width: 300px;" placeholder="单位(kg)" maxlength="20"
                                            @blur="numtofour(ruleForm.ypweight, 1)" clearable
                                            v-model="ruleForm.ypweight"
                                            @input="numchange(ruleForm.ypweight, 1)"></el-input>
                                    </el-form-item>
                                </div>
                                <div style="display:flex">
                                    <el-form-item label="包材材质:" prop="bcczType">
                                        <el-select style="width: 300px;" v-model="ruleForm.bcczType"
                                            @change="changeType" class="itemMR">
                                            <el-option label="纸箱" value="纸箱" />
                                            <el-option label="纸管" value="纸管" />
                                            <el-option label="快递袋" value="快递袋" />
                                            <el-option label="气泡袋" value="气泡袋" />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="样品尺寸:" />
                                    <!-- 纸箱：长度；气泡袋：底宽 -->
                                    <el-form-item :label="ruleForm.bcczType == '纸管' ? '长(m)' : '长(cm)'" prop="length"
                                        label-width="70px" class="itemMR" ref="length">
                                        <el-input-number v-model="ruleForm.length" :min="0" :max="9999"
                                            :placeholder="ruleForm.bcczType == '纸管' ? '长(m)' : '长(cm)'"
                                            :controls="false" :precision="4" />
                                    </el-form-item>
                                    <!-- 纸箱：宽度；纸管：口径；气泡袋：两边长度 -->
                                    <el-form-item :label="ruleForm.bcczType == '纸管' ? '口径(mm)' : '宽(cm)'" ref="width"
                                        prop="width" :label-width="ruleForm.bcczType == '纸管' ? '90px' : '70px'"
                                        class="itemMR">
                                        <el-input-number v-model="ruleForm.width" :min="0" :max="9999"
                                            :placeholder="ruleForm.bcczType == '纸管' ? '口径(mm)' : '宽(cm)'"
                                            :controls="false" :precision="4" />
                                    </el-form-item>
                                    <!-- 纸箱：高度|纸管：壁厚 -->
                                    <el-form-item v-if="ruleForm.bcczType == '纸箱' || ruleForm.bcczType == ''"
                                        label="高(cm)" prop="height" ref="height" label-width="70px" class="itemMR">
                                        <el-input-number v-model="ruleForm.height" :min="0" :max="9999"
                                            placeholder="高(cm)" :controls="false" :precision="4" />
                                    </el-form-item>
                                </div>
                                <el-button type="primary" style="width: 250px"
                                    @click="sumsubmitForm('ruleForm')">模拟计算</el-button>

                            </el-form>
                            <div class="computedResult">
                                <div v-if="data && data.length > 0" v-for="item in data" class="computedResult_item">
                                    <div class="computedResult_item_right" style="margin-right: 0px;">
                                        <div class="computedResult_item_right_item computedResult_item_left">模拟报价：{{
                                            item.simulatedQuotation ?
                                                item.simulatedQuotation : 0 }} 元</div>
                                        <div class="computedResult_item_right_item computedResult_item_left">仓库：{{
                                            item.warehouseName ?
                                                item.warehouseName : '' }}</div>

                                    </div>
                                    <div class="computedResult_item_right">
                                        <div class="computedResult_item_right_item colorRed">计泡重量：{{ item.jpWeight ?
                                            item.jpWeight : 0 }} (公斤)</div>
                                        <div class="computedResult_item_right_item">包装费用：{{ item.packageFee ?
                                            item.packageFee : 0 }} 元</div>
                                        <div class="computedResult_item_right_item">预估快递费用：{{ item.expressFee ?
                                            item.expressFee : 0 }} 元{{ item.type == 1 ? '(德邦快递)' : item.type == 0 ?
                                                '(快递均价)' : item.type == 2 ? '(京东物流)' : '' }}</div>
                                    </div>
                                </div>
                                <div v-if="!data || data.length == 0" class="computedResult_item">
                                    <div class="computedResult_item_right" style="margin-right: 0px;">
                                        <div class="computedResult_item_right_item computedResult_item_left">模拟报价：0 元
                                        </div>
                                        <div class="computedResult_item_right_item computedResult_item_left">仓库：暂无仓库
                                        </div>
                                    </div>
                                    <div class="computedResult_item_right">
                                        <div class="computedResult_item_right_item colorRed">计泡重量：0 (公斤)</div>
                                        <div class="computedResult_item_right_item">包装费用：0 元</div>
                                        <div class="computedResult_item_right_item">预估快递费用：0 元</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

            </div>

        </div>

        <el-dialog v-dialogDrag :title="saveForm.id ? '编辑仓库' : '新增仓库'" :visible.sync="dialogVisible"
            :close-on-click-modal="false" width="50%">
            <el-form :model="saveForm" label-position="left" status-icon :rules="saveFormRules" ref="ruleFormcre"
                label-width="120px" class="demo-ruleForm">
                <el-form-item label="仓库:" prop="warehouseId">
                    <!-- multiple -->
                    <el-select style="width: 50%;" filterable v-model="saveForm.warehouseId" @change="namesel"
                        placeholder="仓库" :collapse-tags="true" clearable>
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <div style="display: flex;">
                    <el-form-item label="重量(kg):" prop="weightType" class="itemMR">
                        <el-select v-model="saveForm.weightType" @change="changeWeightType">
                            <el-option label="介于" :value="0" />
                            <el-option label="大于等于" :value="1" />
                            <el-option label="小于等于" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="weightGte" v-if="saveForm.weightType != 2" class="itemMR" label-width="0px"
                        ref="weightGte">
                        <el-input-number v-model="saveForm.weightGte" :min="0" :max="999999"
                            :placeholder="saveForm.weightType == 0 ? '最小(Kg)' : '大于等于(Kg)'" :controls="false"
                            :precision="4" />
                    </el-form-item>
                    <el-form-item prop="weightLte" v-if="saveForm.weightType != 1" class="itemMR" label-width="0px"
                        ref="weightLte">
                        <el-input-number v-model="saveForm.weightLte" :min="0" :max="999999"
                            :placeholder="saveForm.weightType == 0 ? '最大(Kg)' : '小于等于(Kg)'" :controls="false"
                            :precision="4" />
                    </el-form-item>
                </div>
                <div style="display: flex;">
                    <el-form-item label="最大长度(cm)" prop="lengthLte" class="itemMR" label-width="120px">
                        <el-input-number v-model="saveForm.lengthLte" :min="0" :max="9999" placeholder="小于等于最大长度(cm)"
                            :controls="false" :precision="4" style="width: 200px;" />
                    </el-form-item>
                    <!-- <el-form-item label="宽度(cm)" prop="widthLte" label-width="90px" class="itemMR">
                        <el-input-number v-model="saveForm.widthLte" :min="0" :max="9999" placeholder="小于等于宽度(cm)"
                            :controls="false" :precision="4" />
                    </el-form-item>
                    <el-form-item label="高度(cm)" prop="heightLte" label-width="90px" class="itemMR">
                        <el-input-number v-model="saveForm.heightLte" :min="0" :max="9999" placeholder="小于等于高度(cm)"
                            :controls="false" :precision="4" />
                    </el-form-item> -->
                </div>
                <el-form-item label="特性:" prop="particularity">
                    <el-input type="textarea" placeholder="请输入特性" v-model="saveForm.particularity" maxlength="300"
                        rows="5" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="danger" @click="delrow()" v-if="saveForm.id">删除</el-button>
                <el-button type="primary" @click="submitForm('ruleFormcre')">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
            <span>
                <el-upload ref="upload2" class="upload-demo" :auto-upload="true" :file-list="fileList" :multiple="false"
                    :limit="1" action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadFile2">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-form :model="fileForm" label-position="left" status-icon :rules="fileFormRules" ref="ruleFormup"
                        label-width="100px" class="demo-ruleForm">
                        <el-form-item label="仓库:" prop="specialWarehouseId">
                            <!-- multiple -->
                            <el-select style="width: 50%;" filterable v-model="fileForm.specialWarehouseId"
                                placeholder="仓库" :collapse-tags="true" clearable>
                                <el-option v-for="item in listtop" :key="item.id" :label="item.warehouseName"
                                    :value="item.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="快递公司:" prop="expressCompany">
                            <!-- multiple -->
                            <el-select style="width: 50%;" filterable v-model="fileForm.expressCompany" placeholder="仓库"
                                :collapse-tags="true" clearable>
                                <el-option v-for="item in kuaidilist" :key="item" :label="item" :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <!-- <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button> -->
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
                <el-button size="small" type="primary" @click="onSubmitupload('ruleFormup')">提交</el-button>

            </span>
        </el-dialog>


    </MyContainer>
</template>

<script>
import checkPermission from '@/utils/permission'
import MyContainer from '@/components/my-container'
import { getAllWarehouse } from '@/api/inventory/warehouse'
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import axios from 'axios'
import decimal from '@/utils/decimal'
import {
    specialWarehouseSubmit, specialWarehousePage, specialWarehouseRemove,
    specialWarehouseShippingAreaList, specialWarehouseExpressFeeCalculate, specialWarehouseExpressFeeImport
} from "@/api/bladegateway/worcestorejk.js"
import {
    //分页查询店铺商品资料
    getListForScan,
} from "@/api/inventory/basicgoods"

const kuaidilist = [
    '中通',
    '圆通',
    '申通',
    '韵达',
    '顺丰',
    '邮政',
    '德邦',
    '京东',
    '苏宁',
    '唯品会',
    '极兔',
    '跨越',
    '菜鸟驿站'
];
export default {
    components: { MyContainer, },
    data() {
        var checkNum = (rule, value, callback) => {
            if (value == 0 || value == undefined) {
                return callback(new Error('不能为零或空'));
            }
            callback();
        };
        return {
            kuaidilist,
            dialogVisible: false,
            dialogVisibleSyj: false,
            ruleForm: {
                fahuo: '',
                gao: '',
                kuang: '',
                chang: '',
                bcczType: '',
                // cangku: '',
                warehouse: '',
                zhongliang: ''
            },

            addFormRules: {
                bcczType: [{ required: true, message: '请选择包材材质', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                ypweight: [{ required: true, message: '请填写', trigger: 'blur' }],
                specialWarehouseId: [{ required: true, message: '请选择', trigger: 'blur' }],
                province: [{ required: true, message: '请选择', trigger: 'blur' }],
                length: [{ required: true, message: '请填写', trigger: 'blur' }],
                width: [{ required: true, message: '请填写', trigger: 'blur' }],
                height: [{ required: true, message: '请填写', trigger: 'blur' }],
            },
            saveForm: {
                weightType: 0,
                weightGte: undefined,
                weightLte: undefined,
                lengthLte: undefined,
                widthLte: undefined,
                heightLte: undefined,
            },
            saveFormRules: {
                particularity: [{ required: true, message: '请填写', trigger: 'blur' }],
                warehouseId: [{ required: true, message: '请选择', trigger: 'blur' }],
                weightType: [{ required: true, message: '请选择', trigger: 'blur' }],
                weightGte: [{ required: true, message: '请填写最小重量', trigger: 'blur' }],
                weightLte: [{ required: true, message: '请填写最大重量', trigger: 'blur' }],
                lengthLte: [{ required: true, message: '请填写长度', trigger: 'blur' }],
                widthLte: [{ required: true, message: '请填写宽度', trigger: 'blur' }],
                heightLte: [{ required: true, message: '请填写高度', trigger: 'blur' }],
            },
            fileFormRules: {
                expressCompany: [{ required: true, message: '请选择', trigger: 'blur' }],
                specialWarehouseId: [{ required: true, message: '请选择', trigger: 'blur' }],
            },
            fileForm: {},
            warehouselist: [],
            baocailist: [],
            listtop: [],
            quyulist: [],  //区域列表下拉
            fileList: [],
            data: [],
        }
    },
    mounted() {
        this.getlist(); //获取列表
        this.getbaocai(); //获取包材
        this.getWarehouseList(); //获取仓库
    },
    methods: {
        changeType() {
            this.ruleForm.height = undefined;
            this.ruleForm.width = undefined;
            this.ruleForm.length = undefined;
            this.$refs.height.clearValidate();
            this.$refs.width.clearValidate();
            this.$refs.length.clearValidate();
        },
        changeWeightType() {
            this.saveForm.weightGte = undefined;
            this.saveForm.weightLte = undefined;
            this.$refs.weightGte.clearValidate();
            this.$refs.weightLte.clearValidate();
        },
        numtofour(value, type) {
            if (!this.ruleForm.chang) {
                this.ruleForm.chang = 0;
            }
            if (!this.ruleForm.kuang) {
                this.ruleForm.kuang = 0;
            }
            if (!this.ruleForm.gao) {
                this.ruleForm.gao = 0;
            }

            if (type == 1 && Number(this.ruleForm.ypweight) > 0) {
                this.ruleForm.ypweight = parseFloat(value).toFixed(4);
                if (Number(this.ruleForm.ypweight) >= 9999.9999) {
                    this.ruleForm.ypweight = 999.9999;
                }
            } else if (type == 2 && Number(this.ruleForm.chang)) {
                this.ruleForm.chang = parseFloat(value).toFixed(2);
                if (Number(this.ruleForm.chang) >= 9999.99) {
                    this.ruleForm.chang = 999.99;
                }
            } else if (type == 3 && Number(this.ruleForm.kuang)) {
                this.ruleForm.kuang = parseFloat(value).toFixed(2);
                if (Number(this.ruleForm.kuang) >= 9999.99) {
                    this.ruleForm.kuang = 999.99;
                }
            } else if (type == 4 && Number(this.ruleForm.gao)) {
                this.ruleForm.gao = parseFloat(value).toFixed(2);
                if (Number(this.ruleForm.gao) >= 9999.99) {
                    this.ruleForm.gao = 999.99;
                }
            }


            this.ruleForm.system_weight = (this.ruleForm.chang * this.ruleForm.kuang * this.ruleForm.gao) / 8000;
            // }
            if (this.ruleForm.ypweight - this.ruleForm.system_weight > 0) {
                this.ruleForm.weight = this.ruleForm.ypweight;
            } else {
                this.ruleForm.weight = this.ruleForm.system_weight;
            }

            this.$forceUpdate();
        },
        onshowfile() {
            this.fileForm = {};
            this.fileList = [];
            this.$nextTick(() => {
                this.dialogVisibleSyj = true;
                setTimeout(() => {
                    this.$refs.upload2.clearFiles();
                }, 0)
            })
        },
        async onSubmitupload(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (!valid) {
                    return false;
                }
                let params = {
                    ...this.fileForm
                }
                const { data, success } = await specialWarehouseExpressFeeImport(params)
                if (!success) {
                    return
                }
                this.$message({
                    message: '导入成功',
                    type: 'success'
                })
                this.dialogVisibleSyj = false
                // this.ruleForm.kuaiprice = data;
            })

        },
        uploadFile2(filedata) {
            console.log(filedata)
            const file = filedata.file;
            this.AjaxFile(file, 0, "");
        },
        async AjaxFile(file, i, batchnumber) {

            console.log(2222222, file, i, batchnumber)


            var name = file.name; //文件名

            var size = file.size; //总大小

            var shardSize = 15 * 1024 * 1024;//2m

            var shardCount = Math.ceil(size / shardSize); //总片数

            if (i >= shardCount) {

                return;

            }

            //计算每一片的起始与结束位置

            var start = i * shardSize;

            var end = Math.min(size, start + shardSize);

            //构造一个表单，FormData是HTML5新增的

            i = i + 1;

            var form = new FormData();

            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分

            form.append("batchnumber", batchnumber);

            form.append("fileName", name);

            form.append("total", shardCount); //总片数

            form.append("index", i); //当前是第几片

            const res = await xMTVideoUploadBlockAsync(form);

            if (res?.success) {

                if (i == shardCount) {

                    res.data.fileName = name;

                    res.data.uid = file.uid;

                    res.data.upLoadPhotoId = 0;

                    this.fileForm.fileName = res.data.fileName;
                    this.fileForm.fileUrl = res.data.url;


                    // this.retdata.push(res.data);

                    // this.imglist.push(res.data.url)

                } else {

                    await this.AjaxFile(file, i, res.data);

                }

            } else {

                this.$message({ message: res?.msg, type: "warning" });

            }

        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        addformfuc() {
            if(!checkPermission('TSC:KDF:YYG:XZ')){
                this.$message({
                    message: '暂无新增权限，请联系管理员开通！',
                    type: 'error'
                })
                return;
            }
            this.saveForm = {};

            this.dialogVisible = true;
        },
        async sumsubmitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (!valid || this.ruleForm.height == 0 || this.ruleForm.width == 0 || this.ruleForm.length == 0) {
                    return this.$message.error('样品尺寸信息不能为0或校验失败');
                }

                // let params = {
                //     "province": this.ruleForm.province,
                //     "specialWarehouseId": this.ruleForm.specialWarehouseId,
                //     "weight": this.ruleForm.weight
                // }
                console.log(this.ruleForm, "this.ruleForm");

                const { data, success } = await specialWarehouseExpressFeeCalculate(this.ruleForm)
                if (!success) {
                    return
                }
                this.data = data
                // this.ruleForm.kuaiprice = data;

                // this.ruleForm.tolprice = data + this.ruleForm.price;
                this.$forceUpdate();

            });

        },
        baocaichange(val) {
            console.log(this.baocailist.filter(g => g.label == val))
            this.ruleForm.price = this.baocailist.filter(g => g.label == val)[0].value;
        },
        numchange(val, type) {

        },
        async getquyulist(val) {
            let params = {
                "specialWarehouseId": val,
            }
            const { data, success } = await specialWarehouseShippingAreaList(params)
            if (!success) {
                return
            }
            this.ruleForm.province = "";
            this.quyulist = data;
        },
        async delrow() { //删除item
            let params = {
                "id": this.saveForm.id,
            }
            //删除
            const { data, success } = await specialWarehouseRemove(params)
            if (!success) {
                return
            }
            this.$message({
                message: '删除成功',
                type: 'success'
            })
            this.dialogVisible = false;
            this.getlist();

        },
        itemclick(row) {
            if(!checkPermission('TSC:KDF:YYG:BJ')){
                this.$message({
                    message: '暂无编辑权限，请联系管理员开通！',
                    type: 'error'
                })
                return;
            }
            this.saveForm = JSON.parse(JSON.stringify(row))
            this.dialogVisible = true;
        },
        async getlist() {
            let params = {
                "currentPage": 1,
                "pageSize": 9999
            }
            //新增
            const { data, success } = await specialWarehousePage(params)
            if (!success) {
                return
            }

            this.listtop = data.list;
            this.listtop.map((item) => {
                item.id = item.id.toString();
            })
        },
        namesel(val) {
            console.log(11111, this.warehouselist.filter(g => g.value == val))
            this.saveForm.warehouseName = this.warehouselist.filter(g => g.value == val)[0].label;
        },
        async submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (!valid) {
                    return;
                }
                const map = {
                    weightGte: '最小重量',
                    weightLte: '最大重量',
                    lengthLte: '最大长度',
                }
                if (this.saveForm.weightType == 0) {
                    if (this.saveForm.weightGte >= this.saveForm.weightLte) {
                        this.$message.error('最小重量不能大于等于最大重量');
                        return;

                    }
                    for (const key in map) {
                        if (this.saveForm[key] == 0 || this.saveForm[key] == undefined || this.saveForm[key] == null) {
                            this.$message.error(`${map[key]}不能为0或空`);
                            return;
                        }
                    }
                } else if (this.saveForm.weightType == 1) {
                    for (const key in map) {
                        if (key != 'weightLte') {
                            if (this.saveForm[key] == 0 || this.saveForm[key] == undefined || this.saveForm[key] == null) {
                                this.$message.error(`${map[key]}不能为0或空`);
                                return;
                            }
                        }
                    }
                } else if (this.saveForm.weightType == 2) {
                    for (const key in map) {
                        if (key != 'weightGte') {
                            if (this.saveForm[key] == 0 || this.saveForm[key] == undefined || this.saveForm[key] == null) {
                                this.$message.error(`${map[key]}不能为0或空`);
                                return;
                            }
                        }
                    }
                }
                let params = {
                    ...this.saveForm
                }
                //新增
                const { data, success } = await specialWarehouseSubmit(params)
                if (!success) {
                    return
                }
                this.$message({
                    message: '保存成功',
                    type: 'success'
                })
                this.dialogVisible = false;
                this.getlist();

                // }
            })

        },
        async getbaocai() {
            let params = {
                "brandId": ["5"]
            }
            const { data: { list }, success } = await getListForScan(params)
            if (!success) {
                return
            }
            this.baocailist = list.map(item => {
                return {
                    value: item.costPrice,
                    label: item.goodsCode + " " + item.goodsName
                }
            })
            console.log(list, "包材哦耗材");
        },
        async getWarehouseList() {
            let res3 = await getAllWarehouse();
            // this.warehouselist = res3.data.filter((x) => x.name.indexOf('代发') < 0);
            this.warehouselist = res3.data;
            this.warehouselist.map((x) => {
                x.value = x.wms_co_id.toString();
                x.label = x.name;
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.ces-main {
    box-sizing: border-box;

}

.itemMR {
    margin-right: 10px;
}

.computedResult {
    display: flex;
    align-items: center;
    padding-top: 20px;
    font-size: 14px;
    flex-wrap: wrap;
    font-weight: 600;

    .computedResult_item {
        margin: 0 10px 15px 0;
        display: flex;

        .computedResult_item_right {
            margin-left: 20px;
            display: flex;
            flex-direction: column;

            .computedResult_item_right_item {
                line-height: 35px;
                width: 220px;
                text-align: left;
            }

            .colorRed {
                color: red;
            }

            .computedResult_item_left {
                width: 150px;
                text-align: left;
            }
        }


    }
}
</style>
