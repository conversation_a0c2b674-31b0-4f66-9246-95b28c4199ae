<template>
    <my-container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :isSelectColumn="false"  :tableData='goodslist' 
            :tableCols='tableCols'  :tablefixed="true" :loading="goodslistloading">
        </ces-table>
                
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import { getSeriesGoodsCode } from "@/api/order/procodesimilarity"

const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', permission:"proCodeSimilarityGrow", tipmesg: '', width: '200', },
    { istrue: true, prop: 'goodsName', label: '商品名称', permission:"proCodeSimilarityGrow", tipmesg: '', width: 'auto', },
]

export default {
    name: 'YunHanAdminProcodesimilaritygrowdrawer',
    components: {MyContainer, MySearch, MySearchWindow, cesTable},

    data() {
        return {
            that: this,
            styleCode: '',
            goodslist: [],
            tableCols: tableCols,
            total: 0,
            sels: [],
            goodslistloading: false,
            dialogAddVisible: false,
        };
    },

    async mounted() {
        
    },

    methods: {
        async onSearch(row) {
            console.log('接收数据',row)
            this.styleCode = row.styleCode;
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            var pager = this.$refs.pager.getPager();
            const params = {seriesCode: this.styleCode, ...pager};
            this.dialogAddVisible = true;
            this.goodslistloading = true;
            var res = await getSeriesGoodsCode(params);
            if (!res.success) return;
            this.total = res.data.total;
            const data = res.data.list;
            this.goodslist = data;
            
            this.goodslistloading = false;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>