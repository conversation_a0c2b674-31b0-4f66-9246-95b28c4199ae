<template>
  <container>
    <el-row>
     
        <div>
          <ces-table ref="table" :that='that' style="height:260px" :isIndex='true' @sortchange='sortchange' :isSelectColumn="false" @cellclick='cellclick'
         :hasexpand='true' :tableData='detaillist' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      </ces-table>
        </div>
        
      <div style="margin-top:10px">
        <el-pagination
          @size-change="sizeChange"
          @current-change="currentChange"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total">
        </el-pagination>        
      </div>
          
    </el-row>
    <el-row>
<el-col :span="24">
        <div class="grid-content bg-purple-light">
          <div
            style="margin-top: 20px"
            v-loading="charts.loading"
            element-loading-text="加载中"
            element-loading-spinner="el-icon-loading"
          >
            <div id="myChartOrderCount"></div>
          </div>
        </div>
      </el-col>
    </el-row>
  </container>
</template>

<script>
import {  queryPurchaseOrderDetail,  getOrderChartsOrderCount,} from "@/api/inventory/purorder";
import container from "@/components/my-container/noheader";
import cesTable from "@/components/Table/table.vue";
import { formatTime,formatYesornoBool,formatWarehouseArea,formatNoLink,formatIsError,formatIsOutStock,formatSecondToHour,} from "@/utils/tools";
import * as echarts from "echarts";
import dayjs from "dayjs";
const tableCols = [
  { istrue: true,fprop: "goodsCode",label: "商品编码",width: "100",type: "html",formatter: (row) => formatNoLink(row.goodsCode),},
  { istrue: true, prop: "goodsName", label: "商品名称", width: "80" },
  { istrue: true,prop: "stockDays3D",label: "3维库存销量",width: "100",formatter:(row)=> !row.stockDays3D?" ": row.stockDays3D?.toFixed(2)},
  { istrue: true,prop: "stockDays4D",label: "4维库存销量",width: "100",formatter:(row)=> !row.stockDays4D?" ": row.stockDays4D?.toFixed(2)},
  { istrue: true,prop: "amount3D",label: "3维采购量",width: "100",formatter:(row)=> !row.amount3D?" ": row.amount3D?.toFixed(0)},
  { istrue: true,prop: "amount4D",label: "4维采购量",width: "100",formatter:(row)=> !row.amount4D?" ": row.amount4D?.toFixed(0)},
  { istrue: true,prop: "inventoryDays3D",label: "3维库存天数",width: "100",},
  { istrue: true,prop: "inventoryDays4D",label: "4维库存天数",width: "100",},
  { istrue: true,prop: "count",label: "采购数量",width: "80",},
  { istrue: true,prop: "useableCount",label: "实际可用数",width: "100",}, 
  { istrue: true,prop: "salesVolume7",label: "7天销量",width: "100",}, 
  { istrue: true,prop: "salesVolume15",label: "15天销量",width: "100",}, 
  { istrue: true,prop: "salesVolume30",label: "月销量",width: "100",}, 
  { istrue: true,prop: "inCount",label: "已入库数量",width: "100",},
  { istrue: true,prop: "lastInTransitTime",label: "在途时长",width: "80",formatter:(row)=>formatSecondToHour(row.lastInTransitTime)},
  { istrue: true,prop: "isOutStock",label: "缺货状态",width: "80",formatter:(row)=>formatIsError(row.isOutStock)},
  { istrue: true,prop: "price",label: "单价",width: "60",},
  // { istrue: true,prop: "totalAmont",label: "差异数",width: "80",},
  // { istrue: true,prop: "nonInCount",label: "未入库金额",width: "100",},
  // { istrue: true,prop: "inAmont",label: "已入库金额",width: "100",},
  // { istrue: true,prop: "warehouse",label: "第三方物流和分仓",width: "130",formatter:(row)=>formatWarehouseArea(row.warehouse)},
  // { istrue: true,prop: "goodsLable",label: "商品标签",width: "100",type: "custom",},
  // { istrue: true,prop: "isError",label: "是否异常",width: "*",formatter: (row) => formatIsError(row.isError),},
];
const tableHandles=[ ];
export default {
  name: "YunhanAdminPurorderindex2",
  components: {container,cesTable},
  data() {
    return {
      that:this,
      title: "订单",
      detaillist: [],
      listLoading: false,
      tableCols:tableCols,
      tableHandles:tableHandles,
      filter: { buyNo: "" },
      pager: { OrderBy: "", IsAsc: false },
      total: 0,
      sels: [],
      summaryarry:{},
      currentPage:1,
      pageSize:5,
      //图表配置 Start
      charts: {
        myChart: null,
        chartsType: null,
        Ylist: [{ value: 0, unit: "" }],
        loading: false,
        visible: false,
        title: null,
        filter: {
          GoodsCode: null,
          startDate: null,
          endDate: null,
          timeRange: [
            formatTime(dayjs().subtract(29, "day"), "YYYY-MM-DD"),
            formatTime(new Date(), "YYYY-MM-DD"),
          ],
        },
      },
      //图表配置 End
      selids: [],
      platformList: [],
      shopList: [],
      groupList: [],
      brandlist: [],
      sendWarehouseList: [],
      collapseActiveNames: [], //折叠面板激活页面
      orderDetailActiveName: "tabOrderDetailTable",
      formatWarehouseArea: formatWarehouseArea,
      formatYesornoBool: formatYesornoBool,
      formatTime: formatTime,
      formatIsOutStock: formatIsOutStock,
      formatSecondToHour: formatSecondToHour,
    };
  },

  async mounted() {

  },

  methods: {
    async onSearch(buyNo) {
        this.filter = { buyNo: buyNo },
        //this.$refs.pager.setPage(1)
        this.getdetaillist();
    },
    async getdetaillist() {
        if (!this.pager.OrderBy) this.pager.OrderBy = "";
       var pager = { currentPage: this.currentPage, pageSize: this.pageSize }
       console.log('分页',pager)
      const params = { ...pager, ...this.pager, ...this.filter };
      this.listLoading = true
      this.detaillist = [];
      const res = await queryPurchaseOrderDetail(params);
      this.listLoading = false
      if (!(res.code == 1 && res.data)) return;
      this.total = res.data.total;
      console.log('总条数',this.total)
      this.detaillist = res.data.list;
      for (let index = 0; index < this.detaillist.length; index++) {
                this.charts.filter.GoodsCode = undefined;
                this.charts.filter.GoodsCode = this.detaillist[0].goodsCode;
              }
      this.onSearchChartsOrderCount();
    },
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async cellclick(row, column, cell, event){
     if (column.property=='goodsCode'&&row.goodsCode)
        this.charts.filter.GoodsCode=null;
        this.charts.filter.GoodsCode = row.goodsCode;
        await this.onSearchChartsOrderCount();
    },
    //订单数查询。type:1总，2部门，3原因
    async onSearchChartsOrderCount() {
      this.charts.chartsType = "orderCount";
      await this.onSearchCharts();
    },
    async nclick(goodsCode) {
      this.charts.filter.GoodsCode = goodsCode;
      this.onSearchChartsOrderCount();
    },
    //图表查询
    async onSearchCharts() {
      if (
        this.charts.filter.timeRange &&
        this.charts.filter.timeRange.length > 1
      ) {
        this.charts.filter.startDate = this.charts.filter.timeRange[0];
        this.charts.filter.endDate = this.charts.filter.timeRange[1];
      }
      var backupDate = formatTime(this.filter.backupDate, "yyyy-MM-dd");
      var params = { ...this.filter, ...this.charts.filter, backupDate };
      this.charts.loading = true;
      var res = null;
      var title = "";
      var unit = "";
      var chartsId = "myChart";

      if (this.charts.chartsType == "orderCount") {
        title = "销量";
        unit = "个";
        res = null;
        res = await getOrderChartsOrderCount(params);
        this.charts.loading = false;
        if (!res?.code) {
          return false;
        }
        chartsId = "myChartOrderCount";
      }

      setTimeout(async () => {
        var chartDom = document.getElementById(chartsId);
        this.charts.myChart && this.charts.myChart.dispose();

        this.charts.myChart = echarts.init(chartDom);

        var option = await this.Getoptions(res.data, title, unit);
        (await option) && this.charts.myChart.setOption(option);
      }, 100);
    },
    //折线/柱形图图表配置
    async Getoptions(element, title, unit) {
      var colors = [
        "#5470C6",
        "#c77eb5",
        "#EE6666",
        "#409EFF",
        "#00ae9d",
        "#67C23A",
      ];
      var series = [];
      element.series.forEach((s) => {
        series.push({
          smooth: true,
          ...s,
          itemStyle: { normal: { label: { show: true } } },
        });
      });
      var legendData = element.legend || [];
      var yAxis = [];
      var left = true;
      var leftOffset = 0;
      var rightOffet = 0;
      var ii = 0;
      this.charts.Ylist.forEach((s) => {
        yAxis.push({
          type: "value",
          name: s.label,
          show: true,
          axisLabel: {
            formatter: "{value} " + unit,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[ii++],
            },
          },
          position: left ? "left" : "right",
          offset: left ? leftOffset : rightOffet,
        });
        left ? (leftOffset += 50) : (rightOffet += 50);
        left = !left;
      });
      var option = {
        title: { text: title },
        tooltip: {
          trigger: "axis",
          textStyle: { align: "left" },
        },
        legend: {
          formatter: function (name) {
            return echarts.format.truncateText(
              name,
              200,
              "10px Microsoft Yahei",
              "..."
            );
          },
          tooltip: {
            show: true,
          },
          data: legendData,
          type: "scroll",
          pageIconColor: "#409EFF",
          pageIconInactiveColor: "#909399",
          width: "75%",
        },
        grid: {
          left: "3%",
          right: "3%",
          bottom: "3%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            magicType: { show: true, type: ["line", "bar"] },
            //restore: { show: false },
          },
        },
        xAxis: {
          type: "category",
          data: element.xAxis,
          axisLabel: {
            interval: 0,
            rotate: 40,
          },
        },
        yAxis: yAxis,
        series: series,
      };
      return option;
    },
    //分页条件
    async sizeChange(val) {
        this.pageSize=val;
        await this.getdetaillist();
        console.log(`每页 ${val} 条`);
      },
     async currentChange(val) {
        this.currentPage=val;
        await this.getdetaillist();
        console.log(`当前页: ${val}`);
      }
  },
};
</script>

<style lang="scss" scoped>
#myChartOrderCount {
  width: 99%;
  height: 350px;
  text-align: center;
}
</style>