<template>
    <div>
        <div style="color: red;padding-left: 15px;margin-bottom: 10px;">每个标签使用逗号(,)隔开,否则设置无效!</div>
        <el-form label-width="80px" :model="ruleform">
            <el-form-item label="剔除标签:">
                <el-input type="textarea" :rows="5" show-word-limit maxlength="999" placeholder="剔除标签"
                    v-model="ruleform.ignoreLabels1">
                </el-input>
            </el-form-item>
            <el-form-item label="剔除品牌:">
                <el-input type="textarea" :rows="5" show-word-limit maxlength="999" placeholder="剔除品牌"
                    v-model="ruleform.ignoreBrands1">
                </el-input>
            </el-form-item>
            <el-form-item label="长度限制放行编码:">
                <el-input type="textarea" :rows="5" show-word-limit maxlength="999999" placeholder="放行编码"
                    v-model="ruleform.passGoodsCode1">
                </el-input>
            </el-form-item>
        </el-form>
        <div style="margin-top: 10px;display: flex;justify-content: center;">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="saveData" style="margin-left: 10px;" v-throttle="2000">保存</el-button>
        </div>
    </div>
</template>

<script>
import request from '@/utils/request'
export default {
    data() {
        return {
            api: '/api/verifyOrder/SaleItems/Weight/',
            ruleform: {
                ignoreLabels: [],
                ignoreBrands: [],
                passGoodsCode: [],
                ignoreLabels1: '',
                ignoreBrands1: '',
                passGoodsCode1: '',
            }
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        async getData() {
            const { data } = await request.post(`${this.api}GetSetting`)
            if (data) {
                data.ignoreLabels1 = data.ignoreLabels.join('\n')
                data.ignoreBrands1 = data.ignoreBrands.join('\n')
                data.passGoodsCode1 = data.passGoodsCode.join('\n')
                this.ruleform = data
            } else {
                this.ruleform = {
                    ignoreLabels: [],
                    ignoreBrands: [],
                    passGoodsCode: [],
                    ignoreLabels1: '',
                    ignoreBrands1: '',
                    passGoodsCode1: ''
                }
            }
        },
        async saveData() {
            this.ruleform.ignoreLabels = this.ruleform.ignoreLabels1.split(/[,，\n]/).filter(item => item.trim() !== '')
            this.ruleform.ignoreBrands = this.ruleform.ignoreBrands1.split(/[,，\n]/).filter(item => item.trim() !== '')
            this.ruleform.passGoodsCode = this.ruleform.passGoodsCode1.split(/[,，\n]/).filter(item => item.trim() !== '')
            const { success } = await request.post(`${this.api}SaveSetting`, this.ruleform)
            if (success) {
                this.$emit('close')
                this.$message.success('保存成功')
            }
        }
    }
}
</script>

<style scoped lang="scss"></style>