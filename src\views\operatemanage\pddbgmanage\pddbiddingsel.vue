<template>
    <my-container v-loading="pageLoading">
        <div class="content flexcenter">
            <div class="boxcontent">
                <div class="boxall">
                    <div class="flexrow" style="font-size: 13px;"><span class="greytext">竞价活动</span>&nbsp/&nbsp<span
                            style="font-weight: 600;">填写报名信息</span></div>
                    <div class="onebox">
                        <div style="width: 100%; height: 40px;" class="flexrow">
                            <div style="display: flex; justify-content: flex-start;">第一步：选择竞价商品</div>
                            <div style="display: flex; margin-left: auto; justify-content: flex-end;">
                                <!-- <el-button type="text">活动规则</el-button> -->
                            </div>
                            <div style="text-align: right; margin-right:150px">
                                <el-button type="text"  @click="openChart()">
                                    放大
                                </el-button>
                            </div>
                        </div>
                        <div style="height: 250px;">
                            <el-row>
                                <el-col :span="12">
                                    <div class="grid-content bg-purple-dark" style="padding: 5px;">
                                        <div style="border: 1px solid #eee; height: 240px;">
                                            <div style="height: 50px;background-color: #eee; padding: 0 10px;"
                                                class="alicenter titletext">参考商品信息</div>
                                            <el-row>
                                                <el-col :span="24">
                                                    <div class="grid-content bg-purple-dark">
                                                        <div class="marginzero flexrow">
                                                            <img :src="par.imageUrl" alt=""
                                                                style="width: 140px; height: 140px; margin-left: 10px;">
                                                            <div style="margin-left: 10px;width: 600px;" class="flexcloumn">
                                                                <span class="martop greytext">商品名称：{{ par.goodsName }}</span>
                                                                <!-- <span
                                                                    class="martop greytext">商品类别：{{ par.type1 + '/' + par.type2 + '/' + par.type3 }}</span> -->
                                                                <span class="martop greytext">商品ID：{{ par.goodsId }}</span>
                                                                <div class="martop"
                                                                    style="height: 80px; width: 100%; margin-top: 10px;">
                                                                    <el-row>
                                                                        <el-col :span="12">
                                                                            <div class="grid-content bg-purple-dark flexcenter"
                                                                                style="height: 80px;">
                                                                                <div class="flexcloumn flexcenter">
                                                                                    <span class="greytext">昨日参考销量</span>
                                                                                    <span>{{ par.yestodaySaleRange }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </el-col>
                                                                        <el-col :span="12">
                                                                            <div class="grid-content bg-purple-dark flexcenter"
                                                                                style="height: 80px;">
                                                                                <div class="flexcloumn flexcenter">
                                                                                    <span class="greytext">参考价</span>
                                                                                    <span>{{ par.priceRange }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </el-col>
                                                                    </el-row>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="grid-content bg-purple-dark" style="padding: 5px;">
                                        <div style="border: 1px solid #eee; height: 240px;">
                                            <buscharPddJJ style="margin-top: 10px;" ref="buschar" :analysisData="showDetailVisible.data" v-if="showDetailVisible.data">
                                            </buscharPddJJ>
                                        </div>
                                    </div>
                                </el-col>
                                <!-- <el-col :span="12">
                                    <div class="grid-content bg-purple-dark" style="padding: 5px;">
                                        <div style="border: 1px solid #eee; height: 240px;">
                                            <div style="height: 50px;background-color: #eee; padding: 0 10px;"
                                                class="alicenter titletext">竞价商品信息
                                                <div style="display: flex; margin-left: auto; justify-content: flex-end;">
                                                    <el-button type="text" @click="newsel">重新选择</el-button>
                                                </div>
                                            </div>
                                                
                                            <el-row v-loading="listloading">
                                                <el-col :span="24" v-show="!selendshow">
                                                    <div class="grid-content bg-purple-dark"
                                                        style="display: flex; justify-content: center; align-items: center; height: 190px;">
                                                        <el-button size="mini" type="primary"
                                                            @click="openShopList()">选择竞价商品</el-button>
                                                    </div>
                                                </el-col>
                                                <el-col :span="24" v-show="selendshow">
                                                    <div class="grid-content bg-purple-dark">
                                                        <div class="marginzero flexrow">
                                                            <img :src="bidding.picture" alt=""
                                                                style="width: 140px; height: 140px; margin-left: 10px;">
                                                            <div style="margin-left: 10px;width: 600px;" class="flexcloumn">
                                                                <span class="martop greytext">商品名称：{{ bidding.goodsName }}</span>
                                                                <span
                                                                    class="martop greytext">商品类别：{{ bidding.styleCode }}</span>
                                                                <span class="martop greytext">商品ID：{{ bidding.goodsCode }}</span>
                                                                <div class="martop"
                                                                    style="height: 80px; width: 100%; margin-top: 10px;">
                                                                    <el-row>
                                                                        <el-col :span="8">
                                                                            <div class="grid-content bg-purple-dark flexcenter"
                                                                                style="height: 80px;">
                                                                                <div class="flexcloumn flexcenter">
                                                                                    <span class="greytext">拼单价</span>
                                                                                    <span>{{ bidding.costPrice }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </el-col>
                                                                        <el-col :span="8">
                                                                            <div class="grid-content bg-purple-dark flexcenter"
                                                                                style="height: 80px;">
                                                                                <div class="flexcloumn flexcenter">
                                                                                    <span class="greytext">线上库存</span>
                                                                                    <span>{{ bidding.stockCount }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </el-col>
                                                                        <el-col :span="8">
                                                                            <div class="grid-content bg-purple-dark flexcenter"
                                                                                style="height: 80px;">
                                                                                <div class="flexcloumn flexcenter">
                                                                                    <span class="greytext">商品评分(不含默认)</span>
                                                                                    <span>{{ bidding.priceRange }}</span>
                                                                                </div>
                                                                            </div>
                                                                        </el-col>
                                                                    </el-row>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </div>
                                </el-col> -->
                            </el-row>
                        </div>
                    </div>
                    <div class="twobox">
                        <div style="width: 100%; height: 40px;" class="flexrow">
                            <div style="display: flex; justify-content: flex-start;">第二步、匹配同款规则</div>
                        </div>
                        <div>
                            <el-collapse v-for="(par,index) in query" v-model="activeNames" >
                                <el-collapse-item :name="index">
                                    <template slot="title">
                                        <img :src="par.imageUrl" alt=""
                                            style="width: 30px; height: 30px; margin-left: 10px;">
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;商品名称：{{ par.goodsName }}
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;商品ID：{{ par.goodsId }}
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;昨日参考销量:{{ par.yestodaySaleRange }}
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参考价:{{ par.priceRange }}
                                            <span style="color: #409EFF;float: right;" @click="addGoods(par)">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;添加选品</span>
                                    </template>
                                    <vxe-table resizable :max-height="hotList[index].length==0?100:450" :border="border" :align="allAlign" :merge-cells="mergeCells"
                                        :data="hotList[index]">
                                        <vxe-column width="40">
                                            <template>
                                                <div style="height: 100%; width: 100%;">必报热销规格</div>
                                            </template>
                                        </vxe-column>
                                       
                                        <vxe-column title="参考商品规格" width="400">
                                            <template #default="{ row }">
                                                <div style="height: 100%; width: 100%;" class="flexrow">
                                                    <img :src="row.skuImgUrl"
                                                        alt="" style="width: 50px; height: 50px;">
                                                    <span style="padding-left: 10px;">{{ row.skuName }}</span>
                                                </div>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="salePrice" title="售价" width="200">
                                            <template slot-scope="scope">
                                                {{(scope.row.salePrice?1.0*JSON.parse(scope.row.salePrice)/100:0)}}
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="kuaiDi" title="快递" width="200">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.kuaiDi"
                                                    maxlength="50">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="baoZhuang" title="包装">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.baoZhuang"
                                                    maxlength="50">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="chengBen" title="成本">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.chengBen"
                                                    maxlength="50">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="goodsId" title="商品Id">
                                            <template slot-scope="scope">
                                                    <span v-if="scope.row.goodsId">
                                                        {{scope.row.goodsId}}
                                                    </span>
                                                    <template>
                                                        <el-button type="text"  @click="selectGoods(scope.rowIndex,index,1)">
                                                            选择商品对应sku
                                                        </el-button>
                                                        <el-button v-if="scope.row.goodsId" type="text"  @click="qkGoods(scope.row)">
                                                            清空
                                                        </el-button>
                                                    </template>
                                                </template>
                                        </vxe-column>
                                        <vxe-column field="jjStatus" title="状态">
                                            <template slot-scope="scope">
                                                    <span v-if="scope.row.jjStatus">
                                                        {{scope.row.jjStatus}}
                                                    </span>
                                                </template>
                                        </vxe-column>
                                        <vxe-column width="250" title="操作">
                                            <template slot-scope="scope">
                                                <div style="height: 100%; width: 100%;">
                                                <el-button type="text"  @click="jjGoods(index)">
                                                    竞价
                                                </el-button>
                                                <!-- <el-button type="text"  @click="insertGoods(scope.row,par)">
                                                    添加至已选品
                                                </el-button> -->
                                            </div>
                                            </template>
                                        </vxe-column>
                                    </vxe-table>

                                    <vxe-table resizable :max-height="parList[index].length==0?100:450" :border="border" :align="allAlign" :merge-cells="mergeCells"
                                        :data="parList[index]">
                                        <vxe-column width="40">
                                            <template>
                                                <div style="height: 100%; width: 100%;">同款商品</div>
                                            </template>
                                        </vxe-column>
                                        <vxe-column title="参考商品规格" width="400">
                                            <template #default="{ row }">
                                                <div style="height: 100%; width: 100%;" class="flexrow">
                                                    <img :src="row.skuImgUrl"
                                                        alt="" style="width: 50px; height: 50px;">
                                                    <span style="300px">{{ row.skuName }}</span>
                                                </div>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="salePrice" title="售价" width="200"></vxe-column>
                                        <vxe-column field="kuaiDi" title="快递" width="200">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.kuaiDi"
                                                    maxlength="50">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="baoZhuang" title="包装">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.baoZhuang"
                                                    maxlength="50">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="chengBen" title="成本">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.chengBen"
                                                    maxlength="50">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="goodsId" title="商品Id">
                                            <template slot-scope="scope">
                                                    <span v-if="scope.row.goodsId">
                                                        {{scope.row.goodsId}}
                                                    </span>
                                                    <template>
                                                        <el-button type="text"  @click="selectGoods(scope.rowIndex,index,2)">
                                                            选择商品对应sku
                                                        </el-button>
                                                        <!-- <el-button v-if="!scope.row.goodsId" type="text"  @click="insertGoods(scope.row)">
                                                            添加至已选品
                                                        </el-button> -->
                                                        <el-button v-if="scope.row.goodsId" type="text"  @click="qkGoods(scope.row)">
                                                            清空
                                                        </el-button>
                                                    </template>
                                                </template>
                                        </vxe-column>
                                        <!-- <vxe-column field="jjStatus" title="状态">
                                            <template slot-scope="scope">
                                                    <span v-if="scope.row.jjStatus">
                                                        {{scope.row.jjStatus}}
                                                    </span>
                                                </template>
                                        </vxe-column>
                                        <vxe-column field="" title="操作">
                                            <template slot-scope="scope">
                                                <el-button type="text"  @click="jjGoods(scope.row)">
                                                    竞价
                                                </el-button>
                                            </template>
                                        </vxe-column> -->
                                    </vxe-table>
                                </el-collapse-item>
                            </el-collapse>
                            <!-- <el-collapse v-model="activeNames" >
                                <el-collapse-item :name="1">
                                    <template slot="title">
                                        <img :src="par.imageUrl" alt=""
                                            style="width: 30px; height: 30px; margin-left: 10px;">
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;商品名称：{{ par.goodsName }}
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;商品ID：{{ par.goodsId }}
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;昨日参考销量:{{ par.yestodaySaleRange }}
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参考价:{{ par.priceRange }}
                                    </template>
                                    <vxe-table resizable height="450" :border="border" :align="allAlign" :merge-cells="mergeCells"
                                        :data="skuList">
                                        <vxe-column width="40">
                                            <template>
                                                <div style="height: 100%; width: 100%;">必报热销规格</div>
                                            </template>
                                        </vxe-column>
                                        <vxe-column title="参考商品规格" width="400">
                                            <template #default="{ row }">
                                                <div style="height: 100%; width: 100%;" class="flexrow">
                                                    <img :src="row.skuImgUrl"
                                                        alt="" style="width: 50px; height: 50px;">
                                                    <span style="300px">{{ row.skuName }}</span>
                                                </div>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="salePrice" title="售价" width="200"></vxe-column>
                                        <vxe-column field="kuaiDi" title="快递" width="200">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.kuaiDi">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="baoZhuang" title="包装">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.baoZhuang">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="chengBen" title="成本">
                                            <template slot-scope="scope">
                                                <el-input
                                                    size="mini"
                                                    placeholder="请输入"
                                                    v-model="scope.row.chengBen">
                                                </el-input>
                                            </template>
                                        </vxe-column>
                                        <vxe-column field="goodsId" title="商品Id">
                                            <template slot-scope="scope">
                                                    <span v-if="scope.row.goodsId">
                                                        {{scope.row.goodsId}}
                                                    </span>
                                                    <template>
                                                        <el-button type="text"  @click="selectGoods(scope.rowIndex)">
                                                            选择商品对应sku
                                                        </el-button>
                                                        <el-button v-if="!scope.row.goodsId" type="text"  @click="insertGoods(scope.row)">
                                                            添加至已选品
                                                        </el-button>
                                                    </template>
                                                </template>
                                        </vxe-column>
                                        <vxe-column field="jjStatus" title="状态">
                                            <template slot-scope="scope">
                                                    <span v-if="scope.row.jjStatus">
                                                        {{scope.row.jjStatus}}
                                                    </span>
                                                </template>
                                        </vxe-column>
                                        <vxe-column field="" title="操作">
                                            <template slot-scope="scope">
                                                <el-button type="text"  @click="jjGoods(scope.row)">
                                                    竞价
                                                </el-button>
                                            </template>
                                        </vxe-column>
                                    </vxe-table>
                                </el-collapse-item>
                            </el-collapse> -->
                           
                            <!-- :merge-cells="mergeCells"
                            :span-method="colspanMethod" -->
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog title="选择商品" :visible.sync="dialogVisibleData" style="height: 1000px;" v-dialogDrag v-loading="listloading">
            <div style="height: 550px;">
                <div style="height: 30px;">
                    <span>商品ID： </span>
                    <el-select filterable v-model="filter.proCode" placeholder="请选择" clearable style="width: 70%" 
                    :filter-method="getShopGoodsList" @change="changeproduct" >
                        <el-option v-for="item in shopGoodsList" :key="item.proCode" :label="'['+ item.proCode+']' +item.title" :value="item.proCode"/>
                    </el-select>
                    <!-- <el-input v-model.trim="filter.goodsName" clearable placeholder="商品名称" style="width:160px;" /> -->
                    <!-- <el-button type="primary" style="margin-left: 10px;" @click="getShopList">查询</el-button> -->
                </div>
                <div style="height: 480px;">
                    <el-table :data="skuShopGoodsList" height="300" @row-click="rowclick">
                        <el-table-column prop="goodsImage" label="商品列表" width="900">
                            <template #default="{ row }">
                                <div style="height: 100%; width: 100%;" class="flexrow">
                                    <img :src="row.goodsImage"
                                        alt="" style="width: 50px; height: 50px;">
                                    <span style="padding-left: 10px;">{{ row.goodsName }}</span>
                                </div>
                                
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <!-- <div style="height: 70px;">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="filter.currentPage" :page-sizes="[50, 100, 150, 200]" :page-size="filter.pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="total">
                    </el-pagination>
                </div> -->
            </div>
        </el-dialog>

        <el-dialog title="选择规格" :visible.sync="specificationVisibleData" style="height: 1000px;" v-dialogDrag v-loading="listloading">
            <div style="height: 550px;">
                <!-- <div style="height: 30px;">
                    <el-input v-model.trim="filter.goodsName" clearable placeholder="商品名称" style="width:160px;" />
                    <el-button type="primary" style="margin-left: 10px;" @click="getShopList">查询</el-button>
                </div> -->
                <div style="height: 480px;">
                    <el-table :data="specificationList" height="300" @row-click="specificationrowclick">
                        <el-table-column prop="goodsCode" label="商品编码" width="250">
                        </el-table-column>
                        <el-table-column prop="goodsName" label="商品名称" width="500">
                        </el-table-column>
                    </el-table>
                </div>
                <div style="height: 70px;">
                    <el-pagination @size-change="specificationChange" @current-change="specificationCurrentChange"
                        :current-page="filter.currentPage" :page-sizes="[50, 100, 150, 200]" :page-size="filter.pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="specificationtotal">
                    </el-pagination>
                </div>
            </div>
        </el-dialog>

        <el-dialog title="竞品昨日销量图表" :visible.sync="dialogopenChart" width ="80%" v-dialogDrag>
                <buscharhmax ref="buschar2" :analysisData="showDetailVisible.data" v-if="showDetailVisible.data">
                </buscharhmax>
        </el-dialog>
    </my-container>
</template>

<script>
// import VxeTable from 'vxe-table'
import MyContainer from '@/components/my-container';
import buscharPddJJ from "@/components/Bus/buscharPddJJ";
import buscharhmax from "@/components/Bus/buscharhmax";
import {
    getPageCompetitorPDDEntityAsync, importCompetitorPDDAsync, getCompetitorPDDType1Async,
    getCompetitorPDDChartAsync, pageShopGoodsAsync, getCompetitorPDDSkuAsync, updateCompetitorPDDSkuAsync
    , getCompetitorPDDSkuMonyAsync, getCompetitorPDDMonyChartAsync,
    getGoodStatusAsync,getCompetitorPDDMonyAsync,updateCompetitorPDDJJStatusAsync
} from '@/api/operatemanage/newmedia/pddcontributeinfo'
import { pageShopGoods } from '@/api/operatemanage/base/basicgoods'
import { getProductsByFilter } from '@/api/operatemanage/base/product' 
import {
    isDoHotSaleGoodsAsync,getHotSaleGoodsByFilter
} from '@/api/operatemanage/productalllink/alllink'
export default {
    name: 'MainPddbiddingsel',
    components: { MyContainer,buscharPddJJ,buscharhmax },
    data () {
        return {
            dialogopenChart:false,
            flag:1,
            index:0,
            rowIndex: 0,
            hotList: [],
            parList: [],
            skuMonyShopGoodsList:[],
            skuShopGoodsList:[],
            shopGoodsList: [],
            activeNames: [0],
            query:[],
            total: 0,
            showDetailVisible: { visible: false, title: "", data: {} },
            myskuList:[],
            skuList:[],
            specificationList: [],
            filter: {
                currentPage: 1,
                pageSize: 50,
                goodsName: '',
            },
            shopList: [],
            dialogVisibleData: false,
            parhot:[],
            par: {},
            pageLoading: false,
            selendshow: false,
            specificationVisibleData: false,
            allAlign: null,
            specificationtotal: null,
            skuindex: null,
            newgoodsshow: true,
            listloading: false,
            bidding: {},
            tableData: [
                { id: 10001, name: 'Test1', nickname: 'T1', role: 'Develop', sex: '', age: '', age1: '', age2: '', address: 'test abc' },
                { id: 10002, name: 'Test2', nickname: 'T2', role: 'Test', sex: '', age: '', age1: '', age2: '', address: 'Guangzhou' },
                { id: 10003, name: 'Test3', nickname: 'T3', role: 'PM', sex: '', age: '', age1: '', age2: '', address: 'Shanghai' },
            ],
            border: 'inner',
            mergeCells: [
                { row: 0, col: 0, rowspan: 50, colspan: 1 },
                { row: 0, col: 8, rowspan: 50, colspan: 1 },
                // { row: 0, col: 2, rowspan: 1, colspan: 5 },
                // { row: 1, col: 2, rowspan: 1, colspan: 5 },
                // { row: 2, col: 2, rowspan: 1, colspan: 5 },

            ]
        };
    },
    created () {

    },
    async mounted () {
        console.log(this.$route, 'qw2e12q')
        let p = {goodsIds: this.$route.query.goodsIds} ;
        const res = await getCompetitorPDDMonyAsync(p);
        this.query = res?.data;
        await this.getMonySkuShopGoodsList();
        this.getpar();
        //console.log(this.par,'par')
        //await this.getTKGoodList();
        await this.getChart();
    },

    methods: {
        addGoods(val){
            console.log(val,'val');
            val.isOut = 'pdd'
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/AddChooseForm.vue`,
                title: '新增选品',
                args: { oid: '', mode: 0, ...val },
                height: 700,
                callOk: self.getMonySkuShopGoodsList
            });
        },
        async openChart () { 
            this.dialogopenChart = true;
            await this.$refs.buschar.initcharts()
        },
        qkGoods (row) { 
            row.goodsId = '';
            row.goodsName = '';
            row.chengBen = '';
            row.baoZhuang = '';
            row.kuaiDi = '';
            row.jjStatus = '';
        },
        async jjGoods (index) {
            let b = false;
            this.hotList[index].forEach(a => { 
                if (!a.goodsId)
                    b = true;
                return;
            })
            if (b) { 
                this.$message({ message: '存在未选择商品Id', type: "warning" });
                return;
            }
            
            var that = this;
            let t = true;
            let par = {};
            par = this.hotList[index][0];
            const res1 = await updateCompetitorPDDJJStatusAsync(par);
            if (res1?.data == true) {
                for (let i = 0; i < this.hotList[index].length; i++) {
                    let par = {};
                    par = this.hotList[index][i];

                    const res = await updateCompetitorPDDSkuAsync(par);
                    if (!res.data) {
                        t = false;
                        break;
                    }
                }
                that.$message({ message: '竞价提交成功！', type: "success" });
            }
            await this.getMonySkuShopGoodsList();
         },
        async insertGoods (row,par) { 
            var that = this;
            let params = {
                chooseGoodsType: "1",
                chooseRemark: "",
                goodsCompeteId: par.goodsId,
                goodsCompeteImgUrl: par.imageUrl,
                goodsCompeteName: par.goodsName,
                isDo: true,
                newPlatform: 2,
                oldType: 3,
                platform: 2,
                type: 3,
            };
            let reqRlt = await isDoHotSaleGoodsAsync(params);
            if (reqRlt && reqRlt.success) {
                that.$message({ message: '添加到选品成功！', type: "success" });
            }
        },
        async selectGoods (rowIndex,index,i) { 
            this.rowIndex = rowIndex;
            this.index = index;
            this.flag = i;
            this.dialogVisibleData = true;
        },

        async changeproduct () { 
            await this.getSkuShopGoodsList();
        },
        async getShopGoodsList (v) { 
            if (v == null || v == "") {
                return;
            }
            this.shopGoodsList = [];
            var that = this;
            this.listloading = true;
            let par = {};
            par.proCode = v
            const res = await getProductsByFilter(par).then(res => {
                that.shopGoodsList = res?.data;
            });
            this.listloading = false; 
        },

        async getSkuShopGoodsList () { 
            var that = this;
            const params = { ...this.filter };
            params.shopStyleCode = this.filter.proCode;
            const res = await pageShopGoods(params).then(res => {
                that.skuShopGoodsList = res?.data?.list;
            });
        },

        async getMonySkuShopGoodsList () { 
            let p = [];
            this.query.forEach(a => { 
                let par = { goodsId: '' };
                par.goodsId = a.goodsId;
                p.push(par);
            })
            var that = this;
            const res = await getCompetitorPDDSkuMonyAsync(p).then(res => {
                that.skuMonyShopGoodsList = res?.data;
            });

            this.query.forEach(a => { 
                let hot =this.skuMonyShopGoodsList.filter((v)=>{
                    return v.goodsCompeteId == a.goodsId &&v.isHot==1;
                })
                this.hotList.push(hot);
                let par =this.skuMonyShopGoodsList.filter((v)=>{
                    return v.goodsCompeteId == a.goodsId &&v.isHot!=1;
                })
                this.parList.push(par);
            });
            console.log(this.hotList, 'hot')
            console.log(this.parList,'par')
        },
        getpar () {
            let pricearr = [];
            if (this.query) { 
                // for (let i = 0; i < this.query.length; i++) { 
                //     if(this.query.priceRange)
                //         pricearr.push(this.query[i].priceRange.split('-')[0]);
                // }
                // let index = pricearr.indexOf(Math.min(...pricearr))>0?pricearr.indexOf(Math.min(...pricearr)):0;
                this.par = this.query[0];
            }
        },
        async getChart () {
            let p = [];
            this.query.forEach(a => { 
                let par = { goodsId: '' };
                par.goodsId = a.goodsId;
                p.push(par);
            })
            var that = this;
            const res1 = await getCompetitorPDDMonyChartAsync(p).then((res) => {
                that.showDetailVisible.visible = true;
                that.showDetailVisible.data = res.data;
                that.showDetailVisible.title = res.data.legend[0];
            });
            await this.$refs.buschar.initcharts()
        },
        // 初始页currentPage、初始每页数据数pagesize和数据data
        async handleSizeChange (size) {
            this.filter.pageSize = size;
            await this.getShopList();
        },
        async handleCurrentChange (currentPage) {
            this.filter.currentPage = currentPage;
            await this.getShopList();
        },
        async specificationChange (size) {
            this.filter.pageSize = size;
            await this.getTKGoodList();
        },
        async specificationCurrentChange (currentPage) {
            this.filter.currentPage = currentPage;
            await this.getTKGoodList();
        },
        async getShopList () { 
            var that = this;
            this.listloading = true;
            const params = {  ...this.filter };
            const res = await pageShopGoodsAsync(params).then(res => {
                that.total = res?.data?.total;
                that.shopList = res?.data?.list;
            });
            this.listloading = false;

        },

        async getTKGoodList () { 
            var that = this;
            this.listloading = true;
            const params = { goodsId: this.par.goodsId, ...this.filter };
            console.log(params,'params')
            const res = await getCompetitorPDDSkuAsync(params).then(res => {
                let a = res?.data.map((item)=>{
                    item.istrue = false;
                    return item;
                })
                that.skuList = a;
            });
            this.listloading = false;
        },
        async openShopList () {
            this.dialogVisibleData = true;
            this.listloading = true;
            await this.getShopList();
            this.listloading = false;

        },
        async rowclick(row, column, event){
            //this.selendshow = true;
            this.dialogVisibleData = false;
            let p = {goodsId :row.shopStyleCode}
            let res = await getGoodStatusAsync(p);
            if (this.flag == 1) { 
                this.hotList[this.index][this.rowIndex].chengBen = row.costPrice;
                this.hotList[this.index][this.rowIndex].goodsId = row.shopStyleCode;
                this.hotList[this.index][this.rowIndex].goodsName = row.shopStyleName;
                this.hotList[this.index][this.rowIndex].jjStatus = res?.data?.status;
            }
            if(this.flag == 2) { 
                this.parList[this.index][this.rowIndex].chengBen = row.costPrice;
                this.parList[this.index][this.rowIndex].goodsId = row.shopStyleCode;
                this.parList[this.index][this.rowIndex].goodsName = row.shopStyleName;
                this.parList[this.index][this.rowIndex].goodsName = res?.data?.status;
            }
            console.log("row数据",row)
        },
        async newsel(){
            this.dialogVisibleData = true;
            this.listloading = true;
            await this.getShopList();
            this.listloading = false;

        },
        async specificationrowclick(row, column, event){
            
            let that = this;
            console.log("第几个",that.skuindex)
            this.newgoodsshow = false;
            that.skuList.forEach((item,index)=>{
                if(index===that.skuindex){
                    item.newgoodsName = row.goodsName;
                    item.newcostPrice = row.costPrice;
                    item.newstockCount = row.stockCount;
                    item.picture = row.picture;
                    item.istrue = true;
                }
            })
            this.newgoodsshow = true;
            that.specificationVisibleData = false;
            that.$forceUpdate();
            console.log("点击行事件",that.skuList)
            
        },
        async selspecification (row) {
            let that = this;
            if(!this.bidding.styleCode){
                this.$message("款式编码不存在，请重新选择！")
                return
            }
            that.specificationVisibleData = true;
            that.skuindex = that.skuList.indexOf(row);
            const params = {styleCode: this.bidding.styleCode, ...this.filter};
            this.listloading = true;
            const res = await pageShopGoodsAsync(params).then(res => {
                // return
                that.specificationtotal = res?.data?.total;
                that.specificationList = res?.data?.list;
            });
            this.listloading = false;
            this.$forceUpdate();
        },
        colspanMethod ({ _rowIndex, _columnIndex }) {
            if (_columnIndex === 0 && _rowIndex == 0) {
                return { rowspan: 3, colspan: 1 }
            }
            if (_columnIndex === 2) {
                return { rowspan: 1, colspan: 5 }
            } else if (_columnIndex === 1) {
                return { rowspan: 1, colspan: 1 }
            } else {
                return { rowspan: 0, colspan: 0 }
            }
        }
    },
};
</script>

<style lang="scss" scoped>
.content {
    // height: 100%;
    width: 100%;
    // padding: 5px;
}

.flexcenter {
    display: flex;
    justify-content: center;
    align-items: center;
}

.alicenter {
    display: flex;
    align-items: center;
}

.boxcontent {
    // margin: 10px;
    height: auto;
    width: 98%;
    box-shadow: 2px 2px 2px 2px rgb(182, 180, 180);

    .boxall {
        margin: 5px;
        min-height: 93vh;
        background-color: #eee;
        padding: 10px;

        .onebox {
            height: 300px;
            margin-top: 20px;
            padding: 0 10px;
            background: white;
        }

        .twobox {
            min-height: 510px;
            margin-top: 20px;
            padding: 0 10px;
            background: white;
        }
    }
}

.flexrow {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.greytext {
    color: rgb(71, 70, 70);
    font-size: 13px;
}

.marginzero {
    height: 190px;
    border: 1px solid #eee;
    // background: #eee;
}

.flexcloumn {
    display: flex;
    flex-direction: column;
}

.titletext {
    font-size: 15px;
    font-weight: 600;
}

.martop {
    margin-top: 3px;
}</style>