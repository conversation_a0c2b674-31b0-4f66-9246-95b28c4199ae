<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form ref="jslrForm" :inline="true" label-position="left" :model="form" :rules="jslrFormRulesList" label-width="120px" :disabled="!formEditMode">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="竞品平台：">
                            <el-select disabled v-model="form.platform" style="width:100px;">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="竞品ID：">
                            <div v-html="formatLinkProCode(form.platform, form.goodsCompeteId)"></div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item v-if="form.goodsCompeteName == ''|| form.goodsCompeteName == null" label="竞品标题：" prop="newGoodsCompeteName" :rules="[
                        { required: true, message: '请填写竞品标题', trigger: 'blur' },
                        { min: 4, max: 100, message: '长度在 4 到 100 个字符', trigger: 'blur' }]" >
                            <el-input v-model.trim="form.newGoodsCompeteName"  :maxlength="100" :minlength="4" style="width:800px">
                            </el-input>
                        </el-form-item>
                        <el-form-item v-else label="竞品标题：">
                            <!-- <div style="width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: normal; -webkit-line-clamp: 1;">{{ form.goodsCompeteName}}</div> -->

                            <el-tooltip class="item" effect="dark" :content="form.goodsCompeteName" placement="top">
                                <div class="overtext">
                                    {{ form.goodsCompeteName }}
                                </div>
                            </el-tooltip>

                        </el-form-item>
                    </el-col>
                </el-row>
                <div style=" display: flex; width: 100%; z-index: 99;">
                    <div style="font-weight: Bold; font-size: 14px; margin-right: auto; margin-bottom: 10px;color: #333333;">竞品利润信息</div>
                    <div style="margin-left: auto; color: #BDBDBD;" @click="closeopen">{{ tansitionshow?'关闭':'展开' }}<i :class="tansitionshow?'el-icon-arrow-down':'el-icon-arrow-up'"></i></div>
                    <!-- el-icon-arrow-up -->
                </div>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="毛二利润率：" class="commonstyle">
                            {{calcGrossProfitRatio }}%
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="毛二利润：" class="commonstyle">
                            {{calcGrossProfit}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="毛三利润率：" class="commonstyle">
                            {{calcGrossProfit3Ratio}}%
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="毛三利润：" class="commonstyle">
                            {{form.grossProfit3}}
                        </el-form-item>
                    </el-col>

                </el-row>
                <transition name="el-zoom-in-top">
                    <div v-show="tansitionshow" class="transition-box">
                    <!-- //广告费 -->
                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="广告费：">
                                <el-select v-model="form.advertisingFeeType" style="width:80px;">
                                    <el-option v-for="item in advertisingFeeTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <el-input-number type="number" :precision="2" :min="0" :max="99999999" :controls="false" v-model.number="form.advertisingFee" style="width:110px;"></el-input-number>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="其他费用(预估)：">
                                <el-select v-model="form.otherFeeType" style="width:80px;">
                                        <el-option v-for="item in otherFeeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <el-input-number type="number" :precision="2" :min="0" :max="10000000" :controls="false" v-model.number="form.otherFee" style="width:110px;"></el-input-number>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="快递单价：">
                                <el-input-number type="number" :precision="2" :min="0" :max="1000" :controls="false" v-model.number="form.expressPrice" style="width:190px;"></el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="快递费(预估)：">
                                {{calcExpressAmount}}
                                <el-tooltip class="item" effect="dark" content="快递单价*支付人数；无支付人数时，快递单价*月销量" placement="top-end">
                                    <span><i class="el-icon-question"></i></span>
                                </el-tooltip>
                            </el-form-item>
                        </el-col>


                    </el-row>

                    <!-- //支付人数 -->
                    <el-row>


                        <el-col :span="6">
                        <el-form-item label="SKU总成本：">
                                {{calcSkuTotalCost}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="包装总成本：">
                                {{calcPackTotalCost}}
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="月销量：">
                                {{calcLastMonthSaleCount}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="月销售额：">
                                {{calcLastMonthSaleAmount}}
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="6">
                            <el-form-item label="支付人数：">
                                <el-input-number type="number" :precision="0" :min="0" :max="99999999" :controls="false" v-model.number="form.lastMonthPayerCount" style="width:190px;">
                                </el-input-number>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                        <el-form-item label="净利比例(预估)：">
                                <el-select v-model="form.grossProfit4RateType" style="width:80px;">
                                        <el-option v-for="item in grossProfit4RateTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                                <el-input-number type="number" :precision="2" :min="0" :max="10000000" :controls="false" v-model.number="form.grossProfit4Rate" style="width:110px;">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="净利利润率：">
                                {{calcGrossProfit4Ratio}}%
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="净利利润：">
                                {{form.grossProfit4}}
                            </el-form-item>
                        </el-col>


                    </el-row>
                    </div>
                </transition>


                <!-- //产品简称 -->
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="goodsCompeteShortName" label="产品简称：">
                            <el-input v-model="form.goodsCompeteShortName" :maxlength="40" style="width:190px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="goodsCompeteType" label="产品类型：">
                            <el-select v-model="form.goodsCompeteType" style="width:190px;">
                                <el-option :key=1 :value=1 label="自发" ></el-option>
                                <el-option :key=2 :value=2 label="代发" ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>


                </el-row>

                 <!-- //供应商平台 -->
                <el-row  v-if="form.isGroupMember ">
                    <el-col :span="6">
                        <el-form-item prop="supplierPlatForm" label="供应商平台：">
                            <el-select v-model="form.supplierPlatForm" style="width:190px;">
                                <el-option v-for="item in supplierPlatFormList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="supplierName" label="供应商名称：">
                            <el-input v-model="form.supplierName" :maxlength="40" style="width:190px"></el-input>
                        </el-form-item>
                    </el-col>

                     <el-col :span="6" :hidden="(form.supplierPlatForm!=2)">
                        <el-form-item prop="supplierLink" label="供应商链接：">
                            <el-input v-model="form.supplierLink" :maxlength="2000" style="width:190px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :hidden="(form.supplierPlatForm!=2)">
                        <el-form-item prop="supplierGoodLink" label="产品链接：">
                            <el-input v-model="form.supplierGoodLink" :maxlength="2000" style="width:190px"></el-input>
                        </el-form-item>
                    </el-col>


                </el-row>

                <!-- 附件 -->
                <el-row>
                    <el-col>
                        <el-form-item prop="uploadInfo" label="附件：">
                        <div style="max-height: 130px; width: 500px;">
                            <uploadimgFile ref="uploadimgFile" :disabled="!formEditMode" :noDel="!formEditMode" :uploadInfo="form.calProfitFiles" :keys="[1,1]" :filemaxsize="1" :imgmaxsize="5"></uploadimgFile>
                        </div>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>

                    <el-col :span="4" :hidden="(form.supplierPlatForm!=1)">
                        <el-form-item prop="supplierWxNum" label="微信账号：">
                            <el-input v-model="form.supplierWxNum" :maxlength="40" style="width:140px"></el-input>
                        </el-form-item>
                    </el-col>


                </el-row>
                <!-- //需求注释 -->
                <!-- <el-row>



                </el-row>
                <el-row>

                </el-row> -->


                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="SKU利润">
                                <el-row>
                                    <el-col :span="24" style="padding-top:10px;">
                                        <el-button type="primary" @click="onOpenAddSkuPage">手动添加SKU</el-button>
                                        <el-button type="primary" @click="onDisableAllSku(1)">一键禁用</el-button>
                                        <el-button type="primary" @click="onDisableAllSku(0)">一键启用</el-button>

                                        <el-button type="primary" v-if="!enquiryInfo.hasEnquiry && skuTableData && skuTableData.length>0"  @click="launchEnquiry()">发起询价</el-button>
                                        <el-button  v-if="enquiryInfo.hasEnquiry"  @click="enquiryList()">
                                            报价列表({{enquiryInfo.fbCount}})
                                            <span style="color:red" v-if="enquiryInfo.hasFb">(新)</span>
                                        </el-button>

                                    </el-col>
                                </el-row>
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="skutable" :that='that' :isIndex='false'
                                    :hasexpandRight='true' :hasexpand='true'
                                    :tableData='skuTableData' :tableCols='skuTableCols'
                                    :loading="listLoading" :isSelectColumn="false" :customRowStyle="customRowStyle2">
                                        <template>
                                            <!-- left  -->
                                            <!-- <el-table-column width="60" label="Sku图">
                                                <template slot-scope="scope">
                                                    <el-image style="width: 40px; height: 40px" :src="scope.row.skuImgUrl" :preview-src-list="[scope.row.skuImgUrl]">

                                                    </el-image>
                                                </template>
                                            </el-table-column> -->
                                        </template>
                                        <!-- center from jsonCols array  -->
                                        <template slot="right">
                                            <!-- right  -->
                                            <el-table-column width="120" label="售价" prop="salePrice" :sortable="true">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>

                                                  <template v-else>
                                                     <el-input-number   v-model.number="scope.row.salePrice"
                                                        style="width:70px" type="number"
                                                        :min="0" :max="100000" :precision="2" :controls="false"  size="mini"
                                                        @change="rowChanged(scope.row,'salePrice')"
                                                    />
                                                    <el-button type="text"  @click="batchSetRowColVal(scope.row,'salePrice')">
                                                            批量
                                                        </el-button>

                                                  </template>

                                                </template>
                                            </el-table-column>
                                            <el-table-column width="80" label="月销售额" prop="monthSaleMoney" :sortable="true">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.monthSaleMoney}}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="130" label="月销量" prop="monthSales" :sortable="true">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>

                                                    <template v-else>
                                                         <el-input-number  v-model.number="scope.row.monthSales"
                                                            style="width:80px" type="number"
                                                            :min="0" :max="90000000" :precision="0" :controls="false"  size="mini"
                                                            @change="rowChanged(scope.row,'monthSales')"
                                                        />
                                                         <el-button type="text"  @click="batchSetRowColVal(scope.row,'monthSales')">
                                                            批量
                                                        </el-button>
                                                    </template>


                                                </template>
                                            </el-table-column>
                                            <el-table-column width="80" label="月销占比" prop="monthSalesRatio" :sortable="true">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{formatPercen(scope.row.monthSalesRatio)}}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="190" label="商品编码">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>

                                                    <template  v-else>
                                                        <el-input  v-model="scope.row.goodsCodes" readonly size="mini" style="width:140px;">
                                                            <el-button size="mini" slot="append" icon="el-icon-close" @click="clearSelGoods(scope.row,'goodsCodes')"  ></el-button>
                                                            <el-button size="mini" slot="prepend" icon="el-icon-search" @click="onSelGoods(scope.row,'goodsCodes')" ></el-button>
                                                        </el-input>
                                                        <el-button type="text"  @click="batchSetRowColVal(scope.row,'goodsCodes')">
                                                                批量
                                                            </el-button>
                                                    </template>

                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="成本" :sortable="true" prop="goodsCost">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>

                                                    <el-input-number v-else style="width:80px" type="number" :min="0" :max="10000000" :precision="3" :controls="false" v-model.number="scope.row.goodsCost" size="mini" @change="rowChanged(scope.row,'goodsCost')"  />


                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="SKU总成本" :sortable="true" prop="goodsCostSum">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.goodsCostSum}}
                                                    </span>

                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="毛一利润率" :sortable="true" prop="grossProfit1Ratio">
                                                <template slot-scope="scope">

                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.grossProfit1Ratio}}%
                                                    </span>
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="60" label="修改人">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.goodsCostEditName}}
                                                    </span>

                                                </template>
                                            </el-table-column>

                                            <el-table-column width="180" label="包装编码">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>

                                                    <template  v-else>
                                                        <!-- <el-input v-model="scope.row.packCode" readonly size="mini" style="width:130px;">
                                                            <el-button size="mini" slot="append" icon="el-icon-close" @click="clearSelGoods(scope.row,'packCode')"></el-button>
                                                            <el-button size="mini" slot="prepend" icon="el-icon-search" @click="onSelGoods(scope.row,'packCode')"></el-button>
                                                        </el-input> -->


                                                        <el-select  style="width:130px;" clearable
                                                            v-model="scope.row.packCode" filterable remote placeholder="请输入关键词"
                                                            :remote-method="remoteSeachGoods" @change="remoteSeachGoodsChg(scope.row,'packCode')"
                                                            :loading="remoteSeachGoodsLoading">
                                                            <el-option
                                                            v-for="item in goodsOptions"
                                                            :key="item.goodsCode"
                                                            :label="item.goodsCode"
                                                            :value="item.goodsCode">
                                                                <strong>成本:</strong>{{item.costPrice}}
                                                                <strong style="margin-left:10px;">编码:</strong>{{item.goodsCode}}
                                                                <strong style="margin-left:10px;">品名:</strong>{{item.goodsName}}
                                                            </el-option>
                                                        </el-select>
                                                          <el-button type="text"  @click="batchSetRowColVal(scope.row,'packCode')">
                                                                    批量
                                                        </el-button>

                                                    </template>
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="100" label="包装成本" :sortable="true" prop="packCostPrice">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <el-input-number v-else style="width:80px" type="number" :min="0" :max="1000000" :precision="3" :controls="false" v-model.number="scope.row.packCostPrice" @change="rowChanged(scope.row,'packCostPrice')" :disabled="!!(scope.row.packCode)" />
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="60" label="修改人">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.packCostEditName}}
                                                    </span>
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="90" label="利润" :sortable="true" prop="profit">
                                                <template slot-scope="scope">

                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.profit}}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column width="70" label="利润率" :sortable="true" prop="profitRatio">
                                                <template slot-scope="scope">

                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        -
                                                    </span>
                                                    <span v-else>
                                                        {{scope.row.profitRatio}}%
                                                    </span>
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="150" label="SKU数据时间">
                                                <template slot-scope="scope">
                                                    {{scope.row.dataTime}}
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="70" label="状态" :sortable="true" prop="isDisabled">
                                                <template slot-scope="scope">
                                                    <span v-if="scope.row.isDisabled && scope.row.isDisabled==1">
                                                        已禁用
                                                    </span>
                                                    <span v-else>
                                                        已启用
                                                    </span>
                                                </template>
                                            </el-table-column>

                                            <el-table-column width="100" label="">
                                                <template slot-scope="scope">
                                                    <el-button type="text" v-if="scope.row.isDisabled && scope.row.isDisabled==1" @click="setRowSkuDisableState(scope.row,0)">启用</el-button>
                                                    <el-button type="text" v-else @click="setRowSkuDisableState(scope.row,1)">禁用</el-button>

                                                    <!-- 无采样记录 且 未禁用的SKU  才允许采样 -->
                                                    <el-button type="text" v-if=" !(scope.row.isDisabled && scope.row.isDisabled==1) " @click="curRow=scope.row;opSamplingList(scope.row);">
                                                        采样({{ (scope.row.samplingDtls && scope.row.samplingDtls.length>0)? scope.row.samplingDtls.length:0 }})
                                                    </el-button>

                                                </template>

                                            </el-table-column>

                                        </template>
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                            <el-tab-pane label="供应商备注">
                                <el-row>
                                    <el-col :span="24">
                                        <div style="padding-top:20px;max-height:300px;overflow:auto;border:1px solid silver;">
                                            <el-timeline>
                                                <el-timeline-item v-for="item in supplierRecords" :timestamp="item.createdTime +'--'+ item.recordUserName+'--'+item.supplierName" placement="top" :key="item.id">
                                                    <el-card>
                                                        <div v-html="item.supplierRemark" class="tempdiv"></div>
                                                    </el-card>
                                                </el-timeline-item>

                                            </el-timeline>
                                        </div>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24" style="max-height:300px;overflow:auto">
                                        <yh-quill-editor :value.sync="form.supplierRemark" v-if="formEditMode">
                                        </yh-quill-editor>
                                    </el-col>
                                </el-row>
                            </el-tab-pane>

                        </el-tabs>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <!-- <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;padding-top:10px;">
                    <el-button @click="onClose">取 消</el-button>
                    ////<el-button type="primary" @click="onSkuSave(false)" :loading="skuSaveLoading" v-if="skuSaveHiddle">保存草稿</el-button>
                    <el-button type="primary" @click="onSave(true)"  v-if="mode!=3">{{'保存利润&关闭'}}</el-button>
                </el-col>
            </el-row>
        </template> -->

        <!--选择商品-->
        <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag append-to-body>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:2000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>



        <!-- 查看采样记录 showSampling-->
        <el-dialog title="采样记录" :visible.sync="visibleSamplingList" width='88%' height='500px' v-dialogDrag append-to-body>
            <div style="height:500px;">
                <el-button type="primary" @click="visibleSkuOrderForm=true;skuOrderForm.parentId=curRow.id;skuOrderForm.quantity=1;">新增采样</el-button>
                <ces-table ref="skuSamplingTable" :that='that' :isIndex='false' :tableData='currentSamplingList' :tableCols='skuSamplingCols' :loading="false" :isSelectColumn="false" :hasexpandRight="true">
                    <template slot="right">
                        <el-table-column width="50" label="操作">
                            <template slot-scope="scope">
                                <el-button type="text" @click="opSamplingDtl(scope.row,false)">详情</el-button>
                            </template>
                        </el-table-column>
                    </template>
                </ces-table>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="visibleSamplingList = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- visibleSkuOrderForm 登记采样 -->
        <el-dialog title="登记采样" :visible.sync="visibleSkuOrderForm" v-loading="loadingSkuOrderForm" :close-on-click-modal="false" width='600px' height='460px' v-dialogDrag append-to-body>
            <el-form ref="skuOrderForm" :model="skuOrderForm" label-width="100px">
                <el-form-item label="采样来源：" prop="sampleSource" :rules="[{ required: true, message: '请选择来源', trigger: 'blur' }]">
                    <el-select v-model="skuOrderForm.sampleSource" clearable @change="()=>{if(skuOrderForm.sampleSource!='厂家'){
                        skuOrderForm.factoryName='';
                        skuOrderForm.factoryUrl='';
                        skuOrderForm.factorySpecification='';
                        }}">
                        <el-option value="厂家" label="厂家" />
                        <el-option value="竞品" label="竞品" />
                    </el-select>
                </el-form-item>
                <el-form-item label="数量(个)：" prop="quantity" :rules="[{ required: true, message: '请填写数量', trigger: 'blur' }]">
                    <el-input-number type="number" align="right" clearable :precision="0" :min="1" :max="10000" v-model.number="skuOrderForm.quantity"></el-input-number>
                </el-form-item>

                <el-form-item label="厂家：" v-if="skuOrderForm.sampleSource=='厂家'" prop="factoryName">
                    <el-input v-model="skuOrderForm.factoryName" :maxlength="100"></el-input>
                </el-form-item>
                <el-form-item label="厂家链接：" v-if="skuOrderForm.sampleSource=='厂家'">
                    <el-input v-model="skuOrderForm.factoryUrl" :maxlength="200"></el-input>
                </el-form-item>
                <el-form-item label="厂家规格：" v-if="skuOrderForm.sampleSource=='厂家'">
                    <el-input v-model="skuOrderForm.factorySpecification" :maxlength="50"></el-input>
                </el-form-item>

                <el-form-item label="订单号：">
                    <el-input v-model="skuOrderForm.orderNum" :maxlength="30"></el-input>
                    <span style="color:red">有订单号，快递单号可以不填，系统会自动同步。</span>
                </el-form-item>

                <el-form-item label="快递公司：">
                    <yh-expressselector :value.sync="skuOrderForm.expressCompanyCode" :text.sync="skuOrderForm.expressCompanyName"></yh-expressselector>
                </el-form-item>

                <el-form-item label="快递单号：">
                    <el-input v-model="skuOrderForm.expressNum" :maxlength="40"></el-input>
                    <el-button v-if="!!skuOrderForm.expressNum" type='text' @click="onShowExproessHttp(skuOrderForm)">查看轨迹</el-button>
                </el-form-item>

            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="saveRegSkuOrder">登记采样</el-button>
                    <el-button @click="visibleSkuOrderForm=false">取 消</el-button>
                </span>
            </template>
        </el-dialog>

        <el-drawer title="物流跟踪" :visible.sync="drawervisible" direction="rtl" :append-to-body="true">
            <logistics ref="logistics"></logistics>
        </el-drawer>

        <el-dialog title="采样详情" :visible.sync="visibleSamplingDtl" width='90%' :close-on-click-modal="false" append-to-body v-dialogDrag v-loading="dialogSamplingDtlLoading" element-loading-text="拼命加载中">
            <skuOrderPage4Enquiry ref="skuOrderPage4Enquiry" style="z-index:1000;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="visibleSamplingDtl = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- visibleAddSkuPage 添加SKU -->
        <el-dialog title="添加SKU" :visible.sync="visibleAddSkuPage" :close-on-click-modal="false" width='80%' height='460px' v-dialogDrag append-to-body>
            <el-table :data="listAddSkuPage" row-key="rowId">
                <el-table-column prop="skuImgUrl" label="SKU图" width="90" column-key="rowId">
                    <template slot="header" >
                        <span style="color:red;">*</span>SKU图
                    </template>
                    <template slot-scope="scope">
                        <div style="width:90px;height:90px;overflow:hidden;">
                            <yh-img-upload :value.sync="scope.row.skuImgUrl" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="skuCode" label="SKU编码" width="100">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.skuCode" />
                    </template>
                </el-table-column>
                <el-table-column prop="skuName" label="规格名称" min-width="300">
                    <template slot="header" >
                        <span style="color:red;">*</span>规格名称
                    </template>
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.skuName" />
                    </template>
                </el-table-column>
                <el-table-column width="100" label="售价" prop="salePrice" :sortable="true" column-key="rowId">
                    <template slot="header" >
                        <span style="color:red;">*</span>售价
                    </template>
                    <template slot-scope="scope">
                        <el-input-number v-model.number="scope.row.salePrice"
                            style="width:80px" type="number"
                            :min="0" :max="100000" :precision="2" :controls="false"  size="mini"
                            @change="rowChanged(scope.row,'salePrice')"
                        />
                    </template>
                </el-table-column>

                <el-table-column width="100" label="月销量" prop="monthSales" :sortable="true" >
                    <template slot="header" >
                        <span style="color:red;">*</span>月销量
                    </template>
                    <template slot-scope="scope">
                        <el-input-number  v-model.number="scope.row.monthSales"
                            style="width:80px" type="number"
                            :min="0" :max="90000000" :precision="0" :controls="false"  size="mini"
                            @change="rowChanged(scope.row,'monthSales')"
                        />
                    </template>
                </el-table-column>
                <el-table-column width="80" label="月销售额" prop="monthSaleMoney" :sortable="true">
                    <template slot-scope="scope">
                        <span >
                            {{scope.row.monthSaleMoney}}
                        </span>
                    </template>
                </el-table-column>


                <el-table-column width="90">
                    <template slot="header" >
                        <el-button type="primary" @click="listAddSkuPage.push({skuImgUrl:'',rowId:Date.parse(new Date()) +new Date().getMilliseconds()})">新增一行</el-button>
                    </template>
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="listAddSkuPage.splice(scope.$index, 1)">删除</el-button>
                    </template>
                </el-table-column>

            </el-table>

            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="saveAddSkuPage" >保 存</el-button>
                    <el-button @click="visibleAddSkuPage=false">取 消</el-button>
                </span>
            </template>
        </el-dialog>


    </my-container>
</template>
<script>


    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import { formatmoney, formatPercen, platformlist, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { getHotSaleGoodsChooseSkuData, saveHotSaleGoodsChooseSkuData,   SetEnquiryFeedbackWinnerBefore,
        setSkuDisableState, getSkuSamplingListBySkuId, registerSkuOrderBySkuAsync,addSkuListByUser,SetAllSkuDisableState
    } from '@/api/operatemanage/productalllink/alllink';
    import goodschoice from "@/views/base/goods/goods2.vue";

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import skuOrderPage4Enquiry from '@/views/operatemanage/productalllink/skuenquiry/hotsalegoodskuorder4enquiry.vue';

    import YhExpressselector from '@/components/YhCom/yh-expressselector.vue'
    import YhImgUpload from '../../../../components/upload/yh-img-upload.vue';

    import logistics from '@/components/Comm/logistics'

     import {getList      } from "@/api/inventory/basicgoods"

     import uploadimgFile from "@/components/Comm/uploadimgFile.vue";

    //采样状态
    function formatSampleOrderState(val) {

        //采样状态 0无状态、10已登记、20已下单、30已到货
        switch (val) {
            case 0:
                return "已登记";
            case 10:
                return "已登记";
            case 20:
                return "已下单";
            case 30:
                return "已到货";
        }

        return "";
    }


    const skuTableCols = [
        //调整列记得调整 getSkuTableData的[10]
        // { istrue: true, prop: 'id', label: '主键', width: '100', display: false },
        // { istrue: true, prop: 'hotSaleGoodsChooseId', label: '主键p', width: '100', display: false },
        // { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '120' },
        // { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '120' },
        // { istrue: true, prop: 'platform', label: '竞品平台', width: '80', formatter: (row) => row.platformName || ' ' },

        { istrue: true, prop: 'skuImgUrl', label: 'SKU图', width: '64', type: 'image' },
        //{ istrue: true, prop: 'skuCode', label: 'SkuID', width: '120' },
        { istrue: true, prop: 'skuName', label: '规格名称', minwidth: '180', sortable: true },//, width: '120'

        //{ istrue: true, prop: 'salePrice', label: '售价', width: '80', sortable: true },
        //{ istrue: true, prop: 'monthSaleMoney', label: '月销售额', width: '80', sortable: true },

        //{ istrue: true, prop: 'monthSales', label: '月销量', width: '80', sortable: true },
        // {
        //     istrue: true, prop: 'monthSalesRatio', label: '月销占比', width: '80', sortable: true, formatter: (row) => {
        //         if (row.isDisabled && row.isDisabled == 1)
        //             return '-';
        //         else
        //             return formatPercen(row.monthSalesRatio);
        //     }
        // },

    ];



    const skuSamplingCols = [
        { istrue: true, prop: 'sampleSource', label: '采样来源', width: '100', sortable: true },
        { istrue: true, prop: 'quantity', label: '数量(个)', width: '100', sortable: true },
        { istrue: true, prop: 'factoryName', label: '厂家', width: '140', sortable: true },
        { istrue: true, prop: 'factorySpecification', label: '厂家规格', width: '120', sortable: true },
        { istrue: true, prop: 'factoryUrl', label: '厂家链接', minwidth: 140, sortable: true },
        { istrue: true, prop: 'expressCompanyName', label: '快递公司', width: 80, sortable: true },
        { istrue: true, prop: 'expressNum', label: '快递单号', width: 160, sortable: true  ,type: 'click', handle: (that, row, column) => that.onShowExproessHttp(row) },
        { istrue: true, prop: 'sampleOrderState', label: '采样状态', width: '80', formatter: (row) => formatSampleOrderState(row.sampleOrderState) },
    ]
    function getSupplierPlatForm()
    {
        if(form.supplierPlatForm==2)
return
    }
    export default {
        name: "hotsalegoodschooselistsku",
        components: { MyContainer, MyConfirmButton,logistics, goodschoice, cesTable, YhQuillEditor, skuOrderPage4Enquiry, YhExpressselector,YhImgUpload,uploadimgFile },
        data() {
            return {
                that: this,
                mode:3,
                reftype: null,
                supplierPlatFormList:[{value:1,label:"微信"},{value:2,label:"1688"}],
                advertisingFeeTypeList:[{value:1,label:"占比"},{value:2,label:"具体金额"}],
                otherFeeList:[{value:1,label:"占比"},{value:2,label:"具体金额"}],
                grossProfit4RateTypeList:[{value:1,label:"占比"},{value:2,label:"具体金额"}],
                form: {
                    hotSaleGoodsChooseId: null,
                    goodsCompeteId: "",
                    goodsCompeteName: "",
                    newGoodsCompeteName:"",
                    platform: null,
                    lastMonthSaleCount: 1,
                    lastMonthPayerCount: 1,
                    lastMonthSaleAmount: 1111,
                    skuTotalCost: 1111,
                    packTotalCost: 1111,
                    expressPrice: 2.5,
                    expressAmount: null,
                    grossProfit: null,
                    grossProfitRatio: null,
                    otherFeeType: null,
                    otherFee: null,
                    grossProfit3: null,
                    grossProfit3Ratio: null,
                    grossProfit4RateType: null,
                    grossProfit4Rate: null,
                    grossProfit4: null,
                    grossProfit4Ratio: null,
                    supplierName: "",
                    supplierRemark: "",
                    supplierPlatForm: null,
                    supplierWxNum: "",
                    supplierLink: "",
                    supplierGoodLink: "",
                    advertisingFeeType: null,
                    advertisingFee: null,
                    goodsCompeteShortName:"",
                },
                enquiryInfo:{
                    hasEnquiry:false,
                    fbCount:0,
                },
                jslrFormRules:{},
                // jslrFormRules: {
                //     supplierPlatForm: [{ required: true, message: '请选择供应商平台', trigger: 'blur' }],
                //     supplierWxNum: [{ required: true, message: '请输入微信账号', trigger: 'blur' }],
                // },
                // jslrFormRules2: {
                //     supplierPlatForm: [{ required: true, message: '请选择供应商平台', trigger: 'blur' }],
                //     supplierWxNum: [{ required: true, message: '请输入微信账号', trigger: 'blur' }],
                //     supplierName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
                //     supplierGoodLink: [{ required: true, message: '请输入产品链接', trigger: 'blur' }],
                // },
                skuTableCols: skuTableCols,
                platformlist: platformlist,
                total: 0,
                skuTableData: [],
                //summaryarry: {},
                tansitionshow: false,
                listLoading: false,
                pageLoading: false,
                goodschoiceVisible: false,//选择商品显隐
                curRow: null,
                curCol: "",
                formEditMode: true,//是否编辑模式
                supplierRecords: [],
                selfInfo: {},

                //--------------采样列表Being-------------
                visibleSamplingList: false,//显示报价记录
                skuSamplingCols: skuSamplingCols,
                currentSamplingList: [], //报价记录
                //--------------采样列表End-------------
                //--------------采样详情Being-------------
                visibleSamplingDtl: false,
                loadingSkuOrderForm:false,
                dialogSamplingDtlLoading: false,
                //--------------采样详情End-------------

                visibleSkuOrderForm: false,//SKU订单
                skuOrderForm: {
                    id: 0,
                    parentId: 0,
                    quantity: 1,
                    sampleSource: '',
                    factoryName: '',
                    factoryUrl: '',
                    factorySpecification: '',
                    expressNum: '',
                    orderNum:''
                },
                visibleAddSkuPage:false,//新增SKU页面
                listAddSkuPage:[],  //新增SKU数据
                drawervisible:false,
                tableHeightBase:85,
                goodsOptions:[],
                remoteSeachGoodsLoading:false,


            };
        },
        async mounted() {
            const userInfoName = "hotsalegoods_selfuserinfo";
            let selfInfo4Store = getStore(userInfoName);
            if (selfInfo4Store) {
                this.selfInfo = selfInfo4Store;
            }
            this.tableHeightBase-=1;
        },
        computed: {

            jslrFormRulesList:function()
            {
                var jslrFormRules= {
                    supplierPlatForm: [{ required: true, message: '请选择供应商平台', trigger: 'change' }],
                    supplierWxNum: [{ required: true, message: '请输入微信账号', trigger: 'blur' }],
                    goodsCompeteShortName: [{ required: true, message: '请输入产品简称', trigger: 'blur' }],
                    goodsCompeteType: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
                    supplierName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
                    supplierGoodLink: [{ required: this.form.supplierPlatForm==2, message: '请输入产品链接', trigger: 'blur' }],
                    supplierLink: [{ required: this.form.supplierPlatForm==2, message: '请输入供应商链接', trigger: 'blur' }],
                };
                return jslrFormRules;
            },
            tableHeight: function () {
                let rowsCount = 1;
                if (this.skuTableData && this.skuTableData.length && this.skuTableData.length > 0) {
                    rowsCount = this.skuTableData.length;
                }
                let rowsHeight = (rowsCount + 1) * 40 + this.tableHeightBase;
                return rowsHeight > 400 ? 400 : rowsHeight;
            },
            calcLastMonthSaleCount: function () {
                //月销量
                let sumCost = 0;
                this.skuTableData.forEach((x) => {
                    if (!(x.isDisabled && x.isDisabled == 1))
                        sumCost += isNaN(x.monthSales) ? 0 : x.monthSales;
                });
                this.form.lastMonthSaleCount = sumCost.toFixed(0);
                //启用禁用后，重算月销量占比
                this.skuTableData.forEach((x) => {
                    if (!(x.isDisabled && x.isDisabled == 1)) {
                        if (sumCost > 0)
                            x.monthSalesRatio = Number((100.0 * (isNaN(x.monthSales) ? 0 : x.monthSales) / sumCost).toFixed(2));
                        else
                            x.monthSalesRatio = 0;
                    }

                });
                return sumCost.toFixed(0);
            },
            calcLastMonthSaleAmount: function () {
                //月销售额
                let sumCost = 0;
                this.skuTableData.forEach((x) => {
                    if (!(x.isDisabled && x.isDisabled == 1))
                        sumCost += isNaN(x.monthSaleMoney) ? 0 : x.monthSaleMoney;
                });
                this.form.lastMonthSaleAmount = sumCost.toFixed(2);
                return sumCost.toFixed(2);
            },
            calcSkuTotalCost: function () {
                //sku总成本
                let sumCost = 0;
                this.skuTableData.forEach((x) => {
                    if (!(x.isDisabled && x.isDisabled == 1))
                        sumCost += (isNaN(x.goodsCost) ? 0 : x.goodsCost) * x.monthSales;
                });
                this.form.skuTotalCost = sumCost.toFixed(3);
                return sumCost.toFixed(3);
            },
            calcPackTotalCost: function () {
                //包装总成本
                let sumCost = 0;
                this.skuTableData.forEach((x) => {
                    if (!(x.isDisabled && x.isDisabled == 1))
                        sumCost += (isNaN(x.packCostPrice) ? 0 : x.packCostPrice) * (isNaN(x.monthSales) ? 0 : x.monthSales);
                });
                this.form.packTotalCost = sumCost.toFixed(3);
                return sumCost.toFixed(3);
            },
            calcExpressAmount: function () {
                //快递成本
                var sumCost = 0;
                let expressPrice = (isNaN(this.form.expressPrice) ? 0 : this.form.expressPrice);
                //无支付人数时，暂用销量代替
                let lastMonthPayerCount = ((this.form.lastMonthPayerCount == null || this.form.lastMonthPayerCount == 0) ? this.form.lastMonthSaleCount : this.form.lastMonthPayerCount);
                sumCost = expressPrice * lastMonthPayerCount;

                // if (this.form.platform === 2) {
                //     sumCost = expressPrice * this.form.lastMonthSaleCount;
                // } else {
                //     sumCost = expressPrice * lastMonthPayerCount;
                // }

                this.form.expressAmount = sumCost.toFixed(2);
                return sumCost.toFixed(2);
            },
            calcGrossProfit: function () {
                //毛利润=总额-sku总成本-包装总成本-快递费-广告费
                let sumCost = this.form.lastMonthSaleAmount - this.form.skuTotalCost - this.form.packTotalCost - this.form.expressAmount;
                if(this.form.advertisingFeeType==1)
                {//占比
                    let ggf = (isNaN(this.form.advertisingFee) ? 0 : this.form.advertisingFee) / 100 * this.calcLastMonthSaleAmount;
                    sumCost=sumCost-ggf;
                }
                else if(this.form.advertisingFeeType==2)
                {//金额
                    sumCost=sumCost-(isNaN(this.form.advertisingFee) ? 0 : this.form.advertisingFee);
                }
                this.form.grossProfit = sumCost.toFixed(2);
                return sumCost.toFixed(2);
            },
            calcGrossProfitRatio: function () {
                //毛利润=毛利润/总额*100%

                //毛利润
                //let sumMlrCost = this.form.lastMonthSaleAmount - this.form.skuTotalCost - this.form.packTotalCost - this.form.expressAmount;
                let sumCost = this.calcLastMonthSaleAmount == 0 ? 0 : (this.calcGrossProfit / this.calcLastMonthSaleAmount * 100.00);

                this.form.grossProfitRatio = sumCost.toFixed(2);
                return sumCost.toFixed(2);
            },
            calcGrossProfit3: function () {
                //毛3利润率=（毛利润-其他费用）/总额*100%
                //其他费用为比利
                let otherFeeCalc = (isNaN(this.form.otherFee) ? 0 : this.form.otherFee) / 100 * this.calcLastMonthSaleAmount;
                if(this.form.otherFeeType==2)
                {
                    otherFeeCalc = (isNaN(this.form.otherFee) ? 0 : this.form.otherFee);
                }
                return Number( (this.calcGrossProfit - otherFeeCalc).toFixed(2) );
            },
            calcGrossProfit3Ratio: function () {
                //毛3利润率=（毛利润-其他费用）/总额*100%
                //其他费用为比利
                let otherFeeCalc = (isNaN(this.form.otherFee) ? 0 : this.form.otherFee) / 100 * this.calcLastMonthSaleAmount;
                if(this.form.otherFeeType==2)
                {
                    otherFeeCalc = (isNaN(this.form.otherFee) ? 0 : this.form.otherFee);
                }
                let sumCost = this.calcLastMonthSaleAmount == 0 ? 0 : ((this.calcGrossProfit - otherFeeCalc) / this.calcLastMonthSaleAmount * 100.00);

                this.form.grossProfit3=  Number( (this.calcGrossProfit - otherFeeCalc).toFixed(2) );
                this.form.grossProfit3Ratio = Number(sumCost.toFixed(2));

                //毛3涉及到所有金额，可以计算行利润
                this.skuTableData.forEach((x) => {
                    //SKU成本=月销量*成本
                    x.goodsCostSum = Number((x.monthSales * (isNaN(x.goodsCost) ? 0 : x.goodsCost)).toFixed(3));
                    //毛一利润率grossProfit1Ratio= （销售额-SKU成本）/销售额
                    x.grossProfit1Ratio = Number(
                        x.monthSaleMoney == 0
                            ? 0
                            : ((x.monthSaleMoney - x.goodsCostSum) / x.monthSaleMoney * 100).toFixed(2)
                    );

                    x.profit = Number(
                        (
                            (isNaN(x.monthSaleMoney) ? 0 : x.monthSaleMoney)
                            -
                            ((isNaN(x.goodsCost) ? 0 : x.goodsCost)
                                + (isNaN(x.packCostPrice) ? 0 : x.packCostPrice)) * x.monthSales).toFixed(2)
                    );

                    x.profitRatio = Number((x.monthSaleMoney == 0 ? 0 : (x.profit / x.monthSaleMoney * 100)).toFixed(2));
                });

                return sumCost.toFixed(2);
            },
            calcGrossProfit4Ratio: function () {
                //净利利润=毛3-净利比例(预估)*月销售额
                //净利利润率=净利利润/月销售额
                if(this.form.grossProfit4RateType==1)
                {
                    this.form.grossProfit4=  Number((this.calcGrossProfit3 - (isNaN(this.form.grossProfit4Rate) ? 0 : this.form.grossProfit4Rate) / 100 * this.calcLastMonthSaleAmount).toFixed(2));
                }
                else
                {
                    this.form.grossProfit4=  Number((this.calcGrossProfit3 - (isNaN(this.form.grossProfit4Rate) ? 0 : this.form.grossProfit4Rate)).toFixed(2));
                }
                let sumCost = this.calcLastMonthSaleAmount == 0 ? 0 : (this.form.grossProfit4 / this.calcLastMonthSaleAmount * 100.00);
                this.form.grossProfit4Ratio = Number(sumCost.toFixed(2));
                return Number(sumCost.toFixed(2));
            },
        },
        methods: {
            formatLinkProCode: formatLinkProCode,
            formatTime: formatTime,
            formatPercen: formatPercen,
            formatSampleOrderState: formatSampleOrderState,

            beforeclose(datetime){


                if(this.reftype==2&&!datetime){
                    this.$confirm('关闭界面将无法继续下一步操作，如需继续选品请点击保存利润，是否确认关闭页面？', '', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$emit('isclose');
                        this.$emit('onSearch');
                        // this.dialogSkuVisible = false;
                    }).catch(() => {
                    });
                    return;
                }
                this.$emit('isclose');
            },

            closeopen(){
                this.tansitionshow = !this.tansitionshow;
                console.log("点击变化",this.tansitionshow)
            },
            //一键禁用
            onDisableAllSku(disState){
                let self=this;
                let txt='即将禁用所有SKU，对于需要测算的SKU请手动启用。如有未保存数据，请先执行保存。是否确定继续？';
                if(disState==0)
                    txt='即将启用所有SKU，是否确定继续？';
                this.$confirm(txt,"").then(() => {
                        SetAllSkuDisableState({"chooseId":self.form.hotSaleGoodsChooseId,  "disState":disState}).then(()=>{
                            self.skuTableData.forEach(sku=>{
                                sku.isDisabled=disState;
                            });
                        })
                    }).catch(() => {
                        //已取消
                    });
            },
            allowSaveBtn(){
                let hasTbData= this.skuTableData && this.skuTableData.length>0;

                return hasTbData && this.calcLastMonthSaleCount>0 && this.calcLastMonthSaleAmount>0 && this.calcSkuTotalCost>0 ;

            },
            onClose(){
                this.$emit('close');
            },
            async loadData({oid,mode}){
                if(mode)
                    this.mode=mode;

                await this.getSkuTableData(oid,mode!=3);
            },
            async onSave(isClose){
                if(await this.saveSkuTableData(true)){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('isclose');
                }
            },

            async remoteSeachGoods(query){
                if (query !== '') {
                    this.remoteSeachGoodsLoading = true;
                    let qrRlt=await getList({"pageSize":100,"currentPage":1,"OrderBy":"goodsCode","IsAsc":true,"styleCode":null,keywords:query.trim()})
                    if(qrRlt && qrRlt.success)
                        this.goodsOptions = qrRlt.data.list;
                    this.remoteSeachGoodsLoading =false;
                } else {
                    this.goodsOptions = [];
                }
            },
            remoteSeachGoodsChg(row,col){
                if(row[col]){
                    let tempRow=this.goodsOptions.find(x=>x.goodsCode==row[col]);
                    if(col=='packCode'){
                        row.packCostPrice=tempRow.costPrice;
                        this.rowChanged(row, 'packCostPrice')
                    }
                }
            },
            batchSetRowColVal(row,col){
//salePrice\monthSales\goodsCodes\packCode
                let self=this;
                 self.$confirm('此操作将覆盖其他行当前列数据, 确定继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                        let currentVal=row[col];
                        self.skuTableData.forEach((x) => {
                            x[col]=currentVal;

                            let chgCol=col;
                            if(col=="goodsCodes"){
                                chgCol="goodsCost";
                                x[chgCol]=row[chgCol];
                            }else if(col=="packCode"){
                                chgCol="packCostPrice";
                                x[chgCol]=row[chgCol];
                            }

                            self.rowChanged(x,chgCol);
                        });
                    }).catch(() => {

                });



            },
            //保存SKU
            async saveAddSkuPage(){
                let that=this;
                if(this.listAddSkuPage==null|| this.listAddSkuPage.length==0){
                    this.$alert('请填写SKU信息！');
                    return;
                }

                let errMsg='';
                this.listAddSkuPage.forEach(r=>{

                    r.hotSaleGoodsChooseId=that.form.hotSaleGoodsChooseId;

                    if(!r.skuName){
                        errMsg="请填写规格名称！";
                        return ;
                    }

                     if(!r.skuImgUrl){
                        errMsg="请上传SKU图片！";
                        return ;
                    }

                    if(r.monthSaleMoney==undefined || r.monthSaleMoney==null || r.monthSaleMoney==0){
                        errMsg="请填写售价及销量数据！";
                        return ;
                    }
                });

                if(errMsg){
                    this.$alert(errMsg);
                    return;
                }

                let rlt=await addSkuListByUser(this.listAddSkuPage);
                if(rlt && rlt.success){
                    this.$message.success('添加成功！');

                    this.skuTableData=this.skuTableData.concat(rlt.data);

                    this.visibleAddSkuPage=false;
                    this.listAddSkuPage=[];

                }

            },
            //打开手动新增SKU页面
            onOpenAddSkuPage(){
                this.visibleAddSkuPage=true;
                if(this.listAddSkuPage.length==0){
                    this.listAddSkuPage.push({skuImgUrl:'',rowId:Date.parse(new Date()) +new Date().getMilliseconds()});
                }
            },
            //查看SKU采样详情
            opSamplingDtl(row, isEdit) {
                this.skuSaveHiddle = !!isEdit;
                this.visibleSamplingDtl = true;
                this.$nextTick(() => {
                    this.$refs.skuOrderPage4Enquiry.getSkuOrderData(row.id, isEdit);
                });
            },
            //打开采样登记列表
            async opSamplingList(row) {
                this.curRow = row;
                let smpRlt = await getSkuSamplingListBySkuId({ skuId: row.id });
                this.currentSamplingList = [];
                if (smpRlt.success && smpRlt.data && smpRlt.data.samplingDtls) {
                    this.currentSamplingList = smpRlt.data.samplingDtls;
                }
                this.visibleSamplingList = true;


            },
            //登记采样 registerSkuOrderBySkuAsync
            async saveRegSkuOrder() {

                if (!(this.skuOrderForm.quantity && this.skuOrderForm.quantity > 0) || !(this.skuOrderForm.sampleSource)) {
                    this.$message.error('请填写采样来源和数量！');
                    return;
                }

                this.loadingSkuOrderForm=true;

                var rlt = await registerSkuOrderBySkuAsync({ ...this.skuOrderForm });
                if (rlt && rlt.success) {
                    this.curRow.skuOrder = { ...this.skuOrderForm };
                    this.$message.success('采样登记成功！');
                    this.currentSamplingList.push(rlt.data);
                    this.visibleSkuOrderForm = false;

                    this.skuOrderForm.factoryName = "";
                    this.skuOrderForm.factoryUrl = "";
                    this.skuOrderForm.factorySpecification = "";
                    this.skuOrderForm.expressNum = "";
                    this.skuOrderForm.orderNum='';

                }
                this.loadingSkuOrderForm=false;
            },
            //行样式
            customRowStyle2({ row, rowIndex }) {
                if (row.isDisabled && row.isDisabled == 1) {
                    //禁用时
                    return { 'color': '#C0C4CC' }
                }
            },
            //启用禁用SKU, 0启用，1禁用
            async setRowSkuDisableState(row, state) {
                var rlt = await setSkuDisableState({ skuId: row.id, disState: state, remark: '' });
                if (rlt && rlt.success) {
                    row.isDisabled = state;
                    this.$message.success('操作成功！');
                }
            },
            //发起询价
            async launchEnquiry(row) {
                let self=this;
                this.$refs.jslrForm.clearValidate();
                let validate = true;
                if (self.form.goodsCompeteName == null || self.form.goodsCompeteName.trim() == "") {
                    this.$refs.jslrForm.validateField("newGoodsCompeteName",(error)=>{
                        if(error!=""){
                            validate = false;
                        }
                    });
                }
                this.$refs.jslrForm.validateField("goodsCompeteShortName",(error)=>{
                    if(error!=""){
                        validate = false;
                    }
                });
                if (!validate) {
                    return;
                }

                this.$showDialogform({
                    path:`@/views/operatemanage/productalllink/hotsale/LaunchEnquiryForm.vue`,
                    title:'发起询价',
                    args:{
                        id:self.form.hotSaleGoodsChooseId,
                        goodsCompeteId:self.form.goodsCompeteId,
                        goodsCompeteName:(self.form.goodsCompeteName == null || self.form.goodsCompeteName.trim() == "")?self.form.newGoodsCompeteName:self.form.goodsCompeteName,
                        goodsCompeteShortName:self.form.goodsCompeteShortName,
                        eqList:self.skuTableData.filter(sku=>!(sku.isDisabled && sku.isDisabled==1)).map(sku=>{
                            return {
                                id:sku.id,
                                skuImgUrl:sku.skuImgUrl,
                                skuName:sku.skuName,
                                hopeCostPrice:null,
                                estimatedQuantity:null
                            }
                        })
                    },
                    height:500,
                    width:'780px',
                    callOk:(rlt)=>{
                        self.enquiryInfo.hasEnquiry=true;
                    }
                })
            },
            //报价列表
            async enquiryList(){
                let self=this;
                this.$showDialogform({
                    path:`@/views/operatemanage/productalllink/hotsale/EnquiryFbInfoForChoose.vue`,
                    title:'报价列表',
                    autoTitle:false,
                    args:{
                        oid:self.form.hotSaleGoodsChooseId
                    },
                    height:'680px',
                    width:'780px',
                    callOk:(data)=>{
                        self.setFbInfo(data)
                    }
                });
                self.enquiryInfo.hasFb=false;
            },
            //设置报价信息
            setFbInfo(fbInfo){
                console.log(fbInfo);

                let self=this;
                self.form.fbId=fbInfo.id;
                if(fbInfo.supplierPlatformName){
                    let spPlatformOpt=self.supplierPlatFormList.find(opt=>opt.label==fbInfo.supplierPlatformName);
                    if(spPlatformOpt){
                        self.form.supplierPlatForm=spPlatformOpt.value;
                    }
                }

                self.form.supplierName=fbInfo.supplierName;
                self.form.supplierWxNum=fbInfo.supplierWxNum;
                self.form.supplierGoodLink=fbInfo.factoryUrl;


                fbInfo.fbSkuList?.forEach(b=>{
                    let skuItem=self.skuTableData.find(s=>s.id==b.skuId);
                    if(skuItem){
                        skuItem.goodsCost=b.fbCostPrice;
                        skuItem.enquiry.winnerFbId = b.id;
                        skuItem.winnerFbId = b.id;
                        self.rowChanged(skuItem,"goodsCost");
                    }
                });

            },
            rowChanged(row, col) {
                if (col === "goodsCost") {
                    //商品成本
                    row.goodsCostEditId = this.selfInfo.id;
                    row.goodsCostEditName = this.selfInfo.nickName;
                    row.goodsCostEditTime = formatTime(new Date(), "YYYY-MM-DD HH:mm:ss");
                } else if (col === "packCostPrice") {
                    //包装成本
                    row.packCostEditId = this.selfInfo.id;
                    row.packCostEditName = this.selfInfo.nickName;
                    row.packCostEditTime = formatTime(new Date(), "YYYY-MM-DD HH:mm:ss");
                } else if(col=="monthSales"){
                    row.monthSaleMoney =Number((( isNaN( row.monthSales)?0:row.monthSales)*( isNaN( row.salePrice)?0:row.salePrice)).toFixed(2));
                } else if(col=="salePrice"){
                    row.monthSaleMoney =Number((( isNaN( row.monthSales)?0:row.monthSales)*( isNaN( row.salePrice)?0:row.salePrice)).toFixed(2));
                }
            },
            async getSkuTableData(hotSaleGoodsChooseId, formEditMode) {
                //this.form = { platform: null };
                let form = this.form;
                // 清空from对象

                Object.keys(form).forEach(key => (form[key] = null));

                //处理编辑模式
                this.formEditMode = formEditMode;
                //1.处理选择按钮显隐，2.备注处理
                //this.skuTableCols[5].display = false;

                // if (formEditMode) {
                //     this.skuTableCols[5].display = formEditMode;
                // }
                this.listLoading = true;
                const res = await getHotSaleGoodsChooseSkuData({ hotSaleGoodsChooseId: hotSaleGoodsChooseId });
                this.listLoading = false;
                // if (res?.data?.list) {
                //     res?.data?.list.forEach(f => {
                //         f.selGoodsCodes = "选择";
                //     });
                // }

                this.total = res?.data?.total;
                this.skuTableData = res?.data?.list;
                this.form = { ...res?.data?.summary?.summary };
                this.$refs.uploadimgFile.setData(this.form.calProfitFiles == null ? [] : this.form.calProfitFiles);
                this.supplierRecords = res?.data?.summary?.supplierRecords;
                this.enquiryInfo=res?.data?.extData?.enquiryInfo;

                this.reftype = res?.data?.summary?.calData?.refType;
                // console.log(res?.data?.summary);
                // console.log(this.form);
                this.$nextTick(() => {
                    this.$refs.jslrForm.clearValidate();
                });

            },
            async saveSkuTableData(isEnd) {
                if(!this.allowSaveBtn()){
                    this.$message.error('请先计算利润数据！月销量、销售额、SKU总成本不能为0。');
                    return false;
                }

                if(!this.form.supplierPlatForm)
                {
                    this.$message.error('请选择供应商平台');
                    return false;
                }

                if(this.form.supplierPlatForm!=1)
                {
                    this.form.supplierWxNum="";
                }
                if(this.form.supplierPlatForm!=2)
                {
                    this.form.supplierLink="";
                    this.form.supplierGoodLink="";
                }
                let listData = [...this.skuTableData];
                listData.forEach(f=>{
                    if(f.monthSales==null)
                        f.monthSales=0;
                    if(f.monthSalesRatio==null)
                        f.monthSalesRatio=0;
                    if(f.salePrice==null)
                        f.salePrice=0;
                    if(f.monthSaleMoney==null)
                        f.monthSaleMoney=0;
                    if(f.goodsCost==null)
                        f.goodsCost=0;
                    if(f.packCostPrice==null)
                        f.packCostPrice=0;
                    if(f.profit==null)
                        f.profit=0;
                });
                let dtoData = {
                    ...this.form,
                    skuProfitDtls: listData
                };
                dtoData.IsSumEnd = isEnd ? 1 : 0;
                if(this.form.supplierPlatForm!=1){this.form.supplierWxNum="";}
                this.listLoading = true;
                const res = await saveHotSaleGoodsChooseSkuData(dtoData);
                this.listLoading = false;
                if (res?.success) {
                    this.$message({ message: '保存成功', type: "success" });
                    return true;
                }
                return false;
            },
            //选择商品
            async onSelGoods(row, col) {
                this.curRow = row;
                this.curCol = col;
                this.goodschoiceVisible = true;
                this.$nextTick(() => {
                    this.$refs.goodschoice.removeSelData();
                });
            },
            //清除商品
            async clearSelGoods(row, col) {
                if (col === "goodsCodes") {
                    row.goodsCodes = '';
                    this.rowChanged(row, 'goodsCost');
                } else if (col == "packCode") {
                    row.packCode = '';
                    this.rowChanged(row, 'packCostPrice')
                }
            },
            //选择商品确定
            async onQueren() {
                //选择半成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                    //产品编码/名称逗号拼接
                    var goodsCodes = "", supplierCodes = "", supplierNames = "";
                    var cost = 0;
                    // goodsCodes=choicelist[0].goodsCode;
                    // supplierCodes=choicelist[0].supplierCode;
                    // supplierNames=choicelist[0].supplierName;
                    // cost=choicelist[0].costPrice;
                    // if(choicelist.length>1)
                    //     this.$message.warning('只能选择一条记录，已默认选取选中记录的首行！');

                    choicelist.forEach(f => {
                        goodsCodes += (f.goodsCode + ",");
                        cost += (f.costPrice ?? 0);
                        if (f.supplierCode)
                            supplierCodes += (f.supplierCode + ",");
                        if (f.supplierName)
                            supplierNames += (f.supplierName + ",");
                    });
                    if (goodsCodes.length > 0) {
                        goodsCodes = goodsCodes.substring(0, goodsCodes.length - 1);
                    }
                    if (supplierCodes.length > 0) {
                        supplierCodes = supplierCodes.substring(0, supplierCodes.length - 1);
                    }
                    if (supplierNames.length > 0) {
                        supplierNames = supplierNames.substring(0, supplierNames.length - 1);
                    }

                    if (this.curCol == "goodsCodes") {
                        this.curRow.goodsCodes = goodsCodes;
                        this.curRow.goodsCost = cost;
                        this.curRow.supplierCode = supplierCodes;
                        this.curRow.supplierName = supplierNames;

                        this.rowChanged(this.curRow, 'goodsCost');
                    } else if (this.curCol == "packCode") {
                        this.curRow.packCode = goodsCodes;
                        this.curRow.packCostPrice = cost;
                        this.rowChanged(this.curRow, 'packCostPrice')

                    }


                    // //计算利润 月销售额-成本*销量
                    // var compMonthSaleMoney = this.curRow.monthSaleMoney ?? 0;
                    // var compCost = this.curRow.goodsCost ?? 0;
                    // var compMonthSales = this.curRow.monthSales ?? 0;
                    // var comp = (compMonthSaleMoney - compCost * compMonthSales).toFixed(2);
                    // this.curRow.profit = comp;
                    this.goodschoiceVisible = false;
                }
            },
            async onShowExproessHttp(row) {
                this.drawervisible = true;
                this.$nextTick(function () {
                    this.$refs.logistics.showlogistics("",row.expressNum);
                })
            }
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }

    ::v-deep .el-input__inner {
        padding-left: 5px;
        padding-right: 5px;
    }

    .overtext{
        max-width: 600px;
        // min-width: 50px;
        // width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
    .commonstyle ::v-deep .el-form-item__label{
        color: #F56C6C;
    }

    /* 商品编码 选择按钮 */
    ::v-deep .el-input-group__append {
        padding-left: 2px;
        padding-right: 2px;
    }
    ::v-deep .el-input-group__append .el-button {
        padding-left: 0px;
        padding-right: 0px;
        margin-left: 0px;
        margin-right: 0px;
    }

    ::v-deep .el-input-group__prepend {
        padding-left: 2px;
        padding-right: 2px;
    }
    ::v-deep .el-input-group__prepend .el-button {
        padding-left: 0px;
        padding-right: 0px;
        margin-left: 0px;
        margin-right: 0px;
    }

</style>
<style  >
    ::v-deep.el-form-item--mini.el-form-item {
        margin-bottom: 0px;
    }

    .el-input__inner {
        padding-left: 5px;
        padding-right: 5px;
    }

    .el-input-number.is-without-controls .el-input__inner {
        padding-left: 2px;
        padding-right: 2px;
    }
</style>
