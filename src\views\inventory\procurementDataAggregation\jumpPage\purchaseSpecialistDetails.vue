<template>
  <my-container>
    <template #header>
      <div style="margin-bottom: 10px;">
        <el-button type="primary" @click="onExport">导出</el-button>
        <el-button style="margin-left: 5px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;"
          @click="onUpdateTime">
          更新时间：{{ renewTime }}
        </el-button>
      </div>
    </template>
    <div id="YunHanAdminGoodspurchaseSpecialistDetails202501181511" v-show="!dataVisible"
      style="width: 100%;height: 100%;">
      <vxetablebase v-show="!dataVisible" :id="'purchaseSpecialistDetails202412311412'" ref="table" :that='that'
        :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;margin: 0"
        :loading="loading" :height="'100%'" @cellStyle="cellStyle" cellStyle :hasSeq="hasSeq">
      </vxetablebase>
    </div>

    <el-dialog title="更新时间" :visible.sync="updateTimeVisible" width="30%" v-dialogDrag>
      <div style="display: flex;">
        <vxetablebase :id="'updateTime202501051635'" :tablekey="'updateTime202501051635'" ref="table2" :that='that'
          :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='updatesortchange' :isNeedExpend="false"
          :tableData='updateTableData' :tableCols='updateTableCols' :isSelection="false" :isSelectColumn="false"
          style="width: 100%;  margin: 0" :loading="updateloading" :height="'250px'">
        </vxetablebase>
      </div>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pageBianMaBrand } from '@/api/inventory/warehouse'
import structuralChange from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/structuralChange'
import setScorecomponent from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/setScorecomponent'
import scoreSetLogDialog from '@/views/inventory/procurementDataAggregation/profit3CommissionPercentage/scoreSetLogDialog'
import { importProfit3CommissionPercentage, getProfit3CommissionPercentageStopList, CalPurchaseSumProfit3BrandStopRpt, getRenewLog } from '@/api/inventory/purchaseSummary';
import dayjs from 'dayjs'
import decimal from '@/utils/decimal'
import { formatTime } from "@/utils";
import html2canvas from 'html2canvas';
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import { saveAs } from 'file-saver'
import exportExecl from "@/utils/exportExecl.js"

const tableCols = [
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '日期', prop: 'rptDate', formatter: (row) => formatTime(row.rptDate, "YYYY-MM") },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '采购员', prop: 'userName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '岗位', prop: 'title' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '区域', prop: 'areaName' },

  {
    istrue: true, summaryEvent: true, rop: '', label: `降价金额得分`, merge: true, headerBgColor: '#d7d6f9',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'allotDownAmount', label: '降价' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'allotDownAmountIndex', label: '降价排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'allotDownAmountScore', label: '降价得分', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `全量扣款得分`, merge: true, headerBgColor: '#d6f6f1',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'deductAmount', label: '扣款金额' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'deductAmountIndex', label: '扣款排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'deductAmountScore', label: '扣款得分', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `采购退滞销金额得分`, merge: true, headerBgColor: '#d7d6f9',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'refundAmount', label: '滞销退款金额' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'refundAmountIndex', label: '退款排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'refundAmountScore', label: '退款得分', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `库存周转得分`, merge: true, headerBgColor: '#d6f6f1',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'brandTurnoverDay', label: '库存周转' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'brandTurnoverDayIndex', label: '周转排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'brandTurnoverDayScore', label: '周转得分', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `扣分项`, merge: true, headerBgColor: '#d7d6f9',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'dayWorkDeductScore', label: '日常工作扣分' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'packingGoodsTaskDeductScore', label: '摘品扣分' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'brandRefundDeductScore', label: '滞销退款扣分' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'allDeductScore', label: '扣分总计', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `全仓缺货得分`, merge: true, headerBgColor: '#d6f6f1',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'fullWmsMonthLackCount', label: '全仓缺货次数' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'fullWmsMonthLackCountIndex', label: '全仓缺货排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'fullWmsMonthLackCountScore', label: '全仓缺货得分', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `本仓缺货得分`, merge: true, headerBgColor: '#d7d6f9',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'inWmsMonthLackCount', label: '本仓缺货次数' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'inWmsMonthLackCountIndex', label: '本仓缺货排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'inWmsMonthLackCountScore', label: '本仓缺货得分', headerBgColor: '#FFFF00' },
    ]
  },
  {
    istrue: true, summaryEvent: true, rop: '', label: `云仓缺货得分`, merge: true, headerBgColor: '#d6f6f1',
    cols: [
      { sortable: 'custom', istrue: true, width: '100', prop: 'wmsMonthLackCount', label: '云仓缺货次数' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'wmsMonthLackCountIndex', label: '云仓缺货排名' },
      { sortable: 'custom', istrue: true, width: '100', prop: 'wmsMonthLackCountScore', label: '云仓缺货得分', headerBgColor: '#FFFF00' },
    ]
  },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '总得分', prop: 'allScore' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '销售额提成百分比', prop: 'brandProfit3Rate', headerBgColor: '#FFFF00' },
];

const updateTableCols = [
  // { sortable: 'custom', istrue: true, align: 'center', label: '更新时间', prop: 'rptDate' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '操作类型', prop: 'calType' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '操作人', prop: 'operateUserName' },
  { sortable: 'custom', istrue: true, width: 'auto', align: 'center', label: '更新时间', prop: 'operateTime' },
];

export default {
  name: "purchaseSpecialistDetails",
  components: {
    MyContainer, vxetablebase, structuralChange, setScorecomponent, scoreSetLogDialog
  },
  data() {
    return {
      hiddenColumns: [],
      hasSeq: true,
      that: this,
      updateTableData: [],//更新时间
      updateTableCols: updateTableCols,//更新时间列
      updateloading: false,//更新时间加载
      tableCols: tableCols,
      tableData: [],
      startTime: null,//计算日期
      uploadLoading: false,//导入加载
      importVisible: false,//导入弹窗显示
      importMonth: null,//导入月份
      fileList: [],//上传文件列表
      changePostVisible: false,//架构变动
      scoreLogVisible: false,//分值日志
      updateTimeVisible: false,//更新时间
      computeVisible: false,//计算
      updateTimeInfo: {
        type: '个人-试算',
        orderBy: 'operateTime',
        isAsc: false,
      },
      userNameList: [],//采购员
      buyerList: [],//采购员
      rptDate: null,//月份
      filter: {
        currentPage: 1,
        pageSize: 99999,
        orderBy: 'brandProfit3Rate',
        isAsc: false,
        //过滤条件
        rptDate: null,//月份
        brandIdList: [],//采购员
        titleList: [],//岗位
        areaNameList: [],//区域
      },
      setScoreVisible: false,//分值设置弹窗
      renewTime: null,//更新时间
      loading: false,
      total: 0,
      dataURL: '',
      dataVisible: false,
      loadingUrl: false,
    };
  },
  async mounted() {
    let _this = this;
    _this.rptDate = _this.$route.query.rptDate;
    if (_this.rptDate != 0) {
      _this.filter.rptDate = _this.rptDate;
      _this.getList()
    }
  },
  methods: {
    async onExport() {
      this.loading = true;
      await new Promise(resolve => setTimeout(resolve, 500));
      this.hasSeq = false
      setTimeout(async () => {
        const endDate = formatTime(dayjs(), "YYYY-MM-DD");
        exportExecl("YunHanAdminGoodspurchaseSpecialistDetails202501181511", `专员明细数据` + endDate + '.xlsx');
        this.hasSeq = true
        await new Promise(resolve => setTimeout(resolve, 500));
        this.loading = false;
      }, 1000);
    },
    async cellStyle(row, column, callback) {
      if (column.field === 'userName' && row.isLastBrandProfit3Rate) {
        callback({ color: '#FF0000' });
      }
      if (column.field === 'allotDownAmountScore' || column.field === 'deductAmountScore' || column.field === 'refundAmountScore' || column.field === 'brandTurnoverDayScore' || column.field === 'allDeductScore' || column.field === 'wmsMonthLackCountScore' || column.field === 'inWmsMonthLackCountScore' || column.field === 'fullWmsMonthLackCountScore' || column.field === 'brandProfit3Rate') {
        callback({ backgroundColor: '#FFFF00' });
      }
    },
    // 排序查询
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.filter.currentPage = 1
      }
      const firstDayOfMonth = this.rptDate ? dayjs(this.rptDate).startOf('month').format('YYYY-MM-DD') : ''
      const params = { ...this.filter, rptDate: firstDayOfMonth };
      this.loading = true
      const { data, success } = await getProfit3CommissionPercentageStopList(params)
      if (success) {
        this.tableData = data.list
        this.renewTime = data.extData.lastRenewTime
        this.tableData.forEach(item => {
          item.rptDate = dayjs(item.rptDate).format('YYYY-MM')
        })
        this.total = data.total
        this.summaryarry = data.summary
        let hiddenList = data.extData.hiddenRows
          .filter(item => item.includes("得分"))
          .map(item => {
            if (item === "退款得分") {
              return item.replace("款得分", "");
            }
            return item.replace("得分", "");
          });
        this.onShowupMethod(hiddenList);
        this.loading = false
      } else {
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    async onUpdateTime() {
      this.updateloading = true
      const { data, success } = await getRenewLog({ rptDate: this.filter.rptDate, ...this.updateTimeInfo })
      this.updateloading = false
      if (!success) return
      this.updateTableData = data.list
      this.updateTimeVisible = true;
    },
    updatesortchange({ order, prop }) {
      if (prop) {
        this.updateTimeInfo.orderBy = prop
        this.updateTimeInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.onUpdateTime()
      }
    },
    async onShowupMethod(list) {
      this.$nextTick(() => {
        const tableRef = this.$refs.table;
        if (tableRef) {
          let takePlace = [];
          let payment = [];
          this.tableCols.forEach(item => {
            if (list.some(substring => item.label.includes(substring))) {
              if (item.cols && Array.isArray(item.cols)) {
                item.cols.forEach(col => {
                  payment.push(col.prop);
                });
              } else {
                payment.push(item.prop);
              }
            } else {
              if (item.cols) {
                item.cols.forEach(col => {
                  takePlace.push(col.prop);
                });
              } else {
                takePlace.push(item.prop);
              }
            }
          });
          this.hiddenColumns = payment
          tableRef.changecolumn(payment);
          tableRef.changecolumn_setTrue(takePlace);
        }
        this.$forceUpdate();
      });
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 40px;
}

.dialogcss ::v-deep .body--wrapper {
  height: auto !important;
}
</style>
