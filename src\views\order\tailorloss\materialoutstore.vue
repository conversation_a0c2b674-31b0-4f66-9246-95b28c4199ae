<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="出库时间">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始出库时间" end-placeholder="结束出库时间"
                        :picker-options="pickerOptions">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.goodsCodeHalf" style="width: 140px" placeholder="半成品编码"
                        @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.outStoreUser" style="width: 140px" placeholder="出库操作人"
                        @keyup.enter.native="onSearch" clearable maxlength="50" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入" :visible.sync="dialogVisibleUpload" width="40%" v-dialogDrag :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :http-request="onUploadFile" :on-change="uploadChange"
                            :on-remove="uploadRemove" :on-success="onUploadSuccess">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleUpload = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getMaterialOutStorePageList, importMaterialOutStore, enabledMaterialOutStore } from '@/api/order/tailorloss';
const tableCols = [
    { istrue: true, prop: 'outStoreOrderNo', label: '出库单号', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'outStoreTime', label: '出库时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsCodeHalf', label: '半成品编码', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsNameHalf', label: '半成品名称', width: '400' },
    { istrue: true, prop: 'outStoreUser', label: '出库操作人', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsCount', label: '商品数量', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'outStoreTotalArea', label: '出库总面积㎡', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'enabled', label: '状态', width: '80', sortable: 'custom', formatter: (row) => row.enabled == true ? "禁用" : "启用" },
    {
        istrue: true, type: 'button', label: '操作', width: '80',
        btnList: [
            {
                label: "禁用", ishide: (that, row) => row.enabled, handle: (that, row) => that.onEnabled(row, true)
            },
            {
                label: "启用", ishide: (that, row) => !row.enabled, handle: (that, row) => that.onEnabled(row, false)
            },
        ]
    }
]
const tableHandles1 = [
    { label: "导入", handle: (that) => that.onImport() },
];
export default {
    name: 'materialoutstore',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
    props: {

    },
    data() {
        return {
            that: this,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                    formatTime(new Date(), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "outStoreTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,
            dialogVisibleUpload: false,
            fileList: [],
            fileparm: {},
            uploadLoading: false,
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择出库时间", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择时间", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getMaterialOutStorePageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onImport() {
            this.dialogVisibleUpload = true;
        },
        async uploadChange(file, fileList) {
            var list = [];
            if (fileList && fileList.length > 0) {
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
            }
            this.fileList = list;
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await importMaterialOutStore(form);
            if (res?.success)
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
            this.uploadLoading = false
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.dialogVisibleUpload = false;
        },
        onSubmitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
        async onEnabled(row, enabled) {
            var res = await enabledMaterialOutStore({
                outStoreOrderNo: row.outStoreOrderNo,
                goodsCodeHalf: row.goodsCodeHalf,
                enabled: enabled
            });
            if (res?.success) {
                this.$message({ message: "操作成功", type: "success" });
                await this.onSearch();
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
