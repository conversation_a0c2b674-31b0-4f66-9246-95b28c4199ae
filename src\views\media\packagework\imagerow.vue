<template>
 <div>
     <!-- :http-request="UpSuccessload('packingImg',$event)" -->

  <el-row :gutter="20" v-loading="loading">
   <el-col :span="6"><div class="flexcenter" style="padding: 5px;">
    <div class="imgstyle marbot">
      <el-image  class="imgstyle marbot" :preview-src-list="[jstimgnei]" :src="jstimgnei" fit="fill" ></el-image>
    </div>
     <div class="marbot"> 聚水潭图片</div>
     <el-upload
     class="marbot"
     action ="#"
     ref="upload1"
     disabled
     :auto-upload="true"
     :limit="limit"
     :show-file-list ="false"
     :accept ="'.jpg,.jpeg,.png,.webp,.gif'"
     :http-request="(file)=>{return UpSuccessload('packingImg',file)}"
     :on-exceed ="exceed"
     :file-list="retdata"
     :before-upload="beforeAvatarUpload">
     <!-- <el-button size="small" type="primary">点击上传</el-button> -->
     <div class="marbot"><el-button type="primary" :disabled="true">上传图片</el-button></div>
   </el-upload>

     <div class="marbot"><el-button type="danger" :disabled="true" plain>删除图片</el-button></div>
   </div></el-col>
   <el-col :span="6"><div class="flexcenter" style="padding: 5px;">
    <!-- {{ updata.packingImg }} -->
    <div class="imgstyle marbot">
      <el-image  class="imgstyle marbot" :preview-src-list="[updata.packingImg]" v-if="updata.packingImg" :src="updata.packingImg" fit="fill" ></el-image>
    </div>
     <div class="marbot"> 包装图片</div>
     <el-upload
     class="marbot"
     ref="upload2"
     action ="#"
     :auto-upload="true"
     :limit="limit"
     :show-file-list ="false"
     :accept ="'.jpg,.jpeg,.png,.webp,.gif'"
     :http-request="(file)=>{return UpSuccessload('packingImg',file)}"
     :on-exceed ="exceed"
     :file-list="retdata"
     :before-upload="beforeAvatarUpload">
     <!-- <el-button size="small" type="primary">点击上传</el-button> -->
     <div class="marbot"><el-button type="primary">上传图片</el-button></div>
   </el-upload>
     <div class="marbot"><el-button type="danger" plain @click="delimg('packingImg')">删除图片</el-button></div>
   </div></el-col>
   <el-col :span="6"><div class="flexcenter" style="padding: 5px;">
    <div class="imgstyle marbot">
      <el-image  class="imgstyle marbot" :preview-src-list="[updata.productImg]" v-if="updata.productImg" :src="updata.productImg" fit="fill" ></el-image>
    </div>
     <div class="marbot"> 产品图片</div>
     <el-upload
     class="marbot"
     action ="#"
     ref="upload3"
     :auto-upload="true"
     :limit="limit"
     :show-file-list ="false"
     :accept ="'.jpg,.jpeg,.png,.webp,.gif'"
     :http-request="(file)=>{return UpSuccessload('productImg',file)}"
     :on-exceed ="exceed"
     :file-list="retdata"

     :before-upload="beforeAvatarUpload">
     <!-- <el-button size="small" type="primary">点击上传</el-button> -->
     <div class="marbot"><el-button type="primary">上传图片</el-button></div>
   </el-upload>
     <div class="marbot"><el-button type="danger" plain @click="delimg('productImg')">删除图片</el-button></div>
   </div></el-col>
   <el-col :span="6"><div class="flexcenter" style="padding: 5px;">
    <div class="imgstyle marbot">
      <el-image  class="imgstyle marbot" :preview-src-list="[updata.certificateImg]" v-if="updata.certificateImg" :src="updata.certificateImg" fit="fill" ></el-image>
    </div>
     <div class="marbot"> 合格证图片</div>
     <el-upload
     class="marbot"
     action ="#"
     ref="upload4"
     :auto-upload="true"
     :limit="limit"
     :show-file-list ="false"
     :accept ="'.jpg,.jpeg,.png,.webp,.gif'"
     :http-request="(file)=>{return UpSuccessload('certificateImg',file)}"
     :on-exceed ="exceed"
     :file-list="retdata"
     :before-upload="beforeAvatarUpload">
     <!-- <el-button size="small" type="primary">点击上传</el-button> -->
     <div class="marbot"><el-button type="primary">上传图片</el-button></div>
   </el-upload>
     <div class="marbot"><el-button type="danger" plain @click="delimg('certificateImg')">删除图片</el-button></div>
   </div></el-col>
 </el-row>

 </div>
</template>

<script>
export default {
 name: 'Vue2demoImagerow',
//  props: ['jstimgnei','updatapro'],
 props: {
  jstimgnei: {type: String,default: ''},
  updatapro: {type: Object,default: {}},

 },
 // jstimg: "https://img.alicdn.com/imgextra/i2/31217340/O1CN01vddoON245ltQoJPDn_!!31217340.png",

 data() {
  return {
   limit: 1,
   retdata: [],
   updata: {
     packingImg: "",
     productImg: "",
     certificateImg: "",
     disproductImg: false,
     discertificateImg: false,
     dispackingImg: false,
   }
  };
 },

 mounted() {
  // this.updata = this.updatapro||{
  //    packingImg: "",
  //    productImg: "",
  //    certificateImg: "",
  //  };
 },
 watch: {
  updatapro:{
      handler(newName,oldName){
          this.updata = this.updatapro||{
            packingImg: "",
            productImg: "",
            certificateImg: "",
          };

          this.disproductImg= false;
          this.discertificateImg= false;
          this.dispackingImg= false;
      },
      deep:true
    }
 },

 methods: {
  delimg(e){
   // return
    if(e=='packingImg'){
      this.updata.packingImg = '';
      // this.$refs.upload.clearFiles();
      this.clearlist();
    }else if(e=='productImg'){
      this.updata.productImg = '';
      // this.$refs.upload.clearFiles();
      this.clearlist();
    }else if(e=='certificateImg'){
      this.updata.certificateImg = '';
      // this.$refs.upload.clearFiles();
      this.clearlist();
    }
    this.$emit("getimglist",this.updata);
  },
  exceed(files, fileList) {
    let _this = this;
    // console.log("44444",_this.$refs.upcertificateImg.uploadFiles)
    // if(_this.$refs.upcertificateImg.uploadFiles==0){

    // }else{
     this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    // }

  },
  beforeAvatarUpload(file){
       if (this.uploadVerifyType == "图片") {
           let files=['image/jpeg','image/jpg','image/png','image/gif','image/webp'];
           if(files.indexOf(file.type)==-1)
           {
               this.$message.info('不支持的文件格式！');
               return false;
           }
       }
   },
   UpSuccessload(name,e){
    // return
    let _this = this;
    _this.uploadToServer(e.file, (res) => {
        // _this.canvasimg.push(res.data.url);

        if(res.success){
          if(name == 'packingImg'){
            // this.dispackingImg = false;
            this.updata.packingImg = res.data.url
            this.dispackingImg = true;
            this.$forceUpdate();
          }else if(name == 'productImg'){
           this.updata.productImg = res.data.url
           this.disproductImg = true;
           this.$forceUpdate();
          }else if(name == 'certificateImg'){
           this.updata.certificateImg = res.data.url
           this.discertificateImg = true;
           this.$forceUpdate();
          }
          this.$emit("getimglist",this.updata);
          this.$nextTick(()=>{
            this.clearlist();
          });

        }
    })

   },
   clearlist(){
    // this.$refs.upload1.clearFiles();
    this.$refs.upload2.clearFiles();
    this.$refs.upload3.clearFiles();
    this.$refs.upload4.clearFiles();

   },
   uploadToServer(file, callback) {
        var xhr = new XMLHttpRequest()
        var formData = new FormData()
        formData.append('file', file)
        xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
        xhr.withCredentials = true
        xhr.responseType = 'json'
        xhr.send(formData)
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4 && xhr.status === 200) {
                callback(xhr.response)
            }
        }
    },
 },
};
</script>

<style lang="scss" scoped>
.flexcolmn{
 display: flex;
 flex-direction: column;
 justify-content: center;
 width: 100%;
 height: 100%;
}

.flexcenter{
 display: flex;
 justify-content: center;
 align-items: center;
 flex-direction: column;
}

.marbot{
 margin-bottom: 10px;
}

.imgstyle{
 height: 70px;
//  margin-top: 20px;
}
</style>
