<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="110px" class="demo-ruleForm">
        <div class="city-name">南昌:</div>
        <el-form-item label="员工餐" prop="ncEm">
          <inputNumberYh v-model="ruleForm.ncEm" :placeholder="'南昌员工餐'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <el-form-item label="员工生日会" prop="ncEbp">
          <inputNumberYh v-model="ruleForm.ncEbp" :placeholder="'南昌员工生日会'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <div class="city-name">义乌:</div>
        <el-form-item label="员工餐" prop="ywEm">
          <inputNumberYh v-model="ruleForm.ywEm" :placeholder="'义乌员工餐'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <el-form-item label="员工生日会" prop="ywEbp">
          <inputNumberYh v-model="ruleForm.ywEbp" :placeholder="'义乌员工生日会'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <div class="city-name">武汉:</div>
        <el-form-item label="员工餐" prop="whEm">
          <inputNumberYh v-model="ruleForm.whEm" :placeholder="'武汉员工餐'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <el-form-item label="员工生日会" prop="whEbp">
          <inputNumberYh v-model="ruleForm.whEbp" :placeholder="'武汉员工生日会'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <div class="city-name">深圳:</div>
        <el-form-item label="员工餐" prop="szEm">
          <inputNumberYh v-model="ruleForm.szEm" :placeholder="'深圳员工餐'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <el-form-item label="员工生日会" prop="szEbp">
          <inputNumberYh v-model="ruleForm.szEbp" :placeholder="'深圳员工生日会'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <div class="city-name">选品中心:</div>
        <el-form-item label="员工餐" prop="xpEm">
          <inputNumberYh v-model="ruleForm.xpEm" :placeholder="'选品中心员工餐'" class="publicCss" :fixed="2"   />
        </el-form-item>
        <el-form-item label="员工生日会" prop="xpEbp">
          <inputNumberYh v-model="ruleForm.xpEbp" :placeholder="'选品中心员工生日会'" class="publicCss" :fixed="2"   />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { satisfactionRatingSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        ncEm: '',//南昌员工餐
        ncEbp: '',//南昌员工生日会
        ywEm: '',//义乌员工餐
        ywEbp: '',//义乌员工生日会
        whEm: '',//武汉员工餐
        whEbp: '',//武汉员工生日会
        szEm: '',//深圳员工餐
        szEbp: '',//深圳员工生日会
        xpEm: '',//选品中心员工餐
        xpEbp: '',//选品中心员工生日会
      },
      rules: {
        ncEm: [
          { required: true, message: '请输入南昌员工餐', trigger: 'blur' },
        ],
        ncEbp: [
          { required: true, message: '请输入南昌员工生日会', trigger: 'blur' },
        ],
        ywEm: [
          { required: true, message: '请输入义乌员工餐', trigger: 'blur' },
        ],
        ywEbp: [
          { required: true, message: '请输入义乌员工生日会', trigger: 'blur' },
        ],
        whEm: [
          { required: true, message: '请输入武汉员工餐', trigger: 'blur' },
        ],
        whEbp: [
          { required: true, message: '请输入武汉员工生日会', trigger: 'blur' },
        ],
        szEm: [
          { required: true, message: '请输入深圳员工餐', trigger: 'blur' },
        ],
        szEbp: [
          { required: true, message: '请输入深圳员工生日会', trigger: 'blur' },
        ],
        xpEm: [
          { required: true, message: '请输入选品中心员工餐', trigger: 'blur' },
        ],
        xpEbp: [
          { required: true, message: '请输入选品中心员工生日会', trigger: 'blur' },
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await satisfactionRatingSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}

.city-name {
  font-size: 17px;
  font-weight: bold;
  margin: 0 0 5px 5%;
}
</style>
