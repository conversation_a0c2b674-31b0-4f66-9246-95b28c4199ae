<template>
    <MyContainer>
        <template #header>
            <el-form :model="ruleForm" status-icon style="width: 100%;" ref="ruleForm" label-width="100px"
                class="demo-ruleForm">
                <el-form-item label="备注:" prop="pass">
                    <el-input type="textarea" :rows="4" placeholder="请输入备注" v-model="ruleForm.remark" maxlength="500"
                        show-word-limit style="width: 100%;" />
                </el-form-item>
                <el-form-item label="图片:" prop="checkPass">
                    <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :accepttyes="accepttyes" isImage
                        :uploadInfo="chatUrls" :keys="[1, 1]" @callback="getImg" :imgmaxsize="9" :limit="9"
                        :multiple="true">
                    </uploadimgFile>
                </el-form-item>
            </el-form>
        </template>
        <div class="top" v-if="!isBatch">
            <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
            <div>
                <el-button type="primary" @click="getList('search')">搜索</el-button>
            </div>
        </div>
        <vxetablebase ref="table" :loading="loading" id="20241210161050" :that="that" :is-index="true" :hasexpand="true"
            :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            :is-selection="false" :is-select-column="true" :is-index-fixed="false" :isNeedExpend="false"
            style="width: 100%; margin: 0;height: 400px;" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary" @sortchange="sortchange" v-if="!isBatch" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange"
                v-if="!isBatch" />
            <div class="btnGroup">
                <el-button @click="$emit('close')">取消</el-button>
                <el-button type="primary" @click="submitLog">保存</el-button>
            </div>
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/BrandRemark/'
import { mergeTableCols } from '@/utils/getCols'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, uploadimgFile
    },
    props: {
        query: {
            type: Object,
            default: {}
        },
        isBatch: {
            type: Boolean,
            default: false
        },
        batchProps: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createTime',
                isAsc: false,
                summarys: [],
                goodsCode: this.query.goodsCode,
                brandId: this.query.brandId
            },
            ruleForm: {
                remark: "",
                picture: "",
                goodsCode: this.query.goodsCode,
                brandId: this.query.brandId
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            editPriceVisible: false,
            chatUrls: [],
        }
    },
    async mounted() {
        if (!this.isBatch) {
            await this.getCol();
            await this.getList()
        }
        this.editPriceVisible = true
    },
    methods: {
        async submitLog() {
            if (!this.isBatch) {
                const { success } = await request.post(`${this.api}Modify`, this.ruleForm)
                if (success) {
                    this.$message.success('保存成功')
                    this.$emit('close')
                    this.$emit('getList')
                }
            } else {
                const res = this.batchProps.map(item => {
                    return {
                        goodsCode: item.goodsCode,
                        remark: this.ruleForm.remark,
                        picture: this.ruleForm.picture,
                        brandId: item.brandId
                    }
                })
                const { success } = await request.post(`${this.api}BatchModify`, res)
                if (success) {
                    this.$message.success('保存成功')
                    this.$emit('close')
                    this.$emit('getList')
                }
            }
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.picture = data.map(item => item.url).join(',')
            }
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    data.list.forEach(item => {
                        item.picture = JSON.stringify(item.picture ? item.picture.split(',').map(item => ({ url: item })) : [])
                    })
                    this.data = data;
                }
            } catch (error) {
                console.log(error);
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
