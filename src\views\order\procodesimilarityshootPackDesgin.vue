<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :showsummary='false'
      :tablefixed='true' :summaryarry='summaryarry' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles'
      :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getlist" />
    </template>
    <!--创建任务-->
    <div class="dialog1">
      <el-dialog title="" :visible.sync="addTask" style="position: fixed; top: -60px;" width="750px" :show-close="false"
        :before-close="handleClose" append-to-body element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
        <packdesgintaskaddfrom ref="microVediotaskaddfrom" v-if="addTask" :onCloseAddForm="onCloseAddForm"
          :taskUrgencyList="taskUrgencyList" :groupList="groupList" :packclasslist="packclasslist" :brandList="brandList"
          :warehouselist="warehouselist" :userList="fpPhotoLqNameList" :platformList="platformList" :islook='false'
          :iscodeCreate="true"></packdesgintaskaddfrom>
      </el-dialog>
    </div>
  </my-container>
</template>

<script>
import packdesgintaskaddfrom from '@/views/media/shooting/packdesgin/packdesgintaskaddfrom';
import { formatmoney, formatTime, formatWarehouse as formatWarehousePublic } from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { pagePurchasePlan2 } from '@/api/inventory/purchase'
import { pageProCodeSimilarityGoods } from "@/api/order/procodesimilarity"
const tableCols = [
  { istrue: true, prop: 'images', label: '图片', width: '60', fixed: true, type: 'image' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '150', fixed: true, sortable: 'custom', type: 'click', handle: (that, row) => that.directgoodsCode(row.goodsCode) },
  { istrue: true, prop: 'brandId', label: '采购员', width: '65', fixed: true, type: 'html', formatter: (row) => { return `<a href="javascript:void(0);" style="font-size:small;color:#606266";">${row.brandName ?? ''}</a>`; } },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '180', fixed: true, sortable: 'custom' },
  { istrue: true, prop: 'packDsginIds', label: '任务编号', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'useableCount', label: '实际可用数', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'goodsLable', label: '商品标签', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'weight', label: '商品重量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay3', label: '3天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay7', label: '7天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay15', label: '15天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay30', label: '30天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay45', label: '45天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay60', label: '60天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay90', label: '90天日均销', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'avgSalesDay120', label: '120天日均销', width: '100', sortable: 'custom' },
  { istrue: true, type: 'button', width: '80', btnList: [{ label: "包装设计", display: (row) => { return false; }, handle: (that, row) => that.onAddTask(row) }] },
];
const tableHandles = [];
//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
  var money = formatmoney(
    Math.abs(value) > 1 ? Math.round(value, 2) : Math.round(value, 1)
  );
  return money
};
var formatWarehouse = function (warehouse) {
  if (warehouse == -1) return "全仓";
  return formatWarehousePublic(warehouse);
};
export default {
  name: 'Roles',
  components: { cesTable, MyContainer, MyConfirmButton, packdesgintaskaddfrom },
  props: {
    codeinfo: { type: Object, default: {  } },//
    codefilter: { type: Object, default: { goodsCode: null } },//
    taskUrgencyList: { type: Array, default: [] }, //平台
    platformList: { type: Array, default: [] }, //平台
    warehouselist: { type: Array, default: [] }, //仓库
    packclasslist: { type: Array, default: [] }, //仓库 
    dockingPeopleList: { type: Array, default: [] }, //对接人
    fpPhotoLqNameList: { type: Array, default: [] }, //分配查询
    groupList: { type: Array, default: [] }, //运营组 
    tablekey: { type: String, default: "" },//   
    brandList: { type: Array, default: [] }, // 
  },
  data() {
    return {
      that: this,
      listLoading: false,
      pageLoading: false,
      list: [],
      total: 0,
      addTask: false,
      addLoading: false,
      tableCols: tableCols,
      tableHandles: tableHandles,
      pager: { OrderBy: "", IsAsc: false },
      summaryarry: {},
      filter: {
        goodsCodes: [],
      },
    };
  },
  async mounted() {
    await this.getlist();
  },
  methods: {
    handleClose() {
      this.$confirm("是否确定关闭窗口", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.addTask = false;
      });
    },
    //新增编辑窗口关闭
    async onCloseAddForm(type) { 
        this.addTask = false; 
    },
    //新增任务
    async onAddTask(row) {
      this.addTask = true;
      this.$nextTick(async () => {
        var tt = {
          ...this.codeinfo, 
          goodsName :row.goodsName,
          goodsCode:row.goodsCode,
        }
        this.$refs.microVediotaskaddfrom.initSomething(tt); 
      });
    
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    //获取查询条件
    getCondition() {
      var ret = {
        pageSize: 100,
        currentPage: 1,
        ...this.codefilter
      }
      //通过系列编码获取商品编码

      return ret;
    },
    async getlist() {
      var retparams = this.getCondition();
      var retsercodes = await pageProCodeSimilarityGoods(retparams);
      if (!retsercodes?.success) return;
      let string = "";
      retsercodes.data.list.forEach((item, index) => {
        if (index > 0) {
          string = string + "," + item.goodsCode
        } else {
          string = item.goodsCode
        }
      })

      this.filter.goodsCode = string ==""?"------------":string;
      this.filter.newWarehouses = -1;
      var pager = this.$refs.pager.getPager()
      let params = { ...pager, ...this.pager, ... this.filter }
      this.listLoading = true
      let res = await pagePurchasePlan2(params)
      this.listLoading = false
      if (!res?.success) return
      this.total = res.data.total;

      this.list = res.data.list;
      this.summaryarry = res.data.summary;
    },
  }

};
</script>
<style scoped>
::v-deep .el-link.el-link--primary {
  margin-right: 7px;
}

::v-deep .el-table__fixed {
  pointer-events: auto;
}

.dialog1 ::v-deep .el-dialog__body {
  padding: 0 !important;
}

.dialog1 ::v-deep .el-drawer__header {
  display: none !important;
}

::v-deep .myheader {
  padding: 5px 0px 0px 5px;
}
</style>
