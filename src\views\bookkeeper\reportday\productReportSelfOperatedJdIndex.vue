<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="京东自营入仓日报" name="first1" style="height: 100%">
        <productReportSelfOperatedJd ref="productReportSelfOperatedJd" style="height: 100%" />
      </el-tab-pane>
      <el-tab-pane label="京东自营商品明细" name="first2" :lazy="true" style="height: 100%;">
        <selfOperatedProductDetailsJd ref="refselfOperatedProductDetailsJd" style="height: 100%;" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportSelfOperatedJd from "./productReportSelfOperatedJd.vue";
import selfOperatedProductDetailsJd from "./selfOperatedProductDetailsJd.vue";
export default {
  name: "productReportSelfOperatedJdIndex",
  components: {
    MyContainer, productReportSelfOperatedJd, selfOperatedProductDetailsJd
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
};
</script>

<style lang="scss" scoped></style>
