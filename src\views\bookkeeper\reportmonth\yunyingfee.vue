<template>
    <my-container style="height: 100%">
        <el-tabs v-model="activeName" style="height: 94%">

            <el-tab-pane label="拼多多推广费" name="yunyingfeepdd" style="height: 100%">
                <yunyingfeepdd ref="yunyingfeepdd" style="height: 100%">
                </yunyingfeepdd>
            </el-tab-pane>

            <el-tab-pane label="淘系推广费" name="yunyingfeetx" style="height: 100%" lazy>
                <yunyingfeetx ref="yunyingfeetx" style="height: 100%">
                </yunyingfeetx>
            </el-tab-pane>

        </el-tabs>
    </my-container>
</template>

<script>

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import yunyingfeepdd from "./yunyingfeepdd.vue";
import yunyingfeetx from "./yunyingfeetx.vue";

export default {
    name: "yunyingfee",
    components: {
        cesTable,
        MyContainer,
        MyConfirmButton,
        yunyingfeepdd, yunyingfeetx,
    },
    data() {
        return {
            that: this,
            activeName: "yunyingfeepdd",
        };
    },
    async mounted() {
    },
    methods: {
    },
};
</script>

<style lang="scss" scoped></style>