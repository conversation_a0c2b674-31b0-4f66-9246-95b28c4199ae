<template>
    <my-container v-loading="pageLoading">
        <!-- 列表 -->
        <template #header>
            <el-button-group>

                <el-button style="padding: 0;margin: 0; margin-right: 5px;">
                    <el-date-picker style="width: 320px" v-model="orderPayTimeRange" type="daterange" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="付款时间" 
                        end-placeholder="结束时间" :picker-options="pickerOptions"  >
                    </el-date-picker>
                    <el-checkbox label="以结束时间为起点" v-model="isChecked" @change="changeJudgeTime" style="width:160px;"></el-checkbox>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 5px;">
                    <el-input v-model="filter.productID" v-model.trim="filter.productID" placeholder="商品ID(多个ID，隔开)"></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 5px;">
                    <el-select v-model="filter.shop" placeholder="店铺" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in shopList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 5px;">
                    <el-select v-model="filter.leader" placeholder="组长" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in leaderList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 5px;">
                    <el-select v-model="filter.commissioner" placeholder="专员" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in commissionerList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 5px;">
                    <el-select v-model="filter.assistant" placeholder="助理" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in assistantList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 5px;">
                    <el-select v-model="filter.refundReason" placeholder="买家退款原因" multiple clearable collapse-tags filterable>
                        <el-option v-for="item in refundReasonList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>

            </el-button-group>
        </template>

        <vxetablebase :id="'IDWarning202408111325'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' 
            @sortchange='sortchange' :tableData='allRefundDataList' :tableCols='tableCols' :isSelection='false' :isSelectColumn="false" 
            :summaryarry="summaryarry" :showsummary='true' style="width: 100%;margin: 0" v-loading="listLoading" 
            :height="'100%'" >
        </vxetablebase>
        
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" 
            @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <!-- 趋势图 -->
        <el-dialog :title="dialogVisible.title" :visible.sync="dialogVisible.visible" width="80%" 
            :close-on-click-model="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogVisible.visible" :analysisData="dialogVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import { getIDWarning, exportIDWarning, getIDWarningMap, getShopNameInProductEntity, getLeaderNameInProductEntity, getCommissionerNameInProductEntity, getAssistantNameInProductEntity, getRefundReason } from "@/api/operatemanage/refundData";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import buschar from "@/components/Bus/buschar";

const tableCols = [
    // {  istrue: true, width: '60', align: 'center', type: "checkbox" },
    {  istrue: true, width: '100', align: 'center', label: '商品ID', prop: 'productID', display: true, style: "color:blue;cursor:pointer;", type: 'click', handle: (that, row) => that.showchart(row) },
    {  istrue: true, width: '170', align: 'center', label: '宝贝标题', prop: 'proTitle' },
    {  istrue: true, width: '170', align: 'center', label: '店铺', prop: 'shop' },
    {  istrue: true, width: '110', align: 'center', label: '组长', prop: 'leader' },
    {  istrue: true, width: '100', align: 'center', label: '专员', prop: 'commissioner' },
    {  istrue: true, width: '100', align: 'center', label: '助理', prop: 'assistant' },
    {  istrue: true, width: '100', align: 'center', label: '交易金额', prop: 'transactionAmount' },
    
    {  istrue: true, width: '100', align: 'center', label: '订单量', prop: 'orderVolumn' },
    {  istrue: true, width: '100', align: 'center', label: '退单量', prop: 'refundOrderVolumn' },

    {  istrue: true, width: '100', align: 'center', label: '退款金额', prop: 'buyerRefundAmount' },
    {  istrue: true, width: '100', align: 'center', label: '退款率', prop: 'refundPercentage', type: "colorClick", 
        style: (that, row) => that.refundPercentageFormatter(row.refundPercentage),
        formatter:(row)=> (row.refundPercentage*100).toFixed(2)+"%"
    },
    {  istrue: true, width: '100', align: 'center', label: '发错货退单量', prop: 'wrongGoodsRefundOrderVolumn' },
    {  istrue: true, width: '100', align: 'center', label: '发错货退单金额', prop: 'wrongGoodsRefundOrderAmount' },
    {  istrue: true, width: '100', align: 'center', label: '发错货退单率', prop: 'wrongGoodsRefundPercentage', formatter:(row)=> (row.wrongGoodsRefundPercentage*100).toFixed(2)+"%" },
    {  istrue: true, width: '100', align: 'center', label: '持续上升', prop: 'persistent' },
];

export default {
    components: { MyContainer, cesTable, vxetablebase, buschar },
    data() {
        return {
            dialogVisible: { visible: false, title: "", data: [] },
            pageLoading: false,
            listLoading: false,
            orderPayTimeRange: [],//订单付款时间
            filter: {//过滤条件
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                //过滤条件
                beginOrderPayTime: null,//付款时间开始
                endOrderPayTime: null,//付款时间结束
                productID: null,//商品ID
                shop: [],//店铺
                leader: [],//组长
                commissioner: [],//专员
                assistant: [],//助理
                refundReason: [],//买家退款原因
                judgeTime: 'start',//持续上升判断时间
            },
            isChecked: false,//是否选中“以结束时间为起点”
            chartFilter: {//趋势图过滤条件
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                //过滤条件
                beginOrderPayTime: null,//付款时间开始
                endOrderPayTime: null,//付款时间结束
                productID: null,//商品ID
                shop: [],//店铺
                leader: [],//组长
                commissioner: [],//专员
                assistant: [],//助理
                refundReason: [],//买家退款原因
                judgeTime: 'start',//持续上升判断时间
            },
            
            shopList: [],//店铺
            leaderList: [],//组长
            commissionerList: [],//专员
            assistantList: [],//助理
            refundReasonList: [],//买家退款原因
            that: this,

            allRefundDataList: [],//列表数据
            tableCols: tableCols,//输出列表
            total: 0,
            summaryarry: { },
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            // selids: [],

            pickerOptions: {
                shortcuts: [{
                text: '前一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        end.setTime(end.getTime());
                        picker.$emit('pick', [start, end]);
                        // window.setshowprogress(false);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
                        const date2 = new Date(); date2.setDate(date2.getDate());
                        picker.$emit('pick', [date1, date2]);
                        // window.setshowprogress(false);
                    }
                }]
            }
        }
    },
    async mounted() {
        let end = new Date();
        let start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        end.setTime(end.getTime());
        
        this.orderPayTimeRange = [start, end];
        this.filter.beginOrderPayTime = start;
        this.filter.endOrderPayTime = end;

        this.init();
        this.onSearch();
    },
    methods: {
        async init() {
            //店铺
            var { data } = await getShopNameInProductEntity();
            this.shopList = data;
            //组长
            var { data } = await getLeaderNameInProductEntity();
            this.leaderList = data;
            //专员
            var { data } = await getCommissionerNameInProductEntity();
            this.commissionerList = data;
            //助理
            var { data } = await getAssistantNameInProductEntity();
            this.assistantList = data;
            //买家退款原因
            var { data } = await getRefundReason();
            this.refundReasonList = data;
        },
        //退款率是否超过8%  
        refundPercentageFormatter(row) {
            let color;
            if (row*100 > 8) color = "red";
            else color = "grey";
            const map = {
                "red" : "color: red",
                "grey" : "color: grey"
            }
            return map[color];
        },
        //排序
        sortchange(column) {
            if (column.order) {
                this.filter.orderBy = column.prop;
                this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
        // selectchange: function (rows, row) {
        //     this.selids = [];
        //     rows.forEach(f => {
        //         this.selids.push(f.id);
        //     })
        // },
        //付款时间
        changePayTime(e) {
            const formatDate = date => {
                if (!date) return null;
                const localDate = new Date(date);
                // 获取时间偏差（分钟）
                const offset = localDate.getTimezoneOffset();
                // 转换为 UTC 时间
                return new Date(localDate.getTime() + offset * 60000).toISOString().split('T')[0];
            };
            
            this.filter.beginOrderPayTime = e ? formatDate(e[0]) : null;
            this.filter.endOrderPayTime = e ? formatDate(e[1]) : null;
        },
        //每页数量改变
        Sizechange(val) {
            this.listLoading = true;
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList();
            this.listLoading = false;
        },
        //当前页改变
        Pagechange(val) {
            this.pageLoading = true;
            this.filter.currentPage = val;
            this.getList();
            this.pageLoading = false;
        },
        //查询
        onSearch() {
            //点击查询时才将页数重置为1
            this.filter.currentPage = 1;
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            this.filter.beginOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[0] : null;
            this.filter.endOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[1] : null;
            this.listLoading = true;
            const { data, success } = await getIDWarning(this.filter);
            this.listLoading = false;
            if (success) {
                this.allRefundDataList = data.list;
                this.total = data.total;
                this.summaryarry = data.summary;
            } else {
                this.$message.error('获取ID预警数据失败！');
            }
        },
        //导出
        async onExport() {
            if (this.orderPayTimeRange) {
                this.filter.beginOrderPayTime = this.orderPayTimeRange[0];
                this.filter.endOrderPayTime = this.orderPayTimeRange[1];
            }
            this.listLoading = true;
            const res = await exportIDWarning(this.filter);
            this.listLoading = false;
            if (!res?.data) return;
            const aLink = document.createElement('a');
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute('download', 'ID预警数据_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        },
        //以结束时间为起点
        changeJudgeTime() {
            this.filter.judgeTime = this.isChecked ? "end" : "start";
        },
        //趋势图
        async showchart(row) {
            let that = this;
            if (this.orderPayTimeRange) {
                this.filter.beginOrderPayTime = this.orderPayTimeRange[0];
                this.filter.endOrderPayTime = this.orderPayTimeRange[1];
            }

            this.chartFilter.orderBy = 'orderPayTime';
            this.chartFilter.isAsc = true;

            this.chartFilter.beginOrderPayTime = this.filter.beginOrderPayTime;
            this.chartFilter.endOrderPayTime = this.filter.endOrderPayTime;

            this.chartFilter.productID = row.productID;
            
            this.chartFilter.shop = this.filter.shop;
            this.chartFilter.leader = this.filter.leader;
            this.chartFilter.assistant = this.filter.assistant;
            this.chartFilter.refundReason = this.filter.refundReason;
            this.chartFilter.judgeTime = this.filter.judgeTime;
            
            const res = await getIDWarningMap(this.chartFilter).then(res => {
                that.dialogVisible.visible = true;
                that.dialogVisible.data = res;
                that.dialogVisible.title = res.title;
                res.title = "";
            })
            this.dialogVisible.visible = true;
        },
    }
}
</script>
<style lang="scss" scoped>
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>