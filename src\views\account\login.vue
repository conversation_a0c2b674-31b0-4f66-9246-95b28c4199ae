<template>
  <div style="width: 100%; height: 100%;" class="bg">
     <div class="land">
      <div class="Loginbox" style="width: 470px; height: 500px; position: absolute; top: -30px; right: 30px; ">
        <div style="height: 100%; width: 120px; left: 0; top: 0; position: absolute; display: flex; justify-content: center; align-items: center; flex-direction: column;">
          <!-- <div style="display: flex; flex-direction: column; width: 100%; height: 100%; justify-content: center; align-items: center;">
            <button class="loginbutton" v-for="(item,i) in componey" :key="i"
             style="background-color: #268578; color: white; border: none; width: 140px; height:34px; border-radius: 0 5px 5px 0; margin: 0 15px 34px 33px;"
              @click="selcomponey(item)" :style="corpId == item.corpId?selbtn:nobtn"
              >{{ item.title }}</button>
          </div> -->
        </div>
        <div style="display: flex; justify-content: center; align-items: center; ">
          <div id="qrcodemy">
             <iframe @load="loadFrame" v-if="qrcodelogin" id="fram_box" src="/dingding/ddlogin.html"  style="" frameborder="no" border="0" marginwidth="0" marginheight="0"
                 scrolling="no" allowtransparency="yes" name="loginframe" width="350px" height="350px">
             </iframe>
           </div>
        </div>
      </div>
       <!-- <div class="Loginbox" style="flex-direction: row; display: flex;">
            <div style="width: 80px;">
                <div v-for="i in 8" :key="i">222</div>
            </div>
           <div class="QRcode" id="qrcodemy">
             <iframe @load="loadFrame" v-if="qrcodelogin" id="fram_box" src="/dingding/ddlogin.html"  style="" frameborder="no" border="0" marginwidth="0" marginheight="0"
                 scrolling="no" allowtransparency="yes" width="350px" height="350px">
             </iframe>
           </div>
       </div> -->
     </div>
 </div>
</template>
<script>
import { getVerifyCode,ddurl, getCompany } from '@/api/admin/auth'
import Cookies from 'js-cookie'
import { color } from 'echarts';
export default {
 name: 'login',
 data() {
   return {
     shouyeUrl: '',
     editableTabsValue: '1',
     editableTabs: [{title: '扫码登录',name: '1'}, {title: '密码登录',name: '2'}],
     tabIndex: 0,
     qrcodelogin:true,
     form: {
       userName: '',
       password: '',
       verifyCode: '',
       verifyCodeKey: '',
       passwordKey: ''
     },
     formRules: {
       userName: [{ required: true, message: '请输入账号', trigger: 'blur' }],
       password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
       verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
     },
     checked: true,
     verifyCodeUrl: '',
     loginLoading: false,
     loginText: '登录',
     componey: [],
     nobtn: {"backgroundColor": "#89b9b3"},
     selbtn: {"backgroundColor": "#268578"},
     corpId: '',
     appid: ''
   }
 },
 created() {
   this.getLoginVerifyCode()
 },
async mounted() {
   var that=this;
   if(Cookies.get('token')){
    this.$store.commit('user/setToken', Cookies.get('token'))
    this.getLoginInfo()
    return;
   }

  const res = await getCompany({});
    if(!res.success){
    that.$message({type: 'error',title:'扫码错误！'})
    return
    }
    that.appid = res.data[0].appId;
    that.corpId = res.data[0].corpId;
    that.componey = res.data;

    window.newappId = res.data[0].appId;
    that.$nextTick(()=>{
      window.frames["loginframe"].init()
    }) 


   window.gotourl=function(u){
     window.location.href=(u);
   }
   window.getdingdingurl= async function() {
      var url=await that.getredirecturl()
      return url;
   }
   window.qrlogin= async function(code) {
     return await that.qrlogin(code)
   }
  //  this.getcomponey();
 },
 methods: {
  selcomponey(comid){
    this.corpId = comid.corpId;
    window.newappId = comid.appId;
    this.$nextTick(()=>{
      window.frames["loginframe"].init()
    }) 
  },
  // async getcomponey(){
  //    const res = await getCompany({});
  //    if(!res.success){
  //     this.$message({type: 'error',title:'扫码错误！'})
  //     return
  //    }
  //    this.appid = res.data[0].appId;
  //    this.corpId = res.data[0].corpId;
  //    this.componey = res.data;

  //    window.newappId = res.data[0].appId;
  //   this.$nextTick(()=>{
  //     window.frames["loginframe"].init()
  //   }) 
  //    console.log("打印默认值",[this.appid,this.corpId])
  //    return res.data;
  // },
  loadFrame() {
    // setTimeout(() => {
    //       const img = document.querySelectorAll('img')
    //       console.log("img",img)

    //       const iframeBox = document.getElementById('fram_box')
    //       const doc = iframeBox.contentWindow.document
    //       const qcode = doc.getElementById('login_container').getElementsByTagName('iframe')
    //       console.log("1111",qcode)

    //       console.log('4444',qcode[0].parentElement.getElementsByTagName('iframe'));

    //       console.log("22222",doc)
    //     }, 500);
    },
   async getredirecturl(){
     const res = await ddurl({});
     return res.data;
   },
  changetab(){
   if(this.qrcodelogin) this.qrcodelogin=false;
   else this.qrcodelogin=true;
 },
 loginValidate() {
     let isValid = false
     this.$refs.form.validate(async valid => {
       isValid = valid
     })
     return isValid
   },
   // 登录获取Token
   async onLogin() {
     if (!this.loginValidate()) {
       return
     }
     this.loginLoading = true
     this.loginText = '登录中...'
     const paras = { ...this.form }
     const res = await this.$store.dispatch('user/login', paras)
     //返回参数值
     // const nowrouter = res.data.homeData.path;

     // var reg = RegExp(/http/);
     // var newpath = reg.test(nowrouter)?('/'+res.data.homeData.id):res.data.homeData.path;

     // this.shouyeUrl = newpath;
     if (!res) {
       this.loginLoading = false
       this.loginText = '重新登录'
       return
     }
     if (!res.success) {
       this.loginLoading = false
       this.loginText = '重新登录'
       switch (res.data) {
         case 1:
           this.getLoginVerifyCode()
           this.$refs.verifyCode.focus()
           break
         case 2:
           this.$refs.verifyCode.focus()
           break
         case 3:
           this.getLoginVerifyCode()
           this.$refs.userName.focus()
           break
         case 4:
           this.getLoginVerifyCode()
           this.$refs.password.focus()
           break
       }

       return
     }
     this.getLoginInfo()
   },
   async getLoginInfo() {
     const res = await this.$store.dispatch('user/getLoginInfo')
     /////
     // const nowrouter = res.data.homeData.path;

     // var reg = RegExp(/http/);
     // var newpath = reg.test(nowrouter)?('/'+res.data.homeData.id):res.data.homeData.path;

     // this.shouyeUrl = newpath;
     /////
     this.loginLoading = false
     if (!res?.success) {
       this.loginLoading = false
       this.loginText = '重新登录'
       
      //  var iframe=document.getElementById('ddloginiframe');
       var iframe=document.getElementById('fram_box');
       iframe.src = '/dingding/ddlogin.html';
       Cookies.remove('token')
       return
     }
     if (!(res.data?.menus?.length > 0)) {
       this.loginLoading = false
       this.loginText = '重新登录'
       this.$message({message: '该账号未分配权限，请联系管理员！',type: 'error'})
      //  var iframe=document.getElementById('ddloginiframe');
      var iframe=document.getElementById('fram_box');
       iframe.src = '/dingding/ddlogin.html';
       return
     }

     const redirect = this.$route.query ? this.$route.query.redirect : ''
     // this.$router.push({ path: redirect || '/' })
     this.shouyeUrl?this.$router.push({ path: redirect || this.shouyeUrl }):this.$router.push({ path: redirect || '/' })
   },
   // 获取验证码
   async getLoginVerifyCode() {
     this.form.verifyCode = ''
     const res = await getVerifyCode({ lastKey: this.form.verifyCodeKey })
     if (res && res.success) {
       this.verifyCodeUrl = 'data:image/png;base64,' + res.data.img
       this.form.verifyCodeKey = res.data.key
     }
   },
   async qrlogin(code){
     var flag=false;
     const res = await this.$store.dispatch('user/ddlogin', { tempcode: code, corpId: this.corpId })
     /////////
     if (res && res.success) {
      const nowrouter = res.data.homeData.path;

var reg = RegExp(/http/);
var newpath = reg.test(nowrouter)?('/'+res.data.homeData.id):res.data.homeData.path;

this.shouyeUrl = newpath;
       this.getLoginInfo()
       flag=true;
       // window.parent.gotourl("/")
     }
     else {
      //  var iframe=document.getElementById('ddloginiframe');
       var iframe=document.getElementById('fram_box');
       iframe.src = '/dingding/ddlogin.html';
     }
     return flag;
   }
 }
}
</script>

<style scoped>
.bg {
 height: 100%;
 width: 100%;
 margin: 0;
 background: #3a8ee6;
 background: -webkit-linear-gradient(top left, #3a8ee6 0%, #3a8ee6 100%);
 background: linear-gradient(to bottom right, #3a8ee6 0, #3a8ee6);
 /* background-image: url('/static/images/bg.png'); */
 background-image: url('../../static/images/bj2.jpg');
 opacity: 0.9;
 display: flex;
 justify-content: center;
 align-items: center;
 background-position: 50% 50%;

}
.bg ::v-deep .el-scrollbar__view {
 height: 100%;
}
.verifyCode ::v-deep .el-input__inner {
 letter-spacing: 2px;
}

@media screen and (max-width: 868px) {
 .login-card {
   width: 90%;
 }
}
</style>
<style lang="scss" scoped>
.logintabs{
 left: 50%;
 top: 50%;
 margin-left:-200px;
 margin-top: -150px;
 position:fixed;
 text-align: center;
 background-image: url('/static/images/login.png');
 display: block;
}
.login-card {
 width: 350px;
 padding: 25px 25px 5px 25px;
 position: relative;
 margin: 0 auto;
 .title {
   color: rgba(0, 0, 0, 0.85);
   font-weight: 600;
   font-size: 33px;
   font-family: "Myriad Pro", "Helvetica Neue", Arial, Helvetica, sans-serif;
   vertical-align: middle;
   margin: 0px;
   text-align: center;
 }
 .desc {
   margin-top: 12px;
   margin-bottom: 30px;
   color: rgba(0, 0, 0, 0.45);
   font-size: 14px;
   text-align: center;
 }
 .remember {
   margin: 0px 0px 25px 0px;
 }
}

.land {
 min-width: 1000px;
 min-height: 445px;
 // background-image: url(..//assets/lt2.jpg);
 background-image: url('../../static/images/lt2.jpg');
 position: relative;
 border-radius: 2px;
}
.Loginbox {
 display: flex;
 justify-content: center;
 align-content: center;
 background-color: rgb(255, 255, 255);
 /* float: left; */
 /* margin: 00px 0 0 480px; */
 box-shadow: 0px 2px 20px #cacaca;
 border-radius: 2px;
 position: relative;
}

// .Loginbox .Loginmode {
//  /* width: 220px; */
//  height: 60px;
//  // margin: 10px auto;
//  font-weight: bold;
//  text-align: center;
// }

// .Loginbox .Inputbox {
//  margin: 20px 10px;
// }

.QRcode {
 width: 400px;
 height: 400px;
 padding-top: 50px;
 display: flex;
 justify-content: center;
 align-items: center;
 margin-left: -16px;
//  background-color: #eee;
 // background-color: #f5f5f5;
 // margin: 20px auto;
 /* margin-top: 65px; */
}
.QRcodetxt {
 width: 300px;
 margin: 0 auto;
 /* background-color: blueviolet; */
 text-align: center;
 color: #999;
}
.login_qrcode_content{
 width: 300px !important;
 height: 300px !important;
}

.loginbutton{
  text-decoration: none;
  border: none;
  font-size: 16px;
  border-radius: 5px;
  box-shadow: 7px 6px 28px 1px rgba(0,0,0,0.24);
  cursor: pointer;
  outline: none;
  transition: 0.2s all;
}
.loginbutton:hover{
  transform: scale(0.98);
  box-shadow: 3px 2px 22px 1px rgba(0,0,0,0.24);
}
</style>
