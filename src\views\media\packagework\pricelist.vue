<template>
  <my-container v-loading="pageLoading">
    <div style="height: 40px; ">
      <span style="margin-right:0.4%;">
        <el-input style="width:160px;" v-model.trim="addForm.finishedProductCode" :maxlength=100
              placeholder="成品编码" @keyup.enter.native="onSearch" clearable />
      </span>

      <span style="margin-right:0.4%;">
        <el-input style="width:160px;" v-model.trim="addForm.finishedProductName" :maxlength=100
            placeholder="成品名称" suffix-icon="el-icon-search"  clearable />
      </span>

      <span style="margin-right:0.4%;">
                <el-select style="width:12%" v-model="addForm.machineTypeCode" filterable clearable collapse-tags multiple placeholder="机型">
                  <el-option v-for="item in machineTypeList" :key="item.setId" :label="item.sceneCode"
                    :value="item.setId" />
                </el-select>
      </span>

      <span style="margin-right:0.4%;">
        <el-date-picker  style="position: relative; top: 1px; width: 300px"
            type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            end-placeholder="结束日期" v-model="addForm.createdtimerange"  :picker-options="pickerOptions">
        </el-date-picker>
      </span>

      <span style="margin-left:5px;">
        <el-button type="primary" @click="onSearch">查询</el-button>
      </span>
      <span style="margin-left:5px;">
          <el-button @click="onclear" plain>重置</el-button>
      </span>

  <div style="width:30%;display: inline-block;text-align: right;">
      <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain @click="addNew"><i class="el-icon-plus"></i>&nbsp;新增工价</el-button>
      <el-button type="primary" @click="onImportPrice">导入</el-button>
      <el-button type="primary" @click="handleExportCommand">导出</el-button>
      <el-button type="primary" @click="DownLoadImportSyj">下载导入模板</el-button>
      <el-button type="primary" @click="clickRefresh">刷新</el-button>
    </div>
  </div>
    <div style="height: 90%;">
      <vxe-table ref="xTable" border="default" height="100%" :show-footer="true" style="width: 100%;"
      class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
      :show-footer-overflow="'tooltip'" keep-source size="mini" :loading="pageLoading" :data="tableData"
      :scroll-y="{ gt: 100 }" :scroll-x="{ gt: 100 }"  :header-row-class-name="'height cellheight1'"
      :expand-config="{ accordion: true}"
      :row-class-name="rowStyleFun"
      :row-config="{ isCurrent: true, isHover: true }">


        <vxe-column title="编号" field="packagesProcessingId" width="55" align="left" fixed='left'>
        </vxe-column>
        <vxe-column field="finishedProductImg" title="成品图片" width="66" align="left" fixed='left'>
          <template #default="{ row }">
            <el-image style="width: 50px; height: 50px" :src="row.finishedProductImg"
              :preview-src-list="[row.finishedProductImg]">
            </el-image>
          </template>
        </vxe-column>
        <vxe-column type="expand" align="left" width="25" :edit-render="{}" fixed='left'>
          <template #content="{ row, rowIndex }">
            <div style="padding: 20px 220px 20px 180px;background-color: #909399;">
              <div style="background-color: white;">
              <vxe-table height="200px" show-overflow resizable border="default"
                :row-config="{ height: '36', isCurrent: true, isHover: true }" :header-row-class-name="'height1'"
                :cell-class-name="'cellheight1'" :align="allAlign1" stripe :data="row.detialList">
                <vxe-column title="编号" width="60">
                  <template #default="{ row }">
                    <div>{{ row.showCode }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="id" title="半成品Id" width="135" v-if="false" fixed='left'></vxe-column>
                <vxe-column field="halfProductImg" title="出厂图" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.halfProductImg"
                      :preview-src-list="[row.halfProductImg]">
                    </el-image>
                  </template>
                </vxe-column>
                <vxe-column field="requiredImg" title="所需图" width="90" align="left">
                  <template #default="{ row }">
                    <el-image style="width: 50px; height: 50px" :src="row.requiredImg"
                      :preview-src-list="[row.requiredImg]">
                    </el-image>
                  </template>
                </vxe-column>
                <vxe-column field="halfProductCode" title="半成品编码" width="220">
                  <template #default="{ row }">
                    <div class="relativebox">
                      <el-tooltip effect="dark" :content="row.halfProductCode" placement="top-start">
                        <div class="textover" style="width: 205px;">{{ row.halfProductCode }}</div>
                      </el-tooltip>

                      <div class="copyhover" @click="copytext(row.halfProductCode)">
                        <i class="el-icon-document-copy"></i>
                      </div>
                    </div>

                  </template>
                </vxe-column>
                <vxe-column field="halfProductName" title="半成品名称" width="350"></vxe-column>
                <vxe-column field="halfProductQuantity" title="组合数量" width="150"></vxe-column>
                <vxe-column title="操作">
                  <template #default="{ row }">
                    <el-button type="primary" plain @click="showupfuc(row)">图片上传</el-button>
                  </template>
                </vxe-column>
              </vxe-table>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="finishedProductCode" title="成品编码" width="210" align="left">
          <template #default="{ row }">
            <div class="relativebox">
              <div class="textover" style="width: 205px;">{{ row.finishedProductCode }}</div>

              <div class="copyhover" @click="copytext(row.finishedProductCode)">
                <i class="el-icon-document-copy"></i>
              </div>
            </div>
          </template>
        </vxe-column>

        <vxe-column field="finishedProductName" title="成品名称" width="390" align="left">
          <template #default="{ row }">
            <div class="relativeboxx">
              <!-- @click="doubleclick(row)" -->
              <div class="point  textover" style="width: 255px;">{{ row.finishedProductName }}</div>
              <div v-show="row.isMark" class="positioncenter"><el-badge is-dot class="item"></el-badge></div>
            </div>
          </template>
        </vxe-column>

        <!-- <vxe-column field="workPrice" title="加工工价" width="100" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessWorkPrice')">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 1)" size="mini">{{
              row.workPrice ? row.workPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="dispatchWorkPrice" title="加工工价" width="100">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 2)" size="mini">{{
              row.dispatchWorkPrice ? row.dispatchWorkPrice : 0 }}</el-button>
          </template>
        </vxe-column>
        <!-- <vxe-column field="allotWorkPrice" title="调拨工价" width="100">
          <template #default="{ row }">
            <el-button type="text" style="color: #999" @click="changepriceshow(row, 4)" size="mini">{{
              row.allotWorkPrice ? row.allotWorkPrice : 0 }}</el-button>
          </template>
        </vxe-column> -->
        <vxe-column field="machineTypeCode" title="机型" align="left">
          <template #default="{ row }">
            <div>{{ codetoname(row.machineTypeCode) }}</div>
          </template>
        </vxe-column>

        <vxe-column field="updateWorkPriceDate" title="保存日期" align="left">
          <template #default="{ row }">
            <div>{{ row.updateWorkPriceDate}}</div>
          </template>
        </vxe-column>

        <vxe-column title="操作" width="180" align="left">
          <template #default="{ row }">
            <el-button type="primary" @click="editImg(row)">编辑</el-button>
            <my-confirm-button type="danger" @click="deltask(row)">删除</my-confirm-button>
          </template>
        </vxe-column>

      </vxe-table>
      <!-- <div style="font-size: 14px; color: #606266;">共：{{ tableData?.length }} 条</div> -->
    </div>

    <!--查看备注------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="查看备注" :show-close="false" :visible.sync="viewReferenceRemark" width="60%" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <packdesgtaskRemark ref="packdesgtaskRemark" :rowinfo="rowinfo" v-if="viewReferenceRemark"></packdesgtaskRemark>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewReferenceRemark = false">关 闭</el-button>
          <my-confirm-button type="submit" :loading="shootingTaskRemarkrawer" @click="sumbitshootingTaskRemark" />
        </span>
      </template>
    </el-dialog>

    <!--提交审批------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
    <el-dialog title="提交审批" :show-close="false" :visible.sync="shenpishow" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <div style="height: 35px;box-sizing: border-box;padding:0 25px;border-bottom: 1px solid #ebeef5;">
        <div style="font-size: 16px;color:#666;">编辑{{ pricetitle }}</div>
        <!-- <vxe-input style="margin: 10px;" v-model="pricelist.price" type="text"></vxe-input> -->
      </div>
      <div style="box-sizing: border-box;padding:35px 35px;">
        <div style="width:100%;margin:15px auto 70px auto;">
          <vxe-select style="width:100%" placeholder="请选择仓库" @change="storagechange" v-model="diaobodata.wareId"
            :clearable="true" :collapse-tags="true" filterable>
            <vxe-option v-for="item in storagelist" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
          </vxe-select>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shenpishow = false">取消</el-button>
          <el-button @click="approvefuc" type="primary">保存</el-button>
          <!-- <my-confirm-button type="submit"  @click="subchange" /> -->
        </span>
      </template>
    </el-dialog>

    <!-- 价格 -->
    <el-dialog title="" :show-close="false" :visible.sync="priceshow" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
      <div style="height: 35px;box-sizing: border-box;padding:0 25px;border-bottom: 1px solid #ebeef5;">
        <div style="font-size: 16px;color:#666;">编辑{{ pricetitle }}</div>
        <!-- <vxe-input style="margin: 10px;" v-model="pricelist.price" type="text"></vxe-input> -->
      </div>
      <div style="box-sizing: border-box;padding:35px 35px;"><el-input-number style="width: 100%;"
          v-model="pricelist.price" :precision="5" :step="0.1" :min="0" :max="100000" :controls="false"></el-input-number>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="priceshow = false">取消</el-button>
          <el-button @click="subchange" type="primary">保存</el-button>
          <!-- <my-confirm-button type="submit"  @click="subchange" /> -->
        </span>
      </template>
    </el-dialog>


    <vxe-modal v-model="qualificationshow" mask-closable title="合格证信息">
      <template #default>
        <vxe-textarea v-model="qualfifter.msg" :disabled="disabled" placeholder="" :autosize="{ minRows: 6, maxRows: 10 }"
          clearable :maxlength=300>
        </vxe-textarea>
        <div class="qualibtn" style="width: 100%;">
          <div><el-button style="width:100%;" size="mini" @click="copytext(qualfifter.msg)">复制内容</el-button></div>
          <div>
            <upfile ref="refupfile" @geturl="geturl" :downmsg="qualfifter"></upfile>
          </div>

          <el-button style="width:49%;margin-right:2%;" type="info" size="mini"
            @click="qualificationedit('不加合格证')">无合格证</el-button>
          <el-button style="width:49%;" v-if="disabled" size="mini" type="primary" @click="qualfieditfuc">编辑</el-button>

          <div v-else>
            <div class="flexrow">
              <div class="marginrt">
                <el-button width="200px" size="medium" @click="disabled = true">取消</el-button>
              </div>
              <el-button width="200px" size="medium" type="primary" @click="savecertificateInfo">保存</el-button>
            </div>
          </div>
        </div>
      </template>
    </vxe-modal>



    <vxe-modal v-model="workshow" mask-closable :title="recordTitle" className="vxeclass">
      <template #default>
        <div style="width:100%;">
          <div style="width:100%;margin:15px auto;">
            <!-- //隐藏元素bug -->
            <div style="display: none;">{{ userid }}</div>
            <vxe-select v-if="isnei" :disabled="!checkPermission('api:Inventory:PackagesProcessing:EditPerson')"
              style="width:100%;" @change="userselect" :value="fiftername(taskrow.createPackId)" size=""
              placeholder="请选择人员" filterable>
              <vxe-option v-for="(uitem, i) in userlist" :key="i" :value="uitem.userId"
                :label="uitem.userName"></vxe-option>
            </vxe-select>
            <vxe-select v-else :disabled="!checkPermission('api:Inventory:PackagesProcessing:EditPerson')"
              style="width:100%;" @change="userselect" :value="fiftername(taskroww.selUserId)" size="" placeholder="请选择人员"
              filterable>
              <vxe-option v-for="(uitem, i) in userlist" :key="i" :value="uitem.userId"
                :label="uitem.userName"></vxe-option>
            </vxe-select>
          </div>
          <div style="width:100%;margin:15px auto;">
            <vxe-input controls-position="right" @change="qualityinput" style="width:100%;" v-model="taskrow.quantity"
              placeholder="请输入数量" type="integer" :step="1" :min="1" :max="10000" size=""></vxe-input>
          </div>


          <div style="height:5px;"></div>
        </div>
        <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;" v-if="isnei">
          <div class="flexrow">
            <div class="marginrt">
              <el-button width="200px" size="medium" @click="workshow = false">取消</el-button>
            </div>
            <el-button width="200px" size="medium" type="primary" @click="neisaveworkshow" v-throttle="2000"
              v-loading="btnloading">保存</el-button>
          </div>
        </div>

        <div class="qualibtn" style="width: 100%;display: flex; flex-direction: column;" v-else>
          <div class="flexrow">
            <div class="marginrt">
              <el-button width="200px" size="medium" @click="workshow = false">取消</el-button>
            </div>
            <el-button width="200px" size="medium" type="primary" @click="saveworkshow" v-throttle="2000"
              v-loading="btnloading">保存</el-button>
          </div>
        </div>
      </template>
    </vxe-modal>

    <el-drawer :visible.sync="tableeditshow" close-on-click-modal append-to-body direction="rtl" size="875px"
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
      <template #default>
        <div>
          <div class="bzbjbt">
            <span style="float: left">{{ tableedittitle }}</span>
          </div>
          <div class="rwmc">
            <div class="xh" style="width: 8%">{{ (typeId == 3 || typeId == 4) ? titlerow.showCode :
              titlerow.packagesProcessingId }}</div>
            <div class="mc" style="width: 2%">|</div>
            <div class="mc" style="width: 70%">{{ (typeId == 3 || typeId == 4) ? titlerow.halfProductName :
              titlerow.finishedProductName }}</div>
            <div class="icon" style="width: 20%;text-align: right;">
              <span v-if="typeId == 6"><el-button v-if="checkPermission('api:Inventory:PackagesProcessing:QCOp')"
                  type="primary" @click="onediaobo">一键调拨</el-button></span>
              <span v-if="typeId == 4"><el-button v-if="checkPermission('api:Inventory:PackagesProcessing:DeliveryOp')"
                  type="primary" @click="onediaobo">一键调拨</el-button></span>

            </div>
          </div>
          <div class="box-card tablehei">
            <vxe-table height="100%" resizable border="default" :key="tokey" :align="allAlign2" :data="tableData2"
              show-footer :footer-method="footerMethod1" @checkbox-all="neitablesel" @checkbox-change="neitablesel">
              <vxe-column type="checkbox" width="28" v-if="typeId != 1"></vxe-column>
              <vxe-column type="seq" width="35"></vxe-column>
              <vxe-column field="createUserName" title="姓名" width="75">
              </vxe-column>
              <vxe-column field="quantity" title="数量" width="80">
                <template #default="{ row }">
                  {{ row.quantity }}
                </template>
              </vxe-column>
              <vxe-column field="workPrice" title="工价" width="75" v-if="typeId != 4">
              </vxe-column>
              <vxe-column field="totalWorkPrice" title="合计" width="70" v-if="typeId != 4">
              </vxe-column>
              <vxe-column field="state" title="状态" width="75" v-if="typeId == 6 || typeId == 4 || typeId == 2">
                <template #default="{ row }">
                  <div class="flexrow statuscss">
                    <!-- <el-tag :type="row.taskStatusName=='已完成'?'success':'info'" effect="dark" >{{ row.taskStatusName }}</el-tag> -->
                    <el-tag
                      :color="row.state == '已撤销' ? '#909399' : row.state == '审批中' ? '#409EFF' : row.state == '已完成' ? '#67C23A' : row.state == '已拒绝' ? '#F56C6C' : '#ffffff'"
                      effect="dark">{{ row.state }}</el-tag>

                    <!-- {{ row.state }} -->
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="createDate" title="日期" width="90">
                <template #default="{ row }">
                  <div class="flexrow">
                    {{ formatIsCommission(row.createDate) }}
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="age" title="操作">
                <template #default="{ row }">
                  <div class="flexrow" v-if="typeId == 1">
                    <div class="flexrow" v-if="checkPermission('api:Inventory:PackagesProcessing:ProcessOp')">
                      <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                      <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'"
                        style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                    </div>

                  </div>
                  <div class="flexrow" v-else-if="typeId == 3">
                    <div class="flexrow" v-if="checkPermission('api:Inventory:PackagesProcessing:DeliveryOp')">
                      <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                      <el-button :disabled="row.state == '审批中'" type="primary" @click="shnpishowfuc(row, 5, 'nei')"
                        v-if="typeId == 6 || typeId == 4">调拨</el-button>
                      <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'"
                        style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                    </div>

                  </div>
                  <div class="flexrow" v-else-if="typeId == 6">
                    <div class="flexrow" v-if="checkPermission('api:Inventory:PackagesProcessing:QCOp')">
                      <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                      <el-button :disabled="row.state == '审批中'" type="primary" @click="shnpishowfuc(row, 5, 'nei')"
                        v-if="typeId == 6 || typeId == 4">调拨</el-button>
                      <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'"
                        style="margin-left: 10px;" @click="delRecord(row)">删除</el-button>
                    </div>

                  </div>
                  <div class="flexrow" v-else>
                    <el-button plain :disabled="row.state == '审批中'" @click="workshowfuc(row, 5, 'nei')">编辑</el-button>
                    <el-button :disabled="row.state == '审批中'" type="primary" @click="shnpishowfuc(row, 5, 'nei')"
                      v-if="typeId == 6 || typeId == 4">调拨</el-button>
                    <el-button type="danger" :disabled="row.state == '审批中' || row.state == '审批完'" style="margin-left: 10px;"
                      @click="delRecord(row)">删除</el-button>
                  </div>

                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
      </template>
    </el-drawer>

    <el-drawer :visible.sync="editTaskshow" wrapperClosable :close-on-click-modal="true" direction="rtl" size="767px"
      element-loading-text="拼命加载中" v-loading="addLoading" :show-close="false">
      <dialogright ref="refdialogright" style="height: 100%;width:100%" @onCloseAddForm="onCloseAddForm"
        :allsellist="allsellist"></dialogright>
      <span slot="title" style="height: 0px;"></span>
    </el-drawer>

    <el-dialog title="文件上传" :visible.sync="updialogVisible" width="30%" v-dialogDrag :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="4">
            出厂图
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="upForm.halfProductImg" ref="supplier_id" :limit="1" keys="one"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
          <el-col :span="12" :push="2">
            <!-- <el-form-item prop="purImageUrl1" label="所需图"> -->
            所需图
            <yh-img-upload :value.sync="upForm.requiredImg" ref="supplier_id" :limit="1" keys="two"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="upfinish" :loading="uploadLoading">上 传</el-button>
      </span>
    </el-dialog>

    <!-- <el-dialog title="工价板块" :visible.sync="dialogVisibleSyj" width="30%">
        <span>
            <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" @click="onSubmitupload2">上传</my-confirm-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisibleSyj = false">关闭</el-button>
        </span>
    </el-dialog> -->

    <el-dialog title="工价板块" :visible.sync="dialogVisibleSyj" width="30%"  v-dialogDrag>
      <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
        :file-list="fileList" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
        <template #trigger>
          <el-button size="small" type="primary">选取文件</el-button>
        </template>
        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{
          (uploadLoading ? '上传中' : '上传') }}</el-button>
      </el-upload>
    </el-dialog>

    <el-dialog title="编辑成品图" :visible.sync="showfinishedProductImg" width="30%" v-dialogDrag :close-on-click-modal="true">
      <div style="padding: 30px;">
        <el-row>
          <el-col :span="12" :push="10">
            成品图
            <!-- <el-form-item prop="purImageUrl" label="出厂图"> -->
            <yh-img-upload :value.sync="upFinishForm.finishedProductImg" ref="supplier_id" :limit="1"
              keys="three"></yh-img-upload>
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showfinishedProductImg = false">取 消</el-button>
        <el-button type="primary" @click="saveImg" :loading="uploadLoading">{{ (uploadLoading ? '上传中' : '上传')
        }}</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="showAddnew" width="750px" v-dialogDrag :close-on-click-modal="true">
      <el-form :model="addForm" ref="addForm" label-width="120px" :rules="addFormRules">
        <div class="bzjzcjrw">
          <div class="bt">
            <span style="float: left">创建加工</span>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">成品名称</div>
            <div class="lxwz2 formtop">
              <el-form-item prop="finishedProductName" label=" " label-width="12px">
                <!-- <el-input style="width:100%" :clearable="true" v-model="addForm.finishedProductName"
                                :maxlength=100 key="spmc"></el-input> -->
                <vxe-input :maxlength="100" disabled style="height: 28px; width: 320px; font-size: 12px;"
                  v-model="addForm.finishedProductName" placeholder="成品名称" clearable></vxe-input>
              </el-form-item>
            </div>
          </div>

          <div class="bzccjlx">
            <div class="lxwz">成品编码</div>
            <div class="lxwz2">
              <el-form-item label=" " label-width="12px">
                <!-- <el-select v-model="addForm.finishedProductCode" :clearable="true" :collapse-tags="true" filterable @change="onchangeplatform" >
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select> -->
                <div style="display: flex; flex-direction: row;">
                  <span style="color: #F56C6C; margin: 0 7px 0 -13px">*</span>
                  <div style="width:65%">
                    <el-input :clearable="true" disabled @change="changecode" v-model="finishedProductCode" :maxlength=100
                      key="cpbm">
                      <el-button slot="append" icon="el-icon-plus" @click="onSelctCp(0)"></el-button>
                    </el-input>
                  </div>
                  <!-- <div style="margin-left: 20px;text-align: right;">
                                    <span><el-button type="primary"
                                            @click="tofinishedProductCode">暂无编码</el-button></span>
                                </div> -->
                </div>

              </el-form-item>
            </div>
          </div>
          <div class="bzccjlx">
            <div class="lxwz">机型</div>
            <div class="lxwz2">
              <el-form-item prop="machineTypeCode" label=" " label-width="12px">
                <el-select style="width:40%" v-model="addForm.machineTypeCode" filterable clearable>
                  <el-option v-for="item in machineTypeList" :key="item.setId" :label="item.sceneCode"
                    :value="item.setId" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="box-card">
            <div slot="header" class="clearfix" style="display: flex;margin-bottom: 10px;">
              <div style="width:50%;line-height:28px;font-size:16px;"><span style="color: #F56C6C;">*</span>半成品编码</div>
              <div style="width:50%;text-align: right;"><el-button type="primary"
                  @click="onSelctCp(1)">选择半成品编码</el-button></div>
            </div>
            <div style="width:100% ;height: 300px;overflow: auto; border: 1px solid #dcdfe6;">
              <el-table :data="addForm.detialList" header-row-class-name="bcpb">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="id" label="id" v-if="false" />
                <el-table-column prop="halfProductCode" label="半成品编码" width="160" />
                <el-table-column prop="halfProductName" label="半成品名称" width="320" />
                <!-- <el-table-column prop="halfProductQuantity" label="组合数量" width="160">
                                <template slot-scope="scope">
                                    <el-input-number v-model="scope.row.halfProductQuantity" :min="1" :max="10000"
                                        placeholder="数量" :precision="0">
                                    </el-input-number>
                                </template>
                            </el-table-column> -->
                <el-table-column lable="操作">
                  <template slot-scope="scope">
                    <el-button type="danger" @click="onDelDtlGood(scope.$index)">移除 <i class="el-icon-remove-outline"></i>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

        </div>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAddnew = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">提 交</el-button>
      </span>
    </el-dialog>
    <!--选择商品-->
    <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag append-to-body>
      <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="goodschoiceVisible = false">取 消</el-button>
          <el-button type="primary" @click="onQueren">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </my-container>
</template>
<script>
import MyConfirmButton from "@/components/my-confirm-button";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container/noheader.vue";
import dialogright from '@/views/media/packagework/dialogright';
import {
  getPackagesProcessingDetialListAsync, editPackagesProcessRecord, getPackagesProcessingRecordListAsync, getCallOutRecordListAsync, updateWorkPrice,
  updateCertificateBatch, updateFinishState, updateUrgencyDegree, deleteReord, getCurrentUser, editRecord, pckagesProcessingCallOut, pckagesProcessingCallIn, updateHalfProductImg,
  editCallInOutRecord, initiateApprovalCallInOut, packagesProcessQuality, updateTemplatesWorkPrice, delPackagesPrice, exportPackagesProcessingPricSet, importPriceSetAsync, updatePriceTemplateProductImg,
  addPackagesProcessingPrice
} from '@/api/inventory/packagesprocess';//包装加工
import { getShootOperationsGroup, getOperationsGroup, getErpUserInfoView } from '@/api/media/mediashare';
import { getUserRoleList, getShootingViewPersonAsync } from '@/api/media/ShootingVideo';
import packdesgtaskRemark from '@/views/media/packagework/packRemark';
import YhImgUpload from "@/components/upload/yh-img-upload1.vue";
import upfile from "@/views/media/packagework/upfile.vue"
import goodschoice from "@/views/base/goods/goods2.vue";
export default {
  name: "pricelist",
  props: ['summaryarry', 'storagelist', 'allsellist', 'machineTypeList'],
  components: { MyContainer, dialogright, MyConfirmButton, packdesgtaskRemark, YhImgUpload, upfile, goodschoice },
  filters: {
    datefifter: (date) => {
      if (!date) {
        return '';
      }
      return date.slice(2, 10);
    }
  },
  data () {
    return {
      showAddnew: false,
      addForm: {
        createdtimerange: [],
        stDate:null,
        edDate:null,
        currentPage: 1,
        pageSize: 50,
        packagesProcessId: 0,
        finishedProductName: "",
        finishedProductCode: "",
        finishedProductImg: "",
        brandCode: "",
        brandName: "",
        packingMaterialCode: "",
        packingMaterialName: "",
        machineTypeCode: "",
        machineTypeName: "",
        packageSizeCode: "",
        packageSizeName: "",
        finishedProductQuantity: 0,
        pfDate: "",
        urgencyDegree: "",
        quantityRequired: 0,
        certificateInfo: "",
        remark: "",
        detialList: []
      },
      addFormRules: {
        finishedProductName: [{ required: true, message: '请填写', trigger: 'blur' }],
        finishedProductCode: [{ required: true, message: '请选择', trigger: 'blur' }],
        machineTypeCode: [{ required: true, message: '请选择', trigger: 'blur' }],
      },
      showfinishedProductImg: false,
      fileList: [],
      uploadLoading: false,
      userid: '',
      value101: '',
      value20: '',
      tableedittitle: '',
      keys: 'four',
      tokey: 'one',
      shenpidata: {},
      diaobodata: {},
      shenpishow: false,
      btnloading: false,
      updialogVisible: false,
      addLoading: true,
      tableeditshow: false,
      pageLoading: false,
      qualificationshow: false,
      disabled: true,
      workshow: false,
      editTaskshow: false,
      viewReferenceRemark: false,
      shootingTaskRemarkrawer: false,
      nohgz: false,
      editshow: true,
      qualfifter: {
        msg: ''
      },
      zhijiandata: {},
      isnei: false,
      taskroww: {},
      outlist: {},
      upForm: {},
      upFinishForm: {},
      titlerow: null,
      loginuser: {},
      tableData: [],
      allAlign1: null,
      tableData1: [],
      allAlign2: null,
      tableData2: [],
      typeId: null,
      taskrow: null,
      allid: [],
      userlist: [],
      neiarr: [],
      recordTitle: "",
      tbPid: "",
      tbPCode: "",
      pricelist: {},
      priceshow: false,
      pricetitle: '',
      dialogVisibleSyj: false,

      seltype: null,
      goodschoiceVisible: false,
      finishedProductCode: "",
      pager: {},
      pickerOptions: {
          disabledDate(time) {
          // 设置禁用最小日期
          const minDate = new Date(1970, 0, 1);
          return time.getTime() < minDate.getTime();
          }
      },
    };
  },
  async mounted() {
        await this.onSearch();
    },
  methods: {
    // 重置
  onclear() {
  this.addForm.finishedProductCode = ''
  this.addForm.finishedProductName = ''
  this.addForm.machineTypeCode = null
  //     IsNeedDetial: false,
  //     IsHideComplete : true
  // };
  // this.packageSizeName = [];
  // this.machineTypeName = [];
  // this.packingMaterialName = [];
},
    //查询
    async onSearch() {
      this.getalist();
},
  async getalist(){
    this.addForm.stDate = null;
    this.addForm.edDate = null;
    if (this.addForm.createdtimerange && this.addForm.createdtimerange.length > 1) {
        this.addForm.stDate = this.addForm.createdtimerange[0];
        this.addForm.edDate = this.addForm.createdtimerange[1];
    }
    // var pager = this.$refs.pager.getPager();
    // const params = {
    //             ...pager,
    //             ...this.pager,
    //             ...this.addForm,
    //         };
    this.$emit('getPriceTemplateListfuc',this.addForm)
  },
    cellclick (events) {
      let _this = this;
      if (events.column.field == 'expand') {
        _this.loadingg = true;
        _this.openexpand(events.row, null)
      }

    },
    openexpand (row, rowindex) {
      let _this = this;
      this.$emit('getfinishedProductQuantity', row.finishedProductQuantity);
      this.waiid = row.packagesProcessingId;
      _this.$nextTick(() => {
        _this.getneireq();
      })
      this.neitableshow = true
    },
    onSubmit () {
      if (!this.finishedProductCode) {
        this.$message({ message: '请选择成品编码', type: 'error' }); return;
      }
      if (this.addForm.detialList.length < 1) {
        this.$message({ message: '请选择半成品编码', type: 'error' }); return;
      }
      if (this.finishedProductCode == '暂无编码') {
        this.addForm.finishedProductCode = '暂无编码';
      }
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          addPackagesProcessingPrice(this.addForm).then(res => {
            if (!res.success) {
              return
            } else {
              this.$message({ message: '添加成功', type: 'success' })
              this.showAddnew = false;
              this.clickRefresh();
            }
          })
        }
      })
    },
    //移除明细
    async onDelDtlGood (index) {
      this.addForm.detialList.splice(index, 1);
    },
    changecode (val) {
      this.addForm.finishedProductCode = val;
    },
    //选择商品确定
    async onQueren () {
      if (this.seltype == 0) {
        //选择成品商品确定
        var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
        if (choicelist && choicelist.length == 1) {
          choicelist.forEach(f => {
            //反填数据
            this.addForm.finishedProductCode = f.goodsCode;
            this.finishedProductCode = f.goodsCode;
            this.addForm.finishedProductName = f.goodsName;
          })
          this.goodschoiceVisible = false;
        }
      }
      else {
        //选择半成品商品确定
        var choicelist = await this.$refs.goodschoice.getchoicelist();
        if (choicelist && choicelist.length > 0) {
          //反填数据,
          // if (this.addForm.detialList) {
          if (!this.addForm.detialList) {
            this.addForm.detialList = []
          }
          //已存在的不添加
          var temp = this.addForm.detialList;
          var isNew = true;
          choicelist.forEach(f => {
            isNew = true;
            if (temp?.length > 0) {
              temp.forEach(old => {
                if (old.halfProductCode == f.goodsCode) {
                  isNew = false;
                }
              });
            }

            //
            if (isNew) {
              this.addForm.detialList.push({ halfProductCode: f.goodsCode, halfProductName: f.goodsName, halfProductQuantity: 0, dtlActualGoodsAmount: 0 });

            } else {
              this.addForm.detialList.forEach(told => {
                if (told.halfProductCode == f.goodsCode) {
                  told.halfProductName = f.goodsName;
                }
              });
            }
          })

          // }
          this.goodschoiceVisible = false;
        }
      }
    },
    onSelctCp (type) {
      this.seltype = type;
      this.goodschoiceVisible = true;
      this.$nextTick(() => {
        this.$refs.goodschoice.removeSelData();
      })
    },
    addNew () {
      this.addForm = {
        packagesProcessId: 0,
        finishedProductName: "",
        finishedProductCode: "",
        finishedProductImg: "",
        brandCode: "",
        brandName: "",
        packingMaterialCode: "",
        packingMaterialName: "",
        machineTypeCode: "",
        machineTypeName: "",
        packageSizeCode: "",
        packageSizeName: "",
        finishedProductQuantity: 0,
        pfDate: "",
        urgencyDegree: "",
        quantityRequired: 0,
        certificateInfo: "",
        remark: "",
        detialList: []
      },
        this.finishedProductCode = '';
      this.showAddnew = true;
    },
    async saveImg () {
      this.uploadLoading = true;
      await updatePriceTemplateProductImg(this.upFinishForm).then(res => {
        this.uploadLoading = false;
        if (!res?.success) {
          return
        }
        if (res?.success) {
          this.clickRefresh()
          this.$message({
            message: '操作成功！',
            type: 'success'
          })
          this.showfinishedProductImg = false;
        }
      });
    },
    // 编辑成品图
    editImg (row) {
      this.upFinishForm.priceTemplateId = row.packagesProcessingId;
      this.upFinishForm.finishedProductImg = row.finishedProductImg;
      this.upFinishForm.typeId = 1;
      this.$nextTick(() => {
        this.showfinishedProductImg = true;
      })
    },
    DownLoadImportSyj () {
      window.open("/static/excel/inventory/包装加工导入工价模板.xlsx", "_blank");
    },
    async handleExportCommand (command) {
      var res = null;
      var fileName = "包装加工-"
      const params = { ... this.filter }
      this.pageLoading = true;
      var res = await exportPackagesProcessingPricSet(params);
      fileName = fileName + "工价模板_"
      this.pageLoading = false;
      const aLink = document.createElement("a");
      let blob = new Blob([res?.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', fileName + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    // async onSubmitupload2() {
    //   console.log("打印数据",this.$refs.upload2)
    //     this.$refs.upload2.submit()
    // },
    // async uploadSuccess2(response, file, fileList) {
    //     fileList.splice(fileList.indexOf(file), 1);
    // },
    submitUpload () {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      if (this.$refs.upload.uploadFiles.length != 0) {
        this.$refs.upload.submit();
      } else {
        this.$message({
          type: 'fail',
          message: '请选择文件！'
        })
      }
    },
    // async uploadFile2(item) {
    //     const form = new FormData();
    //     form.append("upfile", item.file);
    //     form.append("platform", 2);
    //     form.append("pddGroup", 0);
    //     console.log("打印数据",form)
    //     const res =await importPriceSetAsync(form);
    //     this.$message({ message: '上传成功,正在导入中...', type: "success" });
    // },

    async uploadFile (item) {
      this.uploadLoading = true;
      if (!item || !item.file || !item.file.size) {
        this.uploadLoading = false;
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      let res = await importPriceSetAsync(form);
      if (res.code == 1) {
       // this.importDialog.visible = false
       this.dialogVisibleSyj=false;
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      }
      this.fileList = []
      this.uploadLoading = false
    },
    async uploadChange (file, fileList) {
      let files = [];
      files.push(file);
      this.fileList = files;
    },
    async uploadRemove (file, fileList) {
      let files = [];
      this.fileList = files;
    },

    onImportPrice () {
      this.dialogVisibleSyj = true
    },
    async deltask (row) {
      const res = await delPackagesPrice(row.packagesProcessingId);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '删除成功！',
        type: 'success'
      })
      this.priceshow = false;
      this.$emit('getPriceTemplateListfuc');
    },
    codetoname (val) {
      var a = "";
      this.machineTypeList.map((item) => {
        if (item.setId == val) {
          a = item.sceneCode
        }
      })
      return a;
    },
    onediaobo () {
      if (this.neiarr.length == 0) {
        this.$message({
          message: '请勾选需要调出质检！',
          type: 'fail'
        })
        return
      }
      this.shenpishow = true;
    },
    async geturl (data) {

      this.qualfifter.certificateUrl = data.url;
      this.qualfifter.fileName = data.fileName;
      this.$message.success("上传成功");
      await this.savecertificateInfo();
      this.qualificationshow = false;
    },
    rowStyleFun ({ row, rowIndex, $rowIndex }) {
      if (!row.isEnd) {
        return '';
      } else {
        return 'droprow';
      }
    },
    changepriceshow (row, index) {
      this.pricelist.typeId = index;
      this.pricelist.packagesProcessId = row.packagesProcessingId;
      switch (index) {
        case 1:
          this.pricelist.price = row.workPrice;
          this.pricetitle = '加工工价';
          break;
        case 2:
          this.pricelist.price = row.dispatchWorkPrice;
          this.pricetitle = '质检工价';
          break;
        case 3:
          this.pricelist.price = row.deliveryWorkPrice;
          this.pricelist.packagesProcessDetialId = row.id;
          this.pricetitle = '收货工价';
          break;
        case 4:
          this.pricelist.price = row.allotWorkPrice;
          this.pricelist.packagesProcessDetialId = row.id;
          this.pricetitle = '调拨工价';
          break;
      }
      this.priceshow = true;
    },
    async subchange (type, row) {
      let params = {
        ...this.pricelist,
      }
      // if(this.pricelist.typeId == 1){
      const res = await updateTemplatesWorkPrice(params);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '操作成功！',
        type: 'success'
      })
      this.priceshow = false;
      this.$emit('getPriceTemplateListfuc');
      // }else{
      //   const res = await updateWorkPrice(params);
      //   if (!res?.success) {
      //     return
      //   }
      //   this.$message({
      //     message: '操作成功！',
      //     type: 'success'
      //   })
      //   this.priceshow = false;
      //   this.$emit('getTaskList');
      // }

    },
    changeprice (type, price) {
      this.pricelist.price = price.value;
      this.pricelist.typeId = type;
    },
    toggleTreeMethod ({ expanded, row }) {
      this.$emit('getfinishedProductQuantity', row.finishedProductQuantity);
      return true;
    },
    async upfinish () {
      // const res = await updateHalfProductImg(this.upForm);
      // if (!res?.success) {
      //   return
      // }
      // this.$message({
      //   message: '操作成功！',
      //   type: 'success'
      // })
      // this.$emit('getTaskList');
      // this.updialogVisible = false;
      this.uploadLoading = true;
      await updatePriceTemplateProductImg(this.upForm).then(res => {
        this.uploadLoading = false;
        if (!res?.success) {
          return
        }
        if (res?.success) {
          this.clickRefresh()
          this.$message({
            message: '操作成功！',
            type: 'success'
          })
          this.updialogVisible = false;
        }
      });
    },
    showupfuc (row) {
      this.upForm.priceTemplateId = row.id;
      this.upForm.requiredImg = row.requiredImg;
      this.upForm.halfProductImg = row.halfProductImg;
      this.upForm.typeId = 2;
      let _this = this;
      _this.$nextTick(() => {
        _this.updialogVisible = true;
      })
    },
    qualityinput (val) {
      this.taskroww.quantity = val.value;
    },
    storagechange (val) {
      var name = '';
      this.storagelist.map((item) => {
        if (item.wms_co_id == val.value) {
          this.outlist.wareName = item.name;
          this.diaobodata.wareName = item.name;
        }
      })
    },
    sumbitshootingTaskRemark () {
      let res = this.$refs.packdesgtaskRemark.onsubmit();
      if (res) {
        this.viewReferenceRemark = false;
      }
      this.$emit('getTaskList');
    },
    //查看详情备注页
    openTaskRmarkInfo (row) {
      // return
      this.remarkRow = row;
      this.rowinfo = row.packagesProcessingId;
      this.viewReferenceRemark = true;
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count
    },
    countAmount (row) {
      return row.amount * row.num1
    },
    countAllAmount (data) {
      let count = 0
      data.forEach(row => {
        count += this.countAmount(row)
      })
      return count
    },
    footerMethod ({ columns, data }) {
      // debugger
      const sums = [];
      if (!this.summaryarry)
        return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
        return sums
      var hashj = false;
      columns.forEach((column, index) => {
        if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
          var sum = this.summaryarry[column.property + '_sum'];
          if (sum == null) return;
          sums[index] = sum
          // else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
          // else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum
          // else sums[index] = sum
        }
        else sums[index] = ''
      });
      return [sums]
    },
    footerMethod1 ({ columns, data }) {
      if (this.typeId == 1) {
        const sums = [];
        sums[2] = this.summaryy.totalQuantity;
        return [sums]
      } else {
        const sums = [];
        sums[3] = this.summaryy.totalQuantity;
        return [sums]
      }

    },
    userselect (user) {
      this.taskrow.createPackId = user.value;
      this.taskroww.selUserId = user.value;
      this.taskroww.createPackId = user.value;
      this.userid = user.value;
      this.userlist.map((item) => {
        if (item.userId == user.value) {
          this.taskroww.createUserName = item.userName;
          this.zhijiandata.createUserName = item.userName;
        }
      })
      // this.tableData2.map((item)=>{
      //   if(item.recordId == this.taskrow.recordId){
      //     item.createUserName = this.fiftername(user.value);
      //   }
      // })
      this.$forceUpdate();
    },
    formatIsCommission (value) {
      return value == null ? null : formatTime(value, 'YY-MM-DD')
    },
    async endtask (row) {
      let params = {
        processId: row.packagesProcessingId
      }
      // return
      const res = await updateFinishState(params);
      if (!res?.success) {
        return
      }
      this.$message({
        message: '更新状态成功！',
        type: 'success'
      })
      this.$emit('getTaskList');
    },
    async updateDegree (row) {
      let that = this;

      switch (row.urgencyDegree) {
        case 1://加急
          if (row.urgencyDegreeName == '加急') {
            that.$message({ message: '处于加急状态', type: "info" });
            return
          }
          break;
        case 2://待审
          this.$confirm("是否改为加急?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            distinguishCancelAndClose: true,
          })
            .then(async () => {
              let params = {
                processId: row.packagesProcessingId,
                degreeType: 1
              }
              var res = await updateUrgencyDegree(params);
              if (res?.success) {
                that.$message({ message: '更新紧急程度成功！', type: "success" });
                await this.$emit('getTaskList');
              }
            }).catch(async (e) => {
              if (e == 'cancel') {
                let params = {
                  processId: row.packagesProcessingId,
                  degreeType: 2
                }
                var res = await updateUrgencyDegree(params);
                if (res?.success) {
                  that.$message({ message: '更新紧急程度成功！', type: "success" });
                  await this.$emit('getTaskList');
                }

              } else if (e == 'close') {

              }

            });
          break;
        case 9://正常
          let params = {
            processId: row.packagesProcessingId,
            degreeType: 0
          }
          if (row.urgencyDegreeName == "正常") {
            this.$confirm("是否确认加急?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                var res = await updateUrgencyDegree(params);
                if (res?.success) {
                  that.$message({ message: '更新紧急程度成功！', type: "success" });
                  await this.$emit('getTaskList');
                }
              });
          }
          break;
      }


    },
    getuser (val) {
      this.userlist = val;
      this.getloginuser();
    },
    async getloginuser () {
      const res = await getCurrentUser();
      if (!res?.success) {
        return
      }
      this.loginuser = res.data;
    },
    fiftername (value) {
      var info = ' '
      this.userlist.forEach((item) => {
        if (item.userId == value) info = item.userName
      })
      return info;
    },
    async savecertificateInfo () {
      const res = await updateCertificateBatch({
        fileName: this.qualfifter.fileName,
        certificateUrl: this.qualfifter.certificateUrl,
        certificateInfo: this.qualfifter.msg,
        packProcessingIds: this.allid,
        noCertificate: this.nohgz ? true : false,
      });
      if (!res?.success) {
        return
      }
      this.$message({
        message: '保存成功',
        type: 'success'
      })
      this.$emit('getTaskList');
      this.qualificationshow = false;
      this.nohgz = false;
    },
    async saveworkshow () {
      this.btnloading = true;
      if (this.typeId == 2) {
        // this.outlist.detialList = [];
        // this.outlist.detialList.push(this.taskroww);
        // debugger
        this.taskroww.typeId = 2;
        if (!this.taskroww.createPackId) {
          this.taskroww.createPackId = this.loginuser.userId;
          this.taskroww.createUserName = this.loginuser.userName;
        }
        // console.log("打印数据",this.taskroww);
        // console.log("打印数据",this.outlist);
        // return
        // this.outlist.detialList[0].qua = this.outlist.wareId;
        // this.outlist.detialList[0].wareId = this.outlist.wareId;
        // this.outlist.detialList[0].wareName = this.outlist.wareName;
        // if (!this.outlist.wareId) {
        //   this.$message({ type: 'warning', message: "请选择仓库" });
        //   return
        // }
        var res = await packagesProcessQuality(this.taskroww);
        if (!res?.success) {
          this.btnloading = false;
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        this.$emit('getTaskList');
        this.workshow = false;
      } else if (this.typeId == 4) {
        this.outlist.detialList = [];
        this.outlist.detialList.push(this.taskroww);
        this.outlist.detialList[0].wareId = this.outlist.wareId;
        this.outlist.detialList[0].wareName = this.outlist.wareName;

        this.outlist.detialList[0].packaesProcessingDetialId = this.packaesProcessingDetialIds;

        this.outlist.detialList[0].produnctName = this.halfrow.halfProductName;
        this.outlist.detialList[0].produnctCode = this.halfrow.halfProductCode;
        // if (!this.outlist.wareId) {
        //   this.$message({ type: 'warning', message: "请选择仓库" });
        //   return
        // }

        var res = await pckagesProcessingCallIn(this.outlist);
        if (!res?.success) {
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        this.$emit('getTaskList');
        this.workshow = false;
      } else {
        let params = {
          packaesProcessingId: this.packagesProcessingId,
          packaesProcessingDetialIds: this.packaesProcessingDetialIds,
          packaesProcessingDetialIds: this.taskrow.id,
          ...this.taskrow,
          typeId: this.typeId
        }
        const res = await editPackagesProcessRecord(params);
        if (!res?.success) {
          return
        }
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.$emit('getTaskList');
        this.workshow = false;
      }
      this.btnloading = false;
    },

    async neisaveworkshow () {
      if (this.typeId == 2 || this.typeId == 4) {

        let params = {
          id: this.taskrow.recordId,
          ...this.taskroww,
          ...this.outlist
        }

        // if (!this.outlist.wareId) {
        //   this.$message({ type: 'warning', message: "请选择仓库" });
        //   return
        // }
        var res = await editCallInOutRecord(params);
        if (!res?.success) {
          this.btnloading = false;
          return
        }

        this.$message({ type: 'success', message: "操作成功" });
        this.$emit('getTaskList');
        this.workshow = false;
      } else if (this.typeId == 6) {
        let params = {
          packaesProcessingId: this.taskrow.packagesProcessingId,
          ...this.zhijiandata
        }
        var res = await packagesProcessQuality(params);
        if (!res?.success) {
          return
        }
        this.$emit('getTaskList');
        this.workshow = false;

      } else {
        let params = {
          packaesProcessingId: this.packagesProcessingId,
          packaesProcessingDetialIds: this.packaesProcessingDetialIds,
          ...this.taskrow,
          typeId: this.typeId
        }
        const res = await editPackagesProcessRecord(params);
        if (!res?.success) {
          return
        }
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.$emit('getTaskList');
        this.workshow = false;
      }
    },
    async shnpishowfuc (row, i, nei) {
      this.shenpishow = true;
      this.diaobodata.id = row.recordId;

      this.shenpidata.row = row;
      this.shenpidata.i = i;
      this.shenpidata.nei = nei;
      // let params = {
      //   id: row.recordId,
      // }
      // var res = await initiateApprovalCallInOut(params);
      // if (!res?.success) {
      //   return
      // }

      // this.$message({ type: 'success', message: "发起审批成功" });
      // // this.$emit('getTaskList');
      // this.workshow = false;
    },

    async approvefuc () {

      let params = {
        ...this.diaobodata,
        typeId: this.typeId == 6 ? 2 : 1 ////1半成品调入 2质检调出
      }
      var res = await initiateApprovalCallInOut(params);
      if (!res?.success) {
        return
      }

      this.$message({ type: 'success', message: "发起审批成功" });
      // this.$emit('getTaskList');
      // this.neiarr = [];
      this.shenpishow = false;
    },
    // updatelist(e) {
    //   this.tableData = e.list;
    // },
    updatelistt (e) {
      this.tableData = e.data;
      console.log("子列表获取数据99998", e)
    },
    updatelistt1 (e) {
      this.tableData = e;
    },
    openAlert (options) {
      VXETable.modal.alert(options)
    },
    qualificationshowfuc (row) {
      this.disabled = true;
      this.qualfifter.certificateUrl = row.certificateUrl;
      this.qualfifter.fileName = row.fileName;
      this.allid = [];
      this.allid.push(row.packagesProcessingId);
      this.qualfifter.msg = row.certificateInfo;
      this.qualificationshow = true;
      this.$nextTick(() => {
        this.$refs.refupfile.downmsgfuc(this.qualfifter)
      });
    },
    qualificationedit (e) {
      this.qualfifter.msg = '无合格证';
      this.nohgz = true;
      this.$nextTick(() => {
        this.savecertificateInfo();
      });
    },
    qualfieditfuc () {
      this.disabled = false
    },
    workshowfuc (row, i, nei) {
      // return;
      // return
      this.taskrow = row;
      this.taskroww.quantity = row.quantity;
      if (nei) {
        this.zhijiandata = row;
        this.taskrow.createPackId = row.createUserId;
        this.editshow = false;//可编辑
        this.isnei = true;
        this.taskroww.selUserId = row.createUserId;
      } else {
        this.taskrow.createPackId = this.loginuser.userId;
        this.editshow = true;//不可编辑
        this.isnei = false;
        this.taskroww.selUserId = this.loginuser.userId;
      }

      this.taskroww.produnctName = row.finishedProductName;
      this.taskroww.produnctCode = row.finishedProductCode;
      this.taskroww.packaesProcessingId = row.packagesProcessingId;




      this.packagesProcessingId = row.packagesProcessingId;

      if (!nei) {
        this.typeId = i
      }

      switch (i) {
        case 1:
          this.recordTitle = '加工记录';
          break;
        case 2:
          this.recordTitle = '质检记录';
          break;
        case 5:
          this.recordTitle = '收货记录';
          this.packaesProcessingDetialIds = row.id ? row.id : row.packagesProcessingDetialId;
          break;
        case 3:
          this.recordTitle = '编辑记录';
          break;
      }
      this.workshow = true;

    },
    async tableeditfuc (row, i) {
      this.typeId = i;
      this.titlerow = row;
      this.taskrow = row;
      this.tableedittitle = i == 1 ? '加工编辑' : this.tableedittitle = i == 2 ? '调出编辑' : this.tableedittitle = i == 3 ? '收货编辑'
        : this.tableedittitle = i == 4 ? '调入编辑' : this.tableedittitle = i == 6 ? '质检编辑' : '其他';
      switch (i) {
        case 1:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: i
          }
          this.tokey = 'one';
          const res = await getPackagesProcessingRecordListAsync(params);
          if (!res?.success) {
            return
          }

          // res.data.recordList.map((row)=>{
          //   if(row.createDate){
          //     row.createDate = row.createDate.slice(2,10)
          //   }
          // })
          this.tableData2 = res.data.recordList;
          this.summaryy = res.data;
          break;
        case 2:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: i
          }
          this.tokey = 'two';
          const res2 = await getCallOutRecordListAsync(params);
          if (!res2?.success) {
            return
          }
          this.tableData2 = res2.data.recordList;
          this.summaryy = res2.data;
          break;
        case 3:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            packagesProcessDetialId: row.id,
            typeId: i
          }
          this.tokey = 'three';
          const res3 = await getPackagesProcessingRecordListAsync(params);
          if (!res3?.success) {
            return
          }
          this.tableData2 = res3.data.recordList;
          this.summaryy = res3.data;
          break;
        case 4:
          this.halfrow = row;
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: 1,
            packagesProcessDetialId: row.id
            // packaesProcessingDetialId: row.id

          }
          this.tokey = 'four';
          const res4 = await getCallOutRecordListAsync(params);
          if (!res4?.success) {
            return
          }

          this.tableData2 = res4.data.recordList;
          this.summaryy = res4.data;
          break;
        case 6:
          var params = {
            packagesProcessId: row.packagesProcessingId,
            typeId: 2
          }
          this.tokey = 'six';
          const res6 = await getPackagesProcessingRecordListAsync(params);
          if (!res6?.success) {
            return
          }
          this.tableData2 = res6.data.recordList;
          this.summaryy = res6.data;
          break;
      }
      this.tableeditshow = true;
    },
    doubleclick (e) {
      this.editTaskshow = true;
      this.$nextTick(() => {
        this.$refs.refdialogright.getfamsg(e);
      })

    },
    //关闭窗口，初始化数
    async onCloseAddForm (type) {
      await this.$emit('getTaskList');
      if (type == 1) {
        // this.addTask = false;
        this.editTaskshow = false;
      }
    },
    copytext (e) {
      let textarea = document.createElement("textarea")
      textarea.value = e
      textarea.readOnly = "readOnly"
      document.body.appendChild(textarea)
      textarea.select()
      let result = document.execCommand("copy")
      if (result) {
        this.$message({
          message: '复制成功',
          type: 'success'
        })
      }
      textarea.remove()
    },
    neitablesel (data) {
      var arr = [];
      data.records.map((item) => {
        arr.push(item.recordId)
      })
      this.neiarr = arr;
      this.diaobodata.id = arr.join();
    },
    checkchange (data) {
      this.$nextTick(() => {
        this.$emit('pids', data.records);
      });
    },
    chilcheckchange (data) {
      this.$emit('chipids', data.records);
    },
    async delRecord (row) {
      this.$confirm("是否确定删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let params = {
          recordIds: row.recordId,
          typeId: (this.typeId == 1 || this.typeId == 3 || this.typeId == 6) ? 0 : 1
        }
        var res = await deleteReord(params);
        if (!res?.success) {
          return
        }
        this.tableData2.splice(this.tableData2.indexOf(row), 1)
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.$emit('getTaskList');
      });
    },
    clickRefresh () {
      this.$emit('getPriceTemplateListfuc');
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 20px;
}

::v-deep .vxe-table--render-default .vxe-header--column {
  line-height: 18px !important;
}

/*滚动条整体部分*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 18px !important;
  height: 26px !important;
}

/*滚动条的轨道*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
  background-color: #f1f1f1 !important;
}

/*滚动条里面的小方块，能向上向下移动*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
  background-color: #c1c1c1 !important;
  border-radius: 3px !important;
  box-sizing: border-box !important;
  border: 2px solid #F1F1F1 !important;
  box-shadow: inset 0 0 6px rgba(255, 255, 255, .5) !important;
}

// 滚动条鼠标悬停颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
  background-color: #A8A8A8 !important;
}

// 滚动条拖动颜色
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
  background-color: #787878 !important;
}

/*边角，即两个滚动条的交汇处*/
::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
  background-color: #dcdcdc !important;
}


// 表格内边距
::v-deep .vxe-table--render-default .vxe-cell {
  padding: 0 0 0 8px !important;
}





.flexrow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.qualibtn ::v-deep .el-button {
  margin-left: 0 !important;
  margin-top: 10px;
}

.marginrt {
  margin: 0 10px 0 auto;
}

.point:hover {
  cursor: pointer;
}

.point {
  color: #409EFF;
}


.item {
  margin-bottom: 18px;
}

.clearfix {
  font-size: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

::v-deep .box-card {
  margin-top: 20px;
  box-sizing: border-box;
  padding: 0 30px;

}

.flexcenter {
  display: flex;
  justify-content: left;
  align-items: left;
}

::v-deep .height {
  height: 58px !important;
}

::v-deep .height1 {
  height: 48px !important;
  font-size: 12px !important;
}

::v-deep .cellheight1 {
  font-size: 12px !important;
}

.relativebox {
  position: relative;
  width: 100%;
}

.relativeboxx {
  position: relative;
  width: 100%;
}

.positioncenter {
  position: absolute;
  right: 10px;
  top: 28%;
  bottom: 50%;
  // transform: translate(-50%,-50%);
}

::v-deep .droprow td {
  color: rgb(250, 9, 9);
  position: relative;
}

::v-deep .droprow ::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 0.1px;
  background-color: rgb(250, 9, 9);
  transform: translateY(-50%);
}


.copyhover {
  display: none;
}

.relativebox {
  width: 250px;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover {
  width: 225px;
}

.textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .textover {
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.relativebox:hover .copyhover {
  display: block;
  position: absolute;
  top: 50%;
  left: 80%;
  margin: 0 10px;
  z-index: 99;
  transform: translate(-50%, -50%);
  color: #409EFF;
  font-weight: 600;
}

// .copyhover:hover>.copyhover{
//   display: block;

//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%,-50%);
// }

.minibtn ::v-deep .el-button--mini {
  padding: 7px 8px;
}

.vxeclass {
  z-index: 10000 !important;
}

::v-deep .vxe-body--expanded-cell {
  padding: 0 !important;
}

.el-icon-document-copy {
  font-size: 16px;
  margin-left: 2px;
}

.el-icon-document-copy:hover {
  font-size: 16px;
  margin-left: 2px;

}

::v-deep .vxe-modal--header {
  margin-top: 8px !important;
  background-color: transparent !important;
  padding: 0 6px;

}

::v-deep .vxe-modal--header-title {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

::v-deep .vxe-modal--header-right {
  // color: transparent ;
  font-size: 12px;
  line-height: 32px;
}

::v-deep .vxe-modal--content {
  padding: 20px 35px 35px 35px;
}

::v-deep .bzbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}

::v-deep .rwmc {
  // width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
  display: flex;
}

::v-deep .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

.tablehei {
  height: 80vh;
}

::v-deep .rwmc .mc,
::v-deep .icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-drawer .el-drawer__header {
  padding: 0 !important;
}

::v-deep .vxe-footer--row {
  height: 50px;
}

.statuscss ::v-deep .el-tag--dark {
  border-color: #fff !important;
}

.minisize ::v-deep .vxe-button {
  padding: 0 !important;
}

::v-deep .el-main {
  overflow: hidden;
}

::v-deep .bzjzcjrw {
  width: 100%;
  margin-top: 15px;
  background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
  height: 40px;
  /* background-color: aquamarine; */
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 0 35px;
}

::v-deep .bzjzcjrw .bzccjlx {
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
  width: 100%;
  height: 35px;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
  width: 20%;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
  width: 80%;
}</style>

