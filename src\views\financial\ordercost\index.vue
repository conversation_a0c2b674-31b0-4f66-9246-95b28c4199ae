<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="结算月份:">
            <el-date-picker style="width: 120px" v-model="filter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择"
            :picker-options="pickOptions" @change="onSearch"></el-date-picker>
        </el-form-item>
        <el-form-item label="计算状态:">
            <el-select v-model="filter.computeStatus" placeholder="计算状态" style="width: 200px" :clearable="true"  @change="onSearch">
                <el-option v-for="item in computeStatusList" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

  <el-tabs v-model="activeName" style="height: 94%;">
    <el-tab-pane label="快递费" name="ordernoexpressfee" style="height: 100%;">
       <ordernoexpressfee :filter="filter" ref="ordernoexpressfee" @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch'  @ondeleteByShop='ondeleteByShop' @onstartcomput='onstartcomput'
        :shopList="shopList" @changePlatform="changePlatform" :platformList="platformList" @onExport="onExport"/>
    </el-tab-pane>
    <el-tab-pane label="特殊类别" name="virtualtype" style="height: 100%;">
       <virtualtype :filter="filter" ref="virtualtype" @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="包装均值" name="packagemonthaveragecost" style="height: 100%;">
       <packagemonthaveragecost :filter="filter" ref="packagemonthaveragecost" @onstartImport='onstartImport' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="计算" name="compute" style="height: 100%;">
       <compute :filter="filter" ref="compute" @onstartcomput='onstartcomput' :shopList="shopList" @changePlatform="changePlatform" :platformList="platformList"/>
    </el-tab-pane>
    <el-tab-pane label="月报" name="computeresult" style="height: 100%;">
       <computeresult :filter="filter" ref="computeresult" @onstartcomput='onstartcomput' :shopList="shopList" @changePlatform="changePlatform" :platformList="platformList" @onExport="onExport"/>
    </el-tab-pane>
  </el-tabs>

   <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
        <el-form class="ad-form-query" :inline="true" :model="importDialog.filter" @submit.native.prevent>
        <el-form-item label="结算月份:">
            <el-date-picker style="width: 120px" v-model="importDialog.filter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择" :picker-options="pickOptions">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="平台:">
                <el-select
                  v-model="importDialog.filter.platform"
                  placeholder="平台" style="width: 100px"  @change="changePlatform"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in platformList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
        </el-form-item>
        <el-form-item label="店铺:" v-if="importDialog.filter.platform != 11">
                <el-select
                  v-model="importDialog.filter.shopCode"
                  placeholder="店铺" style="width: 200px"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in shopList"
                    :key="item.shopCode"
                    :label="item.shopName"
                    :value="item.shopCode"/>
                </el-select>
        </el-form-item>
      </el-form>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile"
                  :file-list="fileList"
                  :data="fileparm">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                  <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
                </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
          <el-form class="ad-form-query" :inline="true" :model="deletefilter" @submit.native.prevent label-position="right" label-width="100px">
            <el-form-item label="结算月份:">
                <el-date-picker style="width: 200px" v-model="deletefilter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择"></el-date-picker>
            </el-form-item>
            <el-form-item label="平台:">
                <!-- <el-select
                  v-model="deletefilter.shopCode" 
                  placeholder="店铺" style="width: 200px"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in shopList"
                    :key="item.shopCode"
                    :label="item.shopName"
                    :value="item.shopCode"/>
                </el-select> -->
                <el-select v-model="deletefilter.platform" placeholder="平台" style="width: 100px" :clearable="true" :collapse-tags="true" filterable>
                    <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item >
                <el-button type="primary" @click="deleteByBatch">删除</el-button>
            </el-form-item>
          </el-form>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span> -->
    </el-dialog>
    <el-dialog title="单店删除" :visible.sync="dialogdeleteShopCodeVisible" width="500px" v-dialogDrag>
          <el-form class="ad-form-query" :inline="true" :model="deletefilter" @submit.native.prevent label-position="right" label-width="100px">
            <el-form-item label="结算月份:">
                <el-date-picker style="width: 200px" v-model="deletefilter.settMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择"></el-date-picker>
            </el-form-item>
            <el-form-item label="平台:">
                <el-select v-model="deletefilter.platform" placeholder="平台" style="width: 100px" :clearable="true" :collapse-tags="true" filterable>
                    <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="店铺:">
                <el-select
                  v-model="deletefilter.shopCode" 
                  placeholder="店铺" style="width: 200px"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in shopList"
                    :key="item.shopCode"
                    :label="item.shopName"
                    :value="item.shopCode"/>
                </el-select>
            </el-form-item>
            <el-form-item >
                <el-button type="primary" @click="deleteByShop">删除</el-button>
            </el-form-item>
          </el-form>
    </el-dialog>
    <el-dialog title="计算分摊" :visible.sync="dialogcomputVisible" width="40%" v-dialogDrag>
      <el-form class="ad-form-query" :inline="true" :model="computfilter" @submit.native.prevent>
            <el-form-item label="平台:">
                <el-select
                  v-model="computfilter.platform"
                  placeholder="平台" style="width: 90px"  @change="changePlatform"
                  :clearable="true" :collapse-tags="true"  filterable>
                  <el-option
                    v-for="item in platformList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
            </el-form-item>
            <el-form-item label="结算月份:">
                <el-date-picker style="width: 100%" v-model="computfilter.settMonth" type="month" format="yyyyMM"
                  value-format="yyyyMM" placeholder="选择月份" :picker-options="pickOptions"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="月报类型">
              <el-select filterable v-model="computfilter.Version" placeholder="类型" style="width: 100px">
                <el-option label="工资月报" value="v1"></el-option>
                <el-option label="参考月报" value="v2"></el-option>
            </el-select>
            </el-form-item>
            <el-form-item >
                <el-button style="float:right" type="primary" @click="oncomput">计算分摊</el-button>
            </el-form-item>
          </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogcomputVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import {importOrderNoExpressFee,deleteOrderNoExpressFee,computeOrderCost,computePlatformDeduction,exportOrderNoExpressFee,exportOrderCostShare,deleteOrderNoExpressFeeByShop} from '@/api/financial/ordercost'
import ordernoexpressfee from '@/views/financial/ordercost/ordernoexpressfee'
import virtualtype from '@/views/financial/ordercost/virtualtype'
import packagemonthaveragecost from '@/views/financial/ordercost/packagemonthaveragecost'
import compute from '@/views/financial/ordercost/compute'
import computeresult from '@/views/financial/ordercost/computeresult'
import container from '@/components/my-container/nofooter'
import dayjs from "dayjs";
import { computProductFreeToBigData, importExpressFee_FenXiaoAsync } from '@/api/monthbookkeeper/financialDetail'
import { formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform} from "@/utils/formruletools";

var curMonth =formatTime1(dayjs().startOf("month").subtract(1,"month"),"yyyyMM");
export default {
  name: 'Roles',
  components: {container,ordernoexpressfee,virtualtype,packagemonthaveragecost,compute,computeresult},
  data() {
    return {
      activeName: 'ordernoexpressfee',
      filter: {
        startTime: null,
        endTime: null,
        settMonth:curMonth,
        timerange:null,
        shopCode:'',
        orderNo:'',
        Version:null,
        platform:null
      },
      deletefilter: {shareFeeType:0,batchNumber:'', shopCode: '' },
      computfilter: {settMonth:'',shareFeeType:0,platform:'', Version:'' },
      expresscompanylist: [],
      onimportfilter: {shareFeeType:0 },
      pageLoading: false,
      dialogVisible: false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
      dialogdeleteShopCodeVisible:false,
      dialogcomputVisible:false,
      importDialog:{
        filter:{
          settMonth:curMonth,
          platform:1,
          shopCode:'',
        }
      },
      pickOptions:{
        disabledDate(time){
          return time.getTime() > Date.now()
        }
      },
      shopList:[],
      platformList:[],
      computeStatusList:[
        {label:"已计算",value:1},
        {label:"未计算",value:0},
      ],
    }
  },
  async mounted() {
    await this.setPlatform();
    await this.setShopList();
  },
  methods: {
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    async changePlatform(val){
      await this.setShopList(val);
    },
    //设置店铺下拉
    async setShopList(val){
      const res = await getshopList({platform:val,CurrentPage:1,PageSize:2000});
      this.shopList=res.data.list;
      this.filter.shopCode='';
    },
    onSearch() {
      this.filter.startTime=null;
      this.filter.endTime=null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      switch (this.activeName){
        case "ordernoexpressfee":
          this.$refs.ordernoexpressfee.onSearch();
          break;
        case "virtualtype":
          this.$refs.virtualtype.onSearch();
          break;
        case "packagemonthaveragecost":
          this.$refs.packagemonthaveragecost.onSearch();
          break;
        case "compute":
          this.$refs.compute.onSearch();
          break;
        case "computeresult":
          this.$refs.computeresult.onSearch();
          break;
      }
    },
   async onstartImport(shareFeeType){
      this.dialogVisible=true;
      this.onimportfilter.shareFeeType=shareFeeType;
      await this.setShopList(this.importDialog.filter.platform);
    },
    async ondeleteByBatch(shareFeeType) {
        this.dialogdeletebatchNumberVisible=true;
        this.deletefilter.shareFeeType=shareFeeType;
        this.deletefilter.batchNumber='';
    },
    async ondeleteByShop(shareFeeType) {
        this.dialogdeleteShopCodeVisible=true;
        this.deletefilter.shareFeeType=shareFeeType;
        this.deletefilter.shopCode='';
    },
   async onstartcomput(shareFeeType) {
        this.dialogcomputVisible=true;
        this.computfilter.shareFeeType=shareFeeType;
        this.computfilter.yearmonth='';
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if(!this.importDialog.filter.settMonth){
        this.$message({message: "请选择结算月份", type: "warning" });
        return false;
      }
      if (!this.importDialog.filter.platform) {
       this.$message({type: 'warning',message: '请选择平台!'});
       return;
      }
      if (!this.importDialog.filter.shopCode) {
       this.$message({type: 'warning',message: '请选择店铺!'});
       return;
      }
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
      if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading=true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("settMonth", this.importDialog.filter.settMonth);
      form.append("platform", this.importDialog.filter.platform);
      form.append("shopCode", this.importDialog.filter.shopCode);
      var res;
      if(this.importDialog.filter.platform == 11){
        res = await importExpressFee_FenXiaoAsync(form);
      }else{
      if (this.onimportfilter.shareFeeType==0) res= await importOrderNoExpressFee(form);
      }
      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      //else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
  async deleteByBatch() {
      if (!this.deletefilter.settMonth) {
       this.$message({type: 'warning',message: '请输入结算月份!'});
       return;
      }
      if (!this.deletefilter.platform) {
       this.$message({type: 'warning',message: '请选择平台!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            var res;
            var params={...this.deletefilter};
            switch (this.activeName){
              case "ordernoexpressfee":
                res = await deleteOrderNoExpressFee(params)
                break;
              case "virtualtype":
                break;
              case "packagemonthaveragecost":
                break;
              case "compute":
                break;
              case "computeresult":
                break;
            }

            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    async deleteByShop() {
      if (!this.deletefilter.settMonth) {
       this.$message({type: 'warning',message: '请输入结算月份!'});
       return;
      }
      if (!this.deletefilter.shopCode) {
       this.$message({type: 'warning',message: '请选择店铺!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            var res;
            var params={...this.deletefilter};
            switch (this.activeName){
              case "ordernoexpressfee":
                res = await deleteOrderNoExpressFeeByShop(params)
                break;
              case "virtualtype":
                break;
              case "packagemonthaveragecost":
                break;
              case "compute":
                break;
              case "computeresult":
                break;
            }
            if (!res?.success) {return}
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    async oncomput(){
      if (!this.computfilter.settMonth) {
        this.$message({type: 'warning',message: '请选择年月!'});
        return;
      }
      this.$confirm('确认计算分摊, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
           var res;
           if(this.computfilter.platform==11){
               res = await computProductFreeToBigData({...this.computfilter,yearMonth:this.computfilter.settMonth})
           }else{
           if(this.computfilter.shareFeeType==10){
               res = await computePlatformDeduction(this.computfilter)
           }
           else{
               res = await computeOrderCost(this.computfilter)
           }
          }
            if (!res?.success) {return }
            this.$message({type: 'success',message: '提交成功,正在后台计算分摊...'});
            this.onSearch();
        }).catch(() => {
          this.$message({type: 'info',message: '已取消计算'});
        });
    },
    async onExport(){
      if(!this.filter.settMonth){
        this.$message({message: "请选择结算月份", type: "warning" });
        return false;
      }
      var res;
      var fileName="";
      this.filter.startTime=null;
      this.filter.endTime=null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var params={
        ...this.filter
      };
      switch (this.activeName){
        case "ordernoexpressfee":
          res = await exportOrderNoExpressFee(params);
          fileName="原始订单快递费_"+this.filter.settMonth;
          break;
        case "virtualtype":
          break;
        case "packagemonthaveragecost":
          break;
        case "compute":
          break;
        case "computeresult":
          res = await exportOrderCostShare(params);
          fileName="订单费用_"+this.filter.settMonth;
          break;
      }
      if(!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download',fileName+'_' + new Date().toLocaleString() + '.xlsx' )
      aLink.click();
    }
  }
}
</script>
