<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table style="height:90%" :ref="tablekey" :key="tablekey" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='newOrderList' :isSelection='false' :summaryarry="summaryarry"
            :tableCols='tableCols' :loading="listLoading" :isSelectColumn="true">
            <template slot='extentbtn'>
                <el-button-group>
                    <template v-if="isshowAll">
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="filter.orderSituation" placeholder="订单情况" style="width:90px;" clearable>
                                <el-option label="正常" :value="1"></el-option>
                                <el-option label="停发" :value="4"></el-option>
                                <el-option label="预售" :value="2"></el-option>
                                <el-option label="缺货" :value="3"></el-option>
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="timeType" placeholder="请选择周期" style="width:70px;"
                                @change="changeTimeType()">
                                <el-option v-for="item in cycleList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="filter.timeRangeType" placeholder="请选择时长类型" style="width:100px;"
                                @change="changeTimeRangeType()">
                                <el-option v-for="item in timeRangeTypeList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;" v-if="false">
                            <el-select v-model="filter.timeFrameType" clearable
                                :placeholder="'请选择' + timeRangeTypeList[filter.timeRangeType].selectLabel + '范围'"
                                style="width:145px;" @change="changeTimeFrame()">
                                <el-option v-for="item in timeFrameTypeList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-date-picker style="width: 320px" v-model="filter.timerange" type="datetimerange"
                                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至"
                                start-placeholder="开始申请时间" end-placeholder="结束申请时间" :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="内部订单号" v-model="filter.orderNoInner" style="width: 100px"
                                clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;">
                            <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform"
                                clearable style="width: 70px">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;">
                            <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable style="width: 100px">
                                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                    :value="item.shopCode" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓"
                                style="width: 140px">
                                <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <!-- <el-button style="padding: 0;margin: 0; border: 0;" :hidden="true">
                            <el-input type="number" placeholder="最小拣货次数" v-model="filter.minPickCount" style="width: 130px" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>**********){value=**********}" clearable>
                            </el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;" :hidden="true">
                            <el-input type="number" placeholder="最大拣货次数" v-model="filter.maxPickCount" style="width: 130px" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>**********){value=**********}" clearable>
                            </el-input>
                        </el-button> -->
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="订单标签包含" v-model="filter.label" style="width: 120px" clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="订单标签不包含" v-model="filter.labelNo" style="width: 140px"
                                clearable></el-input>
                        </el-button>
                        <el-button style="border: 0;">
                            <el-checkbox v-model="filter.isLabelYbz">有备注</el-checkbox>
                        </el-button>
                    </template>
                    <template v-if="!(isshowpick) && !(isshowAll)">
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-date-picker style="width: 260px" v-model="filter.timerange" type="datetimerange"
                                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd" range-separator="至"
                                :start-placeholder="timestartholder" :end-placeholder="timeendholder">
                            </el-date-picker>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="内部订单号" v-model="filter.orderNoInner" style="width: 110px"
                                clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;">
                            <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform"
                                clearable style="width: 70px">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;">
                            <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable style="width: 110px">
                                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                    :value="item.shopCode" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓"
                                style="width: 140px">
                                <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input type="number" placeholder="最小拣货次数" v-model="filter.minPickCount" style="width: 135px"
                                oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>**********){value=**********}"
                                clearable>
                            </el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input type="number" placeholder="最大拣货次数" v-model="filter.maxPickCount" style="width: 135px"
                                oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>**********){value=**********}"
                                clearable>
                            </el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="filter.selectedNodeIndex" filterable placeholder="节点" style="width:120px"
                                @change="selectedNodeChange">
                                <el-option label="审单" :value="0"></el-option>
                                <el-option label="打单" :value="1"></el-option>
                                <el-option label="等待波次" :value="5"></el-option>
                                <!--     <el-option label="波次配货" :value="4"></el-option> -->
                                <el-option label="配货" :value="2"></el-option>
                                <el-option label="发货" :value="3"></el-option>
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="订单标签包含" v-model="filter.label" style="width: 120px" clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="订单标签不包含" v-model="filter.labelNo" style="width: 140px"
                                clearable></el-input>
                        </el-button>
                    </template>
                    <template v-if="isshowpick">
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="拣货员" v-model="filter.picker" style="width: 110px" clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="商品编码" v-model="filter.goodsCode" style="width: 110px"
                                clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input placeholder="商品名称" v-model="filter.goodsName" style="width: 110px"
                                clearable></el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓"
                                style="width: 140px">
                                <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input type="number" placeholder="最小拣货时长" v-model="filter.minPickTime" style="width: 135px"
                                clearable>
                            </el-input>
                        </el-button>
                        <el-button style="padding: 0;margin: 0; border: 0;">
                            <el-input type="number" placeholder="最大拣货时长" v-model="filter.maxPickTime" style="width: 135px"
                                clearable>
                            </el-input>
                        </el-button>
                    </template>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button v-if="checkPermission('PressNewOrderNodeExPort')" type="primary"
                        @click="ExPortExecl">导出</el-button>
                </el-button-group>
            </template>

            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
            <template>
                <my-container v-loading="echarLoading">
                    <el-tabs v-model="activeName" @tab-click="tabclick" type="card" ref="tabs">
                        <el-tab-pane :label="timeRangeTypeList[filter.timeRangeType].label" name="one">
                            <!-- <el-alert v-if="activeName==='one'" style="height:30px;" :title="'温馨提示：24小时='+timeRangeTypeList[filter.timeRangeType].selectLabel+'0~24小时内的订单 48小时='+timeRangeTypeList[filter.timeRangeType].selectLabel+'24~48小时内的订单 48小时外='+timeRangeTypeList[filter.timeRangeType].selectLabel+'大于48小时的订单'" type="warning" show-icon :closable="false">
                            </el-alert> -->
                            <div v-if="activeName === 'one'" :id="'buscharone' + randrom" ref="refbuscharone"
                                style="height:350px;width:100%"></div>
                        </el-tab-pane>
                        <el-tab-pane label="审单" name="two">
                            <div v-if="activeName === 'two'" :id="'buschartwo' + randrom" ref="refbuschartwo"
                                style="height:350px;width:100%"></div>
                        </el-tab-pane>
                        <el-tab-pane label="打单" name="three">
                            <div v-if="activeName === 'three'" :id="'buscharthree' + randrom" ref="refbuscharthree"
                                style="height:350px;width:100%"></div>
                        </el-tab-pane>
                        <el-tab-pane label="等待波次" name="four">
                            <div v-if="activeName === 'four'" :id="'buscharfour' + randrom" ref="refbuscharfour"
                                style="height:350px;width:1770px"></div>
                        </el-tab-pane>
                        <el-tab-pane label="波次配货" name="five">
                            <div v-if="activeName === 'five'" :id="'buscharfive' + randrom" ref="refbuscharfive"
                                style="height:350px;width:1770px"></div>
                        </el-tab-pane>
                        <el-tab-pane label="拣货" name="six">
                            <div v-if="activeName === 'six'" :id="'buscharsix' + randrom" ref="refbuscharsix"
                                style="height:350px;width:100%"></div>
                        </el-tab-pane>
                        <el-tab-pane label="打包" name="seven">
                            <div v-if="activeName === 'seven'" :id="'buscharseven' + randrom" ref="refbuscharseven"
                                style="height:350px;width:100%"></div>
                        </el-tab-pane>
                        <el-tab-pane label="补货分析" name="eight">
                            <div v-if="activeName === 'eight'" :id="'buschareight' + randrom" ref="refbuschareight"
                                style="height:350px;width:100%"></div>
                        </el-tab-pane>
                    </el-tabs>
                </my-container>
            </template>
        </template>
        <el-dialog :title="'全部订单-' + timeRangeTypeList[filter.timeRangeType].label" v-if="dialogVisible"
            :visible.sync="dialogVisible" width="90%" height="700px" v-dialogDrag>
            <orderDialog ref="orderDialog" :filter="dialogFilter" style="z-index:15000;height:700px" />
        </el-dialog>
        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <orderLogPage ref="orderLogPage" :orderNoInner="sendOrderNoInner" style="z-index:10000;height:600px" />
        </el-dialog>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import {
    getTbWarehouseList, getNewOrderList, getOrderRepairList,
    getNewAllOrderNotesTypeListMapNew, getNewOrderNotesTypeListMap, getNewOrderNotesRepairListMap,
    exportOrderRepairAsync, exportOrderNoteAsync
} from '@/api/order/newordernodes';
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import orderDialog from '@/views/order/newordernodes/orderDialog'
import orderLogPage from "@/views/order/logisticsWarning/orderLogPage.vue";
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { platformlist, orderpositionlist } from '@/utils/tools';//enmSendWarehouse as warehouselist,
import * as echarts from 'echarts';

const _startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD 00:00:00");
const _endDate = formatTime(new Date(), "YYYY-MM-DD 23:59:59");
export default {
    name: "newOrderNodeIndexGrid",
    components: { MyContainer, cesTable, orderDialog, orderLogPage },
    props: {
        tableCols: [],
        tablekey: {
            type: String,
            default: ''
        },
        orderImportType: {
            type: Number,
            default: 0
        },
        isshowAll: {
            type: Boolean,
            default: false
        },
        isshowJh: {
            type: Boolean,
            default: false
        },
        isshowpick: {
            type: Boolean,
            default: false
        },
        isshowZt: {
            type: Boolean,
            default: false
        },
        isshowCj: {
            type: Boolean,
            default: false
        },
        isshowOther: {
            type: Boolean,
            default: false
        }


    },
    data() {
        return {
            dialogVisible: false,
            dialogFilter: {
                orderSituation: null,
                timeRangeType: 0,
                seriesTime: null,
                seriesTimeType: null,
                seriesTimeRangeType: null,
                orderNoInner: null,
                platform: null,
                shopCode: null,
                warehouse: null,
                minPickTime: null,
                maxPickTime: null,
                label: null,
                labelNo: null
            },
            timestartholder: '发货开始日期',
            timeendholder: '发货结束日期',
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            selids: [],
            newOrderList: [],
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "orderNoInner", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            echarLoading: false,
            pageLoading: false,
            allOrderDataAnalysis: null,
            filter: {
                orderSituation: null,
                timeRangeType: 0,
                timeFrameType: null,
                startTime: null,
                endTime: null,
                timerange: [_startDate, _endDate],
                orderNoInner: null,
                platform: null,
                shopCode: null,
                warehouse: null,
                selectedNodeIndex: 0,
                orderPosition: null,
                flag: null,
                picker: null,
                goodsCode: null,
                goodsName: null,
                minPickTime: null,
                maxPickTime: null,
                OrderImportType: 0,
                chartype: 0,
                isLabelYbz: false,
            },
            timeType: 0,
            alertTitle: "温馨提示：24小时=在仓时长0~24小时内的订单 48小时=在仓时长24~48小时内的订单 48小时外=在仓时长大于48小时的订单",
            shopList: [],
            platformlist: platformlist,
            warehouselist: [],//warehouselist,
            orderpositionlist: orderpositionlist,
            //echars数据
            activeName: "one",
            activeNameList: ["one", "two", "three", "four", "five", "six", "seven", "eight"],
            echarNameList: ["buscharone", "buschartwo", "buscharthree", "buscharfour", "buscharfive", "buscharsix", "buscharseven", "buschareight"],
            chardata: [],
            myChart1: null,
            randrom: "",
            //审单
            checkedColumnsFor0: ['内部订单号', '线上订单号', '店铺名称', '付款时间', '审单时间', '审单时长', '审单人员', '订单标签', '拣货次数'],
            //打单
            checkedColumnsFor1: ['内部订单号', '线上订单号', '店铺名称', '付款时间', '打单时间', '打单时长', '打单人员', '订单标签', '拣货次数'],
            //等待波次
            checkedColumnsFor5: ['内部订单号', '线上订单号', '店铺名称', '付款时间', '波次等待时长', '订单标签', '拣货次数'],
            //波次配货
            checkedColumnsFor4: ['内部订单号', '线上订单号', '店铺名称', '付款时间', '波次时长', '波次操作人', '订单标签', '拣货次数'],
            //配货
            checkedColumnsFor2: ['内部订单号', '线上订单号', '店铺名称', '付款时间', '配货时间', '配货时长', '配货人', '订单标签', '拣货次数'],
            //发货
            checkedColumnsFor3: ['内部订单号', '线上订单号', '店铺名称', '付款时间', '发货时间', '发货人', '订单标签', '拣货次数'],
            checkedColumnsFor: [
                '内部订单号', '线上订单号', '店铺名称', '付款时间',
                '影响原因',
                '审单时间',
                '补货完成时间', '生成批次时间',
                '打单时间', '配货时间', '发货时间', '领批次时间', '批次结束时间', '称重时间', '审单时长',
                '补货时长', '生成批次时长',
                '打单时长', '波次等待时长', '波次时长',
                '配货时长', '打包时长', '审单人员', '打单人员', '配货人', '波次操作人', '发货人', '拣货次数', '在仓时长', '订单标签', '单据类型'],
            cycleList: [{ label: '日', value: 0 }, { label: '周', value: 1 }, { label: '月', value: 2 }],
            timeRangeTypeList: [{ label: '在仓汇总', value: 0, selectLabel: '在仓时长' }, { label: '审单汇总', value: 1, selectLabel: '审单时长' }, { label: '打单汇总', value: 2, selectLabel: '打单时长' }, { label: '配货汇总', value: 3, selectLabel: '配货时长' }, { label: '打包汇总', value: 4, selectLabel: '打包时长' }],
            timeFrameTypeList: [{ label: '0~24小时', value: 1 }, { label: '24~48小时', value: 2 }, { label: '48小时之外', value: 3 }],
            styleColor: [{ color: '#5470c6' }, { color: '#91cc75' }, { color: '#fac858' }, { color: '#ee6666' }, { color: '#73c0de' }, { color: '#3ba272' }, { color: '#fc8452' }, { color: '#9a60b4' }, { color: '#ea7ccc' }, { color: '#b97a57' }, { color: '#400080' }, { color: '#808000' }],
            dialogHisVisible: false,
            sendOrderNoInner: "",
        };
    },
    async mounted() {
        await this.getInventoryWareHouseList();
        if (this.isshowpick) {
            this.timestartholder = "开始日期"; this.timeendholder = "结束日期";
            this.pager = { OrderBy: "bcBatchNumber", IsAsc: false };
        }
        await this.onSearch();
        this.showtab();
    },
    watch: {

    },
    created() {
        var e = 10;
        var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
            a = t.length,
            n = "";
        for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
        this.randrom = n;
    },
    methods: {
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        if (f.name.indexOf("代发") <= -1 &&
                            f.name.indexOf("罗兵邮邮仓") <= -1 &&
                            f.name.indexOf("JD-昀晗义乌仓") <= -1 &&
                            f.name.indexOf("昀晗-包装厂") <= -1
                        )
                            this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        async changeTimeRangeType() {
            if (this.filter.timeFrameType) {
                await this.onSearch();
            } else {
                await this.showprchart();
            }
        },
        showLogDetail(row) {
            this.dialogHisVisible = true;
            this.sendOrderNoInner = row.orderNoInner;
        },
        async changeTimeType() {
            var chartype = this.getCharType();
            this.echarLoading = true;
            const finaldata = this.Getoptions(this.allOrderDataAnalysis[this.timeType]);
            await this.initchartsline(finaldata, this.echarNameList[chartype]);
            this.echarLoading = false;
        },
        async changeTimeFrame() {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            else {
                this.filter.startTime = null;
                this.filter.endTime = null;
            }
            this.filter.OrderImportType = this.orderImportType;
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ...this.filter,
            };
            this.pageLoading = true;
            let res;
            if (this.orderImportType == 4) {
                res = await getOrderRepairList(params);
            } else {
                res = await getNewOrderList(params);
            }
            this.ShowHideonSearch();
            this.pageLoading = false;
            this.total = res.total
            this.newOrderList = res.list;
            this.summaryarry = res.summary;
        },
        showtab() {
            if (this.isshowAll) {
                this.$refs.tabs.$children[0].$refs.tabs[0].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[1].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[2].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[3].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[4].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[5].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[6].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[7].style.display = 'none';
                this.activeName = "one";
            }
            else if (this.isshowZt || this.isshowJh) {
                this.$refs.tabs.$children[0].$refs.tabs[0].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[1].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[2].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[3].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[4].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[5].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[6].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[7].style.display = 'none';
                this.activeName = "two";
            }
            else if (this.isshowCj || this.isshowOther) {
                this.$refs.tabs.$children[0].$refs.tabs[0].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[1].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[2].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[3].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[4].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[5].style.display = 'inline';
                this.$refs.tabs.$children[0].$refs.tabs[6].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[7].style.display = 'none';
                this.activeName = "two";
            } else if (this.isshowpick) {
                this.$refs.tabs.$children[0].$refs.tabs[0].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[1].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[2].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[3].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[4].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[5].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[6].style.display = 'none';
                this.$refs.tabs.$children[0].$refs.tabs[7].style.display = 'inline';
                this.activeName = "eight";
            }
        },
        async selectedNodeChange(val) {
            if (val == 0) {
                this.timestartholder = "审单开始日期"; this.timeendholder = "审单结束日期";
            }
            else if (val == 1) {
                this.timestartholder = "打单开始日期"; this.timeendholder = "打单结束日期";
            }
            else if (val == 2) {
                this.timestartholder = "配货开始日期"; this.timeendholder = "配货结束日期";
            }
            else if (val == 3) {
                this.timestartholder = "发货开始日期"; this.timeendholder = "发货结束日期";
            }
            //this.ShowHideonSearch();
        },
        async onchangeplatform(val) {
            this.categorylist = []
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },

        sortchange(column) {
            if (!column.order) {
                this.pager = {};
            }
            else {
                var orderBy = column.prop;
                if (orderBy.endsWith("HH")) {
                    orderBy = orderBy.replace("HH", "");
                }
                this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            else {
                // this.filter.startTime = null;
                // this.filter.endTime = null;
                this.$message({ message: "请选择日期", type: "warning" });
                return;
            }
            this.filter.OrderImportType = this.orderImportType;
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ...this.filter,
            };
            this.pageLoading = true;
            let res;
            if (this.orderImportType == 4) {
                res = await getOrderRepairList(params);
            } else {
                res = await getNewOrderList(params);
            }
            this.ShowHideonSearch();
            this.pageLoading = false;
            this.total = res.total
            this.newOrderList = res.list;
            this.summaryarry = res.summary;
            await this.showprchart();
        },
        ShowHideonSearch() {
            this.$nextTick(() => {
                var table = this.tablekey;
                if (this.filter.OrderImportType != 4) {
                    var temp = this.$refs[table].checkBoxGroup;
                    if (this.filter.OrderImportType == 99) {
                        this.$refs[table].checkedColumns = this.checkedColumnsFor;
                    } else {
                        if (this.filter.selectedNodeIndex == 0) {
                            this.$refs[table].checkedColumns = this.checkedColumnsFor0;
                            this.$refs[table].checkedColumnsShow(this.checkedColumnsFor0, temp);
                            this.$refs[table].checkBoxGroup = this.checkedColumnsFor0;
                        }
                        if (this.filter.selectedNodeIndex == 1) {
                            this.$refs[table].checkedColumns = this.checkedColumnsFor1;
                            this.$refs[table].checkedColumnsShow(this.checkedColumnsFor1, temp);
                            this.$refs[table].checkBoxGroup = this.checkedColumnsFor1;
                        }
                        if (this.filter.selectedNodeIndex == 2) {
                            this.$refs[table].checkedColumns = this.checkedColumnsFor2;
                            this.$refs[table].checkedColumnsShow(this.checkedColumnsFor2, temp);
                            this.$refs[table].checkBoxGroup = this.checkedColumnsFor2;
                        }
                        if (this.filter.selectedNodeIndex == 3) {
                            this.$refs[table].checkedColumns = this.checkedColumnsFor3;
                            this.$refs[table].checkedColumnsShow(this.checkedColumnsFor3, temp);
                            this.$refs[table].checkBoxGroup = this.checkedColumnsFor3;
                        }
                        if (this.filter.selectedNodeIndex == 4) {
                            this.$refs[table].checkedColumns = this.checkedColumnsFor4;
                            this.$refs[table].checkedColumnsShow(this.checkedColumnsFor4, temp);
                            this.$refs[table].checkBoxGroup = this.checkedColumnsFor4;
                        }
                        if (this.filter.selectedNodeIndex == 5) {
                            this.$refs[table].checkedColumns = this.checkedColumnsFor5;
                            this.$refs[table].checkedColumnsShow(this.checkedColumnsFor5, temp);
                            this.$refs[table].checkBoxGroup = this.checkedColumnsFor5;
                        }
                    }
                }
            });
        },
        async ExPortExecl() {
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            else {
                this.filter.startTime = null;
                this.filter.endTime = null;
            }
            this.filter.OrderImportType = this.orderImportType;
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ...this.filter,
            };
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            var res;
            if (this.orderImportType == 4) {
                res = await exportOrderRepairAsync(params);
            } else {
                res = await exportOrderNoteAsync(params);
            }
            loadingInstance.close();
            if (res?.data?.type == 'application/json') {
                return;
            }
            const aLink = document.createElement("a");
            var blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '订单_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async tabclick() {
            let that = this;
            this.$nextTick(() => {
                that.showprchart();
            });
        },
        async showprchart() {
            var chartype = this.getCharType();
            this.filter.chartype = chartype;
            const params = { ...this.filter };
            let that = this;
            this.echarLoading = true;
            if (this.activeName == "eight") {
                await getNewOrderNotesRepairListMap(params).then(red => {
                    that.chardata = red
                });
                this.echarLoading = false;
                const finaldata = this.Getoptions(this.chardata);
                await this.initchartsline(finaldata, this.echarNameList[chartype]);
            } else {
                if (this.filter.OrderImportType == 4)
                    return;
                if (this.orderImportType == 99) {
                    this.timeType = 0;
                    //全部订单获取趋势图
                    console.log(this.activeName);
                    if (this.activeName == "one") {
                        await getNewAllOrderNotesTypeListMapNew(params).then(red => {
                            that.allOrderDataAnalysis = red
                        });
                    }
                    this.echarLoading = false;
                    const finaldata = this.Getoptions(this.allOrderDataAnalysis[this.timeType]);
                    await this.initchartsline(finaldata, this.echarNameList[chartype]);
                } else {
                    await getNewOrderNotesTypeListMap(params).then(red => {
                        that.chardata = red
                    })
                    this.echarLoading = false;
                    const finaldata = this.Getoptions(this.chardata);
                    await this.initchartsline(finaldata, this.echarNameList[chartype]);
                }
            }

        },
        async initchartsline(analysisData, charid) {
            var that = this;
            that.$nextTick(() => {
                var chartDom1 = document.getElementById(charid + that.randrom);
                chartDom1.style.width = 1682 + "px";

                let myChart = that.myChart1
                if (myChart != null) {
                    myChart.dispose(); //销毁
                }
                myChart = echarts.init(chartDom1);
                //获取选中lable标签下标
                if (that.activeName == "one") {
                    myChart.on('click', params => {
                        if (params.seriesType == "bar") {
                            console.log(params);
                            that.dialogFilter.seriesTime = params.name;
                            that.dialogFilter.seriesTimeType = that.timeType;
                            if (that.dialogFilter.seriesTimeType == 1) {
                                that.dialogFilter.seriesTime = that.dialogFilter.seriesTime.replace("第", "-").replace("周", "")
                            } else if (that.dialogFilter.seriesTimeType == 2) {
                                that.dialogFilter.seriesTime = that.dialogFilter.seriesTime.replace("-", "").replace("月", "");
                            }
                            if (params.seriesName.indexOf("24小时") == 0) {
                                that.dialogFilter.seriesTimeRangeType = 1;
                            } else if (params.seriesName.indexOf("48小时外") == 0) {
                                that.dialogFilter.seriesTimeRangeType = 3;
                            } else {
                                that.dialogFilter.seriesTimeRangeType = 2;
                            }
                            that.dialogFilter.seriesTimeRangeType = 0;//查全部
                            that.dialogFilter.orderNoInner = that.filter.orderNoInner;
                            that.dialogFilter.platform = that.filter.platform;
                            that.dialogFilter.shopCode = that.filter.shopCode;
                            that.dialogFilter.warehouse = that.filter.warehouse;
                            that.dialogFilter.minPickCount = that.filter.minPickCount;
                            that.dialogFilter.maxPickCount = that.filter.maxPickCount;
                            that.dialogFilter.label = that.filter.label;
                            that.dialogFilter.labelNo = that.filter.labelNo;
                            that.dialogFilter.timeRangeType = that.filter.timeRangeType;
                            that.dialogFilter.orderSituation = that.filter.orderSituation;
                            that.dialogVisible = true;
                        }
                    })
                }
                myChart.clear();
                var option1 = analysisData;
                myChart.setOption(option1);
            });
        },
        CalculateyAxis(element, selectIndexArray) {
            var ymaxmin = [];
            var maxratmin = 1;
            var isNegative = false;
            selectIndexArray.forEach(index => {
                var s = element.series[index];
                var max = Math.max.apply(null, s.data)
                var min = Math.min.apply(null, s.data)
                if (min != 0 && (max / min) < maxratmin) {
                    maxratmin = s.max / s.min;
                }
                if (min < 0) {
                    isNegative = true;
                }
                var one = ymaxmin.filter(function (value, index, self) { return value.yAxisIndex == s.yAxisIndex; })
                if (one.length == 0) ymaxmin.push({ yAxisIndex: s.yAxisIndex, max: max, min: min });
                else {
                    ymaxmin.forEach(f => {
                        if (f.yAxisIndex == s.yAxisIndex) { if (f.max < max) f.max = max; if (f.min > min) f.min = min; }
                    })
                }
            });
            if (isNegative) {
                maxratmin = 1;
                ymaxmin.forEach(f => { if (f.min != 0 && (f.max / f.min) < maxratmin) maxratmin = f.max / f.min })
                ymaxmin.forEach(f => { f.min = f.max / maxratmin })
                ymaxmin.forEach(f => { element.yAxis[f.yAxisIndex].max = f.max.toFixed(0); element.yAxis[f.yAxisIndex].min = f.min.toFixed(0); })

                //过滤min>0的数据作为赋值
                var saveMaxMin = ymaxmin.filter(function (value) {
                    return value.min < 0;
                });
                element.yAxis.forEach(s => {
                    if (s.max == null) {
                        s.max = saveMaxMin[0].max.toFixed(0);
                    }
                    if (s.min == null) {
                        s.min = saveMaxMin[0].min.toFixed(0);
                    }
                });
            } else {
                element.yAxis.forEach(s => {
                    s.max = null;
                    s.min = null;
                });
            }

        },
        Getoptions(element) {
            var series = [];
            element.series.forEach(s => { series.push({ smooth: true, ...s }) })
            var yAxis = []
            element.yAxis.forEach(s => {
                yAxis.push({
                    type: 'value', minInterval: 10, offset: s.offset, splitLine: { show: false }, axisLine: { onZero: true, }, position: s.position, name: s.name,
                    axisLabel: {
                        formatter: function (value) {
                            if (value >= 100000000) {
                                value = Math.ceil(value / 100000000) + 'Y';
                            }
                            if (value >= 10000000) {
                                value = Math.ceil(value / 10000000) + 'KW';
                            }
                            if (value >= 10000) {
                                value = Math.ceil(value / 10000) + 'W';
                            }
                            if (value >= 1000) {
                                value = Math.ceil(value / 1000) + 'K';
                            }
                            if (value <= -100000000) {
                                value = Math.floor(value / 100000000) + 'Y';
                            }
                            if (value <= -10000000) {
                                value = Math.floor(value / 10000000) + 'KW';
                            }
                            if (value <= -10000) {
                                value = Math.floor(value / 10000) + 'W';
                            }
                            if (value <= -1000) {
                                value = Math.floor(value / 1000) + 'K';
                            }
                            return value + s.unit;
                        }
                    }
                })
            })
            var selectedLegend = {};//{};
            if (element.selectedLegend) {
                element.legend.forEach(f => {
                    //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
                    if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
                })
            }
            var optionGrid = {
                top: '20%',
                left: '5%',
                right: '4%',
                bottom: '20%',
                containLabel: false
            }
            if (this.filter.OrderImportType == 99) {
                optionGrid = {
                    left: '2%',
                    right: 0,
                    bottom: 0,
                    containLabel: true
                }
                var dataCounts = element.series[0].data.length;
                //console.log(dataCounts);
                element.series.forEach(s => {
                    s.smooth = true;
                    if (dataCounts >= 100) {
                        s.barMaxWidth = '5';
                    } else if (element.series[0].data.length >= 60) {
                        s.barMaxWidth = '10';
                    }
                });
            } else {
                element.series.map(item => {
                    item.barWidth = 15;
                    item.smooth = true;
                })
            }
            var option = {
                title: { text: element.title },
                legend: {
                    selected: selectedLegend,
                    data: element.legend,
                },
                grid: optionGrid,
                toolbox: {
                    feature: {
                        magicType: { show: true, type: ['line', 'bar'] },
                        //restore: {show: true},
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    padding: [5, 10]
                },
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series: element.series,
            };

            return option;
        },
        getCharType() {
            var activeName = this.activeName;
            let activeNameList = this.activeNameList;
            let nameLength = activeNameList.length;

            var charIndex = 0;
            for (var i = 0; i < nameLength; i++) {
                if (activeName == activeNameList[i]) {
                    charIndex = i;
                    break;
                }
            }
            return charIndex;
        },
        clickBar() {

        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

// ::v-deep .el-main >  div:nth-child(1) {
//       height: 95% !important;
//       background-color: red;
//   }
::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
    height: 95% !important;
    background-color: red;
}

//*[@id="app"]/section/section/main/section/section/main/div
// -webkit-box-direction: normal;
// height: 100%;
// ::v-deep {
//   height: 95% !important;
// }
</style>
