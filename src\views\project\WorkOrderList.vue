<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-button type="primary" @click="onOpenForm(1)">新增咨询或工单</el-button>
      <el-button type="primary" @click="onSearch">刷新</el-button>
    </template>
    <template>
      <ces-table :id="'WorkOrderList202412201913'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
        style="height: 500px;" :tableData='list' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :loading="listLoading">
      </ces-table>
    </template>

    <el-dialog title="咨询或工单" :visible.sync="formVisible" width="80%" v-dialogDrag append-to-body>
      <el-row v-loading="pageLoading">
        <el-col :span="14" style="padding-right: 20px;">
          <el-form ref="form" :model="form" label-width="140px" :disabled="formMode==3">
            <el-form-item label="工单类型">
              <el-radio-group v-model="form.orderType">
                <el-radio label="业务咨询"></el-radio>
                <el-radio label="问题反馈"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="问题简述(50字内)">
              <el-input v-model="form.title" :maxLength="50"></el-input>
            </el-form-item>

            <el-form-item label="问题详情">
              <yh-quill-editor v-if="formMode == 1" :value.sync="form.description"
                style="max-height: 300px;overflow: auto;"></yh-quill-editor>                
              <div v-else  style="border:solid 1px silver;max-height: 400px;overflow: auto;">
                <div v-html="form.description"></div>
              </div>
            </el-form-item>

            <!-- <el-form-item label="附件" prop="attachFiles">
              <YhImgUpload :value.sync="form.attachFiles" :isImg="false" accept=".pdf,.xlsx,.docx" :limit="10">
              </YhImgUpload>
            </el-form-item> -->

            <el-form-item>
              <el-button type="primary" @click="onSubmit">立即创建</el-button>
              <el-button @click="formVisible = false">取消</el-button>
            </el-form-item>
          </el-form>
        </el-col>
       
        <el-col :span="10" style="max-width:600px;overflow: auto;">
          <b>跟进回复：</b><br/>
           <div v-if="formMode==3">
              <yh-quill-editor :value.sync="replyForm.gjContent" ></yh-quill-editor>
              <!-- <YhImgUpload :value.sync="replyForm.attachFiles" :isImg="false" accept=".pdf,.xlsx,.docx" :limit="10">
              </YhImgUpload> -->
              <el-button type="primary" @click="onReply">提交回复</el-button>
           </div>
           <div style="max-height: 400px;overflow: auto;">
              <div v-for="item in form.gjList" style="border:solid 1px silver;padding-left: 10px ;margin-bottom: 2px;">
                <div style="margin-left: -10px;" >
                  <b>{{ item.gjUserName }}</b> {{ item.gjTime }} 
                </div>
                <div  v-html="item.gjContent">
                </div>
              </div>
           </div>
        </el-col>
      </el-row>

    </el-dialog>

  </my-container>
</template>

<script>
import cesTable from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { GetMyWorkOrderList, AddWorkOrderAsync, GetWorkOrderDtl,ReplyWorkOrder } from '@/api/project/projbug';
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import YhImgUpload from '@/components/upload/yh-img-upload.vue';
import { on } from "codemirror";

const tableCols = [
  
  { istrue: true, prop: 'orderType', label: '类型', width: 80},
  { istrue: true, prop: 'title', label: '标题'},
  { istrue: true, prop: 'orderTime', label: '时间', width: 160 },
  { istrue: true, prop: 'lastGjUserName', label: '回复人', width: 80},
  { istrue: true, prop: 'lastGjTime', label: '回复时间', width: 150},
  { istrue: true, prop: 'lastGjContent', label: '回复内容', width: 150},
  { istrue: true, prop: 'isEnd', label: '状态', width: 80,formatter:  (row) => row.isEnd==1?'关闭':'开启' },
  {
    istrue: true, prop: 'fileUrl', label: '操作', width: 80, type: 'button', btnList: [
      {
        label: '查看',
        handle: (that, row) => {
          that.onOpenForm(3, row);
        }       
      },
    ]
  },
];

export default {
  name: 'WorkOrderList',
  components: { cesTable, MyContainer, YhQuillEditor, YhImgUpload },
  data() {
    return {
      that: this,
      list: [],
      listLoading: false,
      tableCols: tableCols,
      pageLoading: false,
      formVisible: false,
      formMode: 1,
      newForm: {
        orderType: '业务咨询',
        title: '',
        description: '',
        attachFiles: null,
        gjList:[]
      },
      form: {
        orderType: '业务咨询',
        title: '',
        description: '',
        attachFiles: null,
        gjList:[]
      },
      newReplyForm:{
        workOrderId:'0',
        gjContent:'',
        gjAttachFiles:null
      },
      replyForm:{
        workOrderId:'0',
        gjContent:'',
        gjAttachFiles:null
      }
    };
  },
  loadData() {

  },
  async mounted() {
    await this.getlist();
  },

  methods: {
    onSearch() {
      this.getlist();
    },
    //查询
    async getlist() {
      this.listLoading = true;
      const { data } = await GetMyWorkOrderList()
      this.listLoading = false;
      this.list = data
    },
    async onOpenForm(mode, row) {
      this.formMode = mode;
      if (mode == 1) {
        this.form = { ...this.newForm };
        this.formVisible = true;
      }
      else if (row) {
        this.formVisible = true;

        this.pageLoading=true;

        this.form = { ...row };

        let res=await GetWorkOrderDtl({id:row.id});
        this.replyForm.workOrderId=res.data.id;
        this.form={...res.data};

        this.replyForm={...this.newReplyForm};
        this.replyForm.workOrderId=res.data.id;
        this.pageLoading=false;
      }
    },
    async onSubmit() {
      if (!this.form.title) {
        this.$message({
          message: '请输入问题简述',
          type: 'warning'
        })
        return;
      }
      if (!this.form.description) {
        this.$message({
          message: '请输入问题详情',
          type: 'warning'
        })
        return;
      }

      const res = await AddWorkOrderAsync(this.form);
      if (res && res.success) {
        this.$message({
          message: '创建成功',
          type: 'success'
        })

        this.formVisible = false;
        this.getlist();
      }

    },
    async onReply(){
      console.log('replyForm', this.replyForm);
      if (!this.replyForm.gjContent) {
        this.$message({
          message: '请输入回复内容',
          type: 'warning'
        })
        return;
      }
      const res = await ReplyWorkOrder(this.replyForm);
      if (res && res.success) {
        this.$message({
          message: '回复成功',
          type: 'success'
        })

        this.formVisible = false;
        this.getlist();
      }
    }

  },
};
</script>