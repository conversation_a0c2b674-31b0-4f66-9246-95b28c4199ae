<template>
    <my-container v-loading="pageLoading">
        <div class="rowstyle" >
            <div class="marleftrig">
                <div class="aligncenter">
                    <div class="flexrow">
                        <el-button-group>
                        <el-button :type="num==1?'primary':''" @click="whactclick(1)">直通车图</el-button>
                        <el-button :type="num==2?'primary':''" @click="whactclick(2)">主图</el-button>
                        <el-button :type="num==3?'primary':''" @click="whactclick(3)">sku</el-button>
                        <el-button :type="num==4?'primary':''" @click="whactclick(4)">详情页</el-button>
                        <el-button :type="num==5?'primary':''" @click="whactclick(5)">主图视频</el-button>
                        <el-button :type="num==6?'primary':''" @click="whactclick(6)">微详情视频</el-button>
                        </el-button-group>
                    </div>
                    <div  style="margin-top: 10px; display: flex; flex-direction: row;">
                        <!-- <el-button :type="tonum==11?'primary':''" @click="addmokuai(11)">增加模块</el-button>
                        <el-button :type="tonum==12?'primary':''" @click="savekuai(12)" v-show="listidd">直接保存</el-button>
                        <el-button :type="tonum==12?'primary':''" @click="savemokuai(12)" v-show="!listidd">点击保存</el-button> -->
                    </div>
                    <div style="margin-top: 10px;">
                        <el-button :type="tonum==11?'primary':''" :disabled="(listidd==0||!listidd)?true:false" style="width:120px;" @click="tocreateimg(11)">生成图片</el-button>
                        <el-button :type="tonum==12?'primary':''" :disabled="(listidd==0||!listidd)?true:false" style="width:120px;" >发送钉钉</el-button>
                    </div>
                </div>
                <div style="height:190px;">
                    <shootingvideotaskuploadfile  ref="shootingvideotaskuploadfile" v-if="selectRowKey" :rowinfo="selectRowKey"></shootingvideotaskuploadfile>
                </div>
                <div class="content" ref="oneboxx" v-if="listidd!=0">
                    <div v-if="listidd?(num==1&&listall.data1!=null):num==1">
                        <shootingcreateindex1 :ispaste = "false" :btnshow="false" @getalllist="getalllist" @changefuc="changefuc" @cleardig="cleardig" :listid="listidd" :main="mainlist" :alllist="listall.data1" ref="createindex1" :key="num" :bannum="num" :disabled="true" :name="'carimg'" />
                    </div>
                    <div v-else-if="listidd?(num==2&&listall.data2!=null):num==2">
                        <shootingcreateindex2 :ispaste = "false" :btnshow="false" @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data2" ref="createindex2" :key="num" :bannum="num" :disabled="true" :name="'indeximg'" />
                    </div>
                    <div v-else-if="listidd?(num==3&&listall.data3!=null):num==3">
                        <shootingcreateindex3 :ispaste = "false" :btnshow="false" @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data3" ref="createindex3" :key="num" :bannum="num" :disabled="true" :name="'skuimg'" />
                    </div>
                    <div v-else-if="listidd?(num==4&&listall.data4!=null):num==4">
                        <shootingcreateindex4 :ispaste = "false" :btnshow="false" @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data4" ref="createindex4" :key="num" :bannum="num" :disabled="true" :name="'msgimg'"/>
                    </div>
                    <div v-else-if="listidd?(num==5&&listall.data5!=null):num==5">
                        <shootingcreateindex5 :ispaste = "false" :btnshow="false" :disabled="true" @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data5" :isvideoh ="true" ref="createindex5" :key="num" :bannum="num" :name="'indexvideo'" />
                    </div>
                    <div v-else-if="listidd?(num==6&&listall.data6!=null):num==6">
                        <shootingcreateindex6 :ispaste = "false" :btnshow="false" :disabled="true" @getalllist="getalllist" @changefuc="changefuc" :listid="listidd" :main="mainlist" :alllist="listall.data6"  :isvideoh ="true" ref="createindex6" :key="num" :bannum="num"  :name="'weivideo'" />
                    </div>
                </div>

                <el-dialog title="海报生成" :visible.sync="dialogTableVisible" append-to-body>
                    <div class="border">
                        <img
                        style="width: auto; height: auto; max-width: 100%; max-height: 100%;"
                        :src="imgUrl"/>
                    </div>
                </el-dialog>
            </div>
        </div>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import html2canvas from 'html2canvas'
import shootingcreateindex1 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex2 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex3 from '@/views/media/shooting/fuJianmanage/shootingcreateindex2';
import shootingcreateindex4 from '@/views/media/shooting/fuJianmanage/shootingcreateindex1';
import shootingcreateindex5 from '@/views/media/shooting/fuJianmanage/shootingcreateindex3';
import shootingcreateindex6 from '@/views/media/shooting/fuJianmanage/shootingcreateindex3';
import shootingvideotaskuploadfile from '@/views/media/shooting/shootingvideotaskuploadfile';
import {getReferenceMainReferencs,saveReferenceMainReferencs,saveReferenceMainReferencsForImg,getReferenceMainReferencsAll} from '@/api/media/referencemanage';

export default {
    components: {MyContainer,shootingcreateindex1,shootingcreateindex2,shootingcreateindex3,shootingcreateindex4,shootingcreateindex5,shootingcreateindex6,MyContainer,shootingvideotaskuploadfile},
    data() {
        return {
            pageLoading:false,
            num: 1,
            tonum: 11,
            imgUrl: '',
            dialogTableVisible: false,
            listall: [],
            mainlist: null,
            listidd: 0,
            ischange: false,
            selectRowKey: null,
            urlimg: null,
        };
    },
    props: {
        listid: {default: ''},
    },

    mounted() {
        let _this = this;
        // _this.listidd = '1608377591637180416';
        _this.listidd = _this.$route.query.refid;
        _this.selectRowKey = _this.$route.query.id;
        if(_this.listidd!=0){
            _this.getalllist()
        }
        // if(!this.listid){
        //     this.savemokuai(12);
        // }

        
    },

    methods: {
        cleardig(val){
            this.$emit('cleardig',val)
        },
        changefuc(val){
            this.ischange = val;
        },
        async getalllist(val){
            let _this =this;
            if(val){
                await _this.$nextTick(()=>{
                    _this.listidd = val;
                })
            }
            _this.pageLoading= true;
            if(_this.listidd){
                let res = await getReferenceMainReferencsAll(_this.listidd);
                if(res.success)
                    _this.listall = res.data;
                    _this.mainlist = res.data.mainTask;

            }
            _this.pageLoading= false;
        },
        whactclick(num){
            let _this = this;
            if(this.ischange){
                this.$message({
                    message: "请先保存再进行操作！",
                    offset: 150,
                    duration: 2000
                })
                return
            }
            _this.num = num;
            // _this.savemokuai(12);
            
        },
        tocreateimg(val){
            let _this = this;
            this.tonum = val;
            html2canvas(this.$refs.oneboxx,{allowTaint: true,
				useCORS: true,}).then(async (canvas) => {
				let dataURL = canvas.toDataURL('image/png')
				this.imgUrl = dataURL
                let name = Date.now();

                let bolb = await _this.dataURLtoBlob(dataURL);
                let file = await _this.blobToFile(bolb,name+'.png');
                await _this.uploadToServer(file, (res) => {
                    _this.urlimg = res.data.url
                })
                this.dialogTableVisible = true;
			})
        },
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
        dataURLtoBlob(dataurl) {
            var arr = dataurl.split(','),
                mime = arr[0].match(/:(.*?);/)[1],
                bstr = atob(arr[1]),
                n = bstr.length,
                u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new Blob([u8arr], { type: mime });
            },
        blobToFile(theBlob, fileName){
            theBlob.lastModifiedDate = new Date();
            theBlob.name = fileName;
            return new File([theBlob], fileName, {type: theBlob.type,name:fileName, lastModified: Date.now()});
            },
        addmokuai(val){
            let _this = this;
            _this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.addlist();
            }else if(_this.num == 2){
                this.$refs.createindex2.addlist();
            }else if(_this.num == 3){
                this.$refs.createindex3.addlist();
            }else if(_this.num == 4){
                this.$refs.createindex4.addlist();
            }else if(_this.num == 5){
                this.$refs.createindex5.addlist();
            }else if(_this.num == 6){
                this.$refs.createindex6.addlist();
            }
        },
        savekuai(val){
            let _this = this;
            _this.ischange = false;
            this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.tosubmitt();
            }else if(_this.num == 2){
                this.$refs.createindex2.tosubmitt();
            }else if(_this.num == 3){
                this.$refs.createindex3.tosubmitt();
            }else if(_this.num == 4){
                this.$refs.createindex4.tosubmitt();
            }else if(_this.num == 5){
                this.$refs.createindex5.tosubmitt();
            }else if(_this.num == 6){
                this.$refs.createindex6.tosubmitt();
            }
        },
        savemokuai(val){
            let _this = this;
            this.tonum = val;
            if(_this.num == 1){
                this.$refs.createindex1.submitt();
            }else if(_this.num == 2){
                this.$refs.createindex2.submitt();
            }else if(_this.num == 3){
                this.$refs.createindex3.submitt();
            }else if(_this.num == 4){
                this.$refs.createindex4.submitt();
            }else if(_this.num == 5){
                this.$refs.createindex5.submitt();
            }else if(_this.num == 6){
                this.$refs.createindex6.submitt();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.rowstyle{
    margin: 0;
    height: 100%;
    // display: flex;
    // justify-content: center;
    // align-items: center;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: auto;
    // border: 1px solid #eee;
}
.aligncenter{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    height: auto;
    flex-direction: column;
    border-bottom: 1px solid #eee;
    padding: 10px;
    // border: 1px solid blue;
}
.content{
    // margin: 0 400px;
    // height: 700px;
    height: 100%;
    // overflow-y: auto;
    border: 1px solid #eee;
}
.border{
    border: 2px solid #409EFF;
}
.marleftrig{
    margin: 0 380px;
//    width: 1000px;
}
</style>