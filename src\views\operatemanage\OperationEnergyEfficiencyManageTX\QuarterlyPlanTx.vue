<template>
<my-container v-loading="pageLoading">
  淘系季度计划
    <vxe-table
          border
          show-overflow
          ref="table"
          :loading="listLoading"
          :data="list1"
         
          height="300px"
          :row-config="{height: 45}"
          :column-config="{resizable: true}"
          header-align="center"
          
        
          >
          <vxe-column type="seq" width="60" align="center"></vxe-column>
          <vxe-column  field="platForm" title="平台"  width="200" align="center"> 
            <template #default="{ row }">
            <template v-if="row.platForm==1">
                <span >淘系</span>
               </template>
               <template v-else-if="row.platForm==8">
                   <span >淘工厂</span>
               </template>
               
            </template>
          </vxe-column>
          <vxe-column field="targetType" title="目标类别" width="200" align="center">
            <template #default="{ row }">
                   <span >销售额</span><br/>
                    <span >毛三</span>
                </template>

          </vxe-column>
          <vxe-column field="q1Saleamount" title="第一季度" width="200" align="center">
            <template #default="{ row }">
                {{row.q1Saleamount}}
                <br/>
                {{row.q1profit3}}
              </template>
          </vxe-column>
          <vxe-column field="q2Saleamount" title="第二季度" width="200" align="center">
            <template #default="{ row }">
                {{row.q2Saleamount}}
                <br/>
                {{row.q2profit3}}
              </template>
          </vxe-column>
          <vxe-column field="q3Saleamount" title="第三季度" width="200" align="center">
            <template #default="{ row }">
                {{row.q3Saleamount}}
                <br/>
                {{row.q3profit3}}
              </template>
          </vxe-column>
          <vxe-column field="q4Saleamount" title="第四季度" width="150" align="center">
            <template #default="{ row }">
                {{row.q4Saleamount}}
                <br/>
                {{row.q4profit3}}
              </template>
          </vxe-column>
          <vxe-column  width="100" title="操作"  align="center">
            <template  #default="{ row }">
                <vxe-button style="color: blue;" type="text" @click="UpdateQuarterlyPlan(row)" >编辑</vxe-button>
              </template>
          </vxe-column>
        </vxe-table>
        淘系季度计划详情
        <vxe-table
              border
              show-overflow
              ref="table"
              :loading="listLoading"
              :data="list2"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              height="460px"
              :row-config="{height: 51}"
              :column-config="{resizable: true}"
              header-align="center"
             
              >
              <vxe-column type="seq" width="60" align="center"></vxe-column>
              <vxe-colgroup title="季度">
                <vxe-column field="platForm" title="月份"width="122" align="center">
                    <template #default="{ row }">
                        <template v-if="row.platForm==1">
                            <span >淘系</span>
                           </template>
                           <template v-else-if="row.platForm==8">
                               <span >淘工厂</span>
                           </template>
                        </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-column field="targetType" title="目标类别" width="122"align="center">
                <template #default="{ row }">
                    <span >销售额</span><br/>
                    <span >毛三</span>
                    </template>
              </vxe-column>
              <vxe-colgroup title="第一季度">
                <vxe-column field="date3" title="1月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m1Saleamount}}
                        <br/>
                        {{row.m1profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="2月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m2Saleamount}}
                        <br/>
                        {{row.m2profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="3月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m3Saleamount}}
                        <br/>
                        {{row.m3profit3}}
                      </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-colgroup title="第二季度">
                <vxe-column field="date3" title="4月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m4Saleamount}}
                        <br/>
                        {{row.m4profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="5月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m5Saleamount}}
                        <br/>
                        {{row.m5profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="6月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m6Saleamount}}
                        <br/>
                        {{row.m6profit3}}
                      </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-colgroup title="第三季度">
                <vxe-column field="date3" title="7月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m7Saleamount}}
                        <br/>
                        {{row.m7profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="8月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m8Saleamount}}
                        <br/>
                        {{row.m8profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="9月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m9Saleamount}}
                        <br/>
                        {{row.m9profit3}}
                      </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-colgroup title="第四季度">
                <vxe-column field="date3" title="10月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m10Saleamount}}
                        <br/>
                        {{row.m10profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="11月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m11Saleamount}}
                        <br/>
                        {{row.m11profit3}}
                      </template>
                </vxe-column>
                <vxe-column field="date3" title="12月"width="122" align="center">
                    <template #default="{ row }">
                        {{row.m12Saleamount}}
                        <br/>
                        {{row.m12profit3}}
                      </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-column  width="122" title="操作" align="center">
                <template  #default="{ row }">
                    <vxe-button style="color: blue;" type="text" @click="UpdateQuarterlyPlanDetail(row)" >编辑</vxe-button>
                  </template>
              </vxe-column>
            </vxe-table>
      <el-drawer title="编辑季度计划" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                    direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
                  <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
                  <div class="drawer-footer">
                  <el-button @click.native="editVisible = false">取消</el-button>
                  <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
                  </div>
            </el-drawer>
      <el-drawer title="编辑季度计划详情" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisibleDetail" 
            direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
          <form-create :rule="autoform1.rule1" v-model="autoform1.fApi" :option="autoform1.options"/>
          <div class="drawer-footer">
          <el-button @click.native="editVisibleDetail = false">取消</el-button>
          <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmitDetail" />
          </div>
    </el-drawer>
  </my-container>
   
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import { getQuarterlyPlanTx,getQuarterlyPlanDetailsTx ,editQuarterlyPlanTx,editQuarterlyPlanTxDetail} from '@/api/operatemanage/base/product'

let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
}; 

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,InputMult,vxetablebase},
  data() {
    return {
       that:this,
       list1:[],
       list2:[],
       editVisible:false,
       editVisibleDetail:false,
       listLoading: false,
       pageLoading: false,
       sels: [],
       editLoading:false,
      autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[{required: false}]
        },
        autoform1:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule1:[{required: false}]
        },
    pager: { OrderBy: "", IsAsc: false },
    total: 0,
    };
  },
  
   mounted() {
    this.initform();
     this.onSearch();
  },
  
  
  methods: {


  
     //编辑
   async UpdateQuarterlyPlan(row){
    row.platForm=row.platForm.toString()
    this.editVisible = true
    this.$nextTick(() => {
     this.autoform.fApi.setValue(row);
    }); 
  },

    //编辑淘系季度详情
    async UpdateQuarterlyPlanDetail(row){
    row.platForm=row.platForm.toString()
    this.editVisibleDetail = true
    this.$nextTick(() => {
     this.autoform1.fApi.setValue(row);
    });
  },

  async onEditSubmitDetail() {
    this.editLoading=true;
    await this.autoform1.fApi.validate(async (valid, fail) => {
    if(valid){
        const formData = this.autoform1.fApi.formData();
        const res = await editQuarterlyPlanTxDetail(formData);
        if(res.code==1){
          this.$message.success('修改成功！');
          this.autoform1.fApi.resetFields()
          this.editVisibleDetail=false;  
          this.getlist1(); 
        }
      }else{}
   })
   this.editLoading=false;
  },


  async onEditSubmit() {
    this.editLoading=true;
    await this.autoform.fApi.validate(async (valid, fail) => {
    if(valid){
        const formData = this.autoform.fApi.formData();
        const res = await editQuarterlyPlanTx(formData);
        if(res.code==1){
          this.$message.success('修改成功！');
          this.autoform.fApi.resetFields()
          this.editVisible=false;  
          this.getlist(); 
        }
      }else{}
   })
   this.editLoading=false;
  },
    async updateruleGroup(groupid) {
      
   if(groupid=="")
      this.autoform1.fApi.resetFields()
   else{
     const res = await getParm({groupId:groupid})
     res.data.groupId=groupid;
     if(!res.data?.Profit3PredictRate) res.data.Profit3PredictRate=0;
     if(!res.data?.ShareRate) res.data.ShareRate=0;
     await this.autoform1.fApi.setValue(res?.data)
    }
  },
   async initform(){
    let that=this;
        this.autoform.rule= [
                  
                  { type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
                  {type:'select',field:'platForm',title:'平台',value: '',props:{clearable:true,disabled:true },col:{span:6},options: [{ value: '1', label: '淘系',disabled:true }, { value: '8', label: '淘工厂',disabled:true }]},
                  {type:'inputnumber',field:'q1Saleamount',title:'第一季度销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'q1profit3',title:'第一季度毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'q2Saleamount',title:'第二季度销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'q2profit3',title:'第二季度毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },  
                  {type:'inputnumber',field:'q3Saleamount',title:'第三季度销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'q3profit3',title:'第三季度毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'q4Saleamount',title:'第四季度销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'q4profit3',title:'第四季度毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },                    
                   ]
                   this.autoform1.rule1= [
                  
                  { type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
                  {type:'select',field:'platForm',title:'平台',props:{clearable:true,disabled:true },value: '',col:{span:6},options: [{ value: '1', label: '淘系',disabled:true }, { value: '8', label: '淘工厂',disabled:true }]},
                  {type:'inputnumber',field:'m1Saleamount',title:'1月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m1profit3',title:'1月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m2Saleamount',title:'2月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m2profit3',title:'2月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m3Saleamount',title:'3月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m3profit3',title:'3月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m4Saleamount',title:'4月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m4profit3',title:'4月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m5Saleamount',title:'5月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m5profit3',title:'5月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m6Saleamount',title:'6月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m6profit3',title:'6月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m7Saleamount',title:'7月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m7profit3',title:'7月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m8Saleamount',title:'8月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m8profit3',title:'8月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m9Saleamount',title:'9月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m9profit3',title:'9月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },  
                  {type:'inputnumber',field:'m10Saleamount',title:'10月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m10profit3',title:'10月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m11Saleamount',title:'11月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m11profit3',title:'11月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },  
                  {type:'inputnumber',field:'m12Saleamount',title:'12月销售额',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },
                  {type:'inputnumber',field:'m12profit3',title:'12月毛三',value:'',col:{span:6},props: { min: 0, max: 999999999999, precision: 0 } },                    
                   ]
    },
    
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
      await this.getlist();
      await this.getlist1();
    },
    async getlist(){
      var that=this;
      const params = {...this.pager};
      startLoading(); 
      const res = await getQuarterlyPlanTx(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
           that.list1 = res.data?.list;
          
      });
    },
    async getlist1(){
      var that=this;
      const params = {...this.pager};
      startLoading(); 
      const res = await getQuarterlyPlanDetailsTx(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          that.list2 = res.data?.list;
          
      });
    },

    
 
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
  
},
  
};
</script>
<style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
</style>
 
 