<template>
  <container v-loading="pageLoading"> 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false"
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>    
  </container>
</template>

<script>
import {pageAbnormalGeneral} from '@/api/inventory/abnormal'
import container from '@/components/my-container/noheader'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatTime,formatSecondToHour,formatmoney} from "@/utils/tools";
import { ruleBusinessModul } from '@/utils/formruletools'
import {getAllBuModule} from '@/api/admin/business'
const tableCols =[
      {istrue:true,prop:'createdTime',label:'日期', width:'130',sortable:'custom',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD')},
      {istrue:true,prop:'totalWaitOrderNum',label:'总压单量', width:'80',sortable:'custom'},
      {istrue:true,prop:'sjWaitOrderNum',label:'实际压单量', width:'110'},
      {istrue:true,prop:'preSaleOrderNum',label:'预售压单量', width:'110',sortable:'custom'},
      {istrue:true,prop:'taoKeOrderNum',label:'淘客压单量', width:'110',sortable:'custom'},
      {istrue:true,prop:'totalWaitGoodNum',label:'总压品数', width:'100',sortable:'custom'},
      {istrue:true,prop:'nonCompletedPurchase',label:'未完结采购单', width:'110',sortable:'custom'}
     ];
const tableHandles1=[
        //{label:"新增", handle:(that)=>that.onAdd()},
        //{label:'编辑', handle:(that)=>that.onEdit()}
      ];
export default {
  name: 'Roles',
  components: {cesTable, container, MyConfirmButton },
  props:{
     filter:{ },
  },
  data() {
    return {
      that:this,       
      list: [],
      pager:{OrderBy:"createdTime",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platFormList:[],
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
    }
  },
 watch:{
    filter:function(val,oldval){
         this.$nextTick(function(){
            this.onSearch( );
       })
    }
  },
  // watch: {
  //   value(n) {
  //     if(n) {
  //       this.$nextTick(() => {
  //          this.onSearch();
  //       });
  //     }
  //   }
  // },
  async mounted() {
    this.getlist();
    //await this.setBandSelect();
    //await this.setGroupSelect();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager}
      this.listLoading = true
      const res = await pageAbnormalGeneral(params)
      this.listLoading = false
      if (!res?.success)return    
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
