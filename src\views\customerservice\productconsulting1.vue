<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent
      >
        <el-form-item label="商品ID:">
          <el-input v-model="Filter.ProductID" style="width: 110px" />
        </el-form-item>

        <el-link :underline="false">人员转换率：</el-link>

        <el-form-item prop="renmin">
          <el-input v-model="Filter.MinPersonRate" style="width: 80px" />
        </el-form-item>
        ~
        <el-form-item prop="renmax">
          <el-input v-model="Filter.MaxPersonRate" style="width: 80px" />
        </el-form-item>

        <el-link :underline="false">机器转换率：</el-link>

        <el-form-item prop="renmin">
          <el-input v-model="Filter.MinAiRate" style="width: 80px" />
        </el-form-item>
        ~
        <el-form-item prop="renmax">
          <el-input v-model="Filter.MaxAiRate" style="width: 80px" />
        </el-form-item>

        <el-form-item label="时间:">
 

          <el-date-picker
            v-model="Filter.timerange"
            type="datetimerange" 
             :picker-options="pickerOptions"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>


        <el-form-item label="包含机器人:">
        <el-switch :width="40" @change="changecontainjiqi"
          v-model="Filter.containjiqistatus"
          inactive-color="#228B22"
          active-text="是"
          inactive-text="否">
        </el-switch>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>

        <!-- <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
       
        <el-form-item>
          <a href="../static/excel/快递费用规则导入模板(每个快递公司一个文件).xlsx">
            <el-button type="primary">下载导入魔板</el-button>
          </a>
        </el-form-item> -->
        <el-form-item>
          <el-button size="small" type="primary" @click="startImport"
            >导入商品咨询明细</el-button
          >
        </el-form-item>

        <el-form-item>
          <el-button size="small" type="primary" @click="startExport"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->

    <ces-table
      ref="table"
      :that="that"
      :isIndex="true"
      :hasexpand="true"
      @sortchange="sortchange"
      :isSelection="true"
      :tableData="ExpressList"
      :tableCols="tableCols"
      :tableHandles="tableHandles"
      :loading="listLoading"
    >
    </ces-table>

    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="ratelisttotal"
        @get-page="getExpressList"
      />
    </template>

    <!--新增快递费规则-->

    <el-dialog
      title="导入商品咨询明细"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <span>
        <el-row>
          <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
            <el-upload
              ref="upload"
              class="upload-demo"
              :auto-upload="false"
              :multiple="false"
              :limit="1"
              action
              accept=".xlsx"
              :http-request="uploadFile"
              :on-change="uploadchange"
              :file-list="fileList"
              :data="fileparm"
            >
              <template #trigger>
                <el-button size="small" type="primary"
                  >选取商品咨询文件</el-button
                >
              </template>
              <el-button
                style="margin-left: 10px"
                size="small"
                type="success"
                @click="submitUpload"
                >上传</el-button
              >
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="每人转化统计"
      :visible.sync="everyPersonVisible"
      width="60%"
    >
      <span>
        <el-table
          v-loading="listLoading"
          :data="persondata"
          highlight-current-row
          :lazy="true"
          style="width: 100%; height: 100%"
          @selection-change="onSelsChange"
          :default-expand-all="false"
          :row-class-name="getRowClass"
        >
          <!-- <el-table-column type="selection" width="40" /> -->
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column prop="productID" label="商品ID" width="120" />
          <el-table-column prop="seller" sortable label="人员" width="120">
            <!-- <template #default="{ row }">
          <el-tag
            :type="row.enabled ? 'success' : 'danger'"
            disable-transitions
            >{{ row.enabled ? "正常" : "禁用" }}</el-tag>
        </template> -->
          </el-table-column>

          <el-table-column
            prop="expressCompany"
            sortable
            label="成交"
            width="100"
          >
            <template #default="{ row }">
              {{ row.rensuccess + row.jiqisuccess }}
            </template>
          </el-table-column>

          <el-table-column
            prop="freightRuleType"
            sortable
            label="未成交"
            width="100"
          >
            <template #default="{ row }">
              {{ row.renfail + row.jiqifail }}
            </template>
          </el-table-column>

          <el-table-column
            prop="jiqi"
            sortable
            label="合计咨询人数"
            width="100"
          >
            <template #default="{ row }">
              {{
                row.rensuccess + row.jiqisuccess + row.renfail + row.jiqifail
              }}
            </template>
          </el-table-column>

          <el-table-column prop="rate" sortable label="转化率" width="180">
            <template #default="{ row }">
              <div v-if="row.isred" style="color: red">
                {{ parseInt(row.rate * 1000) / 10 }}%
              </div>

              <div v-if="!row.isred" style="color: black">
                {{ parseInt(row.rate * 1000) / 10 }}%
              </div>
            </template>
          </el-table-column>
        </el-table>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="everyPersonVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="detailType" :visible.sync="detailVisible" width="70%" v-dialogDrag>
      <span>
        <el-table
          v-loading="listLoading"
          :data="detaildata"
          highlight-current-row
          :lazy="true"
          style="width: 100%; height: 100%"
          @selection-change="onSelsChange"
          :default-expand-all="false"
          :row-class-name="getRowClass"
        >
          <!-- <el-table-column type="selection" width="40" /> -->
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column prop="productID" label="商品ID" width="120" />
          <el-table-column prop="seller" label="人员" width="120">
            <!-- <template #default="{ row }">
          <el-tag
            :type="row.enabled ? 'success' : 'danger'"
            disable-transitions
            >{{ row.enabled ? "正常" : "禁用" }}</el-tag>
        </template> -->
          </el-table-column>

          <el-table-column prop="productName" label="商品名称" width="600">
          </el-table-column>

          <el-table-column prop="buyer" label="咨询者" width="180">
            <template #default="{ row }">
              <el-link :underline="true" @click="copyinfo(row.buyer)">{{
                row.buyer
              }}</el-link>
            </template>
          </el-table-column>

          <el-table-column prop="chatdate" label="咨询日期" width="100">
            <template #default="{ row }">
              {{ formatdate(row.chatdate) }}
            </template>
          </el-table-column>

          <el-table-column prop="createdTime" label="是否成交" width="80">
            <template #default="{ row }">
              <div v-if="row.result == 2" style="color: red">未成交</div>

              <div v-if="row.result == 1" style="color: black">已成交</div>
            </template>
          </el-table-column>
        </el-table>

        <div class="block">
          <el-pagination
            layout="prev, pager, next"
            @current-change="changePage"
            :total="detailpager.total"
          >
          </el-pagination>
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import cesTable from "@/components/Table/table.vue";
import {
  formatPlatform,
  formatYesorno,
  formatYesornoBool,
  formatbianmastatus,
  formatproducmask,
  formatLink,
  formatLinkProCode,
} from "@/utils/tools";
import {
  ruleExpressComany,
  ruleWarehouse,
  ruleYesornoBool,
} from "@/utils/formruletools";

import {
  importProductconsulting,
  getRateList,
  getRateDetailList,
  getConsultingList,
  exportRateList,
} from "@/api/customerservice/productconsulting";

/*  formatterPersonsum(row) {

 return row.rensuccess + row.renfail;

   },
   formatterPersonRate(row) {
  return parseInt(row.ren * 100)+"%";

   },
   formatterAIsum(row) {
 return row.jiqisuccess + row.jiqifail;

   },
   formatterAIRate(row) {
 return parseInt(row.jiqi * 100)+"%";

   },
   formatterCompareRate(row) {

     if(row.ren - row.jiqi >= 0.03)
     {
return "<div style='color:red;'>"+parseInt((row.ren - row.jiqi) * 1000) / 10+"%"+"</div>";
     }
     else
     {
return  parseInt((row.ren - row.jiqi) * 1000) / 10+"%";

     }

     
   },


   */

const tableCols = [
  {
    istrue: true,
    prop: "productID",
    label: "商品ID",
    width: "180",
    sortable: "custom",
    type: "html",
    type: "html",
    formatter: (row) => formatLinkProCode(row.platform, row.productID),
  },
  { istrue: true, prop: "productName", label: "标题", width: "260" },
  {
    istrue: true,
    prop: "rensuccess",
    label: "人工咨询合计",
    width: "120",
    sortable: "custom",
    formatter: (row) => {
      return row.rensuccess + row.renfail;
    },
  },
  { istrue: true, prop: "shopname", label: "店铺", width: "120" },
  { istrue: true, prop: "oprater", label: "运营人", width: "100" },
  { istrue: true, prop: "oprateManager", label: "组长", width: "100" },

  {
    istrue: true,
    prop: "ren",
    label: "人工转化率",
    width: "120",
    sortable: "custom",
    formatter: (row) => {
      return parseInt(row.ren * 100) + "%";
    },
  },
  {
    istrue: true,
    prop: "jiqisuccess",
    label: "机器咨询合计",
    width: "120",
    formatter: (row) => {
      return row.jiqisuccess + row.jiqifail;
    },
  },
  {
    istrue: true,
    prop: "jiqi",
    label: "机器转化率",
    width: "120",
    sortable: "custom",
    formatter: (row) => {
      return parseInt(row.jiqi * 100) + "%";
    },
  },
  {
    istrue: true,
    prop: "highrate",
    label: "高转化均值",
    width: "120",
    sortable: "custom",
    formatter: (row) => {
      return parseInt(row.highrate * 1000) / 10 + "%";
    },
  },
  {
    istrue: true,
    prop: "hasContainFaceFee",
    label: "转化差值",
    width: "100",
    type: "html",
    formatter: (row) => {
      if (row.ren - row.jiqi >= 0.03) {
        return (
          "<div style='color:red;'>" +
          parseInt((row.ren - row.jiqi) * 1000) / 10 +
          "%" +
          "</div>"
        );
      } else {
        return parseInt((row.ren - row.jiqi) * 1000) / 10 + "%";
      }
    },
  },

  {
    istrue: true,
    type: "button",
    width: "230",
    btnList: [
      { label: "按人统计", handle: (that, row) => that.showperson(row) },
      { label: "成交明细", handle: (that, row) => that.showdetail(row, 1) },
      { label: "未成交明细", handle: (that, row) => that.showdetail(row, 2) },
    ],
  },
];
const tableHandles1 = [];

export default {
  name: "Users",
  components: {
    MyContainer,
    cesTable,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
  },

  data() {
    return {
      that: this,
      Filter: {
        StartCreatedTime: "",
        EndCreatedTime: "",
        Warehouse: null,
        Province: null,
        Enabled: null,

        FreightRuleType: null,
        timerange: "",
        yearmonth: null,
      },
      tableCols: tableCols,
      tableHandles: tableHandles1,
      pager: { OrderBy: "rensuccess", IsAsc: false },
      dynamicFilter: null,
      ExpressList: [],

      detailpager: {
        OrderBy: "seller",
        pageSize: 10,
        total: 1,
        pageIndex: 1,
        IsAsc: true,
      },

          pickerOptions: {
          shortcuts: [{
            text: '近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },{
            text: '近十五天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },

      detailVisible: false,
      detailType: "成交明细",

      personpager: {
        OrderBy: "rensuccess",
        pageSize: 200,
        pageIndex: 1,
        IsAsc: false,
      },
      everyPersonVisible: false,

      persondata: [],
      detaildata: [],

      showpersonrow: {},

   
      

      total: 0,
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      addDialogFormVisible: false,
      editFormVisible: false, // 编辑界面是否显示
      editLoading: false,
      editFormRules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
      },
      userNameReadonly: true,
      // 编辑界面数据
      editForm: {},
      addFormVisible: false, // 新增界面是否显示
      addLoading: false,
      addFormRules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      // 新增界面数据
      addForm: {},
      deleteLoading: false,
      importFilte: { companyid: null, warehouse: null, yearmonth: null },
      expresscompanylist: [],
      dialogVisible: false,
      dialogbatchNumberVisible: false,
      dialogdeletebatchNumberVisible: false,
      dialogCloneVisible: false,

      fileList: [],
      fileparm: {},
      cloneparm: {
        oldBatchnumber: "",
        newYearMonth: "",
      },
    };
  },
  async mounted() {
  

   this.getDate();

 
  await this.onSearch();
 
  },
  methods: {

    getDate() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);      
              this.Filter.timerange=[start, end]
  },
 
    changecontainjiqi(){
 

    },
    async startExport() {
      var that = this;
      const para = {};
      if (that.Filter.Enabled == "true") para.enabled = true;
      else if (that.Filter.Enabled == "false") para.enabled = false;
      para.warehouse = that.Filter.Warehouse;

      console.log(that.Filter);
      //debugger
      if (that.Filter.timerange) {
        para.StartDate = that.Filter.timerange[0];
        para.EndDate = that.Filter.timerange[1];
      }

      if (that.Filter.MinPersonRate) {
        para.MinPersonRate = that.Filter.MinPersonRate / 100;
      }
         if(that.Filter.containjiqistatus)
      {
           para.MinAiRate = 0.001;
        
      }
      if (that.Filter.MinAiRate) {
        para.MinAiRate = that.Filter.MinAiRate / 100;
      }
      if (that.Filter.MaxPersonRate) {
        para.MaxPersonRate = that.Filter.MaxPersonRate / 100;
      }


           if (that.Filter.MaxAiRate) {
        para.MaxAiRate = that.Filter.MaxAiRate / 100;
      }


         if(!that.Filter.containjiqistatus)
      {

          if(para.MinAiRate)
           para.MinAiRate = 0;
           para.MaxAiRate = 0;
        
      }



 
      if (that.Filter.ProductID) {
        para.ProductID = that.Filter.ProductID;
      }
   

      var pager = this.$refs.pager.getPager();
      var filter = that.Filter.filter;

      const params = {
        ...this.pager,
        ...pager,

        ...para,
        //dynamicFilter: this.dynamicFilter
      };

      var res = await exportRateList(params);
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "商品咨询分析_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },
    startImport() {
      this.dialogVisible = true;
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    persondatasortchange(column) {
      if (!column.order)
        this.personpager = { OrderBy: column.prop, IsAsc: false };
      else
        this.personpager = {
          OrderBy: column.prop,
          pageSize: 200,
          pageIndex: 1,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.showperson(this.showpersonrow);
    },
    copyinfo(e1) {
      var that = this;

      this.$copyText(e1).then(
        function (e) {
          that.$message({
            type: "success",
            message: "已复制",
          });
        },
        function (e) {
          alert("Can not copy");
          console.log(e);
        }
      );
    },
    changePage(e) {
      var that = this;

      if (this.Filter.timerange) {
        this.detailpager.StartDate = this.Filter.timerange[0];
        this.detailpager.EndDate = this.Filter.timerange[1];
      }

      this.detailpager.CurrentPage = e;

      const params = {
        ...this.detailpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };
      getConsultingList(params).then((res) => {
        that.detaildata = res.data.list;
        that.detailpager.total = res.data.total;
        that.detailVisible = true;
      });
    },
    showperson(row) {
      var that = this;
      this.showpersonrow = row;

      if (this.Filter.timerange) {
        this.personpager.StartDate = this.Filter.timerange[0];
        this.personpager.EndDate = this.Filter.timerange[1];
      } else {
        this.personpager.StartDate = null;
        this.personpager.EndDate = null;
      }

      console.log(row);

      const params = {
        ...this.personpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };
      params.ProductID = row.productID;

      getRateDetailList(params).then((res) => {
        that.persondata = res.data.list;

        //(row.rensuccess+row.jiqisuccess)*1000/(row.rensuccess+row.jiqisuccess+row.renfail+row.jiqifail)
        var allsuccess = 0;
        var allfail = 0;
        that.persondata.forEach(function (item, index) {
          allsuccess += item.rensuccess;
          allfail += item.renfail;
        });

        var allrate = allsuccess / (allfail + allsuccess);

        that.persondata.forEach(function (item, index) {
          var success = item.rensuccess + item.jiqisuccess;
          var fail = item.renfail + item.jiqifail;

          item.rate = success / (fail + success);
          if (item.rate < allrate - 0.03) {
            item.isred = true;
          }
        });

        that.persondata.push({
          seller: "人工转化率合计",
          jiqisuccess: 0,
          rensuccess: allsuccess,
          renfail: allfail,
          jiqifail: 0,
          rate: allrate,
        });

        console.log(res.data);
        that.everyPersonVisible = true;
      });
    },
    showdetail(row, type) {
      var that = this;
      if (type == 1) this.detailType = "成交明细";
      if (type == 2) this.detailType = "未成交明细";

      this.detailpager.ResultType = type;

      if (this.Filter.timerange) {
        this.detailpager.StartDate = this.Filter.timerange[0];
        this.detailpager.EndDate = this.Filter.timerange[1];
      } else {
        this.detailpager.StartDate = null;
        this.detailpager.EndDate = null;
      }

      this.detailpager.ProductID = row.productID;
      this.detailpager.CurrentPage = 1;

      const params = {
        ...this.detailpager,
        ...this.Filter,
        //dynamicFilter: this.dynamicFilter
      };
      getConsultingList(params).then((res) => {
        that.detaildata = res.data.list;
        that.detailpager.total = res.data.total;
        that.detailVisible = true;
      });
    },
    formatCreatedTime(row, column, time) {
      return formatTime(time, "YYYY-MM-DD HH:mm");
    },
    formatdate(time) {
      return formatTime(time, "YYYY-MM-DD");
    },
    getRowClass({ row, rowIndex }) {
      return "row-expand-cover";
      // if (row.ruleDetails.length == 0) {
      // return 'row-expand-cover';
      // } else {
      // return '';
      //    }
    },

    //上传成功
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
        //状态码为200时则上传成功
      } else {
        //状态码不是200时上传失败 从列表中删除
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    uploadchange() {
      // this.$refs.upload.clearFiles();
    },
    submitUpload() {
      this.$refs.upload.submit();
      // this.$confirm("确定导入吗?, 是否继续?", "提示", {
      // confirmButtonText: "确定",
      // cancelButtonText: "取消",
      // type: "warning",
      // })
      // .then(() => {
      //   debugger
      //     this.$refs.upload.submit();
      // })
      // .catch(() => {});
    },
    uploadFile(item) {
      var that = this;
      const form = new FormData();
      form.append("token", this.token);

      form.append("upfile", item.file);
      const res = importProductconsulting(form).then((res) => {
        console.log(res);

        that.dialogVisible = false;
        that.fileList = [];

        this.$message({
          message: "上传成功,正在导入中...",
          type: "success",
        });
      });
    },

    // 查询
    async onSearch(dynamicFilter) {
      this.$refs.pager.setPage(1);
      this.dynamicFilter = dynamicFilter;
      this.getExpressList();
    },

    async getExpressList() {
      var that = this;
      const para = {};
      if (that.Filter.Enabled == "true") para.enabled = true;
      else if (that.Filter.Enabled == "false") para.enabled = false;
      para.warehouse = that.Filter.Warehouse;

      console.log(that.Filter);
      //debugger
      if (that.Filter.timerange) {
        para.StartDate = that.Filter.timerange[0];
        para.EndDate = that.Filter.timerange[1];
      }

      if (that.Filter.MinPersonRate) {
        para.MinPersonRate = that.Filter.MinPersonRate / 100;
      }

 
//用于过滤是否包含机器人，不包含机器人则最低AI rate 为0.1%
      if(that.Filter.containjiqistatus)
      {
           para.MinAiRate = 0.001;
        
      }
      if (that.Filter.MinAiRate) {
        para.MinAiRate = that.Filter.MinAiRate / 100;
      }
      if (that.Filter.MaxPersonRate) {
        para.MaxPersonRate = that.Filter.MaxPersonRate / 100;
      }

 
     
      if (that.Filter.MaxAiRate) {
        para.MaxAiRate = that.Filter.MaxAiRate / 100;
      }



            if(!that.Filter.containjiqistatus)
      {


           if(para.MinAiRate)
           para.MinAiRate = 0;
           para.MaxAiRate = 0;
        
      }



      if (that.Filter.ProductID) {
        para.ProductID = that.Filter.ProductID;
      }

      var pager = this.$refs.pager.getPager();
      var filter = that.Filter.filter;

      const params = {
        ...this.pager,
        ...pager,

        ...para,
        //dynamicFilter: this.dynamicFilter
      };
      this.listLoading = true;

      getRateList(params).then((res) => {
        that.listLoading = false;
        //if (!res?.success) {
        //return;
        //}
        that.ratelisttotal = res.data.total;
        const data = res.data.list;
        var i = 1;
        data.forEach((d) => {
          d.enabled = true;
          d.id = i;
          i++;
          // d._loading = false;
        });
        that.ExpressList = data;
        that.$forceUpdate();

        console.log(that.ExpressList);
      });

      // const res = await getRateList(params);
    },
    async startEdit(row) {
      row.edit = true;
    },
    cancelEdit(row) {
      row.edit = false;
      if (row.id === undefined) {
        // 重新加载该页面
      }
    },

    // 选择
    onSelsChange(sels) {
      this.sels = sels;
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
