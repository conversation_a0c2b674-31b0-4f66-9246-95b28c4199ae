<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="添加开始日期" end-placeholder="添加结束日期" style="margin-right: 10px;"
                    :picker-options="pickerOptions" :clearable="false" @change="changeListTime" />
                <el-select v-model="ListInfo.status" placeholder="状态" clearable class="publicCss">
                    <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.businessDDUserId" placeholder="寄样商务" clearable class="publicCss"
                    filterable>
                    <el-option v-for="item in businessManList" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.wiseManAccountCode" placeholder="抖音号" clearable class="publicCss"
                    filterable>
                    <el-option v-for="item in accountCodeList" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.shopCode" placeholder="店铺名称" clearable class="publicCss" filterable>
                    <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model="ListInfo.proCode" placeholder="产品id" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.proName" placeholder="产品名称" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.expressNo" placeholder="快递单号" maxlength="20" clearable class="publicCss" />
                <el-button type="primary" @click="getList(true)">搜索</el-button>
                <el-button type="primary" @click="addInfo()">新增</el-button>
                <el-button type="primary" @click="startImport">导入</el-button>
                <el-button type="primary" @click="onModel">下载模板</el-button>
            </div>
        </template>
        <vxetablebase :id="'sampleBusiness'" :tablekey="'sampleBusiness'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" v-loading="loading" style="width: 100%; margin: 0"
            height="100%" />
            <template #footer>
                <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
            </template>

        <!-- 跟进情况 -->
        <el-dialog title="跟进情况" :visible.sync="followUpDialogVisible" width="50%" v-dialogDrag>
            <div class="top">
                <el-date-picker v-model="dialogTimeRange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" class="publicCss" :picker-options="pickerOptions"
                    @change="changeTime" />
                <el-button type="primary" @click="addFollowList">新增</el-button>
            </div>
            <vxetablebase :id="'sampleBusiness202408041442'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
                :tablefixed='true' @sortchange='followUpSortchange' :tableData='followUpTableData'
                :tableCols='followUpTableCols' :isSelection="false" :isSelectColumn="false" v-loading="loading"
                style="width: 100%;  margin: 0" />
            <my-pagination ref="pager" :total="followUpTotal" @page-change="followUpPagechange"
                @size-change="followUpSizechange" />
        </el-dialog>

        <el-dialog title="新增跟进记录" :visible.sync="addFollowUpDialogVisible" width="40%" v-dialogDrag>
            <el-input type="textarea" :rows="2" placeholder="跟进内容" v-model="addFollowUpInfo.trackContext"
                style="margin-bottom: 10px;" />
            <uploadimgFile v-if="addFollowUpDialogVisible" ref="uploadimgFile" :disabled="!formEditMode"
                :noDel="!formEditMode" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                @callback="getImg" :filemaxsize="8" :imgmaxsize="8" :limit="8" :multiple="true">
            </uploadimgFile>
            <div class="dialogTop">
                <el-button @click="addFollowUpDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitAddInfo">保存</el-button>
            </div>
        </el-dialog>

        <el-dialog title="新增寄样信息" :visible.sync="addDialogVisible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <el-form label-width="150px" :model="formData" :rules="rules" ref="ruleForm">
                <el-form-item label="寄样商务" prop="businessMan">
                    <el-input v-model="formData.businessMan" placeholder="寄样商务" maxlength="300" clearable disabled />
                </el-form-item>
                <el-form-item label="搜索" prop="search">
                    <el-select v-model="formData.search" filterable placeholder="搜索" @change="changeUid" clearable
                        style="width:100%" class="formSelect" :disabled="changeExpressNoDisabled">
                        <el-tooltip v-for="(item, i) in wiseManUidList" :key="i" class="item" effect="dark"
                            :content="item.wiseManUID + '--' + item.wiseManAccountName + `(${item.wiseManAccountCode})` + '--' + item.proCode + '--' + item.proName"
                            placement="top-start">
                            <el-option class="selectBox"
                                :label="item.wiseManUID + '--' + item.wiseManAccountName + `(${item.wiseManAccountCode})` + '--' + item.proCode + '--' + item.proName"
                                :value="item.id">
                            </el-option>
                        </el-tooltip>
                    </el-select>
                </el-form-item>
                <el-form-item label="达人UID" prop="wiseManUID" disabled>
                    <el-input v-model="formData.wiseManUID" placeholder="达人UID" maxlength="300" clearable disabled />
                </el-form-item>
                <el-form-item label="达人昵称" prop="wiseManAccountName">
                    <el-input v-model="formData.wiseManAccountName" placeholder="达人昵称" maxlength="300" clearable
                        disabled />
                </el-form-item>
                <el-form-item label="抖音号" prop="wiseManAccountCode">
                    <el-input v-model="formData.wiseManAccountCode" placeholder="抖音号" maxlength="300" disabled />
                </el-form-item>
                <el-form-item label="粉丝数" prop="fansCount">
                    <el-input-number style="width:100%" v-model="formData.fansCount" placeholder="粉丝数" clearable
                        :controls="false" :precision="0" :max="*********" :min="0" />
                    <!-- <el-input v-model="formData.fansCount" placeholder="粉丝数" maxlength="10" type="number" :max="*********"
                        :min="0" clearable  @change="changeFansCount"/> -->
                </el-form-item>
                <el-form-item label="产品id" prop="proCode">
                    <el-input v-model="formData.proCode" placeholder="产品id" maxlength="300" disabled />
                </el-form-item>
                <el-form-item label="产品名称" prop="proName">
                    <el-input v-model="formData.proName" placeholder="产品名称" maxlength="300" clearable />
                </el-form-item>
                <el-form-item label="店铺名称" prop="shopName">
                    <el-input v-model="formData.shopName" placeholder="店铺名称" maxlength="300" disabled />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="formData.status" placeholder="状态" clearable class="publicCss"
                        :disabled="changeExpressNoDisabled && changeStatusDisabled">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="快递单号">
                    <el-input v-model.trim="formData.expressNo" placeholder="快递单号" maxlength="300" clearable
                        :disabled="changeExpressNoDisabled" />
                </el-form-item>
                <el-form-item label="是否开播/视频链接">
                    <el-input v-model.trim="formData.videoLink" placeholder="是否开播" maxlength="400" clearable
                        :disabled="changeVideoLinkDisabled" />
                </el-form-item>
                <el-form-item>
                    <div class="btnGroup">
                        <el-button @click="colseAddDialog">取消</el-button>
                        <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">提交</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>
        <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs'
import { pickerOptions, formatLinkProCode } from '@/utils/tools'
import { GetDYBusinessWiseManList, GetAllWiseManDetailList, getProductDySampleWiseManBzRefList } from "@/api/bookkeeper/reportdayDouYin";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    SaveProductDyWiseManBzRef,
    GetBusinessMan,
    getProductDySamplePageList,
    saveProductDySample,
    getProductDySampleTrackPageList,
    saveProductDySampleTrack,importProductDySampleAsync
} from "@/api/bookkeeper/reportdayDouYin";
import _ from 'lodash'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { getCurrentUser } from '@/api/inventory/packagesprocess'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
const statusList = [
    {
        label: '未寄样',
        value: 0
    },
    {
        label: '已寄样',
        value: 1
    },
    {
        label: '待开始',
        value: 2
    },
    {
        label: '已完成',
        value: 3
    },
]

const tableCols = [
    { istrue: true, prop: 'status', label: '状态', sortable: 'custom', width: 'auto', formatter: (row) => statusList.find(item => item.value == row.status).label },
    { istrue: true, prop: 'businessMan', label: '寄样商务', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'wiseManAccountName', label: '达人昵称', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'wiseManAccountCode', label: '抖音号', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'wiseManUID', label: '达人UID', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'fansCount', label: '粉丝数', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'proCode', label: '产品id', sortable: 'custom', width: '150', type: 'html', formatter: (row) => formatLinkProCode(6, row.proCode) },
    { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'proName', label: '产品名称', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'expressNo', label: '快递单号', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'expressStartTime', label: '寄样日期', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'videoLink', label: '是否开播/视频链接', width: '200', type: 'click', handle: (that, row) => that.openLink(row) },
    { istrue: true, prop: 'finishTime', label: '完成日期', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'createdTime', label: '添加日期', sortable: 'custom', width: 'auto' },
    {
        istrue: true, label: '跟进情况', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '详情', handle: (that, row) => that.followUpInfo(row, true) },
            ]
    },
    {
        istrue: true, label: '功能', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '编辑', handle: (that, row) => that.editInfo(row) },
            ]
    },
]

const followUpTableCols = [
    { istrue: true, prop: 'createdTime', label: '日期', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'trackContext', label: '跟进内容', sortable: 'custom', width: 'auto' },
    { istrue: true, prop: 'trackImgs', label: '图片', width: 'auto', type: 'images' },
]

export default {
    name: 'sampleBusiness',
    components: {
        MyContainer, vxetablebase, uploadimgFile
    },
    data() {
        return {
            dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
            total: 0,
            formEditMode: true,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            pickerOptions,
            timeRange: [],
            dialogTimeRange: [],
            tableCols,
            statusList,
            that: this,
            shopList: [],
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                shopCode: null,
                businessDDUserId: null,
                wiseManAccountCode: null,
                proCode: null,
                proName: null,
                expressNo: null,
                status: null,
                startTime: null,
                endTime: null,
            },
            tableData: [],
            loading: false,
            businessManList: [],
            accountNameList: [],
            accountCodeList: [],
            followUpDialogVisible: false,
            followUpTableData: [],
            followUpTableCols,
            followUpTotal: 0,
            addDialogVisible: false,
            formData: {
                businessMan: null,
                wiseManUID: null,
                wiseManAccountName: null,
                wiseManAccountCode: null,
                // fansCount: null,
                proCode: null,
                proName: null,
                shopName: null,
                status: null,
                expressNo: null,
                videoLink: null,
                shopCode: null,
                id: null,
                search: null,
            },
            rules: {
                businessMan: [
                    { required: true, message: '请输入寄样商务', trigger: 'blur' },
                ],
                wiseManUID: [
                    { required: true, message: '请选择达人UID', trigger: 'blur' },
                ],
                wiseManAccountName: [
                    { required: true, message: '请输入达人昵称', trigger: 'blur' },
                ],
                wiseManAccountCode: [
                    { required: true, message: '请输入抖音号', trigger: 'blur' },
                ],
                fansCount: [
                    { required: true, message: '请输入粉丝数', trigger: 'blur' },
                ],
                proCode: [
                    { required: true, message: '请输入产品id', trigger: 'blur' },
                ],
                proName: [
                    { required: true, message: '请输入产品名称', trigger: 'blur' },
                ],
                shopName: [
                    { required: true, message: '请输入店铺名称', trigger: 'blur' },
                ],
                status: [
                    { required: true, message: '请输入状态', trigger: 'blur' },
                ]
            },
            getProductInfo: {
                businessDDUserId: null,
                wiseManInfo: null,
            },
            wiseManUidList: [],
            isEdit: false,
            followInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                parentId: null,
                id: null,
                startTime: null,
                endTime: null,
            },
            addFollowUpDialogVisible: false,
            addFollowUpInfo: {
                parentId: null,
                trackContext: null,
                trackImgs: null,
            },
            chatUrls: [],
            userInfo: {},
            changeExpressNoDisabled: false,
            changeVideoLinkDisabled: false,
            changeStatusDisabled: false,
            isDks: false,
        }
    },
    //监听formData.videoLink和formData.expressNo
    watch: {
        //深度监听
        'formData.expressNo': {
            handler: function (val, oldVal) {
                if (val == '') {
                    this.formData.expressNo == null
                }
                if ((this.formData.videoLink != null && this.formData.videoLink != '') && (this.formData.expressNo != null && this.formData.expressNo != '')) {
                    this.formData.status = 3
                }
                if (this.isDks) {
                    if ((this.formData.videoLink == null || this.formData.videoLink == '') && this.formData.expressNo != null && this.formData.expressNo != '') {
                        this.formData.status = 2
                    }
                } else {
                    if ((this.formData.videoLink == null || this.formData.videoLink == '') && this.formData.expressNo != null && this.formData.expressNo != '') {
                        this.formData.status = 1
                    }
                }
                if ((this.formData.videoLink == null || this.formData.videoLink == '') && (this.formData.expressNo == null || this.formData.expressNo == '')) {
                    this.formData.status = 0
                }
            },
            deep: true
        },
        'formData.videoLink': {
            handler: function (val, oldVal) {
                if (val == '') {
                    this.formData.videoLink == null
                }
                if ((this.formData.videoLink != null && this.formData.videoLink != '') && (this.formData.expressNo != null && this.formData.expressNo != '')) {
                    this.formData.status = 3
                }
                if (this.isDks) {
                    if ((this.formData.videoLink == null || this.formData.videoLink == '') && this.formData.expressNo != null && this.formData.expressNo != '') {
                        this.formData.status = 2
                    }
                } else {
                    if ((this.formData.videoLink == null || this.formData.videoLink == '') && this.formData.expressNo != null && this.formData.expressNo != '') {
                        this.formData.status = 1
                    }
                }
                if ((this.formData.videoLink == null || this.formData.videoLink == '') && (this.formData.expressNo == null || this.formData.expressNo == '')) {
                    this.formData.status = 0
                }
            },
            deep: true
        },
    },
    async mounted() {
        this.getList()
        const { data } = await getCurrentUser()
        this.userInfo = data
        this.getProductInfo.businessDDUserId = data.userId
    },
    methods: {
        changeListTime(e) {
            this.ListInfo.startTime = dayjs(e[0]).format('YYYY-MM-DD')
            this.ListInfo.endTime = dayjs(e[1]).format('YYYY-MM-DD')
            this.timeRange = e
            this.getList()
        },
        changeTime(e) {
            if (e) {
                this.followInfo.startTime = dayjs(e[0]).format('YYYY-MM-DD')
                this.followInfo.endTime = dayjs(e[1]).format('YYYY-MM-DD')
                this.dialogTimeRange = e
            } else {
                this.followInfo.startTime = null
                this.followInfo.endTime = null
                this.dialogTimeRange = []
            }
            this.followUpInfo(this.followInfo, false, true)
        },
        openLink(row) {
            window.open(row.videoLink, '_blank')
        },
        async submitAddInfo() {
            if (this.chatUrls.length == 1) {
                this.addFollowUpInfo.trackImgs = this.chatUrls.map(item => item.url).join(',')
            } else {
                this.addFollowUpInfo.trackImgs = this.chatUrls.map((item, i) => {
                    return {
                        url: item.url,
                        name: i
                    }
                })
                this.addFollowUpInfo.trackImgs = JSON.stringify(this.addFollowUpInfo.trackImgs)
            }
            this.addFollowUpInfo.parentId = this.followInfo.parentId
            const { success } = await saveProductDySampleTrack(this.addFollowUpInfo)
            if ((success)) {
                this.$message.success('添加跟进记录成功')
            }
            this.followUpInfo(this.followInfo, false, true)
            this.addFollowUpDialogVisible = false
        },
        getImg(data) {
            this.chatUrls = data
            this.addFollowUpInfo.trackImgs = data.map(item => item.url)
            this.addFollowUpInfo.trackImgs = this.addFollowUpInfo.trackImgs.join(',')
        },
        addFollowList() {
            this.chatUrls = []
            this.addFollowUpInfo.trackImgs = null
            this.addFollowUpInfo.trackContext = null
            this.addFollowUpDialogVisible = true
        },
        async editInfo(row) {
            this.changeExpressNoDisabled = (row.status != 0) ? true : false
            this.changeVideoLinkDisabled = row.status == 3 ? true : false
            if (row.status == 1) {
                if ((row.expressNo != null && row.expressNo != '') && (row.videoLink == null || row.videoLink == '')) {
                    this.changeStatusDisabled = false
                } else {
                    this.changeStatusDisabled = true
                }
            }
            this.isEdit = true
            this.clear()
            this.addDialogVisible = true
            const { data, success } = await getProductDySamplePageList({ id: row.id })
            if (success) {
                if (data.list[0].status == 2) {
                    this.isDks = true
                } else {
                    this.isDks = false
                }
                this.formData = {
                    businessMan: data.list[0].businessMan,
                    wiseManUID: data.list[0].wiseManUID,
                    wiseManAccountName: data.list[0].wiseManAccountName,
                    wiseManAccountCode: data.list[0].wiseManAccountCode,
                    fansCount: data.list[0].fansCount,
                    proCode: data.list[0].proCode,
                    proName: data.list[0].proName,
                    shopName: data.list[0].shopName,
                    status: data.list[0].status,
                    expressNo: data.list[0].expressNo,
                    videoLink: data.list[0].videoLink,
                    shopCode: data.list[0].shopCode,
                    id: data.list[0].id
                }
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    let queryInfo = {
                        ...this.formData,
                        businessDDUserId: this.getProductInfo.businessDDUserId,
                    }
                    if (this.formData.fansCount < 0) return this.$message.error('粉丝数不能小于0');
                    //如果有快递单号,没有视频地址就提示
                    if ((this.formData.expressNo == null && this.formData.expressNo == '') && (this.formData.videoLink != null && this.formData.videoLink != '')) {
                        return this.$message.error('请填写快递单号');
                    }
                    if (this.formData.status == 0 || this.formData.status == 3) {
                        if ((this.formData.expressNo != null && this.formData.expressNo != '') && (this.formData.videoLink == null || this.formData.videoLink == '')) {
                            return this.$message.error('未填写视频地址不能选择未寄样和已完成状态');
                        }
                    }
                    const { success } = await saveProductDySample(queryInfo)
                    if (success) {
                        this.$message.success('操作成功')
                        this.getList()
                        this.addDialogVisible = false
                    } else {
                        this.$message.error('操作失败')
                    }
                } else {
                    return this.$message.error('请完善信息');
                }
            });
        }, // 限制函数的执行频率为每秒最多一次
        async changeUid(e) {
            if (e) {
                this.getProductInfo.wiseManInfo = e
            } else {
                this.getProductInfo.wiseManInfo = null
                this.clear()
            }
            const res = this.wiseManUidList.filter(item => item.id == e)[0]
            this.formData.wiseManUID = res.wiseManUID
            this.formData.wiseManAccountName = res.wiseManAccountName
            this.formData.wiseManAccountCode = res.wiseManAccountCode
            this.formData.proCode = res.proCode
            this.formData.proName = res.proName
            this.formData.shopName = res.shopName
            this.formData.shopCode = res.shopCode
        },
        colseAddDialog() {
            this.clear()
            this.addDialogVisible = false
        },
        clear() {
            this.formData = {
                businessMan: null,
                wiseManUID: null,
                wiseManAccountName: null,
                wiseManAccountCode: null,
                // fansCount: null,
                proCode: null,
                proName: null,
                shopName: null,
                status: null,
                expressNo: null,
                videoLink: null,
            }
        },
        async addInfo() {
            this.isEdit = false
            this.changeExpressNoDisabled = false
            this.changeVideoLinkDisabled = false
            //this.formData.fansCount = null
            this.getProductInfo.wiseManInfo = null
            this.clear()
            this.formData.businessMan = this.userInfo.userName
            this.formData.status = 0
            if (!this.isEdit) {
                const { data, success } = await getProductDySampleWiseManBzRefList(this.getProductInfo)
                if (success) {
                    this.wiseManUidList = data
                }
            }
            this.addDialogVisible = true
        },
        async followUpInfo(row, isFirst, isAdd) {
            if (isAdd) {
                this.followInfo.parentId = row.parentId
            } else {
                this.followInfo.parentId = row.id
            }
            this.addFollowUpInfo.parentId = row.id
            if (isFirst) {
                this.followInfo.orderBy = 'createdTime'
                this.dialogTimeRange = this.timeRange
                this.followInfo.startTime = dayjs(this.dialogTimeRange[0]).format('YYYY-MM-DD')
                this.followInfo.endTime = dayjs(this.dialogTimeRange[1]).format('YYYY-MM-DD')
            }
            const { data, success } = await getProductDySampleTrackPageList(this.followInfo)
            if (success) {
                this.followUpTableData = data.list
                this.followUpTotal = data.total
                this.followUpDialogVisible = true
            }
        },
        async init() {
            const { data } = await getAllShopList({ platforms: [6] });
            this.shopList = data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
            const { data: data1, success } = await GetBusinessMan({ keywords: "" });
            if (success && data1 && data1.length > 0) {
                data1.forEach(f => {
                    this.businessManList.push({ value: f.key, label: f.value1 });
                });
            }
            const { data: data3 } = await GetAllWiseManDetailList();
            this.accountCodeList = data3?.map(item => { return { value: item.wiseManAccountCode, label: item.wiseManAccountName + '（' + item.wiseManAccountCode + '）' }; });
        },
        async getList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
            }
            this.init()
            if (this.timeRange == 0) {
                //默认七天
                this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
                this.timeRange = [this.ListInfo.startTime, this.ListInfo.endTime]
            }
            const replaceArr = ['proCode', 'proName', 'expressNo']
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            const { data, success } = await getProductDySamplePageList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.loading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
        },
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        followUpPagechange(val) {
            this.followInfo.currentPage = val;
            this.followUpInfo(this.followInfo, false, true)
        },
        followUpSizechange(val) {
            this.followInfo.currentPage = 1;
            this.followInfo.pageSize = val;
            this.followUpInfo(this.followInfo, false, true)
        },
        followUpSortchange({ order, prop }) {
            if (prop) {
                this.followInfo.orderBy = prop
                this.followInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.followUpInfo(this.followInfo, false, true)
            }
        },
           //上传文件
     onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importProductDySampleAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async onModel() {
            window.open("/static/excel/dayreport/寄样商务导入模版.xlsx", "_blank");
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 20px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.dialogTop {
    display: flex;
    margin-bottom: 20px;
    justify-content: end;

}

.selectBox ::v-deep {
    width: 400px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.btnGroup {
    display: flex;
    justify-content: end;
}
</style>
