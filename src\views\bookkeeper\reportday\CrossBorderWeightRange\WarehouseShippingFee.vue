<template>
    <MyContainer>
        <el-tabs v-model="current" style="height: 95%;">
            <el-tab-pane label="佳速达" name="tab1" style="height: 98%;">
                <ShippingShipOut :type="'ShipOut'" name="佳速达" />
            </el-tab-pane>
            <el-tab-pane label="九方" name="tab2" :lazy="true" style="height: 98%;">
                <ShippingTofba :type="'Tofba'" name="九方" />
            </el-tab-pane>
            <el-tab-pane label="亚吉达" name="tab3" :lazy="true" style="height: 98%;">
                <ShippingElt :type="'Elt'" name="亚吉达" />
            </el-tab-pane>
            <el-tab-pane label="赤道" name="tab4" :lazy="true" style="height: 98%;">
                <ShippingSogoodseller :type="'Sogoodseller'" name="赤道" />
            </el-tab-pane>
            <el-tab-pane label="左海" name="tab5" :lazy="true" style="height: 98%;">
                <ShippingLeftOcean :type="'LeftOcean'" name="左海" />
            </el-tab-pane>
            <el-tab-pane label="环世" name="tab6" :lazy="true" style="height: 98%;">
                <ShippingWorldwide  name="环世" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>
<script>
import MyContainer from "@/components/my-container";
import ShippingShipOut from './tab/ShippingShipOut.vue'
import ShippingTofba from './tab/ShippingTofba.vue'
import ShippingElt from './tab/ShippingElt.vue'
import ShippingSogoodseller from './tab/ShippingSogoodseller.vue'
import ShippingLeftOcean from './tab/ShippingLeftOcean.vue'
import ShippingWorldwide from './tab/ShippingWorldwide.vue'

export default {
    components: {
        MyContainer, ShippingShipOut, ShippingTofba, ShippingElt, ShippingSogoodseller,ShippingLeftOcean,ShippingWorldwide
    },
    data() {
        return {
            current: 'tab1'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>