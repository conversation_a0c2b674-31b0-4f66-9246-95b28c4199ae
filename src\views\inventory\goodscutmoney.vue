<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent v-if="buttonshow == true">
        <el-form-item label="创建时间:">
          <!-- //// -->
          <!-- <el-date-picker
                        v-model="value1"
                        type="daterange"
                        datetimerange
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                      </el-date-picker> -->
          <!-- /// -->
          <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"></el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="姓名:">
          <el-input v-model="filter.brandName" placeholder="姓名" style="width: 150px" @keyup.enter.native="onSearch"
            clearable />
        </el-form-item> -->
        <!-- <el-form-item label="创建人:">
                <el-input v-model="filter.people" placeholder="创建人" style="width: 100px" @keyup.enter.native="onSearch" clearable/>
                </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExportDetail">导出</el-button>
        </el-form-item>
        <el-form-item>
          <el-select v-model.trim="filter.titleName" multiple collapse-tags filterable clearable placeholder="岗位" style="width: 210px">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
              :value="item.titleName" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select filterable v-model.trim="filter.deptId" clearable multiple collapse-tags placeholder="架构"
            style="width: 210px">
            <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model.trim="filter.brandRegion" multiple collapse-tags filterable clearable placeholder="分公司"
            style="width: 165px">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="武汉" value="武汉"></el-option>
            <el-option label="深圳" value="深圳"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select filterable v-model.trim="filter.brandIdList" clearable multiple collapse-tags placeholder="采购" style="width: 200px">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <div style="margin-left: 17%;">
          <el-button type="primary" @click="onSortMethod">排序</el-button>
        </div>
      </el-form>
    </template>

    <!-- <el-row style="margin-top: 30px">
      <el-col :span="3" :offset="1" v-for="item in list" :key="item.brandId">
        <el-card :body-style="{ padding: '20px',marginTop: '10px'}" shadow="always">
          <div class="grid-header">{{item.brandName}}
              <el-tooltip class="item" effect="dark" content="未知" placement="top-end">
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
          </div>
          <div class="grid-text" >
            <span class="canclick" @click="onShowDetailGroup(item.brandId)">{{item.amountPaid}}</span>
          </div>
              </el-card>
      </el-col>
    </el-row> -->

        <el-card >
            <div style="margin:0px; word-break: break-all;" :style="{height: buttonshow == true?'630px':'550px'}">
              <div style="overflow-x: auto; height:100%">
                  <el-table :data="list" v-loading="listLoading" height="100%" style="width: 100%" :header-cell-style="{ 'background-color': '#f5f7fa', 'color': '#909399', 'font-weight': 'bold' }">
                    <!-- <el-table-column prop="firstTime" label="最早时间" width="180"></el-table-column> -->
                    <el-table-column align="center" sortable prop="brandName" label="采购组" width="150" fixed="left">
                      <template slot-scope="scope" >
                        <el-link type="primary" @click="onShowDetailGroup(scope.row['brandId'])">{{scope.row['brandName']}}</el-link>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" sortable prop="dDeptName" label="岗位" width="140" fixed="left"></el-table-column>
                    <el-table-column align="center" prop="purDept" label="架构" width="90" fixed="left"></el-table-column>
                    <el-table-column align="left" prop="goodsCodeCount" sortable label="商品编码数" width="110"></el-table-column>
                    <el-table-column align="left" prop="purchaseCodeNumber" sortable label="进货编码数" width="110"></el-table-column>
                    <el-table-column align="left" prop="styleCodeCount" sortable label="款式编码数" width="110"></el-table-column>
                    <el-table-column align="left" prop="purOrderCount" sortable label="开单数量" width="110">
                      <template slot-scope="scope">
                        <el-link type="primary" @click="viewTrendChart(scope.row)">{{ scope.row['purOrderCount'] }}</el-link>
                      </template>
                    </el-table-column>
                    <el-table-column align="left" prop="amountPaid" sortable label="扣款金额" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column align="left" prop="amSort" sortable label="扣款排名" width="100">
                      <template slot-scope="scope" >
                        {{scope.row.amSort == 0 ? ' ' : scope.row.amSort}}
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit3'])" align="left" sortable prop="txProfit3" label="淘系毛三" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit3'])" align="left" sortable prop="pinProfit3" label="拼系毛三" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit3'])" align="left" sortable prop="douYinProfit3" label="抖音毛三" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit3'])" align="left" sortable prop="otherProfit3" label="其他毛三" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit3'])" align="left" sortable prop="allProfit3" label="毛三汇总" width="100" :formatter="formatNumber"></el-table-column>

                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit4'])" align="left" sortable prop="txProfit4" label="淘系毛四" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit4'])" align="left" sortable prop="pinProfit4" label="拼系毛四" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit4'])" align="left" sortable prop="douYinProfit4" label="抖音毛四" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit4'])" align="left" sortable prop="otherProfit4" label="其他毛四" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit4'])" align="left" sortable prop="allProfit4" label="毛四汇总" width="100" :formatter="formatNumber"></el-table-column>

                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit6'])" align="left" sortable prop="txProfit6" label="淘系毛六" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit6'])" align="left" sortable prop="pinProfit6" label="拼系毛六" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit6'])" align="left" sortable prop="douYinProfit6" label="抖音毛六" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit6'])" align="left" sortable prop="otherProfit6" label="其他毛六" width="100" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:Profit6'])" align="left" sortable prop="allProfit6" label="毛六汇总" width="100" :formatter="formatNumber"></el-table-column>

                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:SaleAmount'])" align="left" sortable prop="txSaleAmount" label="淘系销售额" width="110" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:SaleAmount'])" align="left" sortable prop="pinSaleAmount" label="拼系销售额" width="110" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:SaleAmount'])" align="left" sortable prop="douYinSaleAmount" label="抖音销售额" width="110" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:SaleAmount'])" align="left" sortable prop="otherSaleAmount" label="其他销售额" width="110" :formatter="formatNumber"></el-table-column>
                    <el-table-column v-if="checkPermission(['OrderIllegalPurchase:SaleAmount'])" align="left" sortable prop="allSaleAmount" label="销售额汇总" width="110" :formatter="formatNumber"></el-table-column>
                  </el-table>
              </div>
            </div>
        </el-card>


    <!-- //弹窗 -->
    <el-dialog :visible.sync="dialodayssisVisible" ref="elDialog"  width="75%" v-dialogDrag :fullscreen="isfull" :lock-scroll='false' :center="true" :show-close="false">
        <div class="updatediv" style="height: 500px;">

        <goodscutmoneydetail ref="goodscutmoneydetail" :filter="filter"></goodscutmoneydetail>

        </div>
    </el-dialog>

    <el-dialog title='设置排序' :visible.sync="sortVisible" width="40%" v-dialogDrag style="margin-top:-10vh">
      <div slot="title">
        <span style="font-size: 20px;">设置排序</span>
      </div>
      <div>
        <goodsSetSort ref="goodsSetSort" :optionsListInfo="optionsListInfo" @sortClose="onStorageMethod" v-if="sortVisible" />
      </div>
      <div style="display: flex; justify-content: center; gap: 20px;">
        <el-button @click="sortVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSortSave">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 趋势图 -->
    <el-dialog title="" :visible.sync="purOrderTrendChart.visible" width="75%" v-dialogDrag :center="true">
      <div style="height: 40px; margin-top: 10px; margin-bottom: 15px; display: flex; align-items: center;">
        <span style="margin-right:0.4%;">
          <el-date-picker style="width: 250px" v-model="ruleForm.timerange" type="daterange" :clearable="false"
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'"
            :end-placeholder="'结束时间'" @change="handleCheckedCitiesChange($event, 1)"></el-date-picker>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.brandId" filterable clearable placeholder="采购" style="width: 90px"
            @change="handleCheckedCitiesChange($event, 2)">
            <el-option label="公司" :value="-1" />
            <el-option label="筛选" :value="-2" />
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.company" multiple collapse-tags filterable clearable placeholder="地区"
            style="width: 165px" @change="handleCheckedCitiesChange($event, 3)" :disabled="ruleForm.brandId == -1 || ruleForm.brandId == -2">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="武汉" value="武汉"></el-option>
            <el-option label="深圳" value="深圳"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.dDeptName" multiple collapse-tags filterable clearable placeholder="岗位" style="width: 165px"
            @change="handleCheckedCitiesChange($event, 4)" :disabled="ruleForm.brandId == -1 || ruleForm.brandId == -2">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName" :value="item.titleName" />
          </el-select>
        </span>
        对比
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.brandId2" filterable clearable placeholder="采购" style="width: 90px"
            @change="handleCheckedCitiesChange($event, 5)">
            <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>
        <span style="margin-right:0.4%;">
          <el-select v-model.trim="ruleForm.company2" multiple collapse-tags filterable clearable placeholder="地区"
            style="width: 165px" @change="handleCheckedCitiesChange($event, 6)">
            <el-option label="义乌" value="义乌"></el-option>
            <el-option label="南昌" value="南昌"></el-option>
            <el-option label="武汉" value="武汉"></el-option>
            <el-option label="深圳" value="深圳"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </span>
        <span style="margin-right:20px;">
          <el-select v-model.trim="ruleForm.dDeptName2" multiple collapse-tags filterable clearable placeholder="岗位" style="width: 165px"
            @change="handleCheckedCitiesChange($event, 7)">
            <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
              :value="item.titleName" />
          </el-select>
        </span>
        <span style="margin-right:20px;">
          <el-radio v-model="ruleForm.monthOrDay" label="日" @input="handleCheckedCitiesChange($event, 9)">日</el-radio>
          <el-radio v-model="ruleForm.monthOrDay" label="月" @input="handleCheckedCitiesChange($event, 9)">月</el-radio>
        </span>
      </div>
      <div style="height: 550px;">
        <buschar v-if="purOrderTrendChart.visible" ref="detailtrendchartref" :analysisData="purOrderTrendChart.data"></buschar>
      </div>
    </el-dialog>

  </my-container>
</template>

<script>
  import { formatTime } from "@/utils";
  import dayjs from "dayjs";
  import formCreate from '@form-create/element-ui'
  import FcEditor from "@form-create/component-wangeditor";
  import MyContainer from '@/components/my-container'
  import MyConfirmButton from '@/components/my-confirm-button'
  import tableBtn from "@/views/inventory/components/tablebtn.vue";
  import cesTable from "@/components/Table/table.vue";
  import buschar from '@/components/Bus/buschar'
  // import cesTable from "@/components/Table/tablebtn.vue";
  import {
    upLoadFile,
    upLoadImage
  } from '@/api/upload/file'
  // import { addLogUpdate, getUpdateLog, getUpdate, deleteLogUpdate} from '@/api/admin/opration-log'
  import {
    addPurchaseLog,
    getUpdate,
    deletePurchaseLog,
    getPurchaseLogModuleAsync,
    updateSortPurchaseLogOneAsync,
    getPurchaseLogListAsync,
    getPurchaseLogOneAsync
  } from '@/api/admin/opration-log'
  import {
    getOrderIllegalPurchase,
    getOrderIllegalPurchaseDetailAsync,
    exportOrderIllegalPurchase,
    getPurOrderCountTrendChart
  } from '@/api/order/orderdeductmoney'
  import goodscutmoneydetail from './goodscutmoneydetail.vue';
  import { getBianManPositionListV2 } from '@/api/inventory/warehouse'
  import { getPurchaseNewPlanTurnDayDeptList ,getPurchaseNewPlanTurnDayBrandList } from '@/api/inventory/purchaseordernew'
  import goodsSetSort from "./goodsSetSort.vue";

  const tableCols = [{
      istrue: true,
      prop: 'proCode',
      label: '宝贝ID',
      width: '100'
    },{
      istrue: true,
      prop: 'occurrenceTime',
      label: '日期',
      width: '100'
    },{
      istrue: true,
      prop: 'platformName',
      label: '平台',
      width: '100'
    },{
      istrue: true,
      prop: 'shopName',
      label: '店铺',
      width: '100'
    },
    // {
    //   istrue: true,
    //   prop: 'brandName',
    //   label: '姓名',
    //   width: '100'
    // },
    // {istrue:false, prop:'content',label:'更新内容', type:'editor',width:'400'},
    {
      istrue: true,
      prop: 'amountPaid',
      label: '扣款金额',
      width: 'auto'
    },
    // {
    //   istrue: true,
    //   prop: 'brandId',
    //   label: '采购id',
    //   width: '100'
    // },
    // {istrue:true,prop:'createdTime',label:'创建时间', width:'100',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD')},
  ];
  // const tableHandles1=[
  //          {label:"新增记录", handle:(that)=>that.onHand(1)},
  //          // {label:'编辑记录', handle:(that)=>that.onHand(3)},
  //          // {label:'查看', handle:(that)=>that.onHand(2)},
  //       ];
  const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");

  export default {
    name: 'YunhanAdminUpdatelog',
    components: {
      tableBtn,
      cesTable,
      MyContainer,
      MyConfirmButton,
      goodsSetSort,
      goodscutmoneydetail,
      buschar
    },
    data() {
      return {
        brandlist: [],//采购
        optionsListInfo: {},
        sortVisible: false,
        positionList: [],//岗位
        purchasegrouplist: [],//架构
        summaryarry:{},
        isfull: false,
        koulist: [],
        dialodayssisVisible: false,
        buttonshow: false,
        isSelectColumn: false,
        that: this,
        filter: {
          brandName: '',
          people: '',
          timerange: [null, null],
          startDate: null,
          endDate: null,
          brandRegion: [],
          brandIdList: [],
          deptId: [],
          titleName: [],
        },
        list: [],
        pager: {
          OrderBy: "id",
          IsAsc: false
        },
        tableCols: tableCols,
        // tableHandles:tableHandles1,
        uploadfilelist: [],
        fileList: [],
        uploadimagelist: [],
        imageList: [],
        handtype: 1,
        formtitle: null,
        autoform: {
          fApi: {},
          options: {
            submitBtn: false,
            global: {
              '*': {
                props: {
                  disabled: false
                },
                col: {
                  span: 16
                }
              },
              upload: {
                props: {
                  onError: function(r) {
                    alert('上传失败')
                  }
                }
              }
            }
          },
          rule: []
        },
        total: 0,
        sels: [],
        listLoading: false,
        pageLoading: false,
        handVisible: false,
        handLoading: false,
        reportsingle: {
          processList: []
        },
        collapseactiveName: '1',
        dialogVisiblePurOrderTrendChart: false,
        purOrderTrendChart: {
          visible: false,
          title: '',
          data: {}
        },
        ruleForm: {
          timerange: [null, null],
          startDate: null,
          endDate: null,
          monthOrDay: "日"
        }
      };
    },
    props: {
      whichmodules: {
        type: String,
        default: true
      },
      clickoutin: {
        type: Boolean,
        default: false,
      }
    },
    computed: {
      clickshow() {
        return this.clickoutin;
      },
      // module() {
      //   return this.whichmodules;
      // }
    },
    watch: {
      clickshow(data) {
        console.log("模块显示", this.whichmodules);
        console.log("监听展开", data)
        this.isSelectColumn = data;
        this.buttonshow = data;

        var array = this.tableCols;
        console.log("原数组", array);

        let newItem = {
          istrue: true,
          prop: 'title',
          label: '标题',
          width: '570'
        };
        let newItemm = {
          istrue: true,
          prop: 'title',
          label: '标题',
          width: 'auto'
        };
        // let content = {istrue:true, prop:'content',label:'更新内容', type:'editor',width:'400'};
        // let contentt = {istrue:false, prop:'content',label:'更新内容', type:'editor',width:'400'};

        if (data == true) {
          var list = array.map(t => {
            return t.label === newItemm.label ? newItemm : t;
          });
          this.tableCols = list;
          console.log("改变后数组", list);
        } else {
          var list = array.map(t => {
            return t.label === newItemm.label ? newItem : t;
          });
          this.tableCols = list;
          console.log("改变后数组", list);
        }
      },
      // module(data){
      //   return
      // }
    },
    async mounted() {
      this.defaultDate()
      await this.init();
      formCreate.component('editor', FcEditor);
      await this.onSearch()
    },
    methods: {
      onStorageMethod(){
        this.sortVisible = false;
        this.getlist();
      },
      onSortSave(){
        this.$nextTick(()=>{
          this.$refs.goodsSetSort.onSave()
        })
      },
      onSortMethod(){
        let postList = [];//岗位
        let placeList = [];//城市
        let architectureList = [];//架构
        // 处理 dDeptName 的值和 purDept 字段，去重
        this.list.forEach(item => {
          if (item.dDeptName) {
            const [place, post] = item.dDeptName.split('-');
            if (place) placeList.push(place); // 添加城市部分
            if (post) postList.push(post); // 添加职位部分
          }
          if (item.purDept) {
            architectureList.push(item.purDept);// 添加架构
          }
        });
        placeList = [...new Set(placeList)];
        postList = [...new Set(postList)];
        architectureList = [...new Set(architectureList)];
        this.optionsListInfo = {
          postList,
          placeList,
          architectureList
        };
        this.sortVisible = true;
      },
      formatNumber(row, column, cellValue, index) {
        // 转换为整数
        let intValue = Math.floor(cellValue);
        //为负数会向下取一位，所以需要加1
        if (cellValue < 0 && cellValue !== intValue) {
          intValue += 1;
        }
        // 添加千位符
        return intValue.toLocaleString('en-US');
      },
      //获取选择器数据
      async init() {
        //架构
        let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
        if (success) {
          this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
        }
        //岗位
        var resPosition = await getBianManPositionListV2();
        this.positionList = resPosition?.data;
        //采购
        var { data } = await getPurchaseNewPlanTurnDayBrandList();
        this.brandlist = data.map(item => { return { value: item.id, label: item.brandName }; });
      },
      defaultDate(){
        let date = new Date()
        let year = date.getFullYear().toString()   //'2019'
        let month = date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1).toString():(date.getMonth()+1).toString()  //'04'
        let da = date.getDate() < 10 ? '0'+date.getDate().toString():date.getDate().toString()  //'12'
        let end = year + '-' + month + '-' + da  //当天'2019-04-12'
        let beg = year + '-' + month + '-01'    //当月第一天'2019-04-01'
        this.filter.timerange = [beg,end]
    },
      getCondition(){
        if (this.filter.timerange&&this.filter.timerange.length>1) {
          this.filter.startDate = this.filter.timerange[0];
          this.filter.endDate = this.filter.timerange[1];
        }
        else {
          this.$message({message:"请先选择日期",type:"warning"});
          return false;
        }
        //this.filter.platform=2
        //var pager = this.$refs.pager.getPager();
        var page  = this.pager;
        const params = {
          //...pager,
          ...page,
          ... this.filter
        }
        if (params.brandRegion && params.brandRegion.length > 0) {
          params.brandRegion = params.brandRegion.join(',');
        }
        if (params.titleName && params.titleName.length > 0) {
          params.titleName = params.titleName.join(',');
        }

        return params;
      },
      async onShowDetailGroup(val) {
        console.log('value',val)
          if(val || val >= 0){
            this.brandId= val;
          }
        this.filter.brandId = this.brandId

        this.dialodayssisVisible = true

        this.$nextTick(async () =>{
            await this.$refs.goodscutmoneydetail.onSearch()
        })
      },
      viewTrendChart(row) {

        this.ruleForm.brandId = null;
        this.ruleForm.company = [];
        this.ruleForm.dDeptName = [];

        this.ruleForm.brandId2 = null;
        this.ruleForm.company2 = [];
        this.ruleForm.dDeptName2 = [];

        var timerange = this.filter.timerange;
        this.ruleForm.timerange = timerange;
        this.ruleForm.startDate = timerange[0];
        this.ruleForm.endDate = timerange[1];
        if(row.brandId && row.brandId < 99999)
          this.ruleForm.brandId = row.brandId;
        else if (this.filter.titleName?.length > 0 || this.filter.deptId?.length > 0 || this.filter.brandRegion?.length > 0)
          this.ruleForm.brandId = -2;
        else
          this.ruleForm.brandId = -1;

        if(row.brandId && row.brandId < 99999) {
          this.ruleForm.company.push(row.company);
          this.ruleForm.dDeptName.push(row.title);
        }

        var params = { ...this.ruleForm };

        if (this.ruleForm.brandId == -2) {
          params.company = this.filter.brandRegion;
          params.dDeptName = this.filter.deptId;
        } 

        this.getTrendChart(params);
      },
      async getTrendChart(params){
        var res = await getPurOrderCountTrendChart(params);

        this.purOrderTrendChart.visible = true,
        this.purOrderTrendChart.data = res.data;
        this.purOrderTrendChart.title = res.data.legend[0];

        this.$nextTick(() => {
          if (this.$refs.detailtrendchartref) {
            this.$refs.detailtrendchartref.initcharts();
          }
        });
      },
      handleCheckedCitiesChange(data,val){

        this.ruleForm.startDate = this.ruleForm.timerange[0];
        this.ruleForm.endDate = this.ruleForm.timerange[1];

        var params = { ...this.ruleForm };

        if (this.ruleForm.brandId == -1){
          this.ruleForm.company = [];
          this.ruleForm.dDeptName = [];
          params.company = [];
          params.dDeptName = [];
        }else if (this.ruleForm.brandId == -2){
          params.company = this.filter.brandRegion;
          params.dDeptName = this.filter.deptId;
        }

        this.getTrendChart(params);
      },
      async onShowDetailGrouplist(){
          this.listLoading = true

          if (this.filter.timerange&&this.filter.timerange.length>1) {
            this.filter.startDate = this.filter.timerange[0];
            this.filter.endDate = this.filter.timerange[1];
          }
          else {
            this.$message({message:"请先选择日期",type:"warning"});
            return false;
          }
          //this.filter.platform=2
          var pager = this.$refs.pager.getPager();
          var page  = this.pager;
          const params = {
            ...pager,
            ...page,
            ... this.filter
          }

          if(params===false){
                  return;
          }
          params.brandId = this.brandId;
          // params.brandId = this.brandId;
          // console.log("dianji看板最终参数",params);

          const res = await getOrderIllegalPurchaseDetailAsync(params)
          this.listLoading = false;

          if (!res?.code) {
            return
          }
          this.total = res.data.total
          const data = res.data.list
          //////////
          this.summaryarry=res.data.summary;
          if(this.summaryarry)
              this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));


          data.forEach(d => {
            d._loading = false
          })
          this.koulist = data
          this.dialodayssisVisible= true;
      },
      async initform() {
        let that = this
        this.autoform.rule = [{
            type: 'hidden',
            field: 'id',
            title: 'id',
            value: ''
          },
          {
            type: 'input',
            field: 'title',
            title: '标题',
            value: '',
            col: {
              span: 24
            },
            validate: [{
              type: 'string',
              required: true,
              message: '请输入标题'
            }]
          },
          {
            type: 'editor',
            field: 'content',
            title: '更新内容',
            value: '',
            col: {
              span: 24
            },
            validate: [{
              type: 'string',
              required: true,
              message: '必填'
            }],
            props: {
              init: async (editor) => {
                await that.initeditor(editor)
              }
            }
          }
        ]
        this.autoform.rule.forEach(f => {
          if (f.field == 'toUserId1') f.validate = []
          if (f.field == 'toUserId2') f.validate = []
        })
      },
      async onSearch(data) {
        await this.getlist();
      },
      //请求列表数据//
      async getlist() {
        this.listLoading = true

        var params=this.getCondition();
        params.brandId = null;
        if(params===false){
                return;
        }
        // console.log("list看板最终参数",params);
        // const params = {
        //   // module: this.whichmodules,
        //   pageSize: 50,
        //   currentPage: 1,
        //   OrderBy: 'OccurrenceTime',
        //   IsAsc: false,
        //   startDate: this.filter.timerange ? this.filter.timerange[0] : '',
        //   endDate: this.filter.timerange ? this.filter.timerange[1] : '',
        // }
        const res = await getOrderIllegalPurchase(params)
        // const res = await getLogTimeAsync(params)
        this.listLoading = false
        if (!res?.code) {
          return
        }
        this.total = res.data.total
        const data = res.data.list
        data.forEach(d => {
          d._loading = false
        })
        this.list = data
      },
      //删除数据
      async deleteLog(data) {
        console.log("父删除更新信息", data.id);
       const params = {
          id: data.id,
        }
        this.listLoading = true
        const res = await deletePurchaseLog(params);
        // (res?.code)? await this.getlist() : this.$message.warning('权限错误');
        if (res?.code) {
          await this.getlist()
        }
        this.listLoading = false
        console.log('回调参数', res);
      },
      //排序
      async orderTopdown(index, row, updown) {
        if (index == 1 && updown == 1) {
          this.$message.warning('已经移动到最顶端');
        } else if (index == this.list.length && updown == 0) {
          this.$message.warning('已经移动到最末端');
        } else {
          const params = {
            sort: row.sort,
            id: row.id,
            sortid: updown,
            module: this.whichmodules,
          }
          this.listLoading = true
          const res = await updateSortPurchaseLogOneAsync(params)
          console.log("排序回调", res);
          // (res?.code)? await this.getlist() : this.$message.warning('权限错误');
          if (res?.code) {
            await this.getlist()
          }
          this.listLoading = false
        }
      },
      //
      async onHand(arr) {

        if (arr == 1) {
          var type = 1;
        } else {
          var type = arr[0];
          let array = [arr[1]];
          this.selids = [];
          array.forEach(f => {
            this.selids.push(f.id);
          })
        }

        this.handVisible = true
        this.handtype = type
        var res;
        if (type == 1) {
          this.formtitle = '新增更新记录'
          await this.initform()
        } else if (type == 2) {
          var reportid = this.selids[0]
          res = await getPurchaseLogOneAsync({
            id: reportid
          })
          if (res.code == 1) {
            this.reportsingle = res.data
          }
        } else if (type == 3) {
          this.formtitle = '编辑更新记录'
          await this.initform()
          var reportid = this.selids[0]
          console.log("排序", this.selids);
          res = await getPurchaseLogOneAsync({
            id: reportid
         })
           if (res.code == 1) {
            var model = {
              id: reportid,
              title: res.data ? res.data.title : '',
              content: res.data ? res.data.content : ''
           }
             this.$nextTick(async () => {
              var arr = Object.keys(this.autoform.fApi)
              if (arr.length > 0)
                await this.autoform.fApi.resetFields()
              await this.autoform.fApi.setValue(model)
            })
          }
        }

      },
      async initeditor(editor) {
        editor.config.uploadImgMaxSize = 3 * 1024 * 1024
        editor.config.excludeMenus = ['emoticon', 'video']
        // editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        editor.config.uploadImgAccept = []
        // editor.customConfig.debug = true;
        editor.config.customUploadImg = async function(resultFiles, insertImgFn) {
          console.log('resultFiles', resultFiles)
          const form = new FormData();
          form.append("image", resultFiles[0]);
          const res = await upLoadImage(form);
          var url = `${res.data}`
          console.log('url', url)
          insertImgFn(url)
        }
      },
      async onSubmit() {
        const formData = this.autoform.fApi.formData();
        formData.reportId = this.reportsingle.id;
        await this.autoform.fApi.validate(async (valid, fail) => {
          if (valid) {
            this.handLoading = true
            const formData = this.autoform.fApi.formData();
            console.log("提交事件", formData);
            formData.id = formData.id ? formData.id : 0;
            formData.Enabled = true;
            var res;

            if (this.handtype == 1) {
              formData.module = this.whichmodules;
              res = await addPurchaseLog(formData)
              console.log("打印添加返回数据", res);
            } else if (this.handtype == 3) {
              formData.module = this.whichmodules;
              console.log("打印编辑数据", formData);
              res = await addPurchaseLog(formData)
            }
            if (res?.code) {
              await this.getlist()
              this.handVisible = false
              this.handLoading = false
            }
          } else {}
        })
      },
      //导出
      async onExportDetail(){
            var params=this.getCondition();
            params.brandId = null;
            if(params===false){
                    return;
            }
            var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
            var res= await exportOrderIllegalPurchase(params);
            loadingInstance.close();
            if(!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download','采购扣款统计_' + new Date().toLocaleString() + '.xlsx' )
            aLink.click();
        },
      selectchange: function(rows, row) {
        // console.log('打印选择',rows);
        this.selids = [];
        rows.forEach(f => {
          this.selids.push(f.id);
        })
      },
    },
  };
</script>

<style lang="scss" scoped>
.canclick{
  color:#409EFF;
  cursor: pointer;
}
.el-table__header {
  position: sticky;
  top: 0;
  background-color: #f5f7fa;
  z-index: 1;
}
//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 80px;
}
.ad-form-query {
  display: flex;
}
</style>
