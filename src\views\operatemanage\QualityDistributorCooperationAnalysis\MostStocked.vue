<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <div>
                    <el-input v-model.trim="ListInfo.distributor" :clearable="true" maxlength="100" placeholder="分销商"
                        class="publicCss" />
                    <el-select filterable v-model="ListInfo.distributor_PlatFormList" placeholder="分销商销售渠道" multiple
                        collapse-tags clearable class="publicCss" @change="changeSel">
                        <el-option v-for="item in platformlistDistribution" :key="item.value" :label="item.label"
                            :value="item.label" />
                    </el-select>
                    <el-date-picker style="width: 251px;margin-right: 10px;" v-model="ListInfo.timerange"
                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                        start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions"
                        @change="changeTime" />
                    <el-button type="primary" @click="getList(true)">查询</el-button>
                    <el-button @click="clear">重置</el-button>
                </div>
                <el-button type="primary" @click="onExport" style="float:right">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'MostStocked202408041729'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;margin: 0" v-loading="listLoading" height="100%"
            :showsummary='true' :summaryarry='summaryarry' class="doesn">
            <template slot="right">
                <vxe-column title="分销商销售渠道" width="200" align="left">
                    <template #default="{ row, $index }">
                        <div style="overflow: hidden; text-overflow: ellipsis;white-space: nowrap;">
                            <span style="margin-right: 5px;">{{ row.distributorChannels }}</span>
                        </div>
                    </template>
                </vxe-column>
                <vxe-column title="铺货商品数" align="center" field="goods_shelve_sku_cnt" :sortable="true">
                    <template #default="{ row, $index }">
                        <div style="color: red;font-weight: 700;">{{ row.goods_shelve_sku_cnt }}</div>
                    </template>
                </vxe-column>
                <vxe-column title="分销金额" align="center" field="pay_amount" :sortable="true" >
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.pay_amount, '分销金额') }}</div>
                    </template>
                </vxe-column>
                <vxe-column title="分销订单数" align="center" field="order_cnt" :sortable="true" >
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.order_cnt, '分销订单数') }}</div>
                    </template>
                </vxe-column>
                <vxe-column title="分销件数" align="center" field="sale_quantity" :sortable="true" >
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.sale_quantity, '分销件数') }}</div>
                    </template>
                </vxe-column>
                <vxe-column title="分销售后占比" align="center" field="refund_rate" :sortable="true">
                    <template #default="{ row, $index }">
                        <div>{{ row.refund_rate !== null ? tonumfuc((row.refund_rate * 100).toFixed(2) + '%', '分销售后占比') : '' }}</div>
                    </template>
                </vxe-column>
                <vxe-column title="售后订单数" align="center" field="afterSaleOrderNumber" :sortable="true">
                    <template #default="{ row, $index }">
                        <div >{{ tonumfuc(row.afterSaleOrderNumber, '售后订单数') }}</div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { platformlistDistribution, pickerOptions } from '@/utils/tools'
import { getMostStockedList, exportMostStockedList } from '@/api/operatemanage/distributiongoodsOnline'
import { replaceSpace } from '@/utils/getCols'
import { tonumfuc } from '@/utils/tonumqian.js'
const tableCols = [
    { istrue: true, sortable: 'custom', prop: 'goods_shelve_sku_cnt_rank_desc', label: '排名', width: '60', },
    { istrue: true, sortable: 'custom', prop: 'dowlond_time', label: '时间', width: '120', formatter: (row) => row.dowlond_time ? dayjs(row.dowlond_time).format('YYYY-MM-DD') : '' },
    { istrue: true, sortable: 'custom', prop: 'distributor_co_name', label: '分销商', width: '280', align: 'left' },
]
export default {
    name: 'YunHanAdminIndex',
    components: { MyContainer, vxetablebase },

    data() {
        return {
            that: this,
            tonumfuc,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'goods_shelve_sku_cnt_rank_desc',
                isAsc: true,
                startTime: null,
                endTime: null,
                timerange: [],
                distributor: null,
                distributor_PlatForm: '',
                distributor_PlatFormList: [],
                pay_amountIncrease: null,
            },
            platformlistDistribution,
            tableData: [],
            summaryarry: {},
            pickerOptions,
            tableCols,
            total: 0,
            listLoading: false,
        };
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        changeSel(e) {
            this.ListInfo.distributor_PlatForm = e ? e.join(',') : ''
        },
        changeTime(e) {
            this.ListInfo.startTime = e ? e[0] : null
            this.ListInfo.endTime = e ? e[1] : null
            this.getList()
        },
        clear() {
            this.ListInfo.startTime = null;
            this.ListInfo.endTime = null;
            this.ListInfo.timerange = [];
            this.ListInfo.distributor = null;
            this.ListInfo.distributor_PlatForm = null;
            this.ListInfo.pay_amountIncrease = null;
            this.ListInfo.distributor_PlatFormList = []
        },
        async onExport() {
            this.$message.success('导出中,请稍后')
            var { data } = await exportMostStockedList(this.ListInfo);
            if (!data) return
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '铺货最多' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async getList(isSearch) {
            if (isSearch) {
                this.ListInfo.currentPage = 1
            }
            const replaceArr = ['distributor'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.listLoading = true
            // 使用时将下面的方法替换成自己的接口
            const { data, success } = await getMostStockedList(this.ListInfo)
            if (success) {
                this.tableData = data.list
                this.total = data.total
                this.summaryarry = data.summary;
                this.listLoading = false
            } else {
                //获取列表失败
                this.$message.error('获取列表失败')
            }
            this.$nextTick(() => {
                if (this.ListInfo.startTime == null || this.ListInfo.startTime == this.ListInfo.endTime) {
                    this.$refs.table.$refs.xTable.showColumn(this.$refs.table.$refs.xTable.getColumnByField('dowlond_time'))
                } else {
                    this.$refs.table.$refs.xTable.hideColumn(this.$refs.table.$refs.xTable.getColumnByField('dowlond_time'))
                }
            });
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    margin-bottom: 10px;
    justify-content: space-between;

    .publicCss {
        margin-right: 10px;
        width: 200px;
    }
}

.doesn ::v-deep .vxe-tools--operate {
    display: none !important;
}
</style>
