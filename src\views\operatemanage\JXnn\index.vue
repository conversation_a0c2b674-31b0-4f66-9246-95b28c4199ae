<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsId" v-model="ListInfo.goodsId"
                    placeholder="商品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="codeCallback($event, '1')" title="商品ID"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.skuId" v-model="ListInfo.skuId"
                    placeholder="SKU编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="codeCallback($event, '2')" title="SKU编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
                    :maxlength="1000000" @callback="codeCallback($event, '3')" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <!-- <el-button type="primary" @click="oneClickDeclaration">一键申报</el-button> -->
                </div>
            </div>
        </template>

        <!--  @select="selectCheckBox" -->
        <vxetablebase ref="table" :that='that' id="20241120103042" :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' @cellClick="cellClick"
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
            :height="'100%'">
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="15天销量趋势" :visible.sync="chatProp.chatDialog" width="80%" v-dialogDrag
            v-loading="chatProp.chatLoading">
            <buschar ref="dialogMapVisibleBuschar" v-if="chatProp.chatDialog" :analysisData="chatProp.data">
            </buschar>
        </el-dialog>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import { jdNNSkuStatisticPage } from '@/api/bladegateway/worcestorejk'
import inputYunhan from "@/components/Comm/inputYunhan";
import buschar from "@/components/Bus/buschar";
import { pageGoodsCodeStock } from "@/api/inventory/goodscodestock"
const tableCols = [
    // { type: 'checkbox', label: '', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'skuId', label: 'SKU编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsId', label: '商品ID', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'ysalesVolume', label: '昨日订单销量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'threeDaySalesVolume', label: '过去3天销量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sevenDaySalesVolume', label: '过去7天销量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'sevenDayAvgSalesVolume', label: '7天平均销量', },
    { width: 'auto', align: 'center', prop: 'jdSkuVolume', type: 'echarts', chartProp: 'jdSkuVolume', label: '15天销量趋势', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'jstStock', label: '聚水潭库存', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'yavailableStock', label: '昨日库存', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'surplusSafetyStock', label: '剩余安全库存天数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'predictStock', label: '预计20天库存量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updateTime', label: '数据更新时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan, buschar
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            selectList: [],
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        cellClick(prms) {
            console.log(prms, 'prms');
            if (prms.row.jdSkuVolume == null || !prms.row) return
            if (prms?.column?.field && prms?.column?.field === 'jdSkuVolume') {
                let row = JSON.parse(JSON.stringify(prms.row));
                this.showprchart(row.jdSkuVolume);
            }
        },
        showprchart(jdSkuVolume) {
            this.chatProp.chatDialog = true;
            let params = {
                xAxis: jdSkuVolume.xAxis,
                yAxis: {
                    type: 'value'
                },
                series: [{
                    data: jdSkuVolume.series[0].data,
                    type: 'line',
                }],
            }
            this.chatProp.data = params;
            this.$nextTick(() => {
                this.chatProp.chatLoading = false;
                this.chatProp.chatDialog = true;
            });
        },
        codeCallback(e, val) {
            if (val == '1') {
                this.ListInfo.goodsId = e
            } else if (val == '2') {
                this.ListInfo.skuId = e
            } else if (val == '3') {
                this.ListInfo.goodsCode = e
            }
        },
        async oneClickDeclaration() {
            if (this.selectList.length == 0) return this.$message.warning('还未勾选申报数据，请勾选数据申报!')
            // const goodsCode = this.selectList.map(item => item.goodsCode).join(',')
            // const { data, success } = await pageGoodsCodeStock({
            //     goodsCode,
            //     startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
            //     endDate: dayjs().format('YYYY-MM-DD'),
            //     currentPage: 1,
            //     pageSize: 100000,
            //     isAll: 0,
            //     orderBy: "goodsCode"
            // })
            // if (success && data && data.list && data.list.length > 0) {
            //     console.log(data.list, 'data.list');
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/ApplyStockGoodsForm.vue`,
                title: '批量申报',
                autoTitle: false,
                args: { selRows: this.selectList, isJXnn: true },
                height: 700,
                width: '80%',
            })
            // } else {
            //     this.$message.error('暂无相匹配的数据')
            // }

        },
        selectCheckBox(val) {
            // val.forEach(item => {
            //     item.stockInQtyPromotion = item.predictStock
            //     item.salesDay = item.ysalesVolume
            //     item.salesDay3 = item.threeDaySalesVolume
            //     item.salesDay7 = item.sevenDaySalesVolume
            //     item.salesDay15 = item.salesDay15 ? item.salesDay15 : 2;
            //     item.salesDay30 = item.salesDay30 ? item.salesDay30 : 2;
            // })
            this.selectList = JSON.parse(JSON.stringify(val))
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await jdNNSkuStatisticPage(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                    this.$nextTick(() => {
                        this.$refs.table.loadRowEcharts();
                        this.$forceUpdate()
                    })
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
