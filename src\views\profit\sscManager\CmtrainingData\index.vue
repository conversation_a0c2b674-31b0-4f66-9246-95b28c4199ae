<template>
    <MyContainer>
        <template #header>
        <div class="top">
          <!-- <el-date-picker v-model="ListInfo.calculateMonth" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"  type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker> -->
          <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker>
          <el-select v-model="ListInfo.type" placeholder="类型" class="publicCss" clearable>
            <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
          </el-select>
          <el-select v-model="ListInfo.regionName" placeholder="区域" class="publicCss" clearable>
            <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
          </el-select>

          <el-select v-model="ListInfo.trainer" placeholder="培训人" class="publicCss" clearable>
            <el-option v-for="item in peopledistrictList" :key="item" :label="item" :value="item" />
          </el-select>

          <el-button type="primary" @click="getList('search')">查询</el-button>
          <!-- <el-button type="primary" @click="startImport">导入</el-button> -->
          <el-button type="primary" @click="startImport" v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
          <el-button type="primary" @click="startImport" v-else :disabled="timeCundang">导入</el-button>
          <el-button type="primary" @click="downExcel">模板下载</el-button>
          <el-button type="primary" @click="exportExcel('search')">导出</el-button>
          <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div>

        </div>
      </template>
      <vxe-table
        border
        ref="newtable"
        show-footer
        height="100%"
        :row-config="{height: 40}"
        show-overflow
        :loading="loading"
        :merge-footer-items="mergeFooterItems"
        :footer-data="footerData"
        :span-method="mergeRowMethod"
        :data="tableData">
        <vxe-column field="calculateMonth" title="月份"></vxe-column>
        <vxe-column field="type" title="类型"></vxe-column>
        <vxe-column field="regionName" title="区域" ></vxe-column>
        <vxe-column field="joinPersonCount" title="参训人数"  ></vxe-column>
        <vxe-column field="sessionAndType" title="场次&类型"  ></vxe-column>
        <vxe-column field="trainer" title="培训人" footer-align="center"></vxe-column>
        <!-- <vxe-column field="avgExamScore" title="考试平均分" footer-align="center">
          <template #default="{ row }">
            {{ row.avgExamScore === 100 ? "100" : row.avgExamScore ? Number(row.avgExamScore).toFixed(2) : '' }}
          </template>
        </vxe-column> -->

        <vxe-column field="trainingSatisfaction" title="培训满意度" footer-align="center">
          <template #default="{ row }">
            {{row.trainingSatisfaction}}
            <!-- {{ row.trainingSatisfaction === 100 ? "100%" : row.trainingSatisfaction ? Number(row.trainingSatisfaction).toFixed(2) + "%" : '' }} -->
          </template>
        </vxe-column>
        <!-- <vxe-column field="age2" title="c" footer-align="center"></vxe-column>
        <vxe-column field="rate3" title="组长人数" footer-align="center"></vxe-column>
        <vxe-column field="age4" title="主管人数" footer-align="center"></vxe-column>

        <vxe-column field="rate5" title="经理人数" footer-align="center"></vxe-column>
        <vxe-column field="age6" title="总监" footer-align="center"></vxe-column>
        <vxe-column field="rate7" title="总经理/区负责人" footer-align="center"></vxe-column>
        <vxe-column field="age8" title="总裁" footer-align="center"></vxe-column> -->
        <vxe-column title="操作" footer-align="center">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="mini" :disabled="scope.row.status==1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
              <el-button type="text" size="mini" v-if="checkPermission('ArchiveStatusEditing')" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" size="mini" :disabled="scope.row.status==1" v-else @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" :disabled="scope.row.status == '1'" size="mini" @click="handleDelete(scope.row.id)">删除</el-button>
            </template>
        </vxe-column>
      </vxe-table>

      <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
        <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist" :districtList="districtList" :typeList="typeList"
            @cancellationMethod="dialogVisibleEdit = false" />
      </el-drawer>
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <span>
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
        </el-dialog>

      <!-- <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template> -->
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { pickerOptions } from '@/utils/tools'
  import dayjs from 'dayjs'
  import { downloadLink } from "@/utils/tools.js";
  import { trainingDataPage, trainingDataArchive, trainingDataImport, trainingDataRemove } from '@/api/people/peoplessc.js';
  import departmentEdit from "./departmentEdit.vue";
  import checkPermission from '@/utils/permission'

  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
  ]
  export default {
    name: "scanCodePage",
    components: {
      MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = [
    //   { id: 10001, regionName: '义务',calculateMonth:"2024-12", name: 'Test1', nickname: 'T1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
    //   { id: 10002, regionName: '义务',calculateMonth:"2024-12", name: 'Test2', nickname: 'T2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
    //   { id: 10003, regionName: '南昌',calculateMonth:"2024-12", name: 'Test3', nickname: 'T3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
    //   { id: 10004, regionName: '南昌',calculateMonth:"2024-12", name: 'Test4', nickname: 'T4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
    //   { id: 10005, regionName: '北京',calculateMonth:"2024-12", name: 'Test5', nickname: 'T5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
    //   { id: 10006, regionName: '北京',calculateMonth:"2024-12", name: 'Test6', nickname: 'T6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
    //   { id: 10007, regionName: '深圳',calculateMonth:"2024-12", name: 'Test7', nickname: 'T7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
    //   { id: 10008, regionName: '深圳',calculateMonth:"2024-12", name: 'Test8', nickname: 'T8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
    ]
    const footerData = [
    //   { calculateMonth: '合计', regionName: '云中城小计', role: '33', rate: '56' },
    //   { calculateMonth: '合计', regionName: '武汉小计', role: 'bb', rate: '56' },
    //   { calculateMonth: '合计', regionName: '深圳小计', role: 'bb', rate: '1235' },
    //   { calculateMonth: '合计', regionName: '合计', role: 'bb', rate: '1235' }
    ]
    const mergeFooterItems = [
    //   { row: 0, col: 0, rowspan: 1, colspan: 2 },
    //   { row: 1, col: 0, rowspan: 1, colspan: 2 },
    //   { row: 2, col: 0, rowspan: 1, colspan: 2 }
    ]
      return {
        downloadLink,
        fileList: [],
        dialogVisible: false,
        districtList: [],
        typeList: [],
        peopledistrictList: [],
        timeCundang: '',
        dialogVisibleEdit: false,
        tableData,
        footerData,
        mergeFooterItems,
        somerow: 'calculateMonth,trainingSatisfaction,trainer',
        that: this,
        ListInfo: {
            calculateMonthArr: [dayjs().subtract(0, 'month').format('YYYY-MM'), dayjs().subtract(0, 'month').format('YYYY-MM')]
        //   currentPage: 1,
        //   pageSize: 50,
        //   orderBy: null,
        //   isAsc: false,
        //   startTime: null,//开始时间
        //   endTime: null,//结束时间
        },
        timeRanges: [],
        tableCols,
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
        //上传文件
        onUploadRemove(file, fileList) {
        this.fileList = []
        },
        async onUploadChange(file, fileList) {
        this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
        },
        async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("file", item.file);
        form.append("isArchive", checkPermission("ArchiveStatusEditing"));
        var res = await trainingDataImport(form);
        if (res?.success){
            this.$message({ message: res.msg, type: "success" });
        }
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
        },
        onSubmitUpload() {
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
        this.fileList = []
        this.dialogVisible = true;
        },
        downExcel(){
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250430/1917474621981376513.xlsx', '培训成效分析导入模板.xlsx');
        },
        async saveBane(){
              this.$confirm('是否存档？存档后不可修改！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { data, success } = await trainingDataArchive(this.ListInfo)
                    if(!success){
                        return;
                    }
                    this.getList();
                    this.$message.success('保存存档成功！')

                }).catch(() => {
                    // this.$message.error('取消')
                });


        },
        closeGetlist(){
            this.dialogVisibleEdit = false;
            this.getList()
        },
        exportExcel(){
            this.$refs.newtable.exportData({filename:'培训成效分析',    sheetName: 'Sheet1',type: 'xlsx',useStyle: true })
        },
        handleEdit(index, row){
            this.editInfo = row;
            this.dialogVisibleEdit = true;
        },
        async handleDelete(id) {
        this.$confirm('是否删除！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.loading = true
          const {data, success} = await trainingDataRemove(id)
          this.loading = false
          if (success) {
            this.$message.success('删除成功')
            this.getList();
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {

        });


      },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]

                if (column.property === 'trainingSatisfaction') {
                    if (prevRow && prevRow['regionName'] === row['regionName'] && prevRow['trainer'] === row['trainer']) {
                        return { rowspan: 0, colspan: 0 }
                    } else {
                        let countRowspan = 1
                        while (nextRow && nextRow['regionName'] === row['regionName'] && nextRow['trainer'] === row['trainer']) {
                            nextRow = visibleData[++countRowspan + _rowIndex]
                        }
                        if (countRowspan > 1) {
                            return { rowspan: countRowspan, colspan: 1 }
                        }
                    }
                }else if (column.property === 'trainer') {
                    if (prevRow && prevRow['regionName'] === row['regionName'] && prevRow['trainer'] === row['trainer']) {
                        return { rowspan: 0, colspan: 0 }
                    } else {
                        let countRowspan = 1
                        while (nextRow && nextRow['regionName'] === row['regionName'] && nextRow['trainer'] === row['trainer']) {
                            nextRow = visibleData[++countRowspan + _rowIndex]
                        }
                        if (countRowspan > 1) {
                            return { rowspan: countRowspan, colspan: 1 }
                        }
                    }
                } else if (column.property === 'calculateMonth') {
                    if (prevRow && prevRow['calculateMonth'] === cellValue) {
                        return { rowspan: 0, colspan: 0 }
                    } else {
                        let countRowspan = 1
                        while (nextRow && nextRow['calculateMonth'] === cellValue) {
                            nextRow = visibleData[++countRowspan + _rowIndex]
                        }
                        if (countRowspan > 1) {
                            return { rowspan: countRowspan, colspan: 1 }
                        }
                    }
                }
            }
        },
      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      //导出数据,使用时将下面的方法替换成自己的接口
      // async exportProps() {
      //     const { data } = await exportStatData(this.ListInfo)
      //     const aLink = document.createElement("a");
      //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      //     aLink.href = URL.createObjectURL(blob)
      //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      //     aLink.click()
      // },
      async getList(type) {
        // if (type == 'search') {
        //   this.ListInfo.currentPage = 1
        //   this.$refs.pager.setPage(1)
        // }
        // if (this.timeRanges && this.timeRanges.length == 0) {
        //   //默认给近7天时间
        //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
        // }
        if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
            this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
            this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
        }
        this.loading = true
        const { data, success } = await trainingDataPage(this.ListInfo)
        if (success) {
          data.list.map((row)=>{
            row.trainingSatisfaction =  row.trainingSatisfaction === 100 ? "100%" : row.trainingSatisfaction ? Number(row.trainingSatisfaction).toFixed(2) + "%" : ''
          })
          this.tableData = data.list
          const newTypes = this.tableData.map(item => item.type).filter(district => district !== undefined && district !== null)
          this.typeList = Array.from(new Set([...this.typeList, ...newTypes]));
          this.total = data.total
          data.summary.regionName = '合计';
          data.summary.calculateMonth = data.list.length>0? data.list[0].calculateMonth : '';
        //   this.footerData = [data.summary]
        this.footerData = data.summary
        this.footerData.forEach(item => {
          item.avgExamScore = item.avgExamScore === 100 ? "100" : item.avgExamScore ? Number(item.avgExamScore).toFixed(2) : "0";
          item.trainingSatisfaction = item.trainingSatisfaction === 100 ? "100%" : `${item.trainingSatisfaction ? Number(item.trainingSatisfaction).toFixed(2) : 0}%`;
        });
        const fieldsToFormat = [
            "joinPersonCount",
          ];
          this.footerData.forEach((item) => {
            fieldsToFormat.forEach((field) => {
              if (item[field] !== null && item[field] !== undefined) {
                item[field] = this.formatNumberWithThousandSeparator(item[field]);
              }
            });
          });
          this.footerData.forEach((item) => {
            if (item.avgExamScore !== null && item.avgExamScore !== undefined) {
              item.avgExamScore = Math.round(Number(item.avgExamScore)); // 四舍五入为整数
            }
          });
        // this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
        if (data.summary && data.summary[0]) {
          this.timeCundang = data.summary[0].archiveTime
        }



          //取列表中的区域
        const newDistricts = this.tableData.map(item => item.regionName).filter(district => district !== undefined && district !== null)
        this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));

         //取列表中的区域
         const newDistrictsa = this.tableData.map(item => item.trainer).filter(district => district !== undefined && district !== null)
        this.peopledistrictList = Array.from(new Set([...this.peopledistrictList, ...newDistrictsa]));


          this.loading = false
        } else {
            this.loading = false
          this.$message.error('获取列表失败')
        }
      },
      formatNumberWithThousandSeparator(value){
        if (value === null || value === undefined) return value;
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }
  :deep(.vxe-header--column){
    background: #00937e;
    color: white;
    font-weight: 600;
}
:deep(.vxe-footer--row){
    background: #00937e;
    color: white;
    font-weight: 600;
}
  </style>
