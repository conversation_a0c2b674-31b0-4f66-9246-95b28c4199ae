<template>
    <MyContainer v-loading="pageLoading">
        <template #header>
        </template>
        <vxetablebase :id="'HotSaleBrandPushNewChoose1202408041714'" ref="table" :that='that' :isIndex='true'
            :hasexpand='true' :tablefixed='true' :border="true" @sortchange='sortchange' @select='selectchange'
            :tableData='tableData' :tableCols='tableCols' :showsummary='true' :summaryarry='summaryarry'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="listLoading"
            :height="'100%'">
        </vxetablebase>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import datepicker from '@/views/customerservice/datepicker';
import { pickerOptions } from '@/utils/tools';
import dayjs from 'dayjs';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import {
    GetHotSaleBrandPushNewChoose2Dtl1PageList
} from '@/api/operatemanage/productalllink/alllink'
const tableCols = [
    { sortable: 'custom', width: '120', align: 'center', prop: 'createdGoodsCategory', label: '产品类目', },
    { sortable: 'custom', width: '200', align: 'center', prop: 'createdCategoryLeve1', label: '一级类目', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdGoodsCompeteId2', label: '竞品ID', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createdUserName', label: '推荐人', },
    { sortable: 'custom', width: '150', align: 'center', prop: 'createFirstTime', label: '推新时间', },
    { width: '150', align: 'center', prop: 'chooseUserName', label: '选品人', },
];
export default {
    name: "HotSaleBrandPushNewChoose4",
    components: {
        MyContainer, datepicker, vxetablebase
    },
    props: ['myCreatedGoodsCategory', 'myCreatedCategoryLeve1', 'myTimerange', 'myChoose4Type'],
    data() {
        return {
            auditVisible: false,
            activities: [],
            timeRanges: [],
            that: this,
            pickerOptions,
            filter: {
                timerange: [],
            },
            pager: { OrderBy: "createFirstTime", IsAsc: false },
            tableCols,
            tableData: [],
            total: 0,
            listLoading: false,
            pageLoading: false,
            sels: [],
            selids: [],
            summaryarry: {},
        }
    },
    async mounted() {
        this.onSearch();
    },
    computed: {
    },
    methods: {
        async onSearch() {
            this.filter.createdGoodsCategory = this.myCreatedGoodsCategory;
            this.filter.createdCategoryLeve1 = this.myCreatedCategoryLeve1;
            this.filter.timerange = this.myTimerange;
            console.log(this.filter);
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getParam() {
            //推品日期
            if (this.filter.timerange && this.filter.timerange.length == 2 && this.myChoose4Type == 1) {
                this.filter.createdStartDate = this.filter.timerange[0];
                this.filter.createdEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.createdStartDate = null;
                this.filter.createdEndDate = null;
            }
            //选品日期
            if (this.filter.timerange && this.filter.timerange.length == 2 && this.myChoose4Type == 2) {
                this.filter.chooseStartDate = this.filter.timerange[0];
                this.filter.chooseEndDate = this.filter.timerange[1];
            }
            else {
                this.filter.chooseStartDate = null;
                this.filter.chooseEndDate = null;
            }

            let pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            params.selectType = this.myChoose4Type;
            return params;
        },
        async getList() {
            let param = this.getParam();
            this.listLoading = true
            const res = await GetHotSaleBrandPushNewChoose2Dtl1PageList(param)
            this.listLoading = false
            console.log(res);
            if (res?.success) {
                this.tableData = res.data.list;
                this.total = res.data.total;
                this.summaryarry = res.data.summary;
            } else {
                this.$message.error('获取列表失败')
            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.sels = rows;
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.itemBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
}

::v-deep .el-form-item {
    display: flex;
    align-items: center;
}

::v-deep .el-form-item__content {
    margin: 0 !important;
    width: 100%;
}

.iptCss {
    width: 200px;
}

.el-icon-right {
    font-size: 26px;
    font-weight: 700;
    cursor: pointer;
}

.right {
    color: #409EFF;
    float: right;
    font-size: 30px;
    font-weight: 700;
}
</style>
