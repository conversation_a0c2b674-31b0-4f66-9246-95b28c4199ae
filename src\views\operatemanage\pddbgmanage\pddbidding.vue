<template>
    <div class="allbody">
        <div class="heard">
            <!-- <span class="marleft">商品类目</span> -->
            <!-- <el-select filterable v-model="typeindex" placeholder="商品类目" clearable style="width: 130px" @change="gettypetail()">
                    <el-option v-for="(item,index) in typeList" :key="item.type" :label="item.type" :value="index"/>
                </el-select> -->


            <div style="padding-top: 5px;padding-bottom: 5px;">
                <el-select filterable v-model="filter.typeSelList1" placeholder="商品类目1" clearable style="width: 220px"
                    collapse-tags multiple @change="gettypetail1">
                    <el-option v-for="(item, index) in typeList1" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.typeSelList2" placeholder="商品类目2" clearable style="width: 220px"
                    collapse-tags multiple @change="gettypetail2">
                    <el-option v-for="(item, index) in typeList2" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.typeSelList3" placeholder="商品类目3" clearable style="width: 220px"
                    collapse-tags multiple>
                    <el-option v-for="(item, index) in typeList3" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>


                <!-- <span class="marleft">商品名称</span> -->
                <el-input v-model.trim="filter.goodsName" clearable placeholder="商品名称" maxlength="200"
                    style="width:160px;" />
                <el-checkbox v-model="namechecked" @change="isGoodsName()">是否全名</el-checkbox>
                <!-- <span class="marleft">昨日销量区间</span> -->
                <el-select filterable v-model="filter.yestodaySaleRange" placeholder="昨日销量区间" clearable
                    style="width: 130px">
                    <el-option v-for="item in yestodaySaleList" :key="item" :label="item" :value="item" />
                </el-select>
                <!-- <span class="marleft">竞价状态</span> -->
                <el-select multiple collapse-tags filterable v-model="seljjstatus" placeholder="竞价状态" clearable
                    style="width: 100px">
                    <el-option v-for="item in jjstatusList" :key="item" :label="item" :value="item" />
                </el-select>
                <!-- <span class="marleft">持续</span> -->
                <el-select filterable v-model="filter.fbDay" placeholder="持续" clearable style="width: 80px">
                    <el-option v-for="item in dayList" :key="item" :label="item + '天'" :value="item" />
                </el-select>
                <el-select filterable v-model="filter.saleQS" placeholder="趋势" clearable style="width: 80px">
                    <el-option v-for="item in saleQSList" :key="item" :label="item" :value="item" />
                </el-select>
                <el-select filterable v-model="filter.sku" placeholder="Sku申请状态" clearable style="width: 100px">
                    <el-option label="全部" value="" />
                    <el-option label="正在申请" value="1" />
                    <el-option label="申请完成" value="2" />
                </el-select>
                <el-select filterable v-model="filter.allDataState" placeholder="选品状态" clearable style="width: 100px">
                    <el-option label="已选品" :value="0" />
                    <el-option label="未选品" :value="-1" />
                </el-select>
                <el-button type="primary" style="margin-left: 10px;" @click="onSearch">查询</el-button>
                <el-button type="primary" style="margin-left: 10px;" @click="dialogVisibleData = true">导入拼多多竞品数据</el-button>
            </div>
            <div style="padding-bottom: 20px;">
                <el-select filterable v-model="filter.typeNoSelList1" placeholder="排除商品类目1" clearable style="width: 220px"
                    collapse-tags multiple @change="gettypetailNo1">
                    <el-option v-for="(item, index) in typeNoList1" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.typeNoSelList2" placeholder="排除商品类目2" clearable style="width: 220px"
                    collapse-tags multiple @change="gettypetailNo2">
                    <el-option v-for="(item, index) in typeNoList2" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-select filterable v-model="filter.typeNoSelList3" placeholder="排除商品类目3" clearable style="width: 220px"
                    collapse-tags multiple>
                    <el-option v-for="(item, index) in typeNoList3" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </div>
        </div>
        <div>
            <my-container v-loading="pageLoading">
                <div class="content flexstart" style="justify-content: start; margin-bottom: auto;">
                    <!-- <el-row> -->
                    <div class="neicontent flexstart">
                        <div v-for="i in competitorPDDlist" :key="i.id" style="width: 560px;">
                            <div class="grid-content bg-purple-dark">
                                <div class="marginzero flexrow">
                                    <img :src="i.imageUrl" alt="" style="width: 90px; height: 90px; margin-left: 10px;">
                                    <div style="margin-left: 10px; width: 400px;" class="flexcloumn">
                                        <el-tooltip class="item" effect="dark" :content="i.goodsName" placement="top">
                                            <div class="textellipsis">{{ i.goodsName }}</div>
                                        </el-tooltip>
                                        <span style="color: #909399; font-size: 12px;">商品类目：{{ i.type1 + '/' + i.type2
                                            + '/' + i.type3 }}</span>
                                        <span style="color: #909399; font-size: 12px;">竞品起始价：{{ i.priceRange }}
                                        </span>
                                        <span style="float:right; color: #909399; font-size: 12px;text-align: right;">
                                            选品状态: {{ i.allDataState ? '已选品' : '未选品' }}
                                        </span>
                                        <span>
                                            <el-link type="primary" :underline="false" style="font-size: 12px;"
                                                @click="getChart(i)">昨日销量：{{ i.yestodaySaleRange }}</el-link>
                                            <span style="float:right; color: #909399; font-size: 12px;">商品状态：{{ i.jjStatus
                                            }}</span>

                                        </span>

                                        <el-row>
                                            <el-col :span="8">
                                                <el-button size="mini" type="primary"
                                                    @click="tonewbidding(i)">有同款，参与竞价</el-button>
                                            </el-col>
                                            <el-col :span="5">
                                                <el-button size="mini" type="primary" @click="addGoods(i)"
                                                    v-if="!i.allDataState">添加选品</el-button>
                                            </el-col>
                                            <el-col :span="5" v-if="!i.sku">
                                                <el-button size="mini" type="primary"
                                                    @click="getSkuAsync(i.goodsId)">申请sku</el-button>
                                            </el-col>
                                            <el-col :span="6" v-if="i.sku == '1'">
                                                <el-button size="mini" type="primary" @click="getSkuAsync(i.goodsId)"
                                                    :disabled="skudisabled">sku申请中</el-button>
                                            </el-col>

                                            <el-col :span="5">
                                                <el-button size="mini" type="primary" @click="addhb(i)">合并显示</el-button>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- </el-row> -->
                </div>
                <template #footer>
                    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"
                        @page-change="pagechange" @size-change="sizechange" />
                </template>
            </my-container>


        </div>
        <!-- ///// -->
        <div class="btoright">
            <el-popover placement="left" width="400" trigger="click">
                <el-row>
                    <el-col :span="26" v-for="(i, index) in hbcompetitorPDDlist" :key="i.id">
                        <div class="grid-content bg-purple-dark">
                            <div class="marginzero flexrow">
                                <img :src="i.imageUrl" alt="" style="width: 90px; height: 90px; margin-left: 10px;">
                                <div style="margin-left: 10px; width: 280px;" class="flexcloumn">
                                    <el-tooltip class="item" effect="dark" :content="i.goodsName" placement="top">
                                        <div class="textellipsis">{{ i.goodsName }}</div>
                                    </el-tooltip>
                                    <span style="color: #909399; font-size: 12px;">商品类目：{{ i.type1 + '/' + i.type2
                                        + '/' + i.type3 }}</span>
                                    <!-- <span style="color: #909399; font-size: 12px;">昨日销量：{{ i.yestodaySaleRange }}</span> -->
                                    <span style="color: #909399; font-size: 12px;">竞品起始价：{{ i.priceRange }}
                                        <!-- <span style="float:right; color: #909399; font-size: 12px;">放榜天数：{{ i.fbDay }}</span> -->
                                    </span>
                                    <span>
                                        <el-link type="primary" :underline="false" style="font-size: 12px;"
                                            @click="getChart(i)">昨日销量：{{ i.yestodaySaleRange }}</el-link>
                                        <!-- <el-link v-if="!i.sku" type="primary" :underline="false" style="float:right; font-size: 12px;" @click="getSkuAsync(i.goodsId)">申请sku</el-link>
                                        <el-link v-if="i.sku=='1'" type="primary" :underline="false" style="float:right; font-size: 12px;" @click="openPddUrl(i.goodsId)">sku申请中</el-link>
                                        <span v-if="i.sku=='2'" style="float:right; color: #909399; font-size: 12px;">sku申请完成</span> -->
                                    </span>
                                    <el-button size="mini" type="primary" @click="removePdd(index)">移除</el-button>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-button v-if="hbcompetitorPDDlist.length > 0" size="mini" type="primary"
                    @click="hbtonewbidding()">有同款，参与竞价</el-button>
                <el-button slot="reference" type="primary">合并列表</el-button>
            </el-popover>
        </div>
        <el-dialog title="导入数据" :visible.sync="dialogVisibleData" width="30%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleData = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog title="昨日销量趋势图" :visible.sync="dialogVisibleChartData" width="50%" v-dialogDrag>
            <span>
                <el-row>
                    <buschar ref="buschar" :analysisData="showDetailVisible.data" v-if="showDetailVisible.data">
                    </buschar>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleChartData = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>
    

<script>
//import { Loading } from 'element-ui';
import MyContainer from '@/components/my-container';
import buschar from "@/components/Bus/buschar";
import {
    getPageCompetitorPDDEntityAsync, importCompetitorPDDAsync, getCompetitorPDDTypeAsync2,
    getCompetitorPDDType1Async, getCompetitorPDDChartAsync, competitorPDDSkuAsync,
    getCompetitorPDDTypeAsync, getCompetitorPDDYestodaySaleAsync
} from '@/api/operatemanage/newmedia/pddcontributeinfo'
import {
    isDoHotSaleGoodsAsync, getHotSaleGoodsByFilter
} from '@/api/operatemanage/productalllink/alllink'
// let loading;
// const startLoading = () => {
//   loading = Loading.service({
//   lock: true,
//   text: '加载中……',
//   background: 'rgba(0, 0, 0, 0.7)'
//   });
// }; 
export default {
    components: { MyContainer, buschar },
    data() {
        return {
            skudisabled: false,
            namechecked: false,
            hbcompetitorPDDlist: [],
            seljjstatus: [],
            yestodaySaleList: [],
            jjstatusList: ['全部', '待选', '竞价中', '竞价成功', '竞价失败'],
            dayList: [2, 3, 4, 5, 6, 7, 8, 9, 10],
            saleQSList: ['增长', '持平', '下降'],
            dialogVisibleChartData: false,
            showDetailVisible: { visible: false, title: "", data: {} },
            typeList: [],
            typeList1: [],
            typeList2: [],
            typeList3: [],
            typeNoList1: [],
            typeNoList2: [],
            typeNoList3: [],
            typeindex: '',
            typetail: {},
            filter: {
                currentPage: 1,
                pageSize: 50,
                goodsName: '',
                isAllName: 0,
                type1: '',
                type2: '',
                type3: '',
                typeSelList1: [],
                typeSelList2: [],
                typeSelList3: [],
                typeNoSelList1: [],
                typeNoSelList2: [],
                typeNoSelList3: [],
                allDataState: null
            },
            sels: [],
            competitorPDDlist: [],
            total: 0,
            pager: { OrderBy: "", IsAsc: false },
            uploadLoading: false,
            dialogVisibleData: false,
            value: '',
            pageLoading: false,

        }
    },
    async mounted() {

        await this.getyestodaySaleList();
        //await this.getTypeList();
        await this.getTypeList1();
        await this.getList();
    },
    methods: {
        async addGoods(i) {
            i.isOut = 'pdd'
            let self = this;
            self.$showDialogform({
                path: `@/views/operatemanage/productalllink/hotsale/AddChooseForm.vue`,
                title: '新增选品',
                args: { oid: '', mode: 0, ...i },
                height: 700,
                callOk: self.getList
            });
        },
        isGoodsName() {
            if (!this.filter.goodsName) {
                this.$message({ message: "商品名称为空！", type: "warning" });
                this.namechecked = false;
            }
        },
        addhb(i) {
            if (this.hbcompetitorPDDlist.length == 20) {
                this.$message({ message: "合并列表最多只能添加20个商品！", type: "warning" });
                return;
            }
            let b = false;
            b = this.hbcompetitorPDDlist.some((v) => {
                return v.id == i.id;
            })
            if (b) {
                this.$message({ message: "该商品已添加至合并列表！", type: "warning" });
            }
            else {
                this.hbcompetitorPDDlist.push(i);
                this.$message({ message: "商品添加至合并列表！", type: "success" });
            }
            console.log(this.hbcompetitorPDDlist, 'add')
        },
        removePdd(index) {
            console.log(index, 'qwewq')
            console.log(this.hbcompetitorPDDlist, 'remove')
            this.hbcompetitorPDDlist.splice(index, 1);
        },
        gettypetail() {
            this.typetail = this.typeList[this.typeindex];
            console.log(this.typetail, '1111');
        },
        // 初始页currentPage、初始每页数据数pagesize和数据data
        async handleSizeChange(size) {
            // this.filter.pageSize = size;
            await this.getList();
        },
        async handleCurrentChange(currentPage) {
            // this.filter.currentPage = currentPage;
            await this.getList();
        },
        async getTypeList() {
            var that = this;
            const res = await getCompetitorPDDTypeAsync().then(res => {
                that.typeList = res.data;
            });
        },
        async getTypeList1() {
            this.typeList1 = []; this.filter.typeSelList1 = [];
            this.typeList2 = []; this.filter.typeSelList2 = [];
            this.typeList3 = []; this.filter.typeSelList3 = [];
            const res2 = await getCompetitorPDDTypeAsync2({ typelevel: 1 });
            if (res2 && res2?.data) {
                res2.data.forEach(f => {
                    this.typeList1.push({ label: f, value: f });
                    this.typeNoList1.push({ label: f, value: f });
                });
            }
        },
        async gettypetail1() {
            this.typeList2 = []; this.filter.typeSelList2 = [];
            this.typeList3 = []; this.filter.typeSelList3 = [];
            const res2 = await getCompetitorPDDTypeAsync2({ typelevel: 2, typename: this.filter.typeSelList1 });
            if (res2 && res2?.data) {
                res2.data.forEach(f => {
                    this.typeList2.push({ label: f, value: f });
                });
            }
        },
        async gettypetail2() {
            this.typeList3 = []; this.filter.typeSelList3 = [];
            const res2 = await getCompetitorPDDTypeAsync2({ typelevel: 3, typename: this.filter.typeSelList2 });
            if (res2 && res2?.data) {
                res2.data.forEach(f => {
                    this.typeList3.push({ label: f, value: f });
                });
            }
        },
        async gettypetailNo1() {
            this.typeNoList2 = []; this.filter.typeNoSelList2 = [];
            this.typeNoList3 = []; this.filter.typeNoSelList3 = [];
            const res2 = await getCompetitorPDDTypeAsync2({ typelevel: 2, typename: this.filter.typeNoSelList1 });
            if (res2 && res2?.data) {
                res2.data.forEach(f => {
                    this.typeNoList2.push({ label: f, value: f });
                });
            }
        },
        async gettypetailNo2() {
            this.typeNoList3 = []; this.filter.typeNoSelList3 = [];
            const res2 = await getCompetitorPDDTypeAsync2({ typelevel: 3, typename: this.filter.typeNoSelList2 });
            if (res2 && res2?.data) {
                res2.data.forEach(f => {
                    this.typeNoList3.push({ label: f, value: f });
                });
            }
        },
        async getyestodaySaleList() {
            var that = this;
            const res = await getCompetitorPDDYestodaySaleAsync().then(res => {
                that.yestodaySaleList = res.data;
            });
        },

        async getChart(row) {
            this.dialogVisibleChartData = true;
            let par = {};
            par.goodsId = row.goodsId;
            var that = this;
            const res1 = await getCompetitorPDDChartAsync(par).then((res) => {
                that.showDetailVisible.visible = true;
                that.showDetailVisible.data = res.data;
                that.showDetailVisible.title = res.data.legend[0];
            });
            await this.$refs.buschar.initcharts()
        },
        async onSearch() {
            this.filter.currentPage = 1;
            await this.getList()
        },
        sizechange(val) {
            this.filter.pageSize = val;
        },
        pagechange(val) {
            this.filter.currentPage = val;
        },

        async getSkuAsync(item) {
            let that = this;
            let par = {};
            par.goodsId = item;
            const res = await competitorPDDSkuAsync(par).then(res => {
                if (res.data == true) {
                    this.$message({ message: "正在请求sku", type: "success" });
                    that.skudisabled = true;
                }

                else
                    this.$message({ message: "请求失败", type: "error" });
            });


            // let routeUrl = this.$router.resolve({
            //     path: "https://mobile.yangkeduo.com/goods2.html?goods_id=447928336284",
            //     query: par
            // });
            //window.open("https://mobile.yangkeduo.com/goods2.html?goods_id=" + item, '_blank');

            await this.getList();
            setTimeout(function () {
                that.skudisabled = false
            }, 1000 * 60);

        },

        async openPddUrl(item) {
            await this.getSkuAsync(item);
            //window.open("https://mobile.yangkeduo.com/goods2.html?goods_id=" + item, '_blank');
        },

        async getList() {
            this.filter.lxUp = '';
            this.filter.lxPw = '';
            this.filter.lxDown = '';
            this.filter.lxDay = '';
            if (this.filter.fbDay > 0) {
                if (this.filter.saleQS == '增长') {
                    this.filter.lxUp = this.filter.fbDay;
                }
                else if (this.filter.saleQS == '持平') {
                    this.filter.lxPw = this.filter.fbDay;
                }
                else if (this.filter.saleQS == '下降') {
                    this.filter.lxDown = this.filter.fbDay;
                }
                else {
                    this.filter.lxDay = this.filter.fbDay;
                }
            }
            var that = this;
            that.pageLoading = true;
            // if (this.typetail) {
            //     this.filter.type1 = this.typetail.type1;
            //     this.filter.type2 = this.typetail.type2;
            //     this.filter.type3 = this.typetail.type3;
            // }
            // else {
            //     this.filter.type1 = '';
            //     this.filter.type2 = '';
            //     this.filter.type3 = '';
            // }
            // if (this.seljjstatus.length > 0) {
            //     let s = "";
            //     this.seljjstatus.forEach(a => {
            //         s += "\'" + a + "\',"
            //     })
            //     this.filter.jjStatus = s.substring(0, s.length - 1);
            //     console.log(this.filter.jjStatus, '111111')
            // }
            // else
            //     this.filter.jjStatus = '';
            this.filter.jjStatus = this.seljjstatus.join(',');
            this.filter.isAllName = 0;
            if (this.namechecked)
                this.filter.isAllName = 1;
            if (this.filter.isAllName == 1 && !this.filter.goodsName) {
                //this.$message({ message: "商品名称为空！", type: "warning" }); 
                this.filter.isAllName = 0;
                this.namechecked = false;
            }
            const params = { ...this.filter };
            const res = await getPageCompetitorPDDEntityAsync(params).then(res => {
                that.total = res.total;
                that.competitorPDDlist = res.list;
            });
            if (this.filter.isAllName == 1 && this.competitorPDDlist.length > 0) {
                if (this.competitorPDDlist.length > 20) {
                    this.$message({ message: "查询出来的数据大于20，合并列表最多只能添加20个商品！", type: "warning" });
                }
                else
                    this.hbcompetitorPDDlist = this.competitorPDDlist;
            }
            that.pageLoading = false;
        },

        async uploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false
            };
            const form = new FormData();
            form.append("upfile", item.file);
            //form.append("platform", 1);
            var res = await importCompetitorPDDAsync(form);
            if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
            else this.$message({ message: res.msg, type: "warning" });
            this.$refs.upload.clearFiles()
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        uploadRemove(file, fileList) {
            this.uploadChange(file, fileList);
        },
        submitUpload() {
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
        tonewbidding(i) {

            //this.$router.push({ path: '/pddbgmanage/pddbiddingsel',query: {id:id}})
            let routeUrl = this.$router.resolve({
                path: "/pddbgmanage/pddbiddingsel?goodsIds=" + i.goodsId,
                //query: encodeURIComponent(JSON.stringify(par))

            });
            window.open(routeUrl.href, '_blank');
        },
        async hbtonewbidding() {
            // var par = this.hbcompetitorPDDlist;
            // par.sort((a, b) => { 
            //     return a.minPrice - b.minPrice;
            // });
            let p = [];
            this.hbcompetitorPDDlist.forEach(a => {
                p.push(a.goodsId);
            });
            let routeUrl = this.$router.resolve({
                path: "/pddbgmanage/pddbiddingsel?goodsIds=" + p.join(','),
                //query: encodeURIComponent(JSON.stringify(par))

            });
            window.open(routeUrl.href, '_blank');
            // for (let i = 0; i < this.hbcompetitorPDDlist.length; i++) {
            //     await this.getSkuAsync(this.hbcompetitorPDDlist[i].goodsId);
            // }
            // this.$router.push({ path: '/pddbgmanage/pddbiddingsel',query: {id:id}})

        }
    }
}
</script>

<style lang="scss" scoped>
.allbody {
    height: 85vh;
    margin: 0 auto;
    position: relative;
}

.btoright {
    position: absolute;
    right: 0;
    bottom: 0;
    // width: 200px;
    // height: 200px;
    margin-bottom: 300px;
    // background: red;
}

.heard {
    height: auto;
    width: 100%;
    // padding: 10px 30px;
    background: #dfebf7;
}

.flexrow {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flexstart {
    display: flex;
    flex-direction: row;
}

.flexcloumn {
    display: flex;
    flex-direction: column;
}

.marleft {
    margin-left: 30px;
    margin-right: 15px;
}

.content {
    // background: #eee;
    width: 100%;
    // height: 720px;
    flex-wrap: wrap;
    height: 76vh;
    overflow-y: auto;
}

.flexwarp {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
}

.marginzero {
    margin: 3px;
    height: 130px;
    border: 1px solid #eee;
    // background: #eee;
}

.neicontent {
    max-height: 76vh;
    width: 100%;
    justify-content: start;
    margin-bottom: auto;
    flex-wrap: wrap;
    overflow-y: auto;
}

.heardcss {
    font-size: 15px;
    height: 80px;
}

.textellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>