<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                 <el-form-item label="确认时间:">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="确认开始时间" end-placeholder="确认结束日期" :clearable="false" :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
                </el-form-item>
                <el-form-item label="内部订单号:">
                    <el-input placeholder="内部订单号" v-model="filter.orderNoInner" style="width: 110px" clearable></el-input>
                </el-form-item>
                <el-form-item >
                        <el-button type="primary" @click="onSearch" >查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list'  :tableCols='tableCols' :isSelection="false" @select="selectchange"
            :tableHandles='tableHandles' @cellclick="cellclick"
            :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import MyConfirmButton from '@/components/my-confirm-button';
import {getOrderAnalysisWrongHairList} from '@/api/order/ordernodes';

const tableCols = [
  {istrue: true,prop: 'orderNoInner',label: '内部单号',width: '80',sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner' },
  {istrue: true,prop: 'confirm_Date',label: '最后确认时间', width: '120',sortable: 'custom'},
  {istrue: true,prop: 'type',label: '售后类型',width: '80',sortable: 'custom',formatter:(row)=>row.typeName||' '},
  {istrue: true,prop: 'question_Type',label: '问题类型',width: '80',sortable: 'custom'},
  {istrue: true,prop: 'remark',label: '备注',width: '150',sortable: 'custom'},
  {istrue: true,prop: 'confirmTime',label: '审单时长',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'printTime',label: '打单时长',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'pickTime',label: '配货时长',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'deliverySpanTime',label: '发货时长',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'confirmer',label: '审单人员',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'printer',label: '打单人员',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'picker',label: '配货人员',width: '120',sortable: 'custom'},
  {istrue: true,prop: 'delivery',label: '发货人员',width: 'auto',sortable: 'custom'},
]
const tableHandles = [
  { label: '刷新', handle: (that) => that.onSearch() },
]

const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminAftersaleanalysiswronghair',
    components: { MyContainer, cesTable, MyConfirmButton },

    data() {
        return {
          date: '',
            that:this,
            cashRedResonItems:[],
            filter:{
                afterNo:'',
                orderNoInner:'',
                goodsName:'',
                goodsCode:'',
                resonLevel1:'',
                resonLevel2:'',
                saleAfter_Type:'5',
                timerange:[startTime,endTime],
                startTime:null,
                endTime:null,
            },
            shareFeeType: 5,
            params: {},
            that: this,
            list: [],
            picList:[],
            saleList: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: 'Confirm_Date', IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                //disabledDate(time) {
                //return time.getTime() > Date.now();
                //},
                disabledDate(date) {
                // 设置禁用日期
                const start = new Date('1970/1/1');
                const end = new Date('9999/12/31');
                return date < start || date > end;
                  }
              },
            defaultDate: new Date('1970/1/1')
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if (!this.filter.timerange||this.filter.timerange.length<2){
                this.$message({message: "请先选择日期！",type: "warning",});
                return;
            }
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            this.listLoading = true
            const res = await getOrderAnalysisWrongHairList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            //this.summaryarry=res.data.summary;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
       },
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else{
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
            }
            await this.onSearch();
        },
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){

        },
    },
};
</script>

<style lang="scss" scoped>

</style>
