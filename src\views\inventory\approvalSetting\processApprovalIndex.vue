<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <!-- <el-select v-model="ListInfo.groupType" placeholder="请选择类别" clearable style="width:120px ;margin: 0 5px 5px 0;"
          @change="operateChange">
          <el-option label="运营组" value="运营组"></el-option>
          <el-option label="采购组" value="采购组"></el-option>
        </el-select> -->
        <el-select v-model="ListInfo.groupId" clearable filterable placeholder="请选择小组" class="publicCss">
          <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" size="mini" @click="onSetup">新增流程</el-button>
          <el-button type="primary" size="mini" @click="startImport">导入</el-button>
          <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
          <el-button type="primary" size="mini" @click="batchDeletion({})">批量删除</el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'processApprovalIndex202410291350'" :tablekey="'processApprovalIndex202410291350'" ref="table"
      :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true" :border="true"
      :table-data="data.list" :table-cols="tableCols" :is-selection="false" :is-select-column="true"
      :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" :showoverflow="false" @cellStyle="cellStyle" cellStyle
      @select="checkboxRangeEnd" :isDisableCheckBox="true" @checCheckboxkMethod="checCheckboxkMethod">
      <template slot="right">
        <vxe-column title="操作" width="70">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" style="color: red;" @click="batchDeletion(row)"
                :disabled="row.groupName == '默认组'">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="addProcessToolbar" width="50%" v-dialogDrag style="margin-top: -12vh;" append-to-body>
      <div>
        <processSettings ref="refprocessSettings" v-if="addProcessToolbar" @close="onClose" :sceneName="sceneNameI" />
      </div>
      <div style="display: flex;justify-content: center;gap: 10px;">
        <el-button @click="addProcessToolbar = false">关闭</el-button>
        <el-button type="primary" @click="onAddProcess">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false"
      append-to-body>
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import { getPurchaseDeptList } from '@/api/inventory/purchaseordernew'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import processSettings from '@/views/base/processSettings.vue'
import processApprovalIndex from '@/views/inventory/approvalSetting/processApprovalIndex.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/inventory/ApprovalProcess/'
export default {
  props: {
    sceneName: {
      type: String,
      default: ''
    },
  },
  name: "processApprovalIndex",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, processSettings, processApprovalIndex
  },
  data() {
    return {
      api,
      grouplist: [],
      operatelist: [],
      purchasegrouplist: [],
      checkboxGroup: [],
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      sceneNameI: '',
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
        groupId: '',
        groupType: '运营组',
      },
      addProcessToolbar: false,
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
      verifyEdit: false
    }
  },
  async mounted() {
    this.sceneNameI = this.sceneName;
    await this.getCol();
    await this.getList()
    await this.init()
  },
  methods: {
    async checCheckboxkMethod(row, callback) {
      let isNotStock = row.groupName == '默认组' ? false : true
      await callback(isNotStock);
    },
    operateChange(e) {
      this.grouplist = []
      if (e == '运营组') {
        this.grouplist = this.operatelist
      } else if (e == '采购组') {
        this.grouplist = this.purchasegrouplist
      } else {
        this.grouplist = []
      }
      this.ListInfo.groupId = ''
    },
    downLoadFile() {
      window.open("../../static/excel/审批配置导入模版.xlsx", "_self");
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("file", item.file);
      form.append("Title", this.sceneNameI);
      var res = await request.post(`${this.api}ImportApprovalProcess`, form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    checkboxRangeEnd(list) {
      this.checkboxGroup = list
    },
    batchDeletion(row) {
      const isSingleDeletion = Object.keys(row).length > 0;
      if (!isSingleDeletion && this.checkboxGroup.length == 0) {
        this.$message({ message: "请先选择数据", type: "warning" });
        return false;
      }
      const confirmMessage = isSingleDeletion ? '是否删除该数据?' : '是否删除选中数据?';
      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const idList = isSingleDeletion ? [row.id] : this.checkboxGroup.map(item => item.id);
        const { data, success } = await request.post(`${this.api}BatchDeleteApprovalProcess`, { idList });
        if (success) {
          this.getList();
          this.$message.success('删除成功');
        }
      }).catch(() => {
        this.$message.info('已取消');
      });
    },
    onClose() {
      this.addProcessToolbar = false
      this.getList()
    },
    async handleEdit(row) {
      this.addProcessToolbar = true
      this.$nextTick(() => {
        this.verifyEdit = true
        this.$refs.refprocessSettings.editProcessMethod(row)
      })
      // const { data, success } = await request.post(`${this.api}UpdateApprovalProcess`, { ...row })
      // if (!success) return
      // console.log(data, 'data');
    },
    onAddProcess() {
      this.$refs.refprocessSettings.addProcessMethod(this.verifyEdit)
    },
    onSetup() {
      this.verifyEdit = false
      this.addProcessToolbar = true
    },
    async cellStyle(row, column, callback) {
      callback({ paddingLeft: '4px' })
    },
    async init() {
      this.grouplist = []
      var res2 = await getDirectorGroupList();
      this.operatelist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
      this.grouplist = this.operatelist;
      // var { data } = await getPurchaseDeptList();
      // this.purchasegrouplist = data.map(item => { return { value: item.dept_id, label: item.full_name }; });
    },
    // 导出数据
    async exportProps() {
      this.isExport = true
      this.loading = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.loading = false
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        const filteredData = data.filter(item => item.visible !== false);
        filteredData.unshift({
          label: "",
          type: "checkbox",
        });
        filteredData.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(2, row.proCode)
          }
        })
        this.tableCols = filteredData;
        this.ListInfo.summarys = filteredData
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;
}

.publicCss {
  width: 180px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.containerCss {
  padding: 10px 0 0 5px;
  height: 100%;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
