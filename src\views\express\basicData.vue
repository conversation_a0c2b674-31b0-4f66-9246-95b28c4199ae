<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="psTimeRange" type="datetimerange" :picker-options="pickerOptions" range-separator="至"
          start-placeholder="拍摄时间开始日期" end-placeholder="拍摄时间结束日期" style="width: 280px;margin-right: 10px;"
          value-format="yyyy-MM-dd HH:mm:ss" @change="changeTime($event, 'ps')" />
        <el-date-picker v-model="stTimeRange" type="datetimerange" :picker-options="pickerOptions" range-separator="至"
          start-placeholder="识图时间开始日期" end-placeholder="识图时间结束日期" style="width: 280px;margin-right: 10px;"
          value-format="yyyy-MM-dd HH:mm:ss" @change="changeTime($event, 'st')" />
        <el-select v-model="ListInfo.package" placeholder="耗材类别" style="width: 120px;margin-right: 10px;;" filterable
          clearable>
          <el-option v-for="item in options" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.isNormal" placeholder="状态" style="width: 80px;margin-right: 10px;;" clearable>
          <el-option key="正常" label="正常" :value="0" />
          <el-option key="异常" label="异常" :value="1" />
          <el-option key="已纠正" label="已纠正" :value="2" />
        </el-select>
        <el-input v-model="ListInfo.machineNo" placeholder="请输入机器编号" class="publicCss" maxlength="200" clearable />
        <div style="width: 200px;">
        <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.expressNo" v-model="ListInfo.expressNo" width="190px"
            placeholder="快递单号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
            :maxlength="21000" @callback="mailNumberCallback" title="快递单号">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.packageCode" placeholder="耗材编码" class="publicCss" maxlength="200" clearable />
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="openSupplies">耗材成本</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'basicData202408041525'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' @summaryClick='onsummaryClick' 
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="true" v-loading="loading"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%; height: 95%; margin: 0">
      <template slot="right">
        <vxe-column title="日志" width="80">
          <template #default="{ row, $index }">
            <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
              <i class="el-icon-document" style="font-size: 20px" @click="operationClick(row)"></i>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />

    <el-dialog title="耗材成本" :visible.sync="suppliesVisible" width="50%" :append-to-body="true" v-dialogDrag>
      <el-select v-model="type" placeholder="耗材类别" style="width: 220px;margin-bottom: 10px;" filterable clearable
        @change="changeType">
        <el-option v-for="item in options" :key="item" :label="item" :value="item" />
      </el-select>
      <p>
        <span style="color: red;">有设置商品编码时，成本值会定期从商品编码中获取</span>
      </p>

      <vxe-table border resizable show-overflow :data="dialogTableData" size="mini" max-height="400px"
        :edit-config="{ trigger: 'click', mode: 'row' }">
        <vxe-column field="packagetype" title="耗材类别" />
        <vxe-column field="packagesize" title="耗材尺寸" />
        <vxe-column field="goodsCode" title="商品编码" :edit-render="{}">
          <template #edit="{ row }">
            <el-input v-model="row.goodsCode" label="商品编码" />
          </template>
        </vxe-column>
        <vxe-column field="cost" title="耗材成本" :edit-render="{}">
          <template #edit="{ row }">
            <el-input-number v-model="row.cost" :max="9999999" label="耗材成本" :precision="2" :controls="false" />
          </template>
        </vxe-column>
      </vxe-table>
      <div style="margin-top: 10px;display: flex;justify-content: end;">
        <el-button type="primary" @click="submit" v-throttle="3000">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="carouselPopupWindow" width="50%" :append-to-body="true" v-dialogDrag @close="pictureOff"
      @keyup.native.37="arrowClick('prev')">
      <div v-loading="echartsLoading">
        <div class="carousel-container">
          <div class="arrow prev">
            <i class="el-icon-arrow-left arrow-icon" @click="arrowClick('prev')"></i>
          </div>
          <div class="image-container">
            <el-image class="custom-image" :src="picture" :preview-src-list="[picture]">
            </el-image>
          </div>
          <div class="arrow next">
            <i class="el-icon-arrow-right arrow-icon" @click="arrowClick('next')"></i>
          </div>
        </div>
        <div class="centered-content">
          <el-select v-model="pictureconsumable" placeholder="请选择" style="margin-right: 20px;" filterable>
            <el-option v-for="item in consumableData" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" size="small" type="primary" :loading="uploadLoading"
            @click="pictureCorrection(1)">{{ (uploadLoading ? '纠正中' : '纠正') }}</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="纠正耗材" :visible.sync="correctiveConsumables" width="20%" :append-to-body="true" v-dialogDrag>
      <div style="height: 50px;margin-top: 30px;">
        <span>选择耗材: </span>
        <el-select v-model="rectifyconsumable" placeholder="请选择" style="margin-right: 20px;" filterable>
          <el-option v-for="item in consumableData" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="correctiveConsumables = false">取消</el-button>
        <el-button type="primary" @click="pictureCorrection(2)">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="操作日志" :visible.sync="operationLog" width="30%" :append-to-body="true" v-dialogDrag>
      <div style="height: 500px; overflow-y: auto;">
        <div v-for="item in logsTable" :key="item.key"
          style="display: flex; flex-direction: column; margin-bottom: 10px; ">
          <div>
            <span style="margin-right: 10px;">时间:</span>
            <span>{{ item.correctionTime }}</span>
          </div>
          <div>
            <span style="margin-right: 10px;">操作人:</span>
            <span>{{ item.correctionUserName }}</span>
          </div>
          <div>
            <span style="margin-right: 10px;">内容:</span>
            <span>操作日志"{{ item.beforeCorrectionPackage }}"变更为"{{ item.afterCorrectionPackage }}"</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="operationLog = false">取消</el-button>
      </span>
    </el-dialog>

<el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data" :loading="buscharDialog.loading"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { getExpressPackageCostList, pageExpressImgPackage, setExpressPackageCost, getAllPackageTypes, getExpressPackageCostSelectList, editExpressImgPackageAsync, pageExpressPackagelogs, exportExpressImgPackage,getMonthImgPackageStatistical} from '@/api/express/expressPackage'
import dayjs from "dayjs";
import buschar from '@/components/Bus/buschar'
import inputYunhan from "@/components/Comm/inputYunhan";


const tableCols = [
  { istrue: true, prop: 'machineno', label: '机器编号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'expressno', label: '快递单号', sortable: 'custom', width: 'auto' },
  {
    istrue: true, prop: 'fileUrl', label: '耗材类别', width: '140px', type: 'button', btnList: [
      { istrue: true, color: 'black', prop: 'package', label: '耗材类别', sortable: 'custom', width: 'auto' },
      { label: '纠正', handle: (that, row) => { that.downloadLink(row); }, },
    ]
  },
  { istrue: true, prop: 'cost', label: '耗材成本',  type: 'custom', summaryEvent: true,    sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'packageCode', label: '耗材编码', sortable: 'custom', width: 'auto' },
  {
    istrue: true, prop: 'rltisnormal', label: '状态', sortable: 'custom', width: 'auto', formatter: (row) => {
      if (row.rltisnormal === 0) {
        return '正常'
      } else if (row.rltisnormal === 1) {
        return '异常'
      } else if (row.rltisnormal === 2) {
        return '已纠正'
      } else {
        return ''
      }
    }
  },
  {
    istrue: true, prop: 'inpout_path', label: '快递图片', width: '56', type: 'carouselimages', align: 'center', formatter: (row) => {
      if (row.rltisnormal === 0) {
        return row.output_normal_path
      } else if (row.rltisnormal === 1) {
        return row.output_abnormal_path
      } else if (row.rltisnormal === 2) {
        return row.output_normal_path
      } else {
        return ''
      }
    }, handle: (that, row, column, cell) => that.canclick(row, column, cell)
  },
  { istrue: true, prop: 'rltshoottime', label: '拍摄时间', sortable: 'custom', width: 'auto', formatter: (row) => row.rltshoottime ? dayjs(row.rltshoottime).format('YYYY-MM-DD HH:mm') : null },
  { istrue: true, prop: 'rltmaptime', label: '识图时间', sortable: 'custom', width: 'auto', formatter: (row) => row.rltmaptime ? dayjs(row.rltmaptime).format('YYYY-MM-DD HH:mm') : null },
  { istrue: true, prop: 'rltsimilarity', label: '相似度', sortable: 'custom', width: 'auto', formatter: (row) => row.rltsimilarity !== null ? (row.rltsimilarity * 100).toFixed(2) + '%' : null },
  { istrue: true, prop: 'rltweight', label: '重量(kg)', sortable: 'custom', width: 'auto', formatter: (row) => row.rltweight },
]
export default {
  components: {
    MyContainer,
    vxetablebase,
    buschar,
    inputYunhan
  },
  data() {
    return {
      uploadLoading: false,//纠正loading
      summaryarry: {},//汇总
      closePopup: false,//关闭弹窗
      picture: '',//图片
      rectifyconsumable: '',//纠正耗材
      pictureconsumable: '',//图片耗材
      echartsLoading: false,//图片加载
      pictureexpressno: '',//图片快递单号
      consumableData: [],//耗材类别
      rectifyTrackingNumber: '',
      ListInfo: {//列表参数
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        shootTimeStart: null,//拍摄时间开始
        shootTimeEnd: null,//拍摄时间结束
        mapTimeStart: null,//识图时间开始
        mapTimeEnd: null,//识图时间结束
        package: null,//耗材类别
        isNormal: null,//状态
        expressNo: null,//快递单号
        machineNo: null,//机器编号
        packageCode: null,//耗材编码
      },
      total: 0,//总条数
      that: this,//表格this
      tableCols,//表格列
      pickerOptions,//时间选择器配置
      tableData: [],//表格数据
      psTimeRange: [],//拍摄时间
      stTimeRange: [],//识图时间
      options: [],//耗材类别
      loading: false,//加载中
      suppliesVisible: false,//耗材成本
      dialogTableData: [],
      selectTableData: [],
      type: null,
      carouselPopupWindow: false,//图片弹窗
      correctiveConsumables: false,//纠正耗材
      operationLog: false,//操作日志
      logsTable: [],//
      buscharDialog: { visible: false, title: "", data: {},loading:false },
    }
  },
  async mounted() {
    //监听键盘事件
    document.addEventListener('keydown', (e) => {
      let key = window.event.keyCode;
      if (key == 37 && this.carouselPopupWindow) {
        this.arrowClick('prev')
      } else if (key == 39 && this.carouselPopupWindow) {
        this.arrowClick('next')
      }
    })
    await this.getList()
    await this.getTypeList()
  },
  methods: {
    //刷新页面
    pictureOff() {
      if (this.closePopup) {
        this.getList()
        return
      }
    },
    //图片点击
    async arrowClick(val) {
      const index = this.tableData.findIndex(item => item.expressno === this.pictureexpressno);
      const isNext = val === 'next';
      const isEnd = isNext ? index === this.tableData.length - 1 : index === 0;

      if (isEnd) {
        this.$message.error(isNext ? '已经是当前页最后一张了,正在加载下一页' : '已经是当前页第一张了,正在加载上一页');
        this.echartsLoading = true;
        if (this.tableData.length >= this.ListInfo.pageSize) {
          await this.Pagechange(this.ListInfo.currentPage + (isNext ? 1 : -1));
        }
        const targetIndex = isNext ? 0 : this.tableData.length - 1;
        this.updatePictureAndExpressno(targetIndex);
        this.echartsLoading = false;
      } else {
        const newIndex = isNext ? index + 1 : index - 1;
        this.updatePictureAndExpressno(newIndex);
      }
    },
    updatePictureAndExpressno(index) {
      this.picture = this.tableData[index].output_normal_path;
      this.pictureexpressno = this.tableData[index].expressno;
      this.pictureconsumable = this.tableData[index].package;
    },
    mailNumberCallback(val) {
      this.ListInfo.expressNo = val
    },
    //导出
    async exportProps() {
      this.loading = true
      const res = await exportExpressImgPackage(this.ListInfo)
      if (!res) {
        this.loading = false
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/zip" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '快递包材数据' + new Date().toLocaleString() + '.zip')
      aLink.click()
      this.loading = false
    },
    //操作日志
    async operationClick(row) {
      const { data, success } = await pageExpressPackagelogs({ expressno: row.expressno })
      if (success) {
        this.logsTable = data
      } else {
        this.$message.error('获取操作日志失败')
      }
      this.operationLog = true
    },
    //纠正确定
    pictureCorrection(val) {
      if (val === 1) {
        this.closePopup = true
      }
      const params = {
        expressno: val === 1 ? this.pictureexpressno : this.rectifyTrackingNumber,
        package: val === 1 ? this.pictureconsumable : this.rectifyconsumable,
        status: 2
      };
      this.determination(params, val);
    },
    async determination(params, val) {
      this.uploadLoading = true;
      const { success } = await editExpressImgPackageAsync(params);
      this.uploadLoading = false;
      if (success) {
        this.$message.success('纠正成功');
        if (val == 2) {
          this.correctiveConsumables = false;
          await this.getList();
        }
      } else {
        this.$message.error('纠正失败');
      }
    },
    //纠正耗材
    downloadLink(row) {
      this.rectifyconsumable = row.package
      this.rectifyTrackingNumber = row.expressno
      this.correctiveConsumables = true
    },
    //图片点击
    canclick(row, column, cell) {
      if (!row) return
      this.picture = row.output_normal_path
      this.pictureexpressno = row.expressno
      this.pictureconsumable = row.package
      this.carouselPopupWindow = true;
    },
    async changeType(e) {
      if (e) {
        this.dialogTableData = this.selectTableData.filter(item => item.packagetype == e)
      } else {
        this.dialogTableData = this.selectTableData
      }
    },
    async getTypeList() {
      const { data, success } = await getAllPackageTypes()
      if (success) {
        this.options = data
      } else {
        this.$message.error('获取耗材类别失败')
      }
      //获取耗材类别
      const res = await getExpressPackageCostSelectList()
      if (res.success) {
        this.consumableData = res.data.map(item => { return { label: item.package, value: item.package }; });
      }
    },
    async submit() {
      this.dialogTableData?.forEach(item => {
        if (item.cost <= 0) {
          this.$message.error('耗材成本必须大于0')
          throw new Error('耗材成本必须大于0')
        }
        if (!item.cost) {
          this.$message.error('耗材成本不能为空')
          throw new Error('耗材成本不能为空')
        }
      })
      const { success } = await setExpressPackageCost(this.dialogTableData)
      if (success) {
        this.$message.success('保存成功')
        this.suppliesVisible = false
      } else {
        this.$message.error('保存失败')
      }
    },
    async getOptions() {
      const { data, success } = await getExpressPackageCostList()
      if (success) {
        this.dialogTableData = data
        this.selectTableData = data
      } else {
        //获取耗材类别失败
        this.$message.error('获取耗材类别失败')
      }
    },
    async changeTime(e, type) {
      if (e) {
        if (type == 'ps') {
          this.ListInfo.shootTimeStart = e[0]
          this.ListInfo.shootTimeEnd = e[1]
        } else {
          this.ListInfo.mapTimeStart = e[0]
          this.ListInfo.mapTimeEnd = e[1]
        }
      } else {
        if (type == 'ps') {
          this.ListInfo.shootTimeStart = null
          this.ListInfo.shootTimeEnd = null
        } else {
          this.ListInfo.mapTimeStart = null
          this.ListInfo.mapTimeEnd = null
        }
      }
      await this.getList()
    },
    async openSupplies() {
      await this.getOptions()
      this.suppliesVisible = true
    },
    async getList(isSearch) {
      if (isSearch) {
        this.ListInfo.currentPage = 1
      }
      if (this.psTimeRange.length == 0) {
        //默认给近7天时间
        this.ListInfo.shootTimeStart = dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss')
        this.ListInfo.shootTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss')
        this.psTimeRange = [this.ListInfo.shootTimeStart, this.ListInfo.shootTimeEnd]
      }
      const replaceArr = ['expressNo', 'machineNo']
      this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
      this.loading = true
      const { data, success } = await pageExpressImgPackage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    async Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      await this.getList()
    },
    //当前页改变
    async Pagechange(val) {
      this.ListInfo.currentPage = val;
      await this.getList()
    },
    async sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        await this.getList()
      }
    },
    async onsummaryClick(property){
        console.log("AAAAA");
        console.log(this.ListInfo.shootTimeEnd);
 
     
      const params = { };
        if (this.ListInfo.shootTimeEnd) {
        params.shootTimeEnd =  this.ListInfo.shootTimeEnd;
      }
      // params.column = property;
  console.log(params);
      let that = this;
      that.buscharDialog.visible = true;
      that.buscharDialog.loading= true;
      const res = await getMonthImgPackageStatistical(params).then(res => {
        that.buscharDialog.loading= false;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
      await that.$refs.buschar.initcharts();
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .custom-image img {
  max-width: 100% !important;
  max-height: 610px !important;
  object-fit: contain;
}

.centered-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.carousel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 574px;
}

.image-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
}

.arrow-icon {
  font-size: 30px !important;
  cursor: pointer;
}

::v-deep .c--tooltip {
  display: flex !important;
}
</style>
