<template>
  <container v-loading="pageLoading">
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="年月:">
            <el-date-picker style="width: 240px" v-model="filter.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="导入时间:">
            <el-date-picker style="width: 240px" v-model="filter.timerange1" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

  <el-tabs v-model="activeName" style="height: 94%;">
    <el-tab-pane label="物流提货费" name="first" style="height: 100%;">
       <purchasepickup :filter="filter" ref="purchasepickup" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="采购运费" name="purchasefreight" style="height: 100%;" lazy>
       <purchasefreight :filter="filter" ref="purchasefreight" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="采购单管理" name="purchaseOrderManage" style="height: 100%;" lazy>
       <purchaseOrderManage :filter="filter" ref="purchaseOrderManage" />
    </el-tab-pane>
    <el-tab-pane label="采购退货" name="purchaseReturn" style="height: 100%;" lazy>
       <purchaseReturn :filter="filter" ref="purchaseReturn" />
    </el-tab-pane>
    <el-tab-pane label="运营下架" name="offlinefee" style="height: 100%;" lazy>
       <offlinefee :filter="filter" ref="offlinefee" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="仓储损耗" name="storelossfee" style="height: 100%;" lazy>
       <storelossfee :filter="filter" ref="storelossfee" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="拍摄道具费" name="third" style="height: 100%;" lazy>
      <shootfee :filter="filter" ref="shootfee" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="样品费报销" name="third1" style="height: 100%;" lazy>
      <samplefeebx :filter="filter" ref="samplefeebx" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="运营拿样品费" name="samplefeeyy" style="height: 100%;" lazy>
       <samplefeeyy :filter="filter" ref="samplefeeyy" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="美工拿样品费" name="samplefeemg" style="height: 100%;" lazy>
       <samplefeemg :filter="filter" ref="samplefeemg" @onstartImport='onstartImport' @ondownloadmb='ondownloadmb' @ondeleteByBatch='ondeleteByBatch' @onstartcomput='onstartcomput'/>
    </el-tab-pane>
    <el-tab-pane label="核算结果" name="six" style="height: 100%;" lazy>
       <compute :filter="filter" ref="compute"/>
    </el-tab-pane>
    <el-tab-pane label="产品费用月报" name="seven" style="height: 100%;" lazy>
       <monthsumfee :filter="filter" ref="productmonthreport"/>
    </el-tab-pane>
  </el-tabs>

   <el-dialog title="导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
      <span>
          <el-row>
           <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
           </el-col>
         </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                <el-upload
                  ref="upload"
                  class="upload-demo"
                  :auto-upload="false"
                  :multiple="false"
                  :limit="1"
                  action
                  accept=".xlsx"
                  :http-request="uploadFile"
                  :file-list="fileList"
                  :data="fileparm">
                <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
              </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="批量删除" :visible.sync="dialogdeletebatchNumberVisible" width="500px" v-dialogDrag>
      <el-row>
          <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
              <!-- <el-input placeholder="请输入年月" v-model="deletefilter.batchNumber" style="width: 100%"></el-input> -->
              <el-date-picker v-model="deletefilter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
          </el-col>
          <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
            <el-button type="primary" @click="deleteByBatch">删除</el-button>
          </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogdeletebatchNumberVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="计算分摊" :visible.sync="dialogcomputVisible" width="600px" v-dialogDrag>
      <el-row>
         <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
           <el-select filterable v-model="computfilter.version" placeholder="类型">
              <el-option label="工资月报" value="v1"></el-option>
              <el-option label="参考月报" value="v2"></el-option>
           </el-select>
         </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-date-picker style="width: 100%" v-model="computfilter.yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-col>
        <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
          <el-input-number v-if="computfilter.shareFeeType==11" v-model="computfilter.amont" :step="1" style="width: 100%"></el-input-number>
        </el-col>
         <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6">
           <el-button type="primary" @click="oncomput">计算分摊</el-button>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogcomputVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </container>
</template>

<script>
import {importPurchasePickUp,importFreightfee,importShootFeeEntity,importSampleFeeBX,importSampleFeeMG,importSampleFeeYY,
        importMachinefee,importOffLinefee,importStoreLossfee,deleteProductCost,computProductCost} from '@/api/financial/productcost'
import purchasepickup from '@/views/financial/productcost/purchasepickup'
import samplefeebx from '@/views/financial/productcost/samplefeebx'
import purchaseOrderManage from '@/views/financial/productcost/purchaseOrderManage'
import purchaseReturn from '@/views/financial/productcost/purchaseReturn'
import samplefeemg from '@/views/financial/productcost/samplefeemg'
import samplefeeyy from '@/views/financial/productcost/samplefeeyy'
import compute from '@/views/financial/productcost/compute'
import monthsumfee from '@/views/financial/productcost/monthsumfee'
import shootfee from '@/views/financial/productcost/shootfee'
import purchasefreight from '@/views/financial/productcost/purchasefreight'
import offlinefee from '@/views/financial/productcost/offlinefee'
import storelossfee from '@/views/financial/productcost/storelossfee'
import container from '@/components/my-container/nofooter'
export default {
  name: 'Roles',
  components: {container,samplefeebx,samplefeeyy,samplefeemg,shootfee,purchasepickup,compute,monthsumfee,purchasefreight,offlinefee,storelossfee, purchaseOrderManage, purchaseReturn},
  data() {
    return {
      activeName: 'first',
      filter: {
        startTime: null,
        endTime: null,
        companyId:null,
        warehouse:null,
        timerange:null,
        timerange1:null
      },
      deletefilter: {shareFeeType:0,yearmonth:'' },
      computfilter: {version:'v1',shareFeeType:0,yearmonth:'',amont:null},
      onimportfilter: {shareFeeType:0 ,yearmonth:'' },
      expresscompanylist: [],
      pageLoading: false,
      dialogVisible: false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      dialogdeletebatchNumberVisible:false,
      dialogcomputVisible:false
    }
  },
  mounted() {

  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
    onSearch() {
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      if (this.filter.timerange1) {
        this.filter.startImpotTime = this.filter.timerange1[0];
        this.filter.endImpotTime = this.filter.timerange1[1];
      }
      if (this.activeName=='first') this.$refs.purchasepickup.onSearch();
      else if (this.activeName=='purchasefreight') this.$refs.purchasefreight.onSearch();
      else if (this.activeName=='machinegz') this.$refs.machinegz.onSearch();
      else if (this.activeName=='offlinefee') this.$refs.offlinefee.onSearch();
      else if (this.activeName=='storelossfee') this.$refs.storelossfee.onSearch();
      else if (this.activeName=='third') this.$refs.shootfee.onSearch();
      else if (this.activeName=='third1') this.$refs.samplefeebx.onSearch();
      else if (this.activeName=='samplefeemg') this.$refs.samplefeemg.onSearch();
      else if (this.activeName=='samplefeeyy') this.$refs.samplefeeyy.onSearch();
      else if (this.activeName=='six') this.$refs.compute.onSearch();
      else if (this.activeName=='seven') this.$refs.productmonthreport.onSearch();
    },
   async onstartImport(shareFeeType){
      this.dialogVisible=true;
      this.onimportfilter.shareFeeType=shareFeeType;
    },
   async ondeleteByBatch(shareFeeType) {
      this.dialogdeletebatchNumberVisible=true;
      this.deletefilter.shareFeeType=shareFeeType;
      this.deletefilter.yearmonth='';
    },
  async onstartcomput(shareFeeType) {
      this.dialogcomputVisible=true;
      this.computfilter.shareFeeType=shareFeeType;
      this.computfilter.yearmonth='';
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.onimportfilter.yearmonth) {
       this.$message({type: 'warning',message: '请选择年月!'});
       return;
      }
      this.uploadLoading=true
      this.$refs.upload.submit();
    },
   async uploadFile(item) {
     if(!item||!item.file||!item.file.size){
        this.$message({message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearmonth", this.onimportfilter.yearmonth);
      var res;
      if (this.onimportfilter.shareFeeType==0) res= await importPurchasePickUp(form);
      else if (this.onimportfilter.shareFeeType==1)res= await importDingDingExamineFee(form);
      else if (this.onimportfilter.shareFeeType==2) res=await importShootFeeEntity(form);
      else if (this.onimportfilter.shareFeeType==6)res= await importSampleFeeYY(form);
      else if (this.onimportfilter.shareFeeType==7)res= await importSampleFeeMG(form);
      else if (this.onimportfilter.shareFeeType==5)res= await importFreightfee(form);
      else if (this.onimportfilter.shareFeeType==4)res= await importSampleFeeBX(form);
      else if (this.onimportfilter.shareFeeType==11)res= await importMachinefee(form);
      else if (this.onimportfilter.shareFeeType==10)res= await importStoreLossfee(form);
      else if (this.onimportfilter.shareFeeType==9)res= await importOffLinefee(form);

      if (res.code==1) this.$message({message: "上传成功,正在导入中...", type: "success" });
      else this.$message({message: res.msg, type: "warning" });
      this.uploadLoading=false
    },
  async deleteByBatch() {
      if (!this.deletefilter.yearmonth) {
       this.$message({type: 'warning',message: '请输入月份!'});
       return;
      }
      this.$confirm('确认删除, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await deleteProductCost(this.deletefilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
  async oncomput(){
      if (!this.computfilter.yearmonth) {
        this.$message({type: 'warning',message: '请选择年月!'});
        return;
      }
      this.$confirm('确认计算分摊, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
        }).then(async () => {
            const res = await computProductCost(this.computfilter)
            if (!res?.success) {return }
            this.$message({type: 'success',message: '提交成功,正在后台计算分摊...'});
            //this.onSearch()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消计算'});
        });
    },
  async ondownloadmb(name) {
       var alink = document.createElement("a");
       alink.href =`../../static/excel/financial2/productcost/${name}.xlsx`;
       alink.click();
    }
  }
}
</script>
