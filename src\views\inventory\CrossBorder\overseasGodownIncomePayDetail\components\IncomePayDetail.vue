<template>
  <MyContainer>
    <template #header>
      <el-form :inline="true" ref="topForm" :model="queryEnum" class="demo-ruleForm">
        <el-form-item prop="timeRanges">
          <el-date-picker v-model="queryEnum.timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px;margin-right: 5px;" :clearable="true"
            :value-format="'yyyy-MM-dd'">
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="warehouseType">
          <el-select v-model="queryEnum.warehouseType" placeholder="平台" clearable filterable>
            <el-option v-for="item in platformTypeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button type="primary" @click="onExport">导出</el-button>
          <el-button type="primary" @click="onCalculate">计算</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template>
      <vxetablebase :id="'IncomePayDetail20241106'" :tablekey="'IncomePayDetail20241106'" :tableData='tableData'
        :tableCols='tableCols' @sortchange='sortchange' :loading='loading' :border='true' :that="that" ref="vxetable"
        :showsummary='true' :summaryarry="summaryarry">
      </vxetablebase>
    </template>

    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>

    <!-- 人民币充值余额 修改 -->
    <el-dialog title="人民币充值余额" center custom-class="formCenter" :visible.sync="RMBBalanceVisible" width="20%"
      v-dialogDrag>

      <el-form ref="" @submit.native.prevent>
        <el-form-item prop="RMBBalance" label="余额">
          <el-input style="width:230px" placeholder="请输入数字，且最多只可四位小数" clearable v-model="RMBBalance" :maxlength="20"
            show-word-limit></el-input>
        </el-form-item>
      </el-form>

      <template slot="footer">
        <el-button @click="RMBBalanceVisible = false">取消</el-button>
        <el-button @click="saveEdit" type="primary">保存</el-button>
      </template>
    </el-dialog>


    <el-dialog title="计算平台收支明细" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">

      <el-date-picker v-model="CalculateTimeRanges" type="daterange" unlink-panels range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px; margin-right: 5px;" :clearable="true"
        :value-format="'yyyy-MM-dd'" :picker-options="pickerOptions">
      </el-date-picker>

      <el-select v-model="warehouseType" placeholder="平台" clearable>
        <el-option v-for="item in platformTypeList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="getCalculate">确 定</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs';
import { formatTime } from "@/utils";
import { PageFeesOverWarehouseAsync, UpdateRateAsync, ExportFeesOverWarehouseAsync, calcKJWarehouseByDate } from "@/api/bookkeeper/crossBorderV2.js";



const tableCols = [
  { istrue: true, sortable: 'custom', prop: 'warehouseType', label: '平台', width: '70', formatter: (row, that) => that.platformTypeList.find((item) => item.value == Number(row.warehouseType)).label },
  { istrue: true, sortable: 'custom', prop: 'dateTime', label: '日期', width: 'auto' , formatter: (row) => {return row.dateTime ? formatTime(row.dateTime, "YYYY-MM-DD") : "";},},
  { istrue: true, sortable: 'custom', prop: 'createdTime', label: '计算时间', width: 'auto' },
  { istrue: true, sortable: 'custom', prop: 'exchangeRate', label: '汇率', width: '60' },
  {
    istrue: true, rop: '', label: '期初余额', merge: true, prop: 'mergeField1',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'initUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'initRMB', label: '人民币', width: '70' }, 
    ]
  }, {
    istrue: true, rop: '', label: '充值', merge: true, prop: 'mergeField2',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'rechargeUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'rechargeRMB', label: '人民币', width: '70', type: 'clickLink', style: (that, row) => { if ((row.warehouseType == 1 || row.warehouseType == 5  || row.warehouseType == 6) && row.rechargeUSD) { return { color: '#409EFF', cursor: 'pointer' } } }, handle: (that, row) => { if ((row.warehouseType == 1 || row.warehouseType == 5 || row.warehouseType == 6) && row.rechargeUSD) { that.RMBBalanceVisible = true; that.currentRow = row; that.RMBBalance = row.rechargeRMB } },
     formatter: (row) => { if (row.rechargeUSD && (row.warehouseType == 1 || row.warehouseType == 5 || row.warehouseType == 6) && !row.rechargeRMB) { return '编辑' } } },
    ]
  }, {
    istrue: true, rop: '', label: '收入', merge: true, prop: 'mergeField3',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'incomeUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'incomeRMB', label: '人民币', width: '70' },
    ]
  }, {
    istrue: true, rop: '', label: '支出', merge: true, prop: 'mergeField4',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'outlayUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'outlayRMB', label: '人民币', width: '70' },
    ]
  }, {
    istrue: true, rop: '', label: '期末余额', merge: true, prop: 'mergeField5',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'overUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'overRMB', label: '人民币', width: '70' },
    ]
  }, {
    istrue: true, rop: '', label: '验算1', merge: true, prop: 'mergeField6', tipmesg: '验算1公式=期初余额+充值+收入-支出-期末余额',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'checkUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'checkRMB', label: '人民币', width: '70' },
    ]
  }, {
    istrue: true, rop: '', label: '消耗', merge: true, prop: 'mergeField7',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'consumeUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'consumeRMB', label: '人民币', width: '70' },
    ]
  }, {
    istrue: true, rop: '', label: '费用调整', merge: true, prop: 'mergeField8', tipmesg: '费用调整=支出-收入-消耗',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'adjustmentUSD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'adjustmentRMB', label: '人民币', width: '70' },
    ]
  }, {
    istrue: true, rop: '', label: '验算2', merge: true, prop: 'mergeField9', tipmesg: '验算2公式=期初余额+充值-消耗-费用调整-期末余额',
    cols: [
      { istrue: true, sortable: 'custom', prop: 'check2USD', label: '美元', width: '70', },
      { istrue: true, sortable: 'custom', prop: 'check2RMB', label: '人民币', width: '70' },
    ]
  },


];

export default {
  name: 'IncomePayDetail',
  components: { vxetablebase, MyContainer, },
  data() {
    return {
      that: this,
      queryEnum: {
        timeRanges: [],
        orderBy: '',
        isAsc: true,
      },

      tableData: [
      ],
      tableCols: tableCols,
      loading: false,
      summaryarry: {},
      total: 0,
      financialTypeList: [
        '充值', '收入', '支出'
      ],
      platformTypeList: [
        {
          value: 1,
          label: '佳速达'
        },
        {
          value: 2,
          label: '九方'
        },
        {
          value: 5,
          label: '左海'
        },
        {
          value: 4,
          label: '赤道'
        },
        {
          value: 6,
          label: '环世'
        },
      ],
      //人民币充值余额
      RMBBalanceVisible: false,
      RMBBalance: '',
      currentRow: null,
      dialogVisible: false,
      CalculateTimeRanges: [],
      warehouseType: null,
      pickerOptions: {
        disabledDate(time) {
          // 禁止选择当前日期之后的时间
          return time.getTime() > Date.now() - 86400000;
        }
      },
    };
  },
  async mounted() {
    await this.getList()

  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.$refs.pager.setPage(1)
      }
      let pager = this.$refs.pager.getPager()
      let Enum = { ...this.queryEnum };
      if (this.queryEnum.timeRanges) {
        Enum.start = this.queryEnum.timeRanges[0];
        Enum.end = this.queryEnum.timeRanges[1];
      }
      console.log("1212121", this.queryEnum.timeRanges)
      let params = {
        ...pager,
        ...Enum

      }
      this.loading = true
      const { data } = await PageFeesOverWarehouseAsync(params)
      this.loading = false
      this.tableData = data.list
      // this.tableData = this.tableData.filter(item=>item.warehouseType == 2)
      this.tableData.forEach(item => {
        if (item.warehouseType == 2 || item.warehouseType == 4) {
          item.initUSD = null
          item.rechargeUSD = null
          item.incomeUSD = null
          item.outlayUSD = null
          item.overUSD = null
          item.checkUSD = null
          item.consumeUSD = null
          item.adjustmentUSD = null
          item.check2USD = null
        }
      })
      this.total = data.total
      this.summaryarry = data.summary
      if (this.queryEnum.warehouseType && (this.queryEnum.warehouseType == 4 || this.queryEnum.warehouseType == 2)) {
        delete this.summaryarry.initUSD_sum
        delete this.summaryarry.rechargeUSD_sum
        delete this.summaryarry.incomeUSD_sum
        delete this.summaryarry.outlayUSD_sum
        delete this.summaryarry.overUSD_sum
        delete this.summaryarry.checkUSD_sum
        delete this.summaryarry.consumeUSD_sum
        delete this.summaryarry.adjustmentUSD_sum
        delete this.summaryarry.check2USD_sum
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.queryEnum.orderBy = prop
        this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },

    // 导出
    async onExport() {
      this.loading = true
      let Enum = { ...this.queryEnum }
      if (this.queryEnum.timeRanges) {
        Enum.start = this.queryEnum.timeRanges[0];
        Enum.end = this.queryEnum.timeRanges[1];
      }
      const { data } = await ExportFeesOverWarehouseAsync(Enum)
      this.loading = false
      if (data.success) {
        this.$message({ message: data.msg, type: "success" })
      }
    },

    //人民币充值余额 
    // 保存编辑人民币充值余额
    async saveEdit() {
      let regExp = /(^[1-9]\d*(\.\d{1,4})?$)|(^0(\.\d{1,4})?$)/
      // console.log(/(^[1-9]([0-9]+)?(\.[0-9]{1,})?$)|(^(0){1}$)|(^[0-9]\.[0-9]{1,}?$)/.test(-0.333))
      if (!regExp.test(this.RMBBalance) || String(this.RMBBalance) == '0') {
        this.$message({ message: "内容格式错误;请输入数字不为零且至多只可有四位小数", type: "warning" });
        return
      }
      // this.RMBBalance 调接口保存修改
      const res = await UpdateRateAsync({ rechargeRMB: this.RMBBalance, id: this.currentRow.id })
      if (res.success) {
        this.$message({ message: "修改成功", type: "success" });
      }
      this.getList()
      this.RMBBalanceVisible = false
    },

    async onCalculate() {
      // if (this.queryEnum.timeRanges) {
      //   this.CalculateTimeRanges = this.queryEnum.timeRanges
      // } else {
      //   this.CalculateTimeRanges = []
      // }



      this.dialogVisible = true;



      if (this.queryEnum.timeRanges) {
        const currentDateTime = dayjs().format('YYYY-MM-DD'); // 获取当前时间的 dayjs 对象
        const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD'); // 获取昨天的日期字符串
        const startTime = this.queryEnum.timeRanges[0];
        const endTime = this.queryEnum.timeRanges[1];


        this.CalculateTimeRanges = this.queryEnum.timeRanges.slice();
        if (startTime >= currentDateTime) {
          this.CalculateTimeRanges[0] = yesterday
        }
        if (endTime >= currentDateTime) {
          this.CalculateTimeRanges[1] = yesterday
        }
      } else {
        this.CalculateTimeRanges = [];
      }

      if (this.queryEnum.warehouseType) {
        this.warehouseType = this.queryEnum.warehouseType
      } else {
        this.warehouseType = null
      }
    },
    async getCalculate() {
      // 验证日期范围和平台类型是否为空
      if (!this.CalculateTimeRanges || this.CalculateTimeRanges.length !== 2) {
        this.$message.warning('请选择日期范围');
        return;
      }
      if (!this.warehouseType) {
        this.$message.warning('请选择平台');
        return;
      }

      var param = {
        start: this.CalculateTimeRanges[0],
        end: this.CalculateTimeRanges[1],
        warehouseType: this.warehouseType
      }

      this.loading = true; // 开始请求前显示加载状态

      try {
        const { data, success } = await calcKJWarehouseByDate(param);
        this.loading = false; // 请求结束后隐藏加载状态

        if (success) {
          this.$message({ message: "计算成功", type: "success" });
          this.dialogVisible = false;
        } else {
          // this.$message.error("计算失败，请重试");
          // this.dialogVisible = false;
        }

        this.getList(); // 请求结束后刷新列表
      } catch (error) {
        this.loading = false; // 请求失败后隐藏加载状态
        this.$message.error("计算请求失败，请检查网络连接");
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => { });
    }

  }
};
</script>

<style lang="scss" scoped>
::v-deep .formCenter .el-form-item {
  display: flex;
  justify-content: center;
}
</style>