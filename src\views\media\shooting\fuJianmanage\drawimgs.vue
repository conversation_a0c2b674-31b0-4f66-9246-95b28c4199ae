<template>
    <div class="body">
        <my-container v-loading="pageLoading">
        <div :class="[minheight?'box':'gubox']" v-for="(item,i) in list" :key="i" @click="indexval(i)">
            <div class="imgbox">
                <div style="position: absolute; top: 0; left: 0; padding: 30px;">
                    <div style="width: 100%; height: 100%; position: relative; display: flex; flex-direction: column;" class="flexcenter">
                        <div ref="replyInput" tabindex="0" :id="'replyInput'+i" @paste="changeContent($event,i)"  :contenteditable="ispaste" spellcheck="false"
                                    @input="onDivInput($event,i)"  :placeholder="placetext" class="pastimg">
                        </div>
                        <img crossorigin="anonymous" v-show="item.url" :src="item.url" :id="'imgid'+i" alt="" style="width: auto; height: auto; max-width: 100%; max-height: 100%; top:50%;left:50%;position: absolute;transform: translate(-50%,-50%);">
                        <div style="top:50%;left:50%;position: absolute;transform: translate(-50%,-50%);min-width: 10px;min-height: 10px;" v-if="draw">
                            <canvas :id="'drawcanvas'+i" :class="{point:drawnum==i}" ></canvas>
                        </div>
                        <div style="top:50%;left:50%;position: absolute;transform: translate(-50%,-50%);min-width: 10px;min-height: 10px;" v-if="draw" class="canvasinput">
                            <canvas :id="'clipcanvas'+i" :class="{point:drawnum==i}"></canvas>
                        </div>

                        <div style="position: absolute; top: 101%; left: 0; width: 100%;">
                            <el-input
                                type="textarea"
                                :disabled="!ispaste"
                                :autosize="{ minRows: 1, maxRows: 3}"
                                placeholder="请输入内容"
                                v-model="item.markinfo"
                                @change="inputchange">
                            </el-input>
                        </div>
                        
                        <div style="top:-8%;right:0;position: absolute;" v-if="btnshow">
                            <el-button type="primary"  @click="tomessage(i)">点评</el-button>
                            <el-button type="primary" plain  @click="deletecanvas(i)">取消</el-button>
                            <el-button type="danger"  @click="delemodule(i)">删除</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="msgbox">
                要求:
                <div style="margin-top: 10px;overflow-x: hidden;width: 450px;" :class="[minheight?'':'overfolw']">
                    <div v-for="(itemm,inn) in item.marks" :key="inn" style="width: 100%;">
                        <div class="flexrow" style="width: 450px;">
                            <i v-if="btnshow" class="el-icon-circle-close" style="line-height: 20px; z-index: 199; color: red;" @click="delmsaage(i,inn)"></i>
                            <div style="word-wrap: break-word; width: 400px;">{{itemm.markinfo}}</div>
                        </div>
                        <img :src="itemm.url" :style="btnshow?{marginLeft: '15px'}:''" style="width: auto; height: auto; max-width: 100%; max-height: 100%;"  alt="">
                    </div>
                </div>
                
            </div>
        </div>

        <el-dialog
        title="请输入保存参考名称"
        :visible.sync="dialogVisible"
        append-to-body
        width="30%">
        <el-input
        placeholder="请输入保存参考名称"
        v-model="titleval">
        </el-input>
        <el-row>
            <el-col :span="22"><div class="grid-content bg-purple-dark"></div></el-col>
            <el-col :span="2"><div class="grid-content bg-purple-dark"><el-button style="margin-left: auto;" type="primary" @click="finsubmit">保存</el-button></div></el-col>
        </el-row>
        </el-dialog>
    </my-container>
    </div>
</template>

<!-- <script src="https://cdn.bootcss.com/html2canvas/0.5.0-beta4/html2canvas.js"></script> -->
<script>
import MyContainer from "@/components/my-container";
import drawImg from '@/views/media/shooting/fuJianmanage/drawimgs'
import { pageReferenceViewTaskAsync,getReferenceMainReferencs,saveReferenceMainReferencsForVedio,saveReferenceMainReferencsForImg,saveReferenceMainReferencsForSku} from '@/api/media/referencemanage';

// import {getReferenceMainReferencs,saveReferenceMainReferencs,saveReferenceMainReferencsForImg} from '@/api/media/referencemanage';
export default {
    name: 'Shootingcreateindex1',
    props:{
        name: {type: String,default: ''},
        btnshow: {type: Boolean,default: true},
        minheight: {type: Boolean,default: true},
        ispaste: {type: Boolean,default: true},
        bannum: {type: Number,default: 1},
        sku: {type: Array,default: function() {
			return []
		}},
        alllist: {type: Array,default: function() {
			return []
		}},
        listid: {default: 0},
        main: {type: Object,default: null},
    },
    components: {drawImg,MyContainer},
    data() {
        return {
            pageLoading: false,
            listidd: null,
            skuu: null,
            bigimg: '',
            titleval: '',
            dialogVisible: false,
            istomessage: false,
            drawnum: -1,
            imgUrl: '',
            tomsg: [],
            cutImgSrc: '',
            sizeinput2: '',
            draw:false,
            isshow: false,
            startdemo: null,
            placetext: '请按Ctrl+v粘贴图片...',
            list:[],
            canvasimg: [],
            img: 'https://gw.alicdn.com/bao/uploaded/i3/430490406/O1CN01d8S7Kg1ErzbK1VpW9_!!430490406.jpg_Q75.jpg_.webp',
            watname: '',
            input: '',
            sizeinput: '',
            num: -1,
            beiinput: '',
            demohtml: [],
            textarea2: null,
            tableData: [],
            urlimg: null,
            mainTask: null,
            indexx: null,
            listname: '任务名称',
        };
    },
    watch: {
        sku: 'skuchange',
        alllist: 'alllistchange'
    },
    mounted() {
        if(this.name){
            this.watname = this.name;
        }
        if(this.sku){
            this.skuu = this.sku;
        }
    },

    methods: {
        delmsaage(i,ind){
            this.list[i].marks.splice(ind,1)
            this.$emit("ischange",true)
        },
        delemodule(index){
            this.list.splice(index,1)
            this.$emit('ischange',true);
        },
        alllistchange(val){
            if(val){
            let _this = this;
            _this.alllist.map((item)=>{
                if(!item.marks){
                    item.marks=[];
                }
            })
            _this.list = _this.alllist;
            // _this.list = _this.alllist;
            console.log("组件接收值打印结果",_this.list)
        }
        },
        skuchange(val){
            // console.log("tableone11111",this.tableone);
            // console.log("tabletwo22222",this.tabletwo);
            let _this = this;
            _this.skuu = val;
            console.log("111最终值sku",val)
            // console.log("sku保存",_this.sku)
        },
        indexval(val){
            this.indexx = val
        },
        inputchange(val){
            let _this = this;
            _this.list[_this.indexx].markinfo = val;
            _this.$emit('ischange',true);
        },
        submitt(){
            let _this = this;
            _this.dialogVisible = true;
        },
        async tosubmitt(){
            let _this = this;
            _this.mainTask = {
                referenceManageTaskId: _this.listid? _this.listid:0,
                taskName: _this.titleval?_this.titleval:_this.main.taskName,
            };
            await _this.getlist();
            await _this.$emit('getalllist');
        },
        async finsubmit(){
            let _this = this;
            _this.mainTask = {
                referenceManageTaskId: 0,
                taskName: _this.titleval?_this.titleval:_this.main.taskName,
            };
            await this.getlist();
            await _this.$emit('getalllist',_this.listidd);
        },
        async getlist(){
            let _this = this;
            let params = {
            type: _this.bannum,
            mainTask: _this.mainTask,
            photoInfo: _this.list,
            sku: _this.skuu?_this.skuu:[]
        }

        // for(var i = 0; i<_this.list.length; i++){
        //     console.log("_this.list",_this.list[i].url)
        //     if(_this.list[i].url==''){
        //         this.$message("内容不能为空，请输入再保存！")
        //         return
        //     }
        // }
        _this.pageLoading= true;
           let res = await saveReferenceMainReferencsForSku(params);
           if(res.success){
                _this.$message.success({
                    message: "保存成功！",
                    offset: 150,
                    duration: 2000
                })
                _this.listidd = res.data.referenceManageTaskId;
                _this.dialogVisible = false;
           }else{
                _this.$message("保存失败:"+res.msg);
            }
            _this.pageLoading= false;
        },
        addlist(){
            let _this = this;
            let param = {
                fileName: '',
                filePath: '',
                url: '',
                type: _this.bannum,
                markinfo: '',
                referenceManageTaskId: _this.listid? _this.listid:0,
                marks: [
                ],
            }
                this.list.push(param);
        },
        changeContent(e,index){
            let _this = this;
            _this.draw = true;
            const dataTransferItemList = e.clipboardData.items;
            // 过滤非图片类型
            const items = [].slice.call(dataTransferItemList).filter(function (item) {
                return item.type.indexOf('image') !== -1;
            });
            if (items.length === 0) {
                this.$message('请粘贴正确图片');
                return;
            }
                if(_this.istomessage){
                    _this.draw = false;
                    // this.$message('请先取消，再粘贴图片操作！');
                    // return;
                }
                _this.$emit('ischange',true);
                const dataTransferItem = items[0];
                const file = dataTransferItem.getAsFile();
                //请求图片接口
                _this.uploadToServer(file, (res) => {
                    // _this.canvasimg.push(res.data.url);
                    _this.bigimg = res.data;
                    _this.list[index].fileName= res.data.fileName;
                    _this.list[index].filePath= res.data.relativePath;
                    _this.list[index].url= res.data.url;
                    // _this.list[index] = {...res.data}
                })


                var event = event || window.event;
                // var file = event.target.files[0];
                var reader = new FileReader(); 
                reader.onload = function(e) {
                    _this.canvasimg[index] = e.target.result
                }
                reader.readAsDataURL(file);
            
        },
        onDivInput(e,index) {
                let _this = this;
                if(e.target.innerText){
                    e.target.innerText=''
                    return;
                }
                    let filterImgContent = e.target.innerHTML.replace(/\<img/g,"<img style='width:50px;height:50px;'")
                    this.bingval = filterImgContent;
                    _this.demohtml = filterImgContent.match(/<img.*?>/g);
                    
                    let DomList=document.getElementById('replyInput'+index).querySelectorAll('img')
                    for(let i in  DomList){
                            if( DomList[i].style){
                                DomList[i].style.width='auto'
                                DomList[i].style.height='auto'
                                DomList[i].style.maxWidth = '100%'
                                DomList[i].style.maxHeight = '100%'
                                DomList[i].id = "imgid"+index

                                DomList[i].onmousedown = function(e){
                                    // console.log("index值",index)
                                    _this.inputcli(index);
                                };
                                // DomList[i].style.
                                // DomList[i].mode = 'aspectFit'
                                // DomList[i].style= 'cover';
                            }
                        }
                        



                let demo = document.getElementById('replyInput'+index)
                // let demo = document.getElementsByClassName('pastimg')
                
                if(_this.demohtml.length>1){
                    if(DomList.item(0)==_this.startdemo){
                        demo.removeChild(DomList.item(0));
                        _this.startdemo = DomList.item(1);
                    }else{
                        demo.removeChild(DomList.item(1));
                        _this.startdemo = DomList.item(0);
                    }
                }else{
                    _this.startdemo = DomList.item(0);
                }
                
                    
        },
        async tomessage(index){
            var _this = this;
            _this.draw = true;
            await _this.$nextTick(()=>{
                _this.istomessage = true;
                _this.draw = true;
                _this.drawnum = index;
            })
            var wrap = document.getElementById("imgid"+index);
            // if(_this.demohtml.length<1){
            //     _this.inputcli(index);
            //     this.$message('请先Ctrl+v粘贴图片再操作！');
            //     return;
            // }
            var width = wrap.offsetWidth;
            var height = wrap.offsetHeight;

            var clipcanvas = document.getElementById("clipcanvas"+index);
            var drawcanvas = document.getElementById("drawcanvas"+index);
            clipcanvas.width = width;
            clipcanvas.height = height;
            // clipcanvas.style.backgroundColor = 'rgba(0,0,0,0.1)';
            drawcanvas.width = width;
            drawcanvas.height = height;

            var clipCtx = drawcanvas.getContext("2d");
            var clipImg = document.createElement("img");
            clipImg.classList.add('img_anonymous');
            clipImg.crossOrigin = "anonymous";
            // clipImg.src = _this.canvasimg[index];
            clipImg.src = _this.list[index].url?_this.list[index].url:_this.canvasimg[index];
            clipImg.width = width;
            clipImg.height = height;
            // clipImg.style.width = width;
            // clipImg.style.height = height;
            clipImg.style.zIndex = 250;
            // clipImg.mode = 'scaleToFill';
            // console.log("生成的图片",clipImg)
            var timg = clipImg.cloneNode();
            wrap.appendChild(clipImg);
            clipImg.onload = function(){
                var x = Math.floor((width - this.width)/2);
                var y = Math.floor((height - this.height)/2);
                // console.log("画图宽高",[timg.width,timg.height]);
                // console.log("this指向",this);
                clipCtx.drawImage(this,0,0,width,height);
                // clipCtx.drawImage(this,0,0,width,height,x,y,this.width,this.height);
            };

            var ctx = clipcanvas.getContext("2d");
                // console.log("2d画图",ctx)
                ctx.fillStyle = 'rgba(0,0,0,0.3)';
                ctx.strokeStyle="rgba(0,143,255,1)";

                // ctx.clearRect(0,0,width,height);
                ctx.beginPath();

                ctx.globalCompositeOperation = "source-over";
                ctx.fillRect(0,0,width,height);
                
                var start = null;
                var clipArea = {};//裁剪范围


                clipcanvas.onmousedown = function(e){
                    start = {
                        x:e.offsetX,
                        y:e.offsetY
                    };
                };
                clipcanvas.onmousemove = function(e){
                    if(start){
                        fill(start.x,start.y,e.offsetX-start.x,e.offsetY-start.y)
                    }
                };
                document.addEventListener("mouseup",async function(e){
                    if(start){
                        start = null;
                        var url = startClip(clipArea);
                        let name = Date.now();

                        let bolb = await _this.dataURLtoBlob(url);
                        let file = await _this.blobToFile(bolb,name+'.png');

                        await _this.uploadToServer(file, (res) => {
                            _this.urlimg = res.data;
                            _this.cutImgSrc = res.data.url;
                        })

                        //生成图
                        // _this.cutImgSrc = url;
                        // console.log("打印截图",url);


                        //添加输入框
                        // let input = document.createElement('input');
                        // let canvasArea = document.getElementsByClassName('canvasinput')[index];
                        // // let canvasArea = document.getElementById("clipcanvas"+index)[0];
                        // canvasArea.appendChild(input);
                        // input.style.left = `${clipArea.x}px`;
                        // input.style.top = `${clipArea.y+clipArea.h}px`;
                        // input.style.border = "2px dashed blue";
                        // input.style.zIndex = 101;
                        // input.style.height = '30px';
                        // input.style.position = 'absolute';
                        // input.focus();

                        //输入框2
                        let canvasArea = document.getElementsByClassName('canvasinput')[index];

                        const oDiv = document.createElement("div");

                        oDiv.style.left = `${clipArea.x}px`;
                        oDiv.style.top = `${clipArea.y+clipArea.h}px`;
                        oDiv.style.position = 'absolute';
                        oDiv.style.zIndex = 101;
                        oDiv.id = 'inputshow'
                        // oDiv.innerHTML = `
                        // <p>${this.testfun()}</p>
                        // <button>点击弹出p的内容</button>
                        // `;
                        oDiv.innerHTML = `
                        <input
                        placeholder="请输入内容"
                        id="inputnei">
                        </input>
                        </br>
                        <button style="background-color: #409EFF; border: 0; color: white;">提交</button>
                        <button style="background-color: #F56C6C; border: 0; color: white;">取消</button>
                        `;
                        canvasArea.appendChild(oDiv);

                        var inPut = oDiv.getElementsByTagName("input")[0];
                        inPut.focus();

                        var oBtn = oDiv.getElementsByTagName("button")[0];
                        // console.log("打印事件",oBtn);
                        oBtn.onclick = function(){
                            _this.submit(index);
                        }

                        // var _this = this;        
                        document.onkeydown = function(e) {            
                        let key = window.event.keyCode;
                        if (key== 13) {
                                window.event.preventDefault() //关闭浏览器快捷键
                                _this.submit(index);
                                }
                            };


                        var offoBtn = oDiv.getElementsByTagName("button")[1];
                        // console.log("打印事件",offoBtn);
                        offoBtn.onclick = function(){
                            _this.offsubmit(index);
                        }


                        // console.log("输入框信息",input)
                    }
                });
                function fill(x,y,w,h){
                    ctx.clearRect(0,0,width,height);
                    ctx.beginPath();
                    //遮罩层
                    ctx.globalCompositeOperation = "source-over";
                    ctx.fillRect(0,0,width,height);
                    //画框
                    ctx.globalCompositeOperation = 'destination-out';
                    ctx.fillRect(x,y,w,h);
                    //描边
                    ctx.globalCompositeOperation = "source-over";
                    ctx.moveTo(x,y);
                    ctx.lineTo(x+w,y);
                    ctx.lineTo(x+w,y+h);
                    ctx.lineTo(x,y+h);
                    ctx.lineTo(x,y);
                    ctx.stroke();
                    ctx.closePath();
                    clipArea = {
                        x,
                        y,
                        w,
                        h
                    };
                }
                function startClip(area){
                    var canvas = document.createElement("canvas");
                    canvas.width = area.w;
                    canvas.height = area.h;

                    var data = clipCtx.getImageData(area.x,area.y,area.w,area.h);
                    // console.log("打印生成图片data",data);

                    var context = canvas.getContext("2d");
                    context.putImageData(data,0,0);
                    return canvas.toDataURL("image/png",1);
                }
            // _this.isshow = false;


            
        },
        inputcli(e){
            document.getElementById('replyInput'+e).focus();
        },
        deletecanvas(){
            this.draw = false;
            // this.istomessage = false;
        },
        submit(e){
            let _this = this;
            // _this.istomessage = false;
            _this.isshow = false;
            _this.isshow = true;
            // var inputvalue = document.getElementsByTagName("input")[0];
            var inputvalue = document.getElementById("inputnei");

            // _this.cutImgSrc
            _this.tomsg.value = inputvalue.value;
            _this.tomsg.url = _this.cutImgSrc;
            // let params = {};
            // params = {..._this.urlimg}
            // params.
            _this.list[e].marks.push({
                    type: _this.bannum,
                    markinfo: inputvalue.value,
                    mainPhotoId: 0,
                    fileName: _this.urlimg.fileName,
                    filePath: _this.urlimg.relativePath,
                    url: _this.urlimg.url,
                    referenceManageTaskId: 0
                    })
            this.$emit('ischange',true);
            let inputshow = document.getElementById('inputshow');
            let hoverdemo = document.getElementsByClassName('canvasinput')[e];
            hoverdemo.removeChild(inputshow);
        },
        dataURLtoBlob(dataurl) {
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
        },
        blobToFile(theBlob, fileName){
        theBlob.lastModifiedDate = new Date();
        theBlob.name = fileName;
        return new File([theBlob], fileName, {type: theBlob.type, lastModified: Date.now()});
        },
        offsubmit(i){
            this.draw = false;
            // console.log("取消",i)
        },
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.body{
    padding: 10px;
    position: relative;
}
.box{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.gubox{
    padding: 10px 0;
    // margin: 30px 0;
    // margin-top: -50px;
    height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.twobox{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 500px;
    display: flex;
    flex-direction: row;
}
.fourbox{
    padding: 20px;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    .bottombox{
        width: 100%;
        height: 100%;
        // border: 1px solid #aaa;
        padding: 20px 0 20px 0;
        display: flex;
        .bottombox-left{
            flex: 4;
            display: flex;
            align-items: center;
        }
        .bottombox-right{
            flex: 6;
            display: flex;
            flex-direction: column;
            .right-bot{
                flex: 8;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
            }
            .right-top{
                display: flex;
                flex-direction: row;
                flex: 2;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
                .top-right{
                    width: auto;
                    margin-left: 20px;
                }

                .top-left{
                    width: 200px;
                }

            }
        }
    }
}
.imgbox{
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 5;
    position: relative;

}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.msgbox{
    flex: 5;
    max-width: 600px;
    // padding: 50px 0;
}
.fontwei{
    font-weight: 600;
}
.flexcolumn{
    display: flex;
    flex-direction: column;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: 100%;
}
.pastimg{
    width: 400px;
    height: 400px;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
div{
    caret-color: transparent;
}
.point{
    cursor: crosshair;
}
.module{
    // background-color: #eee;
    margin-top: 30px;
    width: 100%;
    min-height: 400px;
}
.overfolw{
    height: 95%;
    overflow-y: auto;
}
</style>