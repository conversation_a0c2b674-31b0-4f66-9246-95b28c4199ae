<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='tbshGroupinquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>

                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                :value="item.shopCode">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="filter.sdate"></datepicker>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>

                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getGroupInquirsStatisticsList" />
        </template>

    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import {
    getJdInquirsStatisticsByShopListMonth
} from '@/api/customerservice/jingdonginquirs'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import { formatTime } from "@/utils";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';

const tableCols = [
    { istrue: true, prop: 'shopName', label: '店名', width: '200', sortable: 'custom' },
    {
        istrue: true, prop: '', label: '销售额', merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'totalSalesAmount', label: '总销售额(合计)', width: '120', sortable: 'custom', formatter: (row) => { return row.totalSalesAmount ? row.totalSalesAmount.toFixed(2) : 0 } },
            { istrue: true, prop: 'saleBeforeSalesAmount', label: '销售额(售前)', width: '120', sortable: 'custom', formatter: (row) => { return row.saleBeforeSalesAmount ? row.saleBeforeSalesAmount.toFixed(2) : 0 } },
            { istrue: true, prop: 'saleingSalesAmount', label: '销售额(售中)', width: '120', sortable: 'custom', formatter: (row) => { return row.saleingSalesAmount ? row.saleingSalesAmount.toFixed(2) : 0 } },
            { istrue: true, prop: 'saleAfterSalesAmount', label: '销售额(售后)', width: '120', sortable: 'custom', formatter: (row) => { return row.saleAfterSalesAmount ? row.saleAfterSalesAmount.toFixed(2) : 0 } },
        ]
    },
    {
        istrue: true, prop: '', label: '接待量', merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'totalReception', label: '总接待量(合计)', width: '120', sortable: 'custom' },
            { istrue: true, prop: 'saleBeforeReception', label: '接待量(售前)', width: '120', sortable: 'custom' },
            { istrue: true, prop: 'saleingReception', label: '接待量(售中)', width: '120', sortable: 'custom' },
            { istrue: true, prop: 'saleAfterReception', label: '接待量(售后)', width: '120', sortable: 'custom' },
        ]
    },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker, buschar },
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            filter: {
                sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
            },
            shopList: [],
            userList: [],
            groupList: [],
            tbshGroupinquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "shopName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        await this.getAllShopList();
    },
    methods: {
        async getAllShopList() {
            let shops = await getAllShopList();
            this.shopList = [];
            shops.data?.forEach(f => {
                if ( f.shopCode && f.platform == 7)
                    this.shopList.push(f);
            });
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getGroupInquirsStatisticsList();
        },
        async getGroupInquirsStatisticsList() {
            if (this.filter.sdate) {
                this.filter.startDate = this.filter.sdate[0];
                this.filter.endDate = this.filter.sdate[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            if (this.filter.startDate == null || this.filter.endDate == null) {
                this.$message({ message: '请选择时间范围', type: "warning" });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            if (params.shopCode) {
                this.shopList.forEach(f => {
                    if (f.shopCode == params.shopCode) {
                        params.shopName = f.shopName;
                    }
                });
            }
            this.listLoading = true;
            const res = await getJdInquirsStatisticsByShopListMonth(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tbshGroupinquirsstatisticslist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}</style>
