<template>
  <my-container v-loading="pageLoading">

    <!-- <div>
      <span>平台扣款趋势</span>
    </div>
    <div style="width: 100%;height:330px ">
      <span>
        <buscharheight ref="buschar1" :analysisData="showDetailVisible1.data" v-if="showDetailVisible1.data">
        </buscharheight>
      </span>
    </div> -->

    <!-- <div>
      <span>扣款趋势</span>
    </div> -->
    <div style="width: 100%;height:100% ">
      <div style="text-align: center; ">
        <el-radio-group v-model="radio" size="mini" @change="getchar()">
          <el-radio  label="0">全部</el-radio>
          <el-radio  label="1">拼多多</el-radio>
          <el-radio  label="2">天猫</el-radio>
          <el-radio  label="3">淘宝</el-radio>
          <el-radio  label="4">淘工厂</el-radio>
          <el-radio  label="6">抖音</el-radio>
          <el-radio  label="14">快手</el-radio>
        </el-radio-group>
      </div>
      <span>
        <buscharheight ref="buschar2" :analysisData="showDetailVisible2.data" v-if="showDetailVisible2.data">
        </buscharheight>
      </span>
    </div>

  </my-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import buscharheight from "@/components/Bus/buscharheight";
import { getOrderAllListChartAsync, getOrderAllDutyListChartAsync ,getOrderAllTXDutyListChartAsync,GetOrderAllDouDutyListChartAsync,GetOrderAllOtherDutyListChartAsync} from "@/api/order/orderdeductmoney"
export default {
  name: 'OrderKKEcharts',
  components: { MyContainer, buscharheight },
  props: {
    filter: {}
  },
  data () {
    return {
      radio: '0',
      showDetailVisible1: { visible: false, title: "", data: {} },
      showDetailVisible2: { visible: false, title: "", data: {} },
      pageLoading: false,
    };
  },
  async mounted () {
    // await this.onSearch();
    // console.log(this.showDetailVisible1);
    // console.log(this.showDetailVisible2);

  },
  methods: {
    async getchar () { 
      let that = this;
      let par = {...this.filter };
      if (this.radio == 0) { 
        const res = await getOrderAllListChartAsync(par).then((res) => {
        that.showDetailVisible2.visible = true;
        that.showDetailVisible2.data = res.data;
        that.showDetailVisible2.title = res.data.legend[0];
        });
        await this.$refs.buschar2.initcharts()
      }
      else if (this.radio == 1) {
        const res1 = await getOrderAllDutyListChartAsync(par).then((res) => {
          that.showDetailVisible2.visible = true;
          that.showDetailVisible2.data = res.data;
          that.showDetailVisible2.title = res.data.legend[0];
        });
        await this.$refs.buschar2.initcharts()
      }
      else if (this.radio == 6) {
        const res1 = await GetOrderAllDouDutyListChartAsync(par).then((res) => {
          that.showDetailVisible2.visible = true;
          that.showDetailVisible2.data = res.data;
          that.showDetailVisible2.title = res.data.legend[0];
        });
        await this.$refs.buschar2.initcharts()
      }
      else if (this.radio == 14) {
        const res1 = await GetOrderAllOtherDutyListChartAsync(par).then((res) => {
          that.showDetailVisible2.visible = true;
          that.showDetailVisible2.data = res.data;
          that.showDetailVisible2.title = res.data.legend[0];
        });
        await this.$refs.buschar2.initcharts()
      }
      else { 
        if (this.radio == 2)
          par.platform = 1
        else if (this.radio == 3)
          par.platform = 9
        else if (this.radio == 4)
          par.platform = 8
        const res = await getOrderAllTXDutyListChartAsync(par).then((res) => {
          that.showDetailVisible2.visible = true;
          that.showDetailVisible2.data = res.data;
          that.showDetailVisible2.title = res.data.legend[0];
        });
        await this.$refs.buschar2.initcharts()
      }
    },
    async onSearch () {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }
      // var paras = { ...this.filter };
      // var that = this;
      // const res = await getOrderAllListChartAsync(paras).then((res) => {
      //   that.showDetailVisible1.visible = true;
      //   that.showDetailVisible1.data = res.data;
      //   that.showDetailVisible1.title = res.data.legend[0];
      // });

      //await this.$refs.buschar1.initcharts()

      await this.getchar();
      await this.$refs.buschar2.initcharts()
    },
    //查询
    async getlist () {

    },


  }
};
</script>

<style lang="scss" scoped></style>
