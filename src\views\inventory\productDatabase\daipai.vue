<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item>
                    <!-- <el-input placeholder="商品编码" v-model="filter.goodsCode" clearable style="width: 300px;">
                        <el-tooltip slot="suffix" class="item" effect="dark" content="多重查询用英文逗号,分割"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input> -->
                    <el-tooltip class="item" effect="dark" content="默认为精确匹配在开始或者结尾输入*进行模糊匹配" placement="bottom">
                        <inputYunhan :key="'3'" :keys="'three'" :width="'150px'" ref="childGoodsCode"
                            v-model="filter.goodsCode" :inputt.sync="filter.goodsCode" placeholder="商品编码" :clearable="true"
                            @callback="(val) => filter.goodsCode = val" title="商品编码"></inputYunhan>
                    </el-tooltip>
                </el-form-item>
                <el-form-item>
                    <el-tooltip class="item" effect="dark" content="模糊匹配：商品名称/商品编码/供应商/创建人/库位" placement="bottom">
                        <el-input placeholder="请输入关键字" v-model="filter.keywords" clearable maxlength="100"
                            style="width: 160px"></el-input>
                    </el-tooltip>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.groupId" style="width: 100px" placeholder="运营组" :clearable="true"
                        :collapse-tags="true" filterable>
                        <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.brandId" clearable filterable placeholder="采购组" style="width: 100px"
                        :collapse-tags="true">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input placeholder="厂家名称" v-model="filter.providerName" clearable maxlength="100"
                        style="width: 160px"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.isEnabled" style="width: 100px" placeholder="商品状态" :clearable="true">
                        <el-option label="备用" :value="0" />
                        <el-option label="启用" :value="1" />
                        <el-option label="禁用" :value="-1" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input placeholder="添加人" v-model="filter.createdUserName" clearable maxlength="100"
                        style="width: 100px;"></el-input>
                </el-form-item>
                <el-form-item>
                    <!-- <el-date-picker type="date" placeholder="创建日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        v-model="filter.createdDay" style="width: 100%;" clearable></el-date-picker> -->
                    <el-date-picker size="mini" v-model="filter.daterange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" value-format="yyyy-MM-dd" end-placeholder="结束日期" style="width: 240px;"
                        :picker-options="pickerOptions" clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.isQualificationType" style="width: 90px" placeholder="资质类型"
                        :clearable="true">
                        <el-option label="已上传" :value="true" />
                        <el-option label="未上传" :value="false" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="editProduct({ id: 0 })">新增</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExportGoods()">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onGoodCodeGetSet()">编码抓取设置</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
                @cellClick='cellclick' :tableData='datalist' :tableCols='tableCols' :selectColumnHeight="'0px'"
                :isBorder="false" :treeProp="{ rowField: 'id', parentField: 'parentId' }" :hasexpandRight="true">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 历史价格 -->
        <el-dialog title="历史价格" :visible.sync="historyDialog" width="50%" element-loading-text="拼命加载中" v-dialogDrag>
            <my-container>
                <ces-table1 ref="getHistorytable" :that='that' :isIndex='true' :hasexpand='false' style="height: 500px;"
                    :tableData='historyList' :isSelection="false" :tableCols='tableColsHistory' :isSelectColumn='true'
                    :customRowStyle="customRowStyle" :selectColumnHeight="'0px'" :isBorder="false">
                </ces-table1>
                <template #footer>
                    <my-pagination ref="pagerhistory" :total="totalhistory" @get-page="getHistory" />
                </template>
            </my-container>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="historyDialog = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 操作日志 -->
        <el-dialog title="产品更新日志" :visible.sync="logListDialog" width="50%" element-loading-text="拼命加载中" v-dialogDrag>
            <my-container>
                <ces-table1 ref="logtable" :that='that' :isIndex='true' :hasexpand='false' style="height: 500px;"
                    :tableData='logList' :isSelection="false" :tableCols='tableColslog' :isSelectColumn='true'
                    :customRowStyle="customRowStyle" :selectColumnHeight="'0px'" :isBorder="false">
                </ces-table1>
                <template #footer>
                    <my-pagination ref="pagerlog" :total="totallog" @get-page="getlogList" />
                </template>
            </my-container>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="historyDialog = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 库存仓库 -->
        <el-dialog title="商品仓库" :visible.sync="purchaseDialog" width="50%" element-loading-text="拼命加载中" v-dialogDrag>
            <my-container>
                <ces-table1 ref="logtable" :that='that' :isIndex='true' :hasexpand='false' style="height: 500px;"
                    :tableData='purchaseList' :isSelection="false" :tableCols='tableColspurchase' :isSelectColumn='true'
                    :customRowStyle="customRowStyle" :selectColumnHeight="'0px'" :isBorder="false">
                </ces-table1>
            </my-container>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="purchaseDialog = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 新增/编辑 -->
        <el-dialog :title="eidtDialogTitle" :visible.sync="eidtDialog" width="50%" element-loading-text="拼命加载中"
            :close-on-click-modal="false" v-dialogDrag>
            <el-form label-width="80px" ref="editFormRef" :model="editForm" :rules="formrules">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品编码" prop="goodsCode">
                            <el-input placeholder="商品编码" v-model="editForm.goodsCode" @change="getGoodsDoc"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="商品名称" prop="goodsName">
                            <el-input placeholder="商品名称" :disabled="true" v-model="editForm.goodsName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="备注信息" prop="remark">
                            <el-select v-model="editForm.remark" style="width: 100%" placeholder="备注信息" :clearable="true"
                                @change="() => { editForm.providerName = '' }">
                                <el-option label="原供应商-仓库的货" value="原供应商-仓库的货" />
                                <el-option label="新供应商的货" value="新供应商的货" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="厂家名称" prop="providerName">
                            <el-select v-if="editForm.remark == '原供应商-仓库的货'" v-model="editForm.providerName"
                                style="width: 100%" placeholder="请选择" :clearable="true">
                                <el-option v-for="item in providerNames" :label="item" :value="item" :key="item"/>
                            </el-select>
                            <el-input v-else placeholder="新供应商" v-model="editForm.providerName" :maxlength="100"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="成本" prop="costPrice">
                            <el-input-number v-model="editForm.costPrice" :precision="2" :controls="false"
                                style="width:100%" :min="0" :max="1000000" class="append_unit" data-unit="元">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="仓库" prop="warehouseCode">
                            <el-select v-model="editForm.warehouseCode" style="width: 100%" placeholder="请选择"
                                :clearable="true" @change="onEditFormWarehouseChange" filterable>
                                <el-option v-for="item in warehouseAllList" :label="item.label" :value="item.value" :key="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="仓位" prop="warePosition">
                            <el-select v-model="editForm.warePosition" @blur="selectEnd" style="width: 100%" placeholder="请选择"
                                :remote-method="onWarePositionMethod" :clearable="true" filterable remote>
                                <el-option v-for="item in warePositionList" :label="item.label" :value="item.value" :key="item.value"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品图片" prop="pictureUrl">
                            <!-- <YhImgUpload :value.sync="editForm.pictureUrl" :limit="1" :ismultiple="true" ref="img"></YhImgUpload> -->
                            <el-image style="width: 60px; height: 60px" v-if="editForm.pictureUrl"
                                :src="editForm.pictureUrl" :preview-src-list="[editForm.pictureUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="测量重" prop="clWeight">
                            <el-input-number v-model="editForm.clWeight" :precision="2" :controls="false" style="width:80%"
                                :min="0" class="append_unit" data-unit="g" :max="1000000">
                            </el-input-number>
                            <YhImgUpload :value.sync="editForm.clWeightImgs" :limit="10" :ismultiple="true" ref="img1">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="测量长" prop="clLength">
                            <el-input-number v-model="editForm.clLength" :precision="2" :controls="false" style="width:80%"
                                :min="0" class="append_unit" data-unit="mm" :max="1000000">
                            </el-input-number>
                            <YhImgUpload :value.sync="editForm.clLengthImgs" :limit="10" :ismultiple="true" ref="img2">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="测量宽" prop="clWidth">
                            <el-input-number v-model="editForm.clWidth" :precision="2" :controls="false" class="append_unit"
                                :min="0" style="width:80%" data-unit="mm" :max="1000000">
                            </el-input-number>
                            <YhImgUpload :value.sync="editForm.clWidthImgs" :limit="10" :ismultiple="true" ref="img3">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="测量高" prop="clHeight">
                            <el-input-number v-model="editForm.clHeight" :precision="2" :controls="false" style="width:80%"
                                :min="0" class="append_unit" data-unit="mm" :max="1000000">
                            </el-input-number>
                            <YhImgUpload :value.sync="editForm.clHeightImgs" :limit="10" :ismultiple="true" ref="img4">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="测量厚" prop="clThickness">
                            <el-input-number v-model="editForm.clThickness" :precision="2" :controls="false"
                                class="append_unit" :min="0" style="width:36%" data-unit="mm" :max="1000000">
                            </el-input-number>
                            <YhImgUpload :value.sync="editForm.clThicknessImgs" :limit="10" :ismultiple="true" ref="img5">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="正反面图" prop="goodFrontBackImgs">
                            <YhImgUpload :value.sync="editForm.goodFrontBackImgs" :limit="5" :ismultiple="true"
                                ref="goodFrontBackImgs">
                            </YhImgUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="视频" style="margin-top: 10px; " prop="goodsVedio">
                    <viodeUpload :minisize="false" ref="uploadexl" :limit="1" accepttyes=".mp4" :uploadprogress="true"
                        @uploadFinish="uploadFinish" />
                </el-form-item>

                <el-form-item label="备注" prop="otherRemark">
                    <el-input placeholder="备注" v-model="editForm.otherRemark" clearable type="textarea" rows="2"
                        :maxlength="300" style="width:90%"></el-input>
                    &nbsp;<el-button type="text" @click="otherRemarkClickShow(editForm.id)"
                        v-if="!!editForm.id">备注历史</el-button>
                </el-form-item>

                <el-form-item label="资质类型" prop="qualificationType">
                    <YhImgUpload3 :value.sync="editForm.qualificationType" :isImg="false" accept=".pdf,.jpg,.jpeg,.png"
                        :limit="99" :ismultiple="true"></YhImgUpload3>
                </el-form-item>

            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="eidtDialog = false">取 消</el-button>
                    <el-button type="primary" :loading="subLoad" @click="saveGoodsDoc" v-if="editmode != 3">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!--视频播放-->
        <el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" 
            @close="closeVideoPlyer" :append-to-body="true" v-dialogDrag>
            <videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeVideoPlyer">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 销量 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 编码抓取设置 -->
        <el-dialog :title="goodCodeGetSetDialog.title" :visible.sync="goodCodeGetSetDialog.visible" width="33%" v-dialogDrag>
            <el-form class="ad-form-query" :inline="true" :model="goodCodeGetSetDialog.setData">
                <el-form-item label="一键抓取" prop="isGet" style="margin-right: 20px;">
                    <el-switch v-model="goodCodeGetSetDialog.setData.isGet" active-text="开" inactive-text="关">
                    </el-switch>
                </el-form-item>
                <el-form-item label="连续进货" prop="continuousNum" style="margin-right: 20px;">
                    <el-input-number v-model="goodCodeGetSetDialog.setData.continuousNum" :precision="0" :min="0" :max="10">
                    </el-input-number>
                </el-form-item>
                <el-form-item label="调拨开关" prop="isAllot" v-if="false">
                    <el-switch v-model="goodCodeGetSetDialog.setData.isAllot" active-text="开" inactive-text="关">
                    </el-switch>
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveGoodCodeGetSet" :loading="goodCodeGetSetDialog.loading">保存</el-button>
                <el-button @click="goodCodeGetSetDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="备注历史" :visible.sync="otherRemarkDialog.visible" width="35%" v-dialogDrag>
            <el-container style="height:300px;">
                <el-main style="height:300px;">
                    <el-table :data="otherRemarkDialog.data" :height="220">
                        <el-table-column label="序号" width="50">
                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                        </el-table-column>
                        <el-table-column prop="otherRemarkMan" label="备注人" width="100" />
                        <el-table-column prop="otherRemarkTime" label="备注时间" width="150" />
                        <el-table-column prop="otherRemarkText" label="备注内容" width="300" />
                    </el-table>
                </el-main>
            </el-container>
        </el-dialog>

    </my-container>
</template>
<script>
import inputYunhan from "@/components/Comm/inputYunhan";
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import cesTable from "@/components/VxeTable/vxetablebase.vue";
import cesTable1 from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import {
    pageGoodsDocRecordCgList, saveGoodsDocRecordCg, getGoodsDocCgProvidersDto, pageGoodsPriceList,
    pageGoodsDocRecordCgChangeLogList, getPurchaseNewPlan2ByGoodsCode,
    getTbWarehouseList, getTbWarePositionList, deleteGoodsDocRecordCg, exportGoodsDocRecordCgListGoods,
    getGoodsDocRecordCgSet, saveGoodsDocRecordCgSet, getGoodsDocRecordCgOtherRemarkListByParnetId
} from "@/api/inventory/basicgoods"
import { getPurOrderAnalysis, } from "@/api/inventory/goodscodestock"
import { formatTime } from "@/utils/tools";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import { getAllProBrand } from '@/api/inventory/warehouse'
import videoplayer from '@/views/media/video/videoplaynotdown' //播放器
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import YhImgUpload3 from "@/components/upload/yh-img-upload3.vue";
import viodeUpload from "@/views/media/shooting/uploadfile1.vue";
import buschar from '@/components/Bus/buschar'
const tableCols = [
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', sortable: 'custom', treeNode: true, width: '130', fixed: 'left', },
    { istrue: true, prop: 'goodsName', align: 'left', label: '商品名称', sortable: 'custom', width: '100', },
    { istrue: true, prop: 'pictureUrl', align: 'left', label: '商品图片', type: "images", sortable: 'custom', width: '70', },
    { istrue: true, prop: 'clWeight', align: 'left', label: '测量重', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'clWeightImgs', align: 'left', label: '测重图', width: '70', type: "images", },
    { istrue: true, prop: 'clLength', align: 'left', label: '测量长', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'clLengthImgs', align: 'left', label: '测长图', width: '70', type: "images", },
    { istrue: true, prop: 'clWidth', align: 'left', label: '测量宽', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'clWidthImgs', align: 'left', label: '测宽图', width: '70', type: "images", },
    { istrue: true, prop: 'clHeight', align: 'left', label: '测量高', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'clHeightImgs', align: 'left', label: '测高图', width: '70', type: "images", },
    {
        istrue: true, prop: 'goodsVedio', align: 'left', label: '视频', sortable: 'custom', type: 'html', width: '70',
        formatter: (row) => { return row.goodsVedio.length > 0 ? '<i class="el-icon-video-play"></i>' : '' },
    },
    {
        istrue: true, type: "button", label: '资质类型', width: '70',
        btnList: [
            { label: "查看", handle: (that, row) => that.editProduct(row, 3), ishide: (that, row) => (!row.qualificationType) },
            { label: "上传", handle: (that, row) => that.editProduct(row), ishide: (that, row) => (!!row.qualificationType) },
        ]
    },
    { istrue: true, prop: 'createdTime', align: 'left', label: '创建日期', sortable: 'custom', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD'), width: '70', },
    { istrue: true, prop: 'providerName', align: 'left', label: '厂家名称', sortable: 'custom', width: '70', },
    { istrue: true, prop: 'costPrice', align: 'left', width: '70', label: '单价', type: "click", handle: (that, row) => { that.historyparams = row.goodsCode; that.getHistory(); }, sortable: 'custom', },
    { istrue: true, prop: 'inventoryQty', align: 'left', label: '商品库存', sortable: 'custom', width: '70', type: 'click', handle: (that, row) => that.getPurchase(row.goodsCode) },
    { istrue: true, prop: 'saleQty', align: 'left', label: '销量', width: '70', sortable: 'custom', type: 'click', handle: (that, row) => that.getbirchart(row.goodsCode, 30) },
    { istrue: true, prop: 'remark', align: 'left', label: '备注信息', sortable: 'custom', width: '70', },
    {
        istrue: true, type: "button", label: '产品更新日志', width: '70',
        btnList: [
            { label: "查看", permission: "", handle: (that, row) => { that.logparams = row.id; that.getlogList(); } },
        ]
    },
    { istrue: true, prop: 'isEnabled', align: 'left', label: '商品状态', sortable: 'custom', width: '90', formatter: (row) => row.isEnabled == 0 ? '备用' : row.isEnabled == 1 ? '启用' : '禁用', },
    { istrue: true, prop: 'groupName', align: 'left', label: '运营组', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'brandName', align: 'left', label: '采购组', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'createdUserName', align: 'left', label: '添加人', sortable: 'custom', width: '80' },
    {
        istrue: true, type: "button", label: '操作', width: '90', fixed: 'right',
        btnList: [
            { label: "编辑", permission: "", handle: (that, row) => that.editProduct(row) },
            { label: "删除", permission: "", handle: (that, row) => that.deleteProduct(row) },
        ]
    }
];
const tableColslog = [
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', sortable: true, width: '140' },
    { istrue: true, prop: 'changeContent', align: 'left', label: '修改内容', sortable: true, },
    { istrue: true, prop: 'editUserName', align: 'left', label: '操作人', sortable: true, width: '100' },
    { istrue: true, prop: 'editTime', align: 'left', label: '操作时间', sortable: true, width: '140' },

];
const tableColsHistory = [
    { istrue: true, prop: 'buyNo', align: 'left', label: '采购单号', sortable: true, },
    { istrue: true, prop: 'purchaseDate', align: 'left', label: '采购日期', sortable: true, },
    { istrue: true, prop: 'supplier', align: 'left', label: '供应商', sortable: true, },
    { istrue: true, prop: 'count', align: 'left', label: '数量', sortable: true, },
    { istrue: true, prop: 'price', align: 'left', label: '单价', sortable: true, },
]
const tableColspurchase = [
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', sortable: true, },
    {
        istrue: true, prop: 'warehouseName', align: 'left', label: '仓库', sortable: true,
        formatter: (row) => { return row.warehouse == -1 ? '全仓' : row.warehouseName }
    },
    { istrue: true, prop: 'masterStock', align: 'left', label: '库存数', sortable: true, },

]
export default {
    name: "productDatabase",//商品资料库
    components: {
        MyContainer, cesTable, videoplayer, YhImgUpload, YhImgUpload3, viodeUpload, buschar, inputYunhan, cesTable1
    },
    data() {
        return {
            warePositionmy: '',
            videoDialogVisible: false,
            videoplayerReload: false,
            videoUrl: null,//视频地址
            // 历史价格
            historyDialog: false,
            historyList: [],
            totalhistory: 0,
            historyparams: null,
            tableColsHistory: tableColsHistory,
            // 库存仓库
            purchaseDialog: false,
            purchaseList: [],
            totalpurchase: 0,
            purchaseparams: null,
            tableColspurchase: tableColspurchase,
            // 操作日志
            logListDialog: false,
            logList: [],
            logparams: null,
            totallog: 0,
            tableColslog: tableColslog,
            buscharDialog: { visible: false, title: "", data: [] },
            filterchart: {
                startDate: null,
                endDate: null,
                timerange: []
            },
            eidtDialog: false,
            eidtDialogTitle: '',
            subLoad: false,
            providerNames: [],
            warehouseAllList: [],
            warePositionList: [],
            wareBeforePositionList: [],
            filter: {
                goodsCode: '',
                groupId: null,
                brandId: null,
                providerName: '',
                isEnabled: null,
                createdDay: '',
                createdUserName: '',
                keywords: '',
                daterange: [],
                createdTimeStart: '',
                createdTimeEnd: '',
                hasVedioOrImg: false,
            },
            editmode: 0,
            editForm: {
            },
            formrules: {
                goodsCode: [{ required: true, message: '请填写商品编码', trigger: 'blur' }],
                remark: [{ required: true, message: '请填写备注信息', trigger: 'blur' }],
                warehouseCode: [{ required: true, message: '请填写仓库', trigger: 'blur' }],
                warePosition: [{ required: true, message: '请填写仓位', trigger: 'blur' }],
                // clWeight: [{ required: true, message: '请填写测量重', trigger: 'blur' }],
                // goodFrontBackImgs: [{ required: true, message: '请填写正反面图', trigger: 'blur' }]
            },
            summaryarry: {},
            datalist: [
            ],
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            groupList: [],
            brandlist: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            goodCodeGetSetDialog: {
                visible: false, title: "编码抓取设置", loading: false,
                setData: { isGet: false, continuousNum: 3, isAllot: true }
            },

            otherRemarkDialog: { visible: false, title: "", data: [] },
        };
    },
    watch: {
    },
    async created() {

    },
    async mounted() {
        this.init();
        this.onSearch();
        this.getInventoryWareHouseList();
    },
    methods: {
        selectEnd(){
            this.editForm.warePosition = this.warePositionmy;
        },
        //获取仓库
        async getInventoryWareHouseList() {
            if (this.warehouseAllList.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        this.warehouseAllList.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },

        // 判断视频是否上传成功
        uploadFinish(data) {
            this.subLoad = data
        },
        // 库存
        getPurchase(goodsCode) {
            this.purchaseDialog = true;
            this.$nextTick(() => {
                getPurchaseNewPlan2ByGoodsCode({ goodsCode: goodsCode }).then(res => {
                    if (res.success) {
                        this.purchaseList = res.data;
                    }
                })
            })
        },
        //销量统计
        async getbirchart(goodsCode, number) {
            let loadingInstance = Loading.service();
            let startDate = formatTime(dayjs().subtract(number, 'day'), "YYYY-MM-DD");
            let endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
            this.filterchart.startDate = startDate;
            this.filterchart.endDate = endDate;
            Loading.service({ fullscreen: true });
            var that = this;
            const params = { goodsCode: goodsCode, day: number, timeType: 0, ...this.filterchart };
            await getPurOrderAnalysis(params).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.buscharDialog.title = '商品编码：' + goodsCode;
            });
            loadingInstance.close();
        },
        // 产品拍摄更新日志
        getlogList() {
            this.logListDialog = true;
            this.$nextTick(() => {
                var pager = this.$refs.pagerlog.getPager();
                const params = {
                    ...pager,
                    ...{ recordId: this.logparams }
                };
                pageGoodsDocRecordCgChangeLogList(params).then(res => {
                    if (res.success) {
                        this.logList = res.data.list;
                        this.totallog = res.data.total;
                    }
                })
            })

        },
        // 历史价格
        getHistory() {
            this.historyDialog = true;
            this.$nextTick(() => {
                var pager = this.$refs.pagerhistory.getPager();
                const params = {
                    ...pager,
                    ...{ keyValue: this.historyparams }
                };
                pageGoodsPriceList(params).then(res => {
                    if (res.success) {
                        this.historyList = res.data.list
                        this.totalhistory = res.data.total
                    }
                })
            })

        },
        // 重置表单
        resetForm() {
            this.editForm = {
                id: 0,
                goodsCode: '',
                providerName: '',
                clWeight: null,
                clWeightImgs: null,
                clLength: null,
                clLengthImgs: null,
                clWidth: null,
                clWidthImgs: null,
                clHeight: null,
                clHeightImgs: null,
                clThickness: null,
                clThicknessImgs: '',
                costPrice: null,
                goodsVedio: [],
                qualificationType: null,
                warehouseCode: null,
                warePosition: null,
                remark: '',
                otherRemark: '',
            }
            this.$nextTick(() => {
                this.$refs.uploadexl.setData([]);
            })
        },
        // 查询商品资料及供应商
        getGoodsDoc(goodsCode) {
            this.editForm.warePosition = "";
            this.warePositionList = [];
            this.wareBeforePositionList = [];
            if (goodsCode) {
                getGoodsDocCgProvidersDto({ goodsCode: goodsCode }).then(res => {
                    if (res.data) {
                        this.editForm.goodsName = res.data.goodsName;
                        this.editForm.pictureUrl = res.data.pictureUrl;
                        this.providerNames = res.data.providerNames;
                        this.editForm.remark = '原供应商-仓库的货';
                        this.editForm.costPrice = res.data.costPrice;
                        if (res.data.positionList.length == 1) {
                            let f = res.data.positionList[0];
                            this.editForm.warehouseCode = f.wms_co_id;
                            this.warePositionList.push({ value: f.bin, label: f.bin })
                            this.editForm.warePosition = f.bin;
                        }
                        else if (res.data.positionList.length >= 1) {
                            res.data.positionList.forEach(f => {
                                this.wareBeforePositionList.push({ wms_co_id: f.wms_co_id, sku_id: f.sku_id, bin: f.bin });
                            });
                        }
                    } else {
                        this.$message.warning(`商品编码输入错误！`)
                    }
                })
            } else {
                this.editForm.goodsName = '';
                this.editForm.pictureUrl = null;
                this.providerNames = '';
                this.editForm.remark = '';
                this.editForm.costPrice = 0;
            }
        },
        async onEditFormWarehouseChange(value) {
            this.editForm.warePosition = null;
            this.warePositionList = [];
            var f = this.wareBeforePositionList.find(f => f.wms_co_id == value);
            if (f) {
                this.warePositionList.push({ value: f.bin, label: f.bin });
                this.editForm.warePosition = f.bin;
            }
        },
        async onWarePositionMethod(value) {
            this.warePositionList = [];
            if (value) {
                let pos = await getTbWarePositionList({ warecode: this.editForm.warehouseCode, pos: value });
                if (pos?.success && pos?.data && pos?.data.length > 0) {
                    pos?.data.forEach(f => {
                        this.warePositionList.push({ value: f.bin, label: f.bin });
                    });
                }else if(pos?.data.length == 0){
                    this.warePositionmy = value;
                }
            }
        },
        // 保存商品资料
        saveGoodsDoc() {
            this.editForm.goodsVedio = '';
            let videoRes = this.$refs.uploadexl.getReturns();
            if (videoRes.data?.length > 0) {
                this.editForm.goodsVedio = videoRes.data[0].url;
            }
            this.$refs.editFormRef.validate((valid) => {
                if (valid) {
                    this.subLoad = true;
                    this.editForm.isPc = true;
                    this.editForm.warehouseName = this.warehouseAllList.find(f => f.value == this.editForm.warehouseCode)?.label;
                    saveGoodsDocRecordCg(this.editForm).then(res => {
                        if (res.success) {
                            this.$message({ type: 'success', message: '保存成功!' });
                            this.subLoad = false;
                            this.eidtDialog = false
                            this.onSearch();
                        }
                        else
                            this.subLoad = false;
                    });
                }
            });
        },
        // 显示视频播放弹窗
        playVideo(videoUrl) {
            this.videoplayerReload = false;
            this.videoplayerReload = true;
            this.videoDialogVisible = true;
            this.videoUrl = videoUrl;
        },
        // 关闭视频弹窗
        async closeVideoPlyer() {
            this.videoDialogVisible = false;
            this.videoplayerReload = false;
        },
        //设置运营组、采购组下拉
        async init() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
            var res2 = await getAllProBrand();
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
        },
        // 编辑或新增商品资料
        editProduct(row, editmode = 0) {
            this.editmode = editmode;
            this.eidtDialog = true
            if (row.id) {
                this.resetForm();
                getGoodsDocCgProvidersDto({ goodsCode: row.goodsCode }).then(res => {
                    this.providerNames = res.data.providerNames;
                })
                this.eidtDialogTitle = '编辑商品资料';
                this.$nextTick(() => {
                    if (row.warePosition) this.warePositionList = [{ label: row.warePosition, value: row.warePosition }];
                    else this.warePositionList = [];
                    this.editForm = { ...row };
                    this.editForm.goodsVedio = [];
                    if (row.goodsVedio) {
                        let vi = {
                            url: row.goodsVedio,
                            fileName: row.goodsVedio,
                            relativePath: null,
                            domain: null
                        }
                        this.editForm.goodsVedio = [vi];
                    }

                    this.$refs.uploadexl.setData(this.editForm.goodsVedio);
                })
            } else {
                this.resetForm();
                this.eidtDialogTitle = '新增商品资料'
            }
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        //获取数据
        async getDataList() {
            if (this.filter.daterange) {
                this.filter.createdTimeStart = this.filter.daterange[0];
                this.filter.createdTimeEnd = this.filter.daterange[1];
            } else {
                this.filter.createdTimeStart = null;
                this.filter.createdTimeEnd = null;
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageGoodsDocRecordCgList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        async onExportGoods() {
            if (this.filter.daterange) {
                this.filter.createdTimeStart = this.filter.daterange[0];
                this.filter.createdTimeEnd = this.filter.daterange[1];
            } else {
                this.filter.createdTimeStart = null;
                this.filter.createdTimeEnd = null;
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await exportGoodsDocRecordCgListGoods(params);
            this.listLoading = false;
            if (!res?.data) {//
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "待拍商品编码_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        cellclick(row) {
            if (row.column.property == 'goodsVedio' && row.row.goodsVedio) {
                //弹出视频播放
                this.playVideo(row.row.goodsVedio)
            }
        },

        //列表排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.self = [];
            rows.forEach(f => {
                this.self.push(f.shopDecorationTaskId);
            })
        },

        customRowStyle(row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
        deleteProduct(row) {
            this.$confirm('确定要执行删除吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deleteGoodsDocRecordCg({ id: row.id });
                if (res?.success) {
                    this.$message({ type: 'success', message: '删除成功!' });
                    this.onSearch();
                }
            }).catch(() => {
            });
        },
        async onGoodCodeGetSet() {
            this.goodCodeGetSetDialog.visible = true;
            this.$nextTick(() => {
                getGoodsDocRecordCgSet().then(res => {
                    console.log(res);
                    if (res?.success && res?.data) {
                        this.goodCodeGetSetDialog.setData = {
                            isGet: res.data.isGet,
                            continuousNum: res.data.continuousNum,
                            isAllot: res.data.isAllot,
                        };
                    }
                })
            })
        },
        async saveGoodCodeGetSet() {
            this.goodCodeGetSetDialog.loading = true;
            var res = await saveGoodsDocRecordCgSet(this.goodCodeGetSetDialog.setData);
            this.goodCodeGetSetDialog.loading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '设置成功!' });
                this.goodCodeGetSetDialog.visible = false;
            }
        },
        collapasechange(val) {
            if (val.length) {
                this.collatitle = '折叠';
                this.vxetableheight = '221';
            } else {
                this.collatitle = ' 展开';
                this.vxetableheight = '550';
            }
        },
        async otherRemarkClickShow(id) {
            this.otherRemarkDialog.visible = true;
            var res = await getGoodsDocRecordCgOtherRemarkListByParnetId({ id: id });
            if (res?.success) {
                this.otherRemarkDialog.data = res.data;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.el-form-item__content {
    .append_unit {
        position: relative;

        &::after {
            content: attr(data-unit);
            position: absolute;
            top: 0;
            right: 0;
            padding: 0 10px;
            color: #909399;
            background-color: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }
    }
}

::v-deep .vxe-table--tooltip-wrapper.theme--dark {
    z-index: 2023 !important;
}

::v-deep .el-link.el-link--primary {
    margin-left: 5px;
}

// ::v-deep .el-popup-parent--hidden>>>.v-modal{
//     z-index: 1000 !important;
// }
// ::v-deep .el-dialog__wrapper{
//     z-index: 1001 !important;
// }
</style>
