<template>
    <!-- 历史版本 -->
    <my-container>
        <template #header>
            <el-select style="margin-right:5px;" v-model="versionId" filterable laceholder="选择版本" @change="verChange">
                <el-option v-for="item in versionList" :key="item.versionId" :label="item.versionName"
                    :value="item.versionId" />
            </el-select>
            <el-button @click="deleteHis" type="danger">删除当前版本</el-button>
            <div style="float: right; margin-right: 30px;margin-top:6px;font-size:14px;color:#999;">
                <span>归档人： {{ createdUserName }}</span> <span>归档时间： {{ versionTime }}</span>
            </div>
        </template>
        <el-tabs v-model="activeName" @tab-click="tabclick">
            <el-tab-pane label="薪资核算" name="tab0" :lazy="true" style="height:80vh;">
                <mediaTotalCommissionHistory ref="mediaTotalCommissionHistory" key="mediaTotalCommissionHistory"
                    :versionId="versionId">
                </mediaTotalCommissionHistory>
            </el-tab-pane>
            <el-tab-pane label="统计列表（新品拍摄）" name="tab1" :lazy="true" style="height:80vh;">
                <shootingvideotaskoverCacleHistory ref="shootingvideotaskoverCacleHistory"
                    key="shootingvideotaskoverCacleHistory" :versionId="versionId">
                </shootingvideotaskoverCacleHistory>
            </el-tab-pane>
            <el-tab-pane label="统计列表（短视频拍摄）" name="tab6" :lazy="true" style="height:80vh; ">
                <videotasknewover ref="videotasknewoverHistory" key="videotasknewoverHistory" :platformList="platformList"
                    :warehouselist="warehouselist" :listtype="4" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :islook="true" :filter="{ isShop: 0, isTjInfo: 1, versionId: versionId }" :erpUserInfoList="userList"
                    :fpPhotoLqNameList="userList" :versionId="versionId"> </videotasknewover>
            </el-tab-pane>
            <el-tab-pane label="统计列表（微。视频）" name="tab7" :lazy="true" style="height:80vh;">
                <microvediotaskmain ref="microvediotaskmainHistory" key="microvediotaskmainHistory" :listtype="3"
                    :platformList="platformList" :warehouselist="warehouselist" :dockingPeopleList="userList2"
                    :fpPhotoLqNameList="userList2" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :versionId="versionId" :filter="{ isShop: 0, isdel: 0, IsTjInfo: 1, versionId: versionId }">
                </microvediotaskmain>
            </el-tab-pane>
            <el-tab-pane label="统计列表（直通车图）" name="tab8" :lazy="true" style="height:80vh;">
                <directimgtaskmain ref="directimgtaskmainHistory" key="directimgtaskmainHistory" :listtype="3"
                    :platformList="platformList" :warehouselist="warehouselist" :dockingPeopleList="userList2"
                    :fpPhotoLqNameList="userList2" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :filter="{ isShop: 0, isdel: 0, IsTjInfo: 1, versionId: versionId }" :versionId="versionId">
                </directimgtaskmain>
            </el-tab-pane>
            <el-tab-pane label="统计列表（日常改图）" name="tab9" style="height:80vh;">
                <changeimgtaskmain ref="changeimgtasktaskHistory" key="changeimgtasktaskHistory" :versionId="versionId"
                    :listtype="3" :platformList="platformList" :warehouselist="warehouselist" :dockingPeopleList="userList2"
                    :fpPhotoLqNameList="userList2" :taskUrgencyList="taskUrgencyList" :groupList="groupList"
                    :tablekey="'changeimgtaskcacl'" :filter="{ isShop: 0, isdel: 0, IsTjInfo: 1, versionId: versionId }">
                </changeimgtaskmain>
            </el-tab-pane>
            <el-tab-pane label="统计列表（店铺装修）" name="tab10" :lazy="true" style="height:80vh;">
                <shopdecorationtaskmain ref="shopdecorationtaskHistory" key="shopdecorationtaskHistory"
                    :versionId="versionId" :listtype="3" :role="currole" :platformList="platformList"
                    :activetypelist="activetypelist" :dockingPeopleList="userList2" :fpPhotoLqNameList="userList2"
                    :taskUrgencyList="taskUrgencyList" :groupList="groupList" :tablekey="'shopdecorationtasktaskcacl'"
                    :filter="{ isShop: 0, isdel: 0, IsTjInfo: 1, versionId: versionId }">
                </shopdecorationtaskmain>
            </el-tab-pane>
            <el-tab-pane label="统计列表（包装设计）" name="tab11" :lazy="true" style="height:80vh;">
                <packdesgintask ref="packdesgintasktaskHistory" key="packdesgintasktaskHistory"
                    :tabkey="'packdesgintaskcacl'" :listtype="5" :platformList="platformList" :warehouselist="warehouselist"
                    :dockingPeopleList="dockingPeopleList" :fpPhotoLqNameList="fpPhotoLqNameList"
                    :taskUrgencyList="taskUrgencyList" :packclasslist="packclasslist" :brandList="brandList"
                    :groupList="groupList" :filter="{ isShop: 0, isdel: 0, isCacl: 1, versionId: versionId }"
                    :versionId="versionId">
                </packdesgintask>
            </el-tab-pane>
            <el-tab-pane label="工作统计" name="tab2" :lazy="true" style="height:80vh;">
                <mediaWorkCommissionHistory ref="mediaWorkCommissionHistory" key="mediaWorkCommissionHistory"
                    :versionId="versionId">
                </mediaWorkCommissionHistory>
            </el-tab-pane>
            <el-tab-pane label="道具统计" name="tab3" :lazy="true" style="height:80vh;">
                <div style="width: 1600px;"></div>
            </el-tab-pane>
            <el-tab-pane label="义乌提成" name="tab4" :lazy="true" style="height:80vh;">
                <mediaNCCommissionHistory ref="mediaYYCommissionHistory" key="mediaYYCommissionHistory" :classType="0"
                    :versionId="versionId">
                </mediaNCCommissionHistory>
            </el-tab-pane>
            <el-tab-pane label="南昌提成" name="tab5" :lazy="true" style="height:80vh;">
                <mediaNCCommissionHistory ref="mediaNCCommissionHistory" key="mediaNCCommissionHistory" :classType="1"
                    :versionId="versionId">
                </mediaNCCommissionHistory>
            </el-tab-pane>
            <el-tab-pane label="武汉提成" name="tab12" :lazy="true" style="height:80vh;">
                <mediaNCCommissionHistory ref="mediaWHCommissionHistory" key="mediaWHCommissionHistory" :classType="2"
                    :versionId="versionId">
                </mediaNCCommissionHistory>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import mediaNCCommissionHistory from '@/views/media/shooting/adjustAccounts/commissionHistoryConfig/mediaNCCommissionHistory';
import mediaTotalCommissionHistory from '@/views/media/shooting/adjustAccounts/commissionHistoryConfig/mediaTotalCommissionHistory';
import mediaWorkCommissionHistory from '@/views/media/shooting/adjustAccounts/commissionHistoryConfig/mediaWorkCommissionHistory';
import shootingvideotaskoverCacleHistory from '@/views/media/shooting/adjustAccounts/commissionHistoryConfig/shootingvideotaskoverCacleHistory';
import videotasknewover from '@/views/media/video/videotasknewover'
import microvediotaskmain from '@/views/media/shooting/microvedio/new/microvediotaskmain';
import directimgtaskmain from '@/views/media/shooting/directImg/new/directImgtaskmain';
import changeimgtaskmain from '@/views/media/shooting/changeImg/new/changeimgtaskmain';
import shopdecorationtaskmain from '@/views/media/shooting/shopdecoration/new/shopdecorationtaskmain';
import packdesgintask from '@/views/media/shooting/packdesgin/maintasklist/packdesgintask';
import { getHistoryVersionInfo, delHistoryVersionInfo } from '@/api/media/shootingset';
import { getShootOperationsGroup, getErpUserInfoView } from '@/api/media/mediashare';//主要获取配置的仓库
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'//erp运营组
import { rulePlatform } from "@/utils/formruletools";//平台
import { ShootingVideoTaskUrgencyOptions } from "@/utils/tools";//视觉部紧急程度
export default {
    components: {
        MyContainer, vxetablebase, mediaNCCommissionHistory, mediaTotalCommissionHistory, mediaWorkCommissionHistory,
        shootingvideotaskoverCacleHistory, videotasknewover, microvediotaskmain, directimgtaskmain, changeimgtaskmain, shopdecorationtaskmain, packdesgintask
    },
    data() {
        return {
            versionId: '0',
            versionName: "",
            versionTime: null,
            createdUserName: null,
            that: this,
            listLoading: false,
            activeName: 'tab0',
            tab0isfirst: true,
            tab1isfirst: true,
            tab2isfirst: true,
            tab3isfirst: true,
            tab4isfirst: true,
            tab5isfirst: true,
            menuview: 1,
            versionList: [],
            platformList: [],//平台
            warehouselist: [],//仓库
            groupList: [],//运营组
            userList: [],//erp用户
            userList2: [],//erp用户
            activetypelist: [],
            taskUrgencyList: ShootingVideoTaskUrgencyOptions,  // 紧急程度
            brandList: [],
            packclasslist: [],
        };
    },
    //向子组件注册方法
    provide() {
        return {
        }
    },
    async created() {
        await this.getDropDownList();
    },
    async mounted() {
        //await this.initVersionInfo();
    },
    methods: {
        async getDropDownList() {
            //仓库
            var res = await getShootOperationsGroup({ type: 3 });
            this.warehouselist = res?.map(item => { return { value: item.id, label: item.label }; });
            //包装类型
            var res = await getShootOperationsGroup({ type: 12 });
            this.packclasslist = res?.map(item => { return { value: item.id, label: item.label }; });
            //品牌
            var res = await getShootOperationsGroup({ type: 13 });
            this.brandList = res?.map(item => { return { value: item.id, label: item.label }; });
            //平台
            var pfrule = await rulePlatform();
            this.platformList = pfrule.options;
            //运营组
            var res = await getDirectorGroupList();
            this.groupList = res.data?.map(item => { return { value: item.key, label: item.value }; });
            var res = await getShootOperationsGroup({ type: 9 });
            this.activetypelist = res?.map(item => { return { value: item.id, label: item.label }; });

            //erp用户
            var res = await getErpUserInfoView();
            this.userList = res || [];
            this.userList2 = res?.map(item => { return item.label; });;
        },
        async deleteHis() {
            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delHistoryVersionInfo({ versionId: this.versionId });
                if (res?.success) {
                    this.$message({ message: this.$t('操作成功'), type: 'success' });
                    this.initVersionInfo();
                }
            });
        },
        async initVersionInfo() {
            this.versionList = await getHistoryVersionInfo();
            if (this.versionList.length > 0) {
                this.versionName = this.versionList[0].versionName;
                this.versionId = this.versionList[0].versionId;
                this.versionTime = this.versionList[0].versionTime;
                this.createdUserName = this.versionList[0].createdUserName;

            }
        },
        async verChange(val) {
            for (let num in this.versionList) {
                if (this.versionList[num].versionId == val) {
                    this.versionName = this.versionList[num].versionName;
                    this.versionId = this.versionList[num].versionId;
                    this.versionTime = this.versionList[num].versionTime;
                    this.createdUserName = this.versionList[num].createdUserName;
                }
            }
            //同步刷新列表
            await this.$nextTick(() => {
                var toatla = this.$refs;

                if(this.$refs.mediaTotalCommissionHistory)
                this.$refs.mediaTotalCommissionHistory.tasklist = [];

                if(this.$refs.shootingvideotaskoverCacleHistory)
                this.$refs.shootingvideotaskoverCacleHistory.tasklist = [];

                if(this.$refs.videotasknewoverHistory)
                this.$refs.videotasknewoverHistory.tasklist = [];

                if(this.$refs.microvediotaskmainHistory)
                this.$refs.microvediotaskmainHistory.tasklist = [];

                if(this.$refs.directimgtaskmainHistory)
                this.$refs.directimgtaskmainHistory.tasklist = [];

                if(this.$refs.changeimgtasktaskHistory)
                this.$refs.changeimgtasktaskHistory.tasklist = [];

                if(this.$refs.shopdecorationtaskHistory)
                this.$refs.shopdecorationtaskHistory.tasklist = [];

                if(this.$refs.packdesgintasktaskHistory)
                this.$refs.packdesgintasktaskHistory.tasklist = [];

                if(this.$refs.mediaYYCommissionHistory)
                this.$refs.mediaYYCommissionHistory.tasklist = [];

                if(this.$refs.mediaWHCommissionHistory)
                this.$refs.mediaWHCommissionHistory.tasklist = [];

                if(this.$refs.mediaNCCommissionHistory)
                this.$refs.mediaNCCommissionHistory.tasklist = [];

                if(this.$refs.mediaWorkCommissionHistory)
                this.$refs.mediaWorkCommissionHistory.tasklist = [];
            });
        },
        async tabclick() {
            // switch(this.activeName){
            //     //薪资核算-历史
            //     case 'tab0' :
            //         if(this.tab0isfirst){
            //             this.$refs.mediaTotalCommissionHistory.onSearch();
            //             this.tab0isfirst =false;
            //         }
            //         break;
            //     //统计列表-历史
            //     case 'tab1' :
            //         if(this.tab1isfirst){
            //             await this.$nextTick(() =>{  this.$refs.shootingvideotaskoverCacleHistory.onSearch(); });
            //             this.tab1isfirst =false;
            //         }
            //         break;
            //     //工作统计-历史
            //     case 'tab2' :
            //         if(this.tab2isfirst){
            //             await this.$nextTick(() =>{  this.$refs.mediaWorkCommissionHistory.onSearch(); });

            //         }
            //         this.tab2isfirst =false;
            //         break;
            //     //道具统计-历史
            //     case 'tab3' :
            //         if(this.tab3isfirst){
            //             this.tab3isfirst =false;
            //         }
            //         break;
            //     //义乌提成-历史
            //     case 'tab4' :
            //         if(this.tab4isfirst){
            //             await this.$nextTick(() =>{
            //                 this.$refs.mediaYYCommissionHistory.onSearch();
            //             })
            //             this.tab4isfirst =false;
            //         }
            //         break;
            //     //南昌提成-历史
            //     case 'tab5' :
            //         if(this.tab5isfirst){
            //             await this.$nextTick(() =>{
            //                 this.$refs.mediaNCCommissionHistory.onSearch();
            //             })
            //             this.tab5isfirst =false;
            //         }
            //         break;
            // }
        }
    },
};
</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
}

::v-deep .vxe-table--header {
    width: 500px !important;
}
</style>

