<template>
  <div>
    <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
      <el-select v-model="ListInfo.region"
        :placeholder="ListInfo.type == '仓储' ? '仓储区域' : ListInfo.type == '办公室' ? '办公室区域' : '请选择'" class="publicCss"
        @change="onTypeMethod" multiple collapse-tags>
        <el-option v-for="item in storageAreaList" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-if="ListInfo.type == '仓储' || (ListInfo.type == '办公室' && ListInfo.menuType == '部门违纪数据')"
        v-model="ListInfo.dept" placeholder="部门" class="publicCss" clearable>
        <el-option v-for="item in warehouseDepartmentList" :key="item" :label="item" :value="item" />
      </el-select>

      <el-select v-model="ListInfo.menuType" placeholder="类型" class="publicCss"
        @change="menuTypeChangeMethod($event, 1)">
        <el-option v-for="item in menuTypeList" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-model="ListInfo.costType" placeholder="二级类型" class="publicCss"
        @change="menuTypeChangeMethod($event, 2)" v-if="ListInfo.menuType" multiple collapse-tags>
        <el-option v-for="item in menuTypeListTwo" :key="item.blockCode" :label="item.blockName"
          :value="item.blockCode" />
      </el-select>
      <el-select v-model="ListInfo.details" placeholder="三级类型" class="publicCss"
        v-if="(ListInfo.menuType == 'OA台账' || ListInfo.menuType == '总违纪情况') && threeLevelTypeDisplay" clearable>
        <el-option v-for="item in menuTypeListThree" :key="item.blockCode" :label="item.blockName"
          :value="item.blockCode" />
      </el-select>
      <el-select style="width: 90px" v-model="ListInfo.cycle1" placeholder="同比周期" @change="changTimeType(1)"
        class="publicCss" :clearable="false" collapse-tags>
        <el-option key="月度" label="月度" value="月度" />
        <el-option key="季度" label="季度" value="季度" />
      </el-select>
      <jiduTime v-show="ListInfo.cycle1 == '季度' ? true : false" @change="changegetdatajidu($event, 1)"
        v-model="ListInfo.calculateMonthArrjidu" :clearable='false'></jiduTime>
      <el-date-picker v-show="ListInfo.cycle1 == '月度' ? true : false" v-model="calculateMonth" placeholder="选择月"
        @change="changedata(1)" type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
        :value-format="'yyyy-MM'">
      </el-date-picker>
      <el-select style="width: 90px" v-model="ListInfo.cycle2" placeholder="同比周期" @change="changTimeType(2)"
        class="publicCss" :clearable="false" collapse-tags>
        <el-option key="月度" label="月度" value="月度" />
        <el-option key="季度" label="季度" value="季度" />
      </el-select>
      <jiduTime @change="changegetdatajidu($event, 2)" v-show="ListInfo.cycle2 == '季度' ? true : false"
        v-model="ListInfo.calculateMonthArrjidu2" :clearable='false'></jiduTime>
      <el-date-picker v-show="ListInfo.cycle2 == '月度' ? true : false" v-model="calculateMonth2" placeholder="选择月"
        @change="changedata(2)" type="month" style="width: 250px;margin-right: 5px;" :clearable="false"
        :value-format="'yyyy-MM'">
      </el-date-picker>
      <el-button type="primary" @click="getList('search')">查询</el-button>
    </div>
    <div style="height: 300px; width: 100%; border: 1px solid #ccc; margin-top: 10px;">
      <newcharbus v-if="oneCharts" :toolbox="{
        show: true,
        orient: 'vertical',
        left: 'right',
        top: 'center',
        feature: {
          magicType: { show: true, type: ['line', 'bar', 'stack'] },
          saveAsImage: { show: true }
        }
      }" ref="sptsxformchTyright" :thisStyle="{
        width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
      }" :analysisData="oneCharts" :extraName="extInfo.extraName" :extraColor="extInfo.extraColor" :isField="true">
      </newcharbus>
    </div>
  </div>
</template>

<script>
import newcharbus from "@/views/profit/sscManager/Aashouye/newcharbus.vue";
import jiduTime from "@/views/profit/sscManager/Aashouye/jiduTime.vue";
import yearTime from "@/views/profit/sscManager/Aashouye/yearTime.vue";
import dayjs from 'dayjs'
import {
  trendChart, monthArchivesAnalysisListValue,
  blockList, getDataListByBlock, getDeptList, getDataListByBlockNew, selectAdministrativeDataTwo
} from '@/api/people/peoplessc.js';
export default {
  components: {
    newcharbus, jiduTime, yearTime
  },
  props: {
    trendChartType: {
      type: String,
      default: 'two'
    },
    storageAreaList: {
      type: Array,
      default: () => {
        return []
      }
    },
    departmentInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    menuTypeList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      threeLevelTypeDisplay: true,
      extInfo: {
        extraColor: '',
        extraName: '',
      },
      calculateMonth2: dayjs().subtract(2, 'month').format('YYYY-MM'),
      calculateMonth: dayjs().subtract(1, 'month').format('YYYY-MM'),
      warehouseDepartmentList: [],
      menuTypeListTwo: [],
      menuTypeListThree: [],
      ListInfo: {
        cycle1: '月度',
        cycle2: '月度',
        startTime1: '',
        startTime2: '',
        type: '办公室',
        calculateMonthArr: [],
        calculateMonthArr2: [],
        calculateMonthArryear: null,
        calculateMonthArryear2: null,
        calculateMonthArrjidu: null,
        calculateMonthArrjidu2: null,
        menuType: 'OA台账',
        region: ['南昌'],
        details: '',
        costType: ['办公费用'],
        dept: '',
      },
      oneCharts: null,
    }
  },
  async mounted() {
    if (this.trendChartType == 'two') {
      this.ListInfo.cycle1 = "月度";
      this.ListInfo.cycle2 = "月度";
      this.ListInfo.calculateMonthArr = [
        dayjs().subtract(1, 'month').format('YYYY-MM'),
        dayjs().subtract(1, 'month').format('YYYY-MM')];
      this.ListInfo.calculateMonthArr2 = [
        dayjs().subtract(2, 'month').format('YYYY-MM'),
        dayjs().subtract(2, 'month').format('YYYY-MM')
      ];
    } else {
      this.ListInfo.cycle1 = "月度";
      this.ListInfo.cycle2 = "月度";
      this.ListInfo.calculateMonthArr = [
        dayjs().subtract(1, 'month').format('YYYY-MM'),
        dayjs().subtract(1, 'month').format('YYYY-MM')];
      this.ListInfo.calculateMonthArr2 = [
        dayjs().subtract(2, 'month').format('YYYY-MM'),
        dayjs().subtract(2, 'month').format('YYYY-MM')
      ];
    }
    this.menuTypeChangeMethod(this.ListInfo.menuType, 1, true)
    this.menuTypeChangeMethod(this.ListInfo.costType, 2)
    this.warehouseDepartmentList = this.ListInfo.type == '仓储' ? this.departmentInfo.storageYW : this.ListInfo.type == '办公室' ? this.departmentInfo.officeNC : [];
    await new Promise(resolve => setTimeout(resolve, 2000));
    await this.getList();
  },
  methods: {
    onTypeMethod(e) {
      this.ListInfo.dept = '';
      const warehouseMap = {
        '义乌仓': this.departmentInfo.storageYW,
        '南昌仓': this.departmentInfo.storageNC,
        '西安仓': this.departmentInfo.storageXA,
      };
      const officeMap = {
        '南昌': this.departmentInfo.officeNC,
        '深圳': this.departmentInfo.officeSZ,
        '义乌': this.departmentInfo.officeYW,
        '武汉': this.departmentInfo.officeWH,
        '1688选品中心': this.departmentInfo.officeXPZX,
      };
      let departmentList = [];
      if (this.ListInfo.type === '仓储') {
        Object.entries(warehouseMap).forEach(([key, value]) => {
          if (e.includes(key)) {
            departmentList.push(...value);
          }
        });
      } else if (this.ListInfo.type === '办公室') {
        Object.entries(officeMap).forEach(([key, value]) => {
          if (e.includes(key)) {
            departmentList.push(...value);
          }
        });
      }
      this.warehouseDepartmentList = departmentList;
    },
    async menuTypeChangeMethod(e, val, isInit = false) {
      // 如果一级类型没有，则默认取办公费用
      if (!e) {
        this.ListInfo.costType = [];
        this.ListInfo.details = '';
        this.menuTypeListTwo = [];
        this.menuTypeListThree = [];
        return;
      }
      // 如果二级类型有多个，则不显示三级类型
      if (this.ListInfo.costType.length > 1 && val == 2) {
        this.threeLevelTypeDisplay = false;
        this.ListInfo.details = '';
        return;
      } else {
        this.threeLevelTypeDisplay = true;
      }
      // 一级类型正常取，如果二级类型有多个，则取第一个
      let a;
      if (val == 1) {
        a = e;
      } else if (val == 2) {
        a = this.ListInfo.costType?.[0] || '';
      }
      const { data, success } = await getDataListByBlockNew({
        blockType: a, type: this.ListInfo.type
      })
      if (!success) return
      if (val == 1) {
        this.menuTypeListTwo = data;
        this.ListInfo.costType = [data[0].blockCode] || [];
        if (!isInit && this.ListInfo.menuType === 'OA台账' && this.ListInfo.costType) {
          const res = await getDataListByBlockNew({
            blockType: this.ListInfo.costType?.[0] || '',
            type: this.ListInfo.type
          });
          if (res.success) {
            this.menuTypeListThree = res.data;
            // this.ListInfo.details = res.data[0]?.blockCode || '';
          }
        }
      } else if (val == 2) {
        this.menuTypeListThree = data;
        // this.ListInfo.details = data[0].blockCode || '';
      }
      this.onTypeMethod(this.ListInfo.region);
      if (this.ListInfo.menuType == '总违纪情况') {
        this.menuTypeListThree = this.departmentInfo.situationType.map(item => ({
          blockCode: item,
          blockName: item
        }));
        this.ListInfo.details = '次数';
      }
      if (this.ListInfo.type == '仓储' || (this.ListInfo.type == '办公室' && this.ListInfo.menuType == '部门违纪数据')) {
        this.ListInfo.dept = this.warehouseDepartmentList ? this.warehouseDepartmentList[0] : '';
      } else {
        this.ListInfo.dept = '';
      }
    },
    //父组件传递过来的值
    adjustmentMethod(row) {
      this.ListInfo.type = row.type || '';
      if (row.type === '仓储') {
        this.ListInfo.dept = (this.departmentInfo?.storageYW?.[0]) || '';
        this.warehouseDepartmentList = this.departmentInfo.storageYW || [];
      } else if (row.type === '办公室') {
        this.ListInfo.dept = (this.departmentInfo?.officeNC?.[0]) || '';
      }
      this.ListInfo.region = row.region || [];
      this.ListInfo.menuType = Array.isArray(row.menuType) ? row.menuType[0] : this.menuTypeList ? this.menuTypeList[0] : '';
      this.ListInfo.costType = [row.costType] || [this.menuTypeListTwo[0]?.blockCode] || [];
      // this.ListInfo.details = row.details || this.menuTypeListThree[0]?.blockCode || '';
    },
    getNextQuarter(quarterStr) {
      const [year, quarter] = quarterStr.split('-').map(Number);
      if (quarter === 4) {
        return `${year + 1}-01`;
      } else {
        return `${year}-${(quarter + 1).toString().padStart(2, '0')}`;
      }
    },
    changegetdatajidu(dateStr, type) {
      if (type == 1) {
        this.ListInfo.calculateMonthArrjidu2 = this.getPreviousQuarter(this.ListInfo.calculateMonthArrjidu)
      } else {
        this.ListInfo.calculateMonthArrjidu = this.getNextQuarter(this.ListInfo.calculateMonthArrjidu2)
      }
    },
    getNextMonth(dateStr, type) {
      if (type == 1) {
        return dayjs(dateStr).subtract(1, 'month').format('YYYY-MM')
      } else {
        return dayjs(dateStr).add(1, 'month').format('YYYY-MM')
      }
    },
    changedata(type) {
      if (type == 1) {
        this.calculateMonth2 = this.getNextMonth(this.calculateMonth, 1);
      } else {
        this.calculateMonth = this.getNextMonth(this.calculateMonth2, 2);
      }
      this.$forceUpdate();
    },
    setCurrentQuarterAndPreviousQuarter() {
      const now = dayjs();
      const year = now.year();
      const month = now.month() + 1;
      let quarterCode = '';
      if (month >= 1 && month <= 3) {
        quarterCode = '01'; // Q1
      } else if (month >= 4 && month <= 6) {
        quarterCode = '02'; // Q2
      } else if (month >= 7 && month <= 9) {
        quarterCode = '03'; // Q3
      } else {
        quarterCode = '04'; // Q4
      }
      const currentQuarter = `${year}-${quarterCode}`;
      const previousQuarter = this.getPreviousQuarter(currentQuarter); // 环比
      this.ListInfo.calculateMonthArrjidu = currentQuarter;
      this.ListInfo.calculateMonthArrjidu2 = previousQuarter;
    },
    getPreviousQuarter(quarterStr) {
      const [year, quarter] = quarterStr.split('-').map(Number);
      if (quarter === 1) {
        return `${year - 1}-04`;
      } else {
        return `${year}-${(quarter - 1).toString().padStart(2, '0')}`;
      }
    },
    changTimeType(val) {
      this.ListInfo.startTime1 = null;
      this.ListInfo.startTime2 = null;
      this.calculateMonth = null;
      this.calculateMonth2 = null;
      this.ListInfo.endTime1 = null;
      this.ListInfo.endTime2 = null;
      if (this.ListInfo.cycle1 == '季度') {
        this.ListInfo.calculateMonthArr = [];
        this.ListInfo.calculateMonthArr2 = [];
        this.setCurrentQuarterAndPreviousQuarter();
        this.ListInfo.calculateMonthArryear2 = null;
        this.ListInfo.calculateMonthArryear = null;
      } else if (this.ListInfo.cycle1 == '年') {
        this.ListInfo.calculateMonthArrjidu2 = null;
        this.ListInfo.calculateMonthArrjidu = null;
        this.ListInfo.calculateMonthArr = [];
        this.ListInfo.calculateMonthArr2 = [];
      } else if (this.ListInfo.cycle1 == '月度') {
        this.ListInfo.calculateMonthArryear2 = null;
        this.ListInfo.calculateMonthArryear = null;
        this.ListInfo.calculateMonthArrjidu2 = null;
        this.ListInfo.calculateMonthArrjidu = null;
        this.calculateMonth = dayjs().subtract(1, 'month').format('YYYY-MM')
        this.calculateMonth2 = dayjs().subtract(2, 'month').format('YYYY-MM')
      }
      if (val == 1) {
        this.ListInfo.cycle2 = this.ListInfo.cycle1
      } else if (val == 2) {
        this.ListInfo.cycle1 = this.ListInfo.cycle2
      }
    },
    async getList() {
      this.getoneCharts();
    },
    getQuarterRange(quarterStr) {
      // 检查输入格式
      if (!/^\d{4}-\d{2}$/.test(quarterStr)) {
        throw new Error('输入格式错误，应为 YYYY-QQ 格式');
      }
      const [year, quarter] = quarterStr.split('-');
      // 将季度转换为月份
      const startMonth = (parseInt(quarter) - 1) * 3 + 1;
      const endMonth = startMonth + 2;
      // 格式化月份，确保是两位数
      const formatMonth = (month) => month.toString().padStart(2, '0');
      return [
        `${year}-${formatMonth(startMonth)}`,
        `${year}-${formatMonth(endMonth)}`
      ];
    },
    async getoneCharts() {
      this.oneCharts = null;
      //月度
      if (this.calculateMonth) {
        this.ListInfo.startTime1 = this.calculateMonth
        this.ListInfo.endTime1 = this.calculateMonth
      } else if (this.ListInfo.calculateMonthArryear) {
        this.ListInfo.startTime1 = this.ListInfo.calculateMonthArryear + '-01';
        this.ListInfo.endTime1 = this.ListInfo.calculateMonthArryear + '-12';
      } else if (this.ListInfo.calculateMonthArrjidu) {
        let newarr = this.getQuarterRange(this.ListInfo.calculateMonthArrjidu);
        this.ListInfo.startTime1 = newarr[0];
        this.ListInfo.endTime1 = newarr[1];
      }
      if (this.calculateMonth2) {
        this.ListInfo.startTime2 = this.calculateMonth2
        this.ListInfo.endTime2 = this.calculateMonth2
      } else if (this.ListInfo.calculateMonthArryear2) {
        this.ListInfo.startTime2 = this.ListInfo.calculateMonthArryear2 + '-01';
        this.ListInfo.endTime2 = this.ListInfo.calculateMonthArryear2 + '-12';
      } else if (this.ListInfo.calculateMonthArrjidu2) {
        let newarr = this.getQuarterRange(this.ListInfo.calculateMonthArrjidu2);
        this.ListInfo.startTime2 = newarr[0];
        this.ListInfo.endTime2 = newarr[1];
      }
      const formatToStr = (val) => Array.isArray(val) ? val.join(',') : val;
      const { costType, details, menuType, dept, region } = this.ListInfo;
      let params = {
        ...this.ListInfo,
        trendChartType: this.trendChartType,
        costType: formatToStr(costType),
        details: formatToStr(details),
        menuType: formatToStr(menuType),
        dept: formatToStr(dept),
        region: formatToStr(region),
      }
      let res = await selectAdministrativeDataTwo(params);
      if (!res.success) {
        return;
      }
      // 直接使用API返回的数据构建图表配置
      const newobj = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: null
        },
        legend: {
          data: res.data.legend
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: res.data.yAxis,
          inverse: true
        },
        series: res.data.series,
        extraField: res.data.extraField,
        formatConfig: {
          rateMultiplier: 1
        }
      };
      this.oneCharts = newobj;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
