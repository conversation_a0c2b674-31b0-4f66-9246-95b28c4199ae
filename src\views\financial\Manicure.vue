<template>
    <container>
        <template #header>
            <el-form :inline="true">
                <el-form-item label="批次号:">
                    <el-input v-model="filter.batchNumber" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="供应商:">
                    <el-input v-model="filter.suppliers" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="组合编码:">
                    <el-input v-model="filter.combineCode" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="filter.goodsCode" style="width:120px;" maxlength="100" clearable></el-input>
                </el-form-item>
                <el-form-item label="导入日期:">
                    <el-date-picker style="width: 220px" v-model="timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        :clearable="true" @change="changeTime"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('Search')">查询</el-button>
                    <el-button type="primary" @click="openImport">导入</el-button>
                    <el-button type="primary" @click="openForm(true)">新增</el-button>
                </el-form-item>
            </el-form>
        </template>
        <vxetablebase ref="table" :id="'Manicure202040905'" :that='that' :isIndex='true'
            @sortchange='sortchange' :hasexpand='true' :tableData='list'
            :tableCols='tableCols' :loading="loading">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
<el-dialog title="导入大马美甲" :visible.sync="uploadVisible" v-dialogDrag width="20%" >
            <div style="width: 200px;height: 100px;">
            <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
                            :http-request="uploadRequest" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploading"
                                @click="uploadSubmit">{{ (uploading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
            </div>
        </el-dialog>
        <el-dialog :title="addOrEdit?'新增大马美甲':'编辑大马美甲'" :visible.sync="formVisible" v-dialogDrag width="20%">
            <el-form :inline="true" style="width: 80%;margin: 0 auto;margin-top: 30px;text-align: right;">
                <el-form-item v-if="addOrEdit" label="批次号:">
                    <el-input v-model="form.batchNumber" placeholder="仅支持数字"></el-input>
                </el-form-item>
                <el-form-item label="供应商:">
                    <el-input v-model="form.suppliers"></el-input>
                </el-form-item>
                <el-form-item label="组合商品编码:" >
                    <el-input v-model="form.combineCode"></el-input>
                </el-form-item>
                <el-form-item label="组合成本价:">
                    <el-input-number v-model="form.combineCost":min="0" :max="9999999" :precision="4" style="width: 158px;"></el-input-number>
                </el-form-item>
                <el-form-item label="商品编码:">
                    <el-input v-model="form.goodsCode"></el-input>
                </el-form-item>
                <el-form-item label="子商品成本价:">
                    <el-input-number v-model="form.goodsCost":min="0" :max="9999999" :precision="4" style="width: 158px;"></el-input-number>
                </el-form-item>
                <el-form-item label="数量:">
                    <el-input-number v-model="form.qty":min="0" :max="9999999" step-strictly style="width: 158px;"></el-input-number>
                </el-form-item>
            </el-form>
            <div style="width: 80%;margin: 0 auto;text-align: right;margin-top: 30px;">
                <el-button type="primary" @click="submitForm">提交</el-button>
                <el-button type="primary" @click="formVisible=false">取消</el-button>
            </div>
        </el-dialog>
        
    </container>
</template>

<script>
import container from '@/components/my-container'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { ImportManicure,QueryManicure,InsertOrUpdateManicure,DeleteManicure} from '@/api/financial/Manicure.js'
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs';

const tableCols = [
{ istrue: true, prop: 'batchNumber', label: '批次号', sortable: 'custom', align: 'center' },
{ istrue: true, prop: 'createdTime', label: '导入日期', sortable: 'custom', align: 'center',formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') },
{ istrue: true, prop: 'suppliers', label: '供应商', sortable: 'custom', align: 'center' },
{ istrue: true, prop: 'combineCode', label: '组合商品编码', sortable: 'custom', align: 'center' },
{ istrue: true, prop: 'combineCost', label: '组合成本价', sortable: 'custom', align: 'center' },
{ istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', align: 'center' },
{ istrue: true, prop: 'goodsCost', label: '子商品成本价', sortable: 'custom', align: 'center' },
{ istrue: true, prop: 'qty', label: '数量', sortable: 'custom', align: 'center' },
{
        istrue: true, type: "button", label: '操作', width: "120", btnList: [
             { label: "编辑", handle: (that, row) => that.openForm(false,row) },
             { label: "删除", handle: (that, row) => that.onDelete(row) },
       ] }
]

export default {
    name: 'Manicure',
    components: { container, vxetablebase },
    data() {
        return {
            that: this,
            filter: {
                batchNumber:null,
                suppliers:null,
                combineCode:null,
                goodsCode:null,
                startTime:null,
                endTime:null,
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
            },
            form:{},
            timerange:[],
            tableCols,
            list: [],
            loading: false,
            total: null,
            addOrEdit:false,
            formVisible:false,
            uploadVisible:false,
            fileList: [],
            uploading: false,
        };
    },

    async mounted() {
        this.filter.startTime = dayjs().format('YYYY-MM-DD');
        this.filter.endTime = dayjs().format('YYYY-MM-DD');
        this.timerange=[this.filter.startTime,this.filter.endTime]
        this.getList();
    },
    methods: {
        //获取列表
        async getList(val) {
            if(val=="Search"){
                this.filter.currentPage=1;
                this.$refs.pager.setPage(1);
            }
            const { success, data } = await QueryManicure(this.filter);
            if (success) {
                this.list = data?.list;
                this.total = data?.total;
            }
        },
        
      //导入
      async openImport() {
            this.uploadVisible = true;
            this.uploading=false;
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);

        },
        async uploadRequest(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const{success} = await ImportManicure(form);
            if(success)
            {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.uploadVisible=false;
            }
        },
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        uploadSubmit() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
        },
        async changeTime(e) {
            this.filter.startTime = e ? e[0] : null
            this.filter.endTime = e ? e[1] : null
        },
        //每页数量改变
        Sizechange(val) {
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()
        },
        //排序查询
        sortchange({ order, prop }) {
            if (prop) {
                this.filter.orderBy = prop
                this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        //打开表单
        openForm(addOrEdit,row)
        {
            this.addOrEdit=addOrEdit;
            this.form=addOrEdit?{}:JSON.parse(JSON.stringify(row));
            this.formVisible=true;
        },
        async submitForm()
        {
            const {success} = await InsertOrUpdateManicure(this.form);
            if(success)
            {
                this.formVisible=false;
                this.$message.success("数据更新成功")
                this.getList();
            }
        },

        onDelete(row){
            this.$confirm('确定删除吗, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                    .then(async () => {
                        const {success} = await DeleteManicure(row)
                        if (success)
                        {
                        this.$message({ type: 'success', message: '删除成功!' });
                        this.getList();
                        }
                    }).catch(() => {
                        this.$message({ type: 'info', message: '已取消' });
                    });
        },

    },
};
</script>

<style lang="scss" scoped></style>
    