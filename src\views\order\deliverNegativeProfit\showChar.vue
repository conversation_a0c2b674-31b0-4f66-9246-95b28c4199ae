<template>
  <div style="height: 600px; overflow-y: auto; overflow-x: hidden;">
    <div >
    <buschar :charid="'charNoProfit9'" :thisStyle="thisStyle" ref="refhz" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata9" v-if="isselplatform.length>=2&&chartsdata9" ></buschar>
    </div>
    <div >
    <buschar :charid="'charNoProfit1'" :thisStyle="thisStyle" ref="reftm" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata1" v-if="this.isselplatform.indexOf('天猫')!=-1&&chartsdata1" ></buschar>
    </div>
    <div >
    <buschar :charid="'charNoProfit2'" :thisStyle="thisStyle" ref="refalbb" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata2" v-if="this.isselplatform.indexOf('阿里巴巴')!=-1&&chartsdata2" ></buschar>
    </div>
 
    <div >
    <buschar :charid="'charNoProfit3'" :thisStyle="thisStyle" ref="refdy" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata3" v-if="this.isselplatform.indexOf('抖音')!=-1&&chartsdata3" ></buschar>
    </div>
 
    <div >
    <buschar :charid="'charNoProfit4'" :thisStyle="thisStyle" ref="refjd" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata4" v-if="this.isselplatform.indexOf('京东')!=-1&&chartsdata4" ></buschar>
    </div>
 
    <div >
    <buschar :charid="'charNoProfit5'" :thisStyle="thisStyle" ref="reftgc" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata5" v-if="this.isselplatform.indexOf('淘工厂')!=-1&&chartsdata5" ></buschar>
    </div>
 
    <div >
    <buschar :charid="'charNoProfit6'" :thisStyle="thisStyle" ref="reftb" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata6" v-if="this.isselplatform.indexOf('淘宝')!=-1&&chartsdata6" ></buschar>
    </div>
 
    <div >
    <buschar :charid="'charNoProfit7'" :thisStyle="thisStyle" ref="refsn" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata7" v-if="this.isselplatform.indexOf('苏宁')!=-1&&chartsdata7" ></buschar>
    </div>
 
    <div >
    <buschar :charid="'charNoProfit8'" :thisStyle="thisStyle" ref="refpdd" :filter="filter" :isslice="true" :isselplatformlr="isselplatformlr" :analysisData="chartsdata8" v-if="this.isselplatform.indexOf('拼多多')!=-1&&chartsdata8" ></buschar>
    </div>
 
    <div v-if="allcharbusdata.length==0&&!pageloading" style="height: 50vh; width: 100%;" class="flexcenter">该日期范围内平台暂无数据...</div>
    <div v-if="allcharbusdata.length==0&&pageloading" style="height: 50vh; width: 100%;" class="flexcenter">
      <i class="el-icon-loading" style="font-size: 30px;"></i>
    </div>
 
  </div>
 </template>
 <script>
 import buschar from './buschar.vue'
 import MyContainer from "@/components/my-container";
 import * as echarts from 'echarts/core';
 import { getContinuousNoProfit, newGetContinuousNoProfitAnalysisAsync } from "@/api/bookkeeper/continuousprofitanalysis"
 import {getOrderDayReportOutStoreLossPlatformChart} from '@/api/bookkeeper/reportdayV2'
 
 export default {
  name: "ConsecutiveNoProfitShowChar",
  components: {
    MyContainer, buschar
  },
  props: {
    filter: {
      
    },
    isselplatformlr: [],
    isselplatform: ['天猫',
        '阿里巴巴',
        '抖音',
        '京东',
        '淘工厂',
        '淘宝',
        '苏宁',]
  },
  data() {
    return {
      that: this,
      thisStyle: {width: '1650px', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'},
      pageloading: true,
      allcharbusdata: [],
      emphasisStyle: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,1,0.3)'
        }
      },
      filter1: {
        currentPage: 1,
        pageSize: 50
      },
      chartsdata: {},
      chartsdata1: null,
      chartsdata2: null,
      chartsdata3: null,
      chartsdata4: null,
      chartsdata5: null,
      chartsdata6: null,
      chartsdata7: null,
      chartsdata8: null,
      chartsdata9: null,
 
 
    };
  },
  async mounted() {
    
    await this.showChar();
  },
 
  methods: {
    buscharclick(e, name){
      
 
      
        let queryObj = {
         
          isPositive: false
        }
 
        queryObj.platform = e;
        // queryObj.shopCode = (that.filter.shopCode==""?null:that.filter.shopCode);
        // queryObj.groupId =  (that.filter.groupId==""?null:that.filter.groupId);
        // queryObj.operateSpecialUserId =  (that.filter.operateSpecialUserId==""?null:that.filter.operateSpecialUserId);
        if(!JSON.parse(localStorage.getItem('isselplatformlr'))){
          this.$message.info('请只选择一个标签负利润后才能跳转平台！');
          return
        }
        let seriesName = JSON.parse(localStorage.getItem('isselplatformlr'))[0];
        if (seriesName == "3天负利润") {
          queryObj.dayCount = 3;
        } else if (seriesName == "7天负利润") {
          queryObj.dayCount = 7;
        } else if (seriesName == "15天负利润") {
          queryObj.dayCount = 15;
        } else if (seriesName == "30天负利润") {
          queryObj.dayCount = 30;
        }
        // queryObj.dayCount = JSON.parse(localStorage.getItem('isselplatformlr'))[0];
 
        let patams = {}
        patams.query = queryObj;
        patams.path = "/bookkeeper/reportday/productReportAllIndex";
        if (e == 2) {
          patams.path = "/bookkeeper/reportday/financialReportPdd";
          this.$refs.refpdd.routerpush(patams);
        } else if (e == 4) {
          patams.path = "/bookkeeper/reportday/productReportAlibabaIndex";
          this.$refs.refalbb.routerpush(patams);
        } else if (e == 6) {
          patams.path = "/bookkeeper/reportday/productReportDyIndex";
          this.$refs.refdy.routerpush(patams);
        } else if (e == 7) {
          patams.path = "/bookkeeper/reportday/productReportJDIndex";
          this.$refs.refjd.routerpush(patams);
        } else if (e == 8) {
          patams.path = "/bookkeeper/reportday/productReportGCIndex";
          this.$refs.reftgc.routerpush(patams);
        } else if ( e == 1) {
          patams.path = "/bookkeeper/reportday/productReportTxIndex";
          this.$refs.reftm.routerpush(patams);
        } else if (e == 9 ) {
          patams.path = "/bookkeeper/reportday/productReportTaoBao";
          this.$refs.reftb.routerpush(patams);
        } else if (e == 10) {
          patams.path = "/bookkeeper/reportday/productReportSuNingIndex";
          this.$refs.refsn.routerpush(patams);
        }
 
        
        
 
        // that.$router.push({ path: url, query: queryObj })
    },
    async showChar() {
      this.pageloading = true;
 
      this.chartsdata1 = null;
      this.chartsdata2 = null;
      this.chartsdata3 = null;
      this.chartsdata4 = null;
      this.chartsdata5 = null;
      this.chartsdata6 = null;
      this.chartsdata7 = null;
      this.chartsdata8 = null;
      this.chartsdata9 = null;
 
      let param = { 
        "startTime": this.filter.timerange[0],
        "endTime": this.filter.timerange[1],
        "platForm": null,
        ...this.filter,
        platForms: this.filter.platForms?this.filter.platForms.join(","):null,
       };
      console.log(param);
      let res = await getOrderDayReportOutStoreLossPlatformChart(param);
 
      this.allcharbusdata = res.data;
      // this.chartsdata = res.data;
      res.data.map((item)=>{
        if(item.platformName == '天猫'){
          this.chartsdata1 = item.analysis;
        }else if(item.platformName == '阿里巴巴'){
          this.chartsdata2 = item.analysis;
        }else if(item.platformName == '抖音'){
          this.chartsdata3 = item.analysis;
        }else if(item.platformName == '京东'){
          this.chartsdata4 = item.analysis;
        }else if(item.platformName == '淘工厂'){
          this.chartsdata5 = item.analysis;
        }else if(item.platformName == '淘宝'){
          this.chartsdata6= item.analysis;
        }else if(item.platformName == '苏宁'){
          this.chartsdata7 = item.analysis;
        }else if(item.platformName == '拼多多'){
          this.chartsdata8 = item.analysis;
        }else if(item.platformName == '总'){
          this.chartsdata9 = item.analysis;
        }
      })
      this.pageloading = false;
 
  
    }
  }
 };
 </script>
 <style>
 .a{
  overflow-y: auto;
  position: relative;
  
 }
 .flexcenter{
  display: flex;
  justify-content: center;
  align-items: center;
 }
 </style>