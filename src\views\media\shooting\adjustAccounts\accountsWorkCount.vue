<template>
     <!--自动分配-->
    <my-container v-loading="pageLoading">
        <template #header>
        </template>
        <vxetablebase :id="'shootingvideotaskoverCacle202301291318001'" :that='that'  height="98%"
            border ref="table" class="draggable-table" :isIndex='true' :hasexpand='false' :isSelectColumn='true'
            :tableData='tasklist' :tableCols='tableCols' tablekey="accountsWorkCount" :loading="listLoading"
            :isBorder="false" @sortchange='sortchange'>
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" width="100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn' >
                <div style="margin:10px 5px 5px 0;">
                    <span style="padding: 0;margin-right:2px;">
                        <el-input style="width:25%" v-model="filter.userName" v-model.trim="filter.userName" :maxlength=100
                            placeholder="姓名" @keyup.enter.native="onSearch" clearable />
                    </span>
                    <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:25%" v-model="filter.companyName" placeholder="公司" clearable>
                            <el-option label="义乌" value="义乌"></el-option>
                            <el-option label="南昌" value="南昌"></el-option>
                        </el-select>
                    </span>
                    <span style="padding: 0;margin-right:5px;">
                        <el-select style="width:25%" v-model="filter.isCyFp" placeholder="是否分配" clearable>
                            <el-option label="是" :value=1></el-option>
                            <el-option label="否" :value=0></el-option>
                        </el-select>
                    </span>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </div>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getTaskList"
                :page-size="100" />
        </template>
        <!--新增人员编辑-->
        <el-dialog :title="editformTitle" :visible.sync="editformdialog" width="60%" :close-on-click-modal="false"
            v-loading="editLoading" element-loading-text="拼命加载中" v-dialogDrag :append-to-body="true">
            <el-form :model="editform" ref="editform" label-width="120px" :rules="editformRules">
                <el-row>&nbsp;</el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="userId" label="姓名">
                            <el-select v-model="editform.userId" :disabled="true" :filterable="true" :clearable="true"
                                @change="selUserInfoChange(editform.userId, index)">
                                <el-option v-for="item in userList" :key="item.id" :label="item.label"
                                    :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item prop="companyName" label="公司">
                            <el-select v-model="editform.companyName" :clearable="true" :disabled="true"
                                :collapse-tags="true" filterable>
                                <el-option label="义乌" value="义乌"></el-option>
                                <el-option label="南昌" value="南昌"></el-option>
                                <el-option label="武汉" value="武汉"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="isCyFp" label="是否分配">
                            <el-select v-model="editform.isCyFp" :clearable="true" :collapse-tags="true" :filterable="true">
                                <el-option label="是" :value=1></el-option>
                                <el-option label="否" :value=0></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item prop="fpTaskAutoRate" label="分配比例(%)">
                            <el-input-number :clearable="true" v-model="editform.fpTaskAutoRate"
                                v-model.trim="editform.fpTaskAutoRate" :min="-1" :max="100" :controls="false" :precision="0"
                                placeholder="分配比例"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="6">
                        <el-form-item prop="" label="设计品牌">
                            <el-select v-model="editform.brandListinfo" :clearable="true" :collapse-tags="true" :filterable="true" multiple  >
                                <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editformdialog = false">关 闭</el-button>
                    <my-confirm-button type="submit" @click="onSubmit" />
                </span>
            </template>
        </el-dialog>
        <!--岗位管理-->
        <el-drawer title="岗位管理" :visible.sync="positionOrder" size="75%" :close-on-click-modal="false" direction="btt">
            <accountsWorkPostionManage ref="accountsWorkPostionManage"></accountsWorkPostionManage>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="positionOrder = false">关 闭</el-button>
                </span>
            </template>
        </el-drawer>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/tableforvedio.vue";
import accountsWorkPostionManage from '@/views/media/shooting/adjustAccounts/accountsWorkPostionManage';
import { getErpUserInfoViewforshoot, getWorkPostListAsync } from '@/api/media/mediashare';
import { addOrUpdatePersonnelPositionAsync, getPersonnelPositionAsync, delPersonnelPositionAsync, sortPersonnelPosition 
} from '@/api/media/shootingset'
import { getShootOperationsGroup} from '@/api/media/mediashare'; 
import MyConfirmButton from "@/components/my-confirm-button";
import Sortable from 'sortablejs'
const tableCols = [
    { istrue: true, prop: 'userName', label: '姓名', width: '100' , align: 'left'},
    { istrue: true, prop: 'companyName', label: '公司', width: '80', align: 'left' },
    { istrue: true, prop: 'workPositionStr', label: '工作岗位', width: '100', align: 'left' },
    { istrue: true, prop: 'commissionPositionStr', label: '提成岗位', width: '100', align: 'left' },
    { istrue: true, prop: 'isCyFpbool', type: 'switch', label: '参与分配', width: '100', align: 'center', change: async (row, that) => { return await that.CyCommission(row) } },
    { istrue: true, prop: 'fpTaskAutoRate', label: '分配比例', width: '100', sortable: 'custom', align: 'left', formatter: (row) => row.fpTaskAutoRate + "%" },
    //    { istrue: true, type: 'color' ,backgroudColor:'color: rgb(255, 255, 255,0)',width: '10',align:'left'}, 
    { istrue: true, prop: 'fpTaskCount', label: '分配任务数', width: '100', sortable: 'custom', align: 'left' },
    { istrue: true, prop: 'fpTaskLastCount', label: '剩余任务数', width: '100', sortable: 'custom', align: 'left' },
    { istrue: true, prop: 'fpTaskLastRate', label: '剩余比例', width: '100', sortable: 'custom', align: 'left', formatter: (row) => row.fpTaskLastRate + "%" },
    { istrue: true, prop: 'fpTaskOverCount', label: '完成任务数', width: '100', sortable: 'custom', align: 'left' },
    { istrue: true, type: "button", label: '操作', align: 'left',
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEditAdd(row) }
        ]
    }
];
export default {
    components: { MyContainer, cesTable, MyConfirmButton, accountsWorkPostionManage, vxetablebase },
    data() {
        return {
            that: this,
            pageLoading: false,
            positionOrder: false,
            summaryarry: [],
            userList: [],
            tasklist: [],
            workPositionlist: [],
            commissionPositionlist: [],
            retdata: [],
            sels: [], // 列表选中列
            tableCols: tableCols,
            listLoading: false,
            //人员编辑模块
            editLoading: false,
            editformdialog: false,
            editformTitle: null,
            editform: {
                userId: null,
                userName: null,
                companyName: null,
                workPosition: null,
                commissionPosition: null,
                commissionRate: null,
                isCyCommission: null,
                isCyFp: null,
                brandListinfo:[]
            },
            brandList:[],
            total: 0,
            pager: { OrderBy: "orderNum", IsAsc: true },
            filter: {
            },
            editformRules: {
                userId: [{ required: true, message: '请选择', trigger: 'blur' }],
                isCyCommission: [{ required: true, message: '请选择', trigger: 'blur' }],
                isCyFp: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionRate: [{ required: true, message: '请填写', trigger: 'blur' }],
                companyName: [{ required: true, message: '请选择', trigger: 'blur' }],
                commissionPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                workPosition: [{ required: true, message: '请填写', trigger: 'blur' }],
                fpTaskAutoRate: [{ required: true, message: '请填写', trigger: 'blur' }],
            },
        };
    },

    async mounted() {
        // this.rowDrop();
        await this.getUserList();
        await this.getWorkPostList();
        await this.onSearch();
        var res =await  getShootOperationsGroup({type:13}); 
        this.brandList =res?.map(item => { return { value: item.id, label: item.label }; });
    },
    methods: {
        async saveOrder() {
            this.listLoading = true;
            var res = await sortPersonnelPosition(this.tasklist);
            if (res?.success)
                this.$message({ message: this.$t('操作成功'), type: 'success' });
            this.listLoading = false;
        },
        rowDrop() {
            const tbody = document.querySelector('.draggable-table .vxe-table--body-wrapper tbody')
            const _this = this
            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    if (newIndex == oldIndex) return
                    _this.tasklist.splice(
                        newIndex,
                        0,
                        _this.tasklist.splice(oldIndex, 1)[0]
                    )
                    var newArray = _this.tasklist.slice(0)
                    _this.tasklist = []
                    _this.$nextTick(function () {
                        _this.tasklist = newArray
                    })
                }
            })
        },
        async onDel(row) {

            this.$confirm("将进行删除操作，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await delPersonnelPositionAsync({ positionId: row.positionId });
                if (!res?.success) { return; }
                this.$message({ message: this.$t('操作成功'), type: 'success' });
                await this.onSearch()
            });

        },
        async CyCommission(row) {
            this.editform.isCyCommission = row.isCyCommissionbool ? 1 : 0;
            this.editform.isCyFp = row.isCyFpbool ? 1 : 0;
            this.editform.userId = row.userId.toString();
            this.editform.userName = row.userName;
            this.editform.commissionRate = row.commissionRate;
            this.editform.companyName = row.companyName;
            this.editform.workPosition = row.workPosition;
            this.editform.commissionPosition = row.commissionPosition;
            this.editform.positionId = row.positionId;
            this.editform.fpTaskAutoRate = row.fpTaskAutoRate;
            const para = _.cloneDeep(this.editform);
            var res = await addOrUpdatePersonnelPositionAsync(para);
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
        },
        async workPositionManage() {
            this.editLoading = true;
            this.positionOrder = true;
            this.editLoading = false;
        },
        selUserInfoChange(val, index) {
            let resultArr = this.userList.find((item) => {
                return item.id == val;
            });
            this.editform.userName = resultArr.label;
        },
        //获取岗位下拉数据
        async getWorkPostList() {
            var res = await getWorkPostListAsync();
            if (res) {
                this.workPositionlist = res.workPositionlist;
                this.commissionPositionlist = res.commissionPositionlist;
            }

          

        },
        async getUserList() {
            this.userList = await getErpUserInfoViewforshoot();
            return this.userList;
        },
        //打开新增窗口
        async onOpenAdd() {
            this.editformdialog = true;
            this.editLoading = true;
            await getWorkPostListAsync();
            this.editformTitle = "新增人员";
            this.editform.userId = null;
            this.editform.userName = null;
            this.editform.isCyCommission = 0;
            this.editform.commissionRate = 100;
            this.editform.companyName = null;
            this.editform.workPosition = null;
            this.editform.commissionPosition = null;
            this.editform.userName = null;
            this.editform.isCyFp = 1;
            this.editform.positionId = 0;
            this.editform.fpTaskAutoRate = 100;
            this.editLoading = false;
        },
        //打开新增窗口
        async onEditAdd(row) {
            this.editformdialog = true;
            this.editLoading = true;
            await getWorkPostListAsync();
            this.editformTitle = "编辑人员";
            this.editform.isCyFp = row.isCyFp;
            this.editform.isCyCommission = row.isCyCommission;
            this.editform.userId = row.userId.toString();
            this.editform.userName = row.userName;
            this.editform.commissionRate = row.commissionRate;
            this.editform.companyName = row.companyName;
            this.editform.workPosition = row.workPosition;
            this.editform.commissionPosition = row.commissionPosition;
            this.editform.positionId = row.positionId;
            this.editform.fpTaskAutoRate = row.fpTaskAutoRate;
            if(row.brandListinfo != null){

                this.editform.brandListinfo = row.brandIds.split(',');
            }
            this.editLoading = false;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.editform.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        async onSubmit() {
            if (!this.onSubmitValidate()) {
                return;
            }
            const para = _.cloneDeep(this.editform);
            this.editLoading = true;
            var res = await addOrUpdatePersonnelPositionAsync(para);
            this.editLoading = false;
            if (!res?.success) { return; }
            this.$message({ message: this.$t('保存成功'), type: 'success' });
            this.onSearch();
            this.editformdialog = false;
        },
        async onSearch() {
            /*   this.$refs.pager.setPage(1); */
            this.getTaskList();
        },
        async getTaskList() {

            var pager = this.$refs.pager.getPager();
            const params = {
                ...this.filter,
                ...pager,
                ...this.pager,
            };
            this.listLoading = true;
            const res = await getPersonnelPositionAsync(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tasklist = res.data.list;
            //this.summaryarry = { videoTaskId_sum: " 0" };
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
    },
};
</script>


