<template>
  <my-container v-loading="pageLoading">
    <template #header>
       <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
    </template>
      <el-button-group>         
          <el-button style="padding: 0;margin: 0;">
          <el-date-picker style="width: 210px" v-model="filter.timerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker></el-button>
          <el-button style="padding: 0;">
            <el-select filterable v-model="filter.shopCode" placeholder="店铺" style="width: 150px">
              <el-option key="所有" label="所有" value></el-option>
              <el-option v-for="item in shopList" :key="item.shopCode"  :label="item.shopName" :value="item.shopCode"></el-option>
            </el-select>
          </el-button>
        <el-button style="padding: 0;">
          <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组" style="width: 90px">
            <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-button>
        <el-button style="padding: 0;">
          <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable placeholder="运营专员" style="width: 90px">
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-button>
        <el-button style="padding: 0;">
          <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理" style="width: 90px">
            <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-button>
        
        <el-button type="primary" @click="onSearch">查询</el-button>
      </el-button-group>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="true"  
              :tableData='financialreportlist' @cellclick="cellclick"
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:92%;margin: 0">
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>       
  </my-container>
</template>
<script>
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getDirectorGroupList,getDirectorList} from '@/api/operatemanage/base/shop'
import {exportFinancialPddStaticticsByUser,getParm,setParm,getFinancialPddStaticticsByUser} from '@/api/bookkeeper/reportday' 
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import {formatPlatform,formatTime,formatYesornoBool,formatLinkProCode} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
let loading;
const startLoading = () => {
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
}; 
const tableCols =[
       
        {istrue:true,fixed:true,prop:'shopCode',label:'店铺名称',sortable:'custom', width:'200',formatter:(row)=> row.shopName},
        {istrue:true,fixed:true,prop:'groupId',label:'小组',sortable:'custom', width:'60',formatter:(row)=> row.groupName},
        {istrue:true,fixed:true,prop:'operateSpecialUserId',label:'运营专员',sortable:'custom', width:'80',formatter:(row)=> row.operateSpecialUserName},
        {istrue:true,fixed:true,prop:'userId',label:'运营助理',sortable:'custom', width:'80',formatter:(row)=> row.userName},     
         {istrue:true,prop:'payAmont',label:'付款金额',sortable:'custom',width:'100',formatter:(row)=> !row.payAmont?" ": row.payAmont.toFixed(2)},       
        {istrue:true,prop:'saleAmont',label:'销售金额',sortable:'custom', width:'80',formatter:(row)=> !row.saleAmont?" ": row.saleAmont.toFixed(2)},        
        {istrue:true,prop:'alladv',label:'总广告费',sortable:'custom', width:'80',formatter:(row)=> row.alladv==0?" ": row.alladv?.toFixed(2)},
        {istrue:true,prop:'advratio',label:'广告占比%',sortable:'custom', width:'180',formatter:(row)=> !row.advratio?" ": (row.advratio*100).toFixed(2)+"%"},
        {istrue:true,prop:'profit3',label:'毛三利润',sortable:'custom', width:'180',type:'custom',tipmesg:'毛二利润-预估费用',permission:"lirunprsi",formatter:(row)=> !row.profit3?" ": row.profit3?.toFixed(2)},
        {istrue:true,prop:'profit4',label:'净利润',type:'custom',tipmesg:'毛三利润-公摊费',sortable:'custom', width:'180',permission:"lirunprsi",formatter:(row)=> !row.profit4?" ": row.profit4.toFixed(2)},
         ];
const tableHandles=[  
       
        {label:"导出", handle:(that)=>that.onExport()},
        {label:"刷新", handle:(that)=>that.onRefresh()},
      ];

 


export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable,productdrchart,InputMult,freightDetail},
  data() {
    return {
      that:this,
      filter: {
        platform:null,
        shopCode:null,
        proCode:null,
        productName:null,
        groupId:null,
        startTime: null,
        endTime: null,
        timerange:null,
        // 运营助理
        userId :null,
        // 车手
        userId2:null,
        // 备用
        userId3:null,
        // 运营专员 ID
        operateSpecialUserId:null,
    
      }, 
      onimportfilter:{
        yearmonthday:null,
      },
      shopList:[],
      userList:[],
      grouplist:[],
      directorlist:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      total: 0,
      pager:{OrderBy:" groupid ",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      fileList:[],
      dialogVisible:false,
      uploadLoading:false,
      importFilte:{},
      fileList:[],
      fileparm:{},
      editparmVisible:false,
      editLoading:false,
      editparmLoading:false,
      drawervisible:false,
      dialogDrVisible:false,
      drparamProCode:'',
      autoformparm:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
        },
      freightDetail:{
        visible:false,
        filter:{
          proCode:null,
          timeRange:[]
        }
      }
    };
  },
  async mounted() {
    this.init();
  },
  async created() {
    console.log("created")
    await this.getShopList();
    
   
  },
  methods: {
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-1);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.filter.timerange=[];
        this.filter.timerange[0]=this.datetostr(date1);
        this.filter.timerange[1]=this.datetostr(date2);
        console.log(this.filter)
      },

       async onExport(){ 

      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var that=this;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
     // this.listLoading = true;
      startLoading(); 
      const res = await exportFinancialPddStaticticsByUser(params).then(res=>{
          loading.close();
          if(!res?.data) {
         this.$message({message:"没有数据",type:"warning"});
         return
      }
          
           const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','拼多多统计运营业绩' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
          
      });



 
    },


   async showprchart2(prcode){
      window['lastseeprcodedrchart']=prcode
      this.drparamProCode=prcode
      this.dialogDrVisible=true
   } ,  
 
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          if(f.isCalcSettlement&&f.shopCode&&f.platform==2)
              this.shopList.push(f);
        });
        var res2= await getDirectorGroupList();
        this.grouplist = res2.data?.map(item => {return { value: item.key, label: item.value };}); 

        var res3= await getDirectorList();
        this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };}); 
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
      this.onSearch()
    },
    async onSearch(){
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });
      // loading.close();
    },
    async getList(){
      this.filter.startTime =null;
      this.filter.endTime =null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var that=this;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter};
     // this.listLoading = true;
      startLoading(); 
      const res = await getFinancialPddStaticticsByUser(params).then(res=>{
          loading.close();
          that.total = res.data?.total;
          if(res?.data?.list&&res?.data?.list.length>0){
            for (var i in res.data.list) {
              if (!res.data.list[i].freightFee) {
                res.data.list[i].freightFee =" ";               
              }
            }
          }
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
          that.summaryarry.alladv_rate_sum = that.summaryarry.alladv_rate_sum.toString() + "%"
            that.summaryarry.profit3_rate_sum = that.summaryarry.profit3_rate_sum.toString() + "%"
            that.summaryarry.profit4_rate_sum = that.summaryarry.profit4_rate_sum.toString() + "%"
            that.summaryarry.refundAmontBeforeRate_sum = that.summaryarry.refundAmontBeforeRate_sum.toString() + "%"
            that.summaryarry.profit1Rate_sum = that.summaryarry.profit1Rate_sum.toString() + "%"
      });
    },
    
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   onRefresh(){
        this.onSearch()
    },
  
 
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>

