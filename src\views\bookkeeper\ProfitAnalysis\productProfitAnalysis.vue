<template>
  <container v-loading="pageLoading">
      <!-- :tableHandles='tableHandles'  -->
     <ces-table ref="table" :that='that' :isIndex='true'   
         :hasexpand='true' :tableData='list' :tableCols='tableCols' @sortchange='sortchange' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      
         <template slot='extentbtn'>
      <!-- <el-button-group>
      -->
        
    <!-- <el-button type="primary" @click="startImport">导入店铺利润分析</el-button>
    <el-button type="primary" @click="downloadTemplate">店铺利润分析-模板</el-button> -->
    <!-- <el-button type="primary" @click="PurchaseReturnButton">批次号删除</el-button>  -->
    <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
    <!-- <el-button type="primary" @click="onSearch">刷新</el-button>  -->
        <!-- <el-button style="padding: 0;margin: 0;">
        <el-select filterable  :remote="true" :remote-method="init"  multiple  collapse-tags  v-model="filter.SuName"   clearable placeholder="请搜索供应商"  style="width: 255px">
            <el-option v-for="item in suppilelist" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-button> -->
       <!-- <el-button style="padding: 0;width: 300px;">
            <el-select style="width: 300px;" v-model.trim="seriesCoding" multiple filterable remote reserve-keyword placeholder="系列编码" clearable :remote-method="remoteMethod" > 
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-button>
    <el-button type="primary" @click="onSearch">查询</el-button> 
      </el-button-group> -->
       </template>
      </ces-table>
      
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
   
  
 

  </container>
</template>
<script>
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import {getShopProfitAnalysisSum,getProductProfitAnalysis}from '@/api/bookkeeper/financialreport'
import {formatTime,formatYesornoBool,formatWarehouseArea,formatIsOutStock,formatSecondToHour,formatPlatform} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import logistics from '@/components/Comm/logistics'
import goodscoderecord from '@/views/inventory/goodscoderecord'
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";

const tableCols =[
   
      {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'80',},
      // {istrue:true,prop:'platform',label:'平台', width:'50',formatter:row=>formatPlatform(row.platform)},
      //{istrue:true,prop:'operating',label:'小组', width:'50',},
      //  {istrue:true,prop:'settlementAmount',label:'结算金额', width:'80',},
        {istrue:true,prop:'seriesCoding',label:'系列编码', width:'80',},
      // {istrue:true,prop:'amountOut',label:'退款金额', width:'80',},
      // {istrue:true,prop:'netSales',label:'净销售额', width:'80',},
      //  {istrue:true,prop:'netSalesRate',label:'净销售额占比', width:'120',formatter:(row)=> !row.netSalesRate?" ": (row.netSalesRate*100).toFixed(2)+'%'},
      // //  {istrue:true,prop:'replacementAmount',label:'补发成本', width:'80',},
      // // {istrue:true,prop:'abnormalAmount',label:'异常成本', width:'80',},
      // {istrue:true,prop:'amountGrossProfitSale',label:'销售毛利',sortable:'custom', width:'80',},
      //  {istrue:true,prop:'amountGrossProfitSaleRate',label:'销售毛利率',sortable:'custom', width:'90',formatter:(row)=> !row.amountGrossProfitSaleRate?" ": (row.amountGrossProfitSaleRate*100).toFixed(2)+'%'},
      //    {istrue:true,prop:'profit1SumRate',label:'销售毛利占比', width:'120',formatter:(row)=> !row.profit1SumRate?" ": (row.profit1SumRate*100).toFixed(2)+'%'},
      // //  {istrue:true,prop:'courierFees',label:'快递费', width:'80',},
      // //  {istrue:true,prop:'expressDeductions',label:'其中：快递罚款', width:'120',},
      //  //包装费==辅料
      //   {istrue:true,prop:'packageFee',label:'辅料',sortable:'custom', width:'80',},
      //  //产品运费==物流费
      // //  {istrue:true,prop:'productFreightfee',label:'物流费', width:'80',},
      // //  {istrue:true,prop:'billsFee',label:'账单费用', width:'80',},
      //  {istrue:true,prop:'platformViolationsDeductions',label:'其中：扣款', width:'100',},
      //  {istrue:true,prop:'operatingFeeTotal',label:'推广费',sortable:'custom', width:'80',},
      //  {istrue:true,prop:'grossProfit',label:'产品利润',sortable:'custom', width:'80',},
      //  {istrue:true,prop:'profit3Rate',label:'产品利率',sortable:'custom', width:'100',formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate*100).toFixed(2)+'%'},
      //   {istrue:true,prop:'profit3SumRate',label:'产品利润占比', width:'120',formatter:(row)=> !row.profit3SumRate?" ": (row.profit3SumRate*100).toFixed(2)+'%'},
      //  {istrue:true,prop:'netProfit',label:'净利润',sortable:'custom', width:'80',},
      //  {istrue:true,prop:'netProfitRate',label:'净利率', width:'80',formatter:(row)=> !row.netProfitRate?" ": (row.netProfitRate*100).toFixed(2)+'%'},
      // {istrue:true,prop:'netProfitSumRate',label:'净利润占比', width:'120',formatter:(row)=> !row.netProfitSumRate?" ": (row.netProfitSumRate*100).toFixed(2)+'%'},
      {istrue:true,prop:'settlementAmount',label:'结算金额', width:'80',},
        
        {istrue:true,prop:'amountOut',label:'退款金额',sortable:'custom', width:'90',},
        {istrue:true,prop:'amountOutRet',label:'退款率', width:'90',},
        {istrue:true,prop:'netSales',label:'净销售额', width:'80',},
        {istrue:true,prop:'netSalesRateSumNetSales',label:'净销售额占比总净销售额', width:'80',formatter:(row)=> !row.netSalesRateSumNetSales?" ": (row.netSalesRateSumNetSales).toFixed(2)+'%'},   
         {istrue:true,prop:'saleCostAmount',label:'结算成本', width:'90',},
         {istrue:true,prop:'replacementAmount',label:'补发成本',sortable:'custom', width:'80',},
        {istrue:true,prop:'abnormalAmount',label:'异常成本',sortable:'custom', width:'80',},
        {istrue:true,prop:'amountGrossProfitSale',label:'毛一',sortable:'custom', width:'80',},
        {istrue:true,prop:'amountGrossProfitSaleRatess',label:'毛一占比总毛一'},
         {istrue:true,prop:'amountGrossProfitSaleRate',label:'毛一利率', width:'80',formatter:(row)=> !row.amountGrossProfitSaleRate?" ": (row.amountGrossProfitSaleRate).toFixed(2)+'%'},
         {istrue:true,prop:'courierFees',label:'快递费',sortable:'custom', width:'80',},
         {istrue:true,prop:'courierFeesWgkk',label:'其中：快递罚款', width:'120',},
         //包装费==辅料
          {istrue:true,prop:'packageFee',label:'辅料',sortable:'custom', width:'80',},
         //产品运费==物流费
         {istrue:true,prop:'productFreightfee',label:'物流费',sortable:'custom', width:'80',},
         {istrue:true,prop:'billTotalAmont',label:'账单费用',sortable:'custom', width:'80',},
         {istrue:true,prop:'billsFeeRate',label:'账单费用费率', width:'120',},
         {istrue:true,prop:'amontWagesGroup',label:'小组运营工资', width:'120',},
         {istrue:true,prop:'groupFeeRates',label:'小组运营工资占比销售额', width:'120',},
         {istrue:true,prop:'platformViolationsDeductions',label:'其中：扣款', width:'100',},
         {istrue:true,prop:'courierFeesRate',label:'快递费率', width:'100',},
         {istrue:true,prop:'operatingFeeTotal',label:'推广费',sortable:'custom', width:'80',},
         {istrue:true,prop:'promotionFeeRate',label:'推广费率', width:'100',},
         {istrue:true,prop:'grossProfit',label:'毛三',sortable:'custom', width:'80',},
         {istrue:true,prop:'profit3Ratess',label:'毛三占比总毛三', width:'80',},
         {istrue:true,prop:'profit3Rate',label:'毛三利率', width:'100',formatter:(row)=> !row.profit3Rate?" ": (row.profit3Rate).toFixed(2)+'%'},
         {istrue:true,prop:'amontAdditionfee',label:'除运营工资、美工提成、采购提成工资', width:'80',sortable:'custom'},
         {istrue:true,prop:'rent',label:'房租', width:'80',},
         {istrue:true,prop:'otherFee',label:'其他费用', width:'80',},
         {istrue:true,prop:'netProfit',label:'净利润',sortable:'custom', width:'80',},
         {istrue:true,prop:'netProfitRateSumNetProfit',label:'净利润占比总净利润', width:'80',formatter:(row)=> !row.netProfitRateSumNetProfit?" ": (row.netProfitRateSumNetProfit).toFixed(4)+'%'},
         {istrue:true,prop:'netProfitRate',label:'净利率', width:'80',formatter:(row)=> !row.netProfitRate?" ": (row.netProfitRate).toFixed(2)+'%'},
     ];


export default {
  name: "Users",
  components: {container,cesTable,MyConfirmButton,logistics},
   props:{
       filter: { }
     },
  data() {
    return {
      seriesCoding:"",
      options:[],
      uploadLoading:false,
      dialogVisible: false,
      that:this,
      // filter: {
      //   timerange:'',   
      // },
      list: [],
      drawervisible:false,
      tableCols:tableCols,
      pager:{OrderBy:"",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      listLoading: false,
      pageLoading: false,
    };
  },
  watch: {
    value(n) {
      if(n) {
        this.$nextTick(() => {
          console.log('this.$refs.table--->', this.$refs.table); // 添加这个用于处理fixed定位导致的列表行错位
          this.$refs.table.doLayout();
        });
        this.removeEditPopoverListener(n);  // 监听滚动，用于编辑框的滚动移除
      }
    }
  },
 async mounted() {
    await this.onSearch();
    await this.getlist();
  },
 methods: {
   //系列编码远程搜索
    async remoteMethod(query){
      if (query !== ''){
          this.searchloading == true
          setTimeout(async () => {
              const res = await getListByStyleCode({currentPage:1,pageSize:50, styleCode: query})
              console.log("系列编码远程搜索",res);
              this.searchloading = false
              res?.data?.forEach(f=>{
              this.options.push({value:f.styleCode,label:f.styleCode})
              });
          }, 200)
      }
      else{
          this.options = []
      }
    },
   onSelsChangeReturnGoods(sels){
    this.sels2 = sels
   },
   async onSearch() {
       this.$refs.pager.setPage(1)
       this.getlist()
    },
   async getlist() {
     this.filter.startTime =null;
       this.filter.endTime =null;
       if (this.filter.yearMonth && this.filter.yearMonth.length>0) {
                this.filter.startTime = this.filter.yearMonth[0];
                this.filter.endTime = this.filter.yearMonth[1];
            }
      //  this.filter.seriesCoding=this.seriesCoding.join();
      if (!this.pager.OrderBy) this.pager.OrderBy="";
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,... this.filter}
      params.SeriesCoding=this.filter.SeriesCoding.join();
      // if (params.timerange) {
      //    params.startDate = params.timerange[0];
      //    params.endDate = params.timerange[1];
      // }
    //   params.SuName=this.filter.SuName.join();
      // console.log("供应商id",params.SuName);
      // console.log('params',params)
      this.listLoading = true
      const res = await getProductProfitAnalysis(params)
      this.listLoading = false
      if (!res?.success) return 
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {d._loading = false})
      this.list = data
      this.summaryarry=res.data.summary;
    },

    beforeRemove() {
      return false;
    },
    
   doCopy: function (val) {       
      let that=this;                         
      this.$copyText(val).then(function (e) {
          that.$message({ message: "内容已复制到剪切板！", type: "success" });
      }, function (e) {
          that.$message({ message: "抱歉，复制失败！", type: "warning" });
      })
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   
  },
};
</script>


