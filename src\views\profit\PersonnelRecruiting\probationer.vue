<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select v-model="filter.startEndDateType" placeholder="时间类型" style="width: 100px" size="mini"
                        clearable  @clear="() => { filter.daterange = null }">
                        <el-option label="入职" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-date-picker size="mini" :disabled="!filter.startEndDateType" v-model="filter.daterange"
                        type="daterange" range-separator="至" start-placeholder="开始日期" value-format="yyyy-MM-dd"
                        end-placeholder="结束日期" :picker-options="pickerOptions" clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="">
                    <el-select ref="selectUpResId" v-model="chooseName" clearable style="width: 100px" size="mini"
                        @clear="() => { filter.ddDeptId = null }" placeholder="招聘部门">
                        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.positionName" placeholder="请输入岗位名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.name" placeholder="请输入人才名称" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.recruiterDDUserId" placeholder="招聘专员" style="width: 100px" size="mini"
                        clearable>
                        <el-option v-for="item in recruiterList" :key="item.ddUserId" :label="item.userName"
                            :value="item.ddUserId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.candidateFrom" placeholder="类型" style="width: 80px" size="mini"
                        clearable>
                        <el-option label="全部" :value="null"></el-option>
                        <el-option label="社招" :value="0"></el-option>
                        <el-option label="内推" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.keywords" style="width: 160px" :maxLength="100" placeholder="关键字查询"
                        clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark" content="姓名、电话、部门、岗位、专员、初试部门、复试部门"
                            placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">筛选</el-button>
                </el-form-item>
                <el-form-item>
                    <!-- <el-button type="primary" @click="onAll">手动更新资料</el-button> -->
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="downloadTemplate">下载导入模板</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onstartImport">导入花名册</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            style="height: calc(100vh - 220px);" :tableData='datalist' @select='selectchange' :isSelection="true"
            :tableCols='tableCols' :isSelectColumn='true' :customRowStyle="customRowStyle" :loading="listLoading"
            :summaryarry="summaryarry" :selectColumnHeight="'0px'" :isBorder="false" :hasexpandRight="true">
            <template slot='right'>
                <el-table-column prop="v8" label="是否匹配钉钉" width="120">
                    <template slot-scope="scope">
                        <span class="el-link" style="margin-right: 10px">{{ scope.row.dingTalkUserId ? '是' : '否' }}</span>
                        <el-link type="primary" v-if="!scope.row.dingTalkUserId"
                            @click="finishPostion(scope.row.candidateId)">绑定</el-link>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="editPostion(scope.row.candidateId, 1)">编辑</el-link>
                        <el-link type="primary" style="margin:0 10px"
                            @click="losePostion(scope.row.candidateId)">面谈</el-link>
                        <el-link type="danger" @click="deletePostion(scope.row.candidateId)">删除</el-link>
                    </template>
                </el-table-column>
            </template>

        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getDataList" />
        </template>
        <!-- 新增岗位/编辑岗位 -->
        <el-dialog :title="dialogTitle" :visible.sync="showDialogSon" width="50%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <div ref="show_prohationer" style="height: 65vh; overflow-x: hidden;">
                <talentInformation v-loading = "syncLoading" element-loading-text="正在同步..." :key="candidateInfoUpdate" v-if="showDialogSon" ref="talentInformation" :candidateInfo="candidateInfo"
                    :isEdit="isEdit" @closeDialog="closeDialog"></talentInformation>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="syncDingding">同步钉钉花名册信息</el-button>
                    <el-button @click="showDialogSon = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="submitTalent" /> -->
                    <el-button type="primary" @click="submitTalent">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 人才流失 -->
        <el-dialog title="面谈信息" :visible.sync="showFinishDialog" width="30%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag>
            <el-form ref="losFormRef" label-width="80px" :model="loseForm" :rules="lostrules">
                <el-form-item label="面谈类型" prop="meetingType">
                    <el-select v-model="loseForm.meetingType" placeholder="面谈类型" size="mini">
                        <el-option label="转正" value="转正"></el-option>
                        <el-option label="升职" value="升职"></el-option>
                        <el-option label="调岗" value="调岗"></el-option>
                        <el-option label="离职" value="离职"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="loseForm.meetingType == '离职'" style="margin-bottom:0" label-width="0">
                    <el-form-item label="离职类型" prop="dimissionType">
                        <el-select v-model="loseForm.dimissionType" placeholder="离职类型" size="mini">
                            <el-option label="辞退" value="辞退"></el-option>
                            <el-option label="自离" value="自离"></el-option>
                            <el-option label="辞职" value="辞职"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="离职原因" prop="dimissionReason">
                        <el-select v-model="loseForm.dimissionReason" placeholder="离职原因" size="mini">
                            <el-option label="不符合个人职业规划" value="不符合个人职业规划"></el-option>
                            <el-option label="不适应工作节奏" value="不适应工作节奏"></el-option>
                            <el-option label="升学/考公" value="升学/考公"></el-option>
                            <el-option label="身体原因" value="身体原因"></el-option>
                            <el-option label="家庭原因" value="家庭原因"></el-option>
                            <el-option label="工作态度不端正" value="工作态度不端正"></el-option>
                            <el-option label="学习进度慢/学习能力差" value="学习进度慢/学习能力差"></el-option>
                            <el-option label="业绩不达标" value="业绩不达标"></el-option>
                            <el-option label="未通过试用期" value="未通过试用期"></el-option>
                            <el-option label="联系不上" value="联系不上"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="数据佐证" v-else>
                    <el-input placeholder="数据佐证" v-model="loseForm.dataSupport" maxlength="80">
                    </el-input>
                </el-form-item>
                <el-form-item label="面谈时间" prop="meetingDate">
                    <el-date-picker v-model="loseForm.meetingDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                        placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="面谈结果" prop="meetingContent">
                    <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="面谈结果"
                        v-model="loseForm.meetingContent" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showFinishDialog = false">取 消</el-button>
                    <!-- <my-confirm-button type="submit" @click="submitMeeting" /> -->
                    <el-button type="primary" @click="submitMeeting">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 人才信息 -->
        <el-drawer title="人才信息" size="50%" :visible.sync="drawer" :direction="direction">
            <talentInformation v-if="drawer" :isEdit="isEdit" :candidateInfo="candidateInfo"></talentInformation>
        </el-drawer>
        <!-- 导入 -->
        <el-dialog title="导入花名册" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action
                            accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ uploadLoading ? "上传中" : "上传" }}
                            </el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/tableforvedio.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import postionDialogform from "@/views/profit/PersonnelRecruiting/postionDialogform";
import talentInformation from "@/views/profit/PersonnelRecruiting/talentInformation";
import { AllDDDeptTreeNcWh, getDeptUsers } from '@/api/profit/personnel'
import {
    pageCandidateSy, getCandidateInfo, exportCandidateEmployeeSy, addHrInterviewMeeting,
    bindEmployeeDDUserId, importCandidateEmp, delCandidate, editHrInterviewMeeting,syncDingding
} from '@/api/profit/hr'
import { formatTime } from "@/utils/tools";
import { ConsoleLogger } from '@microsoft/signalr/dist/esm/Utils';

const tableCols = [
    { istrue: true, prop: 'positionName', align: 'left', label: '岗位名称', sortable: 'custom', width: "280" },
    { istrue: true, prop: 'department', align: 'left', label: '招聘部门', sortable: 'custom', },
    { istrue: true, prop: 'name', align: 'left', label: '姓名', type: "click", handle: (that, row) => that.editPostion(row.candidateId, 0), sortable: 'custom', },
    { istrue: true, prop: 'gender', align: 'left', label: '性别', width: "80", formatter: (row) => row.gender == null ? '' : row.gender == 1 ? '男' : '女', sortable: 'custom', },
    { istrue: true, prop: 'phone', align: 'left', label: '联系电话', sortable: 'custom', },
    { istrue: true, prop: 'recruiter', align: 'left', label: '招聘专员', sortable: 'custom', },
    { istrue: true, prop: 'employmentDate', align: 'left', label: '入职时间', formatter: (row) => formatTime(row.employmentDate, 'YYYY-MM-DD'), sortable: 'custom', },
    { istrue: true, prop: 'candidateFrom', align: 'center', label: '类型', formatter: (row) => row.candidateFromTxt, sortable: 'custom' },
];

export default {
    name: "probationer",//试用人才
    components: {
        MyContainer, postionDialogform, MyConfirmButton
        , cesTable, talentInformation,
    },
    props: {
        showDialog: {
            type: Boolean,
            default: () => { return false; }
        },
        diologTitle: {
            type: String,
            default: () => { return ''; }
        },
    },
    watch: {
    },
    data () {
        return {
            showDialogSon: this.showDialog,
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            recruiterList: [],
            filter: {
                ddDeptId: null,//
                recruiterDDUserId: null,//
                name: null,//
                initialTestResult: null,//
                finalTestResult: null,//
                positionName: null,//
                queryType: 3,//1面试人才库、2预备人才库、3试用人才库、4正式人才库、5离职人才库
                keywords: null,
                startEndDateType: null,
                startDate: null,
                endDate: null,
                daterange: [],
                candidateFrom:null,
            },
            loseForm: {
                candidateId: null,
                meetingDate: null,
                meetingType: null,
                dimissionType: null,
                dimissionReason: null,
                dataSupport: null,
                meetingContent: null,
            },
            lostrules: {
                meetingContent: [
                    { required: true, message: '请输入面谈结果', trigger: 'blur' },
                ],
                meetingDate: [
                    { required: true, message: '请选择面谈日期', trigger: 'change' },
                ],
                meetingType: [
                    { required: true, message: '请选择面谈类型', trigger: 'change' },
                ],
                dimissionReason: [
                    { required: true, message: '请选择离职原因', trigger: 'change' },
                ],
                dimissionType: [
                    { required: true, message: '请选择离职类型', trigger: 'change' },
                ]
            },
            istLoading: false,
            summaryarry: {},
            datalist: [
            ],
            total: 0,
            sels: [], // 列表选中列
            listLoading: false,
            that: this,
            pageLoading: false,
            pager: {},
            tableCols: tableCols,
            isEdit: false,
            showFinishDialog: false,//显示完成弹窗
            drawer: false,
            direction: 'rtl',
            dialogVisible: false,
            uploadLoading: false,
            fileList: [],
            fileHasSubmit: false,
            candidateInfo: {},
            dialogTitle: null,
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            candidateInfoUpdate:0,
            syncLoading:false
        };
    },
    watch: {
    },
    async created () {

    },
    async mounted () {
        this.getDeptList();
        this.getRecruiters();
        this.onSearch();
    },
    methods: {

        // 提交面谈记录
        submitMeeting () {
            this.$refs.losFormRef.validate((valid) => {
                if (valid) {
                    this.subLoad = true
                    addHrInterviewMeeting(this.loseForm).then(res => {
                        this.subLoad = false
                        if (res.success) {
                            this.$message({ message: '提交成功', type: "success" });
                            this.showFinishDialog = false;
                        }
                    })
                }
            })
        },
        //获取招聘专员
        getRecruiters () {
            let params = {
                deptName: '人事组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.filter.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();
        },
        // 获取部门列表
        async getDeptList () {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 保存人才信息
        async submitTalent () {
            await this.$refs.talentInformation.submitForm();
        },
        //关闭弹窗
        closeDialog () {
            this.showDialogSon = false;
            this.$refs.talentInformation.resetFrom();
            this.onSearch();
        },
        //导出
        async onExport () {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await exportCandidateEmployeeSy(params);// 导出接口
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '试用人才_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        //下载导入模板
        downloadTemplate () {
            window.open("../../static/excel/hr/昀晗花名册导入模板.xlsx", "_self");//模板文件
        },
        // 筛选
        onSearch (filter) {
            this.$refs.pager.setPage(1);
            this.getDataList();
        },
        // 面谈
        losePostion (candidateId) {
            this.loseForm = {
                candidateId: null,
                meetingDate: null,
                meetingType: null,
                dimissionType: null,
                dimissionReason: null,
                dataSupport: null,
                meetingContent: null,
            }
            this.loseForm.candidateId = candidateId;
            this.showFinishDialog = true;
        },
        //删除
        deletePostion (candidateId) {
            this.$confirm('是否确认删除该条人才资料?', '删除人才信息', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await delCandidate({ candidateId: candidateId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        //完成绑定钉钉
        finishPostion (candidateId) {
            this.$confirm('是否确认该员通过面试考察，绑定钉钉?', '绑定钉钉', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await bindEmployeeDDUserId({ candidateId: candidateId })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '绑定成功!请尽快完善员工资料。' });
                this.onSearch()
            }).catch(() => {
                this.$message({ type: 'info', message: '取消绑定' });
            });
        },
        // 编辑
        async editPostion (candidateId, number) {
            await getCandidateInfo({ candidateId: candidateId }).then(res => {
                if (res.success) {
                    this.candidateInfo = res.data;
                }
            })
            if (number) {
                this.showDialogSon = true;
                this.dialogTitle = '编辑人才';
                this.isEdit = true;
                this.$nextTick(function () {
                    this.$refs.talentInformation.openPosition()
                    setTimeout(() => {
                        let scrollEl = this.$refs.show_prohationer;
                        scrollEl.scrollTo({ top: scrollEl.scrollHeight, behavior: 'smooth' });
                    }, 300)
                })
            } else {
                this.drawer = true;
                this.isEdit = false;
            }
        },

        //获取数据
        async getDataList () {
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            } else {
                this.filter.startDate = null;
                this.filter.endDate = null;
            }
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter
            };
            this.listLoading = true;
            const res = await pageCandidateSy(params);
            this.listLoading = false;
            this.total = res.data.total;
            this.datalist = res.data.list;
            this.summaryarry = res.data.summary;
        },

        //列表排序
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //多选事件
        selectchange: function (rows, row) {
            this.sels = [];
            rows.forEach(f => {
                this.sels.push(f.candidateId);
            })
        },
        //手动更新
        async onAll () {
            if (this.sels.length == 0) {
                this.$message({ type: 'warning', message: "请选择至少一个员工" });
                return;
            }
            this.$confirm("是否更新所选员工资料", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                return
                const res = await moveTaskToOver(this.sels)// 批量完成方法
                if (res?.success) {
                    this.$message({ message: '更新成功！', type: "success" });
                    this.sels = [];
                    this.onSearch();
                    var self = this;
                    setTimeout(() => { self.reload(); }, 100);
                }
            });
        },
        customRowStyle (row, index) {
            if (row.row?.isend && row.row.isend == 1) {
                let styleJson = {};
                styleJson.color = "rgb(216 216 216)";
                return styleJson
            } else {
                return null
            }

        },
        // 导入
        async onstartImport () {
            this.fileList = [];
            this.uploadLoading = false;
            this.dialogVisible = true;
            setTimeout(() => {
                this.$refs.upload.clearFiles();
            }, 1);
        },
        async submitUpload () {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        async uploadFile (item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            const form = new FormData();
            form.append("token", this.token);
            form.append("upfile", item.file);
            const res = await importCandidateEmp(form);//导入接口
            if (res.code == 1) {
                this.$message({ message: "上传成功,正在导入中...", type: "success" });
                this.fileList = []
                this.$refs.upload.clearFiles();
                this.onSearch();
            }
            // else this.$message({ message: res.msg, type: "warning" });
            this.dialogVisible = false
            this.uploadLoading = false;
        },
        async uploadChange (file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success") list.push(fileList[i]);
                    else list.push(fileList[i].raw);
                }
                this.fileList = list;
            } else {
                this.fileList = [];
            }
        },
        uploadRemove (file, fileList) {
            this.uploadChange(file, fileList);
        },
        async syncDingding(){
            if(!this.candidateInfo.dingTalkUserId)
            {
                this.$alert('没有绑定钉钉,无法同步!');
                return;
            }
            this.syncLoading= true;
            var form = await this.$refs.talentInformation.getform();
            await syncDingding(form).then(res => {
                if (res.success && res.data) {
                    this.candidateInfoUpdate ++;    
                    this.candidateInfo = res.data;
                    this.$message({ type: 'success', message: '同步钉钉花名册信息成功!' });
                }
            })
            this.syncLoading= false;
        }
    },
};
</script>