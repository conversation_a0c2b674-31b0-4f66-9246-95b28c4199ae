<template>
    <container v-loading="pageLoading">
        <template #header>
            <div class="top">
                <el-input v-if="filter.isShop" style="width:150px;" v-model.trim="filter.shopName" placeholder="平台"
                    clearable :maxlength="200" class="publicCss" />
                <el-input v-if="filter.isShop" style="width:150px;" v-model.trim="filter.shopPlatformType"
                    placeholder="店铺" clearable :maxlength="200" class="publicCss" />
                <!-- <el-input style="width:150px;" v-model.trim="filter.goodsCode" placeholder="商品编码" clearable
                    :maxlength="200" /> -->
                <inputYunhan ref="productCode" :inputt.sync="filter.goodsCode" v-model="filter.goodsCode" width="200px"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="50" :maxlength="1000"
                    @callback="callbackGoodsCode" title="商品编码" style="margin: 0 10px 0 0;">
                </inputYunhan>
                <el-select style="width:110px;margin-right: 10px;" v-model="filter.isPrePack" placeholder="是否符合预包"
                    :clearable="true" :collapse-tags="true">
                    <el-option label="符合" :value="true"></el-option>
                    <el-option label="不符合" :value="false"></el-option>
                </el-select>
                <el-select style="width:90px;margin-right: 10px;" v-model="filter.isProcessClaim" placeholder="是否认领"
                    :clearable="true" :collapse-tags="true">
                    <el-option label="已认领" :value="true"></el-option>
                    <el-option label="未认领" :value="false"></el-option>
                </el-select>
                <el-select filterable v-model="filter.trend" placeholder="销量趋势" clearable style="width: 90px">
                    <el-option v-for="item in saleoption" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="filter.operatingResult" clearable filterable placeholder="昨日"
                    style="width: 140px; margin-left: 10px;">
                    <el-option v-for="item in dayoption" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select filterable v-model="filter.operator" placeholder="= < >" clearable style="width: 80px">
                    <el-option label="=" :value="'Eq'"></el-option>
                    <el-option label=">" :value="'GreaterThan'"></el-option>
                    <el-option label=">=" :value="'GreaterThanOrEqual'"></el-option>
                    <el-option label="<" :value="'LessThan'"></el-option>
                    <el-option label="<=" :value="'LessThanOrEqual'"></el-option>
                </el-select>
                <el-input v-model.trim="filter.value" placeholder="输入订单数" style="width:150px;" clearable
                    :maxlength="200" />

                <el-select filterable v-model="filter.isCombineGoods" placeholder="是否组合装商品" clearable
                    style="width: 100px">
                    <el-option label="是" :value="true"></el-option>
                    <el-option label="否" :value="false"></el-option>
                </el-select>
                <el-button style="padding: 0; border: none;">
                    连续
                    <el-input-number clearable v-model="filter.continueDays" style="width:30px" :controls="false"
                        :precision="0" :min="1" :max="100"></el-input-number>
                    天
                    <el-input-number clearable v-model="filter.continueOrderCountMin" placeholder="最低单量"
                        style="width:60px" :controls="false" :precision="0" :min="1" :max="9999999"></el-input-number>
                    <el-input-number clearable v-model="filter.continueOrderCountMax" placeholder="最高单量"
                        style="width:60px" :controls="false" :precision="0" :min="1" :max="9999999"></el-input-number>
                </el-button>
                <el-button style="padding: 0; border: none;">
                    <el-input-number clearable v-model="filter.childWeightMin" placeholder="最低重量" style="width:60px"
                        :controls="false" :precision="2" :min="1" :max="9999999"></el-input-number>
                    <el-input-number clearable v-model="filter.childWeightMax" placeholder="最高重量" style="width:60px"
                        :controls="false" :precision="2" :min="1" :max="9999999"></el-input-number>
                </el-button>
                <el-select v-model="filter.warehouseId" placeholder="加工仓" clearable>
                    <el-option v-for="item in warehouselist1" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <el-button type="primary" @click="onSearch">搜索</el-button>
                <el-button type="primary" @click="important">导入</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button v-if="!filter.isShop && checkPermission('api:inventory:prePack:SavePrePackGoodsTreateAsync')"
                    type="danger" @click="streateDialogfuc">一键加工</el-button>
                <el-button v-if="checkPermission('api:inventory:prePack:BatchCreatePrePackCode')" type="primary"
                    @click="onCreatePrePackCode" :loading="createPrePackCodeLoading">一键生成预包编码</el-button>
                <el-button v-if="checkPermission('api:inventory:prepack:GetManifestsByCombineCodes')" type="primary"
                    @click="printProcess">打印加工清单</el-button>
                <el-switch v-model="filter.isShop" active-text="店铺" inactive-text="汇总" @change="changeIsShop">
                </el-switch>

            </div>
        </template>
        <!--列表-->
        <vxetablebase :id="'goodsrapidretirelog20230701'" :tableData='list' :tableCols='tableCols'
            @cellClick='cellClick' @select='callback' :tableHandles='tableHandles' :loading='listLoading'
            :border='true' :that="that" ref="vxetable" @sortchange='sortchange' :showsummary='true'
            :summaryarry='summaryarry' >
            <!-- @checkbox-range-end="selectchange" -->
            <!-- <template slot="right">
                <vxe-column title="操作" :field="'col_opratorcol'" width="120" fixed="right">
                    <template #default="{ row }">
                        <template>
                            <el-button type="text" @click="onSetInfo(row)">查看</el-button>
                        </template>
                    </template>
                </vxe-column>
            </template> -->
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入" :visible.sync="dialogImportVisible" width="40%" v-dialogDrag>
            <span>
                <span>
                    <el-date-picker style="width: 200px; margin-bottom: 10px;" v-model="yearMonth" type="date"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
                </span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :on-change="uploadChange" :on-remove="uploadRemove"
                            :http-request="uploadFile" :data="fileparm">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogImportVisible = false">关闭</el-button>
            </span>
        </el-dialog>


        <!-- 近15天销量趋势图 -->
        <el-dialog title="近15天销量" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <!--  -->
        <!-- <vxe-modal title="详情" v-model="dialogAddVisible" :esc-closable="true" width='60%' height='700px'>
            <goodsrapidretirelogdetail ref="goodsrapidretirelogdetail" style="height: 600px;" :filter="jstfilter"></goodsrapidretirelogdetail>
        </vxe-modal> -->
        <el-dialog title="一键加工" :visible.sync="streateDialog.visible" width="90%" v-dialogDrag
            :close-on-click-modal="false">
            <el-container v-loading="!preVisible" :style="{ height: !preVisible ? '500px' : '' }">
                <prepackgoodstreatelist @onTreateClose="onTreateClose" v-if="preVisible" :data="streateDialog.data"
                    @updatePage="updatePage" ref="prepackgoodstreatelist">
                </prepackgoodstreatelist>
            </el-container>

        </el-dialog>

        <el-dialog title="选择发货仓" :visible.sync="sendWareshow" width="30%" v-dialogDrag :close-on-click-modal="false">
            <el-select filterable v-if="selWareshow" v-model="filter.warehouseId" placeholder="选择发货仓" clearable
                style="width: 200px" @change="selchange">
                <el-option :label="item.label" :value="item.value" v-for="(item, i) in warehouselist"
                    :key="i"></el-option>
            </el-select>
            <span slot="footer" class="dialog-footer">
                <el-button @click="sendWareshow = false">取消</el-button>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="streateDialogfuc">确认</el-button>
            </span>
        </el-dialog>

        <el-dialog title="打印加工清单" :visible.sync="printProcessListVisable" width="60%" v-dialogDrag
            :close-on-click-modal="false">
            <printProcessList ref="printProcessList" v-if="printProcessListVisable" @close="closePrint"
                :printData="printData" />
        </el-dialog>
        <el-dialog title="打印加工清单" :visible.sync="printTableVisable" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <div style="max-height: 50vh;overflow: auto;" v-if="printTableVisable">
                <printTables ref="printTable" :batchNo="printBatchNo" v-if="printTableVisable" />
            </div>
            <template #footer>
                <el-button type="primary" @click="doPrint">打印</el-button>
            </template>
        </el-dialog>
    </container>
</template>

<script>
import { Loading } from 'element-ui';
import printProcessList from './printProcessList.vue'
import printTables from './printTables.vue'
import dayjs from "dayjs";
import { formatTime, formatNoLink } from "@/utils/tools";
import inputYunhan from "@/components/Comm/inputYunhan";
// import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getDirectorList } from '@/api/operatemanage/base/shop'
import { getManifestsByCombineCodes } from '@/api/inventory/prepack'
import buschar from '@/components/Bus/buschar'
import {
    //分页查询店铺商品资料
    pageGetPrePackStatAsync,
    exportPrePackStatAsync,
    showPrePackStatCharAsync,
    getTreateInfoByGoodsCodeAsync,
    batchCreatePrePackCode
} from "@/api/inventory/prepack.js"
import { getAllWarehouse } from '@/api/inventory/warehouse'

import {
    importGoodsSaleAsync
} from "@/api/inventory/prepackImport.js"

import prepackgoodstreatelist from './prepackgoodstreatelist.vue'

const tableCols = [
    // { istrue: true, prop: 'statDate', align: 'center', label: '统计日期', width: '120', sortable: 'custom', },
    // { istrue: true, prop: 'statDate', label: '统计日期', width: '120', sortable: 'custom', formatter: (row) => formatTime(row.statDate, 'YYYY-MM-DD HH:mm:ss') },
    //{ istrue: true, prop: 'statDate', align: 'center', label: '日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.statDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'checkbox', type: 'checkbox' },
    { istrue: true, prop: 'goodsCode', align: 'left', label: '商品编码', width: '105', sortable: 'custom', },
    { istrue: true, prop: 'goodsName',align: 'left', label: '商品名称', width: '300', },
    { istrue: true, prop: 'shopPlatformType', label: '平台', width: '80', },
    { istrue: true, prop: 'shopName', label: '店铺', width: '120', },
    { istrue: true, prop: 'combineDetailGoodsCount', align: 'center', label: '包含子商品数', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'warehouseName', align: 'left', label: '加工仓', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'sendWarehouseName', align: 'left', label: '发货仓', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'minInventoryDay', align: 'center', label: '最小编码周转天数', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'avgWeight', align: 'center', label: '快递重量', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'childWeight', align: 'center', label: '对应子编码数量的重量/克', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'childPackage', label: '对应子编码包装', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'packageConsumables', label: '打包耗材', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'quantity', label: '订单数', width: '90', sortable: 'custom', },
    { istrue: true, prop: 'wcQuantity', label: '实体编码销量', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'combineQuantity', label: '组合销量', width: '100', sortable: 'custom', },
    { istrue: true, type: 'echarts', prop: 'quantityChartData', chartProp: 'quantityChartData', fix: true, label: '趋势图', width: '80' },
    { istrue: true, prop: 'weekAvgQuantity', label: '7天日均订单数', width: '100', sortable: 'custom', },
    // { istrue: true, prop: 'isPrePack', label: '是否符合预包标准', width: '100', sortable: 'custom',formatter:(row)=> {return row.isPrePackString } },
    // { istrue: true, prop: 'isProcessClaim', label: '加工认领', width: '100', sortable: 'custom', formatter:(row)=> {return row.isProcessClaimString} },
    { istrue: true, prop: 'prePackCode', label: '预包编码', width: '105', sortable: 'custom', },

    { istrue: true, prop: 'prePackInventory', label: '预包编码库存', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'trend', propstring: 'trendString', label: '销量趋势（昨日.前天）', type: 'threeColor', width: '105', sortable: 'custom', formatter: (row) => { return row.trend }, },
    { istrue: true, prop: 'yesterdayQuantity', label: '昨日订单数', width: '85', sortable: 'custom', },

    { istrue: true, prop: 'beforeYesterdayQuantity', label: '前天订单数', width: '85', sortable: 'custom', },
    { istrue: true, prop: 'threeDayQuantity', label: '近3天订单数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'weekQuantity', label: '近7天订单数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'halfMonthQuantity', label: '近15天订单数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'monthQuantity', label: '近30天订单数', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'isCombineGoods', label: '是否组合装', width: '85', sortable: 'custom', formatter: (row) => { return row.isCombineGoodsString } },

];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];



//销量趋势
let saleoption = [
    { label: '下降', value: 2 },
    { label: '上升', value: 1 },
    { label: '持平', value: 0 },
]

//昨日
let dayoption = [
    { label: '昨日订单数', value: 'yesterdayQuantity' },
    { label: '前天订单数', value: 'beforeYesterdayQuantity' },
    { label: '近3天订单数', value: 'threeDayQuantity' },
    { label: '近7天订单数', value: 'weekQuantity' },
    { label: '近15天订单数', value: 'halfMonthQuantity' },
    { label: '7天日均订单数', value: 'weekAvgQuantity' },
]


const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminGoodsrapidretire',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan, buschar, prepackgoodstreatelist, printProcessList, printTables },

    data() {
        return {
            printTableVisable: false,
            that: this,
            pageLoading: false,
            dialogImportVisible: false,
            sendWareshow: false,
            selWareshow: true,
            fileList: [],
            warehouselist: [],
            yearMonth: "",
            fileparm: {},
            uploadLoading: false,
            saleoption: saleoption,
            dayoption: dayoption,
            printProcessListVisable: false,//打印加工清单
            filter: {
                goodsCode: null,
                operator: null,
                operatingResult: null,
                trend: null,
                isProcessClaim: null,
                isPrePack: null,
                isCombineGoods: null,
                //continueDays:null,
                //continueOrderCount :null,
                isShop: false,
                warehouseId: null,
            },
            jstfilter: {
                id: null
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { orderBy: "statDate", isAsc: false },
            total: 0,
            sels: [],
            selids: [],
            groupList: [],
            directorList: [],
            listLoading: false,
            pageLoading: false,
            dialogAddVisible: false,
            onFinishLoading: false,
            dialogLoading: false,
            buscharDialog: {
                visible: false,
                data: null
            },
            summaryarry: {},
            treateList: [],
            streateDialog: {
                visible: false,
                data: null
            },
            createPrePackCodeLoading: false,
            warehouselist1: [],
            printList: [],
            printData: [],
            printBatchNo: null,
            preVisible: false
        };
    },

    async mounted() {
        await this.init();
        await this.onSearch();
        await this.getwarehouse();
        this.list = this.data;
    },

    methods: {
        updatePage(list) {
            this.preVisible = false
            this.streateDialog.data = list;
            setTimeout(() => {
                // this.preVisible = true
                this.$nextTick(() => {
                    this.preVisible = true
                    //     this.$refs.prepackgoodstreatelist.closeLoading()
                })
            }, 1000)
            // this.preVisible = true;
        },
        doPrint() {
            this.$nextTick(() => {
                this.$refs.printTable.doPrint()
            })
        },
        closePrint(batchNo) {
            this.printProcessListVisable = false
            if (batchNo) {
                this.printBatchNo = batchNo;
                this.printTableVisable = true
            }
        },
        verify(verifyArr) {
            let arr = []
            arr = verifyArr.map((item, i) => {
                if (!item.isCombineGoods) {
                    return this.$message.error(`选择的第${i + 1}条数据不是组合装商品`)
                } else {
                    return item.goodsCode
                }
            })
            return arr
        },
        async printProcess() {
            if (this.sels.length == 0) return this.$message({ message: "请至少选择一条数据", type: "warning", });
            const val = this.verify(this.printList)
            const { data, success } = await getManifestsByCombineCodes(val)
            if (success) {
                this.printData = data
                this.printProcessListVisable = true
            }
        },
        callbackGoodsCode(val) {
            this.filter.goodsCode = val;
        },
        selchange(val) {
            window.localStorage.setItem('expectedgoodsIndex', val)
            this.selWareshow = false;
            this.selWareshow = true;
        },
        async getwarehouse() {
            let res3 = await getAllWarehouse();
            this.warehouselist = res3.data.filter((x) => x.isSendWarehouse == '是');
            this.warehouselist.map((x) => {
                x.value = x.wms_co_id;
                x.label = x.name;
            });
            // this.list=this.data;
        },
        async onExport() {
            if (this.filter.continueOrderCountMin != null && this.filter.continueOrderCountMax != null) {
                if (this.filter.continueOrderCountMax < this.filter.continueOrderCountMin) {
                    this.$message({ message: "最大单量不能小于最小单量", type: "warning", });
                    return;
                }
            }
            var page = this.pager;
            const params = { ...page, ... this.filter }
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            var res = await exportPrePackStatAsync(params);
            loadingInstance.close();
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/zip" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '导出预包编码数据_' + new Date().toLocaleString() + '.zip')
            aLink.click();
        },
        submitUpload() {
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请先选择文件", type: "warning" });
                return false;
            }
            this.uploadLoading = true
            this.$refs.upload.submit();
        },
        async uploadFile(item) {
            if (!this.yearMonth) {
                this.$message({ message: "请先选择时间", type: "warning" });
                this.uploadLoading = false
                return false;
            }
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先选择文件", type: "warning" });
                this.uploadLoading = false
                return false;
            }
            const form = new FormData();
            form.append("file", item.file);
            form.append("statDate", this.yearMonth);
            var res = await importGoodsSaleAsync(form);
            this.uploadLoading = false

            this.fileList = [];
            this.$refs.upload.clearFiles();
            if (!res?.success) {
                return
            }
            this.dialogImportVisible = false;
            this.$message.success("上传成功,正在导入中...")

            this.onSearch();

        },
        uploadChange(file, fileList) {
            if (fileList && fileList.length > 0) {
                var list = [];
                for (var i = 0; i < fileList.length; i++) {
                    if (fileList[i].status == "success")
                        list.push(fileList[i]);
                    else
                        list.push(fileList[i].raw);
                }
                this.fileList = list;
            }
        },
        uploadRemove(file, fileList) {
            this.fileList = fileList;
            // this.uploadChange(file, fileList);
        },
        important() {
            this.dialogImportVisible = true;
        },
        async onSearch() {
            this.sels = [];
            if (this.filter.continueOrderCountMin != null && this.filter.continueOrderCountMax != null) {
                if (this.filter.continueOrderCountMax < this.filter.continueOrderCountMin) {
                    this.$message({ message: "最大单量不能小于最小单量", type: "warning", });
                    return;
                }
            }
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        async init() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
            const res1 = await getDirectorList({});
            this.directorList = [{ key: '0', value: '未知' }].concat(res1.data || []);
            let { data } = await getAllWarehouse();
            let result = data.filter((x) => x.name.indexOf('代发') < 0);
            this.warehouselist1 = result;
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            // var pager = {};

            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageGetPrePackStatAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data;
            this.summaryarry = res.data.summary;
            this.$refs.vxetable.loadRowEcharts();
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },
        async onSetInfo(row) {
            this.jstfilter.id = row.id;
            this.dialogAddVisible = true;
            this.$nextTick(async () => {
                await this.$refs.goodsrapidretirelogdetail.onSearch();
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
        changeIsShop: function () {
            this.pager = { orderBy: "statDate", isAsc: false }
            if (!this.filter.isShop) {
                this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('shopPlatformType'))
                this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('shopName'))
                this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('warehouseName'))
                this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('sendWarehouseName'))
                this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('minInventoryDay'))
            } else {
                this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('shopPlatformType'))
                this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('shopName'))
                this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('warehouseName'))
                this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('sendWarehouseName'))
                this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('minInventoryDay'))
            }
            this.sels = [];
            this.onSearch();
        },
        async cellClick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column == null) {
                return;
            }
            if (column.property == 'quantity' || column.property == 'quantityChartData') {
                var params = new Object();
                params.goodsCode = row.goodsCode;
                params.shopCode = row.shopCode;
                params.shopPlatformType = row.shopPlatformType;
                params.goodsName = row.goodsName;
                params.isShop = this.filter.isShop;
                await this.showChar(params);
            }
        },
        async showChar(params) {
            const res = await showPrePackStatCharAsync(params);
            let that = this;
            if (res && res.success) {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data;
                that.$nextTick(() => {
                    that.$refs.buschar.initcharts();
                });
            }
        },
        async onTreate() {
            if (this.sels.length == 0) {
                this.$message({ message: "请至少选择一条数据", type: "warning", });
                return;
            }
            await this.getwarehouse();
            if (window.localStorage.getItem('expectedgoodsIndex')) {
                this.filter.warehouseId = parseInt(window.localStorage.getItem('expectedgoodsIndex'));
            }

            this.sendWareshow = true;
        },
        async streateDialogfuc() {
            this.preVisible = false
            this.streateDialog.visible = false;
            // if (!this.filter.warehouseId) {
            //     this.$message({ message: "请至少一个仓库", type: "warning", });
            //     return
            // }
            if (this.sels.length == 0) {
                this.$message({ message: "请至少选择一条数据", type: "warning", });
                return;
            }
            this.sendWareshow = false;
            this.pageLoading = true;
            let yesterdayQuantity;
            // let goodsCodes = [];
            let par = new Object();
            par.details = [];
            this.sels.forEach(x => {
                // goodsCodes.push(x.goodsCode);
                par.details.push({ goodsCodes: x.goodsCode, yesterdayQuantity: x.yesterdayQuantity,yesterdayWcQuantity:x.wcQuantity,yesterdayCombineQuantity:x.combineQuantity })
            });
            // let  a ={}
            // a.goodsCodes  = 每行的code ;
            // a.yesterdayQuantity =每行的值；

            // par.details.goodsCodes  = goodsCodes;
            // par.details.yesterdayQuantity  = yesterdayQuantity; //昨日订单数

            par.warehouseId = this.filter.warehouseId;

            window.localStorage.setItem('expectedgoodsIndex', this.filter.warehouseId)
            const res = await getTreateInfoByGoodsCodeAsync(par);
            let that = this;
            this.streateDialog.data = [];
            if (res && res.success) {
                that.sels.forEach(item => {
                    let opt = res.data.find(x => x.goodsCode == item.goodsCode);
                    item.combineDetailGoodsCount = opt.combineDetailGoodsCount;
                    item.isSameWareHouse = opt.isSameWareHouse;
                    item.minInventoryDay = opt.minInventoryDay;
                    item.minMasterStock = opt.minMasterStock;
                    item.wareHouseId = opt.wareHouseId ? opt.wareHouseId : '';
                    item.childWeight = opt.childWeight;
                    item.childPackage = opt.childPackage;
                    item.packageConsumables = opt.packageConsumables;
                    item.isProcessClaim = opt.isProcessClaim;
                    item.prePackCode = opt.prePackCode;
                    item.prepackInventory = opt.prepackInventory;
                    item.isCombineGoods = opt.isCombineGoods;
                    item.treateCount = opt.treateCount;
                    item.childGoodsCodes = opt.childGoodsCodes;
                    item.isCanPackage = opt.isCanPackage;
                    item.sendWarehouseId = opt.sendWarehouseId ? parseInt(opt.sendWarehouseId) : '';
                    item.sendWarehouseInventory = opt.sendWarehouseInventory;
                    item.packageWarehouseListsmy = [];
                    item.sendWarehouseListsmy = [];
                    if (opt?.packageWarehouseLists?.length > 0) {
                        opt.packageWarehouseLists.map((itemm) => {
                            itemm.label = itemm.name;
                            itemm.value = itemm.wms_co_id;
                        });
                        item.packageWarehouseListsmy = opt.packageWarehouseLists;
                    }
                    if (opt?.sendWarehouseLists?.length > 0) {
                        opt.sendWarehouseLists.map((itemm) => {
                            itemm.label = itemm.name;
                            itemm.value = parseInt(itemm.wms_co_id);
                        });
                        item.sendWarehouseListsmy = opt.sendWarehouseLists;
                    }



                    // item.yesterdayQuantity = item.yesterdayQuantity;
                    // let treateCount = item.weekAvgQuantity;
                    // if (item.quantity > treateCount) {
                    //     treateCount = item.quantity;
                    // }
                    // if (item.minMasterStock < treateCount) {
                    //     treateCount = item.minMasterStock;
                    // }
                    // item.treateCount = treateCount;
                    that.streateDialog.data.push(item);
                });
                this.preVisible = true;
                that.streateDialog.visible = true;
            }
            this.pageLoading = false;
        },
        async onTreateClose() {
            this.streateDialog.visible = false;
        },
        callback(val) {
            this.sels = [];
            this.printList = val
            val.forEach(f => {
                let ob = new Object();
                ob.goodsCode = f.goodsCode;
                ob.goodsName = f.goodsName;
                ob.quantity = f.quantity;
                ob.weekAvgQuantity = f.weekAvgQuantity;
                ob.avgWeight = f.avgWeight;
                ob.yesterdayQuantity = f.yesterdayQuantity;
                ob.prePackCode = f.prePackCode;
                ob.wcQuantity = f.wcQuantity;
                ob.combineQuantity = f.combineQuantity;
                this.sels.push(ob);
            })
        },
        async onCreatePrePackCode() {
            if (this.sels.length == 0) {
                this.$message({ message: "请至少选择一条数据", type: "warning", });
                return;
            }
            let isCheck = false;
            this.sels.forEach(f => {
                if (!f.goodsCode || f.goodsCode.indexOf("ZH-") != 0 || f.prePackCode) {
                    isCheck = true;
                    return;
                }
            });
            if (isCheck) {
                this.$message({ message: "勾选数据中包含非组合装商品编码或已生成预包编码，请核实", type: "warning", });
                return;
            }
            this.$confirm('确定要将勾选的数据生成预包编码吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(async () => {
                this.createPrePackCodeLoading = true;
                const res = await batchCreatePrePackCode(this.sels);
                this.createPrePackCodeLoading = false;
                if (res && res.success) {
                    this.$message({ message: "操作成功", type: "success", });
                }
                else {
                    //this.$message({ message: "操作失败", type: "error", });
                }
            }).catch(() => { });
        },
    },
};
</script>

<style lang="scss" scoped>
.top {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
