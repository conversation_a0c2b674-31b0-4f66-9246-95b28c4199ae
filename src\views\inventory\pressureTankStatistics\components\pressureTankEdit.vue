<template>
    <MyContainer>
        <el-form :model="ruleForm" status-icon :rules="rules" style="width: 90%;" ref="ruleForm" label-width="100px"
            class="demo-ruleForm">
            <el-form-item label="商品编码:">
                <div>{{ ruleForm.goodsCode }}</div>
            </el-form-item>
            <el-form-item label="厂家信息:" prop="supplierList">
                <el-button type="text" @click="addprops">新增一行</el-button>
                <el-table :data="ruleForm.supplierList" style="width: 95%" :max-height="150">
                    <el-table-column label="厂家名称">
                        <template #default="{ row }">
                            <el-input v-model="row.supplierName" style="width: 180px;" maxlength="40"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="厂家链接">
                        <template #default="{ row }">
                            <el-input v-model="row.supplierLink" style="width: 180px;" maxlength="499"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="{ row, $index }">
                            <el-button type="danger" @click="ruleForm.supplierList.splice($index, 1)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="长(cm):" prop="length">
                        <el-input-number :controls="false" v-model="ruleForm.length" :precision="2" :min="0" :max="999"
                            style="width: 180px;" placeholder="长(cm)" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="宽(cm):" prop="width">
                        <el-input-number :controls="false" v-model="ruleForm.width" :precision="2" :min="0" :max="999"
                            style="width: 180px;" placeholder="宽(cm)" />
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="高(cm)" prop="height">
                        <el-input-number :controls="false" v-model="ruleForm.height" :precision="2" :min="0" :max="999"
                            style="width: 180px;" placeholder="高(cm)" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="重量(kg)" prop="weight">
                        <el-input-number :controls="false" v-model="ruleForm.weight" :precision="2" :min="0" :max="999"
                            style="width: 180px;" placeholder="重量(cm)" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="产品成分:" prop="ingredient">
                        <el-input v-model="ruleForm.ingredient" style="width: 180px;" maxlength="40" clearable
                            placeholder="产品成分"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="备注:" prop="remark">
                        <el-input v-model="ruleForm.remark" style="width: 180px;" type="textarea" maxlength="40"
                            placeholder="备注"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="产品图片:" prop="goodsImgUrl">
                <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true"
                    :uploadInfo="chatUrls" :keys="[1, 1]" @callback="getImg" :imgmaxsize="1" :limit="1"
                    :multiple="true">
                </uploadimgFile>
            </el-form-item>
            <el-form-item>
                <el-button @click="$emit('close')">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
            </el-form-item>
        </el-form>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import dateRange from "@/components/date-range/index.vue";
import { GetPressureTankStatisticsInfo, SavePressureTankStatistics } from '@/api/operatemanage/pressureTankStatistics'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, uploadimgFile
    },
    props: {
        id: {
            type: String,
            default: () => ''
        }
    },
    data() {
        return {
            that: this,
            ruleForm: {
                supplierList: [
                    {
                        supplierName: '',
                        supplierLink: ''
                    }
                ],
                ingredient: '',
                remark: '',
                goodsImgUrl: ''
            },
            rules: {
                supplierList: [
                    { required: true, message: '请输入厂家信息', trigger: 'blur' }
                ],
                length: [
                    { required: true, message: '请输入长', trigger: 'blur' }
                ],
                width: [
                    { required: true, message: '请输入宽', trigger: 'blur' }
                ],
                height: [
                    { required: true, message: '请输入高', trigger: 'blur' }
                ],
                weight: [
                    { required: true, message: '请输入重量', trigger: 'blur' }
                ],
                ingredient: [
                    { required: true, message: '请输入产品成分', trigger: 'blur' }
                ],
                remark: [
                    { required: true, message: '请输入备注', trigger: 'blur' }
                ],
                goodsImgUrl: [
                    { required: true, message: '请上传产品图片', trigger: 'blur' }
                ]
            },
            timeRanges: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            chatUrls: [],
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            editPriceVisible: false
        }
    },
    created() {
        this.editPriceVisible = false
    },
    async mounted() {
        await this.getList(this.id)
    },
    methods: {
        addprops() {
            this.ruleForm.supplierList.push({
                supplierName: '',
                supplierLink: ''
            })
        },
        getImg(data) {
            if (data) {
                this.chatUrls = data
                this.ruleForm.goodsImgUrl = data.map(item => item.url).join(',')
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await SavePressureTankStatistics(this.ruleForm)
                    if (!success) return
                    this.$message.success('操作成功')
                    this.$emit('close')
                    this.$emit('getList')
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        async getList(id) {
            try {
                const { data, success } = await GetPressureTankStatisticsInfo({ id })
                if (success) {
                    this.chatUrls = data.goodsImgUrl?.split(',').map((item, i) => {
                        return {
                            url: item,
                            name: `聊天截图${i + 1}`
                        }
                    }) || []
                    this.ruleForm = data
                } else {
                    this.loading = false
                    this.$message.error('获取明细数据失败')
                }
            } catch (error) {
                this.loading = false
            } finally {
                this.editPriceVisible = true
                this.loading = false
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
