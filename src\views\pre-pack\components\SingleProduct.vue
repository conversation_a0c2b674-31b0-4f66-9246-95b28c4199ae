<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <inputYunhan ref="productCode" :inputt.sync="query.goodsCode" v-model="query.combineCode" width="190px"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="goodsCodeCallback" title="商品编码" style="margin: 0 5px 5px 0;">
                </inputYunhan>
                <el-input v-model.trim="query.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <!-- <el-input v-model.trim="query.groupName" placeholder="小组名称" maxlength="50" clearable
                    class="publicCss" /> -->
                <!-- <inputYunhan ref="productCode" :inputt.sync="query.skus" v-model="query.skus" width="190px"
                    placeholder="skus/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="SkusCallback" title="skus" style="margin: 0 5px 5px 0;">
                </inputYunhan> -->
                <el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="未确认" :value="0" />
                    <el-option label="加工" :value="1" />
                    <el-option label="不加工" :value="2" />
                </el-select>
                <number-range class="publicCss1" :min.sync="query.confirmQtyMin" :max.sync="query.confirmQtyMax"
                    min-label="确认数量最小值" max-label="确认数量最大值" />
                <chooseWareHouse v-model="query.sendWmsId" class="publicCss" :filter="sendWmsesFilter" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
                    <el-button type="primary" :disabled="isExport" v-if="checkPermission('batchProcess')"
                        @click="openBatchSetInvtory(1)">确认加工</el-button>
                    <el-button type="primary" :disabled="isExport" v-if="checkPermission('batchProcess')"
                        @click="batchProcess(2)">确认不加工</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'SingleProduct202408041854'" ref="table" v-loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="false" :is-index-fixed="false" style="width: 100%;  margin: 0" :height="'100%'"
            @select="checkboxRangeEnd" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange">
            <template #combineCode="{ row }">
                <prepackComfirm :width="1000" :condition="{ goodsCode: row.combineCode, date: row.startDate }"
                    :columns="['groupName', 'operateSpecialName', 'operateAssistantName', 'goodsCode', 'platformStr', 'proCode', 'days1', 'day3Qty', 'day7Qty', 'dayAvgIn3', 'dayAvgIn7', 'confirmResultStr']">
                    {{ row.combineCode }}</prepackComfirm>
            </template>
            <template #confirmResultStr="{ row }">
                <el-button type="text" @click="openLogs(row.goodsCode, row.startDate)">查看</el-button>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange"
                :sizes="[50, 100, 300, 500, 1000]" />
        </template>
        <el-dialog v-dialogDrag title="确认历史" :visible.sync="logVisible" width="50%">
            <singleGoodCodeHistory :goodsCode="goodsCode" v-if="logVisible" />
        </el-dialog>

        <el-dialog v-dialogDrag title="确认加工" :visible.sync="batchSetInvtoryVisable" width="15%">
            <div style="display: flex;justify-content: center;margin-bottom: 10px;">
                <chooseWareHouse v-model="batchSetInfo.sendWmsId" style="width: 100%;" :filter="sendWmsesFilter"
                    @chooseWms="chooseWms" v-if="batchSetInvtoryVisable" />
            </div>
            <div style="display: flex;justify-content: center;">
                <el-button @click="batchSetInvtoryVisable = false">取消</el-button>
                <el-button type="primary" @click="batchSubmit" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
import day7 from './day7.vue'
import numberRange from '@/components/number-range/index.vue'
import { download } from '@/utils/download'
import prepackComfirm from './prepackConfirm.vue'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import singleGoodCodeHistory from './singleGoodCodeHistory.vue'
import {
    getColumns, pageGetData, exportData, getLogColumns, logPageGetData, sureAndAlarm, getColumns_SingleGoods, pageGetData_SingleGoods, exportData_SingleGoods
} from '@/api/vo/operateConfirm'
import inputYunhan from "@/components/Comm/inputYunhan";
export default {
    name: 'ScanCodePage',
    components: {
        MyContainer, vxetablebase, day7, prepackComfirm, numberRange, inputYunhan, chooseWareHouse, singleGoodCodeHistory
    },
    data() {
        return {
            wmsesList: [],
            rules: {
            },
            that: this,
            query: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: '',
                endDate: '',
                type: 1
            },
            data: { total: 0, list: [], summary: {} },
            tableCols: [],
            loading: false,
            isExport: false,
            ruleForm: {},
            batchSetInvtoryVisable: false,
            chooseList: [],
            logVisible: false,
            logTableCols: [],
            logData: [],
            chooseList: [],
            batchSetInfo: {
                sendWmsId: '',
                sendWmsName: '',
                items: [],
                confirmResult: null
            },
            goodsCode: ''
        }
    },
    async mounted() {
        await this.getCol()
        this.getList()
    },
    methods: {
        chooseWms(data) {
            this.batchSetInfo.sendWmsName = data.name
        },
        openBatchSetInvtory(val) {
            this.batchSetInfo = {
                sendWmsId: '',
                sendWmsName: '',
                items: this.chooseList,
                confirmResult: null
            }
            if (this.chooseList.length == 0) return this.$message.error('请选择数据')
            this.batchSetInfo.confirmResult = val
            this.batchSetInvtoryVisable = true
        },
        async batchSubmit() {
            if (!this.batchSetInfo.sendWmsId) return this.$message.error('请选择仓库')
            const { success } = await sureAndAlarm(this.batchSetInfo)
            if (success) {
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
                await this.getList()
                this.batchSetInvtoryVisable = false
            }
        },
        sendWmsesFilter(wmses) {
            this.wmsesList = wmses;
            return wmses.filter((a) => a.isSendWarehouse === "是");
        },
        SkusCallback(data) {
            this.query.skus = data
        },
        goodsCodeCallback(data) {
            this.query.goodsCode = data
        },
        entityCodeCallback(data) {
            this.query.entityCode = data
        },
        checkboxRangeEnd(row) {
            this.chooseList = row.map(item => { return { goodsCode: item.goodsCode, startDate: item.startDate } })
            console.log(this.chooseList)
        },
        batchProcess(confirmResult) {
            if (this.chooseList.length == 0) return this.$message.error('请选择数据')
            this.$confirm(`此操作将批量${confirmResult == 1 ? '加工' : '不加工'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await sureAndAlarm({ items: this.chooseList, confirmResult })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    await this.getList()
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });

        },
        async openLogs(goodsCode, date) {
            this.goodsCode = goodsCode
            this.logVisible = true
        },
        async getCol() {
            const { data, success } = await getColumns_SingleGoods()
            if (success) {
                data.unshift({
                    label: '',
                    type: 'checkbox'
                })
                data.push({ label: '确认历史', prop: 'confirmResultStr' })
                this.tableCols = data
                this.query.summarys = data.filter(a => a.summaryType).map(a => { return { column: a['sort-by'], summaryType: a.summaryType } })
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await exportData_SingleGoods(this.query).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type === 'search') {
                this.query.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await pageGetData_SingleGoods(this.query)
                if (success) {
                    this.data = data
                } else {
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.$message.error('获取列表失败')
            } finally {
                this.loading = false
            }
        },
        // 每页数量改变
        Sizechange(val) {
            this.query.currentPage = 1
            this.query.pageSize = val
            this.getList()
        },
        // 当前页改变
        Pagechange(val) {
            this.query.currentPage = val
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.query.orderBy = prop
                this.query.isAsc = order.indexOf('descending') === -1
                this.getList()
            }
        }
    }
}
</script>
<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;

    .publicCss {
        width: 175px;
        margin: 0 10px 5px 0;
    }

    .publicCss1 {
        height: 28px;
        width: 210px;
        margin: 0 5px 5px 0;
    }
}
</style>