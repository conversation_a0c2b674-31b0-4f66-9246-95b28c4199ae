<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-date-picker style="width: 260px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="爆款开始日期"
                        end-placeholder="爆款结束日期" :clearable="true" :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-input v-model.trim="filter.styleCode" placeholder="款式编码" style="width:120px;" clearable
                        maxlength="40" />
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable v-model="filter.goodGroupIds" placeholder="挂靠小组" style="width: 160px" multiple
                        collapse-tags clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable v-model="filter.canGroupIds" placeholder="可上架小组" style="width: 160px" multiple
                        collapse-tags clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.isTxUp" placeholder="淘系是否可做" style="width: 120px">
                        <el-option label="是" value=true></el-option>
                        <el-option label="否" value=false></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.isRise" placeholder="是否起量" style="width: 120px">
                        <el-option label="是" value=true></el-option>
                        <el-option label="否" value=false></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.isAlone" placeholder="是否独立发展小组"
                        style="width: 120px">
                        <el-option label="是" value=true></el-option>
                        <el-option label="否" value=false></el-option>
                    </el-select>
                </el-button>

                <div>
                    <el-button-group style="margin-right:5px;">
                        <el-button type="primary" @click="onSearch">查询</el-button>
                        <el-button type="primary" @click="onImport">导入</el-button>
                        <el-button type="primary" @click="onModel">下载模板</el-button>
                        <el-button type="primary" @click="onExport">导出</el-button>
                    </el-button-group>
                </div>
            </el-button-group>
        </template>

        <vxetablebase :id="'burststyleprotectpddnoup20231019'" :border="true" :align="'center'"
            :tablekey="'burststyleprotectpddnoup20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='false' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999" @select="callback">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog title="导入" :visible.sync="dialogUploadData.visible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="dialogUploadData.uploadLoading" @click="onSubmitUpload">{{
                                    (dialogUploadData.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row v-if="dialogUploadData.showError" style="color:darkorange;">
                    <span v-for="(err, errIndex) in dialogUploadData.showErrorList" :key="errIndex">{{ err
                        }};<br /></span>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogUploadData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="趋势图" :visible.sync="drawerVisible" width="80%" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="quantityprocessed.visible" ref="drawerbuschar"
                        :analysisData="quantityprocessed.data">
                    </buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="drawerVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";

import { getDirectorGroupList, getList as getshopList, getDirectorList } from '@/api/operatemanage/base/shop'
import { GetBurstStyleProtectPddNoUpPageList, ImportBurstStyleProtectPddNoUp, ExportBurstStyleProtectPddNoUpList } from '@/api/operatemanage/burststyleprotect'
import buschar from '@/components/Bus/buschar'

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '款式编码', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'canGroupIds', label: '可上架小组', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'isTxUp', label: '淘系是否可做', sortable: 'custom', width: '150', formatter: (row) => row.isTxUp == true ? "是" : "否" },
    { istrue: true, prop: 'burstStyleTime', label: '保护开始时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'isRise', label: '是否起量', sortable: 'custom', width: '150', formatter: (row) => row.isRise == true ? "是" : "否" },
    { istrue: true, prop: 'goodGroupIds', label: '编码挂靠小组', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'isAlone', label: '是否独立发展小组', sortable: 'custom', width: '150', formatter: (row) => row.isAlone == true ? "是" : "否" },
    { istrue: true, prop: 'createdTime', label: '导入时间', sortable: 'custom', width: '150' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs(), "YYYY-MM-DD");
const endDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
    name: "burststyleprotectpddnoup",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan, buschar
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                goodGroupIds: [],
                canGroupIds: [],
            },
            weeks: [1, 2, 3, 4],
            directorlist: [],
            drawerVisible: false,
            quantityprocessed: { visible: false, title: "", data: {} },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "burstStyleTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],

            platformlist: platformlist,
            shopList: [],
            directorGroupList: [],

            fileList: [],
            fileparm: {},

            dialogUploadData: {
                title: "",
                visible: false,
                uploadLoading: false,
                showError: false,
                showErrorList: [],
            },
        };
    },
    async mounted() {
        await this.getloadgroupselect();
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        async getloadgroupselect() {
            const res2 = await getDirectorGroupList({});
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);

            let res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            else {
                this.filter.startDate = '2020-01-01';
                this.filter.endDate = '2030-01-01';
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetBurstStyleProtectPddNoUpPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        callback(val) {
            this.selrows = [...val];
        },
        async onExport() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await ExportBurstStyleProtectPddNoUpList(params)
            this.listLoading = false;
            if (!res?.data) {
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "拼多多不可上架编码_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onModel() {
            window.open("/static/excel/productalllink/拼多多不可上架编码导入模板.xlsx", "_blank");
        },
        async onImport() {
            this.dialogUploadData.showError = false;
            this.dialogUploadData.showErrorList = [];
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            let res = await ImportBurstStyleProtectPddNoUp(form);
            if (res?.success) {
                this.$message({ message: "导入成功", type: "success" });
                this.dialogUploadData.showError = false;
                this.dialogUploadData.visible = false;
            }
            else {
                if (res?.data) {
                    this.dialogUploadData.showErrorList = res?.data;
                    this.dialogUploadData.showError = true;
                }
            }
            this.dialogUploadData.uploadLoading = false;
        },
        async onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.showError == false) {
                this.dialogUploadData.visible = false;
                await this.onSearch();
            }
        },
        onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async callbackProCode(val) {
            this.filter.proCode = val;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-select__tags-text {
    max-width: 70px;
}
</style>
