<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="京喜日报" name="first1" style="height: 100%" >
        <productReportJd ref="productReportJd" style="height: 100%" :shopType="shopType"></productReportJd>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('JdOrderDayReport')">
        <JdOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="JdOrderDayReport" style="height: 100%"  :shopType="shopType" >
        </JdOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('JdGoodCodeDayReport')">
        <JdGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="JdGoodCodeDayReport" style="height: 100%"  :shopType="shopType"></JdGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%" v-if="checkPermission('JdIdDayReport')">
        <JdIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="JdIdDayReport"
          style="height: 100%"  :shopType="shopType"></JdIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%" v-if="checkPermission('JdShopDayReport')">
        <JdShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="JdShopDayReport"
          style="height: 100%"  :shopType="shopType"></JdShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('JdCommodityDayReport')">
        <JdCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="JdCommodityDayReport" style="height: 100%"  :shopType="shopType"></JdCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('JdDetailDayReport')">
        <JdDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="JdDetailDayReport" style="height: 100%"  :shopType="shopType"></JdDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('JdOutgoingprofitIDorderdetail')">
        <JdOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="JdOutgoingprofitIDorderdetail" style="height: 100%"  :shopType="shopType"></JdOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('JdOrderDayReport')">
        <JdSkusDayReport @ChangeActiveName2="ChangeActiveName2"  :shopType="shopType" ref="JdSkusDayReport" style="height: 100%">
        </JdSkusDayReport>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportJd from "./productReportJd.vue";
import JdOrderDayReport from "./JdOrderDayReport.vue";
import JdSkusDayReport from "./JdSkusDayReport.vue";
import JdGoodCodeDayReport from "./JdGoodCodeDayReport.vue";
import JdIdDayReport from "./JdIdDayReport.vue";
import JdCommodityDayReport from "./JdCommodityDayReport.vue";
import JdOutgoingprofitIDorderdetail from "./JdOutgoingprofitIDorderdetail.vue";
import JdShopDayReport from "./JdShopDayReport.vue";
import JdDetailDayReport from "./JdDetailDayReport.vue";
import middlevue from "@/store/middle.js"
export default {
  name: "productReportGCIndex",
  components: {
    MyContainer, productReportJd, JdOrderDayReport,JdSkusDayReport, JdGoodCodeDayReport, JdIdDayReport, JdShopDayReport, JdDetailDayReport, JdCommodityDayReport, JdOutgoingprofitIDorderdetail
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
      shopType: 1,
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'jd') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
   
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.JdOrderDayReport.JdGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.JdDetailDayReport.JdDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
