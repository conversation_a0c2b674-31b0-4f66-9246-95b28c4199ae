<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="结算数据" name="tab1" style="height: 100%;">
                <sphJieSuan ref="sphJieSuan" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="违规赔付" name="tab2" style="height: 100%;" lazy>
                <sphOther :baseFeeType="1" ref="sphOther1" style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane label="商责欠款" name="tab3" style="height: 100%;" lazy>
                <sphOther :baseFeeType="2" ref="sphOther2" style="height: 100%;" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import sphJieSuan from '@/views/bookkeeper/details/sphJieSuan.vue'
import sphOther from '@/views/bookkeeper/details/sphOther.vue'

export default {
    name: "sphData",
    components: { MyContainer, sphJieSuan, sphOther },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            activeName: 'tab1'
        };
    },
    mounted() {

    },
    methods: {


    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
