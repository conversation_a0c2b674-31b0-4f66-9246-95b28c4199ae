<template>
    <my-container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>   
                <el-form-item label="扣款时间:">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>     
                <el-form-item label="宝贝ID:">
                    <el-input v-model="filter.procode" style="width: 150px" placeholder="宝贝ID" @keyup.enter.native="onSearch" clearable/>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopId" placeholder="请选择店铺" clearable style="width: 130px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName" :value="item.id"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="组长:">
                <el-select filterable v-model="filter.groupId" placeholder="请选择组长" style="width: 100px" clearable>
                    <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="运营专员:">
                <el-select filterable v-model="filter.operateSpecialId" placeholder="请选择负责人" clearable style="width: 100px">
                    <el-option label="所有" value=""/>
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item label="运营助理:">
                <el-select filterable v-model="filter.user1Id"  placeholder="请选择负责人" clearable style="width: 100px">
                    <el-option label="所有" value=""/>
                    <el-option v-for="item in directorList" :key="item.key" :label="item.value" :value="item.key"/>
                </el-select>
                </el-form-item>
                <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="true" :loading="listLoading" @cellclick="cellclick">
            <el-table-column :width="500" label="图表" fixed="right">
                <template slot-scope="scope">
                    <div style="height: 230px;width:100%;margin-left: -20px;" :ref="'echarts'+scope.row.proCode" v-loading="echartsLoading"></div>
                </template>
            </el-table-column>         
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template>

        <el-dialog :visible.sync="showDetailVisible" width="72%" :show-close="false" v-dialogDrag>
            <div style="height:600px;">
                <withholdboarddetail ref="withholdboarddetail" style="height:100%;"></withholdboarddetail>
            </div>
        </el-dialog>

    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import * as echarts from 'echarts'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import { platformlist} from '@/utils/tools'
import { getDirectorList, getDirectorGroupList,getProductBrandPageList,getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatLinkProCode} from "@/utils/tools";
import { getWithholdboardList } from "@/api/order/orderdeductmoney"
import withholdboarddetail from "./withholdboarddetail.vue"

const tableCols =[
       {istrue:true,prop:'proCode',label:'产品ID', width:'120',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
       {istrue:true,prop:'platform',label:'平台', width:'80',formatter:(row)=> !row.platformName?" " : row.platformName}, 
       {istrue:true,prop:'title',label:'产品名称', width:'280', },
       {istrue:true,prop:'shopName',label:'店铺', width:'200', },
       {istrue:true,prop:'groupId',label:'组长', width:'70',formatter:(row)=>row.groupName||' '},
       {istrue:true,prop:'operateSpecialUserId',label:'运营专员', width:'80',formatter:(row)=> row.operateSpecialUserName||' '},
       {istrue:true,prop:'userId',label:'运营助理', width:'80',formatter:(row)=> row.userRealName||' '},
       {istrue:true,prop:'realpay',label:'扣款金额', width:'auto',sortable:'custom',formatter:(row)=>parseFloat(row.realpay.toFixed(2))},
    ];
const tableHandles=[];
const startTime = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminWithholdboard',
    components: {MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, withholdboarddetail},

    data() {
        return {
            that:this,
            filter: {
                startTime: null,
                endTime: null,
                timerange:[startTime, endTime],
                procode:null,
                title:null,
                platform:null,
                shopCode:null,
                platform:null,
                newPattern:null,
                customer:null,
            },
            list:[],
            total: 0,
            sels: [], 
            selids: [], 
            shopList:[],
            directorGroupList:[],
            brandlist: [],
            directorList:[],
            summaryarry:{},
            pager:{OrderBy:"realpay",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles,
            platformlist:platformlist,
            listLoading:false,
            echartsLoading:false,
            showDetailVisible:false,
            pickerOptions: {
                disabledDate(time) {
                return time.getTime() > Date.now();
                },
            },
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
        await this.getDirectorlist()
    },

    methods: {
        async onchangeplatform(val){
            this.categorylist =[]
            const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:300});
            this.shopList=res1.data.list
        },
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})     
            const res3 = await getProductBrandPageList()
            
            this.directorList = res1.data
            this.directorGroupList =[{key:'0',value:'未知'}].concat(res2.data ||[]);
            this.bandList = res3.data?.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager,...page,... this.filter}
            if(params===false){
                return;
            }
            this.listLoading = true
            var res = await getWithholdboardList(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
            this.getEcharts()
        },

        getEcharts() {
            setTimeout(_ => {
                this.list.forEach(f =>{
                    let myChart = echarts.init(this.$refs['echarts' + f.proCode]);

                    var series = []
                    var data = []
                    this.echartsLoading = true
                    f.pieList.pieSeries.forEach(s=>{
                        data.push({value: s.value, name: s.name})
                    })
                    this.echartsLoading = false


                    var xx=[{errortype:'非异常订单',color:'#dd6b66'},{errortype:'缺货',color:'#759aa0'},{errortype:'疫情停发',color:'#e69d87'},
                    {errortype:'未设定快递',color:'#8dc1a9'},{errortype:'南昌订单',color:'#ea7e53'}]
                    var colors=[];
                    var allcolor= ['#37A2DA',
                                    '#32C5E9',
                                    '#67E0E3',
                                    '#9FE6B8',
                                    '#FFDB5C',
                                    '#ff9f7f',
                                    '#fb7293',
                                    '#E062AE',
                                ]

                    for(var i=0;i<f.pieList.legend.length;i++)
                    {
                        var isfound=0;
                        for(var j=0;j<xx.length;j++){
                            if(f.pieList.legend[i]==xx[j].errortype)
                            {
                                colors.push(xx[j].color);
                                isfound=1;
                                break;
                            }

                        }
                          if(isfound==0)
                            colors.push(allcolor[parseInt(Math.random()*6)]);
                    }
                    
                    series.push({
                        name: f.pieList.title,
                        type: 'pie',
                        //roseType: 'radius',
                        radius: [10, 60],
                        center: ['50%', '38%'],
                        data: data,
                        animationEasing: 'cubicInOut',
                        //animationDuration: 2600
                    
                    })                   
                    myChart.setOption({
                            tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b} : {c} ({d}%)'
                        },
                        legend: {
                            left: 'center',
                            bottom: '1',
                            data: f.pieList.legend
                        },
                        series: series,
                        color:colors                       
                    })
                    var that = this
                    myChart.on('click', function (param) {

                        that.showDetailVisible = true
                        var para = {proCode: param.seriesName, errorType: param.name, timerange: that.filter.timerange}
                        that.$nextTick( async () =>{
                            await that.$refs.withholdboarddetail.onSearch1(para)
                        })   
                        
                    });
                })
            },1000)
        },
        async changelist(e){
            this.list = e
        },
        async clicknum(num,ii){
            var res = await getProductAdveClick({proCode:num.yearMonthDay})
            this.amontDialog.visible =true;
            this.amontDialog.rows=res.data;
        },
        sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            this.onSearch();
        },
        async cellclick(row, column, cell, event){
            
        },
        selsChange: function(sels) {
            this.sels = sels
        },
        selectchange:function(rows,row) {
            this.selids=[];
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>
dev{
    color:rgb(108, 97, 82)
}
</style>