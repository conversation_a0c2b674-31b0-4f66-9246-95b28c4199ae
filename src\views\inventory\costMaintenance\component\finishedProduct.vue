<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.date" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                    class="publicCss" :clearable="false" />
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback" title="商品编码"
                    style="width: 200px;margin:0 10px 0 0;">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.operateUserName" placeholder="操作人" maxlength="50" clearable
                    class="publicCss" />
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="待审核" value="待审核" />
                    <el-option label="审核中" value="审核中" />
                    <el-option label="已审核" value="已审核" />
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                    <el-button type="primary" @click="batchApproval(true)">一键审批</el-button>
                    <el-button type="primary" @click="pullProps">拉取</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @select="select"
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false" border
            showsummary :summaryarry="summaryarry" :isSelectColumn="false" style="width: 100%;  margin: 0"
            isDisableCheckBox @checCheckboxkMethod="checCheckboxkMethod" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="batchApproval(false, row)"
                                :disabled="row.status == '审核中' || row.status == '已审核'">审批</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import inputYunhan from "@/components/Comm/inputYunhan";
import { calPurchaseGoodsCostDiffs, ExportPurchaseGoodsCostDiffsList, ApprovePurchaseGoodsCostDiffsList, GetPurchaseGoodsCostDiffsList } from '@/api/cwManager/costMaintenanceManager'
const tableCols = [
    { type: 'checkbox', label: '', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'yearMonthDay', label: '日期', formatter: (row) => row.yearMonthDay ? dayjs(row.yearMonthDay).format('YYYY-MM-DD') : row.yearMonthDay },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'prevPrice', label: '历史成本价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'cost', label: '成本价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'marginCost', label: '差异', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operatedUserName', label: '操作人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'operatedTime', label: '操作时间', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, inputYunhan
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                //昨天
                date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            selectList: [],
            summaryarry: {}
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        checCheckboxkMethod(row, callback) {
            let isAllowCheck = row.status == '待审核'
            callback(isAllowCheck);
        },
        proCodeCallback(val) {
            this.ListInfo.goodsCodes = val
        },
        select(val) {
            this.selectList = val
        },
        batchApproval(isBatch, row) {
            let obj = {}
            if (isBatch) {
                if (this.selectList.length == 0) {
                    this.$message.error('请选择数据')
                    return
                }
                const ids = this.selectList.map(item => item.id)
                obj = {
                    ids,
                    status: '已审核'
                }
            } else {
                obj = {
                    ids: [row.id],
                    status: '已审核'
                }
            }
            this.$confirm('此操作审批这些数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await ApprovePurchaseGoodsCostDiffsList(obj)
                if (success) {
                    this.$message.success('审批成功')
                    this.getList()
                    this.$set(this, 'selectList', [])
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });

        },
        async pullProps() {
            const { success } = await calPurchaseGoodsCostDiffs()
            if (!success) return
            this.$message.success('拉取成功,几分钟后刷新列表查看数据')
            this.getList()
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await ExportPurchaseGoodsCostDiffsList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '成本维护_成品' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await GetPurchaseGoodsCostDiffsList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.summaryarry = data.summary
                    this.loading = false
                } else {
                    //获取列表失败
                    this.loading = false
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0px;
    }
}
</style>
