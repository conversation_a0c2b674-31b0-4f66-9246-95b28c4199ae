<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="120px" label-position="right" >       
               
                <el-row>
                    <el-col :span="24" style="height:400px;overflow:auto">                      
                        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' :tableData='sellist' :tableCols='tableCols' :isSelection="false" :isSelectColumn='false' :loading="sellistLoading">
                        </ces-table>
                    </el-col>
                </el-row>

               <el-row>
                    <el-col :span="4">
                        <el-form-item label="是否认可：" prop="firstAuditState" :rules="[
                        { required: true, message: '请选择是否认可', trigger: ['blur', 'change'] }    
                        ]">
                            <el-radio-group v-model="form.firstAuditState">
                                <el-radio :label="-1">不认可</el-radio>
                                <el-radio :label="1">认可</el-radio>
                            </el-radio-group>     
                           
                        </el-form-item>
                        <br/>
                            <el-button type="primary" style="float:right" @click="trans2Other">转派给同部门其他人员</el-button>      
                    </el-col>
                    <el-col :span="20">
                        <el-form-item label="初审资料："  prop="firstAuditRemark" >
                            <yh-quill-editor :value.sync="form.firstAuditRemark" ></yh-quill-editor>    
                        </el-form-item>                        
                    </el-col>
               </el-row>           
              
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button  type="primary" @click="onSave(true)">确认保存</el-button> 
                </el-col>
            </el-row>
        </template>      

    </my-container>
</template>
<script>  
    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode ,DeductOrderZrDeptActions,DeductOrderZrReasons} from "@/utils/tools";
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import { GetDeductZrAppeal4CRUD,BatchFirstAuditDeductZrAppeal } from "@/api/order/orderdeductmoney";
    import {getAllWarehouse,getAllProBrand} from '@/api/inventory/warehouse'

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'


    import {
        getDirectorList,
        getDirectorGroupList,
        getProductBrandPageList,
        getList as getshopList,
    } from "@/api/operatemanage/base/shop";


    //机器人查询状态 1成功、0下架、-1失败
    const fmtJqrNoticeState=(v)=>{
        switch(v){
            case 0:return '下架';
            case 1:return '已查询';
            case -1:return '失败';
        }
        return ' ';
    };

    const fmtApplyState=function(val){
        if(val==-1) return "已拒绝";
        else if(val==0) return "待申请";
        else if(val==1) return "申请中";
        else if(val==2) return "已审核";
        return val;
    }

    
    const tableCols = [
        { istrue: true, prop: 'orderPlatform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => row.orderPlatformText },
        { istrue: true, prop: 'orderNo', label: '订单编号', width: '180', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo },
        { istrue: true, prop: 'deductOccurTime', label: '扣款日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.deductOccurTime, "YYYY-MM-DD") },
      
        { istrue: true, prop: 'groupName', label: '小组', width: '60' , sortable: 'custom'},
        { istrue: true, prop: 'operateSpecialUserName', label: '运营',  width: '60', sortable: 'custom'},

        { istrue: true, prop: 'orgZrType1', label: '原责任类型', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'orgZrType2', label: '原责任原因', width: '90', sortable: 'custom' },

        { istrue: true, prop: 'orgZrDeptAction', label: '原责任部门', width: '98', sortable: 'custom' },
        { istrue: true, prop: 'orgZrReason', label: '原责任原因2', width: '95', sortable: 'custom' },
        { istrue: true, prop: 'orgMemberName', label: '原责任人', width: '76', sortable: 'custom' },
        { istrue: true, prop: 'orgZrSetTime', label: '原责任计算时间', width: '128', sortable: 'custom', formatter: (row) => formatTime(row.orgZrSetTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'orgZrConditionFullName', label: '原责任规则', width: '200', sortable: 'custom' },
        
        { istrue: true, prop: 'applyUserName', label: '申请人', width: '76', sortable: 'custom' },
        { istrue: true, prop: 'applyTime', label: '申请时间', width: '128', sortable: 'custom', formatter: (row) => formatTime(row.applyTime, "YYYY-MM-DD HH:mm") },

        { istrue: true, prop: 'newZrType1', label: '新责任类型', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'newZrType2', label: '新责任原因', width: '90', sortable: 'custom' },

        { istrue: true, prop: 'newZrDeptAction', label: '新责任部门', width: '98', sortable: 'custom' },
        { istrue: true, prop: 'newZrReason', label: '新责任原因2', width: '95', sortable: 'custom' },
        { istrue: true, prop: 'newMemberName', label: '新责任人', width: '76', sortable: 'custom' },
               
        
        { istrue: true, prop: 'newZrConditionFullName', label: '申诉理由', minwidth: '160', sortable: 'custom' },
        { istrue: true, prop: 'applyContent', label: '申请内容', width: '128', sortable: 'custom' },
        { istrue: true, prop: 'sendTime', label: '发货日期', width: '150', sortable: 'custom', formatter: (row) => !row.sendTime ? "" : row.sendTime },
        { istrue: true, prop: 'planDeliveryDate', label: '预计发货时间', width: '124', sortable: 'custom', formatter: (row) => !row.planDeliveryDate ? " " : formatTime(row.planDeliveryDate, "YYYY-MM-DD HH:mm") },
      
    ]

    export default {
        name: "OrderDeductZrApplyBatchFirstAuditForm",
        components: { MyContainer, MyConfirmButton,  YhQuillEditor ,cesTable},
        data() {
            return {              
                that: this,
                mode:3,
                sellist: [],
                sellistLoading: false,
                tableCols: tableCols,
                illegalTypeList:[],
                zrDeptActions:DeductOrderZrDeptActions,
                zrReasons:DeductOrderZrReasons,

                brandlist: [], 
                directorList: [],

                form: {
                    firstAuditRemark:"",                 
                    firstAuditState:null,
                },
            
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
             
                isTx:false,      
            };
        },
        async mounted() {          
        },
        computed: {    
        },
        methods: {  
          
            fmtApplyState:fmtApplyState, 
            fmtJqrNoticeState:fmtJqrNoticeState,

            trans2Other(){
                let self=this;        
                let AppealIds=[];
                this.sellist.forEach((item)=>{
                    AppealIds.push(item.id);
                })    
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyFirstAuditTrans2OtherForm.vue`,
                    title: '初审转派',
                    autoTitle:false,
                    args: {appealId:self.sellist[0].id, 
                        newZrDeptAction:self.sellist[0].newZrDeptAction, 
                        newZrDept:self.sellist[0].newZrDept, 
                        newZrAction:self.sellist[0].newZrAction,
                        appealIds:AppealIds
                    },
                    height: 300,
                    width: '80%',
                    callOk: ()=>{
                        self.$emit('afterSave');
                        self.$emit('close');
                    }
                })
            },
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({ rows }) {
                let self=this;         
              
                self.formEditMode = true;
                self.mode = 2;    
                 
                self.sellist = rows;
              
            },
            async save() {
                this.pageLoading = true;
                
                if(this.form.firstAuditState==null || this.form.firstAuditState==0){
                    this.$message.error('请选择初审是否认可或同意！');
                    this.pageLoading = false;
                    return false;
                }                
                

                let saveData = {                 
                    auditState:this.form.firstAuditState ==1 ?1:0,
                    auditRemark:this.form.firstAuditRemark,
                    ids:[]
                };  

                for(let i=0;i<this.sellist.length;i++){
                    saveData.ids.push(this.sellist[i].id);
                }

                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 
               

                let rlt = await BatchFirstAuditDeductZrAppeal(saveData);
                if (rlt && rlt.success) {
                    this.$message.success(rlt.data.rltMsg);           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
<style lang="scss" scoped>
   
    .tempdiv ::v-deep img {
        width: auto;
        max-width: 1000px;
    }
</style>