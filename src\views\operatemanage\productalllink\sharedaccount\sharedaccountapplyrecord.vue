<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="60px">
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="请选择平台" @change="onchangeplatform" clearable style="width: 130px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺:" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="请选择" class="el-select-content" style="width:260px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select v-model="filter.groupId" placeholder="运营组" :clearable="true" :collapse-tags="true" style="width: 120px" filterable>
                        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账号:">
                    <el-input v-model="filter.accountNum" placeholder="账号" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelectColumn="!isHistory" :loading="listLoading">
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container';
    import cesTable from "@/components/Table/table.vue";
    import { platformlist } from '@/utils/tools'
    import MyConfirmButton from '@/components/my-confirm-button';
    import { getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop';
    import {
        getSharedAccountApplyRecordPageList
    } from '@/api/operatemanage/productalllink/sharedaccount';
    const tableCols = [
        { istrue: true, prop: 'applyTime', label: '申请时间', width: '150', sortable: 'custom', formatter: (row) => row.applyTime == null ? null : formatTime(row.applyTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'downTime', label: '下线时间', width: '150', sortable: 'custom', formatter: (row) => row.downTime == null ? null : formatTime(row.downTime, 'YYYY-MM-DD HH:mm:ss') },
        { istrue: true, prop: 'useDuration', label: '使用时长', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'useUserName', label: '使用人', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'shopCode', label: '店铺', width: '260', sortable: 'custom', formatter: (row) => row.shopName || ' ' },
        { istrue: true, prop: 'accountNum', label: '账号', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'phoneNum', label: '绑定手机号', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => row.platformName || ' ' },
        { istrue: true, prop: 'groupId', label: '登记运营组', width: '120', sortable: 'custom', formatter: (row) => row.groupName || ' ' },
    ];
    export default {
        name: 'Roles',
        components: { cesTable, MyContainer },
        props: {

        },
        data() {
            return {
                that: this,
                sels: [],
                //界面标题
                filter: {
                    ReceiptNo: null
                },
                platformlist: platformlist,//平台下拉
                shopList: [],//商店下拉
                groupList: [],//运营组下拉
                list: [],
                summaryarry: {},
                pager: { OrderBy: "createdTime", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                listLoading: false,
                pageLoading: false,
            }
        },
        async mounted() {
            await this.getGroupList();
            await this.onSearch();
        },
        methods: {
            //平台联动商店
            async onchangeplatform(val) {
                const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res1.data.list
            },
            //获取运营组下拉
            async getGroupList() {
                var res2 = await getDirectorGroupList();
                this.groupList = res2.data?.map(item => { return { value: item.key, label: item.value }; });
            },
            //获取查询条件
            getCondition() {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist() {
                this.filter.BeginDate = null;
                this.filter.EndDate = null;
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                var res = await getSharedAccountApplyRecordPageList(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                //this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
        }
    }
</script>
