<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="发生时间">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="平台:">
                    <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable style="width: 80px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-select filterable v-model="filter.shopCode" placeholder="店铺" clearable style="width: 130px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="账单类型:">
                    <el-select filterable v-model="filter.nType" placeholder="请选择账单类型" clearable style="width: 120px">
                        <el-option label="全部" value="" />
                        <el-option label="退运费" value="退运费" />
                        <el-option label="欺诈发货" value="欺诈发货" />
                        <el-option label="运费险扣减货款" value="运费险扣减货款" />
                        <el-option label="优惠券结算" value="优惠券结算" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
            @select="selectchange" :tableHandles='tableHandles' @cellclick="cellclick" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="导入数据" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="5">
                    <el-select filterable v-model="importDialog.filter.platform" placeholder="请选择平台" clearable
                        style="width: 120px;float: left; margin-top: 3px;">
                        <el-option label="拼多多" value="2" />
                        <el-option label="淘工厂" value="8" />
                        <el-option label="抖音" value="6" />
                    </el-select>
                </el-col>
                <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="15">
                    <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove"
                        :file-list="fileList">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }} </el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { importBillFee } from '@/api/bookkeeper/import'
import { getBillFeePageList } from '@/api/bookkeeper/financialDetail'

const tableCols = [
    { istrue: true, prop: 'recordNumber', label: '流程编码', tipmesg: '', width: '120', sortable: 'custom', },
    { istrue: true, prop: 'shopCode', label: '店铺名', tipmesg: '', width: '100', formatter: (row) => !row.shopName ? " " : row.shopName },
    { istrue: true, prop: 'customRemark', label: '自定义备注', tipmesg: '', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'billType', label: '项目账单', width: '125', },
    { istrue: true, prop: 'nType', label: '账单类型', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'clientNum', label: '商户订单号', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'financialType', label: '账务类型', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'occurrenceTime', label: '发生时间', tipmesg: '', width: '100', sortable: 'custom', },
    // { istrue: true, prop: 'billFee', label: '收入金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'amountPaid', label: '支出金额', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'remark', label: '备注', tipmesg: '', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'modifiedTime', label: '修改时间', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'modifiedUserName', label: '修改人', tipmesg: '', width: 'auto', sortable: 'custom', },
]

const tableHandles = [
    { label: "导入", handle: (that) => that.startImport() },
    //{ label: "导出", handle: (that) => that.onExportDetail() },
    //{ label: "模板-代拍导入模板", handle: (that) => that.downloadOrherTemplate() },
];

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const curMonth = formatTime1(dayjs().startOf("month").subtract(1, "month"), "yyyyMM");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
                procode: null,
                title: null,
                platform: null,
                shopCode: null,
                groupId: null,
                operateSpecialId: null,
                operateName: null,
                user3Id: null,
                shopId: null,
                newPattern: null,
                customer: null,
                nType: null,
                refundStatus: null
            },
            platformlist:platformlist,
            importDialog: {
                filter: {
                    settMonth: curMonth,
                    platform: null
                }
            },
            list: [],
            shopList: [],
            summaryarry: {},
            pager: { OrderBy: "yearMonthDay", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            onHandNumber: null,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            sels: [],
            editparmLoading: false,
            uploadLoading: false,
            editparmLoading1: false,
            editparmLoading2: false,
            editparmVisible: false,
            editparmVisible1: false,
            editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            showDetailVisible: false,
            fileList: []
        };
    },

    async mounted() {
        await this.onSearch()
        //await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100 });
            this.filter.shopCode = null
            this.shopList = res1.data.list
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getBillFeePageList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        //导出
        async onExportDetail() {
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...page, ... this.filter }

            let loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            let res = await exportReplaceDayReportNewList(params);
            loadingInstance.close();
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '代拍日报详情_' + new Date().toLocaleString() + '.xlsx')
            aLink.click();
        },
        async showAmont(row) {
            if (row.orderCount === 1) return
            this.showDetailVisible = true
            let param = { orderNoInner: row.orderNoInner }
            let that = this
            that.$nextTick(async () => {
                await that.$refs.replacedayreportdetail.onSearch1(param)
            })

        },
        renderAmont(row) {
            if (row.orderCount > 1) return "color:blue;cursor:pointer;";
            else return "";
        },
        async nSearch() {
            await this.getlist()
        },
        //字体颜色
        renderRefundStatus(row) {
            if (row.refundStatus == '成功退款' || row.refundStatus == '等待退款') {
                return "color:red;cursor:pointer;";
            } else return "";
        },
        //开始导入
        startImport() {
            this.importDialog.filter.platform = null
            this.fileList = []
            this.uploadLoading=false
            this.dialogVisible = true;
        },
        //取消导入
        cancelImport() {
            this.dialogVisible = false;
        },
        uploadSuccess(response, file, fileList) {
            if (response.code == 200) {
            } else {
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        async submitUpload() {
            if (this.importDialog.filter.platform == null) {
                this.$message({ message: "请选择平台", type: "warning" });
                return false;
            }
            if (!this.fileList || this.fileList.length == 0) {
                this.$message({ message: "请选取文件", type: "warning" });
                return false;
            }
            this.fileHasSubmit = true;
            this.uploadLoading = true;
            this.$refs.upload.submit();
        },
        clearFiles(){
            this.$refs['upload'].clearFiles();
        },
        async uploadFile(item) {
            if (!this.fileHasSubmit) {
                return false;
            }
            this.fileHasSubmit = false;
            this.uploadLoading = true;
            const form = new FormData();
            form.append("platForm", this.importDialog.filter.platform);
            form.append("token", this.token);
            form.append("upfile", item.file);
            let res = await importBillFee(form);
                if (res.code == 1) {
                    this.$message({ message: "上传成功,正在导入中...", type: "success" });
                    this.$refs.upload.clearFiles();
                    this.dialogVisible = false;
                }
            this.fileList = []
            this.uploadLoading = false;
        },
        async uploadChange(file, fileList) {
            let files=[];
            files.push(file)
            this.fileList = files;
        },
        async uploadRemove(file, fileList) {
            this.fileList = []
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
        async downloadOrherTemplate() {
            window.open("../static/excel/dayreport/新版代拍导入模板.xlsx", "_self");
        },
    }
};
</script>

<style lang="scss" scoped>

</style>
