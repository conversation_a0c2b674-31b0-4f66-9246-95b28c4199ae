<template>
    <my-container v-loading="pageLoading">
        <ces-table style="height: 100%;" :ref="tablekey" :key="tablekey" :that='that' :isIndex='true' :isSelectColumn='false' :hasexpand='false' @sortchange='sortchange' :tableData='list' :tableCols='tableCols' :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import cesTable from "@/components/Table/table.vue";
    import { getExpressStopSendDetailDataAsync } from "@/api/order/covidStopSendPage";
    import { formatTime } from "@/utils";
    export default {
        name: 'ExpressStopSendPage',
        components: { cesTable, MyContainer },
        props: {
            filter: {
                type: Object,
                default: null
            }
        },
        data() {
            return {
                that: this,
                tablekey: "expressStopSendPageDetail",
                tableCols: [],
                list: [],
                summaryarry: {},
                pager: {},
                importExpressName: "",
                importWarehouse: "",
                warehouselist: [{ label: "南昌", value: "南昌" }, { label: "义乌", value: "义乌" }],
                dialogVisible: false,
                dialogEportVisible: false,
                exportWarehouse: "",
                total: 0,
                listLoading: false,
                pageLoading: false,
                importLoading: false,
                fileList: [],
            }
        },
        async mounted() {
            this.initTableCols();
            //await this.onSearch();
        },
        methods: {
            initTableCols() {
                var showTableCols = [];
                showTableCols.push({ istrue: true, prop: 'expressName', label: '快递公司', width: '100', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'warehouse', label: '发货仓', width: '90', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'province', label: '省', width: '70', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'city', label: '市', width: '100', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'area', label: '区', width: '100', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'street', label: '街道', width: '140', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'remake', label: '停发原因', width: '140', sortable: 'custom', });
                showTableCols.push({ istrue: true, prop: 'startTime', label: '停发开始时间', width: '120', sortable: 'custom', formatter: (row) => row.startTime == null ? null : formatTime(row.startTime, 'YYYY-MM-DD') });
                showTableCols.push({ istrue: true, prop: 'endTime', label: '停发结束时间', width: '120', sortable: 'custom', formatter: (row) => row.endTime == null ? null : formatTime(row.endTime, 'YYYY-MM-DD') });
                showTableCols.push({ istrue: true, prop: 'stopTime', label: '停发时间', width: '110', sortable: 'custom', formatter: (row) => row.stopTime == null ? null : formatTime(row.stopTime, 'YYYY-MM-DD') });
                showTableCols.push({ istrue: true, prop: 'importTime', label: '导入时间', width: '110', sortable: 'custom', formatter: (row) => row.importTime == null ? null : formatTime(row.importTime, 'YYYY-MM-DD') });
                showTableCols.push({ istrue: true, prop: 'batchNumber', label: '批次号', width: '170', sortable: 'custom', });
                this.tableCols = showTableCols;
            },
            //获取查询条件
            getCondition() {
                if (this.filter.stopSendTimerange && this.filter.stopSendTimerange.length > 1) {
                    this.filter.startTime = this.filter.stopSendTimerange[0];
                    this.filter.endTime = this.filter.stopSendTimerange[1];
                } else {
                    this.filter.startTime = null;
                    this.filter.endTime = null;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //查询第一页
            async onSearch() {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await getExpressStopSendDetailDataAsync(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                this.summaryarry = res.data.summary;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            }
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
</style>
