# tableColumnMerge.js 使用说明

## 功能概述

`tableColumnMerge.js` 是一个 Vue mixin，封装了表格列合并功能，基于 `yh_vxetable.vue` 中的 `tableMergeColumn` 功能。可以在多个表格组件中复用。

## 使用方法

### 1. 在组件中引入 mixin

```javascript
import tableColumnMerge from "@/views/profit/sscManager/tableColumnMerge.js";

export default {
  name: "YourComponent",
  mixins: [tableColumnMerge], // 引入 mixin
  // ... 其他配置
}
```

### 2. 配置列合并参数

```javascript
data() {
  return {
    // 配置列合并
    tableMergeColumn: {
      column: ['dept', 'deptName'], // 需要合并的列
      default: 'deptName', // 合并后显示的字段
      condition: (row) => {
        // 可选：条件函数，只有满足条件的行才进行合并
        return row.dept === '小计';
      }
    },
    
    // 如果需要行合并，也可以配置 somerow
    somerow: 'regionName,type,calculateMonth'
  }
}
```

### 3. 在模板中使用

```vue
<template>
  <vxe-table
    :data="tableData"
    :span-method="tableColumnMergeMethod"
  >
    <!-- 普通列 -->
    <vxe-column field="calculateMonth" title="月份"></vxe-column>
    
    <!-- 需要合并的列 -->
    <vxe-column field="dept" title="部门">
      <template #default="{ row }">
        <!-- 使用条件判断是否显示合并内容 -->
        <span v-if="shouldShowTableColumnMerged(row, { field: 'dept' })">
          {{ getTableColumnMergeDisplayValue(row) }}
        </span>
        <span v-else>{{ row.dept }}</span>
      </template>
    </vxe-column>
    
    <vxe-column field="deptName" title="部门名称">
      <template #default="{ row }">
        <!-- 第二个合并列通常会被隐藏，但也可以添加条件判断 -->
        <span v-if="!shouldShowTableColumnMerged(row, { field: 'dept' })">
          {{ row.deptName }}
        </span>
      </template>
    </vxe-column>
  </vxe-table>
</template>
```

## 在 AllRegionalPersonnel/index.vue 中的具体应用

### 修改步骤

1. **引入 mixin**：
```javascript
import tableColumnMerge from "@/views/profit/sscManager/tableColumnMerge.js";

export default {
  mixins: [tableFooterMerge, tableColumnMerge], // 同时使用两个 mixin
  // ...
}
```

2. **配置列合并参数**：
```javascript
data() {
  return {
    // 配置 dept 和 deptName 列合并
    tableMergeColumn: {
      column: ['dept', 'deptName'],
      default: 'deptName' // 合并后显示 deptName 的值
    },
    // ... 其他数据
  }
}
```

3. **修改表格配置**：
```vue
<vxe-table
  :span-method="tableColumnMergeMethod"
  <!-- 其他配置保持不变 -->
>
```

4. **修改列模板**：
```vue
<!-- 部门列 -->
<vxe-column field="dept" width="80" title="部门" footer-align="center">
  <template #default="{ row }">
    <span v-if="shouldShowTableColumnMerged(row, { field: 'dept' })">
      {{ getTableColumnMergeDisplayValue(row) }}
    </span>
    <span v-else>{{ row.dept }}</span>
  </template>
  <template #footer="{ items, _columnIndex, row }">
    <span v-if="mergeColumn?.column?.includes('dept')" class="display_centered">
      {{ getMergeDisplayValue(row) }}
    </span>
    <span v-else>{{ items[_columnIndex] }}</span>
  </template>
</vxe-column>

<!-- 部门名称列 -->
<vxe-column field="deptName" width="100" title="部门类型" footer-align="center">
  <template #default="{ row }">
    <!-- 第二个合并列，当第一列显示合并内容时，这列通常被隐藏 -->
    <span v-if="!shouldShowTableColumnMerged(row, { field: 'dept' })">
      {{ row.deptName }}
    </span>
  </template>
  <template #footer="{ items, _columnIndex, row }">
    <span v-if="mergeColumn?.column?.includes('deptName')" class="display_centered">
      {{ getMergeDisplayValue(row) }}
    </span>
    <span v-else>{{ items[_columnIndex] }}</span>
  </template>
</vxe-column>
```

## API 说明

### 方法

#### `tableColumnMergeMethod({ row, _rowIndex, column, visibleData })`
- **用途**：表格的 span-method，处理列合并逻辑
- **参数**：vxe-table 的 span-method 参数
- **返回值**：`{ rowspan, colspan }` 合并配置

#### `getTableColumnMergeDisplayValue(row)`
- **用途**：获取合并列的显示值
- **参数**：`row` - 行数据
- **返回值**：显示的字符串

#### `shouldShowTableColumnMerged(row, col)`
- **用途**：判断是否应该显示合并列内容
- **参数**：
  - `row` - 行数据
  - `col` - 列配置对象，需要包含 `field` 或 `prop` 属性
- **返回值**：布尔值

#### `createTableColumnMergeConfig(columns, defaultField, condition)`
- **用途**：创建列合并配置的工厂方法
- **参数**：
  - `columns` - 需要合并的列数组
  - `defaultField` - 合并后展示的字段（可选）
  - `condition` - 条件函数（可选）
- **返回值**：列合并配置对象

### 配置对象

#### `tableMergeColumn`
```javascript
{
  column: ['field1', 'field2'], // 需要合并的列字段数组
  default: 'field1', // 合并后显示的字段名
  condition: (row) => boolean // 可选：条件函数
}
```

## 注意事项

1. **字段名兼容性**：支持 `column.field` 和 `column.property` 两种字段名格式
2. **与行合并兼容**：可以同时使用列合并和行合并功能
3. **条件合并**：通过 `condition` 函数可以实现条件合并
4. **模板灵活性**：在模板中可以灵活控制合并列的显示逻辑
5. **与现有功能兼容**：不影响原有的 `tableFooterMerge` 功能

## 完整示例

参考 `AllRegionalPersonnel/index.vue` 的修改，将 `dept` 和 `deptName` 两列合并，合并后显示 `deptName` 的值。
