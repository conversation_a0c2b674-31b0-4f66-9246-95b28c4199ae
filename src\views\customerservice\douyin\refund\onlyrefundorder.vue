<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <div class="top">
                <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始售后申请时间" end-placeholder="结束售后申请时间"
                    style="width: 250px" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-select v-model="filter.shopCode" placeholder="店铺" style="width:160px;" filterable clearable>
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopCode">
                    </el-option>
                </el-select>
                <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" style="width:160px;" clearable
                    :maxlength="50" />
                <el-input v-model.trim="filter.goodsName" placeholder="商品名称" style="width:160px;" clearable
                    :maxlength="50" />
                <el-select v-model="filter.saleAfterReason" placeholder="售后原因" style="width:160px;" filterable
                    clearable>
                    <el-option v-for="item in saleAfterReasonBaseList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.expressStatus" placeholder="发货物流状态" style="width:160px;" filterable
                    clearable>
                    <el-option v-for="item in expressStatusBaseList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
                <el-select v-model="filter.batchStr" placeholder="批次号" style="width:120px;" filterable clearable>
                    <el-option v-for="item in pettyPaymentList" :key="item.batchStr" :label="item.batchStr"
                        :value="item.batchStr">
                    </el-option>
                </el-select>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExportOnlyRefund">导出</el-button>
            </div>
        </template>
        <!--列表-->
        <div style="height: 95%;">
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
                :summaryarry="summaryarry" showsummary :tableData='pagelist' @select='selectchange' :isSelection='false'
                :tableCols='tableCols' :loading="listLoading"
                :style="{ height: queryInfo ? '400px' : '100%', }">
            </ces-table>
        </div>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import dayjs from 'dayjs'
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    getOnlyRefundOrderPageList, exportOnlyRefundOrderList
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'saleAfterNo', label: '售后单号', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'orderNo', label: '订单号', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '250', sortable: 'custom' },
    { istrue: true, prop: 'shopName', label: '店铺', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'payableAmount', label: '应付金额', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterApplyTime', label: '售后申请时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterEndTime', label: '售后完结时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'goodsBackAmount', label: '商品退款金额', width: '130', sortable: 'custom' },
    { istrue: true, prop: 'saleAfterReason', label: '售后原因', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'expressStatus', label: '发货物流状态', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'batchStr', label: '批次号', width: '120', sortable: 'custom' },
];
export default {
    name: "onlyrefundorder",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    props: ["saleAfterReasonBaseList", "expressStatusBaseList", "batchStrBaseList", "queryInfo","partInfo"],
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                shopCode: null,
                goodsCode: null,
                goodsName: null,
                saleAfterReason: null,
                expressStatus: null,
                batchStr: null,
            },
            shopList: [],
            pettyPaymentList: [],
            // saleAfterReasonList: saleAfterReasonBaseList,//售后原因
            // expressStatusList: expressStatusBaseList,//发货物流状态
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "saleAfterApplyTime", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
        };
    },
    watch: {
        partInfo: {
            handler: function (newVal, oldVal) {
                if(newVal){
                    setTimeout(() => {
                       this.showlist_onlyrefundorder(newVal)
                    }, 200);
                }
            },
            immediate: true,
        },
    },
    async mounted() {
        if (this.filter.timerange.length == 0) {
            this.filter.startDate = dayjs().format('YYYY-MM-DD')
            this.filter.endDate = dayjs().format('YYYY-MM-DD')
            this.filter.timerange = [this.filter.startDate, this.filter.endDate]
            this.batchScreening()
        }
        if (this.queryInfo) {
            this.filter = { ...this.filter, ...this.queryInfo }
        }
        console.log(this.filter, 'this.filter');
        await this.getAllShopList();
        this.onSearch();
        // window.showlist_onlyrefundorder = this.showlist_onlyrefundorder;
    },
    methods: {
      async changeTime(e) {
          this.filter.startDate = e ? e[0] : null
          this.filter.endDate = e ? e[1] : null
          this.filter.timerange = e
          this.batchScreening()
        },
        batchScreening() {
          let dates = [ new Date(this.filter.timerange[0]), new Date(this.filter.timerange[1] + ' 23:59:59') ];
          this.pettyPaymentList = this.batchStrBaseList.filter(item => {
            let createdTime = new Date(item.createdTime);
            return createdTime >= dates[0] && createdTime <= dates[1];
          });
        },
        showlist_onlyrefundorder(param) {
            if (!param.startDate || !param.endDate)
                this.filter.timerange = [];
            else
                this.filter.timerange = [param.startDate, param.endDate];
            this.filter.shopCode = param.shopCode;
            this.filter.goodsCode = param.goodsCode;
            this.filter.saleAfterReason = param.saleAfterReason;
            this.filter.expressStatus = param.expressStatus;
            if (param.onlysaleAfterReasonSort == true) {
                this.pager = { OrderBy: "saleAfterReason", IsAsc: false }
            }
            this.onSearch()
        },
        async getAllShopList() {
            let shops = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            this.shopList.push({ shopCode: '未知', shopName: '未知' });
            shops.data?.forEach(f => {
                if (f.shopCode && f.platform == 6)
                    this.shopList.push(f);
            });
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            let params = {
                ...para,
                ...this.pager,
                ...pager,
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            let params = this.getParam();
            console.log(params, 'params');
            this.listLoading = true;
            const res = await getOnlyRefundOrderPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onExportOnlyRefund() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await exportOnlyRefundOrderList(params);
            this.listLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音仅退款订单数据_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

// .top {
//     display: flex;
//     margin-bottom: 10px;
// }
</style>
