<template>
    <div style="height:100%;padding:10px;overflow: auto;">
        <template>
            <el-form class="ad-form-query" :model="filter" @submit.native.prevent label-width="100px">
                <el-row>
                    <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                        <el-form-item label="日期:">
                            <el-date-picker style="width: 260px"
                                v-model="filter.timerange"
                                type="datetimerange"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-form-item>
                            <el-button type="primary" @click="onfresh">刷新</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template> 
        <div id="echartprocodeanalysis1" style="width: 100%;height: 450px; box-sizing:border-box; line-height: 360px;"/>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import container from '@/components/my-container/nofooter'
import { proCodeSimilarityAnalysis } from "@/api/order/procodesimilarity"

export default {
    name: 'YunhanAdminProcodesimilarityanalysis',
    components: {container},

    data() {
        return {
            filter: {
                timerange:null,
                startDate:null,
                endDate:null,
                proCode:null
            },
            period:0,
            pageLoading: false,
            listLoading:false
            };
    },

    mounted() {
        
    },

    methods: {
        async onSearch(para){          
            this.filter.proCode = para.proCode
            this.filter.timerange = para.timerange
            await this.getanalysisdata()
        },
        async onfresh() {
            await this.getanalysisdata()
        },
        async getanalysisdata() {
            if (!this.filter.timerange||this.filter.timerange.length<2){
                this.$message({message: "请先选择日期！",type: "warning",});
                return;
            } 
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            var parm={...this.filter};
            parm.period=this.period;
            const res = await proCodeSimilarityAnalysis(parm);
            if (!res?.code)  return;       
            var chartDom = document.getElementById('echartprocodeanalysis1');
            var myChart = echarts.init(chartDom);
            myChart.clear();
            if (!res.data) {
                this.$message({message: "没有数据!",type: "warning",});
                return;
            }
            var option = this.Getoptions(res.data);
            option && myChart.setOption(option);
            },
            Getoptions(element){
            var series=[]
            element.series.forEach(s=>{
            series.push({smooth: true, ...s})
            })
            var yAxis=[]
            element.yAxis.forEach(s=>{
            yAxis.push({type: 'value',offset:s.offset,position:s.position,name: s.name,axisLabel: {formatter: '{value}'+s.unit}})
            })
            var option = {
                title: {text: element.title},
                tooltip: {trigger: 'axis'},
                legend: {
                data: element.legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {feature: {
                    magicType: {show: true, type: ['line', 'bar']},
                    //restore: {show: true},
                }},
                xAxis: {
                    type: 'category',
                    data: element.xAxis
                },
                yAxis: yAxis,
                series:  series
            };
            return option;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>