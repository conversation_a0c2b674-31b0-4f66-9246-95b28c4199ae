<template>
    <container>
        <vxetablebase 
            :id="'handlelog20241206'" 
            :tablekey="'handlelog20241206'" 
            :tableData='tableData' 
            :tableCols='tableCols'
            :loading='loading' 
            :border='true'  
            :that="that"  
            @sortchange="sortchange" 
            height="440px" 
            ref="vxelogtable"  
            :showsummary='false' 
            :toolbarshow="false">
        </vxetablebase>
        <template #footer>
            <my-pagination  ref="pager" :total="total"  @get-page="getList()"/>
        </template>
    </container>
</template>
  
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from '@/components/my-container'
import {getUpdateFinanceTypeLogPageList,getUpdateBillFinanceTypeLogPageList} from '@/api/kj/cost.js';
const tableCols =[
         {istrue:true,prop:'createTime',label:'时间',sortable:'custom'},  
         { istrue: true, prop: 'operationBefore', label: '更新前', sortable: 'custom' },
         { istrue: true, prop: 'operationAfter', label: '更新后', sortable: 'custom' },
         {istrue:true,prop:'createName',label:'操作人', width:'150',sortable:'custom'},  
]

export default {
name: 'viewLog',
components: { vxetablebase,container},
props: {
  type: {
    type: String,//用于判断使用接口 '维护' '仓库'
    default:'仓库'
  },
  currentRow:{
    type: Object,
    required: true
  },
},
data() {
    return {
        that: this,
        queryInfo: {
            orderBy: null,
            isAsc: false,
        },
        tableData: [],
        tableCols: tableCols,
        loading: false,
        total: 0,
        logVisible:false,
    };
},
async mounted() {
        // await this.getList('search')
},
methods: {
    async getList(type) {
        if (type == 'search') {
                this.$refs.pager.setPage(1)
        }
        let page = this.$refs.pager.getPager()
        this.queryInfo.page = page.currentPage
        this.queryInfo.pageSize = page.pageSize
        let params = {
            id:this.currentRow.id,
            ...this.queryInfo,
        }
        this.loading = true
        let res
        console.log(this.type === '维护')
        if(this.type == '维护'){
            res = await getUpdateFinanceTypeLogPageList(params)
        }else{
            res = await getUpdateBillFinanceTypeLogPageList(params)
        }
        this.loading = false
        this.total = res.total
        this.tableData = res.data;
    },



    sortchange({ order, prop }) {
    if (prop) {
        this.queryInfo.orderBy = prop
        this.queryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
    }
    },
    


}
};
</script>

<style lang="scss" scoped>
.top {
display: flex;
margin-bottom: 10px;
}
</style>