<template>
    <div style="height: 100%; width: 100%;">
        <el-tabs v-model="activeName" style="height: 94%;width: 100%; ">
        <el-tab-pane label="入库称重" name="first" style="height: 100%;width: 100%;">
            <chengzhong></chengzhong>
        </el-tab-pane>
        <el-tab-pane label="仓库工作量统计" name="second" style="height: 95%;width: 100%; " v-if="checkPermission('warehouseWorkloadStatistics')">
            <chengzhongTj></chengzhongTj>
        </el-tab-pane>
        </el-tabs>
    </div>
  </template>
  <script>
  import chengzhong from "@/views/storehouse/rukuweight/chengzhong.vue";
  import chengzhongTj from "@/views/storehouse/rukuweight/chengzhongTj.vue";
    export default {
      components: { chengzhong, chengzhongTj },
      data() {
        return {
          activeName: 'first'
        };
      },
      methods: {
      }
    };
  </script>