<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
        <el-form-item label="">
          <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
            value-format="yyyyMM" placeholder="月份"></el-date-picker>
        </el-form-item>


        <el-form-item label="">
          <el-select filterable v-model="filter.platform" placeholder="平台" @change="onchangeplatform" clearable
            style="width: 130px">
            <el-option label="天猫" :value=1 />
            <el-option label="淘宝" :value=9 />
          </el-select>
        </el-form-item>
        <el-form-item label="" label-position="right">
          <el-select filterable clearable v-model="filter.shopCode" placeholder="店铺" class="el-select-content"
            style="width:300px">
            <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="" label-position="right">
          <el-input v-model="filter.serialNumberOrder" style="width:183px;" placeholder="订单编号" />
        </el-form-item>

        <el-form-item label="" label-position="right">
          <el-select filterable v-model="filter.recoganizeType" placeholder="收入支出" clearable style="width: 130px">
            <el-option label="收入" :value=0 />
            <el-option label="支出" :value=1 />
          </el-select>
        </el-form-item>

        <el-form-item label="" label-position="right">
          <el-select filterable v-model="filter.txWxFeeType" placeholder="账单费用" clearable style="width: 130px">
            <el-option label="微信基础软件服务费" value=1 />
            <el-option label="微信官方竞价软件服务费" value=2 />
            <el-option label="微信保证金扣款" value=3 />
            <el-option label="微信百亿补贴软件服务费" value=4 />
            <el-option label="微信每日必买" value=5 />
            <el-option label="微信品牌新享-首单拉新计划" value=6 />
            <el-option label="微信公益宝贝捐赠" value=7 />
            <el-option label="微信品牌新享" value=8 />
          </el-select>
        </el-form-item>


        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport">汇总导出</el-button>
        </el-form-item>
      </el-form>
    </template>

    <vxetablebase :id="'financialDetailTxWx20241031001'" ref="table" :that='that' :isIndex='true'
      :cstmExportFunc="onExport" :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
      :showsummary="true" @select="selectchange" :tableData='ZTCKeyWordList' :tableCols='tableCols' :isSelection="true"
      :loading="listLoading">
    </vxetablebase>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink } from "@/utils/tools";
import { GetFinancialDetail_TxWx, ExportFinancialDetail_TxWx } from '@/api/monthbookkeeper/financialDetail'

const tableCols = [
  { istrue: true, prop: 'accountDate', label: '入账时间', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'serialNumberBusiness', label: '支付流水号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'recoganizeSerialNumberOrder', label: '淘宝订单编号', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'accountType', label: '入账类型', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'amountIncome', label: '收入金额（元）', sortable: 'custom', width: 'auto', },
  { istrue: true, prop: 'amountPaid', label: '支出金额', sortable: 'custom', width: 'auto', },
  { istrue: true, prop: 'recoganizeType', label: '收入支出', sortable: 'custom', width: 'auto', formatter: (row) => (row.recoganizeType == 0 ? "收入" : row.recoganizeType == 1 ? "支出" : "") },
  { istrue: true, prop: 'feeType', label: '账单费用类型', sortable: 'custom', width: 'auto', formatter: (row) => row.feeTypeName },
  { istrue: true, prop: 'descBusinessType', label: '业务描述', sortable: 'custom', width: 'auto' },
  { istrue: true, prop: 'descRemark', label: '备注', sortable: 'custom', width: 'auto' },
];
export default {
  name: "financialDetailTxWx",
  components: { cesTable, vxetablebase, MyContainer, MyConfirmButton, MySearch, MySearchWindow, },
  data() {
    return {
      that: this,
      filter: {
        yearMonth: null,
        platform: null,
        shopCode: null,
      },
      shopList: [],
      userList: [],
      groupList: [],
      ZTCKeyWordList: [],
      tableCols: tableCols,
      total: 0,
      pager: { OrderBy: "id", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      summaryarry: {},
      onExportLoading: false,
    };
  },
  async mounted() {
    //await this.getShopList();
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    async onchangeplatform(val) {
      this.filter.shopCode = null;
      this.shopList = [];
      const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
      this.shopList = res1.data.list
    },
    async getShopList() {
      const res1 = await getAllShopList();
      this.shopList = [];
      res1.data?.forEach(f => {
        if (f.isCalcSettlement && f.shopCode)
          this.shopList.push(f);
      });
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getList();
    },
    getCondition() {
      let pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };

      return params;
    },
    async getList() {
      let params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await GetFinancialDetail_TxWx(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },

    async setExportCols() {
      await this.$refs.table.setExportCols();
    },
    async onExport(opt) {
      if (!this.filter.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      let pars = this.getCondition();
      if (pars === false) {
        return;
      }
      const params = { ...pars, ...opt };
      let res = await ExportFinancialDetail_TxWx(params);
      if (!res?.data) {
        return
      }
      this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>