<template>
    <!-- <my-container> -->
      <div class="panel-group">
        <div style="display: flex;justify-content: end;padding: 6px 10px 0 0;">
          <el-switch
            v-model="desensitized"
            active-color="#13ce66"
            inactive-color="#dcdfe6">
          </el-switch>
        </div>
        <el-row type="flex" justify="flex-start" style="margin:0,">
          <el-col :span="3" :style="stylegoodscodenum">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('goodsCodeNum')">
    <!-- <div class="card-panel-icon-wrapper icon-warning">
      <i class='el-icon-warning-outline' style=""></i>
    </div> -->
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="实时缺货编码数" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689226380972" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2315" width="200" height="200">
          <path d="M768 896H256a128.1 128.1 0 0 1-128-128V640a32 32 0 1 1 64 0v128a64.1 64.1 0 0 0 64 64h512a64.1 64.1 0 0 0 64-64V640a32 32 0 1 1 64 0v128a128.1 128.1 0 0 1-128 128zM864 416a32 32 0 0 1-32-32V256a64.1 64.1 0 0 0-64-64H256a64.1 64.1 0 0 0-64 64v128a32 32 0 1 1-64 0V256a128.1 128.1 0 0 1 128-128h512a128.1 128.1 0 0 1 128 128v128a32 32 0 0 1-32 32z" fill="#333333" p-id="2316"></path>
          <path d="M437.3 704a32 32 0 0 1-32-32v-320a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zM288 704a32 32 0 0 1-32-32v-320a32 32 0 1 1 64 0v320a32 32 0 0 1-32 32zM586.7 704a32 32 0 0 1-32-32v-320a32 32 0 1 1 64 0v320a32 32 0 0 1-32 32zM736 704a32 32 0 0 1-32-32v-320a32 32 0 1 1 64 0v320a32 32 0 0 1-32 32z" fill="#333333" p-id="2317"></path>
          </svg>
            缺货编码</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.goodsCodeNum" :duration="2000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<el-col :span="3"  :style="stylegoodnumcurrentnum">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('waitOrderNumCurrent')">
    <!-- <div class="card-panel-icon-wrapper icon-warning">
      <i class='el-icon-warning' style=""></i>
    </div> -->
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="实时缺货单量" placement="top-start">
          <span style="margin-right: 10px;">
            <svg t="1689226525047" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3337" width="200" height="200">
            <path d="M704 192a106.666667 106.666667 0 0 1 106.666667 106.666667v469.333333a106.666667 106.666667 0 0 1-106.666667 106.666667H320a106.666667 106.666667 0 0 1-106.666667-106.666667V298.666667a106.666667 106.666667 0 0 1 106.666667-106.666667 21.333333 21.333333 0 1 0 0-42.666667 149.333333 149.333333 0 0 0-149.333333 149.333334v469.333333a149.333333 149.333333 0 0 0 149.333333 149.333333h384a149.333333 149.333333 0 0 0 149.333333-149.333333V298.666667a149.333333 149.333333 0 0 0-149.333333-149.333334 21.333333 21.333333 0 0 0 0 42.666667z" fill="#222222" p-id="3338"></path>
            <path d="M320 554.666667a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320z m0-128a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320z m0 256a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320zM405.333333 192h213.333334a21.333333 21.333333 0 0 0 0-42.666667H405.333333a21.333333 21.333333 0 0 0 0 42.666667z" fill="#222222" p-id="3339"></path>
            </svg>
             缺货单量</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.waitOrderNumCurrent" :duration="2000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<el-col :span="3"  :style="styleamountpaid">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('sumAmountPaid')">
    <!-- <div class="card-panel-icon-wrapper icon-warning">
      <i class='el-icon-warning-outline' style=""></i>
    </div> -->
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="罚款金额" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689226710318" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7379" width="200" height="200">
            <path d="M73.142857 0h877.714286v877.714286H73.142857z" fill="#FFFFFF" p-id="7380"></path>
            <path d="M804.571429 45.714286A27.428571 27.428571 0 0 1 832 73.142857v687.305143a45.714286 45.714286 0 0 1-71.204571 37.942857L687.542857 749.147429l-62.262857 41.874285a45.714286 45.714286 0 0 1-48.073143 1.828572l-2.925714-1.828572L512 749.147429l-62.262857 41.874285a45.714286 45.714286 0 0 1-48.073143 1.828572l-2.925714-1.828572L336.457143 749.165714l-73.234286 49.243429a45.714286 45.714286 0 0 1-22.125714 7.68L237.714286 806.180571a45.714286 45.714286 0 0 1-45.714286-45.714285V73.142857A27.428571 27.428571 0 0 1 219.428571 45.714286z m-208.201143 178.944l-84.077715 84.041143-84.022857-84.041143-38.784 38.765714 74.843429 74.861714H384v54.857143h100.571429v54.857143H384v54.857143h100.553143L484.571429 585.142857h54.857142l-0.018285-82.285714H640v-54.857143h-100.571429v-54.857143H640v-54.857143h-79.707429l74.861715-74.843428-38.784-38.784z" fill="#111111" p-id="7381"></path>
            </svg>
            罚款金额</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.sumAmountPaid" :duration="2000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<el-col :span="3" :style="stylecountprocode">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('countProCode')">
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="昨日影响链接量" placement="top-start">
          <span style="margin-right: 10px;">
            <svg t="1689227267233" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="26357" width="200" height="200">
            <path d="M810.666667 256a128 128 0 0 1 127.786666 120.490667L938.666667 384v213.333333a128 128 0 0 1-120.490667 127.786667L810.666667 725.333333h-202.666667a42.666667 42.666667 0 0 1-4.992-85.034666L608 640H810.666667a42.666667 42.666667 0 0 0 42.368-37.674667L853.333333 597.333333V384a42.666667 42.666667 0 0 0-37.674666-42.368L810.666667 341.333333h-202.666667a42.666667 42.666667 0 0 1-4.992-85.034666L608 256H810.666667zM416 256a42.666667 42.666667 0 0 1 4.992 85.034667L416 341.333333H213.333333a42.666667 42.666667 0 0 0-42.368 37.674667L170.666667 384v213.333333a42.666667 42.666667 0 0 0 37.674666 42.368L213.333333 640h202.666667a42.666667 42.666667 0 0 1 4.992 85.034667L416 725.333333H213.333333a128 128 0 0 1-127.786666-120.490666L85.333333 597.333333V384a128 128 0 0 1 120.490667-127.786667L213.333333 256h202.666667z m249.6 192a42.666667 42.666667 0 0 1 4.992 85.034667l-4.992 0.298666h-298.666667a42.666667 42.666667 0 0 1-4.992-85.034666L366.933333 448h298.666667z" fill="#000000" p-id="26358"></path>
            </svg>
            影响链接</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.countProCode" :duration="2000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>

<el-col :span="3" :style="stylerefundamont">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('payAmountAll')">
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="付款金额" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689226833671" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12851" width="200" height="200">
            <path d="M512 64.503937a447.637165 447.637165 0 0 1 174.170709 859.857638A447.637165 447.637165 0 0 1 337.83937 99.638425 444.663937 444.663937 0 0 1 512 64.503937m0-64.503937C229.230866 0 0 229.230866 0 512s229.230866 512 512 512 512-229.230866 512-512S794.769134 0 512 0z" fill="#333333" p-id="12852"></path>
            <path d="M480.433386 381.984252h64.503937v417.431181h-64.503937z" fill="#333333" p-id="12853"></path><path d="M305.829291 374.929134h412.694174v64.503937H305.829291z" fill="#333333" p-id="12854"></path><path d="M339.498331 273.686173l45.611338-45.611338 165.155276 165.155275-45.611339 45.611339z" fill="#333333" p-id="12855"></path><path d="M478.746205 393.237165l166.843464-166.843464 45.612347 45.61033-166.844473 166.843465zM343.705197 552.314961h336.932283v64.503937h-336.932283z" fill="#333333" p-id="12856"></path>
            </svg>
            付款金额</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.payAmountAll" :duration="1000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>

<el-col :span="3" :style="stylerefundamont">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('refundAmont')">
    <!-- <div class="card-panel-icon-wrapper icon-warning">
      <i class='el-icon-warning' style=""></i>
    </div> -->
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="退款金额" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689227025389" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18292" width="200" height="200">
            <path d="M936.470481 431.851589c-9.006118-7.248078-22.181185-5.816472-29.404704 3.25616l-35.853579 44.924165c-1.173732-17.362436-3.472078-34.854832-7.119141-52.390207-44.728714-216.201064-255.093862-354.923522-469.839785-309.911352C179.482791 162.782434 41.648562 374.513696 86.3783 590.670757c44.729737 216.202087 255.091816 354.925568 469.861274 309.87042 78.063937-16.363689 145.169287-55.384401 197.687408-108.122533 0.194428-0.173962 0.39295-0.345877 0.587378-0.563842 0.934279-0.955768 1.257643-2.039449 2.191922-3.037173 1.541099-2.607384 2.64627-5.686513 2.64627-8.940626 0-9.54847-7.723915-17.319457-17.231453-17.319457-5.120624 0-9.592472 2.343371-12.760628 5.903453l-0.12996-0.085958c-47.680952 47.789423-108.860337 83.033112-179.697685 97.879238-194.476273 40.799218-384.982118-84.857667-425.479461-280.615119-40.518832-195.758476 84.314291-387.522987 278.791587-428.324252 194.476273-40.757263 384.982118 84.901669 425.480484 280.660144 3.124154 15.061021 5.253654 30.036084 6.509251 44.968168l-43.447535-35.114752c-9.029654-7.248078-22.181185-5.816472-29.404704 3.25616-7.251148 9.071609-5.794983 22.309098 3.212158 29.601178 0.041956 0.044002 0.041956 0.044002 0.085958 0.044002l80.06143 64.717977c0.022513 0 0.064468 0.041956 0.084934 0.041956 4.560876 3.689019 10.202363 5.121648 15.581883 4.470825 5.424546-0.563842 10.615779-3.123131 14.281261-7.724938 0.022513-0.041956 0.044002-0.086981 0.044002-0.086981l64.282049-80.646761c0.023536 0 0.023536 0 0.062422-0.044002C946.910251 452.382181 945.476599 439.144692 936.470481 431.851589z" fill="#272636" p-id="18293"></path><path d="M463.635479 730.611973c0 9.981329 8.138354 18.10024 18.144242 18.10024 10.004865 0 18.123776-8.116865 18.123776-18.10024l0.107447 0L500.010945 622.923322l109.033275 0 0-0.172939c10.006911 0 18.122753-8.160867 18.122753-18.145266 0-10.025331-8.116865-18.141172-18.122753-18.141172l-109.033275 0 0-56.035224 109.054765 0 0-0.002047 0-0.173962c9.983375 0 18.10024-8.116865 18.10024-18.143219 0-10.026354-8.116865-18.144242-18.10024-18.144242L500.008898 493.965252l0-33.378202 108.101043-106.037035-0.10847-0.130983c7.487531-6.641257 8.159844-18.10024 1.51961-25.610284-6.66377-7.509021-18.122753-8.159844-25.610284-1.518586l-101.676727 100.222609L375.285269 322.472436c-7.510044-6.642281-18.969027-5.946432-25.610284 1.561565-6.640234 7.466042-5.966898 18.925025 1.51961 25.566282l-0.10847 0.130983 112.463398 109.989043 0 34.244942L354.493733 493.965252l0 0.088004c-9.983375 0-18.10024 8.115841-18.10024 18.144242 0 10.026354 8.116865 18.144242 18.10024 18.144242l0 0.086981 109.055788 0 0 56.035224L354.493733 586.463945l0 0.084934c-9.983375 0-18.122753 8.160867-18.122753 18.145266 0 10.025331 8.138354 18.141172 18.122753 18.141172l0 0.088004 109.055788 0 0 107.687628L463.635479 730.61095z" fill="#272636" p-id="18294"></path>
            </svg>
            退款金额</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.refundAmont" :duration="1000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<el-col :span="3" :style="styleordercount">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('orderCount')">
    <!-- <div class="card-panel-icon-wrapper icon-warning">
      <i class='el-icon-warning' style=""></i>
    </div> -->
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="订单量" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689226525047" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3337" width="200" height="200">
            <path d="M704 192a106.666667 106.666667 0 0 1 106.666667 106.666667v469.333333a106.666667 106.666667 0 0 1-106.666667 106.666667H320a106.666667 106.666667 0 0 1-106.666667-106.666667V298.666667a106.666667 106.666667 0 0 1 106.666667-106.666667 21.333333 21.333333 0 1 0 0-42.666667 149.333333 149.333333 0 0 0-149.333333 149.333334v469.333333a149.333333 149.333333 0 0 0 149.333333 149.333333h384a149.333333 149.333333 0 0 0 149.333333-149.333333V298.666667a149.333333 149.333333 0 0 0-149.333333-149.333334 21.333333 21.333333 0 0 0 0 42.666667z" fill="#222222" p-id="3338"></path>
            <path d="M320 554.666667a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320z m0-128a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320z m0 256a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320zM405.333333 192h213.333334a21.333333 21.333333 0 0 0 0-42.666667H405.333333a21.333333 21.333333 0 0 0 0 42.666667z" fill="#222222" p-id="3339"></path>
            </svg>
            订单量</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.orderCount" :duration="1000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<el-col  :span="3" :style="styleAverageExpressPrice" >
  <div v-if="checkPermission('homepermission')" class="card-panel"

  >
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="快递均价" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689227102015" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21693" width="200" height="200">
            <path d="M927.887085 627.985886c0-0.722653 0-0.722653 0 0L865.738885 361.326747c-16.62103-70.097389-72.988003-108.398024-117.069866-108.398024h-71.542696c-13.730416 0-25.292872 11.562456-25.292872 25.292872v267.381792c0 13.730416 11.562456 25.292872 25.292872 25.292873h100.448836c13.730416 0 25.292872-11.562456 25.292872-25.292873s-11.562456-25.292872-25.292872-25.292872H703.141849V303.514467h46.249824c15.898377 0 54.921665 17.343684 67.929428 69.374736l61.425547 265.936485c6.503881 33.964714 1.445307 53.476359-15.898377 59.98024-13.007763 4.335921-20.234298 18.788991-15.898377 31.796754 3.613267 10.839802 13.730416 17.343684 23.847565 17.343684 2.890614 0 5.781228-0.722653 7.949189-1.445307 20.956951-7.949188 66.484121-33.242061 49.140437-118.515173z" fill="" p-id="21694"></path>
            <path d="M161.874382 244.256881h422.752294c13.730416 0 25.292872-11.562456 25.292872-25.292873s-11.562456-25.292872-25.292872-25.292872H161.874382c-45.52717 0-78.769231 29.628793-78.76923 71.542696V672.067749c0 27.460833 14.45307 52.753705 38.300635 67.206775 4.335921 2.16796 8.671842 3.613267 13.007763 3.613267 8.671842 0 16.62103-4.335921 21.679605-12.285109 7.226535-12.285109 3.613267-27.460833-8.671842-34.687368-8.671842-5.058574-13.730416-14.45307-13.730417-23.847565V264.491179c0-18.788991 21.679605-20.234298 28.183486-20.234298z" fill="" p-id="21695"></path>
            <path d="M706.755116 609.919548c-54.921665 0-101.171489 39.023289-112.011291 91.776994h-187.889908c-10.839802-52.031052-57.089626-91.776994-112.011292-91.776994-62.870854 0-114.179252 51.308398-114.179252 114.179252S231.971771 838.278052 294.842625 838.278052c53.476359 0 98.280875-36.855328 110.565985-86.718419h190.780522c12.285109 49.863091 57.089626 86.718419 110.565984 86.718419 62.870854 0 114.179252-51.308398 114.179252-114.179252s-51.308398-114.179252-114.179252-114.179252z m-348.318983 121.405787c-3.613267 31.796754-30.351447 56.366972-63.593508 56.366973-35.410021 0-63.593507-28.90614-63.593507-63.593508s28.90614-63.593507 63.593507-63.593507c33.964714 0 62.1482 26.738179 63.593508 60.702893-0.722653 2.16796-0.722653 3.613267-0.722654 5.781228 0 1.445307 0 2.890614 0.722654 4.335921z m348.318983 56.366973c-35.410021 0-63.593507-28.90614-63.593507-63.593508s28.90614-63.593507 63.593507-63.593507 63.593507 28.90614 63.593508 63.593507-28.183486 63.593507-63.593508 63.593508z" fill="" p-id="21696"></path>
            <path d="M578.845448 513.08398m-27.460833 0a27.460833 27.460833 0 1 0 54.921666 0 27.460833 27.460833 0 1 0-54.921666 0Z" fill="" p-id="21697"></path>
            <path d="M578.845448 416.971066m-27.460833 0a27.460833 27.460833 0 1 0 54.921666 0 27.460833 27.460833 0 1 0-54.921666 0Z" fill="" p-id="21698"></path>
            <path d="M578.845448 320.858151m-27.460833 0a27.460833 27.460833 0 1 0 54.921666 0 27.460833 27.460833 0 1 0-54.921666 0Z" fill="" p-id="21699"></path>
            </svg>
            快递均价</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.averageExpressPrice" :decimals="2" :duration="1000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<el-col  :span="3" :style="styleguestunitprice" >
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('guestUnitPrice')">
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="客单价（支付金额/订单量）" placement="top-start">
          <span style="margin-right: 5px;">
            <svg t="1689226525047" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3337" width="200" height="200">
            <path d="M704 192a106.666667 106.666667 0 0 1 106.666667 106.666667v469.333333a106.666667 106.666667 0 0 1-106.666667 106.666667H320a106.666667 106.666667 0 0 1-106.666667-106.666667V298.666667a106.666667 106.666667 0 0 1 106.666667-106.666667 21.333333 21.333333 0 1 0 0-42.666667 149.333333 149.333333 0 0 0-149.333333 149.333334v469.333333a149.333333 149.333333 0 0 0 149.333333 149.333333h384a149.333333 149.333333 0 0 0 149.333333-149.333333V298.666667a149.333333 149.333333 0 0 0-149.333333-149.333334 21.333333 21.333333 0 0 0 0 42.666667z" fill="#222222" p-id="3338"></path>
            <path d="M320 554.666667a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320z m0-128a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320z m0 256a21.333333 21.333333 0 0 1 0-42.666667h384a21.333333 21.333333 0 0 1 0 42.666667H320zM405.333333 192h213.333334a21.333333 21.333333 0 0 0 0-42.666667H405.333333a21.333333 21.333333 0 0 0 0 42.666667z" fill="#222222" p-id="3339"></path>
            </svg>
            客单价</span>
        </el-tooltip>
      </div>
      <count-to v-show="desensitized" style="margin-right: 10px;" :start-val="0" :end-val="daySum.guestUnitPrice" :decimals="2" :duration="1000" class="card-panel-num" />
      <div v-show="!desensitized" style="font-size: 18px;cursor: default;">*****</div>
    </div>
  </div>
</el-col>
<!-- <el-col :span="3" class="card-panel-col">
  <div v-if="checkPermission('homepermission')" class="card-panel" @click="handleSetLineChartData('warning')">
    <div class="card-panel-description">
      <div class="card-panel-text">
        <el-tooltip class="item" effect="dark" content="动销率（销售链接数/总链接数）" placement="top-start">
          <span>动销率</span>
        </el-tooltip>
      </div>
      <count-to :start-val="0" :end-val="0" :duration="3000" class="card-panel-num" />
    </div>
  </div>
</el-col> -->
    </el-row>
      </div>

    <!-- </my-container> -->
</template>

<script>
import MyContainer from "@/components/my-container";
import countTo from 'vue-count-to'
export default {
    name: 'YunhanAdminIndex',
    components: {MyContainer,countTo},
    props: {
      daySum: {}
    },
    data() {
        return {
            desensitized: false,
            list:[],
            styleall:'background-color: #409EFF;color:#fff;border-radius: 10px;',
            styletao:'',
            stylepin:'',
            stylegong:'',
            styledy:'',
            styleSheIn:'',
            styleKWaiShop:'',
            styleVideo:'',
            styleJD:'',
            styleJingXi:'',
            styleJingXiNN:'',
            styleAlibaba:'',
            styleSuNing:'',
            stylegoodscodenum:'',
            stylegoodnumcurrentnum:'',
            styleamountpaid:'',
            stylecountprocode:'',
            stylerefundamont:'',
            styleordercount:'',
            styleguestunitprice:'',
            styleAverageExpressPrice:''
        };
    },
    async mounted() {

    },

    methods: {
         async handleSetLineChartData(type){
            if(!this.desensitized){
              return
            }
           this.$emit('handleSetLineChartData', type, 0)
           if (type == 'shoppings'){
             this.styleall = 'background-color: #409EFF;color:#fff;border-radius: 10px;'
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''

           }
           else if (type == 'saleAmonttian'){
             this.styleall = ''
             this.styletian='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.stylepin='',
             this.styletao='',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmonttao'){
             this.styleall = ''
             this.styletao='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.stylepin='',
             this.styletian='',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'messages'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'purchases'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontDY'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontJD'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleJD='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontJingXi'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleJD='',
             this.styleJingXi='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontJingXiNN'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styleAlibaba='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontAlibaba'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontSuNing'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontSheIn'){
             this.styleall = ''
             this.styletao='',
             this.styletian='',
             this.stylepin='',
             this.stylegong='',
             this.styledy='',
             this.styleSheIn='background-color: #409EFF;color:#fff;border-radius: 10px;',
             this.styleKWaiShop='',
             this.styleVideo='',
             this.styleJD='',
             this.styleJingXi='',
             this.styleJingXiNN='',
             this.styleAlibaba='',
             this.styleSuNing='',
             this.stylegoodscodenum='',
             this.stylegoodnumcurrentnum='',
             this.styleamountpaid='',
             this.stylecountprocode='',
             this.stylerefundamont='',
             this.styleordercount='',
             this.styleguestunitprice='',
             this.styleAverageExpressPrice=''
           }
           else if (type == 'saleAmontKWaiShop'){
              this.styleall = ''
              this.styletao='',
              this.styletian='',
              this.stylepin='',
              this.stylegong='',
              this.styledy='',
              this.styleSheIn='',
              this.styleKWaiShop='background-color: #409EFF;color:#fff;border-radius: 10px;',
              this.styleVideo='',
              this.styleJD='',
              this.styleJingXi='',
              this.styleJingXiNN='',
              this.styleAlibaba='',
              this.styleSuNing='',
              this.stylegoodscodenum='',
              this.stylegoodnumcurrentnum='',
              this.styleamountpaid='',
              this.stylecountprocode='',
              this.stylerefundamont='',
              this.styleordercount='',
              this.styleguestunitprice='',
              this.styleAverageExpressPrice=''
            }
           else if (type == 'saleAmontWeChat'){
              this.styleall = ''
              this.styletao='',
              this.styletian='',
              this.stylepin='',
              this.stylegong='',
              this.styledy='',
              this.styleSheIn='',
              this.styleKWaiShop='',
              this.styleVideo='background-color: #409EFF;color:#fff;border-radius: 10px;',
              this.styleJD='',
              this.styleJingXi='',
              this.styleJingXiNN='',
              this.styleAlibaba='',
              this.styleSuNing='',
              this.stylegoodscodenum='',
              this.stylegoodnumcurrentnum='',
              this.styleamountpaid='',
              this.stylecountprocode='',
              this.stylerefundamont='',
              this.styleordercount='',
              this.styleguestunitprice='',
              this.styleAverageExpressPrice=''
            }

      },
    },
};
</script>

<style lang="scss" scoped>
.timecard{
    width: 90%;
    height:520px;
    overflow:auto;

}
.demo-image__lazy {
    margin: 10px;
    height: 520px;
    overflow-y: auto;
}
.kedanjiacss{
  background-color: #34bfa3;
}
.panel-group {
  // margin-top: 18px;


  .card-panel-col {
    // margin-bottom: 15px;
    color: rgba(51, 51, 51, 1);

  }

  .card-panel {
    // height: 108px;
    cursor: pointer;
    //font-size: 12px;
    position: relative;
    //overflow: hidden;
    // color: #666;
    // background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);
    text-align: center;
    padding: 10px 0;
    &:hover {
      .card-panel {
        color: #fff;
      }
      .el-col-1 {
          width: 8.33333%;
      }
        // transform: scale(1.05);
        overflow: hidden;
        // color: #fff;
        // background-color: #409EFF;
        // border-radius: 10px;
    }

    .icon-people {
      color: #40c9c6;
      font-size: 48px;
    }

    .icon-message {
      color: #36a3f7;
      font-size: 48px;
    }

    .icon-money {
      color: #f4516c;
      font-size: 48px;
    }

    .icon-shopping {
      color: #34bfa3;
      font-size: 48px;
    }

    .icon-warning {
      color: #f4516c;
      font-size: 48px;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 12px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      // color: rgba(0, 0, 0, 0.45);
      // float: right;
      font-weight: bold;
      // margin: 26px 0;
      // margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 18px;
        color: #409EFF;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
      //background-color: #409EFF;
    }
  }
}
// .marginleft{
//   margin-left: -20px;
// }
</style>
