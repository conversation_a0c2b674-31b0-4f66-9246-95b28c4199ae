<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.yhGoodsCode" type="text" maxlength="100" clearable placeholder="商品编码" style="width:120px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-input v-model.trim="Filter.yhGoodsName" type="text" maxlength="100" clearable placeholder="商品名称" style="width:160px;" />
                    </el-button>
                    <el-button type="primary" @click="onSearch" icon="el-icon-search">查询</el-button>
                    <el-button @click="()=>{Filter={};}"  icon="el-icon-close">清空查询条件</el-button>
                    <el-button type="primary" @click="onAddClick" icon="el-icon-plus">新增关联</el-button>
                </el-button-group>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' 
        :isSelection='false' :isSelectColumn="false" :tableCols='tableCols' :loading="listLoading" >

            <!-- <template slot='extentbtn'>
                <el-button-group>                   
                    <el-button type="primary" @click="onImport">导入</el-button>
                </el-button-group>
            </template> -->
            
            <el-table-column width="120" label="操作" fixed="right">               
                <template slot-scope="scope">                   
                    <el-button type="text" @click="onOpenDtl(scope.row.yhGoodsCode,2)" >编辑</el-button>
                    <el-button type="text" @click="onOpenDtl(scope.row.yhGoodsCode,3)" >详情</el-button>
                </template>
            </el-table-column>

        </ces-table>
   
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>


     

    </my-container>
</template>
<script>  

    import {
        PageHpYgGoodsAsync, GetDpSupplierByIdAsync
    } from '@/api/order/alllinkDaiPai'

    import cesTable from "@/components/Table/table.vue";
    import { formatTime } from "@/utils";
    import { formatmoney, formatPercen, getUrlParam,  setStore, getStore,formatLink } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";


    const tableCols = [
        { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '120', sortable: 'custom'},
        { istrue: true, prop: 'yhGoodsName', label: '商品名称', minwidth: '160', sortable: 'custom' },

        { istrue: true, prop: 'auditUserName', label: '审核人', width: '100', sortable: 'custom' },
       
        { istrue: true, prop: 'yhGoodsPrice', label: '商品价格', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'dpTotalSupPrice', label: '厂家SKU总成本', width: '140', sortable: 'custom' },

    ];

    export default {
        name: "YhGoodsList",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
        data() {
            return {
                that: this,
                Filter: {
                    yhGoodsCode: "",
                    yhGoodsName: "",
                    auditUserName:''
                },
                tbdatalist: [],
                tableCols: tableCols,
                total: 0,
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
            };
        },
        async mounted() {

            this.onSearch();
        },
        methods: {        
           
            onOpenDtl(oid,mode){
                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/YhGoodsRelationForm.vue`,
                    args:{oid:oid,mode:mode},
                    title:"关联厂家商品",
                    height:300,
                    callOk:()=>{
                        self.onRefresh();
                    }
                });              
               
            },
            onAddClick(){
                let self=this;
                this.$showDialogform({
                    path:`@/views/order/daipai/YhGoodsRelationForm.vue`,
                    title:"关联厂家商品",
                    args:{oid:'',mode:1},
                    height:300,
                    callOk:self.onRefresh
                });
               
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                var pager = this.$refs.pager.getPager();
                const params = {
                    ...pager,
                    ...this.pager,
                    ...para,
                };

                this.listLoading = true;
                const res = await PageHpYgGoodsAsync(params);

                this.listLoading = false;

                this.total = res.data.total
                this.tbdatalist = res.data.list;

            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>
