<template>
  <my-container>
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item>
          <el-button size="small" type="primary" @click="startImport">导入订单</el-button>
        </el-form-item>
      </el-form>
    </template>

    <el-dialog title="导入订单数据" :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false" v-dialogDrag>
      <div>
        <span style="line-height: 28px;height: 28px;">关键词：</span>
        <el-tag v-for="tag in keyNameList" :key="tag" size="medium" :disable-transitions="false" @close="removeKey(tag)"
          closable>{{ tag }} </el-tag>
        <el-input class="input-new-tag" v-if="inputVisible" v-model.trim="inputValue" clearable ref="saveTagInput" maxlength="10" placeholder="关键词"
          size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
        </el-input>
        <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 关键词</el-button>
      </div>
      <p></p>
      <div>
        <span style="line-height: 28px;height: 28px;">是否包含关键词下一条非关键词时间</span>
        <el-select v-model="hasKeyNameNext" placeholder="请选择" size="small">
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </div>
      <p></p>
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="true" :limit="4" action
          accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :file-list="fileList">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template> 
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="success" @click="submitUpload" :loading="uploadLoading">
          {{ (uploadLoading ? '上传中' : '上传&生成导出任务') }}
        </el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { importSearchOrderKeyNodeAnalysisAsync } from "@/api/order/orderdeductmoney";
export default {
  name: "YunHanOrderKeyNodeAnalysis",
  components: { MyContainer, MyConfirmButton },
  data() {
    return { 
      inputVisible: false,
      inputValue: '',
      keyNameList: [],
      hasKeyNameNext:0,
      dialogVisible: false,
      uploadLoading:false,
      fileList: []
    };
  },
  async mounted() {

  },
  methods: {
    //开始导入
    startImport() {
      this.uploadLoading = false;
      this.dialogVisible = true;
      let that = this;
      this.$nextTick(()=>{
        if(that.keyNameList.length==0){
          that.keyNameList = ['金华','上海','昆山']
        }
        that.$refs.upload.clearFiles();
      }); 
    },
    //取消导入
    cancelImport() {
      this.dialogVisible = false;
    },
    async submitUpload() {
      if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit = true;
      this.uploadLoading = true;
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!this.fileHasSubmit) {
        return false;
      }
      this.fileHasSubmit = false;
      this.uploadLoading = true;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("keyNameList",this.keyNameList);
      form.append("hasKeyNameNext",this.hasKeyNameNext);
      const res = await importSearchOrderKeyNodeAnalysisAsync(form);
      if (res.code == 1) {
        this.$message({ message: "上传成功,正在导入中,请稍后在下载管理查看...", type: "success" });
        this.dialogVisible = false;
      }
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
    },
    async uploadChange(file, fileList) {
      let files = [];
      files.push(file)
      this.fileList = files;
    },
    addKey() {
      let nkey = this.keyName;
      var obj = this.keyNameList.filter(x => x == nkey);
      if (obj == null || obj.length == 0) {
        this.keyNameList.push(nkey);
      } else {
        this.$message({
          message: '关键词已存在！',
          type: "warning",
        });
      }
    },
    removeKey(key) {
      this.keyNameList = this.keyNameList.filter(x => x != key);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      var obj = this.keyNameList.filter(x => x == inputValue);
      if (inputValue) {
        if ((obj == null || obj.length == 0)) {
          this.keyNameList.push(inputValue);
          this.inputVisible = false;
          this.inputValue = '';
        } else {
          this.$message({
            message: '关键词已存在！',
            type: "warning",
          });
        }
      }
      this.inputVisible = false;
      this.inputValue = '';
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

.el-tag+.el-tag {
  margin-left: 10px;
  margin-top: 1px;
}

.button-new-tag {
  margin-left: 10px;
  height: 28px;
  line-height: 28px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  height: 28px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
