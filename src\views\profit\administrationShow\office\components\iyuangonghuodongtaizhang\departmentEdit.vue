<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="110px" class="demo-ruleForm">
        <div class="city-name">南昌:</div>
        <el-form-item label="场次" prop="ncSession">
          <inputNumberYh v-model="ruleForm.ncSession" :placeholder="'南昌场次'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人数" prop="ncPeople">
          <inputNumberYh v-model="ruleForm.ncPeople" :placeholder="'南昌人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额" prop="ncAmount">
          <inputNumberYh v-model="ruleForm.ncAmount" :placeholder="'南昌金额'" class="publicCss" />
        </el-form-item>
        <div class="city-name">义乌:</div>
        <el-form-item label="场次" prop="ywSession">
          <inputNumberYh v-model="ruleForm.ywSession" :placeholder="'义乌场次'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人数" prop="ywPeople">
          <inputNumberYh v-model="ruleForm.ywPeople" :placeholder="'义乌人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额" prop="ywAmount">
          <inputNumberYh v-model="ruleForm.ywAmount" :placeholder="'义乌金额'" class="publicCss" />
        </el-form-item>
        <div class="city-name">武汉:</div>
        <el-form-item label="场次" prop="whSession">
          <inputNumberYh v-model="ruleForm.whSession" :placeholder="'武汉场次'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人数" prop="whPeople">
          <inputNumberYh v-model="ruleForm.whPeople" :placeholder="'武汉人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额" prop="whAmount">
          <inputNumberYh v-model="ruleForm.whAmount" :placeholder="'武汉金额'" class="publicCss" />
        </el-form-item>
        <div class="city-name">深圳:</div>
        <el-form-item label="场次" prop="szSession">
          <inputNumberYh v-model="ruleForm.szSession" :placeholder="'深圳场次'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人数" prop="szPeople">
          <inputNumberYh v-model="ruleForm.szPeople" :placeholder="'深圳人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额" prop="szAmount">
          <inputNumberYh v-model="ruleForm.szAmount" :placeholder="'深圳金额'" class="publicCss" />
        </el-form-item>
        <div class="city-name">选品中心:</div>
        <el-form-item label="场次" prop="xpSession">
          <inputNumberYh v-model="ruleForm.xpSession" :placeholder="'选品中心场次'" class="publicCss" />
        </el-form-item>
        <el-form-item label="人数" prop="xpPeople">
          <inputNumberYh v-model="ruleForm.xpPeople" :placeholder="'选品中心人数'" class="publicCss" />
        </el-form-item>
        <el-form-item label="金额" prop="xpAmount">
          <inputNumberYh v-model="ruleForm.xpAmount" :placeholder="'选品中心金额'" class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { employeeActivityLedgerSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        ncSession: '',//南昌场次
        ncPeople: '',//南昌人数
        ncAmount: '',//南昌金额
        ywSession: '',//义乌场次
        ywPeople: '',//义乌人数
        ywAmount: '',//义乌金额
        whSession: '',//武汉场次
        whPeople: '',//武汉人数
        whAmount: '',//武汉金额
        szSession: '',//深圳场次
        szPeople: '',//深圳人数
        szAmount: '',//深圳金额
        xpSession: '',//选品中心场次
        xpPeople: '',//选品中心人数
        xpAmount: '',//选品中心金额
      },
      rules: {
        ncSession: [
          { required: true, message: '请输入南昌场次', trigger: 'blur' },
        ],
        ncPeople: [
          { required: true, message: '请输入南昌人数', trigger: 'blur' },
        ],
        ncAmount: [
          { required: true, message: '请输入南昌金额', trigger: 'blur' },
        ],
        ywSession: [
          { required: true, message: '请输入义乌场次', trigger: 'blur' },
        ],
        ywPeople: [
          { required: true, message: '请输入义乌人数', trigger: 'blur' },
        ],
        ywAmount: [
          { required: true, message: '请输入义乌金额', trigger: 'blur' },
        ],
        whSession: [
          { required: true, message: '请输入武汉场次', trigger: 'blur' },
        ],
        whPeople: [
          { required: true, message: '请输入武汉人数', trigger: 'blur' },
        ],
        whAmount: [
          { required: true, message: '请输入武汉金额', trigger: 'blur' },
        ],
        szSession: [
          { required: true, message: '请输入深圳场次', trigger: 'blur' },
        ],
        szPeople: [
          { required: true, message: '请输入深圳人数', trigger: 'blur' },
        ],
        szAmount: [
          { required: true, message: '请输入深圳金额', trigger: 'blur' },
        ],
        xpSession: [
          { required: true, message: '请输入选品中心场次', trigger: 'blur' },
        ],
        xpPeople: [
          { required: true, message: '请输入选品中心人数', trigger: 'blur' },
        ],
        xpAmount: [
          { required: true, message: '请输入选品中心金额', trigger: 'blur' },
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await employeeActivityLedgerSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}

.city-name {
  font-size: 17px;
  font-weight: bold;
  margin: 0 0 5px 5%;
}
</style>
