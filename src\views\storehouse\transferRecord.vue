<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <!-- <spam>单据日期：</spam> -->
        <el-date-picker v-model="allocationDateRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="单据开始日期" end-placeholder="单据结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeAllocationDate">
        </el-date-picker>
        <el-select v-model="ListInfo.Status" placeholder="状态" multiple collapse-tags class="publicCss" clearable>
          <el-option :key="'调拨中'" label="调拨中" value="Confirmed" />
          <el-option :key="'确认'" label="确认" value="Confirming" />
          <el-option :key="'取消'" label="取消" value="Cancelled" />
        </el-select>
        <button style="padding: 0; border: none;">
          <inputYunhan title="请分行输入商品编码" placeholder="商品编码" :maxRows="100" :inputshow="0" :clearable="true"
            @callback="skucallback" :inputt.sync="ListInfo.sku_id" skuid></inputYunhan>
        </button>
        
        <button style="padding: 0; border: none;">
          <inputYunhan title="请分行输入调入调出单号" placeholder="调入、调出单号" :maxRows="100" :inputshow="0" :clearable="true"
            @callback="InOutcallback" :inputt.sync="ListInfo.inOut_id" inOutid></inputYunhan>
        </button>
        <!-- <el-input placeholder="调入、调出单号" v-model="ListInfo.inOut_id" :maxlength="100" :clearable="true" style="width:150px" type="text" @input="InOutcallback"></el-input> -->

        <el-select v-model="ListInfo.link_warehouse" placeholder="调入仓" style="width:130px" multiple collapse-tags
          clearable>
          <el-option v-for="item in WarehouseList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>  
        </el-select>
        <el-select v-model="ListInfo.warehouse" placeholder="调出仓" style="width:130px" multiple collapse-tags clearable>
          <el-option v-for="item in WarehouseList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="ListInfo.notify" placeholder="是否通知" style="width:100px" clearable>
          <el-option :key="'已通知'" label="已通知" value="已通知" />
          <el-option :key="'未通知'" label="未通知" value="未通知" />
        </el-select>
        <!-- <spam>最近通知时间：</spam> -->
        <el-date-picker v-model="notifyTimeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="最近通知开始时间" end-placeholder="最近通知结束时间" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeNodifyTime">
        </el-date-picker>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onSetup">设置</el-button>
        <el-button type="primary" @click="onNote">一键通知</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'transferRecord202408041901_1'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="true" :isSelectColumn="true"
      :summaryarry='summaryarry' :showsummary='true' @select="choose" style="width: 100%;margin: 0" v-loading="loading"
      :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="设置" :visible.sync="setup" width="25%" v-dialogDrag>
      <div style="height: 500px;">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
          <div style="display: flex; align-items: flex-start; justify-content: baseline;">
            <el-form-item label="单据日期超过" prop="limitDay">
              <!-- <el-input v-model="ruleForm.limitDay" style="width: 182px;" type="number" /> -->
              <el-input-number v-model="ruleForm.limitDay" style="width: 182px;" :min=0 :max=999 :precision="0"></el-input-number>
              <span style="margin-left: 10px;">天进入</span>
            </el-form-item>
          </div>
          <el-form-item label="只通知" prop="notifyClass">
            <el-select v-model="ruleForm.notifyClass">
              <el-option label="全部" value="全部"></el-option>
              <el-option label="调入仓" value="调入仓"></el-option>
              <el-option label="调出仓" value="调出仓"></el-option>
            </el-select>
          </el-form-item>
          <div style="display: flex; justify-content: space-between; align-items: center;width: 100%;">
            <span style="font-weight: bold;">设置仓库接收人</span>
            <i class="el-icon-circle-plus" style="font-size: 25px;margin-right: 10px;cursor: pointer;"
              @click="onSettings('receiver')"></i>
          </div>
          <div style="height: 160px">
            <div style="margin: 10px 0 5px 0;">
              <span>仓库</span>
              <span style="margin-left: 35%;">接收人</span>
            </div>
            <el-scrollbar style="height: 100%;">
              <div v-for="(item, i) in ruleForm.namesResponse" :key="i"
                style="display: flex;align-items: center;margin-top: 5px;">
                <el-select v-model="item.warehouseID" placeholder="请选择仓库" style="width: 40%;" clearable>
                  <el-option v-for="item in WarehouseList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
                <YhUserselectormulti maxlength="50" :value.sync="item.receiverID" :text.sync="item.receiverName"
                  style="width: 40%;margin: 0 11% 0 5px;" clearable placeholder="请选择接收人">
                </YhUserselectormulti>
                <i class="el-icon-remove" style="font-size: 25px;margin-right: 10px;cursor: pointer;"
                  @click="onDeleteEvent(i, 'receiver')"></i>
              </div>
            </el-scrollbar>
          </div>

          <div style="display: flex; justify-content: space-between; align-items: center;width: 100%;margin-top: 25px;">
            <span style="font-weight: bold;">设置通知频率</span>
            <i class="el-icon-circle-plus" style="font-size: 25px;margin-right: 10px;cursor: pointer;"
              @click="onSettings('frequency')"></i>
          </div>
          <div style="height: 160px;width: 100%;">
            <el-scrollbar style="height: 100%;">
              <div v-for="(item, index) in ruleForm.notifyFrequencysResponse" :key="index"
                style="display: flex;align-items: center;margin-top: 5px;">
                <el-time-select v-model="item.time" placeholder="请选择时间" value-format="HH:mm" format="HH:mm"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '23:30'
                }" style="width: 50%;"></el-time-select>
                <i class="el-icon-remove" style="font-size: 25px;margin-left: 42%;cursor: pointer;"
                  @click="onDeleteEvent(index, 'frequency')"></i>
              </div>
            </el-scrollbar>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="setup = false">取 消</el-button>
        <el-button type="primary" @click="onAddedSettingsEvent">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="通知日志" :visible.sync="noteShow" v-dialogDrag >
      <div style="height: 500px;">
        <vxetablebase :id="'transferRecord202408041901_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :toolbarshow="false" :tablefixed='true'
          @sortchange='noteSortChange' :tableData='noteData' :tableCols='noteCols' :isSelection="false"
          :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true' style="width: 100%;margin: 0"
          :height="'100%'">
        </vxetablebase>
      </div>
      <template #footer>
      <my-pagination ref="noetPager" :total="noteTotal" @page-change="noetPageChange" @size-change="noetSizeChange" />
      </template>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { getTbWarehouseList } from '@/api/inventory/openwebjushuitan';
import {
  getInventoryAllocate, setInventoryAllocateSetting, exportInventoryAllot,
  getInventoryAllotLog, getInventoryAllocateSetting, clickNotifyInventoryAllocate, GetH5PageData
} from "@/api/inventory/allocate";
import YhUserselectormulti from "@/components/YhCom/yh-userselectormulti.vue";
import inputYunhan from '@/components/Comm/inputYunhan.vue'

//格式化列格式
function formatStatus(status) {
  if (status == "Confirmed") {
    return "调拨中";
  } else if (status == "Confirming") {
    return "确认";
  } else if (status == "Cancelled") {
    return "取消";
  } else return "";
}

const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '状态', formatter: (row) => formatStatus(row.status) },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'io_date', label: '单据日期', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'sku_id', label: '商品编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'inId', label: '调入单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'outId', label: '调出单号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'type', label: '调拨类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'link_warehouse', label: '调入仓库', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouse', label: '调出仓库', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'qty', label: '数量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'remark', label: '备注', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'notify', label: '是否通知', },
  { sortable: 'custom', width: 'auto', align: 'center', type: 'click', prop: 'notifyTime', label: '最近通知时间', handle: (that, row) => that.showNote(row) },
];
const noteCols = [
  { width: 'auto', align: 'center',sortable: 'custom', prop: 'notifyClass', label: '通知类型' },
  { width: 'auto', align: 'center',sortable: 'custom', prop: 'nextNotifyTime', label: '通知时间' },
  { width: 'auto', align: 'center', sortable: 'custom',prop: 'receiverName', label: '接收人' },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase, YhUserselectormulti, inputYunhan
  },
  data() {
    var validateLimitDay = (rule, value, callback) => {
      if (value) {
        if (value > 999 || value < 0) {
          callback(new Error('单据日期必须在0-999天之内!'));
        }
      }
      callback();
    };
    return {
      rules: {
        limitDay: [
          { validator: validateLimitDay, trigger: 'blur' }
        ]
      },
      ruleForm: {
        limitDay: 0,
        notifyClass: "全部",
        namesResponse: [],
        notifyFrequencysResponse: [],
      },
      setup: false,
      chooseList: [],
      summaryarry: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startAllocationDate: null,//开始时间
        endAllocationDate: null,//结束时间
        Status: [],
        link_warehouse: [],
        warehouse: [],
        sku_id: null,
        inOut_id: null,
        startNotify_time: null,
        endNotify_time: null,
        notify: null,
      },
      allocationDateRanges: [],
      notifyTimeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      WarehouseList: [],
      ReciverList: [],
      name: "transferRecord",
      noteShow: false,
      noteCols,
      noteTotal: 0,
      noteData: [],
      noteList: [],
      notice:{
        loading:false,
        pageSize:50,
        currentPage:1,
        orderBy: null,
        isAsc: false,
        io_id:null,
        ioi_id:null,
      },
    }
  },
  async mounted() {
    await this.getWarehouseList();
    await this.getList();
  },
  methods: {
    async onDeleteEvent(item, type) {
      if (type == 'receiver' && this.ruleForm.namesResponse.length > 1) {
        this.ruleForm.namesResponse.splice(item, 1)
      } else if (type == 'frequency' && this.ruleForm.notifyFrequencysResponse.length > 1) {
        this.ruleForm.notifyFrequencysResponse.splice(item, 1)
      } else {
        this.$message.error("最后一个元素不可删除");
      }
    },
    async onAddedSettingsEvent() {
      if (this.ruleForm.limitDay < 0 || this.ruleForm.limitDay > 999){
        this.$message.error("请设置正确期限天数!");
        return;
      }
      let nameRepeat = false;
      let frequencyRepeat = false;
      for (let i = 0; i < this.ruleForm.namesResponse.length; i++) {
        if (null == this.ruleForm.namesResponse[i].warehouseID || "" == this.ruleForm.namesResponse[i].warehouseID 
        || null == this.ruleForm.namesResponse[i].receiverID || "" == this.ruleForm.namesResponse[i].receiverID) {
          this.$message.error("请设置接收人和对应的仓库!");
          return;
        }
        for (let j = i + 1; j < this.ruleForm.namesResponse.length; j++) {
          if (this.ruleForm.namesResponse[i].warehouseID == this.ruleForm.namesResponse[j].warehouseID) {
            nameRepeat = true; 
          }
        }
        // if (this.ruleForm.namesResponse[i].warehouseID != null&&this.ruleForm.namesResponse[i].warehouseID!="" || this.ruleForm.namesResponse[i].receiverID!=null&&this.ruleForm.namesResponse[i].receiverID.length > 0) {
        //   if (this.ruleForm.namesResponse[i].warehouseID == null || this.ruleForm.namesResponse[i].receiverID == null) {
        //     this.$message.error("请设置接收人和对应的仓库!"); 
        //   }
        // }
      }
      for (let i = 0; i < this.ruleForm.notifyFrequencysResponse.length; i++) {
        if ("" == this.ruleForm.notifyFrequencysResponse[i].time || null == this.ruleForm.notifyFrequencysResponse[i].time){
          this.$message.error("请设置通知频率!");
          return;
        }
        for (let j = i + 1; j < this.ruleForm.notifyFrequencysResponse.length; j++) {
          if (this.ruleForm.notifyFrequencysResponse[i].time == this.ruleForm.notifyFrequencysResponse[j].time) { 
            frequencyRepeat = true;
            break;
          }
        }
      }
      if (nameRepeat) {
        this.$message.error("仓库重复!");
        return;
      }
      if (frequencyRepeat) {
        this.$message.error("通知频率重复!");
        return;
      }
      this.ruleForm.limitDay=this.ruleForm.limitDay==null?0:this.ruleForm.limitDay;
      const { success } = await setInventoryAllocateSetting(this.ruleForm);
      if (success) {
        this.setup = false;
        this.$message.success("设置成功");
      }
    },
    onSettings(label) {
      if (label == 'receiver') {
        this.ruleForm.namesResponse.push({ warehouseID: null, receiverID: null })
      } else {
        this.ruleForm.notifyFrequencysResponse.push({})
      }
    },
    async onSetup() {
      this.setup = true
      const data = await getInventoryAllocateSetting();
      if (data != null) {
        this.ruleForm.limitDay = data.limitDay;
        this.ruleForm.notifyClass = data.notifyClass;
        this.ruleForm.notifyFrequencysResponse = data.notifyFrequencysResponse;
        this.ruleForm.namesResponse = data.namesResponse;
      } else {
        //获取列表失败
        this.$message.error('获取设置信息失败')
        this.setup = false;
      }
    },
    //复选框数据
    choose: function (sels) {
      this.noteList = [];
      for (let i = 0; i < sels.length; i++) {
        this.noteList.push({ Io_id: sels[i].io_id, Ioi_id: sels[i].ioi_id });
      } 
    },
    //更改单据日期
    async changeAllocationDate(e) {
      this.ListInfo.startAllocationDate = e ? e[0] : null
      this.ListInfo.endAllocationDate = e ? e[1] : null
    },
    //更改通知时间
    async changeNodifyTime(e) {
      this.ListInfo.startNotify_time = e ? e[0] : null
      this.ListInfo.endNotify_time = e ? e[1] : null
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    // async exportProps() {
    //     const { data } = await exportStatData(this.ListInfo)
    //     const aLink = document.createElement("a");
    //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
    //     aLink.href = URL.createObjectURL(blob)
    //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
    //     aLink.click()
    // },

    //通知排序
    noteSortChange({ order, prop }){
      if (prop) {
        this.notice.orderBy = prop
        this.notice.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getLogList()
      }
    },
    async getList(type) {

//商品编码            
      // 使用正则表达式保留数字和逗号，去除其他非必要字符
      let value_sku_id = this.ListInfo.sku_id.replace(/[^\w,+，-]/g, '');
      // 按逗号分割输入值
      let segments_sku_id = value_sku_id.split(/[,，]/);
      // 更新输入框的值，只保留有效的数字和逗号部分
      this.ListInfo.sku_id = segments_sku_id.join(',');

//调入调出单号
      // 使用正则表达式保留数字和逗号，去除其他非必要字符
      let value_inOut_id = this.ListInfo.inOut_id.replace(/[^\d,，]/g, '');
      // 按逗号分割输入值
      let segments_inOut_id = value_inOut_id.split(/[,，]/);
      // 更新输入框的值，只保留有效的数字和逗号部分
      this.ListInfo.inOut_id = segments_inOut_id.join(',');

      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.allocationDateRanges != null && this.allocationDateRanges.length == 0) {
        //默认给近三个月时间
        this.ListInfo.startAllocationDate = dayjs().subtract(3, 'month').format('YYYY-MM-DD')
        this.ListInfo.endAllocationDate = dayjs().format('YYYY-MM-DD')
        this.allocationDateRanges = [this.ListInfo.startAllocationDate, this.ListInfo.endAllocationDate]
      }
      this.loading = true

      try {
        // 使用时将下面的方法替换成自己的接口
        const { data, success } = await getInventoryAllocate(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
        }
        // else {
        //   //获取列表失败
        //   this.$message.error('获取列表失败');
        // }
      }catch (error){
        this.$message.error('获取列表失败'+error);
      }
      this.loading = false;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    async getLogList()
    {
      const { data, success } = await getInventoryAllotLog(this.notice)
      if (success) {
        this.noteData = data.list;
        this.noteTotal = data.total;
      } else {
        //获取列表失败
        this.$message.error('获取通知日志失败')
      }
    },
     //当前页改变
     noetPageChange(val) {
      this.notice.currentPage = val;
      this.getLogList()
    },
    //每页数量改变
    noetSizeChange(val)
    {
      this.notice.pageSize=val;
      this.notice.currentPage=1;
      this.getLogList();
    }
    ,
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },

    //获取仓库列表
    async getWarehouseList() {
      let wares = await getTbWarehouseList();
      if (wares?.success && wares?.data && wares?.data.length > 0) {
        wares?.data.forEach(f => {
          if (f.manager_ddid)
            this.WarehouseList.push({ value: f.wms_co_id, label: f.name });
        });
      }
    },
    //获取商品编码
    skucallback(val) {
      this.ListInfo.sku_id = val;
      
      // 使用正则表达式保留数字和逗号，去除其他非必要字符
      let value_sku_id = this.ListInfo.sku_id.replace(/[^\w,+，-]/g, '');
      // 按逗号分割输入值
      let segments_sku_id = value_sku_id.split(/[,，]/);
      // 更新输入框的值，只保留有效的数字和逗号部分
      this.ListInfo.sku_id = segments_sku_id.join(',');

    },
    //获取调入调出单号
    InOutcallback(val) {
      this.ListInfo.inOut_id = val;

      // 使用正则表达式保留数字和逗号，去除其他非必要字符
      let value_inOut_id = this.ListInfo.inOut_id.replace(/[^\d,，-]/g, '');
      // 按逗号分割输入值
      let segments_inOut_id = value_inOut_id.split(/[,，]/);
      // 更新输入框的值，只保留有效的数字和逗号部分
      this.ListInfo.inOut_id = segments_inOut_id.join(',');

    },
    //导出数据
    async onExport() {
      const res = await exportInventoryAllot(this.ListInfo)
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '调拨导出_' + this.name + '_' + new Date().toLocaleString() + '.xlsx');
      aLink.click()
    },
    async showNote(row) {
      this.noteShow = true
      this.notice.io_id=row.io_id,
      this.notice.ioi_id=row.ioi_id;
      this.notice.orderBy=null;
      this.notice.isAsc=false;
      this.getLogList();
    }, 
    async onNote() {
      if (!this.noteList || this.noteList.length == 0)
        this.$message({ message: "请先选择数据", type: "warning", });
      let res = await clickNotifyInventoryAllocate(this.noteList); 
      if (res?.success) {
        this.total = res.data.total;
        this.failedList = res.data.list;
        this.noteList = [];
      } 
      this.getList();
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 20px;
}
</style>
