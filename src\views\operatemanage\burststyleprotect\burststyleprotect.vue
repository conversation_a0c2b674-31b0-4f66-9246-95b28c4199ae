<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-date-picker style="width: 260px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="爆款开始日期"
                        end-placeholder="爆款结束日期" :clearable="true" :picker-options="pickerOptions"></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select v-model="filter.platform" placeholder="平台" @change="getloadshopselect" clearable
                        filterable style="width: 80px">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select v-model="filter.shopCode" style="width: 200px" size="mini" placeholder="店铺" clearable
                        filterable>
                        <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width:110px;" clearable
                        maxlength="40" />
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <!-- <el-input v-model.trim="filter.proCode" placeholder="产品ID" style="width:120px;" clearable
                        maxlength="40" /> -->

                    <inputYunhan ref="burststyleprotectproCode" v-model="filter.proCode" :inputt.sync="filter.proCode"
                        placeholder="产品ID" :clearable="true" width="180px" @callback="callbackProCode" title="产品ID">
                    </inputYunhan>

                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-input v-model.trim="filter.proName" placeholder="产品名称" style="width:110px;" clearable
                        maxlength="50" />
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.productProPerty" placeholder="属性"
                        style="width: 110px">
                        <el-option label="日销" value="日销"></el-option>
                        <el-option label="季节" value="季节"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable v-model="filter.groupId" placeholder="小组" style="width: 100px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable v-model="filter.operateSpecialUserId" collapse-tags clearable
                        placeholder="运营专员" style="width: 90px">
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable v-model="filter.userId" collapse-tags clearable placeholder="运营助理"
                        style="width: 90px">
                        <el-option v-for="item in directorlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin-left: 10px; border: none;">
                    连续
                    <el-select filterable clearable v-model="filter.continuousWeekCount" placeholder="请选择"
                        style="width: 75px" @change="handleChange">
                        <el-option v-for="week in weeks" :key="week" :label="week" :value="week"> </el-option>
                    </el-select>
                    周
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.amountType" placeholder="属性" style="width: 90px">
                        <el-option label="订单量" value="订单量"></el-option>
                        <el-option label="销售额" value="销售额"></el-option>
                        <el-option label="总广告费" value="总广告费"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin-right: 10px;border: none;">
                    <el-select filterable clearable v-model="filter.upOrDown" placeholder="类型" style="width: 90px">
                        <el-option label="上升" value="上升"></el-option>
                        <el-option label="下降" value="下降"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin:0; border: none;">
                    <el-select filterable clearable v-model="filter.continuousWeekCount2_1" placeholder="第几周"
                        style="width: 75px" @change="handleChange">
                        <el-option v-for="week in weeks" :key="week" :label="week" :value="week"> </el-option>
                    </el-select>
                    对比
                    <el-select filterable clearable v-model="filter.continuousWeekCount2_2" placeholder="第几周"
                        style="width: 75px" @change="handleChange">
                        <el-option v-for="week in weeks" :key="week" :label="week" :value="week"> </el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.amountType2" placeholder="属性" style="width: 90px">
                        <el-option label="订单量" value="订单量"></el-option>
                        <el-option label="销售额" value="销售额"></el-option>
                        <el-option label="总广告费" value="总广告费"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: none;">
                    <el-select filterable clearable v-model="filter.upOrDown2" placeholder="类型" style="width: 90px">
                        <el-option label="上升" value="上升"></el-option>
                        <el-option label="下降" value="下降"></el-option>
                    </el-select>
                </el-button>

                <div>
                    <el-button-group style="margin-right:5px;">
                        <el-button type="primary" @click="onSearch">查询</el-button>
                        <el-button type="primary" @click="onImport">导入</el-button>
                        <el-button type="primary" @click="onModel">下载模板</el-button>
                        <el-button type="primary" @click="onExport">导出</el-button>
                        <el-button type="primary" @click="onOneKeyLoss">一键流失</el-button>
                    </el-button-group>
                </div>
            </el-button-group>
        </template>

        <vxetablebase :id="'burststyleprotect20231019'" :border="true" :align="'center'"
            :tablekey="'burststyleprotect20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='false' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999" @select="callback">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <el-dialog title="导入" :visible.sync="dialogUploadData.visible" width="30%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                            action accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
                            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success"
                                :loading="dialogUploadData.uploadLoading" @click="onSubmitUpload">{{
                                    (dialogUploadData.uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
                <el-row v-if="dialogUploadData.showError" style="color:darkorange;">
                    <span v-for="(err, errIndex) in dialogUploadData.showErrorList" :key="errIndex">{{ err
                        }};<br /></span>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogUploadData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="趋势图" :visible.sync="drawerVisible" width="80%" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="quantityprocessed.visible" ref="drawerbuschar"
                        :analysisData="quantityprocessed.data">
                    </buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="drawerVisible = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import inputYunhan from "@/components/Comm/inputYunhan";

import { getDirectorGroupList, getList as getshopList, getDirectorList } from '@/api/operatemanage/base/shop'
import { GetBurstStyleProtectPageList, ImportBurstStyleProtect, OneKeyIsLoss, ExportBurstStyleProtect, getBurstStyleProtectChart } from '@/api/operatemanage/burststyleprotect'
import buschar from '@/components/Bus/buschar'

const tableCols = [
    { istrue: true, label: '', type: "checkbox" },
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '80', formatter: (row) => formatPlatform(row.platform) },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom', width: '200', formatter: (row) => row.shopName },
    { istrue: true, prop: 'productCategoryId', label: '类目', sortable: 'custom', width: '120', formatter: (row) => row.productCategoryName },
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '120', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, prop: 'productProPerty', label: '属性', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'title', label: '产品名称', sortable: 'custom', width: '300' },
    { istrue: true, prop: 'groupId', label: '小组', sortable: 'custom', width: '80', formatter: (row) => row.groupName },
    { istrue: true, prop: 'operateSpecialUserId', label: '专员', sortable: 'custom', width: '80', formatter: (row) => row.operateSpecialUserName },
    { istrue: true, prop: 'userId', label: '助理', sortable: 'custom', width: '80', formatter: (row) => row.userName },
    { istrue: true, prop: 'burstStyleTime', label: '爆款时间', sortable: 'custom', width: '150' },
    {
        istrue: true, summaryEvent: true, prop: 'hebdomad', label: `近1周`, width: '240', merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'before1WeekOrderCountArray', label: '订单量', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before1WeekSaleAmountArray', label: '销售额', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before1WeekGuangGaoAmountArray', label: '总广告费', width: '80', type: 'before', sortable: 'custom' },
        ]
    },
    {
        istrue: true, summaryEvent: true, prop: 'twoweeks', label: `近2周`, merge: true, prop: 'mergeField1', width: '240',
        cols: [
            { istrue: true, prop: 'before2WeekOrderCountArray', label: '订单量', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before2WeekSaleAmountArray', label: '销售额', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before2WeekGuangGaoAmountArray', label: '总广告费', width: '80', type: 'before', sortable: 'custom' },
        ]
    },
    {
        istrue: true, summaryEvent: true, prop: 'trizhou', label: `近3周`, merge: true, prop: 'mergeField2', width: '240',
        cols: [
            { istrue: true, prop: 'before3WeekOrderCountArray', label: '订单量', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before3WeekSaleAmountArray', label: '销售额', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before3WeekGuangGaoAmountArray', label: '总广告费', width: '80', type: 'before', sortable: 'custom' },
        ]
    },
    {
        istrue: true, summaryEvent: true, prop: 'allaround', label: `近4周`, merge: true, prop: 'mergeField3', width: '240',
        cols: [
            { istrue: true, prop: 'before4WeekOrderCountArray', label: '订单量', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before4WeekSaleAmountArray', label: '销售额', width: '80', type: 'before', sortable: 'custom' },
            { istrue: true, prop: 'before4WeekGuangGaoAmountArray', label: '总广告费', width: '80', type: 'before', sortable: 'custom' },
        ]
    },
    {
        istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: '60', align: 'center',
        formatter: (row) => '趋势图', type: 'click', handle: (that, row, column) => that.showchart(row, column)
    },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(1, 'month'), "YYYY-MM-DD");
const endDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
    name: "burststyleprotect",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, inputYunhan, buschar
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                shopCode: "",
                productProPerty: null,
                continuousWeekCount: null,
                upOrDown: null,
                amountType: null,

                continuousWeekCount2_1: null,
                continuousWeekCount2_2: null,
                upOrDown2: null,
                amountType2: null,
            },
            weeks: [1, 2, 3, 4],
            directorlist: [],
            drawerVisible: false,
            quantityprocessed: { visible: false, title: "", data: {} },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "burstStyleTime", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],

            platformlist: platformlist,
            shopList: [],
            directorGroupList: [],

            fileList: [],
            fileparm: {},

            dialogUploadData: {
                title: "",
                visible: false,
                uploadLoading: false,
                showError: false,
                showErrorList: [],
            },
        };
    },
    async mounted() {
        await this.getloadgroupselect();
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        handleChange(value) {
            if (!Number.isInteger(value) || value > 4) {
                this.filter.continuousWeekCount = null;
            }
        },
        async showchart(row) {
            let proCode = row.proCode
            const params = this.getCondition();
            params.proCode = proCode
            if (params === false) {
                return;
            }
            const { data } = await getBurstStyleProtectChart(params);
            data.series.map((item) => {
                item.itemStyle = {
                    "normal": {
                        "label": {
                            "show": true,
                            "position": "top",
                            "textStyle": {
                                "fontSize": 14
                            }
                        }
                    }
                }

                item.emphasis = {
                    "focus": "series"
                }
                item.smooth = false;
            })
            this.quantityprocessed.visible = true;
            this.quantityprocessed.data = data
            this.quantityprocessed.title = data.legend[0]
            this.$nextTick(() => {
                this.$refs.drawerbuschar.initcharts();
            });
            this.drawerVisible = true;

        },
        async getloadshopselect() {
            this.filter.shopCode = "";
            const res1 = await getshopList({ platform: this.filter.platform, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list.map(item => { return { value: item.shopCode, label: item.shopName }; });
        },
        async getloadgroupselect() {
            const res2 = await getDirectorGroupList({});
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);

            let res3 = await getDirectorList();
            this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            else {
                this.filter.startDate = '2020-01-01';
                this.filter.endDate = '2030-01-01';
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            return params;
        },
        async getList() {
            //检查周，属性，类型是否有值
            if ([this.filter.amountType, this.filter.upOrDown, this.filter.continuousWeekCount].some(Boolean) && !([this.filter.amountType, this.filter.upOrDown, this.filter.continuousWeekCount].every(Boolean))) {
                this.$message({ showClose: false, message: '周，属性，类型为必选项，请选择完整！', type: 'error' });
                return;
            }
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetBurstStyleProtectPageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        callback(val) {
            this.selrows = [...val];
        },
        async onOneKeyLoss() {
            if (this.selrows.length <= 0) {
                this.$message({ type: 'error', message: '请勾选!' });
                return;
            }
            let ids = [];
            this.selrows.forEach(f => {
                ids.push(f.id);
            });

            this.$confirm('确定要一键流失勾选的行吗?', '提示', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                this.pageLoading = true;
                let res = await OneKeyIsLoss(ids);
                this.pageLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '操作成功' });
                    await this.onSearch();
                }
            }).catch(() => {
            });
        },
        async onExport() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await ExportBurstStyleProtect(params)
            this.listLoading = false;
            if (!res?.data) {
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "爆款保护_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        async onModel() {
            window.open("/static/excel/productalllink/爆款保护导入模板.xlsx", "_blank");
        },
        async onImport() {
            this.dialogUploadData.showError = false;
            this.dialogUploadData.showErrorList = [];
            this.dialogUploadData.visible = true;
        },
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.dialogUploadData.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            let res = await ImportBurstStyleProtect(form);
            if (res?.success) {
                this.$message({ message: "导入成功", type: "success" });
                this.dialogUploadData.showError = false;
                this.dialogUploadData.visible = false;
            }
            else {
                if (res?.data) {
                    this.dialogUploadData.showErrorList = res?.data;
                    this.dialogUploadData.showError = true;
                }
            }
            this.dialogUploadData.uploadLoading = false;
        },
        async onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.fileList = [];
            if (this.dialogUploadData.showError == false) {
                this.dialogUploadData.visible = false;
                await this.onSearch();
            }
        },
        onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        onSubmitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        async callbackProCode(val) {
            this.filter.proCode = val;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
