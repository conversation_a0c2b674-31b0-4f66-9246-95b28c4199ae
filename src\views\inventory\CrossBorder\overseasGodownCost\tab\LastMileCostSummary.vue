<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 5px;" :clearable="false" :value-format="'yyyy-MM-dd'"
                    @change="changeTime">
                </el-date-picker>

                <el-select v-model="queryInfo.thirdPlatform" placeholder="仓库名称" style="width:120px; margin-left: 5px;"
                    class="el-select-content" clearable filterable>
                    <el-option v-for="item in DepotNameList" :key="item.sort" :label="item.codeName"
                        :value="item.code" />
                </el-select>

                <el-button-group style="margin-left: 10px;">
                    <inputYunhan :key="'1'" :keys="'one'" :width="'150px'" ref="" :inputt.sync="queryInfo.reference_no"
                        v-model.trim="queryInfo.reference_no" placeholder="订单号或仓库单号/多条输入请按回车" :clearable="true"
                        @callback="callback" title="订单号或仓库单号" @entersearch="entersearch">
                    </inputYunhan>
                </el-button-group>

                <el-select v-model="queryInfo.ft_name" clearable filterable multiple collapse-tags placeholder="请选择费用类型" class="publicCss">
                <el-option v-for="item in costType" :key="item.key" :label="item.value" :value="item.key">
                </el-option>
                </el-select>

                <el-select v-model="queryInfo.finance_name" placeholder="财务类型" sclass="publicCss" clearable filterable multiple collapse-tags >
                    <el-option v-for="item in FeeNameList" :key="item.key" :label="item.value" :value="item.value" />
                </el-select>

                <el-button type="primary" style="margin-left: 10px;" @click="getList('search')">搜索</el-button>
                <el-button type="primary" style="margin-left: 10px;" v-if="checkPermission('overseasGodown_Cost_export')" @click="exportList">导出</el-button>
            </div>
        </template>

        <template>
            <vxetablebase :id="'overseasGodownCost20240906'" :tablekey="'overseasGodownCost20240906'"
                :tableData='tableData' :tableCols='tableCols' @sortchange='sortchange' :loading='loading' :border='true'
                :that="that" ref="vxetable" :summaryarry="summaryarry" :showsummary='true'>
            </vxetablebase>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getList" />
        </template>

    </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { getCostBillCombinePageList, exportCombineList, GetFinanceFeeTypes ,getFeeNames} from '@/api/kj/cost.js';
import { getThirdPlatform } from '@/api/kj/system'
import inputYunhan from "@/components/Comm/inputYunhan";
import { pickerOptions } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs';

const tableCols = [
    { istrue: true, sortable: 'custom', prop: 'warehouse_code', label: '仓库名称' },
    { istrue: true, sortable: 'custom', prop: 'warehouse_order', label: '仓库单号' },
    { istrue: true, sortable: 'custom', prop: 'reference_no', label: '订单号' },
    { istrue: true, sortable: 'custom', prop: 'skus', label: 'SKU' },
    { istrue: true, sortable: 'custom', prop: 'ft_name', label: '费用类型' },
    { istrue: true, sortable: 'custom', prop: 'finance_name', label: '财务类型' },
    // { istrue: true, sortable: 'custom', prop: 'bi_amount', label: '金额(美元)' },
    { istrue: true, sortable: 'custom', prop: 'bi_amount_rmb', label: '金额(人民币)' },
    { istrue: true, sortable: 'custom', prop: 'bi_chargeable_time', label: '记账时间' },
    { istrue: true, sortable: 'custom', prop: 'update_time', label: '更新时间' },

];

export default {
    name: 'overseasGodown',
    components: { vxetablebase, MyContainer, inputYunhan, buschar },
    props: {
        type: {
            type: String,
            required: true
        },
        name: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            that: this,
            queryInfo: {
                reference_no: null,
                finance_name: null,
                reference_nos: [],
                thirdPlatform: null,
                page: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,
                endDate: null,
                ft_name:null
            },
            costType: [],
            tableData: [],
            tableCols: tableCols,
            loading: false,
            total: 0,
            pickerOptions,
            DepotNameList: [],
            FeeNameList: [],
            timeRanges: [],
            summaryarry: [],
            //费用类型列表
            CostList:[],
        };
    },
    async mounted() {
        await this.getCostTypeList()
        await this.getDropdownList()
        await this.getList()
    },
    methods: {
        //仓库联动 费用类型
        async getCostTypeList() {
            const { data } = await getFeeNames();
            this.costType = data;
            },
        async changeTime(e) {
            this.queryInfo.startDate = e ? e[0] : null
            this.queryInfo.endDate = e ? e[1] : null
        },
        async getDropdownList() {
            const thirdPlatform = await getThirdPlatform();
            this.DepotNameList = thirdPlatform.data;

            const financeFeeName = await GetFinanceFeeTypes();
            this.FeeNameList = financeFeeName.data;

        },
        async getList(type) {
            if(type == 'search'){
                this.$refs.pager.setPage(1)
            }
            let page = this.$refs.pager.getPager()
            this.queryInfo.page = page.currentPage
            this.queryInfo.pageSize = page.pageSize
            this.loading = true
            if (this.timeRanges != null && this.timeRanges.length == 0) {
                //默认给近7天时间
                this.queryInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.queryInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeRanges = [this.queryInfo.startDate, this.queryInfo.endDate]
            }
            if (this.queryInfo.reference_no) {
                this.queryInfo.reference_nos = this.queryInfo.reference_no.split(',')
            } else {
                this.queryInfo.reference_nos = []
            }
            const { data, total, summary } = await getCostBillCombinePageList(this.queryInfo)
            this.loading = false
            this.total = total
            this.tableData = data;
            this.summaryarry = summary

        },
        async exportList() {
            this.loading = true
            let page = this.$refs.pager.getPager()
            this.queryInfo.page = page.currentPage
            this.queryInfo.pageSize = page.pageSize
            this.loading = true
            if (this.timeRanges != null && this.timeRanges.length == 0) {
                //默认给近7天时间
                this.queryInfo.startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
                this.queryInfo.endDate = dayjs().format('YYYY-MM-DD')
                this.timeRanges = [this.queryInfo.startDate, this.queryInfo.endDate]
            }

            if (this.queryInfo.reference_no) {
                this.queryInfo.reference_nos = this.queryInfo.reference_no.split(',')
            } else {
                this.queryInfo.reference_nos = []
            }
            const { data } = await exportCombineList(this.queryInfo)
            this.loading = false
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', this.name + '-' + dayjs().format('YYYYMMDD') + '.xlsx')
            aLink.click()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.queryInfo.orderBy = prop
                this.queryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        async callback(val) {
            this.queryInfo.reference_no = val;
        },
        async entersearch(val) {
            this.getList();
        },
    }
};
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>