<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' :summaryarry="summaryarry"
            @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div>
                        <el-table :data="props.row.detaildata" style="width: 100%">
                            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label"
                                :key="col">
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 2;margin: 0;">
                        <el-radio-group v-model="Filter.PddGroupTypeSq" @change="onSearch">
                            <el-radio :label="0">全部</el-radio>
                            <el-radio :label="1">外包售中组</el-radio>
                            <el-radio :label="2">新余售中组</el-radio>
                            <el-radio :label="3">售后组</el-radio>
                        </el-radio-group>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select multiple v-model="Filter.GroupNames" placeholder="组名称" clearable
                            :collapse-tags="true" filterable>
                            <el-option v-for="item in groupList" :key="item.groupname" :label="item.groupname"
                                :value="item.groupname" />
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.Sname" v-model.trim="Filter.Sname" placeholder="姓名" clearable
                            style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <datepicker v-model="Filter.Sdate"></datepicker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission(['pddinquirs.pddinquirsstatisticssh.jr'])">
                        <el-input type="number" placeholder="起始介入客单价" v-model="Filter.jrBeginPrice" style="width:130px;"
                            oninput="if(value>10000){value=10000} else if(value<0){value=0}" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;"
                        v-if="checkPermission(['pddinquirs.pddinquirsstatisticssh.jr'])">
                        <el-input type="number" placeholder="结束介入客单价" v-model="Filter.jrEndPrice" style="width:130px;"
                            oninput="if(value>10000){value=10000} else if(value<0){value=0}" />
                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
                @get-page="getinquirsstatisticsList" />
        </template>

        <el-dialog title="个人效率按店统计" :visible.sync="dialogVisibleSyj" width="85%" :close-on-click-modal="false"
            v-dialogDrag>
            <span>
                <pddinquirsstatisticsbyshop v-if="dialogVisibleSyj" ref="pddinquirsstatisticsbyshopsh"
                    style="height: 600px;" />

            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
            <div>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data"></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>

import pddinquirsstatisticsbyshop from '@/views/customerservice/pdd/sh/pddinquirsstatisticsbyshopsh'
import { pagePddUserKfEfficiencyList, pagePddUserKfEfficiencyListMap, getPddGroup, exportPddUserKfEfficiencyList } from '@/api/customerservice/pddInquirs'

import datepicker from '@/views/customerservice/datepicker'
import buschar from '@/components/Bus/buschar'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import Decimal from 'decimal.js';
function precision(number, multiple) {
    return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
    { istrue: true, prop: 'groupName', label: '组名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'sname', label: '姓名', width: '80', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell), formatter: (row) => row.sname },

    { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100).toFixed(2) : 0) + "%" } },

    { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100).toFixed(2) : 0) + "%" } },
    { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100).toFixed(2) : 0) + "%" } },

    { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },

    { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },

    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 80, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, pddinquirsstatisticsbyshop, datepicker, buschar },
    props: ["partInfo"],
    data() {
        return {
            dialogMapVisible: { visible: false, title: "", data: [] },
            that: this,
            Filter: {
                Sdate: [formatTime(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30), "YYYY-MM-DD 00:00:00"), formatTime(new Date(), "YYYY-MM-DD 23:59:59")],
                PddGroupTypeSq: 0,
            },

            shopList: [],
            userList: [],
            groupList: [],
            inquirsstatisticslist: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "groupName", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            //
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            isleavegroup: this.partInfo,//是否离组
        };
    },
    watch: {
        partInfo() {
            this.isleavegroup = this.partInfo;
            this.setGroupSelect();
        }
    },
    async mounted() {
        this.isleavegroup = this.partInfo;
        window.showpddpddlist = this.showlist
        await this.setGroupSelect();
    },
    methods: {
        async setGroupSelect() {
            const form = new FormData();
            form.append("enmPddGroupType", 1);
            form.append("isleavegroup", this.isleavegroup);
            const res = await getPddGroup(form);
            this.groupList = res.data;
        },
        async showchart(row) {

            if (this.Filter.timerange) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1]
            }
            var params = {
                Groupname: row.groupname, Sname: row.sname, StartSdate: this.Filter.startSdate, EndSdate: this.Filter.endSdate, EnmPddGroupType: 1
                , PddGroupTypeSq: this.Filter.PddGroupTypeSq
            }
            let that = this;

            const res = await pagePddUserKfEfficiencyListMap(params).then(res => {
                that.dialogMapVisible.visible = true;
                that.dialogMapVisible.data = res
                that.dialogMapVisible.title = res.title;
                res.title = "";
            })
            this.dialogMapVisible.visible = true
        },
        showlist(groupname, startdate, enddate) {
            this.Filter.GroupNames = [groupname];
            this.Filter.Sdate = [startdate, enddate];
            this.Filter.startSdate = startdate;
            this.Filter.endSdate = enddate;
            this.onSearch()
        },
        async canclick(row, column, cell) {

            var fstartsdate = "";
            var fendsdate = "";
            if (this.Filter.Sdate) {
                var d = new Date(this.Filter.Sdate[0])
                fstartsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
                d = new Date(this.Filter.Sdate[1])
                fendsdate = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
            }
            if (fstartsdate == "NaN-NaN-NaN") {
                fstartsdate = "";
                fendsdate = "";
            }
            var fsname = row.sname;
            this.dialogVisibleSyj = true;
            debugger;
            this.$nextTick(() => {
                this.$refs.pddinquirsstatisticsbyshopsh.dialogOpenAfter({
                    startSdate: fstartsdate,
                    endSdate: fendsdate,
                    sname: fsname
                });
            });
        },
        async deleteBatch(row) {
            var that = this;
            this.$confirm("此操作将删除此批次个人效率统计数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(async () => {
                    await deleteInquirsStatisticsBatch({ batchNumber: row.batchNumber })
                    that.$message({ message: '已删除', type: "success" });
                    that.onRefresh()
                });
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onImportSyj() {
            this.dialogVisibleSyj = true
        },
        async uploadFile2(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const res = importInquirsStatisticsAsync(form);
            this.$message({ message: '上传成功,正在导入中...', type: "success" });
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            this.$refs.upload2.submit()
        },
        onRefresh() {
            this.setGroupSelect();
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getinquirsstatisticsList();
        },
        getParam() {
            if (this.Filter.UseDate) {
                this.Filter.startAccountDate = this.Filter.UseDate[0];
                this.Filter.endAccountDate = this.Filter.UseDate[1];
            }
            if (this.Filter.Sdate) {
                this.Filter.startSdate = this.Filter.Sdate[0];
                this.Filter.endSdate = this.Filter.Sdate[1];
            }
            else {
                this.Filter.startSdate = null;
                this.Filter.endSdate = null;
            }
            this.Filter.EnmPddGroupType = 1;
            const para = { ...this.Filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };
            return params;
        },
        async getinquirsstatisticsList() {
            let params = this.getParam();
            console.log(params)
            this.listLoading = true;
            const res = await pagePddUserKfEfficiencyList(params);
            console.log(res)
            this.listLoading = false;
            console.log(res.list)

            this.total = res.total
            this.inquirsstatisticslist = res.list;
            this.summaryarry = res.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getParam();
            this.listLoading = true
            const res = await exportPddUserKfEfficiencyList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多个人效率统计(售后组)_' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
    max-width: 60px;
}
</style>
