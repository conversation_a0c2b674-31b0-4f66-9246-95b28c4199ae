<template>
    <container>
        <el-tabs v-model="activeName" @tab-click="handleClick" style="height: calc(100% - 40px)">
            <el-tab-pane label="数据" name="data" style="height: 100%;">
                <pagelist></pagelist>
            </el-tab-pane>
            <el-tab-pane label="日志" name="log" style="height: 100%;">
                <loglist></loglist>
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>

import container from "@/components/my-container";
import pagelist from './pagelist.vue';
import loglist from './loglist.vue';

export default {
    name: 'PurchaseAutoOrderDataIndex',
    components: {container,pagelist,loglist},
    data(){
        return {
            activeName: 'data'
        }
    },
    async mounted() {
        
    },
    methods: {
        handleClick(tab, event) {
            
        }
    }
}

</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
  max-width: 30px;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>