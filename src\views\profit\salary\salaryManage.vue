<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%; width:100%;" @tab-click="tabclick">
            <el-tab-pane label="员工薪资结构" name="first" style="height: 100%;" >
                <payStructure ref="payStructure" />
            </el-tab-pane>
            <el-tab-pane label="薪资模板" name="second" style="height: 100%;">
                <salaryTemplate ref="salaryTemplate" />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>
  
  <script>
    import MyContainer from "@/components/my-container";
    //import {  } from '@/api/express/express'    
    import payStructure from '@/views/profit/salary/payStructure'
    import salaryTemplate from '@/views/profit/salary/salaryTemplate'
    export default {
        name: 'DingDingShow',
        components: { payStructure, salaryTemplate,MyContainer },
        data () {
            return {
                activeName: 'first'
            }
        },
        async mounted () {
            // await this.onSearch()
        },
        methods: {
            async onSearch () {
                this.$nextTick(async () => {
                    if (this.activeName == 'first')
                        await this.$refs.payStructure.deptOnSearch()
                   
                })
            },
            async tabclick () {
            //     this.$nextTick(async () => {
            //         if (this.activeName == 'first'){
            //             this.$refs.payStructure.deptOnSearch()

            //         }
            //     })
            }
        }
    }
  </script>
  