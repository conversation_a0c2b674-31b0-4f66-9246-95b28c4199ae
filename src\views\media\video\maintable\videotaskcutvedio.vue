<template>
    <my-container v-loading="pageLoading">
        <div>
            <el-form label-width="100px" class="demo-ruleForm">
                <div class="jqsp">
                    <div class="jqspbt">剪切视频 
                        <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;任务名称：</span>{{ videoTaskId }}<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span><span>{{ productShortName }}</span>
                    </div>
                    <div style="box-sizing: border-box;padding: 0px 10px;">
                        <div class="jqxzsp">
                            <div>
                                <el-row class="tac">
                                    <el-col>
                                        <el-menu default-active="1-1" class="el-menu-vertical-demo"
                                            :default-openeds="openeds">
                                            <el-submenu index="1-1" style="margin-top: 20px">
                                                <template slot="title">
                                                    <i class="el-icon-video-camera"></i>
                                                    <span>视频一</span>
                                                </template>
                                                <el-menu-item-group>
                                                    <el-menu-item style="margin: -5px 0" v-for="(item, order) in pdArrayCk1"
                                                        :key="item.pdId" :index="item.pdId + ''"
                                                        @click="iteminitvideo(item, order)"><i
                                                            class="el-icon-scissors"></i>片段{{ order + 1 }}</el-menu-item>
                                                </el-menu-item-group>
                                            </el-submenu>
                                            <el-submenu index="2-1">
                                                <template slot="title">
                                                    <i class="el-icon-video-camera"></i>
                                                    <span>视频二</span>
                                                </template>
                                                <el-menu-item-group>
                                                    <el-menu-item style="margin: -5px 0"
                                                        v-for="(item, order)  in pdArrayCk2" :key="item.pdId"
                                                        :index="item.pdId + ''" @click="iteminitvideo(item, order)"><i
                                                            class="el-icon-scissors"></i>片段{{ order + 1 }}</el-menu-item>
                                                </el-menu-item-group>
                                            </el-submenu>
                                            <el-submenu index="3-1">
                                                <template slot="title">
                                                    <i class="el-icon-video-camera"></i>
                                                    <span>视频三</span>
                                                </template>
                                                <el-menu-item-group>
                                                    <el-menu-item style="margin: -5px 0"
                                                        v-for="(item, order)  in pdArrayCk3" :key="item.pdId"
                                                        :index="item.pdId + ''" @click="iteminitvideo(item, order)"><i
                                                            class="el-icon-scissors"></i>片段{{ order + 1 }}</el-menu-item>
                                                </el-menu-item-group>
                                            </el-submenu>
                                            <el-submenu index="4-1">
                                                <template slot="title">
                                                    <i class="el-icon-video-camera"></i>
                                                    <span>视频四</span>
                                                </template>
                                                <el-menu-item-group>
                                                    <el-menu-item style="margin: -5px 0"
                                                        v-for="(item, order)  in pdArrayCk4" :key="item.pdId"
                                                        :index="item.pdId + ''" @click="iteminitvideo(item, order)"><i
                                                            class="el-icon-scissors"></i>片段{{ order + 1 }}</el-menu-item>
                                                </el-menu-item-group>
                                            </el-submenu>
                                            <el-submenu index="5-1" style="margin-bottom: 300px">
                                                <template slot="title">
                                                    <i class="el-icon-video-camera"></i>
                                                    <span>视频五</span>
                                                </template>
                                                <el-menu-item-group>
                                                    <el-menu-item style="margin: -5px 0"
                                                        v-for="(item, order)  in pdArrayCk5" :key="item.pdId"
                                                        :index="item.pdId + ''" @click="iteminitvideo(item, order)"><i
                                                            class="el-icon-scissors"></i>片段{{ order + 1 }}</el-menu-item>
                                                </el-menu-item-group>
                                            </el-submenu>
                                        </el-menu>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <!-- --------------------------视频播放区域----------------------- -->
                        <div class="jqwzpd" >
                            <div class="jqwzpdnr">
                                <video ref="originVideo"  style="width: 100%; height: 100%; object-fit: contain;" oncontextmenu="return false;"> </video>
                            </div>
                            <div class="jqwzpdbz">{{ curmark }}</div>
                            <div  v-show="isPalyShow" class="jqwzpdan" style="box-sizing: border-box; padding: 5px 0">
                                <el-button style="width: 100%" type="primary" @click="playVio"><i
                                        class="el-icon-video-play"></i>&nbsp;&nbsp;播放</el-button>
                            </div>
                            <div  v-show="isPalyShow" class="jqwzpdan" style="box-sizing: border-box; padding: 5px 0">
                                <el-button style="width: 100%" type="primary" @click="clipVideo"><i
                                        class="el-icon-scissors"></i>&nbsp;&nbsp;裁剪</el-button>
                            </div>
                        </div>
                        <!-- --------------------------剪切后的数组------------------------ -->
                        <div class="jqyjdl">
                            <div style="overflow: auto">
                                <el-table max-height="510px" :data="clipArray" style="width: 100%"
                                    :row-class-name="rowClassName">
                                    <el-table-column prop="date" label="片段" width="80">
                                        <div>{{ ckvideorow.pdindex }}</div>
                                    </el-table-column>
                                    <el-table-column prop="title" label="段落" width="80">
                                        <template slot-scope="scope">
                                            <el-input v-model="scope.row.title" :disabled="true"> </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="开始时间" width="130">
                                        <template slot-scope="scope">
                                            <el-input style="width: 110px" size="mini" placeholder="00:00:00:000"
                                            :disabled="true"  v-model="scope.row.beginTime"> </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="结束时间" width="130">
                                        <template slot-scope="scope">
                                            <el-input :disabled="true" style="width: 110px" size="mini" placeholder="00:00:00:000"
                                                v-model="scope.row.endTime"> </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column show-overflow-tooltip prop="name" label="备注" width="706px">
                                        <template slot-scope="scope">
                                            <el-input size="mini" v-model="scope.row.remark" placeholder="备注"> </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="操作" width="150">
                                        <template slot-scope="scope">
                                            <el-button size="mini" type="primary"
                                                @click="palyClipV(scope.row)">播放</el-button>
                                            <el-button size="mini" type="danger"
                                                @click="onDelCutVideo(scope.row, scope.$index)"><i
                                                    class="el-icon-delete"></i></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                    <!-------------- 加减按钮区域-------------------------------------------------------- -->
                    <div class="jqsp" v-show="isPalyShow">
                        <div class="jqsjx">
                            <div class="block">
                                <div style="display: flex; flex-direction: row;">
                                <div style="background-color: #fff; display: flex; flex-direction: row;">
                                    <span style="width: 28px; height: 28px;background-color: #F2F6FC;cursor: pointer;" class="flexcenter"
                                        @click="deltimeone">-</span>
                                    <span style="line-height: 28px; width: 70px; font-size: 12px; color: #909399;"
                                        class="flexcenter">{{ transformSecondToVideoFormat(videorage[0]) }}</span>
                                    <span style="width: 28px; height: 28px; background-color: #F2F6FC;cursor: pointer;" class="flexcenter"
                                        @click="addtimeone">+</span>
                                </div>
                                <div style="background-color: #fff; display: flex; flex-direction: row; margin-left: auto; justify-content: end;">
                                    <span style="width: 28px; height: 28px;background-color: #F2F6FC;cursor: pointer;" class="flexcenter"
                                        @click="deltimetwo">-</span>
                                    <span style="line-height: 28px; width: 70px; font-size: 12px; color: #909399;"
                                        class="flexcenter">{{ transformSecondToVideoFormat(videorage[1]) }}</span>
                                    <span style="width: 28px; height: 28px; background-color: #F2F6FC;cursor: pointer;" class="flexcenter"
                                        @click="addtimetwo">+</span>
                                </div>
                                </div>
                                <div style="box-sizing: border-box; padding: 0 35px">
                                    <!-- 进度条------------------------------------------------------------ -->
                                    <el-slider height="10" range :max="maxVideoV" v-model="videorage" :step="1"
                                        :format-tooltip="formatTooltip" @input="setNowVideoV"> </el-slider>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    <!-- 提交or取消区域-------------------------------------------------------- -->
                    <div class="qxtj">
                        <span style="float: left"></span>
                        <span style="float: right">
                            <el-button size="mini" @click="closeInfo(0)">取消</el-button>
                            <el-button size="mini" type="primary" @click="onCutVideoSubmit">提交</el-button>
                        </span>
                    </div>
                </div>
            </el-form>
        </div>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import { getVideoTask2PdListInfo,checkVideoTaskCanChange } from "@/api/media/vediotask";
import { getCuteVideoAsyncByTaskIdList, cuteVideo } from "@/api/media/video";
export default {
    props: {
        videoTaskId: { type: Number, default: 0 },
        closeInfo: { type: Function, default: null },
    },
    components: { MyContainer },
    data() {
        return {
            openeds: ['1-1', '2-1', '3-1', '4-1', '5-1'],
            pdArrayCk1: [],
            pdArrayCk2: [],
            pdArrayCk3: [],
            pdArrayCk4: [],
            pdArrayCk5: [],
            taskPickList: [],
            curmark: null,
            cururl: null,
            showtitle: true,
            //进度条最大长度
            isPlay: false,
            isPalyShow: false,
            isPlaying: false,
            maxVideoV: 0,
            //声音
            rangeData: 10,
            //原已剪辑的片段
            clipArray: [],
            ckvideorow: {},
            curindex: 0,
            //滑动快初始化
            videorage: [0, 0],

            //当前滑动中间
            rageFirst: 0,
            rageCur: 0,
            rageEnd: 0,
            //当前滑动中间
            pageLoading: false,
            productShortName:null,
        };
    },
    async mounted() {
        await this.initCkData();
        // 监听视频播放
        this.$refs.originVideo.addEventListener("error", () => {
            this.$message({ message: '视频源不可用，请检查参考视频链接', type: "error" });
        });
        // 监听视频播放
        this.$refs.originVideo.addEventListener("play", () => {
            this.isPlaying = true;
        });
        // 监听视频暂停
        this.$refs.originVideo.addEventListener("pause", () => {
            this.isPlaying = false;
            this.isPlay = false;
        });
        //监听视频可以播放
        this.$refs.originVideo.addEventListener("canplay", () => {
            this.isPalyShow = true;
            //duration秒 *1000 则到了毫秒。
            this.maxVideoV = this.$refs.originVideo.duration * 100;

        });
        //监听视频播放时间改变
        this.$refs.originVideo.addEventListener("timeupdate", () => {
            if (this.$refs.originVideo.currentTime >= (this.videorage[1] / 100).toFixed(2)) {
                this.$refs.originVideo.pause();
            }
        });
        //视频停止
        this.$refs.originVideo.addEventListener("ended", () => {
            this.isPlay = false;
        });
        this.keyDown();
    },
    methods: {
         //按钮监听键盘
         keyDown() {
                var that = this;
                //监听键盘按钮
                document.onkeydown = function (event) {
                    var e = event || window.event;
                    var keyCode = e.keyCode || e.which;
                    //向前
                    if (keyCode == 37) { 
                        if (that.videorage[0] == that.rageCur) { 
                            that.videorage = [that.videorage[0]- 1, that.videorage[1] ];
                            that.$refs.originVideo.currentTime = that.videorage[0] / 100;
                            that.rageCur = that.videorage[0];
                            that.rageFirst = that.videorage[0];
                        } else {
                            that.videorage = [that.videorage[0] , that.videorage[1]- 1];
                            that.$refs.originVideo.currentTime = that.videorage[1] / 100
                            that.rageCur = that.videorage[1];
                            that.rageEnd = that.videorage[1];
                        }
                     //向后
                    } else if (keyCode == 39) {
                        if (that.videorage[0] == that.rageCur) {
                            that.videorage = [that.videorage[0] + 1, that.videorage[1]];
                            that.$refs.originVideo.currentTime = that.videorage[0] / 100;
                            that.rageCur = that.videorage[0];
                            that.rageFirst = that.videorage[0];
                        } else {
                            that.videorage = [that.videorage[0] , that.videorage[1]+ 1];
                            that.$refs.originVideo.currentTime = that.videorage[1] / 100
                            that.rageCur = that.videorage[1];
                            that.rageEnd = that.videorage[1];
                        }
                    } 
                }
            },
        //初始化左边菜单
        async initCkData() {
            if (this.videoTaskId > 0) {
                var res = await getVideoTask2PdListInfo({ videoTaskId: this.videoTaskId });
                if (!res?.success) return;
                this.pdArrayCk1 = res.data.pdArrayCk1;
                this.pdArrayCk2 = res.data.pdArrayCk2;
                this.pdArrayCk3 = res.data.pdArrayCk3;
                this.pdArrayCk4 = res.data.pdArrayCk4;
                this.pdArrayCk5 = res.data.pdArrayCk5;
                this.taskPickList = res.data.task.taskPickList;
                this.productShortName = res.data.task.productShortName; 

                var res = await getCuteVideoAsyncByTaskIdList({ videoTaskId: this.videoTaskId });
                if (res?.success) {
                    this.clipArray = res.data.data;
                }
            } else {


            }
        },
        ininplayinfo(url) {
            this.isPalyShow = false;
            this.videopath = url;
            this.$refs.originVideo.src = url;
            //音量大小
            this.$refs.originVideo.volume = this.rangeData / 100;
            //是否禁音
            this.$refs.originVideo.muted = false;
            this.videorage = [0, 0];
            this.isPalyShow = true;
            //默认停止播放
            this.isPlay = false;
            this.$refs.originVideo.pause();
        },
        //菜单切换
        iteminitvideo(row, order) {
            this.ckvideorow = row;
            this.ckvideorow.pdindex = "片段" + (order + 1);
            this.ininplayinfo(row.url);
            //this.playVio();
        },
        //视频播放
        playVio() {
            this.$refs.originVideo.currentTime = (this.videorage[0] / 100).toFixed(2);
            this.$refs.originVideo.play();
        },
        //剪切，片段-----------------------------------------------------------

        clipVideo() { 
            if(this.videorage[0] == this.videorage[1]) {
                this.$message({ type: 'warning', message: "请至少剪切0.01秒" });
                return;
            }
            if (this.isPlaying) {
                this.isPlay = true;
                this.playVio();
            }
            var hasold = true;
            this.clipArray.forEach(element => {
                if (element.videoPdId == this.ckvideorow.pdId) {
                    this.curindex = element.ckCounts + 1;
                    hasold = false;
                }
            });
            //未找到
            if (hasold) {
                this.curindex = 1;
            }
            this.clipArray.push({
                "cuteId": 0,
                "ckCounts": this.curindex,
                "title": "第" + this.curindex + "段",
                "remark": this.ckvideorow.remark,
                "videoIndex": this.ckvideorow.ckVideoIndex,
                "videoPdId": this.ckvideorow.pdId,
                "beginTime": this.formatTooltip(this.videorage[0]),
                "endTime": this.formatTooltip(this.videorage[1]),
                "beginTime1": this.videorage[0],
                "endTime1": this.videorage[1]
            })
        },
        onDelCutVideo(row, index) {
      
            if (row.cuteId == 0) {
                this.clipArray.forEach(element => {
                    if (element.videoPdId == row.videoPdId) {
                        if (element.cuteId == 0 && row.ckCounts < element.ckCounts) {
                            element.ckCounts = element.ckCounts - 1;
                            element.title = "第" + element.ckCounts + "段";
                        }
                    }
                });
                this.clipArray.splice(index, 1);
            }
            else {
                this.clipArray.splice(index, 1);
            }
        },
        async palyClipV(row) {
            var selrow = {};
            var order = -1;
            switch (row.videoIndex) {
                case 1:
                    this.pdArrayCk1.forEach((element, index) => {
                        if (element.pdId == row.videoPdId) {
                            selrow = element;
                            order = index;
                            return;
                        }
                    });
                    break;
                case 2:
                    this.pdArrayCk2.forEach((element, index) => {
                        if (element.pdId == row.videoPdId) {
                            selrow = element;
                            order = index;
                            return;
                        }
                    });
                    break;
                case 3:
                    this.pdArrayCk3.forEach((element, index) => {
                        if (element.pdId == row.videoPdId) {
                            selrow = element;
                            order = index;
                            return;
                        }
                    });
                    break;
                case 4:
                    this.pdArrayCk4.forEach((element, index) => {
                        if (element.pdId == row.videoPdId) {
                            selrow = element;
                            order = index;
                            return;
                        }
                    });
                    break;
                case 5:
                    this.pdArrayCk5.forEach((element, index) => {
                        if (element.pdId == row.videoPdId) {
                            selrow = element;
                            order = index;
                            return;
                        }
                    });
                    break;
            }
            if (order > -1) {
                await this.iteminitvideo(selrow, order);
                //初始化滑块，初始化播放开始时间
                this.videorage = [this.transformTimeToSecondFormat(row.beginTime), this.transformTimeToSecondFormat(row.endTime)];
                this.playVio();
            }
        },
        rowClassName: function ({ row }) {
            if (row.videoPdId != this.ckvideorow.pdId) {
                return "vedioplayshowRow";
            }
        },
        async onCutVideoSubmit() {
            var ret = await  checkVideoTaskCanChange({taskid:this.videoTaskId});
            if(!ret?.success) return;

            var that = this;
            if (this.clipArray.length == 0) {
                that.$message({ type: 'warning', message: "请至少剪切一段" });
                return;
            }
            var para = [];
            this.clipArray.forEach((item, index) => {
                var video = {};
                video.ID = that.videoTaskId;
                video.videoPdId = item.videoPdId;
                video.remark = item.remark;
                video.firstSceneId = item.firstSceneId;
                video.firstSceneIds = item.firstSceneIds;
                video.beginTime = item.beginTime;
                video.endTime = item.endTime;
                video.title = item.title;
                video.videoIndex = item.videoIndex;
                video.cuteId = item.cuteId;
                para.push(video);
            });
            this.pageLoading = true;
            const res = await cuteVideo(para);
            this.pageLoading = false;
            if (!res?.success) {
                that.$message({ type: 'warning', message: res?.msg });
            
            } else {
                that.$message({ type: 'success', message: '提交成功,排队剪切中，请到剪切进度中查看...' });
                that.closeInfo(1);
            }
            //关闭窗口，刷新页面
        },
        //------------------------------------------------------------------------------------------------
        addtimeone() {
            if (this.videorage[0] <= this.videorage[1]) {
                this.videorage = [this.videorage[0] + 1, this.videorage[1]];
            } else {
                this.videorage = [this.videorage[0], this.videorage[1] + 1];
            }
        },
        deltimeone() {
            if (this.videorage[0] <=this.videorage[1]) {
                this.videorage = [this.videorage[0] - 1, this.videorage[1]];
            } else {
                this.videorage = [this.videorage[0], this.videorage[1] - 1];
            }
        },
        addtimetwo() {
            if (this.videorage[0] <= this.videorage[1]) {
                this.videorage = [this.videorage[0], this.videorage[1] + 1];
            } else {
                this.videorage = [this.videorage[0] + 1, this.videorage[1]];
            }
        },
        deltimetwo() {
            if (this.videorage[0] > this.videorage[1]) {
                this.videorage = [this.videorage[0] - 1, this.videorage[1]];
            } else {
                this.videorage = [this.videorage[0], this.videorage[1] - 1];
            }
        },
        //拖动进度条
        setNowVideoV() {
            //如果移动的是前面点
            this.$refs.originVideo.pause();
            if (this.rageFirst != this.videorage[0]) {
                this.rageFirst = this.videorage[0];
                this.rageCur = this.videorage[0];
                this.$refs.originVideo.currentTime = (this.videorage[0] / 100).toFixed(2);

            }

            if (this.rageEnd != this.videorage[1]) {
                this.rageEnd = this.videorage[1];
                this.rageCur = this.videorage[1];
                this.$refs.originVideo.currentTime = (this.videorage[1] / 100).toFixed(2);
            }
        },
        formatTooltip(val) {
            return this.transformSecondToVideoFormat(val);
        },
        //精确到0.01秒
        transformSecondToVideoFormat(value = 0) {
            const totalMilliSecond = Number(value * 10)
            let hours = Math.floor((totalMilliSecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
            let minutes = Math.floor((totalMilliSecond % (1000 * 60 * 60)) / (1000 * 60))
            let second = Math.floor((totalMilliSecond % (1000 * 60)) / 1000)

            let hoursText = ''
            let minutesText = ''
            let secondText = ''
            let millisecondText = ''
            if (hours < 10) {
                hoursText = `0${hours}`
            } else {
                hoursText = `${hours}`
            }
            if (minutes < 10) {
                minutesText = `0${minutes}`
            } else {
                minutesText = `${minutes}`
            }
            if (second < 10) {
                secondText = `0${second}`
            } else {
                secondText = `${second}`
            }
            if (value % 100 < 10) {
                millisecondText = `0${Math.floor((value % 100))}`
            } else {

                millisecondText = `${Math.floor((value % 100))}`
            }
            return `${hoursText}:${minutesText}:${secondText}.${millisecondText}`;
        },
        //时分秒转换为滑块数据
        transformTimeToSecondFormat(t) {
            var data = 0;
            var arr = t.split(":");
            let hour = parseInt(arr[0]) * (60 * 60);
            let minutes = parseInt(arr[1]) * (60);
            var arr2 = arr[2].split(".");
            let second = parseInt(arr2[0]);
            let millisecond = parseInt(arr2[1]);
            data = (hour + minutes + second) * 100 + millisecond;
            return data;
        },

    },
};
</script>

<style lang="scss" scoped >
/* 短视频参考 star */

.ckbt,
.ckck {
    margin: 0 auto;
    width: 100%;
    min-width: 1250px;

}

.ckbt {
    height: 55px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 275px;
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.ckck {
    min-width: 1700px;
    /* height: 1000px; */
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 300px;
    padding-bottom: 60px;
}

.cksp {
    width: 99%;
    background-color: antiquewhite;
    box-sizing: border-box;
    margin: 10px auto;
    /* font-weight: bold; */
    text-align: center;
}

.ckck .wzsp {
    width: 20%;
    height: 360px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    box-sizing: border-box;
    padding: 0 0.5%;
    margin-bottom: 10px;
}

.wzsp .wzpd {
    width: 100%;
    height: 80%;
    background-color: #f5f5f5;
    box-sizing: border-box;
    border: 2px solid #dcdfe6;
    text-align: center;
}

.wzsp .wzpd:hover {
    width: 100%;
    height: 80%;
    background-color: #f5f5f5;
    box-sizing: border-box;
    border: 2px solid #409eff;
    text-align: center;
}

.wzsp .wzpdbz {
    width: 100%;
    height: 20%;
    font-size: 14px;
    color: #fff;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    box-sizing: border-box;
    padding: 11px 15px;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

}

.scsppd {
    width: 24%;
    /* height: 180px; */
    display: inline-block;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 1%;
    margin: 5px 0.5%;
    border: 1px solid #dcdfe6;
}

.ckck .scsppdw {
    width: 100%;
    background-color: rgb(255, 224, 224);
    display: inline-block;
    margin-right: 1%;
}

.scsppdw .sppdn {
    width: 23%;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 0.5%;
}

.scsppdw .sppdnbz {
    width: 54%;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 0.5%;
}

.scdpd,
.jqdpd {
    width: 100%;
    height: 80px;
    /* background-color: #077bff; */
    box-sizing: border-box;
    /* border: 1px solid #dcdfe6; */
    margin: 5px 0;
    font-size: 14px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

/* 短视频参考 end */

/* 短视频剪切 star */

.jqsp {
    background-color: #fff;
}

.jqsp .jqspbt {
    height: 55px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 35px;
    font-size: 16px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.jqsp .jqwzpd {
    width: 18%;
    height: 550px;
    background-color: #ffffff;
    display: inline-block;
    box-sizing: border-box;
    padding: 10px 35px;
    overflow: hidden;
}

.jqsp .jqwzpdnr {
    width: 90%;
    height: 340px;
    background-color: #fff;
    margin: 0 auto;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #dcdfe6;
    background-color: black;
}

.jqsp .jqwzpdbz {
    width: 90%;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    font-size: 14px;
    color: #fff;
    margin: 0px auto 10px auto;
    box-sizing: border-box;
    padding: 10px 15px;


    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;


}

.jqsp .jqwzpdan {
    width: 90%;
    max-height: 100px;
    background-color: rgb(255, 255, 255);
    text-align: center;
    font-size: 14px;
    color: #fff;
    margin: 0px auto;
    box-sizing: border-box;
    padding: 15px 20px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.jqsp .jqxzsp {
    width: 10%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.jqsp .jqyjdl {
    width: 72%;
    height: 550px;
    background-color: rgb(255, 255, 255);
    display: inline-block;
    box-sizing: border-box;
    padding: 20px 35px;
    overflow: auto;
}

.jqsp .jqsjx {
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid #dcdfe6;
    border-top: 0px;
    border-right: 0px;
    border-left: 0px;
}

.jqsp .qxtj {
    height: 80px;
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    padding: 20px 60px;
}

/* 短视频剪切 end */

.vedioplayshowRow {
    display: none;
}

.my-video {
    width: 600px;
    height: 400px;
}

.slider-container {
    padding: 0 16px;
}

.flexcenter {
    display: flex;
    border-radius: 2px 2px 0 0;
    border: 1px solid #eee;
    justify-content: center;
    align-items: center;
}
</style>