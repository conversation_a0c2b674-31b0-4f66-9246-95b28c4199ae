<template>

    <div class="body" id="tableContainer">
      <div class="body_hang" :style="elmain11">
  

        <el-container class="el-container">
          <el-main class="elmain1" :style="elmain1" ref="elmain1">
            <div class="icon" @click="iconclick(1)"><i class="el-icon-copy-document" title="进入公告信息维护"></i></div>
            <!-- 1 -->
            <div class="bodytop_left" v-show="clickoutin1 == false">{{dept=='Brand'?'采购公告信息':'跨境运营公告信息'}}</div>
            <goods-pirchase :whichmodules="moudleone" :dept="dept" :clickoutin="clickoutin1" :style="childheight">
            </goods-pirchase>
            <div class="bodybottom_left" v-show="clickoutin1 == true">{{dept=='Brand'?'采购公告信息':'跨境运营公告信息'}}</div>
          </el-main>
  
          <el-main class="elmain2" :style="elmain2" ref="elmain2">
            <div class="icon" @click="iconclick(2)"><i class="el-icon-copy-document" title="进入TEMU半托维护"></i></div>
            <!-- 2 -->
            <div class="bodytop_left" v-if="clickoutin2 == false">{{dept=='Brand'?'采购相关账号信息':'TEMU半托'}}</div>
            <goods-pirchase :whichmodules="moudletwo" :dept="dept" :clickoutin="clickoutin2" :style="childheight">
            </goods-pirchase>
            <div class="bodybottom_left" v-if="clickoutin2 == true">{{dept=='Brand'?'采购相关账号信息':'TEMU半托'}}</div>
          </el-main>
  
          <!-- 3 -->
          <el-main class="elmain3" :style="elmain3" ref="elmain3">
            <div class="icon" @click="iconclick(3)"><i class="el-icon-copy-document" title="进入TEMU全托维护"></i></div>
  
            <div class="bodytop_left" v-if="clickoutin3 == false">{{dept=='Brand'?'采购操作指南':'TEMU全托&SHEIN'}}</div>
            <goods-pirchase :whichmodules="moudlethr" :dept="dept" :clickoutin="clickoutin3" :style="childheight">
            </goods-pirchase>
            <div class="bodybottom_left" v-if="clickoutin3 == true">{{dept=='Brand'?'采购操作指南':'TEMU全托&SHEIN'}}</div>
          </el-main>
  
          <!-- 4 -->
          <!-- <el-main class="elmain4" :style="elmain4" ref="elmain4">
            <div class="icon" @click="iconclick(4)"><i class="el-icon-copy-document" title="进入SHEIN全托&自营维护"></i></div>
  
            <div class="bodytop_left" v-if="clickoutin4 == false">{{dept=='Brand'?'采购绩效':'SHEIN全托&自营'}}</div>
            <goods-pirchase :whichmodules="moudlefour" :dept="dept" :clickoutin="clickoutin4" :style="childheight">
            </goods-pirchase>
            <div class="bodybottom_left" v-if="clickoutin4 == true">{{dept=='Brand'?'采购绩效':'SHEIN全托&自营'}}</div>
          </el-main> -->
        </el-container>
      </div>
  
  
    </div>
  
  </template>
  
  <script>
  import goodsPirchase from './goodspurchase.vue';
  import MyContainer from '@/components/my-container'
  export default {
    data() {
      return {
        clickoutin0: false,
        clickoutin1: false,
        clickoutin2: false,
        clickoutin3: false,
        clickoutin4: false,
        clickoutin5: false,
        moudlefive: '模块五',
        moudlefour: 'SHEIN全托&自营',
        moudlethr: 'TEMU全托',
        moudletwo: 'TEMU半托',
        moudleone: '跨境运营公告',
        childheight: {
          height: '96%',
        },
        elmain11: {
          display: "block",
          cli: true,
        },
        elmain33: {
          display: "block",
          cli: true,
        },
        elmain55: {
          display: "block",
          cli: true,
        },
        elmain0: {
          display: "block",
          cli: true,
        },
        elmain1: {
          display: "block",
          cli: true,
        },
        elmain2: {
          display: "block",
          cli: true,
        },
        elmain3: {
          display: "block",
          cli: true,
        },
        elmain4: {
          display: "block",
          cli: true,
        },
        elmain5: {
          display: "flex",
          cli: true,
        },
        loginInfo: null,
        // 部门 Group  运营   Brand 采购
        dept: "Group"
      }
    },
    components: { goodsPirchase, MyContainer
         ,
     },
    mounted() {
      const tableContainer = document.getElementById('tableContainer')
      const deviceWidth = document.body.clientWidth
      const deviceHeight = document.body.clientHeight
      console.log('打印长宽', [deviceHeight, deviceWidth]);
      console.log('打印参数长宽', tableContainer.style.height);
      // //如果进入的页面为运营部，则设为运营部
      // var menuDept = this.$route.query.Dept;
      // if (menuDept == 'Group') {
      //   this.dept = 'Group';
      // }
      // iframeContainer.style.width = this.sidebar.opened ? (Number(deviceWidth) - 1) + 'px' : (Number(deviceWidth) - 71) + 'px'
      // tableContainer.style.width = '100%'
  
      // tableContainer.style.height = (Number(deviceHeight)) + 'px'
    },
    methods: {
      async getLoginInfo() {
        const res = await getCurrentUserAsync();
        if (!res?.Success) {
          return
        }
        this.loginInfo = res.data;
  
      },
      iconclick(data) {
        // this.clickoutin=!this.clickoutin;
        if (data == 0) {
          this.elmain0.cli == true ?   (this.childheight.height = '100%') && (this.elmain0.cli = !this.elmain0.cli) :
            (this.childheight.height = '96%') && (this.elmain0.cli = !this.elmain0.cli) && (this.elmain55.display = 'block');
          console.log(this.elmain0.cli);
          this.clickoutin0 = !this.clickoutin0;
        } else
        if (data == 1) {
          this.elmain1.cli == true ? (this.elmain2.display = 'none') && (this.elmain3.display = 'none') && (this.elmain4.display = 'none')&& (this.elmain55.display = 'none') && (this.childheight.height = '100%') && (this.elmain1.cli = !this.elmain1.cli) :
            (this.elmain1.display = 'block') && (this.elmain2.display = 'block') && (this.elmain3.display = 'block')&& (this.elmain4.display = 'block') && (this.childheight.height = '96%') && (this.elmain1.cli = !this.elmain1.cli) && (this.elmain55.display = 'block');
          console.log(this.elmain1.cli);
          this.clickoutin1 = !this.clickoutin1;
        } else if (data == 2) {
          this.elmain2.cli == true ? (this.elmain1.display = 'none') && (this.elmain55.display = 'none') && (this.elmain3.display = 'none')&& (this.elmain4.display = 'none') && (this.childheight.height = '100%') && (this.elmain2.cli = !this.elmain2.cli) :
            (this.elmain1.display = 'block') && (this.elmain2.display = 'block') && (this.elmain3.display = 'block')&& (this.elmain4.display = 'block') && (this.childheight.height = '96%') && (this.elmain2.cli = !this.elmain2.cli) && (this.elmain55.display = 'block');
          this.clickoutin2 = !this.clickoutin2;
        } else if (data == 3) {
          this.elmain3.cli == true ? (this.elmain4.display = 'none') && (this.elmain55.display = 'none') && (this.elmain1.display = 'none')&& (this.elmain2.display = 'none') && (this.childheight.height = '100%') && (this.elmain3.cli = !this.elmain3.cli) :
            (this.elmain3.display = 'block') && (this.elmain4.display = 'block') && (this.elmain1.display = 'block')&& (this.elmain2.display = 'block') && (this.childheight.height = '96%') && (this.elmain3.cli = !this.elmain3.cli) && (this.elmain55.display = 'block');
          this.clickoutin3 = !this.clickoutin3;
        } else if (data == 4) {
          this.elmain4.cli == true ? (this.elmain3.display = 'none') && (this.elmain55.display = 'none') && (this.elmain1.display = 'none')&& (this.elmain2.display = 'none') && (this.childheight.height = '100%') && (this.elmain4.cli = !this.elmain4.cli) :
            (this.elmain3.display = 'block') && (this.elmain4.display = 'block') && (this.elmain1.display = 'block')&& (this.elmain2.display = 'block')  && (this.childheight.height = '96%') && (this.elmain4.cli = !this.elmain4.cli) && (this.elmain55.display = 'block');
          this.clickoutin4 = !this.clickoutin4;
        } else if (data == 5) {
          this.elmain5.cli == true ?
          (this.elmain5.height = '100%') &&(this.elmain5.display = 'block') && (this.elmain4.display = 'none') && (this.elmain3.display = 'none') && (this.elmain55.display = 'none') && (this.elmain1.display = 'none')&& (this.elmain2.display = 'none') && (this.elmain0.display = 'none') && (this.childheight.height = '100%') && (this.elmain5.cli = !this.elmain5.cli)
            :
            (this.elmain5.height = '20%') &&(this.elmain5.display = 'block') && (this.elmain3.display = 'block') && (this.elmain4.display = 'block') && (this.elmain1.display = 'block')&& (this.elmain2.display = 'block')  && (this.childheight.height = '90%') && (this.elmain5.cli = !this.elmain5.cli) && (this.elmain5.display = 'block');         
          this.clickoutin5 = !this.clickoutin5;
        }
        console.log(this.elmain1.cli);
  
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .headmain{
    height: 120px;
  }
  .el-row {
    margin-bottom: 20px;
  
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .el-col {
    border-radius: 4px;
  }
  
  .bg-purple-dark {
    background: #99a9bf;
  }
  
  .bg-purple {
    background: #d3dce6;
  }
  
  .bg-purple-light {
    background: #e5e9f2;
  }
  
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
    height: 100%;
  }
  
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  
  .body {
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  
    .body_hang {
      flex: 1;
      min-height: 400px;
  
      // margin: 5px 0 0 0;
      .el-container {
        height: 100%;
        .elmain0 {
          display: flex;
          margin: 0 10px;
          border: 10px solid #a7e6e4;
          box-shadow: -3px 3px 6px #575660,
            3px -3px 6px #ffffff;
          border-radius: 10px;
          position: relative;
  
          .bodytop_left {
            color: #99a9bf;
            // margin: 0 10px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
          }
  
          .icon {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            right: 10px;
            top: 3px;
            z-index: 100;
          }
  
          .bodybottom_left {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            left: 60px;
            bottom: 10px;
            z-index: 100;
            font-weight: 600;
          }
        }
        .elmain1 {
          display: flex;
          margin: 0 10px;
          border: 10px solid #a7e6e4;
          box-shadow: -3px 3px 6px #575660,
            3px -3px 6px #ffffff;
          border-radius: 10px;
          position: relative;
  
          .bodytop_left {
            color: #99a9bf;
            // margin: 0 10px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
          }
  
          .icon {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            right: 10px;
            top: 3px;
            z-index: 100;
          }
  
          .bodybottom_left {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            left: 60px;
            bottom: 10px;
            z-index: 100;
            font-weight: 600;
          }
        }
  
        .elmain2 {
          display: flex;
          margin: 0 10px 0 0;
          border: 10px solid #a7e6e4;
          box-shadow: -3px 3px 6px #575660,
            3px -3px 6px #ffffff;
          border-radius: 10px;
          position: relative;
  
          .bodytop_left {
            color: #99a9bf;
            // margin: 0 10px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
          }
  
          .icon {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            right: 10px;
            top: 3px;
            z-index: 100;
          }
  
          .bodybottom_left {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            left: 60px;
            bottom: 10px;
            z-index: 100;
            font-weight: 600;
          }
        }
  
        .elmain3 {
          display: flex;
          margin: 0 10px;
          border: 10px solid #a7e6e4;
          box-shadow: -3px 3px 6px #575660,
            3px -3px 6px #ffffff;
          border-radius: 10px;
          position: relative;
  
          .bodytop_left {
            color: #99a9bf;
            // margin: 0 10px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
  
          }
  
          .icon {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            right: 10px;
            top: 3px;
            z-index: 100;
          }
  
          .bodybottom_left {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            left: 60px;
            bottom: 10px;
            z-index: 100;
            font-weight: 600;
          }
        }
  
        .elmain4 {
          display: flex;
          margin: 0 10px 0 0;
          border: 10px solid #a7e6e4;
          box-shadow: -3px 3px 6px #575660,
            3px -3px 6px #ffffff;
          border-radius: 10px;
          position: relative;
  
          .bodytop_left {
            color: #99a9bf;
            // margin: 0 10px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
          }
  
          .icon {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            right: 10px;
            top: 3px;
            z-index: 100;
          }
  
          .bodybottom_left {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            left: 60px;
            bottom: 10px;
            z-index: 100;
            font-weight: 600;
          }
        }
  
        .elmain5 {
          height: 600px;
          display: flex;
          margin: 0 10px 0 0;
          border: 10px solid #a7e6e4;
          box-shadow: -3px 3px 6px #575660,
            3px -3px 6px #ffffff;
          border-radius: 10px;
          position: relative;
          box-sizing: border-box;
  
          .bodytop_left {
            color: #99a9bf;
            // margin: 0 10px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
          }
  
          .icon {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            right: 10px;
            top: 3px;
            z-index: 100;
          }
  
          .bodybottom_left {
            position: absolute;
            color: #99a9bf;
            font-size: 20px;
            left: 60px;
            bottom: 10px;
            z-index: 100;
            font-weight: 600;
          }
        }
      }
  
      // background-color: aqua;
    }
  }
  
  .hang_five{
    display: flex;
    .container1{
      width: 49%;
    }
  }
  ::v-deep .el-main {
    height: 96.5%;
  }
  </style>
  