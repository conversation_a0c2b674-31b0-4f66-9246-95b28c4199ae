<template>
    <container v-loading="pageLoading">
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>               
            <el-form-item label="时间:">
              <!-- <el-date-picker style="width: 100%" v-model="filter.yearMonthDay" type="date" format="yyyyMMdd"   value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker> -->
              <el-date-picker v-model="timerange" style="width:220px" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" 
                start-placeholder="开始" end-placeholder="结束" :picker-options="pickerOptions" :clearable="false" @change="onSearch"></el-date-picker>
            </el-form-item>               
            <el-form-item label="系列编码:"><el-input v-model="filter.seriesCode" clearable /></el-form-item>                
            <el-form-item label="商品编码:"><el-input v-model="filter.goodsCode" clearable /></el-form-item>                
            <el-form-item label="商品名称:"><el-input v-model="filter.goodsName" clearable /></el-form-item>
            <el-form-item label="采购员:">
            <el-select v-model="filter.brandId" filterable collapse-tags clearable placeholder="请选择采购员"  style="width: 200px">
              <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
            </el-form-item> 
            <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
        </el-form>
      </template>
        <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange' @cellclick='cellclick' 
         :hasexpand='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
         :showsummary='true' :summaryarry='summaryarry'>
      </ces-table>
    
      <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <span>
          <el-row>
            <el-col :xs="24" :sm="14" :md="14" :lg="14" :xl="14">
            <el-date-picker style="width: 100%" v-model="onimportfilter.yearmonthday" type="date" format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
         </el-col>
         </el-row>
          <el-row>
            <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
              <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" action accept=".xlsx"
              :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
              <template #trigger>
                  <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="submitUpload">{{(uploadLoading?'上传中':'上传' )}}</el-button>
            </el-upload>
            </el-col>
          </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-drawer title="参数设置" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editparmVisible"
                direction="btt" size="'auto'" class="el-drawer__wrapper"  style="position:absolute;">
        <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
          <div class="drawer-footer">
            <el-button @click.native="editparmVisible = false">取消</el-button>
            <my-confirm-button type="submit"  :loading="editparmLoading" @click="onSetEditParm" />
         </div>
    </el-drawer>
    <div>
    <el-dialog :visible.sync="visiblepopoverdetail" v-dialogDrag width="70%" :show-close="false">
      <div style="height:500px">
        <codingdailydetail ref="codingdailydetail" :fileter="purorderindefilter" style="height:100%" ></codingdailydetail> 
      </div>       
    </el-dialog>
    </div>
    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue"
import container from "@/components/my-container"
import {getAllProBrand} from '@/api/inventory/warehouse'
import {importDayReportAsync, getPageDayReportList, addOrUpdateGoodsPercentagePoints, getGoodsPercentagePointsByCode} from '@/api/financial/yyfy'
import MyConfirmButton from "@/components/my-confirm-button"
import codingdailydetail from '@/views/bookkeeper/reportday/codingdailydetail'
import { formatPlatform,formatYesorno} from "@/utils/tools"
const tableCols = [
    {istrue:true, prop:'yearMonthDay', label:'年月日', width:'100', sortable:'custom',formatter:(row)=>formatTime(row.yearMonthDay,'YYYYMMDD')},
    {istrue:true, prop:'seriesCode', label:'系列编码', width:'150', sortable:'custom'},
    {istrue:true, prop:'goodsCode', label:'商品编码', width:'120', sortable:'custom'},
    {istrue:true, prop:'goodsName', label:'商品名称', width:'220', sortable:'custom'}, 
    {istrue:true,prop:'brandId',label:'采购员', width:'80',sortable:'custom',formatter:(row)=>row.brandId == 0 ? " " :row.brandName},
    {istrue:true, prop:'taoProfit1Rate', label:'淘宝毛利', width:'100', sortable:'custom'},
    {istrue:true, prop:'taoPercentagePoints', label:'淘宝提成', width:'100', sortable:'custom',formatter:(row)=>row.taoPercentageRoyalty==0?' ' : row.taoPercentageRoyalty},
    {istrue:true, prop:'pinProfit1Rate', label:'拼多多毛利', width:'100', sortable:'custom'},      
    {istrue:true, prop:'pinPercentagePoints', label:'拼多多提成',width:'120', sortable:'custom',formatter:(row)=>row.pinPercentageRoyalty==0?' ':row.pinPercentageRoyalty},
    {istrue:true, prop:'gongProfit1Rate', label:'工厂店毛利', width:'100', sortable:'custom'}, 
    {istrue:true, prop:'gongPercentagePoints', label:'工厂店提成',width:'120', sortable:'custom',formatter:(row)=>row.gongPercentageRoyalty==0?' ':row.gongPercentageRoyalty},
    {istrue:true, prop:'sumPercentageRoyalty', label:'总提成',width:'auto',formatter:(row)=>row.pinPercentageRoyalty==0 && row.taoPercentageRoyalty==0 && row.gongPercentageRoyalty==0?' ':row.sumPercentageRoyalty},
    {istrue:true,type:'button', width:'55',btnList:[{label:"详情",display:(row)=>{return row.isHandle==true;},handle:(that,row)=>that.nclick(row)}]},
    {istrue:true,type:'button', width:'55',btnList:[{label:"设置",display:(row)=>{return row.isHandle==true;},handle:(that,row)=>that.onHand(row)}]},
];
const tableHandles=[{label:"导入", handle:(that)=>that.startImport()}, {label:"刷新", handle:(that)=>that.getlist()}];
  const startDate = formatTime(dayjs().subtract(7,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'YunhanAdminCodingdaily',
    components: {container, cesTable, MyConfirmButton, codingdailydetail},
    data() {
        return {
            that:this,
            filter: {
                goodsCode:null,
                seriesCode:null,
                goodsName:null,
                startDate:null,
                endDate:null,                
                brandId:null,
                
            },
            timerange:[startDate,endDate],
            purorderindefilter:{
               goodsCode: "",
               yearMonthDay: "" 
               },
            onimportfilter:{
                yearmonthday:formatTime(dayjs().subtract(1,"day"), "YYYY-MM-DD"),
            },
            list:[],
            brandlist:[],
            fileList:[],
            fileparm:{},
            summaryarry:{},
            tableCols:tableCols,
            tableHandles:tableHandles,
            pager:{OrderBy:'yearMonthDay', IsAsc:false},
            total:0,
            sels:[],   
            listLoading:true,   
            pageLoading: false,
            dialogVisible:false,
            uploadLoading:false,
            editparmVisible:false,
            editparmLoading:false,
            addLoading: false,
            visiblepopoverdetail: false,
            popperFlagdetail: false,
            autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 6 }}}},
               rule:[]
          },     
          pickerOptions:{
          disabledDate(time){
          return time.getTime()>Date.now();
        }
      }, 
        };
    },
    watch: {
  
    },
    async mounted() {
        await this.onSearch();
        this.initformparm();
        await this.init();
    },

    methods: {
      initformparm(){
       this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: '',col:{span:12}},
                     {type:'input',field:'goodsCode',title:'编码',validate: [{type: 'string', required: true, message:'请输入组员名称'}]},
                     {type:'InputNumber',field:'taoPercentagePoints',title:'淘宝提成点%',value: null,props:{min:0,precision:3,step:0.5},col:{span:6}},
                     {type:'InputNumber',field:'pinPercentagePoints',title:'拼多多提成点%',value: null,props:{min:0,precision:3,step:0.5},col:{span:6}},
                     {type:'InputNumber',field:'gongPercentagePoints',title:'工厂提成点%',value: null,props:{min:0,precision:3,step:0.5},col:{span:6}},
                    ]
    },
    async onSearch() {      
       this.$refs.pager.setPage(1)
       this.getlist()      
    },
    async getlist(){
        if (!this.pager.OrderBy) this.pager.OrderBy="";
        var pager = this.$refs.pager.getPager()
        this.filter.startDate =null;
        this.filter.endDate =null;
        if (this.timerange) {
          this.filter.startDate = this.timerange[0];
          this.filter.endDate = this.timerange[1];
        }      
        const params = {...pager,...this.pager,... this.filter}       
        console.log('params',params)
        this.listLoading = true
        const res = await getPageDayReportList(params)
        this.listLoading = false
        if (!res?.success) return
        this.total = res.data.total
        const data = res.data.list
        this.summaryarry = res.data.summary
        data.forEach(d => {d._loading = false})
        this.list = data
    },
    async cellclick(row, column, cell, event){

    },  
    async nclick(row){
      this.visiblepopoverdetail = true;
      this.getdetaillist(row)
    },
   getdetaillist(row) {     
      this.purorderindefilter = { goodsCode: row.goodsCode, yearMonthDay: row.yearMonthDay };
      this.$nextTick( ()=>{
        this.$refs.codingdailydetail.onSearch(row.goodsCode, row.yearMonthDay);
      })
    },
    //编辑
    async onHand(row){
        this.editparmVisible = true
        var res= await getGoodsPercentagePointsByCode({goodsCode:row.goodsCode});
        var model={id:res.data == null ? 0 : res.data.id, goodsCode:row.goodsCode, pinPercentagePoints:res.data == null ? 0 : res.data.pinPercentagePoints, 
        taoPercentagePoints:res.data ==null ? 0 : res.data.taoPercentagePoints, gongPercentagePoints:res.data ==null ? 0 : res.data.gongPercentagePoints }
        
        this.$nextTick(() => {
        var arr = Object.keys(this.autoform.fApi);
        if(arr.length >0) {
        this.autoform.fApi.setValue(model)
        this.autoform.fApi.disabled(true, 'goodsCode')
          }
        });
    },
    async onSetEditParm(){
        this.editparmLoading=true;
        this.$nextTick(() => {
        this.autoform.fApi.validate(async (valid, fail) => {
        if(valid){
            const formData = this.autoform.fApi.formData();
            formData.id=formData.id?formData.id:0;
            //formData.Enabled=true;
            const res = await addOrUpdateGoodsPercentagePoints(formData);
            if(res.code==1){
                this.getlist();
                this.editparmVisible=false;
            }
            }else{
            //todo 表单验证未通过
            }
        })
        this.editparmLoading=false;
      });
    },  
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    async submitUpload() {
    if (!this.onimportfilter.yearmonthday) {
       this.$message({type: 'warning',message: '请选择年月!'});
       return;
      }    
   if (!this.fileList || this.fileList.length == 0) {
        this.$message({ message: "请先选取文件", type: "warning" });
        return false;
      }
      this.fileHasSubmit=true;
      this.uploadLoading=true;
      this.$refs.upload.submit();
    },
    //导入
    async uploadFile(item) {
      if(!this.fileHasSubmit){
        return false;
      }
      this.fileHasSubmit=false;
      const form = new FormData();
      form.append("token", this.token);
      form.append("upfile", item.file);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      const res =await importDayReportAsync(form);
      if (res.code==1)   this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else  this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading=false;    
      },
    async uploadChange(file, fileList) {
    if (fileList && fileList.length > 0) {
      var list = [];
      for(var i=0;i<fileList.length;i++){
        if(fileList[i].status=="success")
          list.push(fileList[i]);
        else
          list.push(fileList[i].raw);
      }
      this.fileList = list;
    }
    },
    async init(){
      var res2= await getAllProBrand();
      this.brandlist = res2.data.map(item => {
          return { value: item.key, label: item.value };
      });
    },
    uploadRemove(file, fileList){
       this.uploadChange(file, fileList);
    },
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    },
   selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.proBianMa);
      })
    },
    startImport() {
      this.dialogVisible = true;
    },
    },
};
</script>

<style lang="scss" scoped>

</style>