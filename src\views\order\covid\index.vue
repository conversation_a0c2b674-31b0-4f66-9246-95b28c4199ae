<template>
    <my-container v-loading="pageLoading" style="width:99%;height:100%;">
        <el-tabs v-model="activeName" style="height: 94%;" type="card">
            <el-tab-pane label="疫情停发订单" name="one" style="height: 100%;">
                <covidStopSendPage ref="covidStopSendPage" :tablekey="'covidOne'" />
            </el-tab-pane>
            <el-tab-pane label="停发快递公司" name="two" style="height: 100%;">
                <expressStopSendPage ref="expressStopSendPage" :tablekey="'covidOne'" />
            </el-tab-pane>
            <el-tab-pane label="快递公司停发合并" name="three" style="height: 100%;">
                <expressStopSendPageMerge ref="expressStopSendPageMerge" :tablekey="'covidOne'" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
    import MyContainer from "@/components/my-container";
    import covidStopSendPage from '@/views/order/covid/CovidStopSendPage'
    import expressStopSendPage from '@/views/order/covid/ExpressStopSendPageIndex'
    import expressStopSendPageMerge from '@/views/order/covid/ExpressStopSendPageMerge'
    export default {
        name: 'covidIndex',
        components: { MyContainer, covidStopSendPage, expressStopSendPage, expressStopSendPageMerge },
        props: {

        },
        data () {
            return {
                activeName: "one",
            }
        },
        async mounted () {

        },
        methods: {
        }
    }
</script>
