<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
       <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
    </template>
      <el-button-group>
            <el-button style="padding: 0;margin: 0;">
                <el-input v-model="filter.ProductID" placeholder="商品ID" style="width:120px;"/>
            </el-button>
             <el-button style="padding: 0;margin: 0;">
                <el-input v-model="filter.ProductName" placeholder="商品名称" style="width:160px;"/>
            </el-button>
             <el-button style="padding: 0;margin: 0;">
                <el-date-picker style="width: 160px" v-model="filter.UseMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
            </el-button>
            <el-button style="padding: 0;margin: 0;">
              <el-select filterable v-model="filter.platform" placeholder="平台" style="width:120px;">
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-button>
            <el-button style="padding: 0;">
              <el-select filterable v-model="filter.shopCode" placeholder="所属店铺">
                <el-option key="所有" label="所有" value></el-option>
                <el-option v-for="item in shopList" :key="item.shopCode"  :label="item.shopName" :value="item.shopCode"></el-option>
              </el-select>
         </el-button>
         <el-button type="primary" @click="onSearch">查询</el-button>
         <el-button type="primary" @click="onExport">导出</el-button>
      </el-button-group>
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='financialreportlist' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
    </template>
  </my-container>
</template>
<script>
import { getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import {getFinancialReportList } from '@/api/financial/yyfy'
import {exportFinancialReport } from '@/api/bookkeeper/financialreport'
import {formatWarehouse,formatTime,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
 import { Loading } from 'element-ui';
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
  loading = Loading.service({
  lock: true,
  text: '加载中……',
  background: 'rgba(0, 0, 0, 0.7)'
  });
}; 
const tableCols =[
        {istrue:true,fixed:true,prop:'proCode',fix:true,label:'商品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
        {istrue:true,fixed:true,prop:'idGroup',label:'小组',sortable:'custom', width:'70',formatter:(row)=> row.groupName},
        {istrue:true,prop:'',label:`账单`, merge:true,
            cols:[
              {istrue:true,prop:'yearMonth',label:'年月',sortable:'custom', width:'65'},
              {istrue:true,prop:'createdTime',label:'统计日期',sortable:'custom', width:'150'},
              {istrue:true,prop:'proName',label:'产品名称',sortable:'custom', width:'150'},
              //{istrue:true,prop:'grossProfit',label:'销售毛利',sortable:'custom', width:'80'},
              {istrue:true,prop:'shopCode',label:'店铺名称',sortable:'custom', width:'150',formatter:(row)=> row.shopName},
              {istrue:true,prop:'orderCount',label:'订单数',sortable:'custom', width:'70'},
              {istrue:true,prop:'count',label:'ID数',sortable:'custom', width:'70'},
              {istrue:true,prop:'amountSettlement',label:'结算收入',sortable:'custom', width:'80'},
              {istrue:true,prop:'amountSettlement_1',label:'结算收入-1',sortable:'custom', width:'95'},
              {istrue:true,prop:'amountCrossMonthIn',label:'跨月收入', width:'80'},
              {istrue:true,prop:'amountOut',label:'退款', sortable:'custom',width:'70'},
              {istrue:true,prop:'amountCrossMonthOut',label:'跨月退款', sortable:'custom',width:'80'},
              {istrue:true,prop:'amountSettlement_2',label:'2月之前月份收入', sortable:'custom',width:'80'},
              {istrue:true,prop:'dkTotalAmont',label:'账单扣点', sortable:'custom',width:'80'},
              //{istrue:true,prop:'AmountCrossMonthIn_1',label:'2月之前月份收入-1', sortable:'custom',width:'80'},
              {istrue:true,prop:'amountCost',label:'结算成本',sortable:'custom', width:'80'},
              {istrue:true,prop:'amountOutCost',label:'退款成本',sortable:'custom', width:'80'},
              {istrue:true,prop:'amountEmptyId',label:'空白链接ID成本', sortable:'custom',width:'80'},
              {istrue:true,prop:'amountExceptionCost',label:'异常成本按ID', sortable:'custom',width:'80'},
              {istrue:true,prop:'amountExceptionCostAvg',label:'异常成本分摊', sortable:'custom',width:'80'},
              // {istrue:true,prop:'amountExceptionNoInId',label:'异常未收按ID', sortable:'custom',width:'80'},
              // {istrue:true,prop:'amountExceptionNoInAvg',label:'异常未收分摊', sortable:'custom',width:'80'},
              {istrue:true,prop:'amountReSendCost',label:'补发成本按ID',sortable:'custom', width:'80'},
              {istrue:true,prop:'amountReSendCostAvg',label:'补发成本分摊',sortable:'custom', width:'80'},
              {istrue:true,prop:'agentCost',label:'代发成本差',sortable:'custom', width:'80'},
              // {istrue:true,prop:'AmountSettleCost',label:'代发成本',sortable:'custom', width:'80'},
              // {istrue:true,prop:'AmountSettleCost',label:'护墙角定制差额',sortable:'custom', width:'80'},
              // {istrue:true,prop:'AmountSettleCost',label:'采购运费',sortable:'custom', width:'80'},
              //{istrue:true,prop:'grossProfit',label:'销售毛利',sortable:'custom', width:'80'},
              // {istrue:true,prop:'AmountReturnCrossMonth_2',label:'2月之前月份退款', sortable:'custom',width:'80'},
              // {istrue:true,prop:'AmountExceptionCost',label:'2月之前月份销售成本', sortable:'custom',width:'80'},
              //{istrue:true,prop:'AmountReSend',label:'2月补发成本', sortable:'custom',width:'80'}
                  ]},
        {istrue:true,prop:'',label:`运营费用`, merge:true,
              cols:[{istrue:true,prop:'cuishou',sortable:'custom',label:'催收',width:'80'},
                  {istrue:true,prop:'dahuixiong',sortable:'custom',label:'特殊单费用',width:'70'},
                  {istrue:true,prop:'pingduoduochangjin',sortable:'custom',label:'拼多多场景展示',width:'80'},
                  {istrue:true,prop:'pingduoduofangxintui',sortable:'custom',label:'拼多多放心推',width:'95'},
                  {istrue:true,prop:'pingduoduosousuo',sortable:'custom',label:'拼多多搜索推广', width:'80'},
                  {istrue:true,prop:'pingduoduozhibo',sortable:'custom',label:'拼多多直播推广', width:'80'},
                  {istrue:true,prop:'shizhitou',sortable:'custom',label:'一站式智投',width:'80'},
                  {istrue:true,prop:'taobaoke',sortable:'custom',label:'淘宝客',width:'80'},
                  {istrue:true,prop:'taolijing',sortable:'custom',label:'淘礼金',width:'80'},
                  {istrue:true,prop:'taotetuiguang',sortable:'custom',label:'淘特推广',width:'80'},
                  {istrue:true,prop:'tuijian',sortable:'custom',label:'超级推荐',width:'80'},
                  {istrue:true,prop:'yingxiao',sortable:'custom',label:'营销费用',width:'80'},
                  {istrue:true,prop:'zhitongcheamont',sortable:'custom',label:'直通车',width:'80'}]},
                  {istrue:true,fixed:false,prop:'cbcamont',fix:true,label:'护墙角成本差', width:'80',sortable:'custom',children:[]},
                  {istrue:true,prop:'',label:`产品费用`, merge:true,
            cols:[{istrue:true,prop:'pickUpActualAmont',label:'提货费',sortable:'custom',width:'80'},
                  // {istrue:true,prop:'dingDingExamineActualAmont',label:'钉钉审批',sortable:'custom',width:'80'},
                  {istrue:true,prop:'shootActualAmont',label:'拍摄', sortable:'custom',width:'70'},
                  {istrue:true,prop:'sampleBXActualAmont',label:'样品报销', sortable:'custom',width:'80'},
                  {istrue:true,prop:'sampleGropActualAmont',label:'运营样品', sortable:'custom',width:'80'},
                  {istrue:true,prop:'sampleMGActualAmont',label:'美工样品', sortable:'custom',width:'95'},
                  {istrue:true,prop:'purchaseFActualAmont',label:'采购运费', sortable:'custom',width:'80'}]},
        {istrue:true,prop:'',label:`工资`, merge:true,
            cols:[{istrue:true,prop:'gropuOperateWagesActualAmont',label:'运营工资',sortable:'custom', width:'80'},
                  {istrue:true,prop:'mgCommissionActualAmont',label:'美工提成', sortable:'custom',width:'70'},
                  {istrue:true,prop:'peCommissionActualAmont',label:'采购爆款提成',sortable:'custom', width:'80'}]},
        {istrue:true,prop:'',label:`订单`, merge:true,
            cols:[{istrue:true,prop:'freightFee',label:'快递费',sortable:'custom', width:'80'},
                  {istrue:true,prop:'packageFee',label:'包装费',sortable:'custom', width:'70'}]},
        {istrue:true,prop:'grossProfit',label:'销售毛利',sortable:'custom', width:'80'},
        ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      filter: {
        platform:null,
        UseMonth:null,
        shopCode:null
      },
      platformlist:platformlist,
      shopList:[],
      userList:[],
      groupList:[],
      financialreportlist: [],
      tableCols:tableCols,
      tableHandles:[],
      total: 0,
     // summaryarry:{count_sum:10},
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      summaryarry:{},
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    //await this.onSearch()
    await this.getShopList();
  },
  methods: {
   async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
          //f.isOpen==1&&
          if(f.isCalcSettlement&&f.shopCode)
              this.shopList.push(f);
        });
    },
   async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
     await this.onSearch();
    },
    onRefresh(){
        this.onSearch()
    },
    async onSearch(){
      this.$refs.pager.setPage(1);
      await this.getList().then(res=>{  });
      // loading.close();
    },
    async getList(){
     var that=this;
      var pager = this.$refs.pager.getPager();
      const params = {...pager,...this.pager,...this.filter,};
     // this.listLoading = true;
      startLoading(); 
      const res = await getFinancialReportList(params).then(res=>{
         loading.close();
          that.total = res.data?.total
          that.financialreportlist = res.data?.list;
          that.summaryarry=res.data?.summary;
      });
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
   async onExport(){
      if (!this.filter.UseMonth) {
        this.$message({message:"请先选择月份！",type:"warning"});
        return;
      } 
      var pager = this.$refs.pager.getPager();
      const params = {  ...pager,   ...this.pager,   ...this.filter};
      var res= await exportFinancialReport(params);
      if(!res?.data) {
         this.$message({message:"没有数据",type:"warning"});
         return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','财务账单数据' +  new Date().toLocaleString() + '_.xlsx' )
      aLink.click()
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
