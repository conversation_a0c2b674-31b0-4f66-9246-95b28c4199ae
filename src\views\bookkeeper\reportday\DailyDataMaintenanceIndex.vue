<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane   label="加工费" name="tab1" style="height: 100%;">
          <ProcessingCost :filter="Filter" ref="ProcessingCost" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="客服工资" name="tab2" :lazy="true" style="height: 100%;">
        <CustomerServiceFee :filter="Filter" ref="CustomerServiceFee" style="height: 100%;"/>
      </el-tab-pane>
        <el-tab-pane   label="产品运费" name="tab3" :lazy="true" style="height: 100%;">
          <productFee ref="productFee" />
      </el-tab-pane>
      <el-tab-pane   label="包装费" name="tab4" :lazy="true" style="height: 100%;">
        <packageFee ref="packageFee" />
      </el-tab-pane>

      <el-tab-pane   label="仓储损耗" name="tab6" :lazy="true" style="height: 100%;">
        <LossOffFee :filter="Filter" ref="LossOffFee" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="仓储损耗(系列编码)" name="tab7" :lazy="true" style="height: 100%;">
        <LossOffFeeStyleCode :filter="Filter" ref="LossOffFeeStyleCode" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="出仓成本" name="tab8" :lazy="true" style="height: 100%;">
        <OutWarehouseProportion :filter="Filter" ref="OutWarehouseProportion" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="损耗下架" name="tab9" :lazy="true" style="height: 100%;">
        <LossOffShelf :filter="Filter" ref="LossOffShelf" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="预估费用" name="tab10" :lazy="true" style="height: 100%;">
        <EstimatedCost :filter="Filter" ref="EstimatedCost" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="快递费维护" name="tab13" :lazy="true" style="height: 100%;">
        <dailyExpressFeeDataIndex :filter="Filter" ref="dailyExpressFeeDataIndex" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="不计算包装出仓" name="tab15" :lazy="true" style="height: 100%;">
        <ExcludingPackagingAndWarehouseCosts :filter="Filter" ref="refExcludingPackagingAndWarehouseCosts" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="特殊ID/店铺" name="tab14" :lazy="true" style="height: 100%;">
        <specialIDStore :filter="Filter" ref="refspecialIDStore" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="大马店铺" name="tab20" :lazy="true" style="height: 100%;">
        <dmShopList ref="dmShopList" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="大马美甲商品加工费" name="tab21" :lazy="true" style="height: 100%;">
        <commodityProcessing ref="refcommodityProcessing" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane v-if="checkPermission('brandManagementPermis')" label="品牌管理" name="tab22" :lazy="true" style="height: 100%;">
        <brandManagementIndex ref="refbrandManagementIndex" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="公摊费率" name="tab5" :lazy="true" style="height: 100%;">
        <ContributoryRate :filter="Filter" ref="ContributoryRate" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="淘工厂基础服务费率" name="tab11" :lazy="true" style="height: 100%;">
        <TgcBasicServiceRate :filter="Filter" ref="TgcBasicServiceRate" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="淘工厂超链接类目费率" name="tab12" :lazy="true" style="height: 100%;">
        <TgcSLinkServiceRate :filter="Filter" ref="TgcSLinkServiceRate" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="快手服务费率" name="tab18" :lazy="true" style="height: 100%;">
        <ksServiceRate ref="refksServiceRate" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane label="运营工资" name="tab19" :lazy="true" style="height: 100%;">
        <yygzGroupIndex ref="yygzGroupIndex" style="height: 100%;"/>
      </el-tab-pane>
      <!-- <el-tab-pane   label="普通辅料" name="tab8" style="height: 100%;">
        <NormalAccessory :filter="Filter" ref="NormalAccessory" style="height: 100%;"/>
      </el-tab-pane> -->
        </el-tabs>
    </my-container >

   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import ProcessingCost from './ProcessingCost'
  import CustomerServiceFee from './CustomerServiceFee'
  import productFee from './productFee'
  import checkPermission from '@/utils/permission'
  import packageFee from './packageFee'
  import ContributoryRate from './ContributoryRate'
  import LossOffFee from './LossOffFee'
  import LossOffFeeStyleCode from './LossOffFeeStyleCode'
  import OutWarehouseProportion from './OutWarehouseProportion'
  import NormalAccessory from './NormalAccessory'
  import LossOffShelf from './LossOffShelf'
  import EstimatedCost from './EstimatedCost'
  import TgcSLinkServiceRate from './TgcSLinkServiceRate'
  import ExpressFeeAveragePrice from './ExpressFeeAveragePrice'
  import TgcBasicServiceRate from './TgcBasicServiceRate'
  import specialIDStore from './specialIDStore'
  import particularlyExpressFee from './particularlyExpressFee'
  import shopNotCalcExpressFee from './shopNotCalcExpressFee'
  import dmShopList from './dmShopList'
  import ksServiceRate from './ksServiceRate'
  import commodityProcessing from './commodityProcessing'
  import brandManagementIndex from './management/brandManagementIndex.vue'
  import dailyExpressFeeDataIndex from './DailyExpressFeeDataIndex'
  import ExcludingPackagingAndWarehouseCosts from './ExcludingPackagingAndWarehouseCosts.vue'
  import yygzGroupIndex from './yygztc/yygzGroupIndex'
  



  export default {
    name: "Users",
    components: { MyContainer,ProcessingCost,checkPermission,CustomerServiceFee,productFee,packageFee,ContributoryRate,LossOffFee,LossOffFeeStyleCode,OutWarehouseProportion,NormalAccessory,LossOffShelf
      ,EstimatedCost,TgcBasicServiceRate,ExpressFeeAveragePrice,TgcSLinkServiceRate, specialIDStore, particularlyExpressFee,shopNotCalcExpressFee
      ,ksServiceRate,dmShopList,commodityProcessing,brandManagementIndex,dailyExpressFeeDataIndex,yygzGroupIndex,ExcludingPackagingAndWarehouseCosts},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {






    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1')
    this.$refs.ProcessingCost.onSearch();
  }


    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
