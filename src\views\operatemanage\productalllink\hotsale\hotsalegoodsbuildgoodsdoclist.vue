<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" v-model="showTableTab" @change="onSearch()">
                            <el-option :key="1" label="竞品查看" :value="1">
                            </el-option>
                            <el-option :key="2" label="商品查看" :value="2">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <inputYunhan ref="refGoodsCompeteId" :inputt.sync="Filter.goodsCompeteId" v-model="Filter.goodsCompeteId" width="160px"
                          placeholder="竞品ID/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="2000"
                          @callback="callbackGoodsCompeteId" title="竞品ID">
                        </inputYunhan>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.goodsCompeteName" clearable placeholder="竞品标题" style="width:120px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.applyUserName" clearable placeholder="申请人" style="width:80px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.auditUserName" clearable placeholder="审核人" style="width:80px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" clearable v-model="Filter.auditState" placeholder="审核状态">
                            <el-option v-for="(item) in auditStates" :key="'auditState'+item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.yhGoodsCode" clearable placeholder="商品编码" style="width:100px;" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;">
                        <el-input v-model="Filter.yhGoodsName" clearable placeholder="商品名称" style="width:100px;" />
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" clearable v-model="Filter.forNewWarehouse" placeholder="上新仓库">

                            <!-- <el-option v-for="item in sendWarehouse4HotGoodsBuildGoodsDocList" :key="item.value" :label="item.label" :value="item.value">
                            </el-option> -->
                            <el-option v-for="item in fomSendWarehouse4HotGoodsBuildGoodsDocList" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                            <!-- <el-option :key="3" label="南昌昌东" :value="3">
                            </el-option>
                            <el-option :key="91" label="厂家代发" :value="3">
                            </el-option> -->
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" clearable v-model="Filter.goodsProgressType" placeholder="商品类型">
                            <el-option key="成品" label="成品" value="成品">
                            </el-option>
                            <el-option key="半成品" label="半成品" value="半成品">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;">
                        <el-select style="width:100px" clearable v-model="Filter.isMainSale" placeholder="是否主售">
                            <el-option :key="true" label="是" :value="true">
                            </el-option>
                            <el-option :key="false" label="否" :value="false">
                            </el-option>
                        </el-select>
                    </el-button>

                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="()=>{Filter={};}">清空条件</el-button>

                </el-button-group>
            </el-form>
        </template>

        <!--列表-->
        <template v-if="showTableTab==1">
            <!-- <ces-table rowkey="id" :treeprops="{children: 'docDtls', hasChildren: true}" ref="table1" :isIndex='true'  :that='that' :hasexpand='true' :hasexpandRight="true" @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' :isSelection='false' :isSelectColumn="true" :tableCols='tableColsTree' :loading="listLoading">
                <template slot="right">
                    <el-table-column width="80" label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button type="text" v-if="!scope.row.isGoodsCompete" @click="showGoodsDocDtl(scope.row)">详情</el-button>

                            <el-button  type="text"  v-if="!!scope.row.isGoodsCompete && !scope.row.shootingTaskId"  @click="onOpenAddTask(scope.row,1)">拍摄</el-button>
                        </template>
                    </el-table-column>

                </template>
            </ces-table> -->

            <vxetablebase :id="'hotsalegoodsbuildgoodsdoclist20221212'"
            :tableData='tbdatalist' :tableCols='tableColsTree'
            :treeProp="{ rowField: 'id', parentField: 'hotSaleGoodsChooseId' }"
             @sortchange='sortchange'
            >

                <template slot="right">
                    <vxe-column title="操作" :field="'col'+(tableColsTree.length+1)" width="80" fixed="right">
                        <template #default="{ row }">
                            <el-button type="text" v-if="!row.isGoodsCompete" @click="showGoodsDocDtl(row)">详情</el-button>

                            <!-- <el-button  type="text"  v-if="!!row.isGoodsCompete && !row.shootingTaskId"  @click="onOpenAddTask(row,1)">拍摄</el-button> -->
                        </template>
                    </vxe-column>
                </template>

            </vxetablebase>


        </template>
        <template v-else>
            <ces-table ref="table2" :isIndex='true'  :that='that' :hasexpand='true' :hasexpandRight="true" @sortchange='sortchange' :tableData='tbdatalist' @select='selectchange' :isSelection='false' :isSelectColumn="true" :tableCols='tableCols' :loading="listLoading">
                <template slot="right">
                    <el-table-column width="80" label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button type="text" v-if="!scope.row.isGoodsCompete" @click="showGoodsDocDtl(scope.row)">详情</el-button>
                        </template>
                    </el-table-column>

                </template>
            </ces-table>
        </template>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>

        <el-dialog title="建编码详情" :visible.sync="dialogBuildGoodsDocVisible" width='90%' :close-on-click-modal="false" v-dialogDrag v-loading="dialogBuildGoodsDocLoading" element-loading-text="拼命加载中">
            <buildGoodsDocPage ref="buildGoodsDocPage" style="z-index:1000;" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogBuildGoodsDocVisible = false">取 消</el-button>
                </span>
            </template>
        </el-dialog>

         <!--创建任务-->
        <el-dialog :title="addTaskPageTitle"  :visible.sync="addTask" width="80%" :close-on-click-modal="false" :key="addTaskOpentime"
            element-loading-text="拼命加载中" v-dialogDrag v-loading="addTaskLoading">

            <shootingvideotaskeditfrom :key="'shootingvideotaskeditfrom'+ addTaskOpentime" ref="shootingvideotaskeditfrom"   :taskUrgencyList="addTaskUrgencyList"  :groupList="addTaskGroupList"
            :warehouselist="addTaskWarehouselist"
            :platformList="addTaskPlatformList" :islook='addTaskIslook'  :onCloseAddForm="()=>{addTask=false;onRefresh();}"></shootingvideotaskeditfrom>


            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addTask=false">取 消</el-button>
                    <my-confirm-button type="submit"  @click="addTaskOnSubmit" v-show="!addTaskIslook" />
                </span>
            </template>
        </el-dialog>

    </my-container>
</template>
<script>




    import {
        pageHotSaleGoodsBuildGoodsCodeAsync, pageHotSaleGoodsBuildGoodsCodeNewAsync,GetDataBeforeBuildGoodsMediaTask
    } from '@/api/operatemanage/productalllink/alllink'

    import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
    import { getShootOperationsGroup,getOperationsGroup,getErpUserInfoView} from '@/api/media/mediashare';
    import { rulePlatform } from "@/utils/formruletools";

    import dayjs from "dayjs";
    import cesTable from "@/components/Table/table.vue";


    import vxetablebase from "@/components/VxeTable/vxetablebase.vue";

    import { formatmoney, formatPercen, getUrlParam, platformlist, formatPlatform, formatTime, setStore, getStore, formatLinkProCode ,sendWarehouse4HotGoodsBuildGoodsDocList,ShootingVideoTaskUrgencyOptions} from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getAllWarehouse } from '@/api/inventory/warehouse'

    import buildGoodsDocPage from '@/views/operatemanage/productalllink/hotsale/hotsalegoodsbuildgoodsdoc.vue';

    import shootingvideotaskeditfrom from '@/views/media/shooting/shootingvideotaskeditfromNew'
    import inputYunhan from "@/components/Comm/inputYunhan";

    //审核状态 -2已申请失败 -1不审核通过、0已提交、1已申请、2审核通过
    const auditStates = [
        { label: "已申请失败", value: -2 },
        { label: "不审核通过", value: -1 },
        { label: "已提交", value: 0 },
        { label: "已申请", value: 1 },
        { label: "审核通过", value: 2 },
    ]

    //对样结果
    function formatAuditState(val) {
        //审核状态 -1不通过、0已提交、1已申请、2通过
        switch (val) {
            case -1:
                return "不通过";
            case 0:
                return "已提交";
            case 1:
                return "已申请";
            case 2:
                return "已申请";
        }

        return "";
    }

    //上新仓库 1义乌、3南昌昌东
    function formatForNewWarehouse(val) {
        //sendWarehouse4HotGoodsBuildGoodsDocList
        //审核状态 -1不通过、0已提交、1已申请、2通过
        var item= sendWarehouse4HotGoodsBuildGoodsDocList.find(x=>x.value==val);
        if(item && item.label)
            return item.label;

        return val;
    }

    const tableCols = [
        { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '160', sortable: 'custom',  type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '220', sortable: 'custom' },
        { istrue: true, prop: 'goodsCompeteShortName', width: '80',label: '产品简称' },
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'image' },
        { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '100', sortable: 'custom' },
        { istrue: true, prop: 'yhGoodsName', label: '商品名称', minwidth: '140', sortable: 'custom' },
        { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
        { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
        { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },
        { istrue: true, prop: 'submitTime', label: '提交时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'applyTime', label: '钉钉申请时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'auditTime', label: '钉钉审核时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'auditState', label: '状态', width: '80', sortable: 'custom', formatter: (row) => formatAuditState(row.auditState) },

        { istrue: true, prop: 'applyUserName', label: '申请人', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'auditUserName', label: '审核人', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'auditRemark', label: '审核备注', width: '80', sortable: 'custom' },

        { istrue: true, prop: 'costPrice', label: '成本价', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'forNewWarehouse', label: '上新仓库', width: '80', sortable: 'custom', formatter: (row) => row.forNewWarehouseName},

        { istrue: true, prop: 'goodsProgressType', label: '商品类型', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'yyGroupName', label: '运营组', width: '80', sortable: 'custom' },

        { istrue: true, prop: 'isMainSale', label: '是否主卖', width: '80', sortable: 'custom', formatter: (row) => (row.isMainSale ? '是' : '否') },
        { istrue: true, prop: 'estimateStockInCount', label: '预计进货数量', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'estimateStockInAmount', label: '预计进货金额', width: '120', sortable: 'custom' },
        { istrue: true, prop: 'remark', label: '备注', width: '80', sortable: 'custom' },

    ];
    const tableColsTree = [
        { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '160', sortable: 'custom',
        treeNode:true, fixed:'left',
         type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) },
        { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '220', sortable: 'custom' ,fixed:'left'},
        { istrue: true, prop: 'goodsCompeteShortName', width: '80',label: '产品简称' },
        { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图片', width: '80', type: 'images' ,align:'center'},
        { istrue: true, prop: 'yhGoodsCode', label: '商品编码', width: '100', sortable: false },
        { istrue: true, prop: 'yhGoodsName', label: '商品名称', minwidth: '140', sortable: false },
        { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
        { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
        { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },
        { istrue: true, prop: 'submitTime', label: '提交时间', width: '150', sortable: false },
        { istrue: true, prop: 'applyTime', label: '钉钉申请时间', width: '150', sortable: false },
        { istrue: true, prop: 'auditTime', label: '钉钉审核时间', width: '150', sortable: false },
        { istrue: true, prop: 'auditState', label: '状态', width: '80', sortable: false, formatter: (row) => formatAuditState(row.auditState) },

        { istrue: true, prop: 'applyUserName', label: '申请人', width: '80', sortable: false },
        { istrue: true, prop: 'auditUserName', label: '审核人', width: '80', sortable: false },
        { istrue: true, prop: 'auditRemark', label: '审核备注', width: '80', sortable: false },

        { istrue: true, prop: 'costPrice', label: '成本价', width: '80', sortable: false },
        { istrue: true, prop: 'forNewWarehouse', label: '上新仓库', width: '80', sortable: false, formatter: (row) => row.forNewWarehouseName },

        { istrue: true, prop: 'goodsProgressType', label: '商品类型', width: '80', sortable: false },
        { istrue: true, prop: 'yyGroupName', label: '运营组', width: '80', sortable: false },

        { istrue: true, prop: 'isMainSale', label: '是否主卖', width: '80', sortable: false, formatter: (row) => row.isMainSale == undefined || row.isMainSale == null ? "" : (row.isMainSale ? '是' : '否') },
        { istrue: true, prop: 'estimateStockInCount', label: '预计进货数量', width: '120', sortable: false },
        { istrue: true, prop: 'estimateStockInAmount', label: '预计进货金额', width: '120', sortable: false },
        { istrue: true, prop: 'remark', label: '备注', width: '100', sortable: false },

    ];

    const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");


    const taskPageParams={
        addTaskPageTitle:"创建任务",
        addTask:false,
        addTaskOpentime:0,
        addTaskLoading:false,
        addTaskUrgencyList:ShootingVideoTaskUrgencyOptions,
        addTaskGroupList:[],
        addTaskWarehouselist:[],
        addTaskPlatformList:[],
        addTaskIslook:false,
    };

    export default {
        name: "hotsalegoodsbuildgoodsdoclist",
        components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,vxetablebase, buildGoodsDocPage,shootingvideotaskeditfrom, inputYunhan },
        data() {
            return {
                that: this,
                showTableTab: 1,
                Filter: {
                    auditState: null
                },
                platformlist: platformlist,
                tbdatalist: [],
                tableCols: tableCols,
                tableColsTree: tableColsTree,
                auditStates: auditStates,
                total: 0,
                summaryarry: { count_sum: 10 },
                pager: { OrderBy: "", IsAsc: false },
                sels: [], // 列表选中列
                listLoading: false,
                pageLoading: false,
                //
                selids: [],
                fileList: [],
                selfInfo: {

                },
                dialogBuildGoodsDocVisible: false,
                dialogBuildGoodsDocLoading: false,

                sendWarehouse4HotGoodsBuildGoodsDocList:sendWarehouse4HotGoodsBuildGoodsDocList,
                ...taskPageParams,
            };
        },
        async mounted() {

            const userInfoName = "hotsalegoods_selfuserinfo";
            let selfInfo4Store = getStore(userInfoName);
            if (selfInfo4Store) {
                this.selfInfo = selfInfo4Store;
            }
            this.init();
            this.onSearch();

            //拍摄任务参数
            let res_group = await getDirectorGroupList();
            this.addTaskGroupList= res_group.data?.map(item => { return { value: item.key, label: item.value }; });
            let res_warehouse=await getShootOperationsGroup({type:3});
            this.addTaskWarehouselist=res_warehouse?.map(item => { return { value: item.id, label: item.label }; });
            let pfrule = await rulePlatform();
            this.addTaskPlatformList = pfrule.options;

        },
        methods: {
            callbackGoodsCompeteId(val) {
              this.Filter.goodsCompeteId = val;
            },
            async init() {
                var res3 = await getAllWarehouse();
                this.fomSendWarehouse4HotGoodsBuildGoodsDocList = res3.data;
            },
            async onOpenAddTask(row,mode){

                let bfData=await GetDataBeforeBuildGoodsMediaTask({chooseId:(!row.isGoodsCompete? row.hotSaleGoodsChooseId: row.id )    });
                if(bfData && bfData.errMsg){
                    this.$alert(bfData.errMsg);
                    return ;
                }

                this.addTaskLoading =true;
                this.addTaskOpentime = this.addTaskOpentime +1;
                this.addTask = true;
                this.addTaskPageTitle = "创建任务";
                this.addTaskIslook = false;
                this.addTaskLoading =false;
                this.$nextTick(()=>{
                    this.$refs.shootingvideotaskeditfrom.initaddform({
                        productShortName:(!row.isGoodsCompete? row.goodsCompeteShortName: row.docDtls[0].goodsCompeteShortName ),
                        platform:row.platform,
                        operationGroup:(this.selfInfo && this.selfInfo.groupId ? this.selfInfo.groupId.toString():null ),
                        dockingPeople:(this.selfInfo && this.selfInfo.nickName ? this.selfInfo.nickName:null ),
                        extBzTypeArgs:{
                            extBzType:'建编码',
                            extBzIdOne:(!row.isGoodsCompete? row.hotSaleGoodsChooseId: row.id ) ,
                            extBzInWarehouse: bfData.warehouseCount>0
                        }

                    });
                });
            },
            async addTaskOnSubmit(){
                this.addTaskLoading =true;
                await  this.$nextTick(function () {
                    this.$refs.shootingvideotaskeditfrom.onSubmit();
                });
                this.addTaskLoading =false;

            },
            showGoodsDocDtl(row) {
                this.buildGoodsDocHiddle = true;
                this.dialogBuildGoodsDocVisible = true;
                this.$nextTick(() => {
                    this.$refs.buildGoodsDocPage.getSkuTableData(row.parentId, false);
                });
            },
            sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderField = column.prop;
                    var bFields = [];// ['skuDataState', 'skuDataTime', 'cmRefInfoState', 'cmRefInfoLastOkTime'];
                    if (orderField == "orgPlatformName") {
                        orderField = "PlatformName";
                    } else if (bFields.indexOf(orderField) > -1) {
                        orderField = "b." + orderField;
                    }

                    this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                }
                this.onSearch();
            },
            onRefresh() {
                this.onSearch()
            },
            onSearch() {
                this.$refs.pager.setPage(1);
                this.gettbdatalist1();
            },
            async gettbdatalist1() {
                if (this.Filter.gDate) {
                    this.Filter.startGDate = this.Filter.gDate[0];
                    this.Filter.endGDate = this.Filter.gDate[1];
                }
                else {
                    this.Filter.startGDate = null;
                    this.Filter.endGDate = null;

                }
                const para = { ...this.Filter };
                let pager = this.$refs.pager.getPager();

                //共用排序条件，当列不支持排序时，自动去掉排序 。
                let sortInfo = { ...this.pager }
                if (this.showTableTab == 1 && sortInfo.OrderBy) {
                    if (["goodsCompeteId", "goodsCompeteName"].indexOf(sortInfo.OrderBy) < 0) {
                        sortInfo.OrderBy = "";
                    }
                }

                const params = {
                    ...pager,
                    ...sortInfo,
                    ...para,
                };

                this.listLoading = true;
                const res = this.showTableTab == 1
                    ? await pageHotSaleGoodsBuildGoodsCodeNewAsync(params)
                    : await pageHotSaleGoodsBuildGoodsCodeAsync(params);

                this.listLoading = false;

                this.total = res.data.total

                let dtList=[];
                res.data.list.forEach(x=>{
                    dtList.push(x);
                    if(x.docDtls && x.docDtls.length>0){
                        dtList=dtList.concat([...x.docDtls]);
                    }
                })

                this.tbdatalist = dtList;

            },

            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
<style >
    .el-image__inner {
        max-width: 50px;
        max-height: 50px;
    }
</style>
