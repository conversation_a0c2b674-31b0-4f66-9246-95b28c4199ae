<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
          value-format="yyyyMM" placeholder="选择月份">
        </el-date-picker>
        <el-select filterable clearable v-model="ListInfo.shopCode" placeholder="所属店铺" class="publicCss">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
            :value="item.shopCode"></el-option>
        </el-select>
        <el-button style="padding: 0;margin: 0;">
            <el-input v-model="ListInfo.transactionRecordNumber" placeholder="交易流水号" style="width:180px;" clearable></el-input>
        </el-button>
        <el-button style="padding: 0;margin: 0;">
            <el-input v-model="ListInfo.orderNumber" placeholder="订单号" style="width:180px;" clearable></el-input>
        </el-button>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button type="primary" @click="onExport">汇总导出</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'kuaishoubaozhengjin202507211356'" :tablekey="'kuaishoubaozhengjin202507211356'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <div class="upload-section">
        <div class="upload-row">
          <label class="required-label">
            <span class="required-mark">*</span> 月份选择：
          </label>
          <el-date-picker class="upload-month" v-model="yearMonth" type="month" format="yyyyMM" value-format="yyyyMM"
            placeholder="请选择月份" :clearable="false" />
        </div>
        <div class="upload-row">
          <label class="required-label">
            <span class="required-mark">*</span> 是否最后文件：
          </label>
          <el-select v-model="isLast" placeholder="请选择">
            <el-option key="true" label="是" value="true"></el-option>
            <el-option key="false" label="否" value="false"></el-option>
          </el-select>
        </div>
        <div class="upload-row">
          <label class="required-label">
            <span class="required-mark">*</span> 店铺名称：
          </label>
          <el-input v-model="shopName" placeholder="请输入店铺名称"></el-input>
        </div>

        <div class="upload-row">
          <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" class="upload-btn">
              {{ uploadLoading ? '上传中' : '上传' }}
            </el-button>
          </el-upload>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from 'dayjs'
import { getFinancialDetail_ReturnFreight_KS_Bond, ExportFinancialDetail_ReturnFreight_KS_Bond, ImportKsBondAsync } from '@/api/monthbookkeeper/financialDetail'
import { getList as getshopList } from '@/api/operatemanage/base/shop';

const tableCols = [
  { sortable: 'custom', width: '200', align: 'center', prop: 'shopCode', label: '店铺', formatter: (row) => row.shopName },
  { sortable: 'custom', width: '160', align: 'center', prop: 'orderCreatedTime', label: '订单创建时间', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'accountType', label: '账户类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'operationType', label: '操作类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'amount', label: '金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'transactionStatus', label: '交易状态', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'completionTime', label: '完成时间', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'transactionDescription', label: '交易说明', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'transactionRecordNumber', label: '交易流水单号', },
  { sortable: 'custom', width: '160', align: 'center', prop: 'orderNumber', label: '订单号'}
]
export default {
  name: "KuaiShouBond",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      shopList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        transactionRecordNumber:null,//交易流水号
        platform: 14,
        yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),
        orderNumber: null,
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),
      isLast: 'false',
      shopName: '',
      dialogVisible: false,
      uploadLoading: false,
    }
  },
  async mounted() {
    const res1 = await getshopList({ platform: 14, CurrentPage: 1, PageSize: 100000 });
    this.shopList = res1.data.list
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getFinancialDetail_ReturnFreight_KS_Bond(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
      }
      this.loading = false
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async onExport(opt) {
      if (!this.ListInfo.yearMonth) {
        this.$message({ message: "请先选择月份", type: "warning" });
        return;
      }
      const params = { ...this.ListInfo, ...opt };
      let res = await ExportFinancialDetail_ReturnFreight_KS_Bond(params);
      if (!res?.data) {
        return
      }
    },




     //导入弹窗
    startImport() {
      this.fileList = []
      this.yearMonthDay = dayjs().subtract(1, 'month').format('YYYYMM')
      this.isLast = 'false'
      this.shopName = ''
      this.dialogVisible = true;
    },
    //移除文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    //上传文件
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    //提交验证
    onSubmitUpload() {
      if (!this.yearMonthDay) {
        this.$message({ message: "请选择日期", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //上传文件
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("yearMonth", this.yearMonth);
      form.append("isLast", this.isLast);
      form.append("shopName", this.shopName)
      var res = await ImportKsBondAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
    },
    //上传文件成功后
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}
</style>
