<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent
                v-if="buttonshow == true">
                <el-form-item label="标题:">
                    <el-input v-model.trim="filter.title" placeholder="标题" style="width: 150px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="创建人:">
                    <el-input v-model.trim="filter.CreatedUserName" placeholder="创建人" style="width: 100px"
                        @keyup.enter.native="onSearch" clearable />
                </el-form-item>
                <el-form-item label="创建时间:">
                    <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" end-placeholder="结束">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <table-btn ref="table" :isSelectColumn='isSelectColumn' :that='that' :isIndex='true' :hasexpand='true'
            @sortchange='sortchange' @select='selectchange' :isSelection='false' :tableData='list'
            :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" :buttonshow='buttonshow'
            @onHand='onHand' @onUplod='onUplod' @deleteLog='deleteLog' :whichmodules="whichmodules" @orderTopdown='orderTopdown'
            :customRowStyle="customRowStyle">
        </table-btn>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-drawer :title="formtitle" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
            :visible.sync="handVisible" direction="btt" size="'auto'" class="el-drawer__wrapper"
            style="position:absolute">
            <div class="block table-wrapper" style="height:80%;padding:15px;">
                <el-scrollbar>
                    <el-card v-if="(handtype==2)" style="padding:1px;">
                        <el-descriptions :column="4" size="mini">
                            <el-descriptions-item :span="4" label="标题">{{reportsingle.title }}</el-descriptions-item>
                            <el-descriptions-item :span="4" label="更新内容">
                                <div style="margin-top:-10px" v-html="reportsingle.content">{{reportsingle.content}}
                                </div>
                            </el-descriptions-item>
                            <el-descriptions-item label="创建人">{{reportsingle.createdUserName}}</el-descriptions-item>
                            <el-descriptions-item label="创建时间">{{reportsingle.createdTime}}</el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                    <div v-if="handtype!=2">
                        <el-card>
                            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options" />
                        </el-card>
                    </div>
                    <div class="drawer-footer">
                        <!-- <el-button @click.native="handVisible = false">取消</el-button> -->
                        <el-button @click.native="handVisible = false">取消</el-button>
                        <my-confirm-button type="submit" :loading="handLoading" @click="onSubmit()"
                            v-if="handtype!=4" />
                    </div>
                </el-scrollbar>
            </div>
        </el-drawer>

        <!--上传片段-->
        <el-dialog title="视频上传" :visible.sync="uploadpdVisibleSyj" width="40%" :v-loading="ScpdLoading"
            @close="closedialogVisibleScpd">
            <el-progress   v-if="showProgress" :text-inside="true" :stroke-width="26" :percentage="percentage"></el-progress>
            <span>
                <el-upload ref="upload3" class="upload-demo" action :auto-upload="false" :http-request="uploadpdFile"
                    :on-success="uploadpdSuccess" :limit="1" :file-list="SypdjfileList">
                    <template #trigger>
                        <el-button size="small" :loading="ScpdLoading" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success" :loading="ScpdLoading"  @click="onPdSubmitupload">
                        上传</my-confirm-button>
                </el-upload> 
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closedialogVisibleScpd">关闭</el-button>
            </span>
        </el-dialog>


    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import formCreate from '@form-create/element-ui'
import FcEditor from "@form-create/component-wangeditor";
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import tableBtn from "@/views/bookkeeper/reportday/crossBorderHomepage/components/tablebtn.vue";
import { uploadYYFileVideo, upLoadImage } from '@/api/upload/file'
import { getPurchaseLogModuleAsync ,getPurchaseLogOneAsync,addPurchaseLog,deletePurchaseLog,updateSortPurchaseLogOneAsync,addPurchaseUplod} from '@/api/bookkeeper/crossBorderV2'



import { getCurrentUserAsync } from '@/api/admin/user'
import { uploadYYFileVideoBlockAsync } from '@/api/upload/filenew'

const tableColsBrand = [
    { istrue: true, prop: 'title', label: '标题', width: '225' },
    // {istrue:false, prop:'content',label:'更新内容', type:'editor',width:'400'},
    { istrue: true, prop: 'videoUrl', label: '视频', type: 'html', width: '50', formatter: (row) => { return row.videoUrl.length > 0 ? '<i class="el-icon-video-play"></i>' : '' } },
    { istrue: true, prop: 'createdUserName', label: '创建人', width: '60' },
    // { istrue: true, prop: 'createdTime', label: '创建时间', width: '100', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') },
];
const tableColsGroup = [
    { istrue: true, prop: 'title', label: '标题', width: '225' },
    // {istrue:false, prop:'content',label:'更新内容', type:'editor',width:'400'},
    { istrue: true, prop: 'videoUrl', label: '视频', type: 'html', width: '100', formatter: (row) => { return row.videoUrl.length > 0 ? '<i class="el-icon-video-play"></i>' : '' } },
    { istrue: true, prop: 'createdUserName', label: '创建人', width: '150' },
    { istrue: false, prop: 'createdTime', label: '创建时间', width: '100', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') },
];
const tableHandles1 = [
    { label: "新增记录", handle: (that) => that.onHand(1) },
    // {label:'编辑记录', handle:(that)=>that.onHand(3)},
    // {label:'查看', handle:(that)=>that.onHand(2)},
];
const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunhanAdminUpdatelog',
    components: { tableBtn, MyContainer, MyConfirmButton },
    data() {
        return {
            buttonshow: false,
            isSelectColumn: false,
            that: this,
            filter: {
                title: '',
                people: '',
                timerange: [],
                startDate: null,
                endDate: null,
            },
            list: [],
            pager: { OrderBy: "id", IsAsc: false },
            tableCols: tableColsBrand,
            tableHandles: tableHandles1,
            uploadfilelist: [],
            fileList: [],
            uploadimagelist: [],
            imageList: [],
            addrows:{},
            handtype: 1,
            formtitle: null,
            autoform: {
                fApi: {},
                options: {
                    submitBtn: false, global: {
                        '*': { props: { disabled: false }, col: { span: 16 } },
                        upload: { props: { onError: function (r) { alert('上传失败') } } }
                    }
                },
                rule: []
            },
            total: 0,
            sels: [],
            SypdjfileList: [],
            ScpdLoading: false,
            listLoading: false,
            pageLoading: false,
            handVisible: false,
            handLoading: false,
            uploadpdVisibleSyj: false,
            showProgress: false,
            reportsingle: { processList: [] },
            collapseactiveName: '1',
            customRowStyle: function (data) {
                if (!data.row.userId && !data.row.userId > 0) {
                    return { color: 'red' };
                }
            },
        };
    },
    props: {
        dept: {
            type: String,
            default: 'Brand'
        },
        whichmodules: {
            type: String,
            default: true
        },
        clickoutin: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        clickshow() {
            return this.clickoutin;
        },
        // module() {
        //   return this.whichmodules;
        // }
    },
    watch: {
        clickshow(data) {
            console.log("模块显示", this.whichmodules);
            console.log("监听展开", data)
            this.isSelectColumn = data;
            this.buttonshow = data;

            var array = this.tableCols;
            console.log("原数组", array);

            let newItem = { istrue: true, prop: 'title', label: '标题', width: '225' };
            let newItemm = { istrue: true, prop: 'title', label: '标题', width: 'auto' };
            // let content = {istrue:true, prop:'content',label:'更新内容', type:'editor',width:'400'};
            // let contentt = {istrue:false, prop:'content',label:'更新内容', type:'editor',width:'400'};
            let newtime =  { istrue: true, prop: 'createdTime', label: '创建时间', width: '100', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') };
            let newtimee =  { istrue: false, prop: 'createdTime', label: '创建时间', width: '100', formatter: (row) => formatTime(row.createdTime, 'YYYY-MM-DD') };



            if (this.dept != "Group") {
                if (data == true) {
                    var list = array.map(t => {
                        return t.label === newItemm.label ? newItemm : t;
                    });
                    this.tableCols = list;
                    console.log("改变后数组", list);
                } else {
                    var list = array.map(t => {
                        return t.label === newItemm.label ? newItem : t;
                    });
                    this.tableCols = list;
                    console.log("改变后数组", list);
                }
            }else{
                if (data == true) {
                    var list = array.map(t => {
                        t.label === newItemm.label ? t.width = newItemm.width : t;
                        t.label === newtime.label ? t.istrue = newtime.istrue : t;
                        return t
                    });
                    this.tableCols = list;
                } else {
                    var list = array.map(t => {
                        t.label === newItemm.label ? t.width = newItem.width : t;
                        t.label === newtimee.label ? t.istrue = newtimee.istrue : t;
                        return t;
                    });
                    this.tableCols = list;
                }
            }
        },
        // module(data){
        //   return
        // }
    },
    async mounted() {
        if (this.dept == "Group") {
            this.tableCols = tableColsGroup;
        }
        formCreate.component('editor', FcEditor);
        await this.onSearch();
    },
    methods: {
        async getLoginInfo() {
            const res = await getCurrentUserAsync();
            if (!res?.Success) {
                return
            }
            this.loginInfo = res.data;

        },
        async initform() {
            let that = this
            this.autoform.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '' },
            { type: 'input', field: 'title', title: '标题', value: '', col: { span: 24 }, validate: [{ type: 'string', required: true, message: '请输入标题' }] },
            {
                type: 'editor', field: 'content', title: '更新内容', value: '', col: { span: 24 }, validate: [{ type: 'string', required: true, message: '必填' }],
                props: { init: async (editor) => { await that.initeditor(editor) } }
            }]
            this.autoform.rule.forEach(f => {
                if (f.field == 'toUserId1') f.validate = []
                if (f.field == 'toUserId2') f.validate = []
            })
        },
        async onSearch(data) {
            this.listLoading = true
            await this.getlist();
            this.listLoading = false
        },
        //请求参数
        getCondition() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                //   this.$message({message:"请先选择日期",type:"warning"});
                //   return false;
            }
            //this.filter.platform=2
            var pager = this.$refs.pager.getPager();
            var page = this.pager;

            // var module  = this.whichmodules;

            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            params.module = this.whichmodules;
            params.dept = this.dept;

            console.log("四表看板最终参数", params);
            return params;
        },
        //请求列表数据//
        async getlist() {
            this.listLoading = true

            var params = this.getCondition();
            if (params === false) {
                return;
            }

            const res = await getPurchaseLogModuleAsync(params)
            console.log("测试数据", res);
            // const res = await getLogTimeAsync(params)
            this.listLoading = false
            if (!res?.code) {
                return
            }
            this.total = res.data.total
            const data = res.data.list
            data.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        //删除数据
        async deleteLog(data) {
            console.log("父删除更新信息", data.id);
            const params = {
                id: data.id,
            }
            this.listLoading = true
            const res = await deletePurchaseLog(params);
            // (res?.code)? await this.getlist() : this.$message.warning('权限错误');
            if (res?.code) {
                await this.getlist()
            }
            this.listLoading = false
            console.log('回调参数', res);
        },
        //排序
        async orderTopdown(index, row, updown) {
            if (index == 1 && updown == 1) {
                this.$message.warning('已经移动到最顶端');
            } else if (index == this.list.length && updown == 0) {
                this.$message.warning('已经移动到最末端');
            } else {
                const params = {
                    sort: row.sort,
                    id: row.id,
                    sortid: updown,
                    module: this.whichmodules,
                    dept: this.dept
                }
                this.listLoading = true
                const res = await updateSortPurchaseLogOneAsync(params)
                console.log("排序回调", res);
                // (res?.code)? await this.getlist() : this.$message.warning('权限错误');
                if (res?.code) {
                    await this.getlist()
                }
                this.listLoading = false
            }
        },
        //
        async onHand(arr) {
            if (arr == 1) {
                var type = 1;
            } else {
                var type = arr[0];
                let array = [arr[1]];
                this.selids = [];
                array.forEach(f => {
                    this.selids.push(f.id);
                })
            }
            // if (type>1&&(!this.selids||this.selids.length==0)){
            //     this.$message.warning("请先选择")
            //     return
            // }
            // else if ((type==3 || type == 2) &&(!this.selids||this.selids.length>1)){
            //     this.$message.warning("只能选择1行")
            //     return
            // }
            this.handVisible = true
            this.handtype = type
            var res;
            if (type == 1) {
                this.formtitle = '新增更新记录'
                await this.initform()
            }
            else if (type == 2) {
                var reportid = this.selids[0]
                res = await getPurchaseLogOneAsync({ id: reportid })
                if (res.code == 1) {
                    this.reportsingle = res.data
                }
            }
            else if (type == 3) {
                this.formtitle = '编辑更新记录'
                await this.initform()
                var reportid = this.selids[0]
                console.log("排序", this.selids);
                res = await getPurchaseLogOneAsync({ id: reportid })
                if (res.code == 1) {
                    var model = { id: reportid, title: res.data ? res.data.title : '', content: res.data ? res.data.content : '', videoUrl: res.data.videoUrl }
                    this.$nextTick(async () => {
                        var arr = Object.keys(this.autoform.fApi)
                        if (arr.length > 0)
                            await this.autoform.fApi.resetFields()
                        await this.autoform.fApi.setValue(model)
                    });

                }
            }

        },
        async onUplod(arr){
            this.addrows = arr[1];
            this.uploadpdVisibleSyj = true
        },
        //上传片段
        async uploadpdFile(item) {
            this.ScpdLoading = true;
            this.atfterUplaodData = null;
            this.percentage = 0;
            this.showProgress=true;
            await this.AjaxFile(item.file, 0,"");
            if(this.atfterUplaodData !=null)
            {
                await this.AfteruploadpdFile();
            }
            this.showProgress = false;
            this.ScpdLoading = false;
        },
        async AjaxFile(file,i,batchnumber) {
         
         var name = file.name; //文件名
         var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
         var shardSize = 2 * 1024 * 1024;
         var shardCount = Math.ceil(size / shardSize); //总片数
         if (i >= shardCount) {
             return;
         }
         //计算每一片的起始与结束位置
         var start = i * shardSize;
         var end = Math.min(size, start + shardSize);
         //构造一个表单，FormData是HTML5新增的
         i=i+1;
         var form = new FormData();
         form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
         form.append("batchnumber", batchnumber);
         form.append("fileName", name);
         form.append("total", shardCount); //总片数
         form.append("index", i); //当前是第几片
      
         const res = await uploadYYFileVideoBlockAsync(form);
         if (res?.success) {
             this.percentage = (i*100/shardCount).toFixed(2) ;
         
             if(i == shardCount){
                 this.atfterUplaodData = res.data;
             }else{
                await this.AjaxFile(file, i,res.data);
             }
         }else{
             this.$message({ message: res?.msg, type: "warning" });
             this.ScpdLoading = false;
           
         }
        },
        async AfteruploadpdFile(){
            var para = {id: this.addrows.id, videoUrl: this.atfterUplaodData.url}

             const res = await addPurchaseUplod(para);
             if (res?.success) {
                 this.$message({ message: '上传成功', type: "success" });
                 this.listLoading = true
                 await this.getlist();
                 this.listLoading = false
             } else {
                this.$message({ message: '上传失败', type: "warning" });
            }
            this.percentage = 0;
            this.showProgress = false;
            this.uploadpdVisibleSyj = false;
            
        },
        async uploadpdSuccess(response, file, fileList) {
            this.SypdjfileList.splice(fileList.indexOf(file), 1);

        },
        async onPdSubmitupload() {
            this.$refs.upload3.submit();
        },
        closedialogVisibleScpd() {
            this.uploadpdVisibleSyj = false;
            this.SypdjfileList = [];
        },
        async initeditor(editor) {
            editor.config.uploadImgMaxSize = 3 * 1024 * 1024
            editor.config.excludeMenus = ['emoticon', 'video']
            // editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            editor.config.uploadImgAccept = []
            // editor.customConfig.debug = true;
            editor.config.customUploadImg = async function (resultFiles, insertImgFn) {
                // console.log('resultFiles', resultFiles[0])
                // const form = new FormData();
                // form.append("file", resultFiles[0]);
                // const res = await uploadCommonFile(form);
                // var url = `${res.data}`
                // console.log('url', url)
                // insertImgFn(url)

                var xhr = new XMLHttpRequest()
                var formData = new FormData()
                formData.append('file', resultFiles[0])
                xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
                xhr.withCredentials = true
                xhr.responseType = 'json'
                xhr.send(formData)
                xhr.onreadystatechange = () => {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                         console.log('url', xhr.response.data.url)
                         insertImgFn(xhr.response.data.url)
                    }
                }
            }
        },
        async onSubmit() {
            const formData = this.autoform.fApi.formData();
            formData.reportId = this.reportsingle.id;
            let data = this.autoform.fApi.form;
            console.log("打印下信息",data);
            if(data.content=='<p><br/></p>'){
                this.$message({ message: '内容不能为空', type: "warning" });
                return;
            }
            await this.autoform.fApi.validate(async (valid, fail) => {

                if (valid) {
                    this.handLoading = true
                    const formData = this.autoform.fApi.formData();
                    console.log("提交事件", formData);
                    formData.id = formData.id ? formData.id : 0;
                    formData.dept = this.dept;
                    formData.Enabled = true;
                    var res;

                    if (this.handtype == 1) {
                        formData.module = this.whichmodules;
                        res = await addPurchaseLog(formData)
                        console.log("打印添加返回数据", res);
                        this.handLoading = false
                    }
                    else if (this.handtype == 3) {
                        formData.module = this.whichmodules;
                        console.log("打印编辑数据", formData);
                        res = await addPurchaseLog(formData)
                        this.handLoading = false
                    }
                    if (res?.code) {
                        await this.getlist()
                        this.handVisible = false
                        this.handLoading = false
                    }
                }
                else {
                    this.handLoading = false
                }
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            // console.log('打印选择',rows);
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>
