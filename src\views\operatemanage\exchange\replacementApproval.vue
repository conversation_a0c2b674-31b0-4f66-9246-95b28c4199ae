<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <dateRange :startDate.sync="ListInfo.payStartDate" :endDate.sync="ListInfo.payEndDate"
          :startPlaceholder="'支付开始时间'" :endPlaceholder="'支付结束时间'" class="publicCss" />
        <dateRange :startDate.sync="ListInfo.approveStartDate" :endDate.sync="ListInfo.approveEndDate"
          :startPlaceholder="'审批开始时间'" :endPlaceholder="'审批结束时间'" class="publicCss" />
        <dateRange :startDate.sync="ListInfo.planSendStartDate" :endDate.sync="ListInfo.planSendEndDate"
          :startPlaceholder="'计划发货时间'" :endPlaceholder="'计划发货时间'" class="publicCss" />
        <el-select v-model="ListInfo.approveStatus" placeholder="状态" clearable filterable class="publicCss">
          <el-option key="待审批" label="待审批" value="待审批"></el-option>
          <el-option key="同意" label="同意" value="同意"></el-option>
          <el-option key="拒绝" label="拒绝" value="拒绝"></el-option>
        </el-select>
        <inputYunhan ref="orderNoOnlines" :inputt.sync="ListInfo.orderNoOnlines" v-model="ListInfo.orderNoOnlines"
          placeholder="线上单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000"
          @callback="inputCallback($event, 1)" title="线上单号" style="margin:0 10px 0 0;" width="200px">
        </inputYunhan>
        <inputYunhan ref="orderNoInners" :inputt.sync="ListInfo.orderNoInners" v-model="ListInfo.orderNoInners"
          placeholder="内部订单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000"
          @callback="inputCallback($event, 2)" title="内部订单号" style="margin:0 10px 0 0;" width="200px">
        </inputYunhan>
        <inputYunhan ref="proCodes" :inputt.sync="ListInfo.proCodes" v-model="ListInfo.proCodes"
          placeholder="产品ID/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000"
          @callback="inputCallback($event, 3)" title="产品ID" style="margin:0 10px 0 0;" width="200px">
        </inputYunhan>
        <el-select v-model="ListInfo.groupIds" placeholder="运营组" class="publicCss" filterable clearable multiple
          collapse-tags>
          <el-option v-for="item in groupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <inputYunhan ref="oldGoodCodes" :inputt.sync="ListInfo.oldGoodCodes" v-model="ListInfo.oldGoodCodes"
          placeholder="原商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000"
          @callback="inputCallback($event, 4)" title="原商品编码" style="margin:0 10px 0 0;" width="200px">
        </inputYunhan>
        <inputYunhan ref="newGoodCodes" :inputt.sync="ListInfo.newGoodCodes" v-model="ListInfo.newGoodCodes"
          placeholder="新商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000"
          @callback="inputCallback($event, 5)" title="新商品编码" style="margin:0 10px 0 0;" width="200px">
        </inputYunhan>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="新商品名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.approver" placeholder="审批人" maxlength="50" clearable class="publicCss" />
        <div>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
          <el-button type="primary" size="mini" @click="onSetup">流程设置</el-button>
        </div>
      </div>
    </template>
    <vxetablebase :id="'replacementApproval202410291350'" :tablekey="'replacementApproval202410291350'" ref="table"
      :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true" :border="true"
      :table-data="data.list" :table-cols="tableCols" :is-selection="false" :is-select-column="true"
      :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
      :summaryarry="data.summary" @sortchange="sortchange" :showoverflow="false" @cellStyle="cellStyle" cellStyle />
    <template #footer>
      <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :visible.sync="dailyNewspaperToolbar" width="70%" v-dialogDrag style="margin-top: -5vh;">
      <div style="height: 650px;">
        <processApprovalIndex ref="refprocessSettings" v-if="dailyNewspaperToolbar"
          @close="dailyNewspaperToolbar = false" sceneName="ExchangeGoods" />
      </div>
      <!-- <div style="display: flex;justify-content: center;gap: 10px;">
        <el-button @click="dailyNewspaperToolbar = false">关闭</el-button>
        <el-button type="primary" @click="onAddProcess">确定</el-button>
      </div> -->
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import processSettings from '@/views/base/processSettings.vue'
import processApprovalIndex from '@/views/inventory/approvalSetting/processApprovalIndex.vue'
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/operatemanage/ExchangeGoods/'
export default {
  name: "replacementApproval",
  components: {
    MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan, processSettings, processApprovalIndex
  },
  data() {
    return {
      api,
      groupList: [],
      platformlist,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: '',
        isAsc: false,
        summarys: [],
      },
      dailyNewspaperToolbar: false,
      data: {},
      chatProp: {
        chatDialog: false, // 趋势图弹窗
        chatTime: null, // 趋势图时间
        chatLoading: true, // 趋势图loading
        data: [], // 趋势图数据
      },
      timeRanges: [],
      tableCols: [],
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false
    }
  },
  async mounted() {
    await this.getCol();
    await this.getList()
    await this.init()
  },
  methods: {
    onAddProcess() {
      this.$refs.refprocessSettings.addProcessMethod()
    },
    onSetup() {
      this.dailyNewspaperToolbar = true
    },
    async cellStyle(row, column, callback) {
      callback({ paddingLeft: '4px' })
    },
    async init() {
      const res = await getGroupKeyValue({});
      this.groupList = res.data;
    },
    //输入回调
    inputCallback(val, type) {
      if (type == 1) {
        this.ListInfo.orderNoOnlines = val
      } else if (type == 2) {
        this.ListInfo.orderNoInners = val
      } else if (type == 3) {
        this.ListInfo.proCodes = val
      } else if (type == 4) {
        this.ListInfo.oldGoodCodes = val
      } else if (type == 5) {
        this.ListInfo.newGoodCodes = val
      }
    },
    // 导出数据
    async exportProps() {
      this.isExport = true
      await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
        this.isExport = false
      })
    },
    async getCol() {
      const { data, success } = await request.post(`${this.api}GetColumns`)
      if (success) {
        data.forEach(item => {
          if (item.prop == 'orderNoInner') {
            item.type = 'orderLogInfo'
            item.orderType = 'orderNoInner'
          }
          if (item.prop == 'proCode') {
            item.type = 'html'
            item.formatter = (row) => formatLinkProCode(2, row.proCode)
          }
        })
        this.tableCols = data;
        this.ListInfo.summarys = data
          .filter((a) => a.summaryType)
          .map((a) => {
            return { column: a["sort-by"], summaryType: a.summaryType };
          });
      }
    },
    async getList(type) {
      if (type === "search") {
        this.ListInfo.currentPage = 1;
        this.$refs.pager.setPage(1);
      }
      this.loading = true;
      try {
        const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
        if (success) {
          this.data = data;
        } else {
          this.$message.error("获取列表失败");
        }
      } catch (error) {
        this.$message.error("获取列表失败");
      } finally {
        this.loading = false;
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;
}

.publicCss {
  width: 205px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.containerCss {
  padding: 10px 0 0 5px;
  height: 100%;
}
</style>
