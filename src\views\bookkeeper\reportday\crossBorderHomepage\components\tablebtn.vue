<template>
	<div id="mainmidle" :style="[{height:(isSelectColumn==true?'95%':'99%')},{width:'100%'},{'margin':' 0'}]">
		<el-button type="primary" v-if="buttonshow" @click.stop="addEdit(1)">添加记录</el-button>
		<!-- row-key="id" -->
		<el-table :data='tableData' class="table-wrapper" :height="tableHeight" :border='isBorder' @select='select'
			@select-all='selectAll' @sort-change='sortchange' @cell-click='cellclick' @expand-change='expandchange'
			v-loading='loading' :tree-props='treeprops' :show-summary='showsummary' :summary-method="getSummaries"
			:defaultSelections='defaultSelections' :row-style='customRowStyle' highlight-current-row stripe
			:header-cell-style='headerCellStyle' ref="cesTable">
			<template v-if="hasexpand">
				<slot />
			</template>
			<el-table-column v-if="isSelection" type="selection" align="center" width="30" :fixed="tablefixed">
			</el-table-column>
			<el-table-column v-if="isIndex" type="index" :label="indexLabel" align="center" width="40" :fixed="tablefixed">
			</el-table-column>
			<template v-for="(item, index) in childtableCols">
				<template v-if="item.istrue">
					<cescolumnmerge v-if="item.merge &&(!item.permission||(item.permission&&checkPermission(item.permission)))"
						:that="that" :key="index" :column="item" :size="size" :type="type" :descData="descData" />
					<cescolumn v-else :that="that" :key="index" :column="item" :size="size" :type="type" :descData="descData"
						@previewImageGoodsCode="previewImageGoodsCode" @showImg="showImg" @preview="preview" />
				</template>
			</template>

			<!-- //////////// -->
			<el-table-column label="操作" v-if="buttonshow " width="370">
				<template slot-scope="scope">
					<el-button size="mini" type="primary" icon="el-icon-upload" @click.stop="handleUplod(scope.$index, scope.row)">视频上传
					</el-button>
					<el-button size="mini" type="primary" icon="el-icon-edit" @click.stop="handleEdit(scope.$index, scope.row)">编辑
					</el-button>
					<!-- <el-button size="mini" type="primary" @click="handleSee(scope.$index, scope.row)">查看</el-button> -->
					<el-popover trigger="click" :ref="`popover-${scope.$index}`" placement="right" width="100">
						<p class="el-icon-warning">确定删除</p>
						<div style="text-align: right; margin: 0">
							<el-button size="mini" type="text" @click="doClose(scope)">取消</el-button>
							<el-button type="primary" size="mini" @click="doDelete(scope,scope.row)">确定</el-button>
						</div>
						<!-- <span slot="reference">删除</span> -->
						<el-button slot="reference" type="danger" @click.stop="doShow(scope.$index, scope.row,0)">删除</el-button>
					</el-popover>
					<!-- <el-button size="mini" type="danger" @click.stop="handleDelete(scope.$index, scope.row)">删除</el-button> -->
					<el-button type="primary" style="margin-left: 10px;" icon="el-icon-top"
						@click.stop="sortabledata(scope.$index, scope.row,1)"></el-button>
					<el-button type="primary" icon="el-icon-bottom" @click.stop="sortabledata(scope.$index, scope.row,0)">
					</el-button>
					<!-- <el-button type="primary" icon="el-icon-bottom"  @click="sortabledata(scope.$index, scope.row, sortid=0)"></el-button> -->

				</template>
			</el-table-column>
			<!-- //////// -->

		</el-table>
		<el-dialog :visible.sync="showImage" :modal="false" :show-close="false" :width="ImgWith" v-dialogDrag>
			<img ref="imgdialog" :src="imgList[0]" />
		</el-dialog>
		<el-image-viewer v-if="showGoodsImage" :url-list="imgList" :on-close="closeFunc" style="z-index:9999;" />
		<div class="imgDolg" v-show="imgPreview.show" @click.stop="imgPreview.show = false">
			<i class="el-icon-close" id="imgDolgClose" @click.stop="imgPreview.show = false"></i>
			<img @click.stop="imgPreview.show = true" :src="imgPreview.img" />
		</div>

		<el-dialog :visible.sync="dialodayssisVisible" ref="elDialog" width="75%" v-dialogDrag :fullscreen="isfull"
			:lock-scroll='false' :center="true" :show-close="false">
			<div class="updatediv">
				<updateindex ref="updateindex" class="updateindex"></updateindex>
			</div>
		</el-dialog>


		<!--视频播放-->
		<el-dialog title="视频播放" :visible.sync="videoDialogVisible" width="50%" @close="closeVideoPlyer">
			<videoplayer v-if="videoplayerReload" ref="videoplayer" :videoUrl='videoUrl' />
			<span slot="footer" class="dialog-footer">
				<el-button @click="videoDialogVisible = false">关闭</el-button>
			</span>
		</el-dialog>



	</div>
</template>
<script>
// import videoplayer from '@/views/media/video/videoplayer'
import { addPurchaseClickLog } from '@/api/admin/opration-log'
import videoplayer from '@/views/inventory/components/videoplayer'
// import updateindex from '@/views/inventory/components/spurchase-uplog.vue'
import updateindex from "@/views/bookkeeper/reportday/crossBorderHomepage/components/spurchase-uplog.vue";
import {
	getTableColumnCache,
	setTableColumnCache
} from '@/api/admin/business'
import cescolumnmerge from "@/components/Table/yhcolumnmerge.vue";
import cescolumn from "@/components/Table/yhcolumn.vue";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {
	addOrUpdate as addRephotography
} from "@/api/inventory/goodsimagerephotography";
export default {
	components: {
		cescolumnmerge,
		cescolumn,
		ElImageViewer,
		updateindex,
		videoplayer
	},
	props: {
		that: {
			type: Object,
			default: this
		},
		whichmodules: {
			type: String,
			// default: ''
			required: true,
		},
		buttonshow: {
			type: Boolean,
			default: false,
		},
		hasexpand: {
			type: Boolean,
			default: false
		},
		// 表格型号：mini,medium,small
		size: {
			type: String,
			default: 'mini'
		},
		type: {
			type: String,
			default: 'primary'
		},
		isBorder: {
			type: Boolean,
			default: true
		},
		loading: {
			type: Boolean,
			default: false
		},
		tableHandles: {
			type: Array,
			default: () => []
		},
		// 表格数据
		tableData: {
			type: Array,
			default: () => []
		},
		// 表格列配置
		tableCols: {
			type: Array,
			default: () => []
		},
		tablefixed: {
			type: Boolean,
			default: false
		},
		// 是否显示表格复选框
		isSelection: {
			type: Boolean,
			default: false
		},
		defaultSelections: {
			type: [Array, Object],
			default: () => null
		},
		// 是否显示表格索引
		isIndex: {
			type: Boolean,
			default: false
		},
		indexLabel: {
			type: String,
			default: '#'
		},
		//排序
		orderby: {
			type: Object,
			default: () => ({
				order: "ascending",
				name: 'id'
			})
		},
		filter: {},
		treeprops: {},
		showsummary: {
			type: Boolean,
			default: true
		},
		summaryarry: {
			type: Object,
			default: () => { }
		},
		descData: {
			type: Array,
			default: () => []
		},
		isSelectColumn: {
			type: Boolean,
			default: true
		}, //是否显示列筛选按钮
		customRowStyle: {
			type: Function,
			default: null
		}, //行style样式
		headerCellStyle: {
			type: Function,
			default: null
		}, //表头单元格style样式
		tablekey: {
			type: String,
			default: ''
		} //表格key
	},
	data() {
		return {
			isfull: false,
			dialodayssisVisible: false,
			childtableCols: {
				type: Array,
				default: () => []
			},
			checkBoxGroup: [],
			checkedColumns: [],
			tableHeight: this.gettableHeight(),
			imgPreview: {
				img: "",
				show: false
			},
			showImage: false,
			showGoodsImage: false,
			imgList: [],
			ImgWith: null,
			CansetTableColumn: false,
			summarycolumns: [],
			videoDialogVisible: false,
			videoplayerReload: false,
			videoUrl: ''
		}
	},
	mounted() {
		//this.updateData();
		this.initCheckedColumns(this.tableCols)
	},
	watch: {
		async checkedColumns(val, value) {
			if (!this.isSelectColumn) return;
			let checked = [];
			let unchecked = [];
			var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
			this.childtableCols.forEach((item, index) => {
				if (val.includes(item.label)) {
					checked.push(item.label)
					this.showhiddenColumn(item, true)
					this.$nextTick(() => {
						this.$refs.cesTable.doLayout();
					});
				} else {
					unchecked.push(item.label)
					this.showhiddenColumn(item, false)
					this.$nextTick(() => {
						this.$refs.cesTable.doLayout();
					});
				}
			})
			if (this.CansetTableColumn)
				await setTableColumnCache({
					key: key,
					displays: checked,
					hides: unchecked
				})
		},
		tableHeight(val, value) { },
		async tableCols(val, value) {
			this.initCheckedColumns(val)
		},
		tableData(val, value) {
			this.$nextTick(() => {
				this.$refs.cesTable.doLayout();
			});
		},
	},
	methods: {
		async closeVideoPlyer() {
			this.videoplayerReload = false;
		},
		playerVideoBox(row) {
			this.videoplayerReload = false;
			this.videoplayerReload = true;
			this.videoDialogVisible = true;
			this.videoUrl = row.videoUrl;
			this.$nextTick(() => {
				this.$refs.videoplayer.istimeline({userId: row.userId})
			})
			setTimeout( async () => {
				row.userId = 1
				await addPurchaseClickLog({ id: row.id });
			},500)

		},
		doShow() {
			// this.$refs.popoverSH.doShow()
			// this.btnvisible = true;
			// console.log("dayin",this.btnvisible);
		},
		doClose(scope) {
			console.log("点击取消");
			// this.$refs.popoverSH.doClose()
			scope._self.$refs[`popover-${scope.$index}`].doClose()
			// this.doShow();
			// this.$refs.popoverSH.doShow()
		},
		doDelete(scope, row) {
			scope._self.$refs[`popover-${scope.$index}`].doClose()
			this.handleDelete(1, row);
			console.log("确认删除", scope);

		},
		addEdit(data) {
			console.log(data)
			let arr = data;
			this.$emit('onHand', arr);
		},
		async sortabledata(index, row, updown) {
			var indexnum = index + 1;
			console.log("row数据", row);
			this.$emit('orderTopdown', indexnum, row, updown);
		},
		handleDelete(index, row) {
			// console.log("子删除",row);
			this.$emit('deleteLog', row);
		},
		handleEdit(index, row) {
			console.log("模块归属", this.whichmodules);
			let arr = [3];
			arr.push(row);
			this.$emit('onHand', arr);
		},
		handleUplod(index, row) {
			console.log("模块归属", this.whichmodules);
			let arr = [3];
			arr.push(row);
			this.$emit('onUplod', arr);
		},
		handleSee(index, row) {
			// console.log("每行数据",row);
			let arr = [2];
			arr.push(row);
			this.$emit('onHand', arr);
		},
		async initCheckedColumns(_tableCols) {
			_tableCols.forEach((item, index) => {
				this.checkBoxGroup.push(item.label);
			})
			this.checkBoxGroup.push("操作");
			await this.$nextTick(async function () {
				this.CansetTableColumn = false
				this.childtableCols = this.tableCols;
				if (!this.isSelectColumn) return;
				let checked = [];
				var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
				let res = await getTableColumnCache({
					key: key
				})
				if (res?.data?.hideContent) {
					this.tableCols.forEach((item, index) => {
						if (!res.data.hideContent.includes(item.label))
							checked.push(item.label);
					})
				}
				if (checked.length > 0) this.checkedColumns = checked;
				else this.checkedColumns = this.checkBoxGroup
			});

			let _that = this;
			setTimeout(function () {
				_that.CansetTableColumn = true;
			}, 2000);
		},
		showhiddenColumn(column, ishow) {
			if (!!column.merge && !!column.cols && column.cols.length > 0) {
				//console.log('childtableCols',this.childtableCols)
				//column.cols.forEach(f=> this.showhiddenColumn(f,ishow))
			} else if (!column.label)
				column.istrue = true;
			else if (!column.merge)
				column.istrue = ishow;
		},
		updateData() {
			if (this.tableData) {
				this.tableData.forEach(element => {
					if (!element.id) {
						element.id = this.randrom()
					}
				});
			}
			console.log('this.tableData', this.tableData)
		},
		randrom() {
			var e = 10;
			var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
				a = t.length,
				n = "";
			for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
			return n;
		},
		gettableHeight() {
			return this.isSelectColumn ? '95%' : '100%';
		},
		select(rows, row) {
			this.$emit('select', rows, row);
		},
		// 全选
		selectAll(rows) {
			this.$emit('select', rows)
		},
		sortchange(column) {
			this.$emit('sortchange', column)
		},
		cellclick(row, column, cell, event) {
			console.log("点击表格行", row);
			// console.log("hand", row);
			// if(this.clickbtn){
			//   this.dialodayssisVisible = false
			// }else{

			if (column.property == 'videoUrl' && row.videoUrl) {
				//弹出视频播放
				this.playerVideoBox(row)
			} else {
				this.dialodayssisVisible = true
				this.$nextTick(async () => {
					await this.$refs.updateindex.getlist(row)
				})
				row.userId=1;
				// }
				this.$emit('cellclick', row, column, cell, event)
			}
		},
		expandchange(row, args) {
			this.$emit('expandchange', row, args)
		},
		clearSort() {
			this.$refs.cesTable.clearSort();
		},
		btnHand(hand) {
			// console.log("hand", hand)
			this.$emit(hand)
		},
		getSummaries(param) {
			const sums = [];
			if (!this.summaryarry)
				return sums
			var arr = Object.keys(this.summaryarry);
			if (arr.length == 0)
				return sums
			const {
				columns,
				data
			} = param;
			var hashj = false;
			columns.forEach((column, index) => {
				// if (column.width>70&&hashj==false) {
				//   //sums[index] = '合计';
				//  // hashj=true
				//   return;
				// }
				if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
					var sum = this.summaryarry[column.property + '_sum'];
					if (sum == null) return;
					else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
					else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
					else sums[index] = sum.toFixed(0)
				} else sums[index] = ''
			});
			if (this.summarycolumns.length == 0) {
				this.summarycolumns = columns;
				this.initsummaryEvent();
			}
			return sums
		},
		initsummaryEvent() {
			let self = this;
			let table = document.querySelector('.el-table__footer-wrapper>table');
			this.$nextTick(() => {
				self.summarycolumns.forEach((column, index) => {
					if (column.property) {
						var col = findcol(self.tableCols, column.property);
						if (col && col.summaryEvent) {
							table.rows[0].cells[index].style.color = "red";
							table.rows[0].cells[index].onclick = function () {
								self.$emit('summaryClick', column.property)
							}
						}
					}
				})
			})

			function findcol(cols, property) {
				let column;
				for (var i = 0; i < cols.length; i++) {
					var c = cols[i];
					if (column) break
					else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
						column = c;
						break
					} else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
				}
				return column
			}
		},
		async closeFunc() {
			this.showGoodsImage = false;
		},
		async preview(imgUrl) {
			this.showGoodsImage = true;
			this.imgList = [];
			this.imgList.push(imgUrl);
		},
		// 图片点击放大
		async showImg(e) {
			this.showGoodsImage = true;
			this.imgList = [];
			this.imgList.push(e.target.src);
		},
		//商品编码大图预览 增加自定义相机按钮code商品编码，name商品名称
		async previewImageGoodsCode(imgUrl, code, name) {
			this.showGoodsImage = true;
			this.imgList = [];
			this.imgList.push(imgUrl);
			var that = this;
			//增加自定义相机按钮，记录重拍
			setTimeout(() => {
				var container = document.getElementsByClassName('el-image-viewer__actions__inner');
				if (container && container.length > 0) {
					container = container[0];
					var child = document.createElement('li');
					child.title = "重拍";
					child.className = "el-icon-camera";
					child.onclick = async function () {
						//that.$confirm('确定重拍吗？','提示').then(async ()=>{
						if (confirm("确定重拍吗？")) {
							var params = {
								goodsCode: code,
								goodsName: name,
								goodsImage: imgUrl
							};
							var res = await addRephotography(params);
							if (!res?.success) {
								return;
							} else {
								that.$message({
									message: "重拍登记成功",
									type: "success"
								});
							}
						}
						//}).catch(()=>{});

					};
					container.appendChild(child);
				}
			}, 100);
		},
		//清空选择
		clearSelection() {
			this.$refs.cesTable.clearSelection();
		},
		//取消/选择某一行
		toggleRowSelection(row, selected) {
			this.$refs.cesTable.toggleRowSelection(row, selected);
		},
		//取消/选择所有
		toggleAllSelection(selected) {
			this.$refs.cesTable.toggleAllSelection(selected);
		},
		doLayout() {
			this.$nextTick(() => {
				this.$refs.cesTable.doLayout();
			});
		},

	},
}
</script>
<style lang="scss" scoped>
.el-table th>.cell {
	padding-left: 8px;
}

.el-table .caret-wrapper {
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	height: 34px;
	width: 0;
	vertical-align: middle;
	cursor: pointer;
	overflow: initial;
	position: relative;
}

.el-table__footer-wrapper {
	margin-top: -2px;
	font-size: 9px;
	padding: 0px;
}

.el-table .cell {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
	word-break: break-all;
	line-height: 23px;
	padding-right: 1px;
}

.el-table__footer-wrapper tbody td,
.el-table__header-wrapper tbody td {
	background-color: #f1f1f1;
	color: #0756f5;
}

.ces-table-require::before {
	content: '*';
	color: red;
}

.table-wrapper {
	width: 100%;
	height: calc(100% - 35px);
	margin: 0;
}

.el-table {
	overflow: visible !important;
	height: 99.9% !important;
	width: 99.9% !important;
}

.el-table__empty-block {
	height: 550px;
	border: none;
}

/* .el-table {
  width: 99.9% !important;
} */
/* .wendang p img{max-height: 60px;} */
.wendang img {
	max-height: 50px;
}

.table_column_show {
	display: block;
}

.table_column_hidden {
	display: none;
}
</style>
<style lang="scss" scoped>
.imgDolg {
	width: 100vw;
	height: 100vh;
	position: fixed;
	z-index: 99999;
	background-color: rgba(140, 134, 134, 0.6);
	top: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: auto;

	#imgDolgClose {
		position: fixed;
		top: 35px;
		cursor: pointer;
		right: 7%;
		font-size: 50px;
		color: white;
	}

	// img{
	//    width: 80%;
	// }
}
</style>
