<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px">
          <el-row>
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
              <el-form-item prop="processPlanName" label="方案名称">
                <el-input v-model="addForm.processPlanName" auto-complete="off" placeholder="请填写方案名称" maxlength="100"  show-word-limit/>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
              <el-form-item prop="startDate" label="观察开始日期">
                <el-date-picker v-model="addForm.startDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                  placeholder="请选择观察开始日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
              <el-form-item prop="curDate" label="日报参考日期">
                <el-date-picker v-model="addForm.curDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                  placeholder="请选择日报参考日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
              <el-form-item prop="endDate" label="观察结束日期">
                <el-date-picker v-model="addForm.endDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                  placeholder="请选择观察结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item prop="procodeProcessPlanColCheck1" label="被观察指标">
                <el-checkbox-group v-model="procodeProcessPlanColCheckList" size="mini" @change="CheckChange">
                  <el-checkbox v-for="item in procodeProcessPlanColList" :key="item.value" :label="item.label"
                    :value="item.value" border></el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-button type="primary" @click="onSeeDtl">预览明细</el-button>
              <el-button type="primary" @click="onBatchRemoveDtl">批量移除明细</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
    <template>
      <vxetablebase :id="'procodeprocessplanadd_6122112'" :border="true" :align="'center'"
        :tablekey="'procodeprocessplanadd_6122112'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
        :isSelectColumn="true" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry'
        :tableData='addForm.dtlList' @sortchange='sortchange' :tableCols='tableCols' :loading="listLoading" :tableHandles='tableHandles'
        :showheaderoverflow="false" style="width:100%;height:95%;margin: 0" :xgt="9999" @select='onSelectDtl'>
        <template slot='extentbtn'>
        </template>
      </vxetablebase>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <my-confirm-button type="submit" :loading="addLoading" @click="onCreate" />
      </div>
    </template>
  </my-container>
</template>
<script>
import { formatPlatform, formatLinkProCode} from "@/utils/tools";
import dayjs from 'dayjs';
import MyContainer from '@/components/my-container';
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { CreateProcodeProcessPlan, PreviewProcodeProcessPlanPros } from '@/api/bookkeeper/procodeprocessplan'

const tableCols = [
];

const tableHandles1 = [

];
export default {
  name: 'procodeprocessplanadd',
  components: { MyContainer, MyConfirmButton, vxetablebase },
  props: {
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      tableCols: [],
      tableHandles: tableHandles1,
      procodeProcessPlanColList: [],
      pager: { OrderBy: " saleAmont ", IsAsc: false },
      procodeProcessPlanColCheckList: [],
      addLoading: false,
      selProCodes: [],
      procodeProcessPlanColKeys: [],
      addForm: {
        processPlanName: "",
        curDate: null,
        startDate: null,
        endDate: null,
        procodeItemCols: "",
        procodeItemColNames: "",
        dtlList: [],
      },
      addFormRules: {
        processPlanName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        curDate: [{ required: true, message: '请选择日报参考日期', trigger: 'blur' }],
        startDate: [{ required: true, message: '请选择观察开始日期', trigger: 'blur' }],
        endDate: [{ required: true, message: '请选择观察结束日期', trigger: 'blur' }],
        procodeProcessPlanColCheck1: [{ required: true, message: '请勾选被观察指标', trigger: 'blur' }],
      },
      listLoading: false,
      summaryarry: {},
      selectAddRows: [],
    }
  },
  async mounted() {
  },
  methods: {
    async loadData(addparam) {
      this.pageLoading = true;
      this.selProCodes = addparam.selProCodes;
      this.procodeProcessPlanColKeys = addparam.procodeProcessPlanColKeys;
      this.addForm.curDate = addparam.yearMonthDay;

      this.addForm.startDate = dayjs().format('YYYY-MM-DD');
      this.addForm.endDate = dayjs().add(2, 'day').format('YYYY-MM-DD');

      await this.getProcodeProcessPlanColList();
      this.pageLoading = false;

      this.selectAddRows = [];
    },

    async getProcodeProcessPlanColList() {
      this.procodeProcessPlanColList = [];
      //let res = await GetProcodeProcessPlanColList();
      // if (res?.success == true) {
      //   this.procodeProcessPlanColList = res.data.map(item => { return { value: item.procodeItemCol, label: item.procodeItemColName } });
      // }
        this.procodeProcessPlanColList.push({ value: "yyProfit4Rate", label: "毛四利率" });
        this.procodeProcessPlanColCheckList.push("毛四利率");
        this.procodeProcessPlanColList.push({ value: "yyProfit4AfterRate", label: "毛四利率(减退款)" });
        this.procodeProcessPlanColCheckList.push("毛四利率(减退款)");
        this.procodeProcessPlanColList.push({ value: "yyProfit6Rate", label: "毛六利率" });
        this.procodeProcessPlanColCheckList.push("毛六利率");
        this.procodeProcessPlanColList.push({ value: "yyProfit6AfterRate", label: "毛六利率(减退款)" });
        this.procodeProcessPlanColCheckList.push("毛六利率(减退款)");

    },
    async onSeeDtl() {
      if (dayjs(this.addForm.startDate).format('YYYY-MM-DD') != dayjs().format('YYYY-MM-DD') && dayjs(this.addForm.startDate).format('YYYY-MM-DD') != dayjs().subtract(1, 'day').format('YYYY-MM-DD')) {
        this.$message.warning('观察开始日期必须是昨天或当天')
        return;
      }
      let sediff = dayjs(this.addForm.endDate).diff(dayjs(this.addForm.startDate), 'day')
      if (sediff > 29) {
        this.$message.warning('观察日期不能超过30天')
        return;
      }
      else if (sediff < 0) {
        this.$message.warning('观察开始日期不能小于结束日期')
        return;
      }
      let sediff2 = dayjs(this.addForm.startDate).diff(dayjs(this.addForm.curDate), 'day')
      if (sediff2 < 1) {
        this.$message.warning('观察开始日期必须大于日报参考日期')
        return;
      }

      this.listLoading = true;
      this.addForm.procodeItemCols = "";
      this.addForm.procodeItemColNames = "";
      let procodeProcessPlanColCheckList_Order = [];//用于指标按照顺序，避免指标是乱的
      this.procodeProcessPlanColList.forEach(f => {//循环的目的就是保持指标按照顺序
        let find = this.procodeProcessPlanColCheckList.find(s => s == f.label);
        if (find) {
          procodeProcessPlanColCheckList_Order.push(f);
          this.addForm.procodeItemCols += (f.value + ",");
          this.addForm.procodeItemColNames += (f.label + ",");
        }
      });
      if (this.addForm.procodeItemCols) {
        this.addForm.procodeItemCols = this.addForm.procodeItemCols.substring(0, this.addForm.procodeItemCols.length - 1);
        this.addForm.procodeItemColNames = this.addForm.procodeItemColNames.substring(0, this.addForm.procodeItemColNames.length - 1);
      }
      let dto = {
        curDate: this.addForm.curDate,
        startDate: this.addForm.startDate,
        endDate: this.addForm.endDate,
        procodeItemCols: this.addForm.procodeItemCols,
        procodeItemColNames: this.addForm.procodeItemColNames,
        proCodeList: this.selProCodes,
        orderBy: this.pager.OrderBy,
        isAsc: this.pager.IsAsc
      };
      let res = await PreviewProcodeProcessPlanPros(dto);
      if (res?.success != true) {
        this.listLoading = false;
        return;
      }

      let curymd = dayjs(this.addForm.curDate).format('YYYY-MM-DD');
      let curymd_3 = dayjs(this.addForm.curDate).add(-3, 'day').format('YYYY-MM-DD');
      let curymd_7 = dayjs(this.addForm.curDate).add(-7, 'day').format('YYYY-MM-DD');

      this.tableCols = [];
      let newcols = [
        { istrue: true, fixed: 'left', width: '40', type: "checkbox" },
        { istrue: true, prop: 'platform', label: '平台', width: '40', sortable: 'custom', formatter: row => formatPlatform(row.platform) },
        { istrue: true, prop: 'shopName', label: '店铺名称', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'styleCode', label: '系列编码', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'proCode', label: '产品ID', width: '100', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode)},
        { istrue: true, prop: 'proName', label: '产品名称', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'groupId', label: '运营组', width: '55', sortable: 'custom', formatter: (row) => row.groupName },
        { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '55', sortable: 'custom', formatter: (row) => row.operateSpecialUserName },
        { istrue: true, prop: 'userId', label: '运营助理', width: '55', sortable: 'custom', formatter: (row) => row.userName },
        { istrue: true, prop: 'onTime', label: '上架时间', width: '80', sortable: 'custom' },
        { istrue: true, prop: 'onTimeCount', label: '上架天数', width: '40', sortable: 'custom' },
        { istrue: true, prop: 'productCategoryId', label: '类目', width: '80', sortable: 'custom', formatter: (row) => row.productCategoryName },
      ];

      let newcols2 = [
        { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '60', formatter: (row) => !row.orderCount ? " " : row.orderCount },
        { istrue: true, prop: 'payAmont', label: '付款金额', sortable: 'custom', width: '80', formatter: (row) => !row.payAmont ? " " : row.payAmont.toFixed(2) },
        { istrue: true, prop: 'alladv', label: '总广告费', sortable: 'custom', width: '80', formatter: (row) => row.alladv == 0 ? " " : row.alladv?.toFixed(2) },
        { istrue: true, prop: 'advratio', label: '广告占比%', sortable: 'custom', width: '60', formatter: (row) => !row.advratio ? " " : row.advratio.toFixed(2) + "%" },

        { istrue: true, prop: 'yyProfit1', label: '毛一利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '销售金额-总商品成本-采购运费-代发成本', formatter: (row) => !row.yyProfit1 ? " " : row.yyProfit1.toFixed(2) },
        { istrue: true, prop: 'yyProfit1Rate', label: '毛一利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛一利润/销售金额', formatter: (row) => !row.yyProfit1Rate ? " " : row.yyProfit1Rate.toFixed(2) + '%' },
        { istrue: true, prop: 'yyProfit2', label: '毛二利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛一利润-平台扣点-包装费-快递费', formatter: (row) => !row.yyProfit2 ? " " : row.yyProfit2?.toFixed(2) },
        { istrue: true, prop: 'yyProfit2Rate', label: '毛二利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润/销售金额', formatter: (row) => !row.yyProfit2Rate ? " " : row.yyProfit2Rate.toFixed(2) + '%' },
        { istrue: true, prop: 'yyProfit3', label: '毛三利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3 ? " " : row.yyProfit3?.toFixed(2) },
        { istrue: true, prop: 'yyProfit3Rate', label: '毛三利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛二利润-预估费用', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3Rate ? " " : row.yyProfit3Rate?.toFixed(2) + '%' },
        { istrue: true, prop: 'yyProfit4', label: '毛四利润', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛三利润-出仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4 ? " " : row.yyProfit4?.toFixed(2) },
        { istrue: true, prop: 'yyProfit4Rate', label: '毛四利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛四利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4Rate ? " " : row.yyProfit4Rate?.toFixed(2) + '%' },
        
        { istrue: true, prop: 'yyProfit6', label: '毛六利润', sortable: 'custom', width: '60', type: 'custom', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6 ? " " : row.yyProfit6?.toFixed(2) },
        { istrue: true, prop: 'yyProfit6Rate', label: '毛六利率', sortable: 'custom', width: '60', type: 'custom', tipmesg: '毛六利润/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6Rate ? " " : row.yyProfit6Rate?.toFixed(2) + '%' },
        
        { istrue: true, prop: 'yyProfit1After', label: '毛一(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After?.toFixed(2) },
        { istrue: true, prop: 'yyProfit1AfterRate', label: '毛一利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛一（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit1AfterRate ? " " : row.yyProfit1AfterRate?.toFixed(2) },
        { istrue: true, prop: 'yyProfit2After', label: '毛二(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After?.toFixed(2) },
        { istrue: true, prop: 'yyProfit2AfterRate', label: '毛二利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛二（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit2AfterRate ? " " : row.yyProfit2AfterRate?.toFixed(2) },
        { istrue: true, prop: 'yyProfit3After', label: '毛三(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After?.toFixed(2) },
        { istrue: true, prop: 'yyProfit3AfterRate', label: '毛三利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛三（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit3AfterRate ? " " : row.yyProfit3AfterRate?.toFixed(2) },
        { istrue: true, prop: 'yyProfit4After', label: '毛四(减退款）', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛四-总退款+取消单返还成本+销退仓成本', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After?.toFixed(2) },
        { istrue: true, prop: 'yyProfit4AfterRate', label: '毛四利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛四（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit4AfterRate ? " " : row.yyProfit4AfterRate?.toFixed(2) },
        
        { istrue: true, prop: 'yyProfit6After', label: '毛六(减退款）', sortable: 'custom', width: '70', type: 'custom', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After?.toFixed(2) },
        { istrue: true, prop: 'yyProfit6AfterRate', label: '毛六利率(减退款)', sortable: 'custom', width: '70', type: 'custom', tipmesg: '毛六（减退款）/销售金额', permission: "lirunprsi,profit3rsi", formatter: (row) => !row.yyProfit6AfterRate ? " " : row.yyProfit6AfterRate?.toFixed(2) },
        
        { istrue: true, prop: 'profit4', label: '净利润', type: 'custom', tipmesg: '毛三利润-公摊费', sortable: 'custom', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4 ? " " : row.profit4.toFixed(2) },
        { istrue: true, prop: 'profit4Rate', label: '净利率', type: 'custom', tipmesg: '净利润/销售金额', sortable: 'custom', width: '50', permission: "lirunprsi,shareFeedetail", formatter: (row) => !row.profit4Rate ? " " : (row.profit4Rate).toFixed(2) + '%' },
        {
          istrue: true, rop: '', label: `退款`, width: '120', merge: true, prop: 'mergeField',
          cols: [
            { istrue: true, prop: 'yyRefundAmontBefore', label: '发货前退款', sortable: 'custom', width: '100', type: 'custom' },
            { istrue: true, prop: 'yyRefundAmontBeforeRate', label: '发货前退款率', sortable: 'custom', tipmesg: '发货前退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontBeforeRate ? " " : (row.yyRefundAmontBeforeRate).toFixed(2) + '%' },
            { istrue: true, prop: 'yyRefundAmontAfter', label: '发货后退款', sortable: 'custom', width: '100', type: 'custom' },
            { istrue: true, prop: 'yyRefundAmontAfterRate', label: '发货后退款率', sortable: 'custom', tipmesg: '发货后退款/付款金额', width: '120', type: 'custom', formatter: (row) => !row.yyRefundAmontAfterRate ? " " : (row.yyRefundAmontAfterRate).toFixed(2) + '%' },
            { istrue: true, prop: 'yyRefundAmont', label: '总退款金额', sortable: 'custom', width: '100', tipmesg: '当日发生的总退款金额，包括历史订单', formatter: (row) => !row.yyRefundAmont ? " " : row.yyRefundAmont.toFixed(2) },
          ]
        },
      ];

      let c1 = { align: 'center', prop: '', label: curymd, merge: true, width: '80', cols: [] }
      let c2 = { align: 'center', prop: '', label: "前3天", merge: true, width: '80', cols: [] }
      let c3 = { align: 'center', prop: '', label: "前7天", merge: true, width: '80', cols: [] }
      //当天，前3天，前7天
      procodeProcessPlanColCheckList_Order.forEach(w => {
        //拼列头
        if (w.value.indexOf("Rate") > 0) {
          c1.cols.push({ istrue: true, prop: curymd + w.value, label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[curymd + w.value] + '%') });
          c2.cols.push({ istrue: true, prop: curymd_3 + w.value, label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[curymd_3 + w.value] + '%') });
          c3.cols.push({ istrue: true, prop: curymd_7 + w.value, label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[curymd_7 + w.value] + '%') });
        }
        else {
          c1.cols.push({ istrue: true, prop: curymd + w.value, label: w.label, width: '80', sortable: 'custom' });
          c2.cols.push({ istrue: true, prop: curymd_3 + w.value, label: w.label, width: '80', sortable: 'custom' });
          c3.cols.push({ istrue: true, prop: curymd_7 + w.value, label: w.label, width: '80', sortable: 'custom' });
        }
        //循环数据源
        res.data.forEach(f => {
          //拼数据源
          if(f.proItemList != undefined ){
            f[(curymd + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd && x.itemKey == w.value)?.itemValue ?? 0;
            f[(curymd_3 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_3 && x.itemKey == w.value)?.itemValue ?? 0;
            f[(curymd_7 + w.value)] = f.proItemList.find(x => x.itemDateStr == curymd_7 && x.itemKey == w.value)?.itemValue ?? 0;
          } else {
            f[(curymd + w.value)] = 0;
            f[(curymd_3 + w.value)] = 0;
            f[(curymd_7 + w.value)] = 0;
          }
        })
      });
      newcols.push(c1);
      newcols.push(c2);
      newcols.push(c3);

      //循环观察日期
      for (let i = dayjs(this.addForm.startDate); i <= dayjs(this.addForm.endDate); i = i.add(1, 'day')) {
        let mycols = [];
        //循环指标
        procodeProcessPlanColCheckList_Order.forEach(w => {
          //拼列头
          if (w.value.indexOf("Rate") > 0) {
            mycols.push({ istrue: true, prop: (i.format('YYYY-MM-DD') + w.value), label: w.label, width: '80', sortable: 'custom', formatter: (row) => (row[i.format('YYYY-MM-DD') + w.value] + '%') });
          }
          else {
            mycols.push({ istrue: true, prop: (i.format('YYYY-MM-DD') + w.value), label: w.label, width: '80', sortable: 'custom' });
          }

          //拼数据源
          res.data.forEach(f => {
            if(f.proItemList != undefined){
              f[(i.format('YYYY-MM-DD') + w.value)] = f.proItemList.find(x => x.itemDateStr == i.format('YYYY-MM-DD') && x.itemKey == w.value)?.itemValue ?? 0;
            }
            else{
              f[(i.format('YYYY-MM-DD') + w.value)] = 0;
            }
          });

        });
        newcols.push({ align: 'center', prop: '', label: i.format('YYYY-MM-DD'), merge: true, prop: 'mergeField', width: '80', cols: mycols });
      }

      newcols = newcols.concat(newcols2);

      this.$nextTick(() => {
        this.tableCols = newcols;
        this.addForm.dtlList = res.data;
      });
      this.listLoading = false;


      this.selectAddRows = [];
    },
    getDateBetween(startTime, endTime) {
      const start = dayjs(startTime);
      const end = dayjs(endTime);
      const result = [];
      let current = start;
      while (current.isBefore(end) || current.isSame(end)) {
        result.push(current.format('YYYY-MM-DD'));
        current = current.add(1, 'day');
      }
      return result;
    },
    async onCreate() {
      let addProCodes = this.addForm.dtlList.map(item => item.proCode);
      if (addProCodes.length <= 0) {
        this.$message.error('请预览明细后再提交');
        return;
      }
      let dto = {
        ...this.addForm,
        proCodeList: addProCodes,
      };
      this.pageLoading = true;
      this.addLoading = true;
      const { data, success } = await CreateProcodeProcessPlan(dto);
      this.pageLoading = false;
      this.addLoading = false;
      if (success) {
        this.$message.success('创建成功');
        this.$emit('close');
      }
    },
    CheckChange(){
      this.onSeeDtl()
    },
    onSelectDtl(rows) {
      this.selectAddRows = rows;
    },
    onBatchRemoveDtl() {
      if (this.selectAddRows.length <= 0) {
        this.$message.warning('请至少勾选一行');
        return;
      }
      this.selectAddRows.forEach(row => {
        this.addForm.dtlList.splice(this.addForm.dtlList.indexOf(row), 1);
      });
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      if (column.prop == 'onTime') {
        this.pager.IsAsc = !this.pager.IsAsc;
      }
      this.dataSort();
    },
    dataSort(){
      this.addForm.dtlList.sort((a,b) => {
        const valA = a[this.pager.OrderBy];
        const valB = b[this.pager.OrderBy];
        
        // 数字类型直接比较
        if (typeof valA === 'number' && typeof valB === 'number') {
          return this.pager.IsAsc ? valA - valB : valB - valA;
        }
        
        // 其他类型转为字符串比较
        const strA = String(valA).toLowerCase();
        const strB = String(valB).toLowerCase();
        return this.pager.IsAsc 
          ? strA.localeCompare(strB) 
          : strB.localeCompare(strA);
      })
    },
  }
}
</script>
<style lang="scss" scoped></style>
