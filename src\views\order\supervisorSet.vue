<template>
  <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm"
    v-loading="!isVisible">
    <el-form-item label="负责人1:" prop="pass">
      <yhUserselectors class="publicCss" :value.sync="ruleForm.ownerId1" :text.sync="ruleForm.ownerUserName1" clearable
        v-if="isVisible" />
    </el-form-item>
    <el-form-item label="负责人2:" prop="pass">
      <yhUserselectors class="publicCss" :value.sync="ruleForm.ownerId2" :text.sync="ruleForm.ownerUserName2" clearable
        v-if="isVisible" />
    </el-form-item>
    <el-form-item label="负责人3:" prop="pass">
      <yhUserselectors class="publicCss" :value.sync="ruleForm.ownerId3" :text.sync="ruleForm.ownerUserName3" clearable
        v-if="isVisible" />
    </el-form-item>
    <el-form-item label="负责人4:" prop="pass">
      <yhUserselectors class="publicCss" :value.sync="ruleForm.ownerId4" :text.sync="ruleForm.ownerUserName4" clearable
        v-if="isVisible" />
    </el-form-item>
    <el-form-item label="负责人5:" prop="pass">
      <yhUserselectors class="publicCss" :value.sync="ruleForm.ownerId5" :text.sync="ruleForm.ownerUserName5" clearable
        v-if="isVisible" />
    </el-form-item>
    <el-form-item>
      <el-button @click="$emit('close')">关闭</el-button>
      <el-button type="primary" @click="submitForm" v-throttle="1000">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import MyContainer from "@/components/my-container";
import yhUserselectors from '@/components/YhCom/yh-userselectors.vue'
import {
  getSeriesCodeOwnerLeaders,
  SaveSeriesCodeOwnerLeaders,
} from "@/api/bookkeeper/styleCodeRptData";
export default {
  name: "scanCodePage",
  components: {
    MyContainer, yhUserselectors
  },
  data() {
    return {
      ruleForm: {
        ownerId1: [],
        ownerUserName1: [],
        ownerId2: [],
        ownerUserName2: [],
        ownerId3: [],
        ownerUserName3: [],
        ownerId4: [],
        ownerUserName4: [],
        ownerId5: [],
        ownerUserName5: [],
      },
      isVisible: false
    }
  },
  async mounted() {
    this.getProps()
  },
  methods: {
    async getProps() {
      const { data, success } = await getSeriesCodeOwnerLeaders()
      if (!success) return
      this.ruleForm = data
      this.isVisible = true
    },
    async submitForm() {
      const { success } = await SaveSeriesCodeOwnerLeaders(this.ruleForm)
      if (!success) return
      this.$message.success('设置成功')
      this.$emit('close')
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}
</style>
