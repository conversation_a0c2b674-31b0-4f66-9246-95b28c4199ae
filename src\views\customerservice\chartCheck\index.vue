<template>
    <my-container>
        <el-tabs v-model="activeName" style="height: calc(100% - 40px);" @tab-click="handleClick">

            <el-tab-pane label="售前" name="first" style="height: 100%;">
                <keep-alive>
                <PreSales v-if="activeName === 'first'" :chatInfos="preInfos" :chatRecordKeywords="chatRecordKeywords"></PreSales>
                </keep-alive>
            </el-tab-pane>

            <el-tab-pane label="售后" name="second" style="height: 100%;">
                <keep-alive>
                <AfterSales v-if="activeName === 'second'" ref="secondref" :partInfo="infos" :chatInfos="preInfos" :chatRecordKeywords="chatRecordKeywords">
                </AfterSales>
                </keep-alive>
            </el-tab-pane>

<!--            <el-tab-pane label="发票" name="invoice" style="height: 100%;">
                <keep-alive>
                <InvoiceSales v-if="activeName === 'invoice'" :chatInfos="preInfos"></InvoiceSales>
                </keep-alive>
            </el-tab-pane>-->

            <el-tab-pane label="平台判罚" name="third" style="height: 100%;">
                <keep-alive>
                <SalesThird v-if="activeName === 'third'"></SalesThird>
                </keep-alive>
            </el-tab-pane>

            <el-tab-pane label="敷衍回复" name="fourth" style="height: 100%;">
                <keep-alive>
                <SalesFourth v-if="activeName === 'fourth'"></Salesfourth>
                </keep-alive>
            </el-tab-pane>

<!--            <el-tab-pane label="拉黑买家" name="Seventh" style="height: 100%;">
                <keep-alive>
                <Seventh v-if="activeName === 'Seventh'"></Seventh>
                </keep-alive>
            </el-tab-pane>-->

            <el-tab-pane label="售前/售后审核数据统计" name="fifth" style="height: 100%;">
                <keep-alive>
                <SalesFifth v-if="activeName === 'fifth'"></SalesFifth>
                </keep-alive>
            </el-tab-pane>

            <el-tab-pane label="售前/售后审核数据统计(拼多多)" name="tenth" style="height: 100%;">
                <keep-alive>
                <SalesTenth v-if="activeName === 'tenth'"></SalesTenth>
                </keep-alive>
            </el-tab-pane>

            <el-tab-pane label="售后退款原因数据统计" name="ninth" style="height: 100%;">
                <keep-alive>
                <salesninth v-if="activeName === 'ninth'" @showtabsecond="showtabsecond"></salesninth>
                </keep-alive>
            </el-tab-pane>


            <el-tab-pane label="聊天问题数据汇总" name="eighth" style="height: 100%;"
                v-if="checkPermission(['api:customerservice:UnPayOrder:GetUnPayOrderAuditStatisticsPageList'])">
                <keep-alive>
                <ChatEighth v-if="activeName === 'eighth'" @jumpIndex="jumpIndex"></ChatEighth>
                </keep-alive>
            </el-tab-pane>

            <el-tab-pane label="发票聊天记录汇总" name="invoice" style="height: 100%;">
                <keep-alive>
                  <InvoiceSales v-if="activeName === 'invoice'" :chatInfos="preInfos"></InvoiceSales>
                </keep-alive>
            </el-tab-pane>

            <el-tab-pane label="发票聊天审核数据统计" name="invoiceStats" style="height: 100%;">
                <keep-alive>
                <InvoiceStatistics v-if="activeName === 'invoiceStats'"></InvoiceStatistics>
                </keep-alive>
            </el-tab-pane>

<!--            <el-tab-pane label="正向聊天记录" name="sixth" style="height: 100%;">
                <keep-alive>
                <chatSixth v-if="activeName === 'sixth'"></chatSixth>
                </keep-alive>
            </el-tab-pane>-->



        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import PreSales from "@/views/customerservice/chartCheck/preSales.vue"
import AfterSales from "@/views/customerservice/chartCheck/afterSales.vue"
import SalesThird from "@/views/customerservice/chartCheck/SalesThird.vue"
import SalesFourth from "@/views/customerservice/chartCheck/SalesFourth.vue"
import chatSixth from "@/views/customerservice/chartCheck/chatSixth.vue"
import SalesFifth from "@/views/customerservice/chartCheck/salesFifth.vue"
import Seventh from "@/views/customerservice/chartCheck/Seventh.vue"
import ChatEighth from "@/views/customerservice/chartCheck/chatEighth.vue"
import salesninth from "@/views/customerservice/chartCheck/salesninth.vue"
import SalesTenth from "@/views/customerservice/chartCheck/SalesTenth.vue"
import InvoiceSales from "@/views/customerservice/chartCheck/invoiceSales.vue"
import InvoiceStatistics from "@/views/customerservice/chartCheck/invoiceStatistics.vue"

export default {
    name: "chartCheck",
    components: {
        MyContainer,
        PreSales,
        AfterSales,
        SalesThird,
        SalesFourth,
        chatSixth,
        SalesFifth,
        Seventh,
        ChatEighth,
        salesninth,
        SalesTenth,
        InvoiceSales,
        InvoiceStatistics,
    },
    mounted() {
        // window.showtabsecond = this.showtabsecond
    },
    data() {
        return {
            activeName: 'first',
            infos: null,
            preInfos: null,
            chatRecordKeywords: '',
        }
    },
    methods: {
        showtabsecond(data) {
            this.infos = data;
            this.activeName = "second";//售后页面
        },
        handleClick(tab, event) {
            this.infos = null;
            this.preInfos = null;
            console.log(tab, event);
        },
        jumpIndex(data) {
            this.preInfos = data;
            console.log(data, 'index页面data')
            console.log(this.preInfos, 'index页面info')

            if (data.switchshow == true) {
                this.activeName = "second";//售前页面
            } else {
                this.activeName = "first";//售后页面
            }

        },
    }
}
</script>

<style scoped lang="scss"></style>
