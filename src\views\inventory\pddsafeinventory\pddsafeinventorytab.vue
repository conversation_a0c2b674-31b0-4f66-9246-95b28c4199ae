<template>
    <container>
        <el-tabs @tab-click="onTabClick" v-model="activeName" style="height: 95% ;">
            <el-tab-pane label="待审核" name="first" style="height: 100%;">
                <pendingaudit ref="pendingaudit"></pendingaudit>
            </el-tab-pane>
            <el-tab-pane label="预售" name="second" style="height: 100%;">
                <pass :filter="presaleFilter" ref="presale"></pass>
            </el-tab-pane>
            <el-tab-pane label="上架" name="three" style="height: 100%;">
                <pass :filter="onlineFilter" ref="online"></pass>
            </el-tab-pane>
            <el-tab-pane label="拒绝" name="four" style="height: 100%;">
                <nopass :filter="rejectFilter" ref="reject"></nopass>
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import pendingaudit from './pendingaudit.vue';
import pass from "./pass.vue";
import nopass from "./nopass.vue";
import inputYunhan from "@/components/Comm/inputYunhan";

export default {
    name: 'YunHanAdminPddSafeInventoryTab',
    components: { container, MyConfirmButton, inputYunhan, pendingaudit,nopass,pass},

    data() {
        return {
            that: this,
            activeName: 'first',
            presaleFilter:{
                type:"预售"
            },
            onlineFilter:{
                type:"正常"
            },
            rejectFilter:{
                operationType:-1,
                type:null
            }
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.$nextTick(() => {
                if (this.activeName == 'first') this.$refs.pendingaudit.onSearch();
                if (this.activeName == 'second'){
                     this.$refs.presale.onSearch();
                }
                if (this.activeName == 'three') {
                    this.$refs.online.onSearch();
                }
                if (this.activeName == 'four') {
                    this.$refs.reject.onSearch();
                }
            })
        },
        async onTabClick(){
           await this.onSearch();
        }
    },
};
</script>

<style lang="scss" scoped>
.three .el-tabs__item{
    color: red;
  }
</style>
