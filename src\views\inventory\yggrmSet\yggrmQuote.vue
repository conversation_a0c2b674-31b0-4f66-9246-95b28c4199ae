<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="阳光隔热膜报价测算" name="first" style="height: 100%;" v-if="checkPermission('yggrmQuotePage')">
                <yggrmQuotePage />
            </el-tab-pane>
            <el-tab-pane label="透明桌垫报价测算" name="second" style="height: 100%;" lazy
                v-if="checkPermission('tmzdQuotePage')">
                <tmzdQuotePage />
            </el-tab-pane>
            <el-tab-pane label="皮桌垫报价测算" name="third" style="height: 100%;" lazy
                v-if="checkPermission('pzdQuotePage')">
                <pzdQuotePage />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import yggrmQuotePage from './components/yggrmQuotePage.vue';
import tmzdQuotePage from './components/tmzdQuotePage.vue';
import pzdQuotePage from './components/pzdQuotePage.vue';
export default {
    components: {
        MyContainer, yggrmQuotePage, tmzdQuotePage, pzdQuotePage
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>