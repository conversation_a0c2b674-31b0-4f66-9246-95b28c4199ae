<template>
    <div>
        <!-- 合同签订 -->
        <div class="des-box">
            <el-descriptions title="合同签订" :column="3" size="medium" :colon="false">
                <template slot="extra">
                    <!-- <el-button type="info" circle plain icon="el-icon-plus" size="mini" @click="toggleContent"></el-button> -->
                    <i @click="toggleContent()" class="el-icon-d-arrow-right"
                        :class="{ arrowTransform: !isOpen, arrowTransformReturn: isOpen }"></i>
                </template>
                <el-descriptions-item label="" :span="3" v-if="isEdit">
                    <el-collapse v-model="activeName">
                        <el-collapse-item name="content">
                            <el-form :model="ruleForm" ref="contractForm" label-width="130px"
                                :border="false">
                                <div style="width: 850px;">
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="劳动合同" prop="isContract">
                                            <el-select v-model="ruleForm.isContract" placeholder="是否签订劳动合同"
                                                style="width: 100%;">
                                                <el-option label="已签订" :value="true"></el-option>
                                                <el-option label="未签订" :value="false"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8" v-if="ruleForm.isContract">
                                        <el-form-item label="劳动合同编号" prop="contractNumber">
                                            <el-input v-model="ruleForm.contractNumber"  maxlength="40" placeholder="请输劳动合同编号"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>
                                <el-row >
                                    <el-col :span="8" >
                                        <el-form-item label="合同签订日期" prop="contractSigningDate" v-if="ruleForm.isContract">
                                            <el-date-picker v-model="ruleForm.contractSigningDate" type="date" value-format="yyyy-MM-dd"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="合同到期日期" prop="contractExpirationDate" v-if="ruleForm.isContract">
                                            <el-date-picker v-model="ruleForm.contractExpirationDate" type="date" clearable
                                                value-format="yyyy-MM-dd" @change="changecontractExpirationDate"
                                                style="width: 100%;" :picker-options="pickerOptions">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="合同剩余天数" prop="remainingDates" v-if="ruleForm.isContract">
                                            <el-input v-model="ruleForm.remainingDates" type="number" disabled></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="是否购买商业保险" prop="hasCommercialInsurance">
                                            <el-select v-model="ruleForm.hasCommercialInsurance" placeholder="请选择"
                                                style="width: 100%;">
                                                <el-option label="是" :value="true"></el-option>
                                                <el-option label="否" :value="false"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="购买保险日期" prop="commercialInsurancePurchaseDate" 
                                            v-if="ruleForm.hasCommercialInsurance">
                                            <el-date-picker v-model="ruleForm.commercialInsurancePurchaseDate" type="date" value-format="yyyy-MM-dd"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <!-- <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item> -->
                            </el-form>
                        </el-collapse-item>
                    </el-collapse>
                </el-descriptions-item>
                <el-descriptions-item label="" :span="3" v-else>
                    <el-descriptions title="" :column="3" size="medium" :colon="false">
                        <el-descriptions-item label="劳动合同：">{{ candidateInfo.isContract?'已签':'未签' }}</el-descriptions-item>
                        <el-descriptions-item label="劳动合同编号：" span="2">{{ candidateInfo.contractNumber }}</el-descriptions-item>
                        <el-descriptions-item label="合同签订日期：">{{ candidateInfo.contractSigningDate }}</el-descriptions-item>
                        <el-descriptions-item label="合同到期日期：">{{ candidateInfo.contractExpirationDate }}</el-descriptions-item>
                        <el-descriptions-item label="合同剩余日期：">{{ ruleForm.remainingDates }}</el-descriptions-item>
                        <el-descriptions-item label="" span="3">
                            <el-collapse v-model="activeName">
                                <el-collapse-item name="content">
                                    <el-descriptions title="" :column="3" size="medium" :colon="false">
                                        <el-descriptions-item label="银行卡号：">{{ candidateInfo.bankAccountNumber
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="银行：">{{ candidateInfo.bank }}</el-descriptions-item>
                                        <el-descriptions-item label="持卡人姓名：">{{ candidateInfo.cardholderName
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="是否购买商业保险：">{{ candidateInfo.hasCommercialInsurance?'是':'否'
                                        }}</el-descriptions-item>
                                        <el-descriptions-item label="购买商业保险时间：">{{ candidateInfo.commercialInsurancePurchaseDate
                                        }}</el-descriptions-item>
                                    </el-descriptions>
                                </el-collapse-item>
                            </el-collapse>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-divider></el-divider>
    </div>
</template>
  
<script>
export default {
    name: "contract",//合同签订
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {
                }
            }
        },
    },
    data () {
        return {
            activeName: '',
            isOpen: false,
            // isEdit: false,
            ruleForm: {
                isContract: null,
                contractNumber: null,
                contractSigningDate: null,
                contractExpirationDate: null,
                remainingDates: null,
                hasCommercialInsurance: null,
                commercialInsurancePurchaseDate: null,
            },
            pickerOptions: {
                disabledDate: (time) => {
                    return time.getTime() < (new Date(this.ruleForm.contractSigningDate)).getTime()
                },
            },
        }
    },
    mounted () {
        for (const prop in this.candidateInfo) {
            if (prop in this.ruleForm) {
                this.ruleForm[prop] = this.candidateInfo[prop];
            }
        }
        if (this.candidateInfo.contractExpirationDate) {
            this.changecontractExpirationDate(this.candidateInfo.contractExpirationDate);
        }
    },
    methods: {
        reset () {
            this.ruleForm = {
                isContract: null,
                contractNumber: null,
                contractSigningDate: null,
                contractExpirationDate: null,
                remainingDates: null,
                hasCommercialInsurance: null,
                commercialInsurancePurchaseDate: null,
            }
        },
        changecontractExpirationDate (data) {
            if (data) {
                var date = new Date();
                this.ruleForm.remainingDates = parseInt((Date.parse(data) - Date.parse(date)) / (1 * 24 * 60 * 60 * 1000))+1 
                this.candidateInfo.remainingDates = parseInt((Date.parse(data) - Date.parse(date)) / (1 * 24 * 60 * 60 * 1000)) + 1
                if (this.ruleForm.remainingDates < 0 ) {
                    this.ruleForm.remainingDates = 0
                }
                if (this.candidateInfo.remainingDates < 0 ) {
                    this.candidateInfo.remainingDates = 0
                }
            }

        },
        toggleContent () {
            this.isOpen = !this.isOpen
            this.activeName = this.isOpen ? 'content' : ''
        },
        //提交
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 10px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}

/* ::v-deep .el-collapse-item__header.is-active {
    display: none;
} */
::v-deep .el-collapse-item__wrap {
    border-bottom: none;
}

::v-deep .el-collapse {
    border: none;
}

::v-deep .el-collapse-item__header {
    display: none;
}

::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}

.arrowTransformReturn {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(90deg);
}

.arrowTransform {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(0deg);
}
</style>