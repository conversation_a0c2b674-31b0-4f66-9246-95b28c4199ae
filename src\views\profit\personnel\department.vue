<template>
    <el-card style="margin-top: 5px;">
        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
            <el-tab-pane label="部门" name="first1">
                <template v-if="activeName == 'first1'">
                    <div class="table-top">
                        <div>招聘部门</div>
                        <div>
                            <el-link :underline="false" type="primary" @click="onExport">下载报表</el-link>
                        </div>
                    </div>
                    <!-- <vxetablebase ref="HrHomePageDeptList" :height="'400px'" :tableData='departmentlist'
                        :tableCols='tableColsDepartment' :that='that' @sortchange='sortchange' :loading='listLoading'
                        :hascheck='false' :hasSeq='false' :showToolbar='false'>
                    </vxetablebase> -->
                    <vxe-table :height="'380px'" :data="departmentlist" size="mini" stripe :footer-method="()=>resultdata" show-footer
                    :loading='listLoading' @scroll="getScroll">
                    <template v-for="(col, colIndex) in tableColsDepartment">
                        <vxe-column :field="col.prop" :title="col.label" :width="col.width" :sortable="!!col.sortable">
                            <template #default="{ row }" v-if="col.formatter">
                                {{ col.formatter ? col.formatter(row) : row[col.prop] }}
                            </template>
                        </vxe-column>
                    </template>
                </vxe-table>
                </template>
            </el-tab-pane>
            <el-tab-pane label="岗位" name="first2" :lazy="true">
                <template v-if="activeName == 'first2'">
                    <div class="table-top">
                        <div>招聘岗位</div>
                        <div>
                            <el-link :underline="false" type="primary" @click="onExport">下载报表</el-link>
                        </div>
                    </div>
                    <!-- <vxetablebase :height="'400px'" :tableData='postList' ref="'HrHomePagePositList'" :tableCols='tableColsPost'
                    :that='that' @sortchange='sortchange' :loading='listLoading1' :hascheck='false' :hasSeq='false'
                    :showToolbar='false'>
                </vxetablebase> -->
                <vxe-table :height="'380px'" :data="postList" size="mini" stripe :footer-method="()=>postdata" show-footer
                    :loading='listLoading1' @scroll="getScroll">
                    <template v-for="(col, colIndex) in tableColsPost">
                        <vxe-column :field="col.prop" :title="col.label" :width="col.width" :sortable="!!col.sortable">
                            <template #default="{ row }" v-if="col.formatter">
                                {{ col.formatter ? col.formatter(row) : row[col.prop] }}
                            </template>
                        </vxe-column>
                    </template>
                </vxe-table>
                </template>
            </el-tab-pane>
        </el-tabs>
    </el-card>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import { hrHomePageDeptList, exportHrHomePageDeptList, hrHomePagePositionList, exportHrHomePagePositionList, hrHomePageDeptList_Stat, hrHomePagePositionList_Stat } from "@/api/profit/hr"
import bus from './bus.js';
const tableColsDepartment = [
    { istrue: true, prop: 'department', label: '部门名称', sortable: true  },
    // { istrue: true, prop: 'positionName', label: '招聘岗位' },
    { istrue: true, prop: 'positionCountSum', label: '招聘岗位数' , sortable: true },
    { istrue: true, prop: 'recruitmentCountSum', label: '预计招聘', sortable: true  },
    { istrue: true, prop: 'yaoYueCountSum', label: '邀约人数', width: '100' , sortable: true },
    { istrue: true, prop: 'comeCountSum', label: '到面人数', width: '100', sortable: true  },
    { istrue: true, prop: 'initialTestPassCountSum', label: '初试通过', sortable: true  },
    { istrue: true, prop: 'finalTestPassCountSum', label: '复试通过', sortable: true  },
    { istrue: true, prop: 'initialTestPassCountSumRate', label: '初试通过率', sortable: true,formatter: (row) => row.initialTestPassCountSumRate ? row.initialTestPassCountSumRate + "%" : ""},
    { istrue: true, prop: 'finalTestPassCountSumRate', label: '复试通过率', sortable: true,formatter: (row) => row.finalTestPassCountSumRate ? row.finalTestPassCountSumRate + "%" : ""},
    { istrue: true, prop: 'lostCountSum', label: '流失人数', width: '100', sortable: true  },
    { istrue: true, prop: 'lostCountSumRate', label: '流失率（%）', sortable: true,formatter:(row) =>  row.lostCountSumRate ? row.lostCountSumRate + "%" : ""},
    { istrue: true, prop: 'employeeCountSum', label: '入职人数', width: '100' , sortable: true },
    { istrue: true, prop: 'employeeRate', label: '入职率（%）', sortable: true,formatter:(row) => row.employeeRate ? row.employeeRate + "%" : "" },
    { istrue: true, prop: 'employeeNowCountSum', label: '留存人数', width: '100', sortable: true  },
    { istrue: true, prop: 'employeeNowCountSumRate', label: '留存率（%）', sortable: true,formatter:(row) => row.employeeNowCountSumRate ? row.employeeNowCountSumRate + "%" : ""},

];
const tableColsPost
    = [
        { istrue: true, prop: 'department', label: '部门名称', sortable: true  },
        { istrue: true, prop: 'positionName', label: '岗位名称', sortable: true  },
        { istrue: true, prop: 'recruitmentCountSum', label: '预计招聘', sortable: true  },
        { istrue: true, prop: 'yaoYueCountSum', label: '实际招聘', sortable: true  },
        { istrue: true, prop: 'comeCountSum', label: '到面人数', width: '100', sortable: true  },
        { istrue: true, prop: 'initialTestPassCountSum', label: '初试通过', sortable: true  },
        { istrue: true, prop: 'finalTestPassCountSum', label: '复试通过' , sortable: true },
        { istrue: true, prop: 'employeeCountSum', label: '入职人数', width: '100', sortable: true  },
        { istrue: true, prop: 'employeeRate', label: '入职率（%）', sortable: true,formatter:(row) => row.employeeRate ? row.employeeRate + "%" : ""  },
        { istrue: true, prop: 'lostCountSum', label: '流失人数', sortable: true  },
        { istrue: true, prop: 'lostCountSumRate', label: '流失率（%）', sortable: true,formatter:(row) => row.lostCountSumRate ? row.lostCountSumRate + "%" : ""  },
        { istrue: true, prop: 'dimissionCountSum', label: '离职人数', width: '100', sortable: true  },
        { istrue: true, prop: 'dimissionRate', label: '离职率（%）', sortable: true,formatter:(row) => row.dimissionRate ? row.dimissionRate + "%" : ""   },

    ];
export default {
    name: "department", // 部门
    components: {
        MyContainer, vxetablebase
    },
    data () {
        return {
            resultdata:[],
            postdata: [],
            that: this,
            pageLoading: false,
            activeName: "first1",
            listLoading: false,
            listLoading1: false,
            departmentlist: [],
            postList: [],
            tableColsPost: tableColsPost,
            tableColsDepartment: tableColsDepartment,
            filter: {
                currentPage: 1,
                pageSize: 1000,
                orderBy: 'employeeRate',
                isAsc: true,
                startDate: null,
                endDate: null,
                deptId: 0,
                position: null,
            },
            filter1: {
                currentPage: 1,
                pageSize: 1000,
                orderBy: 'employeeRate',
                isAsc: true,
                startDate: null,
                endDate: null,
                deptId: 0,
                position: null,
            },
            total: 0,
            total1: 0,
        };
    },
    async mounted () {
        await bus.$on('filter', data => {
            for (const prop in data) {
                if (prop in this.filter) {
                    this.filter[prop] = data[prop];
                    this.filter1[prop] = data[prop];
                }
            }
            this.getDept();
            this.getPosit();
        })
    },
    methods: {
        getScroll (type) {
            if (type.scrollHeight - type.scrollTop - 380 == -36) {
                if (this.activeName == 'first1') {
                    if (this.total > this.departmentlist.length) {
                        this.filter.currentPage++
                        hrHomePageDeptList(this.filter).then(res => {
                            if (res.success) {
                                this.departmentlist = this.departmentlist.concat(res.data.list);
                            }
                        })
                    }
                } else {
                    if (this.total1 > this.postList.length) {
                        this.filter1.currentPage++
                        hrHomePagePositionList(this.filter1).then(res => {
                            if (res.success) {
                                this.postList = this.postList.concat(res.data.list);
                            }
                        })
                    }
                }

            }
        },
        // getScroll1 (type) {
        //     if (type.scrollHeight - type.scrollTop - 100 == -36) {
        //         if (this.total1 > this.postList.length) {
        //             this.filter1.currentPage++
        //             hrHomePageApplicantList(this.filter1).then(res => {
        //                 if (res.success) {
        //                     this.postList = this.postList.concat(res.data.list);
        //                 }
        //             })
        //         }
        //     }
        // },
        // 下载
        async onExport () {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }
            if (params === false) {
                return;
            }
            let name = '';
            if (this.activeName == 'first1') {
                var res = await exportHrHomePageDeptList(params);//下载接口
                name = '招聘部门数据导出_'
            } else {
                var res = await exportHrHomePagePositionList(params);//下载接口
                name = '招聘岗位数据导出_'
            }
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', name + new Date().toLocaleString() + '.xlsx')
            aLink.click()

        },
        //列表排序
        sortchange (column) {
            if (column.order) {
                let pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.filter.orderBy = pager.OrderBy
                this.filter.isAsc = pager.IsAsc
            }
            if (this.activeName == 'first1') {
                this.getDept();
            } else {
                this.getPosit();
            }
        },
        formatNumber(number){
            const absNumber = Math.abs(number);
            const options = {
              minimumFractionDigits: absNumber >= 100 ? 0 : 2,
              maximumFractionDigits: absNumber >= 100 ? 0 : 2,
                    };
          return new Intl.NumberFormat('zh-CN', options).format(number);
                }, 

        async getDept () {
          //获取部门数据汇总行
          var statResult = await hrHomePageDeptList_Stat(this.filter)
            if (!statResult.success) {
                return;
              }
            let summary = statResult.data.data;
            var temp = [];
            this.tableColsDepartment.forEach(prop => {
                var num1=summary[prop.prop]
                   if (num1 == null) ;
                   else if ((typeof num1 == 'string') && num1.constructor == String) num1=num1;
                   else if (Math.abs(parseInt(num1)) < 100) num1 = num1.toFixed(2);
                  else num1 = this.formatNumber(num1);
                temp.push(num1)
              });
            this.resultdata = [temp];
            //获取部门数据
            this.listLoading = true;
            hrHomePageDeptList(this.filter).then(res => {
                if (res.success) {
                    this.listLoading = false;
                    res.data.list.forEach(item => {
                        for (const prop in item) {
                            if (typeof item[prop] == 'string' && item[prop].indexOf('%') > -1) {
                                item[prop] = item[prop].replace('%', '');
                            }
                        }
                    });
                    this.departmentlist = res.data.list;
                    this.total = res.data.total;
                }
            });
            return this.resultdata
        },
        async getPosit () {
          var poststatResult = await hrHomePagePositionList_Stat(this.filter)
            if(!poststatResult.success){
              return
            }
            let postsum = poststatResult.data.data;
            var posttemp = [] ;
            this.tableColsPost.forEach(prop => {
               var num1=postsum[prop.prop]
                   if (num1 == null) ;
                   else if ((typeof num1 == 'string') && num1.constructor == String) num1=num1;
                   else if (Math.abs(parseInt(num1)) < 100) num1 = num1.toFixed(2);
                  else num1 = this.formatNumber(num1);

                posttemp.push(num1)
              });
            this.postdata = [posttemp]
            this.listLoading1 = true;
            hrHomePagePositionList(this.filter).then(res => {
                if (res.success) {
                    this.listLoading1 = false;
                    res.data.list.forEach(item => {
                        for (const prop in item) {
                            if (typeof item[prop] == 'string' && item[prop].indexOf('%') > -1) {
                                item[prop] = item[prop].replace('%', '');
                            }
                        }
                    });
                    this.postList = res.data.list;
                    this.total1 = res.data.total;
                }
            });
            return this.postdata;
        },

        load () {
            // this.departmentlist.push(1)
        }
    },
};
</script>

<style lang="scss" scoped>
.table-top {
    padding: 10px 0 0 0;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

::v-deep .el-button-group {
    display: none;
}
</style>
