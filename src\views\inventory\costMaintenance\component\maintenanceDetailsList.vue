<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 210px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.finishedProductId" placeholder="成品编号" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.finishedProductCode" placeholder="成品编码" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.finishedProductName" placeholder="成品名称" maxlength="50" clearable
          class="publicCss" />
        <el-select v-model="ListInfo.isNew" placeholder="是否最新" class="publicCss" filterable clearable>
          <el-option key="是" label="是" :value="1" />
          <el-option key="否" label="否" :value="0" />
        </el-select>
        <el-input v-model.trim="ListInfo.halfProductNo" placeholder="半成品编号" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.halfProductCode" placeholder="半成品编码" maxlength="50" clearable
          class="publicCss" />
        <el-input v-model.trim="ListInfo.halfProductName" placeholder="半成品名称" maxlength="50" clearable
          class="publicCss" />
        <el-select v-model="ListInfo.codeConsistent" placeholder="编码是否一致" class="publicCss" filterable clearable>
          <el-option key="是" label="是" :value="1" />
          <el-option key="否" label="否" :value="0" />
        </el-select>
        <el-select v-model="ListInfo.processTypeId" placeholder="加工方式" filterable clearable class="publicCss">
          <el-option v-for="item in machineTypeList" :key="item.setId" :label="item.sceneCode" :value="item.setId" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
        <el-button type="primary" @click="exportPull">拉取</el-button>
      </div>
    </template>
    <vxetablebase :id="'maintenanceDetailsList202507191107'" :tablekey="'maintenanceDetailsList202507191107'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :border="true"
      :treeProp="{ childrenField: 'detailList' }">
      <template slot="right">
        <vxe-column title="操作" width="70" fixed="right">
          <template #default="{ row, rowIndex }">
            <div style="display: flex;justify-content: center;" v-if="!row.detailList">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="编辑" :visible.sync="editInfo.visible" width="30%" :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <el-form ref="editForm" :model="editInfo.data" :rules="editRules" label-width="100px">
        <el-form-item label="所需半成品" prop="quantityRequired">
          <el-input-number v-model="editInfo.data.quantityRequired" :min="0" :max="999999" style="width: 80%;"
            :controls="false" :precision="4" placeholder="请输入所需半成品"></el-input-number>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editInfo.data.remark" type="textarea" :autosize="{ minRows: 4, maxRows: 4 }"
            placeholder="请输入备注" style="width: 80%;"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editInfo.visible = false">取 消</el-button>
        <el-button type="primary" @click="saveEdit">确 定</el-button>
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { GetPackagesProcesingCostMaintenanceList, ExportPackagesProcesingCostMaintenanceList, ModifyPackagesProcesingCostMaintenance, PullPackagesProcesingHalfProductInfo } from '@/api/cwManager/costMaintenanceManager'
import { getShootingSetData } from '@/api/media/shootingset'
import dayjs from 'dayjs'
const tableCols = [
  { fixed: "left", sortable: 'custom', width: '100', align: 'center', prop: 'pullDate', label: '拉取日期', treeNode: true },
  { sortable: 'custom', width: '140', align: 'center', prop: 'id', label: '成品编号', formatter: (row) => row.id && row.detailList && row.detailList.length > 0 ? row.id : '' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'finishedProductCode', label: '成品编码' },
  { sortable: 'custom', width: '200', align: 'center', prop: 'finishedProductName', label: '成品名称' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isNew', label: '是否最新', formatter: (row) => row.isNew == 1 ? '是' : row.isNew == 0 ? '否' : '' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'halfProductNo', label: '半成品编号' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'codeConsistent', label: '编码是否一致', formatter: (row) => row.codeConsistent == 1 && !row.detailList ? '是' : row.codeConsistent == 0 && !row.detailList ? '否' : '' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'halfProductCode', label: '半成品编码' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'halfProductName', label: '半成品名称' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'halfProductCost', label: '半成品成本' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'halfProductQuantity', label: '半成品组合' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'quantityRequired', label: '所需半成品' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'dispatchWorkPrice', label: '加工工价' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'machineTypeName', label: '加工方式' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'halfProductCombination', label: '组合' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'newCost', label: '新成本' },
  { sortable: 'custom', width: '150', align: 'center', prop: 'remark', label: '备注' },
  { sortable: 'custom', width: '120', align: 'center', prop: 'modifiedTime', label: '维护日期' },
]
export default {
  name: "maintenanceDetailsList",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      editRules: {
        quantityRequired: [{ required: true, message: '请输入所需半成品', trigger: 'blur' }],
      },
      editInfo: {
        visible: false,
        data: {},
      },
      machineTypeList: [],
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startCreateTime: null,//开始时间
        endCreateTime: null,//结束时间
        finishedProductId: '',//成品编号
        finishedProductCode: null,//成品编码
        finishedProductName: null,//成品名称
        isNew: null,//是否最新
        halfProductNo: null,//半成品编号
        codeConsistent: null,//编码是否一致
        halfProductCode: null,//半成品编码
        halfProductName: null,//半成品名称
        processTypeId: null,//加工方式
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    exportPull() {
      this.$confirm('确认拉取吗?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(async () => {
        const { success } = await PullPackagesProcesingHalfProductInfo()
        if (success) {
          this.$message.success('拉取成功,几分钟后刷新列表查看数据')
          this.getList()
        }
      }).catch(() => {
      });
    },
    handleEdit(row) {
      this.editInfo.data = JSON.parse(JSON.stringify(row))
      this.editInfo.visible = true
      this.$nextTick(() => {
        this.$refs.editForm.clearValidate()
      })
    },
    async saveEdit() {
      this.$refs.editForm.validate(async valid => {
        if (valid) {
          const { data, success } = await ModifyPackagesProcesingCostMaintenance(this.editInfo.data)
          if (success) {
            this.$message.success('修改成功')
            this.editInfo.visible = false
            this.getList()
          } else {
            this.$message.error('修改失败')
          }
        }
      })
    },
    async init() {
      const res = await getShootingSetData({ setType: 16 })
      this.machineTypeList = res.data.data.map(obj => ({ setId: obj.setId, sceneCode: obj.sceneCode }));
    },
    async changeTime(e) {
      this.ListInfo.startCreateTime = e ? e[0] : null
      this.ListInfo.endCreateTime = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const { data } = await ExportPackagesProcesingCostMaintenanceList(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '加工成本维护明细表' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startCreateTime = dayjs().format('YYYY-MM-DD')
        this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
      }
      this.loading = true
      const { data, success } = await GetPackagesProcesingCostMaintenanceList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.pullDate = item.pullDate ? dayjs(item.pullDate).format('YYYY-MM-DD') : ''
          item.modifiedTime = item.modifiedTime ? dayjs(item.modifiedTime).format('YYYY-MM-DD HH:mm:ss') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 5px;
  }
}
</style>
