<template>
    <MyContainer>
        <!-- 查询条件 -->
      <template #header>
        <div class="top">
            <el-date-picker class="publicCss" v-model="ListInfo.yearMonth" type="month" format="yyyyMM"
            value-format="yyyyMM" placeholder="请选择月份" :clearable="false" style="width: 130px;">
            </el-date-picker>
          <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" class="top_button" @click="exportProps" :disabled="isExport">导出</el-button>
        </div>
      </template>
      <!-- table表单 -->
      <vxetablebase :id="'jdSelfSkuPriceMonthRpt202505231530'" :tablekey="'jdSelfSkuPriceMonthRpt202505231530'"
        ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
        :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
      </vxetablebase>
      <!-- 页签 -->
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { getJDSelfSkuPriceMonthRpt,exportJDSelfSkuPriceMonthRpt } from '@/api/monthbookkeeper/financialDetail'
  import dayjs from 'dayjs'
  const tableCols = [
    { sortable: 'custom', width: '100', align: 'center', prop: 'yearMonth', label: '年月', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'openingBalance', label: '期初余额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'openingQty', label: '期初数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'todayBalance', label: '当期金额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'todayQty', label: '当期数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'todayPrice', label: '当期单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'ckBalance', label: '出库金额'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'ckQty', label: '出库数量'},
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'endBalance', label: '期末余额', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'endQty', label: '期末数量', },
  ]
  export default {
    name: "jdSelfSkuPriceMonthRpt",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          yearMonth: dayjs().subtract(1, 'month').format('YYYYMM'),//年月
          goodsCode: '',//商品编号
        },
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
        isExport:false,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
      //查询
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await getJDSelfSkuPriceMonthRpt(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          this.$message.error('获取列表失败')
        }
      },
      //导出
      async exportProps() {
        if (!this.ListInfo.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
        }
        let pager = this.$refs.pager.getPager();
        const params = { ...pager, ...this.pager, ...this.ListInfo };
        this.isExport = true
        let res = await exportJDSelfSkuPriceMonthRpt(params);
        this.isExport = false
        if (!res?.data) {
            this.$message({ message: "没有数据", type: "warning" });
            return
        }
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', '编码期初期末_' + new Date().toLocaleString() + '_.xlsx')
        aLink.click()
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      //排序
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }
  </style>
  