<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="评价任务" name="tab1" style="height: 100%;">
                <commenttasklist ref="commenttasklist" />
            </el-tab-pane>
            <el-tab-pane label="评价订单" name="tab2" style="height: 100%;" lazy>
                <commenttaskorderlist ref="commenttaskorderlist" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
    import MyContainer from "@/components/my-container";
    import commenttasklist from '@/views/operatemanage/productalllink/commenttask/commenttasklist.vue';
    import commenttaskorderlist from '@/views/operatemanage/productalllink/commenttask/commenttaskorderlist.vue';

    export default {
        name: "commenttaskindex",
        components: { MyContainer, commenttasklist, commenttaskorderlist },
        data() {
            return {
                that: this,
                pageLoading: '',
                filter: {
                },
                shopList: [],
                userList: [],
                groupList: [],
                selids: [],
                dialogVisibleSyj: false,
                fileList: [],
                activeName: 'tab1'
            };
        },
        mounted() {

        },
        methods: {


        },
    };
</script>
<style lang="scss" scoped>
    .my-search ::v-deep .el-input-group__prepend {
        background-color: #fff;
    }
</style>
