<template>
  <my-container>
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="快递费规则" name="first1" style="height: 99%">
        <expressrule ref="refexpressrule" />
      </el-tab-pane>
      <!-- <el-tab-pane label="其他费用规则" name="first2" style="height: 99%" lazy>
        <otherFeeRules ref="refotherFeeRules" />
      </el-tab-pane> -->
      <el-tab-pane label="操作包材加收" name="first3" style="height: 99%" lazy>
        <handlingMaterial ref="refhandlingMaterial" />
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import expressrule from "./expressrule.vue";
import otherFeeRules from "./otherFeeRules.vue";
import handlingMaterial from "./handlingMaterial.vue";
export default {
  name: "expressFeeIndex",
  components: {
    MyContainer, expressrule, otherFeeRules, handlingMaterial
  },
  data() {
    return {
      activeName: "first1",
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
