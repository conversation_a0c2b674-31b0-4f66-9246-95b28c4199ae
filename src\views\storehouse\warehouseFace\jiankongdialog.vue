<template>
    <my-container v-loading="pageLoading">
      <template #header>
        <el-form ref="form" class="ad-form-query" :rules="formRules" label-position="right" label-width="100px" :inline="true" :model="filter" @submit.native.prevent>
            <el-row>
                <el-col :span="12">
                    <el-form-item label-position="right" prop="deviceName" label="设备名称">
                        <el-input clearable maxlength="50" style="width: 350px" v-model="filter.deviceName" placeholder="请输入设备名称" @input="handleEditSearchVisitorNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label-position="right" prop="deviceLocation" label="设备位置">
                        <el-input clearable maxlength="50" style="width: 350px" v-model="filter.deviceLocation" placeholder="请输入设备位置" @input="handleEditSearchVisitorNumber" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label-position="right" prop="deviceMac" label="MAC地址">
                        <el-input clearable maxlength="50" style="width: 350px" v-model="filter.deviceMac" placeholder="MAC地址" @input="handleEditSearchVisitorNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="仓库" prop="warehouseId">
                        <el-select clearable style="width: 350px" v-model="filter.warehouseId" placeholder="请选择仓库"  clearable  >
                            <el-option v-for="item in Warehouseslist" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="设备号" prop="deviceCode">
                        <el-input clearable maxlength="50" style="width: 350px" v-model="filter.deviceCode" placeholder="设备号" @input="handleEditSearchVisitorNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="通知人" prop="notifyUserIdsArr">
                        <YhUserelector ref="yhUserelector" :value.sync="filter.notifyUserIdsArr" maxlength="50" @change="getUserList($event, $index)"
                             :textmap.sync="filter.notifyUserMapArr" style="width:350px; margin-right: 10px;" placeholder="请输入通知人">
                        </YhUserelector>
                    </el-form-item>
                </el-col>
            </el-row>

        <el-row>
            <el-col :span="12" style="display: flex;">
                <el-button @click="closeSubmit" type="" style="margin-left: auto; margin-right: 30px; margin-bottom: 20px;">取消</el-button>
            </el-col>
            <el-col :span="12" style="display: flex;">
                <el-button @click="saveSubmit" type="primary" style="margin-right: auto; margin-left: 30px; margin-bottom: 20px;">确认</el-button>
            </el-col>
        </el-row>
          <!-- <el-form-item>
            <el-button type="primary" @click="getList">筛选</el-button>
            <el-button v-if="checkPermission('api:operatemanage:productmanager:BatchDownProductAsync')" type="primary"
              @click="batchDown">批量下架</el-button>
          </el-form-item> -->
        </el-form>
      </template>
      <ces-table ref="table" :toolbarshow="false" :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false"
        :tableData='list' :tableCols='tableCols' :loading="listLoading"  :hasexpandRight="true">
        <template slot="right">
            <vxe-column title="操作" width="140" fixed="right">
                <template #default="{ row, $index }">
                    <div style="display: flex;text-align: center; width: 100%;">
                        <el-button type="primary" @click="rowedit(row)">修改</el-button>
                        <el-button type="danger" @click="rowdelete(row)">删除</el-button>
                    </div>
                </template>
            </vxe-column>
        </template>
      </ces-table>
      <template #footer>
        <my-pagination ref="pager" :total="total" @get-page="getList" />
      </template>
  
      <!-- <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
        <span>
          <template>
            <el-form class="ad-form-query" :model="detailfilter" @submit.native.prevent label-width="100px">
              <el-row>
                <el-col :xs="24" :sm="5" :md="5" :lg="5" :xl="5">
                  <el-form-item label="日期:">
                    <el-date-picker style="width: 260px" v-model="detailfilter.timerange" type="daterange"
                      format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                      end-placeholder="结束日期" :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                  <el-form-item>
                    <el-button type="primary" @click="getecharts">刷新</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </span>
        <span>
          <buschar v-if="buscharDialog.visible" ref="buschar" :analysisData="buscharDialog.data"></buschar>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="buscharDialog.visible = false">关闭</el-button>
        </span>
      </el-dialog> -->
  
    </my-container>
  
  </template>
  
  <script>
  import MyContainer from '@/components/my-container'
  import cesTable from "@/components/VxeTable/yh_vxetable.vue";
  import { formatTime, formatLinkProCode } from "@/utils/tools";
  import {  queryCanLowerShelfProductsAsync, batchDownProducts } from '@/api/operatemanage/base/product'
  import * as echarts from 'echarts'
  import dayjs from "dayjs";
//   import buschar from '@/components/Bus/buschar'
  import { getDirectorList, getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop'
  import YhUserelector from '@/components/YhCom/yh-userselectorMany.vue'
  import { equipmentAlarmSettingPage, equipmentAlarmSettingSubmit, equipmentAlarmSettingRemove } from "@/api/bladegateway/worcestorejk.js"
  
  const tableCols = [
    // { istrue: true, prop: 'proCode', label: 'ID', width: '200', sortable: 'custom', type: 'html' ,formatter:(row)=>formatLinkProCode(1,row.proCode) },
    { istrue: true, prop: 'deviceName', label: '设备名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'deviceMac', label: 'MAC地址', width: '180', sortable: 'custom' },
    { istrue: true, prop: 'deviceCode', label: '设备号', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'deviceLocation', label: '设备位置', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'notifyUserNames', label: '通知人', width: 'auto', sortable: 'custom', formatter: (row) => row.notifyUserNames },
    // { istrue: true, prop: 'goodsPurchasedPeopleNumber', label: '操作', width: '120', sortable: 'custom' },
  ];
  
  const endTime = formatTime(new Date(), "YYYY-MM-DD");
  const star = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
  
  export default {
    name: "txProductLowerShelf",
    components: { MyContainer, cesTable,  YhUserelector },
    props: ['Warehouseslist'],
    data() {
      return {
        pageLoading: false,
        filter: {
        //   OrderCount: '',
        //   SearchVisitorNumber: '',
        //   GoodsPurchasedPeopleNumber: '',
        //   PayBuyNumber: '',
        //   OnlineDay: '',
        //   shopId: '',
        //   groupId: '',
        //   operateSpecialId: ''
        },
        // filter: {},
        list: [],
        tableCols: tableCols,
        pager: { OrderBy: "", IsAsc: false },
        listLoading: false,
        echartsLoading: false,
        total: 0,
        detailfilter: {
          procode: null,
          startTime: null,
          endTime: null,
          timerange: [star, endTime]
        },
        formRules: {
            notifyUserIdsArr: [{ required: true, message: '请输入通知人', trigger: 'blur' }],
            deviceCode: [{ required: true, message: '请输入设备号', trigger: 'blur' }],
            warehouseId: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
            deviceMac: [{ required: true, message: '请输入mac地址', trigger: 'blur' }],
            deviceLocation: [{ required: true, message: '请输入设备位置', trigger: 'blur' }],
            deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }]
        },
        buscharDialog: { visible: false, title: "", data: [] },
        pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              let end = new Date();
              let start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近半个月',
            onClick(picker) {
              let end = new Date();
              let start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              let end = new Date();
              let start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        shopList: [],
        directorList: [],
        directorGroupList: [],
      }
    },
    async mounted() {
      await this.getShop()
      await this.getDirectorlist()
      await this.getList()
    },
    methods: {
        rowdelete(row){
            this.$confirm('删除该条数据，是否继续？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let params = { ...row, ids: row.id };
                let res = await equipmentAlarmSettingRemove(params);
                if (!res.success) return
                this.$message({ type: 'success', message: '删除成功' });
                // this.list.splice()
                this.list.map((item)=>{
                    if(item.id==row.id){
                        this.list.splice(this.list.indexOf(item),1)
                    }
                })
            }).catch(() => {
                // this.$message({ type: 'info', message: '已取消操作' });
            });
            
           

        },
        rowedit(row){
            this.filter = row;
            this.filter.warehouseId = Number(this.filter.warehouseId);

            // let newid = this.filter.notifyUserIds.split(",");
            // let newids = [];
            // newid.map((item)=>{
            //     newids.push(Number(item));
            // })
            // console.log("3333", newids);
           

            
            // let newarr = [];
            // this.filter.notifyUserNamesArr = this.filter.notifyUserNames.split(",");
            // this.filter.notifyUserIdsArr = this.filter.notifyUserIds.split(",");

            // let nameArr = this.filter.notifyUserNames.split(",");
            // let idsArr = this.filter.notifyUserIds.split(",");
            // nameArr.forEach((item, index)=>{
            //     let params = {
            //         value: '',
            //         label: ''
            //     };
            //     params.value = Number(idsArr[index]);
            //     params.extData = {"defaultDeptId": index};

            //     params.label = item;

            //     newarr.push(params);
            // })


            // this.$nextTick(()=>{
            //     // this.$refs.yhUserelector.showName(newids);
            //     this.$refs.yhUserelector.showName(newarr, newids);

            // });
            this.filter.notifyUserIdsArr = [];
            this.filter.notifyUserMapArr = JSON.parse(row.notifyUserMap);
            this.$refs.yhUserelector.showName(JSON.parse(row.notifyUserMap));
            JSON.parse(row.notifyUserMap).map((item)=>{
                this.filter.notifyUserIdsArr.push(item.value);
            });
            // this.$refs.yhUserelector.showName(newarr, newids);

        },
        getUserList(val, i) {
            this.setWareHouseList[i].headUid = val ? val[0].extData.userId : null
            this.setWareHouseList[i].headDuid = val ? val[0].value : null
            this.setWareHouseList[i].headName = val ? val[0].label : null
        },
        closeSubmit(){
            this.$emit('closeDialog');
        },
      async saveSubmit() {
        var istrue = true;
        this.$refs["form"].validate(async (valid) => {
        //   if (valid) {
        //     let res= await editLossOffFee(self.form);
        //     if (res.code == 1) {
        //       self.$message.success('操作成功！');
        //       self.getjSpeedDriveList();
        //       self.editVisible = false;
        //     }
        //   } else {
        //     return false;
        //   }
            istrue = valid;
            if(!valid){
                return false;
            }
        });
        if(!istrue){
            return;
        }
        // console.log(this.Warehouseslist.filter(item => item.value == this.filter.warehouseId)[0]?.label,99999999 )
        this.filter.warehouseName =  this.Warehouseslist.filter(item => item.value == this.filter.warehouseId)[0]?.label;

        // this.filter.notifyUserNames = this.filter.notifyUserNamesArr?.join(",");
        // this.filter.notifyUserIds = this.filter.notifyUserIdsArr?.join(",");
        // let nameArr = this.filter.notifyUserMapArr.map(item=>item.label);
        console.log(this.filter.notifyUserMapArr, 10000);
        let valueArr = this.filter.notifyUserMapArr.map(item=>item.value);

        this.filter.notifyUserIds = valueArr.join(",");
        this.filter.notifyUserMap = JSON.stringify(this.filter.notifyUserMapArr);



        let params = {  ...this.filter,   };
        let res = await equipmentAlarmSettingSubmit(params);
        if (!res.success) return
        this.$message.success('操作成功！');
        // if(!this.filter.id){
        //     this.list.unshift(this.filter);
        // }else{
        //     this.getList();
        // }
        this.getList();
        this.filter = {}
        this.$refs.yhUserelector.innerValue = '';
      },
      async getList() {
        let pager = this.$refs.pager.getPager();
        let params = { ...pager, ...this.pager,};
        let res = await equipmentAlarmSettingPage(params);
        if (!res.success) return
        this.list = res.data.list
        this.total = res.data.total;

        res.data.list.map((item)=>{
            let newarra = [];
            if(item.notifyUserMap){
                JSON.parse(item.notifyUserMap).filter(itemm=>{return itemm.label}).map((item2)=>{
                    newarra.push(item2.label)
                });
                item.notifyUserNames = newarra.join(',');
            }
          
         
        })

        // this.filter.notifyUserMapArr = JSON.parse(res.data.notifyUserMap);
        // this.getEcharts()
      },
      regixNum(e) {
        let value = e.replace(/^(0+)|[^\d]+/g, '');
        value = value.replace(/(\d{15})\d*/, '');
        return value
      },
      handleEditOrderCount(e) {
        this.filter.OrderCount = this.regixNum(e)
      },
      handleEditSearchVisitorNumber(e) {
        this.filter.SearchVisitorNumber =this.regixNum(e)
      },
      handleEditGoodsPurchasedPeopleNumber(e) {
        this.filter.GoodsPurchasedPeopleNumber = this.regixNum(e)
      },
      handleEditPayBuyNumber(e) {
        this.filter.PayBuyNumber = this.regixNum(e)
      },
      handleEditOnlineDay(e) {
        this.filter.OnlineDay = this.regixNum(e)
      },
      async sortchange(column) {
        if(column.prop=='notifyUserNames'){
            column.prop = 'notifyUserIds';
          }
        if (!column.order)
          this.pager = {};
        else {
          var orderField = column.prop;
          this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
        }
        await this.getList();
      },
      getEcharts() {
        setTimeout(_ => {
          this.list.forEach(e => {
            let myChart = echarts.init(this.$refs['echarts' + e.proCode]);
            var series = []
            this.echartsLoading = true
            e.series.forEach(s => {
              series.push({ smooth: true, showSymbol: false, ...s })
            })
            this.echartsLoading = false
            myChart.setOption({
              legend: {
                show: false
              },
              grid: {
                left: "0",
                top: "6",
                right: "0",
                bottom: "0",
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                //不显示x轴线
                show: false,
                data: e.xAxis
              },
              yAxis: {
                type: 'value',
                show: false,
              },
              series: series
            });
            window.addEventListener("resize", () => {
              myChart.resize();
            });
          })
        }, 1000)
      },
      async batchDown() {
        if (this.filter.OnlineDay == '' && this.filter.OrderCount == '' && this.filter.SearchVisitorNumber == ''
          && this.filter.GoodsPurchasedPeopleNumber == '' && this.filter.PayBuyNumber == '' && this.filter.shopId == '' && this.filter.groupId == '' && this.filter.operateSpecialId == '') {
          return
        }
        this.$confirm('该操作将按照筛选条件批量下架商品，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let pager = this.$refs.pager.getPager();
          let params = { ...pager, ...this.pager, ...this.filter };
          const res = await batchDownProducts(params)
          if (!res?.success) { return }
          if (res.data) {
            this.$message({ type: 'success', message: '操作成功!' });
            this.getList()
          } else {
            this.$message({ type: 'error', message: '操作失败!' });
          }
        }).catch(() => {
          this.$message({ type: 'info', message: '已取消操作' });
        });
      },
      async cellclick(row, column, cell, event) {
        if (column.label == '趋势') {
          this.detailfilter.procode = row.proCode
          this.getecharts()
        }
      },
      async getecharts() {
        this.detailfilter.startTime = null;
        this.detailfilter.endTime = null;
        if (this.detailfilter.timerange) {
          this.detailfilter.startTime = this.detailfilter.timerange[0];
          this.detailfilter.endTime = this.detailfilter.timerange[1];
        }
        let params = { ...this.detailfilter, isMedia: true }
        let that = this;
        let res = await queryCanLowerShelfProductsAsync(params).then(res => {
          that.buscharDialog.visible = true;
          that.buscharDialog.data = res.data
          that.buscharDialog.title = '趋势'
        })
        await this.$refs.buschar.initcharts()
      },
      async getShop() {
        this.categorylist = []
        let res = await getshopList({ platform: 1, CurrentPage: 1, PageSize: 100 });
        this.shopList = res.data.list
      },
      async getDirectorlist() {
        let res1 = await getDirectorList({})
        let res2 = await getDirectorGroupList({})
  
        this.directorList = res1.data
        this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
      },
    },
  }
  </script>
  
  <style>
  
  </style>