<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="dialogVisible = true">新增区间</el-button>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <vxetablebase :id="'YunHanAdminOnSetTimeRangelist20230701'" :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading='listLoading' :border='true' :that="that" ref="vxetable" />

        <el-dialog title="添加时间区间" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false"
            append-to-body>
            <div style="margin-bottom: 10px;">
                <el-tag type="success">温馨提示：时间区间间隔最大为5分钟。此时间为聚水潭店铺商品管理中的最近更新时间</el-tag>
            </div>
            <span>
                <el-form ref="ruleForm" :model="addForm" :rules="rules" label-width="100px">
                    <el-form-item label="时间区间" prop="timerange">
                        <el-date-picker v-model="addForm.timerange" type="datetimerange" start-placeholder="开始日期"
                            @change="handleTimeRangeChange" end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss" :default-time="['12:00:00']">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <my-confirm-button type="submit" :loading="onFinishLoading" :validate="editFormvalidate"
                            @click="onFinish()">保存&关闭
                        </my-confirm-button>
                        <vxe-button type="reset" size="mini" @click="dialogVisible = false">关闭</vxe-button>
                    </el-form-item>
                </el-form>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import dayjs from "dayjs";
import container from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { formatTime, formatNoLink } from "@/utils/tools";
import { addSyncShopGoodsJob_V2_TimeRange, getSyncShopGoodsJob_V2_TimeRange } from '@/api/operatemanage/base/product'

const tableCols = [
    { istrue: true, align: 'center', prop: 'startTime', label: '开始时间', width: 'auto', },
    { istrue: true, align: 'center', prop: 'endTime', label: '结束时间', width: 'auto', },
];

const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];


const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD HH:MM:SS");
const endTime = formatTime(new Date(), "YYYY-MM-DD HH:MM:SS");

export default {
    name: 'YunHanAdminOnSetTimeRange',
    components: { MyConfirmButton, container, vxetablebase },

    data() {
        return {
            that: this,
            addForm: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
            },
            rules: {
                timerange: [
                    { required: true, message: '请选择时间区间', trigger: 'blur' },
                    //{ validator: validateTimeRange, trigger: 'change' }
                ],
            },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            onFinishLoading: false
        };
    },

    async mounted() {
        //await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.getlist();
        },
        async getlist() {
            this.pageLoading = true
            var res = await getSyncShopGoodsJob_V2_TimeRange();
            this.pageLoading = false
            if (!res?.success) {
                return
            }
            const data = res.data;
            this.list = data
        },
        async onFinish() {
            this.addForm.startTime = null;
            this.addForm.endTime = null;
            if (this.addForm.timerange) {
                this.addForm.startTime = this.addForm.timerange[0];
                this.addForm.endTime = this.addForm.timerange[1];
            }
            const params = { ... this.addForm }
            this.onFinishLoading = true;
            var res = await addSyncShopGoodsJob_V2_TimeRange(params);
            this.onFinishLoading = false;
            if (!res?.success) {
                return;
            }
            this.$message({
                type: 'success',
                message: '操作成功!'
            });
            this.dialogVisible = false;
            await this.onSearch();
        },
        editFormvalidate() {
            let isValid = false
            this.$refs.ruleForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        handleTimeRangeChange() {
            const startTime = this.addForm.timerange[0];
            const endTime = this.addForm.timerange[1];

            if (startTime && endTime) {
                const diffMinutes = Math.abs(new Date(endTime) - new Date(startTime)) / (1000 * 60);
                if (diffMinutes > 5) {
                    this.$message.error('时间区间选择不能超过5分钟');
                    this.addForm.timerange = [];
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>