<template>
    <my-container>
        <el-table
          v-loading="listLoading"
          :data="persondata"
          highlight-current-row
          :lazy="true"
          style="width: 100%; height: 100%"
          @selection-change="onSelsChange"
          :default-expand-all="false"
          :row-class-name="getRowClass"
          show-summary
          :summary-method="getSummaries"
        >
        
          <!-- <el-table-column type="selection" width="40" /> -->
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column prop="productID" label="商品ID" width="120" />
          <el-table-column prop="seller" sortable label="人员" width="120">
            
            <!-- <template #default="{ row }">
          <el-tag
            :type="row.enabled ? 'success' : 'danger'"
            disable-transitions
            >{{ row.enabled ? "正常" : "禁用" }}</el-tag>
        </template> -->
          </el-table-column>

          <el-table-column
            prop="success"
            sortable
            label="成交"
            width="100"
          >
            <!-- <template #default="{ row }">
              {{ row.rensuccess + row.jiqisuccess }}
            </template> -->
          </el-table-column>

          <el-table-column
            prop="notsuccess"
            sortable
            label="未成交"
            width="100"
          >
            <!-- <template #default="{ row }">
              {{ row.renfail + row.jiqifail }}
            </template> -->
          </el-table-column>

          <el-table-column
            prop="heji"
            sortable
            label="合计咨询人数"
            width="120"
          >
            <!-- <template #default="{ row }">
              {{
                row.rensuccess + row.jiqisuccess + row.renfail + row.jiqifail
              }}
            </template> -->
          </el-table-column>

          <el-table-column prop="rate" sortable label="转化率" width="100">
            <template #default="{ row }">
              <div v-if="row.isred" style="color: red">
                {{ parseInt(row.rate * 1000) / 10 }}%
              </div>

              <div v-if="!row.isred" style="color: black">
                {{ parseInt(row.rate * 1000) / 10 }}%
              </div>
            </template>
          </el-table-column>



           <el-table-column prop="rate" sortable label="高转化率差值" width="120">
            <template #default="{ row }">
               
                {{ !row.rate ? 0 : parseInt((row.rate-showpersonrow.highrate) * 1000) / 10 }}%
              
            </template>
          </el-table-column>


        </el-table>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import {getRateDetailList,} from "@/api/customerservice/productconsulting";
export default {
    name: 'YunhanAdminPersionstatistics',
    components:{MyContainer},
    props:{
        Filter:{},
        personpager:{}
    },

    data() {
        return {
            that:this,
            persondata: [],
            sels:null,
            listLoading:false
        };
    },

    async mounted() {
        
    },

    methods: {
        OnSearch(row) {
          this.$nextTick(() =>{

          
          console.log(row)
          var that = this;
          this.showpersonrow = row;

          if (this.Filter.timerange) {
            this.personpager.StartDate = this.Filter.timerange[0];
            this.personpager.EndDate = this.Filter.timerange[1];
          } else {
            this.personpager.StartDate = null;
            this.personpager.EndDate = null;
          };
          

          const params = {
            ...this.personpager,
            ...this.Filter,
            //dynamicFilter: this.dynamicFilter
          };
          if(params.ProductID){
          }else{
            params.ProductID = row.productID;
          }
           getRateDetailList(params).then((res) => {
            that.persondata = res.data.list;

            var allsuccess = 0;
            var allfail = 0;
            that.persondata.forEach(function (item, index) {
              allsuccess += item.rensuccess;
              allfail += item.renfail;
              item.heji=item.rensuccess + item.jiqisuccess + item.renfail + item.jiqifail;
              item.success=item.rensuccess + item.jiqisuccess;
              item.notsuccess=item.renfail + item.jiqifail;
            });

            var allrate = allsuccess / (allfail + allsuccess);

            that.persondata.forEach(function (item, index) {
              var success = item.rensuccess + item.jiqisuccess;
              var fail = item.renfail + item.jiqifail;

              item.rate = success / (fail + success);
              if (item.rate < allrate - 0.03) {
                item.isred = true;
              }
            });
            that.summaridata={
              seller: "人工转化率合计",
              jiqisuccess: 0,
              success: allsuccess,
              notsuccess: allfail,
              heji:allsuccess+allfail,
              jiqifail: 0,
              rate: allrate,
            };
            console.log(res.data);
            that.everyPersonVisible = true;
          });
          })
      },
        getRowClass({ row, rowIndex }) {
            return "row-expand-cover";
            // if (row.ruleDetails.length == 0) {
            // return 'row-expand-cover';
            // } else {
            // return '';
            //    }
        },
        // 选择
        onSelsChange(sels) {
            this.sels = sels;
        },
        getSummaries(param) {
          if (param.columns.length > 0 && param.data.length > 0) 
          {
          const { columns, data } = param;
          const sums = [];
          columns.forEach((column, index) => {
            if (index === 1) {
              sums[index] = '合计';
              return;
            }
            if (index === 0) {
              sums[index] = '';
              return;
            }
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr);
                if (!isNaN(value)) {
                  return prev + curr;
                } else {
                  return prev;
                }
              }, 0);
              sums[index] += '';
            } else {
              sums[index] = 'N/A';
            }
          });


          console.log(this.summaridata)
          sums[6]=parseInt((this.summaridata.rate) * 1000) / 10+"%";
          sums[7]=parseInt((this.summaridata.rate-this.showpersonrow.highrate) * 1000) / 10+"%";

          return sums;
          }
          else{
            return;
          }
      },
    },
};
</script>

<style lang="scss" scoped>

</style>