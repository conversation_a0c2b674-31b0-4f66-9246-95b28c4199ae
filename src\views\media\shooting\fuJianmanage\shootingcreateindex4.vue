<template>
    <div class="body">
        <div v-if="btnshow">
            <el-button type="primary" size="medium" @click="addlist">新增板块</el-button>
        </div>
        <div class="fourbox"  v-show="watname == 'indexvideo'">
            <span class="fontwei">视频链接或上传视频:</span>
            <el-row style="margin-top: 20px;">
                <el-col :span="21"><div class="grid-content bg-purple"><el-input clearable @change="inputvideo" :disabled="disabled" v-model="input" style="width:100%" placeholder="请输入内容"></el-input></div></el-col>
                <el-col :span="3" ><div class="grid-content bg-purple-light">
                    <el-upload
                    action=""
                    :limit="1"
                    :on-change="onchangevideo">
                    <el-button size="mini" type="primary">上传视频</el-button>
                    </el-upload>
                </div></el-col>
                <!-- <el-col :span="3"><div class="grid-content bg-purple-light"><el-button type="danger" style="width: 80px;"  @click="addlist" v-show="watname != 'weivideo'">移除</el-button></div></el-col> -->
            </el-row>
            <div class="bottombox">
                <div class="bottombox-left">
                    <div style="margin: 10px;" class="flexcenter">
                        <video width="100%" height="100%"  :src="upvideo" controls loop muted v-if="upvideo"></video>
                        <span style="width:400px; height:400px; display: flex; justify-content: center; align-items: center;" v-else>请先上传视频</span>
                    </div>     
                </div>
                <!-- <div class="bottombox-left flexcenter" v-else>
                    
                </div> -->
                <div class="bottombox-right">
                    <div class="right-top">
                        <div class="top-left flexcolumn">
                            <span>视频尺寸:</span>
                            <div style="margin-top: 10px;"><el-input @change="inputvideo" v-model="sizeinput" placeholder="750*1000" :disabled="disabled"></el-input></div>
                        </div>
                        <div class="top-right flexcolumn">
                            <span>拍摄方向:</span>
                            <div style="margin-top: 10px;">
                                <el-button-group>
                                <el-button :type="num==1?'primary':''" @click="whactclick(1)">横拍</el-button>
                                <el-button :type="num==2?'primary':''" @click="whactclick(2)">竖拍</el-button>
                                </el-button-group>
                            </div>
                        </div>
                    </div>
                    <div class="right-bot">
                        <div class="flexrow">
                            <span>备注:</span>
                            <el-button type="primary" style="margin-left: auto;">添加备注</el-button>
                        </div>
                        <div>
                            <el-row style="margin-top: 20px;">  
                                <el-col :span="20"><div class="grid-content bg-purple"><el-input v-model="beiinput" placeholder="请输入内容" :disabled="disabled"></el-input></div></el-col>
                                <el-col :span="4"><div class="grid-content bg-purple-light"><el-button type="danger" style="width: 80px;"  @click="addlist">移除</el-button></div></el-col>
                            </el-row>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import drawImg from '@/views/media/shooting/fuJianmanage/drawimgs'
export default {
    name: 'DEMOShootingcreateindex2',
    props:{
        name: {type: String,default: ''},
        btnshow: {type: Boolean,default: true},
        disabled: {type: Boolean,default: false},
    },
    components: {drawImg},
    data() {
        return {
            drawnum: -1,
            imgUrl: '',
            tomsg: [],
            cutImgSrc: '',
            sizeinput2: '',
            draw:false,
            isshow: false,
            startdemo: null,
            contenteditable: true,
            placetext: '请按Ctrl+v粘贴图片...',
            list:[1],
            canvasimg: [],
            img: 'https://gw.alicdn.com/bao/uploaded/i3/430490406/O1CN01d8S7Kg1ErzbK1VpW9_!!430490406.jpg_Q75.jpg_.webp',
            videotest: 'http://*************:8004/media/video/20221202/1598502726654291968.mp4',
            upvideo:'',
            watname: '',
            input: '',
            sizeinput: '',
            num: -1,
            beiinput: '',
            demohtml: [],
            videomsg: null,
            tableData: [{
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄'
            }, {
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄'
            }, {
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄'
            }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄'
            }]
        };
    },

    mounted() {
        if(this.name){
            this.watname = this.name;
        }
    },

    methods: {
        addlist(){
            if(this.watname == 'indeximg'||this.watname == 'msgimg'){
                this.list.push(this.list.length+1);
            }else if(this.watname == 'skuimg'){
                this.tableData.push(this.tableData[0])
            }
        },
        inputvideo(value){
            let _this = this;
            let isvideo = value.indexOf(".mp4")
            if(isvideo!=-1){
                console.log("是视频",value)
                _this.upvideo = value;
            }else{
                console.log("不是视频",value)
                this.$message("请粘贴或者上传正确的视频文件")
            }
        },
        onchangevideo(e){
            // console.log("打印上传样式",e)
            let _this = this;
            var file = e.raw;

            // var url = URL.createObjectURL(files);
            // _this.upvideo = url;
            // console.log("本地视频地址",url)
            _this.uploadToServer(file, (res) => {
                _this.videomsg = res.data;
                _this.input = res.data.url;
                _this.upvideo = res.data.url;
            });

        },
        uploadToServer(file, callback) {
            var xhr = new XMLHttpRequest()
            var formData = new FormData()
            formData.append('file', file)
            xhr.open('post', '/api/uploadnew/file/UploadCommonFileAsync')
            xhr.withCredentials = true
            xhr.responseType = 'json'
            xhr.send(formData)
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // debugger;
                    callback(xhr.response)
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.body{
    padding: 10px;
    position: relative;
}
.box{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: row;
}
.twobox{
    padding: 10px 0;
    margin: 20px 0;
    min-height: 500px;
    display: flex;
    flex-direction: row;
}
.fourbox{
    padding: 20px;
    margin: 20px 0;
    min-height: 500px;
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    .bottombox{
        width: 100%;
        height: 100%;
        // border: 1px solid #aaa;
        padding: 20px 0 20px 0;
        display: flex;
        .bottombox-left{
            flex: 4;
            display: flex;
            align-items: center;
            background: #eee;
        }
        .bottombox-right{
            flex: 6;
            display: flex;
            flex-direction: column;
            .right-bot{
                flex: 8;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
            }
            .right-top{
                display: flex;
                flex-direction: row;
                flex: 2;
                padding: 20px;
                margin: 0;
                // border: 1px solid #eee;
                .top-right{
                    width: auto;
                    margin-left: 20px;
                }

                .top-left{
                    width: 200px;
                }

            }
        }
    }
}
.imgbox{
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 6;
    position: relative;

}
.flexcenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
.msgbox{
    flex: 4;
    // padding: 50px 0;
}
.fontwei{
    font-weight: 600;
}
.flexcolumn{
    display: flex;
    flex-direction: column;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: 100%;
}
.pastimg{
    width: 400px;
    height: 400px;
    background-color: #eee;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
// div{
//     caret-color: transparent;
// }
.point{
    cursor: crosshair;
}
.module{
    // background-color: #eee;
    margin-top: 30px;
    width: 100%;
    min-height: 400px;
}
</style>