<template>
    <my-container v-loading="pageLoading" style="height: 100%">
      <el-tabs v-model="activeName" style="height: 94%">
        <el-tab-pane label="苏宁日报" name="first1" style="height: 100%">
          <productReportSuNing ref="productReportSuNing" style="height: 100%"></productReportSuNing>
        </el-tab-pane>
         <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingOrderDayReport')">
          <SuNingOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="SuNingOrderDayReport" style="height: 100%"></SuNingOrderDayReport>
        </el-tab-pane>
        <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingGoodCodeDayReport')">
          <SuNingGoodCodeDayReport  @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="SuNingGoodCodeDayReport" style="height: 100%"></SuNingGoodCodeDayReport>
        </el-tab-pane>
        <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingIdDayReport')">
          <SuNingIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="SuNingIdDayReport" style="height: 100%"></SuNingIdDayReport>
        </el-tab-pane>
        <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingShopDayReport')">
          <SuNingShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="SuNingShopDayReport" style="height: 100%"></SuNingShopDayReport>
        </el-tab-pane>
        <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingCommodityDayReport')">
          <SuNingCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="SuNingCommodityDayReport" style="height: 100%"></SuNingCommodityDayReport>
        </el-tab-pane>
        <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingDetailDayReport')">
          <SuNingDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="SuNingDetailDayReport" style="height: 100%"></SuNingDetailDayReport>
        </el-tab-pane>
        <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingOutgoingprofitIDorderdetail')">
          <SuNingOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="SuNingOutgoingprofitIDorderdetail" style="height: 100%"></SuNingOutgoingprofitIDorderdetail>
        </el-tab-pane>
         <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%" v-if="checkPermission('SuNingOrderDayReport')">
          <SuNingSkusDayReport @ChangeActiveName2="ChangeActiveName2" ref="SuNingSkusDayReport" style="height: 100%"></SuNingSkusDayReport>
        </el-tab-pane>
      </el-tabs>
    </my-container>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import productReportSuNing from "./productReportSuNing.vue";
  import SuNingOrderDayReport from "./SuNingOrderDayReport.vue";
  import SuNingSkusDayReport from "./SuNingSkusDayReport.vue";
  import SuNingGoodCodeDayReport from "./SuNingGoodCodeDayReport.vue";
  import SuNingIdDayReport from "./SuNingIdDayReport.vue";
  import SuNingCommodityDayReport from "./SuNingCommodityDayReport.vue";
  import SuNingOutgoingprofitIDorderdetail from "./SuNingOutgoingprofitIDorderdetail.vue";
  import SuNingShopDayReport from "./SuNingShopDayReport.vue";
  import SuNingDetailDayReport from "./SuNingDetailDayReport.vue";
  import middlevue from "@/store/middle.js"  
  export default {
    name: "productReportGCIndex",
    components: {
      MyContainer, productReportSuNing,SuNingOrderDayReport,SuNingSkusDayReport,SuNingIdDayReport,SuNingShopDayReport,SuNingGoodCodeDayReport,SuNingDetailDayReport,SuNingCommodityDayReport,SuNingOutgoingprofitIDorderdetail
    },
    data() {
      return {
        that: this,
        pageLoading: false,
        activeName: "first1",
      };
    },
    async mounted() {
      middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'sn') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
    },
    methods: {
      ChangeActiveName(activeName){
        this.activeName = 'first2';
        this.$refs.SuNingOrderDayReport.SuNingGoodCodeDayReportArgument(activeName)
      },
      ChangeActiveName2(activeName,No,Time){
        this.activeName = 'first6';
        this.$refs.SuNingDetailDayReport.SuNingDetailDayReportArgument(activeName,No,Time)
      }
    },
  };
  </script>

  <style lang="scss" scoped></style>
