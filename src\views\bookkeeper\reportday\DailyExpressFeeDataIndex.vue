<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
     
  <el-tab-pane   label="快递费均价" name="tab1" :lazy="true" style="height: 100%;">
    <ExpressFeeAveragePrice :filter="Filter" ref="ExpressFeeAveragePrice" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="特别区域快递费" name="tab2" :lazy="true" style="height: 100%;">
    <particularlyExpressFee ref="particularlyExpressFee" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="特别ID快递费" name="tab3" :lazy="true" style="height: 100%;">
    <specProCodeExpressFee ref="specProCodeExpressFee" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="店铺不计算快递费" name="tab4" :lazy="true" style="height: 100%;">
    <shopNotCalcExpressFee ref="shopNotCalcExpressFee" style="height: 100%;"/>
  </el-tab-pane>
  <el-tab-pane label="重量快递费" name="tab5" :lazy="true" style="height: 100%;">
    <specWeightExpressFee ref="specWeightExpressFee" style="height: 100%;"/>
  </el-tab-pane>
    </el-tabs>
    </my-container >

   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import ExpressFeeAveragePrice from './ExpressFeeAveragePrice'
  import shopNotCalcExpressFee from './shopNotCalcExpressFee'
  import particularlyExpressFee from './particularlyExpressFee'
  import specProCodeExpressFee from './specProCodeExpressFee'
  import specWeightExpressFee from './specWeightExpressFee'

  export default {
    name: "Users",
    components: { MyContainer,ExpressFeeAveragePrice,shopNotCalcExpressFee,particularlyExpressFee,specProCodeExpressFee,specWeightExpressFee},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {






    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1')
    this.$refs.ProcessingCost.onSearch();
  }


    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
