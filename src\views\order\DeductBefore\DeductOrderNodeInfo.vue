<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-select v-model="filter.selectDateType" placeholder="" style="width: 100px" filterable>
                <el-option label="付款时间" value="PayTime" />
                <el-option label="提交发货" value="SubmitTime" />
                <el-option label="绑定批次" value="BindWaveTime" />
                <el-option label="打印面单" value="PrintMdTime" />
                <el-option label="出库完成" value="PhEndTime" />
                <el-option label="打包完成" value="PackageTime" />
                <el-option label="发货时间" value="FaHuoTime" />
                <el-option label="称重时间" value="WeightTime" />
                <el-option label="揽收时间" value="LanShouTime" />
                <el-option label="首次中转" value="FirstZhongZhuanTime" />
                <el-option label="派送时间" value="PaiSongTime" />
                <el-option label="签收时间" value="QianShouTime" />
            </el-select>

            <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <el-select v-model="filter.selectDateType2" placeholder="时间" style="width: 100px" filterable clearable>
                <el-option label="付款时间" value="PayTime" />
                <el-option label="提交发货" value="SubmitTime" />
                <el-option label="绑定批次" value="BindWaveTime" />
                <el-option label="打印面单" value="PrintMdTime" />
                <el-option label="出库完成" value="PhEndTime" />
                <el-option label="打包完成" value="PackageTime" />
                <el-option label="发货时间" value="FaHuoTime" />
                <el-option label="称重时间" value="WeightTime" />
                <el-option label="揽收时间" value="LanShouTime" />
                <el-option label="首次中转" value="FirstZhongZhuanTime" />
                <el-option label="派送时间" value="PaiSongTime" />
                <el-option label="签收时间" value="QianShouTime" />
            </el-select>

            <el-date-picker style="width: 240px" v-model="filter.timerange2" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'" :end-placeholder="'结束时间'"
                :picker-options="pickerOptions">
            </el-date-picker>

            <!-- <el-select v-model="filter.noPassType" style="width:90px;" placeholder="违规类型" :clearable="true">
                <el-option label="==所有==" :value="0"></el-option>
                <el-option label="绑批次超时" :value="1"></el-option>
                <el-option label="杂配货超时" :value="2"></el-option>
                <el-option label="团打单超时" :value="3"></el-option>
                <el-option label="团配货超时" :value="4"></el-option>
                <el-option label="打包超时" :value="5"></el-option>
                <el-option label="称重超时" :value="6"></el-option>
            </el-select> -->


            <el-input v-model.trim="filter.orderNoInner" placeholder="内部单号" style="width:120px;" clearable
                oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}" />

            <el-select v-model="filter.platform" clearable placeholder="平台" style="width: 100px" filterable>
                <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <!-- <el-input v-model.trim="filter.orderNo" placeholder="线上单号" style="width:160px;" clearable /> -->

            <el-select v-model="filter.lanShou_FirstZhongZhuan_ZhenJia" style="width:90px;" placeholder="揽收状态"
                :clearable="true">
                <el-option label="真揽" value="真揽"></el-option>
                <el-option label="假揽" value="假揽"></el-option>
            </el-select>

            <el-input v-model.trim="filter.bindWaveName" clearable placeholder="绑定批次人" style="width:100px;"
                :maxlength="20" />
            <el-input v-model.trim="filter.printMdName" clearable placeholder="打印面单人" style="width:100px;"
                :maxlength="20" />
            <el-input v-model.trim="filter.phEndName" clearable placeholder="出库人" style="width:100px;"
                :maxlength="20" />
            <el-input v-model.trim="filter.packageName" clearable placeholder="打包人" style="width:100px;"
                :maxlength="20" />
            <el-input v-model.trim="filter.faHuoName" clearable placeholder="发货人" style="width:100px;"
                :maxlength="20" />
            <el-select v-model="filter.isFaHuo" style="width:90px;" placeholder="发货" :clearable="true">
                <el-option label="有" value=1></el-option>
                <el-option label="没有" value=2></el-option>
            </el-select>
            <el-input v-model.trim="filter.weightName" clearable placeholder="称重人" style="width:100px;"
                :maxlength="20" />

            <el-input v-model.trim="filter.keywords" clearable placeholder="关键字查询" style="width:180px;"
                :maxlength="100">
                <el-tooltip slot="suffix" class="item" effect="dark" content="支持搜索的内容：线上单号精准匹配、发货仓库模糊匹配、快递公司模糊匹配"
                    placement="bottom">
                    <i class="el-input__icon el-icon-question"></i>
                </el-tooltip>
            </el-input>

            <el-input v-model.trim="filter.batchSetName" clearable placeholder="批次类型" style="width:100px;"
                :maxlength="20" />

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onImport3">导入已揽收</el-button>
            <el-button type="primary" @click="onImport">导入已签收</el-button>

            <el-button type="primary" @click="onSetShow">违规设置</el-button>
            <el-button type="primary" @click="onBatchSetShow">批次设置</el-button>
            <!-- <el-button type="primary" @click="onExport" :loading="exportloading">导出</el-button> -->

            <el-button type="primary" @click="setExportCols" :loading="exportloading">导出</el-button>

            <el-button type="primary" @click="onAgainShow">重算</el-button>
        </template>

        <template style="margin-top: 10px;">
            <vxetablebase :id="'DeductOrderNodeInfo202408041745'" ref="table" :that='that' :cstmExportFunc="onExport"
                :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
                @select="selectchange" :tableData='list' :tableCols='tableCols' :isSelection="true"
                :loading="listLoading">
            </vxetablebase>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="selids.length" @get-page="getlist" />
        </template>

        <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
            v-dialogDrag>
            <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNoInner="orderNoInner"
                style="z-index:10000;height:600px" />
        </el-dialog>

        <el-dialog title="导入快递已签收" :visible.sync="dialogImportData.visible" width="30%" :close-on-click-modal="false"
            v-dialogDrag v-loading="dialogImportData.loading">
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                        action accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2"
                        :on-change="onUploadChange2" :on-remove="onUploadRemove2">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                            @click="onSubmitupload2">上传</my-confirm-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogImportData.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="导入快递已揽收" :visible.sync="dialogImportData3.visible" width="30%" :close-on-click-modal="false"
            v-dialogDrag v-loading="dialogImportData3.loading">
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                    <el-upload ref="upload3" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                        action accept=".xlsx" :http-request="uploadFile3" :on-success="uploadSuccess3"
                        :on-change="onUploadChange3" :on-remove="onUploadRemove3">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                            @click="onSubmitupload3">上传</my-confirm-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogImportData3.visible = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="违规设置" :visible.sync="dialogSetData.visible" width="30%" :close-on-click-modal="false"
            v-loading="dialogSetData.loading" v-dialogDrag>

            <el-select v-show="dialogSetMode == false" v-model="dialogSetDataWmsSel" style="width:300px;"
                placeholder="仓库名称" :clearable="true" @change="onDialogSetDataWmsSelChange">
                <el-option v-for="item in dialogSetDataWmsList" :key="item" :label="item" :value="item" />
            </el-select>
            <el-button v-show="dialogSetMode == false" type="primary" style="margin-left: 10px"
                @click="onDialogSetMode(true)">切换为编辑</el-button>

            <el-select v-show="dialogSetMode == true" v-model="dialogSetDataWmsSelList" style="width:300px;"
                placeholder="仓库名称" multiple collapse-tags :clearable="true">
                <el-option v-for="item in dialogSetDataWmsList" :key="item" :label="item" :value="item" />
            </el-select>
            <el-button v-show="dialogSetMode == true" type="primary" style="margin-left: 10px"
                @click="onDialogSetMode(false)">切换为查看</el-button>

            <el-table :data="dialogSetData.setList" height="540">
                <el-table-column label="序号" width="80">
                    <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <!-- <el-table-column prop="indexNo" label="序号" width="80" /> -->
                <el-table-column prop="setName" label="类型" width="200" />
                <el-table-column prop="setDuration" label="时长(h)" width="200">
                    <template slot-scope="scope">
                        <el-input-number v-model="scope.row.setDuration" :min="0" :max="999" placeholder="时长(h)"
                            :precision="2">
                        </el-input-number>
                    </template>
                </el-table-column>
            </el-table>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogSetData.visible = false">取消</el-button>
                <el-button v-show="dialogSetMode == true" type="primary" @click="onSaveSet">保存</el-button>
            </span>
        </el-dialog>


        <el-dialog title="重算" :visible.sync="dialogAgainData.visible" width="30%" :close-on-click-modal="false"
            v-loading="dialogAgainData.loading" v-dialogDrag>

            <el-date-picker style="width: 240px" v-model="dialogAgainData.timerange" type="daterange"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" :start-placeholder="'开始时间'"
                :end-placeholder="'结束时间'" :picker-options="pickerOptions">
            </el-date-picker>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogAgainData.visible = false">取消</el-button>
                <el-button type="primary" @click="onAgainSave">重算</el-button>
            </span>
        </el-dialog>



        <el-dialog title="批次设置" :visible.sync="dialogBatchData.visible" width="55%" :close-on-click-modal="false"
            v-loading="dialogBatchData.loading" v-dialogDrag>

            <el-button type="primary" @click="onBatchSetAddRow">新增一行</el-button>

            <el-table :data="dialogBatchData.batchSetList" height="540">
                <el-table-column label="序号" width="50">
                    <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column prop="batchSetName" label="批次名称" width="200">
                    <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.batchSetName" placeholder="批次名称" maxlength="40" />
                    </template>
                </el-table-column>
                <el-table-column prop="batchContain" label="包含(多个使用英文逗号隔开)" width="200">
                    <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.batchContain" placeholder="包含" maxlength="100" />
                    </template>
                </el-table-column>
                <el-table-column prop="batchNoContain" label="不包含(多个使用英文逗号隔开)" width="200">
                    <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.batchNoContain" placeholder="不包含" maxlength="100" />
                    </template>
                </el-table-column>
                <el-table-column prop="createdUserName" label="添加人" width="80" />
                <el-table-column prop="createdTime" label="添加时间" width="150" />
                <el-table-column lable="操作" width="80">
                    <template slot-scope="scope">
                        <el-button type="danger" @click="onDelBatchSetRow(scope.$index, scope.row)">删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogBatchData.visible = false">取消</el-button>
                <el-button type="primary" @click="onSaveBatchSet">保存</el-button>
            </span>
        </el-dialog>


    </my-container>
</template>

<script>
import MyConfirmButton from '@/components/my-confirm-button'
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { pickerOptions, platformlist } from '@/utils/tools'
import {
    PageDeductOrderNodeInfoList,
    ImportExpressSignOrder, ImportExpressStatusOrder,
    GetDeductOrderNodeSetList,
    SaveDeductOrderNodeSetList,
    ExportDeductOrderNodeInfoList2,
    GetDeductOrderNodeAllList,
    GetDeductOrderNodeInfo_Again,
    GetDeductOrderNodeBatchSetList,
    DelDeductOrderNodeBatchSet,
    SaveDeductOrderNodeBatchSet,
} from "@/api/order/deductbefore"
import MyContainer from "@/components/my-container";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

const tableCols = [
    { istrue: true, prop: 'orderNoInner', fixed: 'left', label: '内部单号', width: '100', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
    { istrue: true, prop: 'platform', fixed: 'left', label: '平台', width: '80', sortable: 'custom', formatter: (row) => row.platformName },
    { istrue: true, prop: 'orderNo', fixed: 'left', label: '线上单号', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'payTime', label: '平台付款时间', width: '100', sortable: 'custom', formatter: (row) => row.payTime == null ? '' : formatTime(row.payTime, "YYYY") == '2050' ? '' : formatTime(row.payTime, "MM-DD HH:mm") },

    // { istrue: true, prop: 'node1NoPass', label: '绑批次超时', width: '100', sortable: 'custom', formatter: (row) => row.node1NoPass == 1 && !row.node1ZrMemberName ? 'x' : row.node1ZrMemberName },
    // { istrue: true, prop: 'node2NoPass', label: '杂配货超时', width: '100', sortable: 'custom', formatter: (row) => row.node2NoPass == 1 && !row.node2ZrMemberName ? 'x' : row.node2ZrMemberName },
    // { istrue: true, prop: 'node3NoPass', label: '团打单超时', width: '100', sortable: 'custom', formatter: (row) => row.node3NoPass == 1 && !row.node3ZrMemberName ? 'x' : row.node3ZrMemberName },
    // { istrue: true, prop: 'node4NoPass', label: '团配货超时', width: '100', sortable: 'custom', formatter: (row) => row.node4NoPass == 1 && !row.node4ZrMemberName ? 'x' : row.node4ZrMemberName },
    // { istrue: true, prop: 'node5NoPass', label: '打包超时', width: '80', sortable: 'custom', formatter: (row) => row.node5NoPass == 1 && !row.node5ZrMemberName ? 'x' : row.node5ZrMemberName },
    // { istrue: true, prop: 'node6NoPass', label: '称重超时', width: '80', sortable: 'custom', formatter: (row) => row.node6NoPass == 1 && !row.node6ZrMemberName ? 'x' : row.node6ZrMemberName },

    { istrue: true, prop: 'noPassSum', label: '违规合计', width: '80', sortable: 'custom' },

    { istrue: true, prop: 'submitTime', label: '提交发货', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.submitTime, "YYYY") == '2050' ? '' : formatTime(row.submitTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'submit_BindWave', label: '发货-批次耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.submit_BindWave_Html },

    { istrue: true, prop: 'bindWaveTime', label: '绑定批次', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.bindWaveTime, "YYYY") == '2050' ? '' : formatTime(row.bindWaveTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'bindWaveName', label: '绑定批次人', width: '80' },
    { istrue: true, prop: 'bindWave_PrintMd', label: '批次-面单耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.bindWave_PrintMd_Html },

    { istrue: true, prop: 'printMdTime', label: '打印面单', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.printMdTime, "YYYY") == '2050' ? '' : formatTime(row.printMdTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'printMdName', label: '打印面单人', width: '80' },
    { istrue: true, prop: 'printMd_PhEnd', label: '面单-出库耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.printMd_PhEnd_Html },

    { istrue: true, prop: 'phEndTime', label: '出库完成', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.phEndTime, "YYYY") == '2050' ? '' : formatTime(row.phEndTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'phEndName', label: '出库人', width: '80' },
    { istrue: true, prop: 'phEnd_Package', label: '出库-打包耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.phEnd_Package_Html },

    { istrue: true, prop: 'pay_Package', label: '付款-打包耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.pay_Package_Html },
    { istrue: true, prop: 'submit_Package', label: '提交发货-打包耗时(h)', width: '160', sortable: 'custom', type: 'html', formatter: (row) => row.submit_Package_Html },

    { istrue: true, prop: 'packageTime', label: '打包完成', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.packageTime, "YYYY") == '2050' ? '' : formatTime(row.packageTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'packageName', label: '打包人', width: '80' },
    //{ istrue: true, prop: 'package_Weight', label: '打包-称重耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.package_Weight_Html },
    { istrue: true, prop: 'package_FaHuo', label: '打包-发货耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.package_FaHuo_Html },

    { istrue: true, prop: 'faHuoTime', label: '发货时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.faHuoTime, "YYYY") == '2050' ? '' : formatTime(row.faHuoTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'faHuoName', label: '发货人', width: '80' },
    { istrue: true, prop: 'faHuo_Weight', label: '发货-称重耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.faHuo_Weight_Html },

    { istrue: true, prop: 'weightTime', label: '称重时间', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.weightTime, "YYYY") == '2050' ? '' : formatTime(row.weightTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'weightName', label: '称重人', width: '80' },
    { istrue: true, prop: 'weight_LanShou', label: '称重-揽收耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.weight_LanShou_Html },

    { istrue: true, prop: 'faHuo_LanShou', label: '发货-揽收耗时(h)', width: '160', sortable: 'custom', type: 'html', formatter: (row) => row.faHuo_LanShou_Html },

    { istrue: true, prop: 'lanShouTime', label: '揽收时间', width: '90', sortable: 'custom', formatter: (row) => row.lanShouTime == null ? '' : formatTime(row.lanShouTime, "YYYY") == '2050' ? '' : formatTime(row.lanShouTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'lanShou_FirstZhongZhuan', label: '揽收-中转耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.lanShou_FirstZhongZhuan_Html },
    { istrue: true, prop: 'lanShou_FirstZhongZhuan_ZhenJia', label: '揽收状态', width: '80' },

    { istrue: true, prop: 'firstZhongZhuanTime', label: '首次中转时间', width: '100', sortable: 'custom', formatter: (row) => row.firstZhongZhuanTime == null ? '' : formatTime(row.firstZhongZhuanTime, "YYYY") == '2050' ? '' : formatTime(row.firstZhongZhuanTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'firstZhongZhuan_PaiSong', label: '中转-派送耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.firstZhongZhuan_PaiSong_Html },

    { istrue: true, prop: 'paiSongTime', label: '派送时间', width: '80', sortable: 'custom', formatter: (row) => row.paiSongTime == null ? '' : formatTime(row.paiSongTime, "YYYY") == '2050' ? '' : formatTime(row.paiSongTime, "MM-DD HH:mm") },
    { istrue: true, prop: 'paiSong_QianShou', label: '派送-签收耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.paiSong_QianShou_Html },

    { istrue: true, prop: 'qianShouTime', label: '签收时间', width: '80', sortable: 'custom', formatter: (row) => row.qianShouTime == null ? '' : formatTime(row.qianShouTime, "YYYY") == '2050' ? '' : formatTime(row.qianShouTime, "MM-DD HH:mm") },

    { istrue: true, prop: 'submit_LanShou', label: '提交发货-揽收耗时(h)', width: '160', sortable: 'custom', type: 'html', formatter: (row) => row.submit_LanShou_Html },
    { istrue: true, prop: 'pay_LanShou', label: '付款-揽收耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.pay_LanShou_Html },
    { istrue: true, prop: 'pay_QianShou', label: '付款-签收耗时(h)', width: '130', sortable: 'custom', type: 'html', formatter: (row) => row.pay_QianShou_Html },

    { istrue: true, prop: 'maxWmsName', label: '发货仓库', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'maxExpressCompanyName', label: '快递公司', width: '140', sortable: 'custom' },
    { istrue: true, prop: 'maxWaveId', label: '批次号', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'waveOperator', label: '批次操作', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'waveRemark', label: '批次备注', width: '300', sortable: 'custom' },
    { istrue: true, prop: 'batchSetName', label: '批次类型', width: '200', sortable: 'custom' },
]

export default {
    name: 'OrderNodeInfo',
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos, vxetablebase },
    props: {
        outFilter: {
            type: Object,
            default: () => { return {}; }
        },
    },
    data() {
        return {
            that: this,
            filter: {
                submitDateStart: null,
                submitDateEnd: null,
                noPassType: 0,
                orderNoInner: null,
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD")
                ],
                timerange2: [],
                keywords: null,
                selectDateType: "PayTime",
            },
            pickerOptions: pickerOptions,
            platformlist: platformlist,
            dialogHisVisible: false,
            orderNo: '',
            orderNoInner: 0,
            list: [],
            summaryarry: {},
            pager: { OrderBy: "submitTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            exportloading: false,
            dialogVisible: false,

            fileList: [],
            dialogImportData: {
                visible: false,
                loading: false,
            },
            fileList3: [],
            dialogImportData3: {
                visible: false,
                loading: false,
            },
            dialogSetDataWmsList: [],
            dialogSetDataWmsSel: null,
            dialogSetDataWmsSelList: [],
            dialogSetMode: false,
            dialogSetData: {
                visible: false,
                loading: false,

                setList: [],
            },
            dialogAgainData: {
                visible: false,
                loading: false,
                timerange: [],
            },
            dialogBatchData: {
                visible: false,
                loading: false,

                batchSetList: [],
            },
        };
    },
    async mounted() {
        await this.getDialogSetDataWmsList()
        await this.onSearch()
    },
    methods: {
        async getDialogSetDataWmsList() {
            this.dialogSetDataWmsList = [];
            const res = await GetDeductOrderNodeAllList();
            if (res?.success) {
                res.data.forEach(f => {
                    this.dialogSetDataWmsList.push(f.wmsName);
                });
            }
        },
        showLogDetail(row) {
            this.orderNoInner = row.orderNoInner;
            this.dialogHisVisible = true;
        },
        onShowLogistics(row) {
            let self = this;
            this.$showDialogform({
                path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                title: '物流明细',
                args: { expressNos: row.expressNo },
                height: 300,
            });
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.submitDateStart = this.filter.timerange[0];
                this.filter.submitDateEnd = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }

            if (this.filter.timerange2 && this.filter.timerange2.length > 1) {
                this.filter.otherDateStart = this.filter.timerange2[0];
                this.filter.otherDateEnd = this.filter.timerange2[1];
            } else {
                this.filter.otherDateStart = null;
                this.filter.otherDateEnd = null;
            }

            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            console.log(this.outFilter);
            const params = {
                ...pager,
                ...page,
                ...this.outFilter,
                ... this.filter
            }
            console.log(params);
            return params;
        },
        //分页查询
        async getlist() {
            let params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await PageDeductOrderNodeInfoList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.selids = [];
            this.sels = [];

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data;
            this.summaryarry = res.data.summary;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.sels = [];
            //console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
                this.sels.push(f);
            })
        },
        async setExportCols() {
            await this.$refs.table.setExportCols();
        },
        async onExport(opt) {
            let pars = this.getCondition();
            if (pars === false) {
                return;
            }
            const params = { ...pars, ...opt };
            let res = await ExportDeductOrderNodeInfoList2(params);
            if (!res?.data) {
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '违规前置分析_订单节点明细_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async onImport() {
            this.dialogImportData.visible = true;
        },
        async onUploadChange2(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove2(file, fileList) {
            this.fileList = [];
        },
        async uploadFile2(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            this.dialogImportData.loading = true;
            const res = await ImportExpressSignOrder(form);
            this.dialogImportData.loading = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogImportData.visible = false;
            }
        },
        async uploadSuccess2(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload2() {
            this.$refs.upload2.submit();
        },


        async onImport3() {
            this.dialogImportData3.visible = true;
        },
        async onUploadChange3(file, fileList) {
            this.fileList3 = fileList;
        },
        async onUploadRemove3(file, fileList) {
            this.fileList3 = [];
        },
        async uploadFile3(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            this.dialogImportData3.loading = true;
            const res = await ImportExpressStatusOrder(form);
            this.dialogImportData3.loading = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogImportData3.visible = false;
            }
        },
        async uploadSuccess3(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload3() {
            this.$refs.upload3.submit();
        },

        async onDialogSetMode(mode) {
            this.dialogSetMode = mode;
            this.$forceUpdate();
        },
        async onSetShow() {
            this.dialogSetDataWmsSel = null;
            this.dialogSetDataWmsSelList = [];
            this.dialogSetMode = false;
            this.dialogSetData.setList = [];
            this.dialogSetData.loading = false;
            this.dialogSetData.visible = true;

            await this.getSetList();
        },
        async getSetList() {
            this.dialogSetData.loading = true;
            const res = await GetDeductOrderNodeSetList({ wmsName: this.dialogSetDataWmsSel });
            this.dialogSetData.loading = false;
            if (res?.success) {
                this.dialogSetData.setList = res.data;
            }
        },
        async onDialogSetDataWmsSelChange() {
            await this.getSetList();
        },
        async onSaveSet() {
            if (this.dialogSetDataWmsSelList.length <= 0) {
                this.$message({ message: '至少选择一个仓库', type: "error" });
                return;
            }
            let setListList = [];
            this.dialogSetDataWmsSelList.forEach(f => {
                let curSetList = [];
                this.dialogSetData.setList.forEach(t => {
                    curSetList.push({
                        indexNo: t.indexNo,
                        setName: t.setName,
                        setDuration: t.setDuration,
                        wmsName: f
                    })
                })
                setListList.push(curSetList);
            });

            this.dialogSetData.loading = true;
            const res = await SaveDeductOrderNodeSetList(setListList);
            this.dialogSetData.loading = false;
            if (res?.success) {
                this.$message({ message: '保存成功', type: "success" });
                await this.onSetShow();
                //await this.onSearch();
            }
        },
        async onAgainShow() {
            this.dialogAgainData.timerange = [];
            this.dialogAgainData.loading = false;
            this.dialogAgainData.visible = true;
        },
        async onAgainSave() {
            if (!this.dialogAgainData.timerange || this.dialogAgainData.timerange.length < 2) {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            this.$confirm("确定要重算日期范围内的数据吗?", "提示", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }).then(async () => {
                this.dialogSetData.loading = true;
                const res = await GetDeductOrderNodeInfo_Again({
                    yyyy_mm_dd_start: this.dialogAgainData.timerange[0],
                    yyyy_mm_dd_end: this.dialogAgainData.timerange[1],
                });
                this.dialogSetData.loading = false;
                if (res?.success) {
                    this.$message({ message: '操作成功，正在排队重算中...', type: "success" });
                    this.dialogAgainData.visible = false;
                }
            });
        },
        async onBatchSetShow() {
            this.dialogBatchData.visible = true;
            this.dialogBatchData.batchSetList = [];
            this.dialogBatchData.loading = true;
            let res = await GetDeductOrderNodeBatchSetList();
            this.dialogBatchData.loading = false;
            if (res?.success) {
                this.dialogBatchData.batchSetList = res.data;
            }
        },
        async onBatchSetAddRow() {
            console.log(this.dialogBatchData, "this.dialogBatchData");

            this.dialogBatchData.batchSetList.push({
                batchSetName: "",
                batchContain: "",
                batchNoContain: ""
            });
        },
        async onSaveBatchSet() {
            this.dialogBatchData.loading = true;
            let res = await SaveDeductOrderNodeBatchSet(this.dialogBatchData.batchSetList);
            this.dialogBatchData.loading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '保存成功!' });
            }
        },
        async onDelBatchSetRow(index, row) {
            if (row.id) {
                this.$confirm('确认要删除吗?删除后将无法恢复', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    let res = await DelDeductOrderNodeBatchSet({ id: row.id });
                    if (res?.success) {
                        this.dialogBatchData.batchSetList.splice(index, 1);
                        this.$message({ type: 'success', message: '删除成功!' });
                    }
                }).catch(() => {
                });
            }
            else {
                this.dialogBatchData.batchSetList.splice(index, 1);
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
