<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="运营组：">
                    <el-select filterable v-model="filter.groupId" placeholder="运营组" style="width: 110px" clearable>
                        <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value"
                            :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="店铺名称:">
                    <el-select filterable v-model="filter.shopCode" placeholder="请选择店铺" clearable style="width: 120px">
                        <el-option v-for="item in shopList" :key="item.id" :label="item.shopName"
                            :value="item.shopCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="违规编号">
                     <el-input v-model="filter.pddViolationId" placeholder="违规编号" style="width: 120px" maxlength="50" clearable></el-input>
                </el-form-item>
                <el-form-item label="处罚进度">
                    <el-select filterable v-model="filter.punishStatusArray" placeholder="处罚进度" style="width: 180px" multiple clearable collapse-tags  >
                        <el-option label="待申诉" value="待申诉"></el-option>
                        <el-option label="平台处理中" value="平台处理中"></el-option>
                        <el-option label="待完善资料" value="待完善资料"></el-option>
                        <el-option label="申诉成功" value="申诉成功"></el-option>
                        <el-option label="申诉失败" value="申诉失败"></el-option>
                        <el-option label="超时未申诉" value="超时未申诉"></el-option>
                        <el-option label="主动认罚" value="主动认罚"></el-option>
                        <el-option label="处罚撤销" value="处罚撤销"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="重要:">
                        <el-select  v-model="filter.isImportant" placeholder="重要"
                            :collapse-tags="true" clearable >
                            <el-option label="重要" :value="true"></el-option>
                        </el-select>
               </el-form-item>
                <el-form-item label="是否处理:">
                        <el-select v-model="filter.isHandel" placeholder="是否处理"
                            :collapse-tags="true" clearable >
                            <el-option label="未处理" :value="false"></el-option>
                            <el-option label="已处理" :value="true"></el-option>
                        </el-select>
               </el-form-item>
               <el-form-item label="处理人:">
                    <el-select filterable v-model="filter.handeler" placeholder="请选择处理人" clearable style="width: 120px">
                        <el-option v-for="(item,i) in handelerList" :key="i" :label="item.msg"
                            :value="item.haderUserId" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSetImportant">设置重要</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :tableData='list'
            :tableCols='tableCols' :isSelection="false" :loading="listLoading">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="设置重要" :show-close="false" :visible.sync="pddbdemailImportantVisible" width="420px" close-on-click-modal
      element-loading-text="拼命加载中" v-dialogDrag v-loading="addLoading">
        <div style="width:100%;margin:15px auto 70px auto;">
            <span style="margin-left:5px">违规类型:</span>
            <el-select filterable v-model="pddbdemailImportant.OperateManageIds" placeholder="请选择违规类型" multiple clearable style="width: 260px">
                        <el-option v-for="(item,i) in pddEmailTyleList" :key="i" :label="item.label"
                            :value="item.label" />
             </el-select>
          </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pddbdemailImportantVisible = false">取消</el-button>
          <el-button @click="submitSetImportant" v-throttle="3000" type="primary" v-loading="btnloading">保存</el-button>
        </span>
      </template>
    </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { getPingduoduoBGLiveViolationList,exportPingduoduoBGLiveViolationList,setPddBackGroundViolationImportant,
    handlePddBackGroundViolation,getPddEmailDetial,getPddBackGroundShopViolationList,getHandelerList } from '@/api/operatemanage/pddbgmanage/pddbgmanage'
import { getDirectorGroupList,getList as getshopList } from '@/api/operatemanage/base/shop'
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { ConsoleLogger } from '@microsoft/signalr/dist/esm/Utils';

const tableCols = [
    { istrue: true, prop: 'groupId', label: '运营组', tipmesg: '', width: '120', sortable: 'custom', formatter: (row) =>row.groupName},
    { istrue: true, prop: 'shopName', label: '店铺名称', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'pddViolationId', label: '违规编号', tipmesg: '', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'violationType', label: '违规类型', width: '150', tipmesg: '', sortable: 'custom' },
    { istrue: true, prop: 'isImportant', label: '重要', width: '100', sortable: 'custom', formatter: (row) => {return row.isImportant?"重要":"不重要"} },
    { istrue: true, prop: 'violationTzTime', label: '违规通知时间',  tipmesg: '' , formatter: (row) => formatTime(row.violationTzTime, "YYYY-MM-DD HH:mm:ss") },
    { istrue: true, prop: 'complainTime', label: '申诉时间',width: '150',  tipmesg: '', sortable: 'custom', formatter: (row) =>  row.complainTime == null ? "--" :  formatTime(row.complainTime, "YYYY-MM-DD HH:mm:ss")  },
    { istrue: true, prop: 'platfromHandleTime', label: '平台处理时间',width: '150',  tipmesg: '', sortable: 'custom', formatter: (row) =>  row.platfromHandleTime == null ? "--" :  formatTime(row.platfromHandleTime, "YYYY-MM-DD HH:mm:ss")  },
    { istrue: true, prop: 'complainStatus', label: '申诉状态',width: '150',  tipmesg: '', sortable: 'custom' },
    {  istrue: true, type: 'isHandel', label: '是否处理', width: '100', style:(that,row)=>{return row.isHandel==true?"color:gray;":"color: blue;cursor: pointer"}, formatter: (row) =>{ return row.isHandel==true?"已处理":"未处理"},type:'click',handle: (that, row) => that.handelOp(row)},
    { istrue: true, prop: 'handlerUserName', label: '处理人', sortable: 'custom'},
    { istrue: true, prop: 'handlerTime', label: '处理时间', sortable: 'custom'}
]

const startDate = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminPddBGShopViolation',
    components: { container, cesTable, MyConfirmButton },
    data() {
        return {
            that: this,
            filter: {
                groupId: null,
                shopCode: null,
                pddViolationId:null,
                punishStatusArray:null,
                punishStatus:null,
                isImportant:true,
                isHandel:false,
                handeler:null
            },
            list: [],
            shopList: [],
            directorGroupList:[],
            pager: { OrderBy: "violationTzTime", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            uploadLoading: false,
            dialogVisible: false,
            listLoading: false,
            fileList: [],
            //设置重要
           pddEmailTyleList:[],
           pddbdemailImportantVisible:false,
            pddbdemailImportant: {
                OperateManageIds: [],
            },
            addLoading: true,
            btnloading:false,
            handelerList:[]
        };
    },

    async mounted() {
        await this.onSearch()
        await this.onchangeplatform()
    },

    methods: {
        //获取店铺
        async onchangeplatform() {
            this.categorylist = []
            const res1 = await getshopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
            let res3 = await getDirectorGroupList({})
            this.directorGroupList = res3.data

            var resValiationList = await getPddBackGroundShopViolationList({typeId:5});
            this.pddEmailTyleList=resValiationList?.data;

            var resEmailType = await getPddEmailDetial({typeId:5,flag:0});
            var pddTyleList=resEmailType?.data;
            this.pddbdemailImportant.OperateManageIds=[];
            pddTyleList.forEach(item => {
                if (item.isImportant) {
                    this.pddbdemailImportant.OperateManageIds.push(item.label);
                }
            })
            await this.getHandelList();
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="violationTzTime";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.punishStatus=null;
            if(this.filter.punishStatusArray != null && this.filter.punishStatusArray.length > 0){
                this.filter.punishStatus = this.filter.punishStatusArray.join(",")
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await getPingduoduoBGLiveViolationList(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async onExport(){
            if(this.pager.OrderBy==null){
                this.pager.OrderBy="violationTzTime";
                this.pager.IsAsc=false;
            }
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.punishStatus=null;
            if(this.filter.punishStatusArray != null && this.filter.punishStatusArray.length > 0){
                this.filter.punishStatus = this.filter.punishStatusArray.join(",")
            }
            let params = {  ...pager,   ...page,   ...this.filter};
            let res = await exportPingduoduoBGLiveViolationList(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }

            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '拼多多直播违规_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async submitSetImportant () {
         var res = await setPddBackGroundViolationImportant({typeId:5,operateManageIds:this.pddbdemailImportant.OperateManageIds});
         if (!res?.success) {
            this.btnloading = false;
            return
        }
        this.$message({ type: 'success', message: "设置成功" });
        this.btnloading = false;
        this.pddbdemailImportantVisible = false;
        await  this.onSearch();

        },
        onSetImportant () {
          this.pddbdemailImportantVisible = true;
        },
        async handelOp(row)
        {
            if(row.isHandel)
            {
                this.$message({type: 'info',message: '已处理不可重复处理'});
                return;
            }
            this.$confirm('确认处理吗, 是否继续?', '提示', {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'
            }).then(async () => {
                const res = await handlePddBackGroundViolation({bid:row.id,typeId:5});
                if (!res?.success) {return }
                this.$message({type: 'success',message: '操作成功!'});
                await this.getHandelList();
                await this.onSearch()
            }).catch(( x) => {

            this.$message({type: 'info',message: "取消操作"});
            });
        },
        async getHandelList()
        {
            var resHandeler = await getHandelerList({typeId:5});
            this.handelerList = resHandeler?.data;
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
