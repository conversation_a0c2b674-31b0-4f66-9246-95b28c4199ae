<!--卸货入库计件 -->
<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-row>
                <div style="padding-bottom: 5px;font-size:14px; color:#EE7700">
                    数据来源于【入库拍摄】，您也可以手动重新计算。
                </div>
            </el-row>
            <el-row>
                <el-form class="ad-form-query" :inline="true">
                    <el-form-item label="仓库">
                        <el-select v-model="filter.warehouseCode" style="width: 160px" size="mini" @change="onSearch">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="日期">
                        <el-date-picker style="width: 240px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="filter.workType" style="width: 80px" size="mini" @change="onSearch" clearable
                            placeholder="班次">
                            <el-option label="白班" value="白班"></el-option>
                            <el-option label="晚班" value="晚班"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.postCode" placeholder="一级岗位编码" style="width:120px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item label="">
                        <el-select v-model="filter.postName" style="width: 110px" size="mini" @change="onSearch" clearable
                            filterable placeholder="一级岗位名称">
                            <el-option v-for="item in myOnePostNameList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.workItem" placeholder="二级岗位名称" style="width:110px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.account" placeholder="账号" style="width:100px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model.trim="filter.userName" placeholder="姓名" style="width:110px;" clearable
                            maxlength="20" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onCompute">计算卸货入库薪资</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onComputeWages">配置卸货入库工价</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
                :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false"
                :isSelectColumn="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="计算卸货入库计件薪资" :visible.sync="dialogVisibleCompute" width="20%" v-dialogDrag
            :close-on-click-modal="false">
            <span>
                <el-row style="margin-bottom: 5px;font-size:14px; color:#EE7700">
                    不选仓库则计算全部仓库，日期必选。
                </el-row>
                <el-row style="margin-bottom: 5px;">
                    <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                        <el-select v-model="computeWarehouseCode" style="width: 280px" size="mini" clearable
                            placeholder="请选择仓库">
                            <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                        <el-date-picker style="width: 280px" v-model="computeWorkDate" type="daterange" format="yyyy-MM-dd"
                            :picker-options="pickOptions" value-format="yyyy-MM-dd" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-col>
                </el-row>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="copmuteLoading" @click="onCopmuteSave" /> &nbsp;
                <el-button @click="dialogVisibleCompute = false">关闭</el-button>
            </span>
        </el-dialog>

        <el-dialog title="卸货入库工价配置" :visible.sync="dialogVisibleStorageWages" width="25%" v-dialogDrag
            v-loading="storageWagesLoading" :close-on-click-modal="false">
            <span>
                <el-form class="ad-form-query" v-model="storageWagesFormData" label-width="80px"
                    :rules="storageWagesFormRules">
                    <el-row>
                        <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                            <el-form-item label="仓库" prop="warehouseCode">
                                <el-select v-model="storageWagesFormData.warehouseCode" style="width: 280px" size="mini"
                                    clearable placeholder="请选择仓库" @change="getComputeWages()">
                                    <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                            <el-form-item label="件数工价" prop="numWages">
                                <el-input-number v-model.trim="storageWagesFormData.numWages" :min="0" :max="1000000"
                                    auto-complete="off" :precision="4" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="18" :md="24" :lg="24" :xl="24">
                            <el-form-item label="任务工价" prop="taskWages">
                                <el-input-number v-model.trim="storageWagesFormData.taskWages" :min="0" :max="1000000"
                                    auto-complete="off" :precision="4" style="width:180px;" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :loading="storageWagesLoading" @click="onVideoStorageWagesSave" /> &nbsp;
                <el-button @click="dialogVisibleStorageWages = false">关闭</el-button>
            </span>
        </el-dialog>

    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import MySearch from "@/components/my-search";
import buschar from '@/components/Bus/buschar';
import MySearchWindow from "@/components/my-search-window";
import {
    getWarehouseVideoStorageWagesPageList, computeWarehouseVideoStorageWages,
    getWarehouseVideoStorageWagesSetPageList, setWarehouseVideoStorageWages
} from '@/api/profit/warehousewages';
const tableCols = [
    { istrue: true, prop: 'workDate', label: '日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.workDate, 'YYYY-MM-DD') },
    { istrue: true, prop: 'warehouseName', label: '仓库', width: '160', sortable: 'custom' },
    { istrue: true, prop: 'workType', label: '班次', width: '70', sortable: 'custom' },
    { istrue: true, prop: 'postCode', label: '一级岗位编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'postName', label: '一级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'workItem', label: '二级岗位名称', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'userName', label: '姓名', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'wages', label: '工价', width: '60', sortable: 'custom' },
    { istrue: true, prop: 'workCount', label: '计件数量', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'workDayWages', label: '工资', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'computeTime', label: '计算时间', width: '150', sortable: 'custom' },
]
const tableHandles1 = [
    //{ label: "计算人效", handle: (that) => that.onCompute() },
    // { label: "导出", handle: (that) => that.onExport() },
];
export default {
    name: 'routinewages',
    components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow, buschar },
    props: ['myWarehouseList', 'myOnePostNameList'],
    data() {
        return {
            that: this,
            filter: {
                timerange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
                ],
                startDate: null,
                endDate: null,
                warehouseCode: 10361546,//诚信仓
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "workDate", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
            pickerOptions: {
                shortcuts: [
                    {
                        text: '昨天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime());
                            picker.$emit('pick', [start, start]);
                        }
                    }, {
                        text: '近三天',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime());
                            const end = new Date(new Date(tdate.toLocaleDateString()));
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一周',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                            const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '近一个月',
                        onClick(picker) {
                            const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                            console.log("获取前一个月的时间", tdate.getDay());
                            const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                            const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
            },
            tableHandles1: tableHandles1,

            dialogVisibleCompute: false,
            copmuteLoading: false,
            computeWarehouseCode: null,
            computeWorkDate: [],
            bComputeWorkDate: null,
            eComputeWorkDate: null,

            dialogVisibleStorageWages: false,
            storageWagesLoading: false,
            storageWagesFormRules: {
                warehouseCode: [{ required: true, message: '请输入仓库', trigger: 'blur' }],
            },
            storageWagesFormData: {
                id: 0,
                warehouseCode: null,
                warehouseName: null,
                numWages: 0,
                taskWages: 0,
            },
        };
    },
    async mounted() {
        await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            console.log(this.filter)
            if (!this.filter.timerange) {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params, 'params')
            this.listLoading = true;
            const res = await getWarehouseVideoStorageWagesPageList(params)
            this.listLoading = false;
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            data.forEach(d => {
                d._loading = false;
            })
            this.list = data;
        },
        //排序查 询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            console.log(rows)
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onCompute() {
            this.dialogVisibleCompute = true;
            this.computeWarehouseCode = this.filter.warehouseCode;
        },
        async onCopmuteSave() {
            if (!this.computeWorkDate || this.computeWorkDate.length <= 0) {
                this.$message({ type: 'error', message: '请选择要计算的日期!' });
                return;
            }
            else {
                this.bComputeWorkDate = formatTime(this.computeWorkDate[0], "YYYY-MM-DD");
                this.eComputeWorkDate = formatTime(this.computeWorkDate[1], "YYYY-MM-DD");
                this.copmuteLoading = true;
                let res = await computeWarehouseVideoStorageWages({
                    bWorkDate: this.bComputeWorkDate,
                    eWorkDate: this.eComputeWorkDate,
                    warehouseCode: this.computeWarehouseCode
                });
                this.copmuteLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '计算人效中..请几分钟后刷新查看!' });
                    this.dialogVisibleCompute = false;
                }
            }
        },
        async onComputeWages() {
            this.dialogVisibleStorageWages = true;
            this.storageWagesFormData.warehouseCode = this.filter.warehouseCode;
            this.getComputeWages();
        },
        async getComputeWages() {
            let params = { warehouseCode: this.storageWagesFormData.warehouseCode, currentPage: 1, pageSize: 1 };
            console.log(params, 'params')
            this.storageWagesLoading = true;
            const res = await getWarehouseVideoStorageWagesSetPageList(params);
            this.storageWagesLoading = false;
            if (res?.success && res?.data?.list && res?.data?.list.length > 0) {
                let data = res.data.list[0];
                console.log(data);
                this.storageWagesFormData = {
                    id: data.id,
                    warehouseCode: data.warehouseCode,
                    warehouseName: data.warehouseName,
                    numWages: data.numWages,
                    taskWages: data.taskWages,
                };
            }
            else {
                this.storageWagesFormData.id = 0;
                this.storageWagesFormData.numWages = 0;
                this.storageWagesFormData.taskWages = 0;
            }
        },
        async onVideoStorageWagesSave() {
            if (!this.storageWagesFormData.warehouseCode) {
                this.$message({ type: 'error', message: '请选择仓库!' });
                return;
            }
            this.storageWagesFormData.warehouseName = this.myWarehouseList.find(f => f.value == this.storageWagesFormData.warehouseCode)?.label;
            console.log(this.storageWagesFormData);
            this.storageWagesLoading = true;
            const res = await setWarehouseVideoStorageWages(this.storageWagesFormData);
            this.storageWagesLoading = false;
            if (res?.success) {
                this.$message({ type: 'success', message: '配置工价成功!' });
                this.dialogVisibleStorageWages = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
