<template>
    <container v-loading="pageLoading">
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' 
            :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"  @select="selectchange"
            :tableData='list' :tableCols='tableCols' :isSelection="true"  :loading="listLoading">
                <template slot='extentbtn'>
                    <!-- <el-button-group>
                        <el-select v-model="filter.applyState" style="width:90px;">
                            <el-option v-for="item in applyStates" :label="item.label" :value="item.value"></el-option>                          
                        </el-select>
                    </el-button-group>
                    <el-button type="primary" @click="onSearch" style="float:none;" icon="el-icon-search">查询</el-button> -->
                    <el-button type="primary" @click="onExport" style="float:none;" icon="el-icon-download">导出</el-button>
                    <el-button type="primary" @click="zrBatchAudit" style="float:none;" icon="el-icon-s-check">批量审核</el-button>
                    <el-button type="primary" @click="zrBatchFirstAudit" style="float:none;" icon="el-icon-s-check">批量初审</el-button>

                    <el-button type="primary" @click="onNewZrDeptAuditMng" style="float:none;" icon="el-icon-user-solid">管理部门初审人</el-button>
                    <el-button type="primary" @click="onDisableApplyUserMng" style="float:none;" icon="el-icon-user-solid">管理禁止申诉人</el-button>

                    
                    <span style="color:red">【是否认可】操作时限为17:30到次日10:00，过时将自动认可！</span>
                    
                </template>
            </ces-table>
            <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag>
                <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" :isTx="isTx" />
            </el-dialog>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="selids.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
    import container from '@/components/my-container/noheader'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime, } from "@/utils";
    import { formatLinkProCode, formatSendWarehouse, formatExpressCompany } from "@/utils/tools";
    import { PageDeductZrAppealList,AuditDeductZrAppeal ,ExportDeductZrAppeal} from "@/api/order/orderdeductmoney"
    import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";

    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
   
    const fmtIllegalType=function(val){
        if(val==1)
            return "缺货";
        else if(val==2)
            return "虚假轨迹";
        else if(val==3)
            return "延迟发货";
        else if(val==9)
            return "虚假发货";
        else 
            return val;
    }

    const fmtApplyState=function(val){
        if(val==-1) return "已拒绝";
        else if(val==0) return "待申请";
        else if(val==1) return "待审核";
        else if(val==2) return "已审核";
        return val;
    }

    const applyStates=[
        {label:'全部',value:null},
        {label:'待审核',value:1},
        {label:'已审核',value:2},
        {label:'已拒绝',value:-1},        
    ]

    const tableCols = [
        { istrue: true, prop: 'orderPlatform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => row.orderPlatformText },
        { istrue: true, prop: 'orderNo', label: '订单编号', width: '180', sortable: 'custom', formatter: (row) => !row.orderNo ? " " : row.orderNo, type: 'click', handle: (that, row) => that.showLogDetail(row) },
        { istrue: true, prop: 'deductOccurTime', label: '扣款日期', width: '90', sortable: 'custom', formatter: (row) => formatTime(row.deductOccurTime, "YYYY-MM-DD") },
        { istrue: true, prop: 'illegalType', label: '平台原因', width: 'auto', sortable: 'custom', formatter: (row) => fmtIllegalType(row.illegalType)},
        { istrue: true, prop: 'amountPaid', label: '扣款金额', width: '60', sortable: 'custom' },

        { istrue: true, prop: 'groupName', label: '小组', width: '60' , sortable: 'custom'},
        { istrue: true, prop: 'operateSpecialUserName', label: '运营',  width: '60', sortable: 'custom'},

        { istrue: true, prop: 'orgZrType1', label: '原责任类型', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'orgZrType2', label: '原责任原因', width: '90', sortable: 'custom' },

        { istrue: true, prop: 'orgZrDeptAction', label: '原责任部门', width: '98', sortable: 'custom' },
        { istrue: true, prop: 'orgZrReason', label: '原责任原因2', width: '95', sortable: 'custom' },
        { istrue: true, prop: 'orgMemberName', label: '原责任人', width: '76', sortable: 'custom' },
        { istrue: true, prop: 'allocater', label: '责任中台', width: '70', sortable: 'custom'  },
        { istrue: true, prop: 'orgZrSetTime', label: '原责任计算时间', width: '128', sortable: 'custom', formatter: (row) => formatTime(row.orgZrSetTime, "YYYY-MM-DD HH:mm") },
        { istrue: true, prop: 'orgZrConditionFullName', label: '原责任规则', width: '200', sortable: 'custom' },
        
        { istrue: true, prop: 'applyUserName', label: '申请人', width: '76', sortable: 'custom' },
        { istrue: true, prop: 'applyTime', label: '申请时间', width: '128', sortable: 'custom', formatter: (row) => formatTime(row.applyTime, "YYYY-MM-DD HH:mm") },

        { istrue: true, prop: 'newZrType1', label: '新责任类型', width: '90', sortable: 'custom' },
        { istrue: true, prop: 'newZrType2', label: '新责任原因', width: '90', sortable: 'custom' },

        { istrue: true, prop: 'newZrDeptAction', label: '新责任部门', width: '98', sortable: 'custom' },
        { istrue: true, prop: 'newZrReason', label: '新责任原因2', width: '95', sortable: 'custom' },
        { istrue: true, prop: 'newMemberName', label: '新责任人', width: '76', sortable: 'custom' },
               
        
        { istrue: true, prop: 'newZrConditionFullName', label: '申诉理由', minwidth: '160', sortable: 'custom' },
        { istrue: true, prop: 'applyContent', label: '申请内容', width: '128', sortable: 'custom' },

        //{ istrue: true, prop: 'firstAuditState', label: '是否认可', width: '86', sortable: 'custom', formatter: (row) => row.firstAuditStateText},
        { istrue:true, prop: 'firstAuditState', sortable: 'custom', label:'是否认可', width:'100',type:'button',  btnList:[
            {
                label:"",
                htmlformatter:(row)=> row.firstAuditStateText,
                handle:(that,row)=>{}
            },
            {
                label:'查看',
                handle:(that,row)=>that.zrApply(row,false),
                ishide:(that,row)=> !row.firstAuditState 
            },
            {
                label:'处理',
                handle:(that,row)=>that.zrApplyFirstAudit(row),
                ishide:(that,row)=>{ return !row.allowFirstAudit; }
            },
        ]} ,


        { istrue: true, prop: 'sendTime', label: '发货日期', width: '150', sortable: 'custom', formatter: (row) => !row.sendTime ? "" : row.sendTime },
        { istrue: true, prop: 'planDeliveryDate', label: '预计发货时间', width: '124', sortable: 'custom', formatter: (row) => !row.planDeliveryDate ? " " : formatTime(row.planDeliveryDate, "YYYY-MM-DD HH:mm") },


        { istrue: true, prop: 'auditUserName', label: '审核人', width: '80', sortable: 'custom' }, //陈丽君要求开放审核人给所有人查看 , permission:"order:orderdeductmoney:ShowZrApplyAuditor"
        { istrue: true, prop: 'auditTime', label: '审核时间', width: '128', sortable: 'custom', formatter: (row) => !!row.auditTime ? formatTime(row.auditTime, "YYYY-MM-DD HH:mm"):"" },
        { istrue: true, prop: 'auditRemark', label: '审核意见', width: '120', sortable: 'custom' },      
        { istrue: true, prop: 'applyState', label: '审核状态', width: '86', sortable: 'custom', formatter: (row) => row.applyStateText},
        { istrue:true,label:'功能',width:'100',type:'button',fixed:'right',  btnList:[
            {
                label:'查看',
                handle:(that,row)=>that.zrApply(row,false),
                
            },
            {
                label:'审核',
                handle:(that,row)=>that.zrApply(row,true),
                ishide:(that,row)=>{ return row.applyState!=1; }
            },
        ]} 
    ]

    export default {
        name: 'OrderDeductZrApplyList',
        components: { cesTable, container, MyConfirmButton, MySearch, MySearchWindow, OrderActionsByInnerNos },       
        props: {
            outFilter: {},            
        },
        data() {
            return {
                filter: {
                    //applyState:1
                },
                applyStates:applyStates,
                dialogHisVisible: false,
                orderNo: '',
                that: this,
                list: [],
                platformList: [],
                illegalTypeList: [],
                summaryarry: {},
                pager: { OrderBy: "id", IsAsc: false },                        
                tableCols: tableCols,             
                total: 0,
                sels: [],
                selids:[],
                listLoading: false,
                pageLoading: false,
                dialogVisible: false,     
                isTx:false,         
            };
        },
        async mounted() {
            await this.setPlatform()
            await this.onSearch()
        },
        methods: {
            //初审代理人管理
            onNewZrDeptAuditMng(){
                let self=this;            
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductNewZrDeptAuditUserMng.vue`,
                    title: '管理部门初审人',
                    autoTitle:false,
                    args: {mode:3},
                    height: 500,
                    width: '640px',
                    //callOk: self.onSearch
                })
            },
            //禁止申诉人管理
            onDisableApplyUserMng(){
                let self=this;            
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductNewZrApplyDisableUserMng.vue`,
                    title: '管理禁止申诉人',
                    autoTitle:false,
                    args: {mode:3},
                    height: 500,
                    width: '640px',
                    //callOk: self.onSearch
                })
            },
            //首次审核
            async zrApplyFirstAudit(row){    
                let self=this;            
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyFirstAuditForm.vue`,
                    title: '初审认可处理',
                    autoTitle:false,
                    args: {id:row.id,mode:3},
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
            },   
            //申诉
            async zrApply(row,isAuditor){    
                let self=this;            
                this.$showDialogform({
                    path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyForm.vue`,
                    title: isAuditor?'责任申诉审核':'责任申诉明细',
                    autoTitle:false,
                    args: {id:row.id,mode:3,isAuditor:isAuditor},
                    height: 300,
                    width: '80%',
                    callOk: self.onSearch
                })
            },  
            async zrBatchFirstAudit() {
                let self=this;            
                if(self.sels && self.sels.length>0){       

                    this.$showDialogform({
                        path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyBatchFirstAuditForm.vue`,
                        title:'批量初审',
                        autoTitle:false,
                        args: {rows:[...self.sels],mode:2},
                        height: 300,
                        width: '80%',
                        callOk: self.onSearch
                    });
                }else{
                    this.$message.error('请选择要处理的数据！');
                }
            },
            async zrBatchAudit() {
                let self=this;            
                if(self.sels && self.sels.length>0){       

                    this.$showDialogform({
                        path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyBatchAuditForm.vue`,
                        title:'批量审核',
                        autoTitle:false,
                        args: {rows:[...self.sels],mode:2},
                        height: 300,
                        width: '80%',
                        callOk: self.onSearch
                    });
                }else{
                    this.$message.error('请选择要处理的数据！');
                }
            },
            showLogDetail(row) {                
                this.isTx=row.deductPlatform==1;
                this.dialogHisVisible = true;
                this.orderNo = row.orderNo;
            },           
            onShowLogistics(row) {
                let self = this;
                this.$showDialogform({
                    path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
                    title: '物流明细',
                    args: { expressNos:row.expressNo },
                    height: 300,
                });
            },
            //设置平台,扣款因下拉
            async setPlatform() {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
                var ilrule = await ruleIllegalType();
                this.illegalTypeList = ilrule.options;
            },       
            //查询第一页
            async onSearch() {            
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //导出
            async onExport(){
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await ExportDeductZrAppeal(params);
                loadingInstance.close();
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '扣款责任申诉数据_' + new Date().toLocaleString() + '.xlsx')
                aLink.click();
            },
            //获取查询条件
            getCondition() {                
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ...this.outFilter,
                    ... this.filter
                }

                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true
                const res = await PageDeductZrAppealList(params)
                this.listLoading = false
                if (!res?.success) {
                    return
                }

                this.selids=[];
                this.sels=[];

                this.total = res.data.total;
                const data = res.data.list;             
                this.list = data;
                this.summaryarry=res.data.summary;
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },  
            selectchange: function (rows, row) {
                this.selids = []; 
                this.sels=[];
                //console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                    this.sels.push(f);
                })
            },  
        },
    };
</script>

<style lang="scss" scoped>
</style>
