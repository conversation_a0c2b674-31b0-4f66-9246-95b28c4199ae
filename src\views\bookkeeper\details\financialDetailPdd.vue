<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form
        class="ad-form-query"
        :inline="true"
        :model="Filter"
        @submit.native.prevent>
        <el-form-item  label="年月:">
          <el-date-picker style="width: 110px" v-model="Filter.yearMonth" type="month" format="yyyyMM"   value-format="yyyyMM" placeholder="选择月份"></el-date-picker>
        </el-form-item>
        <el-form-item label="所属店铺:" label-position="right" >
          <el-select filterable v-model="Filter.nameShop" placeholder="请选择" class="el-select-content">
            <el-option 
              v-for="item in shopList"
              :key="item.shopName"
              :label="item.shopName"
              :value="item.shopName">             
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号:" label-position="right" >
            <el-input v-model="Filter.serialNumberOrder" style="width:183px;"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :tableData='ZTCKeyWordList' 
              @select='selectchange' :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
      <el-table-column type="expand">
        <template slot-scope="props">
        <div>
          <el-table :data="props.row.detaildata" style="width: 100%">
            <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
            </el-table-column>
          </el-table>
        </div>
      </template>
      </el-table-column>
       <template slot='extentbtn'>
          <el-button-group>            
            <el-button type="primary" @click="onRefresh">刷新</el-button>
          </el-button-group>
        </template>
    </ces-table>    
    <!--分页-->
    <template #footer>
      <my-pagination
        ref="pager"
        :total="total"
        :checked-count="sels.length"
        @get-page="getList"
      />
    </template>
  </my-container>
</template>
<script>
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList
       } from '@/api/operatemanage/base/shop';
import { formatPlatform,formatLink} from "@/utils/tools";
import {getFinancialDetailPddPageList } from '@/api/bookkeeper/financialDetail'

const tableCols =[
      {istrue:true,prop:'orderNumber',label:'商户订单号',sortable:'custom', width:'auto'},
      {istrue:true,prop:'timeOccur',label:'发生时间',sortable:'custom', width:'auto'},
      {istrue:true,prop:'amountIn',label:'收入金额(+元)',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountIn?.toFixed(2)}},
      {istrue:true,prop:'amountOut',label:'支出金额(-元)',sortable:'custom', width:'auto',type:'html',formatter:(row)=>{return row.amountOut?.toFixed(2)}},
      {istrue:true,prop:'orderType',label:'账务类型',sortable:'custom', width:'auto'},
      {istrue:true,prop:'remark',label:'备注',sortable:'custom', width:'auto'},
     ];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow ,cesTable},
  data() {
    return {
      that:this,
      Filter: {
        yearMonth:null,
        timerange: [formatTime(dayjs().subtract(3, 'month'), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
      },
       pickerOptions: {
          shortcuts: [{
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()]);
            }
          },  {
            text: '最近3个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
      shopList:[],
      userList:[],
      groupList:[],
      ZTCKeyWordList: [],
      tableCols:tableCols,
      total: 0,
      pager:{OrderBy:"id",IsAsc:false},
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids:[],
      dialogVisibleSyj:false,
      fileList:[],
    };
  },
  async mounted() {
    await this.getShopList();
  },
  methods: {
    sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    async getShopList(){
      const res1 = await getAllShopList();
      this.shopList=[];
        res1.data?.forEach(f => {
            if(f.isCalcSettlement&&f.shopCode)
              this.shopList.push(f);
        });
    },
    onRefresh(){
        this.onSearch()
    },
    onSearch(){
       this.$refs.pager.setPage(1);
       this.getList();
    },
    async getList(){
      const params = {...pager, ...this.pager,  ...para, }; 
      const res = await getFinancialDetailPddPageList(params);
      this.listLoading = false;
      this.total = res.data?.total
      this.ZTCKeyWordList = res.data?.list;
    },
    selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>