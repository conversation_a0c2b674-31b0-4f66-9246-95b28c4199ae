<template>
  <container>
    <template>
        <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>  
          <el-form-item label="平台:">
          <el-select v-model="filter.plarform" placeholder="请选择" @change="seriechange">
            <el-option v-for="item in options" :key="item.value"  :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        </el-form>
    </template>
    <ces-table ref="table" :that='that' :isIndex='true' style="height:90%" :isSelectColumn="false"
          :hasexpand='false' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading"
          :showsummary='true' :summaryarry='summaryarry'>
          </ces-table>
  </container>
</template>

<script>
import { formatTime } from "@/utils";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import {getDayReportEDetailAsync} from '@/api/financial/yyfy'
const tableCols = [
    {istrue:true, prop:'yearMonthDay', label:'年月日', width:'100',formatter:(row)=>formatTime(row.yearMonthDay,'YYYYMMDD') },
    {istrue:true, prop:'goodsCode', label:'商品编码', width:'120',},
    {istrue:true, prop:'goodsName', label:'商品名称', width:'150', },
    {istrue:true, prop:'shopCode', label:'店铺', width:'150', sortable:'custom', formatter:(row)=>row.shopCode == null ? " " : row.shopName},
    {istrue:true, prop:'salesOrderNum', label:'销售订单数', width:'100', sortable:'custom'},
    {istrue:true, prop:'salesNum', label:'销售数量(扣退)', width:'120', sortable:'custom'},
    {istrue:true, prop:'salesAmount', label:'销售金额(扣退)', width:'120', sortable:'custom'},
    {istrue:true, prop:'salesConst', label:'销售成本(扣退)', width:'120', sortable:'custom'},
    {istrue:true, prop:'profit1', label:'订单毛利', width:'auto', sortable:'custom'},
];
const tableHandles=[ ];
export default {
    name: 'YunhanAdminCodingdaily2',
    components: {container,cesTable},
    data() {
        return {
          activeName:'first',
          that:this,
          list:[],
          summaryarry:{}, 
          listLoading: false,
          tableCols:tableCols,
          tableHandles:tableHandles,
          filter: { 
            goodsCode: "",
            yearMonthDay: "" ,
            plarform: null
            },
          options: [{value: 0,label: '全部'},
                    {value: 1,label: '淘系'},
                    {value: 2,label: '多多'},
                    {value: 8,label: '工厂'},],  
        };
    },

    mounted() {
        
    },
    methods: {
      onSearch(goodsCode, yearMonthDay) {
        this.activeName='first'
        this.filter = { goodsCode: goodsCode ,yearMonthDay: yearMonthDay },
        this.getlist();
      },
      async getlist(){
        const params = {goodsCode : this.filter.goodsCode ,yearMonthDay: this.filter.yearMonthDay, plarform: this.filter.plarform }
        console.log('params',params)
        this.listLoading = true
        const res = await getDayReportEDetailAsync(params)
        this.listLoading = false
        if (!res?.success) return
        const data = res.data.list
        this.summaryarry = res.data.summary
        console.log('汇总',this.summaryarry)
        data.forEach(d => {d._loading = false})
        this.$nextTick(() => {
            this.list = data
        })      
    },
    async seriechange(val)
    {
      this.filter.plarform=val
      this.getlist()
    }
    },
};
</script>

<style lang="scss" scoped>

</style>