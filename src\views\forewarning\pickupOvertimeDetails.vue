<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="payTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="支付时间" end-placeholder="支付时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'zf')" />
                <el-date-picker v-model="sendTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="发货时间" end-placeholder="发货时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'fh')" />
                <el-date-picker v-model="planTimeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="计划发货时间" end-placeholder="计划发货时间" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 'jh')" />
                <el-input v-model.trim="ListInfo.orderNoInner" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNoOuter" placeholder="线上订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.expressNo" placeholder="快递单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.minRemainingPickupHour" placeholder="最小剩余揽收时间" maxlength="5" clearable
                    style="width: 120px;margin-bottom: 5px;margin-right: 5px;" />
                <el-input v-model.trim="ListInfo.maxRemainingPickupHour" placeholder="最大剩余揽收时间" maxlength="5" clearable
                    style="width: 120px;margin-bottom: 5px;margin-right: 5px;" />
                <el-select v-model="ListInfo.orderStatuses" multiple placeholder="订单状态" class="publicCss" clearable
                    :collapse-tags="true">
                    <el-option key="已发货" label="已发货" value="已发货" />
                    <el-option key="异常" label="异常" value="异常" />
                    <el-option key="发货中" label="发货中" value="发货中" />
                    <el-option key="已支付" label="已支付" value="已支付" />
                    <el-option key="待审核" label="待审核" value="待审核" />
                </el-select>
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable filterable>
                    <el-option v-for="item in platformlist" :key="item" :label="item" :value="item" />
                </el-select>
                <inputYunhan ref="productCode" :inputt.sync="ListInfo.storeStr" v-model="ListInfo.storeStr"
                    width="200px" placeholder="店铺/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="300"
                    :maxlength="6000" @callback="shopCallback" title="店铺" style="margin: 0 5px 5px 0;padding: 0;">
                </inputYunhan>
                <el-select v-model="ListInfo.sendWarehouses" placeholder="发货仓" style="width: 290px;margin-right: 5px;"
                    clearable multiple collapse-tags filterable>
                    <el-option v-for="(item, i) in wareHouseList" :key="item.wmsId + '-' + i" :label="item.wmsName"
                        :value="item.wmsName" />
                </el-select>
                <el-select v-model="ListInfo.expressCompanies" placeholder="快递公司" filterable
                    style="width: 220px;margin-right: 10px;" clearable collapse-tags multiple>
                    <el-option v-for="item in kdCompany" :key="item" :label="item" :value="item" />
                </el-select>
                <!-- <div style="display: flex;align-items: center;">
                    <span style="font-size: 14px;">排除订单:</span>
                    <el-select v-model="ListInfo.excludeTypes" placeholder="排除订单" class="publicCss" clearable multiple
                        :collapse-tags="true">
                        <el-option v-for="item in excludeTypesSel " :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div> -->
                <div style="display: flex;">
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="exportDisabled">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase :id="'pickupOvertimeDetails202408040533'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols="tableCols" :isSelection="false"
            :visibleMethod="() => { return false }" @cellClick="openExpand" :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'"
            :expandConfig="{ accordion: true, trigger: 'cell', }">
            <template slot="left">
                <vxe-column title="宝贝id" width="120" type="expand" align="left" :edit-render="{}" field="expand">
                    <template #default="{ row, rowIndex }">
                        <div class="clihover" v-show="row.products">
                            <i :class="(rowIndex == isselindex && isselnow) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
                                style="font-size: 14px;"></i>
                        </div>
                    </template>
                    <template #content="{ row, rowIndex }">
                        <div v-if="neitableshow" style="height: 110px;">
                            <vxetablebase :id="'pickupOvertimeDetails202408040533_2'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
                                @sortchange='sortchange' :tableData='row.products ? JSON.parse(row.products) : []'
                                :tableCols="tableCols1" :isSelection="false" :isSelectColumn="false"
                                style="margin: 0;height: 100%;" v-loading="loading" class="already" />
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { PageGetData, GetLogisticsWmsHeads, GetColumns, GetExpressCompanies, ExportData, GetPlatforms } from '@/api/warning/LogisticsCollecting'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
const excludeTypesSel = [
    { label: '售后订单', value: 0 },
    { label: '补差价订单', value: 1 },
    { label: '物流公司-南昌菜鸟京广 ', value: 2 },
    { label: '物流公司-包含有【代发】字样', value: 3 },
]
const tableCols1 = [
    { istrue: true, prop: 'ProCode', label: '宝贝id', width: '200', align: 'left' },
    { istrue: true, prop: 'GoodsName', label: '商品名称', align: 'left' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, inputYunhan
    },
    data() {
        return {
            isselindex: -1,
            isselnow: false,
            neitableshow: false,
            tableCols: [],
            tableCols1,
            that: this,
            excludeTypesSel,
            platformlist: [],
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                payStartTime: null,//支付开始时间
                payEndTime: null,//支付结束时间
                sendStartTime: null,//发货开始时间
                sendEndTime: null,//发货结束时间
                planSendStartTime: null,//计划发货开始时间
                planSendEndTime: null,//计划发货结束时间
                expressCompanies: [],//快递公司
                // excludeTypes: [0, 1, 2, 3],
                isTimeOut: true,
                sendWarehouse: null,//发货仓
                sendWarehouses: [],//发货仓
                storeId: null,//店铺
                store: null,//店铺
                storeStr: null,//店铺
                stores: null,//店铺
                platform: null,//平台
                orderStatuses: null,//订单状态
                remainingPickupHour: null,//剩余揽收时间
                minRemainingPickupHour: null,//最小剩余揽收时间
                maxRemainingPickupHour: null,//最大剩余揽收时间
                expressNo: null,//快递单号
                orderNoOuter: null,//线上订单号
                orderNoInner: null,//内部订单号
            },
            payTimeRanges: [],
            sendTimeRanges: [],
            planTimeRanges: [],
            total: 0,
            loading: false,
            pickerOptions,
            tableData: [],
            wareHouseList: [],
            kdCompany: [],
            exportDisabled: false,
        }
    },
    async mounted() {
        await this.getClos()
        await this.getList()
        await this.getWareHouse()
        await this.getKdCompany()
        await this.getPlatForm()
    },
    methods: {
        async getPlatForm() {
            const { data, success } = await GetPlatforms()
            if (success) {
                this.platformlist = data
            }
        },
        async openExpand({ row, rowIndex }) {
            this.neitableshow = false
            if (!row.products) return
            let _this = this;
            if (rowIndex == _this.isselindex) {
                this.isselnow = !this.isselnow;
            } else {
                this.isselnow = true
            }
            _this.isselindex = rowIndex;
            this.neitableshow = true
        },
        async getKdCompany() {
            const { data, success } = await GetExpressCompanies()
            if (success) {
                this.kdCompany = data
            }
        },
        async getClos() {
            const { data, success } = await GetColumns()
            if (success) {
                data.forEach(item => {
                    if (item.label == '内部订单号') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                        item.align = 'left'
                    }
                    item.width = '120'
                })
                this.tableCols = data
            }
        },
        shopCallback(val) {
            this.ListInfo.storeStr = val
            this.ListInfo.stores = this.ListInfo.storeStr ? this.ListInfo.storeStr.split(',') : []
        },
        async getWareHouse() {
            const { data, success } = await GetLogisticsWmsHeads()
            if (success) {
                this.wareHouseList = data
            }
        },
        async changeTime(e, type) {
            //如果type为支付时间,并且有时间范围,就赋值,否则就清空,使用三元
            if (type == 'zf') {
                this.ListInfo.payStartTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
                this.ListInfo.payEndTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            } else if (type == 'fh') {
                this.ListInfo.sendStartTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
                this.ListInfo.sendEndTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            } else if (type == 'jh') {
                this.ListInfo.planSendStartTime = e ? dayjs(e[0]).format('YYYY-MM-DD') : null
                this.ListInfo.planSendEndTime = e ? dayjs(e[1]).format('YYYY-MM-DD') : null
            }
            this.getList()
        },
        async exportProps() {
            this.exportDisabled = true
            const { data } = await ExportData(this.ListInfo)
            const aLink = document.createElement("a");
            let blob = new Blob([data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '揽收已超时明细' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
            this.exportDisabled = false
        },
        async getList(type) {
            //如果最大揽收时间小于最小揽收时间,就提示
            if (this.ListInfo.minRemainingPickupHour && this.ListInfo.maxRemainingPickupHour && this.ListInfo.minRemainingPickupHour > this.ListInfo.maxRemainingPickupHour) {
                this.$message.error('最大揽收时间不能小于最小揽收时间')
                return
            }
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total }, success } = await PageGetData(this.ListInfo)
            if (success) {
                this.tableData = list
                this.loading = false
                this.total = total
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                if (prop == 'remainingPickupRealTimeStr') {
                    this.ListInfo.orderBy = 'RemainingPickupRealTime'
                } else {
                    this.ListInfo.orderBy = prop
                }
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;
    align-items: center;

    .publicCss {
        width: 190px;
        margin: 0 5px 5px 0;
    }
}

.already ::v-deep .vxe-tools--operate {
    display: none !important;
}
</style>
