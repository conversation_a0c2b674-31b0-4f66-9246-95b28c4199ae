<template>
    <my-container v-loading="pageLoading"  :element-loading-text="upmsginfo">
        <div class="rowstyle" >
            <div class="marleftrig" ref="oneboxx">
                <div class="aligncenter">
                    <div class="flexrow">
                        <el-button-group>
                        <el-button :type="num==1?'primary':''" @click="whactclick(1)" style="width: 150px;">成品</el-button>
                        <el-button :type="num==2?'primary':''" @click="whactclick(2)" style="width: 150px;">素材</el-button>
                        </el-button-group>
                    </div>
                    <!-- <div style="margin-top: 10px;"><el-button type="primary" style="width:200px;" @click="sumbitUploadInfo()">点击保存</el-button></div> -->
                </div>
            </div>
            <div class="content" v-if="contentshow">
                <div v-show="num==1" style="width: 100%; height: 100%;" class="overhiddle">
                    <!-- 一 -->
                  
                    <div  v-for="(i,index ) in InitDataArrayOne" :key="index" style="width: 470px;  border: 1px solid #eee;" :style="{height: i.outheight}"   class="backcolor">
                        <div style="margin: 10px 20px;" >
                            <span>{{i.title}}</span>
                            <uploadfileforsuccess
                                :ref="i.refName" 
                                :uploadInfo="i.uploadinfo" 
                                :limit="10000" 
                                :islook="islook"
                                :accepttyes="i.acctpye" 
                                :uptype="i.uptype" 
                            :contentheight="i.contentheight">
                            </uploadfileforsuccess>
                        </div>
                    </div>
       
                  
                </div>
                <!-- ///素材 -->
                <div v-show="num==2" style="width: 100%; height: 100%;" class="overhiddle">
                    <div  v-for="(i,index ) in InitDataArrayTwo" :key="index" style="width: 470px;  border: 1px solid #eee;" :style="{height: i.outheight}" class="backcolor">
                            <div style="margin: 10px 20px;" >
                                <span>{{i.title}}</span>
                                <uploadfileforsuccess
                                    :ref="i.refName" 
                                    :uploadInfo="i.uploadinfo" 
                                    :limit="10000" 
                                    :islook="islook"
                                    :accepttyes="i.acctpye" 
                                    :uptype="i.uptype"
                                    :contentheight="i.contentheight">
                                </uploadfileforsuccess>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </my-container>
</template>

<script>
import { xMTVideoUploadBlockAsync } from '@/api/upload/filenew'
import MyContainer from "@/components/my-container";
import uploadfileforsuccess from '@/views/media/shooting/fuJianmanage/uploadfilefu';
import {getUploadSuccessAttachmentNew,checkShootingTaskAction,uploadSuccessAttachment  } from '@/api/media/ShootingVideo';
export default {
    name: 'DEMOResultmatter',
    components: { MyContainer ,uploadfileforsuccess},
    data() {
        return {
            pageLoading:false,
            InitDataArrayOne:[],
            InitDataArrayTwo:[],
            imgtype:".image/jpg,image/jpeg,image/png",
            rartype:".rar,.zip,.7z",
            psdtype:".psd,.psb",
            vediotype:".mp4,.mov,.vedio,.av,.wmv,.mpg,.mpeg,.rm,.flv,.swf",
            num:1,

            upmsginfo:'努力加载中',
            uploadinfos:[],
            uploadtype:0,
            upmsgtxt:null,
            percentage:0,
            contentshow: true,
            islook: true
        };
    },

    async mounted() {
        await this.gettaskid();
        await this.initdata();
        
    },

    methods: { 
        gettaskid(){
            this.rowinfo = this.$route.query.id;
        },
        async initdata(){
            this.contentshow = false;
            var ret = await getUploadSuccessAttachmentNew({taskId:this.rowinfo});
            if(ret?.success){
                this.InitDataArrayOne = ret?.data?.initDataArrayOne;
                this.InitDataArrayTwo = ret?.data?.initDataArrayTwo;
            }
            this.contentshow = true;
             
        },
        whactclick(num){
            let _this = this;
            _this.num = num;
        },
         //判断是否改变
         getIsChange(){
            var ischange  = false;
            for(let num in this.InitDataArrayOne)
            { 
                var infos = this.$refs[this.InitDataArrayOne[num].refName][0]?.getChange();
                if(infos){
                    ischange =true;
                }
               
            }
            for(let num in this.InitDataArrayTwo)
            { 
                var infos = this.$refs[this.InitDataArrayTwo[num].refName][0]?.getChange();
                if(infos){
                    ischange =true;
                }
            }  
            return ischange;
        },
        getUploadInfo(){ 
            var retarray = [];
            for(let num in this.InitDataArrayOne)
            { 
                var infos = this.$refs[this.InitDataArrayOne[num].refName][0].getReturns();
                var uploadType = this.InitDataArrayOne[num].uploadType;
                var fileType = this.InitDataArrayOne[num].fileType;
                infos.data.forEach(function(item){
                    item.uploadType = uploadType
                    item.fileType = fileType
                    retarray.push(item);
                });
            }
            for(let num in this.InitDataArrayTwo)
            { 
                var infos = this.$refs[this.InitDataArrayTwo[num].refName][0].getReturns();
                var uploadType = this.InitDataArrayTwo[num].uploadType;
                var fileType = this.InitDataArrayTwo[num].fileType;
                infos.data.forEach(function(item){
                    item.uploadType = uploadType
                    item.fileType = fileType
                    retarray.push(item);
                });
            } 
            return retarray;
        },
        //提交上传的信息
        async sumbitUploadInfo()
         {
            var res =  await checkShootingTaskAction({taskid:this.rowinfo});
            if(!res?.success){
                return
            }
            if(!this.getIsChange()){
                this.$message({ message: '未发生改变，请勿提交', type: "error" });
                return;
            }
            //校验是否可以提交

            this.uploadinfos = this.getUploadInfo();
            this.pageLoading = true;
            this.tempoutlist = [];
            this.uploadinfos.forEach(element => { 
                this.tempoutlist.push(element); 
            });
            this.startIndex = this.tempoutlist.length;
           
            for (var i = 0; i < this.tempoutlist.length; i++) {
                var item = this.tempoutlist[i];
                this.atfterUplaodData = null;
                this.percentage = 0;
                this.startIndex = this.startIndex - 1;
                //判断是否需要上传  //只上传新加的文件
                if(item.filestaus == 0){
                    this.upmsgtxt = "正在上传："+ item.file.name 
                    this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                    await this.AjaxFile(item.file, 0, "");
                    if (this.atfterUplaodData != null) {
                        await this.afteruploadtempList(item, this.startIndex);
                    }
                }else{
                    this.upmsginfo = "正在处理："+item.fileName;
                    await this.afteruploadtempList(item, this.startIndex);
                }
                this.percentage = 0;
            }  
            this.pageLoading = false;
        },
        //startIndex == 0 判断全部上传完成，也代表文件的顺序，采用倒序排版
        async afteruploadtempList(item,startIndex){
            //进行业务处理
            var params = {
                ...item,
                shootingTaskId:this.rowinfo,
                OrderNum: startIndex
            }
            if(item.filestaus == 0){
                params.url = this.atfterUplaodData.url;
                params.relativePath = this.atfterUplaodData.relativePath;
            }
            var res =  await uploadSuccessAttachment(params);
            //上传到数据记录中
            if(res?.success)
            {
                for (let num in this.uploadinfos) {
                    if (this.uploadinfos[num].uid == item.uid) {
                        this.uploadinfos.splice(num, 1)
                    }
                }
            }
            if (startIndex == 0) {
                this.upmsginfo = "上传完成！";
                this.$message({ message: '上传完成！', type: "success" });
                if (this.uploadinfos.length > 0) 
                {
                    this.$message({ message: '存在操作失败信息请查看，截图保存', type: "error" });
                    this.dialogVisibleSyjout = true;
                    this.uploadtype = item.uploadType;
                }else{
                    // await this.reloadupfile(item.uploadType);
                    await this.initdata();
                }
            }
        },
         //切片上传
        async AjaxFile(file, i, batchnumber) {
            var name = file.name; //文件名
            var size = file.size; //总大小shardSize = 2 * 1024 * 1024,
            var shardSize = 2 * 1024 * 1024;
            var shardCount = Math.ceil(size / shardSize); //总片数
            if (i >= shardCount) {
                return;
            }
            //计算每一片的起始与结束位置
            var start = i * shardSize;
            var end = Math.min(size, start + shardSize);
            //构造一个表单，FormData是HTML5新增的
            i = i + 1;
            var form = new FormData();
            form.append("data", file.slice(start, end)); //slice方法用于切出文件的一部分
            form.append("batchnumber", batchnumber);
            form.append("fileName", name);
            form.append("total", shardCount); //总片数
            form.append("index", i); //当前是第几片
            const res = await xMTVideoUploadBlockAsync(form);
            if (res?.success) {
                this.percentage = (i * 100 / shardCount).toFixed(2);
                this.upmsginfo =this.upmsgtxt +"   " +  this.percentage +"%";
                if (i == shardCount) {
                    this.atfterUplaodData = res.data;
                } else {
                    await this.AjaxFile(file, i, res.data);
                }
            } else {
                this.$message({ message: res?.msg, type: "warning" });
            }
        }, 
    },
};
</script>

<style lang="scss" scoped>
.rowstyle{
    margin: 0;
    height: 100%;
}
.flexrow{
    display: flex;
    flex-direction: row;
    width: auto;
    // border: 1px solid #eee;
}
.aligncenter{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    height: auto;
    flex-direction: column;
    padding: 10px;
}
.content{
    // margin-top: 30px;
    // border: 1px solid #eee;
    // background-color: #eee;
    height: 90%;
    width: 100%;
    // overflow-y: auto;
}
.border{
    border: 2px solid #409EFF;
}
.flexx{
    flex: 1;
}
.marleftrig{
    margin: 0;
}
.backcolor{
    background-color: white;
}
.overhiddle{
    display: flex; 
    flex-direction: row; 
    // padding: 0 20px;
    flex-wrap: wrap;
    overflow-x: hidden;
}
</style>