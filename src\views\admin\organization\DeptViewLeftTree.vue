<template>
    <vxe-table ref="xTreeRef" height="100%" size="mini" 
    show-overflow border="inner" 
    :column-config="{ resizable: true }"
        highlight-hover-row highlight-current-row :show-header="false"
        :row-config="{keyField: 'dept_id',isCurrent:true}"
        :tree-config="{ transform: true,  rowField: 'dept_id', parentField: 'parent_id',       
        expandRowKeys:[-99999,-999,-1] //,597959144,599262021,847451347,851106774,854910527
    }" 
        :data="deptList"
        @current-change="deptSelect">
        <vxe-column field="dept_name" tree-node></vxe-column>
    </vxe-table>
</template>
<script>

import { AllDeptViewContainRootList } from '@/api/admin/deptuser'

export default {
    name: 'DeptViewLeftTree',
    components: {},
    data() {
        return {
            deptList: [],
        }
    },
    async mounted() {
        await this.initDept();
    },
    methods: {
        async initDept() {
            const data = await AllDeptViewContainRootList()
            if (data && data.success) {
                this.deptList = data.data;
            }
        },
        deptSelect({ row }) {
            this.$emit("selected", row);
        }
    }
}
</script>
  

<style scoped lang="scss" ></style>