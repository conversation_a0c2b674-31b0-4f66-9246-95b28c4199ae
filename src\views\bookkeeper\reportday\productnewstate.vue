<template>
    <my-container>
        <el-dialog width="30%" style="height: 80%" :visible.sync="isshowprosimstate" v-dialogDrag>
            <el-tabs>
                <el-tab-pane label="选择状态" name="first">
                <el-table
                    :data="
                    prosimstatelist.slice(
                        (gpcurrentPage - 1) * gppagesize,
                        gpcurrentPage * gppagesize
                    )" style="width: 100%">
                    <el-table-column prop="state" label="状态" width="180">
                    </el-table-column>

                    <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                        @click="selectstate(scope.row)"
                        :type="scope.row.selecttype"
                        :icon="scope.row.selectedicon">
                        选择
                        </el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="gpcurrentPage"
                    :page-sizes="[5]"
                    :page-size="gppagesize"
                    layout=" prev, pager, next"
                    :total="parseInt(prosimstatelist.length)"
                >
                </el-pagination>
                </el-tab-pane>
                <el-tab-pane label="状态管理" name="third">
                <el-input
                    v-model.trim="newstatename"
                    placeholder="输入新的状态"
                    style="width: 130px"
                />
                <el-button @click="addstatename" type="text" size="small">添加</el-button>
                <el-table
                    :data="
                    prosimstatelist.slice(
                        (gpcurrentPage - 1) * gppagesize,
                        gpcurrentPage * gppagesize)"
                    style="width: 100%"
                >
                    <el-table-column prop="state" label="状态名称" width="180">
                    </el-table-column>

                    <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                        @click="handleDeleteState(scope.row)"
                        type="text"
                        size="small"
                        >删除</el-button
                        >
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="gpcurrentPage"
                    :page-sizes="[5]"
                    :page-size="gppagesize"
                    layout=" prev, pager, next"
                    :total="parseInt(prosimstatelist.length)"
                >
                </el-pagination>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>

        <el-dialog
            title="提示"
            :visible.sync="deletestatedialogVisible"
            width="30%">
            <span>确认删除状态名吗？此操作会从所有编码中删除这个状态？</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="deletestatedialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="deleteStateName">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog width="30%" style="height: 80%" :visible.sync="isshowcusgroup" v-dialogDrag>
            <el-tabs>
                <el-tab-pane label="选择分组" name="first">
                <el-table
                    :data="
                    cusgroups.slice(
                        (gpcurrentPage - 1) * gppagesize,
                        gpcurrentPage * gppagesize )"
                    style="width: 100%">
                    <el-table-column prop="groupname" label="客服组名称" width="180">
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                        @click="selectgroup(scope.row)"
                        :type="scope.row.selecttype"
                        :icon="scope.row.selectedicon">
                        选择
                        </el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="gpcurrentPage"
                    :page-sizes="[5]"
                    :page-size="gppagesize"
                    layout=" prev, pager, next"
                    :total="parseInt(cusgroups.length)">
                </el-pagination>
                </el-tab-pane>
                <el-tab-pane label="组管理" name="third">
                <el-input
                    v-model.trim="newgroupname"
                    placeholder="输入新的组名"
                    style="width: 130px"/>
                <el-button @click="addgroupname" type="text" size="small">添加</el-button>

                <el-table
                    :data="
                    cusgroups.slice(
                        (gpcurrentPage - 1) * gppagesize,
                        gpcurrentPage * gppagesize)"
                    style="width: 100%">
                    <el-table-column prop="groupname" label="客服组名称" width="180">
                    </el-table-column>
                    <!-- <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                        @click="handleDeleteGroup(scope.row)"
                        type="text"
                        size="small"
                        >删除</el-button>
                    </template>
                    </el-table-column> -->
                </el-table>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="gpcurrentPage"
                    :page-sizes="[5]"
                    :page-size="gppagesize"
                    layout=" prev, pager, next"
                    :total="parseInt(cusgroups.length)">
                </el-pagination>
                </el-tab-pane>
            </el-tabs>
            </el-dialog>

            <el-dialog
            title="提示"
            :visible.sync="deletegroupdialogVisible"
            width="30%">
            <span
                >确认删除分组名称嘛？此操作会从所有编码中删除这个组名，并且将同步删除店铺中的同名分组</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="deletegroupdialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="deleteGroupName">确 定</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import {getProductStateName, addProductStateName, addProductState, deleteProductState,
addProcodeCustomer} from '@/api/operatemanage/base/product'
import {getcusgroups, addcusgroup, deletecusgroup,} from "@/api/customerservice/customergroup";

const getDate = formatTime(new Date(), "YYYY-MM-DD HH-mm:ss");
export default {
    name: 'YunhanAdminProductnewecharts',
    components : {MyContainer, MyConfirmButton},
    data() {
        return {
            that:this,
            filter:{
                proCode:null,
            },
            prosimstatelist:[],
            list:[],
            cusgroups: [],
            groupNameList: [],
            deletegroupnamevalue: "",
            bechangegoodscode: "",
            isshowcusgroup: false,
            newgroupname: "",
            newstatename: "",
            gpcurrentPage: 1,
            gppagesize: 5,
            isshowprosimstate:false,
            deletegroupdialogVisible: false,
            deletestatedialogVisible: false,
        };
    },

    async mounted() {
       await this.getprosimstatelist(); 
       await this.initcustomerserviceGroups();
    },

    methods: {
        async OnSearch(e, list){
            this.filter.proCode = e
            this.list=list
            await this.setprostate(this.filter.proCode)
        },
        //获取状态信息
        async getprosimstatelist(){
            var res = await getProductStateName();
            if (res?.code){
                this.prosimstatelist = res.data.map(function (item) {
                var ob = new Object();
                ob.state = item;
                ob.isshow = false;
                ob.selectedicon = "";
                ob.selecttype = "fail";
                return ob;
                })
            }
        },
        //设置系列状态
        async setprostate(e){

        this.bechangegoodscode = e;
        var selectedListIndex = 0;
        for (var listindex in this.list) {
            if (this.list[listindex].proCode == e) {
            selectedListIndex = listindex;
            }
        }

        for (var i = 0; i < this.prosimstatelist.length; i++) {
            this.prosimstatelist[i].selecttype = "fail";
            this.prosimstatelist[i].selectedicon = "";
        }
        if (this.list[selectedListIndex].newPattern != "暂无状态") {

            var states = this.list[selectedListIndex].newPattern.split(',')
            for (var s in states){
            for (var i = 0; i < this.prosimstatelist.length; i++){
                if (this.prosimstatelist[i].state == states[s]){
                this.prosimstatelist[i].selecttype = "success";
                this.prosimstatelist[i].selectedicon = "el-icon-check";
                }
            }
            } 
        } else {
            for (var i = 0; i < this.prosimstatelist.length; i++) {
            this.prosimstatelist[i].selecttype = "fail";
            this.prosimstatelist[i].selectedicon = "";
            }
        }
        this.isshowprosimstate = true
        },
        //状态选择
        async selectstate(e) {
            for (var i = 0; i < this.prosimstatelist.length; i++) {
                if (this.prosimstatelist[i].state == e.state) {
                if (this.prosimstatelist[i].selecttype == "success") {
                    this.prosimstatelist[i].selecttype = "fail";
                    this.prosimstatelist[i].selectedicon = "";
                } else {
                    this.prosimstatelist[i].selecttype = "success";
                    this.prosimstatelist[i].selectedicon = "el-icon-check";
                }
                }
            }
            var choosedstatename = '';
            for (var s in this.prosimstatelist) {
                if (this.prosimstatelist[s].selecttype == "success")
                choosedstatename += this.prosimstatelist[s].state + ","
            }
            var params = {
                statename : choosedstatename,
                proCode : this.bechangegoodscode
            }
            addProductStateName(params)
            .then((res) => {
                if (res.code == 1){
                for (var listindex in this.list) {
                    if (this.list[listindex].proCode == this.bechangegoodscode) {
                    if (choosedstatename == "") choosedstatename = "暂无状态";
                    this.list[listindex].newPattern = choosedstatename;
                    }
                }
                this.$emit('changelist',this.list)
                }
            });
        },
        //点击添加状态
        async addstatename(e){
        var res = await addProductState({statename : this.newstatename})
            if (res.code){
                var ob = new Object();
                ob.state = this.newstatename;
                ob.isshow = false;
                ob.selectedicon = "";
                ob.selecttype = "fail";

                this.prosimstatelist.push(ob);
                this.newstatename = "";
            }
        },
        //删除状态
        async handleDeleteState(e){
            this.deletestatedialogVisible = true;
            this.deletestatenamevalue = e.state;
        },
        async deleteStateName(e) {
            var that = this;
            //updatecustomerservicergroup
            deleteProductState({ statename: this.deletestatenamevalue }).then((res) => {
                
                that.deletestatedialogVisible = false;
                that.getprosimstatelist();
            });
        },
        async initcustomerserviceGroups() {
            var g = await getcusgroups({});

            this.cusgroups = g.data.list.map(function (item) {
                var ob = new Object();
                ob.groupname = item;
                ob.isshow = false;
                ob.selectedicon = "";
                ob.selecttype = "fail";
                return ob;
            });
        },
        //点击新加分组名称
        async addgroupname(e) {
            await addcusgroup({ groupname: this.newgroupname });
            var ob = new Object();
            ob.groupname = this.newgroupname;
            ob.isshow = false;
            ob.selectedicon = "";
            ob.selecttype = "fail";

            this.cusgroups.push(ob);
            this.newgroupname = "";
        },
        //弹出客服组选择管理框
        async setcustomergroup(e,list) {
            this.filter.proCode = e
            this.list=list
            this.bechangegoodscode = e;
            var selectedListIndex = 0;
            for (var listindex in this.list) {
                if (this.list[listindex].proCode == e) {
                selectedListIndex = listindex;
                }
            }

            for (var i = 0; i < this.cusgroups.length; i++) {
                this.cusgroups[i].selecttype = "fail";
                this.cusgroups[i].selectedicon = "";
            }

            if (this.list[selectedListIndex].customerGroups != "空") {
            
                var groups = this.list[selectedListIndex].customerGroups.split(",");
                for (var g in groups) {
                for (var i = 0; i < this.cusgroups.length; i++) {
                    if (this.cusgroups[i].groupname == groups[g]) {
                    this.cusgroups[i].selecttype = "success";
                    this.cusgroups[i].selectedicon = "el-icon-check";
                    }
                }
                }
            } else {
                for (var i = 0; i < this.cusgroups.length; i++) {
                this.cusgroups[i].selecttype = "fail";
                this.cusgroups[i].selectedicon = "";
                }
            }
            this.isshowcusgroup = true;
        },
        //点击选择按钮，选择或取消选择分组
        async selectgroup(e) {
            var code = "";
            var cusgroups = "";
            for (var i = 0; i < this.cusgroups.length; i++) {
                if (this.cusgroups[i].groupname == e.groupname) {
                if (this.cusgroups[i].selecttype == "success") {
                    this.cusgroups[i].selecttype = "fail";
                    this.cusgroups[i].selectedicon = "";
                } else {
                    this.cusgroups[i].selecttype = "success";
                    this.cusgroups[i].selectedicon = "el-icon-check";
                }
                }
            }
            var choosedgroupname = "";
            for (var g in this.cusgroups) {
                if (this.cusgroups[g].selecttype == "success")
                choosedgroupname += this.cusgroups[g].groupname + ",";
            }
            var params = {
                statename : choosedgroupname,
                proCode : this.bechangegoodscode
            }
            addProcodeCustomer({
                statename : choosedgroupname,
                proCode : this.bechangegoodscode
            }).then((res) => {
                if (res.code == 1) {
                for (var listindex in this.list) {
                    if (this.list[listindex].proCode == this.bechangegoodscode) {
                    if (choosedgroupname == "") choosedgroupname = "空";
                    this.list[listindex].customerGroups = choosedgroupname;
                
                    }
                }
                this.$emit('changelist',this.list)
            }
        });
        },
        handleDeleteGroup(e) {
            this.deletegroupdialogVisible = true;
            this.deletegroupnamevalue = e.groupname;
            },
        async deleteGroupName(e) {
            var that = this;
            //updatecustomerservicergroup
            deletecusgroup({ groupname: this.deletegroupnamevalue }).then((res) => {
                //that.cusgroups.remove(that.deletegroupnamevalue);
                that.deletegroupdialogVisible = false;
                that.initcustomerserviceGroups();
                deleteCustomerServicerGroup({ groupname: this.deletegroupnamevalue });
                this.$emit('onSearch')
            });
        },
        async handleSizeChange(val) {
            this.gppagesize = val;
        },
        async handleCurrentChange(val) {
            this.gpcurrentPage = val;
        },
    },
};
</script>

<style lang="scss" scoped>

</style>