<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent label-position="right" label-width="120px">
                <el-form-item label="平台">
                    <el-select  v-model="filter.platform"  placeholder="请选择"  :clearable="true" :collapse-tags="true"
                    filterable  @change="onchangeplatformMain"  style="width: 100px" >
                    <el-option     v-for="item in platformList"  :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                </el-form-item>
                <el-form-item label="店铺:">
                <el-select  v-model="filter.shopCode"  style="width: 150px" placeholder="请选择" :clearable="true" :collapse-tags="true"  filterable  >
                    <el-option  v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
                </el-select>
                </el-form-item>
                <el-form-item label="运营组:">
                    <el-select  v-model="filter.groupId"  style="width: 110px" placeholder="请选择" :clearable="true" :collapse-tags="true"  filterable >
                        <el-option  v-for="item in groupList"  :key="item.key"  :label="item.value" :value="item.key" />
                    </el-select>
                </el-form-item>
                <el-form-item label="系列编码:">
                    <el-input  v-model.trim="filter.goodsCode"   placeholder="系列编码"  style="width: 130px" maxlength="50"  />
                </el-form-item>
                <el-form-item label="产品ID:">
                    <el-input v-model.trim="filter.proCode"  placeholder="产品ID" style="width: 130px"   maxlength="30"   />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>

        <ces-table ref="table" :that='that' :isSelection='true' :hasexpand='true'
             @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles'
              :isSelectColumn="false" :loading="listLoading"
              @select='selectchange'
              >
        </ces-table>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
         <el-dialog :visible.sync="detailPro.visible"   width="20%" v-dialogDrag height="700"
         @close="detailPro.pagerStyle={};"  append-to-body>


            <ces-table ref="tableDetailPro" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchangeDetailPro'
                :tableData='detailPro.list'    :tableCols='detailPro.tableCols' :isSelection="false" :summaryarry="summaryarryDetailPro"
                :tableHandles='detailPro.tableHandles' :isSelectColumn="false" @select="selsChangeDetailPro" style="height:360px;"
                :loading="detailPro.listLoading">
            </ces-table>

            <my-pagination
                ref="pagerDetailPro"
                :total="detailPro.total"
                :checked-count="detailPro.sels.length"
                @get-page="getlistDetailPro" :style="detailPro.pagerStyle"
                />
        </el-dialog>

    </my-container>
</template>

<script>
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import { getList as getshopList } from '@/api/operatemanage/base/shop'
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import { formatPlatform, formatLinkProCode } from "@/utils/tools";
    import { getGroupKeyValue } from "@/api/operatemanage/base/product";
    import { rulePlatform } from "@/utils/formruletools";
    import {
        pageProCodeSimilarityDetailGood,
        getProCodeDetailAsync,
        getProCodeSimilarityDetailAsync
    } from "@/api/order/procodesimilarity"
    const tableCols = [
        { istrue: true, prop: 'goodsCode', label: '系列编码', width: '280' },
        { istrue: true, prop: 'platform', label: '平台', width: '100', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
        { istrue: true, prop: 'shopName', label: '店铺', width: '280' },
        { istrue:true  ,prop:'groupId',label:'运营组', width:'90',sortable:'custom',formatter:(row)=>row.groupName},
        { istrue: true, prop: 'proCode', label: '产品ID', width: '125', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },

         {istrue:true,label:"查看",width:"100",type:'button',btnList:[
          {label:"产品编码",handle:(that,row)=>that.showDetailPro(row)}
       ]},
     ];
     const tableColsProGoods=[
       {istrue:true,prop:'goodsCode',label:'商品编码', width:'130',sortable:'custom'},
     ];
    const tableHandles1 = [

    ];

    const startDate = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endDate = formatTime(new Date(), "YYYY-MM-DD");

    export default {
        name: 'Roles',
        components: { cesTable, MyContainer, MyConfirmButton },
        props: {
            isHistory: false,
        },
        data () {
            return {
                that: this,
                filter: {
                    platform: null,
                    shopCode: "",
                    proCode: null,
                    goodsCode: null,
                    groupId: null,
                    similarity: 0,
                    days: null,
                    profit2UnZero:null,
                    profit3UnZero:null,
                    state: null,
                    startDate: null,
                    endDate: null,
                    timerange: [startDate, endDate],
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "salesQty", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                platformList: [],
                shopList: [],
                dialogVisible: false,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                addFormVisible: false,
                addLoading: false,
                deleteLoading: false,
                uploadLoading: false,
                fileList: [],
                pickerOptions: {
                    disabledDate (time) {
                        return time.getTime() > Date.now();
                    }
                },
                selrows: [],
                detailPro:{
                    list: [],
                    pager:{OrderBy:"isSame",IsAsc:false},
                    tableCols:tableColsProGoods,
                    tableHandles:[],
                    total: 0,
                    sels: [],
                    listLoading: false,
                    visible:false,
                    filter:{
                        detailParentId:null,
                        goodsCode:null
                    },
                    selRow:{},
                    summaryarry:{},
                    pagerStyle:{
                        "margin-top":"33px"
                    }
                },
            }
        },
        async mounted () {
            await this.setPlatform();
            await this.setGroupSelect();
            await this.getlist();
        },
        methods: {
            selectchange:function(rows,row) {
                this.selrows=rows;
            },
            async getchoicelistOnly(){
                if(!this.selrows||this.selrows.length==0) {

                    this.$message({message: "请选择一条数据，",type: "warning",});
                    return;
                }
                if(!this.selrows||this.selrows.length >1) {

                    this.$message({message: "只能选择一条数据",type: "warning",});
                    return;
                }
                //goodsCode
                var row =this.selrows[0];
                this.pageLoading =true;
                var ret = await getProCodeDetailAsync({seriesCode:row.goodsCode});
                if(ret?.success){
                    row.productCode = ret.data ;
                    this.pageLoading =false;
                    return row;
                }else{
                    this.$message({message: "获取产品Id失败",type: "warning",});
                    this.pageLoading =false;
                    return null;
                }

            },
            //设置平台下拉
            async setPlatform () {
                var pfrule = await rulePlatform();
                this.platformList = pfrule.options;
                if (this.platformList && this.platformList.length)
                    this.platformList.push({ label: '未知', value: 0 });
            },
            //设置店铺下拉
            async onchangeplatformMain (val) {
                const res = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
                this.shopList = res.data.list || [];
                this.shopList.push({ shopCode: "{线下}", shopName: "{线下}" });
                this.filter.shopCode = "";
                await this.onSearch();
            },
        //设置运营组下拉
        async setGroupSelect() {
            const res = await getGroupKeyValue({});
            this.groupList = res.data;
            },
            //获取查询条件
            getCondition () {
                this.filter.startDate = null;
                this.filter.endDate = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }

                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }

                this.listLoading = true
                var  res = await getProCodeSimilarityDetailAsync(params);
                this.listLoading = false
                if (!res?.success) {
                    return
                }
                this.total = res.data.data.total;
                const data = res.data.data.list;
                this.summaryarry = [];
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            ///==产品明细 Start==========================================
            showDetailPro(row){
                this.detailPro.visible=true;
                this.detailPro.selRow=row;
                this.detailPro.filter.detailParentId=row.proCodeChild;
                setTimeout(async () => {
                    await this.onSearchDetailPro();
                }, 500);
            },
            selsChangeDetailPro: function(sels) {
                this.detailPro.sels = sels;
            },
            async sortchangeDetailPro(column){
                if(!column.order)
                    this.detailPro.pager={};
                else{
                    var orderBy =column.prop;
                    this.detailPro.pager={OrderBy:orderBy,IsAsc:column.order.indexOf("descending")==-1?true:false};
                }
                await this.onSearchDetailPro();
            },
            //获取查询条件
            getConditionDetailPro(){
            var pager = this.$refs.pagerDetailPro.getPager();
            var page  = this.detailPro.pager;
            const params = {
                ...pager,
                ...page,
                ...this.detailPro.filter
            }

            return params;
            },
            //查询第一页
            async onSearchDetailPro() {
                this.$refs.pagerDetailPro.setPage(1)
                await this.getlistDetailPro()
            },
            //分页查询
            async getlistDetailPro() {
                var params=this.getConditionDetailPro();
                if(params===false){
                        return;
                }
                this.detailPro.listLoading = true;
                var res= await pageProCodeSimilarityDetailGood(params);
                this.detailPro.listLoading = false;
                if (!res?.success) {
                    return
                }
                this.detailPro.total = res.data.total;
                const data = res.data.list||[];
                data.forEach(d => {
                    d._loading = false
                })
                this.detailPro.list = data;
                this.detailPro.summaryarry =res.data.summary;
            },
            showNextPro(){
                if(this.list&&this.list.length>0){
                    var nextRow=this.list[0];
                    var findCur=false;
                    this.list.forEach(item => {
                    if(findCur){
                        findCur=false;
                        nextRow=item;
                    }
                    if(item.id==this.detailPro.selRow.id){
                        findCur=true;
                    }
                    });
                    this.detailPro.selRow=nextRow;
                    this.showDetailPro(nextRow);
                }
            },

            ///==产品明细 End  ==========================================

        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
</style>
