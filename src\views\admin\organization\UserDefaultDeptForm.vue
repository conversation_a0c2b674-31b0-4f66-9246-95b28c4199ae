<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" ref="form" label-width="140px" label-position="right" >  
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="用户：">
                            {{ form.userName }}
                        </el-form-item>
                    </el-col>                   
                </el-row>                 
                <el-row>     
                    
                    <el-col :span="24">
                        <el-form-item label="默认部门：" prop="deptFullName" >
                              {{ form.deptFullName }}
                        </el-form-item>
                    </el-col>   
                </el-row>                 
                <el-row>  
                    <el-col :span="24">
                        <el-form-item label="新默认部门：" prop="newDeptId">
                              <el-select v-model="form.newDeptId"  style="width: 300px;">
                                    <el-option v-for="(item,index) in deptList" :label="item.label" :value="item.value" :key="item.value"></el-option>
                              </el-select>
                        </el-form-item>
                    </el-col>   
                </el-row>   
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="mode<3 " type="primary" @click="onSave(true)">保存</el-button>   
                </el-col>
            </el-row>
        </template>

    </my-container>
</template>
<script>  

    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";

    import { SaveUserDefaultDeptId } from '@/api/admin/deptuser'

    export default {
        name: "UserDefaultDeptForm",
        components: { MyContainer, MyConfirmButton,  },
        data() {
            return {              
                that: this,
                mode:3,
                userName:'',
                form: {
                    ddUserId:'',
                    userName:'',
                    deptFullName:'',
                    deptDict:'',
                    defaultDeptId:0,
                    newDeptId:-1
                },            
                deptList:[],
                pageLoading: false,             
                formEditMode: true,//是否编辑模式     
            };
        },
        async mounted() {          
        },
        computed: {   
        },
        methods: {             
        
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({deptDict,mode,defaultDeptId,userName,ddUserId,deptFullName}) { 
                
                this.deptList=[... Object.entries({...deptDict}).map(([value, label]) => ({
                    label,
                    value: Number(value)
                }))] ;
              
                let self=this;    
                self.pageLoading = true;
                                

            
                  
                    self.formEditMode = mode!=3;
                    self.mode = mode;     
                    self.form.ddUserId=ddUserId;
                    self.form.userName=userName;
                    self.form.deptFullName=deptFullName;
                    self.form.newDeptId=defaultDeptId;
                    console.log(this.deptList);
                
                    self.pageLoading = false;
            
             
            },
            async save() {
                this.pageLoading = true;
                
                let saveData = { ...this.form };   
                try {
                    await this.$refs["form"].validate();
                } catch (error) {
                    this.pageLoading = false;
                    return false;
                } 

                let rlt = await SaveUserDefaultDeptId({ddUserId:this.form.ddUserId,newDeptId:this.form.newDeptId});
                if (rlt && rlt.success) {
                    this.$message.success('保存成功！');           
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
