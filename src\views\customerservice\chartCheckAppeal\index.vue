<template>
    <my-container>
        <el-tabs v-model="activeName" style="height: calc(100% - 40px);" @tab-click="handleClick">

            <el-tab-pane label="客服聊天核查申诉" name="first" style="height: 100%;">
                <ChartAppeal v-if="activeName === 'first'" ></ChartAppeal>
            </el-tab-pane>


            <el-tab-pane label="申诉审核" name="second" style="height: 100%;">
                <ReviewAppeal v-if="activeName === 'second'" ></ReviewAppeal>
            </el-tab-pane>

        </el-tabs> 
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import ChartAppeal from "@/views/customerservice/chartCheckAppeal/ChartAppeal.vue"
import ReviewAppeal from "@/views/customerservice/chartCheckAppeal/ReviewAppeal.vue"

export default {
    name: "chartCheck",
    components: {
        MyContainer,
        ChartAppeal,
        ReviewAppeal,


    },

    data() {
            return {
            activeName: 'first',
    
        }
    },

}
</script>

<style scoped lang="scss"></style>
