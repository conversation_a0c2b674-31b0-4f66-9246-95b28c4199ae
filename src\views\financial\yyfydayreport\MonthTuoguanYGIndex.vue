<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
        <el-tab-pane   label="推广明细报表" name="tab1" style="height: 100%;">
    <MonthTuoguanYG :filter="Filter" ref="MonthTuoguanYG" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane   label="推广佣金" name="tab2" style="height: 100%;">
    <MonthTuoguanYG_Tg :filter="Filter" ref="MonthTuoguanYG_Tg" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane   label="日预算" name="tab3" style="height: 100%;">
    <MonthTuoguanYG_Day :filter="Filter" ref="MonthTuoguanYG_Day" style="height: 100%;"/>
</el-tab-pane>
<el-tab-pane   label="周预算" name="tab4" style="height: 100%;">
    <MonthTuoguanYG_Week :filter="Filter" ref="MonthTuoguanYG_Week" style="height: 100%;"/>
</el-tab-pane>
    </el-tabs>
    </my-container >
   </template>
  <script>
  import MyContainer from "@/components/my-container";

  import MonthTuoguanYG from '@/views/financial/yyfydayreport/MonthTuoguanYG'
  import MonthTuoguanYG_Tg from '@/views/financial/yyfydayreport/MonthTuoguanYG_Tg'
  import MonthTuoguanYG_Day from '@/views/financial/yyfydayreport/MonthTuoguanYG_Day'
  import MonthTuoguanYG_Week from '@/views/financial/yyfydayreport/MonthTuoguanYG_Week'


  import checkPermission from '@/utils/permission'
  export default {
    name: "Users",
    components: { MyContainer,MonthTuoguanYG,MonthTuoguanYG_Tg,MonthTuoguanYG_Day,MonthTuoguanYG_Week},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {
    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1')
    this.$refs.TGCztc.onSearch();

  }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
