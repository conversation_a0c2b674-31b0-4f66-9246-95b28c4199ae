<template>
  <my-container>
    <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane label="销售后台明细" name="first1" style="height: 98%;">
        <sellBackgroundDetail />
      </el-tab-pane>
      <el-tab-pane label="结算数据" name="first2" :lazy="true" style="height: 98%;">
        <settlementData />
      </el-tab-pane>
      <el-tab-pane label="退货补运费" name="first3" :lazy="true" style="height: 98%;">
        <returnFreight />
      </el-tab-pane>
      <el-tab-pane label="违规赔付" name="first4" :lazy="true" style="height: 98%;">
        <violationsCompensate />
      </el-tab-pane>
      <el-tab-pane label="资金明细" name="first5" :lazy="true" style="height: 98%;">
        <detailsFund />
      </el-tab-pane>
      <el-tab-pane label="保证金" name="first6" :lazy="true" style="height: 98%;">
        <Bond />
      </el-tab-pane>
    </el-tabs>
  </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import settlementData from './settlementData.vue'
import returnFreight from './returnFreight.vue'
import violationsCompensate from './violationsCompensate.vue'
import detailsFund from './detailsFund.vue'
import sellBackgroundDetail from './sellBackgroundDetail.vue'
import Bond from './bond.vue'

export default {
  name: "settlementKWaiShopIndex",
  components: { MyContainer, settlementData, returnFreight, violationsCompensate, detailsFund, sellBackgroundDetail, Bond },
  data() {
    return {
      activeName: 'first1'
    };
  },
};
</script>
<style lang="scss" scoped></style>
