<template>
    <my-container v-loading="listLoading">
        <el-form :model="addForm" ref="addForm" label-width="100px" :rules="addFormRules" :disabled="islook" >
            <div class="bzjzcjrw">
                <div class="bt" style="border: 0px;" v-if="heard">
                    <span style="float: left">编辑信息</span>
                </div>
                <div class="bt showdom" style="height: 70px;line-height: 70px;" v-if="heard&&addForm!={}">
                    <span style="float: left">{{ addForm.goodsCode }}  |  {{ addForm.goodsName }}</span>
                    <!-- <div class="xh" style="width: 150px;float: left;">{{ addForm.goodsCode }}</div>
                    <div class="mc" style="height: 100px;float: left;">|</div>
                    <div class="mc" style="width: 150px;float: left;">{{ addForm.goodsName }}</div> -->
                    <div class="icon" style="float: right;width: 70px;height: 50px;">
                        <el-button type="primary" v-throttle @click="onSubmit">保存</el-button>
                    </div>
                </div>
                <div v-else style="display: flex; flex-direction: row;">
                    <div class="bt" style="border: 0px; margin-right: auto;">
                        <span>编辑信息</span>
                    </div>
                    <div class="icon" style="width: 160px;height: 50px; margin-top: -15px; margin-left: auto; font-size: 14px;">
                        已选编码数：{{ addForm.goodsCodeList.length }}
                    </div>
                </div>
                <div style="padding-bottom: 20px;"></div>
                <div class="flowto" style="overflow-y: auto;" :style="heard?{height:'70vh'}:{}">

                   <div class="bzccjlx">
                        <div class="lxwz">包装</div>
                        <div class="lxwz2">
                            <el-form-item prop="packingCode" label=" " label-width="12px">
                                <el-select style="width:70%" @change="codetoname1"  :value="addForm.packingCode"  clearable :collapse-tags="true"
                                    filterable>
                                    <el-option v-for="item in brandList" :key="item.setId" :label="item.sceneCode"
                                        :value="item.setId" />
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="bzccjlx">
                        <div class="lxwz">合格证</div>
                        <div class="lxwz2">
                            <el-form-item prop="hasCertificate" label=" " label-width="12px">
                                <el-select filterable v-model="addForm.hasCertificate" collapse-tags clearable placeholder="有无合格证"
                                  style="width: 355px">
                                  <el-option label="有" :value="true" />
                                  <el-option label="无" :value="false" />
                                </el-select>
                            </el-form-item>
                        </div>
                    </div>

                    <div class="bzccjlx1">
                        <div class="lxwz2">
                          <el-button type="primary" style="width:520px" v-model="productList" @click="addallremark">新增合格证信息</el-button>
                        </div>
                    </div>
                  <div class="bzccjlx2" v-for="(item,i) in addForm.productDetialList" :key="i">
                    <!-- <div class="bzccjlx">
                        <div class="lxwz">品牌</div>
                        <div class="lxwz2">
                            <el-form-item prop="brandCode" label=" " label-width="12px">
                                <el-select key="1" style="width:70%" @change="codetoname1"  :value="addForm.brandCode" :clearable="true" :collapse-tags="true"
                                    filterable>
                                    <el-option v-for="item in brandList" :key="item.setId" :label="item.sceneCode"
                                        :value="item.setId" />
                                </el-select>
                            </el-form-item>
                        </div>
                    </div> -->
                    <div class="bzccjlx" v-show="false">
                        <div class="lxwz">id</div>
                        <div class="lxwz2">
                          <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.productId" :maxlength="200"></el-input>
                        </div>
                    </div>

                    <div class="bzccjlx">
                        <div class="lxwz">品牌</div>
                        <div class="lxwz2">
                            <el-form-item  label=" " label-width="12px">
                              <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.brandCode" :maxlength="200"></el-input>
                            </el-form-item>

                            <!-- <el-form-item prop="packingCode" label=" " label-width="12px">
                                <el-select style="width:70%" @change="codetoname2($event,i)"  :value="item.brandCode" :clearable="true" :collapse-tags="true"
                                    filterable>
                                    <el-option v-for="item in brandList" :key="item.setId" :label="item.sceneCode"
                                        :value="item.setId" />
                                </el-select>
                            </el-form-item> -->
                        </div>
                    </div>

                    <div class="bzccjlx">
                        <div class="lxwz">执行标准</div>
                        <div class="lxwz2">
                            <el-form-item  label=" " label-width="12px">
                                <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.execStandard" :maxlength="200"></el-input>
                            </el-form-item>
                        </div>
                    </div>

                    <div class="bzccjlx">
                        <div class="lxwz">生产商</div>
                        <div class="lxwz2">
                            <el-form-item  label=" " label-width="12px">
                                <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.producer" :maxlength="200"></el-input>
                            </el-form-item>
                        </div>
                    </div>

                    <div class="bzccjlx">
                        <div class="lxwz">生产地址</div>
                        <div class="lxwz2">
                            <el-form-item  label=" " label-width="12px">
                                <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.productAddress" :maxlength="200"></el-input>
                            </el-form-item>
                        </div>
                    </div>

                    <div class="bzccjlx">
                        <div class="lxwz">销售方</div>
                        <div class="lxwz2">
                            <el-form-item  label=" " label-width="12px">
                                <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.seller" :maxlength="200"></el-input>
                            </el-form-item>
                        </div>
                    </div>

                    <div class="bzccjlx">
                        <div class="lxwz">销售方地址</div>
                        <div class="lxwz2">
                            <el-form-item  label=" " label-width="12px">
                                <el-input :step="1" controls-position="right" style="width:70%" :clearable="true"
                                    v-model.trim="item.sellerAddress" :maxlength="200"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="bzccjlx" v-for="(itemm,index) in item.customizeList" :key="index">
                       <el-tooltip effect="dark" :content="itemm.title" placement="top-start" >
                        <div class="lxwz overtitle">{{ itemm.title }}</div>
                        </el-tooltip>
                        <div class="lxwz2">
                          <div >
                            <el-form-item label=" " label-width="12px">
                                <el-input :step="1" controls-position="right" style="width:60%" :clearable="true"
                                    v-model="itemm.customize" :maxlength="200" :key="index"></el-input>
                                <el-button type="danger" icon="el-icon-delete" @click="onDelMarks(index,i)"></el-button>
                            </el-form-item>
                          </div>
                        </div>
                    </div>

                    <div>
                      <el-dialog
                          title="提示"
                          :visible.sync="dialogVisiblew"
                          width="30%"
                          append-to-body
                          v-dialogDrag
                        >
                          <div class="bzccjlx">
                            <!-- <div class="lxwz">自定义名称</div> -->
                            <div class="lxwz2">
                                <el-form-item  label=" " label-width="12px">
                                    <el-input :step="1" controls-position="right" style="width:70%" :clearable="true" placeholder="请输入自定义名称"
                                        v-model="title" :maxlength="200"></el-input>
                                </el-form-item>
                            </div>
                          </div>
                          <span slot="footer" class="dialog-footer">
                            <el-button @click="dialogVisiblew = false">取 消</el-button>
                            <el-button type="primary" plain @click="onAddMarks">确 定</el-button>
                          </span>
                        </el-dialog>
                    </div>

                    <div class="bzccjlx">
                    <div class="lxwz">更新时间</div>
                      <div class="lxwz2">
                        {{ formatDate(item.createdTime) }}
                      </div>
                  </div>
                    <!--  <div class="bzccjlx" style="width: 100%; text-align: center;">
                      <div class="lxwz">更新时间</div>
                        <div class="lxwz2" style="font-size: 13px; color: #909399;">
                          更新时间:{{ formatDate(item.createdTime) }}
                        </div>
                    </div>-->

                    <div class="bzccjlx3">
                        <!-- <el-button type="primary" style="width: 170px;" class="lxwz" plain @click="onAddMarks(i)">新增信息</el-button> -->
                        <el-button type="primary" style="width: 170px;" class="lxwz" plain @click="addnew(i)">新增信息</el-button>
                        <div style="width: 170px;"></div>
                        <!-- <el-button type="primary" style="width: 170px;" class="lxwz">打印合格证</el-button> -->
                        <el-button type="danger" style="width: 170px;" class="lxwz" @click="delallremark(i)">删除</el-button>
                    </div>
                  </div>
                <div style="float: right;" v-if="!heard"><el-button type="primary" v-throttle @click="onSubmit">保存</el-button></div>
                    <!-- 操作日志 star -->
                <div class="dspbjfgx"   style="min-height:160px" v-if="heard">
                <div class="bzczrz" style="margin-top:10px;"><!-- 操作日志 --></div>
                <div class="bzczrzx" v-for="(item,index ) in loginfoList " :key="index">
                    <div>
                    <div class="rztx"></div>
                    <div class="rzmz">{{ item.name }}</div>
                    <div class="rzxgx">
                        {{ item.changeinfo }}
                    </div>
                    <div class="rzxgsj">  {{ item.time }}</div>
                    </div>
                    <div>
                    <div class="rzxgq">修改后：</div>
                    <div class="rzxgnr">{{ item.before }}</div>
                    </div>
                    <div>
                    <div class="rzxgq">修改前：</div>
                    <div class="rzxgnr">{{ item.after }}</div>
                    </div>
                </div>

                </div>
                </div>

            </div>
        </el-form>
    </my-container>
</template>
<script>
import uploadfile from '@/views/media/shooting/uploadfile'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import { editPackagesProcess, getUrgencyDegreeyList, endAction, markAction, restartAction, deletePackagesProcessing, unMarkAction,
    getProductLogList, getPackagesProcessingProductDetial,editPackagesProcessingProduct
 } from '@/api/inventory/packagesprocess';//包装加工
import { getList as getshopList } from '@/api/operatemanage/base/shop'
import { formatWarehouse } from "@/utils/tools";
import shootingreferencelist from '@/views/media/shooting/fuJianmanage/shootingreferencelistSel';
import semifinishedtable from '@/views/media/packagework/semifinishedtable.vue';
import goodschoice from "@/views/base/goods/goods2.vue";
import {getShootingSetData } from '@/api/media/shootingset'
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
export default {
    props: ["taskUrgencyList", 'groupList', 'platformList', 'islook', 'onCloseAddForm', 'warehouselist', 'allsellist','heard'],
    components: { MyContainer, MyConfirmButton, uploadfile, shootingreferencelist, semifinishedtable, goodschoice, YhImgUpload },
    data() {
        return {
            title:"",
            moindex: 0,
            dialogVisiblew:false,
            remarks:[],
            allremark: [],
            productList:"",
            hasCertificate:"",
            selected:'',
            that: this,
            addLoading: true,
            dialogFormVisible: false,
            dialogVisible: false,
            pageLoading: false,
            listLoading: true,
            inputshow: true,
            endworktime: '',
            formatWarehouse: formatWarehouse,
            //选择商品窗口
            goodschoiceVisible: false,
            allAlign1: null,
            tableData1: [
            ],
            shopList: [],
            userList: [],
            taskPhotofileList: [],
            taskExeclfileList: [],

            packageSizeList: [],
            machineTypeList: [],
            packingMaterialList: [],
            brandList: [],
            statuslist: [],
            //warehouseList:[],
            // productDetialList:[{
            //   productId: 0,
            //   brandCode: "",
            //   execStandard: "",
            //   producer: "",
            //   productAddress: "",
            //   seller: "",
            //   sellerAddress: "",
            //   customizeList: [],
            // }],
            addForm: {
                goodsCodeList: [],
                goodsCode:"",
                productDetialList:[{
                      brandCode: "",
                      producer: "",
                      productId: null,
                      execStandard: "",
                      productAddress: "",
                      seller: "",
                      sellerAddress: "",
                      customizeList:[{
                        customize:"",
                        title:"",
                      }]
                  }]
            },
            allldata: {},
            loginfo: null,
            fpPhotoLqNameEnable: false,
            fpVideoLqNameEnable: false,
            fpDetailLqNameEnable: false,
            fpModelLqNameEnable: false,
            addFormRules: {
                brandCode: [{ required: true, message: '请选择', trigger: 'blur' }],
                execStandard: [{ required: true, message: '请填写', trigger: 'blur' }],
                producer: [{ required: true, message: '请填写', trigger: 'blur' }],
                productAddress: [{ required: true, message: '请填写', trigger: 'blur' }],
                seller: [{ required: true, message: '请填写', trigger: 'blur' }],
                sellerAddress: [{ required: true, message: '请填写', trigger: 'blur' }]
            },
            extBzTypeArgs: null,
            taskrow: {},
            showType:1,
            loginfoList:[]
        };
    },
    watch: {},
    async created() {
        // this.gettabmsg();
    },
    async mounted() {
        // this.getDataSetList(15);
        // console.log(" this.showType=2;",this.addForm)
        // this.addForm.dockingPeople = this.$store.erName?.split("-")[0].trim();
    },
    computed: {
        calcAddFormRules() {
            return {
                // productShortName: [{ required: true, message: '请填写', trigger: 'blur' }],
                // shopName: [{ required: true, message: '请选择', trigger: 'blur' }],
                // operationGroup: [{ required: true, message: '请选择', trigger: 'blur' }],
                // dockingPeople: [{ required: true, message: '请填写', trigger: 'blur' }],
                // warehouse: [{ required: true, message: '请选择', trigger: 'blur' }],
                // isDelivered: [{ required: true, message: '请选择', trigger: 'blur' }],
                // shootingTaskPickList: [{ required: true, message: '请选择', trigger: 'blur' }],
                // platform: [{ required: true, message: '请选择', trigger: 'blur' }],
                // isYYJY: [{ required: true, message: '请选择', trigger: 'blur' }],
                // yyExpressNum: [{ required: this.addForm.isYYJY == 1, message: '请填写', trigger: 'blur' }],
            }
        }
    },
    methods: {
      // handleClose(done) {
      //   this.$confirm('确认关闭？')
      //     .then(_ => {
      //       done();
      //     })
      //     .catch(_ => {});
      // },
      addnew(e){
        this.title = "";
        this.dialogVisiblew = true;
        this.moindex = e;
      },
        formatDate: function(timestamp) {
            if (!timestamp) {return "";}
            else{
          var date = new Date( timestamp);
          var year = date. getFullYear();
          var month = date. getMonth() + 1;
          var day = date. getDate( );
          if(timestamp){var dateend = timestamp.toString().slice(11,timestamp.length)}
          console.log("11111",dateend)
          return year + '年' + month + '月'  + day + '日' +dateend??"";
            }
        },
        async gettabmsgbatch(goodsCodelist) {
            this.listLoading = true;
            this.addForm = {brandCode:''};
            this.showType=2;
            this.addForm.goodsCodeList=goodsCodelist;
            this.listLoading = false;
            console.log("222222",this.addForm)
        },
      delallremark(e){
        this.addForm.productDetialList.splice(e,1)
      },
      addallremark(){
        this.addForm.productDetialList.push({
                   brandCode: "",
                  producer: "",
                  productId: null,
                  execStandard: "",
                  productAddress: "",
                  seller: "",
                  sellerAddress: "",
                  customizeList:[{
                        title:"",
                        customize:"",
                      }]
                });
        console.log("333333",this.addForm.productDetialList)
      },
        //终止重启
        async onEndRestartAction() {
            let _this = this;
            this.$confirm("选中的任务将会重启，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await restartAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isend = 0;
                // this.onCloseAddForm(2);
                }
            });
        },
        //终止
        async onEndShootingTaskAction() {
            let _this = this;
            this.$confirm("选中的任务将会终止，是否确定 ", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await endAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isend = 1;
                // this.onCloseAddForm(2);
                }
            });
        },
        // 删除操作
        async onDeleteShootingTaskAction() {
            if (this.islook) return;
            this.$confirm("选中的任务会移动到回收站，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deleteShootingTaskActionAsync([this.addForm.changeImgTaskId]);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                // this.onCloseAddForm(3);
                }
            });
        },
        //任务标记
        async onSignShootingTaskAction() {
            let _this = this;
            this.$confirm("标记选中任务，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await markAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isTopOldNum = 2;
                // this.onCloseAddForm(2);
                }
            });
        },
        //任务标记
        async deltask() {
            let _this = this;
            this.$confirm("删除选中任务，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await deletePackagesProcessing(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isTopOldNum = 2;
                // this.onCloseAddForm(2);
                }
            });
        },
        // 取消标记
        async onUnSignShootingTaskAction() {
            let _this = this;
            this.$confirm("取消标记任务，是否确定执行", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                var res = await unMarkAction(_this.addForm.packagesProcessingId);
                if (res?.success) {
                this.$message({ message: '操作成功', type: "success" });
                this.addForm.isTopOldNum = 0;
                // this.onCloseAddForm(2);
                }
            });
        },
        codetoname1(code){
          let _this = this;
         _this.addForm.packingCode = null;
          this.brandList.map((item)=>{
            if(item.setId == code){
             // _this.addForm.brandCode = item.sceneCode;
              _this.addForm.packingCode = item.setId;
            }
          })
          this.$forceUpdate();
          return
        },
        // codetoname2(code,index){
        //   let _this = this;
        //   this.brandList.map((item)=>{
        //     if(item.setId == code){
        //         console.log("222222",item.value)
        //     //   _this.addForm.brandCode = item.sceneCode;
        //     }
        //   })
        //   this.$forceUpdate();
        //   return
        // },
        async getfamsg(goodsCode,goodsName,productDetialList) {
            let _this = this;
            this.showType=1;
            // this.addForm = {};
            this.addForm={brandCode:''};
            this.addForm.goodsCode=goodsCode;
            this.addForm.goodsName=goodsName;
            // this.addForm.productDetialList=productDetialList;
            // this.addForm={productId:''};
            // this.addForm.execStandard="";
            // this.addForm.producer="";
            // this.addForm.productAddress="";
            // this.addForm.seller="";
            // this.addForm.sellerAddress="";
            this.listLoading = true;
            // this.addForm.brandCode="";
            //
            await _this.getDataSetList(15);
            const res = await getPackagesProcessingProductDetial(goodsCode);
            console.log("shujujujuuuuuuu",res.data)
            if (!res?.success) {
                return
            }
            if(res.data!=null){
              res.data.productDetialList.map((item)=>{
                if(!item.customizeList){
                    item.customizeList = [];
                }
              })
              this.addForm = res.data;
              // this.title = res.data
              console.log("111111",res.data)
              console.log("2222222",_this.brandList)

              _this.brandList.map((item)=>{
                if(item.setId == res.data.packingCode){
                    // this.addForm.brandCode
                    this.addForm.brandCode = item.sceneCode;
                }
              })
                // this.addForm = res.data
            }
            console.log("4444",this.addForm.brandCode)
            this.$forceUpdate();
            this.getlogInfo(goodsCode);
            this.listLoading = false;
        },
        // async gettabmsg(data) {
        //     this.listLoading = true;
        //     this.packageSizeList = this.allsellist.packageSizeList;
        //     this.machineTypeList = this.allsellist.machineTypeList;
        //     this.brandList = this.allsellist.brandList;
        //     this.packingMaterialList = this.allsellist.packingMaterialList;
        //     await this.getStatus();
        //     this.listLoading = false;
        // },

        onAddMarks(e){
            this.addForm.productDetialList[this.moindex].customizeList.push({
            "title": this.title,
            "customize":"",
          });
          this.dialogVisiblew = false;
          console.log("22222", this.addForm.productDetialList)
        },
        async onDelMarks(index,i)
        {
            this.addForm.productDetialList[i].customizeList.splice(index,1);
            // if(remarkId > 0){
            //     this.$confirm('此操作将会彻底删除该记录，是否执行')
            //     .then(async() => {
            //        var res= await delRemark({remarkId:remarkId});
            //        if(res?.success)
            //             this.remarks.splice(index,1);
            //     })
            //     .catch(_ => {
            //         this.pageLoading =false;
            //     });
            // }else{
            //     this.remarks.splice(index,1);
            // }

        },
        async gettabmsg(data) {
            this.showType=1;
            this.listLoading = true;
            this.packageSizeList = this.allsellist.packageSizeList;
            this.machineTypeList = this.allsellist.machineTypeList;
            this.brandList = this.allsellist.brandList;
            this.packingMaterialList = this.allsellist.packingMaterialList;
            await this.getStatus();
            this.listLoading = false;
        },
        async getStatus(){
            const res = await getUrgencyDegreeyList();
            if (!res?.success) {
                return
            }
            this.statuslist = res.data;
        },
        statustotext(val){
            this.addForm.urgencyDegree = val;
            var num = ""
            this.statuslist.forEach((item)=>{
                if(item.value == val) num = item.label;
                return
            })
            return num;
        },
        async getDataSetList(index) { //14包装加工，15-品牌，16包装加工-机型，17包装加工-尺寸
            const res = await getShootingSetData({ setType: index });
            if (!res?.success) {
                return
            }
            switch (index) {
                case 14:
                    this.packingMaterialList = res?.data?.data;
                    break;
                case 15:
                    this.brandList = res?.data?.data;
                    break;
                case 16:
                    this.machineTypeList = res?.data?.data;
                    break;
                case 17:
                    this.packageSizeList = res?.data?.data;
                    break;
            }
        },
        //移除明细
        async onDelDtlGood(index) {
            this.addForm.detialPackagesProcess.splice(index, 1);
        },
        //选择商品确定
        async onQueren() {
            console.log("选择数据",this.seltype)
            if (this.seltype == 0) {
                //选择成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.addForm.finishedProductCode = f.goodsCode;
                        this.addForm.finishedProductName = f.goodsName;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 2) {
                //完成界面选择商品
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.finishForm.consumeGoodsCode = f.goodsCode;
                        this.finishForm.consumeGoodsName = f.goodsName;
                        this.finishForm.consumePicture = f.picture;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else if (this.seltype == 10) {
                //完成界面选择商品
                var choicelist = await this.$refs.goodschoice.getchoicelistOnly();
                if (choicelist && choicelist.length == 1) {
                    choicelist.forEach(f => {
                        //反填数据
                        this.updateUserGoodsAmountData.consumeGoodsCode = f.goodsCode;
                        this.updateUserGoodsAmountData.consumeGoodsName = f.goodsName;
                        this.updateUserGoodsAmountData.consumePicture = f.picture;
                    })
                    this.goodschoiceVisible = false;
                }
            }
            else {
                //选择半成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                    //反填数据,
                    if (this.addForm.detialPackagesProcess) {
                        //已存在的不添加
                        var temp = this.addForm.detialPackagesProcess;
                        var isNew = true;
                        choicelist.forEach(f => {
                            isNew = true;
                            temp.forEach(old => {
                                if (old.halfProductCode == f.goodsCode) {
                                    isNew = false;
                                }
                            });
                            //
                            if (isNew) {
                                this.addForm.detialPackagesProcess.push({ halfProductCode: f.goodsCode, halfProductName: f.goodsName, halfProductQuantity: 0, dtlActualGoodsAmount: 0 });
                            } else {
                                this.addForm.detialPackagesProcess.forEach(told => {
                                    if (told.halfProductCode == f.goodsCode) {
                                        told.halfProductName = f.goodsName;
                                    }
                                });
                            }
                        })

                    }
                    this.goodschoiceVisible = false;
                }
            }
        },
        //新增/编辑/完成界面的【选择商品】按钮
        onSelctCp(type) {
            console.log("点击hi按钮",type)
            this.seltype = type;
            this.goodschoiceVisible = true;
            this.$nextTick(() => {
                this.$refs.goodschoice.removeSelData();
            })
        },
        async OpenAdd() {
            this.addDialogTitle = "创建加工表单";
            this.dialogFormVisible = true;
        },
        async selreference() {
            var res = await editPackagesProcess(this.addForm);
            this.addLoading = false;
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' }); return; }
            this.$message({ message: '创建任务成功！', type: 'success' });
        },
        reference() {
            this.dialogVisible = true;
        },
        async onchangeplatform(val) {
            var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
            this.addForm.shopName = "";
            this.shopList = res1.data.list;
        },
        //提交保存时验证
        onSubmitValidate: function () {
            let isValid = true;
            this.$refs.addForm.validate(valid => {
                isValid = valid
            })
            return isValid;
        },
        //提交保存
        async onSubmit() {

            var istrue = false;
            // if(this.addForm.productDetialList.length>0){
            //     this.addForm.productDetialList.map((item)=>{
            //         if(!item.sellerAddress||!item.seller||!item.productAddress||!item.producer||!item.execStandard||!item.brandCode){
            //             istrue = true
            //         }
            //     })
            //     if(!this.addForm.brandCode||istrue){
            //         this.$message.error("请填写完整！")
            //         return
            //     }

            // }
            // if(!this.addForm.brandCode){
            //     this.$message.error("请填写完整！")
            //     return
            // }


            // if (!this.onSubmitValidate()) {
            //     return;
            // }
            if(this.addForm.goodsCode)
            {
                var goodsCodeList = new  Array();
                goodsCodeList.push(this.addForm.goodsCode);
                this.addForm.goodsCodeList=goodsCodeList;
            }
            var res = await editPackagesProcessingProduct(this.addForm);
            this.addLoading = false;
            if (!res?.success) { this.$message({ message: res.msg, type: 'error' }); return; }
            else {
                this.$message({ message: '修改商品成功！', type: 'success' });
                this.addLoading = false;
                if(this.showType==1)
                {
                    await this.getlogInfo();
                    // this.addForm={brandCode:''};
                    this.$emit('onCloseAddForm',2);
                }
                 else if(this.showType==2)
                {
                    // this.addForm={brandCode:''};
                    this.$emit('onCloseAddForm',2);
                }
                // this.$emit('onCloseAddForm',1);
            }
        },
        async getlogInfo (){
        this.pageLoading =true;
        var res  =  await getProductLogList(this.addForm.goodsCode);
        if(res?.success){
            console.log(333,res)
            this.loginfoList  = res.data;
       }
        this.pageLoading =false;
      },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .bzjzcjrw {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
}

::v-deep .bzjzcjrw .bt {
    height: 40px;
    /* background-color: aquamarine; */
    font-size: 18px;
    color: #666;
    border: 1px solid #dcdfe6;
    border-right: 0px;
    border-left: 0px;
    box-sizing: border-box;
    padding: 0 35px;
}

::v-deep .bzjzcjrw .rwmc {
    margin-bottom: 20px;
}

::v-deep .bzjzcjrw .bt i {
    color: #999;
}

::v-deep .bzjzcjrw .bt i {
    margin-left: 8px;
    line-height: 26px;
}

::v-deep .bzjzcjrw .bt i:hover {
    margin-left: 8px;
    line-height: 26px;
    color: #409eff;
    position: relative;
    top: -2px;
}

::v-deep .bzjzcjrw .bzccjlx {
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx {
    width: 100%;
    height: 40px;
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}

::v-deep .bzjzcjrw .bzccjlx .lxwz {
    width: 20%;
    font-size: 14px;
    color: #666;
    vertical-align: top;
    line-height: 26px;
    /* background-color: rgb(204, 204, 255); */
}

::v-deep .bzjzcjrw .bzccjlx .lxwz2 {
    width: 80%;
}
::v-deep .el-form-item__content{
    margin-left: 30px !important;
}

::v-deep   .bzczrz {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep  .bzczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .el-form-item {
  margin: 0px !important;
}

::v-deep .el-form-item__error {
  position: absolute !important;
  top: 30% !important;
  left: 400px !important;
  width: 60px !important;
}

::v-deep .mycontainer{
  padding:0px 5px !important;
  height:100% !important;
}

::v-deep  .bzbjrw {
  width: 750px;
  background-color: #fff;
}

::v-deep  .bzbjrw .bzbjbt {
  height: 60px;
  background-color: rgb(255, 255, 255);
  font-size: 18px;
  color: #666;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 20px 35px;
}
::v-deep  .bzbjrw .bzbjbt i {
  color: #999;
}
::v-deep .bzbjrw .bzbjbt i {
  margin-left: 8px;
  line-height: 26px;
}
::v-deep .bzbjrw .bzbjbt i:hover {
  margin-left: 8px;
  line-height: 26px;
  color: #409eff;
  position: relative;
  top: -2px;
}

::v-deep  .bzbjrw .rwmc {
  width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  float: left;
  box-shadow: 0px 3px 5px #eeeeee;
  box-sizing: border-box;
  padding: 0 56px;
}
::v-deep .bzbjrw .rwmc .xh {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  padding: 0 2px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

::v-deep  .bzbjrw .rwmc .mc,
::v-deep  .icon {
  height: 65px;
  font-size: 18px;
  line-height: 68px;
  box-sizing: border-box;
  margin-left: 10px;
  padding: 0 2px;
  display: inline-block;
  color: #666;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .bzbjrw .bzbjlx {
  width: 100%;
  height: 35px;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .bzbjrw .bzbjlxf {
  width: 100%;
  /* background-color: bisque; */
  box-sizing: border-box;
  padding: 0 60px;
}

::v-deep .bzbjrw .bzbjlx .lxwz {
  width: 112px;
  font-size: 14px;
  color: #666;
  vertical-align: top;
  line-height: 26px;
  /* background-color: rgb(204, 204, 255); */
  display: inline-block;

}

.overtitle{
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  white-space: normal;
  width: 50px;
}

::v-deep .bzbjrw .scpd {
  width: 100px;
  font-size: 14px;
  color: #666;
  line-height: 36px;
  /* background-color: rgb(204, 204, 255); */
  float: left;
}

::v-deep  .bzbjrw .zjck {
  display: inline-block;
  float: right;
  position: relative;
  top: 5px;
}

::v-deep  .bzbjrw .bzczxx {
  width: 100%;
  box-sizing: border-box;
  /* padding: 0 60px; */
  text-align: center;
  border: 1px solid #dcdfe6;
  border-right: 0px;
  border-bottom: 0px;
  border-left: 0px;
  margin: 0 0;
}

::v-deep  .bzbjrw .qxtj {
  height: 30px;
  /* background-color: aquamarine; */
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep  .bzbjrw .bzsccg {
  width: 100%;
  box-sizing: border-box;
  padding: 5px 60px;
  margin-bottom: 30px;
}

::v-deep  .bzbjrw .bzczx {
  box-sizing: border-box;
  padding: 5px 60px;
  font-size: 14px;
  color: #666;
}

::v-deep  .bzbjrw .bzscpd .bzczan,
.bzczs,
.bzczan,
.bzczmz,
.bzczsj {
  min-width: 50px;
  max-width:150px;
  display: inline-block;
  margin-right: 10px;
  /* background-color: aquamarine; */
}

::v-deep   .bzbjrw .bzczsj {
  color: #999;
}
::v-deep   .bzbjrw .bzczk {
  height: 35px;
}

::v-deep  .bzbjrw .bzczksps {
  width: 14%;
  display: inline-block;
}
::v-deep  .bzbjrw .bzczkzy {
  width: 43%;
  /* background-color: aqua; */
  display: inline-block;
}

::v-deep   .bzbjrw .bzczs {
  width: 65px;
}
::v-deep   .bzbjrw .bzbjfgx {
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  box-sizing: border-box;
  padding: 25px 0;
}

::v-deep  .bzbjrw .bzczrz {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .bzbjrw .bzczrzx {
  box-sizing: border-box;
  padding: 10px 60px;
}

::v-deep .bzbjrw .rztx,
.rzmz,
.rzxgx,
.rzxgsj {
  height: 30px;
  display: inline-block;
  font-size: 14px;
  line-height: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep  .bzbjrw .rztx {
  width: 25px;
  height: 25px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15px;
}

::v-deep  .bzbjrw .rzmz {
  width: 50px;
  margin-right: 5px;
}
.showdom{
    box-shadow: 0px 3px 8px #cacaca;
}

 .rzxgx {
  max-width: 200px;
  margin-right: 10px;
  color: #999;
}

 .rzxgsj {
  max-width: 200px;
  color: #999;
}

 .rzxgq,
.rzxgnr {
  max-width: 450px;
  line-height: 15px;
  display: inline-block;
  font-size: 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// ::v-deep .bzbjrw .rzxgq {
//   width: 50px;
//   margin-left: 43px;
//   margin-right: 2px;
// }
::v-deep ::-webkit-scrollbar{
  width: 0 !important;
  display: none;
}
::v-deep  .bzbjrw .bzczksps {
  width: 15%;
  display: inline-block;
}
::v-deep  .bzbjrw .bzczkz {
  width: 38%;
  /* background-color: aqua; */
  display: inline-block;
}

::v-deep  .bzbjrw .bzczky {
  width: 42%;
  /* background-color: aqua; */
  display: inline-block;
}
.bzccjlx1{
    width: 100%;
    height: 50px;
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
}
.bzccjlx2{
  border: 1px solid #dcdfe6;
  border-width: 1px 0;
}
::v-deep .bzjzcjrw .bzccjlx3 {
    width: 100%;
    height: 40px;
    box-sizing: border-box;
    padding: 0 60px;
    display: flex;
    margin-bottom	:10px;
}

</style>

