<template>
    <!-- 义乌提成 -->
    <my-container>
        <template #header>
            <el-button style="height: 28px;" type="primary" @click="onSave"
                v-if="checkPermission('shootingHs-tc-save')">保存2</el-button>
            <el-button style="height: 28px;" type="primary" @click="onSearch">刷新</el-button>
            <el-button style="height: 28px;" type="primary" @click="onExeprotShootingTask">导出</el-button>
            <el-button style="height: 28px;" type="primary" @click="onImportSyj">导入</el-button>
        </template>
        <div style="height: 87vh;margin-top: -20px">
            <vxetablebase :id="'mediaNCCommission'" :hasSeq="false" :border="true" :hasexpand='true' :hascheck="false"
                :align="'center'" ref="table" @editclosed="editclosed" 
                :editconfig="{ trigger: 'click', mode: 'cell', showStatus: true }" :that='that' :tableData='tasklist'
                :tableCols='tableCols' :loading="listLoading"></vxetablebase>
        </div>
        <!--薪资模板导入-->
        <el-dialog title="提成导入" :visible.sync="dialogVisibleSyj" width="30%" :before-close="onImportClose">
            <span>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1"
                    :file-list="fileList" action accept=".xlsx" :http-request="uploadFile">
                    <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <my-confirm-button style="margin-left: 10px;" size="small" type="success"
                        @click="onSubmitupload">上传</my-confirm-button>
                </el-upload>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="onImportClose()">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";
import { getCommissionPositionAsync, saveCommissionPositionAsync,importShootCommonSalary ,exportCommissionTaskReport} from '@/api/media/shootingset';

const tableCols = [
    {
        istrue: true, label: '', width: '105', merge: true, prop: 'mergeField',
        cols: [{ istrue: true, prop: 'sceneCode', label: '提成岗位', width: '105', align: 'center', fixed: 'left' },
        ]
    },
    //新品拍摄
    {
        istrue: true, prop: '', label: '新品拍摄', merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'shootingDeptCommission', type: 'editNumber', label: '部门总款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingPhotoCommission', type: 'editNumber', label: '照片总款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingPhotoPrice', type: 'editNumber', label: '照片单款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingVedioCommission', type: 'editNumber', label: '视频总款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingVedioPrice', type: 'editNumber', label: '视频单款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingMicroVedioPrice', type: 'editNumber', label: '微。单条', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingDetailCommission', type: 'editNumber', label: '详情页总款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingDetailPrice', type: 'editNumber', label: '详情页单款', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingModelPhotoCommission', type: 'editNumber', label: '建模照片总数', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingModelPhotoPrice', type: 'editNumber', label: '建模照片单张', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingModelVedioCommission', type: 'editNumber', label: '建模视频总数', width: '52', align: 'center' },
            { istrue: true, prop: 'shootingModelVedioPrice', type: 'editNumber', label: '建模视频单个', width: '52', align: 'center' },
        ]
    },
    //微详情视频
    {
        istrue: true, prop: '', label: '微。视频', width: '140', merge: true, prop: 'mergeField2',
        cols: [
            { istrue: true, prop: 'microVedioCommission', type: 'editNumber', label: '微。拍摄单条', width: '52', align: 'center' },
            { istrue: true, prop: 'microVedioPrice', type: 'editNumber', label: '微。建模单条', width: '52', align: 'center' },
        ]
    },
    //直通车图
    {
        istrue: true, prop: '', label: '直通车图', width: '140', merge: true, prop: 'mergeField3',
        cols: [
            { istrue: true, prop: 'directDeptCommission', type: 'editNumber', label: '部门总组', width: '52', align: 'center' },
            { istrue: true, prop: 'directPhotoCommission', type: 'editNumber', label: '照片总组', width: '52', align: 'center' },
            { istrue: true, prop: 'directPhotoPrice', type: 'editNumber', label: '照片单组', width: '52', align: 'center' },
            { istrue: true, prop: 'directDesignCommission', type: 'editNumber', label: '设计总组', width: '52', align: 'center' },
            { istrue: true, prop: 'directDesignPirce', type: 'editNumber', label: '设计单组', width: '52', align: 'center' },
            { istrue: true, prop: 'directModelPhotoCommission', type: 'editNumber', label: '建模照片总张', width: '52', align: 'center' },
            { istrue: true, prop: 'directModelPhotoPrice', type: 'editNumber', label: '建模照片单张', width: '52', align: 'center' },
        ]
    },
    //店铺装修
    {
        istrue: true, prop: '', label: '店铺装修', width: '140', merge: true, prop: 'mergeField4',
        cols: [
            { istrue: true, prop: 'shopDeptCommission', type: 'editNumber', label: '部门总套', width: '52', align: 'center' },
            { istrue: true, prop: 'shopDesignCommission', type: 'editNumber', label: '设计总套', width: '52', align: 'center' },
            { istrue: true, prop: 'shopDesignPrice', type: 'editNumber', label: '设计单套', width: '52', align: 'center' },
            { istrue: true, prop: 'shopModelPhotoCommission', type: 'editNumber', label: '建模照片总张', width: '52', align: 'center' },
            { istrue: true, prop: 'shopModelPhotoDirectPrice', type: 'editNumber', label: '建模照片单张', width: '52', align: 'center' },
        ]
    },
    //包装设计
    {
        istrue: true, prop: '', label: '包装设计', width: '140', merge: true, prop: 'mergeField5',
        cols: [
            { istrue: true, prop: 'packageDeptCommission', type: 'editNumber', label: '部门总款', width: '52', align: 'center' },
            { istrue: true, prop: 'packageDesignPrice', type: 'editNumber', label: '设计单套', width: '52', align: 'center' },
            { istrue: true, prop: 'packageModelPhotoCommission', type: 'editNumber', label: '建模照片总张', width: '52', align: 'center' },
            { istrue: true, prop: 'packageModelPhotoPrice', type: 'editNumber', label: '建模照片单张', width: '52', align: 'center' },
        ]
    },
    //改图
    {
        istrue: true, prop: '', label: '改图', width: '140', merge: true, prop: 'mergeField6',
        cols: [
            { istrue: true, prop: 'imgCDeptCommission', type: 'editNumber', label: '部门总组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCPhotoCommission', type: 'editNumber', label: '照片总组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCPhotoPrice', type: 'editNumber', label: '照片单组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCVedioCommission', type: 'editNumber', label: '视频总组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCVedioPrice', type: 'editNumber', label: '视频单组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCDetailCommission', type: 'editNumber', label: '美工总组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCDetailPrice', type: 'editNumber', label: '美工单组', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCModelPhotoCommission', type: 'editNumber', label: '建模照片总张', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCModelPhotoPrice', type: 'editNumber', label: '建模照片单张', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCModelVedioCommission', type: 'editNumber', label: '建模视频总数', width: '52', align: 'center' },
            { istrue: true, prop: 'imgCModelVedioPrice', type: 'editNumber', label: '建模视频单个', width: '52', align: 'center' },
        ]
    },

    //其他
    {
        istrue: true, prop: '', label: '短视频', width: '140', merge: true, prop: 'mergeField7',
        cols: [
            { istrue: true, prop: 'deptShortVideoPriceCommission', type: 'editNumber', label: '部门总条', width: '52', align: 'center' },
            { istrue: true, prop: 'shootShortVideoPriceCommission', type: 'editNumber', label: '拍摄总条', width: '52', align: 'center' },
            { istrue: true, prop: 'cuteShortVideoPriceCommission', type: 'editNumber', label: '剪辑总条', width: '52', align: 'center' },
            { istrue: true, prop: 'shootShortVideoPrice', type: 'editNumber', label: '拍摄单条', width: '52', align: 'center' },
            { istrue: true, prop: 'cuteShortVideoPrice', type: 'editNumber', label: '剪辑单条', width: '52', align: 'center' },
        ]
    },
    //其他
    {
        istrue: true, prop: '', label: '其他', width: '140', merge: true, prop: 'mergeField8',
        cols: [
            { istrue: true, prop: 'otherDeptCommission', type: 'editNumber', label: '部门总组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherPhotoCommission', type: 'editNumber', label: '照片总组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherPhotoPrice', type: 'editNumber', label: '照片单组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherVedioCommission', type: 'editNumber', label: '视频总组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherVedioPrice', type: 'editNumber', label: '视频单组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherDetailCommission', type: 'editNumber', label: '详情页总组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherDetailPrice', type: 'editNumber', label: '详情页单组', width: '52', align: 'center' },
            { istrue: true, prop: 'otherModelPhotoCommission', type: 'editNumber', label: '建模照片总张', width: '52', align: 'center' },
            { istrue: true, prop: 'otherModelPhotoPrice', type: 'editNumber', label: '建模照片单张', width: '52', align: 'center' },
            { istrue: true, prop: 'otherModelVedioCommission', type: 'editNumber', label: '建模视频总数', width: '52', align: 'center' },
            { istrue: true, prop: 'otherModelVedioPrice', type: 'editNumber', label: '建模视频单个', width: '52', align: 'center' },
        ]
    },

];
export default {
    components: { MyContainer, vxetablebase ,MyConfirmButton},
    props: {
        classType: { type: Number, default: 0 },
    },
    data() {
        return {
            that: this,
            listLoading: false,
            tableCols: tableCols,
            dialogVisibleSyj: false,
            tasklist: [],
            caclenum: 1
        };
    },
    //向子组件注册方法
    provide() {
        return {
        }
    },
    async mounted() {
        //await this.onSearch();
    },
    methods: {
 
        onImportClose() {
            this.dialogVisibleSyj = false;
            this.fileList = [];
        },
        onImportSyj() {
            this.dialogVisibleSyj = true;
        },
        async onSubmitupload() {
            this.$refs.upload.submit()
        },
        async uploadFile(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("actiontype", this.classType);
            var res = await importShootCommonSalary(form);
            this.fileList = [];
            if (res?.success) {
                this.$message({ message: '上传成功!', type: "success" });
                this.onSearch();
                this.dialogVisibleSyj = false;
            }
        },
        async onExeprotShootingTask() {
          
            this.listLoading = true;
            var res = await  exportCommissionTaskReport({classType: this.classType}); 
            if (res?.data?.type == 'application/json') { this.listLoading = false; return;}
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download',(this.classType == 1?'南昌提成_':'义乌提成_' )+ new Date().toLocaleString() + '.xlsx' )
            aLink.click();
            this.listLoading = false;
        },
        editclosed(val) {
            this.$emit('editclosed', val);
        },
        async onSave() {
            this.listLoading = true;
            let ret = await saveCommissionPositionAsync(this.tasklist);
            if (ret?.success) {
                this.$message({ message: '保存成功', type: "success" });
                this.$emit('editclosed', false);
            }
            this.listLoading = false;
        },
        async onSearch() {
            this.listLoading = true;
            let ret = await getCommissionPositionAsync({ type: this.classType });
            if (ret?.success) {
                this.tasklist = ret.data;
            }
            this.listLoading = false;
        },
    },
};
</script> 
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
}

::v-deep .vxe-header--column .vxe-cell--edit-icon,
::v-deep span.vxe-input--extra-suffix {
    display: none;
}

::v-deep .vxetoolbar20221212 {
    position: absolute;
    top: 30px;
    right: 5px;
    padding-top: 0;
    padding-bottom: 0;
    z-index: 999;
    background-color: rgb(255 255 255 / 0%);
}
</style>

