<template>
    <my-container v-loading="pageLoading">
       <el-form >
           <el-row>
                   <el-col :span="6">
                       <el-row>  </el-row>
                       <el-row>  </el-row> 
                       <el-row>  <el-button type="primary" >拍摄照片</el-button> </el-row>
                       <el-row>  <el-button type="primary" >建模照片</el-button> </el-row> 
                       <el-row>  </el-row>
                       <el-row>  </el-row> 
                       <el-row>  <el-button type="primary" >主图视频</el-button></el-row>
                       <el-row>  <el-button type="primary" >微详情视频</el-button> </el-row> 
                       <el-row>  <el-button type="primary" >建模视频</el-button> </el-row>
                       <el-row>  </el-row> 
                       <el-row>  <el-button type="primary" >详情页</el-button> </el-row>
                       <el-row>  </el-row> 
                   </el-col>
                   <el-col :span="18">
                       <el-form-item  prop="shopName"  label="店铺:">
                           <el-select v-model="addForm.shopName" :clearable="true" :collapse-tags="true" filterable>
                               <el-option v-for="item in shopList" :key="item.label" :label="item.label"
                                   :value="item.label" />
                           </el-select>
                       </el-form-item>
               </el-col>
           </el-row>
       </el-form>
   </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";


export default {
   components: { MyContainer,shootingsuccessfiledetail },
   data() {
       return {
          
           pageLoading:false,
          
       };
   },
  
   async mounted() {
   }, 
   methods: {
       
   },
};
</script>

