<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <template #header>
      <div style="width: 100%; display: flex; flex-direction: row; align-items: center;">


          <el-date-picker style="width: 200px" v-model="timerange" type="datetimerange" format="yyyy-MM-dd" @change="changedate" v-show="activeName=='first1'"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
          </el-date-picker>

          <el-date-picker style="width: 200px" v-model="timerangetwo" type="datetimerange" format="yyyy-MM-dd" @change="changedatetwo" v-show="activeName=='first2'"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
          </el-date-picker>

        
        <el-checkbox style="margin-left: 20px;" :checked="allplatform" v-model="allplatform" @change="allCheckChange()">全选</el-checkbox>

        <el-checkbox-group v-model="isselplatform" style="margin-left: 18px;" @change="handleCheckChange($event,'fat')">
          <el-checkbox  v-for="item in platforms" :label="item.platform" :key="item.platform" @change="handleCheckChange(item.platform,'chil')">{{item.platform}}</el-checkbox>
        </el-checkbox-group>
        <el-button type="primary" style="margin-left: 10px; " @click="onSearch">查 询</el-button>
        <el-button v-if="checkPermission('api:bookkeeper:ContinuousProfitAnalysis:NewContinuousNoProfitNoticeSaveAsync')" type="warning" style="margin-left: 30px;" @click="onSure">确 认</el-button>
        
        

      </div>
      <div style="margin-left: 220px;">
       
        <div style="display: flex; flex-direction: row;" v-show="activeName=='first1'?true:false">
          <el-checkbox :checked="allplatformlr" :disabled="allplatformlr" v-model="allplatformlr" @change="allCheckChangelr()">全选</el-checkbox>

          <el-checkbox-group v-model="isselplatformlr" style="margin-left: 18px;" @change="handleCheckChangelr($event,'fat')">
            <el-checkbox  v-for="item in lirunlists" :disabled="isselplatformlr.length==1&&isselplatformlr[0]==item.platform" :label="item.platform" :key="item.platform">{{item.platform}}</el-checkbox>
          </el-checkbox-group>
        </div>



        <div v-show="activeName=='first2'?true:false">
          <el-button-group>
            <el-button :type="item.istrue?'primary':'info'"  @click="tagclick(item)" v-for="item in zhiweilist" :key="item.label">{{item.label}}</el-button>
          </el-button-group>

        </div>  
      </div>
    </template>
    <el-tabs v-model="activeName" style="height: 94%" @tab-click="handleClick">
      <el-tab-pane label="趋势图" name="first1" style="height: 100%">
        <showChar ref="showChar" :filter="filter" style="height: 100%" :isselplatformlr="isselplatformlr" :isselplatform="isselplatform"></showChar>
      </el-tab-pane>
      <el-tab-pane label="数据看板" name="first2" style="height: 100%">
        <showTable ref="showTable" :filter="filter" style="height: 100%"></showTable>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import showChar from "./showChar.vue";
import showTable from "./showTable.vue";
import { getList as getshop  } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import { continuousNoProfitNoticeSaveAsync, newContinuousNoProfitNoticeSaveAsync } from "@/api/bookkeeper/continuousprofitanalysis"
import { formatTime } from "@/utils/tools";

const platformvalue = [{label: '天猫',value: 1},{label: '拼多多',value: 2},{label: '阿里巴巴',value: 4},
{label: '抖音',value: 6},{label: '京东京喜',value: 7},{label: '淘工厂',value: 8},{label: '淘宝',value: 9},{label: '苏宁',value: 10},]
export default {
  name: "ConsecutiveNoProfitSearchIndex",
  components: {
    MyContainer, showChar, showTable
  },
  data() {
    return {
      that: this,
      timerange: [],
      timerangetwo: [],

      pageLoading: false,
      activeName: "first1",
      allplatformlr: true,
      filter: {
        yearMonthDay: null,
        shopCode: null,
        groupId: null,
        operateSpecialUserId: null,
        groupTypes: [],
        profitTypes: null,
        platforms: [1,2,4,6,7,8,9,10],
        

        startTime: formatTime(new Date().setDate(new Date().getDate() - 30), "YYYY-MM-DD"),
        endTime: formatTime(new Date(), "YYYY-MM-DD"),

      },
      shopList: [],
      grouplist: [],
      directorlist: [],
      platforms: [
        {platform:'天猫',istrue: true},
        {platform:'阿里巴巴',istrue: true},
        {platform:'抖音',istrue: true},
        {platform:'京东京喜',istrue: true},
        {platform:'淘工厂',istrue: true},
        {platform:'淘宝',istrue: true},
        {platform:'苏宁',istrue: true},
        {platform:'拼多多',istrue: true},
      ],
      lirunlists: [
        {platform:'3天负利润',istrue: true},
        {platform:'5天负利润',istrue: true},
        {platform:'7天负利润',istrue: true},
        {platform:'10天负利润',istrue: true},
        {platform:'15天负利润',istrue: true},
        {platform:'30天负利润',istrue: true},
      ],
      isselplatformlr: ['3天负利润','5天负利润','7天负利润','10天负利润','15天负利润','30天负利润'],
      // isselplatform: [],
      isselplatform: [
        '天猫',
        '阿里巴巴',
        '抖音',
        '京东京喜',
        '淘工厂',
        '淘宝',
        '苏宁',
        '拼多多',
      ],
      lirunlist: ['3天负利润','5天负利润','7天负利润','10天负利润','15天负利润','30天负利润'],
      zhiweilist: [
      
      {label: '运 营 组', value: 3, istrue: false},
      {label: '运营专员', value: 4, istrue: false},

      {label: '运营助理', value: 5, istrue: false},
      {label: '店铺名称', value: 2, istrue: false},

    ],
      seltag: '',
      allplatform: true,
    };
  },
  async mounted() {
    if(!JSON.parse(localStorage.getItem('isselplatformlr'))||JSON.parse(localStorage.getItem('isselplatformlr'))?.length==0){
      localStorage.setItem('isselplatformlr', JSON.stringify(this.lirunlist))
    }
    this.isselplatformlr = JSON.parse(localStorage.getItem('isselplatformlr')).length>0?JSON.parse(localStorage.getItem('isselplatformlr')):[]

    if(this.isselplatformlr.length == this.lirunlist.length){
        this.allplatformlr = true;
      }else{
        this.allplatformlr = false;
      }

    // this.filter.yearMonthDay= formatTime(new Date(),"YYYYMMDD");

    let end = new Date();
    let start = new Date();
    start.setDate(start.getDate() - 30);
    this.timerange = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]
    this.timerangetwo = [formatTime(start, "YYYY-MM-DD"), formatTime(end, "YYYY-MM-DD")]



    // this.filter.startTime = this.timerange[0];
    // this.filter.endTime = this.timerange[1];

    await this.getShopList();
  },
  methods: {
    getPrevious30Days(dateString) {
      const date = new Date(dateString);
      date.setDate(date.getDate() - 30);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      const previousDate = `${year}-${month}-${day}`;
      return previousDate;
    },
    getDaysDifference(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      const firstDate = new Date(date1);
      const secondDate = new Date(date2);

      // 将日期转换为时间戳，并且取绝对值以确保始终得到正数
      const diffDays = Math.abs(Math.round((firstDate - secondDate) / oneDay));

      return diffDays;
    },
    changedate(val){
      let days = this.getDaysDifference(val[0],val[1]);
      if(days<30){
        this.$message.info("请选择至少一个月时间，已为您自动选择一个月时间")
        this.filter.startTime = this.getPrevious30Days(val[1]);
        this.filter.endTime = val[1];
      }else{
        this.filter.startTime = val[0];
        this.filter.endTime = val[1];
      }

      this.timerange = [this.filter.startTime, this.filter.endTime];
     

    },
    changedatetwo(val){
      // let days = this.getDaysDifference(val[0],val[1]);

      this.filter.startTime = val[0];
      this.filter.endTime = val[1];


      this.timerangetwo = [this.filter.startTime, this.filter.endTime];
     

    },
    tagclick(e){
      this.zhiweilist.map((item)=>{
        if(item.label == e.label){
          item.istrue = !item.istrue
        }
      })

      this.filter.groupTypes = [];
      this.zhiweilist.map((item)=>{
        if(item.istrue){
          this.filter.groupTypes.push(item.value)
        }
      })
    },
    allCheckChange(e){
      if(this.allplatform){
        this.isselplatform = [
          '天猫',
          '阿里巴巴',
          '抖音',
          '京东京喜',
          '淘工厂',
          '淘宝',
          '苏宁',
          '拼多多',

        ]
       
      }else{
        this.isselplatform = [];


      }
      this.$set(this.filter, 'platforms', this.allplatform ? [1,2,4,6,7,8,9,10] : null)
      this.onSearch()
    },
    allCheckChangelr(e){
      if(this.allplatformlr){
        this.isselplatformlr = this.lirunlist;
        localStorage.setItem('isselplatformlr', JSON.stringify(this.lirunlist))
       
      }else{
        localStorage.setItem('isselplatformlr', JSON.stringify([]))

        this.isselplatformlr = [];
      }
      this.onSearch();
      // this.filter.platforms = null;
    },
    handleCheckChange(e,type){
      if(type == 'chil'){
        console.log("打印数据1",e)
       
      }else if(type == 'fat'){
     
      let selplatform = [
          '天猫',
          '阿里巴巴',
          '抖音',
          '京东京喜',
          '淘工厂',
          '淘宝',
          '苏宁',
          '拼多多',

        ]

        

       
        if(selplatform.length == e.length){
          this.filter.platforms = null;
          this.allplatform = true;
        }else{
          this.filter.platforms = []
          platformvalue.map((item)=>{
            if(e.indexOf(item.label) != -1){
              this.filter.platforms.push(item.value)
            }
          })
          this.allplatform = false;
        }


        

      }
      this.onSearch()

    },
    handleCheckChangelr(e,type){
      

      

      this.isselplatformlr = e;

      
      localStorage.setItem('isselplatformlr', JSON.stringify(e))
      let selplatform = this.lirunlist;

      if(selplatform.length == e.length){
        this.allplatformlr = true;
      }else{
        
        this.allplatformlr = false;
      }


      this.onSearch()

    },
    async getShopList() {

      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    onSearch() {
      if (this.activeName == "first1") {
        this.filter.startTime = this.timerange[0];
        this.filter.endTime = this.timerange[1];
        this.$refs.showChar.showChar();
      } else {
        this.filter.startTime = this.timerangetwo[0];
        this.filter.endTime = this.timerangetwo[1];
        this.$refs.showTable.getlist();
      }
    },
    changeCheck() {
      this.onSearch();
    },
    handleClick() {
      this.onSearch();
    },
    onSure() {
      let that = this;
      this.$confirm('是否确认?', '提示', {
        confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
      }).then(async () => {
        // let params = { platform: that.filter.platforms, yearMonthDay: that.filter.yearMonthDay };
        let params = { platform: that.filter.platforms, startTime: that.filter.startTime, endTime: that.filter.endTime, };

        const res = await newContinuousNoProfitNoticeSaveAsync(params)
        if (!res?.success) { return }
        this.$message({ type: 'success', message: '正在逐天计算中，请稍后查看结果！' });
      })
    },
    async onchangeplatform (val) { 
      if(val == ''){ 
        this.filter.shopCode=null;
        this.shopList=[]; 
        this.onSearch();
        return;
      }

      this.filter.platforms = val;
      const res1 = await getshop({ platforms: val, CurrentPage: 1, PageSize: 10000 });
      this.shopList = res1.data.list;

      this.filter.shopCode=null;
      this.onSearch();
    }
  },
};
</script>

<style lang="scss" scoped>
.a{
  
}
</style>
