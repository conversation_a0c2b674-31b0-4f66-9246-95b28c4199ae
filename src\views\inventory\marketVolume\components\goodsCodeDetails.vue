<template>
    <MyContainer style="padding: 10px;" v-loading="loading">
        <div class="top_Info">
            <div style="display: flex;width: 100%">
                <div class="top_Info_item">
                    <div class="top_Info_item_title item">款式编码</div>
                    <el-tooltip effect="dark" :content="ruleForm.styleCode" placement="top-start">
                        <div class="item">{{ ruleForm.styleCode }}</div>
                    </el-tooltip>
                </div>
                <div class="top_Info_item">
                    <div class="top_Info_item_title item">状态</div>
                    <el-tooltip effect="dark" :content="ruleForm.statusName" placement="top-start">
                        <div class="item">{{ ruleForm.statusName }}</div>
                    </el-tooltip>
                </div>
            </div>
            <div style="display: flex;width: 100%">
                <div class="top_Info_item">
                    <div class="top_Info_item_title item">商品编码</div>
                    <el-tooltip effect="dark" :content="ruleForm.goodsCode" placement="top-start">
                        <div class="item">{{ ruleForm.goodsCode }}</div>
                    </el-tooltip>
                </div>
                <div class="top_Info_item">
                    <div class="top_Info_item_title item">商品名称</div>
                    <el-tooltip effect="dark" :content="ruleForm.goodsName" placement="top-start">
                        <div class="item">{{ ruleForm.goodsName }}</div>
                    </el-tooltip>
                </div>
            </div>
            <div style="display: flex;width: 100%">
                <div class="top_Info_item">
                    <div class="top_Info_item_title item">采购</div>
                    <el-tooltip effect="dark" :content="ruleForm.brandName" placement="top-start">
                        <div class="item">{{ ruleForm.brandName }}</div>
                    </el-tooltip>
                </div>
                <div class="top_Info_item">
                    <div class="top_Info_item_title item">聚水潭成本价</div>
                    <el-tooltip effect="dark" :content="String(ruleForm.cost)" placement="top-start">
                        <div class="item">{{ ruleForm.cost }}</div>
                    </el-tooltip>
                </div>
            </div>

        </div>
        <el-form label-position="top" label-width="80px" :model="ruleForm" :rules="rules" ref="ruleForm">
            <el-form-item label="拼多多:" prop="pddUrl">
                <div class="top_Info_pdd">
                    <div>拼多多销量链接*</div>
                    <el-input v-model.trim="ruleForm.pddUrl" placeholder="请输入链接" maxlength="4000" clearable
                        class="publicCss top_Info_pdd_link" style="width: 100%;" />
                </div>
            </el-form-item>
            <el-form-item label="拼多多销量截图:" prop="pddPics">
                <div class="chatPicUrl">
                    <uploadimgFile ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true"
                        :uploadInfo="pddChatUrls" :keys="[1, 1]" v-if="uploadimgFileVisable" @callback="getPddImg"
                        :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                    <span class="picTips">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
                </div>
            </el-form-item>
            <el-form-item label="天猫:" prop="tianMaoUrl">
                <div class="top_Info_pdd">
                    <div>天猫销量链接*</div>
                    <el-input v-model.trim="ruleForm.tianMaoUrl" placeholder="请输入链接" maxlength="4000" clearable
                        class="publicCss top_Info_pdd_link" style="width: 100%;" />
                </div>
            </el-form-item>
            <el-form-item label="天猫销量截图:" prop="tianMaoPics">
                <div class="chatPicUrl">
                    <uploadimgFile ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true" :uploadInfo="tmChatUrls"
                        :keys="[1, 1]" v-if="uploadimgFileVisable" @callback="getTmImg" :imgmaxsize="9" :limit="9"
                        :multiple="true">
                    </uploadimgFile>
                    <span class="picTips">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
                </div>
            </el-form-item>
            <el-form-item label="聚水潭成本价对比:" prop="compareType">
                <el-select v-model="ruleForm.compareType" clearable placeholder="对比">
                    <el-option label="分批含运" value="分批含运" />
                    <el-option label="分批不含运" value="分批不含运" />
                    <el-option label="按月备货含运" value="按月备货含运" />
                    <el-option label="按月备货不含运" value="按月备货不含运" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商信息:">
                <div class="top_Info_pdd">
                    <!-- <span style="color: red;">*</span> -->
                    <div>市场体量 </div>
                    <el-input-number v-model.trim="ruleForm.volume" placeholder="请输入市场体量" :min="0" :max="99999999"
                        :controls="false" class="publicCss top_Info_pdd_link" style="width: 100%;" :precision="0" />
                </div>
                <div class="top_Info_pdd">
                    <div>供应商名称 <span style="color: red;">*</span> </div>
                    <el-input v-model.trim="ruleForm.provider" placeholder="请输入供应商名称" maxlength="4000" clearable
                        class="publicCss top_Info_pdd_link inputWord" style="width: 100%;text-align: center;" />
                </div>
                <div class="top_Info_pdd">
                    <div>分批含运</div>
                    <el-input-number v-model="ruleForm.batchInTranCost" placeholder="请输入分批含运价格" :min="0" :max="1000000"
                        :controls="false" class="publicCss top_Info_pdd_link" style="width: 100%;" :precision="4" />
                </div>
                <div class="top_Info_pdd">
                    <div>分批不含运 </div>
                    <el-input-number v-model="ruleForm.batchNotTranCost" placeholder="请输入分批不含运价格" :min="0"
                        :max="1000000" :controls="false" class="publicCss top_Info_pdd_link" style="width: 100%;"
                        :precision="4" />
                </div>
                <div class="top_Info_pdd">
                    <div>按月备货含运 <span style="color: red;">*</span> </div>
                    <el-input-number v-model="ruleForm.monthInTranCost" placeholder="请输入按月备货含运价格" :min="0"
                        :max="1000000" :controls="false" class="publicCss top_Info_pdd_link" style="width: 100%;"
                        :precision="4" />
                </div>
                <div class="top_Info_pdd">
                    <div>按月备货不含运 </div>
                    <el-input-number v-model="ruleForm.monthNotTranCost" placeholder="请输入按月备货不含运价格" :min="0"
                        :max="1000000" :controls="false" class="publicCss top_Info_pdd_link" style="width: 100%;"
                        :precision="4" />
                </div>
            </el-form-item>
            <el-form-item label="报价凭证:" prop="voucher">
                <div class="chatPicUrl">
                    <uploadimgFile ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true"
                        :uploadInfo="bjpzChatUrls" :keys="[1, 1]" v-if="uploadimgFileVisable" @callback="getBjpzImg"
                        :imgmaxsize="9" :limit="9" :multiple="true">
                    </uploadimgFile>
                    <span class="picTips">提示:点击灰框可直接贴图,最多可上传9张！！！</span>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="btnGroup">
                    <el-button @click="close">关闭</el-button>
                    <el-button type="primary" @click="submit('ruleForm')" v-throttle="1500">保存</el-button>
                </div>
            </el-form-item>
        </el-form>


    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import {
    getVolumeGoodsInfoByGoodsCode,
    saveVolumeGoodsInfo
} from "@/api/inventory/volumeGoods";
const ys = {
    // 'volume': '市场体量',
    // 'batchInTranCost': '分批含运',
    // 'batchNotTranCost': '分批不含运',
    'monthInTranCost': '按月备货含运',
    // 'monthNotTranCost': '按月备货不含运',
}

export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, uploadimgFile,
    },
    props: {
        goodsCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            ruleForm: {
                goodsCode: null,
                goodsName: null,
                styleCode: null,
                brandId: null,
                brandName: null,
                cost: null,
                status: null,
                pddUrl: null,
                pddPics: null,
                tianMaoUrl: null,
                tianMaoPics: null,
                volume: null,
                provider: null,
                batchInTranCost: null,
                batchNotTranCost: null,
                monthInTranCost: null,
                monthNotTranCost: null,
                voucher: null,
                claimBrandId: null,
                claimBrandName: null,
                modifiedUserId: null,
                modifiedUserName: null,
                modifiedTime: null,
                compareType: null
            },
            loading: true,
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            uploadimgFileVisable: false,
            pddChatUrls: [],//拼多多销量截图
            tmChatUrls: [],//天猫销量截图
            bjpzChatUrls: [],//报价凭证
            rules: {
                // pddUrl: [
                //     { required: true, message: '请输入拼多多销量链接', trigger: 'blur' }
                // ],
                // pddPics: [
                //     { required: true, message: '请上传拼多多销量截图', trigger: 'blur' }
                // ],
                // tianMaoUrl: [
                //     { required: true, message: '请输入天猫销量链接', trigger: 'blur' }
                // ],
                // tianMaoPics: [
                //     { required: true, message: '请上传天猫销量截图', trigger: 'blur' }
                // ],
                voucher: [
                    { required: true, message: '请上传报价凭证', trigger: 'blur' }
                ],
                compareType: [
                    { required: true, message: '请选择聚水潭成本价对比', trigger: 'blur' }
                ],
            }
        }
    },
    async mounted() {
        await this.getList()
        this.uploadimgFileVisable = true
    },
    methods: {
        getPddImg(data) {
            if (data) {
                this.pddChatUrls = data ? data : []
                this.ruleForm.pddPics = data.map(item => item.url).join(',')
            }
        },
        getTmImg(data) {
            if (data) {
                this.tmChatUrls = data ? data : []
                this.ruleForm.tianMaoPics = data.map(item => item.url).join(',')
            }
        },
        getBjpzImg(data) {
            if (data) {
                this.bjpzChatUrls = data ? data : []
                this.ruleForm.voucher = data.map(item => item.url).join(',')
            }
        },
        close() {
            this.$emit('close')
        },
        submit(formName) {
            if (!this.ruleForm.provider) return this.$message.error('供应商名称不能为空')
            // const verify = ['volume', 'batchInTranCost', 'batchNotTranCost', 'monthInTranCost', 'monthNotTranCost']
            // const verify = ['monthInTranCost']
            // for (let key of verify) {
            //     if (this.ruleForm[key] <= 0 || this.ruleForm[key] === null || this.ruleForm[key] === undefined) return this.$message.error(ys[key] + '不能小于等于0或为空')
            // }
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isConfirm = null
                    const { data, success } = await saveVolumeGoodsInfo(this.ruleForm)
                    if (success && data) {
                        this.$confirm(`${data}`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(async () => {
                            this.ruleForm.isConfirm = 1
                            const { data, success } = await saveVolumeGoodsInfo(this.ruleForm)
                            if (success) {
                                this.$message.success('保存成功')
                                this.$emit('getList')
                                this.$emit('close')
                            }
                        }).catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消'
                            });
                        });
                    } else if (success && !data) {
                        this.$message.success('保存成功')
                        this.$emit('getList')
                        this.$emit('close')
                    } else {
                        this.$message.error('保存失败')
                    }
                } else {
                    return false;
                }
            });
        },
        async getList() {
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getVolumeGoodsInfoByGoodsCode({ goodsCode: this.goodsCode })
                if (success) {
                    // const ys = {
                    //     // 'volume': '市场体量',
                    //     // 'batchInTranCost': '分批含运',
                    //     // 'batchNotTranCost': '分批不含运',
                    //     'monthInTranCost': '按月备货含运',
                    //     // 'monthNotTranCost': '按月备货不含运',
                    // }

                    data.batchInTranCost = data.batchInTranCost === null ? undefined : data.batchInTranCost
                    data.batchNotTranCost = data.batchNotTranCost === null ? undefined : data.batchNotTranCost
                    data.monthInTranCost = data.monthInTranCost === null ? undefined : data.monthInTranCost
                    data.monthNotTranCost = data.monthNotTranCost === null ? undefined : data.monthNotTranCost
                    this.ruleForm = data
                    this.pddChatUrls = data.pddPics ? data.pddPics.split(',').map((item, i) => ({ url: item, name: `拼多多截图${i + 1}` })) : []
                    this.tmChatUrls = data.tianMaoPics ? data.tianMaoPics.split(',').map((item, i) => ({ url: item, name: `天猫截图${i + 1}` })) : []
                    this.bjpzChatUrls = data.voucher ? data.voucher.split(',').map((item, i) => ({ url: item, name: `报价凭证${i + 1}` })) : []
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取数据失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.top_Info {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 10px;

    .top_Info_item {
        display: flex;
        flex: 1;

        .top_Info_item_title {
            text-align: center;
        }

        .item {
            font-size: 14px;
            flex: 1;
            display: flex;
            align-items: center;
            height: 30px;
            border: 1px solid #dcdfe6;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

    }
}

.top_Info_pdd_title {
    font-size: 16px;
    margin: 10px 0;
}

.top_Info_pdd {
    display: flex;
    justify-content: center;
    align-items: center;

    div {
        width: 50%;
        height: 30px;
        border: 1px solid #dcdfe6;
        line-height: 30px;

        &:first-child {
            text-align: center;
            border-right: none;
        }
    }

    .top_Info_pdd_link {
        flex: 1;
        text-align: center;
    }

    .inputWord {
        ::v-deep .el-input__inner {
            text-align: center;
            padding-right: 8px;
        }
    }

}

.publicCss {
    ::v-deep .el-input__inner {
        border: none;
    }
}

.chatPicUrl {
    position: relative;

    .picTips {
        position: absolute;
        top: 0;
        left: 150px;
        color: #ff0000;
        font-size: 14px;
    }
}

.btnGroup {
    display: flex;
    justify-content: end;
}
</style>
