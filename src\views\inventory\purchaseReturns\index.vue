<template>
    <my-container>
        <el-tabs v-model="activeName" style="height:95%;">
            <el-tab-pane label="汇总" name="tab0" style="height: 100%;" lazy>
                <SummaryPage ref="purchrasedeptrolepermission" />
            </el-tab-pane>
            <el-tab-pane label="明细" name="tab1" style="height: 100%;" lazy>
                <detailsPage ref="purchrasedeptrolepermission" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import purchrasedeptrolepermission from '@/views/inventory/purchrasedeptpermission/purchrasedeptrolepermission';
import SummaryPage from './components/SummaryPage.vue'
import detailsPage from './components/detailsPage.vue'
export default {
    name: "purchrasedeptpermissionIndex",
    components: {
        MyContainer,
        detailsPage,
        SummaryPage,
    },
    data() {
        return {
            activeName: 'tab0',
        };
    },
    mounted() {
    },
    methods: {
    },

};
</script>
<style lang="scss" scoped></style>
