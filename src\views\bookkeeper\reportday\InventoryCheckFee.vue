<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button  style="padding:0;margin:0;">
                    <el-date-picker v-model="filter.timeRange" type="daterange" format="yyyyMMdd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:230px;">
                    </el-date-picker>
                </el-button>                               
                <el-button  style="padding:0;margin:0;">
                    <el-input v-model.trim="filter.proCode" placeholder="产品ID" :clearable="true" ></el-input>
                </el-button>
                <el-button  style="padding:0;margin:0;">
                     <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" :clearable="true" ></el-input>
                </el-button>

                <el-button type="primary" @click="onSearch">查询</el-button>
              
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {
    getInventoryCheckFee
} from '@/api/inventory/warehouse';


import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";

const tableCols =[
     {istrue:true,prop:'yearMonthDay',label:'年月日', width:'350',sortable:'custom'},
      {istrue:true,prop:'proCode',label:'产品ID',sortable:'custom', width:'350',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
      {istrue:true,prop:'goodsCode',label:'商品编码',sortable:'custom', width:'350'},
      {istrue:true,prop:'amontAvg',label:'盘亏金额', width:'350',sortable:'custom',formatter:(row)=> row.amontAvg},
     
];

const tableHandles=[
      
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
          
            that:this, 
            myfilter:{
                //proCode:null,
                // timeRange:[startDate,endDate],
                // startDate:null,
                // endDate:null,
                orderNo:null,
                orderNoInner:null,
                expressNo:null
            },                      
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            proCode:null,
            timeRange:[startDate,endDate],
            startDate:null,
            endDate:null,
            goodsCode:null
          
        },   
    },

    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            this.filter.startDate =null;
            this.filter.endDate =null;
            this.filter.StartTimeStockIn =null;
            this.filter.EndTimeStockIn =null;
            if (this.filter.timeRange && this.filter.timeRange.length>0) {
                this.filter.startDate = this.filter.timeRange[0];
                this.filter.endDate = this.filter.timeRange[1];
            }
              
            this.filter
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
            const params = {...pager,...this.pager,...this.filter,...this.myfilter};       
            const res = await getInventoryCheckFee(params).then(res=>{
                that.total = res.data?.total;
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>