<!-- 发送消息通知  -->
<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <el-row>
                <el-col :span="8" style="padding: 20px 20px;">                    
                    <!--表单-->                   
                    <el-form ref="form" :model="form" :rules="formRules" label-width="110px" label-position="right" :disabled="!formEditMode">
                        <el-row>
                            <el-col :span="24" style="text-align: center;">
                               <p><strong>Erp公告发送</strong></p>  
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="消息类别" prop="msgType">
                                    <el-select v-model="form.msgType">
                                        <el-option value="sysNotice" label="系统通知"></el-option>
                                        <el-option value="info" label="普通消息"></el-option>
                                        <el-option value="success" label="成功消息"></el-option>
                                        <el-option value="error" label="失败消息"></el-option>
                                        <el-option value="GetSystemDeviceInfo" label="硬件消息"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="标题：" prop="msgTitle">
                                    <el-input v-model="form.msgTitle"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="消息内容：" prop="msgContent">
                                    <el-input v-model="form.msgContent" type="textarea" :rows="3"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="" >
                                    <el-button type="primary" @click="SendNotice(true)">发送</el-button>       
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="接收人" prop="receiver">
                                    <el-select v-model="form.receiver" filterable placeholder="请选择">
                                        <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </el-form>
                </el-col>
                <el-col :span="16"  style="padding: 20px 20px; border-left: solid 1px silver;">
                    <!--表单-->                    
                    <el-form ref="formSiteMsg" :model="formSiteMsg" :rules="formRulesSiteMsg" label-width="130px" label-position="right" :disabled="!formEditMode">
                        <el-row>
                            <el-col :span="24" style="text-align: center;">
                               <p> <strong>ERP站内信发送</strong></p>
                            </el-col>
                        </el-row>
                        <el-row>                            
                            <el-col :span="24">
                                <el-form-item label="消息类型：" prop="SubType">
                                    <el-input v-model="formSiteMsg.SubType"></el-input>
                                    <span>比如：普通消息；采购单-提交审批失败；违规扣款-申诉通知；等等</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="标题：" prop="Title">
                                    <el-input v-model="formSiteMsg.Title"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="消息内容：" prop="Content">
                                    <el-input v-model="formSiteMsg.Content" type="textarea" :rows="3"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                       
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="主题数据(json)：" prop="SubExtData">
                                    <el-input v-model="formSiteMsg.SubExtData" type="textarea" :rows="1"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="回调队列：" prop="SubExtQueueName">
                                    <el-input v-model="formSiteMsg.SubExtQueueName"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="链接类型：" prop="LinkType">
                                    <el-select v-model="formSiteMsg.LinkType">
                                        <el-option :value="0" label="无链接"></el-option>
                                        <el-option :value="1" label="ERP内菜单页签"></el-option>
                                        <el-option :value="2" label="网页新弹窗"></el-option>                                       
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="链接路径：" prop="LinkPath">
                                    <el-input v-model="formSiteMsg.LinkPath"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="未读转小韵助理：" prop="UnRead2Ding">
                                    <el-select v-model="formSiteMsg.UnRead2Ding">
                                        <el-option :value="0" label="不需要"></el-option>
                                        <el-option :value="1" label="需要"></el-option>                                                                    
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="多少分钟转小韵：" prop="UnRead2DingMinutes">
                                    <el-input-number v-model="formSiteMsg.UnRead2DingMinutes"></el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="需要接收人确认：" prop="NeedExec">
                                    <el-select v-model="formSiteMsg.NeedExec">
                                        <el-option :value="0" label="不需要"></el-option>
                                        <el-option :value="1" label="需要"></el-option>                                                                    
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="需要回复内容：" prop="NeedExecRemark">
                                    <el-select v-model="formSiteMsg.NeedExecRemark">
                                        <el-option :value="0" label="不需要"></el-option>
                                        <el-option :value="1" label="需要"></el-option>                                                                    
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="接收人确认文本：" prop="ExecBtnText">
                                    <el-input v-model="formSiteMsg.ExecBtnText"></el-input>
                                    <span>用英文逗号分隔，如：确定,取消 或 同意,拒绝 ，前面放通过后面放拒绝</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="接收人处理类型：" prop="IsOrExec">
                                    <el-select v-model="formSiteMsg.IsOrExec">
                                        <el-option :value="0" label="或批"></el-option>
                                        <el-option :value="1" label="会签"></el-option>                                                                    
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="接收人：" prop="ReceiverDingNames">
                                    <el-input readonly v-model="formSiteMsg.ReceiverDingNames">
                                        <slot name="prepend">
                                            <el-button slot="prepend" @click="onSelectSiteMsgRecUser">选择</el-button>
                                        </slot>
                                    </el-input>
                                </el-form-item>
                            </el-col>                            
                        </el-row>


                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="" >
                                    <el-button type="primary" @click="SendNoticeSiteMsg">发送</el-button>       
                                </el-form-item>
                            </el-col>
                        </el-row>
                       

                    </el-form>
                </el-col>
                
            </el-row>

        </template>       

    </my-container>
</template>
<script>  



    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";

    import { PostNoticeAsync,PostSiteMsgAsync } from '@/api/admin/common';
    import {  getUserListPage } from '@/api/admin/user'


    export default {
        name: "PostNoticeForm",
        components: { MyContainer, MyConfirmButton },
        data() {
            return {
                that: this,
                options:[{value:0,label:'所有人'}],
                form: {

                },
                pageLoading: false,
                formEditMode: true,//是否编辑模式              
                mode: 1,
                formSiteMsg:{
                    Title:"",
                    Content:"",
                    SubType:"普通消息",
                    SubTypes:[],
                    SubExtData:"",
                    SubExtQueueName:"",
                    LinkType:0,
                    LinkPath:"",
                    UnRead2Ding:0,
                    UnRead2DingMinutes:0,
                    NeedExec:0,
                    NeedExecRemark:0,
                    ExecBtnText:"",                    
                    IsOrExec:0,
                    SenderDingId:"",
                    ReceiverIdType:0,                                   
                    ReceiverDingNames:"",
                    ReceiverDingIds:"",
                    SendTime:new Date().toISOString(),
                },
                
            };
        },
        async mounted() {
            this.$PageReqLog("平台管理","发送公告","发送公告","查看");
        },
        computed: {
            formRules() {
                return {
                    "receiver": [{ required: true, message: '请选择接收人员', trigger: 'change' }],
                    "msgTitle": [{ required: true, message: '请填写消息标题', trigger: 'blur' }],
                    "msgContent": [{ required: true, message: '请填写消息内容', trigger: 'blur' }],
                    "msgType": [{ required: true, message: '请选择消息类型', trigger: 'change' }],
                }
            },
            formRulesSiteMsg(){
                let ruleObj={};
                ruleObj["Title"]=[{ required: true, message: '请填写消息主题', trigger: 'blur' }];
                ruleObj["Content"]=[{ required: true, message: '请填写消息内容', trigger: 'blur' }];
                ruleObj["SubType"]=[{ required: true, message: '请填写消息类型', trigger: 'blur' }];
                ruleObj["LinkType"]=[{ required: true, message: '请选择链接类型', trigger: 'change' }];
                if(this.formSiteMsg.LinkType>0){
                    ruleObj["LinkPath"]=[{ required: true, message: '请填写链接地址', trigger: 'blur' }];
                }
                ruleObj["UnRead2Ding"]=[{ required: true, message: '请选择未读是否转小韵助理', trigger: 'change' }];
                if(this.formSiteMsg.UnRead2Ding==1){
                    ruleObj["UnRead2DingMinutes"]=[{ required: true, message: '请填写未读转小韵助理时间（分钟）', trigger: 'blur' }];
                }
                ruleObj["NeedExec"]=[{ required: true, message: '请选择是否需要需要接收人确认', trigger: 'change' }];
                ruleObj["NeedExecRemark"]=[{ required: true, message: '请选择是否需要需要接收人填写回复', trigger: 'change' }];
                if(this.formSiteMsg.NeedExec==1){                    
                    ruleObj["ExecBtnText"]=[{ required: true, message: '请填写接收人回复确认按钮文字！', trigger: 'blur' }];
                }
                ruleObj["IsOrExec"]=[{ required: true, message: '请选择是否需要接收人确认', trigger: 'change' }];
                ruleObj["ReceiverDingNames"]=[{ required: true, message: '请选择接收人', trigger: 'change' }];
               

                return ruleObj;
            }
        },
        methods: {
            async afterSelectSiteMsgRecUser(users) {
                //console.log(users);
                this.formSiteMsg.ReceiverDingIds = users.map(item=>item.ddUserId);
                this.formSiteMsg.ReceiverDingNames = users.map(item=>item.userName).join(',');
            },
            async onSelectSiteMsgRecUser() {                
                let _this = this
                this.$showDialogform({
                    path: `@/views/base/arrPublicDialogPage.vue`,
                    title: '选择人员',
                    autoTitle: false,
                    args: { isMore:true },
                    height: 300,
                    width: '1300px',
                    callOk: _this.afterSelectSiteMsgRecUser
                })
            },
            onClose() {
                this.$emit('close');
            },
            async onSave(isClose) {
                if (await this.save()) {
                    this.$emit('afterSave');
                    if (isClose)
                        this.$emit('close');
                }
            },
            async loadData() {

            },
            async SendNotice() {
                let self = this;
                this.$PageReqLog("平台管理","发送公告","发送公告","新增");
                try {
                    await self.$refs["form"].validate((v) => {
                        if (v) {
                            PostNoticeAsync(self.form).then((rlt)=>{
                                  if (rlt && rlt.success) {
                                        this.$message.success('消息发送成功!');
                                    }
                                    this.pageLoading = false;

                                    return (rlt && rlt.success);
                            });                          
                        }
                    });
                } catch (ex) {
                }
            },
            async SendNoticeSiteMsg(){
                let self = this;
                self.formSiteMsg.SendTime=new Date().toISOString();
                self.formSiteMsg.SubTypes=self.formSiteMsg.SubType.split("-");
                console.log(self.formSiteMsg);
                this.$PageReqLog("平台管理","发送公告","发送公告","新增");
                try {
                    await self.$refs["formSiteMsg"].validate((v) => {
                        if (v) {
                            PostSiteMsgAsync(self.formSiteMsg).then((rlt)=>{
                                if (rlt && rlt.success) {
                                        this.$message.success('消息发送成功!');
                                    }
                                    this.pageLoading = false;

                                    return (rlt && rlt.success);
                            });                          
                        }
                    });
                } catch (ex) {
                }
            },
            async remoteSearchUser (parm) {
                if (!parm) {                  
                    return;
                }
                var dynamicFilter = { field: 'nickName', operator: 'Contains', value: parm }
                var options = [];
                const res = await getUserListPage({ currentPage: 1, pageSize: 50, dynamicFilter: dynamicFilter })
                res?.data?.list.forEach(f => {
                    options.push({ value: f.id, label: f.nickName })
                })
                this.autoform.fApi.getRule('userId').options = options;
            },
        },
    };
</script>
