<template>
  <MyContainer>
    <template #header>
      <div class="top" @click="getFocus">
        <el-select v-model="query.wmsId" placeholder="发货仓" class="publicCss" clearable @change="changeWareHouse">
          <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-input ref="input" v-model.trim="query.goodsCode" placeholder="商品编码" maxlength="200" clearable
          class="publicCss" @keyup.enter.native="doScan" />
        <el-button v-throttle="50" type="primary" style="margin-right: 10px;" @click="doScan">扫码</el-button>
      </div>
    </template>
    <div class="main" @click="getFocus">
      <div v-for="(item, i) in 2" :key="i" :class="['main_item', index == i ? 'active' : '']">
        <div class="main_item_top">
          <div class="item_fixed_top"><span>{{ i + 1 }}</span></div>
          <vxetablebase :id="'ybScanCode_index202408041758_1'" v-if="i == 0" ref="table" v-loading="pageLoading" :that="that" :is-index="true"
            :hasexpand="true" :index-width="40" :tablefixed="true" :table-data="passageOne" :table-cols="tableCols"
            :is-selection="false" :is-select-column="false" style="width: 100%;  margin: 0" :height="'100%'"
            class="already" />
          <vxetablebase :id="'ybScanCode_index202408041758_2'" v-if="i == 1" ref="table" v-loading="pageLoading" :that="that" :is-index="true"
            :hasexpand="true" :index-width="40" :tablefixed="true" :table-data="passageTwo" :table-cols="tableCols2"
            :is-selection="false" :is-select-column="false" style="width: 100%;  margin: 0" :height="'100%'"
            class="already" />
        </div>
        <div v-if="i == 0" class="main_item_bottom">共{{ passageOne.length }}件</div>
        <div v-if="i == 1" class="main_item_bottom">共{{ passageTwo.length }}件</div>
      </div>
      <div class="surplusOrders">
        <template v-if="surplusOrders.length > 0">
          <el-card v-for="surplusOrder in surplusOrders" :key="surplusOrder.id" shadow="hover" class="surplusOrder">
            <div class="surplusOrderContent" @click.stop>
              {{ surplusOrder.count }}
              <div>{{ surplusOrder.goodsCode }}</div>
              <div style="display: flex;align-items: center;flex-wrap: wrap;">
                <span>您可以连续打印</span>
                <el-input-number v-model="surplusOrder.count" :min="1" :max="surplusOrder.surplusOrderCount" :step="1"
                  @change="forceUpdate" @keyup.enter.native="doScanMulti(surplusOrder)" /> <span>张面单，</span>
                <el-button @click="doScanMulti(surplusOrder)">打印</el-button>
              </div>
            </div>
            <div class="scanBtn">
              <el-button v-throttle="50" v-for="(item, i) in 10" type="primary"
                @click="doScanMulti(surplusOrder, i + 1)" :disabled="i > surplusOrder.count - 1" :key="i"
                style="width:18%;margin-left: 0;">打印{{ i + 1
                }}张</el-button>
            </div>
          </el-card>
        </template>
      </div>
    </div>
    <audio ref="one" src="../../../../static/audio/一号通道.mp3" />
    <audio ref="two" src="../../../../static/audio/二号通道.mp3" />
    <audio ref="five" src="../../../../static/audio/已扫码.mp3" />
    <audio ref="six" src="../../../../static/audio/扫码失败.mp3" />
  </MyContainer>
</template>
<script>
import MyContainer from '@/components/my-container'
import { scan, rpaPrint, getWcScanSendWmses } from '@/api/vo/prePackScan'
import vxetablebase from '@/components/VxeTable/yh_vxetable.vue'
const tableCols = [
  { istrue: true, align: 'left', label: '商品编码', prop: 'goodsCode', width: 'auto' },
  { istrue: true, align: 'right', label: '内部订单号', prop: 'orderNoInner', width: 'auto' },
  {
    istrue: true, align: 'right', label: '面单', width: 'auto', type: 'button', btnList: [
      { label: '补打', handle: (that, row) => that.printPage(row.id) }
    ]
  }
]
const tableCols2 = [
  { istrue: true, align: 'left', label: '商品编码', prop: 'goodsCode', width: 'auto' },
  { istrue: true, align: 'right', label: '内部订单号', prop: 'orderNoInner', width: 'auto' }
]
export default {
  name: 'OperateUser',
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      pageLoading: false,
      passageOne: [], // 通道一
      passageTwo: [], // 通道二
      tableCols,
      tableCols2,
      that: this,
      index: null,
      isSecondScan: false,
      wareHouseList: [],
      query: {
        goodsCode: null,
        wmsId: null,
        wmsName: null,
        count: 1
      },
      surplusOrders: []
    }
  },
  async mounted() {
    await this.getWareHouse()
    this.getFocus()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    changeWareHouse(e) {
      this.query.wmsName = e ? this.wareHouseList.find(item => item.wms_co_id === e).name : null
    },
    async getWareHouse() {
      const params = {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false
      }
      const { data } = await getWcScanSendWmses(params)
      this.wareHouseList = data
    },
    async printPage(id) {
      const { success } = await rpaPrint({ id })
      if (success) {
        this.$message.success('打印成功')
      } else {
        this.$message.error('打印失败')
      }
    },
    async doScan() {
      // 没有仓库就报错
      if (!this.query.wmsId) return this.$message.error('发货仓为空')
      if (!this.query.goodsCode) return this.$message.error('商品编码为空')
      if (!/^[a-zA-Z0-9-]+$/.test(this.query.goodsCode)) return this.$refs.six.play()
      await this.doScaning(this.query)
    },
    async doScanMulti({ goodsCode, wmsId, wmsName, count }, val) {
      var option = { goodsCode, wmsId, wmsName, count }
      if (val) {
        option.count = val
      }
      await this.doScaning(option)
    },

    async doScaning(option) {
      this.pageLoading = true
      const { data, success } = await scan(option)
      if (success) {
        this.index = data.status - 1
        this.pageLoading = false
        const map = {
          1: {
            data: this.passageOne,
            fn: () => this.$refs.one.play()
          },
          2: {
            data: this.passageTwo,
            fn: () => this.$refs.two.play()
          }
        }
        map[data.status].data.unshift(data)
        this.query.goodsCode = null
        map[data.status].fn()
        this.getFocus()

        if (data.surplusOrderCount > 0 && data.status == 1) {
          this.surplusOrders = this.surplusOrders.filter(a => a.goodsCode !== data.goodsCode)
          data.count = data.surplusOrderCount
          this.surplusOrders.unshift(data)
          if (this.surplusOrders.length > 1) {
            this.surplusOrders.pop()
          }
        }
      }
    },
    getFocus() {
      this.$refs.input.focus()
    }
  }
}
</script>
<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

.main {
  height: 85vh;
  width: 100%;
  display: flex;
  padding: 5px;
  box-sizing: border-box;
  justify-content: start;
  //边框线合并
  gap: 10px;

  .main_item {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    box-sizing: border-box;
    border: 1px solid #ccc;

    .main_item_top {
      flex: 1;
      display: flex;
      font-size: 16px;
      box-sizing: border-box;
      position: relative;
      flex-direction: column;
      overflow-y: hidden;

      .item_fixed_top {
        position: absolute;
        top: 5px;
        left: 0px;
        width: 100%;
        // height: 30px;
        z-index: 1000;
        font-weight: 700;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        border-top-left-radius: 0px;

        span {
          font-size: 16px;
          text-align: center;
          width: 30px;
          height: 30px;
          background-color: #409EFF;
          border-radius: 50%;
          line-height: 30px;
          margin: auto;
        }
      }
    }

    .main_item_bottom {
      height: 40px;
      width: 100%;
      display: flex;
      justify-content: end;
      font-size: 14px;
      padding: 10px;
      box-sizing: border-box;
    }
  }

  .surplusOrders {
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
    flex-direction: column;

    .surplusOrder {
      margin-bottom: 10px;
      box-shadow: 0 0 10px 0 #f56c6c;

      .surplusOrderContent {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
      }
    }
  }
}

.active {
  //红色盒子阴影
  outline: #409EFF solid 2px;
}

.already ::v-deep .vxe-tools--operate {
  display: none !important;
}

::v-deep .vxe-table--render-default .vxe-cell {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.scanBtn {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
