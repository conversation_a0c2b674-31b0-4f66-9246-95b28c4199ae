<template>
  <container v-loading="pageLoading">
    <template>
      <ces-table ref="table" :that="that" :isIndex="true" :hasexpand="true" @sortchange="sortchange"
        :summaryarry="summaryarry" :tableData="list" :tableCols="tableCols" :isSelection="true" @select="selectchange"
        :tableHandles="tableHandles" @cellclick="cellclick" :loading="listLoading">
        <template slot="extentbtn">
                    <span style="color:red">申诉开放时间为(非扣款时间)责任计算时间起当天17:30至次日10:00，其他时间或者过期均不再开放</span>
                </template>
      </ces-table>
    </template>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
    </template>

    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px"
      v-dialogDrag>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" :isTx="true"
        style="z-index:10000;height:600px" />
    </el-dialog>
    <el-dialog :title="OrderIllegaldetailForYunYingTitle" v-if="OrderIllegaldetailForYunYingVisible" :visible.sync="OrderIllegaldetailForYunYingVisible" width="65%" height="600px" v-dialogDrag>
        <OrderIllegaldetailForYunYing ref="OrderIllegaldetailForYunYing" style="z-index:10000;height:650px" :yyId="yyId" />
    </el-dialog>
  </container>
</template>

<script>
import container from "@/components/my-container/noheader";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import {
  formatLinkProCode,
  formatSendWarehouse,
  formatPlatform,
} from "@/utils/tools";
import {
  getOrderWithholdList,
  exportOrderWithhold,
  importPinOrderIllegal,
  getOrderWithholdTXList,
  exportOrderWithholdTXList
} from "@/api/order/orderdeductmoney";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
import OrderIllegaldetailForYunYing from "@/views/order/orderillegal/OrderIllegaldetailForYunYing.vue";

import store from '@/store'

const tableCols = [
  { istrue: true, prop: 'proCode', label: '宝贝ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
  { istrue: true, prop: 'orderNo', label: '订单编号', width: '170', sortable: 'custom', type: 'click', handle: (that, row) => that.showLogDetail(row) },
  { istrue: true, prop: 'occurrenceTime', label: '扣款日期', width: '100', sortable: 'custom', formatter: (row) => formatTime(row.occurrenceTime, "YYYY-MM-DD") },
  { istrue: true, prop: 'payTime', label: '付款日期', width: '130', sortable: 'custom', formatter: (row) => !row.payTime ? " " : formatTime(row.payTime, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'sendTime', label: '发货日期', width: '150', sortable: 'custom', formatter: (row) => !row.sendTime ? "" : row.sendTime },
  { istrue: true, prop: 'planDeliveryDate', label: '预计发货时间', width: '124', sortable: 'custom', formatter: (row) => !row.planDeliveryDate ? " " : formatTime(row.planDeliveryDate, "YYYY-MM-DD HH:mm") },
  { istrue: true, prop: 'deliveryTotalMinsTxt', label: '发货时长', width: '90', sortable: 'custom'  },
  { istrue: true, prop: 'platform', label: '平台', width: '60', sortable: 'custom', formatter: (row) => !row.platformName ? " " : row.platformName },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '200', sortable: 'custom', formatter: (row) => !row.goodsName ? "" : row.goodsName },
  { istrue: true, prop: 'shopId', label: '店铺', width: '200', formatter: (row) => !row.shopName ? " " : row.shopName },
  { istrue: true, prop: 'sendWarehouseName', label: '发货仓', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'expressNo', label: '快递单号', width: '125', sortable: 'custom',type: 'click', handle: (that, row) => that.onShowLogistics(row)   },
  { istrue: true, prop: 'expressCompany', label: '快递公司', width: '125', sortable: 'custom', formatter: (row) => row.expressCompanyName },
  { istrue: true, prop: 'expressCollectionMins', label: '揽收时长', width: '90', sortable: 'custom', formatter: (row) => row.expressCollectionTxt   },
  { istrue: true, prop: 'expressTags', label: '物流标记', width: '90', sortable: 'custom'  },
  { istrue: true, prop: 'groupId', label: '小组', width: '60', formatter: (row) => !row.groupName ? " " : row.groupName },
  { istrue: true, prop: 'operateSpecialUserId', label: '运营专员', width: '80', formatter: (row) => !row.operateSpecialUserName ? " " : row.operateSpecialUserName},
  { istrue: true, prop: 'amountPaid', label: '金额', width: '60', sortable: 'custom', formatter: (row) => parseFloat(row.amountPaid.toFixed(6)) },
  { istrue: true, prop: 'illegalType', label: '平台原因', width: 'auto', sortable: 'custom', formatter: (row) => !row.illegalTypeName ? " " : row.illegalTypeName },
  { istrue: true, prop: 'zrType1', label: '扣款类型', width: '80', sortable: 'custom'},
  { istrue: true, prop: 'zrType2', label: '扣款原因', width: '120', sortable: 'custom'},

  { istrue: true, prop: 'zrDept', label: '部门', width: '60', sortable: 'custom', formatter: (row) => !row.zrDept ? " " : row.zrDept },
  { istrue: true, prop: 'zrAction', label: '操作', width: '60', sortable: 'custom', formatter: (row) => !row.zrAction ? " " : row.zrAction },
  { istrue: true, prop: 'zrSetTime', label: '责任计算时间', width: '130', sortable: 'custom', formatter: (row) => !row.zrSetTime ? " " : formatTime(row.zrSetTime, "YYYY-MM-DD HH:mm") },
  //{ istrue: true, prop: 'ysLx', label: '预售', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'zrConditionFullName', label: '划责规则', width: 'auto', sortable: 'custom', formatter: (row) => (row.zrConditionFullName ?? "") },
  { istrue: true, prop: 'memberName', label: '责任人', width: '70', sortable: 'custom' },
  { istrue: true, prop: 'memberDeptFullName', label: '钉钉组织', width: '160', sortable: 'custom' },
  // { istrue: true, prop: 'brandName', label: '采购', width: '80', sortable: 'custom' },
  // { istrue: true, prop: 'brandRegion', label: '采购分区', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'zrAppealState', label: '申诉状态', width: '80', formatter: (row) => !row.zrAppealStateText ? " " : row.zrAppealStateText },
  { istrue:true,label:'功能',width:'80',fixed:'right',type:'button',btnList:[
      {
      label:'申诉',
      handle:(that,row)=>that.zrApply(row),
      ishide: (that, row) => { return !row.showZrAppealBtn ; }
      },
      {
        label:'指派',
        permission:'api:order:orderdeductmoney:SetZrMemberCustomize',
        handle:(that,row)=>that.onSetZrMember(row),
        ishide: (that, row) => { return  row.zrAppealState ==1  ; }
      },
      {
        label:'重算',
        handle:(that,row)=>that.zrRecalc(row),
        ishide:(that,row)=>{ return [1,2,3,9].indexOf(row.illegalType)<0  || that.store.getters.userName.indexOf('徐洪鹏')<0;; }
      }
  ]}
];

const tableHandles = [
  { label: "导出淘系扣款详情", handle: (that) => that.onExportDetail() },
  { label: "批量申诉", handle: (that) => that.batchZrApply() },
  { label: "批量指派", handle: (that) => that.onSetZrMemberBatch(), permission:'api:order:orderdeductmoney:SetZrMemberCustomize', },
];

export default {
  name: "YunhanAdminOrderillegaldetail",
  components: {
    cesTable,
    container,
    MyConfirmButton,
    MySearch,
    MySearchWindow,
    OrderActionsByInnerNos, OrderIllegaldetailForYunYing
  },
  props: {
    filter: {},
  },
  data() {
    return {
      that: this,
      store:store,
      list: [],
      platformList: [],
      illegalTypeList: [],
      summaryarry: {},
      pager: { OrderBy: "OccurrenceTime", IsAsc: false },
      filterImport: {
        platform: 1,
        occurrenceTime: formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD"),
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      dialogHisVisible: false,
      orderNo: '',
      selids:[],
      selrows:[],
      yyId: 0,
      OrderIllegaldetailForYunYingTitle: "",
      OrderIllegaldetailForYunYingVisible: false,
    };
  },

  async mounted() {
    await this.setPlatform();
    await this.onSearch();
  },

  methods: {
    //申诉
    async zrApply(row){
        let self=this;
        this.$showDialogform({
            path: `@/views/order/orderillegal/zrApply/OrderDeductZrApplyForm.vue`,
            title: '责任申诉',
            autoTitle:false,
            args: {id:0, orderNo:row.orderNo, occTime:row.occurrenceTime, platform:1,mode:1},
            height: 300,
            width: '80%',
            callOk: self.onSearch
        })
    },
    //重算责任
    async zrRecalc(row){
            let self=this;
            this.$showDialogform({
                            path: `@/views/order/orderillegal/zrApply/OrderDeductZrRecalcForm.vue`,
                            title: '重算责任',
                            autoTitle:false,
                            args: {orderNo:row.orderNo, occTime:row.occurrenceTime, platform:1,mode:2},
                            height: 300,
                            width: '80%',
                            //callOk: self.onSearch
                        })
            },
    //批量申诉
    async batchZrApply(){
        let self=this;
        if(!self.selrows||self.selrows.length<=0)
        {
            this.$message({ message: "请勾选至少一行数据", type: "warning" });
            return;
        }
        this.$showDialogform({
                        path: `@/views/order/orderillegal/zrApply/OrderDeductBatchZrApplyForm.vue`,
                        title: '批量责任申诉',
                        autoTitle:false,
                        args: {selRows:self.selrows,platform:1},
                        height: 600,
                        width: '80%',
                        callOk: self.onSearch
                    })
    },
    //指派
    async onSetZrMember(row) {
        let self = this;
        this.$showDialogform({
            path: `@/views/order/orderillegal/zrApply/OrderDeductZrSetMemberForm.vue`,
            title: '责任指派',
            autoTitle: false,
            args: { ...row, platform: 1, mode: 1 },
            height: 300,
            width: '80%',
            callOk: self.onSearch
        })
    },
    //指派
    async onSetZrMemberBatch() {
      let self=this;
        if(!self.selrows||self.selrows.length<=0)
        {
            this.$message({ message: "请勾选至少一行数据", type: "warning" });
            return;
        }
        this.$showDialogform({
            path: `@/views/order/orderillegal/zrApply/OrderDeductZrSetMemberForm.vue`,
            title: '责任指派',
            autoTitle: false,
            args: { ...self.selrows[0], platform: 1, mode: 1 ,orderList:[...self.selrows]},
            height: 300,
            width: '80%',
            callOk: self.onSearch
        })
    },
    //设置平台,扣款因下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
      var ilrule = await ruleIllegalType();
      this.illegalTypeList = ilrule.options;
    },
    showLogDetail(row) {
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },
    onShowLogistics(row) {
      let self = this;
      this.$showDialogform({
        path: `@/views/order/logisticsWarning/DbLogisticsRecords.vue`,
        title: '物流明细',
        args: { expressNos:row.expressNo },
        height: 300,
      });
    },
    beforeRemove() {
      return true;
    },

    //查询第一页
    async onSearch() {
      if (!this.filter.timerange) {
        this.$message({ message: "请选择日期", type: "warning" });
        return;
      }
      this.$refs.pager.setPage(1);
      await this.getlist();
    },
    //获取查询条件
    getCondition() {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }
      if (this.filter.dutyDept == "采购") this.filter.dutyDept = "采购部";
      if (this.filter.dutyDept == "运营") this.filter.dutyDept = "运营部";
      if (this.filter.dutyDept == "客服") this.filter.dutyDept = "客服部";
      if (this.filter.dutyDept == "仓储") this.filter.dutyDept = "仓储部";
      //this.filter.platform=2
      var pager = this.$refs.pager.getPager();
      var page = this.pager;
      const params = {
        ...pager,
        ...page,
        ...this.filter,
      };

      return params;
    },
    //分页查询
    async getlist() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }

      this.listLoading = true;
      const res = await getOrderWithholdTXList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry = res.data.summary;
      if (this.summaryarry)
        this.summaryarry.amountPaid_sum = parseFloat(
          this.summaryarry.amountPaid_sum.toFixed(6)
        );
      data.forEach((d) => {
        d._loading = false;
      });
      this.list = data;
    },
    //排序查询
    async sortchange(column) {
      if (!column.order) this.pager = {};
      else {
        this.pager = {
          OrderBy: column.prop,
          IsAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      }
      await this.onSearch();
    },
    selectchange: function (rows, row) {
        console.log(rows)
        this.selrows=rows;

        this.selids = [];
        rows.forEach(f => {
            this.selids.push(f.id);
        })
    },
    cellclick(row, column, cell, event) { },
    //导出
    async onExportDetail() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
      var res = await exportOrderWithholdTXList(params);
      loadingInstance.close();
      if (!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系扣款详情_' + new Date().toLocaleString() + '.xlsx')
      aLink.click();
    },
    async showYunYing(row) {
        this.yyId = row.operateSpecialUserId;
        this.OrderIllegaldetailForYunYingTitle = row.operateSpecialUserName+"-历史责任扣款";
        this.OrderIllegaldetailForYunYingVisible = true;
    }
  },
};
</script>

<style lang="scss" scoped></style>
