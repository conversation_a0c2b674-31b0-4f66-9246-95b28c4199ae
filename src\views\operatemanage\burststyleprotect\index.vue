<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="爆款保护" name="first1" style="height: 100%">
                <burststyleprotect ref="burststyleprotect"></burststyleprotect>
            </el-tab-pane>
            <el-tab-pane label="爆款流失" name="first2" style="height: 100%" lazy>
                <burststyleprotectloss ref="burststyleprotectloss"></burststyleprotectloss>
            </el-tab-pane>
            <el-tab-pane label="商品池" name="first3" style="height: 100%" lazy>
                <burststyleprotectstyle ref="burststyleprotectstyle"></burststyleprotectstyle>
            </el-tab-pane>
            <el-tab-pane label="趋势款" name="first4" style="height: 100%"
                v-if="checkPermission('baokuanbaohu.qushikuan')">
                <burststyleprotecttrend ref="burststyleprotecttrend"></burststyleprotecttrend>
            </el-tab-pane>
            <el-tab-pane label="拼多多不可上架编码" name="first5" style="height: 100%"
                v-if="checkPermission('baokuanbaohu.pddbksjbm')">
                <burststyleprotectpddnoup ref="burststyleprotectpddnoup"></burststyleprotectpddnoup>
            </el-tab-pane>
            <el-tab-pane label="爆款排查" name="first6" style="height: 100%"
                v-if="checkPermission('baokuanbaohu.baokuanpaicha')">
                <burststylecheck ref="burststylecheck"></burststylecheck>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import burststyleprotect from "./burststyleprotect.vue";
import burststyleprotectloss from "./burststyleprotectloss.vue";
import burststyleprotectstyle from "./burststyleprotectstyle.vue";
import burststyleprotecttrend from "./burststyleprotecttrend.vue";
import burststyleprotectpddnoup from "./burststyleprotectpddnoup.vue";
import burststylecheck from "./burststylecheck.vue";
export default {
    name: "burststyleprotectindex",//
    components: {
        MyContainer, burststyleprotect, burststyleprotectloss, burststyleprotectstyle, burststyleprotecttrend, burststyleprotectpddnoup, burststylecheck
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    async mounted() {
    },
    methods: {
    },
};
</script>

<style lang="scss" scoped></style>