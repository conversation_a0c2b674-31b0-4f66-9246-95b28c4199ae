<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="queryInfo.timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :clearable="true" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>

        <el-select v-model="queryInfo.costName" clearable filterable placeholder="请选择费用类型" style="margin-right: 5px;width:200px;">
          <el-option v-for="item in costType" :key="item.key" :label="item.value" :value="item.key">
          </el-option>
        </el-select>

        <el-input v-model.trim="queryInfo.shipmentIdentificationNumber"  placeholder="物流单号"  clearable style="margin-right: 5px;width:200px;" />

        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportList" v-if="checkPermission('overseasGodown_Cost_export')">导出</el-button>
      </div>
    </template>

    <template>
      <vxetablebase :id="'overseasGodownCost20240906'" :tablekey="'overseasGodownCost20240906'" :tableData='tableData'
        :tableCols='tableCols' @sortchange='sortchange' :loading='loading' :border='true' :that="that" ref="vxetable"
        :showsummary='true' :summaryarry="summaryarry" @summaryClick='onsummaryClick'>
      </vxetablebase>
    </template>

    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getList" />
    </template>


    <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
      <span>
        <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"
          :loading="buscharDialog.loading"></buschar>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="buscharDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import MyContainer from "@/components/my-container";
import { getShipmentLogisticsAccount, getLogisticsAccountsFeeNames, exportShipmentLogisticsAccount, logisticsAccountsSumChart } from '@/api/kj/cost.js';
import inputYunhan from "@/components/Comm/inputYunhan";
import { pickerOptions } from '@/utils/tools'
import buschar from '@/components/Bus/buschar'
import dayjs from 'dayjs';
import { formatTime } from "@/utils";

const tableCols = [
  // {
  //   istrue: true, sortable: 'custom', prop: 'orderTime', label: '下单时间', width: '200px',
  // },
  {
    istrue: true,
    prop: "accountDate",
    label: "费用日期", 
    width: "100",
    sortable: "custom",
    formatter: (row) => {
      return row.accountDate ? formatTime(row.accountDate, "YYYY-MM-DD") : "";
    },
  },
  { istrue: true, sortable: 'custom', prop: 'shipmentIdentificationNumber', label: '物流单号', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'channelCode', label: '物流渠道', width: '200px', },
  { istrue: true, sortable: 'custom', prop: 'channelName', label: '物流渠道名', width: '300px' },
  { istrue: true, sortable: 'custom', prop: 'referenceNo', label: '跟踪号', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'costName', label: '费用名称', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'unitPrice', label: '单价', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'count', label: '数量', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'totalCost', label: '总额', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'remain', label: '未支付余额', width: '200px',formatter: (row) => row.remain == 0 ? " " : row.remain },
  { istrue: true, sortable: 'custom', prop: 'currency', label: '币种', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'remark', label: '备注', width: '200px' },
  { istrue: true, sortable: 'custom', prop: 'update_time', label: '更新时间', width: '200px' },
 
];

export default {
  name: 'overseasGodown',
  components: { vxetablebase, MyContainer, inputYunhan, buschar },
  props: {
    type: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      that: this,
      queryInfo: {
        timeRanges: [],
        reference_no: null,
        thirdPlatform: this.type,
        page: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: '',
        endTime: ''
      },
      costType: [],
      tableData: [],
      tableCols: tableCols,
      summaryarry: [],
      loading: false,
      total: 0,
      pickerOptions,
      buscharDialog: { visible: false, title: "", data: {}, loading: false },

    };
  },
  async mounted() {
    await this.getCostTypeList()
    await this.getList()
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.queryInfo.page = 1
        this.$refs.pager.setPage(1)
      }
      let page = this.$refs.pager.getPager()
      this.queryInfo.page = page.currentPage
      this.queryInfo.pageSize = page.pageSize
      this.loading = true
      const { data, summary, total } = await getShipmentLogisticsAccount(this.queryInfo)
      this.loading = false
      this.total = total
      this.tableData = data;
      this.summaryarry = summary
    },
    async exportList() {
      this.loading = true
      const { data } = await exportShipmentLogisticsAccount(this.queryInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel;charset=utf-8" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', this.name + '-' + dayjs().format('YYYYMMDD') + '.xlsx')
      aLink.click()
    },
    async getCostTypeList() {

      var params = {
        thirdPlatform: this.type
      }
      const { data } = await getLogisticsAccountsFeeNames(params)
      this.costType = data;
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.queryInfo.orderBy = prop
        this.queryInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async changeTime(e) {
      this.queryInfo.startDate = e ? e[0] : null
      this.queryInfo.endDate = e ? e[1] : null
    },
    async onsummaryClick(property) {
      this.queryInfo.startDate = null;
      this.queryInfo.endDate = null;
      if (this.queryInfo.timeRanges) {
        this.queryInfo.startDate = this.queryInfo.timeRanges[0];
        this.queryInfo.endDate = this.queryInfo.timeRanges[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.queryInfo };
      params.column = property;
      let that = this;
      that.listLoading = true;
      that.buscharDialog.loading = true;
      const res = await logisticsAccountsSumChart(params).then(res => {
        that.buscharDialog.loading = false;
        that.buscharDialog.data = res.data;
        that.buscharDialog.title = res.data.legend[0];
      });
      that.listLoading = false;
      that.buscharDialog.visible = true;
      await that.$refs.buschar.initcharts();

    },
  }
};
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  margin-bottom: 10px;
}
</style>