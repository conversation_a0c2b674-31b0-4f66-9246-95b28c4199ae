<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="140px" class="demo-ruleForm">

                <el-form-item label="点餐总数(份)：" prop="totalOrders">
                    <inputNumberYh v-model="ruleForm.totalOrders" :placeholder="'点餐总数(份)'" class="publicCss" />
                </el-form-item>
                <el-form-item label="异物次数(次)：" prop="foreignObjects">
                    <inputNumberYh v-model="ruleForm.foreignObjects" :placeholder="'异物次数(次)'" class="publicCss" />
                </el-form-item>
                <el-form-item label="总金额(元)：" prop="totalAmount">
                    <inputNumberYh v-model="ruleForm.totalAmount" :fixed="0" :placeholder="'总金额(元)'"
                        class="publicCss" />
                </el-form-item>


                <el-form-item label="当月考勤人数：" prop="attendanceFigures">
                    <inputNumberYh v-model="ruleForm.attendanceFigures" :placeholder="'当月考勤人数'" class="publicCss" />
                </el-form-item>



            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { employeeMealSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        typeList: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            selectProfitrates: [],
            ruleForm: {
                label: '',
                name: ''
            },
            rules: {
                attendanceFigures: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalAmount: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                foreignObjects: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalOrders: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },

    async mounted() {
        this.$nextTick(() => {
            this.$refs.refruleForm.clearValidate();
        });
        this.ruleForm = { ...this.editInfo };
    },
    methods: {
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
                    const { data, success } = await employeeMealSubmit(this.ruleForm)
                    if (!success) {
                        return
                    }
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
            //   this.$confirm('是否保存?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }).then(async () => {
            //     this.$refs[formName].validate(async(valid) => {
            //       if (valid) {
            //         const { data, success } = await employeeMealSubmit(this.ruleForm)
            //         if(!success){
            //             return
            //         }
            //         await this.$emit("search");

            //       } else {
            //         console.log('error submit!!');
            //         return false;
            //       }
            //     });
            //   }).catch(() => {
            //   });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
