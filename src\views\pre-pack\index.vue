<template>
  <MyContainer>
    <el-tabs v-model="activeName" style="height: 95%;">
      <el-tab-pane label="仓库设置" name="five" style="height: 100%;" lazy v-if="checkPermission('prepackOperationSubmit')">
        <warehouseSettings />
      </el-tab-pane>
      <el-tab-pane label="商品库存" name="six" style="height: 100%;" lazy v-if="checkPermission('prepackOperationSubmit')">
        <inventory />
      </el-tab-pane>
      <el-tab-pane label="预包数据" name="first" style="height: 100%;" lazy
        v-if="checkPermission('prepackOperationSubmit')">
        <prepackProps />
      </el-tab-pane>
      <el-tab-pane label="滞销分析" name="unsalable" style="height: 100%;" lazy
        v-if="checkPermission('prepackOperationSubmit')">
        <unsalable />
      </el-tab-pane>
      <el-tab-pane label="运营预包数据" v-if="checkPermission('prepackOperationSubmit')" style="height: 100%;" lazy>
        <operateConfirmStat />
      </el-tab-pane>
      <el-tab-pane label="一单一品" v-if="checkPermission('prepackOperationSubmit')" style="height: 100%;" lazy>
        <SingleProduct />
      </el-tab-pane>
      <el-tab-pane label="一单多品" v-if="checkPermission('prepackOperationSubmit')" style="height: 100%;" lazy>
        <multipleProducts />
      </el-tab-pane>
      <el-tab-pane label="编码统计" v-if="checkPermission('prepackOperationSubmit')" style="height: 100%;" lazy>
        <codeStatistics />
      </el-tab-pane>
      <el-tab-pane label="运营预包确认" :name="checkPermission('prepackOperationSubmit') ? 'operate-confirm' : 'first'"
        style="height: 100%;" lazy>
        <operateConfirm />
      </el-tab-pane>
      <el-tab-pane label="掉量数据" name="loss-qty" style="height: 100%;" lazy>
        <lossQty />
      </el-tab-pane>
      <el-tab-pane label="预包掉量" v-if="checkPermission('prepackOperationSubmit')" style="height: 100%;" lazy>
        <prepackDrop />
      </el-tab-pane>
      <el-tab-pane label="加工计划" name="second" style="height: 100%;" lazy
        v-if="checkPermission('prepackOperationSubmit')">
        <machiningPlanning />
      </el-tab-pane>
      <el-tab-pane label="基础数据" name="third" style="height: 100%;" lazy
        v-if="checkPermission('prepackOperationSubmit')">
        <basicData />
      </el-tab-pane>
      <el-tab-pane label="包装耗材" name="four" style="height: 100%;" lazy v-if="checkPermission('prepackOperationSubmit')">
        <packConsumables />
      </el-tab-pane>
    </el-tabs>
  </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import prepackProps from './components/prepackProps.vue'
import unsalable from './components/unsalable.vue'
import machiningPlanning from './components/machiningPlanning.vue'
import basicData from './components/basicData.vue'
import packConsumables from './components/packConsumables.vue'
import warehouseSettings from './components/warehouseSettings.vue'
import inventory from './inventory/index.vue'
import lossQty from './components/lossQty.vue'
import prepackDrop from './components/prepackDrop.vue'
import operateConfirmStat from './components/operate-confirm-stat.vue'
import operateConfirm from './components/operate-confirm.vue'
import SingleProduct from './components/SingleProduct.vue'
import multipleProducts from './components/multipleProducts.vue'
import codeStatistics from './components/codeStatistics.vue'
export default {
  components: {
    MyContainer,
    prepackProps,
    machiningPlanning,
    basicData,
    packConsumables,
    warehouseSettings,
    inventory,
    lossQty,
    prepackDrop,
    operateConfirm,
    operateConfirmStat,
    unsalable,
    SingleProduct,
    multipleProducts,
    codeStatistics
  },
  data() {
    return {
      activeName: 'five',
    }
  },
  mounted() {
    if (this.$route.query.tab) {
      this.activeName = this.$route.query.tab
    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped></style>
