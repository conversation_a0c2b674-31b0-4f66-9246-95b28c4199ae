<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="支付宝店铺资金" name="fifth" style="height: 100%" lazy>
                <ZFBShopFunds />
            </el-tab-pane>
            <el-tab-pane label="京东店铺资金" name="first" style="height: 100%" lazy>
                <JDShopfunds />
            </el-tab-pane>
            <el-tab-pane label="京东钱包" name="second" style="height: 100%" lazy>
                <JDPurse />
            </el-tab-pane>
            <el-tab-pane label="抖音店铺资金" name="third" style="height: 100%" lazy>
                <DYShopFunds />
            </el-tab-pane>
            <el-tab-pane label="快手店铺资金" name="forth" style="height: 100%" lazy>
                <KSShopFunds />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import JDShopfunds from "./components/JDShopfunds.vue";
import JDPurse from "./components/JDPurse.vue";
import DYShopFunds from "./components/DYShopFunds.vue";
import KSShopFunds from "./components/KSShopFunds.vue";
import ZFBShopFunds from "./components/ZFBShopFunds.vue";
export default {
    components: {
        MyContainer, JDShopfunds, JDPurse, DYShopFunds, KSShopFunds, ZFBShopFunds
    },
    data() {
        return {
            activeName: 'fifth',
        };
    },
    mounted() {
    },
    methods: {},
};
</script>

<style lang="scss" scoped></style>
