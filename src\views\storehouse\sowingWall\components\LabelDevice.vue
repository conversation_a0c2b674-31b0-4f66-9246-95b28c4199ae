<template>
    <MyContainer >
       <template #header>
            <el-form  :inline="true" ref="topForm" :model="queryEnum" class="demo-ruleForm">
                <el-form-item prop="status">
                    <el-select v-model="queryEnum.status" placeholder="选择状态"  clearable>
                        <el-option
                            v-for="item in statuOption"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="sow_channel_id">
                    <el-select v-model="queryEnum.sow_channel_id" placeholder="请选择播种通道" clearable>
                      <el-option v-for="item in sowingChannelList" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="sow_car">
                    <el-input  v-model="queryEnum.sow_car" placeholder="播种车" clearable style="width: 150px" >
                    </el-input>
                </el-form-item>
                <el-form-item prop="sow_bin">
                    <el-input  v-model="queryEnum.sow_bin" placeholder="播种柜" clearable style="width: 150px" >
                    </el-input>
                </el-form-item>
                  <el-form-item prop="mast_mac">
                    <el-input  v-model="queryEnum.mast_mac" placeholder="主控地址" clearable style="width: 150px" >
                    </el-input>
                </el-form-item>
                <el-form-item prop="lable_adress">
                    <el-input  v-model="queryEnum.lable_adress" placeholder="标签地址" clearable style="width: 150px" >
                    </el-input>
                </el-form-item>
                
                <el-form-item >
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
    
                </el-form-item>
            </el-form>
       </template>
    
        <template>
         <vxetablebase 
         :id="'sow20240913'" 
         :tablekey="'sow20240913'" 
         :tableData='tableData' 
         :tableCols='tableCols' 
         @sortchange='sortchange'
          :loading='loading' 
          :border='true' 
          :that="that" 
          ref="vxetable"  
          >
         </vxetablebase>
         <el-dialog title="编辑" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="450px"  v-dialogDrag>
            <el-form  ref="editForm" :model="editEnum" label-width="70px" style="margin-top: 40px;display: flex;flex-direction: column;align-items: center;" >
                <el-form-item prop="lable_adress" label="标签地址">
                    <el-input  v-model="editEnum.lable_adress" disabled placeholder="标签地址" clearable style="width: 200px" >
                    </el-input>
                </el-form-item>
                <el-form-item prop="mast_mac" label="主控地址">
                    <el-input  v-model="editEnum.mast_mac" disabled placeholder="主控地址" clearable style="width: 200px" >
                    </el-input>
                </el-form-item>
                <el-form-item prop="sow_channel_id" label="播种通道">
                  <el-select
                    v-model="editEnum.sow_channel_id"
                    placeholder="请选择"
                    filterable
                    disabled
                    style="width: 200px"
                  >
                    <el-option
                      v-for="item in sowingChannelList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item prop="sow_car" label="播种车">
                    <el-input  v-model="editEnum.sow_car"  placeholder="播种车" clearable style="width: 200px" >
                    </el-input>
                </el-form-item>
                <el-form-item prop="sow_bin" label="播种柜">
                    <el-input  v-model="editEnum.sow_bin"  placeholder="播种柜" clearable style="width: 200px" >
                    </el-input>
                </el-form-item>
            </el-form>
            <template slot="footer" >
                <el-button @click="dialogHisVisible = false">关闭</el-button>
                <el-button @click="saveEdit" type="primary" >保存</el-button>
            </template>
        </el-dialog>
              </template>
              <el-dialog title="播种订单" v-if="orderListVisible" :visible.sync="orderListVisible" width="70%" height="600px" v-dialogDrag>
                  <vxetablebase 
                    :id="'orderList20240913'" 
                    :tablekey="'orderList20240913'" 
                    :tableData='orderList' 
                    :tableCols='orderListTableCols' 
                    @sortchange='sortchange'
                      :loading='orderListLoading' 
                      :border='true' 
                      :that="that" 
                      ref="orderListVxetable"  
                    
                      />
                      <my-pagination ref="pageLog" :total="totalOrderList"  @get-page="getOrderList()"/>
              </el-dialog>
    
       <template #footer>
          <my-pagination ref="pager" :total="total"  @get-page="getList"/>
        </template>
     </MyContainer>
    </template>
    
    <script>
    import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
    import MyContainer from "@/components/my-container";
    import dayjs from 'dayjs';
    import {QueryLabelList ,EditLabel } from '@/api/wmsoperation/lable.js'
    import {GetSowingChannel} from '@/api/wmsoperation/sow.js'
    
    import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";
    
    
    const statuOption = [{
              value: '0',
              label: '离线'
            }, {
              value: '1',
              label: '在线'
            }, {
              value: '2',
              label: '异常'
            }]
    const tableCols = [
    
     { istrue: true,sortable: 'custom', prop: 'lable_adress', label: '标签地址',  width: 'auto' },
     {istrue: true, sortable: 'custom',prop:'mast_mac',label: '主控MAC地址',  width: 'auto' ,},
     {istrue: true, sortable: 'custom',prop:'sow_channel_id',label: '播种通道',  width: '80' ,formatter: (row,that) => that.formatChanneInfo(row.sow_channel_id)},
     {istrue: true, sortable: 'custom',prop: 'sow_car', label: '播种车',  width: '200px',},
     {istrue: true, sortable: 'custom',prop: 'sow_bin', label: '播种柜',  width: '200px',},
     { istrue: true,sortable: 'custom',prop: 'status', label: '状态',  width: '200px',formatter: (row) => {return statuOption[row.status].label}}, 
     { istrue: true,sortable: 'custom',prop: 'sync_time', label: '最后同步时间',   width: '200px',}, 
     {type:'button',label:'操作',width:'150px',btnList:[
     {label:"编辑",/*display:(row)=>{return true},display:true,*/permission:'api:wmsoperation:Sow:EditLabel',  handle:(that,row)=>that.edit(row)},
      ]
                                              },
    ];
    
    
    const orderListTableCols = [
    
     {istrue: true, sortable: 'custom',prop:'shipout_edison_sellable',label: '订单号',  width: '80px' , },
    ];
    
    export default {
     name: 'sow',
     components: { vxetablebase, MyContainer, OrderActionsByInnerNos},
     data() {
       return {
            that:this,
            statuOption: statuOption,
            queryEnum:{
                timeRanges:[],
                mast_mac:'',
                lable_adress:'',
                status:'',
                sow_bin:'',
                sow_car:'',
                orderBy:'',
                isAsc: true,
            },
            editEnum:{
              
            },
            tableData:[],
            tableCols:tableCols,
            loading:false,
            summaryarry:[],
            total:0,
    
            //订单dialog
            orderNo:null,
            orderListVisible:false,
            orderListTableCols:orderListTableCols,
            orderList:[],
            orderListLoading:false,
            totalOrderList:0,
            dialogHisVisible:false,
       };
     },
      async mounted(){
        const { data } = await GetSowingChannel()
          this.sowingChannelList = data
          
        await this.getList()
    
      },
     methods:{
        formatChanneInfo(value) {
          let info = ' '
          this.sowingChannelList.forEach(item => { if (item.id === value) info = item.name })
          return info
        },
          changeTime(e) {
          this.queryEnum.begin_sow_starttime = e ? e[0] : null
          this.queryEnum.end_sow_starttime = e ? e[1] : null
        },
        async getList(type){
          if(type == 'search'){
            this.$refs.pager.setPage(1)
          }
          let pager = this.$refs.pager.getPager()
    
          let params = {
            ...pager,
            ...this.queryEnum
            
          }
          this.loading = true
          const {list,total} =  await QueryLabelList(params)
          this.loading = false
          this.tableData = list
         
          this.total = total
        },
        sortchange({ order, prop }) {
          if (prop) {
            this.queryEnum.orderBy = prop
            this.queryEnum.isAsc = order.indexOf("descending") == -1 ? true : false
            this.getList()
          }
        },
        
        async saveEdit(){
            if(!this.editEnum.sow_bin.toString()||!this.editEnum.sow_bin.toString()||!this.editEnum.sow_channel_id.toString()) return this.$message({message:'修改内容不能为空',type:'error'})
           const data  =  await EditLabel(this.editEnum)
           if(data.isSuccess){
            this.$message({ 
              message:"操作成功",
              type: "success" 
            });
            await this.getList()
            this.dialogHisVisible = false;
          }else{
            this.$message({ 
              message:data.message,
              type: "error" 
            });
          }
        },
        getOrderList(){
    
        },
        async sowing(row){
          const data = await  StartSow({ wave_id:row.id})
          if(data.isSuccess){
            this.$message({ 
              message:"操作成功",
              type: "success" 
            });
            await this.getList()
          }
        },
        // async edit(row){
        //   this.editEnum = row
        //   if(this.sowingChannelList.length>0){
        //     this.editEnum.sow_channel_id = this.sowingChannelList[0].id
        //   }
        //   this.dialogHisVisible = true;
        // },
        async edit(row) {
          this.editEnum = row
          // 查找对应的播种通道 ID
          const targetChannel = this.sowingChannelList.find(item => item.id === row.sow_channel_id);
          if (targetChannel) {
            this.editEnum.sow_channel_id = targetChannel.id;
          } else {
            console.warn('未找到对应的播种通道 ID:', row.sow_channel_id);
            this.editEnum.sow_channel_id = null;
          }

          this.dialogHisVisible = true;
    },
        onsummaryClick(){
    
        }
     }
    };
    </script>
    
    <style lang="scss" scoped>
    
    </style>
    