<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item label="">
                    <el-select ref="selectUpResId" v-model="chooseName" style="width: 100px" size="mini" clearable
                        placeholder="招聘部门" @clear="() => { filter.ddDeptId = null }">
                        <el-option hidden value="一级菜单" :label="chooseName"></el-option>
                        <el-tree style="width: 200px;" :data="deptList" :props="defaultProps" :expand-on-click-node="false"
                            :check-on-click-node="true" @node-click="handleNodeClick">
                        </el-tree>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input v-model.trim="filter.positionName" placeholder="请输入岗位" style="width:120px;" clearable
                        maxlength="20" />
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="filter.recruiterIds" placeholder="招聘专员" multiple style="width: 150px" size="mini" collapse-tags filterable>
                        <el-option v-for="item in recruiterList" :key="item.ddUserId" :label="item.userName"
                            :value="item.ddUserId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-input  v-model.trim="filter.keywords" style="width: 160px"
                        :maxLength="100" placeholder="关键字查询" clearable>
                        <el-tooltip slot="suffix" class="item" effect="dark"
                            content="部门、岗位、专员、申请人" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-form-item>
                <el-form-item label="" v-if="activeName == 'first2'">
                    <el-select v-model="filter.planStatus" placeholder="完成原因" style="width: 100px" size="mini" clearable>
                        <el-option label="逾期关闭" :value="21"></el-option>
                        <el-option label="手动关闭" :value="22"></el-option>
                        <el-option label="满员关闭" :value="23"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item v-if="activeName == 'first1'">
                    <el-button type="primary" @click="onfinishAll">批量完成</el-button>
                </el-form-item>
                <el-form-item v-if="activeName == 'first1'">
                    <el-button type="primary" @click="addPosition">新增岗位</el-button>
                </el-form-item>
                <!-- <el-form-item v-else>
                    <el-button type="primary" @click="republish">重新发布</el-button>
                </el-form-item> -->

                <!-- <el-form-item style="float:right;margin-right: 50px;">
                    <span style="color: red;">最近钉钉信息时间：{{dingtalkDate}}</span>
                </el-form-item> -->
            </el-form>
        </template>
        <el-tabs v-model="activeName" style="height: calc(100% - 40px);">
            <el-tab-pane label="进行中" name="first1" style="height: calc(100vh - 230px)">
                <ongoing ref="ongoing" v-if="activeName == 'first1'" :ddDeptId="filter.ddDeptId"></ongoing>
            </el-tab-pane>
            <el-tab-pane label="已完成" name="first2" style="height: calc(100vh - 230px)" lazy>
                <completeRecruitment v-if="activeName == 'first2'" ref="completeRecruitment" :ddDeptId="filter.ddDeptId"></completeRecruitment>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import ongoing from "@/views/profit/PersonnelRecruiting/onGoing.vue";
import completeRecruitment from "@/views/profit/PersonnelRecruiting/completeRecruitment.vue";
import { AllDDDeptTreeNcWh, getDeptUsers } from '@/api/profit/personnel'
// import { formatTime } from "@/utils/tools";

export default {
    name: "recruitmentPositions", // 招聘岗位
    components: {
        MyContainer, ongoing, completeRecruitment
    },
    data () {
        return {
            recruiterList: [],
            pageLoading: false,
            activeName: "first1",
            // 下拉框选中节点id与名称
            chooseId: '',
            chooseName: '',
            defaultProps: {
                children: 'childDeptList',
                label: 'name'
            },
            deptList: [],
            filter: {
                closeStatus: 0,//计划状态:-1删除、0进行中、1已完成
                ddDeptId: null,//招聘部门
                positionName: null,//岗位名称
                recruiterIds: [],//招聘专员
                planStatus: null,// 10招聘中、21逾期关闭、22手动关闭、23满员关闭
                keywords:null,
            },
            showDialog: false,
            // dingtalkDate:null
        };
    },
    created () {

    },
    mounted () {
        this.getDeptList();
        this.getRecruiters();
        
        // //计算钉钉信息时间
        // this.getDingtalkDate();
    },
    methods: {
        //获取招聘专员
        getRecruiters () {
            let params = {
                deptName: '招聘组,人事组,SSC&员工关系组,培训组',
                includeAllChildDpt: 1,
            }
            getDeptUsers(params).then(res => {
                if (res.success) {
                    this.recruiterList = res.data;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 获取部门列表
        async getDeptList () {
            await AllDDDeptTreeNcWh().then(res => {
                if (res.success) {
                    this.deptList = res.data.childDeptList;
                } else {
                    this.$message({ message: res.msg, type: "danger" });
                }
            })
        },
        // 节点点击事件
        handleNodeClick (data) {
            // 配置树形组件点击节点后，设置选择器的值，配置组件的数据
            this.chooseName = data.name;
            this.filter.ddDeptId = data.dept_id;
            // 选择器执行完成后，使其失去焦点隐藏下拉框效果
            this.$refs.selectUpResId.blur();
        },
        //重新发布
        republish () {
            this.$refs.completeRecruitment.onAll();
        },
        // 新增岗位按钮
        addPosition () {
            this.$refs.ongoing.onAdd();
        },

        onSearch () {
            if (this.activeName == 'first1') {
                this.filter.closeStatus = 0
                this.$refs.ongoing.onSearch(this.filter);
            } else {
                this.filter.closeStatus = 1
                this.$refs.completeRecruitment.onSearch(this.filter);
            }
        },
        // 批量完成
        onfinishAll () {
            this.$refs.ongoing.onAll();
        },
        // //获取钉钉信息时间
        // getDingtalkDate(){
        //     const hourDate = ' 05:00:00';
        //     var today = new Date();
        //     var hours = today.getHours();
        //     if(hours < 5){
        //         //当前时间为上午5点前，则显示昨天的时间
        //         today.setDate(today.getDate() - 1); 
        //     }

        //     this.dingtalkDate = formatTime(today, 'YYYY-MM-DD')+hourDate; 
        // }

    },
};
</script>
  
<style lang="scss" scoped></style>
  