
<template> 

<el-date-picker style="width:320px" v-model="timevalue1" :picker-options="pickerOptions" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="clearable"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="changeTime"></el-date-picker>
</template>

<script>
import dayjs from 'dayjs'
export default {
  name: 'd2DateTimePicker',
  model: {
    prop: 'timevalue',
    event: 'change'
  },

  props: {
    clearable: { type: Boolean, default: () => { return true } },
    timevalue: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    }

  },
  data() {
    return {
      date: '',
      timevalue1: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      pickerOptions: {
          shortcuts: [
          // {
          //   text: '前天',
          //   onClick(picker) {

          //     const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
          //     const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
          //     const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
          //     start.setTime(start.getTime() - 3600 * 1000 * 24);
          //     end.setTime(start.getTime() + 3600 * 1000 * 24);
          //     picker.$emit('pick', [start, start]);
          //   }
          // },
          {
            text: '大前天',
            onClick(picker) {

                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 2);

              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime() - 3600 * 1000 * 24);

              picker.$emit('pick', [start, start]);
            }
          }, {
            text: '近一周',
            onClick(picker) {
              const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 7);
              const end = new Date(new Date(tdate.toLocaleDateString()).getTime()+ 3600 * 1000 * 24 * 5);
              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime() - 3600 * 1000 * 24*2);
              end.setTime(end.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '近一个月',
            onClick(picker) {
              const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 30);
              console.log("获取前一个月的时间",tdate.getDay());
              const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
              const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          }],
          disabledDate(date) {
          // 设置禁用日期
          const start = new Date('1970/1/1');
          const end = new Date('9999/12/31');
          return date < start || date > end;
          }
        },
        defaultDate: new Date('1970/1/1')
    }
  },
  watch: {
    timevalue: {
      immediate: true,
      // console.log(val, 'time')
      handler(val) {
        if (val.length > 0) {
          this.timevalue1 = val
        }
        this.$emit('change', val)
      }

    },
    timevalue1: {
      immediate: true,
      // console.log(val, 'time')
      handler(newVal) {
        this.$emit('change', newVal)
      }

    }

  },
  methods: {
    changeTime(val) {
      // var oval = this.moment(val).format('YYYY-MM-DD HH:mm:ss')
      // console.log(val)
      this.$emit('change', val)
    }
  }

}
</script>
