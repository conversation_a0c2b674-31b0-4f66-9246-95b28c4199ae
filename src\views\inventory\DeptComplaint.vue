<template>
    <MyContainer>
        <template #header>
            <el-form :inline="true" :model="filter">
                <el-form-item label="投诉时间:">
                    <el-date-picker style="width: 240px" v-model="compTimeRange" type="daterange"
                        :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束"
                        @change="changeCompTime"></el-date-picker>
                </el-form-item>
                <el-form-item label="添加时间:">
                    <el-date-picker style="width: 240px" v-model="addTimeRange" :picker-options="pickerOptions"
                        type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至"
                        start-placeholder="开始" end-placeholder="结束" @change="changeAddTime"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <!-- <el-select v-model="filter.adder.id" placeholder="添加人" style="width:130px" filterable clearable>
                        <el-option v-for="item in stuffList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select> -->
                    <YhUserelector :value.sync="filter.adder.id" maxlength="50" @update:value="updateValue"
                        :text.sync="filter.adder.name" style="width:80%;" placeholder="请输入添加人">
                    </YhUserelector>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.complainee.id" placeholder="被投诉人" style="width:130px" filterable
                        clearable>
                        <el-option v-for="item in stuffList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="filter.compDept.id" placeholder="被投诉组" filterable clearable
                        style="width:130px">
                        <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getList('search')">查询</el-button>
                </el-form-item>
            </el-form>
            <div>
                <el-button type="primary" @click="importComp">导入</el-button>
                <el-button type="primary" @click="download">下载导入模板
                </el-button>
                <el-button type="primary" @click="exportComp">导出</el-button>
                <el-button type="primary" @click="addOrEdit('add')">新增</el-button>
            </div>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
        :tablefixed='true'  :row-config="{ isHover: true }"
        :tableData='tableData' :tableCols='tableCols'  :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading"
        :height="'100%'">
        <template #column>
                <vxe-column title="操作" fixed="right" :width="240">
                    <template #default="{ row, $index }">
                        <el-button type="primary" @click="addOrEdit('edit', row)">编辑</el-button>
                        <el-button type="danger" @click="deleteRow(row)">删除</el-button>
                    </template>
                </vxe-column>
            </template>
        </ces-table>
        <el-dialog :title="form.title" :visible.sync="form.visible" width="500px" v-dialogDrag>
            <el-form :model="form">
                <el-form-item>
                    <el-select v-model="form.compDept.id" placeholder="投诉组" style="width:130px" filterable clearable
                        @change="changeComp($event, 'Dept')">
                        <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="form.complainee.id" placeholder="被投诉人" style="width:130px" filterable clearable
                        @change="changeComp($event, 'plainee')">
                        <el-option v-for="item in stuffList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker v-model="form.comptime" type="datetime" placeholder="投诉时间"
                        value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-input type="textarea" :rows="5" placeholder="投诉内容" v-model="form.compContent" maxlength="50"
                        show-word-limit>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <YhImgUpload :value.sync="form.attachs" :limit="3" :ismultiple="true" attachs>
                    </YhImgUpload>
                </el-form-item>
                <el-form-item style="text-align: right;">
                    <el-button @click="form.visible = false">取消</el-button>
                    <el-button type="primary" @click="onSubmit" v-throttle="1000">确定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <el-dialog title="导入部门投诉数据" :visible.sync="importCompVisible" width="600px" style="height: 300px;" v-dialogDrag>
            <span>
                <el-row>
                    <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                        <el-upload ref="upload" :auto-upload="false" :multiple="false" action accept=".xlsx"
                            :http-request="request" :on-change="uploadChange" :on-remove="uploadRemove">
                            <template #trigger>
                                <el-button size="small" type="primary">选取文件</el-button>
                            </template>
                            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                                @click="submitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </span>
        </el-dialog>
        <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
    </MyContainer>
</template>

<script>
import MyContainer from '@/components/my-container'
import { getProcStaff, getProcSupv, getCompDept, getDeptComplaint, editDeptComplaint, addDeptComplaint, deleteDeptComplaint, importDeptComplaintFile, exportDeptComplaintFile } from '@/api/inventory/DeptComplaint';
import { pickerOptions } from '@/utils/tools'
import cesTable from "@/components/Table/table.vue";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import MyConfirmButton from "@/components/my-confirm-button";
import YhUserelector from '@/components/YhCom/yh-userselector.vue'

const tableCols = [
    { istrue: true, sortable: 'custom', width: '250', align: 'left', prop: 'compDepartmentName', label: '投诉组'},
    { istrue: true, prop: 'complaineeName', align: 'center', label: '被投诉人',  sortable: 'custom', },
    { istrue: true, prop: 'adderName', align: 'center', label: '添加人', sortable: 'custom', },
    { istrue: true, prop: 'compTime', align: 'center', label: '投诉时间',  sortable: 'custom', },
    { istrue: true, prop: 'addTime', align: 'center', label: '添加时间', sortable: 'custom', },
    { istrue: true, sortable: 'custom', align: 'center', prop: 'compContent', label: '投诉内容', },
    { istrue: true, sortable: 'custom', width: '80', align: 'left', prop: 'compAttachs', label: '附件', type:'images'},
    {
        istrue: true, type: "button", label: '操作', 
        btnList: [
            { label: "编辑", handle: (that, row) => that.addOrEdit('edit', row)},
            { label: "删除", handle: (that, row) => that.deleteRow(row)},
        ]
    },
];
export default {
    name: "DeptComplaint",
    components: {
        MyContainer, cesTable, YhImgUpload, MyConfirmButton,YhUserelector
    },
    data() {
        return {
            that: this,
            filter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                id: null,
                startCompTime: null,
                endCompTime: null,
                startAddTime: null,
                endAddTime: null,
                adder: {
                    id: null,
                    name: null
                },
                complainee: {
                    id: null,
                    name: null
                },
                compDept: {
                    id: null,
                    name: null
                },
            },
            compTimeRange: [],
            addTimeRange: [],
            pickerOptions,
            stuffList: [],
            supvList: [],
            deptList: [],
            tableCols,
            tableData: [],
            loading: false,
            form: {
                visible: false,
                type: null,
                title: "",
                id: null,
                comptime: null,
                compDept: {
                    id: null,
                    name: null
                },
                complainee: {
                    id: null,
                    name: null
                },
                compContent: null,
                attachs: null,
            },
            importCompVisible: false,
            attachVisbale: false,
            fileList: [],
            uploadLoading: false,
            total: null,
        };
    },
    async mounted() {
        await this.getStaffList();
        await this.getSupvList();
        await this.getDeptList();
        await this.getList("search");
    },
    methods: {
        changeComp(e, type) {
            if (type == 'Dept') {
                this.form.compDept.name = e ? this.deptList.find(dept => dept.id == e).name : null
            } else {
                this.form.complainee.name = e ? this.stuffList.find(dept => dept.id == e).name : null
            }
        },
        async changeCompTime(e) {
            this.filter.startCompTime = e ? e[0] : null;
            this.filter.endCompTime = e ? e[1] : null;
        },
        async changeAddTime(e) {
            this.filter.startAddTime = e ? e[0] : null;
            this.filter.endAddTime = e ? e[1] : null;
        },
        async getStaffList() {
            this.stuffList = await getProcStaff();
        },
        async getSupvList() {
            this.supvList = await getProcSupv();
        },
        async getDeptList() {
            this.deptList = await getCompDept();
        },
        async getList(type) {
            if(type=="search")
            {
                this.filter.currentPage=1;
                this.filter.orderBy = 'addTime';
                this.filter.isAsc=false;
            }
            this.loading = true;
            const { data, success } = await getDeptComplaint(this.filter);
            if (success) {
                this.total = data.total;
                this.tableData = data.list;
            } else {
                this.$message.error("获取列表失败!");
            }
            this.loading = false;
        },
        addOrEdit(type, row) {
            this.form.type = type
            this.form.id = type == 'edit' ? row.id : null
            this.form.compDept.id = type == 'edit' ? row.compDepartmentId : null
            this.form.compDept.name = type == 'edit' ? row.compDepartmentName : null
            this.form.complainee.id = type == 'edit' ? row.complaineeId : null
            this.form.complainee.name = type == 'edit' ? row.complaineeName : null
            this.form.comptime = type == 'edit' ? row.compTime : null
            this.form.compContent = type == 'edit' ? row.compContent : null
            this.form.attachs = type == 'edit' ? row.compAttachs : null
            this.form.visible = true;
        },
        async sortchange(column) {
            this.filter.orderBy = column.prop
            this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false
            await this.getList();
        },
        async onSubmit() {
            if (this.form.type == "edit") {
                const { success } = await editDeptComplaint(this.form);
                if (success) {
                    this.$message.success("保存成功");
                    this.form.visible = false;
                    await this.getList()
                }
            } else {
                const { success } = await addDeptComplaint(this.form);
                if (success) {
                    this.$message.success("添加成功");
                    this.form.visible = false;
                    await this.getList()
                }
            }
        },
        download() {
            const aLink = document.createElement("a");
            aLink.href = "/static/excel/inventory/部门投诉导入模板.xlsx";
            aLink.setAttribute('download', '部门投诉导入模板.xlsx')
            aLink.click();
        },
        async deleteRow(row) {
            this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await deleteDeptComplaint({ Id: row.id })
                if (success) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        async importComp() {
            this.importCompVisible = true;
            this.uploadLoading=false;
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
        },
        async request(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const res = importDeptComplaintFile(form);
            this.$message({ message: '上传成功,正在导入中...', type: "success" });
        },
        async exportComp() {
            this.loading = true
            const res = await exportDeptComplaintFile(this.filter)
            this.loading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '部门投诉表' + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        uploadRemove(file, fileList) {
            this.fileList.splice(0, 1);
        },
        submitUpload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
            this.importCompVisible=false;
        },
        async Pagechange(val) {
            this.filter.currentPage = val;
            this.getList()

        },
        async Sizechange(val) {
            this.filter.pageSize = val;
            this.filter.currentPage=1;
            this.getList()
        }
    }
};
</script>