<template>
    <MyContainer v-loading="pageLoading">
        <orderabnormalnew></orderabnormalnew>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";  
import orderabnormal from "@/views/inventory/orderabnormal";  
import orderabnormalnew from "@/views/inventory/orderabnormalnew";
export default {
    name: 'Stockoutplate',
    components: {MyContainer,orderabnormal,orderabnormalnew},
    data() {
        return {
            pageLoading: false,
        };
    },

    mounted() {
        // this.$router.push({path: '/inventory/orderabnormal'})
    },

    methods: {
        
    },
};
</script>

<style lang="scss" scoped>

</style>