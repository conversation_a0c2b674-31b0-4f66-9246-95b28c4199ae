<template>
  <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
    <el-scrollbar style="height: 100%">
      <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="110px" class="demo-ruleForm">
        <div class="city-name">资产名称:</div>
        <el-form-item label="显示屏" prop="display">
          <inputNumberYh v-model="ruleForm.display" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="主机" prop="host">
          <inputNumberYh v-model="ruleForm.host" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="键盘" prop="keyboard">
          <inputNumberYh v-model="ruleForm.keyboard" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="鼠标" prop="mouse">
          <inputNumberYh v-model="ruleForm.mouse" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <div class="city-name">资产信息:</div>
        <el-form-item label="单位" prop="unit">
          <el-input v-model.trim="ruleForm.unit" placeholder="请输入单位" maxlength="50" clearable class="publicCss" />
        </el-form-item>
        <el-form-item label="采购数" prop="procurementQuantity">
          <inputNumberYh v-model="ruleForm.procurementQuantity" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="新增数" prop="newAdditions">
          <inputNumberYh v-model="ruleForm.newAdditions" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="上次盘点数" prop="lastInventory">
          <inputNumberYh v-model="ruleForm.lastInventory" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="盘点数" prop="inventoryCount">
          <inputNumberYh v-model="ruleForm.inventoryCount" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <div class="city-name">盘点总况:</div>
        <el-form-item label="盘点结果" prop="inventoryResults">
          <el-input v-model.trim="ruleForm.inventoryResults" placeholder="请输入盘点结果" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="盘点情况" prop="inventorySituation">
          <el-input v-model.trim="ruleForm.inventorySituation" placeholder="请输入盘点情况" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <el-form-item label="盘亏盘盈原因" prop="reasons">
          <el-input v-model.trim="ruleForm.reasons" placeholder="请输入盘亏盘盈原因" maxlength="50" clearable
            class="publicCss" />
        </el-form-item>
        <div class="city-name">资产现状:</div>
        <el-form-item label="在使用" prop="inUse">
          <inputNumberYh v-model="ruleForm.inUse" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="库存空闲" prop="inventoryIdle">
          <inputNumberYh v-model="ruleForm.inventoryIdle" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="调拨" prop="allocateTransfer">
          <inputNumberYh v-model="ruleForm.allocateTransfer" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="待维修" prop="repaired">
          <inputNumberYh v-model="ruleForm.repaired" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="损坏" prop="damage">
          <inputNumberYh v-model="ruleForm.damage" placeholder="请输入" class="publicCss" />
        </el-form-item>
        <el-form-item label="其他" prop="other">
          <el-input v-model.trim="ruleForm.other" placeholder="请输入其他" maxlength="50" clearable class="publicCss" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
      <el-button @click="cancellationMethod">取消</el-button>
      <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
    </div>
  </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { fixedAssetsSubmit } from '@/api/people/peoplessc.js';
export default {
  name: 'departmentEdit',
  components: {
    inputNumberYh, MyConfirmButton
  },
  props: {
    editInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      ruleForm: {
        display: '',// 显示屏
        host: '',// 主机
        keyboard: '',// 键盘
        mouse: '',// 鼠标
        unit: '',// 单位
        procurementQuantity: '',// 采购数
        newAdditions: '',// 新增数
        lastInventory: '',// 上次盘点数
        inventoryCount: '',// 盘点数
        inventoryResults: '',// 盘点结果
        inventorySituation: '',// 盘点情况
        reasons: '',// 盘亏盘盈原因
        inUse: '',// 在使用
        inventoryIdle: '',// 库存空闲
        allocateTransfer: '',// 调拨
        repaired: '',// 待维修
        damage: '',// 损坏
        other: '',// 其他
      },
      rules: {
        display: [
          { required: true, message: '请输入显示器', trigger: 'blur' },
        ],
        host: [
          { required: true, message: '请输入主机', trigger: 'blur' },
        ],
        keyboard: [
          { required: true, message: '请输入键盘', trigger: 'blur' },
        ],
        mouse: [
          { required: true, message: '请输入鼠标', trigger: 'blur' },
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' },
        ],
        procurementQuantity: [
          { required: true, message: '请输入采购数', trigger: 'blur' },
        ],
        newAdditions: [
          { required: true, message: '请输入新增数', trigger: 'blur' },
        ],
        lastInventory: [
          { required: true, message: '请输入上次盘点数', trigger: 'blur' },
        ],
        inventoryCount: [
          { required: true, message: '请输入盘点数', trigger: 'blur' },
        ],
        inventoryResults: [
          { required: true, message: '请输入盘点结果', trigger: 'blur' },
        ],
        inventorySituation: [
          { required: true, message: '请输入盘点情况', trigger: 'blur' },
        ],
        reasons: [
          { required: true, message: '请输入盘亏盘盈原因', trigger: 'blur' },
        ],
        inUse: [
          { required: true, message: '请输入在使用', trigger: 'blur' },
        ],
        inventoryIdle: [
          { required: true, message: '请输入库存空闲', trigger: 'blur' },
        ],
        allocateTransfer: [
          { required: true, message: '请输入调拨', trigger: 'blur' },
        ],
        repaired: [
          { required: true, message: '请输入待维修', trigger: 'blur' },
        ],
        damage: [
          { required: true, message: '请输入损坏', trigger: 'blur' },
        ],
        other: [
          { required: true, message: '请输入其他', trigger: 'blur' },
        ]
      }
    }
  },

  async mounted() {
    this.$nextTick(() => {
      this.$refs.refruleForm.clearValidate();
    });
    this.ruleForm = { ...this.editInfo };
  },
  methods: {
    cancellationMethod() {
      this.$emit('cancellationMethod');
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { data, success } = await fixedAssetsSubmit(this.ruleForm)
          if (!success) {
            return
          }
          this.$emit("search");
        } else {
          console.error('submit failed, reason: ', valid);
          return false;
        }
      });
    },
  }
}
</script>
<style scoped lang="scss">
.publicCss {
  width: 80%;
}

.city-name {
  font-size: 17px;
  font-weight: bold;
  margin: 0 0 5px 5%;
}
</style>
