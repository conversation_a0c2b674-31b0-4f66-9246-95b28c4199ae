<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>

                <el-form-item label="">
                    <el-date-picker style="width: 110px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="年月">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺" style="width: 180px">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.tradeType" placeholder="交易类型" style="width:120px;" maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.accountSourceType" placeholder="结算账户" style="width:120px;"
                        maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.shopGoodsCode" placeholder="规格ID" style="width:120px;" maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.proCode" placeholder="产品ID" style="width:120px;" maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.orderNumber" placeholder="订单号" style="width:160px;" maxlength="20" />
                </el-form-item>

                <el-form-item label="" label-position="right">
                    <el-input v-model="filter.remark" placeholder="备注" style="width:160px;" maxlength="100" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExport">汇总导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='ZTCKeyWordList' :showsummary='true' :summaryarry='summaryarry' @select='selectchange'
            :isSelection='false' :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { getAllList as getAllShopList, getList as getshopList } from '@/api/operatemanage/base/shop';
import { formatPlatform, formatLink, platformlist } from "@/utils/tools";
import { GetXhsFinacialDetailPageList as getPageList, ExportXhsFinacialDetailList } from '@/api/monthbookkeeper/financialDetail'
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '年月', width: '90', sortable: 'custom' },
    { istrue: true, prop: 'orderNumber', label: '订单号', sortable: 'custom', width: '180' },
    { istrue: true, prop: 'afterOrderNumber', label: '售后单号', sortable: 'custom', width: '180' },
    { istrue: true, prop: 'orderTime', label: '下单时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'finishTime', label: '完成时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'settlementTime', label: '结算时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'tradeType', label: '交易类型', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'accountSourceType', label: '结算账户', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'accountAmount', label: '动账金额', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'goodsCode', label: 'SKU条码', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'shopGoodsCode', label: '规格ID', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'proCode', label: '产品ID', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'qty', label: '商品数量', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'compCommBaseNum', label: '计佣基数', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'actualPayAmount', label: '商品实付/实退', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'discountType', label: '优惠类型', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'shopDiscount', label: '商家优惠', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'platDiscountSubsidies', label: '平台优惠补贴', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'platFreightSubsidies', label: '平台运费补贴', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'commissionRate', label: '佣金率', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'fanLiRate', label: '返利率', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'commissionTotalAmount', label: '佣金总额', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'jiShuiPriceHanShui', label: '计税价格(含税)', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'jiShuiPriceWeiShui', label: '计税价格(未税)', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'shuiLv', label: '税率', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'kuaJingShuiDaiJiao', label: '跨境税代缴', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'goodsShuiJin', label: '商品税金', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'maiJiaCpsCommissionRate', label: '卖家CPS佣金率', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'fenXiaoCommissionAmount', label: '分销佣金', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'wiseManId', label: '推广达人ID', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'wiseManName', label: '达人昵称', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'wiseManDaiHuoType', label: '带货类型', sortable: 'custom', width: '100' },
    //{ istrue: true, prop: 'fuWuShangCommissionAmount', label: '服务商佣金', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'yyFuWuShangCommissionAmount', label: '代运营服务商佣金', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'kfFuWuShangCommissionAmount', label: '代开发服务商佣金', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'freightAmount', label: '运费', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'freightAmountShuiJin', label: '运费税金', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'payChannelFee', label: '支付渠道费', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'huaBeiFenQiShouXuFei', label: '花呗分期手续费', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'guoBuOrderMaoBaoFei', label: '国补订单毛保金额', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'Remark', label: '备注', sortable: 'custom', width: '200' },
];
export default {
    name: "xhsshopbalance",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
                platform: 21,
                yearMonth: null,
                shopCode: null,
                proCode: null,
                isNullProCode: null,
                RecoganizeType: null
            },
            shopList: [],
            userList: [],
            groupList: [],
            platformlist: platformlist,
            ZTCKeyWordList: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
        };
    },
    async mounted() {
        this.onchangeplatform();
    },
    methods: {
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onchangeplatform() {
            const res1 = await getshopList({ platform: 21, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            let params = this.getCondition();
            this.listLoading = true;
            const res = await getPageList(params);
            this.listLoading = false;
            this.total = res.data?.total
            this.ZTCKeyWordList = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        getCondition() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            return params;
        },
        async onExport(opt) {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份", type: "warning" });
                return;
            }
            let pars = this.getCondition();
            if (pars === false) {
                return;
            }
            const params = { ...pars, ...opt };
            let res = await ExportXhsFinacialDetailList(params);
            if (!res?.data) {
                return
            }
            this.$message({ message: "正在后台导出中，请点击头像-在下载管理中查看", type: "success" });
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
