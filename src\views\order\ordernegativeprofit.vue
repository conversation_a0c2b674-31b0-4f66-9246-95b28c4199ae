<template>
  <el-container style="height:100%;">
      <my-container v-loading="pageLoading" style="width:99%;">
        <template #header>
          <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>      
              <el-form-item label="付款日期:">
                <el-date-picker
                  style="width:240px"
                  v-model="filter.timerange"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始"
                  end-placeholder="结束"
                  :picker-options="pickerOptions"
                  @change="Query"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="平台:">
                <el-select style="width:130px"
                  v-model="filter.platform"
                  placeholder="请选择"
                  :clearable="true" 
                  :collapse-tags="true"
                  @change="changePlatform"
                >
                  <el-option
                    v-for="item in platformList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="店铺:">
                <el-select style="width:130px"
                  v-model="filter.shopCode"
                  placeholder="请选择"
                  :clearable="true" 
                  :collapse-tags="true"
                  @change="Query"
                >
                  <el-option
                    v-for="item in shopList"
                    :key="item.shopCode"
                    :label="item.shopName"
                    :value="item.shopCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="运营组:">
                <el-select style="width:130px"
                  v-model="filter.groupId"
                  placeholder="请选择"
                  :clearable="true" 
                  :collapse-tags="true"   
                  @change="Query"               
                >
                  <el-option
                    v-for="item in groupList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="产品数:">
                <el-select style="width:130px"
                  v-model="filter.proCount"
                  placeholder="请选择"
                  :clearable="true" 
                  :collapse-tags="true"
                  
                >
                  <el-option key="1" label="一单一品" value="1"></el-option>
                  <el-option key="2" label="一单多品" value="2"></el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="内部订单号">
                  <el-input v-model="filter.orderNoInner" placeholder="内部订单号" @change="Query"></el-input>
              </el-form-item>
              <el-form-item label="产品ID">
                  <el-input v-model="filter.proCode" placeholder="产品ID" @change="Query"></el-input>
              </el-form-item>
              <el-form-item label="商品编码">
                  <el-input v-model="filter.goodsCode" placeholder="商品编码" @change="Query"></el-input>
              </el-form-item>
              <el-form-item label="状态:">
                <el-select v-model="filter.status"  placeholder="请选择" style="width: 130px;" clearable @change="Query">
                  <el-option label="已发货" value="已发货"/>
                  <el-option label="被拆分" value="被拆分"/>
                  <el-option label="被合并" value="被合并"/>
                  <el-option label="取消" value="取消"/>
                  <el-option label="已客审待财审" value="已客审待财审"/>
                </el-select>
              </el-form-item>
              <el-form-item>
                  <el-button type="primary" @click="Query">查询</el-button>
              </el-form-item>            
          </el-form>
        </template>

        <el-tabs v-model="activeName" style="height:94%;">
          <el-tab-pane label="订单" name="tabOrder" style="height:100%;">
              <tabOrder :filter="filter" :tablekey="tableKey1" ref="tabOrder"></tabOrder>
          </el-tab-pane>
          <el-tab-pane label="产品" name="tabProduct" style="height:100%;">
              <tabProduct :filter="filter" :tablekey="tableKey2" ref="tabProduct"></tabProduct>
          </el-tab-pane>
          <el-tab-pane label="商品" name="tabGoods" style="height:100%;">
              <tabGoods :filter="filter" :tablekey="tableKey3" ref="tabGoods"></tabGoods>
          </el-tab-pane>
          <el-tab-pane label="店铺" name="tabShop" style="height:100%;">
              <tabShop :filter="filter" :tablekey="tableKey4" ref="tabShop"></tabShop>
          </el-tab-pane>
          <el-tab-pane label="运营" name="tabGroup" style="height:100%;">
              <tabGroup :filter="filter" :tablekey="tableKey5" ref="tabGroup"></tabGroup>
          </el-tab-pane>
        </el-tabs>         
      </my-container>
  </el-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import tabProduct from "@/views/order/ordernegativeprofit/OrderProfitTabProduct";
import tabOrder from "@/views/order/ordernegativeprofit/OrderProfitTabOrder";
import tabGoods from "@/views/order/ordernegativeprofit/OrderProfitTabGoods";
import tabShop from "@/views/order/ordernegativeprofit/OrderProfitTabShop";
import tabGroup from "@/views/order/ordernegativeprofit/OrderProfitTabGroup";
import { getGroupKeyValue } from '@/api/operatemanage/base/product';
import { getList as getshopList } from '@/api/operatemanage/base/shop';
import { rulePlatform} from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";

export default {
  name: 'Roles',
  components: { MyContainer, MyConfirmButton, tabProduct, tabOrder,tabGoods,tabShop,tabGroup},
  data() {
    return {
      activeName:"tabOrder",
      that:this,
      filter: {
        startDate:null,
        endDate:null,
        platform:null,
        shopCode:"",
        groupId:null,
        proCount:null,
        proCode:null,
        timerange:[formatTime(dayjs().startOf("month"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")],
        orderNoInner:null,
        status:null
      },
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
      platformList: [],
      shopList:[],
      groupList:[],
      pageLoading: false,
      tableKey1:"tableKey1",
      tableKey2:"tableKey2",
      tableKey3:"tableKey3",
      tableKey4:"tableKey4",
      tableKey5:"tableKey5",
    }
  },
  async mounted() {
    await this.setPlatform();
    await this.setGroupSelect();
  },
  methods: {
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
    },
    async changePlatform(val){
      const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
      this.shopList=res1.data.list;
      this.filter.shopCode="";
      this.Query();
    },
    async setGroupSelect(){
      const res = await getGroupKeyValue({});
      this.groupList=res.data;
    },
    //总查询
    async Query(){
      switch(this.activeName){
        case "tabOrder":
          this.$refs.tabOrder.onSearch();
          break;
        case "tabProduct":
          this.$refs.tabProduct.onSearch();
          break;
        case "tabGoods":
          this.$refs.tabGoods.onSearch();
          break;
        case "tabShop":
          this.$refs.tabShop.onSearch();
          break;
        case "tabGroup":
          this.$refs.tabGroup.onSearch();
          break;
      }
    },   
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
