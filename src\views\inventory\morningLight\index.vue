<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height:95%">
            <el-tab-pane label="厂家信息" name="first" style="height: 100%;" lazy>
                <factoryInfo></factoryInfo>
            </el-tab-pane>
            <el-tab-pane label="采购信息" name="second" style="height: 100%;" lazy>
                <procurement></procurement>
            </el-tab-pane>

        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import factoryInfo from './component/factoryInfo.vue';
import procurement from './component/procurement.vue';

export default {
    components: {
        MyContainer, factoryInfo, procurement
    },
    data() {
        return {
            activeName: 'first'
        };
    },
    methods: {

    }
};
</script>

<style lang="scss" scoped></style>