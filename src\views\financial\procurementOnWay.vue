<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                    style="width: 250px;margin-right: 10px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
                </el-date-picker>
                <el-input v-model="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="importProps">导入</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :showsummary='true'
            :summaryarry='summaryarry' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading"
            :height="'100%'">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-date-picker v-model="yearMonthDay" align="right" type="date" placeholder="选择日期"
                    :picker-options="pickerOptions1" value-format="yyyy-MM-dd" style="margin-bottom: 30px;">
                </el-date-picker>
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button type="primary" @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions  } from '@/utils/tools'
import { getStyleCodeProcureInTransitList, importStyleCodeProcureInTransit } from '@/api/bookkeeper/styleCodeRptData'
import dayjs from 'dayjs'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'rptDate', label: '日期', formatter: (row) => row.rptDate ? dayjs(row.rptDate).format('YYYY-MM-DD') : '' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyNo', label: '采购单号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsName', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'count', label: '采购数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'inCount', label: '入库数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'outCount', label: '缺货数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'price', label: '单价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalInTransitPrice', label: '总价', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            pickerOptions,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'totalInTransitPrice',
                isAsc: false,
                startDate: null,//开始时间
                endDate: null,//结束时间
                styleCode: null,//系列编码
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: false,
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            summaryarry: {},
            yearMonthDay: null,
            pickerOptions1: {
                shortcuts: [{
                    text: '今天',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '昨天',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24);
                        picker.$emit('pick', date);
                    }
                }, {
                    text: '一周前',
                    onClick(picker) {
                        const date = new Date();
                        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', date);
                    }
                }]
            },
        }
    },
    async mounted() {
        this.ListInfo.startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
        console.log(this.timeRanges, ' this.timeRanges ');
        await this.getList()
    },
    methods: {
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.yearMonthDay) return this.$message.error('请选择日期')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("yearMonthDay", this.yearMonthDay);
            this.importLoading = true
            await importStyleCodeProcureInTransit(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.yearMonthDay = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async changeTime(e) {
            console.log(e,'e');
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
            await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            const replaceArr = ['styleCode'] //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(replaceArr, this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            const { data: { list, total, summary }, success } = await getStyleCodeProcureInTransitList(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.summaryarry = summary
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>