<template>
  <my-container>
    <my-container v-loading="pageLoading" style="height: 100%">
      <template #header>

            <el-date-picker
              style="width: 240px"
              v-model="filter.timerange"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="扣款开始日期"
              end-placeholder="扣款结束日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>

            <el-date-picker  v-if="activeName!='ZrApplyInnerMng'"
              style="width: 240px"
              v-model="filter.timerange2"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="付款开始日期"
              end-placeholder="付款结束日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>

            <el-date-picker  v-if="activeName!='ZrApplyInnerMng'"
              style="width: 240px"
              v-model="filter.timerange3"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="发货开始日期"
              end-placeholder="发货结束日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>

            <el-select  v-if="activeName!='ZrApplyInnerMng'"
              style="width:90px;"
              v-model="filter.ysLx"
              placeholder="预售类型"
              @change="onSearch"
              :clearable="true"
              :collapse-tags="true"
              filterable
            >
              <el-option label="预售" value="预售"/>
              <el-option label="非预售" value="非预售"/>
            </el-select>

            <el-input  v-if="activeName!='ZrApplyInnerMng'"
              v-model.trim="filter.proCode"
              style="width: 140px"
               placeholder="宝贝ID" maxLength="40"
              @keyup.enter.native="onSearch"
              clearable
            />

            <el-select  v-if="activeName!='ZrApplyInnerMng'"
              filterable
              v-model="filter.shopId"
              placeholder="请选择店铺"
              @change="onSearch"
              clearable
              style="width: 180px"
            >
              <el-option label="所有" value></el-option>
              <el-option
                v-for="item in shopList"
                :key="item.id"
                :label="item.shopName"
                :value="item.id"
              />
            </el-select>

            <el-select  v-if="activeName!='ZrApplyInnerMng'"
             v-model="filter.wmsCoId" clearable placeholder="请选择发货仓" style="width: 250px">
              <el-option v-for="item in newWareHouseList" :key="item.name" :label="item.name" :value="item.wms_co_id" />
            </el-select>

            <el-select  v-if="activeName!='ZrApplyInnerMng'"
             filterable
              style="width: 160px"
              v-model="filter.illegalType"
              placeholder="请选择平台原因"
               @change="onSearch"
              clearable
            >
              <el-option
                v-for="item in illegalTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>

              <el-select v-show="activeName !='first4'" v-model="filter.brandId" style="width:140px" :clearable="true"  placeholder="采购组" filterable>
                <el-option v-for="item in brandlist" :key="item.key" :label="item.value" :value="item.key"/>
              </el-select>

              <el-select  v-show="(activeName!='ZrApplyInnerMng')&&(activeName !='first4')"  v-model="filter.brandRegion" style="width:90px" :clearable="true"  placeholder="分区">
                <el-option v-for="item in brandRegionlist" :key="item.key" :label="item.value" :value="item.key"/>
              </el-select>

              <el-select v-show="activeName=='first4'" v-model="filter.brandIdList" style="width:153px" :clearable="true" multiple collapse-tags  placeholder="采购组" filterable>
                <el-option v-for="item in brandlist" :key="item.key" :label="item.value" :value="item.key"/>
              </el-select>

              <el-select  v-show="activeName=='first4'"  v-model="filter.brandRegions" style="width:140px" :clearable="true" multiple collapse-tags  placeholder="分区">
                <el-option v-for="item in brandRegionlist" :key="item.key" :label="item.value" :value="item.key"/>
              </el-select>

              <el-select  v-show="activeName=='first4'"  v-model="filter.titleNames" style="width:153px" :clearable="true" multiple collapse-tags filterable  placeholder="岗位">
                <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName" :value="item.titleName"/>
              </el-select>


              <el-select  v-show="activeName=='first4'"  v-model="filter.deptId" style="width:160px" :clearable="true" filterable multiple collapse-tags placeholder="架构">
                <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>

              <el-switch  v-if="activeName=='ZrApplyInnerMng'"   v-model="filter.aboutMe"  active-text="与我有关" active-value="1" inactive-value="0" ></el-switch>
          <el-select v-if="activeName=='ZrApplyInnerMng'"  v-model="filter.applyState" style="width:90px;" placeholder="申诉状态" :clearable="true">
                            <el-option v-for="item in applyStates" :label="item.label" :value="item.value"></el-option>
                        </el-select>



            <el-input
              v-model.trim="filter.keywords"
              style="width: 240px"
               placeholder="关键字查询"
              @keyup.enter.native="onSearch"
              clearable
              :maxLength="100"
            >
            <el-tooltip  v-if="activeName=='ZrApplyInnerMng'"  slot="suffix" class="item" effect="dark" content="订单号、申诉内容、新老责任人、审核内容" placement="bottom">
                                <i class="el-input__icon el-icon-question"></i>
                            </el-tooltip>
            <el-tooltip v-else slot="suffix" class="item" effect="dark" content="订单号、快递、商品名、店铺、责任规则" placement="bottom">
                                <i class="el-input__icon el-icon-question"></i>
                            </el-tooltip>
          </el-input>



            <el-button type="primary" @click="onSearch" >查询</el-button>


      </template>

      <el-tabs v-model="activeName" style="height: 94%" @tab-click="onSwitching">
        <el-tab-pane label="采购扣款总额" name="first4" style="height: 100%">
          <OrderIllegaldetailTotal
            :filter="filter"
            :showTableHeaderHandles="false"
            ref="orderillegaldetailTotal"
            style="height: 100%"
          ></OrderIllegaldetailTotal>
        </el-tab-pane>
        <el-tab-pane label="拼多多扣款详情" name="first1" style="height: 100%" lazy>
          <OrderIllegaldetailPdd
            :filter="filter"
            :showTableHeaderHandles="false"
            ref="orderillegaldetail"
            style="height: 100%"
          ></OrderIllegaldetailPdd>
        </el-tab-pane>
        <el-tab-pane label="淘系扣款详情" name="first2" style="height: 100%" lazy>
          <OrderIllegaldetailTx
            :filter="filter"
            :platforms="[1,9]"
            :showTableHeaderHandles="false"
            ref="orderillegaldetailTx"
            style="height: 100%"
          ></OrderIllegaldetailTx>
        </el-tab-pane>
        <el-tab-pane label="淘工厂扣款详情" name="first3" style="height: 100%" lazy>
          <OrderIllegaldetailTx
            :filter="filter"
            :platforms="[8]"
            :showTableHeaderHandles="false"
            ref="orderillegaldetailTgc"
            style="height: 100%"
          ></OrderIllegaldetailTx>
        </el-tab-pane>
        <el-tab-pane label="抖音扣款详情" name="first5" style="height: 100%" lazy>
          <OrderIllegaldetailDou
            :filter="filter"
            :platforms="[8]"
            :showTableHeaderHandles="false"
            ref="OrderIllegaldetailDou"
            style="height: 100%"
          ></OrderIllegaldetailDou>
        </el-tab-pane>
        <el-tab-pane label="快手扣款详情" name="first6" style="height: 100%" lazy>
          <OrderIllegaldetailKs
            :filter="filter"
            :showTableHeaderHandles="false"
            ref="OrderIllegaldetailKs"
            style="height: 100%"
          ></OrderIllegaldetailKs>
        </el-tab-pane>


        <!-- <el-tab-pane label="内部责任申诉审核" name="ZrApplyInnerMng" style="height: 100%">
          <DeductZrMemberApplyList4Cg
            :filter="filter"
            :showTableHeaderHandles="false"
            ref="DeductZrMemberApplyList4Cg"
            style="height: 100%"
          ></DeductZrMemberApplyList4Cg>


        </el-tab-pane> -->
      </el-tabs>
    </my-container>
  </my-container>
</template>

<script>
import {
  getDirectorList,
  getDirectorGroupList,
  getProductBrandPageList,
  getList as getshopList,
} from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
import { rulePlatform, ruleIllegalType } from "@/utils/formruletools";
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import { sendwarehouselist as warehouselist} from "@/utils/tools";
import OrderIllegaldetailPdd from "@/views/inventory/purchaser/OrderIllegaldetailPdd.vue";
import OrderIllegaldetailTx from "@/views/inventory/purchaser/OrderIllegaldetailTx.vue";
import OrderIllegaldetailTotal from "@/views/inventory/purchaser/OrderIllegaldetailTotal.vue";
import DeductZrMemberApplyList4Cg from "@/views/inventory/purchaser/DeductZrMemberApplyList4Cg.vue";
import OrderIllegaldetailDou from "@/views/inventory/purchaser/OrderIllegaldetailDou.vue";
import OrderIllegaldetailKs from "@/views/inventory/purchaser/OrderIllegaldetailKs.vue";
import { getPurchaseDeductionOnusDeptList } from '@/api/inventory/purchaseordernew'

import {
  getWithholdSum,
  getWitholdCompany,
  getWithGroupSum,
  getOrderWithholdBrandRegionList
} from "@/api/order/orderdeductmoney";
import { getDutyDept } from "@/api/order/ordererror";
import {getAllWarehouse,getAllProBrand,getBianManPositionListV2 } from '@/api/inventory/warehouse'

const applyStates=[
  {label:'全部',value:null},
  {label:'申诉中',value:1},
  {label:'已审核',value:2},
  {label:'已拒绝',value:-1},
]


export default {
  name: "OrderIllegal",
  components: {
    cesTable,
    MyContainer,
    MyConfirmButton,
    OrderIllegaldetailPdd ,
    OrderIllegaldetailTx  ,
    OrderIllegaldetailDou,
    OrderIllegaldetailKs,
    OrderIllegaldetailTotal,
    DeductZrMemberApplyList4Cg
  },
  data() {
    return {
      that: this,
      activeName: "first4",
      applyStates:applyStates,
      shopList: [],
      deptList: [],
      directorList: [],
      directorGroupList: [],
      groupSumList: [],
      illegalTypeList: [],
      filter: {
        dutyDept: "",
        startDate: null,
        endDate: null,
        startSendDate: null,
        endSendDate: null,
        startPayDate: null,
        endPayDate: null,
        platform: null,
        deptId: [],
        titleNames: [],
        brandIdList: [],
        brandRegions: [],
        shopId: null,
        groupId: null,
        proCode: null,
        sendWarehouse: null,
        wmsCoId:null,
        illegalType: null,
        expressCompany: null,
        operateSpecialId: null,
        occurrenceTime: null,
        timerange: [
          // formatTime(dayjs().subtract(2, "day"), "YYYY-MM-DD"),//默认前两天
          dayjs().startOf('month').format('YYYY-MM-DD'),//默认本月
          formatTime(new Date(), "YYYY-MM-DD"),
        ],
        timerange2:[],
        timerange3:[],
        ysLx:'',
        zrDept:'采购',
        brandId:null,
        brandRegion:null,
        titleName:null
      },
      purchasegrouplist: [],
      deptList: [],
      warehouselist: warehouselist,
      newWareHouseList:[],
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
              {
                text: '昨天',
                onClick(picker) {
                  const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                  const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                  start.setTime(start.getTime() );
                  picker.$emit('pick', [start, start]);
                }
              },  {
                text: '近三天',
                onClick(picker) {
                  const tdate = new Date(new Date().getTime()  );
                  const end = new Date(new Date(tdate.toLocaleDateString()));
                  const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                  start.setTime(start.getTime() - 3600 * 1000 * 24*3);
                   end.setTime(end.getTime() - 3600 * 1000 * 24);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '近一周',
                onClick(picker) {
                  const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                  const end = new Date(new Date(tdate.toLocaleDateString()).getTime()+ 3600 * 1000 * 24 * 5);
                  const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                  start.setTime(start.getTime() - 3600 * 1000 * 24*2);
                  end.setTime(end.getTime() - 3600 * 1000 * 24);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '近一个月',
                onClick(picker) {
                  const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                  console.log("获取前一个月的时间",tdate.getDay());
                  const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                  const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                  start.setTime(start.getTime() - 3600 * 1000 * 24);
                  end.setTime(end.getTime() - 3600 * 1000 * 24);
                  picker.$emit('pick', [start, end]);
                }
              }]
      },
      platformList: [],
      dialogVisible: false,
      showDetailVisible: false,
      total: 0,
      sels: [],
      listLoading: false,
      pageLoading: false,
      addFormVisible: false,
      addLoading: false,
      deleteLoading: false,
      uploadLoading: false,
      fileHasSubmit: false,
      formtitle: "新增",
      fileList: [],
      selids: [], //选择的id

      brandlist: [],
      brandRegionlist: [],
      positionList:[]
    };
  },
  async mounted() {
    await this.setPlatform();
    await this.setBandSelect()
    await this.setBrandRegionSelect();
    await this.onSearch();
    await this.getDirectorlist();
    await this.setDutyDeptCondition();
    await this.onchangeplatform();

    var res = await getAllWarehouse();
    this.newWareHouseList=res.data.filter((x)=> {return x.name.indexOf('代发')<0;});

    var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;

    //采购组
    let { data: deptList, success } = await getPurchaseDeductionOnusDeptList();
    if (success) {
      this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
    }
  },
  methods: {
      onSwitching(){
        if(this.activeName == 'first4'){
          this.filter.deptId = [];
          this.filter.titleNames = [];
          this.filter.brandRegion = null;
          this.filter.brandId = null;
        }
      },
      async setBandSelect(){
      var res= await  getAllProBrand();
          if (!res?.success) return;
          this.brandlist = res.data;
      },
      async setBrandRegionSelect(){
      var res= await  getOrderWithholdBrandRegionList();
          if (!res?.success) return;
          this.brandRegionlist = res.data;
          this.brandRegionlist.push(
            { key: "深圳", value: "深圳" },
            { key: "武汉", value: "武汉" }
          );
      },

    //设置部门下拉
    async setDutyDeptCondition() {
      const res = await getDutyDept();
      this.deptList = res.data;
    },
    //设置平台下拉
    async setPlatform() {
      var pfrule = await rulePlatform();
      this.platformList = pfrule.options;
      var illegalType = await ruleIllegalType();
      this.illegalTypeList = illegalType.options;
      this.illegalTypeList.push({value:-1,label:"总扣款"});
      this.illegalTypeList.push({value:-2, label:'非总扣款'});
    },
    async onchangeplatform(val) {
      this.categorylist = [];
      const res1 = await getshopList({
        platform: val,
        CurrentPage: 1,
        PageSize: 10000,
      });
      this.shopList = res1.data.list;
    },

    async getDirectorlist() {
      const res1 = await getDirectorList({});
      const res2 = await getDirectorGroupList({});

      this.directorList = res1.data;
      this.directorGroupList = [{ key: "0", value: "未知" }].concat(
        res2.data || []
      );
    },

    async onSearch() {
      if (this.filter.timerange && this.filter.timerange.length > 1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      } else {
        this.$message({ message: "请先选择日期", type: "warning" });
        return false;
      }

      if (this.filter.timerange2&&this.filter.timerange2.length>1) {
        this.filter.startPayDate = this.filter.timerange2[0];
        this.filter.endPayDate = this.filter.timerange2[1];
      }


      this.filter.zrDept="采购";
      this.$nextTick(() => {
        if (this.activeName == "first1")
          this.$refs.orderillegaldetail.onSearch();
        else if (this.activeName == "first2")
          this.$refs.orderillegaldetailTx.onSearch();
        else if (this.activeName == "first3")
          this.$refs.orderillegaldetailTgc.onSearch();
        else if (this.activeName == "first4")
          this.$refs.orderillegaldetailTotal.onSearch();
        else if (this.activeName == "first5")
          this.$refs.OrderIllegaldetailDou.onSearch();
        else if (this.activeName == "ZrApplyInnerMng")
          this.$refs.DeductZrMemberApplyList4Cg.onSearch();
        else if (this.activeName == "first6")
          this.$refs.OrderIllegaldetailKs.onSearch();

      });
    },

  },
};
</script>

<style lang="scss" scoped>
//控制选择器多选-标签宽度
::v-deep .el-select__tags-text {
  max-width: 30px;
}
</style>
