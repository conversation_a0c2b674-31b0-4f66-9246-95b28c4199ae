<template>
    <container v-loading="pageLoading">
        <!-- 岗位 -->
        <template #header>
            <div style="display: flex;margin-bottom: 20px;">
                <el-select v-model="filter.company" clearable placeholder="分公司" style="width: 150px;margin-right: 10px;">
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                </el-select>
                <el-select v-model="filter.brandName" filterable clearable placeholder="岗位" @change="changeSetPost"
                    style="width: 150px;margin-right: 10px;">
                    <el-option v-for="item in postList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onAdd">新增</el-button>
                <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
            </div>
        </template>
        <!--列表-->
        <vxetablebase :id="'purchasepost20230701'" :tablekey="'purchasepost20230701'" :tableData='list' :tableCols='tableCols'
            @cellClick='cellclick' @select='selectchange' :tableHandles='tableHandles' :loading='listLoading' :border='true'
            :that="that" ref="vxetable" @sortchange='sortchange'>
            <template slot="right">
                <vxe-column title="操作" :field="'col_opratorcol'" width="220" fixed="right">
                    <template #default="{ row }">
                        <template v-if="row.parentId == null">
                            <el-button type="text" size="default" @click="onHand(row, 1)">编辑</el-button>
                            <el-button type="text" size="default" @click="onHand(row, 2)">{{ row.enabled == true ? "禁用" :
                                "启用" }}</el-button>
                            <!-- <el-button type="text" size="default" @click="onHand(row, 3)">计算</el-button> -->
                        </template>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="title" :visible.sync="dialogShowInfoVisible" v-dialogDrag width='65%'
            :close-on-click-modal="false" height='800px'>
            <opearpurchasepost :filter="detailfilter" @onClose="onClose" ref="opearpurchasepost"></opearpurchasepost>
        </el-dialog>

    </container>
</template>

<script>
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getPurchasePostWagesAsync, getPurchaseWagesCalPositions, opearPurchasePostWagesAsync, handComputeWagesAsync, exportPurchasePostWagesAsync } from '@/api/profit/purchasepostwages';
import opearpurchasepost from './opearpurchasepost.vue';
import dailyPicker from './dailyPicker.vue'

const tableCols = [
    { istrue: true, prop: 'company', align: 'center', label: '分公司', width: 'auto', sortable: 'custom', },
    { istrue: true, prop: 'purchasePost', label: '岗位', width: 'auto', sortable: 'custom', },
    // { istrue: true, prop: 'getValueType', label: '取值', width: 'auto', sortable: 'custom', },
];
const tableHandles = [
    //{ label: "导出", throttle: 3000, handle: (that) => that.onExport() },
];


export default {
    name: 'YunHanAdminGoodsFinishedpart',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan, opearpurchasepost,dailyPicker },
    data () {
        return {
            that: this,
            filter: {
                purchasePost: null,
                brandName: null,
                positions: [],//岗位
            },
            addForm: {
                id: null,
                diffHours: null,
                part_Count: 0,
            },
            detailfilter: {},
            rules: {
                diffHours: [
                    { required: true, message: '请选择开单间隔', trigger: 'change' },
                ],
                part_Count: [
                    { required: true, message: '请输入半成品数量', trigger: 'blur' },
                ],
            },
            editFilter: {
                selData: [],
                type: null
            },
            title: '',
            keywordsTip: '支持搜索的内容：采购单号、Erp单号',
            titile: '批量开启/关闭',
            brandlist: [],
            list: [],
            postList: [],
            postListAll: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createdTime", IsAsc: false },
            total: 0,
            sels: [],
            chooseTags: [],
            selids: [],
            onExporting: false,
            listLoading: false,
            pageLoading: false,
            dialogAddVisible: false,
            dialogEditVisible: false,
            dialogShowInfoVisible: false,
            onFinishLoading: false,
            dialogLoading: false
        };
    },

    async mounted () {
        await this.onSearch();
        await this.init();
    },

    methods: {
        async changeSetPost (e) {
            if (e !== null && e !== undefined && e !== '') {
                //清空岗位
                this.filter.positions = [];
                //根据e找出对应的岗位
                this.filter.purchasePost = this.postList.find((item) => item.value === e).label
            } else {
                this.filter.purchasePost = null
            }
        },
        computeDialog () {
            this.showComputeDialog = true
        },
        async init () {
            const { data, success } = await getPurchaseWagesCalPositions()
            if (!success) {
                return
            } else {
                this.postList = data.map((item, i) => {
                    return {
                        label: item,
                        value: i,
                    }
                })
            }
        },
        async onSearch () {
            this.$refs.pager.setPage(1)
            this.getlist();
            this.selids = [];
        },
        //分页查询
        async getlist () {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const { data, success } = await getPurchasePostWagesAsync(params);
            this.listLoading = false
            if (!success) {
                return
            }
            this.total = data.total;
            this.list = data.list;
            this.summaryarry = data.summary;
        },
        editFormvalidate () {
            let isValid = false
            this.$refs.ruleForm.validate(valid => {
                isValid = valid
            })
            return isValid
        },
        async onHand (row, val) {
            if (val == 1) {
                this.title = '岗位编辑';
                this.dialogShowInfoVisible = true;
                this.detailfilter = { ...row }
                this.$nextTick(async () => {
                    this.$refs.opearpurchasepost.onSearch(true);
                })
            } else if (val == 2) {
                var enabled = !row.enabled;
                var para = { id: row.id, enabled: enabled };
                this.listLoading = true;
                var res = await opearPurchasePostWagesAsync(para);
                this.listLoading = false;
                if (res?.success) {
                    this.$message({ type: 'success', message: '操作成功!' });
                    await this.getlist();
                } else { }
            } else if (val == 3) {
                this.$confirm('此操作将计算薪资, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    var para = { purchasePost: row.purchasePost, company: row.company };
                    var res = await handComputeWagesAsync(para);
                    if (res?.success) {
                        this.$message({ type: 'success', message: '操作成功，正在计算中!' });
                        await this.getlist();
                    } else { }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消操作'
                    });
                });
            }
        },
        async onAdd () {
            this.title = '岗位新增';
            this.dialogShowInfoVisible = true;
            this.$nextTick(async () => {
                this.$refs.opearpurchasepost.onSearch(false);
            })
        },
        async onClose (val) {
            this.dialogShowInfoVisible = false;
            if (val) {
                await this.getlist();
            }
        },
        async onExport () {
            if (this.onExporting) return;
            try {
                const params = { ... this.filter }
                var res = await exportPurchasePostWagesAsync(params);
                if (!res?.data) return
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', '采购岗位工价_' + new Date().toLocaleString() + '.xlsx')
                aLink.click()
            } catch (err) {
                console.log(err)
            }
            this.onExporting = false;
        },
        async cellclick ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column.property == 'goodsCode') {
                let selData = { goodsCode: row.goodsCode };
                this.dialogShowInfoVisible = true;
                this.$nextTick(async () => {
                    this.$refs.goodsfinishedpartdetail.loadData({ selRows: selData, });
                });
            }
        },
        sortchange (column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number.is-controls-right .el-input__inner {
    text-align: left !important;
}

.btnBox {
    display: flex;
    justify-content: space-around;
}

.pickerBox {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}
</style>
