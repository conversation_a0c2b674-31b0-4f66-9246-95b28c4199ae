<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="开始付款日期" end-placeholder="结束付款日期" :picker-options="pickerOptions"
                    style="width: 270px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 1)">
                </el-date-picker>
                <el-date-picker v-model="scanRanges" type="daterange" unlink-panels range-separator="至"
                    start-placeholder="贴单时间" end-placeholder="贴单时间" :picker-options="pickerOptions"
                    style="width: 270px;margin-right: 10px;" :value-format="'yyyy-MM-dd'"
                    @change="changeTime($event, 2)">
                </el-date-picker>
                <el-select v-model="ListInfo.repeatedlyType" placeholder="类型" class="publicCss" clearable>
                    <el-option label="退件转售后" value="退件转售后" />
                    <el-option label="售后转退件" value="售后转退件" />
                    <el-option label="二次退件" value="二次退件" />
                    <el-option label="二次售后" value="二次售后" />
                </el-select>
                <el-select v-model="ListInfo.platform" placeholder="平台" class="publicCss" clearable>
                    <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model="ListInfo.shopName" placeholder="店铺" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.skus" placeholder="SKUS" maxlength="50" clearable class="publicCss" />
                <el-input v-model="ListInfo.orderNoInner" placeholder="原内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.newOrderNoInner" placeholder="新内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model="ListInfo.printerName" placeholder="贴单人" maxlength="50" clearable class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase :id="'errorStaus202408041748'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'"
            :tree-config="{}">
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions, platformlist, formatPlatform } from '@/utils/tools'
import dayjs from 'dayjs'
import middlevue from "@/store/middle.js"
import { pageGetRepeatedlys, getRepeatedlyColumns, exportRepeatedlys } from '@/api/vo/ReturnOrderScan'
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase
    },
    data() {
        return {
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                repeatedlyType: null,//类型
                platform: null,//平台
                shopName: null,//店铺
                payStartTime: null,//开始时间
                payEndTime: null,//结束时间
                orderNoInner: null,//内部订单号
                newOrderNoInner: null,//新内部订单号
                skus: null,//skus
                printerName: null,//贴单人
            },
            timeRanges: [],
            scanRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: false,
            pickerOptions,
            isExport: false
        }
    },
    async mounted() {
        await middlevue.$on('queryErrorsList', (e) => {
            this.ListInfo.printerName = e.printer
            this.ListInfo.printStartTime = e.startDate
            this.ListInfo.printEndTime = e.endDate
            this.ListInfo.repeatedlyType = e.repeatedlyType
            this.scanRanges = [e.startDate, e.endDate]
            this.$nextTick(async () => {
                await this.getList()
            })
        })
        await this.getCol()
        await this.getList()
    },
    //销毁
    beforeDestroy() {
        middlevue.$off('queryErrorsList')
    },
    methods: {
        async getLinkList(params) {
            this.ListInfo = { ...ListInfo, ...params }
            await this.getList()
        },
        async getCol() {
            const { data, success } = await getRepeatedlyColumns()
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'orderNoInner' || item.prop == 'newOrderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                })
                this.tableCols = data
            }
        },
        async changeTime(e, val) {
            if (val == 1) {
                this.ListInfo.payStartTime = e ? e[0] : null
                this.ListInfo.payEndTime = e ? e[1] : null
            } else {
                this.ListInfo.printStartTime = e ? e[0] : null
                this.ListInfo.printEndTime = e ? e[1] : null
            }

            await this.getList()
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportRepeatedlys(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '异常数据' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            //替换空格的方法,该数组对应str类型的input双向绑定的值
            this.ListInfo = replaceSpace(['orderNo', 'orderNoInner', 'skus', 'printerName', 'shopName', 'newOrderNoInner'], this.ListInfo)
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            console.log(this.ListInfo, 'this.ListInfo');
            const { data: { list, total }, success } = await pageGetRepeatedlys(this.ListInfo)
            if (success) {
                this.tableData = list
                this.total = total
                this.loading = false
            } else {
                //获取列表失败
                this.loading = false
                this.$message.error('获取列表失败')
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop == 'platformStr' ? 'platform' : prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
