<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-form class="ad-form-query" :inline="true" :model="ListInfo" @submit.native.prevent>
                    <el-form-item>
                        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
                            :clearable="false" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions" style="width: 220px;" :value-format="'yyyy-MM-dd'"
                            @change="changeTime">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <button style="padding: 0; border: none;">
                            <inputYunhan title="请分行输入系列编码" placeholder="系列编码/若输入多条请按回车" :maxRows="100" :inputshow="0"
                                :clearable="true" @callback="styleCallback" :inputt.sync="styleCodes"></inputYunhan>
                        </button>
                        <!-- <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" style="width: 130px" clearable /> -->
                    </el-form-item>

                    <el-form-item>
                        <el-select style="width: 130px;" v-model="ListInfo.bzCategory" placeholder="经营大类"
                            :collapse-tags="true" remote :remote-method="remoteMethodBusinessCategory" clearable
                            filterable>
                            <el-option label="未绑定" value="未绑定" />
                            <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="i" :label="item"
                                :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select style="width: 130px;" v-model="ListInfo.bzCategory1" placeholder="一级类目"
                            :collapse-tags="true" remote :remote-method="remoteMethodCategoryName1s" clearable
                            filterable>
                            <el-option label="未绑定" value="未绑定" />
                            <el-option v-for="(item, i) in filterList.categoryName1s" :key="i" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select style="width: 130px;" v-model="ListInfo.bzCategory2" placeholder="二级类目"
                            :collapse-tags="true" remote :remote-method="remoteMethodCategoryName2s" clearable
                            filterable>
                            <el-option label="未绑定" value="未绑定" />
                            <el-option v-for="(item, i) in filterList.categoryName2s" :key="i" :value="item" />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-input-number v-model="ListInfo.zjzySumStart" :min="0" :controls="false" placeholder="≥资金占用"
                            style="width:80px;" :max="9999999999"></el-input-number>
                        -
                        <el-input-number v-model="ListInfo.zjzySumEnd" :min="0" :controls="false" placeholder="<资金占用"
                            style="width:80px;" :max="9999999999"></el-input-number>
                    </el-form-item>

                    <el-form-item>
                        <el-input-number v-model="ListInfo.jsyStart" :controls="false" placeholder="≥净收益"
                            style="width:80px;" :max="9999999999"></el-input-number>
                        -
                        <el-input-number v-model="ListInfo.jsyEnd" :controls="false" placeholder="<净收益"
                            style="width:80px;" :max="9999999999"></el-input-number>
                    </el-form-item>

                    <el-form-item>
                        <el-input-number v-model="ListInfo.zjHblStart" :controls="false" placeholder="≥年化回报率"
                            style="width:80px;" :max="9999999999"></el-input-number>
                        -
                        <el-input-number v-model="ListInfo.zjHblEnd" :controls="false" placeholder="<年化回报率"
                            style="width:80px;" :max="9999999999"></el-input-number>
                    </el-form-item>

                    <el-form-item label="查询破损数据">
                        <el-switch v-model="ListInfo.showDamaged" :active-value="true" :inactive-value="false">
                        </el-switch>
                    </el-form-item>

                    <el-form-item label="更多查询条件">
                        <el-switch v-model="showRptFilter" :active-value="true" :inactive-value="false"> </el-switch>
                    </el-form-item>

                    <template v-if="showRptFilter">


                        <el-form-item>
                            <el-input-number v-model="ListInfo.zjsyStart" :controls="false" placeholder="≥资金收益"
                                style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.zjsyEnd" :controls="false" placeholder="<资金收益"
                                style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>

                        <el-form-item>
                            <el-input-number v-model="ListInfo.lcsyStart" :controls="false" placeholder="≥理财收益"
                                style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.lcsyEnd" :controls="false" placeholder="<理财收益"
                                style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>

                        <el-form-item>
                            <el-input-number v-model="ListInfo.extKczjQxStart" :min="0" :controls="false"
                                placeholder="≥本系含借出库存资金" style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.extKczjQxEnd" :min="0" :controls="false"
                                placeholder="<本系含借出库存资金" style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>

                        <el-form-item>
                            <el-input-number v-model="ListInfo.extKczjXlStart" :min="0" :controls="false"
                                placeholder="≥本系含借入库存资金" style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.extKczjXlEnd" :min="0" :controls="false"
                                placeholder="<本系含借入库存资金" style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>


                        <el-form-item>
                            <el-input-number v-model="ListInfo.extCgztQxStart" :min="0" :controls="false"
                                placeholder="≥本系含借出采购在途" style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.extCgztQxEnd" :min="0" :controls="false"
                                placeholder="<本系含借出采购在途" style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>

                        <el-form-item>
                            <el-input-number v-model="ListInfo.extCgztXlStart" :min="0" :controls="false"
                                placeholder="≥本系含借入采购在途" style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.extCgztXlEnd" :min="0" :controls="false"
                                placeholder="<本系含借入采购在途" style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>

                        <el-form-item>
                            <el-input-number v-model="ListInfo.extXsztQxStart" :min="0" :controls="false"
                                placeholder="≥销售在途" style="width:80px;" :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.extXsztQxEnd" :min="0" :controls="false"
                                placeholder="<销售在途" style="width:80px;" :max="9999999999"></el-input-number>
                        </el-form-item>


                        <el-form-item label="资金周转率">
                            <el-input-number v-model="ListInfo.zjZzlStart" :controls="false" style="width:80px;"
                                :max="9999999999" placeholder=">=资金周转率"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.zjZzlEnd" :controls="false" style="width:80px;"
                                :max="9999999999" placeholder="<资金周转率"></el-input-number>
                        </el-form-item>

                        <!-- <el-form-item label="回款天数">
                            <el-input-number v-model="ListInfo.hkDaysStart" :controls="false" style="width:80px;"
                                :max="9999999999"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.hkDaysEnd" :controls="false" style="width:80px;"
                                :max="9999999999"></el-input-number>
                        </el-form-item> -->


                        <el-form-item label="订单量">
                            <el-input-number v-model="ListInfo.orderCountStart" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=订单量"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.orderCountEnd" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<订单量"></el-input-number>
                        </el-form-item>

                        <el-form-item label="主卖数量">
                            <el-input-number v-model="ListInfo.meSalesQtyStart" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=主卖数量"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.meSalesQtyEnd" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<主卖数量"></el-input-number>
                        </el-form-item>


                        <el-form-item label="借卖数量">
                            <el-input-number v-model="ListInfo.heSalesQtyStart" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=借卖数量"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.heSalesQtyEnd" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<借卖数量"></el-input-number>
                        </el-form-item>

                        <el-form-item label="销售金额">
                            <el-input-number v-model="ListInfo.saleAmtStart" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=销售金额"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.saleAmtEnd" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<销售金额"></el-input-number>
                        </el-form-item>

                        <el-form-item label="销售成本">
                            <el-input-number v-model="ListInfo.saleCostStart" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=销售成本"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.saleCostEnd" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<销售成本"></el-input-number>
                        </el-form-item>

                        <el-form-item label="扣款金额">
                            <el-input-number v-model="ListInfo.deductAmtStart" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=扣款金额"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.deductAmtEnd" :min="0" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<扣款金额"></el-input-number>
                        </el-form-item>

                        <el-form-item label="毛1">
                            <el-input-number v-model="ListInfo.profit1Start" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=毛1"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.profit1End" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<毛1"></el-input-number>
                        </el-form-item>

                        <el-form-item label="毛2">
                            <el-input-number v-model="ListInfo.profit2Start" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=毛2"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.profit2End" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<毛2"></el-input-number>
                        </el-form-item>

                        <el-form-item label="毛3">
                            <el-input-number v-model="ListInfo.profit3Start" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=毛3"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.profit3End" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<毛3"></el-input-number>
                        </el-form-item>

                        <el-form-item label="毛4">
                            <el-input-number v-model="ListInfo.profit33Start" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder=">=毛4"></el-input-number>
                            -
                            <el-input-number v-model="ListInfo.profit33End" :min="-1000000" :controls="false"
                                style="width:80px;" :max="9999999999" placeholder="<毛4"></el-input-number>
                        </el-form-item>


                        <el-form-item>
                            <el-select v-model="ListInfo.platform" placeholder="平台" :clearable="true"
                                :collapse-tags="true" filterable @change="onchangeplatform" style="width: 100px">
                                <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-select v-model="ListInfo.shopCode" style="width: 150px" placeholder="店铺"
                                :clearable="true" :collapse-tags="true" filterable>
                                <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                                    :value="item.shopCode" />
                            </el-select>
                        </el-form-item>

                        <el-form-item>
                            <el-select v-model="ListInfo.groupId" style="width: 110px" placeholder="运营组"
                                :clearable="true" :collapse-tags="true" filterable>
                                <el-option v-for="item in groupList" :key="item.key" :label="item?.value"
                                    :value="item.key" />
                            </el-select>
                        </el-form-item>


                    </template>

                    <el-form-item>
                        <el-date-picker v-model="timeRangesStyleCreate" type="daterange" unlink-panels
                            range-separator="至" :clearable="true" start-placeholder="≥系列创建日期" end-placeholder="≤系列创建日期"
                            :picker-options="pickerOptions" style="width: 220px;" :value-format="'yyyy-MM-dd'"
                            @change="changeTimeStyleCreate">
                        </el-date-picker>
                    </el-form-item>


                    <!-- <el-form-item label="采购组:">
                        <el-select v-model="ListInfo.brandId" clearable filterable placeholder="请选择采购员"
                            style="width: 140px" :collapse-tags="true">
                            <el-option v-for="item in brandlist" :key="item?.value" :label="item.label"
                                :value="item?.value" />
                        </el-select>
                    </el-form-item> -->
                    <!--
                    <el-form-item label="产品ID:">
                        <el-input v-model.trim="ListInfo.proCode" placeholder="产品ID" style="width: 130px" clearable />
                    </el-form-item>
                    <el-form-item label="毛二正负利润:" v-if="checkPermission('prosameprofit')">
                        <el-select filterable v-model="ListInfo.profit2UnZero" collapse-tags clearable placeholder="毛利2"
                            style="width: 100px">
                            <el-option label="全部" />
                            <el-option label="正利润" :value="false" />
                            <el-option label="负利润" :value="true" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="毛三正负利润:" v-if="checkPermission('prosameprofit')">
                        <el-select filterable v-model="ListInfo.profit3UnZero" collapse-tags clearable placeholder="毛利3"
                            style="width: 100px">
                            <el-option label="全部" />
                            <el-option label="正利润" :value="false" />
                            <el-option label="负利润" :value="true" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="毛四正负利润:" v-if="checkPermission('prosameprofit')">
                        <el-select filterable v-model="ListInfo.profit33UnZero" collapse-tags clearable
                            placeholder="毛利4" style="width: 100px">
                            <el-option label="全部" />
                            <el-option label="正利润" :value="false" />
                            <el-option label="负利润" :value="true" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态:" v-if="checkPermission('prosameprofit')">
                        <el-select v-model="ListInfo.state" placeholder="请选择" clearable :collapse-tags="true" filterable
                            style="width: 100px">
                            <el-option v-for="item in prosimstatelist" :key="item.state" :label="item.state"
                                :value="item.state" />
                        </el-select>
                    </el-form-item> -->
                    <el-form-item>
                        <el-select v-model="ListInfo.versionId" style="width: 150px" placeholder="拍照版本"
                            :clearable="true" :collapse-tags="true" filterable>
                            <el-option v-for="item in versionList" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-cascader style="width: 200px;" placeholder="发货地" collapse-tags clearable :options="options1"
                            :props="{ multiple: true, checkStrictly: true, filterable: true }"
                            v-model="ListInfo.provinceCodes1" filterable class="publicCss cascaderCss"></el-cascader>
                    </el-form-item>
                    <!-- <el-form-item>
                        <YhUserelector :value.sync="ListInfo.ownerId" maxlength="50" :text.sync="ListInfo.owners"
                            :Unbound="true" style="width:80%;" placeholder="请输入负责人">
                        </YhUserelector>
                    </el-form-item> -->
                    <el-form-item>
                        <el-button type="primary" @click="getList('search')">搜索</el-button>
                        <el-button type="primary" @click="onExport()">导出</el-button>
                        <el-button type="primary" @click="photograph()">拍照</el-button>
                        <el-button type="primary" @click="editCategory"
                            v-if="checkPermission('editCategory')">修改类目</el-button>
                        <el-button type="primary" @click="importProps"
                            v-if="checkPermission('importChargePerson')">导入</el-button>
                        <!-- <el-button type="primary" @click="dowmLoadStyleCodeFile"
                            v-if="checkPermission('importChargePerson')">系列编码负责人导入模版</el-button> -->
                    </el-form-item>
                    <el-form-item v-if="rltExtData && rltExtData.calcDataTag && !loading">
                        <span>
                            日报[{{ rltExtData.rptDateMin }}~{{ rltExtData.rptDateMax }}]
                            计算时间[{{ rltExtData.verDate }}]
                            数据[
                            <span :style="rltExtData.calcDataTag['全平台日报'] ? 'color:green' : 'color:red'">全平台日报</span>|
                            <span :style="rltExtData.calcDataTag['分销日报'] ? 'color:green' : 'color:red'">分销日报</span>|
                            <span :style="rltExtData.calcDataTag['库存资金'] ? 'color:green' : 'color:red'">库存资金</span>|
                            <span :style="rltExtData.calcDataTag['采购在途'] ? 'color:green' : 'color:red'">采购在途</span>|
                            <span :style="rltExtData.calcDataTag['销售在途'] ? 'color:green' : 'color:red'">销售在途</span>
                            ] 绿色代表已导入
                        </span>
                    </el-form-item>
                </el-form>
            </div>
        </template>
        <vxetablebase :id="'styleCodeReports2_240804111601'" ref="table" :that='that' :isIndex='true' :hasexpand='true'
            :tablefixed='true' :showsummary="true" :summaryarry="summaryarry" @sortchange='sortchange'
            @select="selectCheckBox" :showheaderoverflow="false" :tableData='tableData' :tableCols='tableCols'
            :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0" v-loading="loading"
            :border="true" :height="'100%'">

        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="趋势图" :visible.sync="dialogMapVisible.visible" size="80%" v-dialogDrag
            v-loading="dialogMapVisible.loading" direction="btt">
            <div style="display: flex;align-items: center;">
                <strong>{{ dialogMapVisible.title }}</strong>
                <dateRange :startDate.sync="dialogMapVisible.startDate" :endDate.sync="dialogMapVisible.endDate"
                    :clearable="false" isOtherPicker :OtherPickerOptions="seriesTimePickerOptions"
                    style="width: 220px;margin:0 10px;" />
                <!-- <el-select style="width: 200px;margin:0 10px;" v-model="dialogMapVisible.platform" 
                    placeholder="平台" :collapse-tags="true" clearable filterable>
                    <el-option v-for="(item, i) in platformlist" :key="i" :value="item.value" :label="item.label" />
                </el-select> -->
                <el-button type="primary"
                    @click="showDetailAnalysis(dialogMapVisible, dialogMapVisible.type, false)">查询</el-button>
            </div>
            <div>
                <span>
                    <buschar ref="dialogMapVisibleBuschar" v-if="dialogMapVisible.chartvisible"
                        :analysisData="dialogMapVisible.data"
                        :thisStyle="{ width: '100%', height: '600px', 'box-sizing': 'border-box', 'line-height': '360px' }">
                    </buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-drawer>


        <el-dialog v-dialogDrag :title="`${chartsData.styleCode ? chartsData.styleCode : '无'}-${title}`"
            :visible.sync="detailsVisable" width="75%">
            <div class="condition">
                <el-button type="text" v-throttle="1000" @click="nextCharts('last')"> 上一个</el-button>
                <el-button type="text" v-throttle="1000" @click="nextCharts('next')"> 下一个</el-button>
                <!-- <el-button type="text"> 查看趋势图</el-button> -->
                <div class="top">
                    <el-tag v-for="item in echartsList" :key="item.value" :type="propStr == item.prop ? '' : 'info'"
                        @click="command(item)" size="medium" class="tag">{{
                            item.label
                        }}</el-tag>
                </div>
            </div>
            <styleCodeReportDetails v-if="detailsVisable1" :data="detailsData" :isPlatform="isPlatform"
                :detailsLabel="detailsLabel" />
        </el-dialog>

        <vxe-modal v-model="detail.visible" width="90%" marginSize='-500' title="系列编码" resize>
            <template #default>
                <div style="display: flex;">
                    <div style="width: 80%;">
                        <el-button type="primary" @click="onSearchDetail">查询</el-button>
                        <el-button type="primary" @click="showNext('last')">上一个</el-button>
                        <el-button type="primary" @click="showNext('next')">下一个</el-button>
                        <div style="margin-bottom: 10px">
                            <el-descriptions :column="3" size="mini" border>
                                <el-descriptions-item label="系列编码">{{
                                    detail.selRow.styleCode
                                }}</el-descriptions-item>
                                <el-descriptions-item label="主链接">
                                    <div v-html="myformatLinkProCode(detail.selRow.platform, detail.selRow.proCode)"
                                        @click="handleClick($event, detail.selRow.proCode)">
                                    </div>
                                </el-descriptions-item>
                                <el-descriptions-item label="店铺">{{
                                    detail.selRow.shopName
                                }}</el-descriptions-item>
                                <el-descriptions-item label="运营组">{{
                                    detail.selRow.groupName1
                                }}</el-descriptions-item>
                                <el-descriptions-item label="30天销量">{{
                                    detail.selRow.meSalesQty
                                }}</el-descriptions-item>
                                <el-descriptions-item label="耗材">{{
                                    detail.selRow.packageCode
                                }}</el-descriptions-item>
                                <el-descriptions-item label="重量">{{
                                    detail.selRow.calcWeight !== null ? detail.selRow.calcWeight + 'kg' : ''
                                }}</el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>
                    <div style="width: 20%;padding-left: 10px;">
                        <el-image :src="detail.goodsImage" :preview-src-list="detail.srcList" class="imgcss">
                            <template #error>
                                <div class="image-slot">
                                    <el-icon>
                                        <picture />
                                    </el-icon>
                                </div>
                            </template>
                        </el-image>
                        <el-tooltip class="item" effect="dark" content="耗材图片" placement="top-start">
                            <el-image :src="detail.selRow.packageCodeImage"
                                :preview-src-list="[detail.selRow.packageCodeImage]" style="margin-left: 10px;"
                                class="imgcss">
                                <template #error>
                                    <div class="image-slot">
                                        <el-icon>
                                            <picture />
                                        </el-icon>
                                    </div>
                                </template>
                            </el-image>
                        </el-tooltip>
                    </div>
                </div>
                <el-tabs type="card" style="margin-bottom: 0px" v-model="detail.tabName" v-if="detail.visible">
                    <el-tab-pane label="相似产品" name="tabSame">
                        <similarGoods :filter="detail.filter" ref="sameProDetail" style="height: 600px"
                            :platformList="platformlist" :groupList="groupList" @showNext="showNext">
                        </similarGoods>
                    </el-tab-pane>

                    <el-tab-pane label="系列编码" name="tabSeries" v-if="checkPermission('prosameprofit')">
                        <styleCodePage :filter="detail.filter" ref="seriesGoods" style="height: 600px">
                        </styleCodePage>
                    </el-tab-pane>

                    <el-tab-pane label="主链接编码" name="tabMain" v-if="checkPermission('prosameprofit')">
                        <mainLink :filter="detail.filter" :proCode="proCode" ref="mainProGoods" style="height: 600px">
                        </mainLink>
                    </el-tab-pane>

                    <el-tab-pane label="运营申报" name="forth" lazy>
                        <operationalDeclarations :filter="detail.filter" :proCode="proCode"
                            ref="operationalDeclarations" style="height: 600px">
                        </operationalDeclarations>
                    </el-tab-pane>

                    <el-tab-pane label="采购单跟进" name="fifth" lazy>
                        <PurchaseOrderFollowUp :filter="detail.filter" :proCode="proCode" ref="PurchaseOrderFollowUp"
                            style="height: 600px">
                        </PurchaseOrderFollowUp>
                    </el-tab-pane>

                    <!-- <el-tab-pane label="发货地" name="sixth" lazy>
                        <shippingPlace :filter="detail.filter" :proCode="proCode" ref="shippingPlace"
                            style="height: 600px">
                        </shippingPlace>
                    </el-tab-pane> -->
                </el-tabs>
            </template>
        </vxe-modal>

        <el-dialog title="修改类目" :visible.sync="categoryVisible" width="25%" v-dialogDrag>
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px"
                class="demo-ruleForm">
                <el-form-item label="经营大类" prop="bzCategory">
                    <el-select style="width: 250px;" v-model="ruleForm.bzCategory" placeholder="经营大类"
                        :collapse-tags="true" remote :remote-method="remoteMethodBusinessCategory" clearable filterable>
                        <el-option v-for="(item, i) in filterList.bussinessCategoryNames" :key="i" :label="item"
                            :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="一级类目" prop="bzCategory1">
                    <el-select style="width: 250px;" v-model="ruleForm.bzCategory1" placeholder="一级类目"
                        :collapse-tags="true" remote :remote-method="remoteMethodCategoryName1s" clearable filterable>
                        <el-option v-for="(item, i) in filterList.categoryName1s" :key="i" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="二级类目" prop="age">
                    <el-select style="width: 250px;" v-model="ruleForm.bzCategory2" placeholder="二级类目"
                        :collapse-tags="true" remote :remote-method="remoteMethodCategoryName2s" clearable filterable>
                        <el-option v-for="(item, i) in filterList.categoryName2s" :key="i" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <div style="display: flex;justify-content: end;">
                        <el-button @click="categoryVisible = false">关闭</el-button>
                        <el-button type="primary" @click="submitForm('ruleForm')" v-throttle="1000">提交</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog title="备忘录" :visible.sync="memorandumVisible" width="50%" v-dialogDrag destroy-on-close>
            <memorandum style="height: 500px;" @close="close" :styleCode="styleCode" v-if="memorandumVisible" />
        </el-dialog>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="发货地" :visible.sync="shippingPlaceVisible" width="70%" v-dialogDrag>
            <shippingPlace :filter="shipPlaceInfo" ref="shippingPlace" style="height: 600px"
                v-if="shippingPlaceVisible" />
        </el-dialog>
    </MyContainer>
</template>

<script>
import { getList as getshopList } from "@/api/operatemanage/base/shop";
import MyContainer from "@/components/my-container";
import { getGroupKeyValue } from "@/api/operatemanage/base/product";
import vxetablebase from "@/components/VxeTable/yh_vxetableVirtualScroll.vue";
import { pickerOptions, formatPlatform, platformlist, formatLinkProCode } from '@/utils/tools'
import styleCodeReportDetails from './styleCodeReportDetails.vue'
import dayjs from 'dayjs'
import { rulePlatform } from "@/utils/formruletools";
import {
    getProCodeSimilarityStateName,
} from "@/api/order/procodesimilarity"
import decimal from '@/utils/decimal.js'
import {
    pageSimilarityAsync,
    ExportSimilarityAsync,
    SimilarityAnalysis,
    getMainSaleProductInfoByStyleCode,
    saveSimilarityVersion,
    getSeriesMainVersion,
    pageSimilarityByVersionAsync,
    exportSimilarityByVersionAsync,
    setStyleCodeBzCategory,
    similarityZJZYAnalysis,
    importBatchUpdateSeriesMainOwner
} from '@/api/bookkeeper/styleCodeRptData'
import { getAllProBrand } from '@/api/inventory/warehouse'
import SameProDetail from "@/views/order/procodesimilarity/SameProDetail.vue";
import SeriesGoods from "@/views/order/procodesimilarity/SeriesGoods.vue";
import MainProGoods from "@/views/order/procodesimilarity/MainProGoods.vue";
import { getBusinessCategorySelectData } from '@/api/operatemanage/base/category'
import { SaveProductIdViewLog } from '@/api/operatemanage/PddChart'
import buschar from '@/components/Bus/buschar'
import similarGoods from "./similarGoods.vue";
import styleCodePage from "./styleCodePage.vue";
import mainLink from "./mainLink.vue";
import operationalDeclarations from './operationalDeclarations.vue'
import PurchaseOrderFollowUp from './PurchaseOrderFollowUp.vue'
import inputYunhan from '@/components/Comm/inputYunhan.vue'
import YhUserelector from '@/components/YhCom/yh-userselector.vue'
import middlevue from "@/store/middle.js"
import dateRange from "@/components/date-range/index.vue";
import memorandum from './memorandum.vue'
import { seriesTimePickerOptions } from '@/utils/getCols'
import shippingPlace from './shippingPlace.vue'
import { GetProvinceCityDistrict } from '@/api/inventory/purchase'
const echartsList = [
    { title: '订单数', detailsLabel: '数量', isPlatform: true, label: '订单数', value: 'orderCount', prop: 'orderCountPtJson' },
    { title: '主卖销售数量', detailsLabel: '数量', isPlatform: true, label: '主卖销售数量', value: 'meSalesQty', prop: 'meSalesQtyPtJson' },
    { title: '借卖销售数量', detailsLabel: '数量', isPlatform: true, label: '借卖销售数量', value: 'heSalesQty', prop: 'heSalesQtyPtJson' },
    { title: '毛1', detailsLabel: '金额', isPlatform: true, label: '毛1', value: 'profit1', prop: 'profit1PtJson' },
    { title: '毛2', detailsLabel: '金额', isPlatform: true, label: '毛2', value: 'profit2', prop: 'profit2PtJson' },
    { title: '毛3', detailsLabel: '金额', isPlatform: true, label: '毛3', value: 'profit3', prop: 'profit3PtJson' },
    { title: '毛4', detailsLabel: '金额', isPlatform: true, label: '毛4', value: 'profit33', prop: 'profit33PtJson' },
    { title: '毛5', detailsLabel: '金额', isPlatform: true, label: '毛5', value: 'profit33', prop: 'profit5PtJson' },
    { title: '毛6', detailsLabel: '金额', isPlatform: true, label: '毛6', value: 'profit33', prop: 'profit6PtJson' },
    { title: '运营毛三', detailsLabel: '金额', isPlatform: true, label: '运营毛三', value: 'yyProfit3', prop: 'yyProfit3PtJson' },
    { title: '运营毛四', detailsLabel: '金额', isPlatform: true, label: '运营毛四', value: 'yyProfit4', prop: 'yyProfit4PtJson' },
    { title: '运营毛五', detailsLabel: '金额', isPlatform: true, label: '运营毛五', value: 'yyProfit5', prop: 'yyProfit5PtJson' },
    { title: '运营毛六', detailsLabel: '金额', isPlatform: true, label: '运营毛六', value: 'yyProfit6', prop: 'yyProfit6PtJson' },
    { title: '运营毛3(减退款)', detailsLabel: '金额', isPlatform: true, label: '运营毛3(减退款)', value: 'yyProfit3After', prop: 'yyProfit3AfterPtJson' },
    { title: '运营毛4(减退款)', detailsLabel: '金额', isPlatform: true, label: '运营毛4(减退款)', value: 'yyProfit4After', prop: 'yyProfit4AfterPtJson' },
    { title: '运营毛5(减退款)', detailsLabel: '金额', isPlatform: true, label: '运营毛5(减退款)', value: 'yyProfit5After', prop: 'yyProfit5AfterPtJson' },
    { title: '运营毛6(减退款)', detailsLabel: '金额', isPlatform: true, label: '运营毛6(减退款)', value: 'yyProfit6After', prop: 'yyProfit6AfterPtJson' },
    { title: '全系列库存资金(含借出)', detailsLabel: '金额', isPlatform: false, label: '全系列库存资金(全系)', value: 'extKczjQx', prop: 'extKczjQxJson' },
    { title: '系列库存资金(含借入)', detailsLabel: '金额', isPlatform: false, label: '系列库存资金(全系)', value: 'extKczjXl', prop: 'extKczjXlJson' },
    { title: '全系列采购在途资金(含借出)', detailsLabel: '金额', isPlatform: false, label: '全系列采购在途资金(全系)', value: 'extCgztQx', prop: 'extCgztQxJson' },
    { title: '系列采购在途资金(含借入)', detailsLabel: '金额', isPlatform: false, label: '系列采购在途资金(全系)', value: 'extCgztXl', prop: 'extCgztXlJson' },
    { title: '全系列销售在途资金', detailsLabel: '金额', isPlatform: true, label: '全系列销售在途资金', value: 'extXsztQx', prop: 'extXsztQxPtJson' },
    { title: '销售金额', detailsLabel: '金额', isPlatform: true, label: '销售金额', value: 'saleAmt', prop: 'saleAmtPtJson' },
    { title: '销售成本', detailsLabel: '金额', isPlatform: true, label: '销售成本', value: 'saleCost', prop: 'saleCostPtJson' },
    { title: '违规扣款总和', detailsLabel: '金额', isPlatform: true, label: '违规扣款总和', value: 'deductAmt', prop: 'deductAmtPtJson' },
    { title: '仅退款', detailsLabel: '金额', isPlatform: true, label: '仅退款', value: 'onlyRefundAmont', prop: 'onlyRefundAmontPtJson' },
]

const tableCols = [
    { width: '30', align: 'center', type: 'checkbox', label: '', fixed: 'left' },
    { sortable: 'custom', width: '100', align: 'left', prop: 'styleCode', label: '系列编码', fixed: 'left', formatter: (row) => row.styleCode ? row.styleCode : '无', type: 'click', handle: (that, row) => that.onSerialCoding(row, "tabSame") },
    { sortable: 'custom', width: '80', align: 'left', prop: 'styleCreateTime', label: '创建时间', fixed: 'left', formatter: (row) => row.styleCreateTime ? dayjs(row.styleCreateTime).format('YYYY-MM-DD') : '' },
    { width: '70', align: 'center', prop: 'createDays', label: '创建天数' },
    { sortable: 'custom', width: '100', align: 'left', prop: 'bzCategory', label: '经营大类', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'bzCategory1', label: '一级类目', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'bzCategory2', label: '二级类目', },
    { width: '60', align: 'center', prop: 'platform', label: '平台', formatter: (row) => row.platform !== null ? formatPlatform(row.platform) : null },
    { sortable: 'custom', width: '70', align: 'center', prop: 'sendGoodsProvinceRate', label: '发货地', type: 'click', handle: (that, row) => that.openShipPlace(row) },
    // { width: '70', align: 'center', prop: 'owners', label: '负责人' },
    { width: '60', align: 'center', prop: 'goodsImage', label: '商品图片', type: 'images' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'orderCount', label: '订单数', type: 'click', handle: (that, row) => that.showDetail('orderCountPtJson', "订单数", true, '数量', row) },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit3', label: '毛3', type: 'click', handle: (that, row) => that.showDetail('profit3PtJson', "毛3", true, '金额', row), tipmesg: '发生维度毛3' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit33', label: '毛4', type: 'click', handle: (that, row) => that.showDetail('profit33PtJson', "毛4", true, '金额', row), tipmesg: '发生维度毛4' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit5', label: '毛5', type: 'click', handle: (that, row) => that.showDetail('profit5PtJson', "毛5", true, '金额', row), tipmesg: '发生维度毛5' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit6', label: '毛6', type: 'click', handle: (that, row) => that.showDetail('profit6PtJson', "毛6", true, '金额', row), tipmesg: '发生维度毛6' },
    { width: '70', align: 'center', prop: 'profit3Rate', label: '毛3率', formatter: (row) => row.profit3Rate ? row.profit3Rate + '%' : '', tipmesg: '发生维度毛3率' },
    { width: '80', align: 'center', prop: 'profit33Rate', label: '毛4率', formatter: (row) => row.profit33Rate ? row.profit33Rate + '%' : '', tipmesg: '发生维度毛4率' },
    { width: '80', align: 'center', prop: 'profit5Rate', label: '毛5率', formatter: (row) => row.profit5Rate ? row.profit5Rate + '%' : '', tipmesg: '发生维度毛5率' },
    { width: '80', align: 'center', prop: 'profit6Rate', label: '毛6率', formatter: (row) => row.profit6Rate ? row.profit6Rate + '%' : '', tipmesg: '发生维度毛6率' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit3After', label: '运营毛3(减退款)', type: 'click', handle: (that, row) => that.showDetail('yyProfit3AfterPtJson', "运营毛3(减退款)", true, '金额', row), tipmesg: '运营维度毛3(减退款)' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit4After', label: '运营毛4(减退款)', type: 'click', handle: (that, row) => that.showDetail('yyProfit4AfterPtJson', "运营毛4(减退款)", true, '金额', row), tipmesg: '运营维度毛4(减退款)' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit5After', label: '运营毛5(减退款)', type: 'click', handle: (that, row) => that.showDetail('yyProfit5AfterPtJson', "运营毛4(减退款)", true, '金额', row), tipmesg: '运营维度毛5(减退款)' },
    { sortable: 'custom', width: '60', align: 'center', prop: 'yyProfit6After', label: '运营毛6(减退款)', type: 'click', handle: (that, row) => that.showDetail('yyProfit6AfterPtJson', "运营毛4(减退款)", true, '金额', row), tipmesg: '运营维度毛6(减退款)' },
    { width: '80', align: 'center', prop: 'yyProfit3AfterRate', label: '运营毛3(减退款)率', formatter: (row) => row.yyProfit3AfterRate ? row.yyProfit3AfterRate + '%' : '', tipmesg: '运营维度毛3(减退款)率' },
    { width: '80', align: 'center', prop: 'yyProfit4AfterRate', label: '运营毛4(减退款)率', formatter: (row) => row.yyProfit4AfterRate ? row.yyProfit4AfterRate + '%' : '', tipmesg: '运营维度毛4(减退款)率' },
    { width: '80', align: 'center', prop: 'yyProfit5AfterRate', label: '运营毛5(减退款)率', formatter: (row) => row.yyProfit5AfterRate ? row.yyProfit5AfterRate + '%' : '', tipmesg: '运营维度毛5(减退款)率' },
    { width: '80', align: 'center', prop: 'yyProfit6AfterRate', label: '运营毛4(减退款)率', formatter: (row) => row.yyProfit6AfterRate ? row.yyProfit6AfterRate + '%' : '', tipmesg: '运营维度毛4(减退款)率' },
    { sortable: 'custom', width: '110', align: 'center', prop: 'zjHbl', label: '年化回报率', tipmesg: '年化资金回报率=毛六利润/资金占用合计/日报选择天数*360*100%', formatter: (row) => row.zjHbl ? row.zjHbl + '%' : '' },
    { sortable: 'custom', width: '90', align: 'center', prop: 'zjsy', label: '资金收益', tipmesg: '资金收益=资金占用合计*0.05' },
    // { sortable: 'custom', width: '90', align: 'center', prop: 'lcsy', label: '理财收益', tipmesg: '理财收益=资金占用合计*年化回报率' },
    { sortable: 'custom', width: '90', align: 'center', prop: 'jsy', label: '净收益', tipmesg: '净收益=资金收益-理财收益' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'saleAmtCost', label: '售卖成本', tipmesg: '全平台日报中ID系列编码的（销售金额-毛三利润）+分销日报中商品系列编码的（销售金额-毛四', },
    { sortable: false, width: '120', align: 'center', prop: 'zjzySum', label: '资金占用合计', tipmesg: '资金占用合计=系列库存资金（本系、借入）+采购在途（本系、借入）+ 售卖成本', type: 'click', handle: (that, row) => that.showDetailAnalysis(row, 2, true) },
    { sortable: 'custom', width: '160', align: 'center', prop: 'extXsztQx', label: '全系列销售在途资金', tipmesg: '本系列下所有商品编码数据', type: 'click', handle: (that, row) => that.showDetail('extXsztQxPtJson', "全系列销售在途资金", true, '金额', row) },
    { sortable: 'custom', width: '110', align: 'center', prop: 'zjZzl', label: '资金周转率', tipmesg: '销售金额/资金占用合计*100%', formatter: (row) => row.zjZzl ? row.zjZzl + '%' : '' },
    { sortable: 'custom', width: '90', align: 'center', prop: 'saleAmt', label: '销售金额', type: 'click', handle: (that, row) => that.showDetail('saleAmtPtJson', "销售金额", true, '金额', row) },
    { sortable: 'custom', width: '90', align: 'center', prop: 'saleCost', label: '销售成本', type: 'click', handle: (that, row) => that.showDetail('saleCostPtJson', "销售成本", true, '金额', row) },
    { sortable: 'custom', width: '100', align: 'center', prop: 'deductAmt', label: '违规扣款总和', type: 'click', handle: (that, row) => that.showDetail('deductAmtPtJson', "违规扣款总和", true, '金额', row) },
    { sortable: 'custom', width: '100', align: 'left', prop: 'refundAmontRate', label: '总退款率', formatter: (row) => row.refundAmontRate !== null ? row.refundAmontRate + '%' : '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'refundAmontBefore', label: '发货前退款金额', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'refundAmontBeforeRate', label: '发货前退款率', formatter: (row) => row.refundAmontBeforeRate !== null ? row.refundAmontBeforeRate + '%' : '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'refundAmontAfter', label: '发货后退款金额', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'refundAmontAfterRate', label: '发货后退款率', formatter: (row) => row.refundAmontAfterRate !== null ? row.refundAmontAfterRate + '%' : '' },
    { sortable: 'custom', width: '100', align: 'center', prop: 'onlyRefundAmont', label: '仅退款', type: 'click', handle: (that, row) => that.showDetail('onlyRefundAmontPtJson', "仅退款", true, '金额', row) },
    { sortable: 'custom', width: '100', align: 'center', prop: 'onlyRefundAmontRate', label: '仅退款率', formatter: (row) => row.onlyRefundAmontRate !== null ? row.onlyRefundAmontRate + '%' : '' },
    { sortable: 'custom', width: '100', align: 'left', prop: 'alladv', label: '广告费', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'packageFee', label: '包装材料费', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'packageFeeAvg', label: '包装材料费均价', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'freightFeeTotal', label: '快递费', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'freightFeeTotalAvg', label: '快递均价', },
    { sortable: 'custom', width: '100', align: 'left', prop: 'wcOrderRate', label: '外仓率', formatter: (row) => row.wcOrderRate ? row.wcOrderRate + '%' : '' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit1', label: '毛1', type: 'click', handle: (that, row) => that.showDetail('profit1PtJson', "毛1", true, '金额', row), tipmesg: '发生维度毛1' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'profit2', label: '毛2', type: 'click', handle: (that, row) => that.showDetail('profit2PtJson', "毛2", true, '金额', row), tipmesg: '发生维度毛2' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit3', label: '运营毛3', type: 'click', handle: (that, row) => that.showDetail('yyProfit3PtJson', "运营毛3", true, '金额', row), tipmesg: '运营维度毛3' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit4', label: '运营毛4', type: 'click', handle: (that, row) => that.showDetail('yyProfit4PtJson', "运营毛4", true, '金额', row), tipmesg: '运营维度毛4' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit5', label: '运营毛5', type: 'click', handle: (that, row) => that.showDetail('yyProfit5PtJson', "运营毛5", true, '金额', row), tipmesg: '运营维度毛5' },
    { sortable: 'custom', width: '70', align: 'center', prop: 'yyProfit6', label: '运营毛6', type: 'click', handle: (that, row) => that.showDetail('yyProfit6PtJson', "运营毛6", true, '金额', row), tipmesg: '运营维度毛6' },
    { width: '70', align: 'center', prop: 'profit1Rate', label: '毛1率', formatter: (row) => row.profit1Rate ? row.profit1Rate + '%' : '', tipmesg: '发生维度毛1率' },
    { width: '70', align: 'center', prop: 'profit2Rate', label: '毛2率', formatter: (row) => row.profit2Rate ? row.profit2Rate + '%' : '', tipmesg: '发生维度毛2率' },
    { width: '100', align: 'center', prop: 'yyProfit3Rate', label: '运营毛3率', formatter: (row) => row.yyProfit3Rate ? row.yyProfit3Rate + '%' : '', tipmesg: '运营维度毛3率' },
    { width: '100', align: 'center', prop: 'yyProfit4Rate', label: '运营毛4率', formatter: (row) => row.yyProfit4Rate ? row.yyProfit4Rate + '%' : '', tipmesg: '运营维度毛4率' },
    { width: '100', align: 'center', prop: 'yyProfit5Rate', label: '运营毛5率', formatter: (row) => row.yyProfit5Rate ? row.yyProfit5Rate + '%' : '', tipmesg: '运营维度毛5率' },
    { width: '100', align: 'center', prop: 'yyProfit6Rate', label: '运营毛6率', formatter: (row) => row.yyProfit6Rate ? row.yyProfit6Rate + '%' : '', tipmesg: '运营维度毛6率' },
    { width: '130', align: 'left', prop: 'shopName', label: '店铺名称', },
    { sortable: 'custom', width: '100', align: 'center', prop: 'meSalesQty', label: '主卖销售数量', type: 'click', handle: (that, row) => that.showDetail('meSalesQtyPtJson', "主卖销售数量", true, '数量', row), formatter: (row) => row.meSalesQty ? (row.meSalesQty) + '  |  ' + (decimal(decimal(row.meSalesQty, decimal(row.meSalesQty, row.heSalesQty, 4), 4, '/'), 100, 2, '*') + '%') : 0 },
    { sortable: 'custom', width: '100', align: 'center', prop: 'heSalesQty', label: '借出销售数量', type: 'click', handle: (that, row) => that.showDetail('heSalesQtyPtJson', "借卖销售数量", true, '数量', row), formatter: (row) => row.heSalesQty ? (row.heSalesQty) + '  |  ' + (decimal(decimal(row.heSalesQty, decimal(row.meSalesQty, row.heSalesQty, 4), 4, '/'), 100, 2, '*') + '%') : 0 },
    {
        label: `全系列库存资金`, merge: true, prop: 'mergeField', tipmesg: '本系列下所有商品编码数据，含借给其他系的数据', width: '70',
        cols: [
            { sortable: 'custom', align: 'center', prop: 'extKczjQx', label: '全系', type: 'click', handle: (that, row) => that.showDetail('extKczjQxJson', "全系列库存资金（含借出）", false, '金额', row) },
            { sortable: 'custom', align: 'center', prop: 'extKczjQx_Me', label: '本系', },
            { sortable: 'custom', align: 'center', prop: 'extKczjQx_Out', label: '借出', },
        ]
    },
    {
        label: `系列库存资金`, merge: true, prop: 'mergeField1', tipmesg: '当前系列本系资金+借来他系的', width: '70',
        cols: [
            { sortable: 'custom', align: 'center', prop: 'extKczjXl', label: '全系', type: 'click', handle: (that, row) => that.showDetail('extKczjXlJson', "系列库存资金（含借入）", false, '金额', row) },
            { sortable: 'custom', align: 'center', prop: 'extKczjXl_Me', label: '本系', },
            { sortable: 'custom', align: 'center', prop: 'extKczjXl_In', label: '借入', },
        ]
    },
    {
        label: `全系列采购在途资金`, merge: true, prop: 'mergeField2', tipmesg: '本系列下所有商品编码数据，含借给其他系的数据', width: '70',
        cols: [
            { sortable: 'custom', align: 'center', prop: 'extCgztQx', label: '全系', type: 'click', handle: (that, row) => that.showDetail('extCgztQxJson', "全系列采购在途资金（含借出）", false, '金额', row) },
            { sortable: 'custom', align: 'center', prop: 'extCgztQx_Me', label: '本系', },
            { sortable: 'custom', align: 'center', prop: 'extCgztQx_Out', label: '借出', },
        ]
    },
    {
        label: `系列采购在途资金`, merge: true, prop: 'mergeField3', tipmesg: '当前系列本系资金+借来他系的', width: '70',
        cols: [
            { sortable: 'custom', align: 'center', prop: 'extCgztXl', label: '全系', type: 'click', handle: (that, row) => that.showDetail('extCgztXlJson', "系列采购在途资金（含借入）", false, '金额', row) },
            { sortable: 'custom', align: 'center', prop: 'extCgztXl_Me', label: '本系', },
            { sortable: 'custom', align: 'center', prop: 'extCgztXl_In', label: '借入', },
        ]
    },
    { istrue: true, prop: 'sendOrderCount', label: '发货订单数', type: 'custom', width: '80', },
    { istrue: true, prop: 'damagedOrderCount', label: '破损订单数', type: 'custom', width: '80', },
    { istrue: true, prop: 'damagedOrderRatio', label: '损耗订单比', type: 'custom', width: '80' },
    { istrue: true, prop: 'goodsCostPrice', label: '商品单价', type: 'custom', width: '80', },
    { istrue: true, prop: 'damagedAmount', label: '损耗总费用', type: 'custom', width: '80', },
    {
        width: '120', align: 'center', fixed: 'right', label: '操作', type: 'button', btnList: [
            { label: "趋势图", handle: (that, row) => that.showDetailAnalysis(row, 1, true) },
            { label: "备忘录", handle: (that, row) => that.memorandumDialog(row) }
        ]
    },
    // { sortable: 'custom', width: '150', align: 'center', prop: 'extKczjXl', label: '系列库存资金', tipmesg: '当前系列本系资金+借来他系的', type: 'click', handle: (that, row) => that.showDetail(row.extKczjXlJson, "系列库存资金", false, '金额', row.styleCode) },
    // { sortable: 'custom', width: '150', align: 'center', prop: 'extKczjXlJson', label: 'Xl', },
    // { sortable: 'custom', width: '130', align: 'center', prop: 'extKczjQx', label: '全系列库存资金', tipmesg: '本系列下所有商品编码数据，含借给其他系的数据', type: 'click', handle: (that, row) => that.showDetail(row.extKczjQxJson, "全系列库存资金", false, '金额', row.styleCode) },
    // { sortable: 'custom',  align: 'center', prop: 'extKczjQxJson', label: '库存资金全系列平台占比存入', },
    // { sortable: 'custom', width: '170', align: 'center', prop: 'extCgztQx', label: '全系列采购在途资金', tipmesg: '本系列下所有商品编码数据，含借给其他系的数据', type: 'click', handle: (that, row) => that.showDetail(row.extCgztQxJson, "全系列采购在途资金", false, '金额', row.styleCode) },
    // { sortable: 'custom', width: '150', align: 'center', prop: 'extCgztQxJson', label: 'QxJson', },
    // { sortable: 'custom', width: '120', align: 'center', prop: 'verDate', label: '版本日期', formatter: (row) => row.verDate ? dayjs(row.verDate).format('YYYY-MM-DD') : '' },
    // { width: '100', align: 'center', prop: 'proCode', label: '宝贝ID', },
    // { width: '100', align: 'center', prop: 'goodsName', label: '商品名称', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'orderCountPtJson', label: '订单数：平台占比存入', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'mainStyleCodeCostRate', label: '主卖销售数量', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'meSalesQtyPtJson', label: '主卖销售数量：平台占比存入', },
    // { sortable: 'custom', width: '170', align: 'center', prop: 'extCgztXl', label: '系列采购在途资金', tipmesg: '当前系列本系资金+借来他系的', type: 'click', handle: (that, row) => that.showDetail(row.extCgztXlJson, "系列采购在途资金", false, '金额', row.styleCode) },
    // { sortable: 'custom', width: '150', align: 'center', prop: 'extCgztXlJson', label: 'ExtXsztQxPtJson', },
    // { sortable: 'custom', width: '120', align: 'center', prop: 'calcDataTag', label: '已计算数据标签', },
    // { sortable: 'custom', width: '150', align: 'center', prop: 'extXsztQxPtJson', label: '销售在途，平台占比存入', },
    //{ sortable: 'custom', width: '90', align: 'center', prop: 'hkDays', label: '回款天数', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'heSalesQtyPtJson', label: '借卖销售数量：平台占比存入', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'saleAmtPtJson', label: '销售金额：平台占比存入', },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'mainStyleCodeCostRate', label: '利润1', },
    // { sortable: 'custom', width: '100', align: 'center', prop: 'saleCostPtJson', label: '销售成本：平台占比存入', },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'profit3PtJson', label: '利润3：平台占比存入', },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'profit1PtJson', label: '利润1：平台占比存入', },
    // { sortable: 'custom', width: '70', align: 'center', prop: 'profit2PtJson', label: '利润2：平台占比存入', },
    // { width: '200', align: 'center', prop: 'mainStyleCodeCostRate', label: '主卖系列编码成本占比', formatter: (row) => row.mainStyleCodeCostRate ? row.mainStyleCodeCostRate + '%' : '' },
    // { width: '200', align: 'center', prop: 'notMainStyleCodeCostRate', label: '主卖系列编码成本占比', formatter: (row) => row.notMainStyleCodeCostRate ? row.notMainStyleCodeCostRate + '%' : '' },
    // { width: '200', align: 'center', prop: 'leaseSaleStyleCodeCostRate', label: '借卖系列编码成本占比', formatter: (row) => row.leaseSaleStyleCodeCostRate ? row.leaseSaleStyleCodeCostRate + '%' : '' },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer,
        vxetablebase,
        styleCodeReportDetails,
        MainProGoods,
        SeriesGoods,
        SameProDetail,
        buschar,
        similarGoods,
        styleCodePage,
        mainLink,
        inputYunhan,
        YhUserelector,
        dateRange,
        memorandum,
        operationalDeclarations,
        PurchaseOrderFollowUp,
        shippingPlace
    },
    data() {
        return {
            seriesTimePickerOptions,
            rules: {
                bzCategory: [{ required: true, message: '请选择经营大类', trigger: 'change' }],
                bzCategory1: [{ required: true, message: '请选择一级类目', trigger: 'change' }],
                bzCategory2: [{ required: true, message: '请选择二级类目', trigger: 'change' }],
            },
            ruleForm: {
                bzCategory: '',
                bzCategory1: '',
                bzCategory2: '',
            },
            categoryVisible: false,
            echartsList,
            platformlist,
            that: this,
            showRptFilter: false,
            tabLoading: false,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                startDate: null,
                endDate: null,
                platform: null,
                shopCode: "",
                proCode: null,
                goodsCode: null,
                groupId: null,
                brandId: null,
                profit2UnZero: null,
                profit3UnZero: null,
                profit33UnZero: null,
                state: null,
                bzCategory: null,
                bzCategory1: null,
                bzCategory2: null,
                showDamaged: false,
                ownerId: null,
                owners: null,
                provinceCodes1: [],
                provinceCodes: [],
            },
            styleCode: '',
            timeRanges: [],
            timeRangesStyleCreate: [],
            tableCols,
            tableData: [],
            summaryarry: {},
            rltExtData: {},
            total: 0,
            loading: false,
            pickerOptions,
            detailsData: [],
            title: '',
            detailsVisable: false,
            filterList: {
                bussinessCategoryNames: [],
                categoryName1s: [],
                categoryName2s: []
            },
            dialogMapVisible: {
                type: 1,
                visible: false,
                loading: false,
                chartvisible: false,
                platform: null,
                data: null,
                title: "",
                startDate: dayjs().subtract(30, "day").format("YYYY-MM-DD"),
                endDate: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
                styleCode: "",
            },
            brandlist: [],//品牌列表
            groupList: [],//运营组列表
            shopList: [],//店铺列表
            prosimstatelist: [],//状态列表
            isPlatform: true,
            detailsLabel: '',
            proCode: '',
            styleCodes: null,
            chartsData: {},
            propStr: '',
            chartLoading: false,
            detailsVisable1: false,
            versionList: [],
            selectList: [],//选中的数据
            memorandumVisible: false,
            zjzyChartProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: {}, // 趋势图数据
            },
            fileList: [],
            importLoading: false,
            importVisible: false,
            file: null,
            detail: {
                visible: false,
                filter: {
                    parentId: null,
                    proCode: null,
                    status: null,
                    platform: null,
                    shopId: "",
                    similarity: null,
                    styleCode: null,
                    goodsCode: null,
                    startDate: null,
                    endDate: null,
                    timerange: [],
                    styleCodeStartDate: null,
                    styleCodeEndDate: null,
                    styleCodeTimerange: [],
                    styleCodeId: null,
                    styleCodes: null,
                },
                selRow: {},
                srcList: [],
                tabName: "tabSame",
                customRowStyle: function (data) {
                    if (data.row.isMain) {
                        return { color: 'red' };
                    }
                },
            },
            shipPlaceInfo: {
                styleCode: '',
                startDate: '',
                endDate: '',
            },
            shippingPlaceVisible: false,
        }
    },
    async mounted() {
        this.getVersionList()
        this.getGetProvinceCityDistrict()
        this.init()
        // await this.getList()
        middlevue.$on('BookKeeper_StyleCodeRptData_SimilarityVersionSuccessMsg', (data) => {
            if (!this.versionList.some(item => item.name == data.Name)) {
                this.$message.success('添加版本成功,请在下拉选择中查看')
                this.versionList.unshift({ id: data.Id, name: data.Name })
            } else {
                this.$message.warning('版本已存在')
            }
        })
    },
    beforeDestroy() {
        middlevue.$off('BookKeeper_StyleCodeRptData_SimilarityVersionSuccessMsg')
    },
    methods: {
        async getGetProvinceCityDistrict() {
            const { data, success } = await GetProvinceCityDistrict();
            if (!success) {
                return;
            }
            data.forEach(item => {
                delete item.children
            })
            this.options1 = data ? data : [];
        },
        openShipPlace(row) {
            this.shipPlaceInfo = {
                styleCode: row.styleCode,
                startDate: this.ListInfo.startDate,
                endDate: this.ListInfo.endDate,
            }
            this.shippingPlaceVisible = true
        },
        myformatLinkProCode(platform, proCode) {
            return formatLinkProCode(platform, proCode);
        },
        handleClick(e, prop) {
            if (!prop) return
            let res = JSON.parse(JSON.stringify(prop));
            if (res.length > 6) {
                res = res.substring(0, 2) + '**' + res.substring(res.length - 2, res.length);
            }
            if (e.target.innerHTML == '复') {
                var _this = this;
                this.$copyText(prop).then(function (e) {
                    _this.$message({ message: "内容已复制到剪切板！", type: "success" });
                }, function (e) {
                    _this.$message({ message: "抱歉，复制失败！", type: "warning" });
                })
                this.sendLog(prop, '复制宝贝ID', 'ERP')
            } else if (e.target.innerHTML == '查 ') {
                if (e.target.parentNode.innerHTML.includes(res)) {
                    e.target.parentNode.innerHTML = e.target.parentNode.innerHTML.replace(res, prop)
                }
                this.sendLog(prop, '查看宝贝ID', 'ERP')
            } else {
                if (res == e.target.innerHTML || prop == e.target.innerHTML) {
                    this.sendLog(prop, '打开链接', 'ERP')
                }
            }
        },
        async sendLog(proCode, action, source) {
            await SaveProductIdViewLog({ proCode, action, source })
        },
        // dowmLoadStyleCodeFile() {
        //     window.open("../../static/excel/系列编码负责人导入模版.xlsx", "_self");
        // },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            //没有时间就提示
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            this.importLoading = true
            await importBatchUpdateSeriesMainOwner(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        close() {
            this.memorandumVisible = false
        },
        memorandumDialog(row) {
            this.styleCode = row.styleCode
            this.memorandumVisible = true
        },
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    const { success } = await setStyleCodeBzCategory(this.ruleForm)
                    if (success) {
                        this.categoryVisible = false
                        this.$message.success('操作成功')
                        this.getList()
                        this.selectList = []
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        editCategory() {
            if (this.selectList.length == 0) return this.$message.warning('请选择数据')
            if (this.selectList.length > 1) return this.$message.warning('只能选择一条数据')
            this.ruleForm = {
                bzCategory: '',
                bzCategory1: '',
                bzCategory2: '',
                styleCode: this.selectList[0].styleCode
            }
            this.categoryVisible = true
            this.$nextTick(() => {
                this.$refs.ruleForm.resetFields()
            })
        },
        selectCheckBox(val) {
            this.selectList = val
        },
        async getVersionList() {
            const { data, success } = await getSeriesMainVersion()
            if (success) {
                this.versionList = data
            }
        },
        async photograph() {
            await saveSimilarityVersion({ startDate: this.ListInfo.startDate, endDate: this.ListInfo.endDate })
            this.$message.success('正在拍照中,预计3-5分钟后可查看')
        },
        nextCharts(type) {
            let index = this.tableData.findIndex(item => this.chartsData.styleCode == item.styleCode)
            if (type == 'last') {
                index--
                if (index < 0) {
                    index = this.tableData.length - 1
                    this.$message({ type: 'warning', message: '当前已经是第一条数据,已跳转到最后一条' })
                }
            } else {
                index++
                if (index >= this.tableData.length) {
                    index = 0
                    this.$message({ type: 'warning', message: '当前已经是最后一条数据,已跳转到第一条' })
                }
            }
            this.$set(this, 'chartsData', this.tableData[index])
            this.command(this.echartsList.find(item => item.prop == this.propStr))
        },
        command(item) {
            this.chartLoading = true
            this.detailsVisable1 = false
            this.$set(this, 'isPlatform', item.isPlatform)
            this.$set(this, 'detailsLabel', item.detailsLabel)
            this.$set(this, 'detailsData', this.chartsData[item.prop] ? JSON.parse(this.chartsData[item.prop]) : [])
            this.$set(this, 'title', item.title)
            this.$set(this, 'propStr', item.prop)
            if (this.detailsData.length > 0) {
                this.detailsData.forEach(item => {
                    item.diffStyleCode = this.chartsData.styleCode
                })
            }
            this.$nextTick(() => {
                this.detailsVisable1 = true
                this.chartLoading = false
            })
        },
        async showDetailAnalysis(row, type, isFirst) {
            // if (isFirst) {
            //     this.dialogMapVisible.platform = this.ListInfo.platform
            // }
            this.dialogMapVisible.type = type
            this.dialogMapVisible.title = row.styleCode;
            this.dialogMapVisible.chartvisible = false
            this.dialogMapVisible.loading = true
            this.dialogMapVisible.styleCode = row.styleCode;
            let res = {};
            if (type == 1) {
                res = await SimilarityAnalysis({
                    styleCode: row.styleCode,
                    StartDate: this.dialogMapVisible.startDate,
                    EndDate: this.dialogMapVisible.endDate,
                    // platform: this.ListInfo.platform
                });
            } else if (type == 2) {
                res = await similarityZJZYAnalysis({
                    styleCode: row.styleCode,
                    StartDate: this.dialogMapVisible.startDate,
                    EndDate: this.dialogMapVisible.endDate,
                    // platform: this.ListInfo.platform
                });
            }
            if (res.success) {
                this.dialogMapVisible.data = res.data;
                this.dialogMapVisible.visible = true;
                this.$nextTick(() => {
                    this.dialogMapVisible.chartvisible = true;
                    this.dialogMapVisible.loading = false
                });
            } else {
                this.dialogMapVisible.loading = false
            }
        },
        showNext(type) {
            console.log('父组件');

            console.log(this.detail.filter.styleCode, 'this.detail.filter.styleCode');
            let index = this.tableData.findIndex(item => item.styleCode == this.detail.filter.styleCode)
            if (type == 'last') {
                index--
                if (index < 0) {
                    index = this.tableData.length - 1
                    this.$message({ type: 'warning', message: '当前已经是第一条数据,已跳转到最后一条' })
                }
            } else {
                index++
                if (index >= this.tableData.length) {
                    index = 0
                    this.$message({ type: 'warning', message: '当前已经是最后一条数据,已跳转到第一条' })
                }
            }
            this.onSerialCoding(this.tableData[index], this.detail.tabName, true);
        },
        //分页查询
        async onSearchDetail() {
            this.$refs.sameProDetail.getList('search');
            this.$refs.seriesGoods.getList('search');
            this.$refs.mainProGoods.getList('search');
            this.$refs.operationalDeclarations.getList('search');
            this.$refs.PurchaseOrderFollowUp.getList('search');
            // this.$refs.shippingPlace.getList('search');
        },
        clearDetailFilter() {
            this.detail.filter = {
                parentId: null,
                proCode: null,
                status: null,
                platform: null,
                shopId: "",
                similarity: null,
                styleCode: null,
                goodsCode: null,
                timerange: [],
                startDate: null,
                endDate: null,
                styleCodeTimerange: [],
                styleCodeStartDate: null,
                styleCodeEndDate: null
            };
        },
        async onSerialCoding(row, tabName, next) {
            // this.detail.selRow = row;
            this.detail.srcList = [row.goodsImage];
            this.detail.goodsImage = row.goodsImage;
            this.detail.tabName = tabName;
            this.clearDetailFilter();
            if (this.detail.filter.timerange.length == 0) {
                this.detail.filter.timerange = [this.ListInfo.startDate, this.ListInfo.endDate,];
                this.detail.filter.startDate = this.detail.filter.timerange[0];
                this.detail.filter.endDate = this.detail.filter.timerange[1];
            }
            if (this.detail.filter.styleCodeTimerange.length == 0) {
                this.detail.filter.styleCodeTimerange = [this.ListInfo.startDate, this.ListInfo.endDate,];
                this.detail.filter.styleCodeStartDate = this.detail.filter.styleCodeTimerange[0];
                this.detail.filter.styleCodeEndDate = this.detail.filter.styleCodeTimerange[1];
            }
            this.detail.filter.parentId = row.styleCodeId;
            // this.detail.filter.seriesCode = row.styleCode;
            this.detail.filter.styleCode = row.styleCode;
            this.detail.filter.similarity = this.ListInfo.similarity;
            this.detail.filter.shopCode = this.ListInfo.shopCode;
            this.detail.filter.groupId = this.ListInfo.groupId;
            // this.detail.filter.proCode = this.ListInfo.proCode;
            this.detail.filter.platform = this.ListInfo.platform;
            this.detail.pager = { OrderBy: "id", IsAsc: false };
            await this.getMainProps(row.styleCode)
            this.detail.visible = true;
            if (next) {
                this.onSearchDetail();
            }
            // this.onTabClick({ label: "相似产品" });
            // this.onSearchDetail()
            console.log(this.detail.filter, 'this.detail.filter');
        },
        async getMainProps(styleCode) {
            const { data, success } = await getMainSaleProductInfoByStyleCode({ styleCode })
            if (success) {
                console.log(data, 'data');
                this.detail.selRow = data
                this.detail.selRow.groupName1 = data.groupName
                this.proCode = data.proCode
            }
        },
        // onTabClick(tab, event) {
        //     setTimeout(async () => {
        //         await this.onSearchDetail();
        //     }, 500);
        // },
        //导出
        async onExport() {
            //默认一个月
            if (!this.ListInfo.startDate && !this.ListInfo.endDate) {
                this.ListInfo.startDate = dayjs().subtract(8, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            if (!this.ListInfo.versionId) {
                var res = await ExportSimilarityAsync(this.ListInfo);
            } else {
                var res = await exportSimilarityByVersionAsync(this.ListInfo);
            }
            return;
        },
        async init() {
            if (!this.ListInfo.startDate && !this.ListInfo.endDate) {
                this.ListInfo.startDate = dayjs().subtract(8, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            var { data } = await getAllProBrand();
            this.brandlist = data.map(item => {
                return { value: item.key, label: item.value };
            });
            const { data: data3 } = await getGroupKeyValue({});
            this.groupList = data3;
            var { data: data4, success } = await getProCodeSimilarityStateName();
            if (success) {
                this.prosimstatelist = data4.map(function (item) {
                    var ob = new Object();
                    ob.state = item;
                    return ob;
                })
            }
        },
        //设置店铺下拉
        async onchangeplatform(val) {
            const res = await getshopList({
                platform: val,
                CurrentPage: 1,
                PageSize: 1000,
            });
            this.shopList = res.data.list || [];
            this.ListInfo.shopCode = "";
        },
        remoteMethodBusinessCategory(query) {
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            this.options = [];
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 1, categoryName: query })
                this.searchloading = false
                this.filterList.bussinessCategoryNames = res.data
            }, 200)
        },
        remoteMethodCategoryName1s(query) {
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            this.options = [];
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 2, categoryName: query })
                this.searchloading = false
                this.filterList.categoryName1s = res.data
            }, 200)
        },
        remoteMethodCategoryName2s(query) {
            query = query.replace(/(^\s*)|(\s*$)/g, "");
            this.searchloading == true;
            this.options = [];
            setTimeout(async () => {
                const res = await getBusinessCategorySelectData({ currentPage: 1, pageSize: 500, categoryType: 3, categoryName: query })
                this.searchloading = false
                this.filterList.categoryName2s = res.data
            }, 200)
        },
        showDetail(data, title, isPlatform, detailsLabel, row) {
            this.detailsVisable1 = false
            this.$set(this, 'detailsLabel', detailsLabel)
            this.$set(this, 'isPlatform', isPlatform)
            this.$set(this, 'detailsData', row[data] ? JSON.parse(row[data]) : [])
            this.$set(this, 'title', title)
            this.$set(this, 'propStr', data)
            this.$set(this, 'chartsData', JSON.parse(JSON.stringify(row)))
            if (this.detailsData.length > 0) {
                this.detailsData.forEach(item => {
                    item.diffStyleCode = row.styleCode
                })
            }
            this.$nextTick(() => {
                this.detailsVisable = true
                this.detailsVisable1 = true
                this.chartLoading = false
            })
        },
        async changeTime(e) {
            this.ListInfo.startDate = e ? e[0] : null
            this.ListInfo.endDate = e ? e[1] : null
            // await this.getList()
        },
        async changeTimeStyleCreate(e) {
            this.ListInfo.styleCreateTimeStart = e ? e[0] : null
            this.ListInfo.styleCreateTimeEnd = e ? e[1] : null
            // await this.getList()
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.ListInfo.styleCodes = null;
            if (this.styleCodes) {
                this.ListInfo.styleCodes = this.styleCodes.split(',');
            }
            //默认一个月
            if (!this.ListInfo.startDate && !this.ListInfo.endDate) {
                this.ListInfo.startDate = dayjs().subtract(8, 'day').format('YYYY-MM-DD')
                this.ListInfo.endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
                this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
            }
            this.ListInfo.provinceCodes = this.ListInfo.provinceCodes1.flat()
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                let res
                if (this.ListInfo.versionId) {
                    res = await pageSimilarityByVersionAsync(this.ListInfo)
                } else {
                    res = await pageSimilarityAsync(this.ListInfo)
                }
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                    this.loading = false;
                    this.summaryarry = res.data.summary;
                    this.rltExtData = res.data.extData;

                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
        styleCallback(val) {
            this.styleCodes = val;

            // 按逗号分割输入值
            let segments_styleCodes = this.styleCodes.split(/[,，]/);
            // 更新输入框的值，只保留有效的数字和逗号部分
            this.styleCodes = segments_styleCodes.join(',');
        }
    },
    // computed: {
    //     selGroupName() {
    //         var name = this.groupList?.find(
    //             (a) => a.key == this.detail.selRow.groupId
    //         )?.value;
    //         return name || "未知";
    //     },
    // },
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.condition {
    z-index: 9999;

    .top {
        flex-wrap: wrap;
        background: #fff;
        display: flex;

        .tag {
            margin: 0 5px 5px 0;
            cursor: pointer;
        }
    }
}

.imgcss ::v-deep img {
    min-width: 100px !important;
    min-height: 100px !important;
    width: 100px !important;
    height: 100px !important;
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.cascaderCss ::v-deep .el-input__inner {
    height: 28px !important;
}

::v-deep .el-cascader__search-input {
    margin: 0 0 0 2px;
}
</style>
