<template>
    <container v-loading="pageLoading">
        <template #header>
        </template>
        <!--列表-->
        <vxetablebase :id="'jstshopgoods20230701'" :tablekey="'jstshopgoods20230701'" :tableData='list' :tableCols='tableCols'
            @cellClick='cellclick' @select='selectchange' :tableHandles='tableHandles' :loading='listLoading' :border='true'
            :that="that" ref="vxetable" @sortchange='sortchange' style="height: 93%;">
        </vxetablebase>
        <span style="text-align: right;">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :page-size="50" layout="total, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </span>

        <!-- 设置开单信息 -->
        <el-dialog title="设置开单信息" :visible.sync="dialogAddVisible" v-dialogDrag width='30%' :close-on-click-modal="false"
            height='700px'>

        </el-dialog>
    </container>
</template>

<script>
import { Loading } from 'element-ui';
import dayjs from "dayjs";
import { formatTime, formatNoLink } from "@/utils/tools";
import inputYunhan from "@/components/Comm/inputYunhan";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getJstShopGoodsListAsync } from "@/api/inventory/goodsrapidretire"

const tableCols = [
    { istrue: true, prop: 'platformName', align: 'center', label: '平台', width: '120', },
    { istrue: true, prop: 'shopName', label: '店铺', width: '260', },
    { istrue: true, prop: 'proCode', label: '宝贝ID', width: '170', tipmesg: '如存在多个相同id，代表此id有多个规格'},
    { istrue: true, prop: 'groupId', label: '运营组', width: '150', formatter: (row) => row.groupName },
    { istrue: true, prop: 'operationId', label: '运营专员', width: 'auto', formatter: (row) => row.operationName },

];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];



export default {
    name: 'YunHanAdminJstshopgoods',
    components: { MyConfirmButton, container, vxetablebase, inputYunhan },
    props: {
        filter:{}
    },

    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "createdTime", IsAsc: false, currentPage: 1, pageSize: 50, },
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
            dialogAddVisible: false,
            onFinishLoading: false,
            dialogLoading: false
        };
    },

    async mounted() {
        await this.onSearch();
    },

    methods: {
        async onSearch() {
            this.pager.currentPage = 1;
            this.getlist();
        },
        //分页查询
        async getlist() {
            var page = this.pager;
            const params = { ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getJstShopGoodsListAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
        },
        async onSetInfo(row, val) {

        },
        async handleSizeChange(val) {
            this.pager.pageSize = val;
            await this.getlist();
            console.log(`每页 ${val} 条`);
        },
        async handleCurrentChange(val) {
            this.pager.currentPage = val;
            await this.getlist();
            console.log(`当前页: ${val}`);
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            // if (column.property == 'goodsCode') {
            // }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
