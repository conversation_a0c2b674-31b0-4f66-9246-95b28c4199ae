<template>
    <my-container>
        <template #header>
            
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
            :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="true" :loading="listLoading" @cellclick="cellclick">
            <template slot='extentbtn'>
                <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 200px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        range-separator="至" start-placeholder="开始" end-placeholder="结束" clearable :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-button> 
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.goodsCode" placeholder="请输入商品编号" clearable style="width: 110px"/>
                </el-button>
                <!--
                    不支持的搜索条件，没有goodsName列 
                    <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.goodsName" placeholder="请输入商品名称" clearable style="width: 110px"/>
                </el-button> -->
                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="filter.brandId" clearable placeholder="请选择采购员" style="width: 140px">
                        <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.reason" placeholder="请输入原因" clearable style="width: 110px"/>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"  @get-page="getlist"/>
        </template>

        <el-drawer title="处理" :modal="false" :wrapper-closable="true" :modal-append-to-body="false" :visible.sync="editVisible" 
                direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
            <form-create :rule="autoform.rule" v-model="autoform.fApi" :option="autoform.options"/>
            <div class="drawer-footer">
                <el-button @click.native="editVisible = false">取消</el-button>
                <my-confirm-button type="submit" :loading="editLoading" @click="onEditSubmit" />
            </div>
        </el-drawer>

        <el-dialog title="订单详情" :visible.sync="isshowOrderCount" width="80%" v-dialogDrag>
        <div>
            <orderwithholdcount ref="orderwithholdcount" style="height: 480px" ></orderwithholdcount>
        </div>
        </el-dialog>

        <el-dialog :visible.sync="visiblepopover" v-dialogDrag width="80%">
            <withholdgoodscoderecord ref="withholdgoodscoderecord"  style="height: 400px"></withholdgoodscoderecord> 
        </el-dialog>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { formatTime, } from "@/utils";
import { formatPlatform, formatNoLink} from "@/utils/tools";
import FcEditor from "@form-create/component-wangeditor";
import {upLoadImage} from '@/api/upload/file'
import { getOrderWithhold, editOrderWithhold } from "@/api/order/orderdeductmoney"
import {getAllProBrand} from '@/api/inventory/warehouse'
import orderwithholdcount from "./orderwithholdcount.vue"
import withholdgoodscoderecord from "./withholdgoodscoderecord.vue"


const tableCols =[
        {istrue:true,prop:'occurrenceTime',label:'罚款日期', width:'120',sortable:'custom',formatter:(row) => formatTime(row.occurrenceTime, "YYYY-MM-DD")},
        {istrue:true,prop:'platform',label:'平台', width:'60',sortable:'custom',formatter:(row)=>formatPlatform(row.platform)},
        {istrue:true,prop:'image',label:'图片', width:'60', type:'image'}, 
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'150',sortable:'custom',formatter:(row)=> !row.goodsCode? "" : row.goodsCode},
        {istrue:true,prop:'goodsName',label:'商品名称', width:'200',formatter:(row)=> !row.goodsName? "" : row.goodsName},       
        {istrue:true,prop:'orderCount',label:'罚款订单数', width:'100',sortable:'custom',type:'html',formatter:(row)=> formatNoLink(row.orderCount)},  
        {istrue:true,prop:'amountPaid',label:'罚款总金额', width:'100',sortable:'custom',formatter:(row)=> !row.amountPaid?" " : row.amountPaid },     
        {istrue:true,prop:'avgAmountPaid',label:'平均每单罚金', width:'125',sortable:'custom',formatter:(row)=> !row.avgAmountPaid?" " : row.avgAmountPaid.toFixed(2) },
        {istrue:true,prop:'brandId',label:'采购员',sortable:'custom', width:'80',formatter:(row)=> !row.brandName?" " : row.brandName}, 
        {istrue:true,type:'button', width:'55',btnList:[{label:"编辑",display:(row)=>{return row.isHandle==true;},handle:(that,row)=>that.onHand(row)}]},      
        {istrue:true,prop:'reason',label:'原因部门', width:'100',sortable:'custom'},
        {istrue:true,prop:'cgReMark',label:'采购备注', width:'auto',sortable:'custom',type:'editor'},
        {istrue:true,prop:'ckReMark',label:'仓库备注', width:'auto',sortable:'custom',type:'editor'},
        {istrue:true,prop:'yyReMark',label:'运营备注', width:'auto',sortable:'custom',type:'editor'},
        {istrue:true,prop:'kfReMark',label:'客服备注', width:'auto',sortable:'custom',type:'editor'},
        {istrue:true,prop:'cwReMark',label:'财务备注', width:'auto',type:'editor'}
]

const tableHandles=[];


export default {
    name: 'YunhanAdminOrdergoodscodewithhold',
    components: {MyContainer, MySearch, cesTable, MySearchWindow, MyConfirmButton, FcEditor, orderwithholdcount,withholdgoodscoderecord},

    data() {
        return {
            that:this,
            filter: {
                startDate:null,
                endDate:null,
                platform:null,
                goodsCode:null,
                goodsName:null,
                reason:null,
                brandId:null,
                occurrenceTime:null,
                timerange:[formatTime(dayjs().subtract(7,"day"), "YYYY-MM-DD"),formatTime(new Date(), "YYYY-MM-DD")]
            },
            list: [],
            summaryarry:{},
            pager:{OrderBy:"OccurrenceTime",IsAsc:false},
            tableCols:tableCols,
            tableHandles:tableHandles,
            formtitle:null,
            total: 0,
            sels: [], 
            brandlist:[],
            listLoading: false,
            pageLoading: false,
            isHandle:false,
            editVisible:false,
            editLoading:false,
            isshowOrderCount:false,
            visiblepopover:false,
            pickerOptions: {
                disabledDate(time) {
                return time.getTime() > Date.now();
                },
            },
            autoform:{
               fApi:{},
               options:{submitBtn:false,global: {'*': {props: {  disabled: false },col: { span: 8 }}}},
               rule:[]
            },
        };
    },

    async mounted() {
        formCreate.component('editor', FcEditor);
        await this.initform()
        await this.onSearch();
        await this.init();
    },

    methods: {
        async initform(){
            let that=this;
            this.autoform.rule= [{type:'hidden',field:'id',title:'id',value: ''},
                     {type:'hidden',field:'occurrenceTime',title:'occurrenceTime',value: ''},
                     {type:'input',field:'goodsCode',title:'商品编码',value: '',props:{readonly:true}},
                     {type:'input',field:'goodsName',title:'商品名称',value: '',props:{readonly:true}},
                     {type:'input',field:'brandName',title:'采购负责人',value: '',props:{maxlength:10,readonly:true}},
                    //  {type:'select',field:'isCheckError',title:'审单状态',value:'',validate: [{type: 'boolean', required: true, message:'请选择'}],
                    //       options: [{value:null, label:'请选择'},{value:false, label:'正常'},{value:true, label:'异常'}]},
                      {type:"cascader",field:"_reason",title:"原因",value:[],props:{options:[
                      {value: '采购原因',label: '采购原因',},
                      {value: '运营原因',label: '运营原因',},
                      {value: '仓库原因',label: '仓库原因',},
                      {value: '客服原因',label: '客服原因',},
                      {value: '财务原因',label: '财务原因',},
                      ]}},
                      //{type:'DatePicker',field:'planArrivalTime',title:'预计到货日期',value:'',validate: [{type: 'string', required: true, message:'请输入预计到货日期'}],props: {type:'datetime',format:'yyyy-MM-dd',placeholder:'预计到货日期',}},
                     //{type:'input',field:'solution',title:'解决方案',value: '',props:{maxlength:40}},
                     {type:'editor',field:'remark',title:'原因分析',value:'',col:{span:20},validate: [{type: 'string', required: true, message:'请输入解决方案'}],
                      props:{maxlength:400,init:async(editor)=>{await that.initeditor(editor) }}}
                    ]
        },
        async init(){
            var res = await getAllProBrand();
            this.brandlist = res.data.map(item => {return { value: item.key, label: item.value };});
        },
        async initeditor(editor){
            editor.config.uploadImgMaxSize = 3 * 1024 * 1024 
            editor.config.excludeMenus = ['emoticon','video']
            editor.config.uploadImgAccept = []
            editor.config.customUploadImg =async function (resultFiles, insertImgFn) {
                console.log('resultFiles',resultFiles)
                const form = new FormData();
                form.append("image", resultFiles[0]);
                const res =await upLoadImage(form);
                var url=`${res.data}`
                console.log('url',url)
                insertImgFn(url)
            }
        },
        async onSearch(){
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        async getlist() {
            var params=this.getCondition();
            if(params===false){
                    return;
            }
            this.listLoading = true
            const res = await getOrderWithhold(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry=res.data.summary;
            // if(this.summaryarry)
            //     this.summaryarry.amountPaid_sum=parseFloat(this.summaryarry.amountPaid_sum.toFixed(6));
            // data.forEach(d => {
            //     d._loading = false
            // })
            this.list = data
       },
       //获取查询条件
        getCondition(){
            if (this.filter.timerange&&this.filter.timerange.length>1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({message:"请先选择日期",type:"warning"});
                return false;
            }
            //this.filter.platform=2
            var pager = this.$refs.pager.getPager();
            var page  = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }

            return params;
        },
        async onHand(row){
            this.formtitle='编辑'
            this.editVisible = true
            var arr = Object.keys(this.autoform.fApi);
            if(arr.length > 0)
                this.autoform.fApi.resetFields()
            this.$nextTick(async () =>{
                await this.autoform.fApi.setValue(row)
            })
            
        },
        async onEditSubmit(){            
            await this.autoform.fApi.validate(async (valid, fail) => {
                if(valid){
                    this.editLoading=true;
                    const formData = this.autoform.fApi.formData();
                    this.editLoading=false;
                    // if (formData._reason.length>0) {
                    //    formData.reason=`${formData._reason[0]}-${formData._reason[1]}`;
                    // }
                    const res = await editOrderWithhold(formData)
                    if (res.code==1){
                        this.getlist();
                        this.editVisible=false
                    }
                }else{}
            })
        },
        selectchange:function(rows,row) {
            this.selids=[];console.log(rows)
            rows.forEach(f=>{
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event){
            if (column.property=='orderCount'){
                this.isshowOrderCount = true
                this.$nextTick(async () =>{
                    this.$refs.orderwithholdcount.onSearch(row.occurrenceTime,row.goodsCode,row.platform)
                })
                
            }
            else if(column.property=='reason'||column.property=='cgReMark'||column.property=='ckReMark'||column.property=='yyReMark'
                ||column.property=='kfReMark'||column.property=='cwReMark'){
                    this.visiblepopover = true
                    this.$nextTick(() =>{
                        this.$refs.withholdgoodscoderecord.onSearch(row.occurrenceTime,row.goodsCode)
                    })
                    
                }
        }, 
        async sortchange(column) {
            if (!column.order) this.pager = {};
            else {
                this.pager = {
                OrderBy: column.prop,
                IsAsc: column.order.indexOf("descending") == -1 ? true : false,
                };
            }
            await this.onSearch();
        },
    },
};
</script>

<style lang="scss" scoped>

</style>