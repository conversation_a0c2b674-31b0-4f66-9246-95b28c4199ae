<template>
  <MyContainer>
    <vxetablebase :id="'purchaseOrderFollowup_logDetails202408041609'" ref="table" :that='that' :isIndex='true'
      :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols'
      :isSelection="false" :isSelectColumn="false" seqAlign="center" :showoverflow="false" border :toolbarshow="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import { pageGetChangeLogs } from "@/api/inventory/basicgoods"
import { pageGetPurchaseOrderTrackChangeLog } from "@/api/inventory/purchaseOrderTrack"

const tableCols = [
  { sortable: 'custom', width: '80', align: 'center', prop: 'modifyTime', label: '时间', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'modifier', label: '操作人', },
  { width: 'auto', align: 'center', prop: 'diffPhoneNumber', label: '电话', formatter: (row) => row.diffPhoneNumber ? row.diffPhoneNumber : '' },
  { width: 'auto', align: 'center', prop: 'diffOrderNumber', label: '单号', formatter: (row) => row.diffOrderNumber ? row.diffOrderNumber : '' },
  { width: 'auto', align: 'center', prop: 'diffTrackStatus', label: '采购单状态', formatter: (row) => row.diffTrackStatus ? row.diffTrackStatus : '' },
  { width: 'auto', align: 'center', prop: 'diffConsignmentTypes', label: '发货方式', formatter: (row) => row.diffConsignmentTypes ? row.diffConsignmentTypes : '' },
  { width: 'auto', align: 'center', prop: 'diffRemark', label: '备注', formatter: (row) => row.diffRemark ? row.diffRemark : '' },
  { width: 'auto', align: 'center', prop: 'expectedDeliveryTime', label: '预计到货时间', formatter: (row) => row.expectedDeliveryTime ? (row.expectedDeliveryTime) : '' },
  { width: '100', align: 'center', prop: 'remarkImagesOldLog', label: '修改前图片', type: "imagess" },
  { width: '100', align: 'center', prop: 'remarkImagesNewLog', label: '修改后图片', type: "imagess" },
]
export default {
  name: "logDetails",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'modifyTime',
        isAsc: false,
        logicId: null,
        scene: 'purchaseOrderTrack',
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
  },
  methods: {
    async getList(type, params) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
        this.ListInfo.logicId = params.logicId
      }
      this.loading = true
      const { data: { list, total }, success } = await pageGetChangeLogs(this.ListInfo)
      if (success) {
        list.forEach(item => {
          item.modifyTime = item.modifyTime ? dayjs(item.modifyTime).format('YYYY-MM-DD') : ''
          if (item.newValue && item.newValue[0] == '{') {
            const newVal = JSON.parse(item.newValue)
            item.newValuePhoneNumber = newVal.TelePhoneNumber ? newVal.TelePhoneNumber : '无'
            item.newValueOrderNumber = newVal.OrderNumber ? newVal.OrderNumber : '无'
            item.newValueTrackStatus = newVal.TrackStatus ? newVal.TrackStatus : '无'
            item.newValueConsignmentTypes = newVal.ConsignmentTypes ? newVal.ConsignmentTypes : '无'
            item.newValueExpectedDeliveryTime = newVal.ExpectedDeliveryTime ? dayjs(newVal.ExpectedDeliveryTime).format('YYYY-MM-DD HH:mm:ss') : '无'
            item.newValueRemark = newVal.Remark ? newVal.Remark : '无'
            item.newValueRemarkImages = newVal.RemarkImages ? newVal.RemarkImages.split(',') : []
          } else {
            item.newValueRemark = item.newValue ? item.newValue : '无'
            item.newValuePhoneNumber = '无'
            item.newValueOrderNumber = '无'
            item.newValueTrackStatus = '无'
            item.newValueConsignmentTypes = '无'
            item.newValueExpectedDeliveryTime = '无'
            item.newValueRemarkImages = []
          }
          if (item.originalValue && item.originalValue[0] == '{') {
            const oldVal = JSON.parse(item.originalValue)
            item.originalValuePhoneNumber = oldVal.TelePhoneNumber ? oldVal.TelePhoneNumber : '无'
            item.originalValueOrderNumber = oldVal.OrderNumber ? oldVal.OrderNumber : '无'
            item.originalValueTrackStatus = oldVal.TrackStatus ? oldVal.TrackStatus : '无'
            item.originalValueConsignmentTypes = oldVal.ConsignmentTypes ? oldVal.ConsignmentTypes : '无'
            item.originalValueExpectedDeliveryTime = oldVal.ExpectedDeliveryTime ? dayjs(oldVal.ExpectedDeliveryTime).format('YYYY-MM-DD HH:mm:ss') : '无'
            item.originalValueRemark = oldVal.Remark ? oldVal.Remark : '无'
            item.originalValueRemarkImages = oldVal.RemarkImages ? oldVal.RemarkImages.split(',') : []
          } else {
            item.originalValueRemark = item.originalValue ? item.originalValue : '无'
            item.originalValuePhoneNumber = '无'
            item.originalValueOrderNumber = '无'
            item.originalValueTrackStatus = '无'
            item.originalValueConsignmentTypes = '无'
            item.originalValueExpectedDeliveryTime = '无'
            item.originalValueRemarkImages = []
          }
          item.diffRemark = item.originalValueRemark + '-->' + item.newValueRemark
          item.diffPhoneNumber = item.originalValuePhoneNumber + '-->' + item.newValuePhoneNumber
          item.diffOrderNumber = item.originalValueOrderNumber + '-->' + item.newValueOrderNumber
          item.diffTrackStatus = item.originalValueTrackStatus + '-->' + item.newValueTrackStatus
          item.diffConsignmentTypes = item.originalValueConsignmentTypes + '-->' + item.newValueConsignmentTypes
          item.expectedDeliveryTime = item.originalValueExpectedDeliveryTime + '-->' + item.newValueExpectedDeliveryTime
          item.remarkImagesOldLog = item.originalValueRemarkImages ? item.originalValueRemarkImages : []
          item.remarkImagesNewLog = item.newValueRemarkImages ? item.newValueRemarkImages : []
        })
        this.tableData = list
        this.total = total
        this.loading = false
      } else {
        //获取列表失败
        this.loading = false
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      const map = {
        diffPhoneNumber: 'telePhoneNumberChangeLog',
        diffOrderNumber: 'orderNumberChangeLog',
        diffTrackStatus: 'trackStatusChangeLog',
        diffConsignmentTypes: 'consignmentTypesChangeLog',
        diffRemark: 'remarkChangeLog',
      }

      if (prop) {
        // 如果有映射关系,则替换
        if (map[prop]) {
          this.ListInfo.orderBy = map[prop]
        } else {
          this.ListInfo.orderBy = prop
        }
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
