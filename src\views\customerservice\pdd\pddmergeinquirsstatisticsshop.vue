<template>
  <div v-loading="pageLoading">
    <!-- <template #header> -->
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    <!-- </template> -->

    <ces-table ref="table" :that='that' :summaryarry="summaryarry" :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :tableData='inquirsstatisticslist' @select='selectchange' :isSelection='false'
      :isSelectColumn='false' :tableCols='tableCols' :loading="listLoading" style="height: 94%;">
    </ces-table>

    <!-- <template #footer> -->
      <my-pagination ref="myPager" :total="total" @get-page="onRefresh" />
    <!-- </template> -->

  </div>
</template>
<script>
import { getPddShopInquirsStatisticsMargeUserList } from '@/api/customerservice/pddInquirs'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";

import Decimal from 'decimal.js';
function precision(number, multiple) {
  return new Decimal(number).mul(new Decimal(multiple));
}

const tableCols = [
  { istrue: true, prop: 'groupName', label: '组名称', width: '120', sortable: 'custom' },
  { istrue: true, prop: 'sname', label: '姓名', width: '100', sortable: 'custom', formatter: (row) => row.sname },

  { istrue: true, prop: 'inquirs', label: '咨询人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'manualReply', label: '人工接待人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsCount', label: '询单人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'receiveCount', label: '最终成团人数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'successpayrate', label: '询单转化率', width: '80', sortable: 'custom', formatter: (row) => { return (row.successpayrate ? precision(row.successpayrate, 100).toFixed(2) : 0) + "%" } },

  { istrue: true, prop: 'threeSecondLost', label: '3分钟未回复人数', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'threeSecondGet', label: '3分钟回复人数', width: '90', sortable: 'custom' },
  { istrue: true, prop: 'threeSecondReplyRate', label: '3分钟人工回复率', width: '90', sortable: 'custom', formatter: (row) => { return (row.threeSecondReplyRate ? precision(row.threeSecondReplyRate, 100).toFixed(2) : 0) + "%" } },
  { istrue: true, prop: 'responseTime', label: '均响(秒)', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'thirtySecondResponseRate', label: '30秒应答率', width: '80', sortable: 'custom', formatter: (row) => { return (row.thirtySecondResponseRate ? precision(row.thirtySecondResponseRate, 100).toFixed(2) : 0) + "%" } },

  { istrue: true, prop: 'outTimes', label: '出勤人次', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'reciveTimes', label: '人均接待量', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsPercentstr', label: '询单占比', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'ipsPriceRate', label: '客单价', width: '80', sortable: 'custom' },

  { istrue: true, prop: 'salesvol', label: '客服销售额（元）', width: '100', sortable: 'custom' },
  { istrue: true, prop: 'serviceScore', label: '客服服务分', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'lowRatingOrderCount', label: '评分≤3订单数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'disputeRefundCount', label: '纠纷退款数', width: '80', sortable: 'custom' },
  { istrue: true, prop: 'complainCount', label: '投诉数', width: '80', sortable: 'custom' },

];
export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      Filter: {
      },
      shopList: [],
      userList: [],
      groupList: [],
      inquirsstatisticslist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: { count_sum: 10 },
      pager: { OrderBy: "ipsCount", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      params: {},
    };
  },
  async mounted() {
  },
  created() {
  },
  methods: {
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onRefresh();
    },
    async loadData(param) {
      this.Filter.startSdate = param.startSdate;
      this.Filter.endSdate = param.endSdate;
      this.Filter.shopname = param.shopname;

      this.$refs.myPager.setPage(1);
      this.onRefresh();
    },
    async onRefresh() {

      const para = { ...this.Filter };
      var pager = this.$refs.myPager.getPager();
      this.params = {
        ...pager,
        ...this.pager,
        ...para,
      }
      this.listLoading = true;
      const res = await getPddShopInquirsStatisticsMargeUserList(this.params);
      this.listLoading = false;
      this.total = res.total
      this.inquirsstatisticslist = res.list;
      this.summaryarry = res.summary;
    },

    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
