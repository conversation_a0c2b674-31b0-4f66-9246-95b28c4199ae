<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <div class="publicCss" style="width: 170px;">
          <inputYunhan ref="refGoodsCode" :inputt.sync="ListInfo.goodsCode" v-model="ListInfo.goodsCode" width="170px"
            placeholder="商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="1000" :maxlength="20000"
            @callback="callbackGoodsCode($event, 'goodsCode')" title="商品编码">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.brandId" clearable filterable placeholder="请选择采购员" class="publicCss">
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.warehouseId" placeholder="请选择仓库" class="publicCss" clearable filterable>
          <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <div class="publicCss" style="width: 170px;">
          <YhUserelector :value.sync="ListInfo.productWeightManager_ddid"
            :text.sync="ListInfo.productWeightManagerNameList" style="width: 170px;" placeholder="请输入负责人" />
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="exportProps">导出</el-button>
      </div>
    </template>
    <vxetablebase :id="'unweighedIndex202506061127'" :tablekey="'unweighedIndex202506061127'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :border='true'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getNoWeightGoodsList, exportNoWeightGoodsList, pageGetTbWarehouseAsync } from '@/api/inventory/prepack.js'
import inputYunhan from "@/components/Comm/inputYunhan";
import YhUserelector from '@/components/YhCom/yh-userselector.vue'

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '250', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'styleCode', label: '系列编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'brandName', label: '采购员', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseName', label: '仓库名称', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'binLocation', label: '仓位', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productWeightManager_Name', label: '商品重量负责人姓名', },
]
export default {
  name: "unweighedIndex",
  components: {
    MyContainer, vxetablebase, inputYunhan, YhUserelector
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        brandId: null,//采购员
        warehouseId: null,//仓库
        goodsCode: null,//商品编码
        productWeightManager_ddid: null,//商品重量负责人
        productWeightManagerNameList: null,//商品重量负责人姓名
      },
      brandlist: [],//采购员
      wareHouseList: [],//仓库
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    callbackGoodsCode(val, type) {
      const map = {
        goodsCode: () => (this.ListInfo.goodsCode = val),
      };
      map[type]?.();
    },
    async init() {
      var res = await getAllProBrand();
      this.brandlist = res.data.map(item => {
        return { value: item.key, label: item.value };
      });
      const params = {
        currentPage: 1,
        pageSize: 1000,
        orderBy: null,
        isAsc: false,
      }
      const { data: { list } } = await pageGetTbWarehouseAsync(params)
      this.wareHouseList = list
    },
    async exportProps() {
      this.loading = true
      const { data } = await exportNoWeightGoodsList(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '编码未称重导出数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getNoWeightGoodsList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}
</style>
