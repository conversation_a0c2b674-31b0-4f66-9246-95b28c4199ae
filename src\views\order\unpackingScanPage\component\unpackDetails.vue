<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-select v-model="ListInfo.wmsId" placeholder="仓库" class="publicCss" clearable filterable>
                    <el-option v-for="item in wareHouseList" :key="item.wms_co_id" :label="item.name"
                        :value="item.wms_co_id" />
                </el-select>
                <number-range :min.sync="ListInfo.totalCostMin" :max.sync="ListInfo.totalCostMax" min-label="最小总成本"
                    max-label="最大总成本" class="publicCss" />
                <number-range :min.sync="ListInfo.goodsCountMin" :max.sync="ListInfo.goodsCountMax" min-label="最小拆分编码数"
                    max-label="最大拆分编码数" class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.labels" placeholder="标签" maxlength="50" clearable class="publicCss" />
                <el-select v-model="ListInfo.isDiscard" placeholder="是否丢弃" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.hasSplitPin" placeholder="是否安排拆包" class="publicCss" clearable>
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-input v-model.trim="ListInfo.splitArea" placeholder="区域" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.splitShelve" placeholder="货架" maxlength="50" clearable
                    class="publicCss" />
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0;" height="100%"
            :showsummary="data.summary ? true : false" :summaryarry="data.summary" @sortchange="sortchange" />
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, formatLinkProCode } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/Split/Unpacking/'
import middlevue from '@/store/middle.js'
import { pageGetTbWarehouseAsync } from "@/api/inventory/prepack.js"
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange, buschar, numberRange, inputYunhan
    },
    props: {
        styleCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            api,
            platformlist,
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: '',
                isAsc: false,
                summarys: [],
            },
            data: {},
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: [], // 趋势图数据
            },
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            wareHouseList: [],
        }
    },
    async mounted() {
        await this.getCol();
        await this.getList()
        this.getWareHouse()
    },
    methods: {
        getStatisticProps(e) {
            this.$set(this.ListInfo, 'totalCostMin', e.totalCostMin)
            this.$set(this.ListInfo, 'totalCostMax', e.totalCostMax)
            this.$set(this.ListInfo, 'goodsCountMin', e.goodsCountMin)
            this.$set(this.ListInfo, 'goodsCountMax', e.goodsCountMax)
            this.$set(this.ListInfo, 'goodsCode', e.goodsCode)
            this.$set(this.ListInfo, 'goodsName', e.goodsName)
            this.$set(this.ListInfo, 'labels', e.labels)
            this.$set(this.ListInfo, 'isDiscard', e.isDiscard)
            this.$set(this.ListInfo, 'hasSplitPin', e.hasSplitPin)
            this.$set(this.ListInfo, 'splitArea', e.splitArea)
            this.$set(this.ListInfo, 'splitShelve', e.splitShelve)
            this.$set(this.ListInfo, 'wmsId', e.wmsId)
            console.log('this.ListInfodetails', this.ListInfo);
            this.getList()
        },
        async getWareHouse() {
            const params = {
                currentPage: 1,
                pageSize: 1000,
                orderBy: null,
                isAsc: false,
            }
            const { data: { list } } = await pageGetTbWarehouseAsync(params)
            this.wareHouseList = list
        },
        proCodeCallback(val) {
            this.ListInfo.proCode = val
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.isExport = true
            await request.post(`${this.api}ExportData`, this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns`)
            if (success) {
                data.forEach(item => {
                    if (item.prop == 'orderNoInner') {
                        item.type = 'orderLogInfo'
                        item.orderType = 'orderNoInner'
                    }
                    if (item.prop == 'proCode') {
                        item.type = 'html'
                        item.formatter = (row) => formatLinkProCode(row.platform, row.proCode)
                    }
                })
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;
    flex-wrap: wrap;

    .publicCss {
        width: 200px;
        margin: 0 5px 5px 0;
    }
}
</style>
