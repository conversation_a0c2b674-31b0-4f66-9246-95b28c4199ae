<template>
  <el-dialog title="审核" :visible.sync="isShow" width="50%" :before-close="closeDialog" v-dialogDrag v-if="isShow">
    <div style="" >
      <el-descriptions size="small" class="margin-top" title="" :column="3">
        <el-descriptions-item label="平台/店铺"> {{platformName}} / {{dataJson?.shopName}}</el-descriptions-item>
        <el-descriptions-item label="线上订单号">{{dataJson.orderNo}}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{conversationTime}}   <el-button style="margin-left:20px;" type="primary" @click="showOrhide">{{this.isShowOrHide?"收起聊天记录":"展开聊天记录"}}</el-button></el-descriptions-item>
        <el-descriptions-item v-if="interfaceType && dataJson?.afterSalesRemark !='' &&  dataJson?.afterSalesRemark  !=null" label="售后原因">{{dataJson?.afterSalesRemark }}</el-descriptions-item>

      </el-descriptions>
    </div>

<!-- 聊天记录 -->
  <div v-show="isShowOrHide">
      <chartComponent   ref="chartRef"    :isShow="isShow"></chartComponent>
  </div>

    <el-form ref="formDataRef" :model="formData" label-width="100px" style="margin-top:10px">
      <div style="display: flex; margin-top: 20px">
        <el-form-item label="审核" prop="auditResult" :rules="{required: true,message: '请选择审核结果',trigger: ['blur', 'change'],}">
              <el-select style="width: 120px" v-model="formData.auditResult" placeholder="请选择" class="el-select-content"  @change="changeResult" clearable>
                      <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
        </el-form-item>

        <el-form-item label="审核类型" prop="auditResultReasonType" :rules="{required: true,message: '请选择',trigger: ['blur', 'change'],}"
            v-if="formData.auditResult == 2 ">
              <el-select   v-model="formData.auditResultReasonType" placeholder="请选择" class="el-select-content" clearable filterable multiple collapse-tags>
                    <el-option v-for="item in firstStatusList" :key="item" :label="item" :value="item" />
              </el-select>
        </el-form-item>
<!-- 售后才有退款原因稽查，责任人 -->
        <el-form-item   v-if="interfaceType" label="退款原因稽查" prop="reasonForRefund"  style="margin-left: 30px" >
               <el-select   v-model="formData.reasonForRefund" placeholder="请选择" class="el-select-content" clearable filterable multiple collapse-tags>
                    <el-option v-for="item in reasonForRefundList" :key="item" :label="item" :value="item" />
              </el-select>
        </el-form-item>
        <el-form-item v-if="interfaceType"  label="责任人" prop="person" >
             <el-input v-model="formData.person" maxlength="20"></el-input>
        </el-form-item>
<!-- 售后才有退款原因稽查，责任人 -->
      </div>

      <el-form-item label="上传凭证:" prop="firstStatus"  v-if="isShow" >
            <uploadimgFile v-if="editPriceVisible" ref="uploadimgFile" :disabled="isView" :ispaste="!isView"
                      :noDel="isView" :accepttyes="accepttyes" :isImage="true" :uploadInfo="chatUrls" :keys="[1, 1]"
                      @callback="getImg" :imgmaxsize="9" :limit="9" :multiple="true">
            </uploadimgFile>
      </el-form-item>

        <el-form-item label="责任客服：" prop="newMemberName">
                 <template >
               <el-select v-model="formData.responsibleName" :remote-method="remoteMethod"  remote clearable filterable maxlength="20" >
                <el-option v-for="item in customerlist" :key="item.value" :label="item.label"
                   :value="item.value" />
          </el-select>
                </template>
        </el-form-item>


      <el-form-item label="说明" prop="auditRemark">
        <el-input show-word-limit :maxlength="100" v-model="formData.auditRemark" type="textarea" />
      </el-form-item>

      <div>
        <el-collapse :value="activeReviewIndex" @change="(val) => activeReviewIndex = val">
          <el-collapse-item :name="0" v-if="reviewList.length > 0">
            <template #title>
              <span>审核信息 1</span>
            </template>

            <div style="display: flex">
              <el-form-item label="审核:" class="first custom-label">
                <div style="width: 50px;">{{ filterInitialAuditType(reviewList[0].initialAuditType) }}</div>
              </el-form-item>
              <el-form-item label="数据编码:" style="margin-left: 50px" class="first custom-label">
                <div style="width: 180px;">{{ reviewList[0].conversationId }}</div>
              </el-form-item>
              <el-form-item label="审核类型:" style="margin-left: 20px" class="first custom-label"
                            v-if="reviewList[0].initialAuditType == 2">
                <div style="width:300px">{{ reviewList[0].refuseInitialAuditType }}</div>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="审核人:" class="custom-label">
                <div style='width:100px'>{{ reviewList[0].initialOperator }}</div>
              </el-form-item>
              <el-form-item label="审核日期:"  class="custom-label">
                <div style='width:150px'>{{ reviewList[0].initialOperatorTime }}</div>
              </el-form-item>
              <el-form-item label="审核凭证:" class="custom-label" style="margin-left: 50px">
                <span v-for="(image, imgIndex) in reviewList[0].initialAuditImgs ? reviewList[0].initialAuditImgs.split(',') : []" :key="imgIndex">
                  <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
                </span>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="责任客服:"  class="custom-label">
                <div style='width:100px'>{{ reviewList[0].initialResponsibleName }}</div>
              </el-form-item>
              <el-form-item label="说明:"  class="custom-label">
                <div>{{ reviewList[0].initialAuditRemark }}</div>
              </el-form-item>
            </div>
          </el-collapse-item>

          <el-collapse-item v-for="(review, index) in reviewList.slice(1)" :key="index" :name="(index + 1).toString()">
            <template #title>
              <span>审核信息 {{ index + 2 }}</span>
            </template>

            <div style="display: flex">
              <el-form-item label="审核:" class="first custom-label">
                <div style="width: 50px;">{{ filterInitialAuditType(review.initialAuditType) }}</div>
              </el-form-item>
              <el-form-item label="数据编码:" style="margin-left: 50px" class="first custom-label">
                <div style="width: 180px;">{{ review.conversationId }}</div>
              </el-form-item>
              <el-form-item label="审核类型:" style="margin-left: 20px" class="first custom-label"
                            v-if="review.initialAuditType == 2">
                <div style="width:300px">{{ review.refuseInitialAuditType }}</div>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="审核人:" class="custom-label">
                <div style='width:100px'>{{ review.initialOperator }}</div>
              </el-form-item>
              <el-form-item label="审核日期:"  class="custom-label">
                <div style='width:150px'>{{ review.initialOperatorTime }}</div>
              </el-form-item>
              <el-form-item label="审核凭证:" class="custom-label" style="margin-left: 50px">
                <span v-for="(image, imgIndex) in review.initialAuditImgs ? review.initialAuditImgs.split(',') : []" :key="imgIndex">
                  <el-image v-if="image" :src="image" :preview-src-list="[image]" style="padding-right:10px"></el-image>
                  <span v-else>无</span>
                </span>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="责任客服:"  class="custom-label">
                <div style='width:100px'>{{ review.initialResponsibleName }}</div>
              </el-form-item>
              <el-form-item label="说明:"  class="custom-label">
                <div>{{ review.initialAuditRemark }}</div>
              </el-form-item>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

    </el-form>
    <template #footer>
           <div class="dialog-footer" style="display:flex;justify-content: flex-end;">
                    <div style="position: relative;">
                        <el-button @click="btnChange('last')" :disabled="isLastButtonDisabled" type="primary">查看上一个</el-button>
                        <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" >
                            <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i class="el-icon-question"></i></el-tooltip>
                        </div>
                    </div>
                    <div style="position: relative;margin-left:20px;">
                         <el-button @click="btnChange('next')" :disabled="isNextButtonDisabled" type="primary" >查看下一个</el-button>
                          <div  style="position: absolute;right:-20px;top:-20px; cursor:pointer;" >
                              <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i class="el-icon-question"></i></el-tooltip>
                         </div>
                    </div>
                    <div style="position: relative;margin-left:20px;">
                          <el-button @click="onSubmitDot" :disabled="isSubmitDisabled" :loading="isLoading" type="primary" v-throttle="3000">保存关闭</el-button>
                           <el-button @click="saveNextSubmitDot" :disabled="isNextSubmitDisabled" :loading="isLoading2" type="primary" v-throttle="3000">保存审核下一个</el-button>
                    </div>
         </div>
    </template>
  </el-dialog>
</template>
<script>
import { saveChatRecordReview,getUnPayOrderById,getGroupSnameList,GetUnpayOrderSalesList } from "@/api/customerservice/chartCheck";
import { queryAllCustomerServiceDDUserTop100 } from "@/api/admin/deptuser";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { formatTime } from "@/utils";
import chartComponent from "./SalesDialog/chartComponent"
const signalR = require('@microsoft/signalr')
import store from '@/store'

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    }, isView: {
            type: Boolean,
            default: false
        },
  },
  components:{uploadimgFile,chartComponent},
  computed: {
    platformName() {
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
      } else {
        return ""
      }
    },
    conversationTime() //日期转换
    {
      return this.dataJson?.conversationTime?formatTime(this.dataJson?.conversationTime , "YYYY-MM-DD"):""
    },
  },
   mounted() {
        //  this.showList();
        this.orgOptions = [...this.customerlist];
         if (this.pictures) {
            this.chatUrls = this.pictures.split(',').map((item, i) => {
                return {
                    url: item,
                    name: `聊天截图${i + 1}`
                }
            })
        }
     this.editPriceVisible = true
  },
  data() {
    return {
      // isView:true,
      formData: {
        auditResult: "",
        auditResultReasonType: [],
        auditRemark: "",
        person:"",
        responsibleName:null,
        groupName:"",
        groupManager:"",
        sname:"",

      },
      statusList: [
        { name: "合格", value: 1 },
        { name: "不合格", value: 2 },
      ],
      // tianMaoStatusList:["回复问题","专业能力","敷衍怠慢","态度问题","客户自身原因","存在违规"],//天猫/淘宝/抖音——售前
      // tianMaoStatusList_after:["回复问题","流程问题","敷衍怠慢","专业能力","态度问题","存在违规"],//天猫/淘宝/抖音——售后
      // pxxStatusList: ["对产品不熟悉","辱骂/反问/嘲讽/阴阳顾客","违背/过度承诺","推荐错误","答非所问","敷衍了事","话术不专业","其他"],//pxx——售前
      // pxxStatusList_after: ["未按流程处理售后","辱骂/反问/嘲讽/阴阳顾客","未引导买家修改退款原因","答非所问","话术不专业","敷衍了事","违背/过度承诺","其他"],//pxx——售后
      allList_pre:["回复问题","专业能力","敷衍怠慢","存在违规/过度承诺","存在违规/引导线下交易","存在违规/其他违规","态度问题/辱骂客户","态度问题/怒怼客户","态度问题/反问客户","态度问题/不耐烦"],//全部平台
      allList_after:["回复问题","流程问题","敷衍怠慢","专业能力","存在违规/过度承诺","存在违规/引导线下交易","存在违规/其他违规","态度问题/辱骂客户","态度问题/怒怼客户","态度问题/反问客户","态度问题/不耐烦", "小额打款异常"],//全部平台
      firstStatusList:[],
      reasonForRefundList:["运营问题/虚假宣传","运营问题/编码问题","运营问题/低价引流问题","仓库问题/订单错漏发","仓库问题/包装问题","仓库问题/定制问题","售前问题/推荐错误","售前问题/过度承诺","售前问题/漏备注或备注错","快递问题","采购问题","售后问题","产品质量问题"],
      dataJson: {},
      isShouQian:false,
      isLoading: false,
      isLoading2:false,
      chatUrls: [],//图片
      editPriceVisible: false,//图片
      accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',//上传格式
      pictures:null,
      isShowOrHide:true,
      tableData:[],
      isLastButtonDisabled:false,
      isNextButtonDisabled:false,
      isNextSubmitDisabled:false,
      isSubmitDisabled:false,
      currentInfo:null,
      interfaceType:false,// true：售后 false：售前
      customerlist:[],
      orgOptions: [],
      reviewList: [],
      activeReviewIndex: [0]
    };
  },
  created(){
document.addEventListener('keydown', this.handleArrowUp);
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$refs.chartRef.dataJson = this.dataJson
          this.buttonDisabled();
           this.initWareSignalR();
        });

      }
    },
  },
  methods: {
    async initWareSignalR() {
        const signalWareRconnection = new signalR.HubConnectionBuilder().withUrl(process.env.VUE_APP_BASE_API_CustomerService + '/ChatAuditNoticeHub', {
            accessTokenFactory: () => { return store.getters.token },
            skipNegotiation: true,
            transport: signalR.HttpTransportType.WebSockets,
        }).withAutomaticReconnect([3000, 5000, 10000, 15000, 30000]).build();
         signalWareRconnection.on('ChatAuditNotice', data => {
            setTimeout(() => {
                  const ref= JSON.parse(data);
                  if(ref.PageType<=1 && this.dataJson.conversationId==ref.Id && ref.Data.AuditState!=0)
                  {
                    this.isSubmitDisabled=true;
                    this.isNextSubmitDisabled=true;
                  }
            }, 2000);
          })
        signalWareRconnection.start()
     },
   handleArrowUp(event) {
      if(!this.isShow){
        return
      }
      if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('last');
      }
     else if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
        // 处理向上键被按下的逻辑
        this.btnChange('next');
      }
    },
    changeResult()
    {
      if(this.formData.auditResult==1){
        this.formData.auditResultReasonType=[];
      }
       if(this.isShouQian) //售前 platform==2 pxx
        {
          // if(this.dataJson?.platform==2)

          //       this.firstStatusList=this.pxxStatusList
          // else this.firstStatusList=this.tianMaoStatusList
           this.firstStatusList=this.allList_pre


        }
        else//售后 platform==2 pxx
        {
          // if(this.dataJson?.platform==2)
          //      this.firstStatusList=this.pxxStatusList_after
          // else this.firstStatusList=this.tianMaoStatusList_after
          this.firstStatusList=this.allList_after
        }

    },
    onSubmitValidate: function () {
      let isValid = true;
      this.$refs.formDataRef.validate((valid) => {
        isValid = valid;
      });
      return isValid;
    },
    async onSubmitDot() { //保存关闭
      try {
        if (!this.onSubmitValidate()) {
          return;
        }
        this.isLoading = true;
        if (this.formData.responsibleName!=null) {
              var formDatas=this.formData.responsibleName.split(',');
              this.formData.responsibleName=formDatas[0];
              this.formData.groupName =formDatas[1];
              this.formData.groupManager =formDatas[2];
            }
        let data = {
          ...this.formData,
          conversationId: this.dataJson.conversationId,
          platform: this.dataJson.platform,
          shopName: this.dataJson.shopName,
          orderNo: this.dataJson.orderNo,
          snick: this.dataJson.chatAccount ?? "黑色草莓",
          reviewType: 1,
          AuditImgs:this.pictures,
        };
        const res = await saveChatRecordReview(data);
        this.$emit("upData");
        this.$message = res.message;
        this.isLoading = false;
        this.pictures = [];
        this.chatUrls = [];
         this.$refs.uploadimgFile.setData([]);
        this.$refs.formDataRef.resetFields();
        this.formData.responsibleName = "";
        this.customerlist=[];
        this.closeDialog();
      } catch (error) {
        this.isLoading = false;
        this.closeDialog();
      }
    },
    closeDialog() {//关闭
      this.pictures = [];
      this.chatUrls = [];
      this.$refs.formDataRef.resetFields();
      this.formData.responsibleName = "";
      this.customerlist=[];
      this.$emit("closeDialog");
    },
   async saveNextSubmitDot(){  //保存审核下一个
            if (!this.onSubmitValidate()) {
              return;
            }
            this.isLoading2 = true;
            if (this.formData.responsibleName!=null) {
              var formDatas=this.formData.responsibleName.split(',');
              this.formData.responsibleName=formDatas[0];
              this.formData.groupName =formDatas[1];
              this.formData.groupManager =formDatas[2];
            }

            let data = {
              ...this.formData,
              conversationId: this.dataJson.conversationId,//
              platform: this.dataJson.platform,
              shopName: this.dataJson.shopName,
              orderNo: this.dataJson.orderNo,
              snick: this.dataJson.chatAccount ?? "黑色草莓",
              reviewType: 1,
              AuditImgs:this.pictures,
            };
            console.log(this.dataJson.conversationId,'当前审核id和状态',this.dataJson.auditState)
            const res = await saveChatRecordReview(data);
             this.$emit("upData");
            // this.$emit("upData");
            this.$message = res.message;
            this.isLoading2 = false;
            this.pictures = [];
            this.chatUrls = [];
            this.$refs.uploadimgFile.setData([]);
            this.$refs.formDataRef.resetFields();
            this.formData.responsibleName = "";
            this.customerlist=[];
            await this.btnChange('next','submit');//下一条数据
    },
    getImg(data) {
        if (data)
        {
            this.chatUrls = data
            this.pictures = data.map(item => item.url).join(',')
        }
    },
     showOrhide(){
      if(this.isShowOrHide){
         this.isShowOrHide=false;
      }
      else {
        this.isShowOrHide=true
      }
    },
   async btnChange(last,btn){//查看上一个、查看下一个
      const index = this.tableData.findIndex(index=>index.conversationId==this.dataJson.conversationId);
      if(btn=='submit'){//已审核过后的数据进行移除
          this.tableData = this.tableData.filter((item) => {return item.conversationId != this.dataJson.conversationId;});
      }

      if(last=='last' && this.tableData.length>0){

          var number=index-1;
          //这种情况只能是最后一条数据审核完了，并且table里面已经清除当前审核id，
          //所以找不到当前审核的id下标，但table中还有数据，我需要上翻页
          if(this.tableData.length>0 && index==-1)
          {
              number=this.tableData.length-1
          }

            const info=  this.tableData[number]
            this.dataJson=JSON.parse(JSON.stringify(info))
            this.keyWord=info?.conversationId ?? null;
            this.platform=info.platform;
            this.$refs.chartRef.dataJson = info;
            // this.$refs.chartRef.getChartList()
      }
      else if(last=='next' && this.tableData.length>0)
      {
            const number=btn =='submit' ? index : index+1;
            const info=this.tableData[number]
            this.dataJson= JSON.parse(JSON.stringify(info))
            this.keyWord=info?.conversationId ?? null;
            this.platform=info.platform;
            this.$refs.chartRef.dataJson = info;
            // this.$refs.chartRef.getChartList()
      }
      this.chatUrls = [];
      this.pictures = [];
      this.$refs.uploadimgFile.setData([]);
      this.$refs.formDataRef.resetFields();
      this.formData.responsibleName = "";
      this.customerlist=[];
      this.buttonDisabled(btn,index)//按钮是否禁用
     //获取是否已经审核的数据
     if (this.dataJson.conversationUserId) {
       var params = {
         conversationUserId: this.dataJson.conversationUserId,
         conversationId: this.dataJson.conversationId,
         salesType: 0,
         shopId: this.dataJson.shopId,
         auditState:2,
         orderBy: "createdTime",
         isAsc: false
       }
       const res = await GetUnpayOrderSalesList(params);
       if (!res?.success) {
         return;
       }
       this.reviewList = res.data.list;
     } else {
       this.reviewList = [];
     }
    },
   async  buttonDisabled(btn,indexs){ //按钮是否禁用

        this.isLastButtonDisabled=false;
        this.isNextButtonDisabled=false;
        this.isSubmitDisabled=false;
        this.isNextSubmitDisabled=false;
        const index = btn =='submit' ? indexs : this.tableData.findIndex(index=>index.conversationId==this.dataJson.conversationId);
      if (this.tableData.length === 1) {
          this.isLastButtonDisabled = true;
          this.isNextButtonDisabled = true;
      }
        if(index==0 || this.tableData.length==0){
          this.isLastButtonDisabled=true;
        }
        if(index==this.tableData.length-1 || (btn=='submit' && index==this.tableData.length)){
          this.isNextButtonDisabled=true;
        }
        this.currentInfo = await getUnPayOrderById({id:this.dataJson.conversationId});
        if(this.currentInfo.data?.auditState!=0){
           this.isSubmitDisabled=true;
           this.isNextSubmitDisabled=true;
        }
    },
    // async remoteMethod(query) {
    //   if (query && query.length > 50) return this.$message.error("输入内容过长");
    //   if (query !== '') {
    //         var res = await queryAllCustomerServiceDDUserTop100({ keywords: query });
    //         if (res && res.success) {
    //                     this.customerlist = res.data?.map(item => {
    //                       return {  label: [
    //                                         item.userName ? item.userName : '',
    //                                         item.position ? `(${item.position}` : '',
    //                                         item.empStatusText ? `, ${item.empStatusText})` : '',
    //                                         item.deptName ? ` ${item.deptName}` : ''
    //                                         ].filter(Boolean).join(''),
    //                                         value: item.ddUserId || null,
    //                                         userName: item.userName,
    //                                         extData: item }
    //                     });
    //                 }  }else{
    //                   this.customerlist = [...this.orgOptions];
    //                 }
    //     },
    async remoteMethod(query) {
      if (query && query.length > 50) return this.$message.error("输入内容过长");
      if (query !== '') {
        var params = {
          sname: query,
          platform: this.dataJson.platform,
          salesType: this.salesType
        }
        var res = await getGroupSnameList(params);
        if (res && res.success) {
          this.customerlist = res.data?.map(item => {
            return {
              label: [
                item.sname ? item.sname : '',
                item.groupName ? `(${item.groupName})` : '',
                item.groupManager ? ` 组长:` : '',
                item.groupManager ? ` ${item.groupManager}` : ''
              ].filter(Boolean).join(''),
              value: [
                item.sname ? item.sname : '',
                item.groupName ? `${item.groupName}` : '',
                item.groupManager ? ` ${item.groupManager}` : ''
              ].filter(Boolean).join(','),
              userName: item.sname,
              extData: item
            }
          });
        }
      }
      else {
        this.customerlist = [...this.orgOptions];
      }
    },
    filterInitialAuditType(type) {
      let name = ''
      if (type == 1) {
        name = '合格'
      } else if (type == 2) {
        name = '不合格'
      }
      return name;
    },
    iniImgsSplitList() //审核图片分割
    {
      return this.dataJson?.initialAuditImgs ? this.dataJson?.initialAuditImgs.split(",") : "";
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  color: #888888 !important; /* 更改为你想要的颜色 */
}
::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell{
  padding: 15px;
}
::v-deep .el-descriptions__body .el-descriptions__table{
  background-color: rgb(242, 244, 245);
}

::v-deep .el-descriptions__body .el-descriptions-item__container {
  font-size: 14px;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 50px;
}

</style>
