<template>
    <my-container>
        <el-tabs v-model="activeName" style="height:94%;">
            <el-tab-pane label="加工单" name="tab0" style="height: 100%;">
                <tabProcessingOrder :filter="filter" ref="tabProcessingOrder" />
            </el-tab-pane>
            <el-tab-pane label="加工日志" name="tab1" style="height: 100%;">
                <tabProcessingOrderRecord :filter="filter" ref="tabProcessingOrderRecord" />
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import tabProcessingOrder from "@/views/base/goods/processingordertemplate";
    import tabProcessingOrderRecord from "@/views/base/goods/processingorderrecord";

    export default {
        name: 'Roles',
        components: { MyContainer, tabProcessingOrder, tabProcessingOrderRecord },
        data() {
            return {
                activeName: "tab0",
                filter: {
                },
            }
        },
        async mounted() {
        },
        methods: {
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
</style>
