<template>
    <!-- 薪资核算 -->
    <my-container>
        <template #header>
            <el-button style="height: 28px;margin-right:5px;" type="primary" @click="onSearch">刷新</el-button>
          
            <el-button style="height: 28px;margin-right:5px;" type="primary" @click="saveData()">保存</el-button>
          
        </template>
        <template>
            <div class="content" :style="versionId?{height:'100%'}:{height:'80vh'}">
                <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212">
                    <template #buttons>
                        <slot name="tbHeader" />
                    </template>
                </vxe-toolbar>
                <div style="width: 100%; height: 100%;">
                    <vxe-table border style="width: 100%; height: 100%;" height="100%" resizable show-overflow
                        show-footer-overflow keep-source size="mini" ref="xTable" :loading="listLoading" :data="configlist"
                        show-footer :footer-method="footerMethod" stripe class="vxetable202212161323 mytable-scrollbar20221212" :row-config="{isCurrent: true, isHover: true}"
                        :edit-config="{ trigger: 'manual', mode: 'row', showStatus: true, showIcon: false, autoClear: false }">
                        <vxe-column field="platFormName" fixed="left" title="平台名称" width='150'>
                        </vxe-column>
                        <vxe-column field="ksDayCount" title="近多少天亏损" width='200'>
                            <template #default="{ row }">
                                <vxe-input v-model="row.ksDayCount" type="integer" min="0" max="100" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="czDayCount" title="多少天无记录重置"  width='200'>
                            <template #default="{ row }">
                                <vxe-input v-model="row.czDayCount" type="integer" min="0" max="100" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                        <vxe-column field="zdDayCount" title="最低要有多少天记录"  width='200'>
                            <template #default="{ row }">
                                <vxe-input v-model="row.zdDayCount" type="integer" min="0" max="100" :controls="false"
                                    placeholder="请输入数值"></vxe-input>
                            </template>
                        </vxe-column>
                    </vxe-table>
                </div>
            </div>
        </template>
      
    </my-container>
</template>

<script>

    
import { formatTime } from "@/utils";

import {getInitPlatformLossDays,initPlatformLossDays} from '@/api/bookkeeper/reportdayV2'

import MyConfirmButton from "@/components/my-confirm-button";
import vxetablebase from "@/components/VxeTable/vxetablemedia.vue";
import MyContainer from "@/components/my-container";

export default {
 
    components: { MyContainer, vxetablebase, MyConfirmButton,},
    data() {
        return {
            configlist:[],
            listLoading:false,
        };
    },
  
    created() {
     
    },
    async mounted() {
        await this.onSearch();
     
    },
    methods: {
        async onSearch() {
        this.listLoading = true
            const res = await getInitPlatformLossDays()
            this.listLoading = false
            if (!res?.success) {
                return
            }
        
             this.configlist = res.data;
             
        },
       
       async saveData() {
          this.listLoading = true
           await initPlatformLossDays( this.configlist)
            this.listLoading = false
       },
    },
};
</script>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: row;
    width: 100%;
    // height: 100%;
}

::v-deep ::-webkit-scrollbar {
    height: 10px;
    width: 10px;
}

.pointer.cursor-pointer:hover {
    cursor: pointer;
}

.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*滚动条整体部分*/
.mytable-scrollbar20221212 ::-webkit-scrollbar {
    width: 18px;
    height: 26px;
}

/*滚动条的轨道*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
    box-sizing: border-box;
    border: 2px solid #F1F1F1;
    box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
}

// 滚动条鼠标悬停颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

// 滚动条拖动颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
    background-color: #dcdcdc;
}

/*滚动条的轨道*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

/*滚动条里面的小方块，能向上向下移动*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
    box-sizing: border-box;
    border: 2px solid #F1F1F1;
    box-shadow: inset 0 0 6px rgba(255, 255, 255, .5);
}

// 滚动条鼠标悬停颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

// 滚动条拖动颜色
.mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*边角，即两个滚动条的交汇处*/
.mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
    background-color: #dcdcdc;
}

::v-deep .vxetoolbar20221212{
    top: 30px !important;
}
</style>

