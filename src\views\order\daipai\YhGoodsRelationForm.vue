<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template>
            <!--表单-->
            <el-form :model="form" label-width="100px" label-position="right" :disabled="!formEditMode">
                <el-row >
                    <el-col :span="8">
                        <el-form-item  v-if="mode>1" label="商品编码：">
                            {{form.yhGoodsCode}}
                        </el-form-item>
                        <el-form-item  v-else label="商品编码：">
                            <el-input placeholder="请选择" :readonly="true" v-model="form.yhGoodsCode" >  
                                <el-button slot="append" icon="el-icon-search" @click="goodschoiceVisible=true;"></el-button>
                            </el-input>
                        </el-form-item>                        
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="商品名称：">
                            {{form.yhGoodsName}}
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="商品价格：">
                            {{form.yhGoodsPrice}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="代拍成本：">
                            {{calcDpTotalSupPrice}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      
                    </el-col>
                </el-row>   
                <el-row v-if="mode>2">
                    <el-col :span="8">
                        <el-form-item label="审核人：">
                            {{form.auditUserName}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="审核时间：">
                            {{form.auditTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      
                    </el-col>
                </el-row>              
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：">
                            <el-input type="textarea" v-model="form.remark" clearable maxlength="200" show-word-limit />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-tabs>
                            <el-tab-pane label="代拍厂家SKU明细">
                                <div :style="'height:'+ tableHeight+'px;'">
                                    <!--列表-->
                                    <ces-table ref="skuTable" :that='that' :isIndex='false' :hasexpandRight='true' 
                                    :hasexpand='true' :tableData='skuTableData' 
                                    :tableCols='skuTableCols' :loading="false" :isSelectColumn="false"
                                     rowkey="id" >
                                        <!-- center from jsonCols array  -->
                                        <template slot="right">
                                            <!-- right  -->                                           
                                            <el-table-column width="100" label="数量" prop="supplierSkuCount" :sortable="true">
                                                <template slot-scope="scope">
                                                    <el-input-number v-model.number="scope.row.supplierSkuCount" 
                                                        style="width:80px" type="number" 
                                                        :min="1" :max="10000" :precision="0" :controls="false"  size="mini"                                                       
                                                    />        
                                                </template>
                                            </el-table-column>                                          

                                            <el-table-column width="110" label="" flexd="right">
                                                <template slot="header">
                                                    <el-button type="primary" @click="supplierSkuListVisible=true">添加SKU</el-button>
                                                </template>
                                                <template slot-scope="scope">
                                                    <el-button type="text" @click="onRemoveSkuTabelRow(scope.row.id)">移除</el-button>
                                                </template>
                                            </el-table-column>

                                        </template>
                                    </ces-table>
                                </div>

                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>

                
            </el-form>

        </template>
        <template slot="footer">
            <el-row>
                <el-col :span="24" style="text-align:right;">  
                    <el-button @click="onClose">关闭</el-button>                     
                    <el-button v-if="this.formEditMode" type="primary" @click="onSave(false)">保存</el-button>
                    <el-button v-if="this.formEditMode" type="primary" @click="onSave(true)">保存&关闭</el-button>
                    
                </el-col>
            </el-row>
        </template>


        <!--选择商品-->
        <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag append-to-body>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:2000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!--选择SKU-->
        <el-dialog title="选择厂家SKU" :visible.sync="supplierSkuListVisible" width='88%'  v-dialogDrag append-to-body>
            <SupplierSkuList :ischoice="true" ref="supplierSkuList" style="z-index:2000;height:500px" 
            @close="supplierSkuListVisible=false;"
            @selected="onSkuSelected"
            />           
        </el-dialog>

    </my-container>
</template>
<script>  


    import cesTable from "@/components/Table/table.vue";
    import { formatTime, formatmoney, formatPercen, setStore, getStore, formatLinkProCode } from "@/utils/tools";
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from "@/components/my-confirm-button";
    import {
        GetDpSupplierByIdAsync, SaveDpSupplierAsync, GetDpYhGoodsRelAsync, DelSupplierGoodsAsync, SaveDpYhGoodsRelAsync
    } from '@/api/order/alllinkDaiPai';

    import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'

    import YhImgUpload from '@/components/upload/yh-img-upload.vue';

    import goodschoice from "@/views/base/goods/goods2.vue";
    import SupplierSkuList from "@/views/order/daipai/SupplierSkuList.vue";

    const skuTableCols = [
        { istrue: true, prop: 'supplierName', label: '厂家', width: '180', sortable: true},
        { istrue: true, prop: 'spGoodsName', label: '厂家商品', minwidth: '300', sortable: true},
        { istrue: true, prop: 'spSkuName', label: '厂家SKU', minwidth: '300', sortable: true },
        { istrue: true, prop: 'price', label: 'SKU单价', width: '100', sortable: true },
       
    ];


    export default {
        name: "YhGoodsRelationForm",
        components: { MyContainer, MyConfirmButton, cesTable, YhQuillEditor, YhImgUpload,goodschoice, SupplierSkuList },
        data() {
            return {              
                that: this,
                form: {
                   
                },
                skuTableCols: skuTableCols,
                total: 0,
                skuTableData: [],
                //summaryarry: {},
                pageLoading: false,
                curRow: null,
                formEditMode: true,//是否编辑模式              
                mode:1,
                goodschoiceVisible:false,
                supplierSkuListVisible:false,
            };
        },
        async mounted() {

        },
        computed: {
            tableHeight() {
                let rowsCount = 1;
                if (this.skuTableData && this.skuTableData.length > 0) {
                    rowsCount = this.skuTableData.length;                    
                }
                let rowsHeight = (rowsCount + 1) * 40 + 40;
                return rowsHeight > 360 ? 360 : rowsHeight;
            },         
            //代拍成本
            calcDpTotalSupPrice(){
                //form.dpTotalSupPrice
                if(this.skuTableData && this.skuTableData.length>0){
                    let sum=0;
                    this.skuTableData.forEach(sku=>{
                        sum+= sku.price *( isNaN(sku.supplierSkuCount)?1:Math.max(sku.supplierSkuCount,1));
                    });
                    this.form.dpTotalSupPrice=Number(sum.toFixed(2));
                    return this.form.dpTotalSupPrice;
                }else{
                    this.form.dpTotalSupPrice=0;
                    return 0;
                }
            }             
        },
        methods: {   
            onRemoveSkuTabelRow(rowId){
                let index=this.skuTableData.findIndex(x=>x.id==rowId);
                if(index>-1){
                    this.skuTableData.splice(index,1);
                }
            },
            async onSkuSelected(skus){
                let self=this;
                if(skus && skus.length>0){
                    let i=0;
                    skus.forEach(sku=>{
                        if(self.skuTableData.findIndex(x=>x.supplierSkuId==sku.id)==-1){
                            let relSku={...sku};
                            relSku.id=-(new Date().valueOf()+(i++));
                            relSku.supplierSkuId=sku.id;
                            
                            self.skuTableData.push(relSku);
                        }
                    })
                }                
            },
            async onQueren(){
                //选择半成品商品确定
                var choicelist = await this.$refs.goodschoice.getchoicelist();
                if (choicelist && choicelist.length > 0) {
                    //产品编码/名称逗号拼接                  
                    this.form.yhGoodsCode=choicelist[0].goodsCode;
                    this.form.yhGoodsName=choicelist[0].goodsName;
                    this.form.yhGoodsPrice=choicelist[0].costPrice;

                    if(choicelist.length>1)
                        this.$message.warning('只能选择一条记录，已默认选取选中记录的首行！');


                    this.goodschoiceVisible = false;
                }else{
                    this.$message.warning('请选择商品！');           
                }
            },
            onClose(){
                this.$emit('close');
            },  
            async onSave(isClose){
                if(await this.save()){
                    this.$emit('afterSave');
                    if(isClose)
                        this.$emit('close');
                }
            },
            async loadData({oid,mode}) {
               
                this.pageLoading = true;
                this.mode=mode;
                this.formEditMode = mode!=3;

                if (oid) {
                  
                     let rlt = await GetDpYhGoodsRelAsync( { yhGoodsCode:oid } );
                    if (rlt && rlt.success) {
                        this.form = rlt.data.yhGoods;
                        this.skuTableData = rlt.data.relations==null?[]:rlt.data.relations;                      
                         this.pageLoading = false;
                    }
                } else {
                    Object.keys(this.form).forEach(key => (this.form[key] = null));
                    this.form.enabled=true;
                    this.form.yhGoodsCode='';                   
                    this.skuTableData =[];

                    this.pageLoading = false;                    
                }
            },
            async save() {
                this.pageLoading = true;

                let saveData = { ...this.form };
                
                let errMsg='';
                if(this.skuTableData&& this.skuTableData.length>0){                
                  
                    this.skuTableData.forEach(element => {
                        if(!(element.supplierSkuCount && element.supplierSkuCount>0)){
                            errMsg='请填写有效的关联厂家sku数量！';                            
                        }
                    });
                    
                }else{
                    errMsg="请添加绑定的厂家SKU！"    ;               
                }

                if(errMsg){
                    this.$alert(errMsg);   
                    this.pageLoading = false; 
                    return false;
                }

                saveData.state=0;
                saveData.dpGoodsRelationList = [...this.skuTableData];
                saveData.dpGoodsRelationList.forEach(x=>{x.yhGoodsCode=saveData.yhGoodsCode;});

                let rlt = await SaveDpYhGoodsRelAsync(saveData);
                if (rlt && rlt.success) {
                    this.$message.success('保存成功！');    
                    
                    this.form = rlt.data.yhGoods;
                    this.skuTableData = rlt.data.relations==null?[]:rlt.data.relations;      
                }

                this.pageLoading = false;
              
                return (rlt && rlt.success);
            }
        },
    };
</script>
