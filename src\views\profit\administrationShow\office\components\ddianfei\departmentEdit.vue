<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="220px" class="demo-ruleForm">

                <el-form-item label="空调费用-耗电量（度）：" prop="airConditioningConsumption">
                    <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.airConditioningConsumption"
                        :placeholder="'空调费用-耗电量（度）'" class="publicCss" />
                </el-form-item>
                <el-form-item label="空调费用-费用（元）：" prop="airConditioningCost">
                    <inputNumberYh @input="computedone(2)" :fixed="0" v-model="ruleForm.airConditioningCost"
                        :placeholder="'空调费用-费用（元）'" class="publicCss" />
                </el-form-item>

                <el-form-item label="日常电费-耗电量（度）：" prop="dailyElectricityConsumption">
                    <inputNumberYh @input="computedone(1)" :fixed="0" v-model="ruleForm.dailyElectricityConsumption"
                        :placeholder="'日常电费-耗电量（度）'" class="publicCss" />
                </el-form-item>
                <el-form-item label="日常电费-费用（元）：" prop="dailyElectricityExpenses">
                    <inputNumberYh @input="computedone(2)" :fixed="0" v-model="ruleForm.dailyElectricityExpenses"
                        :placeholder="'日常电费-费用（元）'" class="publicCss" />
                </el-form-item>

                <el-form-item label="总计-耗电量（度）：" prop="powerConsumption">
                    {{ roundPower(ruleForm.powerConsumption, 0) }}
                    <!-- <inputNumberYh v-model="ruleForm.powerConsumption" :placeholder="'总计-耗电量（度）'" class="publicCss" /> -->
                </el-form-item>
                <el-form-item label="总计-费用（元）：" prop="totalCost">
                    {{ roundPower(ruleForm.totalCost, 0) }}
                    <!-- <inputNumberYh v-model="ruleForm.totalCost" :placeholder="'总计-费用（元）'" class="publicCss" /> -->
                </el-form-item>


                <el-form-item label="备注：" prop="remark">
                    <el-input style="width:80%;" v-model.trim="ruleForm.remark" :maxlength="50" placeholder="备注"
                        clearable />
                </el-form-item>

            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { electricityBillSubmit } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
import decimal from '@/utils/decimal'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        typeList: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            selectProfitrates: [],
            ruleForm: {
                label: '',
                name: ''
            },
            rules: {
                dailyElectricityExpenses: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                dailyElectricityConsumption: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                airConditioningCost: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                airConditioningConsumption: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },

    async mounted() {
        this.$nextTick(() => {
            this.$refs.refruleForm.clearValidate();
        });
        this.ruleForm = { ...this.editInfo };
    },
    methods: {
        roundPower(val, fixed = 2) {
          const num = parseFloat(val);
          if (isNaN(num)) return '';
          return num.toFixed(fixed); // 四舍五入并保留指定小数位数
        },
        computedone(type) {
            if (type == 1) {
                let a = this.ruleForm.dailyElectricityConsumption ? this.ruleForm.dailyElectricityConsumption : 0;
                let b = this.ruleForm.airConditioningConsumption ? this.ruleForm.airConditioningConsumption : 0;

                this.ruleForm.powerConsumption = decimal(a, b, 2, '+').toFixed(2);
                return decimal(a, b, 2, '+').toFixed(2);
            } else if (type == 2) {
                let a = this.ruleForm.airConditioningCost ? this.ruleForm.airConditioningCost : 0;
                let b = this.ruleForm.dailyElectricityExpenses ? this.ruleForm.dailyElectricityExpenses : 0;

                this.ruleForm.totalCost = decimal(a, b, 2, '+').toFixed(2);
                return decimal(a, b, 2, '+').toFixed(2);
            }

        },
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
                    const { data, success } = await electricityBillSubmit(this.ruleForm)
                    if (!success) {
                        return
                    }
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
            //   this.$confirm('是否保存?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }).then(async () => {
            //     this.$refs[formName].validate(async(valid) => {
            //       if (valid) {
            //         const { data, success } = await electricityBillSubmit(this.ruleForm)
            //         if(!success){
            //             return
            //         }
            //         await this.$emit("search");

            //       } else {
            //         console.log('error submit!!');
            //         return false;
            //       }
            //     });
            //   }).catch(() => {
            //   });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
