<template>
    <el-row :gutter="10">
        <el-col :span="6" style="width:20%" >
            <el-card>
                <dingdingDept ref="dingdingDept" :fatherMethod="deptOnSearch" />
            </el-card>
        </el-col>
        <el-col :span="18" style="width:80%">
            <container style="height:800px">
                <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
                    <template slot='extentbtn'>
                        <el-button-group>
                            <el-button style="padding: 0;margin: 0;">
                                <el-input v-model="filter.userName" maxlength="10" placeholder="姓名" clearable style="width:120px;" />
                            </el-button>
                            <el-button style="padding: 0;margin: 0;">
                                <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="入职时间" end-placeholder="入职时间" :clearable="true" :picker-options="pickerOptions">
                                </el-date-picker>
                            </el-button>
                            <el-button style="padding: 0;" >
                                <el-select filterable v-model="filter.employeeStatus" collapse-tags clearable placeholder="员工状态"
                                  style="width: 90px"> 
                                  <el-option label="正式" :value="3" />
                                  <el-option label="试用" :value="2" />
                                  <el-option label="离职" :value="0" />
                                </el-select>
                              </el-button>
                            <el-button type="primary" @click="deptOnSearch">查询</el-button>
                        </el-button-group>
                    </template>
                </ces-table>
                <template #footer>
                    <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
                </template>
            </container>
        </el-col>
        
    <el-dialog title="修改" :visible.sync="dialogVisibleSyj" width="20%" :show-close="false">
        <el-form >
            <el-row>
                <el-col :span="24">
                    <el-form-item label="钉钉号:" prop="">
                        <el-input :clearable="true" v-model="dingCode" maxlength="20"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button style="margin-left: 10px;" size="small" type="primary"    @click="onSubmitupload2">
                        提交 </my-confirm-button>
                <el-button style="margin-left: 10px;" @click="closedialogVisibleSyj">关闭</el-button>
            </span>
    </el-dialog>
    </el-row>

</template>
  <script>
  import MyConfirmButton from "@/components/my-confirm-button";
    import { getPageDeptUser,updateDDuserdingId } from '@/api/operatemanage/base/dingdingShow'
    import dingdingDept from '@/views/operatemanage/base/dingdingDept.vue'
    import container from '@/components/my-container/noheader'
    import cesTable from "@/components/Table/table.vue";
    const tableCols = [
        { istrue: true, prop: 'avatar', label: '头像', type: "image", width: '70' },
        { istrue: true, prop: 'ddUserId', label: 'ID', sortable: 'custom', width: '160' },
        { istrue: true, prop: 'userName', label: '姓名', sortable: 'custom', width: '170' },
        { istrue: true, prop: 'hired_date', label: '入职时间', type: 'editor', width: '200', sortable: 'custom' },
        { istrue: true, prop: 'employeeStatus', label: '员工状态', style: 'center', width: 'auto', sortable: 'custom', formatter: (row) => row.employeeStatus==2?"试用":row.employeeStatus==3?"正式":"离职" },
        { istrue: true, prop: 'dingCode', label: '钉钉号', style: 'center', type: 'click',  width: 'auto' , permission: "dingCodeEdit"},
        {
            istrue: true, type: "button", label: '操作', width: "120",fixed:"right",  permission: "dingCodeEdit",
            btnList: [
                { label: "修改钉钉号", handle: (that, row) => that.EditDingId(row) }
            ]
        }
    ];
    const tableHandles = []
    export default {
        name: 'dingdingDeptUser',
        components: { dingdingDept, container, cesTable,MyConfirmButton},
        data () {
            return {
                dingCode:null,
                ddUserId:0,
                dialogVisibleSyj :false,
                that: this,
                isLoad: false,
                list: [],
                tableCols: tableCols,
                tableHandles: tableHandles,
                pager: { OrderBy: " createtime ", IsAsc: false },
                total: 0,
                sels: [],
                selids: [],
                listLoading: false,
                pageLoading: false,
                filter: {
                    userName:null,
                    timerange:[],
                    employeeStatus:null,
                    entryStartTime:null,
                    entryEndTime:null

                }
            }
        },
        mounted () {
            //this.$refs.dingdingDept.selectDept= 597959144;
            //.this.deptOnSearch();
        },
        beforeUpdate () { },
        methods: {
            //保存编辑的信息
            async onSubmitupload2(){
               var res =  await updateDDuserdingId({ddUserId:this.ddUserId,dingCode:this.dingCode});
               if(res?.success){
                    this.$message({ message: '修改成功', type: "success" });
                    this.dialogVisibleSyj = false;
                   await this.getlist();
               }
            },
            //编辑钉钉号
            EditDingId(row){
                this.dingCode =null;
                this.ddUserId = null;
                this.dialogVisibleSyj = true;
                this.dingCode = row.dingCode;
                this.ddUserId = row.ddUserId;
            },
            closedialogVisibleSyj(){
                this.dingCode =null;
                this.ddUserId = null;
                this.dialogVisibleSyj = false;
            },
            deptOnSearch () {
                this.$refs.pager.setPage(1)
                this.getlist()
            },
            async getlist () {
                this.filter.entryStartTime = null;
                this.filter.entryEndTime = null;
            
            if (this.filter.timerange) {
                this.filter.entryStartTime = this.filter.timerange[0];
                this.filter.entryEndTime = this.filter.timerange[1];
            }
                var deptId = this.$refs.dingdingDept.selectDept;
                if (deptId == null || deptId == "") {
                    return;
                }
                this.filter.dept_id = deptId;
                var pager = this.$refs.pager.getPager()
                const params = { ...pager, ...this.pager, ... this.filter }
                this.listLoading = true
                this.list = [];
                const res = await getPageDeptUser(params)
                this.listLoading = false
                if (!res?.success) return
                this.total = res?.data?.total
                const data = res?.data?.list
                data.forEach(d => {
                    d._loading = false
                    d.hired_date=d.hired_date=="1970-01-01 08:00:00"?"":d.hired_date.slice(0,10);
                })
                this.list = data;
            },
            sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
                this.deptOnSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            selectchange: function (rows, row) {
                this.selids = [];
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
        }
    }
  </script>
  <style scoped lang="scss" >
    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: rgb(233, 233, 248);
        border-radius: 3px;
    }
    ::v-deep .el-table__body-wrapper {
        overflow: auto;
    }
</style>
  