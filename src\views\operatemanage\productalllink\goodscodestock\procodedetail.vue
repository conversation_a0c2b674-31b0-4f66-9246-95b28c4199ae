<template>
    <container v-loading="pageLoading">
        <!--列表-->
       <div style="height: 600px;">
        <cesTable :tableData='list' :tableCols='tableCols' :isSelectColumn="false"
            :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange' />
       </div>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
    </container>
</template>

<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { pageProCodeProfit } from "@/api/inventory/goodscodestock"
import { formatLinkProCode } from "@/utils/tools";

const tableCols = [
    { istrue: true, prop: 'proCode', label: '产品id', width: '120', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode)},
    { istrue: true, prop: 'operateSpecialUserName', label: '运营专员', width: '80', },
    { istrue: true, prop: 'shopName',sortable: 'custom', label: '店铺', width: '180', },
    { istrue: true, prop: 'payQty', label: '销量(付款)', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'sendQty', label: '销量(发货)', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'profit3Rate', label: '毛三利率', width: '100', formatter: (row) => (row.profit3Rate * 100).toFixed(2) + "%"},
    // { istrue: true, prop: 'warehouseStockPayRate', label: '公有可用数占比(付款)', width: '120', sortable: 'custom', formatter: (row) => (row.warehouseStockPayRate * 100).toFixed(2) + "%"},
    // { istrue: true, prop: 'warehouseStockSendRate', label: '公有可用数占比(发货)', width: '120', sortable: 'custom', formatter: (row) => (row.warehouseStockSendRate * 100).toFixed(2) + "%"},
    { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: 'auto', sortable: 'custom', formatter: (row) => (row.turnoverDays).toFixed(2)},
    { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: 'auto', sortable: 'custom', formatter: (row) => (row.turnoverDays3).toFixed(2)},
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];

export default {
    name: 'YunHanAdminProcodedetail',
    components: { container, MyConfirmButton, vxetablebase, cesTable },
    props: {
        filter: {}
    },

    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        
    },

    methods: {
        async clearData() {
            this.list = [];
            this.total = 0;
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            console.log('params',params)
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await pageProCodeProfit(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.list = data
        },
        async cellclick(row, column, cell, event) {

        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped>

</style>