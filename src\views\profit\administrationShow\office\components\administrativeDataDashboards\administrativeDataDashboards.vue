<template>
  <div style="display: flex; flex-direction: column; height: 80vh; overflow-y: auto; overflow-x: hidden;">
    <div>
      <el-switch v-model="ListInfo.type" active-text="仓储" inactive-text="办公室" active-value="仓储" inactive-value="办公室"
        @change="changetype" active-color="#90EE90" inactive-color="#87CEFA">
      </el-switch>
    </div>
    <div style="display: flex; flex-direction: row; align-items: center; margin-top: 10px;">
      <el-select v-model="ListInfo.region"
        :placeholder="ListInfo.type == '仓储' ? '仓储区域' : ListInfo.type == '办公室' ? '办公室区域' : '请选择'" class="publicCss"
        @change="onTypeMethod" clearable multiple collapse-tags>
        <el-option v-for="item in storageAreaList" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-if="ListInfo.type == '仓储'" v-model="ListInfo.dept" placeholder="部门" class="publicCss" clearable
        filterable>
        <el-option v-for="item in warehouseDepartmentList" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-model="ListInfo.menuType" placeholder="类型" class="publicCss" multiple collapse-tags clearable
        filterable>
        <el-option v-for="item in menuTypeList" :key="item" :label="item" :value="item" />
      </el-select>
      <el-date-picker v-model="timeRanges" type="monthrange" range-separator="至" start-placeholder="开始月份"
        end-placeholder="结束月份" style="width: 230px;" :value-format="'yyyy-MM'" @change="changeTime" :clearable="false">
      </el-date-picker>
      <el-button type="primary" @click="getList('search')">查询</el-button>
    </div>
    <div style="min-height: 300px; width: 100%; border: 1px solid #ccc; margin-top: 10px;">
      <newcharbus v-if="oneCharts" :toolbox="{
        show: true,
        orient: 'vertical',
        left: 'right',
        top: 'center',
        feature: {
          magicType: { show: true, type: ['line', 'bar', 'stack'] },
          saveAsImage: { show: true }
        }
      }" ref="sptsxformchTyright" :thisStyle="{
        width: '100%', height: '275px', 'box-sizing': 'border-box', 'line-height': '300px'
      }" :analysisData="oneCharts" :isField="true" :extraName="extraName">
      </newcharbus>
    </div>
    <div>
      <officeBulletinBoardMonth trendChartType="two" ref="refofficeBulletinBoardYear" :storageAreaList="storageAreaList"
        :menuTypeList="menuTypeList.concat(ListInfo.type == '办公室' ? ['采购预算表', '总违纪情况', '满意度评分'] : ListInfo.type == '仓储' ? ['采购预算表'] : [])"
        :departmentInfo="{ storageXA, storageNC, storageYW, officeNC, officeYW, officeWH, officeSZ, officeXPZX, situationType }" />
    </div>
    <div>
      <officeBulletinBoardYear trendChartType="one" ref="refofficeBulletinBoardMonth" :storageAreaList="storageAreaList"
        :menuTypeList="menuTypeList.concat(ListInfo.type == '办公室' ? ['采购预算表', '总违纪情况', '满意度评分'] : ListInfo.type == '仓储' ? ['采购预算表'] : [])"
        :departmentInfo="{ storageXA, storageNC, storageYW, officeNC, officeYW, officeWH, officeSZ, officeXPZX, situationType }" />
    </div>
  </div>
</template>

<script>
import officeBulletinBoardYear from "./components/officeBulletinBoardYear.vue";
import officeBulletinBoardMonth from "./components/officeBulletinBoardMonth.vue";
import jiduTime from "@/views/profit/sscManager/Aashouye/jiduTime.vue";
import newcharbus from "@/views/profit/sscManager/Aashouye/newcharbus.vue";
import dayjs from 'dayjs'
import { pickerOptions } from '@/utils/tools'
import {
  basisTrendChart, monthArchivesAnalysisListValue, getDeptList, selectAdministrativeData, getDataListByBlockNew
} from '@/api/people/peoplessc.js';
const officeList = ['南昌', '义乌', '武汉', '深圳', '选品中心']
const storageList = ['义乌仓', '南昌仓', '西安仓']

const officeNC = ['IT部', '采购部', '客服部', '区总经办', '人力资源部', '视觉部', '新媒体运营部', '运营部', '网络安全部']
const officeYW = ['IT部', '采购部', '客服部', '区总经办', '人力资源部', '视觉部', '新媒体运营部', '运营部', '网络安全部', '业务部', '综合处理部', '总助办']
const officeWH = ['IT部', '客服部', '人力资源部', '网络安全部', '运营部', '总经办']
const officeSZ = ['人力资源部', 'IT部', '跨境运营部', '采购部', '视觉设计部', '后勤部', '网络运维部']
const officeXPZX = ['市场拓展部', '中台支持部', '客户服务部', '供应链管理部']

const storageYW = ['诚信仓', '圆通仓', '邮政仓', '1688发货仓', '集包场地', '加工厂', '跨境仓', '昀晗云仓', '首力仓储部', '其他', '仓储行政', '发货部', '理货部', '入库部', '退件仓']
const storageNC = ['定制仓', '发货部', '收货部', '南昌云仓', '裁剪仓']
const storageXA = ['港务仓']

const officeType = ['OA台账', '员工餐', '电费', '工位', '绩效乐捐合计', '员工活动费用', '公关接待费用', '部门违纪数据']
const storageType = ['OA台账', '员工餐', '水电费', '仓储乐捐合计', '公关接待费用', '宿舍入住数据', '稽查情况数据', '工伤台账数据']

const situationType = ['次数', '金额']

export default {
  components: {
    newcharbus, officeBulletinBoardYear, jiduTime, officeBulletinBoardMonth
  },
  data() {
    return {
      officeList,
      storageList,
      officeNC,
      officeYW,
      officeWH,
      officeSZ,
      officeXPZX,
      storageYW,
      storageNC,
      storageXA,
      officeType,
      storageType,
      situationType,
      menuTypeListTwo: [],
      menuTypeListThree: [],
      warehouseDepartmentList: [],
      menuTypeList: [],
      storageAreaList: [],
      timeRanges: [dayjs().subtract(1, 'month').format('YYYY-MM'), dayjs().subtract(1, 'month').format('YYYY-MM')],
      ListInfo: {
        type: '办公室',
        startMonth: dayjs().subtract(1, 'month').format('YYYY-MM'),
        endMonth: dayjs().subtract(1, 'month').format('YYYY-MM'),
        menuType: [],
        region: ['南昌'],
        dept: '',
        costType: null,
        details: null
      },
      oneCharts: null,
      extraName: ''
    }
  },
  async mounted() {
    this.storageAreaList = this.officeList
    this.menuTypeList = this.officeType
    this.warehouseDepartmentList = this.storageYW || [];
    await new Promise(resolve => setTimeout(resolve, 3000));
    await this.getList();
  },
  methods: {
    async onRefreshMethod() {
      await this.getList();
      this.$nextTick(() => {
        this.$refs.refofficeBulletinBoardMonth.getList()
        this.$refs.refofficeBulletinBoardYear.getList()
      })
    },
    onTypeMethod(e) {
      this.ListInfo.dept = '';
      const warehouseMap = {
        '义乌仓': this.storageYW,
        '南昌仓': this.storageNC,
        '西安仓': this.storageXA,
      };
      let selected = Array.isArray(e) ? e : [e];
      let a = [];
      if (this.ListInfo.type === '仓储') {
        Object.entries(warehouseMap).forEach(([key, value]) => {
          if (selected.includes(key)) {
            a.push(...value);
          }
        });
      }
      this.warehouseDepartmentList = a;
    },
    async changeTime(e) {
      this.ListInfo.startMonth = e ? e[0] : null
      this.ListInfo.endMonth = e ? e[1] : null
    },
    changetype() {
      this.ListInfo.region = []
      this.ListInfo.menuType = []
      if (this.ListInfo.type == '仓储') {
        this.storageAreaList = this.storageList
        this.menuTypeList = this.storageType
        this.warehouseDepartmentList = this.storageYW
        this.ListInfo.dept = this.storageYW[0] || ''
        // this.ListInfo.menuType = [this.storageType[0]] || []
        this.ListInfo.region = [this.storageAreaList[0]] || []
      } else {
        this.storageAreaList = this.officeList
        this.menuTypeList = this.officeType
        this.warehouseDepartmentList = this.officeNC
        this.ListInfo.dept = this.officeNC[0] || ''
        // this.ListInfo.menuType = [this.officeType[0]] || []
        this.ListInfo.region = [this.storageAreaList[0]] || []
      }
      this.$nextTick(() => {
        this.$refs.refofficeBulletinBoardMonth.adjustmentMethod({ ...this.ListInfo })
        this.$refs.refofficeBulletinBoardYear.adjustmentMethod({ ...this.ListInfo })
      })
    },
    async getxiala(that, val) {
      let res = await monthArchivesAnalysisListValue({
        'fieldName': val
      })
      if (!res.success) {
        return
      }
      this[that] = res.data;
      this.ListInfo[val] = res.data[0];
      setTimeout(async () => {
        if (this.ListInfo.deptType) {
          let res = await this.getdeptName(this.ListInfo.deptType)
          this.ListInfo.deptName = res[0];
        }
      }, 1000)
    },
    async getdeptName(val) {
      this.ListInfo.deptName = "";
      let res = await getDeptList({ deptType: val });
      if (!res.success) {
        return;
      }
      this.sectionList2 = res.data;
      this.$forceUpdate();
      return res.data;
    },
    async getList() {
      await this.getoneCharts();
    },
    async getoneCharts() {
      this.oneCharts = null;
      const formatToStr = (val) => Array.isArray(val) ? val.join(',') : val;
      const { costType, details, menuType, region } = this.ListInfo;
      let params = {
        ...this.ListInfo,
        costType: menuType === 'OA台账' ? costType : formatToStr(costType),
        details: formatToStr(details),
        menuType: formatToStr(menuType),
        region: formatToStr(region),
      }
      let res = await selectAdministrativeData(params);
      if (!res.success) {
        return;
      }
      this.extraName = res.data.legend ? res.data.legend[0] : '差异';
      if (res.data?.series && res.data.series.length > 0) {
        res.data.series.map((item) => {
          item.label = {
            show: true
          }
        });
        res.data.xAxis = {
          type: 'category',
          data: res.data.xAxis
        };
        res.data.legend = {
          data: res.data.legend
        };
        res.data.yAxis = [{
          type: 'value'
        }];
        res.data.tooltip = {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: null
        };
      }
      this.oneCharts = res.data;
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
