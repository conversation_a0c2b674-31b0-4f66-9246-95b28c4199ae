<template>
    <container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 260px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="认领时间" end-placeholder="认领时间"
                        :picker-options="pickerOptions" :clearable="false"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-input v-model.trim="filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..."
                        style="width:200px;">
                        <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin-left: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-button>
            </el-button-group>
        </template>
        <!--列表-->
        <vxetablebase :id="'goodsCostPriceChgList202301031318001'" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading='listLoading' :border='true' :that="that" ref="vxetable" @sortchange='sortchange'  @cellClick='cellclick'
            @select='selectchange' :checkbox-config="{labelField: 'id', highlight: true, range: true}"
            @checkbox-range-end="callback"    :tablekey="'goodscodeallocation202304221441'"/>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 编码明细 -->
        <el-dialog :visible.sync="dialogVisible" title="详情信息" width="1200" v-dialogDrag>
            <procodedetail :filter="filterdetail" ref="procodedetail"></procodedetail>
        </el-dialog>
    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { getGoodsCodeAllocation } from "@/api/inventory/goodscodestock"
import { getDirectorList, getDirectorGroupList, } from '@/api/operatemanage/base/shop'
import { formatNoLink,formatSecondNewToHour } from "@/utils/tools";
import procodedetail from './procodedetail.vue'
var formatSecondToHour1 = function(time) {
    return formatSecondNewToHour(time);
}


const tableCols = [
    //{ istrue: true, label: '', width: '100', type:"checkbox", },
    { istrue: true, prop: 'pic', label: '图片', width: '60', type: 'images' },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: '80', },
    { istrue: true, prop: 'buyNoStatus', label: '采购单状态', width: '80', },
    // { istrue: true, prop: 'offTime', label: '截止时间', width: '120', },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '80', },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '80', align: 'center', type: 'html', formatter: (row) => formatNoLink(row.goodsCode) },
    { istrue: true, prop: 'personUsableQty', label: '个人可用数', width: '100', },
    { istrue: true, prop: 'inTransitNum', label: '采购在途数', width: '100', },
    { istrue: true, prop: 'avgInTransitTime', label: '平均在途时长', width: '80', formatter:(row)=>formatSecondToHour1(row.avgInTransitTime) },
    {
        istrue: true, label: `销量`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'salesDay', label: '昨天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay7', label: '7天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay15', label: '15天', width: '80', sortable: 'custom', },
            { istrue: true, prop: 'salesDay30', label: '30天', width: '80', sortable: 'custom', },
        ]
    },
    { istrue: true, prop: 'turnoverDays', label: '1天周转天数', width: '80', sortable: 'custom', formatter: (row) => (row.turnoverDays).toFixed(2) },
    { istrue: true, prop: 'turnoverDays3', label: '3天周转天数', width: '80', sortable: 'custom', formatter: (row) => (row.turnoverDays3).toFixed(2) },
    { istrue: true, prop: 'claimTime', label: '认领时间', width: '100', },
    { istrue: true, prop: 'claimCount', label: '认领数量', width: '80', sortable: 'custom', },
    { istrue: true, prop: 'claimStatus', label: '状态', width: 'auto', sortable: 'custom', formatter: (row) =>
                row.claimStatus == 0 ? "未认领" : row.claimStatus == 1 ? "认领中" : row.claimStatus == 2 ? "认领成功"
                : row.claimStatus == 3 ? "认领失败" : row.claimStatus == 4 ? "审批中" : ""},
];
const tableHandles = [
    //{ label: "认领数量", handle: (that) => that.onSetAllocation() },
];

const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminGoodscodeallocation',
    components: { container, MyConfirmButton, vxetablebase, procodedetail },
    //props: { filter: {} },

    data() {
        return {
            that: this,
            list: [],
            filter: {
                startDate: null,
                endDate: null,
                goodsCode: null,
                keywords: null,
                groupId: null,
                directorId: null,
                purchaseOrderStatus: null,
                timerange: [startTime, endTime],
            },
            keywordsTip: '支持搜索的内容：商品编码、系列编码、Erp单号',
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            selrows: [],
            directorList: [],
            directorGroupList: [],
            listLoading: false,
            pageLoading: false,
            dialogVisible: false,
            visiblepopoverdetail: false,
            filterdetail: {
                goodsCode: null,
                startDate: null,
                endDate: null
            },
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近半个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
        };
    },

    async mounted() {
        await this.onSearch();
        await this.getDirectorlist()
    },

    methods: {
        async getDirectorlist() {
            const res1 = await getDirectorList({})
            const res2 = await getDirectorGroupList({})

            this.directorList = res1.data
            this.directorGroupList = [{ key: '0', value: '未知' }].concat(res2.data || []);
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.timerange) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true
            var res = await getGoodsCodeAllocation(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;

            this.list = data
        },
        onSetAllocation() {
            if (!this.chooseTags || this.chooseTags.length == 0){
                this.$message.warning("请选择要认领的数据！");
                return false;
            }
            this.chooseTags.forEach(f => {
                if (f.claimStatus == 1) {
                    this.$message.warning("存在已认领成功的编码数据，禁止操作");
                    throw("禁止操作");
                }
                if (f.claimStatus == 3) {
                    this.$message.warning("存在认领中的编码数据，禁止操作");
                    throw("禁止操作");
                }
            })

            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/goodscodestock/setgoodscodeallocation.vue`,
                args: { ...this.chooseTags },
                title: "认领数量",
                width: '80%',
                height:'600px',
                callOk: () => {
                    self.onRefresh();
                }
            });
        },
        onRefresh() {
            this.onSearch()
        },
        async clickProfit(row) {
            this.dialogVisible = true;
            this.filterdetail.goodsCode = row.goodsCode
            this.filterdetail.startDate = null;
            this.filterdetail.endDate = null;
            if (this.filter.timerange) {
                this.filterdetail.startDate = this.filter.timerange[0];
                this.filterdetail.endDate = this.filter.timerange[1];
            }
            this.$nextTick(() => {
                this.$refs.procodedetail.clearData();
                this.$refs.procodedetail.onSearch();
            })
        },
        async cellclick({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
            if (column?.property == 'goodsCode')
                await this.clickProfit(row)
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        callback(val){
            this.tablelist = [];
            this.tablelist = val;
            var goodsCode = val.map((item)=>{
                return item;
            })
            this.chooseTags = goodsCode;
            console.log("goods返回值",this.chooseTags)
        },
        selectchange: function (rows, row) {
        //先把当前也的数据全部移除
        this.list.forEach(f => {
            let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
            if (index !== -1) {
            this.chooseTags.splice(index, 1);
            this.selrows.splice(index, 1);
            }
        });
        //把选中的添加
        rows.forEach(f => {
            let index = this.chooseTags.findIndex((v) => (v === f.goodsCode));
            if (index === -1) {
            this.chooseTags.push(f.goodsCode);
            this.selrows.push(f);
            console.log("选中数据",this.selrows);
            }
        });

        let _this = this;
            if(rows.length>0){
                var a = [];
                rows.forEach(element => {
                    let b = _this.list.indexOf(element);
                    a.push(b+1);
                });

                let d = _this.list.indexOf(row);

                var b = Math.min(...a)
                var c = Math.max(...a)

                a.push(d);
                if(d<b){
                    var b = _this.list.indexOf(row);
                    var c = Math.max(...a)
                }else if(d>c){
                    var b = Math.min(...a)-1
                    var c = Math.max(...a)
                }else{
                    var b = Math.min(...a)-1
                    var c = _this.list.indexOf(row)+1;
                }

                let neww = [b,c];
                _this.selids = neww;
            }
            console.log('选择的数据',this.selids)
        },
    },
};
</script>

<style lang="scss" scoped></style>
