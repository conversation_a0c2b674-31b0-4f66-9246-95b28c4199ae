<template>
  <MyContainer>
    <vxetablebase :id="'202408291521Dytablekey3'" tablekey="'tablekey20230512151103'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @summaryClick='onsummaryClick'
      :showheaderoverflow="false" class="assistantCss">
    </vxetablebase>

    <el-dialog title="趋势图" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag style="margin-top: -5vh">
      <div v-loading="trendChartLoading">
        <span>
          <template>
            <el-date-picker style="width: 410px" v-model="dialogMapVisible.filter.timerange" type="daterange"
              @change="similarityDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              :picker-options="fastTimePickerOptions"></el-date-picker>
          </template>
        </span>
        <span>
          <buschar ref="dialogMapVisibleBuschar" v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data">
          </buschar>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
      </span>
    </el-dialog>

    <vxe-modal title="明细" v-model="assistantJump" width="70%" :mask-closable="true" marginSize='-500'
      style="margin-top: -5vh">
      <div style="padding-bottom: 15px;">
        <financialReportStaticsByOneUser ref="reffinancialReportStaticsByDy" v-if="assistantJump" />
      </div>
    </vxe-modal>

    <vxe-modal title="小组店铺明细数据" v-model="dialogGroupDetailVisible" width="80%" :mask-closable="true" marginSize='-500'
      style="margin-top: -5vh">
      <div>
        <span>
          <groupShopDetail style="height: 600px;" ref="refgroupShopDetail" :filter="groupShopDetailList.filter"
            v-if="dialogGroupDetailVisible" />
        </span>
      </div>
    </vxe-modal>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { fastTimePickerOptions } from '@/utils/getCols'
import {
  SetPerformanceTarget, getPerformanceTarget, getPerformanceStaticticsByUser, getPerformanceStaticticsByGroup,
  getPerformanceStaticticsByUserMap, getPerformanceStaticticsByShop, getPerformanceStaticticsByShopMap, getPerformanceStaticticsByonSuperviseMap,
  exportPerformanceStaticticsByUserForDouYin, exportPerformanceStaticticsByGroupForDouYin, exportPerformanceStaticticsByShopForDouYin, exportPerformanceStaticticsByUser,
} from '@/api/bookkeeper/reportday'
import { getPerformanceStaticticsByUserAnalysis, getPerformanceStaticticsByGroupAnalysis } from '@/api/bookkeeper/pddstaticsreport'
import financialReportStaticsByOneUser from '@/views/bookkeeper/reportday/financialReportStaticsByOneUser'
import groupShopDetail from '@/views/bookkeeper/reportday/groupShopDetail'
import buschar from '@/components/Bus/buschar'
import { getPerformanceStaticticsByGroupMap } from '@/api/bookkeeper/reportday'
import dayjs from 'dayjs'
import decimal from '@/utils/decimal'
const tableCols = [
  { istrue: true, label: '小组头像', width: '80', type: 'ddAvatar', ddInfo: { type: 1, prop: 'groupId' } },
  { istrue: true, display: true, prop: 'groupName', label: '所在组', width: '70', formatter: (row) => row.groupName, type: 'ddTalk', ddInfo: { type: 1, prop: 'groupId', name: 'groupName' }, handle: (that, row) => that.showGroupDetail(row), color: (row) => row.issuccess == 1 ? '#409EFF' : 'red' },
  { istrue: true, sortable: 'custom', prop: 'xiFenPlatform', label: '细分平台', width: '100', permission: "SegmentationPlatform" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'orderCount', label: '订单量', width: '80', formatter: (row) => !row.orderCount ? "0" : row.orderCount },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'payAmont', label: '付款金额', width: '80', formatter: (row) => !row.payAmont ? "0" : row.payAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleAmont', label: '销售金额', width: '80', formatter: (row) => !row.saleAmont ? "0" : row.saleAmont },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBefore', exportField: 'refundAmont', label: '退款', width: '120', tipmesg: '发货前退款', formatter: (row) => !row.refundAmontBefore ? "0" : row.refundAmontBefore },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'refundAmontBeforeRate', exportField: 'refundAmontRateStr', label: '退款率', width: '120', tipmesg: '发货前退款率', formatter: (row) => !row.refundAmontBeforeRate ? "0%" : row.refundAmontBeforeRate.toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'saleCost', label: '成本', width: '80', formatter: (row) => !row.saleCost ? " " : row.saleCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit1After', label: '运营毛一(减退款)', width: '80', formatter: (row) => !row.yyProfit1After ? " " : row.yyProfit1After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1', label: '毛一利润(发生)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit11', label: '毛一利润(付款)', width: '120', formatter: (row) => !row.profit1 ? " " : row.profit1 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate', exportField: 'profit1RateStr', label: '毛一利润率(发生)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit1Rate1', label: '毛一利润率(付款)', width: '80', formatter: (row) => !row.profit1Rate ? " " : (row.profit1Rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv', label: '广告费', width: '100', formatter: (row) => row.alladv == 0 ? " " : row.alladv },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'alladv_rate', exportField: 'alladv_rateStr', label: '广告占比', width: '80', formatter: (row) => (row.alladv_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit2After', label: '运营毛二(减退款)', width: '80', formatter: (row) => !row.yyProfit2After ? " " : row.yyProfit2After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3After', label: '运营毛三(减退款)', width: '80', formatter: (row) => !row.yyProfit3After ? " " : row.yyProfit3After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3', label: '毛三利润(发生)', width: '80', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit31', label: '毛三利润(付款)', width: '80', type: 'custom', sortable: 'custom', formatter: (row) => row.profit3 == 0 ? " " : row.profit3 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit3AfterRate', label: '毛3率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit3AfterRate ? " " : (row.yyProfit3AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate', exportField: 'profit3_rateStr', label: '毛三利润率(发生)', width: '120', formatter: (row) => (row.profit3_rate).toString() + "%" },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit3_rate1', label: '毛三利润率(付款)', width: '90', formatter: (row) => (row.profit3_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4After', label: '运营毛四(减退款)', width: '80', formatter: (row) => !row.yyProfit4After ? " " : row.yyProfit4After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5After', label: '运营毛五(减退款)', width: '80', formatter: (row) => !row.yyProfit5After ? " " : row.yyProfit5After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6After', label: '运营毛六(减退款)', permission: "profit6SixtyCents", width: '80', formatter: (row) => !row.yyProfit6After ? " " : row.yyProfit6After },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33', label: '毛四利润(发生)', sortable: 'custom', width: '80', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit331', label: '毛四利润(付款)', sortable: 'custom', width: '80', formatter: (row) => !row.profit33 ? " " : row.profit33 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5', label: '毛五利润(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5 ? " " : row.profit5 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6', label: '毛六利润(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6 ? " " : row.profit6 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit4AfterRate', label: '毛4率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit4AfterRate ? " " : (row.yyProfit4AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit5AfterRate', label: '毛5率(运营减退)', width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit5AfterRate ? " " : (row.yyProfit5AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'yyProfit6AfterRate', label: '毛6率(运营减退)', permission: "profit6SixtyCents", width: '90', sortable: 'custom', formatter: (row) => !row.yyProfit6AfterRate ? " " : (row.yyProfit6AfterRate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate', exportField: 'profit33RateStr', label: '毛四利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit33Rate1', label: '毛四利润率(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.profit33Rate ? " " : row.profit33Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit5Rate', exportField: 'profit5Rate', label: '毛五利润率(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.profit5Rate ? " " : row.profit5Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit6Rate', exportField: 'profit6Rate', label: '毛六利润率(发生)', permission: "profit6SixtyCents", sortable: 'custom', width: '90', formatter: (row) => !row.profit6Rate ? " " : row.profit6Rate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost', label: '出仓成本(发生)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCost1', label: '出仓成本(付款)', sortable: 'custom', width: '90', formatter: (row) => !row.exitCost ? " " : row.exitCost },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'warehouseSalary', label: '仓库薪资', sortable: 'custom', width: '90', formatter: (row) => !row.warehouseSalary ? 0 : row.warehouseSalary },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'exitCostRate', exportField: 'exitCostRateStr', label: '出仓成本占比', sortable: 'custom', width: '100', formatter: (row) => !row.exitCostRate ? " " : row.exitCostRate + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4', label: '净利润(发生)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit41', label: '净利润(付款)', width: '80', formatter: (row) => row.profit4 == 0 ? " " : row.profit4 },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate', exportField: 'profit4_rateStr', label: '净利率(发生)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  // { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'profit4_rate1', label: '净利率(付款)', width: '80', formatter: (row) => (row.profit4_rate).toString() + "%" },
  { istrue: true, sortable: 'custom', summaryEvent: true, prop: 'negativeExitProfitIdDetail', label: '出仓负利润', sortable: 'custom', width: '90', formatter: (row) => !row.negativeExitProfitIdDetail ? " " : row.negativeExitProfitIdDetail },
  { istrue: true, display: true, prop: 'progress', label: '完成比例', type: "progress" },
  { istrue: true, summaryEvent: true, display: true, prop: 'deductAmount', label: '违规扣款金额', width: '100', formatter: (row) => !row.deductAmount ? " " : row.deductAmount },
  { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", width: 70, formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.showchart(row) },
  { istrue: true, display: true, label: '操作', width: '70', type: 'click', permission: "OperatingPerformanceObjectivePermissionsDy", handle: (that, row) => that.onConfigurationOperation(row), formatter: (row) => '配置' },
]
const cstTargetName_ZlProfit3 = "抖音运营助理毛三目标";
const cstTargetName_ZlProfit4 = "抖音运营助理净利目标";
const cstTargetName_ZyProfit3 = "抖音运营专员毛三目标";
const cstTargetName_ZyProfit4 = "抖音运营专员净利目标";
export default {
  name: "operationsGroupsDy",
  components: {
    MyContainer, vxetablebase, financialReportStaticsByOneUser, groupShopDetail, buschar
  },
  props: {
    ListInfo: {
      type: Object,
      default: () => { }
    },
    yyYeJiDto: {
      type: Array,
      default: () => []
    },
    targetType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      trendChartLoading: false,
      filter: {
        currentPage: 1,
        pageSize: 1000,
        orderBy: 'profit3',
        isAsc: false,
        platform: 6,
        refundType: 1,//发生维度
      },
      dialogMapVisible: {
        visible: false,
        title: "",
        data: [],
        filter: {
          timerange: []
        },
        params: {},
        type: 0//1趋势图汇总专员、助理 2汇总运营组、店铺  3助理 4专员 5运营组 6店铺
      },
      groupShopDetailList: {
        filter: {
          timerange: [],
          groupId: '',
          platform: 6,
          UserType: '',
          shopCode: '',
          company: '',
          timerange2: [],
          Profit3Lose: null,
          Profit33Lose: null,
          startTime3: null,
          endTime3: null,
          timerangeOnTime: [],
        }
      },
      dialogGroupDetailVisible: false,
      assistantJump: false,
      that: this,
      pageType: '运营组',
      userType: 3,//1助理 2专员 21运营代教 3运营组
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      fastTimePickerOptions,
    }
  },
  async mounted() {
    // this.targetData = (await getPerformanceTarget({ targetName: cstTargetName_ZyProfit3 })).data;
    // this.targetData1 = (await getPerformanceTarget({ targetName: cstTargetName_ZyProfit4 })).data;
    window.setshowprogress = function (status) {
      this.showprogress = status;
    }
  },
  methods: {
    onConfigurationOperation(row) {
      this.$emit('onConfigureTarget', { id: row.groupId, targetName: '抖音运营组毛六目标' })
    },
    // 导出
    async onSublevelExport() {
      this.$emit('onExportProps', this.tableCols, this.userType)
    },
    async showchart(row) {
      let date1 = new Date(this.filter.timerange[1]);
      date1.setMonth(date1.getMonth() - 1);
      date1.setDate(date1.getDate() - 1);
      let date2 = new Date(this.filter.timerange[1]);
      let data3 = new Date(this.filter.timerange[0]).getTime();
      if (date1.getTime() > data3) {
        date1 = new Date(this.filter.timerange[0]);
      }
      let startTime3 = this.filter.startTime3;
      let endTime3 = this.filter.endTime3;
      this.dialogMapVisible.filter.timerange = [date1, date2];
      var params = {
        ...this.filter,
        groupId: row.groupId,
        startTime: this.dialogMapVisible.filter.timerange[0],
        endTime: this.dialogMapVisible.filter.timerange[1],
        groupId: row.groupId, startTime3, endTime3,
        platform: this.filter.platform, Company: this.filter.company, refundType: this.filter.refundType
      }
      let that = this;

      that.dialogMapVisible.type = 5;
      that.dialogMapVisible.params = params;
      that.loading = true;
      const res = await getPerformanceStaticticsByGroupMap(params).then(res => {
        that.dialogMapVisible.visible = true;
        that.dialogMapVisible.data = res.data
        that.dialogMapVisible.title = res.data.legend[0]
      })
      that.loading = false;
      this.dialogMapVisible.visible = true
    },
    async showGroupDetail(vals) {
      this.dialogGroupDetailVisible = true;
      Object.assign(this.groupShopDetailList.filter, {
        timerange: this.filter.timerange,
        platform: this.filter.platform,
        groupId: vals.groupId,
        shopCode: null,
        UserType: this.userType,
        company: this.filter.company,
        refundType: this.filter.refundType,
        Profit3Lose: this.filter.Profit3Lose,
        Profit33Lose: this.filter.Profit33Lose,
        timerangeOnTime: this.timerangeOnTimeDilog,
        startTime3: this.filter.startTime3,
        endTime3: this.filter.endTime3
      });
      this.$nextTick(async () => {
        await this.$refs.refgroupShopDetail.onSearch();
      });
    },
    async canclick(row, column, cell) {
      this.assistantJump = true;
      let that = this;
      this.$nextTick(() => {
        that.$refs.reffinancialReportStaticsByDy.setFinancialFilterTimeInner(this.filter.startTime, this.filter.endTime, row.userId, row.operateSpecialUserId, row.groupId, this.filter.platform, this.filter.refundType, this.filter.startTime3, this.filter.endTime3, this.filter.Profit3Lose, this.filter.Profit33Lose, this.userType);
      });
    },
    async onsummaryClick(property) {
      this.loading = true;
      let date1 = new Date(this.filter.timerange[1]);
      date1.setMonth(date1.getMonth() - 1);
      date1.setDate(date1.getDate() - 1);
      let date2 = new Date(this.filter.timerange[1]);

      let data3 = new Date(this.filter.timerange[0]).getTime();
      if (date1.getTime() > data3) {
        date1 = new Date(this.filter.timerange[0]);
      }
      const params = { ...this.filter, column: this.mapColumnProp(property) }
      this.dialogMapVisible.filter.timerange = [date1, date2];
      if (this.dialogMapVisible.filter.timerange) {
        params.startTime = this.dialogMapVisible.filter.timerange[0];
        params.endTime = this.dialogMapVisible.filter.timerange[1];
      }
      params.UserType = this.userType;
      try {
        let res;
        this.trendChartLoading = true;
        params.IsTxShopSearch = this.pageType == '店铺';
        res = await getPerformanceStaticticsByGroupAnalysis(params);
        this.dialogMapVisible.type = 2;
        this.trendChartLoading = false;
        if (res) {
          this.dialogMapVisible.params = params;
          this.dialogMapVisible.visible = true;
          this.dialogMapVisible.data = res.data;
          this.dialogMapVisible.title = res.data.legend[0];
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        this.loading = false;
      }
    },
    mapColumnProp(prop) {
      const propMapping = {
        'profit4_rate1': 'profit4_rate',
        'profit41': 'profit4',
        'profit11': 'profit1',
        'profit1Rate1': 'profit1Rate',
        'profit31': 'profit3',
        'profit3_rate1': 'profit3_rate',
        'profit331': 'profit33',
        'profit33Rate1': 'profit33Rate',
        'exitCost1': 'exitCost',
      };
      return propMapping[prop] || prop; // 返回映射的属性，或者原属性
    },
    async getList(type) {
      this.loading = true
      //refundType为1是发生维度
      this.filter = Object.assign(this.ListInfo, this.filter, { platform: 6, refundType: 1 });
      const { data, success } = await getPerformanceStaticticsByUser({ ...this.filter, userType: this.userType, targetType: this.targetType })
      if (success) {
        // let targetData = this.yyYeJiDto.find(item => item.targetName == "抖音运营组毛六目标").targetData
        let days = dayjs(this.filter.endTime).diff(this.filter.startTime, 'day')
        for (var i in data.list) {
          let a = decimal(decimal(data.list[i].profit6, days === 0 ? 1 : days, 4, '/'), 30, 4, '*')
          // let b = decimal(decimal(data.list[i].profit6, targetData ? targetData : 0, 4, '/'), 100, 4, '*')
          data.list[i].progress = data.list[i].progress || 0;
        }
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        //增加%号
        const rateKeys = [
          'refundAmontBeforeRate_sum',
          'profit1Rate_sum',
          'alladv_rate_sum',
          'profit3_rate_sum',
          'profit4_rate_sum'
        ];
        rateKeys.forEach(key => {
          if (this.summaryarry[key] !== undefined) {
            this.summaryarry[key] = this.summaryarry[key] + "%";
          }
        });
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async similarityDateChange() {
      if (this.dialogMapVisible.filter.timerange) {
        this.dialogMapVisible.filter.startTime = this.dialogMapVisible.filter.timerange[0];
        this.dialogMapVisible.filter.endTime = this.dialogMapVisible.filter.timerange[1];
      }
      let params = { ...this.dialogMapVisible.params, ...this.dialogMapVisible.filter }
      let that = this;
      this.trendChartLoading = true;
      if (this.dialogMapVisible.type == 2) {
        await getPerformanceStaticticsByGroupAnalysis(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        });
      } else if (this.dialogMapVisible.type == 5) {
        await getPerformanceStaticticsByGroupMap(params).then(res => {
          that.dialogMapVisible.data = res.data
          that.dialogMapVisible.title = res.data.legend[0]
        })
      }
      this.trendChartLoading = false;
      await this.$refs.dialogMapVisibleBuschar.initcharts()
    },
  }
}
</script>

<style scoped lang="scss">
.assistantCss ::v-deep .mytable-scrollbar20221212 ::-webkit-scrollbar {
  width: 12px !important;
  height: 14px !important;
}
</style>
