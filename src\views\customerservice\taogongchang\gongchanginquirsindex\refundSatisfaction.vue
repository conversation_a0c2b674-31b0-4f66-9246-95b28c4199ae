<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="queryParams" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='handleSortChange'
      :tableData='data' :isSelection='false' :tableCols='tableCols'
      :loading="loading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;border:none">
            <el-date-picker
              v-model="queryParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            />
          </el-button>
          <el-button style="padding: 0;margin: 0;border:none">
            <el-select v-model="queryParams.shopName" placeholder="请选择店铺" filterable clearable>
              <el-option
                v-for="item in shopOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-button>
          <el-button style="padding: 0;margin: 0;border:none">
            <el-select v-model="queryParams.groupName" placeholder="请选择分组" filterable clearable>
              <el-option
                v-for="item in groupOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="warning" @click="handleExport" style="margin-left: 10px;">导出</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
<!--    <template #footer>
      <my-pagination ref="pager"
                     :total="page.total"
                     :page-size="page.pageSize"
                     :current-page="page.currentPage"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange" />
    </template>-->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        :page-size="page.pageSize"
        :current-page="page.currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 编辑分组窗口 -->
    <el-dialog
      title="编辑分组"
      :visible.sync="editDialogVisible"
      width="40%"
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <el-form :model="editForm" :rules="editFormRules" ref="editForm" label-width="100px">
        <el-form-item label="分组" prop="groupName">
          <el-select v-model="editForm.groupName" placeholder="请选择分组" filterable style="width: 80%">
            <el-option
              v-for="item in groupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEditForm">确定</el-button>
      </span>
    </el-dialog>

  </my-container>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  getRefundSatisfactionPage,
  exportRefundSatisfaction,
  getRefundSatisfactionShops,
  getRefundSatisfactionGroups,
  updateRefundSatisfactionGroup
} from "@/api/bladegateway/worcestorejk";
import {
  GetGongChangShops,
  GetGongChangGroup,
  UpdateGongChangGroup,
  AddGongChangGroup
} from "@/api/customerservice/gongchanginquirs";
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";

const tableCols = [
  { istrue: true, prop: 'date', label: '日期', width: 'auto', sortable: 'custom', formatter: (row) => dayjs(row.date).format("YYYY-MM-DD") },
  { istrue: true, prop: 'shopName', label: '店铺', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'groupName', label: '分组', width: 'auto', sortable: 'custom' },
  { istrue: true, prop: 'avgRefundTime', label: '平均退款时长', width: 'auto', sortable: 'custom', formatter: (row) => row.avgRefundTime + ' 小时' },
  { istrue: true, prop: 'refundFinishRate', label: '退款完结率', width: 'auto', sortable: 'custom', formatter: (row) => row.refundFinishRate + '%' },
  { istrue: true, prop: 'refundRejectRate', label: '退款拒绝率', width: 'auto', sortable: 'custom', formatter: (row) => row.refundRejectRate + '%' },
  { istrue: true, prop: 'refundSatisfaction', label: '退款满意度', width: 'auto', sortable: 'custom', formatter: (row) => row.refundSatisfaction + '%' },
  { istrue: true, display: true, label: '操作', width: '120', type: 'click', style: "color:#409EFF;cursor:pointer;", formatter: (row) => '编辑', handle: (that, row) => that.handleEdit(row) },
];

export default {
  name: "RefundSatisfaction",
  components: { MyContainer, cesTable },
  data() {
    return {
      that: this,
      tableCols: tableCols,
      dayjs,
      loading: false,
      pageLoading: false,
      importVisible: false,
      data: [],
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 50,
      },
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      queryParams: {
        dateRange: [],
        shopName: "",
        groupName: "",
        orderBy: "", // 新增排序字段
        isAsc: "" // 新增排序方向
      },
      shopOptions: [], // 动态生成
      groupOptions: [], // 动态生成
      // 编辑分组相关
      editDialogVisible: false,
      editForm: {
        id: "",
        groupName: ""
      },
      editFormRules: {
        groupName: [
          { required: true, message: "请选择分组", trigger: "change" }
        ]
      },
      currentRow: null
    };
  },
  created() {
    // 设置默认查询时间范围（近30天）
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
    this.queryParams.dateRange = [
      start.toISOString().slice(0, 10),
      end.toISOString().slice(0, 10)
    ];

    // 获取店铺和分组信息
    this.getShopOptions();
    this.getGroupOptions();
    // 加载列表数据
    this.onLoad(this.page);
  },
  methods: {
    async onLoad(page, params = {}) {
      this.loading = true;
      try {
        // 构建查询参数
        const queryParams = { ...this.queryParams };

        // 处理日期范围参数
        if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
          queryParams.startDate = this.queryParams.dateRange[0];
          queryParams.endDate = this.queryParams.dateRange[1];
        }
        // 删除dateRange参数，因为后端不需要
        delete queryParams.dateRange;

        const response = await getRefundSatisfactionPage({
          ...queryParams,
          currentPage: page.currentPage,
          pageSize: page.pageSize,
          ...params
        });

        if (response.code === 200) {
          this.data = response.data.list || [];
          this.page.total = response.data.total || 0;
        } else {
          this.$message.error(response.msg || "加载数据失败");
        }
      } catch (error) {
        console.error("加载数据失败:", error);
        this.$message.error("加载数据失败");
      }
      this.loading = false;
    },
    // 获取店铺选项
    async getShopOptions() {
      try {
        // 首先尝试使用专用API获取退款满意度相关的店铺
        const response = await getRefundSatisfactionShops();
        if (response && response.code === 200 && response.data && response.data.length > 0) {
          this.shopOptions = response.data.map(item => ({
            value: item,
            label: item
          }));
        } else {
          // 如果专用API没有返回数据，则使用通用API作为备选
          const fallbackResponse = await GetGongChangShops();
          if (fallbackResponse.success && fallbackResponse.data) {
            // 只取淘工厂的店铺
            const taobaoShops = fallbackResponse.data.filter(item => item.platform === 8);
            this.shopOptions = taobaoShops.map(item => ({
              value: item.shopName || item.name,
              label: item.shopName || item.name
            }));
          }
        }
      } catch (error) {
        console.error("获取店铺选项失败:", error);
        this.$message.error("获取店铺选项失败");
      }
    },
    // 获取分组选项
    async getGroupOptions() {
      try {
        // 获取售前和售后的分组数据
        const promises = [
          GetGongChangGroup({groupType: 0}), // 售前
          GetGongChangGroup({groupType: 1})  // 售后
        ];

        const [preSalesResponse, afterSalesResponse] = await Promise.all(promises);

        // 处理售前分组
        let groupList = [];

        if (preSalesResponse?.success && preSalesResponse?.data) {
          groupList = groupList.concat(preSalesResponse.data);
        }

        if (afterSalesResponse?.success && afterSalesResponse?.data) {
          groupList = groupList.concat(afterSalesResponse.data);
        }

        // 如果两个接口都没有返回数据，尝试使用退款满意度接口
        if (groupList.length === 0) {
          const fallbackResponse = await getRefundSatisfactionGroups();
          if (fallbackResponse && fallbackResponse.code === 200 && fallbackResponse.data) {
            groupList = fallbackResponse.data;
          }
        }

        // 去重并转换为下拉选项格式
        const uniqueGroups = [...new Set(groupList)];
        this.groupOptions = uniqueGroups.map(item => ({
          value: item,
          label: item
        }));
      } catch (error) {
        console.error("获取分组选项失败:", error);
        this.$message.error("获取分组选项失败");
      }
    },
    handleSearch() {
      if (this.$refs.pager) {
        this.$refs.pager.setPage(1);
      } else {
        this.page.currentPage = 1;
      }
      this.onLoad(this.page);
    },
    handleReset() {
      // 保留日期范围的默认值
      const dateRange = [...this.queryParams.dateRange];
      this.queryParams = {
        dateRange: dateRange,
        shopName: "",
        groupName: "",
        orderBy: "",
        isAsc: ""
      };
      this.handleSearch();
    },
    async handleExport() {
      try {
        this.loading = true;
        // 构建要导出的查询参数
        const exportParams = {
          ...this.queryParams
        };

        // 处理日期范围参数
        if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
          exportParams.startDate = this.queryParams.dateRange[0];
          exportParams.endDate = this.queryParams.dateRange[1];
        }
        // 删除dateRange参数，因为后端不需要
        delete exportParams.dateRange;

        const response = await exportRefundSatisfaction(exportParams);
        this.loading = false;

        // 处理文件下载
        if (!response?.data) return;
        const aLink = document.createElement('a');
        let blob = new Blob([response.data], { type: 'application/vnd.ms-excel' });
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute('download', '淘工厂退款满意度_' + new Date().toLocaleString() + '.xlsx');
        aLink.click();

        this.$message.success("导出成功");
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败，请检查接口配置");
        this.loading = false;
      }
    },
    handleSortChange(column) {
      if (!column.order) {
        this.queryParams.orderBy = '';
        this.queryParams.isAsc = '';
      } else {
        this.queryParams.orderBy = column.prop;
        this.queryParams.isAsc = column.order.indexOf("descending") == -1 ? true : false;
      }
      this.onLoad(this.page);
    },
    handleSizeChange(size) {
      this.page.pageSize = size;
      this.onLoad(this.page);
    },
    handleCurrentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    handleEdit(row) {
      this.currentRow = row;
      // 先获取最新的分组数据，确保下拉选项中有当前组以及所有可选组
      this.getGroupOptions().then(() => {
        this.editForm = {
          id: row.id,
          groupName: row.groupName
        };
        this.editDialogVisible = true;
      }).catch(() => {
        this.$message.error('获取分组数据失败，请重试');
      });
    },
    // 提交编辑表单
    submitEditForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          try {
            // 显示加载状态
            this.loading = true;

            // 构建参数
            const params = {
              id: this.editForm.id,
              groupName: this.editForm.groupName
            };

            // 调用后端API更新分组
            const response = await updateRefundSatisfactionGroup(params);

            if (response && (response.code === 200 || response.success)) {
              this.$message.success('修改分组成功');
              this.editDialogVisible = false;
              // 刷新数据
              this.onLoad(this.page);
            } else {
              this.$message.error(response?.msg || '修改分组失败');
            }
          } catch (error) {
            console.error("修改分组失败:", error);
            this.$message.error("修改分组失败: " + (error.message || '未知错误'));
          } finally {
            this.loading = false;
          }
        }
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
.pagination-container {
  background: #fff;
  padding: 10px 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  text-align: right;
  margin-top: 10px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  border-top: 1px solid #ebeef5;
}
</style>

