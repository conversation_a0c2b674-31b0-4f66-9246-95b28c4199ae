<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='dahuixionglist' @select='selectchange' :isSelection='false' :showsummary='true'
      :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div>
            <el-table :data="props.row.detaildata" style="width: 100%">
              <el-table-column v-for="(col) in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <template slot='extentbtn'>
        <el-button-group>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.shopCode" placeholder="店铺编号" maxlength="50" clearable
              style="width: 170px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.shopName" placeholder="快递费店铺" maxlength="50" clearable
              style="width: 170px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.shopName1" placeholder="月报计算店铺" maxlength="50" clearable
              style="width: 170px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0;">
            <el-input v-model.trim="Filter.shopCode1" placeholder="月报计算店铺编号" maxlength="50" clearable
              style="width: 170px;" />
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onImportSyj">导入</el-button>
          <el-button type="primary" @click="onAddMethod">新增</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getJdExpressList" />
    </template>

    <el-dialog title="导入店铺配置" :visible.sync="dialogVisibleSyj" width="30%" v-dialogDrag>
      <span>
        <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :http-request="uploadFile2" :on-success="uploadSuccess2">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <my-confirm-button style="margin-left: 10px;" size="small" type="success"
            @click="onSubmitupload2">上传</my-confirm-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleSyj = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="新增店铺" :visible.sync="newEditVisible" width="35%" v-dialogDrag>
      <div style="height: 170px;padding: 30px 5px 0 5px;">
        <el-form :model="ruleForm" :rules="editrules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="店铺编号" :label-width="'125px'" prop="shopCode">
                <el-input v-model.trim="ruleForm.shopCode" placeholder="请输入店铺编号" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
              <el-form-item label="快递费店铺" :label-width="'125px'" prop="shopName">
                <el-input v-model.trim="ruleForm.shopName" placeholder="请输入快递费店铺" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
              <el-form-item label="月报计算店铺" :label-width="'125px'" prop="shopName1">
                <el-input v-model.trim="ruleForm.shopName1" placeholder="请输入月报计算店铺" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
              <el-form-item label="月报计算店铺编号" :label-width="'125px'" prop="shopCode1">
                <el-input v-model.trim="ruleForm.shopCode1" placeholder="请输入月报计算店铺编号" maxlength="50" clearable
                  class="editCss" />
              </el-form-item>
           
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced">确 定</el-button>
      </span>
    </el-dialog>
  </my-container>
</template>
<script>

import { importMonthlyBillShopConfigurationsync as importTuoguanYGAsync, getMonthlyBillShopConfigurationList as getTuoguanYGList, deleteMonthlyBillShopConfigurationBatchAsync as deleteBatchAsync,addOrEidtMonthlyBillShopConfiguration } from "@/api/express/express";
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode', label: '店铺编号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName', label: '快递费店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopName1', label: '月报计算店铺', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'shopCode1', label: '月报计算店铺编号', },
  { istrue: true, type: "button", label: '操作', width: "auto", btnList: [{ label: "删除批次", handle: (that, row) => that.deleteBatch(row) },{ label: "编辑", handle: (that, row) => that.onEdit(row) }] }
];

export default {
  name: "shopConfiguration",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      Filter: {
        shopName: '',
        shopCode: '',
        shopName1: '',
        shopCode1: '',
      },
      dahuixionglist: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      newEditVisible:false,
      ruleForm: {
        batchNumber:null,
        shopCode: null,
        shopName: null,
        shopName1: null,
        shopCode1: null,
        id: null,
      },
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    async deleteBatch(row) {
      var that = this;
      this.$confirm("此操作将删除此批次导入费用数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteBatchAsync({ batchNumber: row.batchNumber })
          that.$message({ message: '已删除', type: "success" });
          that.onRefresh()
        });
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onImportSyj() {
      this.dialogVisibleSyj = true
    },
    async uploadFile2(item) {
      const form = new FormData();
      form.append("upfile", item.file);
      const res = importTuoguanYGAsync(form);
      this.$message({ message: '上传成功,正在导入中...', type: "success" });
      this.dialogVisibleSyj = false;
    },
    async uploadSuccess2(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
    },
    async onSubmitupload2() {
      this.$refs.upload2.submit()
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getJdExpressList();
    },
    async getJdExpressList() {
      if (/^\d+$/.test(this.Filter.BatchNumber) == false && this.Filter.BatchNumber != null && this.Filter.BatchNumber != "") {
        this.$message.error('请输入正确的批次号！！！！！');
        this.Filter.BatchNumber = null;
        return;
      }
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      this.listLoading = true;
      const res = await getTuoguanYGList(params);
      this.listLoading = false;
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onSingleSave(param);
    }, 1000),
    onSingleSave() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const { success } = await addOrEidtMonthlyBillShopConfiguration(this.ruleForm)
          if (success) {
            this.$message.success('操作成功')
            this.newEditVisible = false
            this.getJdExpressList()
          } else {
            this.$message.error('操作失败')
          }
        }
      })
    },
    onCleardataMethod() {
      this.ruleForm = {
        batchNumber:null,
        shopCode: null,
        shopName: null,
        shopName1: null,
        shopCode1: null,
        id: null,
      }
    },
    onAddMethod() {
      this.onCleardataMethod()
      this.ruleTitle = '新增'
      this.newEditVisible = true
    },
    async onEdit(row) {
      this.onCleardataMethod()
      setTimeout(() => {
        this.ruleForm = JSON.parse(JSON.stringify(row))
        this.ruleForm.batchNumber = row.batchNumber;
        this.ruleForm.shopCode = row.shopCode;
        this.ruleForm.shopName = row.shopName;
        this.ruleForm.shopName1 = row.shopName1;
        this.ruleForm.shopCode1 = row.shopCode1;
        this.ruleForm.id = row.id
        this.ruleTitle = '编辑'
        this.newEditVisible = true
      }, 100)
    },
  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
