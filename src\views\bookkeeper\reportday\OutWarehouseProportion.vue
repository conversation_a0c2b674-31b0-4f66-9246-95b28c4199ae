<template>
  <my-container v-loading="pageLoading">
    <!--顶部操作-->
    <template #header>
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
      </el-form>
    </template>
    <!--列表-->
    <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
      :tableData='dahuixionglist' @select='selectchange' :isSelection='false' :showsummary='true' :tablefixed='true'
      :summaryarry='summaryarry' :tableCols='tableCols' :loading="listLoading">
      <template slot='extentbtn'>
        <el-button-group>
          <el-button type="primary" @click="onSearch">刷新</el-button>
          <el-button type="primary" @click="addButton">添加出仓成本</el-button>
        </el-button-group>
      </template>
    </ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getjSpeedDriveList" />
    </template>


    <el-drawer title="仓库出仓数据维护" :modal="false" :wrapper-closable="true" :modal-append-to-body="false"
      :visible.sync="editVisible" direction="btt" size="'auto'" class="el-drawer__wrapper" style="position:absolute;">
      <el-form ref="form" :model="form" label-width="100px" :rules="rules">
        <el-form-item label="发货仓库" prop="wmsName">
          <el-input v-model="form.id" style="width: 200px;display: none;"/>
          <el-input v-model="form.wmsName" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="出仓成本" prop="exitCost">
          <el-input-number :precision="2" :step="0.01" :min="0" :max="100" v-model="form.exitCost" />
        </el-form-item>
        <el-form-item label="包装费" prop="exitCost">
          <el-input-number :precision="2" :step="0.01" :min="0" :max="100" v-model="form.packageFee" />
        </el-form-item>
        <el-form-item label="虚拟快递费" prop="virtualExpressCost">
          <el-input-number :precision="2" :step="0.01" :min="0" :max="100" v-model="form.virtualExpressCost" />
        </el-form-item>
        <el-form-item label="最低快递费" prop="minExpressCost">
          <el-input-number :precision="2" :step="0.01" :min="0" :max="100" v-model="form.minExpressCost" />
        </el-form-item>
        <el-form-item label="是否云仓" prop="isYunCang">
          <el-select v-model="form.isYunCang" style="width: 200px;"> 
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用起始日期" prop="minQyDate">
            <el-date-picker style="width:200px; float: left;" v-model="form.minQyDate" type="date" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="启用结束日期" prop="maxQyDate">
            <el-date-picker style="width: 200px; float: left;" v-model="form.maxQyDate" type="date" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-input v-model="form.region" style="width: 200px;" />
        </el-form-item>
      </el-form>
      <div class="drawer-footer">
        <el-button @click.native="editVisible = false">取消</el-button>
        <my-confirm-button type="submit" @click="onEditSubmit" />
      </div>
    </el-drawer>

  </my-container>
</template>
<script>

import { getExitCost as getLossOffFee, editExitCost as editLossOffFee } from '@/api/bookkeeper/reportdayV2'

import cesTable from "@/components/Table/table.vue";

import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";

const tableCols = [
  { istrue: true, prop: 'wmsName', label: '仓库', sortable: 'custom' },
  { istrue: true, prop: 'exitCost', label: '出仓成本', width: '130', sortable: 'custom', tipmesg: '出仓成本' },
  { istrue: true, prop: 'packageFee', label: '包装费', width: '130', sortable: 'custom' },
  { istrue: true, prop: 'virtualExpressCost', label: '虚拟快递费', width: '130', sortable: 'custom', tipmesg: '虚拟快递费' },
  { istrue: true, prop: 'minExpressCost', label: '最低快递费', width: '130', sortable: 'custom', tipmesg: '最低快递费，当真实快递费低于此值时，取该值！' },
  { istrue: true, prop: 'isYunCang', label: '是否云仓', width: '130', sortable: 'custom', formatter: (row) => row.isYunCang == 1 ? '是' : '否' },
  { istrue: true, prop: 'region', label: '区域', sortable: 'custom' },
  { istrue: true, prop: 'minQyDate', label: '启用起始日期', sortable: 'custom' },
  { istrue: true, prop: 'maxQyDate', label: '启用结束日期', sortable: 'custom' },
  { istrue: true, type: "button", label: '操作', width: "450", btnList: [{ label: "编辑", handle: (that, row) => that.EditButton(row) }] }
];
export default {
  name: "OutWarehouseProportion",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
  data() {
    return {
      that: this,
      editLoading: false,
      addVisible: false,
      Filter: {
      },
      tableCols: tableCols,
      total: 0,
      summaryarry: {},
      pager: { OrderBy: "wmsName", IsAsc: false },
      sels: [], // 列表选中列
      listLoading: false,
      pageLoading: false,
      //
      selids: [],
      dialogVisibleSyj: false,
      fileList: [],
      platform: 0,
      yearMonth: "",
      editVisible: false,
      form: { wmsName: "", exitCost: 0, virtualExpressCost: 0, minExpressCost: 0, isYunCang: 0,region:"",id:0, minQyDate:'',maxQyDate:''},
      rules: { wmsName: [{ required: true, message: '请输入仓库名称' }] }
    };
  },
  async mounted() {
    this.onSearch();
  },
  methods: {
    addButton() {
      this.editVisible = true;
      this.form = { wmsName: "", exitCost: 0,packageFee:0, virtualExpressCost: 0, minExpressCost: 0, isYunCang: 0 ,id:0, minQyDate:'',maxQyDate:''};
    },
    EditButton(row) {
      this.editVisible = true
      this.form = { ...row };
    },
    async onEditSubmit() {
      let self=this;
      this.$refs["form"].validate(async (valid) => {
          if (valid) {
            let res= await editLossOffFee(self.form);
            if (res.code == 1) {
              self.$message.success('操作成功！');
              self.getjSpeedDriveList();
              self.editVisible = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });     
    },
    sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    onSearch() {
      this.$refs.pager.setPage(1);
      this.getjSpeedDriveList();
    },
    async getjSpeedDriveList() {
     
      const para = { ...this.Filter };
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,

      };

      console.log(para)

      this.listLoading = true;
      const res = await getLossOffFee(params);
      console.log(res)
      this.listLoading = false;
      console.log(res.data.list)   
    
      this.total = res.data.total
      this.dahuixionglist = res.data.list;
      this.dahuixionglist.forEach(f=>{
        if(f.minQyDate)
          f.minQyDate = dayjs(f.minQyDate).format('YYYY-MM-DD')
        if(f.maxQyDate)
        f.maxQyDate = dayjs(f.maxQyDate).format('YYYY-MM-DD')
        })
      this.summaryarry = res.data.summary;
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    }
  },
};
</script>