<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="仓库:" label-position="right">
                    <el-select filterable clearable v-model="filter.warehouseCode" placeholder="请选择仓库" x>
                        <el-option v-for="item in warehouseList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="拣货库位:" label-position="right">
                    <el-input v-model="filter.shelvesLocation" placeholder="拣货库位" clearable style="width:160px;" />
                </el-form-item>
                <el-form-item label="商品编码:" label-position="right">
                    <el-input v-model="filter.goodsCode" placeholder="商品编码" clearable style="width:160px;" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="importProps">导入</el-button>
                    <el-button type="primary" @click="onModel">下载模板</el-button>
                </el-form-item>
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :tableData='list' :showsummary='true' :summaryarry='summaryarry' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading" :isSelectColumn='false'>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>



        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading" :close-on-click-modal="false">
            <div style="display: flex;flex-direction: column;justify-content: center;">

                <el-select filterable clearable v-model="warehouseCode" placeholder="请选择仓库"
                    style="margin-top: 20px; margin-bottom: 20px;">
                    <el-option v-for="item in warehouseList" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                </el-select>

                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="dialogEditActualQuantityVisible" v-dialogDrag v-loading="editLoading" width="20%">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <el-form label-position="left" :model="editFilter" :rules="rules" ref="ruleForm">
                    <el-form-item label="仓库：">
                        <el-input v-model="editFilter.warehouseName" :disabled="true" style="width: 70%;"></el-input>
                    </el-form-item>
                    <el-form-item label="件货库位：">
                        <el-input v-model="editFilter.shelvesLocation" :disabled="true" style="width: 70%;"></el-input>
                    </el-form-item>
                    <el-form-item label="商品编码：">
                        <el-input v-model="editFilter.goodsCode" :disabled="true" style="width: 70%;"></el-input>
                    </el-form-item>
                    <el-form-item label="实际数量：" prop="actualQuantity">
                        <el-input v-model.number="editFilter.actualQuantity" autocomplete="off" 
                        placeholder="请输入实际数量" :min="0" :max="100000" :controls="false" style="width: 70%;" />
                    </el-form-item>
                </el-form>
            </div>
            <div class="btnGroup">
                <el-button @click="dialogEditActualQuantityVisible = false">取消</el-button>
                <el-button type="primary" @click="editActualQuantity">确定</el-button>
            </div>
        </el-dialog>

    </my-container>
</template>
<script>
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { GetAllWarehouseList, ImportKuWeiKuRongList, GetKuWeiKuRongPageList as getPageList, editKuWeiKuRongActualQuantity } from '@/api/inventory/warehouse';

const tableCols = [
    { istrue: true, prop: 'warehouseCode', label: '仓库', width: '140', sortable: 'custom', formatter: (row) => { return row.warehouseName } },
    { istrue: true, prop: 'shelvesLocation', label: '拣货库位', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'shelvesLength', label: '长(mm)', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'shelvesWidth', label: '宽(mm)', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'shelvesHeight', label: '高(mm)', sortable: 'custom', width: '70' },

    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'updateTime', label: '更新时间', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'goodLength', label: '长(mm)', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'goodWidth', label: '宽(mm)', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'goodHeight', label: '高(mm)', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'goodWeight', label: '重量(g)', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'actualQuantity', label: '实际数量', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'minQuantity', label: '库容下限', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'expectedCount', label: '预计可存放数量', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'expectedWeight', label: '预计可存放重量', sortable: 'custom', width: '90' },

    { istrue: true, prop: 'ago1SaleCount', label: '昨日销量', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'ago3SaleCount', label: '3日销量', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'ago7SaleCount', label: '7日销量', sortable: 'custom', width: '90' },
    { istrue: true, label: '操作', fixed:"right", type: 'button', width: '50', btnList: [ { label: "编辑", display: (row) => { return row.isHandle == true; }, handle: (that, row) => that.onEdit(row) } ] }
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable },
    data() {
        return {
            that: this,
            filter: {
            },
            list: [],
            tableCols: tableCols,
            summaryarry: {},
            total: 0,
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],

            fileList: [],
            importLoading: false,
            importVisible: false,
            warehouseList: [],
            warehouseCode: null,
            warehouseName: null,
            editLoading: false,
            dialogEditActualQuantityVisible: false,
            editFilter: {
                warehouseCode: null,
                warehouseName: null,
                shelvesLocation: null,
                goodsCode: null,
                actualQuantity: null
            },
            rules: {
                actualQuantity: [
                    { required: true, message: '实际数量不能为空', trigger: 'blur'},
                    { type: 'number', message: '实际数量必须为数字值', trigger: 'blur'}
                ]
            },
        };
    },
    async mounted() {
        await this.onGetAllWarehouseList();
        this.onSearch();
    },
    methods: {
        async onGetAllWarehouseList() {
            let res = await GetAllWarehouseList();
            this.warehouseList = res.data.map(item => {
                return { value: item.wms_co_id, label: item.name };
            });
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...this.filter,
            };
            this.listLoading = true;
            const res = await getPageList(params);
            this.listLoading = false;
            this.total = res.data?.total
            this.list = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },

        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.warehouseCode) return this.$message.error('请选择仓库')
            this.warehouseName = this.warehouseList.find(f => f.value == this.warehouseCode).label;
            if (!this.warehouseName) return this.$message.error('请选择仓库')
            if (this.file == null) return this.$message.error('请上传文件')
            const form = new FormData();
            form.append("file", this.file);
            form.append("warehouseCode", this.warehouseCode);
            form.append("warehouseName", this.warehouseName);
            this.importLoading = true
            await ImportKuWeiKuRongList(form).then(({ success }) => {
                if (success) {
                    this.$message.success('正在后台导入中,请稍后刷新界面查看....')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.fileList = []
            this.file = null
            this.warehouseCode = null
            this.warehouseName = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async onModel() {
            window.open("/static/excel/inventory/库位库容导入模板.xlsx", "_blank");
        },
        onEdit(row) {
            this.dialogEditActualQuantityVisible = true;
            this.editFilter.warehouseCode = row.warehouseCode;
            this.editFilter.warehouseName = row.warehouseName;
            this.editFilter.shelvesLocation = row.shelvesLocation;
            this.editFilter.goodsCode = row.goodsCode;
            this.editFilter.actualQuantity = row.actualQuantity;
        },
        async editActualQuantity (){
            this.$refs.ruleForm.validate(async (valid) => {
            if (valid) {
                    this.editLoading = true;
                    var params = {
                        warehouse: this.editFilter.warehouseCode,
                        warehouseName: this.editFilter.warehouseName,
                        shelvesLocation: this.editFilter.shelvesLocation,
                        goodsCode: this.editFilter.goodsCode,
                        actualQuantity: this.editFilter.actualQuantity
                    };
                    await editKuWeiKuRongActualQuantity(params).then(({success})=>{
                        if(success){
                            this.$message.success('保存成功');
                            this.dialogEditActualQuantityVisible = false;
                            this.getList();
                        }
                        this.editLoading = false;
                    }).catch(arr => {
                        this.editLoading = false
                        this.$message.error('保存失败')
                    })
                }
            });
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
::v-deep .el-input__inner {
    text-align: left;
}
</style>
