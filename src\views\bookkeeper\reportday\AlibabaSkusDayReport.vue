<template>
  <my-container v-loading="pageLoading">
    <template #header>
      <div>
        <el-button-group>
          <el-button style="padding: 0;border: 0;margin: 0;">
            <el-select filterable v-model="filter.timeType" collapse-tags placeholder="时间类型" style="width: 110px">
              <el-option label="发生时间" :value="0" />
              <el-option label="订单时间" :value="1" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: 0;margin: 0;">
            <el-date-picker style="width: 210px" v-model="filter.timerange" type="daterange" :clearable="false"
              format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-button>
          <el-button style="padding: 0;border: 0;margin: 0; ">
            <el-input v-model.trim="filter.goodsCode" :maxlength="150" clearable placeholder="商品编码"
              style="width:160px;" />
          </el-button>
          <el-button style="padding: 0;margin: 0; border: 0;">
            <el-input v-model.trim="filter.combineCode" :maxlength="150" clearable placeholder="组合装编码"
              style="width:160px;" />
          </el-button>
          <el-button style="padding: 0;border: 0;">
            <el-select filterable v-model="filter.profit1UnZero" collapse-tags clearable placeholder="毛利1"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: 0;">
            <el-select filterable v-model="filter.profit2UnZero" collapse-tags clearable placeholder="毛利2"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: 0;">
            <el-select filterable v-model="filter.profit3UnZero" collapse-tags clearable placeholder="毛利3"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button style="padding: 0;border: 0;">
            <el-select filterable v-model="filter.exitProfitUnZero" collapse-tags clearable placeholder="出仓利润"
              style="width: 90px">
              <el-option label="正利润" :value="false" />
              <el-option label="负利润" :value="true" />
            </el-select>
          </el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="clearaway">清除</el-button>
        </el-button-group>
      </div>
    </template>
    <vxetablebase :id="'alibabaSkusDayReport202302031421'" :border="true" :align="'center'"
      :tablekey="'alibabaSkusDayReport202302031421'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
      @sortchange='sortchange' :cstmExportFunc="onExport" :isSelectColumn="true" :showsummary='true' :tablefixed='true'
      :summaryarry='summaryarry' @summaryClick='onsummaryClick' :tableData='financialreportlist' :tableCols='tableCols'
      @cellClick="cellClick" :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:95%;margin: 0"
        :xgt="9999">
      <template slot='extentbtn'>
        <!-- <el-button type="primary" size="small" @click="dialogConfirmdata =true">日报确认</el-button> -->
        <!-- <el-button type="primary" size="small" @click="dialogConfirmdata2 =true">违规扣款确认</el-button> -->
        <!-- <el-button-group>
            <el-radio-group v-model="filter.refundType" size="small">
              <el-radio-button label="false" >发生维度</el-radio-button>
              <el-radio-button label="true">付款维度</el-radio-button>
           </el-radio-group>
        </el-button-group> -->
      </template>
    </vxetablebase>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
    </template>
  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { getDirectorGroupList, getDirectorList } from '@/api/operatemanage/base/shop'
import EveryDayrefund from '@/views/bookkeeper/reportday/EveryDayrefund'
import { productOrderDayReport as pageProductDayReport, exportProductOrderDayReport, queryProductOrderDayReportSumChart, orderDayRpt_OrderTypes, orderDayRpt_OrderTypes_fmtFunc, orderDayRpt_OrderStatuss } from '@/api/bookkeeper/reportdayV2'
import { importProductDayReport } from '@/api/bookkeeper/import'
import { getAllProBrand } from '@/api/inventory/warehouse'
import productdrchart from '@/views/bookkeeper/reportday/productdrchart'
import ProductADPDD from '@/views/bookkeeper/reportday/ProductADPDD'
import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import InputMult from "@/components/Comm/InputMult";
import { Loading } from 'element-ui';
import { ruleDirectorGroup } from '@/utils/formruletools'
import freightDetail from '@/views/bookkeeper/reportday/freightDetail'
import expressfreightanalysis from '@/views/express/expressfreightanalysis'
import buschar from '@/components/Bus/buschar'
import importmodule from '@/components/Bus/importmodule'
import ordergiftdetail from '@/views/order/gift/ordergiftDetail'
import { getListByStyleCode } from "@/api/inventory/basicgoods"
import inputYunhan from "@/components/Comm/inputYunhan";
import { filterStatus } from '@/utils/getCols'
import YyGroupSelector from "@/components/YhCom/YyGroupSelector.vue"
import YyDirectorSelector from "@/components/YhCom/YyDirectorSelector.vue"
let loading;
const startLoading = () => {
  loading = Loading.service({
    lock: true,
    text: '加载中……',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
const tableCols = [
  //{ istrue: true, prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '80', type: 'custom' },
  { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: '500', type: 'custom' },
  { istrue: true, prop: 'combineCode', label: '组合装编码', sortable: 'custom', width: '150', type: 'custom' },
  { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '100', type: 'custom' },
  { istrue: true, prop: 'saleAmont', label: '销售金额', sortable: 'custom', width: '100', type: 'custom' },
  { istrue: true, prop: 'saleCost', label: '销售成本', sortable: 'custom', width: '100', type: 'custom' },
  { istrue: true, prop: 'profit1', label: '毛利一', sortable: 'custom', width: '100', type: 'custom' },
  { istrue: true, prop: 'profit2', label: '毛利二', sortable: 'custom', width: '100', type: 'custom' },
  { istrue: true, prop: 'profit3', label: '毛利三', sortable: 'custom', width: '100', type: 'custom' },
  { istrue: true, prop: 'exitProfit', label: '出仓利润', sortable: 'custom', width: '100', type: 'custom' },
];
const tableHandles = [
  { label: "导出", handle: (that) => that.$refs.table.setExportCols() },
  //{ label: "计算订单日报", handle: (that) => that.showCalDayRepoty() },
  { label: "刷新", handle: (that) => that.onRefresh() },
];

export default {
  name: "Users",
  components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, productdrchart, InputMult, freightDetail, buschar, expressfreightanalysis, importmodule, ordergiftdetail, ProductADPDD, EveryDayrefund, inputYunhan, vxetablebase, YyGroupSelector, YyDirectorSelector },
  data() {
    return {
      orderDayRpt_OrderTypes: orderDayRpt_OrderTypes,
      orderDayRpt_OrderStatuss: orderDayRpt_OrderStatuss,
      dialogConfirmdata: false,
      // dialogConfirmdata2: false,
      confirmDate: '',
      confirmDate2: '',
      searchloading: '',
      dialogCalDayRepotVis: false,
      calDayRepotyDate: null,
      that: this,
      filter: {
        exitProfitUnZero: true,
        isRefundAmontBefore: false,
        IsSpecialOrder: false,
        payAmountLessThan2: false,
        orderStatusS: [],
        exceptOrderTypes: [],
        orderType: null,
        orderTypes: [],
        timeType: 1,
        orderType: null,
        //refundType: false,
        reportType: 0,
        platform: 4,
        OrderDayReportType: 8,
        // shopCode:null,
        // proCode:null,
        // styleCode:null,
        // productName:null,
        // brandId:null,
        // groupId:null,
        startTime: null,
        endTime: null,
        timerange: null,
        isChart: null,
        orderNo: null,
        orderNoInner: null,
        orderStatusS: null,
        proCode: null,
        goodsCode: null,
        combineCode: null,
        goodsName: null,
        styleCode: null,
        shopName: null,
        groupId: null,
        operateSpecialUserId: null,
        userId: null,
        profit1UnZero: null,
        profit2UnZero: null,
        profit3UnZero: null,
      },
      onimportfilter: {
        yearmonthday: null,
      },
      styleCode: null,
      shopList: [],
      userList: [],
      brandlist: [],
      grouplist: [],
      directorlist: [],
      financialreportlist: [],
      tableCols: tableCols,
      tableHandles: tableHandles,
      total: 0,
      pager: { OrderBy: " SaleAmont ", IsAsc: false },
      sels: [], // 列表选中列
      options: [],
      listLoading: false,
      pageLoading: false,
      earchloading: false,
      summaryarry: {},
      selids: [],
      fileList: [],
      dialogVisible: false,
      uploadLoading: false,
      importFilte: {},
      fileList: [],
      fileparm: {},
      editparmVisible: false,
      editLoading: false,
      editparmLoading: false,
      drawervisible: false,
      dialogDrVisible: false,
      expressfreightanalysisVisible: false,
      drparamProCode: '',
      autoformparm: {
        fApi: {},
        options: { submitBtn: false, global: { '*': { props: { disabled: false }, col: { span: 6 } } } },
        rule: []
      },
      freightDetail: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: []
        },
      },
      EveryDayrefund: {
        visible: false,
        filter: {
          proCode: null,
          timeRange: [],
          afterSaleType: "2",
          orderStatus: "已取消",
          goodsStatus: "",
          timeRange1: [],
          platform: 4
        }
      },
      giftDetail: { visible: false },
      buscharDialog: { visible: false, title: "", data: {}, loading: false },
      drawervisible: false,
    };
  },
  async mounted() {
    this.init();
    this.clearaway();
    // this.filter.orderStatusS = filterStatus('已取消', this.orderDayRpt_OrderStatuss, this.filter.orderStatusS);
    // this.filter.orderTypes = filterStatus(["补发订单", "换货订单"], this.orderDayRpt_OrderTypes, this.filter.orderTypes);
    // this.filter.exceptOrderTypes = filterStatus(["普通订单", "分销Plus", "分销"], this.orderDayRpt_OrderTypes, this.filter.exceptOrderTypes);
  },
  async created() {
    await this.getShopList();
    await this.initformparm();
    if (this.$route.query && this.$route.query.dayCount) {

      this.filter.noProfitDay = parseInt(this.$route.query.dayCount);
      this.filter.shopCode = this.$route.query.shopCode;
      this.filter.groupId = this.$route.query.groupId;
      this.filter.operateSpecialUserId = this.$route.query.operateSpecialUserId;
      let dateStr = this.$route.query.yearMonthDay.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3");
      this.filter.timerange = [dateStr, dateStr];
      this.filter.refundType = false;
      this.onSearch();
    }
  },
  methods: {
    //清除搜索选项内容
    clearaway() {
      this.filter.orderNo = null;
      this.filter.orderNoInner = null;
      this.filter.proCode = null;
      this.filter.goodsCode = null;
      this.filter.combineCode = null;
      this.filter.goodsName = null;
      this.filter.styleCode = null;
      this.filter.shopName = null;
      this.filter.groupId = null;
      this.filter.operateSpecialUserId = null;
      this.filter.userId = null;
      this.filter.profit1UnZero = null;
      this.filter.profit2UnZero = null;
      this.filter.profit3UnZero = null;
      this.filter.orderStatusS = null;
      this.filter.orderTypes = [];
      this.filter.exceptOrderTypes = [];
      this.filter.exitProfitUnZero = null;
      this.filter.payAmountLessThan2 = null;
      this.filter.isRefundAmontBefore = null;
      this.filter.IsSpecialOrder = null;
      this.$refs.YyGroupSelector.clearValue()
      this.$refs.YyDirectorSelector.clearValue()
      this.$refs.YyDirectorSelector1.clearValue()
    },
    async onsummaryClick(property) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      this.filter.isChart = 1;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      params.column = property;
      let that = this;
      that.listLoading = true;
      that.buscharDialog.loading = true;
      const res = await queryProductOrderDayReportSumChart(params).then(res => {
        that.buscharDialog.loading = false;
        that.buscharDialog.data = res.data
        that.buscharDialog.title = res.data.legend[0]
      });
      that.listLoading = false;
      that.buscharDialog.visible = true;
      this.$nextTick(async () => { await that.$refs.buschar.initcharts(); });
      this.filter.isChart = null;
    },

    JumpDetailOrderDayReport(row) {
      this.$emit("ChangeActiveName2", row.orderNo, 1, this.filter.timerange)
    },
    AlibabaGoodCodeDayReportArgument(e) {
      this.filter.orderNoInner = e;
      this.getList();
    },
    cellClick(prms) {
      if (prms?.column?.field && prms?.column?.field === 'profit3IncreaseGoOnDays') {
        let row = prms.row;
        this.showprchart2(row.proCode, row.platform);
      }

    },
    async confirmData() {
      if (!this.confirmDate) {
        this.$message({ type: 'warning', message: '请选择日期!' });
        return;
      }
      let par = {
        dailyReportDate: this.confirmDate,
        dailyReportType: '阿里巴巴订单日报',
      };
      let confirmtitle = '【' + this.confirmDate + '】' + par.dailyReportType + '数据，确定确认？';
      this.$confirm(confirmtitle)
        .then(async _ => {
          let res = await insertDailyReportConfirmList(par);
          if (res.data) {
            this.$message({ type: 'success', message: '保存成功!' });
          }
          this.dialogConfirmdata = false;
        })
        .catch(_ => { });
    },

    async showCalDayRepoty() {
      this.dialogCalDayRepotVis = true;
    },
    async calDayRepoty() {
      if (this.calDayRepotyDate == null) {
        this.$message({ type: 'warning', message: '请先选择日期!' });
        return
      }
      let res = await calDayRepoty({ type: 'PddOrder', yearMonthDay: this.calDayRepotyDate });
      if (!res?.success) return
      this.$message({ type: 'success', message: '正在计算中,请稍候...' });
      this.dialogCalDayRepotVis = false;
    },
    showEveryDayrefund(row) {
      this.EveryDayrefund.filter.proCode = row.proCode;
      if (row.yearMonthDay != null) {
        var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
        this.EveryDayrefund.filter.timeRange = [dayStr, dayStr];
      }
      else {
        this.EveryDayrefund.filter.timeRange = this.filter.timerange
      }
      this.EveryDayrefund.visible = true;
      setTimeout(async () => {
        await this.$refs.EveryDayrefund.onSearch();
      }, 100);

    },
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init() {
      var date1 = new Date(); date1.setDate(date1.getDate() - 1);
      var date2 = new Date(); date2.setDate(date2.getDate() - 1);
      this.filter.timerange = [];
      this.filter.timerange[0] = this.datetostr(date1);
      this.filter.timerange[1] = this.datetostr(date2);
      console.log(this.filter)
    },
    async showprchart2(prcode, platform) {
      window['lastseeprcodedrchart'] = prcode
      window['lastseeprcodedrchart1'] = platform
      window['lastseeprcodedrchart2'] = this.filter.refundType
      this.drparamProCode = prcode
      this.dialogDrVisible = true
    },
    async initformparm() {
      let that = this;
      this.autoformparm.rule = [{ type: 'hidden', field: 'id', title: 'id', value: '', col: { span: 12 } },
      { type: 'select', field: 'groupId', title: '组长', value: '', update(val, rule) { { that.updateruleGroup(val) } }, ...await ruleDirectorGroup(), props: { filterable: true } },
      { type: 'InputNumber', field: 'Profit3PredictRate', title: '毛三预估比例%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } },
      { type: 'InputNumber', field: 'ShareRate', title: '公摊费率%', value: null, props: { min: 0, precision: 3 }, col: { span: 6 } },
      ]
    },
    //系列编码远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true;
        this.options = [];
        setTimeout(async () => {
          const res = await getListByStyleCode({ currentPage: 1, pageSize: 50, styleCode: query })
          console.log("系列编码远程搜索", res);
          this.searchloading = false
          res?.data?.forEach(f => {
            this.options.push({ value: f.styleCode, label: f.styleCode })
          });
        }, 200)
      }
      else {
        this.options = []
      }
    },
    async getShopList() {
      const res1 = await getAllShopList({ platforms: [4] });
      this.shopList = res1.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
      // res1.data?.forEach(f => {
      //   if(f.isCalcSettlement&&f.shopCode)
      //       this.shopList.push(f);
      // });
      var res2 = await getDirectorGroupList();
      this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

      var res3 = await getDirectorList();
      this.directorlist = res3.data?.map(item => { return { value: item.key, label: item.value }; });

      var res4 = await getAllProBrand();
      this.brandlist = res4.data.map(item => {
        return { value: item.key, label: item.value };
      });
    },
    async sortchange(column) {
      if (!column.order)
        this.pager = {};
      else
        this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
      await this.onSearch();
    },
    onRefresh() {
      this.onSearch()
    },
    async onSearch() {
      this.$refs.table.changecolumn_setTrue(["yearMonthDay"]);
      if (this.filter.groupType == 1 || this.filter.groupType == 2) {
        this.$refs.table.changecolumn(["yearMonthDay"]);
      }
      this.$refs.pager.setPage(1);
      await this.getList().then(res => { });

      // loading.close();
    },
    async getList() {
      this.filter.startTime = null;
      this.filter.endTime = null;
      //   this.filter.listingStartTime = null;
      // this.filter.listingEndTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      //   if (this.filter.timerange1) {
      //   this.filter.listingStartTime = this.filter.timerange1[0];
      //   this.filter.listingEndTime = this.filter.timerange1[1];
      // }
      // if(this.filter.starNumber!=null&&this.filter.endNumber!=null)
      // {
      //   if(this.filter.starNumber>this.filter.endNumber)
      //   {
      //     this.$message({ type: 'warning', message: '开始订单量值不能大于结束订单量值!' });
      //    return
      //   }
      // }
      //this.filter.styleCode = this.styleCode.join()
      var that = this;
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter };
      // this.listLoading = true;
      startLoading();
      const res = await pageProductDayReport(params).then(res => {
        loading.close();
        if (res?.data?.list && res?.data?.list.length > 0) {
          for (var i in res.data.list) {
            if (!res.data.list[i].freightFee) {
              res.data.list[i].freightFee = " ";
            }
            if (that.filter.refundType) {
              res.data.list[i].RefundAmont = res.data.list[i].RefundAmontByPay;
              res.data.list[i].Profit3 = res.data.list[i].Profit3ByPay;
              res.data.list[i].Profit3Rate = res.data.list[i].Profit3RateByPay;
            }
          }
        }
        if (that.filter.refundType == true) {
          res.data.summary.Profit3Rate_sum = res.data?.summary?.Profit3RateByPay_sum;
          res.data.summary.RefundAmontByPay = res.data?.summary?.RefundAmontByPay_sum;
          res.data.summary.Profit3ByPay = res.data?.summary?.Profit3ByPay_sum;
        }
        that.total = res.data?.total;
        that.financialreportlist = res.data?.list;
        that.$refs.table.loadRowEcharts();
        that.summaryarry = res.data?.summary;
      });
    },
    showFreightDetail(row) {
      if (row.freightFee >= 1) {
        this.freightDetail.filter.proCode = row.proCode;
        if (row.yearMonthDay != null) {
          var dayStr = row.yearMonthDay.substr(0, 4) + '-' + row.yearMonthDay.substr(4, 2) + '-' + row.yearMonthDay.substr(6, 2)
          this.freightDetail.filter.timeRange = [dayStr, dayStr];
        }
        else {
          this.freightDetail.filter.timeRange = this.filter.timerange
        }
        this.freightDetail.visible = true;
        setTimeout(async () => {
          await this.$refs.freightDetail.onSearch();
        }, 100);
      }
    },
    showProcodesimilarity(row) {
      if (row.styleCode != null) {
        this.$router.push({ path: '/order/procodesimilarity', query: { styleCode: row.styleCode } })
      }
    },
    async showGiftDetail(row) {
      var yearMonthDayStart = row.yearMonthDay
      var yearMonthDayEnd = row.yearMonthDay
      if (this.filter.groupType) {
        yearMonthDayStart = this.filter.timerange[0].replace("-", "").replace("-", "")
        yearMonthDayEnd = this.filter.timerange[1].replace("-", "").replace("-", "")
      }
      this.giftDetail.visible = true;
      let _th = this;
      await this.$nextTick(async () => { await _th.$refs.ordergiftdetail.onShow(yearMonthDayStart, yearMonthDayEnd, row.proCode); });
    },
    selectchange: function (rows, row) {
      this.selids = [];
      rows.forEach(f => {
        this.selids.push(f.id);
      })
    },
    onRefresh() {
      this.onSearch()
    },
    async updateruleGroup(groupid) {
      if (!groupid)
        this.autoformparm.fApi.resetFields()
      else {
        const res = await getParm({ groupId: groupid })
        var arr = Object.keys(this.autoformparm.fApi);
        res.data.groupId = groupid;
        if (!res.data?.Profit3PredictRate) res.data.Profit3PredictRate = 0;
        if (!res.data?.ShareRate) res.data.ShareRate = 0;
        await this.autoformparm.fApi.setValue(res.data)
      }
    },
    async onstartImport() {
      this.dialogVisible = true;
    },
    uploadSuccess(response, file, fileList) {
      if (response.code == 200) {
      } else {
        fileList.splice(fileList.indexOf(file), 1);
      }
    },
    submitUpload() {
      if (!this.onimportfilter.yearmonthday) {
        this.$message({ type: 'warning', message: '请选择年月!' });
        return;
      }
      this.uploadLoading = true
      this.$refs.upload.submit();
    },
    async uploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 1);
      form.append("yearmonthday", this.onimportfilter.yearmonthday);
      var res = await importProductDayReport(form);
      if (res.code == 1) this.$message({ message: "上传成功,正在导入中...", type: "success" });
      else this.$message({ message: res.msg, type: "warning" });
      this.uploadLoading = false
    },
    async onExport(opt) {
      this.filter.startTime = null;
      this.filter.endTime = null;
      if (this.filter.timerange) {
        this.filter.startTime = this.filter.timerange[0];
        this.filter.endTime = this.filter.timerange[1];
      }
      var pager = this.$refs.pager.getPager();
      const params = { ...pager, ...this.pager, ...this.filter, ...opt };
      var res = await exportProductOrderDayReport(params);
      if (!res?.data) {
        return
      }
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '阿里巴巴订单日报数据' + new Date().toLocaleString() + '_.xlsx')
      aLink.click()
    },
    async onShowEditParm() {
      this.editparmVisible = true
      const res = await getParm()
      var arr = Object.keys(this.autoformparm.fApi);
      if (arr.length > 0)
        this.autoformparm.fApi.resetFields()
      await this.autoformparm.fApi.setValue(res.data)
    },
    async onSetEditParm() {
      this.editparmLoading = true;
      await this.autoformparm.fApi.validate(async (valid, fail) => {
        if (valid) {
          const formData = this.autoformparm.fApi.formData();
          const res = await setParm(formData);
          if (res.code == 1) this.editparmVisible = false;
        } else { }
      })
      this.editparmLoading = false;
    },
    showexpressfreightanalysis() {
      this.expressfreightanalysisVisible = true;
      this.$nextTick(() => { this.$refs.expressfreightanalysis.onSearch() });
    },
    renderCost(row) {
      if (row.replaceSendCost > 0) {
        return "color:blue;cursor:pointer;";
      }
      else {
        return "";
      }
    },
    showupload() {
      this.drawervisible = true;
    }
    , async callbackProCode(val) {
      // this.filter.proCode = val;
    },
  }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
