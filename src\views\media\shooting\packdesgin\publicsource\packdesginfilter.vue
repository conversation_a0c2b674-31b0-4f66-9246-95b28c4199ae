<template>
  <!-- 筛选 -->
  <el-button-group>
    <div class="ssanc">
      <div style="width:20%;display: inline-block;text-align: left;">
        <span style="margin-right:0.8%;">
          <el-input placeholder="任务编号" v-model="filter.packageDesignTaskId" oninput="value=value.replace(/[^\d]/g,'')"
            maxlength="9" style="width: 30%;"></el-input>
        </span>

        <span style="margin-right:0.8%;">
          <el-input style="width:66.4%;" v-model.trim="filter.productShortName" :maxlength=100 placeholder="产品简称"
            @keyup.enter.native="onSearch" clearable />
        </span>

      </div>

      <div style="width:25%;display: inline-block;">

        <span style="margin-left:10px;">
          <el-button style="width:90px;" type="primary" @click="onSearch">查&nbsp;询</el-button>
        </span>

        <span style="margin-left:5px;">
          <el-button @click="onclear">重置</el-button>
        </span>

      </div>

      <div style="width:55%;display: inline-block;text-align: right;">

        <span v-if="!islook" style=" margin-left:20px;padding:0 2px;">
          <el-button size="mini" style="width:100px;border: 1px solid #b3d8ff;" type="primary" plain @click="onAddTask"
            v-if="checkPermission('api:media:packdesgin:AddOrUpdateShootingVideoTaskAsync')">
            <i class="el-icon-plus"></i>&nbsp;创建任务</el-button>
        </span>

        <span v-if="!islook" style="box-sizing: border-box; margin-left:6px;">
          <el-button size="mini" type="primary" @click="onAddOrder"
            v-if="checkPermission('packdesgindownorder')">申请拿样</el-button>
        </span>



        <span style=" margin-left:6px;">
          <el-dropdown v-if="checkPermission('packdesgindrop')" size="mini" split-button type="primary"
            icon="el-icon-share" @command="handleCommand">
            批量操作
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="Batcoperation" command="x" style="height:10px;"></el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="listtype == 1" command="a">批量重启</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="listtype == 1" command="b">批量终止</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 1 || listtype == 2)"
                command="c">批量标记</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 1 || listtype == 2)"
                command="d">取消标记</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 1 || listtype == 7)"
                command="e">批量删除</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 3)" command="f">批量使用</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 2 || listtype == 3 || listtype == 4)"
                command="g">批量统计</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 3 || listtype == 5)"
                command="h">信息存档</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 6)" command="i">取消存档</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 4 || listtype == 5)"
                command="j">批量存档</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 7)" command="k">取消存档</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 5)" command="l">取消统计</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 8)" command="m">彻底删除</el-dropdown-item>

              <el-dropdown-item class="Batcoperation" v-if="(listtype == 8)" command="n">任务重启</el-dropdown-item>
              <el-dropdown-item class="Batcoperation" command="x" style="height:10px;"></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <span style="box-sizing: border-box; margin-left:6px;">
          <el-button type="primary" @click="onExport(0)" v-if="checkPermission('packdesginexport')">导出</el-button>
          <el-button type="primary" @click="onExport(1)" v-if="checkPermission('packdesginexport')">导出选中</el-button>
        </span>
        <span style="margin-left:6px;">
          <el-checkbox style="width: 111px;position: relative;top: 1px;" v-if="(listtype == 1)"
            v-model="filter.isComplateCheck" :checked-value="1" :unchecked-value="0" border>隐藏已完成</el-checkbox>
          <el-checkbox style="width: 111px;position: relative;top: 1px;" v-if="(listtype == 2)"
            v-model="filter.isComplateCheck" :checked-value="1" :unchecked-value="0" border>隐藏已确认</el-checkbox>
          <el-checkbox style="width: 111px;position: relative;top: 1px;" v-if="(listtype == 3)"
            v-model="filter.isComplateCheck" :checked-value="1" :unchecked-value="0" border>隐藏已使用</el-checkbox>
        </span>
        <span>
          <el-radio-group style="margin-left:6px;" size="mini" v-if="checkPermission('shootingDropDownList')"
            v-model="onCommand" @change="ShowHideonSearch">
            <el-radio-button label="b">默认</el-radio-button>
            <el-radio-button label="a">全部</el-radio-button>
          </el-radio-group>
        </span>
      </div>
    </div>

    <div class="heardcss">
      <span>
        <el-select v-model="filter.brand" placeholder="品牌" style="width:6%;" multiple :collapse-tags="true" clearable>
          <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>

      <span>
        <el-select v-model="filter.packClass" placeholder="包装类型" style="width:6%;" multiple :collapse-tags="true"
          clearable>
          <el-option v-for="item in packclasslist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>

      <span>
        <el-select v-model="filter.izcjdz" placeholder="厂家定制" style="width:6%;" :collapse-tags="true" clearable>
          <el-option label="是" :value=1></el-option>
          <el-option label="否" :value=0></el-option>
        </el-select>
      </span>

      <span>
        <el-select v-model="filter.warehouse" placeholder="大货仓" style="width:12%;" multiple :collapse-tags="true"
          clearable>
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>

      <span>
        <el-select v-model="filter.fpPhotoLqName" placeholder="分配查询" style="width:10%;" multiple :collapse-tags="true"
          filterable clearable>
          <el-option v-for="item in fpPhotoLqNameList" :key="item.userName" :label="item.userName"
            :value="item.userName" />
        </el-select>
      </span>

      <span>
        <el-select v-model="filter.operationGroup" :clearable="true" placeholder="运营组" filterable style="width:6%;">
          <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>

      <span>
        <el-select v-model="filter.dockingPeople" :clearable="true" placeholder="对接人" filterable 
          style="width:6%">
          <el-option v-for="item in dockingPeopleList" :key="item" :label="item" :value="item" />
        </el-select>
      </span>

      <span>
        <el-select filterable v-model="filter.platform" placeholder="平台" clearable @change="onchangeplatform"
          style="width: 7.5%;">
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </span>

      <span>
        <el-select filterable v-model="filter.shopName" placeholder="店铺" clearable style="width: 12.8%;">
          <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode" />
        </el-select>
      </span>

      <!--下拉（完成时间，创建时间，确认时间，使用时间，）-->
      <span>
        <el-select style="width:5.5%;" v-model="filter.searchTimeType" placeholder="选择时间">
          <el-option label="创建时间" value="2"></el-option>
          <el-option label="完成时间" value="1"></el-option>
          <el-option label="确认时间" value="3"></el-option>
          <el-option label="使用时间" value="4"></el-option>
        </el-select>
      </span>
      <span>
        <el-date-picker style="width:20%;position: relative;top:1px;" type="daterange" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开启日期" end-placeholder="结束日期"
          v-model="filter.createdtimerange" />
      </span>
    </div>
  </el-button-group>
</template>
<script>
import { getList as getshopList } from '@/api/operatemanage/base/shop'

export default {
  props: {
    platformList: { type: Array, default: [] }, //平台
    warehouselist: { type: Array, default: [] }, //仓库
    dockingPeopleList: { type: Array, default: [] }, //对接人
    fpPhotoLqNameList: { type: Array, default: [] }, //分配查询
    groupList: { type: Array, default: [] }, //运营组 
    taskUrgencyList: { type: Array, default: [] },
    islook: { type: Boolean, default: true }, //平台
    brandList: { type: Array, default: [] }, // 
    packclasslist: { type: Array, default: [] }, //
    //批量操作方法 
    listtype: { type: Number, default: 99 },
    //1 任务,2 已完成,3 确认信息,4 已使用,5 统计列表,6 信息存档,7存档,8回收站
  },
  data() {
    return {
      shopList: [],
      onCommand: "b",
      filter: {
        searchTimeType: "2",
        productShortName: '',
        packageDesignTaskId: undefined,
        hasOverTime: '',
        hasConfirmTime: '',
        shopName: null,//店铺
        operationGroup: null,//运营组
        hasOverTime: null,//完成时间
        hasConfirmTime: null,//确认时间
        warehouse: null,//仓库
        fpPhotoLqName: null,//分配人
        dockingPeople: null,//对接人
        platform: null,//平台
        izcjdz: [],
        packClass: null,
        brand: null,
        isComplateCheck: true
      },
    };
  },
  async mounted() {
    //await this.getrole();
  },
  methods: {
    onSearch() {
      if (this.filter.createdtimerange) {
        this.filter.createdstartTime = this.filter.createdtimerange[0];
        this.filter.createdendTime = this.filter.createdtimerange[1];
      } else {
        this.filter.createdstartTime = null;
        this.filter.createdendTime = null;
      }
      this.filter.isComplateChecked = this.filter.isComplateCheck == true ? 0 : 1;
      this.$emit('topSearch', this.filter)
    },
    onAddTask() {
      this.$emit('onAddTask')
    },
    onAddOrder() {
      this.$emit('onAddOrder')
    },
    ShowHideonSearch() {
      this.$emit('ShowHideonSearch', this.onCommand)
    },
    handleCommand(command) {
      this.$emit('handleCommand', command)
    },
    onExport(type) {
      this.$emit('onExport',type)
    },
    onclear() {
      this.filter.productShortName = '';
      this.filter.packageDesignTaskId = null;
      this.filter.hasOverTime = '';
      this.filter.hasConfirmTime = '';
      this.filter.warehouse = [];
      this.filter.createdtimerange = [];
      this.filter.searchTimeType = '2';
      this.filter.shopName = null;
      this.filter.platform = '';
      this.filter.dockingPeople = '';
      this.filter.operationGroup = null;
      this.filter.fpPhotoLqName = [];
      this.filter.brand = [];
      this.filter.packClass = [];
      this.filter.izcjdz = null;
      this.filter.isComplateCheck = false;

    },
    async onchangeplatform(val) {
      var res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100 });
      this.filter.shopName = "";
      this.shopList = res1.data.list;
    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  border: none !important;
  padding: 16px 24px 0 24px;
}

::v-deep .el-header {
  padding: 10px 5px 5px 5px !important;
}

.ssanc {
  width: 100%;
  height: 38px;
  border: 1px solid #dcdfe6;
  border-top: 0px;
  border-right: 0px;
  border-left: 0px;
  display: inline-block;

}

.heardcss {
  width: 100%;
  min-width: 1150px;
  min-height: 35px;
  // background-color: aqua;
  box-sizing: border-box;
  display: inline-block;
  margin-top: 8px;
}


.heardcss span,
.ssanc span {
  padding: 4px 0.2% 4px 0;
}




::v-deep span .el-radio-button__inner {
  line-height: 14px !important;
}

::v-deep .vxetablecss {
  width: 100%;
  margin-top: -20px !important;
}

::v-deep .vxetoolbar20221212 {
  top: 97px;
  right: 15px;
}

.textcss {
  color: #C0C4CC;
}

::v-deep .el-button-group>.el-button:last-child {
  margin-left: -2px !important;
}

::v-deep .vxe-header--row {
  height: 58px;
}

::v-deep .el-table__body-wrapper {
  height: 220px !important;
}

::v-deep .el-table__body-wrapper {
  overflow-y: auto;
}
</style>

