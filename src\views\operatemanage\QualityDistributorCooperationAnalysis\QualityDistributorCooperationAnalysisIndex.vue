<template>
    <my-container>
        <el-tabs v-model="activeName" style="height: 95%;">
            <el-tab-pane label="分销金额最高" name="first" :lazy="true" style="height: 100%;">
                <MaximumDistribution ref="MaximumDistribution" />
            </el-tab-pane>
            <el-tab-pane label="分销金额增量最多" name="second" :lazy="true" style="height: 100%;">
                <DistributionAmountLargest ref="DistributionAmountLargest" />
            </el-tab-pane>
            <el-tab-pane label="铺货最多" name="third" :lazy="true" style="height: 100%;">
                <MostStocked ref="MostStocked" />
            </el-tab-pane>
        </el-tabs>
    </my-container>

</template>
<script>
import MyContainer from "@/components/my-container";
import MaximumDistribution from './MaximumDistribution.vue'
import DistributionAmountLargest from './DistributionAmountLargest.vue'
import MostStocked from './MostStocked.vue'
export default {
    name: "Users",
    components: { MyContainer, MaximumDistribution, DistributionAmountLargest, MostStocked },
    data() {
        return {
            activeName: 'first'
        };
    },
    mounted() {
    },
    methods: {

    },
};
</script>
<style lang="scss" scoped></style>
