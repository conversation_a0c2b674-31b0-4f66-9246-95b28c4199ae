<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="收寄开始日期" end-placeholder="收寄结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-date-picker v-model="timeRangesdr" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime2" :clearable="false">
        </el-date-picker>
        <div style="margin-right: 5px;">
          <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.mailNumber" v-model="ListInfo.mailNumber" width="130px"
            placeholder="邮件号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
            :maxlength="21000" @callback="mailNumberCallback" title="邮件号">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="publicCss" />
        <div>
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </div>
        <!-- <el-select v-model="ListInfo.expressName" clearable filterable placeholder="快递公司" class="publicCss" @change="getprosimstatelist(2)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
          <el-select v-model="ListInfo.prosimstate" clearable filterable placeholder="快递站点" class="publicCss" >
              <el-option label="暂无站点" value="" />
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
        <el-select v-model="ListInfo.warehouse" clearable filterable placeholder="发货仓库" class="publicCss">
             <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
         </el-select> -->
         <el-select v-model="ListInfo.isCf" clearable filterable placeholder="是否重复" class="publicCss">
          <el-option  label="是" value="1"/>
          <el-option  label="否" value="0"/>
        </el-select>
        <div style="display: flex;align-items: center;padding-top: 1px;">
          <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
        </div>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="startClac">计算</el-button>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'dailyBills202410161522'" :tablekey="'dailyBills202410161522'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="100">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <!-- <el-button type="primary" @click="onDayExpressCompanyFeeCalculate(row)">计算</el-button> -->

              <el-button type="danger" @click="onDeleteOperation(row)">批次删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 175px;margin-bottom: 10px;">
        <div style="color: red;padding-bottom: 10px;">
          温馨提示：如该快递公司已配置操作费、包材费等，但存在部站点或发货仓不收取的情况，导入时请选择对应的（操作费、包材费）费用项，则系统在计算时会自动扣除配置的对应费用
        </div>
        <div style="display: flex;gap: 10px;">
          <el-select v-model="importConfirmZt.expressCompanyId" placeholder="快递公司" style="width: 200px;" clearable
            @change="getprosimstatelist(1)">
            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-select v-model="importConfirmZt.prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
            <el-option label="暂无站点" value="" />
            <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
          <el-select v-model="importConfirmZt.warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
            <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div style="margin: 10px 0;display: flex;gap: 10px;">
          <el-date-picker style="width: 45%; float: left;" v-model="importConfirmZt.createTime" type="date" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
          <el-select multiple collapse-tags v-model="importConfirmZt.contactFeeType" clearable filterable placeholder="请选择费用包含类型"
            style="width: 210px;">
            <el-option label="操作费" :value="1"></el-option>
            <el-option label="包材费" :value="2"></el-option>
            <el-option label="加收费" :value="3"></el-option>
            <el-option label="其他" :value="4"></el-option>
          </el-select>
        </div>

        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="计算" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;">
        <el-select v-model="expressCompanyId" placeholder="快递公司"
          style="width: 200px;margin-right: 10px;margin-bottom: 10px;" clearable  @change="getprosimstatelist(1)" >
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
              <el-option label="暂无站点" value="" />
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
        <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 130px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
        <el-date-picker style="width: 230px; float: left;margin-right: 10px;" v-model="createTime" type="date" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>

          <el-input v-model="ruleBatchNumber" placeholder="规则批次号" style="width:230px;" />
        <span style="color: red;"> <br>温馨提示：如需指定计算规则批次号，则填写规则批次号</span>

        <el-input v-model="dayBatchNumber" placeholder="账单数据批次号" style="width:230px;" />
        <span style="color: red;"> <br>温馨提示：如需指定账单数据批次号，则填写账单数据批次号</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="clacDay">确认计算</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { getExpressComanyAll,getExpressComanyStationName, importDayExpressInfoData, getDayExpressInfoData, deleteDayExpressInfoData,dayExpressCompanyFeeCalculate } from "@/api/express/express";
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import queryCondition from "./queryCondition.vue";
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
{ sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompany', label: '快递公司', },
{ sortable: 'custom', width: '80', align: 'center', prop: 'prosimstate', label: '快递站点',  formatter: (row) => row.prosimstateName, type: 'custom' },
{ sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouse', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouse)  },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'receiveDate', label: '收寄日期', formatter: (row) => formatTime(row.receiveDate,"YYYY-MM-DD") },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'inportDate', label: '导入日期', formatter: (row) => formatTime(row.inportDate,"YYYY-MM-DD") },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'province', label: '省', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'city', label: '市', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '重量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'billingWeight', label: '计费重量', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressFee', label: '运费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'isCf', label: '是否重复', formatter: (row) => row.isCf == 1 ? '是'  : '否' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'cfDate', label: '重复日期', formatter: (row) => !row.isCf ? "" : formatTime(row.cfDate,"YYYY-MM-DD") },


]
export default {
  name: "dailyBills",
  components: {
    MyContainer, vxetablebase, queryCondition, inputYunhan
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      importConfirmZt: {
        expressCompanyId: null,
        prosimstate: null,
        warehouse: null,
        createTime: null,
        contactFeeType: []
      },
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      expressCompanyId: null,
      formatWarehouseNew,
      warehouselist: warehouselist,
      warehouse: null,
      createTime:null,
      fileparm: {
        expressCompanyId: null
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        startCreateTime: null,//开始时间
        endCreateTime: null,//结束时间
        expressName: null,
        prosimstate: null,
        warehouse: null,
        noUseCatch: false,
      },
      timeRanges: [],
      timeRangesdr:[],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      expresscompanylist: [],
      dialogVisible2:false,
      prosimstate: null,
      prosimstatelist: [],
      contactFeeType: [],
      ruleBatchNumber:null,
      dayBatchNumber:null,
    }
  },
  async mounted() {
    if (this.timeRangesdr && this.timeRangesdr.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startCreateTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
      this.timeRangesdr = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
    }
    // await this.getList()
    await this.init()
  },
  methods: {
    mailNumberCallback(val) {
      this.ListInfo.mailNumber = val
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }

      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("expressCompanyId", this.importConfirmZt.expressCompanyId);
      form.append("createTime", this.importConfirmZt.createTime);
      form.append("warehouse", this.importConfirmZt.warehouse);
      form.append("prosimstate", this.importConfirmZt.prosimstate);
      form.append("contactFeeType", this.importConfirmZt.contactFeeType.join(','));

      var res = await importDayExpressInfoData(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
    },
    onSubmitUpload() {
      if(!this.importConfirmZt.createTime){
        this.$message({ message: "请选择时间", type: "warning" });
        return false;
      }
      if(!this.importConfirmZt.expressCompanyId){
        this.$message({ message: "请选择快递公司", type: "warning" });
        return false;
      }
      if(!this.importConfirmZt.prosimstate){
        this.$message({ message: "请选择站点", type: "warning" });
        return false;
      }
      if((this.importConfirmZt.warehouse == null || this.importConfirmZt.warehouse == '') && (this.importConfirmZt.warehouse != '0')){
        this.$message({ message: "请选择仓库", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.importConfirmZt.expressCompanyId = null
      this.importConfirmZt.prosimstate = null
      this.importConfirmZt.warehouse = null
      this.importConfirmZt.createTime = null
      this.importConfirmZt.contactFeeType = []
      this.expressCompanyId = null
      this.fileList = []
      this.importConfirmZt.expressCompanyId = this.topfilter.expressCompanyId ? (this.topfilter.expressCompanyId).toString() : null
      setTimeout(async() => {
        this.getprosimstatelist(1)
        this.importConfirmZt.prosimstate = this.topfilter.prosimstateId
        this.importConfirmZt.warehouse = this.topfilter.warehouseId
      }, 100);
      this.dialogVisible = true;
    },
    startClac() {
      this.expressCompanyId = null
      this.prosimstate = null
      this.warehouse = null
      this.createTime = null
      this.ruleBatchNumber = null
      this.dayBatchNumber = null
      this.expressCompanyId = this.topfilter.expressCompanyId ? (this.topfilter.expressCompanyId).toString() : null
      setTimeout(async() => {
        this.getprosimstatelist(1)
        this.prosimstate = this.topfilter.prosimstateId
        this.warehouse = this.topfilter.warehouseId
      }, 100);
      this.dialogVisible2 = true;
    },
    async onDeleteOperation(row) {
      this.$confirm('是否删除该批次数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteDayExpressInfoData({ batchNumber: row.batchNumber ,inportDate:row.inportDate })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async onDayExpressCompanyFeeCalculate(row) {
      this.$confirm('确认计算?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.$message.success('计算提交成功，请稍后到账单明细页签查询')
        await dayExpressCompanyFeeCalculate({ batchNumber: row.batchNumber })
      }).catch(() => {
        this.$message.info('已取消计算')
      });
    },

    async clacDay(row) {
      if(!this.createTime){
        this.$message({ message: "请选择时间", type: "warning" });
        return false;
      }
      this.$confirm('确认计算?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.$message.success('计算提交成功，请稍后到账单明细页签查询')

        await dayExpressCompanyFeeCalculate({ inportDate: this.createTime,expressCompanyId:this.expressCompanyId,warehouse:this.warehouse
          ,prosimstate:this.prosimstate,ruleBatchNumber:this.ruleBatchNumber,dayBatchNumber:this.dayBatchNumber })
      }).catch(() => {
        this.$message.info('已取消计算')
      });
    },

    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async changeTime2(e) {
      this.ListInfo.startCreateTime = e ? e[0] : null
      this.ListInfo.endCreateTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      // if (this.timeRanges && this.timeRanges.length == 0) {
      //   //默认给近7天时间
      //   this.ListInfo.startCreateTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      //   this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
      //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      // }
      this.loading = true
      const params = { ...this.ListInfo, expressName: this.topfilter.expressCompanyId, prosimstate: this.topfilter.prosimstateId, warehouse: this.topfilter.warehouseId }
      const { data, success } = await getDayExpressInfoData(params)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        // this.summaryarry = data.summary

        let summary = data.summary || {}

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
            resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
            const options = {
                useGrouping: true,
            };
            return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getprosimstatelist (val) {

                var id;
                if (val == 1)
                   {
                    id = this.expressCompanyId ? this.expressCompanyId : this.importConfirmZt.expressCompanyId ? this.importConfirmZt.expressCompanyId : null
                    this.prosimstate=null
                   }
                else if (val == 2) {
                    id = this.ListInfo.expressName
                    this.ListInfo.prosimstate = null
                }

                var res = await getExpressComanyStationName({ id: id });
                if (res?.code) {
                    this.prosimstatelist = res.data
                }
            },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 130px;
    margin-right: 5px;
  }
}
</style>
