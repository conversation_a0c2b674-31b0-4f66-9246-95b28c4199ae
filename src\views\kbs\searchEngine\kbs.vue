<template>
  <div style="padding: 2% 1.2% 1% 1.2%;height: 93%;width: 97.5%;" class="login">
    <div style="display: flex;flex:1;">
      <div style="width: 67%;display: flex;align-items: center;">
        <!-- <el-input style="width: 300px;height: 50px;" v-model="ListInfo.keyword" placeholder="验证码">
          <template #suffix>
            <el-button type="primary" style="height: 45px;">获取动态码</el-button>
          </template>
</el-input> -->

        <el-input v-model.trim="ListInfo.keyword" placeholder="请输入您想要的内容" maxlength="50" class="custom-input no-inputborder"
          style="width: 100%;" @keyup.enter.native="getList()">
          <!-- <el-button slot="append" type="primary" icon="el-icon-search" @click="getList">搜索</el-button> -->
          <el-button slot="suffix" class="icon-label-search" type="primary"
            style="margin-top: 5px;height: 50px;width: 130px;" @click="getList">
            <span style="font-weight: bold;font-size: 17px;">搜索</span>
          </el-button>
        </el-input>
        <!-- <el-button type="primary" style="height: 55px;width: 10%;font-size: 20px;" icon="el-icon-search">搜索</el-button> -->
      </div>
      <div style="width: 33%;display: flex;align-items: center;">
        <span class="askquestion" @click="onDataStatistics" v-if="checkPermission('DataStatisticsAuthority')">
          <el-icon name="my-statistics" class="askquestion_icon"></el-icon>
          <span>数据统计</span>
        </span>
        <span class="askquestion" @click="openQuestionLog">
          <el-icon name="my-message" class="askquestion_icon"></el-icon>
          <span>提问</span>
        </span>
        <span class="askquestion" @click="labelMaintenanceMethod(2)">
          <el-icon name="Label-maintenance" class="askquestion_icon" style="margin-right: 5px;"></el-icon>
          <span>标签维护</span>
        </span>
         <span class="askquestion" @click="openQuestionMaintainLog" v-if="checkPermission('ProblemMaintenance')">
          <el-icon name="Problem-maintenance" class="askquestion_icon"></el-icon>
          <span>问题维护</span>
        </span>
      </div>
    </div>
    <div style="display: flex; flex: 2;height: 84%;margin-top: 2.3%;">
      <div style="flex: 0 0 30%; height: 100%; background-color: white;padding: 1%;border-radius: 10px;">
        <div class="title" style="justify-content: space-between;">
          <span class="icon icon-label-maintenance"></span>
          <span class="title-text">最近沟通问题</span>
          <div class="right-container">
            <span class="icon icon-label-Frame" style="height: 15px;"></span>
            <span class="reply-text">
              <span style="cursor: pointer;" @click="onWaitReply">待回复</span>
              <span class="circle-badge">{{ unreplyCount ?? '' }}</span>
            </span>
          </div>
        </div>
        <el-scrollbar class="scroll-container">
          <div v-for="(item, index) in problemData" :key="index" class="item">
            <div class="item-header">
              <div class="item-image">
                <!--<el-image class="circle-image" :src="item.questionAvatar" :preview-src-list="item.img"></el-image>-->
                <el-image class="circle-image" :src="item?.questionAvatar || ''"></el-image>
              </div>
              <div class="item-content" style="justify-content: space-between;">
                <div class="item-info">
                  <span class="name">{{ item?.questionCreateUserName || '' }}</span>
                  <span class="department">{{ item?.questionDeptName || '' }}</span>
                  <div class="right-content">
                    <el-tag v-if="item?.valueTag" style="margin-right: 10px;cursor: pointer;" @click="onLabelSkiproute(item)">
                      {{ item.valueTag }}
                    </el-tag>
                  </div>
                </div>
                <div class="item-description">
                  <span v-if="item.images && item.images.length > 0" style="display: block; margin-bottom: 5px;">
                    <el-image v-for="(image, indexss) in item.images" :key="indexss" :src="image" :class="{ 'pointer-cursor': isSpecialImage(image) }"
                      style="width: 55px; height: 55px; margin-right: 5px; border-radius: 5px;" :preview-src-list="isSpecialImage(image) ? [] : item.images" @click="handleImageClick(image,item.id)">
                    </el-image>
                  </span>
                  <span v-html="sanitizeHTML(item?.question || '')"></span>
                  <span class="logtag">{{ item?.tags || '' }}</span>
                </div>
                <div class="item-footer">
                  <div>{{ item?.questionCreateTime || '' }}
                    <span v-if="item.isAnswered === 0 || item.isAnswered === 1">
                      <el-tag :type="item.isAnswered === 0 ? 'warning' : ''" effect="plain" style="margin-left: 10px;">
                        {{ item.isAnswered === 0 ? '未回复' : '已回复' }}
                      </el-tag>
                    </span>
                  </div>
                  <div class="actions">
                    <span class="icon icon-label-dot-square" style="margin-right: 5px;height: 14px;"
                      @click="onRecover(item, 1)"></span>
                    <span style="cursor: pointer;" @click="onRecover(item, 1)" class="reply-span" :class="{ 'reply-active': item.communicationAnswer }">{{ !item.communicationAnswer ? '回复' : '取消回复' }}
                    </span>
                    <span class="reply-span" :class="{ 'reply-active': item.communicationAnswer }" style="margin-right: 3px;">{{ item?.commentCounts || '' }}</span>
                    <span :class="['icon', 'icon-label-praise', { 'icon-label-acquiesce': item?.isUp === 0 }]"
                      style="height: 16px;" @click="onUpConfirmation(item, 1)"></span>
                    <span class="rightmargin" :style="{ color: item?.isUp == 1 ? '#409EFF' : '' }">赞</span>
                    <span class="rightmargin" :style="{ color: item?.isUp == 1 ? '#409EFF' : '' }">{{ item?.upCounts ?? '' }}</span>
                    <!-- <span :class="['icon', 'icon-label-disfavor', { 'icon-fill-red': isRed }]"></span>
                    <span class="rightmargin">down</span>
                    <span class="rightmargin">{{ item.upCounts }}</span> -->
                  </div>
                </div>
              </div>
            </div>
            <div class="more-item" style="display: flex; flex-direction: column;" v-show="item.communicationAnswer">
              <div style="border: 1px solid #dcdfe6; border-radius: 4px; background-color: white;  width: 100%; position: relative;" >
                <div style="width: 100%;">
                <el-input :id="'refCommunication' + index" v-model="item.content" :key="index" type="textarea"
                  show-word-limit placeholder="请输入" maxlength="200" clearable @input="inputchangeEit($event, item, 1)"
                  @blur="blurinput(item, 1)" class="no-border" :autosize="{ minRows: 4, maxRows: 4}"/>
              </div>
              <div style="background-color: white; width: 100%; display: flex; align-items: center;">
                <el-upload action="/api/uploadnew/file/UploadCommonFileAsync" :limit="19" style="margin-left: 5px;" ref="upload1"
                  :on-success="(response) => handleSuccess(response, item, 1)" :file-list="picFileList" multiple
                  :show-file-list="false" accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                  <i class="el-icon-picture-outline"></i>
                </el-upload>
                <div style="overflow: hidden;display: flex;width: 100%;">
                  <div class="imageList_box" style="margin-right: 10px;">
                    <div class="imageList" v-if="item.privateImagesinput" v-for="(itemss, i) in item.privateImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="imgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="item.privateImagesinput"></el-image>
                      <span class="del" @click="delImg(item, i ,1,'encipher',index)">x</span>
                  </div>
                  </div>
                  <div class="imageList_box">
                    <div class="imageList" v-if="item.publicityImagesinput" v-for="(itemss, i) in item.publicityImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="imgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="item.publicityImagesinput"></el-image>
                      <span class="del" @click="delImg(item, i ,1,'publicity',index)">x</span>
                  </div>
                  </div>
                </div>
                <span style="margin-left: auto;">
                  <el-button type="primary" @click="onRecoveMethodr(item, 1)">发送</el-button>
                </span>
              </div>

              <div style="background-color: white; padding-bottom: 3px;">
                <span style="margin-right: 5%;margin-left: 5px;color: #cdcdcd;cursor: pointer;" @click="onEitRecoverMethod(item, 1)">@</span>
                <el-radio @input="inputchange" v-model="item.needApply" label="1"><span
                    style="font-size: 12px;">申请查看</span></el-radio>
                <el-radio @input="inputchange" v-model="item.needApply" label="0" style="margin-left: 20%;"><span
                    style="font-size: 12px;">图片公开</span></el-radio>
              </div>
              </div>
            </div>
            <div v-for="(more, indexs) in item?.comments || []" :key="indexs" class="more-item">
              <div style="display: flex; align-items: flex-start;">
                <div class="more-image">
                  <el-image class="circle-image" :src="more?.avatar || ''"></el-image>
                </div>
                <div class="more-content">
                  <div class="more-info">
                    <span class="name">{{ more?.replyUserName || '' }}</span>
                    <span class="department">{{ more?.replyDeptName || '' }}</span>
                  </div>
                  <div class="more-description">
                    <span v-if="more.images" style="display: block; margin-bottom: 5px;">
                      <el-image v-for="(image, indexss) in more.images" :key="indexss" :src="image" :class="{ 'pointer-cursor': isSpecialImage(image) }"
                        style="width: 55px; height: 55px; margin-right: 5px; border-radius: 5px;" :preview-src-list="isSpecialImage(image) ? [] : more.images" @click="handleImageClick(image,more.id)">
                      </el-image>
                    </span>
                    <span v-show="more.sourceUserName">回复：</span>
                    <span v-show="more.sourceUserName" style="margin-right: 5px;color: #409EFF;">{{ more.sourceUserName
                      }}</span>
                    <span @click="onEditingMethod(more, 1, item)" :style="{ cursor: checkPermission('EditReplyPermission') ? 'pointer' : 'default' }">{{ more?.content || '' }}</span>
                  </div>
                  <div class="more-footer" style="display: flex;align-items: center;justify-content: space-between;">
                    <span>{{ more?.replyCreateTime || '' }}</span>
                    <div style="display: flex;align-items: center;">
                      <span class="icon icon-label-dot-square" @click="onChildcover(more, 1)"
                        style="margin-right: 5px;height: 14px;"></span>
                      <span style="cursor: pointer;" @click="onChildcover(more, 1)" class="reply-span" :class="{ 'reply-active': more.communicationSubAnswer }">{{ !more.communicationSubAnswer ? '回复' : '取消回复' }}
                      </span>
                      <span :class="['icon', more.isPraise == 1 ? 'icon-label-accepted' : 'icon-label-accept']"
                        @click="onadoptVerify(more, 1, 1)" style="height: 14px;"></span>
                      <span class="work-out" @click="onadoptVerify(more, 1, 1)" :style="{ color: more?.isPraise == 1 ? '#409EFF' : '' }">已解决</span>
                      <span class="reply-span" :class="{ 'reply-active': item.communicationSubAnswer }" :style="{ color: more?.isPraise == 1 ? '#409EFF' : '' }" style="margin-right: 3px;">{{ more?.praiseCount ?? '' }}</span>
                      <span :class="['icon', more.isUnsolved == 1 ? 'icon-label-rejected' : 'icon-label-reject']"
                        @click="onadoptVerify(more, 1, 0)" style="height: 14px;"></span>
                      <span class="work-out" @click="onadoptVerify(more, 1, 0)" :style="{ color: more?.isUnsolved == 1 ? '#409EFF' : '' }">未解决</span>
                      <span class="reply-span" :class="{ 'reply-active': item.communicationSubAnswer }" :style="{ color: more?.isUnsolved == 1 ? '#409EFF' : '' }" style="margin-right: 3px;">{{ more?.unsolvedCount ?? '' }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="more.communicationSubAnswer"
                style="display: flex; flex-direction: column;margin-top: 10px;padding-left: 11%;">
              <div style="border: 1px solid #dcdfe6;border-radius: 4px;background-color: white">
                <div style="width: 100%;">
                  <el-input :id="'refChildRecove' + indexs + 'aa'" v-model="more.contents" :key="indexs" type="textarea"
                    @blur="blurChildinput(item, 1, more.contents)" show-word-limit placeholder="请输入" maxlength="200"
                    clearable @input="inputchangeEit($event,item , 3,more)" class="no-border" :autosize="{ minRows: 4, maxRows: 4}"/>
                </div>
                <div
                  style="background-color: white; margin-top: 10px; width: 100%; display: flex; align-items: center;">
                  <el-upload action="/api/uploadnew/file/UploadCommonFileAsync" :limit="19" style="margin-left: 5px;" ref="upload3"
                    :on-success="(response) => handleSuccess(response, more, 3)" :file-list="picFileList" multiple
                    :show-file-list="false" accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                    <i class="el-icon-picture-outline"></i>
                  </el-upload>
                  <div style="overflow: hidden;display: flex;width: 100%;">
                  <div class="imageList_box" style="margin-right: 10px;">
                    <div class="childimageList" v-if="more.privateImagesinput" v-for="(itemss, i) in more.privateImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="childimgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="more.privateImagesinput"></el-image>
                      <span class="del" @click="delImg(more, i ,3,'encipher',index)">x</span>
                  </div>
                  </div>
                  <div class="imageList_box">
                    <div class="childimageList" v-if="more.publicityImagesinput" v-for="(itemss, i) in more.publicityImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="childimgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="more.publicityImagesinput"></el-image>
                      <span class="del" @click="delImg(more, i ,3,'publicity',index)">x</span>
                  </div>
                  </div>
                </div>
                  <span style="margin-left: auto;">
                    <el-button type="primary" @click="onRecoveMethodr(more, 3, item.bizId)">发送</el-button>
                  </span>
                </div>

                <div style="background-color: white; padding-bottom: 3px;">
                <span style="margin-right: 5%;margin-left: 5px;color: #cdcdcd;cursor: pointer;" @click="onEitRecoverMethod(item, 3,more)">@</span>
                <el-radio @input="inputchange" v-model="more.needApply" label="1"><span
                    style="font-size: 12px;">申请查看</span></el-radio>
                <el-radio @input="inputchange" v-model="more.needApply" label="0" style="margin-left: 20%;"><span
                    style="font-size: 12px;">图片公开</span></el-radio>
              </div>
                </div>
              </div>
            </div>
            <div class="more-actions" v-show="item?.comments && item.comments.length > 2">
              <span @click="!item.seeMoreCheck ? onSeeMoreMethod(item, 'left') : collapseComments(item,'left')"
                style="cursor: pointer;">
                {{ !item.seeMoreCheck ? '查看更多' : '折叠隐藏' }}
                <i class="el-icon-arrow-down" v-if="!item.seeMoreCheck"></i>
                <i class="el-icon-arrow-up" v-else></i>
              </span>
            </div>
            <!-- <div v-for="(more, indexs) in item?.comments || []" :key="indexs"
              style="display: flex; padding: 10px 0 0 12%;">
              <div style="flex: 0 0 5%; margin-right: 2%; justify-content: center; align-items: center;">
                <el-image style="width: 50px; height: 50px; border-radius: 50%;"
                  :src="item?.replyUserAvatar || ''"></el-image>
              </div>
              <div style="flex: 1;">
                <div style="margin: 4% 0;">
                  <span>{{ item?.title || '' }}</span>
                  <span style="margin-left: 5px;">{{ item?.numbele || '' }}</span>
                </div>
                <div style="padding-bottom: 2%; white-space: normal;">
                  <span>{{ item?.sldjkf || '' }}</span>
                </div>
                <div style="color: #cdcdcd; display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ item?.timg || '' }}</span>
                  <div style="display: flex;align-items: center;">
                    <span class="icon icon-label-accept"></span>
                    <span class="rightmargin">采纳</span>
                    <span class="rightmargin">{{ item?.praiseCount ?? ''}}</span>
                    <span class="icon icon-label-reject"></span>
                    <span class="rightmargin">不采纳</span>
                    <span class="rightmargin">{{ item?.praiseCount ?? ''}}</span>
                  </div>
                </div>
              </div>
            </div>
            <div style="color: #cdcdcd;display: flex;justify-content: center;">查看更多<i class="el-icon-arrow-down"></i>
            </div> -->
          </div>
        </el-scrollbar>
      </div>
      <div
        style="flex: 1 1 auto; margin: 0 1%;height: 100%;  background-color: white; padding: 1%; border-radius: 10px;">
        <div class="title">
          <span class="icon icon-label-questions"></span>
          <span class="title-text">热门答疑</span>
          <span class="buttons">
            <!-- <el-button :type="activeButton === 'latest' ? 'primary' : ''"
              @click="activeButton = 'latest'">最新</el-button>
            <el-button :type="activeButton === 'hot' ? 'primary' : ''" @click="activeButton = 'hot'">热点</el-button> -->
          </span>
        </div>
        <el-scrollbar class="scroll-container">
          <div v-for="(item, index) in questionsData" :key="index" class="item">
            <div class="item-header">
              <div class="item-image">
                <el-image class="circle-image" :src="item?.questionAvatar || ''"></el-image>
              </div>
              <div class="item-content">
                <div class="item-info" style="justify-content: space-between;">
                  <span class="name">{{ item?.questionCreateUserName || '' }}</span>
                  <span class="department">{{ item?.questionDeptName || '' }}</span>
                  <div class="right-content">
                    <el-tag v-if="item.isResolved === 0 || item.isResolved === 1" :type="item.isResolved === 0 ? 'warning' : ''" style="margin-right: 10px;">
                      {{ item.isResolved === 0 ? '未解决' : '已解决' }}
                    </el-tag>
                  </div>
                </div>
                <div class="item-description">
                  <span v-if="item.images && item.images.length > 0" style="display: block; margin-bottom: 5px;">
                    <el-image v-for="(image, indexss) in item.images" :key="indexss" :src="image" :class="{ 'pointer-cursor': isSpecialImage(image) }"
                      style="width: 55px; height: 55px; margin-right: 5px; border-radius: 5px;" :preview-src-list="isSpecialImage(image) ? [] : item.images" @click="handleImageClick(image,item.id)">
                    </el-image>
                  </span>
                  <span v-html="sanitizeHTML(item?.question || '')"></span>
                  <!-- <imagePreview ref="imagePreview" /> -->
                  <span class="logtag">{{ item?.tags || '' }}</span>
                </div>
                <div class="item-footer">
                  <div>{{ item?.questionCreateTime || '' }}</div>
                  <div class="actions">
                    <span class="icon icon-label-dot-square" style="margin-right: 5px;height: 14px;"
                      @click="onRecover(item, 2)"></span>
                    <span style="cursor: pointer;" @click="onRecover(item, 2)" class="reply-span" :class="{ 'reply-active': item.recoverAnswer }">{{ !item.recoverAnswer ? '回复' : '取消回复' }}
                    </span>
                    <span class="reply-span" :class="{ 'reply-active': item.recoverAnswer }" style="margin-right: 3px;">{{ item?.commentCounts || '' }}</span>
                    <span :class="['icon', 'icon-label-praise', { 'icon-label-acquiesce': item?.isUp === 0 }]"
                      style="height: 16px;" @click="onUpConfirmation(item, 2)"></span>
                    <span class="rightmargin" :style="{ color: item?.isUp == 1 ? '#409EFF' : '' }">赞</span>
                    <span class="rightmargin" :style="{ color: item?.isUp == 1 ? '#409EFF' : '' }">{{ item?.upCounts ?? '' }}</span>
                    <!-- <span :class="['icon', 'icon-label-disfavor', { 'icon-fill-red': isRed }]"></span>
                    <span class="rightmargin">{{ item.numbele }}</span> -->
                  </div>
                </div>
              </div>
            </div>
            <!--  -->
            <div class="more-item" style="display: flex; flex-direction: column;" v-show="item.recoverAnswer">
              <div style="border: 1px solid #dcdfe6;border-radius: 4px;background-color: white">
              <div style="width: 100%;">
                <el-input :id="'refRecove' + index" v-model="item.content" :key="index" type="textarea"
                  @blur="blurinput(item, 2)" show-word-limit placeholder="请输入" maxlength="200" clearable
                  @input="inputchangeEit($event, item, 2)" class="no-border" :autosize="{ minRows: 4, maxRows: 4}"/>
              </div>
              <div style="background-color: white; width: 100%; display: flex; align-items: center;">
                <el-upload action="/api/uploadnew/file/UploadCommonFileAsync" :limit="19" style="margin-left: 5px;" ref="upload2"
                  :on-success="(response) => handleSuccess(response, item, 2)" :file-list="picFileList" multiple
                  :show-file-list="false" accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                  <i class="el-icon-picture-outline"></i>
                </el-upload>
                <div style="overflow: hidden;display: flex;width: 100%;">
                  <div class="imageList_box" style="margin-right: 10px;">
                    <div class="imageList" v-if="item.privateImagesinput" v-for="(itemss, i) in item.privateImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="imgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="item.privateImagesinput"></el-image>
                      <span class="del" @click="delImg(item, i ,2,'encipher',index)">x</span>
                  </div>
                  </div>
                  <div class="imageList_box">
                    <div class="imageList" v-if="item.publicityImagesinput" v-for="(itemss, i) in item.publicityImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="imgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="item.publicityImagesinput"></el-image>
                      <span class="del" @click="delImg(item, i ,2,'publicity',index)">x</span>
                  </div>
                  </div>
                </div>

                <span style="margin-left: auto;">
                  <el-button type="primary" @click="onRecoveMethodr(item, 2)">发送</el-button>
                </span>
              </div>
              <div style="background-color: white; padding-bottom: 3px;">
                <span style="margin-right: 5%;margin-left: 5px;color: #cdcdcd;cursor: pointer;" @click="onEitRecoverMethod(item, 2)">@</span>
                <el-radio @input="inputchange" v-model="item.needApply" label="1"><span
                    style="font-size: 12px;">申请查看</span></el-radio>
                <el-radio @input="inputchange" v-model="item.needApply" label="0" style="margin-left: 20%;"><span
                    style="font-size: 12px;">图片公开</span></el-radio>
              </div>
            </div>
            </div>
            <div v-for="(more, indexs) in item?.comments || []" :key="indexs" class="more-item">
              <div style="display: flex; align-items: flex-start;">
                <div class="more-image">
                  <el-image class="circle-image" :src="more?.avatar || ''"></el-image>
                </div>
                <div class="more-content">
                  <div class="more-info">
                    <span class="name">{{ more?.replyUserName || '' }}</span>
                    <span class="department">{{ more?.replyDeptName || '' }}</span>
                  </div>
                  <div class="more-description">
                    <span v-if="more.images" style="display: block; margin-bottom: 5px;">
                      <el-image v-for="(image, indexss) in more.images" :key="indexss" :src="image" :class="{ 'pointer-cursor': isSpecialImage(image) }"
                        style="width: 55px; height: 55px; margin-right: 5px; border-radius: 5px;" :preview-src-list="isSpecialImage(image) ? [] : more.images" @click="handleImageClick(image,more.id)">
                      </el-image>
                    </span>
                    <span v-show="more.sourceUserName">回复：</span>
                    <span v-show="more.sourceUserName" style="margin-right: 5px;color: #409EFF;">{{ more.sourceUserName
                      }}</span>
                    <span @click="onEditingMethod(more, 2, item)" :style="{ cursor: checkPermission('EditReplyPermission') ? 'pointer' : 'default' }">{{ more?.content || '' }}</span>
                  </div>
                  <div class="more-footer" style="display: flex;align-items: center;justify-content: space-between;">
                    <span>{{ more?.replyCreateTime || '' }}</span>
                    <div style="display: flex;align-items: center;">
                      <span class="icon icon-label-dot-square" @click="onChildcover(more, 2)"
                        style="margin-right: 5px;height: 14px;"></span>
                      <span style="cursor: pointer;" @click="onChildcover(more, 2)" class="reply-span" :class="{ 'reply-active': more.questionsSubAnswer }">{{ !more.questionsSubAnswer ? '回复' : '取消回复' }}
                      </span>
                      <span :class="['icon', more.isPraise == 1 ? 'icon-label-accepted' : 'icon-label-accept']"
                        @click="onadoptVerify(more, 2, 1)" style="height: 14px;"></span>
                      <span class="work-out" @click="onadoptVerify(more, 2, 1)" :style="{ color: more?.isPraise == 1 ? '#409EFF' : '' }">已解决</span>
                      <span class="reply-span" :class="{ 'reply-active': item.questionsSubAnswer }" :style="{ color: more?.isPraise == 1 ? '#409EFF' : '' }" style="margin-right: 3px;">{{ more?.praiseCount ?? '' }}</span>
                      <span :class="['icon', more.isUnsolved == 1 ? 'icon-label-rejected' : 'icon-label-reject']"
                        @click="onadoptVerify(more, 2, 0)" style="height: 14px;"></span>
                      <span class="work-out" @click="onadoptVerify(more, 2, 0)" :style="{ color: more?.isUnsolved == 1 ? '#409EFF' : '' }">未解决</span>
                      <span class="reply-span" :class="{ 'reply-active': item.questionsSubAnswer }" :style="{ color: more?.isUnsolved == 1 ? '#409EFF' : '' }" style="margin-right: 3px;">{{ more?.unsolvedCount ?? '' }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="more.questionsSubAnswer" style="display: flex;flex-direction: column;padding-left: 11%;">
              <div style="border: 1px solid #dcdfe6;border-radius: 4px;background-color: white">
                <div style="width: 100%;">
                  <el-input :id="'refQuestionsRecove' + indexs + 'aa'" v-model="more.contents" :key="indexs"
                    type="textarea" @blur="blurChildinput(item, 2, more.contents)" show-word-limit placeholder="请输入"
                    maxlength="200" clearable @input="inputchangeEit($event,item , 4,more)" class="no-border" :autosize="{ minRows: 4, maxRows: 4}"/>
                </div>
                <div
                  style="background-color: white; margin-top: 10px; width: 100%; display: flex; align-items: center;">
                  <el-upload action="/api/uploadnew/file/UploadCommonFileAsync" :limit="19" style="margin-left: 5px;" ref="upload4"
                    :on-success="(response) => handleSuccess(response, more, 4)" :file-list="picFileList" multiple
                    :show-file-list="false" accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                    <i class="el-icon-picture-outline"></i>
                  </el-upload>
                  <div style="overflow: hidden;display: flex;width: 100%;">
                  <div class="imageList_box" style="margin-right: 10px;">
                    <div class="childimageList" v-if="more.privateImagesinput" v-for="(itemss, i) in more.privateImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="childimgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="item.privateImagesinput"></el-image>
                      <span class="del" @click="delImg(more, i ,4,'encipher',index)">x</span>
                  </div>
                  </div>
                  <div class="imageList_box">
                    <div class="childimageList" v-if="more.publicityImagesinput" v-for="(itemss, i) in more.publicityImagesinput" :key="i"
                    style="display: flex; align-items: center; margin-left: 5px;">
                    <el-image class="childimgcss" style="width: 45px; height: 45px; border-radius: 5px;" :src="itemss"
                      :preview-src-list="more.publicityImagesinput"></el-image>
                      <span class="del" @click="delImg(more, i ,4,'publicity',index)">x</span>
                  </div>
                  </div>
                </div>
                  <span style="margin-left: auto;">
                    <el-button type="primary" @click="onRecoveMethodr(more, 4, item.bizId)">发送</el-button>
                  </span>
                </div>
                <div style="background-color: white; padding-bottom: 3px;">
                  <span style="margin-right: 5%;margin-left: 5px;color: #cdcdcd;cursor: pointer;" @click="onEitRecoverMethod(item, 4,more)">@</span>
                <el-radio @input="inputchange" v-model="more.needApply" label="1"><span
                    style="font-size: 12px;">申请查看</span></el-radio>
                <el-radio @input="inputchange" v-model="more.needApply" label="0" style="margin-left: 20%;"><span
                    style="font-size: 12px;">图片公开</span></el-radio>
              </div>
                </div>
              </div>
            </div>
            <div class="more-actions" v-show="item?.comments && item.comments.length > 2">
              <span @click="!item.seeMoreCheck ? onSeeMoreMethod(item, 'right') : collapseComments(item,'right')"
                style="cursor: pointer;">
                {{ !item.seeMoreCheck ? '查看更多' : '折叠隐藏' }}
                <i class="el-icon-arrow-down" v-if="!item.seeMoreCheck"></i>
                <i class="el-icon-arrow-up" v-else></i>
              </span>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div style="flex: 0 0 30%; height: 100%; background-color: white;padding: 1%;border-radius: 10px;">
        <div class="title">
          <span class="icon icon-label-problem"></span>
          <span class="title-text">热门问题</span>
          <span class="buttons">
            <!-- <el-button :type="activeButton === 'latest' ? 'primary' : ''"
              @click="activeButton = 'latest'">最新</el-button>
            <el-button :type="activeButton === 'hot' ? 'primary' : ''" @click="activeButton = 'hot'">热点</el-button> -->
          </span>
        </div>
        <el-scrollbar class="scroll-container">
          <div v-for="(item, index) in heatListData" :key="index" class="item">
            <div class="item-header">
              <div class="item-number">
                <span :style="{
                  backgroundColor: index == 0 ? '#fe2d46' : index == 1 ? '#f60' : index == 2 ? '#faa90e' : '#9195a3',
                  color: '#fff',
                  fontWeight: '700',
                  width: '20px',
                  display: 'inline-block',
                  borderRadius: '5px',
                  margin: '0 5px 0 0',
                  textAlign: 'center'
                }">
                  {{ index + 1 }}
                </span>
              </div>
              <div class="item-content">
                <div class="item-description">
                  <!-- <span>{{ item.title }}</span> -->
                  <span v-html="sanitizeHTML(item?.title || '')"></span>
                  <span class="logtag">{{ item.tag }}</span>
                </div>
                <div class="item-description description-limited">
                  <span v-html="sanitizeHTML(item?.description || '')"></span>
                </div>
                <div class="item-description" v-if="item.images && item.images.length > 0">
                  <el-image v-for="(image, indexss) in item.images" :key="indexss" :src="image"
                    style="width: 55px; height: 55px; margin-right: 5px; border-radius: 5px;" :preview-src-list="item.images">
                  </el-image>
                </div>
                <!-- <div class="item-footer">
                  <div>{{ item.timg }}</div>
                  <div class="actions">
                    <i class="el-icon-chat-dot-square" style="margin-right: 5px;"></i>
                    <span class="rightmargin">{{ item.numbele }}</span>
                    <span :class="['icon', 'icon-label-praise', { 'icon-fill-blue': isBlue }]"></span>
                    <span class="rightmargin">{{ item.numbele }}</span>
                    <span :class="['icon', 'icon-label-disfavor', { 'icon-fill-red': isRed }]"></span>
                    <span class="rightmargin">{{ item.numbele }}</span>
                  </div>
                </div> -->
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <el-dialog :visible.sync="questionDialogVisible" custom-class="questionDialog" width="40%" v-dialogDrag>
      <div slot="title">
        提问
        </div>
      <el-form style="margin-top:20px" label-position='left' label-width="110px" :model="ruleForm" :rules="rules" ref="ruleForm">
            <el-form-item label="内容:" prop="content">
                  <el-input v-model="ruleForm.content"  type="textarea" placeholder="请输入" maxlength="300" clearable
                    style="width: 100%;" />
             </el-form-item>
             <el-form-item label="添加申请图片:" prop="imgsPrivate">
                    <div class="questionImg">
                      <uploadimgFile ref="uploadimgCallBackFilePrivate" :accepttyes="accepttyes" :isImage="true"
                          :uploadInfo="imgUrlsPrivate" :keys="[1, 1]"  @callback="getImgsCallBackPrivate"
                          :imgmaxsize="5" :limit="5" :multiple="true">
                      </uploadimgFile>
                      <span class="imgTips">点击灰框可直接贴图</span>
                </div>
                </el-form-item>
                <el-form-item label="添加公开图片:" style="margin:30px 0;" prop="imgsPublic">
                    <div class="questionImg">
                      <uploadimgFile ref="uploadimgCallBackFilePublic" :accepttyes="accepttyes" :isImage="true"
                          :uploadInfo="imgUrlsPublic" :keys="[1, 1]"  @callback="getImgsCallBackPublic"
                          :imgmaxsize="5" :limit="5" :multiple="true">
                      </uploadimgFile>
                      <span class="imgTips">点击灰框可直接贴图</span>
                </div>
                </el-form-item>
                  <el-form-item prop="beAskedUserIds" label="指定回答人:" >
                    <el-select
                    v-model="ruleForm.beAskedUserIds"
                    multiple
                    filterable
                    default-first-option
                    collapse-tags
                    reserve-keyword
                     remote
                     :remote-method="remoteMethod"
                    :loading="loading"
                    placeholder="请选择回答人员">
                    <el-option
                      v-for="item in personList"
                      :key="item.userId"
                      :label="item.name +'-'+ item.posts"
                      :value="item.userId">
                    </el-option>
                  </el-select>
                  </el-form-item>
                  <el-form-item label="标签:" >
                    <el-select v-model="ruleForm.linkId" placeholder="请选择标签" clearable filterable @change="changeTag">
                      <el-option v-for="item in tagMaintenanceData" :key="item.id" :label="item.value" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>



          </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handInQuestion('取消')">
          取 消
          </el-button>
        <el-button type="primary" @click="handInQuestion('提交')">
          提 交
          </el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="questionMaintainDialogVisible"  width="50%"  v-dialogDrag>
      <div slot="title">
        问题维护
        </div>
         <div>
         <div style="height:300px">
            <vxetablebase :id="'kbs20240807'" :tablekey="'kbs20240807'" :tableData='tableData' :tableCols='tableCols'
          :loading='listLoading' :border='true' :that="that"
          ref="vxetable"  :showsummary='false'   :hasSeq="false">
            <template #colslot="{col}">
                    <template v-if="col['images'].length>1">
                                 <el-badge :value="col['images'].length" style="margin-top:10px;">
                                        <el-image  class="imgstyle" :src="col['images']?col['images'][0]:imagedefault" fit="fill" :preview-src-list="col['images']">
                                        </el-image>
                                    </el-badge>
                      </template>
                      <template v-else>
                          <el-image  class="imgstyle" :src="col['images']?col['images'][0]:imagedefault" fit="fill" :preview-src-list="col['images']">

                              <div slot="error" class="image-slot">
                                  <el-image></el-image>
                              </div>
                          </el-image>
                      </template>
                </template>

          </vxetablebase>
         </div>
           <my-pagination ref="pager" :total="total" :checked-count="sels.length" :pageSize="10" :sizes="sizes" @get-page="getQuestionList"/>
           <el-form style="margin-top:20px" label-position='left' label-width="110px" :model="questionMaintainRuleForm" :rules="rules" ref="questionMaintainRuleForm">
            <!-- <el-form-item label="标签:" prop="label">
                  <el-select v-model="ruleForm.label" clearable placeholder="请选择">
                     <el-option
                        v-for="item in labelList"
                        :key="item.userId"
                        :label="item.value"
                        :value="item.userId"
                        >
                      </el-option>
                </el-select>
             </el-form-item> -->
            <el-form-item label="问题:" prop="content">
                  <el-input v-model="questionMaintainRuleForm.content"  placeholder="请输入" maxlength="100" clearable
                    style="width: 100%;" />
             </el-form-item>
            <el-form-item label="添加申请图片:" prop="imgsPrivate">
                    <div class="questionImg">
                      <uploadimgFile ref="uploadimgFilePrivate" :accepttyes="accepttyes" :isImage="true"
                          :uploadInfo="imgUrlsPrivate" :keys="[1, 1]"  @callback="getImgsPrivate"
                          :imgmaxsize="5" :limit="5" :multiple="true">
                      </uploadimgFile>
                      <span class="imgTips">点击灰框可直接贴图</span>
                </div>
                </el-form-item>
                <el-form-item label="添加公开图片:" style="margin-top:30px" prop="imgsPublic">
                    <div class="questionImg">
                      <uploadimgFile ref="uploadimgFilePublic" :accepttyes="accepttyes" :isImage="true"
                          :uploadInfo="imgUrlsPublic" :keys="[1, 1]"  @callback="getImgsPublic"
                          :imgmaxsize="5" :limit="5" :multiple="true">
                      </uploadimgFile>
                      <span class="imgTips">点击灰框可直接贴图</span>
                </div>
                </el-form-item>


          </el-form>

         </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handInQuestionMaintain('取消')">
          取 消
          </el-button>
        <el-button type="primary" @click="handInQuestionMaintain('确认')">
          确 认
          </el-button>
      </div>
    </el-dialog>

    <el-dialog title="标签维护" :visible.sync="labelMaintenance" width="30%" v-dialogDrag>
      <div style="height: 300px;width: 100%;">
        <el-scrollbar style="height: 100%;margin: 3% 0;">
          <div v-for="(item, i) in tagMaintenanceData" :key="i" style="width: 100%;display: flex;align-items: center;margin: 5px 0;">
            <el-input v-model.trim="item.value" placeholder="请输入标签名" maxlength="50" clearable style="width: 40%;" />
            <el-input v-model.trim="item.linkValue" placeholder="请输入链接" maxlength="50" clearable style="width: 40%;margin: 0 4%;" />
            <span style="color: red;cursor: pointer;">
              <i class="el-icon-delete" style="font-size: 15px;margin-right: 3px;"></i>
              <el-button type="text" class="red-text" @click="onlabelremovalMethod(item,i)">删除</el-button>
            </span>
          </div>
        </el-scrollbar>
      </div>
      <div style="margin-top: 15px;">
        <el-button type="primary" plain @click="onAddLabelMethod">新增标签</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
      <el-button @click="labelMaintenance = false">取 消</el-button>
      <el-button type="primary" @click="onMaintenanceCommit()">提 交</el-button>
    </span>
    </el-dialog>

    <el-dialog title="选择人员" :visible.sync="selector.selectordialogVisible" width="20%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 100px;display: flex;align-items: center;justify-content: center;">
        <span>人员：</span>
        <el-select v-model="selector.eitbeAskedUserIds" multiple filterable default-first-option collapse-tags @change="onEitbeAskedUserIdsChange" ref="eitSelectRef"
          reserve-keyword remote :remote-method="eitremoteMethod" placeholder="请选择被艾特人员" style="width: 70%;">
          <el-option v-for="item in selector.eitpersonList" :key="item.userId" :label="item.name + '-' + item.posts"
            :value="item.userId">
          </el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selector.selectordialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethod">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="编辑回复" :visible.sync="editreply.editreplyDialogVisible" width="30%" v-dialogDrag>
      <div style="height: 150px;">
        <el-input type="textarea":autosize="{ minRows: 8, maxRows: 10}"placeholder="请输入内容" v-model="editreply.content" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editreply.editreplyDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onEditreplyMethod">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="数据统计" :visible.sync="dataStatisticsVisible" width="50%" v-dialogDrag>
      <div style="height: 600px;width: 98%;border: 1px solid #dcdfe6;padding: 2% 1%;">
        <div>
          <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
          </el-date-picker>
          <el-select v-model="details.userNo" filterable default-first-option reserve-keyword remote
            :remote-method="detailsMethod" placeholder="请选择姓名" clearable>
            <el-option v-for="item in detailsPersonList" :key="item.userId" :label="item.name + '-' + item.posts"
              :value="item.userId">
            </el-option>
          </el-select>
          <el-button type="primary" style="margin: 0 5px;" @click="onDataStatistics()">搜索</el-button>
          <el-button @click="onViewDetails">查看详情</el-button>
        </div>
        <div style="display: flex;width: 100%;margin-top: 10px;">
          <div style="flex: 1;width: 50%;">
            <span>汇总数据</span>
            <buscharpie :charid="'charNegative1'" :thisStyle="thisStyle" :gridStyle="gridStyle" :piesuspension="true"
              :analysisData="chartData1" v-if="chartData1" :isshowbai="false"></buscharpie>
          </div>
          <div style="flex: 1;width: 50%;">
            <span>提问数据</span>
            <buscharpie :charid="'charNegative2'" :thisStyle="thisStyle" :gridStyle="gridStyle" :piesuspension="true"
              :analysisData="chartData2" v-if="chartData2" :isshowbai="false"></buscharpie>
          </div>
        </div>
        <div style="display: flex;width: 100%;margin-top: 10px;">
          <div style="flex: 1;width: 50%;">
            <span>已回复/待回复</span>
            <barcharpie :charid="'charNegative3'" :thisStyle="thisStyle" :gridStyle="gridStyle" :piesuspension="true"
              :analysisData="chartData3" v-if="chartData3" :isshowbai="false"></barcharpie>
          </div>
          <div style="flex: 1;width: 50%;">
            <span>已解决数据/未解决</span>
            <barcharpie :charid="'charNegative4'" :thisStyle="thisStyle" :gridStyle="gridStyle" :piesuspension="true"
              :analysisData="chartData4" v-if="chartData4" :isshowbai="false"></barcharpie>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="查看详情" :visible.sync="viewDetailsVisible" width="45%" v-dialogDrag append-to-body>
      <div style="height: 450px;">
        <viewDetails v-if="viewDetailsVisible" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
// import { keyWordSearch, kbsEsTagPage, kbsRetrieveLogsave, kbsHotListlist, kbsEsTagSubmit, kbsEsTagremove } from '@/api/bladegateway/kbsSearch.js';
import YhQuillEditor from '@/components/text-editor/yh-quill-editor.vue'
import { saveQuestion } from "@/api/kbs/qa.js"
import { keyWordSearch } from '@/api/kbs/kbsSearch.js';
import { latestList, replyQuestion, adoptAnswer, upQuestion ,maintainQuestionSave,hotList,deleteQuestion, applyImg, replyList, update, unRepliedCount, updateReply} from '@/api/kbs/qa'
// import imagePreview from '@/components/kbs/searchEngine/imagePreview.vue'
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import { getLoginInfo } from '@/api/admin/auth'
import { employeeList } from '@/api/bladegateway/xzgateway.js';
import { batchSubmit, pageList, remove, kbsTaglist, statistic, questionStatistic, replyStatistic, solvedStatistic } from "@/api/order/orderData";
import checkPermission from '@/utils/permission'
import dayjs from 'dayjs'
import middlevue from "@/store/middle.js"
import buscharpie from "@/views/order/LossOrderCostStatistics/childPages/piebus.vue";
import barcharpie from "@/components/Bus/buschar.vue";

import viewDetails from "./viewDetails.vue";

const selectorData = [
  '采购部', '运营部'
]

const taglabelList = [
    { label: '缺货订单', value: 2 },
    { label: '采购单', value: 3 },
    { label: '普通商品资料', value: 4 },
    { label: '日报', value: 5 },
    { label: '月报', value: 6 },
  ];

const tableCols =  [
 {  prop: 'createUserName', label: '维护人',  width: '100px' },
 {istrue: true, prop: 'createTime', label: '维护时间', width: '200px',
 },
 { prop:'content',label: '维护内容'   ,},
 {  prop: 'images', label: '维护图片', type:'colslot', width: '80px',},
//  { prop: 'lable', label: '标签',  width: '200px' ,},
 {type:'button',label:'操作',width:'100px',btnList:[
  {label:"删除",/*display:(row)=>{return true},display:true,*/ handle:(that,row)=>that.onDelete(row)}]
                                          },
];
export default {
  name: "searchEnginekbs",
  components: {
    MyContainer, vxetablebase, YhQuillEditor, uploadimgFile, buscharpie, viewDetails, barcharpie
  },
  //question
  data() {
    return {
      detailsPersonList: [],
      details: {
        startDate: '',
        endDate: '',
        userNo: '',
      },
      viewDetailsVisible: false,
      thisStyle: {
          width: '100%', height: '270px', 'box-sizing': 'border-box', 'line-height': '420px'
      },
      gridStyle: {
          top: '20%',
          left: '10%',
          right: '15%',
          bottom: '10%',
          containLabel: false
      },
      chartData1: null,
      chartData2: null,
      chartData3: null,
      chartData4: null,
      timeRanges: [],
      dataStatisticsVisible: false,
      editreply:{
        editreplyDialogVisible: false,
        id: null,
        content: null,
        value: null,
        bizId: null,
      },
      selector: {
        valueLable: '',
        bizId: null,
        eitpersonList: [],
        eitbeAskedUserIds: [],
        selectordialogVisible: false,
        selectedIds: [],
        id: null,
      },
      // eitbeAskedUserId: null,
      // eitSelectorList: [],
      // eitbeAskedUserIdst: [],
      // showOverlay: false,
      // timerCount: false,
      unreplyCount: 0,
      popovervisible: false,
      maintenanceTag: {
        value: '',
        linkValue: '',
      },
      taglabelList,
      tagMaintenanceData: [],
      highlightKeyword: null,
      loading:false,
      rules: {
        content: [
            { required: true, message: '请填写问题', trigger: 'blur' }
        ],
      },
      imagedefault: require("@/assets/images/detault.jpeg"),
      listLoading:false,
      tableData:[],
      sizes:[10,20,50,100],
      sels:[],
      total:null,
      tableCols:tableCols,
      personList:[],
       imgUrls:[],
       accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      ruleForm:{
        content:null,
        images:null,
        needApply:'0',
        beAskedUserIds:null,
        linkId:'',
      },
      questionMaintainRuleForm:{
        imgs:null,
        content:null,
        label:null,
        needApply:'0',
      },
      personageMessage: {
        avatar: '',
        userName: '',
        section: '',
      },
      loading:false,
      rules: {
        content: [
            { required: true, message: '请填写问题', trigger: 'blur' }
        ],
      },
      listLoading:false,
      tableData:[],
      sizes:[10,20,50,100],
      sels:[],
      total:null,
      tableCols:tableCols,
      personList:[],
       imgUrls:[],
       accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
      ruleForm:{
        content:null,
        beAskedUserIds:null,
         imgs:{
          private:[],
          public:[]
        },
      },
      questionMaintainRuleForm:{
        imgs:{
          private:[],
          public:[]
        },
        content:null,
        label:null,
      },


      imgUrlsPrivate:[],
      imgUrlsPublic:[],

      questionDialogVisible: false,
      questionContent: null,
      activeButton: 'latest',
      isRed: true,
      selectorData,//部门
      maintenance: {
        sourceDept: '',//提供标签部门
        targetDept: '',//搜索标签部门
        addTagName: '',//标签名称
      },
      question: '',//问题反馈
      that: this,
      ListInfo: {
        currentPage: 1,//当前页
        pageSize: 50,//每页数量
        orderBy: '',//排序
        keyword: '',//关键字
        tags: null,//标签
      },
      questionMaintainDialogVisible:false,
      loading: false,//加载
      pickerOptions,//时间选择器配置
      // searchContent: '',//搜索内容
      heatListData: [],//热门搜索
      feedbackProblem: false,//问题反馈
      submitQuestion: false,//提交问题
      labelContent: [],//标签内容
      selectedLabel: [],//选中标签
      labelMaintenance: false,//标签维护
      addedLabelPopup: false,//新增标签

      picFileList: [],//图片上传列表
      picLists: [],//图片列表
      orderNo: 0,
      problemData: [],//热门问题
      questionsData: [],//热门答疑
      inputkey: '',
      labelList:[],
      codename: ''
    }
  },
  created() {
  },
  async mounted() {
    let _this = this;
    setTimeout(() => {
      _this.ListInfo.keyword = ''
      _this.ListInfo.keyword = localStorage.getItem('searchEngine/kbs');
      localStorage.removeItem('searchEngine/kbs');
    }, 0);
    middlevue.$on('searchEnginekbs',(data) =>{
      _this.ListInfo.keyword = ''
      _this.ListInfo.keyword = data.question
      _this.getList()
    })
    setTimeout(() => {
      localStorage.removeItem('searchEngine/kbs');
    }, 0);
    await this.onAcquireMethod()
    await this.getList()
    await this.onGetReply()
    await this.labelMaintenanceMethod(1)
  },
  destroyed () {
    middlevue.$off('searchEnginekbs');
  },
  methods: {
    changeTag(){
      this.$forceUpdate();
    },
    changeTime(e) {
      this.details.startDate = e ? e[0] : null
      this.details.endDate = e ? e[1] : null
    },
    async detailsMethod(query) {
      if (query !== '') {
        const { data } = await employeeList({ name: query })
        this.detailsPersonList = data.map(item => {
          return { name: item.name, posts: item.posts, userId: item.userId }
        })
      } else {
        this.detailsPersonList = [];
      }
    },
    onViewDetails() {
      this.viewDetailsVisible = true
    },
    async onDataStatistics() {
      // this.dataStatisticsVisible = false;
      this.chartData1 = null;
      this.chartData2 = null;
      this.chartData3 = null;
      this.chartData4 = null;

      this.$nextTick(async () => {

        if (this.timeRanges && this.timeRanges.length == 0) {
          //默认给近1天时间
          this.details.startDate = dayjs().format('YYYY-MM-DD')
          this.details.endDate = dayjs().format('YYYY-MM-DD')
          this.timeRanges = [this.details.startDate, this.details.endDate]
        }
        const params = {
          startDate: this.details.startDate,
          endDate: this.details.endDate,
          userNo: this.details.userNo
        }
        await this.chartsone(params);
        await this.chartstwo(params);
        await this.chartsthr(params);
        await this.chartsfour(params);


      })
      setTimeout(()=>{
        this.dataStatisticsVisible = true
      }, 0);

    },
    async chartstwo(params){
      const { data, success } = await questionStatistic(params)
        if (!success) return
        console.log(data, 'data');

        let chatlist = data.map(item=>{
          return  {
            name: item.userName,
            value: item.questionCount
          }
        })

        this.chartData2 = {
          avoidLabelOverlap: false,
          lengendshow: true,
          labelLine: {
            show: false
          },
          title: {
            left: 'center',
            top: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              type: 'pie',
              label: {
                show: true,
                position: 'inner',
              },
              data: chatlist,
            }
          ]
        };


    },
    async chartsone(params){
      const { data, success } = await statistic(params)
        if (!success) return
        console.log(data, 'data');

        let chatlist = [


          {
            name: '已解决次数',
            value: data.resolveCount
          },
          {
            name: '未解决次数',
            value: data.unResolveCount
          },
          {
            name: '回复次数',
            value: data.replyCount
          },
          {
            name: '待回复次数',
            value: data.calledCount
          },
          {
            name: '提问次数',
            value: data.questionCount
          },
          // {
          //   name: '被@次数',
          //   value: data.calledCount
          // },
          // {
          //   name: '被@回复次数',
          //   value: data.answerCalledCount
          // },
        ]

        this.chartData1 = {
          avoidLabelOverlap: false,
          lengendshow: true,
          labelLine: {
            show: false
          },
          title: {
            left: 'center',
            top: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          series: [
            {
              type: 'pie',
              label: {
                show: true,
                position: 'inner',
              },
              data: chatlist,
            }
          ]
        };


    },
    async chartsthr(params){
      const { data, success } = await replyStatistic(params)
        if (!success) return
        console.log(data, 'data');

        if(!data.series){
          this.chartData3 = null;
          return;
        }

        data.series.map((item)=>{
          item.itemStyle ={
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
        })

        this.chartData3 = {
          avoidLabelOverlap: false,
          title: '',
          labelLine: {
            show: false
          },
          tooltip: {
            trigger: 'item'
          },
          "series": data.series,
          "xAxis": data.xAxis,
        };




    },
    async chartsfour(params){
      const { data, success } = await solvedStatistic(params)
        if (!success) return

        if(!data.series){
          this.chartData4 = null;
          return;
        }

        data.series.map((item)=>{
          item.itemStyle ={
            "normal": {
              "label": {
                "show": true,
                "position": "top",
                "textStyle": {
                  "fontSize": 14
                }
              }
            }
          }
        })

        this.chartData4 = {
          avoidLabelOverlap: false,
          // lengendshow: true,
          title: '',
          labelLine: {
            show: false
          },
          tooltip: {
            trigger: 'item'
          },
          "series": data.series,
          "xAxis": data.xAxis,
          // formatter: function (params) {
          //     return zongtotal;
          // }
        };

    },
    onLabelSkiproute(item){
      const message = `是否跳转至 ${item.valueTag}?`;
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push(item.linkValue)
        this.$message({
          type: 'success',
          message: '跳转成功!'
        });
      }).catch(() => {
      });
    },
    onEditingMethod(more,val,item){
      if(checkPermission('EditReplyPermission')){
        this.editreply.editreplyDialogVisible = true
        this.editreply.id = more.id
        this.editreply.bizId = item.bizId
        this.editreply.content = more.content
        this.editreply.value = val
      }
    },
    async onEditreplyMethod(){
      const params = {
        id: this.editreply.id,
        content: this.editreply.content
      }
      const {data, success} = await updateReply(params)
      if(success){
        this.$message({ type: 'success', message: '编辑成功!' });
        this.editreply.editreplyDialogVisible = false
        if(this.editreply.value == 1){
          this.problemData.forEach((item) => {
            if(item.bizId == this.editreply.bizId){
              if(item.comments){
                item.comments.forEach((commentItem) => {
                  if(commentItem.id == this.editreply.id){
                    commentItem.content = this.editreply.content
                  }
                });
              }
            }
          });
        }else if(this.editreply.value == 2){
          this.questionsData.forEach((item) => {
            if(item.bizId == this.editreply.bizId){
              if(item.comments){
                item.comments.forEach((commentItem) => {
                  if(commentItem.id == this.editreply.id){
                    commentItem.content = this.editreply.content
                  }
                });
              }
            }
          });
        }
      }
    },
    onEitbeAskedUserIdsChange(selectedIds) {
      this.$refs.eitSelectRef.query = '';
      if (!this.selector.selectedUsers) {
        this.selector.selectedUsers = [];
      }
      selectedIds.forEach(userId => {
        const selectedUser = this.selector.eitpersonList.find(item => item.userId === userId);
        if (selectedUser) {
          this.selector.selectedUsers.push({
            userId: userId,
            name: selectedUser.name
          });
        }
      });
      this.selector.eitbeAskedUserIds = []
      this.selector.eitbeAskedUserIds = selectedIds;

    },
    onStorageMethod() {
      // 去重 selectedUsers
      this.selector.selectedUsers = this.selector.selectedUsers.reduce((acc, current) => {
        if (!acc.find(item => item.userId === current.userId)) {
          acc.push(current);
        }
        return acc;
      }, []);

      const updateContent = (dataItem, contentKey) => {
        this.selector.selectedUsers.forEach((item) => {
          // 处理内容中的 @ 符号
          if (dataItem[contentKey].endsWith("双方都@@") || dataItem[contentKey].endsWith("双方都@")) {
            dataItem[contentKey] = dataItem[contentKey].slice(0, -1);
          }
          if (dataItem[contentKey].endsWith("@")) {
            dataItem[contentKey] += `${item.name}`;
            dataItem[contentKey] += ' '
          } else {
            dataItem[contentKey] += `@${item.name}`;
            dataItem[contentKey] += ' '
          }
        });
      };

      const updateBeAskedUserIds = (dataItem) => {
        dataItem.beAskedUserIds = (dataItem.beAskedUserIds ? `${dataItem.beAskedUserIds},` : '') + this.selector.eitbeAskedUserIds.join(',');
      };

      if (this.selector.valueLable === 1 || this.selector.valueLable === 2) {
        const dataList = this.selector.valueLable === 1 ? this.problemData : this.questionsData;
        dataList.forEach((dataItem) => {
          if (dataItem.bizId === this.selector.bizId) {
            updateBeAskedUserIds(dataItem);
            updateContent(dataItem, 'content');
          }
        });
      } else if (this.selector.valueLable === 3 || this.selector.valueLable === 4) {
        const dataList = this.selector.valueLable === 3 ? this.problemData : this.questionsData;
        dataList.forEach((dataItem) => {
          if (dataItem.bizId === this.selector.bizId && dataItem.comments) {
            dataItem.comments.forEach((commentItem) => {
              if (commentItem.id === this.selector.id) {
                updateBeAskedUserIds(commentItem);
                updateContent(commentItem, 'contents');
              }
            });
          }
        });
      }
      this.$forceUpdate();
      this.selector.selectordialogVisible = false;
    },

    async eitremoteMethod(query) {
      if (query !== '') {
          const {data} = await employeeList({name:query})
          this.selector.eitpersonList =  data.map(item=>{
        return {name:item.name,posts:item.posts,userId:item.userId}
      })
      } else {
          this.selector.eitpersonList = [];
      }
    },
    onEitRecoverMethod(item,val, more){
      if(val == 1 || val ==2){
        this.selector.bizId = item.bizId
      } else if(val == 3|| val == 4){
        this.selector.bizId = item.bizId
        this.selector.id = more.id
      }
      this.selector.valueLable = val
      this.selector.eitbeAskedUserIds = []
      this.selector.eitpersonList = []
      this.selector.selectedUsers = []
      this.selector.selectordialogVisible = true
      this.$nextTick(() => {
        this.$refs.eitSelectRef.focus();  // 让选择器获取焦点
      });
    },
    async onGetReply(){
      const {data, success} = await unRepliedCount()
      if(!success) return
      this.unreplyCount = data ? data : 0
    },
    async onWaitReply(){
      await this.leftMethod(2)
    },
    onlabelremovalMethod(item,i){
      this.$confirm('是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        if(item.id){
          const params = {
            ids: item.id
          }
          const {data, success} = await remove(params)
          if(!success) return
          this.tagMaintenanceData = this.tagMaintenanceData.filter(data => data.id !== item.id)
        }else if(item.id == 0){
          this.tagMaintenanceData.splice(i, 1)
        }
          this.$message({ type: 'success', message: '删除成功!' });
      }).catch(() => {
        this.$message({type: 'info',message: '已取消删除' });
      });
    },
    onAddLabelMethod(){
      this.tagMaintenanceData.push({
        value: '',
        linkValue: '',
      })
    },
    async onMaintenanceCommit(){
      let tagList = this.tagMaintenanceData
      const {data, success} = await batchSubmit(tagList)
      if(success){
        this.$message({ type: 'success', message: '提交成功!' });
        this.labelMaintenance = false
      }
    },
    async labelMaintenanceMethod(val){
      const { data, success } = await pageList({pageSize: 20, currentPage: 1})
      if(success){
        this.tagMaintenanceData = data
        this.tagMaintenanceData = this.tagMaintenanceData.filter(item => item.id)
      }
      if(val == 2){
        this.labelMaintenance = true
        if(this.tagMaintenanceData.length == 0){
          this.onAddLabelMethod()
        }
      }
    },
    collapseComments(item,label) {
      if(label == 'left'){
        this.problemData.forEach((dataItem, index) => {
          if (dataItem.bizId == item.bizId) {
            dataItem.comments = dataItem.comments.slice(0, 3);
            dataItem.seeMoreCheck = false;
            dataItem.currentPage = 2;
          }
        });
      }else{
        this.questionsData.forEach((dataItem, index) => {
          if (dataItem.bizId == item.bizId) {
            dataItem.comments = dataItem.comments.slice(0, 3);
            dataItem.seeMoreCheck = false;
            dataItem.currentPage = 2;
          }
        });
      }
      this.$forceUpdate()
    },
    async onSeeMoreMethod(item,label){
      const params = {
        bizId:item.bizId,
        pageSize:3,
        currentPage:item.currentPage === 2 ? 2 : item.currentPage
      }
      const {data, success } = await replyList(params)

      if(!success){
        return
      }
      if(data.list == null || (data && data.list.length === 0)){
        this.$message({ message: '暂无更多数据', type: 'warning'});
        return
      }
      if(label == 'left'){
          this.problemData.forEach((dataItem, index) => {
          if (dataItem.bizId == item.bizId) {
            dataItem.currentPage = dataItem.currentPage + 1;
            if (data.list == null || (data.list && data.list.length === 0)) {
              item.seeMoreCheck = true;
            } else {
              console.log(data.list.length, 'data.list.lengthleft');
              if(data.list && data.list.length < 3){
                item.seeMoreCheck = true;
              }
              const processedComments = data.list.map(com => {
                com.communicationSubAnswer = false;
                com.needApply = '0';
                let privateImagesinput = [];
                let publicityImagesinput = [];

                if (com.images) {
                  let images = JSON.parse(com.images);
                  images.public = images.public?.length === 0 ? [] : images.public || [];
                  images.private = images.private?.length === 0 ? [] : images.private || [];
                  if (!images.private || (images.private && images.private.length === 0)) {
                    for (let i = 0; i < com.privateImgCount; i++) {
                      privateImagesinput.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
                    }
                  } else {
                    privateImagesinput = images.private;
                  }

                  publicityImagesinput = images.public;

                  com.images = [...privateImagesinput, ...publicityImagesinput];
                } else {
                  com.images = [];
                }
                return com;
              });
              dataItem.comments = dataItem.comments.concat(processedComments);
            }
          }
        });
      }else{
        this.questionsData.forEach((dataItem, index) => {
          if (dataItem.bizId == item.bizId) {
            dataItem.currentPage = dataItem.currentPage + 1;
            if (data.list == null || (data.list && data.list.length === 0)) {
              item.seeMoreCheck = true;
            } else {
              console.log(data.list.length, 'data.list.length');
              if(data.list && data.list.length < 3){
                item.seeMoreCheck = true;
              }
              const processedComments = data.list.map(com => {
                com.communicationSubAnswer = false;
                com.needApply = '0';
                let privateImagesinput = [];
                let publicityImagesinput = [];

                if (com.images) {
                  let images = JSON.parse(com.images);
                  images.public = images.public?.length === 0 ? [] : images.public || [];
                  images.private = images.private?.length === 0 ? [] : images.private || [];
                  if (!images.private || (images.private && images.private.length === 0)) {
                    for (let i = 0; i < com.privateImgCount; i++) {
                      privateImagesinput.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
                    }
                  } else {
                    privateImagesinput = images.private;
                  }

                  publicityImagesinput = images.public;

                  com.images = [...privateImagesinput, ...publicityImagesinput];
                } else {
                  com.images = [];
                }
                return com;
              });
              dataItem.comments = dataItem.comments.concat(processedComments);
            }
          }
        });
      }
      this.$forceUpdate()
    },
    handleImageClick(image,id) {
      if (this.isSpecialImage(image)) {
        this.$confirm('是否确认申请查看?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          const params = { id: id }
          const {success, data} = await applyImg(params)
          if(success){
            this.$message({ type: 'success', message: '申请成功!' });
          }
        }).catch(() => {
          this.$message({  type: 'info', message: '已取消申请' });
        });
      }
    },
    isSpecialImage(image) {
      return image == "https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png";
    },

    delImg(items,i,val,area,indexs){
      let _this = this;
      if(val == 1){
      _this.$refs.upload1[indexs].uploadFiles.splice(i,1)
        this.problemData.forEach((item, index) => {
          if (item.bizId == items.bizId) {
              if(area == 'publicity'){
                item.publicityImagesinput.splice(i, 1)
              }else{
                item.privateImagesinput.splice(i, 1)
              }
            this.$forceUpdate();
          }
        });
      }else if(val == 2){
      _this.$refs.upload2[indexs].uploadFiles.splice(i,1)
        this.questionsData.forEach((item, index) => {
          if (item.bizId == items.bizId) {
              if(area == 'publicity'){
                item.publicityImagesinput.splice(i, 1)
              }else{
                item.privateImagesinput.splice(i, 1)
              }
            this.$forceUpdate();
          }
        });
      }else if(val == 3){
      _this.$refs.upload3[indexs].uploadFiles.splice(i,1)
        this.problemData.forEach((item, index) => {
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com, indexs) => {
              if (com.id == items.id) {
                if(area == 'publicity'){
                  com.publicityImagesinput.splice(i, 1)
                }else{
                  com.privateImagesinput.splice(i, 1)
                }
                this.$forceUpdate();
              }
            })
          }
        })
      }else if(val == 4){
      _this.$refs.upload4[indexs].uploadFiles.splice(i,1)
        this.questionsData.forEach((item, index) => {
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com, indexs) => {
              if (com.id == items.id) {
                if(area == 'publicity'){
                  com.publicityImagesinput.splice(i, 1)
                }else{
                  com.privateImagesinput.splice(i, 1)
                }
                this.$forceUpdate();
              }
            })
          }
        })
      }
    },

    async onDelete(row){

       this.$confirm('确认要删除这条数据吗？','提示',{
        type:'warning',
        center:true,
        customClass:'tipBox'
      })
          .then(async() => {
            const res = await deleteQuestion({id:row.id})
            if(res.success){
              this.getQuestionList()
            }
          })
          .catch(_ => {});
    },
    async getQuestionList(){
      this.$nextTick(async()=>{
        let page = this.$refs.pager.getPager()
        let params = {
          ...page
        }
        let {data } =  await hotList(params)
        // let labelData =  await getLabelList({})
        // this.labelList = labelData.data
        console.log(data.list,'改前')

        if(data.list){
          data.list.forEach(item=>{
            item.images = JSON.parse(item.images)
            //维护问题列表所有图片可看
            item.images = [...item.images.public,...item.images.private]
          })
        }
        this.total = data.total
        this.tableData = data.list
        console.log(this.tableData,'改后')

      })

    },

    async openQuestionMaintainLog(){
      // this.$refs.vxetable.getPager()
      this.questionMaintainDialogVisible = true
       this.getQuestionList()

    },
     getImgsCallBackPrivate(data){
      if (data) {
            this.imgUrlsPrivate = data ? data : []
            this.ruleForm.imgs.private = data.map(item => item.url)
        }
    },
    getImgsCallBackPublic(data) {
        if (data) {
            this.imgUrlsPublic = data ? data : []
            this.ruleForm.imgs.public = data.map(item => item.url)
        }
    },
    //图片上传成功回调
    async handleSuccess({ data }, items, val) {
      let uploadCheck = false;
      if (val == 1) {
        this.problemData.forEach((item, index) => {
          if (item.bizId == items.bizId) {
            item.publicityImagesinput = item.publicityImagesinput || [];
            item.privateImagesinput = item.privateImagesinput || [];
            if (items.needApply == 0) {
              if(item.publicityImagesinput.length > 8){
                this.delImg(item, 9 ,1,'encipher',index)
                uploadCheck = true;
                return
              }else{
                item.publicityImagesinput.push(data.url);
              }
            } else {
              if(item.privateImagesinput.length > 8){
                this.delImg(item, 9 ,1,'publicity',index)
                uploadCheck = true;
                return
              }else{
                item.privateImagesinput.push(data.url);
              }
            }
            this.$forceUpdate();
          }
        });
      } else if (val == 2) {
        this.questionsData.forEach((item, index) => {
          if (item.bizId === items.bizId) {
            item.publicityImagesinput = item.publicityImagesinput || [];
            item.privateImagesinput = item.privateImagesinput || [];
            if (items.needApply == 0) {
              if(item.publicityImagesinput.length > 8){
                this.delImg(item, 9 ,2,'encipher',index)
                uploadCheck = true;
                return
              }else{
                item.publicityImagesinput.push(data.url);
              }
            } else {
              if(item.privateImagesinput.length > 8){
                this.delImg(item, 9 ,2,'publicity',index)
                uploadCheck = true;
                return
              }else{
                item.privateImagesinput.push(data.url);
              }
            }
            this.$forceUpdate();
          }
        });
      } else if (val == 3) {
        this.problemData.forEach((item, index) => {
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com, indexs) => {
              if (com.id == items.id) {
                // if (!com.imagesinput) {
                //   com.imagesinput = []
                // }
                // com.imagesinput.push(data.url)
                com.publicityImagesinput = com.publicityImagesinput || [];
                com.privateImagesinput = com.privateImagesinput || [];
                if (items.needApply == 0) {
                  if(com.publicityImagesinput.length > 8){
                    this.delImg(com, 9 ,3,'encipher',index)
                    uploadCheck = true;
                    return
                  }else{
                    com.publicityImagesinput.push(data.url);
                  }
                } else {
                  if(com.privateImagesinput.length > 8){
                    this.delImg(com, 9 ,3,'publicity',index)
                    uploadCheck = true;
                    return
                  }else{
                    com.privateImagesinput.push(data.url);
                  }
                }
                this.$forceUpdate();
              }
            })
          }
        })
      } else if (val == 4) {
        this.questionsData.forEach((item, index) => {
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com, indexs) => {
              if (com.id == items.id) {
                com.publicityImagesinput = com.publicityImagesinput || [];
                com.privateImagesinput = com.privateImagesinput || [];
                if (items.needApply == 0) {
                  if(com.publicityImagesinput.length > 8){
                    this.delImg(com, 9 ,4,'encipher',index)
                    uploadCheck = true;
                    return
                  }else{
                    com.publicityImagesinput.push(data.url);
                  }
                } else {
                  if(com.privateImagesinput.length > 8){
                    this.delImg(com, 9 ,4,'publicity',index)
                    uploadCheck = true;
                    return
                  }else{
                    com.privateImagesinput.push(data.url);
                  }
                }
                this.$forceUpdate();
              }
            })
          }
        })
      }
      if (uploadCheck) {
        this.$message({ message: "最多上传9张图片", type: "warning" });
      }else{
        this.$message({ message: "上传成功", type: "success" });
      }
    },

    blurChildinput(list, val, contents) {
      return
      console.log(list, 'list.content');
      if (contents) {
        return
      }
      if (val === 1) {
        this.problemData.forEach((item, index) => {
          if (item.bizId == list.bizId) {
            if (item.comments && item.comments.length > 0) {
              item.comments.forEach(com => {
                // setTimeout(() => {
                com.communicationSubAnswer = false
                // }, 10);
              })
            }
            this.$forceUpdate();
          }
          // if (item.bizId == list.bizId) {
          //   item.communicationAnswer = false;
          //   this.$nextTick(() => {
          //     item.content = ''; // 清空内容
          //   });
          //   this.$forceUpdate();
          // }
        });
      } else if (val === 2) {
        this.questionsData.forEach((item, index) => {
          if (item.bizId == list.bizId) {
            if (item.comments && item.comments.length > 0) {
              item.comments.forEach(com => {
                // setTimeout(() => {
                com.questionsSubAnswer = false
                // }, 0);
              })
            }
            this.$forceUpdate();
          }
        });
      }
      this.$forceUpdate();
    },
    getImgsPublic(data) {
        if (data) {
            this.imgUrlsPublic = data ? data : []
            this.questionMaintainRuleForm.imgs.public = data.map(item => item.url)
        }
    },
    getImgsPrivate(data) {
        if (data) {
            this.imgUrlsPrivate = data ? data : []
            this.questionMaintainRuleForm.imgs.private = data.map(item => item.url)
        }
    },

    async handInQuestionMaintain(type){
        let params = {
          content:this.questionMaintainRuleForm.content,
          images:this.questionMaintainRuleForm.imgs,
        }
      if(type == '取消'){
        this.questionMaintainRuleForm.imgs.private=null
        this.questionMaintainRuleForm.imgs.public=null
         this.imgUrlsPrivate = []
         this.imgUrlsPublic = []
         this.$refs.uploadimgFilePublic._data.retdata = []
         this.$refs.uploadimgFilePrivate._data.retdata = []
        this.$refs.questionMaintainRuleForm.resetFields();
        this.questionMaintainDialogVisible = false
        return
      }
      this.$refs.questionMaintainRuleForm.validate(async(va)=>{
          if(va){

            let res = await maintainQuestionSave(params)
            if (res.success) {
                this.$message({
                  message: '提交问题成功',
                  type: 'success',
                  offset: 40
                });
                this.questionMaintainRuleForm.imgs.private=null
                this.questionMaintainRuleForm.imgs.public=null
                this.imgUrlsPrivate = []
                this.imgUrlsPublic = []
                this.questionMaintainDialogVisible = false
                this.$refs.uploadimgFilePublic._data.retdata = []
                this.$refs.uploadimgFilePrivate._data.retdata = []
                 this.$refs.questionMaintainRuleForm.resetFields();
              } else {
                this.$message({
                  message: '提交失败请重新尝试',
                  offset: 40
                });
              }
          }
      })
    },
    async remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
           const {data} = await employeeList({name:query})
            this.personList =  data.map(item=>{
          return {name:item.name,posts:item.posts,userId:item.userId}
        })
          this.loading = false;
        } else {
           this.personList = [];
        }
      },
      sanitizeHTML(html) {
        const tempDiv = document.createElement('div');
        // 用于判断是否包含 HTML 标签的简单正则表达式
        const hasHTMLTags = /<\/?[a-z][\s\S]*>/i.test(html);

        if (hasHTMLTags) {
            // 如果包含 HTML 标签，则不进行转义，只进行换行符替换
            return html.replace(/\n/g, '<br>');
        } else {
            // 对纯文本进行转义并替换换行符
            tempDiv.textContent = html;
            return tempDiv.innerHTML.replace(/\n/g, '<br>');
        }
    },
    onChildcover(subdata, val) {
      const handleFocus = (dataList, refPrefix, otherDataList, otherKey) => {
        dataList.forEach((item) => {
          item.beAskedUserIds = ''
          if (val == 1) {
            item.communicationAnswer = false;
          } else if (val == 2) {
            item.recoverAnswer = false;
          }
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com, indexs) => {
              com.beAskedUserIds = ''
              if (com.id === subdata.id) {
                const key = val === 1 ? 'communicationSubAnswer' : 'questionsSubAnswer';
                // 切换当前 com[key] 的值，并将其他的设为 false
                com[key] = !com[key];
                item.comments.forEach((otherCom) => {
                  if (otherCom !== com) {
                    otherCom[key] = false;
                  }
                });
                this.$nextTick(() => {
                  if (com[key]) {
                    setTimeout(() => {
                      document.getElementById(`${refPrefix}${indexs}aa`).focus();
                    }, 100);
                  }
                });
              } else {
                // 确保其他 com[key] 都设为 false
                com[val === 1 ? 'communicationSubAnswer' : 'questionsSubAnswer'] = false;
              }
            });
          }
        });
        // 处理其他数据列表的对应字段
        otherDataList.forEach((item) => {
          if(val == 1){
            item.recoverAnswer = false;
          }else if(val == 2){
            item.communicationAnswer = false;
          }
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com) => {
              com[otherKey === 'questionsSubAnswer' ? 'questionsSubAnswer' : 'communicationSubAnswer'] = false;
            });
          }
        });
      };

      if (val === 1) {
        handleFocus(this.problemData, 'refChildRecove', this.questionsData, 'questionsSubAnswer');
      } else if (val === 2) {
        handleFocus(this.questionsData, 'refQuestionsRecove', this.problemData, 'communicationSubAnswer');
      }

      this.$forceUpdate();
    },

    async openQuestionLog() {
      this.tagMaintenanceData = []
      this.ruleForm.linkId = null
      this.questionDialogVisible = true
      this.labelMaintenanceMethod(1)
    },
    async handInQuestion(type) {
        //提交问题 //清空内容

        let params = {
          content:this.ruleForm.content,
          images:this.ruleForm.imgs,
          linkId:this.ruleForm.linkId,
          beAskedUserIds:this.ruleForm.beAskedUserIds.join(','),
        }
      if(type == '取消'){
        this.ruleForm.imgs.private = null
        this.ruleForm.imgs.public = null
        this.imgUrlsPrivate = []
        this.imgUrlsPublic = []
        this.$refs.uploadimgCallBackFilePrivate._data.retdata = []
        this.$refs.uploadimgCallBackFilePublic._data.retdata = []
        this.$refs.ruleForm.resetFields();
        this.questionDialogVisible = false
        return
      }
      this.$refs.ruleForm.validate(async(va)=>{
          if(va){

            let res = await saveQuestion(params)
            if (res.success) {
                this.$message({
                  message: '提交问题成功',
                  type: 'success',
                  offset: 40
                });
                 this.ruleForm.imgs.private = null
                  this.ruleForm.imgs.public = null
                  this.imgUrlsPrivate = []
                  this.imgUrlsPublic = []
                this.$refs.uploadimgCallBackFilePrivate._data.retdata = []
                this.$refs.uploadimgCallBackFilePublic._data.retdata = []
                this.questionDialogVisible = false

                 this.$refs.ruleForm.resetFields();
              } else {
                this.$message({
                  message: '提交失败请重新尝试',
                  offset: 40
                });
              }
          }
      })



    },
    loadMyLatestList() {

    },

    async onadoptVerify(list, val, type) {
      const handlePraiseCount = (dataList) => {
        dataList.forEach(item => {
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach(com => {
              if (com.id === list.id) {
                if(type == 1){
                  com.praiseCount = parseInt(com.praiseCount, 10) || 0;
                  if(com.isPraise == 1){
                    com.isPraise = 0
                    com.praiseCount = com.praiseCount - 1
                  }else{
                    com.isPraise = 1
                    com.praiseCount = com.praiseCount + 1
                    if(com.isUnsolved == 1){
                      com.isUnsolved = 0
                      com.unsolvedCount = com.unsolvedCount - 1
                    }
                  }
                }else{
                  com.unsolvedCount = parseInt(com.unsolvedCount, 10) || 0;
                  if(com.isUnsolved == 1){
                    com.isUnsolved = 0
                    com.unsolvedCount = com.unsolvedCount - 1
                  }else{
                    com.isUnsolved = 1
                    com.unsolvedCount = com.unsolvedCount + 1
                    if(com.isPraise == 1){
                      com.isPraise = 0
                      com.praiseCount = com.praiseCount - 1
                    }
                  }
                }
                this.$forceUpdate();
              }
            });
          }
        });
      };

      if (val === 1) {
        handlePraiseCount(this.problemData);
      } else if (val === 2) {
        handlePraiseCount(this.questionsData);
      }

      const { success } = await adoptAnswer({
        clickType: type,
        id: list.id,
        // adoptOrNot: 1
      });

      // if (success) {
      //   this.$message.success('采纳成功');
      // }
    },

    async onUpConfirmation(list, val) {
      const handleUpCounts = (dataList) => {
        dataList.forEach(item => {
          if (item.bizId === list.bizId && item.id === list.id) {
            if(item.isUp == 1){
              item.isUp = 0
              item.upCounts = item.upCounts - 1
            }else{
              item.isUp = 1
              item.upCounts = item.upCounts + 1
            }
            this.$forceUpdate();
          }
        });
      };

      if (val === 1) {
        handleUpCounts(this.problemData);
      } else if (val === 2) {
        handleUpCounts(this.questionsData);
      }

      const { success } = await upQuestion({ id: list.id });

      // if (success) {
      //   this.$message.success('点赞成功');
      // }
    },

    async onRecoveMethodr(list, val, bizId) {
      console.log(list, 'list');
      //使用dayjs获取当前时间
      let nowTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

      if (!list.contents && !('comments' in list)) {
        list.content = '';
      }
      const { success } = await replyQuestion({
        bizId: bizId || list.bizId,
        content: list.contents || list.content,
        images:{
          private: list.privateImagesinput,
          public: list.publicityImagesinput
        },
        needApply: list.needApply,
        beAskedUserIds: list.beAskedUserIds,
        parentId: list.id
      });

      if (success) {
        this.$message.success('回复成功');
      }

      const handleToggle = (dataList, key, commentKey) => {
        dataList.forEach(item => {
          if (item.bizId === list.bizId) {
            if (commentKey) {
              item.comments?.forEach(com => {
                if (com.id === list.id) {
                  com[commentKey] = !com[commentKey];
                }
              });
            } else {
              item[key] = !item[key];
            }
            this.$nextTick(() => {
              this.$forceUpdate();
            });
          }
        });
      };

      const addComment = (dataList, contentKey) => {
        const newComment = {
          content: list[contentKey],
          // images: list.imagesinput ? JSON.stringify(list.imagesinput) : [],
          images: (list.privateImagesinput && list.privateImagesinput.length > 0)
            ? [...list.privateImagesinput.map(() => 'https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png'), ...(list.publicityImagesinput || [])]
            : (list.publicityImagesinput || []),
          // images: list.privateImagesinput && list.publicityImagesinput ? [...list.privateImagesinput,...list.publicityImagesinput] : [],
          avatar: this.personageMessage.avatar,
          replyUserName: this.personageMessage.userName,
          replyDeptName: this.personageMessage.section,
          needApply: '0',
          praiseCount: 0,
          replyCreateTime: nowTime,
          sourceUserName: list.replyUserName || null,  // 仅在 val == 3 或 4 时有效
        };

        dataList.forEach(item => {
          if (item.bizId === (bizId || list.bizId)) {
            if (!Array.isArray(item.comments)) {
              item.comments = [];
            }
            item.comments.push(newComment);
          }
        });
      };

      if ([1, 3].includes(val)) {
        handleToggle(this.problemData, val === 1 ? 'communicationAnswer' : null, val === 3 ? 'communicationSubAnswer' : null);
        addComment(this.problemData, val === 1 ? 'content' : 'contents');
      } else if ([2, 4].includes(val)) {
        handleToggle(this.questionsData, val === 2 ? 'recoverAnswer' : null, val === 4 ? 'questionsSubAnswer' : null);
        addComment(this.questionsData, val === 2 ? 'content' : 'contents');
      }

      const clearSubAnswer = (dataList, subAnswerKey) => {
        dataList.forEach(item => {
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach(com => {
              if (com.id == list.id) {
                com[subAnswerKey] = false;
                this.$nextTick(() => {
                  com.contents = ''; // 清空内容
                });
                this.$forceUpdate();
              }
            });
          }
        });
      };

      if (val == 3) {
        clearSubAnswer(this.problemData, 'communicationSubAnswer');
      } else if (val == 4) {
        clearSubAnswer(this.questionsData, 'questionsSubAnswer');
      }
    },

    onRecover(list, val) {
      const handleToggleAndFocus = (dataList, key, refPrefix) => {
        dataList.forEach((item, index) => {
          item.beAskedUserIds = ''
          // 处理 item.comments 内的 communicationSubAnswer 或 questionsSubAnswer
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com) => {
              com.beAskedUserIds = ''
              if (val == 1) {
                com.communicationSubAnswer = false;
              } else if (val == 2) {
                com.questionsSubAnswer = false;
              }
            });
          }
          // 处理 communicationAnswer 和 recoverAnswer 的切换逻辑
          if (item.bizId === list.bizId) {
            // 将当前 item[key] 取反
            item[key] = !item[key];
            // 如果 item[key] 为 true，将其他项的 key 设置为 false
            dataList.forEach((otherItem) => {
              if (otherItem !== item) {
                otherItem[key] = false;
              }
            });
            this.$nextTick(() => {
              item.content = ''; // 清空内容
              item.privateImagesinput = []; // 清空图片
              item.publicityImagesinput = []; // 清空图片
              item.needApply = '0'; // 重置是否申请查看
              if (item[key]) {
                setTimeout(() => {
                  document.getElementById(`${refPrefix}${index}`).focus();
                }, 0);
              }
            });
            this.$forceUpdate();
          }
        });
      };
      const handleOppositeData = (oppositeDataList, oppositeKey, oppositeSubKey) => {
        oppositeDataList.forEach((item) => {
          item[oppositeKey] = false;
          if (item.comments && item.comments.length > 0) {
            item.comments.forEach((com) => {
              com[oppositeSubKey] = false;
            });
          }
        });
      };
      if (val === 1) {
        handleOppositeData(this.questionsData, 'recoverAnswer', 'questionsSubAnswer');
        handleToggleAndFocus(this.problemData, 'communicationAnswer', 'refCommunication');
      } else if (val === 2) {
        handleOppositeData(this.problemData, 'communicationAnswer', 'communicationSubAnswer');
        handleToggleAndFocus(this.questionsData, 'recoverAnswer', 'refRecove');
      }

      this.$forceUpdate();
    },

    blurinput(list, val) {
      return
      if (list.content) {
        return
      }
      if (val === 2) {
        this.questionsData.forEach((item, index) => {
          if (item.bizId == list.bizId) {
            item.recoverAnswer = false;
            this.$nextTick(() => {
              item.content = ''; // 清空内容
            });
            this.$forceUpdate();
          }
        });
      } else {
        this.problemData.forEach((item, index) => {
          if (item.bizId == list.bizId) {
            item.communicationAnswer = false;
            this.$nextTick(() => {
              item.content = ''; // 清空内容
            });
            this.$forceUpdate();

          }
        });
      }
      this.$forceUpdate();
    },
    inputchangeEit(e, item, val, list) {

      if (val === 3 || val === 4) {
        this.selector.bizId = list.bizId;
        this.selector.id = item.id;

        // 从 list.contents 中提取名称并创建名称集合
        const contentNames = (list.contents.match(/@(\S+)/g) || []).map(name => name.substring(1).trim());
        console.log(contentNames, 'contentNames');

        const contentNameSet = new Set(contentNames);

        // 过滤掉不在内容名称中的用户 ID
        this.selector.eitbeAskedUserIds = this.selector.eitbeAskedUserIds.filter(userId => {
          const user = this.selector.selectedUsers.find(user => user.userId === userId);
          return user && contentNameSet.has(user.name);
        });
        if (this.selector.eitbeAskedUserIds && this.selector.eitbeAskedUserIds.length > 0) {
          list.beAskedUserIds = this.selector.eitbeAskedUserIds.join(',');
        }

        const atSymbols = e.split('@').slice(1);
        const hasEmptyAfterAt = atSymbols.some(part => part === '');
        console.log(hasEmptyAfterAt, 'hasEmptyAfterAt');

        if (hasEmptyAfterAt) {
          this.onEitRecoverMethod(item, val, list);
        } else {
          this.selector.selectordialogVisible = false;
        }
      } else if (val === 1 || val === 2) {
        // 从 item.content 中提取名称并创建名称集合
        const contentNames = (item.content.match(/@(\S+)/g) || []).map(name => name.substring(1).trim());
        const contentNameSet = new Set(contentNames);

        // 过滤掉不在内容名称中的用户 ID
        this.selector.eitbeAskedUserIds = this.selector.eitbeAskedUserIds.filter(userId => {
          const user = this.selector.selectedUsers.find(user => user.userId === userId);
          return user && contentNameSet.has(user.name);
        });

        if (this.selector.eitbeAskedUserIds && this.selector.eitbeAskedUserIds.length > 0) {
          item.beAskedUserIds = this.selector.eitbeAskedUserIds.join(',');
        }

        const atSymbols = e.split('@').slice(1);
        const hasEmptyAfterAt = atSymbols.some(part => part === '');

        if (hasEmptyAfterAt) {
          this.onEitRecoverMethod(item, val, list);
        } else {
          this.selector.selectordialogVisible = false;
        }
      }

      this.$forceUpdate();
    },
    inputchange() {
      this.$forceUpdate();
    },
    async init(label) {
      let keyword = label ? label : '';
      const { data, success } = await keyWordSearch({ keyword: keyword, pageSize: 20 });
      if (!success) return;
      this.heatListData = data ? data.list : [];
      this.heatListData.forEach(item => {
        function replaceAndHighlight(text, keyword) {
          const newtext = text.split('').map(char => {
            // 如果关键字中包含该字符，替换为带样式的字符
            return keyword.includes(char) ? `<span style="color:#409EFF;">${char}</span>` : char;
          });
          return newtext.join(''); // 返回处理后的字符串
        }
        item.description = replaceAndHighlight(item.description, this.highlightKeyword);
        item.title = replaceAndHighlight(item.title, this.highlightKeyword);

        if(item.images){
          let privateImageslevel = [];
          let publicityImageslevel = [];
          let levelimages = JSON.parse(item.images);
          levelimages.public = levelimages.public?.length === 0 ? [] : levelimages.public || [];
          levelimages.private = levelimages.private?.length === 0 ? [] : levelimages.private || [];
          if (!levelimages.private || (levelimages.private && levelimages.private.length == 0)) {
            for (let i = 0; i < item.privateImgCount; i++) {
              privateImageslevel.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
            }
          } else {
            privateImageslevel = levelimages.private;
          }
          publicityImageslevel = levelimages.public;
          item.images = [];
          item.images = [...privateImageslevel, ...publicityImageslevel];
        }
      })
    },
    leftMethod(val) {
      latestList({latest: val, keyword: this.ListInfo.keyword ? this.ListInfo.keyword : '',commentPageSize: 3}).then(({ data, success }) => {
      if (!success) return;
      this.problemData = data.list;
      this.problemData.forEach(item => {
        item.question = item.question.replace(/<[^>]+>/g,'')
        let oRegExp  =   new RegExp('('+this.highlightKeyword+')','ig');
        item.question = item.question.replace(oRegExp,`<span style="color:#409EFF;">$1</span>`)
        item.valueTag = item.linkTag
        item.linkValue = item.linkUrl
        // if(item.linkId > 0){
        //   const params = { id: item.linkId };
        //   kbsTaglist(params).then(({ data, success }) => {
        //     if (!success || data.length == 0) return;
        //     item.valueTag = data[0].value;
        //     item.linkValue = data[0].linkValue;
        //   });
        // }
        item.communicationAnswer = false
        item.needApply = '0'
        item.content = null
        item.seeMoreCheck = false;
        item.currentPage = 2
        if(item.images){
          let privateImageslevel = [];
          let publicityImageslevel = [];
          let levelimages = JSON.parse(item.images);
          levelimages.public = levelimages.public?.length === 0 ? [] : levelimages.public || [];
          levelimages.private = levelimages.private?.length === 0 ? [] : levelimages.private || [];;
          if (!levelimages.private || (levelimages.private && levelimages.private.length == 0)) {
            for (let i = 0; i < item.privateImgCount; i++) {
              privateImageslevel.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
            }
          } else {
            privateImageslevel = levelimages.private;
          }
          publicityImageslevel = levelimages.public;
          item.images = [];
          item.images = [...privateImageslevel, ...publicityImageslevel];
        }
        if (item.comments && item.comments.length > 0) {
          item.comments.forEach(com => {
            com.communicationSubAnswer = false
            com.needApply = '0'
            let privateImagesinput = [];
            let publicityImagesinput = [];
            if (com.images) {
              let images = JSON.parse(com.images);
              images.public = images.public?.length === 0 ? [] : images.public || [];
              images.private = images.private?.length === 0 ? [] : images.private || [];
              if (!images.private || (images.private && images.private.length == 0)) {
                for (let i = 0; i < com.privateImgCount; i++) {
                  privateImagesinput.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
                }
              } else {
                privateImagesinput = images.private;
              }
              publicityImagesinput = images.public;

              com.images = [];
              com.images = [...privateImagesinput, ...publicityImagesinput];
            } else {
              com.images = [];
            }
          })
        }
      })
    })
    },
    async inMethod(val) {
      const { data, success } = await latestList({ latest: val , keyword:this.ListInfo.keyword ? this.ListInfo.keyword : '', commentPageSize: 3 });
      if (!success) return;
      this.questionsData = data.list;
      this.questionsData.forEach(item => {
        item.question = item.question.replace(/<[^>]+>/g,'')
        let oRegExp  =   new RegExp('('+this.highlightKeyword+')','ig');
        item.question = item.question.replace(oRegExp,`<span style="color:#409EFF;">$1</span>`)
        item.recoverAnswer = false
        item.needApply = '0'
        item.content = null
        item.seeMoreCheck = false;
        item.currentPage = 2
        if(item.images){
          let privateImageslevel = [];
          let publicityImageslevel = [];
          let levelimages = JSON.parse(item.images);
          levelimages.public = levelimages.public?.length === 0 ? [] : levelimages.public || [];
          levelimages.private = levelimages.private?.length === 0 ? [] : levelimages.private || [];
          if (!levelimages.private || (levelimages.private && levelimages.private.length == 0)) {
            for (let i = 0; i < item.privateImgCount; i++) {
              privateImageslevel.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
            }
          } else {
            privateImageslevel = levelimages.private;
          }
          publicityImageslevel = levelimages.public;
          item.images = [];
          item.images = [...privateImageslevel, ...publicityImageslevel];
        }
        if (item.comments && item.comments.length > 0) {
          item.comments.forEach(com => {
            com.questionsSubAnswer = false
            com.needApply = '0'
            let privateImagesinput = [];
            let publicityImagesinput = [];
            if (com.images) {
              let images = JSON.parse(com.images);
              images.public = images.public?.length === 0 ? [] : images.public || [];
              images.private = images.private?.length === 0 ? [] : images.private || [];
              if (!images.private || (images.private && images.private.length == 0)) {
                for (let i = 0; i < com.privateImgCount; i++) {
                  privateImagesinput.push('https://s2.loli.net/2024/08/13/eCcHZRwiOTJ3nNE.png');
                }
              } else {
                privateImagesinput = images.private;
              }
              publicityImagesinput = images.public;

              com.images = [];
              com.images = [...privateImagesinput, ...publicityImagesinput];
              // let images = JSON.parse(com.images);
              // // com.images = JSON.parse(com.images);
              // //com.imager = com.images
              // com.privateImagesinput = images.private
              // com.publicityImagesinput = images.public
            } else {
              com.images = [];
            }
          })
        }
        if (item.question) {
          // 使用正则表达式找到第一个 <img> 标签，并在其前面插入 <br> 标签
          item.question = item.question.replace(/<img/, '<br><img');
        }
      })
    },
    async getList() {
      if(this.ListInfo.keyword){
        let keyword = this.ListInfo.keyword
        this.highlightKeyword = keyword.replace(/\s/g, "")
      }else{
        this.highlightKeyword = ''
      }
      await this.leftMethod(1)
      await this.inMethod(0)
      await this.init(this.ListInfo.keyword)
      await this.onGetReply()
    },

    async onAcquireMethod() {
      const { data, success } = await getLoginInfo();
      if (!success) return;
      this.personageMessage.avatar = data.user.avatar
      this.personageMessage.userName = data.user.userName
      let section = ''
      data.roles.forEach((item, index) => {
        if (item.name.includes('部')) {
          section += item.name + ','
        }
      })
      this.personageMessage.section = section
    },
  }
}
</script>

<style scoped lang="scss">
.container {
  display: flex;
  flex: 2;
  height: 84%;
  margin-top: 2.3%;
}
::v-deep .imgstyle{
  width: 25px;
  height: 25px;
}
.popular-questions {
  background-color: white;
  flex: 1 1 auto;
  margin: 0 1%;
  padding: 1%;
  border-radius: 10px;
}

.recent-issues,
.hot-issues {
  height: 100%;
  background-color: white;
  padding: 1%;
  border-radius: 10px;
  flex: 0 0 30%;
}

// .popular-questions,
.hot-issues {
  flex: 1 1 auto;
  margin: 0 1%;
}

.questionImg {
    position: relative;

    .imgTips {
        position: absolute;
        left: 0;
        color: #ff0000;
        font-size: 14px;
    }
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 2%;
}

.title-text {
  font-weight: bold;
  font-size: 20px;
}

.buttons {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}

.buttons el-button {
  margin: 0;
}

.scroll-container {
  margin: 5% 0;
  height: 94%;
}

.item {
  display: flex;
  flex-direction: column;
  background-color: #f5f6f8;
  // margin: 5% 0;
  padding: 3% 2%;
  border-radius: 10px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #eaedf3;
}

.item-image {
  flex: 0 0 5%;
  margin-right: 2%;
  justify-content: center;
}

.circle-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.item-content {
  flex: 1;
}

.item-info {
  display: flex;
  align-items: center;
  margin: 3% 0;
  font-size: 14px;
}

.name {
  margin-right: 2%;
}

.department {
  color: #FFA200;
}

.item-description {
  padding-bottom: 2%;
  font-size: 13px;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  color: #cdcdcd;
  font-size: 13px;
}

.actions {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.rightmargin {
  margin-right: 3px;
}

.more-item {
  // display: flex;
  padding: 10px 0 0 12%;
}

.more-image {
  flex: 0 0 5%;
  margin-right: 2%;
  justify-content: center;
  align-items: center;
}

.more-content {
  flex: 1;
}

.more-info {
  margin: 3% 0;
  font-size: 14px;
}

.more-description {
  padding-bottom: 2%;
  white-space: normal;
  font-size: 13px;
}

.more-footer {
  color: #cdcdcd;
  font-size: 13px;
}

.more-actions {
  margin-top: 5px;
  color: #cdcdcd;
  display: flex;
  justify-content: center;
}

.Hotpattern {
  display: inline-block;
  padding: 0 2px;
  text-align: center;
  // vertical-align: middle;
  font-style: normal;
  color: #fff;
  overflow: hidden;
  line-height: 14px;
  height: 14px;
  font-size: 12px;
  border-radius: 4px;
  font-weight: 200;
  background-color: #f60;
}

.icon-fill-red {
  // background-color: red;
  -webkit-mask-image: url('./images/down.png');
  mask-image: url('./images/down.png');
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
}

.icon-fill-blue {
  // background-color: red;
  -webkit-mask-image: url('./images/赞.png');
  mask-image: url('./images/赞.png');
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
}

.icon {
  display: inline-block;
  width: 21px;
  height: 26px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 5px;
}

.icon-label-dot-square {
  background-image: url('./images/comment.png');
}

.icon-label-accept {
  background-image: url('./images/accept.png');
}

.icon-label-accepted {
  background-image: url('./images/accepted.png');
}

.icon-label-reject {
  background-image: url('./images/reject.png');
}

.icon-label-rejected {
  background-image: url('./images/rejected.png');
}

.icon-label-Frame {
  background-image: url('./images/Frame.png');
}


.icon-label-search {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* 水平左对齐 */
    padding-left: 32px;
}

.icon-label-search::before {
    content: ''; /* 使用伪元素作为图标 */
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('./images/搜索.png');
    background-size: contain; /* 确保图标保持比例 */
    background-repeat: no-repeat;
    margin-right: 10px;
}

.icon-label-praise {
  background-image: url('./images/点赞.png');
}

.icon-label-acquiesce {
  background-image: url('./images/默认点赞.png');
}

.icon-label-disfavor {
  background-image: url('./images/down.png');
}

.icon-label-maintenance {
  background-image: url('./images/最近沟通.png');
}

.icon-label-questions {
  background-image: url('./images/热门答疑.png');
}

.icon-label-problem {
  background-image: url('./images/热门问题.png');
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

::v-deep .custom-input .el-input__inner {
  height: 60px;
  line-height: 60px;
}

::v-deep .custom-input .el-input__inner {
  border-radius: 7px;
}

::v-deep .custom-input .el-input__inner::placeholder {
    padding-left: 10px; /* 向右偏移10px */
    font-size: 14px;
}

::v-deep .no-inputborder .el-input__inner {
    border: none; /* 去掉边框 */
    box-shadow: none; /* 去掉阴影 */
}

.el-icon-my-message,
.el-icon-Label-maintenance,
.el-icon-my-statistics,
.el-icon-Problem-maintenance {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.el-icon-my-message {
  background-image: url('./images/提问.png');
}

.el-icon-my-statistics {
  background-image: url('./images/statistics.png');
}

.el-icon-Label-maintenance {
  background-image: url('./images/标签维护.png');
}

.el-icon-Problem-maintenance {
  background-image: url('./images/问题维护.png');
}

//背景图片
.login {
  background: url("./images/bj.jpg") no-repeat center;
}

.requestView {
  background: url("./images/申请查看高亮.png") no-repeat center;
}

.logtag {
  background: url("./images/标签.jpg") no-repeat center;
  background-size: cover;
  float: right;
  width: auto;
  min-width: 15%;
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap; //禁止换行
  overflow: hidden; //超出部分隐藏
  padding: 0 12px;
}

.askquestion {
  width: 27%;
  display: inline-flex;
  align-items: center;
  border: 1px solid #409EFF;
  border-radius: 5px;
  color: #409EFF;
  justify-content: center;
  height: 39px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-left: 3%;

  .askquestion_icon {
    margin-right: 5px;
    margin-top: 2px;
  }
}

.askquestion:hover {
  background-color: #ecf5ff;
}



::v-deep img {
  max-width: 100px;
  max-height: 100px;
}

.imageList_box {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  max-height: 300px; /* 设定最大高度，你可以根据需要调整 */
  box-sizing: border-box;
  padding: 5px;

  .childimageList {
    position: relative;
    width: 39px;
    height: 39px;

    .childimgcss ::v-deep img {
      min-width: 39px !important;
      min-height: 39px !important;
      width: 39px !important;
      height: 39px !important;
    }
  }

  .imageList {
    position: relative;
    width: 45px;
    height: 45px;

    .imgcss ::v-deep img {
      min-width: 45px !important;
      min-height: 45px !important;
      width: 45px !important;
      height: 45px !important;
    }

  }
}

.del {
  position: absolute;
  top: 0px;
  right: 0px;
  font-size: 14px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  // border: 1px solid white; /* 灰色边框 */
  // background-color: white; /* 白色背景 */
  color: gray; /* 灰色字体 */
  text-align: center;
  line-height: 12px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.del:hover {
  background-color: gray;
  color: white;
}

::v-deep .no-border .el-textarea__inner {
  border: none;
  box-shadow: none;
}

.pointer-cursor {
  cursor: pointer;
}

.reply-span {
  cursor: pointer;
}

.reply-span:hover {
  color: #409EFF;
}

.reply-active {
  color: #409EFF;
}

.red-text {
    color: red;
    font-size: 13px;
}

.text-style {
  color: #409EFF;
  font-size: 13px;
  width: 8%;
  display: flex;
  justify-content: center;
}

.work-out {
  cursor: pointer;
  // margin-left: 3px;
}

.work-out:hover {
  color: #409EFF;
}

.right-container {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.reply-text {
  position: relative;
  margin-right: 3px;
  color: #409EFF;
  font-size: 15px;
}

.circle-badge {
  position: absolute;
  top: -16px;
  right: -15px;
  background-color: red;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 11px;
}

.right-content {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 10px;
}

.overlay-container {
  position: absolute;
  top: 100%;
  left: 27%;
  transform: translateX(-50%);
  width: 50%;
  height: 300px;
  background: #fff;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.1);
  border-radius: 7px;
  // z-index: 3000;
  padding: 10px;
  z-index: 99;
}

::v-deep .el-select__tags-text {
  max-width: 115px;
}

.description-limited {
    max-width: 460px;
    word-wrap: break-word; /* 自动换行 */
    overflow-wrap: break-word; /* 支持在单词中间换行 */
    margin: 0 auto;
}
</style>
