<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 280px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :clearable="true" :picker-options="pickerOptions"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.styleCode" placeholder="系列编码" style="width:120px;" clearable
                        maxlength="40" />
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'burststyleprotectstyle20231019'" :border="true" :align="'center'"
            :tablekey="'burststyleprotectstyle20231019'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0"   :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { platformlist } from '@/utils/tools';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatPlatform, formatTime, pickerOptions } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { GetBurstStyleProtectStylePageList, ExportBurstStyleProtectStyle } from '@/api/operatemanage/burststyleprotect'

const tableCols = [
    { istrue: true, prop: 'styleCode', label: '系列编码', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'lossCount', label: '爆款次数', sortable: 'custom', width: '150', type: "click", handle: (that, row) => that.showLossDtl(row) },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(1, 'month'), "YYYY-MM-DD");
const endDate = formatTime(dayjs(), "YYYY-MM-DD");

export default {
    name: "burststyleprotectstyle",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                isLoss: true,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            selrows: [],

            platformlist: platformlist,
            shopList: [],
            directorGroupList: [],
        };
    },
    async mounted() {
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getCondition() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            else {
                this.filter.startDate = '2020-01-01';
                this.filter.endDate = '2030-01-01';
            }
            let pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            return params;
        },
        async getList() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.listLoading = true;
            const res = await GetBurstStyleProtectStylePageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onExport() {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true;
            const res = await ExportBurstStyleProtectStyle(params)
            this.listLoading = false;
            if (!res?.data) {
                this.$message({ type: 'error', message: '没有数据可导出或导出失败!' });
                return;
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', "爆款保护系列编码_" + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },

        async showLossDtl(row) {
            const params = this.getCondition();
            if (params === false) {
                return;
            }
            console.log(params);
            this.$showDialogform({
                path: `@/views/operatemanage/burststyleprotect/burststyleprotectlossdtl.vue`,
                title: '爆款次数-' + row.styleCode,
                autoTitle: false,
                args: {
                    startDate: params.startDate,
                    endDate: params.endDate,
                    styleCode: row.styleCode,
                },
                height: "400px",
                width: '40%',
            });
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>

