<template>
  <div style="height: 100%; width: 100%;">
    <MyContainer>
        <template #header>
        <div class="top">
            <dateRange style="width: 220px;" :startDate.sync="ListInfo.startQueryTime" :endDate.sync="ListInfo.endQueryTime" class="publicCss" />

            <el-select v-model="ListInfo.regionNameArr" placeholder="片区" class="publicCss" collapse-tags multiple clearable style="width: 250px">
                <el-option v-for="item in options.regionNamelist" :key="item" :label="item" :value="item" />
            </el-select>
            <el-select filterable v-model="ListInfo.platformsArr" placeholder="请选择平台" multiple collapse-tags
              @change="onchangeplatform" clearable style="width: 250px">
              <el-option v-for="(item,i) in options.platformlist" :key="i"  :label="item" :value="item" />
            </el-select>
            <el-select filterable v-model="ListInfo.deptNameArr" collapse-tags clearable placeholder="运营组"
              style="width: 250px" multiple>
              <el-option key="无运营组" label="无运营组" :value="'无运营组'"></el-option>
              <el-option v-for="(item, i) in options.grouplist" :key="i" :label="item.label" :value="item.value"
                 />
            </el-select>
            <div>
            <el-button type="primary" @click="getList('search')">搜索</el-button>
            <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
            <div style="color: red; margin-left: 5px;font-size: 14px;display: flex;align-items: center;">
            数据来源:各平台运营人员业绩统计
            </div>

        </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
        :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" border
        style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" :showsummary='true' :summaryarry='summaryarry'>
        </vxetablebase>
        <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
    </MyContainer>

    <el-dialog title="对比" :visible.sync="dialogVisibleEdit" width="85%" height="100%" v-dialogDrag top="50px">
    <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :ListInfo="ListInfo" :editInfo="editInfo" @search="closeGetlist" :options="options"
         @onchangeplatform="onchangeplatform" />
    </el-dialog>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist } from '@/utils/tools'
import { getDirectorGroupList, getDirectorList, getList as getshopList } from '@/api/operatemanage/base/shop'
// import { formatPlatform, formatTime, formatYesornoBool, formatLinkProCode, platformlist,formatProCodeStutas3 } from "@/utils/tools";
import { getOperatePerformanceProportionPage, getOperatePerformanceProportionPageReport, getOperateListValue, getOperatePerformanceProportionExport } from '@/api/people/peoplessc.js'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import departmentEdit from "./departmentEdit.vue";
import { init } from "echarts";
const tableCols = [
  { sortable: 'custom', width: '60', align: 'center', prop: 'regionName', label: '片区', fixed: 'left' },
  { sortable: 'custom', width: '60', align: 'center', prop: 'platform', label: '平台', fixed: 'left' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'deptName', label: '运营组', fixed: 'left' },
  { sortable: 'custom', width: '60', align: 'center', prop: 'userCount', label: '人数',type: 'click', handle:(that,row)=>that.handleEdit(row, '人数') },
  { sortable: 'custom', width: '80', align: 'center', prop: 'userCountProportion', label: '人数占比', type: 'click', handle:(that,row)=>that.handleEdit(row, '人数占比'),formatter: (row) => row.userCountProportion !== null ? row.userCountProportion+'%' : '' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'threeUserCount', label: '三项低于均值人数', type: 'click', handle:(that,row)=>that.handleEdit(row, '三项低于均值人数'), },
  { sortable: 'custom', width: '80', align: 'center', prop: 'threeUserCountProportion', label: '三项占比', type: 'click', handle:(that,row)=>that.handleEdit(row, '三项占比'),formatter: (row) => row.threeUserCountProportion !== null ? row.threeUserCountProportion+'%' : '' },
  { sortable: 'custom', width: '90', align: 'center', prop: 'differenceValue', label: '差异值', type: 'click', handle:(that,row)=>that.handleEdit(row, '差异值'),formatter: (row) => row.differenceValue !== null ? row.differenceValue+'%' : '' },
  { sortable: 'custom', width: '130', align: 'center', prop: 'fourUserCount', label: '四项低于均值人数', type: 'click', handle:(that,row)=>that.handleEdit(row, '四项低于均值人数'), },
  { sortable: 'custom', width: '80', align: 'center', prop: 'fourUserCountProportion', label: '四项占比', type: 'click', handle:(that,row)=>that.handleEdit(row, '四项占比'),formatter: (row) => row.fourUserCountProportion !== null ? row.fourUserCountProportion+'%' : '' },
  { sortable: 'custom', width: '70', align: 'center', prop: 'orderCount', label: '订单量', type: 'click', handle:(that,row)=>that.handleEdit(row, '订单量'), },
  { sortable: 'custom', width: '80', align: 'center', prop: 'saleAmount', label: '销售金额', type: 'click', handle:(that,row)=>that.handleEdit(row, '销售金额'), },
  { sortable: 'custom', width: '80', align: 'center', prop: 'profit33', label: '毛四利润', type: 'click', handle:(that,row)=>that.handleEdit(row, '毛四利润'), },
  { sortable: 'custom', width: '90', align: 'center', prop: 'orderCountProportion', label: '订单量占比', type: 'click', handle:(that,row)=>that.handleEdit(row, '订单量占比'), formatter: (row) => row.orderCountProportion !== null ? row.orderCountProportion+'%' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'saleAmontProportion', label: '销售金额占比', type: 'click', handle:(that,row)=>that.handleEdit(row, '销售金额占比'), formatter: (row) => row.saleAmontProportion !== null ? row.saleAmontProportion+'%' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'profit33Proportion', label: '毛四利润占比', type: 'click', handle:(that,row)=>that.handleEdit(row, '毛四利润占比'), formatter: (row) => row.profit33Proportion !== null ? row.profit33Proportion+'%' : '' },
  { sortable: 'custom', width: '140', align: 'center', prop: 'humanResource', label: '按毛四分配人力资源', type: 'click', handle:(that,row)=>that.handleEdit(row, '按毛四分配人力资源'), },
  { sortable: 'custom', width: '90', align: 'center', prop: 'differenceUserCount', label: '人数差异值', type: 'click', handle:(that,row)=>that.handleEdit(row, '人数差异值'), },
  { sortable: 'custom', width: '100', align: 'center', prop: 'differenceValueHalf', label: '差异值*50%', type: 'click', handle:(that,row)=>that.handleEdit(row, '差异值*50%'), },
  { sortable: 'custom', width: '130', align: 'center', prop: 'humanResourceHalf', label: '按毛四定编制人数', type: 'click', handle:(that,row)=>that.handleEdit(row, '按毛四定编制人数'), },
  { sortable: 'custom', width: '80', align: 'center', prop: 'increaseUserCount', label: '增减人数', type: 'click', handle:(that,row)=>that.handleEdit(row, '增减人数'), },
]
export default {
  name: "scanCodePage",
  components: {
    MyContainer, vxetablebase, dateRange, departmentEdit
  },
  data() {
    return {
        dialogVisibleEdit: false,
      options: {
        platformlist: [
          '淘系', '拼多多', '抖音', '京东'
        ],
        grouplist: []
      },
      summaryarry: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startQueryTime: dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),//开始时间
        endQueryTime: dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),//结束时间
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: true,
      pickerOptions,
      isExport: false,
    //   grouplist: [],
    }
  },
  async mounted() {
    var res2 = await getDirectorGroupList();
    this.options.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });

    var res3 = await getOperateListValue('fullName');
    this.options.regionNamelist = res3.data;

    // var res3 = await getOperateListValue('fullName');
    // this.options.platformlist = res3.data;

 /*   var res3 = await getOperateListValue('deptName');
    this.options.grouplist = res3.data;*/
    await this.getList()
  },
  methods: {
    async onchangeplatform(val) {
      this.categorylist = []
    //   this.filter.shopCodeList = []
    //   const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
    //   this.shopList = res1.data.list
    //   return res1.data.list;
    },
    closeGetlist(){
        this.dialogVisibleEdit = false;
        this.getList()
    },
    handleEdit(row, name){
        this.editInfo = row;
        this.editInfo.checkName = name;
        this.dialogVisibleEdit = true;

    },
    async init() {
    //   var res2 = await getDirectorGroupList();
    //   this.options.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
    },
    //导出数据,使用时将下面的方法替换成自己的接口
    async exportProps() {
      this.isExport = true
      await getOperatePerformanceProportionExport(this.ListInfo).then((data ) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '运营组占比' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      this.ListInfo.fullName = this.ListInfo.regionNameArr.join(',');
      this.ListInfo.platform = this.ListInfo.platformsArr.join(',');
      this.ListInfo.groupId = this.ListInfo.deptNameArr.join(',');
      // 使用时将下面的方法替换成自己的接口
      try {
        const { data, success } = await getOperatePerformanceProportionPage(this.ListInfo)
        if (success) {
           function addSumSuffix(obj) {
            const newObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    if(key == 'differenceValue'|| key == 'profit33Proportion'|| key == 'saleAmontProportion'
                    || key == 'orderCountProportion'|| key == 'fourUserCountProportion'|| key == 'differenceValue'
                    || key == 'threeUserCountProportion'|| key == 'userCountProportion'){
                        obj[key] = (obj[key] || obj[key] === 0) ? obj[key] + '%' : ''
                    }
                    newObj[`${key}_sum`] = obj[key];
                }
            }
            return newObj;
            }
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = addSumSuffix(data.summary);
          this.loading = false
        } else {
          //获取列表失败
          this.loading = false
          this.$message.error('获取列表失败')
        }
      } catch (error) {
        this.loading = false
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  .publicCss {
    width: 200px;
    margin: 0 5px 5px 0px;
  }
}
</style>
