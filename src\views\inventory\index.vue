<template>
  <my-container v-loading="pageLoading">
    <template #header> </template>
    <template>
      <div>
        <el-row :gutter="0" class="row-condition">
          <el-col :span="3" :offset="1">
            <div class="my-title">当前压单概况</div>
          </el-col>
           <el-col :span="18" :offset="1">
            <div class="my-title" style="text-align: right;">
              <el-link type="primary" target="_blank" @click="openGeneral()">历史压单</el-link>
             </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div class="grid-header">总压单量 
                <el-tooltip class="item" effect="dark" content="总压单量" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text">
                <span>{{abnormalModel.totalWaitOrderNum}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div class="grid-header">实际压单量 
                <el-tooltip class="item" effect="dark" content="实际压单量" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text">
                <span>{{abnormalModel.sjWaitOrderNum}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div class="grid-header">预售压单量 
                <el-tooltip class="item" effect="dark" content="预售压单量" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text">
                <span>{{abnormalModel.preSaleOrderNum}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div class="grid-header">淘客压单量 
                <el-tooltip class="item" effect="dark" content="淘客压单量" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text">
                <span>{{abnormalModel.taoKeOrderNum}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div class="grid-header">总压品数 
                <el-tooltip class="item" effect="dark" content="总压品数" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text">
                <span>{{abnormalModel.totalWaitGoodNum}}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div class="grid-header">未完结采购单 
                <el-tooltip class="item" effect="dark" content="未完结采购单" placement="top-end"><span><i class="el-icon-question"></i></span></el-tooltip>
              </div>
              <div class="grid-text">
                <span>{{abnormalModel.nonCompletedPurchase}}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <br>
        <el-row>
          <el-col :span="24" :offset="1">
           <el-card  class="abnormalcard">
               <div slot="header" class="clearfix grid-header" style="text-align:left;margin: 0;padding: 0;">
                <span>采购组概况   &nbsp; &nbsp; &nbsp; &nbsp;<span style="color: red;font-weight: bold;font-size: large;">测试数据仅供参考</span></span>
              </div>
              <div style="margin:0px; word-break: break-all;height:800px">
                <el-tabs v-model="activeName" style="height:94%;" @tab-click="handleClick">
                  <el-tab-pane label="实时数据" name="tabOrder" style="height:100%;">
                <div style="overflow-x: auto; height:100%">
                    <el-table :data="abnormalModel.cgItems" v-loading="listLoading" height="100%" style="width: 100%" :header-cell-style="{ 'background-color': '#f5f7fa', 'color': '#909399', 'font-weight': 'bold' }">
                      <!-- <el-table-column prop="firstTime" label="最早时间" width="180"></el-table-column> -->
                      <el-table-column align="center" prop="name" label="采购组" width="150">
                        <template slot-scope="scope" >
                          <el-link type="primary" @click="openanalysis(scope.row['groupOrBrandId'])">{{scope.row['name']}}</el-link>
                        </template>
                      </el-table-column>
                      <el-table-column align="right" prop="goodsCodeCount" label="负责编码数" width="100"></el-table-column>
                      <el-table-column align="right" prop="totalWaitOrderNum" label="总压单量" width="80"></el-table-column>
                      <el-table-column align="right" prop="sjWaitOrderNum" label="实际压单量" width="90"></el-table-column>
                      <el-table-column align="right" prop="scWaitOrderNum" label="审错压单量" width="90"></el-table-column>
                      <el-table-column align="right" prop="preSaleOrderNum" label="预售压单量" width="90"></el-table-column>
                      <el-table-column align="right" prop="taoKeOrderNum" label="淘客压单量" width="90"></el-table-column>
                      <el-table-column align="right" prop="totalWaitGoodNum" label="压品数" width="80"></el-table-column>
                      <el-table-column align="right" prop="nonCompletedPurchase" label="未完结采购单" width="100"></el-table-column>
                      <el-table-column align="right" prop="nonInAmont" label="未入库总金额" width="100"></el-table-column>
                      <el-table-column align="right" prop="endAmont"  label="库存资金" width="100" >
                        <!-- <template slot-scope="scope">
                          <span> {{myformatmoney(scope.row.endAmont)}}</span>
                        </template> -->
                      </el-table-column>
                      <el-table-column align="right" prop="purchaseCount" label="当月采购单数" width="100" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
                      <el-table-column align="right" prop="purchaseGoodsCodeCount" label="当月采购编码数" width="120" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
                      <el-table-column align="right" prop="fStockCount" label="当月负库存数" width="110" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])"></el-table-column>
                      <el-table-column align="right" prop="fStockRate" label="当月负库存率" width="100" v-if="checkPermission(['api:inventory:warehouse:fstockrat'])">
                        <template slot-scope="scope" >
                          <el-link type="primary" @click="openFstockrate(scope.row['groupOrBrandId'])">{{scope.row['fStockRate']}}%</el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                </div>
                </el-tab-pane>
                  <el-tab-pane label="月汇总" name="moonindex" style="height:100%;">
                    <div>
                      <moonindex ref="moonindex" style="height: 800px"></moonindex>
                    </div>  
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-dialog :visible.sync="dialogVisible" v-dialogDrag :show-close="false">
          <abnormalgeneral :filter="filter"  style="height: 500px"></abnormalgeneral> 
        </el-dialog>

         <el-dialog :visible.sync="dialogFstockrateVisible" v-dialogDrag :show-close="false">
           <fstockrate ref="fstockrate" style="height: 450px"></fstockrate> 
        </el-dialog>
        
        <el-dialog :visible.sync="dialoganalysisVisible" v-dialogDrag :show-close="false">
           <abnormalgeneralanalysis ref="abnormalgeneralanalysis" style="height: 450px"></abnormalgeneralanalysis> 
        </el-dialog>
      </div>
    </template>
  </my-container>
</template>
<script>
import {formatmoney} from "@/utils/tools";
import {queryAbnormalGeneralNow} from '@/api/inventory/abnormal'
import MyConfirmButton from '@/components/my-confirm-button'
import MyContainer from '@/components/my-container'
import abnormalgeneral from '@/views/inventory/components/abnormalgeneral'
import fstockrate from '@/views/inventory/components/fstockrate'
import abnormalgeneralanalysis from '@/views/inventory/components/abnormalgeneralanalysis'
import moonindex from '@/views/inventory/components/moonindex'

//格式化money列：大于1 时，去掉小数点，小于1时保留小数点
var myformatmoney = function (value) {
  var money= formatmoney(
    Math.abs(value) > 1 ? Math.round(value,2) : Math.round(value, 1)
   );
  return money
};

export default {
  name: 'Roles',
  components: { MyConfirmButton,MyContainer,abnormalgeneral,fstockrate,abnormalgeneralanalysis,moonindex},
  data() {
    return {
      activeName:"tabOrder",
      abnormalModel:{firstTime:'',totalWaitOrderNum:0,totalWaitGoodNum:0,cGItems:[],yYItems:[] },
      abnormalModelLoading: false,
      pageLoading: false,
      dialogVisible:false,
      dialogFstockrateVisible:false,
      dialoganalysisVisible:false,
      filter:{},
      listLoading:true
    }
  },
 async mounted() {
   await this.getAbnormal();
  },
  methods: {
    async getAbnormal() {
      this.listLoading = true
      const res = await queryAbnormalGeneralNow({type:2})
      this.listLoading = false
      if (!res?.success) return 
      this.abnormalModel=res.data;

    res.data?.cgItems.forEach(f =>{
      f.endAmont = formatmoney(f.endAmont)
      f.nonInAmont=formatmoney(f.nonInAmont)
    })
    },
    async openGeneral(){
      this.dialogVisible=true;
      this.filter={};
    },
    async openFstockrate(brandid){
      this.dialogFstockrateVisible=true;
      this.$nextTick(() => {
          this.$refs.fstockrate.onSearch(brandid);
      });
    },
    async openanalysis(brandid){
      this.dialoganalysisVisible=true;
      this.$nextTick(() => {
          this.$refs.abnormalgeneralanalysis.onSearch(brandid);
      });
    },
    async handleClick(tab, event) {
       if (this.activeName == 'moonindex') {
         this.$nextTick(() =>{
           this.$refs.moonindex.onSearch()
         })
       }
      }
  }
}
</script>
<style lang="scss" scoped>
.row-condition {
  margin-top: 10px;
  margin-bottom: 20px;
  color: #606266;
  font-weight: bold;
} 
.grid-header {
  color: #606266;
  font-size: 12px;
  margin-top: 10px;
  font-weight: bold;
}
.grid-text{
  color: #606266;
  font-size: 12px;
  margin-top: 10px;
  padding: 10px;
  font-weight: bold;
}
.el-col {
  text-align: center;
}
.el-row {
  margin-right: 50px;
}
.el-icon-question {
  color: #909399;
}
.abnormalcard {
   padding: 0;
  }
</style>
