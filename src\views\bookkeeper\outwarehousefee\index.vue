<template>
    <my-container v-loading="pageLoading">
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="数据源" name="first1" style="height: 100%">
                <outwarehousefeesource ref="outwarehousefeesource" style="height: 100%" ></outwarehousefeesource>
            </el-tab-pane>
            <el-tab-pane label="出仓费" name="first2" style="height: 100%">
                <outwarehousefee ref="outwarehousefee" style="height: 100%" ></outwarehousefee>
            </el-tab-pane>
            <el-tab-pane label="仓库薪资(新)" name="first3" style="height: 100%">
                <warehousesalary ref="warehousesalary" style="height: 100%" ></warehousesalary>
            </el-tab-pane>
            <el-tab-pane label="临时工仓库薪资" name="first4" style="height: 100%">
                <warehousesalary_temp ref="warehousesalary_temp" style="height: 100%" ></warehousesalary_temp>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
  
<script>
import MyContainer from "@/components/my-container";
import outwarehousefeesource from "./outwarehousefeesource.vue";
import outwarehousefee from "./outwarehousefee.vue";
import warehousesalary from "./warehousesalary.vue";
import warehousesalary_temp from "./warehousesalary_temp.vue";
export default {
    name: "outwarehousefeeindex",//
    components: {
        MyContainer, outwarehousefeesource, outwarehousefee,warehousesalary,warehousesalary_temp
    },
    data() {
        return {
            that: this,
            pageLoading: false,
            activeName: "first1",
        };
    },
    async mounted() {

    },
    methods: {

    },
};
</script>
  
<style lang="scss" scoped></style>
  