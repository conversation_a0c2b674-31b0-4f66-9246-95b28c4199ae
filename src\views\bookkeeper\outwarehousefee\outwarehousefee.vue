<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model.trim="filter.outWarehouseFeeType" filterable placeholder="配货打包"
                        style="width:100px" @change="onSearch" clearable>
                        <el-option label="配货" value="配货" />
                        <el-option label="打包" value="打包" />
                        <el-option label="其他" value="其他" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model.trim="filter.feeType" filterable placeholder="类型" style="width:140px"
                        @change="onSearch" clearable>
                        <el-option label="拣货_订单数" value="拣货_订单数" />
                        <el-option label="打包_总" value="打包_总" />
                        <el-option label="出库验货_总" value="出库验货_总" />
                        <el-option label="发货" value="发货" />
                        <el-option label="发货抽检登记" value="发货抽检登记" />
                        <el-option label="发货质检" value="发货质检" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    操作时间
                    <el-date-picker style="width: 240px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        :clearable="false" :picker-options="pickerOptions"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: 0;">
                    付款时间
                    <el-date-picker style="width: 240px" v-model="filter.daterange2" type="daterange"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :picker-options="pickerOptions" clearable></el-date-picker>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.warehouseCode" style="width: 240px" size="mini" clearable>
                        <el-option v-for="item in myWarehouseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <!-- <el-input v-model.trim="filter.orderNoInner" placeholder="内部单号" style="width:120px;" clearable
                        oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}"
                        maxlength="10" /> -->

                    <inputYunhan ref="productCode2" :inputt.sync="filter.orderNoInners" v-model="filter.orderNoInners"
                        class="publicCss" placeholder="内部单号/多条请按回车" :clearable="true" :clearabletext="true"
                        :maxRows="300" :maxlength="6000" @callback="orderNoInnerBack" title="内部单号">
                    </inputYunhan>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <!-- <el-input v-model.trim="filter.onlineOrderNumber" placeholder="线上单号" style="width:160px;" clearable
                        maxlength="40" />
                         -->

                    <inputYunhan ref="productCode3" :inputt.sync="filter.onlineOrderNumbers"
                        v-model="filter.onlineOrderNumbers" class="publicCss" placeholder="线上单号/多条请按回车"
                        :clearable="true" :clearabletext="true" :maxRows="300" :maxlength="6000"
                        @callback="onlineOrderNumberBack" title="线上单号">
                    </inputYunhan>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-select v-model="filter.platform" placeholder="请选择平台" style="width: 120px" @change="getShopList"
                        :clearable="true">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border: 0;">
                    <el-input v-model.trim="filter.shopName" placeholder="店铺名称" style="width:160px;" clearable
                        maxlength="40" />
                </el-button>


                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport" style="margin-left: 10px;">导出</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'OutWarehouseFee20231012'" :border="true" :align="'center'"
            :tablekey="'OutWarehouseFee20231012'" ref="table2" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { platformlist, formatPlatform, formatTime, pickerOptions } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import buschar from '@/components/Bus/buschar';
import inputYunhan from "@/components/Comm/inputYunhan";

import { getTbWarehouseList } from '@/api/profit/warehousewages';
import { GetyOutWarehouseOrderFeePageList, ExportOutWarehouseOrderFeeList } from '@/api/bookkeeper/outwarehousefee'

const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom', width: '80', formatter: row => (row.platform == -10 ? "线下" : formatPlatform(row.platform)) },
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom', width: '120', formatter: (row) => row.shopName },
    { istrue: true, prop: 'payTime', label: '付款时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'warehouseCode', label: '仓库', sortable: 'custom', width: '140', formatter: (row) => row.warehouseName },
    { istrue: true, prop: 'operateTime', label: '操作时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'operater', label: '操作人', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'postName', label: '一级岗位名称', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'workItem', label: '二级岗位名称', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'outCount', label: '数量', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'outNo', label: '出仓单号', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'orderNoInner', label: '内部单号', sortable: 'custom', width: '80', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { istrue: true, prop: 'onlineOrderNumber', label: '线上单号', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'defaultWages', label: '工价', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'orderFee', label: '出仓费', sortable: 'custom', width: '70' },
    { istrue: true, prop: 'outWarehouseFeeOrderOrGood', label: '计算方式', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'feeType', label: '类型', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'outWarehouseFeeType', label: '配货打包', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'createdTime', label: '计算时间', sortable: 'custom', width: '140' },
];
const tableHandles = [
    //{ label: "编辑", handle: (that, row) => that.onEdit(row) },
];
const startDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endDate = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: "outwarehousefeesource",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
        buschar, vxetablebase, inputYunhan
    },
    data() {
        return {
            that: this,
            filter: {
                daterange: [startDate, endDate],
                daterange2: [],
                feeType: "",
                outWarehouseFeeType: "",
                warehouseCode: null,
                orderNoInner: null,
                onlineOrderNumber: null,
                payStartDate: null,
                payEndDate: null,
                orderNoInners: null,
                onlineOrderNumbers: null,
            },
            pickerOptions: pickerOptions,
            tableCols: tableCols,
            tableHandles: tableHandles,
            total: 0,
            datalist: [],
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            platformlist: platformlist,

            myWarehouseList: [],
        };
    },
    async mounted() {
        //await this.onSearch();
    },
    async created() {
        await this.getWarehouseList();
    },
    methods: {
        async getWarehouseList() {
            this.myWarehouseList = [];
            const res = await getTbWarehouseList();
            if (res && res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    this.myWarehouseList.push({ label: res[i].name, value: res[i].wms_co_id })
                }
            }
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getpara() {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.daterange) {
                this.filter.startTime = this.filter.daterange[0];
                this.filter.endTime = this.filter.daterange[1];
            }
            else {
                this.$message({ type: 'error', message: '请输入操作日期!' });
                return;
            }

            this.filter.payStartTime = null;
            this.filter.payEndTime = null;
            if (this.filter.daterange2) {
                this.filter.payStartTime = this.filter.daterange2[0];
                this.filter.payEndTime = this.filter.daterange2[1];
            }

            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params;
        },
        async getList() {
            let params = this.getpara();
            console.log(params);
            this.listLoading = true;
            const res = await GetyOutWarehouseOrderFeePageList(params);
            this.listLoading = false;
            console.log(res);
            this.total = res.data?.total;
            this.datalist = res.data?.list;
            this.summaryarry = res.data?.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            let params = this.getpara();
            let warehouseName = "";
            let w = this.myWarehouseList.find(f => f.value == this.filter.warehouseCode);
            if (w) { warehouseName = w.label; }
            this.listLoading = true
            const res = await ExportOutWarehouseOrderFeeList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '出仓费导出_' + warehouseName + new Date().toLocaleString() + '.xlsx');
            aLink.click()
        },
        orderNoInnerBack(val) {
            this.filter.orderNoInners = val;
        },
        onlineOrderNumberBack(val) {
            this.filter.onlineOrderNumbers = val;
        },
    }
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
