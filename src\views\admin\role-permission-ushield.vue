<template>
  <container>
    <el-tabs v-model="activeName" class="demo-tabs" style="height: 95%;">
      <el-tab-pane label="权限" name="permission" style="height: 100%;">
        <rolePermissionTab></rolePermissionTab>
      </el-tab-pane>
      <el-tab-pane label="用户-U盾" name="ushield" style="height: 100%;">
        <ushield></ushield>
      </el-tab-pane>
    </el-tabs>
  </container>
</template>

<script>
import container from "@/components/my-container";
import rolePermissionTab from "@/views/admin/homecomponents/role-permission-tab.vue";
import ushield from "@/views/admin/homecomponents/ushield.vue";
export default {
  name: "YunHanAdminRolePermission",
  components: {
    rolePermissionTab,
    ushield,
    container
  },
  data() {
    return {
      activeName: "permission"
    };
  },

  mounted() {},

  methods: {}
};
</script>

<style lang="scss" scoped>
.container {
  padding: 10px;
}
</style>