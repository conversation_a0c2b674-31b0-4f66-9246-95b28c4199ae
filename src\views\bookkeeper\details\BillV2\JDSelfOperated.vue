<template>
  <MyContainer>
    <template #header>
      <div class="top">
        日期：
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <!--<el-select v-model="ListInfo.isStock" placeholder="地区" class="publicCss" clearable>
          <el-option :key="'是'" label="是" :value="0" />
          <el-option :key="'否'" label="否" :value="1" />
        </el-select> -->
        <el-input v-model.trim="ListInfo.documentId" placeholder="单据ID" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.documentNumber" placeholder="单据编号" maxlength="50" clearable
          class="publicCss" />
        <el-select v-model="accountTypeistList" placeholder="账单项目" multiple collapse-tags clearable filterable
          style="width: 165px;" class="publicCss">
          <el-option v-for="item in billingData" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="billTypeListList" placeholder="ERP账务类型" multiple collapse-tags clearable filterable
          style="width: 165px;" class="publicCss">
          <el-option v-for="item in accountingData" :key="item" :label="item" :value="item" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
    </template>
    <vxetablebase :id="'JDSelfOperated202411282236'" :tablekey="'JDSelfOperated202411282236'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getBillingCharge_JingDongSelfSupport } from '@/api/bookkeeper/reportdayV2'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '90', align: 'center', prop: 'yearMonthDay', label: '日期', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'documentId', label: '单据ID', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'documentNumber', label: '单据编号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'documentType', label: '单据类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'purchaseOrder', label: '采购单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'department', label: '部门', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'groupName', label: '组别', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'contractSubject', label: '合同主体', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'billingItem', label: '账单项目', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'billType', label: 'ERP账务类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'accountPayableTime', label: '进入应付账时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'documentDate', label: '单据日期', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'totalAmount', label: '总金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'nonFinishedOilInvoiceAmount', label: '非成品油开票金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'greaseInvoiceAmount', label: '润滑脂开票金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lubricatingOilInvoiceAmount', label: '润滑油开票金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'settlementOrderNumber', label: '结算单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'writeOffStatus', label: '核销状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'settlementStatus', label: '结算状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'reconciliationStatus', label: '对账状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'purchaserName', label: '采购员', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'purchaseChannel', label: '采购渠道', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'documentSubType', label: '单据子类型', },
]

const billingData = [
  '毛利保护', 'TC运费', '售后退货', '实销实结退货入库单', '未处理'
]
const accountingData = [
  '毛利保护', 'TC运费', '售后退货', '实销实结退货入库单', '未处理'
]
export default {
  name: "JDSelfOperated",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      billingData,
      accountingData,
      billTypeListList: [],
      accountTypeistList: [],
      that: this,
      ListInfo: {
        platform: 74,
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        documentId: '',//单据ID
        documentNumber: '',//单据编号
        accountTypeist: null,//账单项目
        billTypeList: null,//ERP账务类型
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      let accountTypeist = this.accountTypeistList
      let billTypeList = this.billTypeListList
      this.ListInfo.accountTypeist = accountTypeist.length > 0 ? accountTypeist.join(',') : null
      this.ListInfo.billTypeList = billTypeList.length > 0 ? billTypeList.join(',') : null
      const { data, success } = await getBillingCharge_JingDongSelfSupport(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.accountPayableTime = item.accountPayableTime ? dayjs(item.accountPayableTime).format('YYYY-MM-DD') : ''
          item.documentDate = item.documentDate ? dayjs(item.documentDate).format('YYYY-MM-DD') : ''
        })
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
