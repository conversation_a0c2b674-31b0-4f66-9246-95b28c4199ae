<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <span style="font-size: 15px;margin-right: 10px;display: flex;align-items: center;">操作时间:</span>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <span style="font-size: 15px;margin-right: 10px;display: flex;align-items: center;">操作人:</span>
        <el-input v-model.trim="ListInfo.createdUserName" placeholder="操作人" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">查询</el-button>
      </div>
    </template>
    <vxetablebase :id="'operationRecording202408041821'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
      <template #pictureUrls="{ row }">
        <div style="position: relative; display: inline-block;" v-if="row.pictureUrls">
          <el-image class="custom-image" slot="reference" :src="row.pictureUrls[0] || ''" fit="fill"
            :preview-src-list="row.pictureUrls != '' ? row.pictureUrls : ''" style="width: 40px; height: 38px;">
          </el-image>
          <span class="circle-badge">
            {{ row.pictureUrls.length }}
          </span>
        </div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { queryDamagedOrderExplainRecordAsync } from '@/api/customerservice/DamagedOrders'

import dayjs from 'dayjs'
const tableCols = [
  { width: 'auto', align: 'center', prop: 'sendWareHouse', label: '发货仓', },
  { width: 'auto', align: 'center', prop: 'managerMemberName', label: '责任上级', },
  { width: 'auto', align: 'center', prop: 'sendOrderCount', label: '发货订单数', },
  { width: 'auto', align: 'center', prop: 'damagedOrderCount', label: '损耗订单数', },
  { width: 'auto', align: 'center', prop: 'damagedGoodsCount', label: '损耗商品数', },
  { width: 'auto', align: 'center', prop: 'damagedOrderRatio', label: '损耗订单比', formatter: (row) => !row.damagedOrderRatio ? " " : row.damagedOrderRatio + '%', },
  { width: 'auto', align: 'center', prop: 'explainMsg', label: '情况说明', },
  { width: '100', align: 'center', prop: 'pictureUrls', label: '图片' },
  { width: 'auto', align: 'center', prop: 'createdUserName', label: '操作人', },
  { width: '120', align: 'center', prop: 'createdTime', label: '操作时间', },
]
export default {
  name: "operationRecording",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    recordData: { type: Object, default: () => { return {} } },
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        createdUserName: null,
        startTime: null,
        endTime: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    this.timeRanges = [this.recordData.startTime, this.recordData.endTime]
    this.ListInfo = this.recordData
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await queryDamagedOrderExplainRecordAsync(this.ListInfo)
      if (success) {
        this.tableData = data
        this.total = data.length
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

.chatPicUrl {
  position: relative;

  .picTips {
    position: absolute;
    top: 0;
    left: 150px;
    color: #ff0000;
    font-size: 16px;
  }
}

::v-deep .custom-image img {
  max-width: 40px !important;
  max-height: 40px !important;
}

.circle-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: red;
  color: white;
  font-size: 12px;
  width: 13px;
  height: 13px;
  line-height: 13px;
  text-align: center;
  border-radius: 50%;
}
</style>
