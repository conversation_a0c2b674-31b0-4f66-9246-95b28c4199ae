<template>
  <el-container style="height:100%;">
      <my-container style="width:99%;">
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry"
          :tableData='list'    :tableCols='tableCols' :isSelection="false" @select="selectchange" tablekey="OrderProfitTabGoods"
          :tableHandles='tableHandles' 
          :loading="listLoading">
        </ces-table>
        
        <template #footer>
          <my-pagination
            ref="pager"
            :total="total"
            :checked-count="sels.length"
            @get-page="getlist"
          />
        </template>   

        <el-dialog :visible.sync="orderDetail.visible" width="60%" title="订单明细">
            <!-- <el-card style="padding:1px;margin-bottom:10px;"> -->
            <div style="margin-bottom:10px;"> 
                <el-descriptions :column="4" size="mini" border>
                  <el-descriptions-item label="运营组">{{orderDetail.showRow.groupName}}</el-descriptions-item>
                  <el-descriptions-item label="商品编码">{{orderDetail.showRow.goodsCode}}</el-descriptions-item>
                  <el-descriptions-item label="商品名称" min-width="150">{{orderDetail.showRow.goodsName}}</el-descriptions-item>
                  <el-descriptions-item label="订单数">{{orderDetail.showRow.orderCount}}</el-descriptions-item>        
                  <el-descriptions-item label="销售额（元）">{{orderDetail.showRow.salesAmount}}</el-descriptions-item>
                  <el-descriptions-item label="成本（元）">{{orderDetail.showRow.goodsCost}}</el-descriptions-item>
                  <el-descriptions-item label="快递费（元）">{{orderDetail.showRow.freightFee}}</el-descriptions-item>
                  <el-descriptions-item label="利润（元）">{{orderDetail.showRow.profit}}</el-descriptions-item>                               
                </el-descriptions>
            </div>

            <el-form
                class="ad-form-query3"
                :inline="true"
                @submit.native.prevent>
                <el-form-item>
                  <el-button type="primary" @click="onSearchOrderDetail">刷新</el-button>
                </el-form-item>
                <!-- <el-form-item>
                  <el-button type="primary" @click="onExportOrderDetail">导出</el-button>
                </el-form-item> -->
                <!-- <el-form-item>
                  <el-button type="success" size="mini" @click="copyOrderDetailOrderNos">一键复制所有订单号</el-button>  
                </el-form-item> -->
              </el-form>  
          <!-- </el-card>  -->
              
          <ces-table ref="tableOrderDetail" :that='orderDetail.that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchangeOrderDetail' :tableData='orderDetail.tableData'
            :tableCols='orderDetail.tableCols' :loading="orderDetail.listLoading" :isSelectColumn="false"  style="height:510px;">
            </ces-table>    
            <!--分页-->
            <my-pagination
                ref="orderDetailPager"
                :total="orderDetail.total"
                @get-page="getOrderDetailList"
              />              
        </el-dialog>     
      </my-container>
  </el-container>
</template>

<script>
import MyContainer from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import { formatPlatform,formatLinkProCode} from "@/utils/tools";
//import { getGroupKeyValue } from '@/api/operatemanage/base/product';
//import { getList as getshopList } from '@/api/operatemanage/base/shop';
//import { rulePlatform} from "@/utils/formruletools";
import { 
  getNegativeProfitGoods,
  exportNegativeProfitGoods,
  getOrderDetail,
} from "@/api/order/ordernegativeprofit"
const tableCols =[
        {istrue:true,prop:'groupId',label:'运营组', width:'80',sortable:'custom',formatter:(row)=>row.groupName},
        {istrue:true,prop:'goodsCode',label:'商品编码', width:'150',sortable:'custom'},
        {istrue:true,prop:'goodsName',label:'商品名称', width:'auto'},
        {istrue:true,prop:'goodsImage',label:'图片', width:'70',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
        {istrue:true,prop:'orderCount',label:'订单数', width:'100',sortable:'custom',type:"click",handle:(that,row,column,cell)=>that.canclick(row,column,cell)},
        {istrue:true,prop:'salesAmount',label:'销售额（元）', width:'110',sortable:'custom'},
        {istrue:true,prop:'goodsCost',label:'成本（元）', width:'100',sortable:'custom'},
        {istrue:true,prop:'freightFee',label:'快递费（元）', width:'110',sortable:'custom'},
        {istrue:true,prop:'profit',label:'利润（元）', width:'100',sortable:'custom'},
     ];
const tableHandles1=[
        {label:"导出", handle:(that)=>that.onExport()},  
      ];
export default {
  name: 'Roles',
  components: {cesTable, MyContainer, MyConfirmButton },
  props:{
    filter:{}
  },
  data() {
    return {
      that:this,
      pickerOptions:{
        disabledDate(time){
          return time.getTime()>Date.now();
        }
      },
      platformList: [],
      shopList:[],
      groupList:[],
      list: [],
      summaryarry:{},
      pager:{OrderBy:"profit",IsAsc:true},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      dialogVisible: false,
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
      selids:[],//选择的id

      orderDetail:{
        title:"订单明细",
        visible:false,
        tableCols:[
          {istrue:true,prop:'orderNoInner',label:'内部订单号', width:'110',sortable:'custom'},
          {istrue:true,prop:'orderNo',label:'线上订单号', width:'auto',sortable:'custom'}, 
          {istrue:true,prop:'proCode',label:'产品ID', width:'120',sortable:'custom',type:'html',formatter:(row)=>formatLinkProCode(row.platform,row.proCode)},
          //{istrue:true,prop:'proName',label:'产品名称', width:'auto',sortable:'custom',},
          {istrue:true,prop:'goodsImage',label:'图片', width:'70',type:'imageGoodsCode',goods:{code:'goodsCode',name:'goodsName'}},
          {istrue:true,prop:'qty',label:'销售数量', width:'100',sortable:'custom'},
          {istrue:true,prop:'goodsAmount',label:'销售额（元）', width:'120',sortable:'custom'},
          {istrue:true,prop:'goodsCost',label:'成本（元）', width:'110',sortable:'custom'},
          {istrue:true,prop:'goodFreightMoney',label:'快递费（元）', width:'120',sortable:'custom'},
          {istrue:true,prop:'profit',label:'利润（元）', width:'110',sortable:'custom'},
        ],
        tableData:[],
        total:0,
        sels:[],
        listLoading:false,
        pager:{OrderBy:"profit",IsAsc:true},
        pageLoading: false,
        that:this,
        filter:{
          startDate:null,
          endDate:null,
          platform:null,
          shopCode:"",
          groupId:null,
          proCode:null,
        },
        showRow:{},//明细的行         
      },
    }
  },
  async mounted() {
    //await this.setPlatform();
    //await this.setGroupSelect();
    await this.Query();  
  },
  methods: {
    //设置平台下拉
    // async setPlatform() {
    //   var pfrule = await rulePlatform();
    //   this.platformList = pfrule.options;
    // },
    // async changePlatform(val){
    //   const res1 = await getshopList({platform:val,CurrentPage:1,PageSize:1000});
    //   this.shopList=res1.data.list;
    //   this.filter.shopCode="";
    //   this.Query();
    // },
    // async setGroupSelect(){
    //   const res = await getGroupKeyValue({});
    //   this.groupList=res.data;
    // },
    //总查询
    async Query(){
      await this.onSearch();
    },
    //导出
    async onExport(){
        var params=this.getCondition();
        if(params===false){
            return;
        }
        var loadingInstance = this.$loading({text:"正在导出，请稍后",fullscreen:false});
        var res= await exportNegativeProfitGoods(params);
        loadingInstance.close();
        if(!res?.data) return
        const aLink = document.createElement("a");
        let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download','订单负利润_' + new Date().toLocaleString() + '.xlsx' )
        aLink.click();
    },
    //获取查询条件
    getCondition(){
      if (this.filter.timerange&&this.filter.timerange.length>1) {
        this.filter.startDate = this.filter.timerange[0];
        this.filter.endDate = this.filter.timerange[1];
      }
      else {
        this.$message({message:"请先选择日期",type:"warning"});
        return false;
      }
      var pager = this.$refs.pager.getPager();
      var page  = this.pager;
      const params = {
        ...pager,
        ...page,
        ... this.filter
      }

      return params;
    },
    //查询第一页
    async onSearch() {
      this.$refs.pager.setPage(1)
      await this.getlist();
    },
    //分页查询
    async getlist() {
      var params=this.getCondition();
      if(params===false){
            return;
      }

      this.listLoading = true
      const res = await getNegativeProfitGoods(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.summaryarry=res.data.summary;
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    //排序查询
    async sortchange(column){
      if(!column.order)
        this.pager={};
      else{
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearch();
    },         
    selsChange: function(sels) {
      this.sels = sels;
    },
    selectchange:function(rows,row) {
      this.selids=[];console.log(rows)
      rows.forEach(f=>{
        this.selids.push(f.id);
      })
    },
    //单击单元格
    async canclick(row, column, cell){
      if(column.prop=='orderCount')
      {          
        await this.onShowOrderDetail(row);
      }
    },
    //==订单明细查询 Start===============================================
    //查询明细第一页
    async onSearchOrderDetail() {
      this.orderDetail.visible=true;
      setTimeout(async () => {
        this.$refs.orderDetailPager.setPage(1);
        await this.getOrderDetailList();      
      }, 100);     
    },
    //明细排序查询
    async sortchangeOrderDetail(column){
      if(!column.order)
        this.orderDetail.pager={};
      else{
        this.orderDetail.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false};
      }
      await this.onSearchOrderDetail();
    },
    //明细查询条件
    getOrderDetailCondition(){
          var pager = this.$refs.orderDetailPager.getPager();
          var page  = this.orderDetail.pager;
          var params = {
            ...params,
            ...pager,
            ...page,
            ...this.orderDetail.filter
          }    

          return params;
    },
    //明细查询
    async getOrderDetailList() {
      this.orderDetail.visible=true;
      setTimeout(async () => {
          var params=this.getOrderDetailCondition();
          if(params===false){
                return false;
          }
          params.goodsCode=this.orderDetail.showRow.goodsCode;
          this.orderDetail.listLoading = true;
          const res = await getOrderDetail(params);
          this.orderDetail.listLoading = false;
          if (!res?.success) {
            return
          }
          this.orderDetail.total=res.data.total;
          const data = res.data.list;
          data.forEach(d => {
            d._loading = false
          })
          this.orderDetail.tableData = data;
      }, 200);
      
    },
    //查看明细
    async onShowOrderDetail(row){
        this.orderDetail.filter.startDate=this.filter.startDate;
        this.orderDetail.filter.endDate=this.filter.endDate;
        this.orderDetail.filter.proCode=row.proCode;
        this.orderDetail.filter.platform=row.platform;
        this.orderDetail.filter.shopCode=row.shopCode;
        this.orderDetail.filter.groupId=row.groupId;
        this.orderDetail.showRow=row;
        await this.onSearchOrderDetail();
    },
    //==订单明细查询 End=================================================
    myFormatPlatform(platform){
      return formatPlatform(platform);
    },
    myFormatLinkProCode(platform,proCode){
      var res= formatLinkProCode(platform,proCode);
      return res;
    }
  }
}
</script>
<style scoped>
::v-deep .el-link.el-link--primary{
  margin-right: 7px;
}
</style>
