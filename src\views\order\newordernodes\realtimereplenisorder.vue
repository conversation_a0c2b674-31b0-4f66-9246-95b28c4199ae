<template >
    <my-container v-loading="pageLoading" style="height:100%">
        <el-row>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-date-picker style="width: 240px" v-model="filter.dates" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始审核时间" end-placeholder="结束审核时间">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-date-picker style="width: 280px" v-model="filter.dates2" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始补货完成时间"
                        end-placeholder="结束补货完成时间">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="filter.isOk" clearable filterable placeholder="是否完成" style="width: 80px">
                        <el-option label="已完成" :value=1 />
                        <el-option label="未完成" :value=0 />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input placeholder="内部单号" v-model="filter.orderNoInner" style="width: 100px" clearable
                        oninput="if(value){value=value.replace(/[^\-\d]/g,'')} if(value>2147483647){value=2147483647} if(value<0){value=0}"
                        maxlength="10"></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input placeholder="商品编码" v-model="filter.sku_id" style="width: 120px" clearable
                        maxlength="20"></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="filter.warehouse" clearable filterable placeholder="发货仓" style="width: 200px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="filter.morningRight" clearable filterable placeholder="班次" style="width: 80px">
                        <el-option label="白" value="白" />
                        <el-option label="晚" value="晚" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input placeholder="库位" v-model="filter.kuWei" style="width: 120px" clearable
                        maxlength="20"></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input placeholder="负责人上级" v-model="filter.upAislerName" style="width: 100px" clearable
                        maxlength="20"></el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input placeholder="通道负责人" v-model="filter.aislerName" style="width: 100px" clearable
                        maxlength="20"></el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </el-row>
        <el-row>
            <el-button-group>
                <el-button>
                    <el-checkbox v-model="filter.isDate">按天（审核时间）</el-checkbox>
                </el-button>
                <el-button>
                    <el-checkbox v-model="filter.isWare">按发货仓</el-checkbox>
                </el-button>
                <el-button>
                    <el-checkbox v-model="filter.isUpAislerName">按负责人上级</el-checkbox>
                </el-button>
                <el-button>
                    <el-checkbox v-model="filter.isAislerName">按负责人</el-checkbox>
                </el-button>
                <el-button>
                    <el-checkbox v-model="filter.isMorningRight">按班次</el-checkbox>
                </el-button>
            </el-button-group>
        </el-row>
        <ces-table style="height:90%" ref="openwebjushuitanjhpctable" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :tableData='jhpcList' :isSelection='false' :summaryarry="summaryarry"
            :showsummary='true' :tableCols='tableCols' :isSelectColumn="false">
            <template slot='extentbtn'>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getNewOrderListAsync" />
        </template>
    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils";
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import { getTbWarehouseList } from '@/api/inventory/openwebjushuitan';
import { getReplenisOrderPageList } from '@/api/inventory/inventoryorder';
const tableCols = [
    { istrue: true, prop: 'orderNoInner', label: '内部单号', width: '90', sortable: 'custom',type:'orderLogInfo',orderType:'orderNoInner'  },
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'billTime', label: '审核时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'isOkTime', label: '补货完成时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'wareHouseCode', label: '发货仓', width: '200', sortable: 'custom', formatter: (row) => row.wareHouseName },
    { istrue: true, prop: 'kuWei', label: '库位', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'upAislerName', label: '负责人上级', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'aislerName', label: '通道负责人', width: '100', sortable: 'custom', },
    { istrue: true, prop: 'morningRight', label: '班次', width: '70', sortable: 'custom', },
    { istrue: true, prop: 'aislerNameResult', label: '实际完成人', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '创建时间', width: '150', sortable: 'custom' },
    { istrue: true, prop: 'isOkSpanTime', label: '补货时长', width: '90', sortable: 'custom', formatter: (row) => row.isOkSpanTimeHH },
    { istrue: true, prop: 'orderNoInnerCount', label: '订单总量', width: '100', sortable: 'custom' },
];
export default {
    name: "realtimeinventorycheck",
    components: { cesTable, MyContainer, datepicker },
    props: {

    },
    data() {
        return {
            warehouselist: [],
            pageLoading: false,
            tableCols: tableCols,
            jhpcList: [],
            that: this,
            sels: [], // 列表选中列
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "createdTime", IsAsc: false },
            filter: {
                dates: [formatTime(dayjs(), "YYYY-MM-DD"), formatTime(dayjs(), "YYYY-MM-DD")],
                dates2: [],
                sdate: null,
                edate: null,
                sdate2: null,
                edate2: null,
                warehouse: null,
                morningRight: null,
                kuWei: null,
                aislerName: null,
                upAislerName: null,
                isDate: false,
                isWare: false,
                isUpAislerName: false,
                isAislerName: false,
                isMorningRight: false,
            },
        }
    },
    async mounted() {
        await this.getInventoryWareHouseList()
        this.onSearch();
    },
    methods: {
        async getInventoryWareHouseList() {
            if (this.warehouselist.length <= 0) {
                let wares = await getTbWarehouseList();
                if (wares?.success && wares?.data && wares?.data.length > 0) {
                    wares?.data.forEach(f => {
                        if (f.manager_ddid)
                            this.warehouselist.push({ value: f.wms_co_id, label: f.name });
                    });
                }
            }
        },
        //排序查询      
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        onSearch() {
            if (this.filter.isDate == true || this.filter.isWare == true ||
                this.filter.isUpAislerName == true || this.filter.isAislerName == true ||
                this.filter.isMorningRight == true) {
                tableCols[0].istrue = false;
                tableCols[1].istrue = false;
                tableCols[2].istrue = false;
                tableCols[3].istrue = false;
                tableCols[4].istrue = false;
                tableCols[5].istrue = false;
                tableCols[6].istrue = false;
                tableCols[7].istrue = false;
                tableCols[8].istrue = false;
                tableCols[9].istrue = false;
                tableCols[10].istrue = false;
                tableCols[12].istrue = true;
                let isorder = 0;
                if (this.filter.isDate == true) {
                    tableCols[2].istrue = true;
                    if (this.pager.OrderBy == "billTime")
                        isorder++;
                }
                if (this.filter.isWare == true) {
                    tableCols[4].istrue = true;
                    if (this.pager.OrderBy == "wareHouseCode")
                        isorder++;
                }
                if (this.filter.isUpAislerName == true) {
                    tableCols[6].istrue = true;
                    if (this.pager.OrderBy == "upAislerName")
                        isorder++;
                }
                if (this.filter.isAislerName == true) {
                    tableCols[7].istrue = true;
                    if (this.pager.OrderBy == "aislerName")
                        isorder++;
                }
                if (this.filter.isMorningRight == true) {
                    tableCols[8].istrue = true;
                    if (this.pager.OrderBy == "morningRight")
                        isorder++;
                }
                if (isorder == 0 && this.pager.OrderBy != "isOkSpanTime" && this.pager.OrderBy != "orderNoInnerCount")
                    this.pager = { OrderBy: "isOkSpanTime", IsAsc: false };
            }
            else {
                this.tableCols.forEach((col) => {
                    col.istrue = true;
                });
                tableCols[12].istrue = false;
                if (this.pager.OrderBy == "orderNoInnerCount")
                    this.pager = { OrderBy: "createdTime", IsAsc: false };
            }
            this.$refs.pager.setPage(1);
            this.getNewOrderListAsync();
        },
        async getNewOrderListAsync() {
            if (this.filter.dates && this.filter.dates.length > 1) {
                this.filter.sdate = this.filter.dates[0];
                this.filter.edate = this.filter.dates[1];
            }
            else {
                this.filter.sdate = null;
                this.filter.edate = null;
            }
            if (this.filter.dates2 && this.filter.dates2.length > 1) {
                this.filter.sdate2 = this.filter.dates2[0];
                this.filter.edate2 = this.filter.dates2[1];
            }
            else {
                this.filter.sdate2 = null;
                this.filter.edate2 = null;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            this.pageLoading = true;
            let res = await getReplenisOrderPageList(params);
            this.pageLoading = false;
            if (res?.success) {
                this.total = res.data.total
                this.jhpcList = res.data.list;
                this.summaryarry = res.data.summary;
            }
        },
    }
}
</script>