<template>
    <!-- <div class="PlayVideo">
        <video class="video" controls="" ref="video" id="video" v-on:error.prevent="error($event)" style="width:100%;height:100%">
        </video>
        
        <div class="msg" v-if="isError">
            <div style="color: #fff">{{errMsg}}</div>
        </div>
    </div> -->
    <div>
        <video ref="videoPlayer" class="video-js" oncontextmenu="return false;"></video>
    </div>
    <!-- <video class="video-js" data-setup='{"controls": true,"autoplay": false,"preload": "auto","width":"800px"}'> 
        <source src="http://*************:8004/media/video/20221023/1584058261756026880.mp4" type="video/mp4">
    </video> -->

</template>

<script>
    import  videojs from "video.js"
    import 'video.js/dist/video-js.css';
    export default {
        props: { videoUrl: '' },
        data() {
            return {
                player: null,
                options: {
                        type: Object,
                        default() {
                            return {};
                        }
                    }
            }
        },
        mounted() {
            this.playVideo(this.videoUrl);
        },
        methods: {
            playVideo(videoUrl) {
                // var that = this;
                // this.$refs.video.src = this.videoUrl;

                let options={
                        autoplay: true,
                        controls: true,
                        sources: [
                            {
                                //src:  "http://*************:8004/media/video/20221023/1584058261756026880.mp4",
                                src:  videoUrl,
                                type: "video/mp4"
                            }
                        ]
                    }
                this.player = videojs(this.$refs.videoPlayer, options, function onPlayerReady() {
                    console.log('onPlayerReady', this);
                })
            },
            close() {
                this.show = false;
                this.$emit("close");
            },
            error(e) {
                console.log(e);
                if (this.videoUrl == '') {
                    this.isError = true
                    this.errMsg = "该文章暂无视频！"
                } else {
                    this.isError = true
                    this.errMsg = "视频链接失效！无法播放！"
                }
            }
        },
        beforeDestroy() {
            if (this.player) {
                this.player.dispose()
            }
        }
    }
</script>
<style lang='scss' scoped>
    .PlayVideo {
        width: 45.3125vw;
        height: 50.1vh;
        position: relative;
        // z-index: 99999;
        // background: rgba(0, 0, 0, 0.5);
        .close {
            position: absolute;
            top: -32px;
            right: -32px;
            z-index: 9999;
        }
        .video {
            width: 45.3125vw;
            height: 50.1vh;
            background: #000;
            // position: absolute;
            // top: 50%;
            // left: 50%;
            // transform: translate(-50%,-50%);
            // z-index: 100000;
        }
        .msg {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
    }
</style>
