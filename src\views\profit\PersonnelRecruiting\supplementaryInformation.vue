<template>
    <div v-if="isEdit">
        <!-- 补充信息 -->
        <div class="des-box">
            <el-descriptions title="补充信息:" :column="3" size="medium" :colon="false">
                <template slot="extra">
                    <!-- <el-button type="info" circle plain icon="el-icon-plus" size="mini" @click="toggleContent"></el-button> -->
                    <i @click="toggleContent()" class="el-icon-d-arrow-right"
                        :class="{ arrowTransform: !isOpen, arrowTransformReturn: isOpen }"></i>
                </template>
                <el-descriptions-item label="" span="3">
                    <el-collapse v-model="activeName">
                        <el-collapse-item name="content">
                            <el-form :model="ruleForm" ref="supplementForm" label-width="110px" class="ruleForm">
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="民族" prop="ethnicity">
                                            <el-input v-model="ruleForm.ethnicity" placeholder="请输入民族"
                                                maxlength="10"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="户籍" prop="hukou">
                                            <!-- <el-input v-model="ruleForm.hukou" placeholder="请输入户籍"></el-input> -->
                                            <el-select v-model="ruleForm.hukou" placeholder="请选择户籍" style="width: 100%;">
                                                <el-option label="城镇" value="城镇"></el-option>
                                                <el-option label="农村" value="农村 "></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="政治面貌" prop="politicalStatus">
                                            <el-select v-model="ruleForm.politicalStatus" placeholder="请选择政治面貌"
                                                style="width: 100%;">
                                                <el-option label="党员" value="党员"></el-option>
                                                <el-option label="共青团员" value="共青团员"></el-option>
                                                <el-option label="群众" value="群众 "></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="婚姻状况" prop="maritalStatus">
                                            <el-select v-model="ruleForm.maritalStatus" placeholder="请选择婚姻状况"
                                                style="width: 100%;">
                                                <el-option label="已婚" :value="1"></el-option>
                                                <el-option label="未婚" :value="0"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="生育状况" prop="childrenQty">
                                            <el-select v-model="ruleForm.childrenQty" placeholder="请输入生育状况"
                                                style="width: 100%;">
                                                <el-option label="已育" :value="1"></el-option>
                                                <el-option label="未育" :value="0"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="健康状况" prop="healthStatus">
                                            <el-input v-model="ruleForm.healthStatus" placeholder="请输入健康情况"
                                                style="width: 100%;" maxlength="10">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="家庭联系人" prop="relationship">
                                            <el-input v-model="familyMember.relationship" placeholder="与联系人关系"
                                                maxlength="10" style="width: 100%;">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="联系方式" prop="contact">
                                            <el-input v-model="familyMember.contact" maxlength="20"
                                                placeholder="请输入联系方式"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="户口所在地" prop="hukouAddress">
                                            <el-input v-model="ruleForm.hukouAddress" placeholder="请输入户口所在地"
                                                maxlength="100"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="现居住地" prop="nowAddress">
                                            <el-input v-model="ruleForm.nowAddress" placeholder="请输入现居住地"
                                                maxlength="100"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="紧急联系人" prop="emergencyContact">
                                            <el-input v-model="ruleForm.emergencyContact" placeholder="请输入紧急联系人"
                                                maxlength="20"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="关系" prop="emergencyContactRelationship">
                                            <el-input v-model="ruleForm.emergencyContactRelationship"
                                                placeholder="请选择与紧急联系人的关系" style="width: 100%;" maxlength="10">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="联系方式" prop="emergencyContactPhone">
                                            <el-input v-model="ruleForm.emergencyContactPhone" maxlength="20"
                                                placeholder="请输入紧急联系人联系方式"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="银行卡" prop="bankAccountNumber">
                                            <el-input v-model="ruleForm.bankAccountNumber" placeholder="请输入银行卡"
                                                maxlength="30"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="银行" prop="bank">
                                            <el-input v-model="ruleForm.bank" placeholder="请输入银行" maxlength="20"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="开户信息" prop="bankAccountInformation">
                                            <el-input v-model="ruleForm.bankAccountInformation" placeholder="请输入开户信息"
                                                maxlength="30"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="持卡人姓名" prop="cardholderName">
                                            <el-input v-model="ruleForm.cardholderName" placeholder="请输入持卡人姓名"
                                                maxlength="20"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="16">
                                        <el-form-item label="籍贯(县城/镇/乡/村)" prop="hometownAddress">
                                            <el-input v-model="ruleForm.hometownAddress" placeholder="请输入籍贯"
                                                maxlength="100"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="16">
                                        <el-form-item label="籍贯(省/市)" prop="hometownProvinceCity">
                                            <el-input v-model="ruleForm.hometownProvinceCity"
                                                placeholder="请输入籍贯"></el-input>
                                            <!-- <yh-cityselector :clearable="true" :MaxLvl="2" :placeholder="'江西省/南昌市'" :Address="hometownProvinceCity"
                                                @change="changeCity" /> -->
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="16">
                                        <el-form-item label="家庭住址" prop="homeAddress">
                                            <el-input v-model="ruleForm.homeAddress" placeholder="请输入家庭住址"
                                                maxlength="100"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="公司是否有熟人" prop="hasAcquaintanceInCompany">
                                            <el-select v-model="ruleForm.hasAcquaintanceInCompany" placeholder="请选择是否有熟人"
                                                style="width: 100%;">
                                                <el-option label="是" :value="true"></el-option>
                                                <el-option label="否" :value="false"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row v-if="ruleForm.hasAcquaintanceInCompany == 1">
                                    <el-col :span="8">
                                        <el-form-item label="部门" prop="acquaintanceDepartment">
                                            <el-input v-model="ruleForm.acquaintanceDepartment" placeholder="请输入部门"
                                                maxlength="20"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="岗位" prop="acquaintancePosition">
                                            <el-input v-model="ruleForm.acquaintancePosition" placeholder="请输入岗位"
                                                maxlength="10"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="姓名" prop="acquaintanceName">
                                            <el-input v-model="ruleForm.acquaintanceName" placeholder="请输入姓名"
                                                maxlength="20"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="关键字标签" prop="tags">
                                            <el-input type="textarea" v-model="ruleForm.tags"
                                                placeholder="请输入关键字标签,以;英文分号符分隔。如 乐观;认真" maxlength="150"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-collapse-item>
                    </el-collapse>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-divider></el-divider>
    </div>
</template>
  
<script>
import YhCityselector from '@/components/YhCom/yh-cityselector.vue';
export default {
    name: "supplementaryInformation",//补充信息
    components: { YhCityselector },
    props: {
        isEdit: {
            type: Boolean,
            default: () => { return false; }
        },
        candidateInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            activeName: '',
            isOpen: false,
            // isEdit: true,
            ruleForm: {
                ethnicity: null,
                hukou: null,
                politicalStatus: null,
                maritalStatus: null,
                childrenQty: null,
                healthStatus: null,
                familyMemberList: [],
                hometownAddress: null,
                hometownProvinceCity: null,
                homeAddress: null,
                hasAcquaintanceInCompany: null,
                acquaintanceDepartment: null,
                acquaintancePosition: null,
                acquaintanceName: null,
                tags: null,
                bankAccountNumber: null,
                cardholderName:null,
                bank: null,
                bankAccountInformation: null,
                hukouAddress: null,
                nowAddress: null,
                emergencyContact: null,
                emergencyContactRelationship: null,
                emergencyContactPhone: null,
            },
            familyMember: {
                relationship: null,
                contact: null
            },
            hometownProvinceCity: [],
        }
    },

    watch: {
        familyMember: { // 对对象进行深度监听
            handler (nv) {
                this.ruleForm.familyMemberList = []
                this.ruleForm.familyMemberList.push(nv)
            },
            immediate: true,
            deep: true
        },
        // 'familyMember.relationship': { // 对对象的某一个属性进行深度监听
        //     handler (nv) {
        //         console.log(nv)
        //     },
        //     immediate: true,
        //     deep: true
        // }
    },
    mounted () {
        for (const prop in this.candidateInfo) {
            if (prop in this.ruleForm) {
                this.ruleForm[prop] = this.candidateInfo[prop];
            }
        }
        if (this.ruleForm.familyMemberList) {
            this.familyMember = this.ruleForm.familyMemberList[0];
        }
        if (this.ruleForm.hometownProvinceCity) {
            this.hometownProvinceCity = this.ruleForm.hometownProvinceCity.split('/')
        }

    },
    methods: {
        changeCity (e) {
            if (e) {
                this.ruleForm.hometownProvinceCity = e[0] + '/' + e[1];
            }
        },
        reset () {
            this.ruleForm = {
                ethnicity: null,
                hukou: null,
                politicalStatus: null,
                maritalStatus: null,
                childrenQty: null,
                healthStatus: null,
                familyMemberList: [],
                hometownAddress: null,
                hometownProvinceCity: null,
                homeAddress: null,
                hasAcquaintanceInCompany: null,
                acquaintanceDepartment: null,
                acquaintancePosition: null,
                acquaintanceName: null,
                tags: null,
                bankAccountNumber: null,
                cardholderName:null,
                bank: null,
                bankAccountInformation: null,
                hukouAddress: null,
                nowAddress: null,
                emergencyContact: null,
                emergencyContactRelationship: null,
                emergencyContactPhone: null,
            }
        },
        toggleContent () {
            this.isOpen = !this.isOpen
            this.activeName = this.isOpen ? 'content' : ''
        },
        //提交
        submitForm (formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>
  
<style scoped>
.title {
    cursor: pointer;
}

.ruleForm {
    padding: 0px;
}

.des-box {
    padding: 0 10px 0 30px
}

::v-deep .el-descriptions__title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

::v-deep .el-descriptions__header {
    margin-bottom: 10px;
}

::v-deep .el-divider--horizontal {
    margin: 5px 0;
}

/* ::v-deep .el-collapse-item__header.is-active {
    display: none;
} */
::v-deep .el-collapse-item__wrap {
    border-bottom: none;
}

::v-deep .el-collapse {
    border: none;
}

::v-deep .el-collapse-item__header {
    display: none;
}

::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}

.arrowTransformReturn {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(90deg);
}

.arrowTransform {
    transition: 0.2s;
    transform-origin: center;
    transform: rotateZ(0deg);
}</style>