<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startTime" :endDate.sync="ListInfo.endTime" class="publicCss"
                    startPlaceholder="创建时间" endPlaceholder="创建时间" />
                <dateRange :startDate.sync="ListInfo.payStartTime" :endDate.sync="ListInfo.payEndTime" class="publicCss"
                    startPlaceholder="付款时间" endPlaceholder="付款时间" />
                <el-input v-model.trim="ListInfo.internalOrderNo" placeholder="内部订单号" maxlength="50" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.buyUserId" placeholder="买家ID" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.orderNo" placeholder="订单号" maxlength="200" clearable
                    class="publicCss" />
                <el-input v-model.trim="ListInfo.createdUserName" placeholder="创建人" maxlength="50" clearable
                    class="publicCss" />
                <el-button type="primary" @click="getList('search')">搜索</el-button>
                <el-button type="primary" @click="importProps" v-if="checkPermission('pzdImport')">导入</el-button>
                <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
            </div>
        </template>
        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            :isSelectColumn="false" style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
            <template slot="right">
                <vxe-column title="操作" width="120" align="center">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="viewProps(row.id)">编辑</el-button>
                            <el-button type="text"
                                v-if="checkPermission('api:inventory:CustomNormsGoods:DeletePZDCSRecord')"
                                @click="pzdDelete(row.id)">删除</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer title="编辑" :visible.sync="drawer" direction="rtl" size="90%" :wrapperClosable="false">
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm"
                style="padding: 16px;box-sizing: border-box;">
                <el-form-item label="买家id:" prop="pass">
                    <div>{{ ruleForm.buyUserId }}</div>
                </el-form-item>
                <el-form-item label="订单备注:" prop="orderRemark">
                    <el-tooltip class="item" effect="dark" :content="ruleForm.orderRemark" placement="top-start">
                        <div class="orderRemark">{{ ruleForm.orderRemark }}</div>
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="规格报价明细:" prop="pass">
                    <span style="color: red;">(禁止截图给客户！！！)</span>
                    <el-button style="margin-left: 20px;" @click="addProps" type="primary">新增一行</el-button>
                </el-form-item>
                <div style="height: 400px;">
                    <el-table :data="ruleForm.dtls" style="width: 100%;height:100%" max-height="450">
                        <el-table-column prop="norms" label="规格" width="150">
                            <template #default="{ row, $index }">
                                <el-select v-model="row.normsId" placeholder="规格" filterable remote
                                    :remote-method="remoteMethod($index)" @change="(val) => {
                                        row.norsItem = row.normsList.find(a => a.id == val);
                                        if (row.norsItem) {
                                            row.norms = row.norsItem.norms
                                            row.colorType = row.norsItem.colorType
                                            row.hasBaseType = row.norsItem.hasBaseType
                                            row.goodsCode = row.norsItem.goodsCode
                                        }
                                        Cpt();
                                    }">
                                    <el-option v-for="item in row.normsList" :key="item.id"
                                        :label="item.norms + (item.hasBaseType ? '-' + item.hasBaseType : '') + (item.colorType ? '-' + item.colorType : '')"
                                        :value="item.id" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="hasBaseType" label="底色" width="85">
                            <template #default="{ row, $index }">
                                <el-input v-model="row.hasBaseType" maxlength="50" clearable placeholder="底色"
                                    disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="colorType" label="色号" width="70">
                            <template #default="{ row, $index }">
                                <el-input v-model="row.colorType" maxlength="50" clearable placeholder="色号" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="sheetCount" label="张" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.sheetCount" :min="0" :max="10000" :precision="0"
                                    :controls="false" label="张" class="iptCss" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="realTotalCostFt" label="成本" width="85">
                            <template #default="{ row, $index }">
                                <el-input-number v-model="row.realTotalCostFt" :min="0" :precision="4" :controls="false"
                                    label="成本" class="iptCss" disabled />
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" width="auto">
                            <template #default="{ row }">
                                <el-input v-model="row.remark" maxlength="50" clearable placeholder="备注" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderFreightBagFee" label="操作" width="180">
                            <template #default="{ row, $index }">
                                <el-button style="margin-left: 10px;" type="warning"
                                    @click="batchSet(row)">复制</el-button>
                                <el-button type="danger" @click="delProps($index)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-form-item label="合计:" prop="pass">
                    <div>{{ ruleForm.realTotalCost }}</div>
                </el-form-item>
                <el-form-item label="实际报价:" prop="pass">
                    <el-input-number v-model="ruleForm.actualTotalAmount" :min="0" :max="9999999" :precision="2"
                        :controls="false" label="实际报价" class="iptCss" />
                </el-form-item>
                <div style="display: flex;justify-content: end;margin-top: 100px;">
                    <el-button type="primary" @click="submitForm" v-throttle="1000">确认修改</el-button>
                </div>
            </el-form>
        </el-drawer>

        <el-dialog title="导入数据" :visible.sync="importVisible" width="30%" v-dialogDrag v-loading="importLoading">
            <div style="display: flex;flex-direction: column;justify-content: center;">
                <dateRange :startDate.sync="importTime.payStartTime" :endDate.sync="importTime.payEndTime"
                    v-if="importVisible" class="publicCss" startPlaceholder="创建时间" endPlaceholder="创建时间"
                    style="width: 250px;margin:0 10px 10px 0;" />
                <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                    :on-remove="removeFile" :file-list="fileList" accept=".xlsx" :http-request="uploadFile">
                    <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                        <el-button size="small" type="primary">点击上传</el-button>
                    </el-tooltip>
                </el-upload>
            </div>
            <div class="btnGroup">
                <el-button @click="importVisible = false">取消</el-button>
                <el-button type="primary" @click="sumbit" v-throttle="1000">确定</el-button>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="copyVisable" width="15%" height="100">
            <div style="margin-left: 40px;">
                复制<el-input-number style="width: 40px;" v-model="copyRowCount" :min="0" :max="15" :precision="0"
                    :controls="false" />条
                <el-button style="margin-left: 10px;" type="primary" @click="doCopy">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import {
    getCostSetByPZDCache,
    exportPZDCSRecordPageList,
    getPZDCSRecordById,
    savePZDCSRecord,
    deletePZDCSRecord,
    importCustomMadeMultipleAsync,
    getPZDCSRecordPageList
} from "@/api/inventory/customNormsGoods";
import decimal from '@/utils/decimal'
const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'buyUserId', label: '买家ID', },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'internalOrderNo', label: '内部订单号', type: 'orderLogInfo', orderType: 'orderNoInner' },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '订单编号', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdUserName', label: '创建人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'createdTime', label: '创建时间', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'payTime', label: '付款时间', },
    { sortable: 'custom', width: '65', align: 'center', prop: 'totalSheetCount', label: '总张数', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'realTotalCost', label: '总成本', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'actualTotalAmount', label: '实际报价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'shipmentStatus', label: '订单状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'updatedUserName', label: '修改人', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'createdTime',
                isAsc: false,
                startTime: null,//开始时间
                endTime: null,//结束时间
                payStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),//付款开始时间
                payEndTime: dayjs().format('YYYY-MM-DD'),//付款结束时间
            },
            timeRanges: [],
            tableCols,
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            ruleForm: {
                buyUserId: null,//买家id
                orderNo: null,//订单号
                totalAmount: null,//总价
                actualTotalAmount: null,//实际总价
                // chatPicUrl: null,//聊天记录
                orderWeight: 0,//快递重量
                totalSheetCount: 0,//总张数
                weight: 0,//重量
                cost: 0,//成本(不含快递+打包)
                realTotalCost: 0,//成本(含快递+打包)
                dtls: [
                    {
                        norsId: null,
                        customType: null,
                        norms: null,//规格
                        type: null,//类型
                        sheetWidth: null,//单张宽
                        sheetLength: null,//单张长
                        sheetCount: null,//张数
                        totalAmount: null,//售价
                        remark: null,//备注
                        normsList: null,
                        norsItem: null,
                    }
                ]
            },
            drawer: false,
            importTime: {
                payStartTime: null,
                payEndTime: null
            },
            importVisible: false,
            importLoading: false,
            fileList: [],
            buyUserInfo: {
                buyUserId: null,
                id: null
            },
            copyRowCount: 1,
            copyVisable: false,
            timer: null,
        }
    },
    async mounted() {
        await this.getList()
    },
    methods: {
        Cpt() {
            console.log(this.ruleForm.dtls);
            //如果没有重量,单行成本和总成本都为0
            if (this.ruleForm.orderWeight == 0) {
                this.ruleForm.dtls.forEach(item => {
                    item.realTotalCostFt = 0
                })
                this.ruleForm.realTotalCost = 0
            } else if (this.ruleForm.orderWeight && this.ruleForm.dtls && this.ruleForm.dtls.length > 0) {
                //找出最大的公斤成本价 
                const maxKgPriceCost = this.ruleForm.dtls.reduce((total, item) => {
                    return total > item.norsItem.kgPriceCost ? total : item.norsItem.kgPriceCost
                }, 0)
                //找出最大的打包费
                const maxPackProcessCost = this.ruleForm.dtls.reduce((total, item) => {
                    return total > item.norsItem.packProcessCost ? total : item.norsItem.packProcessCost
                }, 0)
                //找出最大的气泡袋
                const maxBubbleBagCost = this.ruleForm.dtls.reduce((total, item) => {
                    return total > item.norsItem.bubbleBagCost ? total : item.norsItem.bubbleBagCost
                }, 0)
                //找出最大的珍珠棉棒
                const maxSpongeBangCost = this.ruleForm.dtls.reduce((total, item) => {
                    return total > item.norsItem.spongeBangCost ? total : item.norsItem.spongeBangCost
                }, 0)
                //找出最大的裁剪费
                const maxCroppingCost = this.ruleForm.dtls.reduce((total, item) => {
                    return total > item.norsItem.croppingCost ? total : item.norsItem.croppingCost
                }, 0)
                //计算公斤成本价+打包费+气泡袋+珍珠棉棒
                const otherCost = decimal(decimal(maxCroppingCost, maxPackProcessCost, 4, '+'), decimal(maxBubbleBagCost, maxSpongeBangCost, 4, '+'), 4, '+')
                //计算总成本 = （重量*公斤成本价+裁剪费+打包费+气泡袋+珍珠棉棒）*利润
                const totalCost = decimal(decimal(decimal(this.ruleForm.orderWeight, maxKgPriceCost, 4, '*'), otherCost, 4, '+'), 1.1, 4, '*')
                //平均计算每一行的成本
                this.ruleForm.dtls.forEach(item => {
                    item.realTotalCostFt = decimal(totalCost, this.ruleForm.dtls.length, 4, '/')
                })
                //计算每行成本的总和
                const costTotal = this.ruleForm.dtls.reduce((total, item) => {
                    return decimal(total, item.realTotalCostFt, 4, '+')
                }, 0)
                //如果计算的总成本不等于实际总成本,则补差到最后一行
                if (costTotal != totalCost) {
                    const index = this.ruleForm.dtls.length - 1
                    this.ruleForm.dtls[index].realTotalCostFt = decimal(this.ruleForm.dtls[index].realTotalCostFt, decimal(totalCost, costTotal, 4, '-'), 4, '+')
                }
                //计算实际总成本
                this.ruleForm.realTotalCost = this.ruleForm.dtls.reduce((total, item) => {
                    return decimal(total, item.realTotalCostFt, 4, '+')
                }, 0)
            }
        },
        remoteMethod(i) {
            let row = this.ruleForm.dtls[i]
            let that = this
            return (name) => {
                if (!name) return row.normsList = []
                clearTimeout(this.timer);
                this.timer = setTimeout(() => {
                    row.normsList = []
                    getCostSetByPZDCache({ name, type: 1 })
                        .then(res => {
                            if (res.success) {
                                row.normsList = res.data
                                that.$set(that.ruleForm.dtls, i, row)
                            }
                        })
                        .catch(err => {
                            that.$set(row, 'normsList', [])
                        });
                }, 100);
            }
        },
        delProps(i) {
            this.ruleForm.dtls.splice(i, 1)
            this.Cpt()
        },
        batchSet(row) {
            this.copyRow = row;
            this.copyVisable = true;
        },
        async doCopy() {
            for (let index = 0; index < this.copyRowCount; index++) {
                this.ruleForm.dtls.push(Object.assign({}, this.copyRow))
            }
            await this.Cpt()
            this.copyVisable = false;
        },
        async viewProps(id) {
            const { data, success } = await getPZDCSRecordById({ id: id })
            if (success) {
                if (data && data.dtls) {
                    data.dtls.forEach(item => {
                        item.normsId = item.norsItem ? item.norsItem.id : null
                        item.normsList = item.norsItem ? [item.norsItem] : []
                    })
                }
                this.ruleForm = data
                this.drawer = true
            }
        },
        validate() {
            const arr = ['normsId', 'sheetCount']
            const map = {
                normsId: '规格',
                sheetCount: '张'
            }
            this.ruleForm.dtls.forEach((item, i) => {
                arr.forEach(key => {
                    if (item[key] === null || item[key] === undefined || item[key] === '' || item[key] <= 0) {
                        this.$message.error(`第${i + 1}行${map[key]}不能为0或空`)
                        throw new Error(`第${i + 1}行${map[key]}不能为0或空`)
                    }
                })
            })
        },
        async submitForm() {
            if (this.ruleForm.dtls.length == 0) return this.$message.error('至少添加一条数据')
            this.validate()
            const { data, success } = await savePZDCSRecord(this.ruleForm)
            if (success) {
                await this.getList()
                this.$message.success('提交成功')
                this.drawer = false
            } else {
                this.$message.error('提交失败')
            }
        },
        async uploadFile(data) {
            this.file = data.file
        },
        async sumbit() {
            if (!this.importTime.payStartTime || !this.importTime.payEndTime) return this.$message.error('请选择时间')
            if (this.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在导入中,请稍后...')
            const form = new FormData();
            form.append("upfile", this.file);
            form.append("payStartTime", this.importTime.payStartTime);
            form.append("payEndTime", this.importTime.payEndTime);
            this.importLoading = true
            await importCustomMadeMultipleAsync(form).then(({ success }) => {
                if (success) {
                    this.$message.success('导入成功')
                    this.importVisible = false
                    this.getList()
                }
                this.importLoading = false
            }).catch(err => {
                this.importLoading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.importTimeRanges = []
            this.importTime = {
                payStartTime: null,
                payEndTime: null
            }
            this.fileList = []
            this.file = null
            this.importVisible = true
        },
        removeFile(file, fileList) {
            this.file = null
        },
        async getCacheList() {
            const { data, success } = await getCostSetByPZDCache({ type: 1 })
            if (success) {
                this.catchList = data
                //获取定制款的规格,并且去重
                this.dzList = data.filter(item => item.customType == 1)
                this.dzList = this.dzList.filter((value, index, self) => {
                    return self.findIndex((t) => {
                        return t.norms === value.norms
                    }) === index
                })
                //获取常规款的规格
                this.cgList = data.filter(item => item.customType == 2)
                this.cgList = this.cgList.filter((value, index, self) => {
                    return self.findIndex((t) => {
                        return t.norms === value.norms
                    }) === index
                })
            }
        },
        addProps() {
            this.ruleForm.dtls.push({
                norms: null,
                colorType: null,
                hasBaseType: null,
                sheetCount: null,
                realTotalCostFt: null,
                remark: null,
            })
        },
        async pzdDelete(id) {
            this.$confirm('删除后无法恢复，是否确认删除?', '删除订单信息', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(async () => {
                const res = await deletePZDCSRecord({ Id: id })
                if (!res?.success) { return }
                this.$message({ type: 'success', message: '删除成功!' });
                this.getList()
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消删除' });
            });
        },
        //导出数据,使用时将下面的方法替换成自己的接口
        async exportProps() {
            this.isExport = true
            await exportPZDCSRecordPageList(this.ListInfo).then(({ data }) => {
                if (data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '皮桌垫报价测算' + new Date().toLocaleString() + '.xlsx')
                    aLink.click()
                    this.isExport = false
                }
            }).catch(() => {
                this.isExport = false
            })
        },
        async getList(type) {
            if (type == 'search') {
                this.ListInfo.currentPage = 1
                this.$refs.pager.setPage(1)
            }
            this.loading = true
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await getPZDCSRecordPageList(this.ListInfo)
                if (success) {
                    this.tableData = data.list
                    this.total = data.total
                    this.loading = false
                } else {
                    //获取列表失败
                    this.$message.error('获取列表失败')
                }
            } catch (error) {
                console.log(error);
                this.loading = false
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    el-button {
        margin-left: 10px;
    }
}

.iptCss {
    width: 80px;
}

::v-deep .cell {
    padding-left: 0;
}

::v-deep .el-table__body-wrapper {
    min-height: 300px !important;
    max-height: 340px !important;
}
</style>
