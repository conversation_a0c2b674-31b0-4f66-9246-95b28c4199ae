<template>
  <el-dialog title="聊天记录" :visible.sync="isShow" width="50%" :before-close="closeDialog" :v-if="isShow" v-dialogDrag >
     <div class="">
        <el-descriptions size="middle" class="margin-top" title="" :column="3">
            <el-descriptions-item label="平台/店铺"> {{platformName}} / {{dataJson?.shopName}}</el-descriptions-item>
            <el-descriptions-item label="线上单号">{{dataJson?.orderNo}}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{conversationTime }}</el-descriptions-item>
            <el-descriptions-item v-if="interfaceType && dataJson?.afterSalesRemark !='' &&  dataJson?.afterSalesRemark  !=null" label="售后原因">{{dataJson?.afterSalesRemark }}</el-descriptions-item>
          
        </el-descriptions>
      </div>
    <div style="height: 500px; overflow: auto" v-loading="isLoading">
      <div class="message-container" v-for="(message, index) in chartList" :key="index"
        :class="getMessageClass(message.userType)">
        <div v-if="message.userType == 3">
          <div class="system-box">
            <div class="system">
              <div>系统消息{{ chartList.length }}</div>
              <div style="margin-left: 20px">{{ message.recordTime }}</div>
            </div>
            <div style="color: #333" v-html="message.content"></div>
          </div>
        </div>
        <div v-if="message.userType == 0" style=" width:100%">
          <el-row>
            <el-col :span="1">
               <img src="@/assets/images/消费者.png" />
            </el-col>
             <el-col :span="23">
                 <div class="bubble">
                        <div style="display: flex; color: #999;margin-bottom: 5px;">
                            <div>
                              <span class="consumer">消费者</span>
                              <span class="username"> {{ message.userName }}</span>
                            </div>
                            <div class="recordTime" style="margin-left: 10px">
                              {{ message.recordTime }}
                            </div>
                        </div>
                        <div v-if="isProbablyImage(message.content)"  >
                          <el-image   :src="message.content" :preview-src-list="[message.content]" ></el-image>
                        </div>
                        <div v-else class="message msg_consumer" style="text-align: left" v-html="message.content"></div>
                  </div>
             </el-col>
          </el-row>
         
        </div>
        <div v-if="message.userType == 1" style=" width:100%">
          <el-row>
              <el-col :span="1">
                <img src="@/assets/images/商家.png" />
              </el-col>
               <el-col :span="23">
                   <div class="bubble">
                          <div style="display: flex; color: #999;margin-bottom: 5px;">
                              <div>
                                <span class="merchant">商家</span>
                                <span class="username"> {{ message.userName }}</span>
                              </div>
                              <div class="recordTime" style="margin-left: 10px">
                                {{ message.recordTime }}
                              </div>
                          </div>
                          <div v-if="isProbablyImage(message.content)"  >
                            <el-image style="object-fit: cover;" :src="message.content" :preview-src-list="[message.content]"></el-image>
                          </div>
                          <div v-else class="message msg_merchant" v-html="message.content"></div>
                    </div>
               </el-col>
            </el-row>
          <!-- <el-row >
            <el-col :span="23" >
                <div class="bubble2">
                    <div style="display: flex;align-items: center; margin-bottom: 5px;justify-content: flex-end; ">
                        <div style="margin-rigth: 10px" class="recordTime">
                          {{ message.recordTime }}
                        </div>
                        <div class="name">
                          <span class="username">{{ message.userName }}</span>
                          <span class="merchant">商家</span>
                        </div>
                    </div>
                    <div class="avatar">
                        <div v-if="isProbablyImage(message.content)">
                            <el-image style="object-fit: cover;" :src="message.content" :preview-src-list="[message.content]"></el-image>
                        </div>
                        <div v-else class="message msg_merchant" v-html="message.content"></div>
                    </div>
                </div>
            </el-col>
            <el-col :span="1">
                <img src="@/assets/images/商家.png" />
            </el-col>
          </el-row> -->
        </div>
        <div v-if="message.userType == 2">
          <div style="display: flex;align-items: left; color: #999;margin-bottom: 5px;">
            <div class="name">
              {{ message.userName }}
            </div>
            <div style="margin-left: 10px;line-height: 29px;">
              {{ message.recordTime }}
            </div>
          </div>
          <div class="avatar">
            <div v-if="isProbablyImage(message.content)">
              <el-image style="width: 500px; height: 500px" :src="message.content" :preview-src-list="[message.content]"></el-image>
            </div>
            <div v-else class="message" style="margin-right: 10px; text-align: left; color: #333" v-html="message.content"></div>
          </div>
        </div>

      </div>
    </div>
    <my-pagination  ref="chartPager" :total="chartTotal" @get-page="getChartList" />

         <template #footer>
                <div class="dialog-footer" style="display:flex;justify-content: flex-end;margin-right:35px">
                    <div style="position: relative;">
                        <el-button @click="btnChange('last')" type="primary" :disabled="isLastButtonDisabled">查看上一个</el-button>
                        <div style="position: absolute;right:-20px;top:-20px;cursor:pointer;" > 
                            <el-tooltip class="item" effect="dark" content="点击键盘的上箭头↑，可以快速查看上一个" placement="top"><i class="el-icon-question"></i></el-tooltip>
                        </div>
                    </div>
                    <div style="position: relative;margin-left:20px;">
                      <el-button @click="btnChange('next')" type="primary" :disabled="isNextButtonDisabled">查看下一个</el-button>
                          <div  style="position: absolute;right:-20px;top:-20px; cursor:pointer;" >
                              <el-tooltip class="item" effect="dark" content="点击键盘的下箭头↓，可以快速查看下一个" placement="top"> <i class="el-icon-question"></i></el-tooltip>
                         </div>
                    </div>
                </div>
        </template>
  </el-dialog>
</template>
<script>
import { getChartList } from "@/api/customerservice/unpaidorder";
import { formatTime } from "@/utils";
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chartList: [],
      chartTotal: 0,
      keyWord: null,
      isLoading: false,
      platform: null,
      dataJson:null,
      tableData:[],
      isLastButtonDisabled:false,
      isNextButtonDisabled:false,
      interfaceType:true,// true：售前 false：售后
    };
  },
    created(){
      document.addEventListener('keydown', this.handleArrowUp);
  },
  watch: {
    isShow(newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$refs.chartPager.setPage(1);
          this.getChartList();
        });
      }
    },
  },
computed:{
    platformName()//平台初始化
     {
      let platformList = [
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
        { name: "天猫", value: 1 },
        { name: "淘工厂", value: 8 },
        { name: "淘宝", value: 9 },
      ]
        // console.log(this.dataJson);
      if (this.dataJson?.platform) {
        return platformList.filter(item => item.value == this.dataJson?.platform)[0].name
      } else {
        return ""
      }
    },
  conversationTime() //日期转换
    {
      return this.dataJson?.conversationTime?formatTime(this.dataJson?.conversationTime , "YYYY-MM-DD"):""
    },
},
  methods: {
     handleArrowUp(event) {
        if(!this.isShow){ 
        return
      }
      if (event.key === 'ArrowUp' && !this.isLastButtonDisabled) {
        this.btnChange('last');
      }
      if (event.key === 'ArrowDown' && !this.isNextButtonDisabled) {
        this.btnChange('next');
      }
    },
    async getChartList() {
      this.isLoading = true;
      var pager = this.$refs.chartPager.getPager();
      let data = {
        platform: this.platform,
        keyWordType: 2,
        keyWord: this.keyWord,
        ...pager,
      };
      const res = await getChartList(data);
      this.chartTotal = res.data.total;
      this.chartList = res.data.list;
      await this.buttonDisabled()//按钮是否禁用
      this.isLoading = false;
    },
    async buttonDisabled(){ //按钮是否禁用
        this.isLastButtonDisabled=false;
        this.isNextButtonDisabled=false;
        const index= this.tableData.indexOf(this.dataJson);
        if(index==0){
          this.isLastButtonDisabled=true;
        }
        if(index==this.tableData.length-1){
          this.isNextButtonDisabled=true;
        }
    },
    //消息框样式动态选择
    getMessageClass(isSent) {
      let className = ""
      switch (isSent) {
        case 0:// 用户
          className = "message-container-left"
          break;
        case 1:// 客服
          className = "message-container-right"
          break;
        case 2:// 机器人
          className = "message-container-right"
          break;
        case 3:// 系统
          className = "message-container-left"
          break;
      }
      return className
      // return isSent != 0 && isSent != 3 ? "message-container-right" : "message-container-left";
    },
    isProbablyImage(url) {
      return (url.match(/\.(jpeg|jpg|gif|png)$/) != null)
    },
    onSubmitDot() {
      this.closeDialog();
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
    async btnChange(last){//查看上一个、查看下一个
      const index= this.tableData.indexOf(this.dataJson);
      this.$refs.chartPager.setPage(1);
      if(last=='last'){
            const info=this.tableData[index-1]
            this.dataJson=info;
            this.keyWord=info.conversationId;
            this.platform=info.platform;
           await this.getChartList();
      }else if(last=='next'){
             const info=this.tableData[index+1]
            this.dataJson=info;
            this.keyWord=info.conversationId;
            this.platform=info.platform;
          await this.getChartList();
      }
       await this.buttonDisabled()//按钮是否禁用
    },
  },
};
</script>
<style scoped lang="scss">
.s{
  position: absolute;
  justify-content: flex-end;
  cursor: pointer;
}
::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell{
  padding: 15px;
}
::v-deep .el-descriptions__body .el-descriptions__table{
  background-color: rgb(242, 244, 245);
}
.msg_merchant{
  padding: 10px; 
  color: white;
   border-radius: 5px;
  background-color: rgb(64, 158, 255);
}
.msg_consumer{
   padding: 10px;
 border-radius: 5px;
  background-color: rgb(240, 246, 255);
}

.system-box {
  background-color: #fafafa;
  padding: 10px;
  box-sizing: border-box;
  width: 300px;
}

.system {
  display: flex;
  margin-bottom: 4px;
  color: #999;
}

.message-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.name {
  text-align: right;
  margin: 5px 0;
  color: #999;
}

.avatar {
  float: right;
  margin-right: 0px;
  /* 修改这里将头像放在消息框的右边 */
  display: flex;
  align-items: center;
  text-align: right
}

.avatar-image {
  width: 400px;
  height: 400px;
  // object-fit: cover;
}

.bubble {
  color: #000;
  border-radius: 5px;
  padding-left: 10px;
  padding-bottom: 10px;
}
.bubble2 {
  color: #000;
  border-radius: 5px;
  padding-right: 10px;
}

.message {
  text-align: left;
  margin: 0;
  width: 400px;
}

// .message-container-right {
  
//   padding-left: 10px;
// }

.message-container-left {
  justify-content: flex-start;
}
.merchant{
  color: rgb(88, 170, 255) !important;
  border: 1px rgb(88, 170, 255) solid;
}
.consumer{
  color:red !important;
   border: 1px red solid;
}
.username{
  color: black !important;
  margin-right: 5px;
  margin-left: 5px;
}
.recordTime{
  color: gray !important;
}
  ::v-deep .el-image .el-image__inner {
       max-width: 500px !important;
        max-height: 1000px !important;
        height: auto;
        width: 500px!important;
    }
</style>
