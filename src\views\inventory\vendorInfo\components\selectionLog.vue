<template>
  <div>
    <vxetablebase :id="'selectionLog20250313'" :tablekey="'selectionLog20250313'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="400">
    </vxetablebase>
    <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { getSampleGoodsLogList } from '@/api/customerservice/albbinquirs'
const tableCols = [
  { sortable: 'custom', width: '150', align: 'center', prop: 'fieldName', label: '被修改字段', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'oldValue', label: '旧值', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'newValue', label: '新值', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'createdTime', label: '操作时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '操作人', },
]
export default {
  name: "selectionLog",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    sampleGoodsId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        sampleGoodsId: this.sampleGoodsId
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    this.ListInfo.sampleGoodsId = this.sampleGoodsId
    await this.getList()
  },
  methods: {
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getSampleGoodsLogList(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss"></style>
