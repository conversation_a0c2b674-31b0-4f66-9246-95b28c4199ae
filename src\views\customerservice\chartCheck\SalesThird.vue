<template>
  <my-container>
    <!--顶部操作-->
    <div class=".top">
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
        <el-form-item label="违规时间:">
          <el-date-picker style="width: 320px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
       <el-form-item label="ERP处理状态:">
          <el-select v-model="Filter.systemState" placeholder="请选择" class="el-select-content" clearable>
            <el-option v-for="item in erpStatusList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
         <el-form-item label="平台处罚状态:">
          <el-select v-model="Filter.platformPunishState" placeholder="请选择" class="el-select-content" clearable>
            <el-option v-for="item in platformStatusList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="平台:">
          <el-select v-model="Filter.platform" placeholder="请选择" class="el-select-content" @change="changePlatform" clearable>
            <el-option v-for="item in platformTypeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺:">
          <el-select v-model="Filter.shopName" placeholder="请选择" class="el-select-content"  filterable multiple clearable collapse-tags> 
            <el-option v-for="item in shopList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="聊天账号:">
          <el-select v-model="Filter.chatAccount" placeholder="请选择" class="el-select-content" clearable filterable multiple collapse-tags>
            <el-option v-for="item in chatAccountList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="违规单号:">
          <el-input v-model.trim="Filter.violationOrderNo" maxlength="20" clearable />
        </el-form-item>
        <el-form-item label="线上订单号:">
          <el-input maxlength="50" v-model.trim="Filter.orderNo" clearable />
        </el-form-item>
        <el-form-item label="宝贝ID:">
          <el-input maxlength="20" v-model.trim="Filter.proId" clearable />
        </el-form-item>
        <el-form-item label="账号使用人:">
          <el-select v-model="Filter.userName" placeholder="请选择" class="el-select-content" clearable filterable multiple collapse-tags>
            <el-option v-for="item in userNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组:">
          <el-select v-model="Filter.groupName" placeholder="请选择" class="el-select-content" clearable filterable multiple collapse-tags>
            <el-option v-for="item in groupNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="初审类型:">
          <el-select v-model="Filter.refuseInitialAuditType" placeholder="请选择" class="el-select-content" clearable>
            <el-option v-for="item in penaltyList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="复审类型:">
          <el-select v-model="Filter.refuseFinalAuditType" placeholder="请选择" class="el-select-content" clearable>
            <el-option v-for="item in penaltyList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onExport()" v-if="checkPermission(['api:customerservice:UnPayOrder:PlatformPunishExport'])">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="uptime">数据更新时间: {{ upDataTime }}</div>

    <!--列表-->
    <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="true" :summaryarry="summaryarry" :tableCols="tableCols" :loading="listLoading" style="width: 100%;
    height: calc(100% - 21%); margin: 0">
    </Ces-table>

    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="gettrainplanList" />
    </template>

<!-- 判罚结果查看 -->
<ChartPenaltyDialog  :v-if="penaltydialogVisible" :isShow="penaltydialogVisible" @closeDialog="penaltydialogVisible = false" ref="penaltyRef"></ChartPenaltyDialog>
<!-- 初审判罚 -->
<FirstPenaltyDialog  :v-if="firstDealwithdialogVisible" :isShow="firstDealwithdialogVisible" @closeDialog="firstDealwithdialogVisible = false" @upData="onSearch" ref="firstDealWithRef"></FirstPenaltyDialog>
<!-- 复审判罚 -->
<PenaltyDialog  :v-if="dealwithdialogVisible" :isShow="dealwithdialogVisible" @closeDialog="dealwithdialogVisible = false" @upData="onSearch" ref="dealwithRef"></PenaltyDialog>
<!-- 聊天记录 -->
<ChartRecordDialog  :v-if="recorddialogVisible" :isShow="recorddialogVisible" @closeDialog="recorddialogVisible = false" ref="recordithRef"></ChartRecordDialog>


    <!-- 订单日志信息 -->
    <el-dialog title="订单日志信息" v-if="dialogHisVisible" :visible.sync="dialogHisVisible" width="70%" height="600px" v-dialogDrag append-to-body>
      <OrderActionsByInnerNos ref="OrderActionsByInnerNos" :orderNo="orderNo" style="z-index:10000;height:600px" />
    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue"; 
import OrderActionsByInnerNos from "@/views/order/logisticsWarning/orderActionsByInnerNoLogPage.vue";

import dayjs from "dayjs";
import { formatTime } from "@/utils";

import {
  getPlatformPunishPageList,
getPlatformPunishQueryGroup,
platformPunishExport} from "@/api/customerservice/chartCheck";
import ChartPenaltyDialog from "@/views/customerservice/chartCheck/SalesDialog/ChartPenaltyDialog"
import PenaltyDialog from "@/views/customerservice/chartCheck/SalesDialog/PenaltyDialog"
import ChartRecordDialog from "@/views/customerservice/chartCheck/SalesDialog/ChartRecordDialog"
import FirstPenaltyDialog from "@/views/customerservice/chartCheck/SalesDialog/firstPenaltyDialog"

import { formatLinkProCode } from "@/utils/tools";
import { Row } from 'element-ui';


//平台下拉
 const platformTypeList=[
        { name: "拼多多", value: 2 },
        { name: "抖音", value: 6 },
 ];
//erp处理状态下拉
 const erpStatusList=[
    {name:"未判罚",value:0},
    {name:"已判罚",value:1},
 ];
 //平台处理状态下拉
const platformStatusList=[
    {name:"已违规，可发起申诉",value:0},
    {name:"已申诉，平台审核中",value:1},
    {name:"平台已处理",value:2},
    {name:"申诉成功",value:3},
    {name:"平台处理中",value:4},
];

const tableCols = [
  {
    istrue: true,
    prop: "systemState",
    label: "ERP处理状态",
    width: "100",
   formatter: (row) => 
      {
        return row.systemState!=null?erpStatusList.filter(item => item.value == row.systemState)[0].name:""
      },
  },
  {
    istrue: true,
    prop: "platformPunishState",
    label: "平台处罚状态",
    width: "150",
    formatter:(row)=>
      {
        return row.platformPunishState!=null?platformStatusList.filter(item=>item.value==row.platformPunishState)[0].name :""
      },
  },
  {
    istrue: true,
    prop: "punishAmount",
    label: "罚款/扣分详情",
    sortable: "custom",
    width: "150",
  },
  {
    istrue: true,
    prop: "platform",
    label: "平台",
    width: "100",
     formatter:(row)=>
      {
        return  row.platform?platformTypeList.filter(item=>item.value==row.platform)[0].name:"" 
      },
  },
  {
    istrue: true,
    prop: "shopName",
    label: "店铺",
    width: "150",
  },
  {
    istrue: true,
    prop: "violationTime",
    label: "违规时间",
    width: "120",
    sortable: "custom",
     formatter: (row) => {
      return row.violationTime?formatTime(row.violationTime, "YYYY-MM-DD"):"";
    },
  },
  {
    istrue: true,
    prop: "violationOrderNo",
    label: "违规单号",
    width: "150",
  },
  {
    istrue: true,
    prop: "orderNo",
    label: "线上订单号",
    width: "150",
     type: 'click',
    handle: (that, row) => that.showLogDetail(row)
  },
  {
    istrue: true,
    prop: "proId",
    label: "宝贝ID",
    width: "100",
     type: "html",
    formatter: (row) => formatLinkProCode(row.platform, row.proId),
  },
  {
    istrue: true,
    prop: "chatAccount",
    label: "聊天账号",
    width: "100",
  },
    {
    istrue: true,
    prop: "userName",
    label: "使用账号人",
    width: "100",
    sortable: "custom",
  },
    {
    istrue: true,
    prop: "groupName",
    label: "分组",
    width: "80",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "groupManager",
    label: "组长",
    width: "80",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "refuseInitialAuditType",
    label: "初审类型",
    width: "80",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "initialOperator",
    label: "初审人",
    width: "80",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "refuseFinalAuditType",
    label: "复审类型",
    width: "80",
    sortable: "custom",
  },
  {
    istrue: true,
    prop: "finalOperator",
    label: "复审人",
    width: "80",
    sortable: "custom",
  },
  {
    istrue: true,
    type: "button",
    label: "操作",
    align: "center",
    fixed: "right",
    width:"250",
    btnList: [
      { label: "违规详情", handle: (that, row) => that.showDetail(row)},
      { label: "查看", handle: (that, row) => that.openResultLog(row)},
      { label: "初审判罚", handle: (that, row) => that.firstDealWith(row),
        display:(row)=>{return  row.refuseInitialAuditType!="" && row.refuseInitialAuditType!=null},
         permission: "api:customerservice:UnPayOrder:InitialPenalty",
         ishide:(that,row) => {return row.platform ==6}
      },
      { label: "判罚", handle: (that, row) => that.firstDealWith(row),
        display:(row)=>{return  row.systemState!=0},
         permission: "api:customerservice:UnPayOrder:InitialPenalty",
         ishide:(that,row) => {return row.platform ==2}
      },
      { label: "复审判罚", handle: (that, row) => that.dealWith(row),
        display:(row)=>{return row.systemState==1 || row.refuseInitialAuditType != "平台误判"},
        permission: "api:customerservice:UnPayOrder:FinalPenalty",
        ishide:(that,row) => {return row.platform ==6}
      },
    ],
  },
];
export default {
  name: "SalesThird",
  components: {
    MyContainer,
    CesTable,
    OrderActionsByInnerNos,
    getPlatformPunishPageList,
    ChartPenaltyDialog,//查看判罚结果
    PenaltyDialog,//复审判罚
    FirstPenaltyDialog,//初审判罚
    ChartRecordDialog,//聊天记录
  },
  data() {
    return {
      that: this,
      Filter: {
        timerange: [
          formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
          formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        ],
        systemState:null,
        platformPunishState:null,
        platform:null,
        shopName:null,
        violationOrderNo:null,
        chatAccount:null,
        orderNo:null,
        proId:null,
        userName:null,
        groupName:null,
      },
      erpStatusList:erpStatusList,
      platformTypeList:platformTypeList,
      platformStatusList:platformStatusList,
      shopList: [],
      userNameList:[],
      groupNameList:[],
      chatAccountList:[],
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "createdTime", isAsc: false },
      listLoading: false,
      penaltydialogVisible:false,//查看判罚结果
      dealwithdialogVisible:false,//复审判罚
      recorddialogVisible:false,//查看聊天记录
      firstDealwithdialogVisible:false,//初审判罚
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate:  new Date(),
      upDataTime: "",
      dialogHisVisible: false,
      orderNo: '',
      penaltyList:["客服责任","平台误判"],
    };
  },
  async mounted() {
        await this.init(); 
    await this.onSearch();
  },
  methods: { 
    datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-4);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.Filter.timerange=[];
        this.Filter.timerange[0]=this.datetostr(date1);
        this.Filter.timerange[1]=this.datetostr(date2);
      },
    showLogDetail (row) {    //查看线上订单号记录
      this.dialogHisVisible = true;
      this.orderNo = row.orderNo;
    },
    showDetail(row) {    //查看聊天记录
      this.$refs.recordithRef.dataJson=row
          this.$refs.recordithRef.tableData=this.tableData
      this.recorddialogVisible = true;
    },
    openResultLog(row) {    //查看判罚结果
      this.$refs.penaltyRef.dataJson = row
      this.$refs.penaltyRef.isShowOrHide=true;
         this.$refs.penaltyRef.tableData=this.tableData
       this.penaltydialogVisible=true;
    },
    firstDealWith(row){   //初审判罚
       this.$refs.firstDealWithRef.dataJson = row
       this.$refs.firstDealWithRef.isShowOrHide=true;
            this.$refs.firstDealWithRef.tableData=this.tableData.filter(item=>{ return (item.systemState==0 || item.systemState==null || item.systemState=='')&&  (item.refuseInitialAuditType==null || item.refuseInitialAuditType=='')});
       this.firstDealwithdialogVisible=true;
    },
    dealWith(row){    //复审判罚
       this.$refs.dealwithRef.dataJson = row
       this.$refs.dealwithRef.isShowOrHide=true;
       this.$refs.dealwithRef.tableData=this.tableData.filter(item=>{ return (item.systemState==0) &&  (item.refuseInitialAuditType=='平台误判') });
       this.dealwithdialogVisible=true;
    },
    onSearch() {    // 条件查询
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.gettrainplanList();
        this.getSelectOptionList();
      });
    },
    //头部下拉列表
    async getSelectOptionList()
    {
        const para = { ...this.Filter };
        if (this.Filter.timerange) {
          para.timeStart = this.Filter.timerange[0];
          para.timeEnd = this.Filter.timerange[1];
        }
      const param={
        platform:this.Filter.platform,
        timeStart:para.timeStart,
        timeEnd:para.timeEnd
        }
        
        const res = await getPlatformPunishQueryGroup(param);
        const data=res.data;
        this.shopList=data.shopNameGrouop
        this.chatAccountList=data.chatAccountGrouop
        this.groupNameList=data.groupNameGrouop
        this.userNameList=data.userNameGrouop
    },
    //平台联动
    async changePlatform(val) {
        const para = { ...this.Filter };
      if (this.Filter.timerange) {
          para.timeStart = this.Filter.timerange[0];
          para.timeEnd = this.Filter.timerange[1];
        }
        const param={
        platform:val,
        timeStart:para.timeStart,
        timeEnd:para.timeEnd
      }
        this.Filter.shopName=[];
        this.Filter.chatAccount=[];
        this.Filter.groupName=[];
        this.Filter.userName=[];
      const res = await getPlatformPunishQueryGroup(param);
        const resData=res.data;
        this.shopList=resData.shopNameGrouop
        this.chatAccountList=resData.chatAccountGrouop
        this.groupNameList=resData.groupNameGrouop
        this.userNameList=resData.userNameGrouop
    },
    //获取头部下拉条件
    getCondition() {
      console.log("getCondition")
      const para = { ...this.Filter };
      if (this.Filter.timerange) {
        para.timeStart = this.Filter.timerange[0];
        para.timeEnd = this.Filter.timerange[1];
      }
      // if (this.Filter.shopName) para.shopName = this.Filter.shopName;
      // else para.shopName = null;
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    //接口访问并赋值tab
    async gettrainplanList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getPlatformPunishPageList(params);
      //  const res = await getPlatformPunishPageList({pageSize:50,currentPage:1,orderBy:"",isAsc:false});
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      this.upDataTime = res.data.extData.dataUpdateTime;
      data.forEach((d) => {
        d._loading = false;
      });
       this.tableData = data;
      this.summaryarry = res.data.summary;
    },
    //表头排序
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    //导出
    // async onExport() {
     
    //   // var loadingInstance = this.$loading({
    //   //   text: "正在导出，请稍后",
    //   //   fullscreen: false,
    //   // });
    //   var res = await platformPunishExport(params);
    //   // loadingInstance.close();
    //   const aLink = document.createElement("a");
    //   let blob = new Blob([res], { type: "application/vnd.ms-excel" });
    //   aLink.href = URL.createObjectURL(blob);
    //   aLink.setAttribute(
    //     "download",
    //     "平台判罚_" + new Date().toLocaleString() + ".xlsx"
    //   );
    //   aLink.click();
    // },

    //导出
    async onExport() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      var res = await platformPunishExport(params);
      const aLink = document.createElement("a");
      let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute(
        "download",
        "平台判罚_" + new Date().toLocaleString() + ".xlsx"
      );
      aLink.click();
    },


  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}
::v-deep .el-select__tags-text { 
  max-width: 45px;
}

</style>
