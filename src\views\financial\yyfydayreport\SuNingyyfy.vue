<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <el-tabs v-model="activeName" style="height:94%;">
      <el-tab-pane   label="直通车" name="tab1" style="height: 100%;">
          <SuNingztc :filter="Filter" ref="SuNingztc" style="height: 100%;"/>
      </el-tab-pane>
      <el-tab-pane   label="万向台" name="tab2" style="height: 100%;">
        <SuNingwxt :filter="Filter" ref="SuNingwxt" style="height: 100%;"/>
    </el-tab-pane>
    </el-tabs>
    </my-container >
   </template>
  <script>
  import MyContainer from "@/components/my-container";
  import SuNingztc from '@/views/financial/yyfydayreport/SuNingztc'
  import SuNingwxt from '@/views/financial/yyfydayreport/SuNingwxt'
  import checkPermission from '@/utils/permission'
  export default {
    name: "Users",
    components: { MyContainer,SuNingztc,checkPermission,SuNingwxt},
    data() {
      return {
        that:this,
        Filter: {
        },
        pageLoading:"",
        activeName:"tab1",
        shopList:[],
        userList:[],
        groupList:[],
        selids:[],
        dialogVisibleSyj:false,
        fileList:[],
      };
    },
    mounted() {
    },
    methods: {
  async onSearch(){
    if (this.activeName=='tab1') 
    this.$refs.SuNingztc.onSearch();
    
  }
    },
  };
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
  