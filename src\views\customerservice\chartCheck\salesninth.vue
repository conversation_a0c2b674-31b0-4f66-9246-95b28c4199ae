<template>
  <my-container>
    <!--顶部操作-->
    <div class=".top">
      <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>
          <el-form-item label="数据维度：">
          <el-select v-model="Filter.dataType" placeholder="数据维度" style="width:120px"  class="el-select-content" >
            <el-option v-for="item in dataTypeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择平台：">
          <el-select v-model="Filter.platform" placeholder="平台" style="width:120px"  class="el-select-content" clearable @change="changePlatform">
            <el-option v-for="item in platformList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="售后时间：">
          <el-date-picker style="width: 320px" v-model="Filter.conversationTime" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="审核时间：">
          <el-date-picker style="width: 320px" v-model="Filter.auditTime" type="datetimerange" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" :default-value="defaultDate"></el-date-picker>
        </el-form-item>
      
          <el-form-item label="">
          <el-select v-model="Filter.groupNameList" placeholder="分组" class="el-select-content" filterable multiple clearable collapse-tags @focus="changeAfterSalesState">
            <el-option v-for="item in gNameList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport" v-if="checkPermission(['api:customerservice:UnPayOrder:ReasonForRefundStatisticsExport'])">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!--列表-->
    <Ces-table ref="table" :that="that" :isIndex="true" @sortchange="sortchange" :tableData="tableData"
      :showsummary="true" :tableCols="tableCols" :loading="listLoading" :summaryarry="summaryarry"
      style="width: 100%; height: calc(100% - 12%); margin: 0">
    </Ces-table>
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @get-page="getPageList" />
    </template>
    
    <!-- 趋势图 -->
         <!-- <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%"
            :close-on-click-modal="false" v-dialogDrag>
             <el-date-picker
                    v-model="chartime"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="chatSearch($event)"
                    >
                  </el-date-picker>
            <div >
                <span>
                    <buschar v-if="dialogMapVisible.chatLoading" :analysisData="dialogMapVisible.data" ></buschar>
                </span>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
            </span>
        </el-dialog> -->
    <!-- 趋势图 -->
    <el-drawer :title="chatProp.title" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch($event, dialogType)"
                    :picker-options="pickerOptions" style="margin: 10px;" :clearable="false" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import CesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar';
import dayjs from "dayjs";
import { formatTime } from "@/utils";
  import middlevue from "@/store/middle.js"
import {
  exportAfter,
  getReasonForRefundStatisticsPageList,
  getReasonForRefundGroupNameList,
  getReasonForRefundTrendChart,
  reasonForRefundStatisticsExport
} from "@/api/customerservice/chartCheck";


const platformList = [
  { name: "拼多多", value: 2 },
  { name: "抖音", value: 6 },
  { name: "天猫", value: 1 },
  // { name: "淘工厂", value: 8 },
  { name: "淘宝", value: 9 },
]

const tableCols = [ 
    { istrue: true, prop: 'platform', label: '平台', sortable: 'custom',
        formatter: (row) => {
         return row.platform==0?"": platformList.filter(item => item.value == row.platform)[0].name
        },
    },
    { istrue: true, prop: 'groupName', label: '组名', sortable: 'custom'},
    {
        istrue: true, summaryEvent: true, rop: '', label: `运营问题`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'column1', label: '虚假宣传', sortable: 'custom', type: 'click', handle: (that,row) => that.groupclick(row, '虚假宣传') },
            { istrue: true, prop: 'column2', label: '编码问题', sortable: 'custom', type: 'click', handle: (that,row) => that.groupclick(row, '编码问题') },
            { istrue: true, prop: 'column3', label: '低价引流问题', sortable: 'custom',  type: 'click', handle: (that,row) => that.groupclick(row, '低价引流问题') },
        ]
    },
    {
        istrue: true, summaryEvent: true, rop: '', label: `仓库问题`, merge: true, prop: 'mergeField1',
        cols: [
            { istrue: true, prop: 'column4', label: '订单错漏发', sortable: 'custom', type: 'click', handle: (that,row) => that.groupclick(row, '订单错漏发')},
            { istrue: true, prop: 'column5', label: '包装问题', sortable: 'custom', type: 'click', handle: (that,row) => that.groupclick(row, '包装问题') },
            { istrue: true, prop: 'column6', label: '定制问题', sortable: 'custom',  type: 'click', handle: (that,row) => that.groupclick(row, '定制问题') },
        ]
    },
    {
        istrue: true, summaryEvent: true, rop: '', label: `售前问题`, merge: true, prop: 'mergeField2',
        cols: [
            { istrue: true, prop: 'column7', label: '推荐错误', sortable: 'custom', type: 'click', handle: (that,row) => that.groupclick(row, '推荐错误')},
            { istrue: true,  prop: 'column8', label: '过度承诺', sortable: 'custom', type: 'click', handle: (that,row) => that.groupclick(row, '过度承诺')},
            { istrue: true,  prop: 'column9', label: '漏备注或备注错', sortable: 'custom',  type: 'click', handle: (that,row) => that.groupclick(row, '漏备注或备注错') },
        ]
    },
    { istrue: true, prop: 'column10', label: '快递问题', sortable: 'custom',type: 'click', handle: (that,row) => that.groupclick(row, '快递问题')},
    { istrue: true, prop: 'column11', label: '采购问题', sortable: 'custom',type: 'click', handle: (that,row) => that.groupclick(row, '采购问题')},
    { istrue: true, prop: 'column12', label: '售后问题', sortable: 'custom',type: 'click', handle: (that,row) => that.groupclick(row, '售后问题')},
    { istrue: true, prop: 'column13', label: '产品质量问题', sortable: 'custom',type: 'click', handle: (that,row) => that.groupclick(row, '产品质量问题')},
    { istrue: true, display: true, label: '趋势图', style: "color:red;cursor:pointer;", formatter: (row) => '趋势图', type: 'click', handle: (that, row) => that.openDialog(row) },
];

export default {
  name: "afterSales",
  components: {
    MyContainer,
    CesTable,
    buschar
  },
  data() {
    return {
      that: this,
      Filter: {
        //  conversationTime:[
        //    formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
        //    formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        // ],
        // timerange: [
        //   formatTime(dayjs().startOf("month"), "YYYY-MM-DD HH:mm:ss"),
        //   formatTime(new Date(), "YYYY-MM-DD 23:59:59"),
        // ],
        platform: null,
        groupNameList:[],//组名
        dataType:0,
      },
      platformList: platformList,
      gNameList:[],
      tableData: [],
      tableCols: tableCols,
      total: 0,
      summaryarry: null,
      pager: { orderBy: "", isAsc: false },
      listLoading: false,
      pickerOptions: {
        disabledDate(date) {
          // 设置禁用日期
          const start = new Date("1970/1/1");
          const end = new Date("9999/12/31");
          return date < start || date > end;
        },
      },
      defaultDate: new Date(),
      dialogHisVisible: false,
      dataTypeList:[
        { name: "平台", value: 0 },
        { name: "分组", value: 1 },
      ],
      chatFilter:{},
      chatProp: {
                title: "",
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
    };
  },
  async mounted() {
    // await this.init();
    await this.onSearch();
  },
  methods: {

     datetostr(date) {
      var y = date.getFullYear();
      var m = ("0" + (date.getMonth() + 1)).slice(-2);
      var d = ("0" + date.getDate()).slice(-2);
      return y + "-" + m + "-" + d;
    },
    async init(){
        var date1 = new Date(); date1.setDate(date1.getDate()-4);
        var date2 = new Date(); date2.setDate(date2.getDate()-1);
        this.Filter.conversationTime=[];
        this.Filter.conversationTime[0]=this.datetostr(date1);
        this.Filter.conversationTime[1]=this.datetostr(date2);
      },
    
    // 查询
    onSearch() {
      this.$nextTick(() => {
        this.$refs.pager.setPage(1);
        this.getPageList();
      });
    },
    getCondition() {
      const para = { ...this.Filter };
      // if (this.Filter.timerange) {
      //   para.orderTimeStart = this.Filter.timerange[0];
      //   para.orderTimeEnd = this.Filter.timerange[1];
      // }
      if(this.Filter.conversationTime){
        para.conversationTimeStart = this.Filter.conversationTime[0];
        para.conversationTimeEnd = this.Filter.conversationTime[1];
      }
      if(this.Filter.auditTime){
        para.operatorTimeStart = this.Filter.auditTime[0];
        para.operatorTimeEnd = this.Filter.auditTime[1];
      }

      
      // if (!(para.orderTimeStart && para.orderTimeEnd)) {
      //   this.$message({ message: "请先选择日期！", type: "warning" });
      //   return false;
      // }
      if (this.Filter.shopNameList) para.shopNameList = this.Filter.shopNameList;
      else para.shopNameList = null;
      var pager = this.$refs.pager.getPager();
      const params = {
        ...pager,
        ...this.pager,
        ...para,
      };
      return params;
    },
    async getPageList() {
      var params = this.getCondition();
      if (params === false) {
        return;
      }
      this.listLoading = true;
      const res = await getReasonForRefundStatisticsPageList(params);
      this.listLoading = false;
      if (!res?.success) {
        return;
      }
      this.total = res.data.total;
      const data = res.data.list;
      
      data.forEach((d) => {
        d._loading = false;
      });
      this.tableData = data;
      this.summaryarry = res.data.summary;
    },
    async changePlatform(val) {
      console.log(val,'平台')
      this.gNameList=[];
      this.Filter.groupNameList=null;
      if(val)
      {
        const groupName = await getReasonForRefundGroupNameList({platform:val});
        this.gNameList=groupName.data;
      }
    },
    changeAfterSalesState(val) {
      if (!this.Filter.platform) {
        // this.statusList=null;
        this.$message({ message: "请先选择平台！", type: "warning" });
        return false;
      }
    },
    sortchange(column) {
      if (!column.order) this.pager = {};
      else
        this.pager = {
          orderBy: column.prop,
          isAsc: column.order.indexOf("descending") == -1 ? true : false,
        };
      this.onSearch();
    },
    async onExport(type) {
         var params = this.getCondition();
          if (params === false) {
            return;
          }
          var res = await reasonForRefundStatisticsExport(params);
          const aLink = document.createElement("a");
          let blob = new Blob([res], { type: "application/vnd.ms-excel" });
          aLink.href = URL.createObjectURL(blob);
          aLink.setAttribute(
            "download",
            "售后退款原因数据统计_" + new Date().toLocaleString() + ".xlsx"
          );
          aLink.click();
    },
    async handleCommand(command) {
            switch (command) {
                //列表数据
                case 'a':
                    await this.onExport(0)
                    break;
                //聊天数据
                case 'b':
                   await this.onExport(1)
                    break;
            }
    },
    groupclick(row, typeName) {
            const params={
                platform:row.platform,
                groupName:row.groupName,
                conversationTime:this.Filter.conversationTime,
                auditTime:this.Filter.auditTime,
                typeName:typeName,
                dataplatform:this.Filter.dataType,
            }
             this.$emit("showtabsecond",params)
          
    },
    async chatSearch() {
      if(this.chatProp.chatTime){
        this.chatFilter.operatorTimeStart = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD');
        this.chatFilter.operatorTimeEnd = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD');
      }
            this.chatProp.chatDialog = true;
            this.chatProp.chatLoading = true;
            const data = await getReasonForRefundTrendChart(this.chatFilter);
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
    },

        async openDialog(row) {
           this.chatProp.title = this.Filter.dataType==0?platformList.filter(item => item.value == row.platform)[0].name:row.groupName;


            this.chatProp.chatDialog = true;

            let params = this.getCondition();
            params.groupName = row.groupName;
            params.platform = row.platform;
            if(this.Filter.auditTime)
            {
              this.chatProp.chatTime=this.Filter.auditTime;
            }else{
              var date1 = new Date(); date1.setDate(date1.getDate()-29);
              var date2 = new Date(); date2.setDate(date2.getDate());
              this.chatProp.chatTime=[];
              this.chatProp.chatTime[0]=this.datetostr(date1);
              this.chatProp.chatTime[1]=this.datetostr(date2);
            }
            this.chatFilter={...params}
            this.chatProp.chatLoading = true;
            const data = await getReasonForRefundTrendChart(this.chatFilter);
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },

  },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
  background-color: #fff;
}

::v-deep .inner-container::-webkit-scrollbar {
  display: none;
}

::v-deep .mycontainer {
  position: relative;
}

.uptime {
  font-size: 14px;
  position: absolute;
  right: 30px;
}
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 45px;
}
</style>
