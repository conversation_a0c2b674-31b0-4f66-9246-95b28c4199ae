<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="filter.type" placeholder="选择成品/半成品" :clearable="true" :collapse-tags="true" filterable>
                        <el-option label="全部" value />
                        <el-option label="成品" value="1" />
                        <el-option label="半成品" value="0" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="warehouses" multiple clearable filterable :collapse-tags="true" placeholder="请选择仓库" style="width: 160px">
                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select v-model="groupId" multiple clearable filterable :collapse-tags="true" placeholder="请选择运营组" style="width: 160px">
                        <el-option label="所有" value="" />
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>             
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.GoodsCode" placeholder="商品编码" maxLength="50" clearable @change="onSearch" />                   
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.GoodsName" placeholder="商品名称" maxLength="50" clearable @change="onSearch" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model.trim="filter.keywords" placeholder="关键字查询" maxLength="20" clearable >
                        <el-tooltip slot="suffix"  effect="dark" content="模糊查询：创建人、编辑人。" placement="bottom">
                            <i class="el-input__icon el-icon-question"></i>
                        </el-tooltip>
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :isSelectColumn="true" :loading="listLoading">
            <el-table-column type="expand">
                <template slot-scope="props">
                    <div style="padding: 0;">
                        <el-row>
                            <el-col :span="12">
                                <el-card class="box-card" style="width:100%">
                                    <div slot="header" class="clearfix">
                                        <span>半成品</span>
                                    </div>
                                    <div style="max-height: 400px;overflow: auto;">
                                        <el-row v-for="(item,index) in props.row.goodsPart" :key="index">
                                            <el-col :span="2">{{index+1}}</el-col>
                                            <el-col :span="5">{{item.goodsCode}}</el-col>
                                            <el-col :span="2">{{item.qty}}</el-col>
                                            <el-col :span="14">{{item.goodsName}}</el-col>
                                        </el-row>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :span="12">
                                <el-card class="box-card" style="width:100%;">
                                    <div slot="header" class="clearfix">
                                        <span>成品</span>
                                    </div>
                                    <div style="max-height: 400px;overflow: auto;">
                                        <el-row v-for="(item,index) in props.row.goodsFinished" :key="index">
                                            <el-col :span="2">{{index+1}}</el-col>
                                            <el-col :span="6">{{item.goodsCode}}</el-col>
                                            <el-col :span="2">{{item.qty}}</el-col>
                                            <el-col :span="14">{{item.goodsName}}</el-col>
                                        </el-row>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>
                    </div>
                </template>
            </el-table-column>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog :title="thisTitle" :visible.sync="dialogFormVisible" width="1000px" style="width: 100%;" v-dialogDrag>
            <el-row>
                <el-col :span="6">

                </el-col>
                <el-col :span="12">
                    <span style="text-align:center;font-weight:bold;">选择仓库:</span>
                    <el-select v-model="entity.warehouse" placeholder="选择仓库" style="width: 80%">
                        <!-- <el-option label="义乌" :value="0"></el-option>
                        <el-option label="昌东" :value="1"></el-option>
                        <el-option label="安徽" :value="3"></el-option> -->
                        <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                            :value="item.wms_co_id" />
                    </el-select>
                </el-col>
                <el-col :span="6"></el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-card class="box-card" style="width:100%">
                        <div slot="header" class="clearfix">
                            <span>半成品</span>
                            <el-button @click="onShowChoice(0)" style="float: right; padding: 3px 0" type="text">选择</el-button>
                        </div>
                        <div style="max-height: 400px;overflow: auto;">
                            <el-row style="text-align:center;font-weight:bold;font-size:xx-small;">
                                <el-col :span="1"></el-col>
                                <el-col :span="6">商品编码</el-col>
                                <el-col :span="3">数量</el-col>
                                <el-col :span="12">名称</el-col>
                            </el-row>
                            <el-row v-for="(item,index) in entity.goodsPart" :key="index">
                                <el-col :span="1">{{index+1}}</el-col>
                                <el-col :span="6">{{item.goodsCode}}</el-col>
                                <el-col :span="3">
                                    <el-input v-model="item.qty" placeholder="数量" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>2147483647){value=2147483647}" />
                                </el-col>
                                <el-col :span="12">{{item.goodsName}}</el-col>
                                <el-col :span="1">
                                    <el-button type="danger" icon="el-icon-delete" @click="ondeleteChoice(0,item.goodsCode)" circle />
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="12">
                    <el-card class="box-card" style="width:100%;">
                        <div slot="header" class="clearfix">
                            <span>成品</span>
                            <el-button @click="onShowChoice(1)" :disabled="entity.goodsFinished.length >= 1" style="float: right; padding: 3px 0" type="text">选择</el-button>
                        </div>
                        <div style="max-height: 400px;overflow: auto;">
                            <el-row style="text-align:center;font-weight:bold;font-size:xx-small;">
                                <el-col :span="1"></el-col>
                                <el-col :span="6">商品编码</el-col>
                                <el-col :span="3">数量</el-col>
                                <el-col :span="12">名称</el-col>
                            </el-row>
                            <el-row v-for="(item,index) in entity.goodsFinished" :key="index">
                                <el-col :span="1">{{index+1}}</el-col>
                                <el-col :span="6">{{item.goodsCode}}</el-col>
                                <el-col :span="3">
                                    <el-input v-model="item.qty" placeholder="数量" oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=0} if(value>2147483647){value=2147483647}" />
                                </el-col>
                                <el-col :span="12">{{item.goodsName}}</el-col>
                                <el-col :span="1">
                                    <el-button type="danger" v-if="isAdd" icon="el-icon-delete" @click="ondeleteChoice(1,item.goodsCode)" circle />
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">关闭</el-button>
                    <el-button type="primary" @click="onSave()">保存</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="选择编码" :visible.sync="goodschoiceVisible" width='88%' height='500px' v-dialogDrag>
            <goodschoice :ischoice="true" ref="goodschoice" style="z-index:10000;height:500px" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="goodschoiceVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onQueren()">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="导入数据" :visible.sync="importDialogVisible" width="400px" height="200px" v-dialogDrag>
            <div>
                <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action=""
                accept=".xlsx" :file-list="fileList" :http-request="uploadFile" :on-success="onUploadSuccess"
                :on-change="onUploadChange" :on-remove="onUploadRemove">
                    <template #trigger>
                        <el-button size="small" type="primary" style="width: 90px;">选取文件</el-button>
                    </template>
                    <el-button style="margin-left: 10px;width: 90px;" size="small" type="success" :loading="uploadLoading"
                        @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importDialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
    import MyContainer from '@/components/my-container'
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import goodschoice from "@/views/base/goods/goods.vue";
    import { formatWarehouse } from "@/utils/tools";
    import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
    import { addAndUpdateGoodsFinishPart, pageGoodsFinishedPart, queryGoodsFinishedPart, deleteGoodsFinishPart, exportGoodsFinishedPart } from "@/api/inventory/machine"
    import { getAllWarehouse } from '@/api/inventory/warehouse'
    import { importGoodsFinishedPartAsync } from '@/api/inventory/importMachine'
    const tableCols = [
        //{istrue:true,prop:'groupId',label:'半成品编码', width:'150'},
        { istrue: true, prop: 'partGoodsCodes', label: '半成品编码', width: '150' },
        { istrue: true, prop: 'partGoodsNames', label: '半成品名称', width: 'auto' },
        { istrue: true, prop: 'finishedGoodsCodes', label: '成品编码', width: '150' },
        { istrue: true, prop: 'finishedGoodsNames', label: '成品名称', width: 'auto' },
        { istrue: true, prop: 'groupIdOperName', label: '运营组', width: '90' },
        { istrue: true, prop: 'warehouse', label: '仓库', width: '90', formatter: (row) => row.warehouseName },
        { istrue: true, prop: 'createdUserName', label: '创建人', sortable: 'custom', width: '70' },
        { istrue: true, prop: 'createdTime', label: '创建时间', sortable: 'custom', width: '145' },
        { istrue: true, prop: 'modifiedUserName', label: '编辑人', width: '70' },
        { istrue: true, prop: 'modifiedTime', label: '编辑时间', width: '150' },
        {
            istrue: true, type: 'button', width: '100', label: '操作', btnList: [
                { label: "编辑", display: (row) => { return false; }, handle: (that, row) => that.onEdit(row.groupId) },
                { label: "删除", display: (row) => { return false; }, handle: (that, row) => that.onDelete(row.groupId) }]
        },
    ];
    const tableHandles1 = [
        { label: "导入", handle: (that) => that.startImport() },
        { label: "下载导入模版", handle: (that) => that.downloadTemplate() },
        { label: "新增", handle: (that) => that.onAdd() },
        { label: "导出", type: "primary", handle: (that) => that.onExport() }
    ];
    export default {
        name: 'machine',
        components: { cesTable, MyContainer, MyConfirmButton, goodschoice },
        data () {
            return {
                that: this,
                filter: {
                    type: null,
                    goodsCode: null,
                    goodsName: null,
                    groupIds: null,
                    modifiedUserName: null,
                    createdUserName: null,
                    keywords:null,
                    warehouses: null,
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "GroupId", IsAsc: false },
                tableCols: tableCols,
                tableHandles: tableHandles1,
                platformList: [],
                grouplist: [],
                warehouselist: [],
                warehouses: [],
                groupId: [],
                entity: { groupId: null, warehouse: null, goodsPart: [], goodsFinished: [] },
                importDialogVisible: false,
                uploadLoading: false,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                fileList: [],
                yesnoList: [
                    { value: true, label: "是" },
                    { value: false, label: "否" }
                ],
                thisTitle: "新增",
                isAdd: false,
                dialogFormVisible: false,
                goodschoiceVisible: false,
                type: 0,
            }
        },
        async mounted () {
            await this.init();
            await this.getlist();
        },
        methods: {
            async init() {
                var res1 = await getDirectorGroupList();
                this.grouplist = res1.data.map(item => {
                    return { value: item.key, label: item.value };
                });
                var res3 = await getAllWarehouse();
                var warehouselist1 = res3.data.filter((x) => x.name.indexOf('代发') < 0);
                this.warehouselist = warehouselist1;
            },
            //获取查询条件
            getCondition () {
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                this.filter.warehouses = this.warehouses.join();
                this.filter.groupIds = this.groupId.join();
                const params = { ...pager, ...page, ... this.filter }
                return params;
            },
            //查询第一页
            async onSearch () {
                this.$refs.pager.setPage(1)
                await this.getlist()
            },
            //分页查询
            async getlist () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true;
                var res = await pageGoodsFinishedPart(params);
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                data.forEach(d => {
                    d._loading = false
                })
                this.list = data
                this.listLoading = false;
            },
            //排序查询
            async sortchange (column) {
                if (!column.order)
                    this.pager = {};
                else {
                    var orderBy = column.prop;
                    this.pager = { OrderBy: orderBy, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selsChange: function (sels) {
                this.sels = sels
            },
            async onAdd () {
                this.dialogFormVisible = true;
                this.isAdd = true;
                this.thisTitle = "新增";
                this.entity = { groupId: null, goodsPart: [], goodsFinished: [] }
            },
            async onEdit (groupId) {
                this.dialogFormVisible = true;
                this.isAdd = false;
                this.thisTitle = "编辑";
                var res = await queryGoodsFinishedPart(groupId);
                if (!res?.success) return
                this.entity.groupId = groupId;
                this.entity = res.data
            },
            async onDelete (groupId) {
                var res = await deleteGoodsFinishPart(groupId);
                if (res?.success) {
                    this.$message({ message: '删除成功', type: "success" })
                    await this.getlist();
                }
                else {
                    this.$message({ message: '删除失败', type: "warning" })
                }
            },
            onShowChoice (type) {
                this.type = type;
                this.goodschoiceVisible = true;
                this.$nextTick(() => {
                    this.$refs.goodschoice.onShowChoice();
                });
            },
            async ondeleteChoice (type, goodsCode) {
                if (type == 0) {
                    var index = this.entity.goodsPart.findIndex(function (value, index, arr) { return value.goodsCode == goodsCode; })
                    this.entity.goodsPart.splice(index, 1);
                }
                else if (type == 1) {
                    var index = this.entity.goodsFinished.findIndex(function (value, index, arr) { return value.goodsCode == goodsCode; })
                    this.entity.goodsFinished.splice(index, 1);
                }
            },
            async onQueren () {
                var choicelist = await this.$refs.goodschoice.getchoicelist();             
                if (choicelist) {
                    if (this.type == 1) {
                        if (choicelist.length > 1) {
                            this.$message({ message: '成品编码数据只允许选一个', type: "warning" }); 
                            this.entity.goodsFinished = [];
                            return;
                        }
                    }
                    choicelist.forEach(f => {
                        if (this.entity.goodsPart.findIndex(function (value, index, arr) { return value.goodsCode == f.goodsCode; }) > -1
                            || this.entity.goodsFinished.findIndex(function (value, index, arr) { return value.goodsCode == f.goodsCode; }) > -1) {
                            return
                        }
                        if (this.type == 0) {
                            f.type = 0;
                            this.entity.goodsPart.push(f);
                        }
                        else if (this.type == 1) {
                            f.type = 1;
                            this.entity.goodsFinished.push(f);
                        }
                    })
                }               
                this.goodschoiceVisible = false
            },
            async onSave () {
                if (!this.entity) { this.$message({ message: '没有数据', type: "warning" }); return }
                else if (this.entity.warehouse == null) { this.$message({ message: '请选择仓库', type: "warning" }); return }
                else if (this.entity.goodsPart != null && this.entity.goodsPart.length <= 0) { this.$message({ message: '没有半成品数据', type: "warning" }); return }
                else if (this.entity.goodsFinished != null && this.entity.goodsFinished.length <= 0) { this.$message({ message: '没有成品数据', type: "warning" }); return }
                else if (this.entity.goodsFinished.length > 1) { this.$message({ message: '成品编码数据只允许选一个', type: "warning" }); return } 
                var param = { groupId: this.entity.groupId, warehouse: this.entity.warehouse, list: [] }
                this.entity.goodsPart.forEach(f => param.list.push({ groupId: f.groupId, type: f.type, goodsCode: f.goodsCode, goodsName: f.goodsName, qty: f.qty }))
                this.entity.goodsFinished.forEach(f => param.list.push({ groupId: f.groupId, type: f.type, goodsCode: f.goodsCode, goodsName: f.goodsName, qty: f.qty }))
                if (param.list.length == 0) {
                    this.$message({ message: '保存失败，请选择产品！', type: "warning" })
                    return;
                }
                param.list.forEach(f => {
                    if (f.qty == 0) {
                        this.$message({ message: '温馨提示，数量不可以为0！', type: "warning" })
                        throw('');
                    }
                })
                var res = await addAndUpdateGoodsFinishPart(param)
                if (res?.success) {
                    this.dialogFormVisible = false
                    this.$message({ message: '保存成功', type: "success" })
                    await this.getlist()
                }
                else {}
                    //this.$message({ message: '保存失败', type: "warning" })
            },
            startImport () {
                this.onUploadRemove();
                this.importDialogVisible = true;
            },
            beforeRemove () {
                return false;
            },
            //上传成功
            onUploadSuccess (response, file, fileList) {
                if (response.code == 200) {
                } else {
                    fileList.splice(fileList.indexOf(file), 1);
                }
            },
            onSubmitUpload (item) {
                if (this.fileList.length == 0) {
                    this.$message({ message: "请先上传文件", type: "warning" });
                    return false;
                }
                this.$refs.upload.submit();
            },
            onUploadChange(file, fileList) {
                this.fileList = fileList;
            },
            //上传文件
            onUploadRemove(file, fileList) {
                this.fileList = []
            },
            async uploadFile (item) {
                this.uploadLoading = true;
                const form = new FormData();
                // form.append("token", this.token);
                form.append("upfile", item.file);
                const res = await importGoodsFinishedPartAsync(form);
                if (res?.success) {
                    this.$message({ message: '上传成功,正在导入中...', type: "success" });
                    this.importDialogVisible = false;
                    await this.getlist();
                }
                else {
                    this.$message({ message: res.message, type: "warning", });
                }
                this.uploadLoading = false;
            },
            //下载导入模版
            downloadTemplate(){
                window.open("../../../static/excel/inventory/成品半成品导入模版.xlsx","_self");
            },
            async onExport() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.listLoading = true;
                var res = await exportGoodsFinishedPart(params);
                this.listLoading = false;
            }
        }
    }
</script>
<style scoped>
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }

    ::v-deep .el-select__tags-text {
        max-width: 30px;
    }
</style>
