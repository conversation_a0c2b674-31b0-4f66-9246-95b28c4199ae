<template>
    <container v-loading="pageLoading">
        <template #header>
        </template>

        <ces-table :tablekey="'finishedpartdetail202305091124'" ref="tableCols" :hasexpandRight='true' :showsummary='true'
            :that='that' :isIndex='true' :tablefixed='true' :hasexpand='false' :isSelectColumn="false" :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' @sortchange="sortchange" :loading="listLoading"   :border="true">
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

    </container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import container from "@/components/my-container";
import { getGoodsFinishedPartAutoLogDetailAsync } from "@/api/inventory/machine"

const tableCols = [
    { istrue: true, prop: 'finishedProductCode', label: '半成品编码', width: '120', },
    { istrue: true, prop: 'finishedProductName', label: '半成品名称', width: 'auto', },
    { istrue: true, prop: 'totalDispatchQuantity', label: '半成品数量', width: 'auto', },
    { istrue: true, prop: 'sellStock', label: '库存', width: 'auto', tipmesg: '库存数为开单提交时间的计划采购建议的实时库存', },
];
const tableHandles = [
    //{ label: "导入", handle: (that) => that.startImport() },
];



export default {
    name: 'YunHanAdminfinishedpart',
    components: { container, cesTable },

    data() {
        return {
            that: this,
            filter: {
                packagesProcessingId: null,
                goodsCode: null,
                goodsName: null,
                keywords: null,
                createdUserName: null,
                brandCode: null,
                job_Position: null,
                companyType: null,
                taskStatus: null,
                checkList: [],
            },
            list: [],
            summaryarry: {},
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "goodsCode", IsAsc: false },
            total: 0,
            sels: [],
            brandList: [],
            jobPositionList: [],
            chooseTags: [],
            selids: [],
            activeName: 'first',
            listLoading: false,
            pageLoading: false,
        };
    },

    async mounted() {
        //await this.onSearch();
    },

    methods: {
        async loadData({ selRows }){
            this.filter.packagesProcessingId = selRows.packagesProcessingId;
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist();
        },
        //分页查询
        async getlist() {
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            this.listLoading = true

            var res = await getGoodsFinishedPartAutoLogDetailAsync(params);
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data?.summary;
            this.list = data
        },
        async callbackGoodsCode(val) {
            this.filter.goodsCode = val;
            //this.onSearch();
        },

        async cellclick(row, column, cell, event) {
            if (column.property == 'indexNo') {

            }
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>


</style>
