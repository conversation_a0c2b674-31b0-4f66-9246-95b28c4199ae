<template>
  <div style="height: 100%;">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="90px" class="demo-ruleForm">
      <el-row style="margin-bottom: 10px;">
        <div>
          <el-button type="text" @click="addRow">新增一行</el-button>
        </div>
        <el-table ref="dataTable" :data="ruleForm.orderDtls" style="width: 100%" :border="true" height="200">
          <el-table-column prop="orderNo" label="订单编号" width="auto">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.orderNo" placeholder="订单编号" maxlength="50" clearable class="formCss"
                @blur="getOrder(scope.$index)" />
            </template>
          </el-table-column>
          <el-table-column prop="goodsName" label="商品名称" width="auto">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.goodsName" placeholder="商品名称" maxlength="50" clearable
                class="formCss" />
            </template>
          </el-table-column>
          <el-table-column prop="goodsCount" label="商品数量" width="80">
            <template slot-scope="scope">
              <el-input-number v-model.trim="scope.row.goodsCount" :min="0" :max="9999999" :controls="false"
                :precision="0" placeholder="商品数量" class="formCss">
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="spec" label="规格" width="100">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.spec" placeholder="规格" maxlength="50" clearable class="formCss" />
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80">
            <template slot-scope="scope">
              <el-input v-model.trim="scope.row.unit" placeholder="单位" maxlength="50" clearable class="formCss" />
            </template>
          </el-table-column>
          <el-table-column prop="actualInvoicedAmount" label="实际开票金额" width="120">
            <template slot-scope="scope">
              <el-input-number v-model.trim="scope.row.actualInvoicedAmount" :min="0" :max="9999999" :controls="false"
                :precision="2" placeholder="实际开票金额" class="formCss">
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleDelete(scope.$index)"
                :disabled="ruleForm.orderDtls.length == 1 && scope.$index == 0">
                <span style="color: red;">删除</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-input v-model="textarea" style="width: 100%;margin-bottom: 5px;" :rows="3" resize="none" type="textarea"
          @change="matchStr"
          placeholder="智能粘贴,目前只能匹配公司/个人、抬头、税号、开户行、账号、地址、电话、客户邮箱、店铺名字,例如输入公司/个人:个人,就会匹配冒号后面的值,不区分中英文冒号,每个信息请用换行分开,否则无法匹配。" />
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="公司/个人" prop="companyOrPersonal">
            <el-select v-model="ruleForm.companyOrPersonal" placeholder="请选择公司/个人" class="formCss" clearable filterable>
              <el-option :key="'公司'" label="公司" :value="'公司'" />
              <el-option :key="'个人'" label="个人" :value="'个人'" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="抬头" prop="invoiceHeader">
            <el-input v-model.trim="ruleForm.invoiceHeader" placeholder="请输入发票抬头" maxlength="50" clearable
              class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="税号" :prop="ruleForm.companyOrPersonal === '公司' ? 'taxID' : ''">
            <el-input v-model.trim="ruleForm.taxID" placeholder="请输入税号" maxlength="50" clearable class="formCss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="开户行">
            <el-input v-model.trim="ruleForm.openingBank" placeholder="请输入开户行" maxlength="50" clearable
              class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="账号">
            <el-input v-model.trim="ruleForm.accountNumber" placeholder="请输入账号" maxlength="50" clearable
              class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地址">
            <el-input v-model.trim="ruleForm.address" placeholder="请输入地址" maxlength="50" clearable class="formCss" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="电话">
            <el-input v-model.trim="ruleForm.mobile" placeholder="请输入电话" maxlength="50" clearable class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户邮箱">
            <el-input v-model.trim="ruleForm.customerEmail" placeholder="请输入客户邮箱" maxlength="50" clearable
              class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" prop="dataType">
            <el-select v-model="ruleForm.dataType" placeholder="请选择类型" class="formCss" clearable filterable>
              <el-option key="普通发票" label="普通发票" value="普通发票" />
              <el-option key="增值税专用发票" label="增值税专用发票" value="增值税专用发票" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="店铺名称" prop="shopName">
            <el-select v-model="ruleForm.shopName" filterable remote reserve-keyword placeholder="请模糊输入并选择店铺名称"
              clearable :remote-method="remoteMethod" class="formCss" @change="handleShopChange">
              <el-option v-for="item in shopOptions" :key="item.value" :label="item.label" :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="店铺编码">
            <el-input v-model.trim="ruleForm.shopCode" placeholder="请输入店铺编码" maxlength="50" clearable disabled
              class="formCss" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开票类型" prop="invoiceType">
            <el-select v-model="ruleForm.invoiceType" placeholder="请选择发票类型" class="formCss" filterable>
              <el-option key="常规发票" label="常规发票" value="常规发票" />
              <el-option key="红冲发票" label="红冲发票" value="红冲发票" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="加急">
            <el-select v-model="ruleForm.isUrgent" placeholder="是否加急" class="formCss" :clearable="false" filterable>
              <el-option key="是" label="是" :value="'是'" />
              <el-option key="否" label="否" :value="'否'" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="备注">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入备注" v-model="ruleForm.remark"
            class="formCss" maxlength="100" show-word-limit>
          </el-input>
        </el-form-item>
      </el-row>
    </el-form>
    <div style="display: flex; justify-content: center;align-items: center;margin-top: 10px;">
      <el-button @click="cancelForm">取消</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import { AllDDDeptTreeNcWh } from '@/api/profit/personnel'
import { getGoodsDocCgProvidersDto } from "@/api/inventory/basicgoods"
import { saveAfterSalesInvoiceManageCustomer, nullifyAfterSalesInvoiceManageData, verifyAfterSalesInvoiceManageOrder } from '@/api/customerservice/afterSalesInvoiceManage'
import { getList as getShopList } from "@/api/operatemanage/base/shop"
import { GetOrderInfo_Goods } from "@/api/customerext/orderInfo";
import dayjs from 'dayjs'
export default {
  name: "invoiceRegistration",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    editData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      that: this,
      textarea: '',
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      rules: {
        orderNo: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
        goodsName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        goodsCount: [{ required: true, message: '请输入商品数量', trigger: 'blur' }],
        actualInvoicedAmount: [{ required: true, message: '请输入实际开票金额', trigger: 'blur' }],
        companyOrPersonal: [{ required: true, message: '请输入公司/个人', trigger: 'blur' }],
        invoiceHeader: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
        taxID: [{ required: true, message: '请输入税号', trigger: 'blur' }],
        openingBank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        accountNumber: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        shopName: [{ required: true, message: '请选择店铺名称', trigger: 'blur' }],
        dataType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        invoiceType: [{ required: true, message: '请选择开票类型', trigger: 'blur' }],
      },
      ruleForm: {
        orderNo: null,
        goodsName: null,
        goodsCount: null,
        actualInvoicedAmount: null,
        companyOrPersonal: null,
        invoiceHeader: null,
        taxID: null,
        openingBank: null,
        accountNumber: null,
        address: null,
        mobile: null,
        customerEmail: null,
        dataType: null,
        invoiceType: '常规发票',
        remark: null,
        orderDtls: [],
        isUrgent: '否',
        shopName: null,
        shopCode: null,
      },
      shopOptions: [],
      timeRanges: [],
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      timing: null,//定时器
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.$refs.ruleForm.clearValidate();
      this.$refs.ruleForm.resetFields();

      const a = JSON.parse(JSON.stringify(this.editData || {})); // 保证是对象

      // 用默认值结构和 editData 合并，避免因空对象覆盖响应式结构
      const defaultForm = {
        orderNo: null,
        goodsName: null,
        goodsCount: null,
        actualInvoicedAmount: null,
        companyOrPersonal: null,
        invoiceHeader: null,
        taxID: null,
        openingBank: null,
        accountNumber: null,
        address: null,
        mobile: null,
        customerEmail: null,
        dataType: null,
        invoiceType: '常规发票',
        remark: null,
        isUrgent: '否',
        shopName: null,
        shopCode: null,
        orderDtls: [{
          orderNo: null,
          goodsName: null,
          goodsCount: null,
          spec: null,
          unit: null,
          actualInvoicedAmount: null,
        }]
      };

      // 用 Object.assign 或展开语法合并，不替换响应式对象
      Object.assign(this.ruleForm, { ...defaultForm, ...a });

      // 特殊处理 orderDtls（避免被空值清空为 []）
      if (!Array.isArray(a.orderDtls) || a.orderDtls.length === 0) {
        this.ruleForm.orderDtls = [{
          orderNo: null,
          goodsName: null,
          goodsCount: null,
          spec: null,
          unit: null,
          actualInvoicedAmount: null,
        }];
      }

      this.onRenewal();
    });
  },
  methods: {
    async getOrder(index) {
      let row = this.ruleForm.orderDtls[index];
      if (!row.orderNo) return
      const { data } = await GetOrderInfo_Goods({ orderNo: row.orderNo });
      const map = {
        goodsName: 'styleCode',
        goodsCount: 'goodsQty',
        actualInvoicedAmount: 'payAmount',
        unit: 'unit',
        shopName: 'shopName',
      }
      if (data && data.length > 0) {
        const styleCodeList = [...new Set(data.map((item) => item.styleCode))];
        styleCodeList.forEach((code, i) => {
          const res = data.filter((item) => item.styleCode === code)
          const qty = res.reduce((sum, item) => sum + item.goodsQty, 0)
          console.log('res', res, 'qty', qty);
          Object.keys(map).forEach((key) => {
            if (key === 'goodsCount' && (row.goodsCount === null || row.goodsCount === undefined || row.goodsCount === '')) {
              this.$set(row, 'goodsCount', qty);
            } else if (data[0][map[key]] !== undefined && (row[key] === null || row[key] === undefined || row[key] === '')) {
              this.$set(row, key, data[0][map[key]]);
            }
          });
          if (styleCodeList.length > 1 && i != 0) {
            this.ruleForm.orderDtls.push({
              orderNo: data[0].orderNo,
              goodsCount: qty,
              goodsName: res[0].styleCode,
              actualInvoicedAmount: res[0].payAmount,
              unit: res[0].unit,
            })
          }
        })
      }
    },
    matchStr(str) {
      const lines = str.split(/[\r\n]+/).map((item) => {
        return {
          label: item.split(/[:：]/)[0].trim(),
          value: item.split(/[:：]/)[1] ? item.split(/[:：]/)[1].trim() : ''
        }
      })
      const map = {
        '公司/个人': 'companyOrPersonal',
        '抬头': 'invoiceHeader',
        '税号': 'taxID',
        '开户行': 'openingBank',
        '账号': 'accountNumber',
        '地址': 'address',
        '电话': 'mobile',
        '客户邮箱': 'customerEmail',
        '店铺名称': 'shopName',
        '类型': 'dataType',
      }
      lines.forEach((item => {
        if (map[item.label] && item.value !== '无') {
          this.$set(this.ruleForm, map[item.label], item.value);
        }
      }))
      const hasDataType = lines.some(item => item.label === '类型');
      if (!hasDataType) {
        this.$set(this.ruleForm, 'dataType', '普通发票');
      }
    },
    handleShopChange(val) {
      this.ruleForm.shopCode = this.shopOptions.find(item => item.label === val).value
    },
    handleDelete(index) {
      this.ruleForm.orderDtls.splice(index, 1)
    },
    addRow() {
      const hasEmptyOrderNo = this.ruleForm.orderDtls.some(row => !row.orderNo);
      if (hasEmptyOrderNo) {
        this.$message.error('请填写完整信息后再新增');
        return;
      }
      this.ruleForm.orderDtls.push({
        orderNo: null,
        goodsName: null,
        goodsCount: undefined,
        spec: undefined,
        unit: null,
        actualInvoicedAmount: undefined,
      })
      this.onRenewal()
    },
    onRenewal() {
      this.$nextTick(() => {
        if (this.$refs.dataTable) {
          this.$refs.dataTable.doLayout();
        }
      });
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.shopOptions = [];
        setTimeout(async () => {
          const params = {
            pageSize: 20,
            currentPage: 1,
            shopName: query
          }
          const { data, success } = await getShopList(params);
          if (success) {
            data.list.forEach(f => {
              this.shopOptions.push({ value: f.shopCode, label: f.shopName })
            });
          } else {
            this.$message.error('获取店铺列表失败')
          }
        }, 200)
      }
      else {
        this.shopOptions = []
      }
    },
    cancelForm() {
      this.$emit('close')
    },
    async preservationMethod() {
      const { data, success } = await saveAfterSalesInvoiceManageCustomer(this.ruleForm)
      if (success) {
        this.$message.success('提交成功')
        this.$emit('cancelFormMethod')
      }
    },
    async submit() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if (this.ruleForm.companyOrPersonal === '公司' && (!this.ruleForm.taxID)) {
            this.$message.error('请输入完整信息')
            return
          }
          if (this.ruleForm.orderDtls.length == 0) {
            this.$message.error('请最少输入一行订单信息')
            return
          }
          const hasEmptyUnit = this.ruleForm.orderDtls.some(row => !row.unit);
          if (hasEmptyUnit) {
            this.$message.error('请填写所有商品的单位')
            return
          }
          const params = {
            ...this.ruleForm,
            orderList: this.ruleForm.orderDtls.map(item => { return { orderNo: item.orderNo, actualInvoicedAmount: item.actualInvoicedAmount } }),
          }
          const { data, success, msg } = await verifyAfterSalesInvoiceManageOrder(params)
          if (success) {
            this.preservationMethod()
          } else {
            setTimeout(() => {
              this.$confirm(msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.preservationMethod()
              }).catch(() => {
              });
            }, 1500)
          }
        } else {
          // this.$message.error('请输入完整信息')
        }
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await pageGetVoOrder(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
}

.formCss {
  width: 100%;
}

.demo-ruleForm ::v-deep .el-input__inner {
  text-align: left;
}
</style>
