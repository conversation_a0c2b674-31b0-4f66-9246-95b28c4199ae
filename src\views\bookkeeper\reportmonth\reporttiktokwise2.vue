<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                    value-format="yyyyMM" placeholder="月份"></el-date-picker>
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <!-- <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                    <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                        :value="item.shopCode"></el-option>
                </el-select> -->
                <el-input v-model.trim="filter.shopName" placeholder="店铺名称" style="width:160px;" maxlength="40" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.proCode" placeholder="商品ID" style="width:120px;" maxlength="40" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.businessName" placeholder="商务" style="width:120px;" maxlength="20" />
            </el-button>

            <el-button style="padding: 0;margin: 0;border: 0;">
                <el-input v-model.trim="filter.wiseManName" placeholder="达人" style="width:120px;" maxlength="20" />
            </el-button>

            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onExport" :loading="onExportLoading">导出</el-button>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='list'
            :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { formatWarehouse, formatTime, formatYesornoBool, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { Loading } from 'element-ui';
import { GetMonthBusinessReport, ExportMonthBusinessReport } from '@/api/monthbookkeeper/financialreport'
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '80' },
    { istrue: true, fixed: true, prop: 'groupName', label: '小组', sortable: 'custom', width: '80' },
    { istrue: true, fixed: true, prop: 'businessName', label: '商务', sortable: 'custom', width: '80' },
    { istrue: true, fixed: true, prop: 'tichengType', label: '类型', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'countSale', label: '销售数量', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountSaleJeJing', label: '净销售额', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountCoustJeJing', label: '净销售成本', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'amountSaleGross', label: '销售毛利', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'performanceRate', label: '提成比例', sortable: 'custom', width: '120' },
    { istrue: true, prop: 'performanceAmount', label: '应发提成金额', sortable: 'custom', width: '120' },

];
export default {
    name: "reporttiktokwise2",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, },
    data() {
        return {
            that: this,
            filter: {
                yearMonth: null,
                platform: 6,
                shopCode: null,
                proCode: null,
                businessName: null,
                wiseManName: null,
            },
            list: [],
            shopList: [],
            userList: [],
            grouplist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: "performanceAmount", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            onExportLoading: false,

            fileList: [],
            importYearMonth: null,
            importLoading: false,
            importVisible: false,

            fileList2: [],
            fileListData2: [],
            importYearMonth2: null,
            importLoading2: false,
            importVisible2: false,
        };
    },
    async mounted() {
        //await this.onSearch()
        //await this.getShopList();
    },
    methods: {
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [6] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            var res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
        },
        getParams() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };

            return params;
        },
        async getList() {
            var that = this;
            const params = this.getParams();
            if (!params) {
                return false;
            }
            startLoading();
            const res = await GetMonthBusinessReport(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.list = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            const params = this.getParams();
            if (!params) {
                return false;
            }
            this.onExportLoading = true;
            var res = await ExportMonthBusinessReport(params);
            this.onExportLoading = false;
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音商务提成汇总报表_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },

    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}

.btnGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
