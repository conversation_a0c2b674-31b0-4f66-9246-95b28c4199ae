<template>
  <container v-loading="pageLoading"> 
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :isSelectColumn="false"
        :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading">
    </ces-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
    </template>    
  </container>
</template>

<script>
import {pagePurchasePlan2Record} from '@/api/inventory/purchase'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import { formatTime,formatSecondToHour,formatmoney} from "@/utils/tools";
const tableCols =[
      {istrue:true,prop:'createdTime',label:'日期', width:'130',formatter:(row)=>formatTime(row.createdTime,'YYYY-MM-DD')},
      {istrue:true,prop:'createdUserName',label:'编辑人', width:'80'},
      {istrue:true,prop:'goodsCode',label:'商品编码', width:'100'},
      {istrue:true,prop:'minimumStockDays',label:'最低库存天数', width:'110'},
      {istrue:true,prop:'planStockDays',label:'建议库存天数', width:'110'}
     ];
const tableHandles1=[ ];
export default {
  name: 'Roles',
  components: {cesTable, container },
  props:{
    // filter:{goodsCode:''},
  },
  data() {
    return {
      that:this,
      filter:{goodsCode:''},
      list: [],
      pager:{OrderBy:"createdTime",IsAsc:false},
      tableCols:tableCols,
      tableHandles:tableHandles1,
      platFormList:[],
      total: 0,
      sels: [], 
      listLoading: false,
      pageLoading: false,
    }
  },
//  watch:{
//     filter:function(val,oldval){
//          this.$nextTick(function(){
//             this.onSearch( );
//        })
//     }
//   },
  async mounted() {
    this.getlist();
  },
  beforeUpdate() {
    console.log('update')
  },
  methods: {
     async onShow(goodsCode) {
       this.filter.goodsCode=goodsCode;
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async onSearch() {      
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async getlist() {
      var pager = this.$refs.pager.getPager()
      const params = {...pager,...this.pager,...this.filter}
      this.listLoading = true
      console.log('params',params)
      const res = await pagePurchasePlan2Record(params)
      this.listLoading = false
      if (!res?.success)return    
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
  async sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
    selsChange: function(sels) {
      this.sels = sels
    }
  }
}
</script>
