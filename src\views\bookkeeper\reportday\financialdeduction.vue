<template>
    <container>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tablekey='tablekey' @summaryClick='onsummaryClick' :tableHandles='tableHandles' :showsummary='true'
            :summaryarry='summaryarry' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;">
                        <el-select v-model="filter1.deductionType" placeholder="类型" style="width: 110px">
                            <el-option label="全部" value />
                            <el-option v-for="item in deductionTypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                            <!-- <el-option label="保险承保" :value="10" />
                            <el-option label="村淘平台服务费" :value="11" />
                            <el-option label="代扣返点积分" :value="12" />
                            <el-option label="代扣交易退回积分" :value="13" />
                            <el-option label="公益捐款" :value="14" />
                            <el-option label="花呗服务费" :value="15" />
                            <el-option label="淘宝现金红包" :value="16" />
                            <el-option label="天猫佣金" :value="17" />
                            <el-option label="信用卡服务费" :value="18" />
                            <el-option label="积分软件服务费" :value="19" />
                            <el-option label="服务费" :value="20" />
                            <el-option label="保证金退款" :value="21" />
                            <el-option label="品牌新享-首单拉新计划" :value="22" />
                            <el-option label="村淘导购" :value="23" />
                            <el-option label="省钱月卡" :value="24" />
                            <el-option label="淘宝特价版联合营销计划系统服务费" :value="25" />
                            <el-option label="售后支付/赔付" :value="26" />
                            <el-option label="极速回款担保服务费" :value="27" />
                            <el-option label="网商银行提前收款扣款" :value="28" />
                            <el-option label="直营&联营&营促销" :value="29" />
                            <el-option label="C2M退货包运费代扣" :value="30" />
                            <el-option label="保险返还" :value="31" />
                            <el-option label="营销扣款" :value="32" />
                            <el-option label="品牌新享新品直降礼金软件服务费" :value="33" />
                            <el-option label="每日必买" :value="34" />
                            <el-option label="海外零售计划供应链管理服务费" :value="35" /> -->
                        </el-select>
                    </el-button>
                    <el-button type="primary" @click="getlist">刷新</el-button>
                    <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <!-- 系列编码趋势图 -->
        <el-dialog :title="buscharDialog.title" :visible.sync="buscharDialog.visible" width="80%">
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
import { getFinacialDeductionPage, getDeductionTypeDic, exportFinacialDeductionRpt } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import cesTable from "@/components/Table/table.vue";
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺名', sortable: 'custom', width: '160', formatter: (row) => { return row.shopName } },
    { istrue: true, prop: 'timeOccur', label: '发生时间', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'serialNumberFinacial', label: '财务流水号', sortable: 'custom', width: '170' },
    { istrue: true, prop: 'originOrderNo', label: '订单编号', sortable: 'custom', width: '170' },
    {
        istrue: true, prop: 'deductionType', label: '业务类型', sortable: 'custom', width: '90', formatter: (row) => { return row.deductionTypeName }
    },
    { istrue: true, summaryEvent: true, prop: 'amountIncome', label: '收入金额', sortable: 'custom', width: '90' },
    { istrue: true, summaryEvent: true, prop: 'amountPaid', label: '支出金额', sortable: 'custom', width: '90' },
];
const tableHandles = [
    // {label:"刷新", handle:(that)=>that.getlist()},
];
export default {
    name: 'Roles',
    components: { cesTable, container,buschar },
    props: {
        filter: {},
        tablekey: { type: String, default: '' },
    },
    data() {
        return {
            that: this,
            filter1: { deductionType: null },
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: " TimeOccur ", IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            selids: [],
            deductionTypeList: [],
            isExport: false,
            listLoading: false,
            pageLoading: false,
            analysisFilter: {
                searchName: "tb_finacialdeduction_zfb",
                isYearMonthDay: true,
                isTimeFormat: true,
                extype: 5,
                selectColumn: "amountIncome",
                filterTime: "timeOccur",
                filter: null,
                columnList: [],
            },
            buscharDialog: { visible: false, title: "", data: [] },
        }
    },
    mounted() {
        this.getDeductionTypeList();
    },
    beforeUpdate() { },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter, ... this.filter1 }
            this.listLoading = true
            const res = await getFinacialDeductionPage(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res?.data?.total
            const data = res?.data?.list
            data.forEach(d => {
                d.deductionTypeName = this.deductionTypeList.find(t => t.value === d.deductionType)?.label ?? "";
                d._loading = false
            })
            this.list = data
            this.summaryarry = res.data?.summary;
        },
        async getDeductionTypeList() {
            const res = await getDeductionTypeDic();
            if (!res?.success) return
            this.deductionTypeList = res?.data;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        selsChange: function (sels) {
            this.sels = sels
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onsummaryClick(property) {
            this.analysisFilter.columnList = [];
            this.analysisFilter.filter = null;
            let that = this;
            let summaryEventList = this.tableCols.filter(f => f.summaryEvent);
            summaryEventList.forEach(element => {
                this.analysisFilter.columnList.push({ columnNameCN: element.label, columnNameEN: element.prop });
            });


            this.analysisFilter.filter = {
                serialNumberFinacial: [this.filter.serialNumberFinacial, 0],
                originOrderNo: [this.filter.originOrderNo, 0],
                shopCode: [this.filter.shopCode, 0],
                deductionType: [this.filter1.deductionType, 0]
            }
            if (this.filter.timerange) {
                this.analysisFilter.filter.timeOccur = [this.filter.timerange[0], this.filter.timerange[1], 0];
            }
            this.analysisFilter.selectColumn = property;
            const res = await getAnalysisCommonResponse(that.analysisFilter).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        },
        //导出
        async exportProps() {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter, ... this.filter1 }
            this.isExport = true
            let res = await exportFinacialDeductionRpt(params);
            this.isExport = false
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '支付宝账单-订单扣点_' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
    }
}
</script>
