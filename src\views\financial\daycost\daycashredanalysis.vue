<template>
  <div>
    <!-- <div>
      <el-button type="primary" @click="onSearch">查询</el-button>
    </div> -->
    <!-- <div id="daycashredanalysisline" style="width: 100%;height: 400px; box-sizing:border-box; line-height: 200px;"/> 
    <div id="daycashredanalysispie" style="width: 100%;height: 100%; box-sizing:border-box; line-height: 200px;"/>   -->
    <el-row>
      <el-col :span="6">
        <div id="daycashredanalysispie" :style="{height:height,width:width}" /> 
      </el-col>
     <el-col :span="18">  
        <div id="daycashredanalysisline" :style="{height:height,width:width}" />
     </el-col>
    </el-row>
  </div>
</template>
<script>
import {getDayCashRedResonLevel1Analysis,getDayCashRedResonLevel2Analysis} from '@/api/financial/daycost'
import container from '@/components/my-container/noheader'
import buschar from '@/components/Bus/buschar'
import pieChart from '@/views/admin/homecomponents/PieChart'
import * as echarts from 'echarts';
import { title } from 'process';
export default {
  name: 'Roles',
  components: {container,buschar,pieChart},
  props:{
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
  },
  data() {
    return {
        parms:{},
        buscharData:{},
        righttext: '现金红包',
        myChart1: null,
        myChart2:null,
      }
  },
  async mounted () {
    window.addEventListener('resize', this.handleResizeChart);
    
  },
  destroyed () {
    window.removeEventListener('resize', this.handleResizeChart);
  },
  beforeUpdate() { },
  methods: {
    async onSearch() {
      await this.getAnalysis(this.parms)
    },
    async getAnalysis(parms) {
      this.parms=parms
      //console.log('loaddaycashredanalysis',parms)
      let res = await getDayCashRedResonLevel1Analysis(this.parms);
      //this.buscharData=res.data;
      await this.initchartsspie(res.data);
      await this.initchartsline(res.data);
    },
    async getResonLevel2Analysis(resonlevel1) {
      console.log(this)
      console.log(this.parms)
      this.parms.resonLevel1=resonlevel1
      let res = await getDayCashRedResonLevel2Analysis(this.parms);
      await this.initchartsline(res.data);
    },
    // initEcharts () {
    //   // 新建一个promise对象
    //   let newPromise = new Promise((resolve) => {
    //     resolve()
    //   })
    //   //然后异步执行echarts的初始化函数
    //   newPromise.then(() => {
    //     //	此dom为echarts图标展示dom
    //     echarts.init(DOm)
    //   })
    // },
    async initchartsspie(analysisData) {
      let that=this;
      this.$nextTick(() => {
        var chartDom1 = document.getElementById('daycashredanalysispie');
        this.myChart2 = echarts.init(chartDom1);
        this.myChart2.clear();
        var option1 = that.getoptionspie(analysisData);
        this.myChart2.setOption(option1);
        this.myChart2.off('click')
        this.myChart2.on('click',(a,b)=>{
          let level1=0;
          if(a.name=='仓库部原因') level1=0;
          else if(a.name=='运营部原因') level1=1;
          else if(a.name=='采购部原因') level1=2;
          else if(a.name=='产品问题') level1=3;
          else if(a.name=='公司承担') level1=4;
          else if(a.name=='快递员因') level1=5;
          else if(a.name=='厂家原因') level1=6;
          else if(a.name=='客服部原因') level1=7;
          else if(a.name=='其他原因') level1=8;
          this.righttext=a.name;
          that.getResonLevel2Analysis(level1)
        })
      });
    },
   async initchartsline(analysisData) {
      let that=this;
     this.$nextTick(() => {
        var chartDom1 = document.getElementById('daycashredanalysisline');
        this.myChart1 = echarts.init(chartDom1);
        this.myChart1.clear();
        var option1 = that.getoptionsline(analysisData);
        //console.log('option1',option1)
        this.myChart1.setOption(option1);
        //console.log('myChart1',myChart1)
      });
    },
    getoptionsline(element) {
    // 自定义格式化函数
    function formatAxisLabel(value) {
        if (value < 100) {
            return value.toFixed(2); // 保留两位小数
        } else {
            return value.toLocaleString(); // 使用千位符
        }
    }

    var series = [];
    element.series.forEach(s => {
        series.push({ smooth: true, ...s });
    });

    var yAxis = [];
    element.yAxis.forEach(s => {
        yAxis.push({
            type: 'value',
            minInterval: 10,
            offset: s.offset,
            splitLine: s.splitLine,
            position: s.position,
            name: s.name,
            axisLabel: {
                formatter: function (value) {
                    return formatAxisLabel(value) + (s.unit || '');
                }
            }
        });
    });

    var selectedLegend = {};
    if (element.selectedLegend) {
        element.legend.forEach(f => {
            if (!element.selectedLegend.includes(f)) selectedLegend[f] = false;
        });
    }

    var option = {
        title: { text: element.title },
        tooltip: { trigger: 'axis' },
        legend: {
            selected: selectedLegend,
            data: element.legend,
            bottom: '0%'
        },
        grid: {
            top: '10%',
            left: '5%',
            right: '4%',
            bottom: '10%',
            containLabel: false
        },
        toolbox: { feature: {} },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            },
            padding: [5, 10]
        },
        xAxis: {
            type: 'category',
            data: element.xAxis
        },
        yAxis: yAxis,
        series: series
    };

    return option;
},
 
getoptionspie(element) {
    // 自定义格式化函数
    function formatValue(value) {
        const isNegative = value < 0;
        const absValue = Math.abs(value);

        let formattedValue;

        if (absValue < 100) {
            // 百位以下保留两位小数
            formattedValue = absValue.toFixed(2);
        } else {
            // 百位以上使用千位分隔符且去掉小数点
            formattedValue = Math.floor(absValue).toLocaleString();
        }

        // 如果原始值为负数，则在格式化值前添加负号
        return isNegative ? `-${formattedValue}` : formattedValue;
    }

    var option = {
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const formattedValue = formatValue(params.value);
                return `${params.seriesName} <br/>${params.name} : ${formattedValue} (${params.percent}%)`;
            }
        },
        title: {
            left: 'center',
            text: '现金红包'
        },
        series: [
            {
                name: '红包各组原因占比',
                type: 'pie',
                radius: ['30%', '50%'],
                center: ['45%', '55%'],
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };

    // 格式化数据并填充到 option.series[0].data
    element.pieSeries.forEach(s => {
        option.series[0].data.push({
            value: s.value, // 保留原始值用于图表计算
            name: s.name,
            // 格式化后的值并不会在图表中直接使用，但在 tooltip 和 label 中使用
        });
    });

    return option;
},
//   getoptionspie(element) {
//     // 自定义格式化函数
//     function formatValue(value) {
//         if (value < 100) {
//             return value.toFixed(2); // 保留两位小数
//         } else {
//             return value.toLocaleString(); // 使用千位符
//         }
//     }

//     var option = {
//         tooltip: {
//             trigger: 'item',
//             formatter: '{a} <br/>{b} : {c} ({d}%)'
//         },
//         title: {
//             left: 'center',
//             text: '现金红包'
//         },
//         series: [
//             {
//                 name: '红包各组原因占比',
//                 type: 'pie',
//                 radius: ['30%', '50%'],
//                 center: ['45%', '55%'],
//                 itemStyle: {
//                     borderRadius: 10,
//                     borderColor: '#fff',
//                     borderWidth: 2
//                 },
//                 data: [
//                     // 示例数据
//                     // { value: 1048, name: 'Search Engine' },
//                     // { value: 735, name: 'Direct' }
//                 ],
//                 emphasis: {
//                     itemStyle: {
//                         shadowBlur: 10,
//                         shadowOffsetX: 0,
//                         shadowColor: 'rgba(0, 0, 0, 0.5)'
//                     }
//                 }
//             }
//         ]
//     };

//     // 格式化数据并填充到 option.series[0].data
//     element.pieSeries.forEach(s => {
//         option.series[0].data.push({
//             value: s.value, // 保留原始值用于图表计算
//             name: s.name,
//             formattedValue: formatValue(s.value) // 格式化后的值
//         });
//     });

//     return option;
// },    
// getoptionspie(element) {
//     // 自定义格式化函数
//     function formatValue(value) {
//         // 处理负值情况
//         const isNegative = value < 0;
//         const absValue = Math.abs(value);

//         let formattedValue;
//         if (absValue < 100) {
//             formattedValue = absValue.toFixed(2); // 保留两位小数
//         } else {
//             formattedValue = absValue.toLocaleString(); // 使用千位符
//         }

//         // 如果原始值为负数，则在格式化值前添加负号
//         return isNegative ? `-${formattedValue}` : formattedValue;
//     }

//     var option = {
//         tooltip: {
//             trigger: 'item',
//             formatter: function(params) {
//                 // 使用params.data中的formattedValue来显示格式化后的值
//                 return `${params.seriesName} <br/>${params.name} : ${params.data.formattedValue} (${params.percent}%)`;
//             }
//         },
//         title: {
//             left: 'center',
//             text: '现金红包'
//         },
//         label: {
//             show: true,
//             formatter: function(params) {
//                 // 使用params.data中的formattedValue来显示格式化后的值
//                 return `${params.name}:\n\r${params.data.formattedValue}`;
//             },
//         },
//         series: [
//             {
//                 name: '红包各组原因占比',
//                 type: 'pie',
//                 radius: ['30%', '50%'],
//                 center: ['45%', '55%'],
//                 itemStyle: {
//                     borderRadius: 10,
//                     borderColor: '#fff',
//                     borderWidth: 2
//                 },
//                 data: [
//                     // 示例数据
//                     // { value: 1048, name: 'Search Engine' },
//                     // { value: 735, name: 'Direct' }
//                 ],
//                 emphasis: {
//                     itemStyle: {
//                         shadowBlur: 10,
//                         shadowOffsetX: 0,
//                         shadowColor: 'rgba(0, 0, 0, 0.5)'
//                     }
//                 }
//             }
//         ]
//     };
//     // 格式化数据并填充到 option.series[0].data
//     element.pieSeries.forEach(s => {
//         option.series[0].data.push({
//             value: s.value, // 保留原始值用于图表计算
//             name: s.name,
//             formattedValue: formatValue(s.value) // 格式化后的值，将在tooltip和label中显示
//         });
//     });

//     return option;
// },
           handleResizeChart () {
            if (this.myChart1) {
                this.myChart1.resize();
          }
          if (this.myChart2) {
                    this.myChart2.resize();
                }
            }
  }
}
</script>
