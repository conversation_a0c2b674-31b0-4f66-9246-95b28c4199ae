<template>
    <my-container v-loading="pageLoading">
        <!-- 列表 -->
        <template #header>

            <el-button-group>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <el-input v-model="filter.masterOrderNumber" v-model.trim="filter.masterOrderNumber" placeholder="主订单编号(多个单号，隔开)" style="width:200px;" clearable></el-input>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <el-input v-model="filter.refundOrderNumber" v-model.trim="filter.refundOrderNumber" placeholder="退款单编号(多个单号，隔开)" style="width:200px;" clearable></el-input>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.refundType" placeholder="退款类型" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in refundTypeList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="orderPayTimeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
                    range-separator="至" start-placeholder="付款时间" end-placeholder="结束时间" :picker-options="pickerOptions" >
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px;">
                    <el-input v-model="filter.productID" v-model.trim="filter.productID" placeholder="商品ID(多个ID，隔开)" style="width:200px;" clearable></el-input>
                </el-button>
                <el-button style="padding: 0; margin: 0; margin-right: 10px; width:400px;">
                    买家退款金额
                    <el-input-number v-model="filter.buyerRefundAmountMin" style="margin-right: 10px;" :min=0 @change="handleMinChange"></el-input-number>
                    <el-input-number v-model="filter.buyerRefundAmountMax" style="margin-right: 10px;" :min=0 @change="handleMaxChange"></el-input-number>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-date-picker style="width: 320px" v-model="orderRefundTimeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
                    range-separator="至" start-placeholder="退款时间" end-placeholder="结束时间" :picker-options="pickerOptions" >
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.refundReason" placeholder="买家退款原因" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in refundReasonList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.shop" placeholder="店铺" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in shopList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.leader" placeholder="组长" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in leaderList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.commissioner" placeholder="专员" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in commissionerList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.assistant" placeholder="助理" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option v-for="item in assistantList" :key="item" :value="item" :label="item"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.logisticsJudgment" placeholder="物流判断" multiple clearable collapse-tags filterable style="width:200px;">
                        <el-option label="退回" value="退回"></el-option>
                        <el-option label="撤单" value="撤单"></el-option>
                        <el-option label="异常" value="异常"></el-option>
                        <el-option label="正常" value="正常"></el-option>
                        <el-option label="无物流信息" value="无物流信息"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.isVoucher" placeholder="是否有凭证" clearable style="width:200px;">
                        <el-option :key="'有凭证'" label="是" value="是"></el-option>
                        <el-option :key="'无凭证'" label="否" value="否"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-select v-model="filter.isRepresent" placeholder="是否申诉" clearable style="width:200px;">
                        <el-option :key="'申诉'" label="是" value="是"></el-option>
                        <el-option :key="'不申诉'" label="否" value="否"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0; margin-right: 10px;">
                    <el-input v-model="filter.representationPerson" placeholder="申诉人(多个单号，隔开)" style="width:200px;" clearable></el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
                <el-button style="padding: 0;margin: 0;">

                <el-switch v-model="switchOperate" active-text="仅退款" inactive-text="全部" 
                    inactive-color="#13ce66"  :width="40" @change="changeRefund">
                </el-switch>

                </el-button>
                <el-button type="primary" @click="onClaim">申领申诉</el-button>
                <el-button type="primary" @click="onWithdraw">撤销申领</el-button>
                <el-button type="primary" @click="ImportDistOrderData">退款管理数据导入</el-button>
            </el-button-group>

        </template>

        <vxetablebase :id="'refundData202408111325'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' 
            @sortchange='sortchange' :tableData='allRefundDataList' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" 
            :summaryarry="summaryarry" :showsummary='true' @select="selectchange" style="width: 100%;margin: 0" v-loading="listLoading" 
            :height="'100%'" >
        </vxetablebase>
        
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length"
            @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-dialog title="退款管理数据导入" :visible.sync="dialogVisible" width="40%" v-dialogDrag>
            <el-row>
                <el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18">
                    <el-upload ref="upload" :auto-upload="false" :multiple="false" action
                        accept=".xlsx" :http-request="uploadFile" :on-change="uploadChange" :on-remove="uploadRemove">
                        <template #trigger>
                            <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                            @click="submitupload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
                    </el-upload>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import cesTable from "@/components/Table/table.vue";
import { importTaoFactoryRefundData, getRefundData, exportRefundData, applyRefundDataRepresent, removeRefundDataApply, getRefundType, getRefundReason, 
        getShopNameInProductEntity, getLeaderNameInProductEntity, getCommissionerNameInProductEntity, getAssistantNameInProductEntity } from "@/api/operatemanage/refundData";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getUserInfo } from "@/api/operatemanage/productalllink/alllink";

const tableCols = [
    { sortable: 'custom', istrue: true, width: '60', align: 'center', type: "checkbox" },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '主订单编号', prop: 'masterOrderNumber' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款单编号', prop: 'refundOrderNumber' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款类型', prop: 'refundType' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '付款时间', prop: 'orderPayTime' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '商品ID', prop: 'productID' },
    { sortable: 'custom', istrue: true, width: '90', align: 'center', label: '宝贝标题', prop: 'proTitle' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '交易金额', prop: 'transactionAmount' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '买家退款金额', prop: 'buyerRefundAmount' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款占比', prop: 'refundPercentage', formatter:(row)=> (row.refundPercentage*100).toFixed(2)+"%" },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款申请时间', prop: 'orderRefundTime' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '退款原因', prop: 'refundReason' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '店铺', prop: 'shop' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '组长', prop: 'leader' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '专员', prop: 'commissioner' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '助理', prop: 'assistant' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '物流判断', prop: 'logisticsJudgment' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '是否有凭证', prop: 'isVoucher' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '是否申诉', prop: 'isRepresent' },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉倒计时', prop: 'representCountdown', 
        formatter:(row)=> {
            const now = new Date().getTime();
            const refundCompleteTime = new Date((row.representCountdown || '').replace(' ', 'T')).getTime(); // representCountdown 转换成退款完成时间的时间戳
            const countDownTime = (refundCompleteTime - now) / (24 * 60 * 60 * 1000); // 倒计时
            return countDownTime >= 0  && countDownTime <= 20 ? `${Math.floor(countDownTime)} 天` : '已超时';
        }
    },
    { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '申诉人', prop: 'representationPerson' },
];

export default {
    name: "refundManage",
    components: { MyContainer, cesTable, vxetablebase },
    data() {
        return {
            pageLoading: false,
            listLoading: false,
            orderPayTimeRange: [],//付款时间
            orderRefundTimeRange: [],//退款时间
            filter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: "orderPayTime",
                isAsc: false,
                //过滤条件
                masterOrderNumber: null,//主订单编号
                refundOrderNumber: null,//退款单编号
                refundType: [],//退款类型
                beginOrderPayTime: null,//付款时间开始
                endOrderPayTime: null,//付款时间结束
                productID: null,//商品ID
                buyerRefundAmountMin: undefined,//买家退款金额1
                buyerRefundAmountMax: undefined,//买家退款金额2
                beginOrderRefundTime:null,//退款时间开始
                endOrderRefundTime: null,//退款时间结束
                refundReason: [],//买家退款原因
                shop: [],//店铺
                leader: [],//组长
                commissioner: [],//专员
                assistant: [],//助理
                logisticsJudgment: [],//物流判断
                isVoucher: null,//是否有凭证
                isRepresent: null,//是否申诉
                representationPerson: null,//申诉人
            },

            switchOperate: false,//操作-全部false-仅退款true
            refundTypeList: [],//退款类型
            shopList: [],//店铺
            leaderList: [],//组长
            commissionerList: [],//专员
            assistantList: [],//助理
            refundReasonList: [],//买家退款原因
            that: this,

            allRefundDataList: [],//列表数据
            tableCols: tableCols,//输出列表
            total: 0,
            summaryarry: { count_sum: 10},
            pager: { OrderBy: "id", IsAsc: false },
            sels: [], // 列表选中列
            selectList: [],
            
            pickerOptions: {
                shortcuts: [{
                text: '前一天',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
                        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        end.setTime(end.getTime());
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
                        const date2 = new Date(); date2.setDate(date2.getDate());
                        picker.$emit('pick', [date1, date2]);
                    }
                }]
            },
            dialogVisible: false,
            uploadLoading: false,
            fileList: [],
        }
    },
    async mounted() {
        //默认显示所有退款数据，不需要过滤条件
        // let end = new Date();
        // let start = new Date();
        // start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
        // end.setTime(end.getTime());

        // this.orderPayTimeRange = [start, end];
        // this.filter.beginOrderPayTime = start;
        // this.filter.endOrderPayTime = end;

        // this.orderRefundTimeRange = [start, end];
        // this.filter.beginOrderRefundTime = start;
        // this.filter.endOrderRefundTime = end;
        this.init();
        this.onSearch();
    },
    methods: {
        async init() {
            //退款类型
            var { data } = await getRefundType();
            this.refundTypeList = data.filter(a => a != null);
            // this.refundTypeList.concat(data);
            //买家退款原因
            var { data } = await getRefundReason();
            this.refundReasonList = data;
            //店铺
            var { data } = await getShopNameInProductEntity();
            this.shopList = data;
            //组长
            var { data } = await getLeaderNameInProductEntity();
            this.leaderList = data;
            //专员
            var { data } = await getCommissionerNameInProductEntity();
            this.commissionerList = data;
            //助理
            var { data } = await getAssistantNameInProductEntity();
            this.assistantList = data;
        },
        //排序
        sortchange(column) {
            if (column.order) {
                this.filter.orderBy = column.prop;
                this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
                this.getList();
            }
        },
        //复选框数据
        selectchange:function(rows,row) {
            this.selectList = [];
            rows.forEach(f => {
                this.selectList.push(f);
            })
        },
        //付款时间
        changePayTime(e) {
            const formatDate = date => {
                if (!date) return null;
                const localDate = new Date(date);
                // 获取时间偏差（分钟）
                const offset = localDate.getTimezoneOffset();
                // 转换为 UTC 时间
                return new Date(localDate.getTime() + offset * 60000).toISOString().split('T')[0];
            };
            this.filter.beginOrderPayTime = e ? formatDate(e[0]) : null;
            this.filter.endOrderPayTime = e ? formatDate(e[1]) : null;
        },
        //买家退款金额最小值
        handleMinChange(value) {
            if (value === '') {
            this.filter.buyerRefundAmountMin = undefined; // 或 null，根据需求
            } else {
            this.filter.buyerRefundAmountMin = value;
            }
        },
        //买家退款金额最大值
        handleMaxChange(value) {
            if (value === '') {
            this.filter.buyerRefundAmountMax = undefined; // 或 null，根据需求
            } else {
            this.filter.buyerRefundAmountMax = value;
            }
        },
        //退款时间
        changeRefundTime(e) {
            const formatDate = date => {
                if (!date) return null;
                const localDate = new Date(date);
                // 获取时间偏差（分钟）
                const offset = localDate.getTimezoneOffset();
                // 转换为 UTC 时间
                return new Date(localDate.getTime() + offset * 60000).toISOString().split('T')[0];
            };
            this.filter.beginOrderRefundTime = e ? formatDate(e[0]) : null;
            this.filter.endOrderRefundTime = e ? formatDate(e[1]) : null;
        },
        //每页数量改变
        Sizechange(val) {
            this.listLoading = true;
            this.filter.currentPage = 1;
            this.filter.pageSize = val;
            this.getList();
            this.listLoading = false;
        },
        //当前页改变
        Pagechange(val) {
            this.pageLoading = true;
            this.filter.currentPage = val;
            this.getList();
            this.pageLoading = false;
        },
        //查询
        onSearch() {
            //点击查询时才将页数重置为1
            this.filter.currentPage = 1;
            this.$refs.pager.setPage(1);
            this.getList();
        },
        async getList() {
            this.filter.beginOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[0] : null;
            this.filter.endOrderPayTime = this.orderPayTimeRange ? this.orderPayTimeRange[1] : null;
            this.filter.beginOrderRefundTime = this.orderRefundTimeRange ? this.orderRefundTimeRange[0] : null
            this.filter.endOrderRefundTime = this.orderRefundTimeRange ? this.orderRefundTimeRange[1] : null;
            this.listLoading = true;
            const { data, success } = await getRefundData(this.filter);
            this.listLoading = false;
            if (success) {
                this.allRefundDataList = data.list;
                this.total = data.total;
                this.summaryarry = data.summary;
            } else {
                this.$message.error('获取退款数据失败！');
            }
        },
        //导出
        async onExport() {
            if (this.orderPayTimeRange) {
                this.filter.beginOrderPayTime = this.orderPayTimeRange[0];
                this.filter.endOrderPayTime = this.orderPayTimeRange[1];
            }
            if (this.orderRefundTimeRange) {
                this.filter.beginOrderRefundTime = this.orderRefundTimeRange[0];
                this.filter.endOrderRefundTime = this.orderRefundTimeRange[1];
            }
            this.listLoading = true;
            const res = await exportRefundData(this.filter);
            this.listLoading = false;
            if (!res?.data) return;
            const aLink = document.createElement('a');
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
            aLink.href = URL.createObjectURL(blob);
            aLink.setAttribute('download', '退款数据_' + new Date().toLocaleString() + '.xlsx');
            aLink.click();
        },
        //全部、仅退款切换
        changeRefund() {
            // this.switchOperate = !this.switchOperate;//会自动变，不需要
            if (this.switchOperate) {
                this.filter.refundType = ['仅退款'];
            }
            else this.filter.refundType = [];
            this.onSearch();
        },
        //申领申诉
        onClaim() {
            if (!this.selectList || this.selectList.length == 0) {
                this.$message({ message: "请先选择数据", type: "warning"} );
                return;
            }
            for ( let i = 0; i < this.selectList.length; i++) {
                if (this.selectList[i].representationPersonID != null || this.selectList[i].representationPerson != null || this.selectList[i].isRepresent != "是") {
                    this.$message({ message: "不可申诉或其他人已申诉数据！", type: "warning"} );
                    return;
                }
            }
            this.$confirm('确认申领?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(async () => {
                    const res = await applyRefundDataRepresent(this.selectList)
                    if (!res) { return }
                    this.$message({ type: 'success', message: '申领申诉成功!' });
            this.getList();
                }).catch(() => {
                    this.$message({ type: 'info', message: '申领申诉失败' });
                });
        },
        //撤销申领
        async onWithdraw() {
            let res = await getUserInfo();

            if (!this.selectList || this.selectList.length == 0) {
                this.$message({ message: "请先选择数据", type: "warning"});
                return;
            }
            for ( let i = 0; i < this.selectList.length; i++) {
                if (this.selectList[i].representationPersonID != res.data.ddUserId || this.selectList[i].representationPerson != res.data.nickName) {
                    this.$message({ message: "只能撤销自己申诉的数据", type: "warning"} );
                    return;
                }
            }
            this.$confirm('确认撤销?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' })
                .then(async () => {
                    const res = await removeRefundDataApply(this.selectList)
                    if (!res) { return }
                    this.$message({ type: 'success', message: '撤销申领成功!' });
            this.getList();
                }).catch(() => {
                    this.$message({ type: 'info', message: '撤销申领失败' });
                });
        },
        //打开上传弹窗
        ImportDistOrderData() {
            this.dialogVisible = true;
            this.uploadLoading = false;
            this.$nextTick(() => {
                if (this.$refs.upload) {
                    this.$refs.upload.clearFiles();
                }
            });
            this.fileList.splice(0, 1);
        },
        //上传文件
        async uploadFile(item) {
            const form = new FormData();
            form.append("upfile", item.file);
            const res = importTaoFactoryRefundData(form);
            this.$message({message: '上传成功,正在导入中...', type: "success"});
        },
        //更改上传文件
        async uploadChange(file, fileList) {
            if (fileList.length == 2) {
                fileList.splice(1, 1);
                this.$message({ message: "只允许单文件导入", type: "warning" });
                return false;
            }
            this.fileList.push(file);
        },
        //移除上传文件
        uploadRemove() {
            this.fileList.splice(0, 1);
        },
        //提交上传文件
        async submitupload() {
            if (this.fileList.length == 0) {
                this.$message.warning('您没有选择任何文件！');
                return;
            }
            this.$refs.upload.submit();
            this.$refs.upload.clearFiles();
            this.fileList.splice(0, 1);
            this.importVisible = false;
            
            //买家退款原因-更新可能增加的退款原因
            var { data } = await getRefundReason();
            this.refundReasonList.push(...data);
            this.refundReasonList = Array.from(new Set(this.refundReasonList));
        },
    },
}
</script>
<style lang="scss" scoped>
//解决下拉菜单多选由文字太长导致样式问题
::v-deep .el-select__tags-text {
  max-width: 60px;
}
</style>