<template>
      <my-container >
        <!--顶部操作-->
        <el-tabs v-model="activeName" style="height:calc(100% - 40px)">
          <el-tab-pane label="商品ID资料"  name="tab0" style="height:100%;">
                <PubProductInfo ref="PubProductInfo" style="height:100%"></PubProductInfo>
            </el-tab-pane>
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import PubProductInfo from "@/views/customerservice/productDatabase2/PubProductInfo.vue";

export default {
  name: "databaseindex",//商品资料库
  components: {
    MyContainer ,PubProductInfo
  },
  data() {
    return {
      activeName:'tab0'     
    };
  },
  async mounted() {    
  },
  methods: {    
  }
};
</script>
<style lang="scss" scoped>
</style>
