<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange' :isSelectColumn="false"  
          :showsummary='true' :tablefixed='true' :summaryarry='summaryarry' :tableData='tableData' 
          :tableCols='tableCols' :tableHandles='tableHandles' :loading="listLoading" style="width:100%;height:98%;margin: 0">
        </ces-table>
        <!--分页-->
        <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList"/>
        </template>
    </my-container>
</template>
<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import MyContainer from '@/components/my-container';
import cesTable from '@/components/Table/table.vue';
import {formatPlatform,formatYesornoBool,formatLinkProCode,platformlist} from "@/utils/tools";
import {getBugRegisterLog} from '@/api/profit/personnel'
const tableCols =[
      {istrue:true,prop:'approver',label:'姓名', width:'300',sortable:'custom'},
      {istrue:true,prop:'approverRemark',label:'备注',sortable:'custom', width:'230'},
      {istrue:true,prop:'approverTime',label:'操作时间',sortable:'custom', width:'230'},
      {istrue:true,prop:'result',fix:true,label:'操作结果', width:'230',sortable:'custom'},
      {istrue:true,prop:'type',label:'操作类型', width:'221',sortable:'custom'},
];

const tableHandles=[
       // {label:"导出", handle:(that)=>that.onExport()},
      ];

const startDate = formatTime(dayjs().subtract(1,'day'), "YYYY-MM-DD");
const endDate = formatTime(new Date(), "YYYY-MM-DD");

export default ({
    name:"Users",
    components:{MyContainer,cesTable},
    data(){
        return {
          
            that:this, 
            tableCols:tableCols,
            tableHandles:tableHandles,
            tableData:[],
            total: 0,
            pager:{OrderBy:"",IsAsc:false},
            listLoading: false,
            pageLoading: false,
            summaryarry:{},    
            sels:[],

        };
    },
    props:{
        filter:{
            combineCode:null,
        },   
    },

    methods:{
        async sortchange(column){
            if(!column.order)
                this.pager={};
            else
                this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
            await this.onSearch();
            },
            async onSearch(){
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        async getList(){
            var that=this;
            this.listLoading=true;
            var pager = this.$refs.pager.getPager();
           
            const params = {...pager,...this.pager,...this.filter};
            const res = await getBugRegisterLog(params).then(res=>{
                that.total = res.data?.total;
               
                that.tableData = res.data?.list;
                that.summaryarry=res.data?.summary;               
            });
            this.listLoading=false;
        },
        
    }
})
</script>
<style scoped>
    ::v-deep .el-table__fixed-footer-wrapper tbody td{
        color:blue;
    }
</style>

