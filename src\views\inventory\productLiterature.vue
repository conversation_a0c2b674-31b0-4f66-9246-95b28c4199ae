<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <span class="top_span">商品编码：</span>
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <span class="top_span">商品名称：</span>
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <span class="top_span">款式编码：</span>
        <el-input v-model.trim="ListInfo.styleCode" placeholder="款式编码" maxlength="50" clearable class="publicCss" />
        <span class="top_span">是否启用：</span>
        <el-select v-model="ListInfo.isEnabled" placeholder="是否启用" style="width: 100px;margin-right: 5px;" clearable>
          <el-option :key="1" label="启用" :value="1" />
          <el-option :key="-1" label="禁用" :value="-1" />
          <el-option :key="0" label="备用" :value="0" />
        </el-select>
        <span class="top_span">是否内外仓：</span>
        <el-select v-model="ListInfo.wareType" placeholder="是否内外仓" style="width: 100px;margin-right: 5px;" clearable>
          <el-option :key="'内仓'" label="内仓" :value="1" />
          <el-option :key="'外仓'" label="外仓" :value="2" />
        </el-select>
        <span class="top_span">品牌：</span>
        <el-select v-model="ListInfo.pinpaiNameArr" placeholder="品牌" style="width: 200px;margin-right: 5px;" clearable multiple collapse-tags>
          <el-option :label="item" :value="item" v-for="item in pinpaiitems" :key="item" />
          <el-option label="其他" :value="'其他'" />
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <div style="flex-grow: 1;"></div>
        <el-button v-if="checkPermission('Api:Inventory:BasicGoods:SetWarehouseIsExt')" type="primary"
          style="margin-right: 1%;" @click="onStashSettings">内外仓设置</el-button>
      </div>
    </template>
    <vxetablebase :id="'productLiterature202408041537'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :summaryarry='summaryarry' :showsummary='false'
      :isSelectColumn="false" style="width: 100%;margin: 0" :loading="loading" :height="'100%'" @select='selectchange'>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog v-dialogDrag :title="title" :visible.sync="detailsVisable" width="75%">
      <productLiteraturePopup v-if="detailsVisable" :data="detailsData" :isPlatform="isPlatform"
        :detailsLabel="detailsLabel" />
    </el-dialog>

    <el-dialog title="内外仓设置" :visible.sync="outerWarehouseVisable" width="40%" v-dialogDrag>
      <div style="height: 500px;">
        <productLiteratureSettings v-if="outerWarehouseVisable" ref="refproductLiteratureSettings"
          @storageMethod="outerWarehouseVisable = false" />
      </div>
      <div style="margin: 20px 5px 0 0; display: flex; justify-content: flex-end;">
        <el-button @click="outerWarehouseVisable = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethod">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="repertoryVisable" width="50%" v-dialogDrag v-loading="repertoryLoading">
      <div slot="title" class="header-title">
        <span style="font-size: 20px;">库存</span>
      </div>
      <div style="height: 120px;">
        <div style="width: 100%;padding: 10px 0;">
          <div class="row">
            <div class="col-40 bordered no-bottom-border">
              <span class="text-label text-label-31 bordered-right">
                商品编码
              </span>
              <span class="value">
                {{ repertoryList.goodsCode }}
              </span>
            </div>
            <div class="col-60 bordered no-bottom-border">
              <span class="text-label text-label-21 bordered-right">
                商品名称
              </span>
              <span class="value">
                {{ repertoryList.goodsName }}
              </span>
            </div>
          </div>
          <div class="row">
            <div class="col-40 bordered">
              <span class="text-label text-label-31 bordered-right">
                内仓库存
              </span>
              <span class="text-value">
                {{ repertoryList.inStock }}
              </span>
            </div>
            <div class="col-60 bordered">
              <span class="text-label text-label-21 bordered-right">
                外仓库存
              </span>
              <span class="text-value">
                {{ repertoryList.extStock }}
              </span>
            </div>
          </div>
        </div>
        <!-- <div style="color: red;margin: 15px 0 5px 5px;font-size: 18px;">内仓</div>
        <div style="width: 100%;height: 250px;">
          <el-table :data="repertoryList.inWareStock" style="width: 100%;height: 100%;" border height="100%">
            <el-table-column prop="warehouseName" label="仓库" width='300'>
            </el-table-column>
            <el-table-column prop="stock" label="库存">
            </el-table-column>
          </el-table>
        </div>
        <div style="color: red;margin: 15px 0 5px 5px;font-size: 18px;">外仓</div>
        <div style="width: 100%;height: 250px;">
          <el-table :data="repertoryList.extWareStock" style="width: 100%;height: 100%;" border height="100%">
            <el-table-column prop="warehouseName" label="仓库" width="300">
            </el-table-column>
            <el-table-column prop="stock" label="库存">
            </el-table-column>
          </el-table>
        </div> -->
      </div>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { replaceSpace } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import { getGoodsSearchExternalPage, getGoodsStockList, getBrandNameList } from "@/api/inventory/basicgoods"
import productLiteraturePopup from './productLiteraturePopup.vue'
import productLiteratureSettings from './productLiteratureSettings.vue'
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, prop: 'goodsCode', label: '商品编码', width: '130', align: 'left', sortable: 'custom', },
  { istrue: true, prop: 'goodsName', label: '商品名称', width: '230', align: 'left', },
  { istrue: true, prop: 'picture', label: '图片', width: '60', align: 'center', type: 'images', },
  { istrue: true, prop: 'styleCode', label: '款式编码', width: '130', align: 'left', sortable: 'custom', },
  { istrue: true, prop: 'shortName', label: '简称', width: '230', align: 'left', sortable: 'custom' },
  { istrue: true, prop: 'costPrice', label: '成本价', width: '80', align: 'center', sortable: 'custom', },
  { istrue: true, prop: 'isEnabled', label: '是否启用', width: '80', align: 'center', sortable: 'custom', formatter: (row) => row.isEnabled == 0 ? "备用" : row.isEnabled == 1 ? "启用" : row.isEnabled == -1 ? "禁用" : "" },
  { istrue: true, prop: 'weight', label: '重量', width: '80', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'brandName', label: '品牌', width: '80', align: 'center', sortable: 'custom' },
  { istrue: true, prop: 'stock', label: '库存', width: '90', align: 'center', sortable: 'custom', type: 'click', handle: (that, row) => that.onInventoryMethod(row) },
  { istrue: true, prop: 'day3Sales', label: '3天销量', width: '90', align: 'center', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row.day3SalesJson, "3天销量", true, '销量', row.styleCode) },
  { istrue: true, prop: 'day7Sales', label: '7天销量', width: '90', align: 'center', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row.day7SalesJson, "7天销量", true, '销量', row.styleCode) },
  { istrue: true, prop: 'day15Sales', label: '15天销量', width: '90', align: 'center', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row.day15SalesJson, "15天销量", true, '销量', row.styleCode) },
  { istrue: true, prop: 'day30Sales', label: '30天销量', width: '90', align: 'center', sortable: 'custom', type: 'click', handle: (that, row) => that.showDetail(row.day30SalesJson, "30天销量", true, '销量', row.styleCode) },
  { istrue: true, prop: 'day3AfterSalesRate', label: '售后率3天销量', width: '100', align: 'center', sortable: 'custom', type: 'click', formatter: (row) => row.day3AfterSalesRate === null || row.day3AfterSalesRate === undefined ? " " : row.day3AfterSalesRate + '%', handle: (that, row) => that.showDetail(row.day3AfterSalesRateJson, "售后率3天销量", false, '售后率', row.styleCode) },
  { istrue: true, prop: 'day7AfterSalesRate', label: '售后率7天销量', width: '100', align: 'center', sortable: 'custom', type: 'click', formatter: (row) => row.day7AfterSalesRate === null || row.day7AfterSalesRate === undefined ? " " : row.day7AfterSalesRate + '%', handle: (that, row) => that.showDetail(row.day7AfterSalesRateJson, "售后率7天销量", false, '售后率', row.styleCode) },
  { istrue: true, prop: 'day15AfterSalesRate', label: '售后率15天销量', width: '100', align: 'center', sortable: 'custom', type: 'click', formatter: (row) => row.day15AfterSalesRate === null || row.day15AfterSalesRate === undefined ? " " : row.day15AfterSalesRate + '%', handle: (that, row) => that.showDetail(row.day15AfterSalesRateJson, "售后率15天销量", false, '售后率', row.styleCode) },
  { istrue: true, prop: 'day30AfterSalesRate', label: '售后率30天销量', width: '100', align: 'center', sortable: 'custom', type: 'click', formatter: (row) => row.day30AfterSalesRate === null || row.day30AfterSalesRate === undefined ? " " : row.day30AfterSalesRate + '%', handle: (that, row) => that.showDetail(row.day30AfterSalesRateJson, "售后率30天销量", false, '售后率', row.styleCode) },
]
export default {
  name: "productLiterature",
  components: {
    MyContainer, vxetablebase, productLiteraturePopup, productLiteratureSettings
  },
  data() {
    return {
      repertoryLoading: false,//库存加载
      repertoryList: {},//库存列表
      repertoryVisable: false,//库存弹窗
      outerWarehouseVisable: false,//内外仓设置弹窗
      detailsLabel: '',//详情标题
      isPlatform: false,//是否是平台
      title: '',//弹窗标题
      detailsData: [],//详情数据
      pinpaiitems: [], //下拉数据源
      detailsVisable: false,//详情弹窗
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        goodsCode: '',//商品编码
        goodsName: '',//商品名称
        styleCode: '',//款式编码
        isEnabled: null,//是否启用
        wareType: null,//是否内外仓
        pinpaiNameArr: []
      },
      tableCols,//表格列
      tableData: [],//表格数据
      summaryarry: {},//表格汇总
      total: 0,//总数
      loading: false,//加载
      pickerOptions,//时间选择器配置
    }
  },
  async mounted() {
    await this.getList()
    await this.getpinpaiMethod();
  },
  methods: {
    //获取库存
    async onInventoryMethod(row) {
      this.repertoryLoading = true
      this.loading = true
      this.repertoryList = {}
      const { data, success } = await getGoodsStockList({ goodsCode: row.goodsCode })
      this.repertoryLoading = false
      this.loading = false
      if (success) {
        this.repertoryList = data
        this.repertoryList.goodsName = row.goodsName
        this.repertoryList.goodsCode = row.goodsCode
        this.repertoryVisable = true
      } else {
        this.$message.error('获取库存失败')
      }
    },
    //获取品牌
    async getpinpaiMethod() {
      const { data, success } = await getBrandNameList({ })
      if (!success) {
        this.$message.error('获取品牌失败');
        return
      }
      this.pinpaiitems = data;
    },
    //保存内外仓设置
    async onStorageMethod() {
      this.$nextTick(() => {
        this.$refs.refproductLiteratureSettings.onStorage()
      })
    },
    //显示详情
    showDetail(data, title, isPlatform, detailsLabel, styleCode) {
      this.detailsLabel = detailsLabel
      this.isPlatform = isPlatform
      this.title = title
      this.detailsData = data ? JSON.parse(data) : []
      this.detailsVisable = true
    },
    //内外仓设置
    async onStashSettings() {
      this.outerWarehouseVisable = true
    },
    selectchange(val) {

    },
    //查询
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.ListInfo.BrandName = this.ListInfo.pinpaiNameArr.join(',');
      this.loading = true
      const { data, success } = await getGoodsSearchExternalPage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

  .top_span {
    margin: 0 0 3px 10px;
    color: #606266;
    font-size: 14px;
  }

  .publicCss {
    width: 180px;
    margin-right: 5px;
  }
}

.value {
  font-size: 16px;
  margin-left: 10px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row {
  display: flex;
}

.col-40 {
  flex: 0 0 35%;
  display: flex;
  align-items: center;
}

.col-60 {
  flex: 0 0 65%;
  display: flex;
  align-items: center;
}

.text-label {
  font-size: 16px;
  padding: 10px;
}

.text-label-31 {
  width: 20%;
}

.text-label-21 {
  width: 14%;
}

.text-value {
  font-size: 16px;
  margin-left: 10px;
}

.bordered {
  border: 1px solid #ccc;
  box-sizing: border-box;
}

.no-bottom-border {
  border-bottom: none;
}

.bordered-right {
  border-right: 1px solid #ccc;
}
</style>
