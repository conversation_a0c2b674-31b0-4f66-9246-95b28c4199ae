<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="发货开始日期" end-placeholder="发货结束日期" :picker-options="pickerOptions"
          style="width: 210px;margin-right: 2px;" :value-format="'yyyy-MM-dd'" @change="changeTime" :clearable="false">
        </el-date-picker>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.source" placeholder="来源" class="publicCss" clearable>
          <el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="publicCss" style="width: 140px;">
          <inputYunhan ref="refinnerOrderNos" :inputt.sync="ListInfo.innerOrderNos" v-model="ListInfo.innerOrderNos"
            width="140px" placeholder="内部订单号/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="1000000" :valuedOpen="true" @callback="callbackMethod($event, 'innerOrderNos')" title="内部订单号">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 140px;">
          <inputYunhan ref="reforderNos" :inputt.sync="ListInfo.orderNos" v-model="ListInfo.orderNos" width="140px"
            placeholder="线上订单号/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100" :maxlength="1000000"
            :valuedOpen="true" @callback="callbackMethod($event, 'orderNos')" title="线上订单号">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 140px;">
          <inputYunhan ref="refexpressNos" :inputt.sync="ListInfo.expressNos" v-model="ListInfo.expressNos"
            width="140px" placeholder="物流单号/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="50000"
            :maxlength="1000000" :valuedOpen="true" @callback="callbackMethod($event, 'expressNos')" title="物流单号">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 140px;">
          <inputYunhan ref="refexpressCompanyNames" :inputt.sync="ListInfo.expressCompanyNames"
            v-model="ListInfo.expressCompanyNames" width="140px" placeholder="物流公司/Enter多行输入" :clearable="true"
            :clearabletext="true" :maxRows="100" :maxlength="1000000" :valuedOpen="true"
            @callback="callbackMethod($event, 'expressCompanyNames')" title="物流公司">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 140px;">
          <inputYunhan ref="refplatformNames" :inputt.sync="ListInfo.platformNames" v-model="ListInfo.platformNames"
            width="140px" placeholder="平台/Enter多行输入" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="1000000" :valuedOpen="true" @callback="callbackMethod($event, 'platformNames')" title="平台">
          </inputYunhan>
        </div>
        <div class="publicCss" style="width: 140px;">
          <inputYunhan ref="refsendWarehouseNames" :inputt.sync="ListInfo.sendWarehouseNames"
            v-model="ListInfo.sendWarehouseNames" width="140px" placeholder="发货仓/Enter多行输入" :clearable="true"
            :clearabletext="true" :maxRows="100" :maxlength="1000000" :valuedOpen="true"
            @callback="callbackMethod($event, 'sendWarehouseNames')" title="发货仓">
          </inputYunhan>
        </div>
        <el-input v-model.trim="ListInfo.keys" placeholder="最新轨迹关键字" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.exceptKeys" placeholder="排除最新轨迹关键字" maxlength="50" clearable
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
      <div class="top">
        <el-button type="primary" class="top_button" @click="startImport">导入归档数据</el-button>
        <el-button type="primary" class="top_button" @click="configurationVisible = true">配置</el-button>
        <el-button type="primary" class="top_button" @click="exportProps">导出理赔文件</el-button>
        <el-button type="primary" class="top_button" @click="onBatchArchiving">批量归档</el-button>
        <el-button type="primary" class="top_button" @click="onBatchProcessing">批量进行中</el-button>
        <el-button type="primary" class="top_button" @click="onBatchCatch">抓取数据</el-button>
      </div>
    </template>
    <vxetablebase :id="'expressDeliveryCompensation202505251350'" :tablekey="'expressDeliveryCompensation202505251350'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'"
      @select="selectchange" @checkbox-range-end="selectchange" :border="true">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div slot="title" class="header-title">
        <span class="title-text"><span>导入数据</span></span>
        <span class="title-close"><el-button @click="downLoadFile">下载模版</el-button></span>
      </div>
      <div>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="configurationVisible" width="35%" v-dialogDrag style="margin-top: -5vh;">
      <claimsConfiguration v-if="configurationVisible" @close="configurationVisible = false"
        @successClose="successClose" />
    </el-dialog>

    <el-dialog :visible.sync="trackRecordinfo.visible" width="50%" v-dialogDrag style="margin-top: -5vh;">
      <trackRecordList v-if="trackRecordinfo.visible" :chuanshenData="trackRecordinfo.chuanshenData"
        @close="trackRecordinfo.visible = false" />
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions, downloadLink } from '@/utils/tools'
import dayjs from 'dayjs'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getExpressClaimOrderListAsync, exportExpressClaimOrderListAsync, batchChangeExpressClaimOrderClose, batchChangeExpressClaimOrderWaitCatch, importBatchCloseExpressClaimOrderAsync, getExpressClaimOrderHisListAsync } from '@/api/customerservice/expressClaimOrder'
import claimsConfiguration from './claimsConfiguration.vue'
import trackRecordList from './trackRecordList.vue'
const statusList = [
  { label: '进行中', value: '进行中' },
  { label: '归档', value: '归档' },
  { label: '匹配失败', value: '匹配失败' },
]
const sourceList = [
  { label: 'RPA抓取', value: 'RPA抓取' },
  { label: '快递拦截', value: '快递拦截' },
  { label: '客服导入', value: '客服导入' },
]
const tableCols = [
  { istrue: true, width: '60', type: "checkbox" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'source', label: '来源', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'innerOrderNo', label: '内部订单号', headerBgColor: '#07928d', type: 'orderLogInfo', orderType: 'orderNoInner', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'orderNo', label: '线上订单号', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'expressNo', label: '物流单号', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'expressCompany', label: '物流公司', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'expressStatus', label: '物流状态', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'interceptStatus', label: '拦截状态', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderStatus', label: '订单状态', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'orderExceptionFlag', label: '订单异常标记', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'firstExceptionType', label: '首次异常类型', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'firstExceptionTime', label: '首次异常时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'afterSalesStatus', label: '售后单状态', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'orderTag', label: '订单标签', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyerMessage', label: '买家留言', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'sellerRemark', label: '卖家备注', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'platformName', label: '平台', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'shopName', label: '店铺', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'distributor', label: '分销商', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'sendWarehouse', label: '发货仓', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'sendWarehouseId', label: '发货仓ID', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'supplier', label: '供应商', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'deliveryAddress', label: '收货省市区', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'paymentTime', label: '支付时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'lastAuditTime', label: '最后一次审核时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'firstAuditTime', label: '首次审核时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'shippingTime', label: '发货时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'collectionTime', label: '揽收时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'signingTime', label: '签收时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'lastUpdateTime', label: '最近更新时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'hoursSinceLastUpdate', label: '最近更新时间至今(h)', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '180', align: 'center', prop: 'latestTracking', label: '最新轨迹', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'actualWeight', label: '实称重量', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'estimatedWeight', label: '预估重量', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'queryTime', label: '查询时间', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'processingRemark', label: '处理备注', headerBgColor: '#07928d', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'newLatestTracking', label: '最新轨迹更新', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'newLatestTrackingTime', label: '最新轨迹更新时间', type: 'click', handle: (that, row) => that.getExpressClaimOrderHisList(row) },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'newLatestTrackingIsSame', label: '更新后最新轨迹是否一致', formatter: (row) => {
      return row.newLatestTrackingIsSame ? '是' : '否'
    }
  },
  { sortable: 'custom', width: '100', align: 'center', prop: 'claimAmount', label: '理赔金额', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'claimReason', label: '理赔原因', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '添加人', },
]
export default {
  name: "expressDeliveryCompensation",
  components: {
    MyContainer, vxetablebase, inputYunhan, claimsConfiguration, trackRecordList
  },
  data() {
    return {
      trackRecordinfo: {
        visible: false,
        chuanshenData: {},
      },
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {
        upfile: null
      },
      configurationVisible: false,
      statusList,//状态
      sourceList,//来源
      selectList: [],//选中的数据
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        status: null,//状态
        source: null,//来源
        innerOrderNos: null,//内部订单号
        orderNos: null,//线上订单号
        expressNos: null,//物流单号
        keys: null,//最新轨迹关键字
        exceptKeys: null,//排除最新轨迹关键字
        createdUserNames: null,//添加人
        expressCompanyNames: null,//物流公司
        platformNames: null,//平台
        sendWarehouseNames: null,//发货仓
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    async getExpressClaimOrderHisList(row) {
      this.trackRecordinfo.chuanshenData = row
      this.trackRecordinfo.visible = true
    },
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250528/1927542814821236737.xlsx', '快递理赔-快递理赔导入模版.xlsx');
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importBatchCloseExpressClaimOrderAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async handleBatchAction({ confirmText, apiFn, successMsg, params }) {
      try {
        await this.$confirm(confirmText, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        const { success } = await apiFn(params);
        if (success) {
          this.$message.success(successMsg);
          this.getList();
        }
      } catch (e) {
      }
    },
    onBatchCatch() {
      if (this.selectList.length == 0) {
        this.$message.warning('请先选择数据')
        return
      }
      this.handleBatchAction({
        confirmText: '确定要批量抓取吗？',
        apiFn: batchChangeExpressClaimOrderWaitCatch,
        params: this.selectList.map(item => ({ expressNo: item.expressNo })),
        successMsg: '批量抓取成功'
      });
    },
    onBatchArchiving() {
      if (this.selectList.length == 0) {
        this.$message.warning('请先选择数据')
        return
      }
      this.handleBatchAction({
        confirmText: '确定要批量修改状态为“归档”吗？',
        apiFn: batchChangeExpressClaimOrderClose,
        params: {
          status: '归档',
          dtos: this.selectList.map(item => ({ expressNo: item.expressNo }))
        },
        successMsg: '批量归档成功'
      });
    },
    onBatchProcessing() {
      if (this.selectList.length == 0) {
        this.$message.warning('请先选择数据')
        return
      }
      this.handleBatchAction({
        confirmText: '确定要批量修改状态为“进行中”吗？',
        apiFn: batchChangeExpressClaimOrderClose,
        params: {
          status: '进行中',
          dtos: this.selectList.map(item => ({ expressNo: item.expressNo }))
        },
        successMsg: '批量进行中成功'
      });
    },
    successClose() {
      this.configurationVisible = false
      this.getList()
    },
    selectchange(val) {
      this.selectList = val;
    },
    callbackMethod(val, type) {
      const map = {
        expressNos: () => (this.ListInfo.expressNos = val),
        expressCompanyNames: () => (this.ListInfo.expressCompanyNames = val),
        platformNames: () => (this.ListInfo.platformNames = val),
        sendWarehouseNames: () => (this.ListInfo.sendWarehouseNames = val),
        orderNos: () => (this.ListInfo.orderNos = val),
        innerOrderNos: () => (this.ListInfo.innerOrderNos = val),
      };
      map[type]?.();
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async exportProps() {
      const { data } = await exportExpressClaimOrderListAsync(this.ListInfo)
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '快递理赔导出数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近一个月时间
        this.ListInfo.startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const { data, success } = await getExpressClaimOrderListAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          if (typeof item.orderTag === 'string') {
            try {
              const parsed = JSON.parse(item.orderTag); // 解析为数组
              if (Array.isArray(parsed)) {
                const ignoreTags = ['1'];
                item.orderTag = parsed.filter(tag => !ignoreTags.includes(tag)).join(',');
              } else {
                item.orderTag = ''; // fallback
              }
            } catch (e) {
              console.warn('orderTag 解析失败:', item.orderTag);
              item.orderTag = ''; // fallback
            }
          }
        });
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 5px;

  .publicCss {
    width: 130px;
    margin-right: 2px;
  }
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 2px;
}

::v-deep(.vxe-header--row) {
  color: black;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 0;

  .title-text {
    display: flex;
    align-items: center;

    .title-close {
      margin-left: 10px;
    }
  }
}
</style>
