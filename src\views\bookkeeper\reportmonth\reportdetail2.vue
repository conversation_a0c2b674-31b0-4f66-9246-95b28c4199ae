<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent></el-form>
            <el-button-group>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.version" placeholder="类型" style="width: 130px">
                        <el-option label="工资月报" value="v1"></el-option>
                        <el-option label="参考月报" value="v2"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker style="width: 120px" v-model="filter.yearMonth" type="month" format="yyyyMM"
                        value-format="yyyyMM" placeholder="核算月份"></el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.proCode" placeholder="商品ID" style="width:120px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-input v-model="filter.ProductName" placeholder="商品名称" style="width:160px;" />
                </el-button>
                <el-button style="padding: 0;margin: 0;">
                    <el-select filterable v-model="filter.platform1" @change="onchangeplatform" clearable placeholder="平台"
                        style="width:120px;">
                        <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable clearable v-model="filter.shopCode" placeholder="所属店铺">
                        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName"
                            :value="item.shopCode"></el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select filterable v-model="filter.groupId" collapse-tags clearable placeholder="运营组"
                        style="width: 90px">
                        <el-option key="无运营组" label="无运营组" :value="0"></el-option>
                        <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onExport">导出</el-button>
            </el-button-group>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :isSelectColumn="false" :showsummary='true' @summaryClick='onsummaryClick' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='financialreportlist' :tableCols='tableCols' :tableHandles='tableHandles'
            :loading="listLoading">
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>

        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar v-if="buscharDialog.visible" :analysisData="buscharDialog.data"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import { getAllList as getAllShopList, getDirectorGroupList, getList as getshopList } from '@/api/operatemanage/base/shop';
// import {pageMonthReportDetail2Async as getMonthDetail2list,exportMonthReportDetail2 } from '@/api/bookkeeper/financialDetail'
import { getFinancialReportList as getMonthDetail2list } from '@/api/financial/yyfy'
import { exportFinancialReport } from '@/api/bookkeeper/financialreport'
import { exportMonthReportDetail2 } from '@/api/bookkeeper/financialreport'
import { formatProName, formatTime, formatPlatform, formatLinkProCode, platformlist } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
import { getAnalysisCommonResponse } from '@/api/admin/common'
let loading;
const startLoading = () => {  // 使用Element loading-start 方法
    loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    });
};
const tableCols = [
    { istrue: true, fixed: true, prop: 'proCode', fix: true, label: '商品ID', width: '120', sortable: 'custom', type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.proCode) },
    { istrue: true, fixed: true, prop: 'idGroup', label: '小组', sortable: 'custom', width: '70', formatter: (row) => row.groupName },
    { istrue: true, prop: 'platform', fix: true, label: '平台', width: '60', sortable: 'custom', formatter: (row) => formatPlatform(row.platform) },
    {
        istrue: true, prop: '', label: `账单`, merge: true, prop: 'mergeField',
        cols: [
            { istrue: true, prop: 'yearMonth', label: '年月', sortable: 'custom', width: '65' },
            //{istrue:true,prop:'createdTime',label:'统计日期',sortable:'custom', width:'150'},
            { istrue: true, prop: 'proName', label: '产品名称', width: '150' },
            //{istrue:true,prop:'grossProfit',label:'销售毛利',sortable:'custom', width:'80'},
            { istrue: true, prop: 'goodsCodes', label: '商品编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'styleCode', label: '系列编码', width: '100', sortable: 'custom' },
            { istrue: true, prop: 'shopCode', label: '店铺名称', sortable: 'custom', width: '150', formatter: (row) => row.shopName },
            //{istrue:true,prop:'orderCount',label:'订单数',sortable:'custom', width:'70'},
            { istrue: true, prop: 'count',  label: 'ID数', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'amountSettlement',  label: '结算收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountSettlement_1',  label: '结算收入-1', sortable: 'custom', width: '95' },
            { istrue: true, prop: 'amountSettlement_2',  label: '2月之前月份收入', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountCrossMonthIn',  label: '跨月收入', width: '80' },
            { istrue: true, prop: 'amountOut',  label: '退款', sortable: 'custom', width: '70' },
            { istrue: true, prop: 'amountCrossMonthOut',  label: '跨月退款', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountTaoKeNot',  label: '淘客不计', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountShare',  label: '参与公摊金额', sortable: 'custom', width: '110' },
            // {istrue:true,prop:'dkTotalAmont',label:'账单扣点', sortable:'custom',width:'80'},
            //{istrue:true,prop:'AmountCrossMonthIn_1',label:'2月之前月份收入-1', sortable:'custom',width:'80'},
            { istrue: true, prop: 'amountCost',  label: '结算成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountOutCost',  label: '退款成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountEmptyId',  label: '空白链接成本', sortable: 'custom', width: '80' },
            //{istrue:true,prop:'amountExceptionCostAvg',label:'异常成本分摊', sortable:'custom',width:'80'},
            { istrue: true, prop: 'amountReSendCost',  label: '补发成本', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'amountExceptionCost',  label: '异常成本', sortable: 'custom', width: '80' },
            //{istrue:true,prop:'amountReSendCostAvg',label:'补发成本分摊',sortable:'custom', width:'80'},
            { istrue: true, prop: 'agentCost',  label: '代发成本差', sortable: 'custom', width: '80' },
            { istrue: true, prop: 'cbcamount',  label: '护墙角成本差', width: '95', sortable: 'custom' },
            { istrue: true, prop: 'saleProfit',  label: '销售毛利', sortable: 'custom', width: '80' },
        ]
    },
    { istrue: true, prop: 'totalProductCost',  label: '产品费用合计', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'totalWagesCost',  label: '工资合计', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'grossProfit',  label: '产品利润', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'detail2_1',  label: '万达办公房租', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_2',  label: '万达宿舍', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_3',  label: '圆通仓储/办公房租', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_4',  label: '圆通宿舍房租', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_5',  label: '诚信通仓库房租', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_6',  label: '诚信通宿舍', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_7',  label: '上海云仓宿舍房租', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'detail2_8',  label: '南昌办公', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'detail2_9',  label: '南昌仓库房租', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_10',  label: '南昌宿舍房租', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_11',  label: '维修费', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_12',  label: '软件费(年付)', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_13',  label: '保险费', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_14',  label: '餐费', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_15',  label: '软件费(月付)', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_16',  label: '日常记帐-线下支出', width: '100', sortable: 'custom' },
    { istrue: true, prop: 'detail2_17',  label: '水电费', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_18',  label: '折旧', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_22',  label: '做账费', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_19',  label: '招待费', width: '65', sortable: 'custom' },
    { istrue: true, prop: 'detail2_20',  label: '代理咨询费', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'detail2_21',  label: '税费', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'detail2_24',  label: '客服工资', width: '80', sortable: 'custom' },
    { istrue: true, prop: 'amontFreightfee',  label: '采购运费', width: '95', sortable: 'custom' },
    { istrue: true, prop: 'amontAdditionfee',  label: '剩余工资', width: '95', sortable: 'custom', tipmesg: '除运营工资、美工提成、采购提成工资' },
    { istrue: true, prop: 'netProfit',  label: '净利润', width: '80', sortable: 'custom' },

    //   {istrue:true,prop:'totalAmont',label:'实际合计', width:'80',sortable:'custom'},
    // {istrue:true,prop:'grossProfit',label:'明细2合计',sortable:'custom', width:'80'},
];
export default {
    name: "Users",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar },
    data() {
        return {
            that: this,
            filter: {
                proCode: null,
                platform1: null,
                selectplat: 2,
                yearMonth: null,
                shopCode: null,
                groupId: null,
                productName: null,
                version: "v1"
            },
            platformlist: platformlist,
            shopList: [],
            userList: [],
            grouplist: [],
            financialreportlist: [],
            tableCols: tableCols,
            tableHandles: [],
            total: 0,
            // summaryarry:{count_sum:10},
            pager: { OrderBy: " AmountSettlement ", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            analysisFilter: {
                searchName: "View_FinancialMonthReport_All_Detail2",
                extype: 5,
                selectColumn: "Count",
                filterTime: "YearMonth",
                isYearMonthDay: false,
                filter: null,
                columnList: [{ columnNameCN: '订单数', columnNameEN: 'Count' }]
            },
            buscharDialog: { visible: false, title: "", data: [] },
        };
    },
    async mounted() {
        //await this.onSearch()
        await this.getShopList();
    },
    methods: {
        async onchangeplatform(val) {
            const res1 = await getshopList({ platform: val, CurrentPage: 1, PageSize: 100000 });
            this.shopList = res1.data.list
        },
        async getShopList() {
            const res1 = await getAllShopList({ platforms: [1, 9] });
            this.shopList = [];
            res1.data?.forEach(f => {
                if (f.isCalcSettlement && f.shopCode)
                    this.shopList.push(f);
            });

            var res2 = await getDirectorGroupList();
            this.grouplist = res2.data?.map(item => { return { value: item.key, label: item.value }; });
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList().then(res => { });
            // loading.close();
        },
        async getList() {
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter, };
            // this.listLoading = true;
            startLoading();
            const res = await getMonthDetail2list(params).then(res => {
                loading.close();
                that.total = res.data?.total
                that.financialreportlist = res.data?.list;
                that.summaryarry = res.data?.summary;
            });
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onExport() {
            if (!this.filter.yearMonth) {
                this.$message({ message: "请先选择月份！", type: "warning" });
                return;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            var res = await exportFinancialReport(params);
            if (!res?.data) {
                this.$message({ message: "没有数据", type: "warning" });
                return
            }
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '明细2报表' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        async onsummaryClick(property) {
            let that = this;
            this.analysisFilter.filter = {
                proCode: [that.filter.proCode, 0],
                //platform: [that.filter.platform, 0],
                //selectplat: [that.filter.selectplat, 0],
                yearMonth: [that.filter.yearMonth, 0],
                shopCode: [that.filter.shopCode, 0],
                groupId: [that.filter.groupId, 0],
                productName: [that.filter.productName, 5],
                version: [that.filter.version, 0],
            };
            this.analysisFilter.selectColumn = property;
            var cn = "金额";
            if (property == "orderCount" || property == "count") {
                cn = "数量";
            }
            this.analysisFilter.columnList = [{ columnNameCN: cn, columnNameEN: property }];

            const res = await getAnalysisCommonResponse(this.analysisFilter).then(res => {
                that.buscharDialog.visible = true;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
        }
    },
};
</script>
<style lang="scss" scoped>.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}</style>
