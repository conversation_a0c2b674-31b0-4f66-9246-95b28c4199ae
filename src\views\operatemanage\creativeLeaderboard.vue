<template>
  <MyContainer style="height: 98%;">
    <template #header>
      <div class="top">
        <el-switch v-model="fromHigh" active-text="转化率从高到低" inactive-text="点击率从高到低" style="margin-right: 5px;"
          @change="getList('search', 1)">
        </el-switch>
        <el-input v-model.trim="ListInfo.shopName" placeholder="店铺名称" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.productTitle" placeholder="商品标题" maxlength="50" clearable class="publicCss" />
        <el-select v-model.trim="ListInfo.category" placeholder="全部类目" class="publicCss" clearable>
          <el-option v-for="item in allotResultStatus" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button type="primary" @click="getList('search')">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <el-button type="primary" @click="exportProps" style="margin-left: 33%;">导出</el-button>
      </div>
    </template>
    <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;  margin: 0" v-loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import dayjs from 'dayjs'
import { getCreativeListAsync, exportCreativeListAsync, getCreativeCategoryAsync } from '@/api/operatemanage/newmedia/publisher'

const tableCols = [
  { sortable: 'custom', width: '100', align: 'center', prop: 'clickRateRanking', label: '点击率排名', formatter: (row) => row.clickRateRanking == 99999999 ? '' : row.clickRateRanking || '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'conversionRateRanking', label: '转化率排名', formatter: (row) => row.conversionRateRanking == 99999999 ? '' : row.conversionRateRanking || '', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'shopCode', label: '店铺名称', formatter: (row) => row.shopName, },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'productTitle', label: '商品标题', },
  { sortable: 'custom', width: '300', align: 'center', prop: 'idDetailsCategory', label: 'ID详情类目', },
  { width: '100', align: 'center', prop: 'saleCount', label: '已售', },
  { sortable: 'custom', width: '200', align: 'center', prop: 'category', label: '视频一级类目', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'videoID', label: '视频ID', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
  { sortable: 'custom', width: '100', align: 'center', prop: 'proCode', label: '商品ID', type: "click", handle: (that, row, column, cell) => that.canclick(row, column, cell) },
]
export default {
  name: "creativeLeaderboard",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      allotResultStatus: [],//类目
      fromHigh: false,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'clickRateRanking',
        isAsc: true,
        category: null,//类目
        shopName: null,//店铺名称
        productTitle: null,//商品标题
      },
      tableCols,
      tableData: [],
      total: 0,
      loading: false,
    }
  },
  async mounted() {
    // 获取类目
    const { data, success } = await getCreativeCategoryAsync()
    if (success) {
      this.allotResultStatus = data.map(item => { return { label: item.category, value: item.category }; });
    }
    await this.getList()
  },
  methods: {
    //点击跳转
    canclick(row, column, cell) {
      let url = '';
      if (column.label == '视频ID') {
        if (!row.videoID) return;
        url = `https://market.m.taobao.com/app/tb-source-app/video-fullpage/pages/index?spm=a2e1zy.28220776.cca12becc.dbc85ada2.2fb540bds8mWGe&wx_navbar_hidden=true&source=publish&wh_weex=true&type=publish&id=${row.videoID}`;
      } else {
        if (!row.proCode) return;
        url = `https://detail.tmall.com/item.htm?id=${row.proCode}`;
      }
      window.open(url);
    },
    //重置
    reset() {
      this.ListInfo.category = null//类目
      this.ListInfo.shopName = null//店铺名称
      this.ListInfo.productTitle = null//商品标题
    },
    //导出数据
    async exportProps() {
      const { data } = await exportCreativeListAsync(this.ListInfo)
      const aLink = document.createElement("a");
      let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '淘系创意排行榜数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    //获取列表
    async getList(type, val) {
      if (val == 1) {
        let down = this.fromHigh ? 'conversionRateRanking' : 'clickRateRanking'
        this.ListInfo.orderBy = down
        this.ListInfo.isAsc = true
      }
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      this.loading = true
      // 使用时将下面的方法替换成自己的接口
      const { data, success } = await getCreativeListAsync(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    //排序
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}
</style>
