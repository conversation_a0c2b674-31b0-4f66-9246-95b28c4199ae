<template>
  <MyContainer>
    <template #header>
      <div class="">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="收寄开始日期" end-placeholder="收寄结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-date-picker v-model="timeRangesdr" type="daterange" unlink-panels range-separator="至"
          start-placeholder="导入开始日期" end-placeholder="导入结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime2" :clearable="false">
        </el-date-picker>
        <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
          <inputYunhan ref="productmailNumber" :inputt.sync="ListInfo.mailNumber" v-model="ListInfo.mailNumber" width="160px"
            placeholder="邮件号(若输入多条请按回车)" :clearable="true" :clearabletext="true" :maxRows="1000" :valuedOpen="true"
            :maxlength="21000" @callback="mailNumberCallback" title="邮件号">
          </inputYunhan>
        </el-button>
          <el-button style="padding: 0;margin-right: 5px;width: 160px; border: none;">
      
        <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="publicCss" />
          </el-button>
          <el-button style="padding: 0;margin-right: 5px;width: 160px; border: none;">
            <el-select v-model="ListInfo.isSelfCompany" clearable filterable placeholder="是否我司" class="publicCss">
          <el-option  label="是" value="1"/>
          <el-option  label="否" value="0"/>
        </el-select>
      </el-button>
      <el-button style="padding: 0;margin-right: 5px;width: 160px; border: none;">
          <el-select v-model="ListInfo.isCf" clearable filterable placeholder="是否重复" class="publicCss">
          <el-option  label="是" value="1"/>
          <el-option  label="否" value="0"/>
        </el-select>
      </el-button>
      <el-button style="padding: 0;width: 360px; border: none;">
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" :width="'100px'"/>
        </el-button>
       
        <!-- <el-select v-model="ListInfo.expressName" clearable filterable placeholder="快递公司" class="publicCss" @change="getprosimstatelist(2)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
          <el-select v-model="ListInfo.prosimstate" clearable filterable placeholder="快递站点" class="publicCss" >
              <el-option label="暂无站点" value="" />
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
        <el-select v-model="ListInfo.warehouse" clearable filterable placeholder="发货仓库" class="publicCss">
             <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
         </el-select> -->
         <el-button style="padding: 0;margin: 0;width: 160px; border: none;">
            <el-input v-model.trim="ListInfo.outWareHouseName" placeholder="真实发货仓库" maxlength="50" clearable class="" />
          </el-button>
          <el-button style="padding: 0;width: 60px; border: none;">
            <div style="display: flex;align-items: center;padding-top: 1px;">
          <el-checkbox v-model="ListInfo.noUseCatch" class="publicCss" style="width: 60px;">非缓存</el-checkbox>
         </div>
        </el-button>

         
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <!-- <el-button type="primary" @click=startImport>导入</el-button> -->
        <el-button type="primary" @click="exportProps" :disabled="isExport">导出</el-button>
        <el-button type="primary" @click="startClac">确认</el-button>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'billingDetails202410161525'" :tablekey="'billingDetails202410161525'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="100">
           <template #default="{ row, $index }">
            <div style="display: flex">
              <!-- <el-button type="primary" @click="onBatchConfirmation(row)">批次确认</el-button> -->
              <el-button type="danger" @click="onDeleteOperation(row)">批次删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="明细确认" :visible.sync="dialogVisible2" width="30%" v-dialogDrag :close-on-click-modal="false">
      <div style="height: 50px;">
        <el-select v-model="expressCompanyId" placeholder="快递公司"
          style="width: 200px;margin-right: 10px;margin-bottom: 10px;" clearable  @change="getprosimstatelist(1)" >
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
              <el-option label="暂无站点" value="" />
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
        <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
                        <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>

        <el-date-picker style="width: 50%; float: left;" v-model="createTime" type="date" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="onBatchConfirmation">提交确认</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { getExpressComanyAll,getExpressComanyStationName, getDayExpressPage, exportDayExpressList, deleteDayExpressClacInfoData, batchIntoExpressInfoData } from "@/api/express/express";
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import queryCondition from "./queryCondition.vue";
import inputYunhan from "@/components/Comm/inputYunhan";

const tableCols = [
  { sortable: 'custom', width: '140', align: 'center', prop: 'batchNumber', label: '批次', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'prosimstate', label: '快递站点',  formatter: (row) => row.prosimstateName, type: 'custom' },
{ sortable: 'custom', width: '110', align: 'center', prop: 'warehouse', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouse)  },
{ sortable: 'custom', width: '100', align: 'center', prop: 'outWareHouseName', label: '真实发货仓库', },
{ sortable: 'custom', width: '80', align: 'center', prop: 'isSelfCompany', label: '是否我司', formatter: (row) => row.isSelfCompany == 1 ? '我司' : row.isSelfCompany == 0 ? '非我司' : '' },
  { sortable: 'custom', width: '80', align: 'center', prop: 'inportDate', label: '导入日期', formatter: (row) => formatTime(row.inportDate,"YYYY-MM-DD") },
  { sortable: 'custom', width: '190', align: 'center', prop: 'originalOnlineOrderNo', label: '原始线上单号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'receiveDate', label: '收寄日期', formatter: (row) => formatTime(row.receiveDate,"YYYY-MM-DD") },
  { sortable: 'custom', width: '150', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'province', label: '省', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'city', label: '市', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'weight', label: '重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'billingWeight', label: '计费重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'expressFee', label: '运费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'jstWeight', label: '聚水潭重量', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'auditAmount', label: '核算金额', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'difference', label: '差额', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'czFee', label: '操作费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'packageFee', label: '包材费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'addFee', label: '加收', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'sjTotalFee', label: '运费合计', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'isCf', label: '是否重复', formatter: (row) => row.isCf == 1 ? '是'  : '否' },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'cfDate', label: '重复日期', formatter: (row) => !row.isCf ? "" : formatTime(row.cfDate,"YYYY-MM-DD") },

]
export default {
  name: "billingDetails",
  components: {
    MyContainer, vxetablebase, queryCondition, inputYunhan
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
      },
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'inportDate',
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        startCreateTime: null,//开始时间
        endCreateTime: null,//结束时间
        recheckExpress: 1,//是否复核快递公司,1为源数据,2为复核数据
        expressName: null,
        prosimstate: null,
        warehouse: null,
        noUseCatch: false,
      },
      timeRanges: [],
      timeRangesdr: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      isExport: false,
      expresscompanylist: [],
      formatWarehouseNew,
      warehouselist: warehouselist,
      warehouse: null,
      createTime:null,
      dialogVisible2:false,
      prosimstate: null,
      prosimstatelist: [],
      expressCompanyId: null,
    }
  },
  async mounted() {
    if (this.timeRangesdr && this.timeRangesdr.length == 0) {
      //默认给当前月第一天至今天
      this.ListInfo.startCreateTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      this.ListInfo.endCreateTime = dayjs().format('YYYY-MM-DD')
      this.timeRangesdr = [this.ListInfo.startCreateTime, this.ListInfo.endCreateTime]
    }
    // await this.getList()
    await this.init()
  },
  methods: {
    mailNumberCallback(val) {
      this.ListInfo.mailNumber = val
    },
    startClac() {
    this.expressCompanyId = null
    this.prosimstate = null
    this.warehouse = null
    this.createTime = null
    this.expressCompanyId = this.topfilter.expressCompanyId ? (this.topfilter.expressCompanyId).toString() : null
    setTimeout(async() => {
      this.getprosimstatelist(1)
      this.prosimstate = this.topfilter.prosimstateId
      this.warehouse = this.topfilter.warehouseId
    }, 100);
 this.dialogVisible2 = true;
},
    async onBatchConfirmation() {
      this.$confirm('是否确认该批次?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } =  await batchIntoExpressInfoData({ inportDate: this.createTime,expressCompanyId:this.expressCompanyId,warehouse:this.warehouse,prosimstate:this.prosimstate })
        if (success) {
          this.$message.success('确认成功')
          this.getList()
        } else {
          this.$message.error('确认失败')
        }
      }).catch(() => {
        this.$message.info('已取消确认')
      });
    },

    async getprosimstatelist (val) {

      var id;
      if (val == 1)
         {
          id = this.expressCompanyId
          this.prosimstate=null
         }
      else if (val == 2) {
        id = this.ListInfo.expressName
        this.ListInfo.prosimstate = null
      }

      var res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
          this.prosimstatelist = res.data
      }
  },
    async onDeleteOperation(row) {
      this.$confirm('是否删除该批次数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteDayExpressClacInfoData({ batchNumber: row.batchNumber ,inportDate:row.inportDate  })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    async init() {
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
    },
    async exportProps() {
      this.isExport = true
      this.$message.success('导出中，请务重复导出，请耐心等待')

      const params = { ...this.ListInfo, expressName: this.topfilter.expressCompanyId, prosimstate: this.topfilter.prosimstateId, warehouse: this.topfilter.warehouseId }
      await exportDayExpressList(params).then(({ data }) => {
        if (data) {
          const aLink = document.createElement("a");
          let blob = new Blob([data], { type: "application/vnd.ms-excel" })
          aLink.href = URL.createObjectURL(blob)
          aLink.setAttribute('download', '快递费日账单导出_' + new Date().toLocaleString() + '.xlsx')
          aLink.click()
          this.isExport = false
        }
      }).catch(() => {
        this.isExport = false
      })
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async changeTime2(e) {
      this.ListInfo.startCreateTime = e ? e[0] : null
      this.ListInfo.endCreateTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      // if (this.timeRanges && this.timeRanges.length == 0) {
      //   //默认给近7天时间
      //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      // }
      this.loading = true
      const params = { ...this.ListInfo, expressName: this.topfilter.expressCompanyId, prosimstate: this.topfilter.prosimstateId, warehouse: this.topfilter.warehouseId }
      const { data, success } = await getDayExpressPage(params)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        let summary = data.summary || {}
        summary.sjTotalFee_sum = String(summary.sjTotalFee_sum)
        // this.summaryarry = summary

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
            resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
            const options = {
                useGrouping: true,
            };
            return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum
        console.log(this.summaryarry,'this.summaryarry');

        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },

  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 120px;
    margin-right: 5px;
  }
}
</style>
