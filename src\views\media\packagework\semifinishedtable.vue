<template>
    <div>
        <el-form ref="addForm" :model="addForm" :rules="addFormRules" label-width="120px">
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="21" :xl="21">
                            <el-form-item prop="goodsCode" label="成品编码">
                                <el-input v-model="addForm.goodsCode" auto-complete="off" :readonly="true" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="3" :xl="3">
                            <el-button @click="onSelctCp(0)" style="float: right ; font-size:14px" type="text">选择
                            </el-button>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-form-item prop="hopeFinishDate" label="希望完成时间">
                        <el-date-picker v-model="addForm.hopeFinishDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" style="width:100%" placeholder="请选择希望完成时间">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-form-item prop="hopeGoodsAmount" label="计划成品数量">
                        <el-input-number v-model="addForm.hopeGoodsAmount" :min="1" :max="100000000" placeholder="数量" auto-complete="off" style="width:100%" :precision="0">
                        </el-input-number>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                    <el-form-item prop="goodsName" label="成品名称">
                        <el-input v-model="addForm.goodsName" auto-complete="off" :readonly="true" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-form-item prop="groupId" label="运营组">
                        <el-select v-model="addForm.groupId" placeholder="请选择运营组" style="width:100%;">
                            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                    <el-form-item prop="title" label="标题">
                        <el-input v-model="addForm.title" auto-complete="off" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-form-item prop="warehouse" label="加工仓库">
                        <el-select v-model="addForm.warehouse" placeholder="请选择加工仓库" style="width:100%;">
                            <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
                    <el-form-item prop="remark" label="备注">
                        <el-input v-model="addForm.remark" auto-complete="off" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <el-form-item prop="platform" label="平台">
                        <el-select v-model="addForm.platform" placeholder="请选择平台" style="width:100%;">
                            <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <!-- 隐藏字段 -->
            <el-row :hidden="true">
                <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                    <el-form-item prop="receiptNo" label="加工单号">
                        <el-input v-model="addForm.receiptNo" auto-complete="off" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-card class="box-card" style="width:100% ;height: 45px;overflow: hidden;">
                    <div slot="header" class="clearfix">
                        <span>半成品明细</span>
                        <el-button @click="onSelctCp(1)" style="float: right; padding: 3px 0" type="text">添加半成品明细</el-button>
                    </div>
                </el-card>
                <el-card class="box-card" style="width:100% ;height: 300px;overflow: auto;">
                    <el-table :data="addForm.dtlGoods">
                        <el-table-column label="序号" width="50">
                            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                        </el-table-column>
                        <el-table-column prop="id" label="id" v-if="false" />
                        <el-table-column prop="dtlGoodsCode" label="商品编码" />
                        <el-table-column prop="dtlGoodsName" label="商品名称" />
                        <el-table-column prop="dtlGoodsAmount" label="计划数量">
                            <template slot-scope="scope">
                                <el-input-number v-model="scope.row.dtlGoodsAmount" :min="1" :max="100000000" placeholder="数量" :precision="0">
                                </el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column lable="操作">
                            <template slot-scope="scope">
                                <el-button type="danger" @click="onDelDtlGood(scope.$index)">删除 <i class="el-icon-remove-outline"></i>
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-row>
        </el-form>

        <div>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <my-confirm-button type="submit" :validate="addFormValidate" :loading="addLoading" @click="onAddSave" />
            </div>
        </div>
        
    </div>
</template>

<script>
export default {
    name: 'MainSemifinishedtable',

    data() {
        return {
            dialogFinishFormVisible: false,
            addFormRules: {
                goodsCode: [{ required: true, message: '请输入成品编码', trigger: 'blur' }],
                goodsName: [{ required: true, message: '请输入成品名称', trigger: 'blur' }],
                hopeGoodsAmount: [{ required: true, message: '请输入计划成品数量', trigger: 'blur' }],
                warehouse: [{ required: true, message: '请输入加工仓库', trigger: 'blur' }],
                hopeFinishDate: [{ required: true, message: '请输入希望完成时间', trigger: 'blur' }],
                groupId: [{ required: true, message: '请输入运营组', trigger: 'blur' }],
                platform: [{ required: true, message: '请输入希望平台', trigger: 'blur' }],
                title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
            },
        };
    },

    mounted() {
        
    },

    methods: {
        
    },
};
</script>

<style lang="scss" scoped>

</style>