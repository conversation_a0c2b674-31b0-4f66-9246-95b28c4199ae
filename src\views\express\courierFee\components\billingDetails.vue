<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="ListInfo.yearmonth" type="month" :clearable="false" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份"  ></el-date-picker>
        <el-select v-model="companyid" placeholder="请选择快递公司" @change="getprosimstatelist(1)" clearable filterable
        >
         <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
       </el-select>
        <el-input v-model.trim="ListInfo.batchNumber" placeholder="批次号" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.mailNumber" placeholder="邮件号" maxlength="50" clearable class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="startClac">数据处理</el-button>
      </div>
    </template>
    <vxetablebase :id="'billingDetails202410130938'" :tablekey="'billingDetails202410130938'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex">
              <el-button type="primary" @click="deletionMethod(row)">删除</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="35%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">

        <el-form :inline="true" :model="formInline" class="demo-form-inline">

          <el-form-item>
            <el-date-picker v-model="yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份" style="width: 100%" @change="datechange"></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-select v-model="companyid" placeholder="请选择快递公司" @change="getprosimstatelist(1)" clearable filterable
           >
            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          </el-form-item>

          <el-form-item>
            <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
              <!-- <el-option label="暂无站点" value="" /> -->
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
          </el-form-item>

          <el-form-item>
            <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
              <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          </el-form-item>

          <el-form-item>
        
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>

          </el-form-item>

        </el-form>

        
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="数据处理" :visible.sync="dialogVisible2" width="35%" v-dialogDrag :close-on-click-modal="false">
      <div style="display: flex; align-items: baseline; height: 75px;margin-top: 10px;">

        <el-form :inline="true" :model="formInline" class="demo-form-inline">

          <el-form-item>
            <el-date-picker v-model="yearmonth" type="month" format="yyyyMM" value-format="yyyyMM" placeholder="选择月份" style="width: 100%" @change="datechange"></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-select v-model="companyid" placeholder="请选择快递公司" @change="getprosimstatelist(1)" clearable filterable
           >
            <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          </el-form-item>

          <el-form-item>
            <el-select v-model="prosimstate" placeholder="请选择快递站点" clearable style="width: 130px">
              <!-- <el-option label="暂无站点" value="" /> -->
              <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
          </el-select>
          </el-form-item>

          <el-form-item>
            <el-select v-model="warehouse" clearable filterable placeholder="请选择发货仓库" style="width: 110px">
              <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          </el-form-item>

        </el-form>

        
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">关闭</el-button>
        <el-button type="primary" @click="onMonthExpressCompanyFeeCalculate">提交确认</el-button>
      </span>
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getExpressComanyAll, importExpressInfoData, getExpressInfoData, deleteExpressInfoData,getExpressComanyStationName ,monthExpressCompanyFeeCalculate} from "@/api/express/express";
import dayjs from 'dayjs'
import { warehouselist, formatWarehouse,formatTime } from "@/utils/tools";
const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', prop: 'yearMonth', label: '月份', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'expressCompany', label: '快递公司', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'batchNumber', label: '批次号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'businessType', label: '业务类型', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'receiveDate', label: '收寄日期', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'mailNumber', label: '邮件号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'sender', label: '寄件人', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'country', label: '国家', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'province', label: '省 ', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'city', label: '市', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'weight', label: '重量', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'billingWeight', label: '计费重量', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shouldReceive', label: '应收', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'actualReceive', label: '实收', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'receiver', label: '收寄员', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'auditAmount', label: '核算金额', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'difference', label: '差额', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'additionalWeightFee', label: '续重费', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shippingLabel', label: '面单', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'sjTotalFee', label: '运费合计', },
]
export default {
  name: "billingDetails",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      expresscompanylist: [],
      warehouselist: warehouselist,
      companyid: null,
      warehouse: null,
      prosimstate: null,
      yearmonth:null,
      dialogVisible: false,
      dialogVisible2: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      that: this,
      ListInfo: {
        yearmonth:null,
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        companyid: '',//快递公司
        batchNumber: '',//批次号
        mailNumber: '',//邮件号
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      prosimstatelist: [],
    }
  },
  async mounted() {
    this.ListInfo.yearmonth= formatTime(new Date(),'YYYYMM');
    const res = await getExpressComanyAll({});
    if (!res?.success) {
      return;
    }
    const data = res.data;
    this.expresscompanylist = data;
    await this.getList()
  },
  methods: {
    deletionMethod(row) {
      console.log(row)
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteExpressInfoData({ batchNumber: row.batchNumber })
        if (success) {
          this.$message({ message: "删除成功", type: "success" });
          await this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("companyid", this.companyid);
      form.append("yearmonth", this.yearmonth);
      form.append("warehouse", this.warehouse);
      form.append("prosimstate", this.prosimstate);
      var res = await importExpressInfoData(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if(!this.yearmonth){
        this.$message({ message: "请选择月份", type: "warning" });
        return false;
      }
      if (this.companyid == '') {
        this.$message({ message: "请选择快递公司", type: "warning" });
        return false;
      }
      if(!this.prosimstate){
        this.$message({ message: "请选择站点", type: "warning" });
        return false;
      }
      if(!this.warehouse){
        this.$message({ message: "请选择仓库", type: "warning" });
        return false;
      }
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    startClac() {
      this.dialogVisible2 = true;
    },
    async onMonthExpressCompanyFeeCalculate() {
      this.$confirm('是否确认该批次?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } =  await monthExpressCompanyFeeCalculate({ inportDate: this.createTime,expressCompanyId:this.expressCompanyId,warehouse:this.warehouse,prosimstate:this.prosimstate })
        if (success) {
          this.$message.success('确认成功')
          this.getList()
        } else {
          this.$message.error('确认失败')
        }
      }).catch(() => {
        this.$message.info('已取消确认')
      });
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      this.loading = true
      const { data, success } = await getExpressInfoData(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },

    async getprosimstatelist (val) {
      
      var id;
      if (val == 1)
         {
          id = this.companyid
          this.prosimstate=null
         }
      else if (val == 2) {
          id = this.ListInfo.expressName
          this.ListInfo.prosimstate = null
      }

      var res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
          this.prosimstatelist = res.data
      }
  },


    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}
</style>
