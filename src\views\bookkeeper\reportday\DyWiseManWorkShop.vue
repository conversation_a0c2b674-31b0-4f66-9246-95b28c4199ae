<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;">
                    <el-date-picker size="mini" v-model="filter.daterange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" value-format="yyyy-MM-dd" end-placeholder="结束日期" style="width: 240px;"
                        :picker-options="pickerOptions" clearable @change="onSearch()">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;">
                    <el-select v-model="filter.shopCode" filterable clearable placeholder="店铺" style="width:200px"
                        @change="onSearch()">
                        <el-option v-for="item in myShopList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
            </el-button-group>
        </template>

        <ces-table ref="table1" v-if="tableCols.length > 0" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="false" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :loading="listLoading"
            @summaryClick='onsummaryClick' style="width:100%;height:100%;margin: 0">
        </ces-table>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>



        <el-dialog :title="dialogMapVisible.title" :visible.sync="dialogMapVisible.visible" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <my-container>
                <span>
                    <buschar v-if="dialogMapVisible.visible" :analysisData="dialogMapVisible.data">
                    </buschar>
                </span>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogMapVisible.visible = false">关闭</el-button>
                </span>
            </my-container>
        </el-dialog>

    </my-container>
</template>
<script>
import dayjs from "dayjs";
import { formatTime } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import {
    GetDyWiseManWorkPageList, GetDyWiseManWorkChat
} from "@/api/bookkeeper/reportdayDouYin";
import buschar from '@/components/Bus/buschar';

const tableCols = [
    { istrue: true, prop: 'shopCode', label: '店铺', sortable: 'custom', width: '200', formatter: (row) => row.shopName },
    { istrue: true, prop: 'orderCount', label: '订单量', sortable: 'custom', width: '100', summaryEvent: true, },
    { istrue: true, prop: 'payAmount', label: '付款金额', sortable: 'custom', width: '100', formatter: (row) => row.payAmount.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'saleAmount', label: '销售金额', sortable: 'custom', width: '100', formatter: (row) => row.saleAmount.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'commissionAmount', label: '精选联盟佣金', sortable: 'custom', width: '140', formatter: (row) => row.commissionAmount.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'commissionRate', label: '佣金率', sortable: 'custom', width: '100', formatter: (row) => row.commissionRate.toFixed(2) + "%", summaryEvent: true, },
    { istrue: true, prop: 'profit3', label: '毛三', sortable: 'custom', width: '80', formatter: (row) => row.profit3.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'profit3Rate', label: '毛三率', sortable: 'custom', width: '100', formatter: (row) => row.profit3Rate.toFixed(2) + "%", summaryEvent: true, },
    { istrue: true, prop: 'profit4', label: '毛四', sortable: 'custom', width: '80', formatter: (row) => row.profit4.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'profit4Rate', label: '毛四率', sortable: 'custom', width: '100', formatter: (row) => row.profit4Rate.toFixed(2) + "%", summaryEvent: true, },
    { istrue: true, prop: 'profit6', label: '毛六', sortable: 'custom', width: '80', formatter: (row) => row.profit6.toFixed(2), summaryEvent: true, },
    { istrue: true, prop: 'profit6Rate', label: '毛六率', sortable: 'custom', width: '100', formatter: (row) => row.profit6Rate.toFixed(2) + "%", summaryEvent: true, },
    { istrue: true, label: "趋势图", style: "color:red;cursor:pointer;", width: 70, type: "click", handle: (that, row) => that.showchart(row, 'user'), formatter: (row) => "趋势图", },
];

export default {
    name: "DyWiseManWorkShop",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, buschar,
    },
    props: ['myGrouplist', 'myBusinessManList', 'myShopList'],
    data() {
        return {
            that: this,
            filter: {
                isShop: true,
                daterange: [
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD HH:mm"),
                    formatTime(dayjs().subtract(1, "day"), "YYYY-MM-DD HH:mm")
                ],
            },
            tableCols: tableCols,
            total: 0,
            datalist: [],
            pager: { OrderBy: "ShopCode", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            summaryarry: {},
            selids: [],
            pickerOptions: {
                shortcuts: [{
                    text: '近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            dialogMapVisible: { visible: false, title: "", data: {} },
        };
    },
    async mounted() {
        await this.onSearch();
    },
    async created() {
    },
    methods: {
        //排序
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            await this.onSearch();
        },
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        getListParam() {
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            } else {
                this.$message({ type: 'error', message: '请选择要查询的日期!' });
                return;
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            return params
        },
        async getList() {
            var that = this;
            const params = this.getListParam();
            console.log(params);
            that.pageLoading = true;
            const res = await GetDyWiseManWorkPageList(params);
            that.pageLoading = false;

            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            that.summaryarry.commissionRate_sum = that.summaryarry && that.summaryarry.commissionRate_sum ? String(that.summaryarry.commissionRate_sum) + "%" : "";
            that.summaryarry.profit3Rate_sum = that.summaryarry && that.summaryarry.profit3Rate_sum ? String(that.summaryarry.profit3Rate_sum) + "%" : "";
            that.summaryarry.profit4Rate_sum = that.summaryarry && that.summaryarry.profit4Rate_sum ? String(that.summaryarry.profit4Rate_sum) + "%" : "";
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async showchart(row, type) {
            let me = this;
            const params = this.getListParam();
            if (params === false) {
                return;
            }
            params.shopCode = row.shopCode;
            this.dialogMapVisible.title = "汇总趋势图";
            const res = await GetDyWiseManWorkChat(params).then(res => {
                me.dialogMapVisible.visible = true;
                me.dialogMapVisible.data = res;
                console.log(res);
            })
        },
        async onsummaryClick(property) {
            const params = this.getListParam();
            if (params === false) {
                return;
            }
            this.pageLoading = true;
            this.dialogMapVisible.title = "汇总趋势图";
            const res = await GetDyWiseManWorkChat(params);

            this.pageLoading = false;
            this.dialogMapVisible.visible = true;
            res.selectedLegend = res.legend;
            this.dialogMapVisible.data = res;
            console.log(res);

        },
    }
};
</script>
