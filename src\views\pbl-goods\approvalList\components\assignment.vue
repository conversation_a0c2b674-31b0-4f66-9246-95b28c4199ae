<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <dateRange :startDate.sync="ListInfo.startDate" :endDate.sync="ListInfo.endDate" class="publicCss" />
                <el-input v-model.trim="ListInfo.styleCode" placeholder="系列编码" maxlength="50" clearable
                    class="publicCss" />
                <el-select filterable v-model="ListInfo.assignGroupId" collapse-tags clearable placeholder="认领小组"
                    class="publicCss">
                    <el-option v-for="item in grouplist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable>
                    <el-option label="未认领" :value="0" />
                    <el-option label="已认领" :value="1" />
                    <!-- <el-option label="拒绝" :value="2" /> -->
                </el-select>
                <el-button type="primary" @click="getList('search')">查询</el-button>
                <el-button type="primary" :disabled="isExport" @click="exportProps">导出</el-button>
                <el-button type="primary" @click="handleAgree(ids, 1, 'batch')">批量认领</el-button>
                <el-button type="primary" @click="handleAgree(ids, 2, 'batch')">批量拒绝</el-button>
            </div>
        </template>
        <vxetablebase :id="'assignment202408041834'" ref="table" :loading="loading" :that="that" :is-index="true" :hasexpand="true" :tablefixed="true"
            :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols" :is-selection="false"
            :is-select-column="true" :is-index-fixed="false" style="width: 100%; margin: 0" :height="'100%'"
            :isDisableCheckBox="true" :showsummary="data.summary ? true : false" :summaryarry="data.summary"
            @sortchange="sortchange" @checCheckboxkMethod="checCheckboxkMethod" @select="checkboxRangeEnd">
            <template slot="right">
                <vxe-column title="操作" width="120">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-button type="text" @click="handleAgree(row.id, 1)" v-if="row.status == 0">认领</el-button>
                            <el-button type="text" @click="handleAgree(row.id, 2)" v-if="row.status == 0">拒绝</el-button>
                        </div>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

    </MyContainer>
</template>

<script>
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import { download } from '@/utils/download'
import dateRange from "@/components/date-range/index.vue";
export default {
    name: "scanCodePage",
    components: {
        MyContainer, vxetablebase, dateRange
    },
    data() {
        return {
            that: this,
            ListInfo: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'status asc,assignTime',
                isAsc: true,
                summarys: [],
            },
            tableCols: [],
            tableData: [],
            data: {},
            total: 0,
            loading: true,
            isExport: false,
            ids: [],
            grouplist: []
        }
    },
    async mounted() {
        this.init()
        await this.getCol();
        await this.getList()
    },
    methods: {
        checCheckboxkMethod(row, callback) {
            console.log(row.status, 'row.status');
            let isNotStock = row.status == 2
            callback(isNotStock)
        },
        async init() {
            const { data } = await getDirectorGroupList();
            this.grouplist = data?.map(item => { return { value: item.key, label: item.value }; });
        },
        checkboxRangeEnd(row) {
            this.ids = row.map((item) => item.id);
        },
        handleAgree(ids, status, type) {
            if (type == 'batch' && this.ids.length == 0) return this.$message.error('请选择要操作的数据')
            this.$confirm(`是否${status == 1 ? '认领' : '拒绝'}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await request.post('/api/bookkeeper/PblGoodPdd/Assign/Confirm', {
                    ids: type == 'batch' ? ids : [ids],
                    status,
                })
                if (success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                    this.getList()
                }
                if (type == 'batch') {
                    this.ids = []
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });
        },
        handleAdd() {

        },
        async exportProps() {
            this.isExport = true
            await request.post('/api/bookkeeper/PblGoodPdd/Assign/ExportData', this.ListInfo, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post('/api/bookkeeper/PblGoodPdd/Assign/GetColumns')
            if (success) {
                // 在data顶部添加一列
                data.unshift({
                    label: "",
                    type: "checkbox",
                });
                this.tableCols = data;
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post('/api/bookkeeper/PblGoodPdd/Assign/pageGetData', this.ListInfo)
                if (success) {
                    this.data = data;
                } else {
                    this.$message.error("获取列表失败");
                }
            } catch (error) {
                this.$message.error("获取列表失败");
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
