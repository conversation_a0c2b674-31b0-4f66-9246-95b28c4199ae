<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <el-tabs v-model="activeName" :before-leave="beforeLeave" style="height:94%;">
            <el-tab-pane name="switch">
                <span slot="label">
                    <el-switch v-model="switchshow" @change="changeShowgroup" :disabled="isAllcheck" active-text="抖音"
                        inactive-text="拼多多">
                    </el-switch>
                </span>
            </el-tab-pane>
<!-- 拼多多 -->
            <el-tab-pane v-if="showSq" label="分组管理(拼多多)" name="tab1" style="height: 100%;" lazy>
                <pddgroup :filter="filter" ref="pddgroup" style="height: 100%;" ></pddgroup>
                <!-- <sqgroup :filter="filter" ref="sqgroup" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="咨询数据导入(拼多多)" name="tab2" style="height: 100%;" lazy>
                <pddinquirs :filter="filter" ref="pddinquirs" style="height: 100%;"></pddinquirs>
                <!-- <sqinquirs :filter="filter" ref="sqinquirs" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="组效率统计(拼多多)" name="tab3" style="height: 100%;" lazy>
                <pddgroupstatistics :filter="filter" ref="pddgroupstatistics" style="height: 100%;" ></pddgroupstatistics>
                <!-- <sqgroupinquirsstatistics :filter="filter" ref="sqgroupinquirsstatistics" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="店效率统计(拼多多)" name="tab4" style="height: 100%;" lazy>
                <pddstorestatistics :filter="filter" ref="pddstorestatistics" style="height: 100%;" ></pddstorestatistics>
                <!-- <sqshopinquirsstatistics :filter="filter" ref="sqshopinquirsstatistics" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSq" label="个人效率统计(拼多多)" name="tab5" style="height: 100%;">
                <pddinquirsstatistics :filter="filter" ref="pddinquirsstatistics" style="height: 100%;"></pddinquirsstatistics>
                <!-- <sqinquirsstatistics :filter="filter" ref="sqinquirsstatistics" style="height: 100%;" /> -->
            </el-tab-pane>
            
<!-- 抖音 -->
            <el-tab-pane v-if="showSh" label="分组管理(抖音)" name="tab11" style="height: 100%;" lazy>
                <dygroup :filter="filter" ref="dygroup" style="height: 100%;" ></dygroup>
                <!-- <shgroup :filter="filter" ref="shgroup" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="咨询数据导入(抖音)" name="tab22" style="height: 100%;" lazy>
                <dyinquirs :filter="filter" ref="dyinquirs" style="height: 100%;"></dyinquirs>
                <!-- <shinquirs :filter="filter" ref="shinquirs" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="组效率统计(抖音)" name="tab33" style="height: 100%;" lazy>
                <dygroupstatistics :filter="filter" ref="dygroupstatistics" style="height: 100%;" ></dygroupstatistics>
                <!-- <shgroupinquirsstatistics :filter="filter" ref="shgroupinquirsstatistics" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="店效率统计(抖音)" name="tab44" style="height: 100%;" lazy>
                <dystorestatistics :filter="filter" ref="dystorestatistics" style="height: 100%;" ></dystorestatistics>
                <!-- <shshopinquirsstatistics :filter="filter" ref="shshopinquirsstatistics" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="个人效率统计(抖音)" name="tab55" style="height: 100%;">
                <dyinquirsstatistics :filter="filter" ref="dyinquirsstatistics" style="height: 100%;" ></dyinquirsstatistics>
                <!-- <shinquirsstatistics :filter="filter" ref="shinquirsstatistics" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="服务数据导入(抖音)" name="tab66" style="height: 100%;" lazy>
                  <dyimportstatistics :filter="filter" ref="dyimportstatistics" style="height: 100%;"></dyimportstatistics>
                <!-- <inquirsstatisticsmonth :filter="filter" ref="inquirsstatisticsmonth" style="height: 100%;" /> -->
            </el-tab-pane>
            <el-tab-pane v-if="showSh" label="服务数据统计(抖音)" name="tab77" style="height: 100%;" lazy>
                <dyservicestatistics :filter="filter" ref="dyservicestatistics" style="height: 100%;"></dyservicestatistics>
                <!-- <inquirsstatisticsmonth :filter="filter" ref="inquirsstatisticsmonth" style="height: 100%;" /> -->
            </el-tab-pane>
            
        </el-tabs>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";

import pddgroup from '@/views/customerservice/waibao/pdd/pddgroup';
import pddgroupstatistics from '@/views/customerservice/waibao/pdd/pddgroupstatistics';
import pddinquirs from '@/views/customerservice/waibao/pdd/pddinquirs';
import pddinquirsstatistics from '@/views/customerservice/waibao/pdd/pddinquirsstatistics';
import pddstorestatistics from '@/views/customerservice/waibao/pdd/pddstorestatistics';

import dygroup from '@/views/customerservice/waibao/douyin/dygroup';
import dygroupstatistics from '@/views/customerservice/waibao/douyin/dygroupstatistics';
import dyinquirs from '@/views/customerservice/waibao/douyin/dyinquirs';
import dyinquirsstatistics from '@/views/customerservice/waibao/douyin/dyinquirsstatistics';
import dystorestatistics from '@/views/customerservice/waibao/douyin/dystorestatistics';

import dyservicestatistics from '@/views/customerservice/waibao/douyin/dyservicestatistics';
import dyimportstatistics from '@/views/customerservice/waibao/douyin/dyimportstatistics';

 
export default {
    name: "Users",
    provide() {
        return {
            reload: this.reload
        }
    },
    components: {
        MyContainer,
        pddgroup,pddgroupstatistics,pddinquirs,pddinquirsstatistics,pddstorestatistics,
        dygroup,dygroupstatistics,dyinquirs,dyinquirsstatistics,dystorestatistics,
        dyservicestatistics,dyimportstatistics
    },
    data() {
        return {
            that: this,
            pageLoading: '',
            filter: {
            },
            shopList: [],
            userList: [],
            groupList: [],
            selids: [],
            dialogVisibleSyj: false,
            fileList: [],
            activeName: 'tab1',
            //判断权限用
            IsSq: true,

            switchshow: false,
            //默认展示售前
            showSq: true,
            //默认展示售前
            showSh: false,
            isAllcheck: false,
            isShowTj: false
        };
    },
    mounted() {
        window.showtab5pxx = this.showtab5pxx;
        window.showtab55dy = this.showtab55dy
    },
    methods: {
        showtab5pxx() {
            this.activeName = "tab5"
        },
        showtab55dy() {
            this.activeName = "tab55"
        },
        beforeLeave(visitName, currentName) {
            if (visitName == "switch")
                return false;
        },
        changeShowgroup() {
            if (this.switchshow) {
                this.activeName = 'tab11';
                this.showSh = true;
                this.showSq = false;
            } else {
                this.activeName = 'tab1';
                this.showSh = false;
                this.showSq = true;
            }
        },
        reload() {
            //刷新其他页面的下拉框选项
            this.$refs.inquirssh.setShopSelect();
            this.$refs.groupinquirsstatisticssh.setGroupSelect();
            this.$refs.shopinquirsstatisticssh.setShopSelect();
            this.$refs.inquirsstatisticssh.setGroupSelect();

            this.$refs.inquirsstatisticsmonth.setGroupSelect();
            this.$refs.inquirsstatisticsmonth.setShopSelect();
        }
    },


};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
