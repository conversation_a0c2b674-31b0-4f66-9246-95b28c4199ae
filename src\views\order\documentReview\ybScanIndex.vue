<template>
    <MyContainer style="position: relative;">
        <el-tabs v-model="activeName">
            <!-- <el-tab-pane label="汇总" name="first" :lazy="true">
                <ybSummary @toDetails="toDetails"/>
            </el-tab-pane>
            <el-tab-pane label="明细" name="second">
                <ybDetails />
            </el-tab-pane> -->
            <el-tab-pane label="发货仓" name="third">
                <shippingWareHouse />
            </el-tab-pane>
            <el-tab-pane label="扫码记录" name="forth" style="height: 100%;" :lazy="true">
                <scanLog />
            </el-tab-pane>
            <el-tab-pane label="异常编码" name="fifth" style="height: 100%;" :lazy="true">
                <exceptionCoding />
            </el-tab-pane>
        </el-tabs>
        <el-button type="primary" style="position: absolute;top: 15px;right: 10px;" @click="openXd">设置蓄单时间</el-button>

        <!-- 设置蓄单时间 -->
        <el-dialog title="设置蓄单剩余发货时长" :visible.sync="xdTimeVisable" width="20%" v-dialogDrag>
            <div style="display: flex;justify-content: center;">
                <el-input-number v-model="num" :max="9999" label="蓄单时间" :precision="1" :controls="false" />
            </div>
            <div class="btnGroup">
                <el-button @click="xdTimeVisable = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import ybDetails from "./scanCodeToShip/ybDetails.vue";
import ybSummary from "./scanCodeToShip/ybSummary.vue";
import scanLog from './scanCodeToShip/scanLog.vue'
import exceptionCoding from './scanCodeToShip/exceptionCoding.vue'
import shippingWareHouse from './scanCodeToShip/shippingWareHouse.vue'
import { getMobilizeHour, setMobilizeHour } from '@/api/vo/prePackScan'
export default {
    components: {
        MyContainer, ybDetails, ybSummary, scanLog, exceptionCoding, shippingWareHouse
    },
    data() {
        return {
            activeName: 'third',
            xdTimeVisable: false,
        };
    },
    methods: {
        toDetails(e) {
            this.activeName = e
        },
        async openXd() {
            const { data, success } = await getMobilizeHour()
            if (success) {
                this.num = data
                this.xdTimeVisable = true
            } else {
                this.$message.error('获取蓄单时间失败')
            }
        },
        async sumbit() {
            if (!this.num || this.num <= 0) returnthis.$message.error('蓄单时间不能为空或小于等于0')
            const { success } = await setMobilizeHour(this.num)
            if (success) {
                this.$message.success('设置成功')
                this.xdTimeVisable = false
            }else {
                this.$message.error('设置失败')
            }
        },
    }
};
</script>

<style lang="scss" scoped>

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>