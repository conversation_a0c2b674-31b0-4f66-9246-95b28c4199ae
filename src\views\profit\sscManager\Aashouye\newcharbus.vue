<template>
    <!-- <div style="padding-left:10px;overflow: auto;">
      <div :id="'buschar' + randrom" :style="thisStyle" />
    </div> -->
    <div style="height:100%;padding-left:10px;overflow: auto;display: flex; box-sizing: border-box;
     flex-direction: column; min-height: 100px;" v-loading="loading">
    <!-- 原逻辑展示 -->
    <div v-if="!isField && (analysisData.extraField == 0 ? true : analysisData.extraField)" :style="{
      marginLeft: 'auto',
      marginRight: '50px',
      fontSize: '12px',
      marginTop: '5px',
      display: 'flex',
      flexDirection: 'row',
      color: extraColor
    }">{{ extraName }}：{{ analysisData.extraField == 0 ? 0 : analysisData.extraField }}{{ symbol }}
    </div>

    <!-- 新逻辑展示 -->
    <div v-if="isField && analysisData.extraField" :style="{
      marginLeft: 'auto',
      marginRight: '50px',
      fontSize: '12px',
      marginTop: '5px',
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-end'
    }">
      <template v-if="Array.isArray(analysisData.extraField)">
        <div v-for="(item, index) in analysisData.extraField" :key="index" style="margin-right: 15px; display: flex; white-space: nowrap;">
          <span>{{ Object.keys(item)[0] }}
          <span v-if="item[Object.keys(item)[0]] == 0">(持平)</span>
          <span v-else-if="item[Object.keys(item)[0]] > 0">(上升)</span>
          <span v-else>(下降)</span>
            :</span>
          <span :style="{ color: getFieldColor(item[Object.keys(item)[0]]) }">
            {{ getFieldValue(item[Object.keys(item)[0]]) }}{{ symbol }}
          </span>
        </div>
      </template>
      <template v-else>
        <div style="display: flex; white-space: nowrap;">
          <span>{{ extraName }}
          <span v-if="analysisData.extraField == 0">(持平)</span>
          <span v-else-if="analysisData.extraField > 0">(上升)</span>
          <span v-else>(下降)</span>
            :</span>
          <span :style="{ color: getFieldColor(analysisData.extraField) }">
            {{ getFieldValue(analysisData.extraField) }}{{ symbol }}
          </span>
        </div>
      </template>
    </div>
      <div v-if="analysisData && analysisData.series && analysisData.series != null && analysisData.series.length > 0"
        :id="'buschar' + randrom" :style="thisStyle"></div>
      <!-- <div :id="'buschar' + randrom" :style="thisStyle" /> -->
      <div v-else>没有可供展示的图表数据！</div>
    </div>
  </template>
  <script>
  import * as echarts from 'echarts';
  export default {
    name: 'buschar',
    components: {},
    props: {
      loading: {
        type: Boolean, default: function () {
          return false;
        }
      },
      action: { type: Function, default: null },
      parms: { type: Object, default: null },
      analysisData: { type: Object, default: null },
      end: { type: Boolean, default: false },
      isField: {
        type: Boolean,
        default: false
      },
      toolbox: {
        type: Object, default: null

      },
      thisStyle: {
        type: Object,
        default: function () {
          return {
            width: '100%', height: '550px', 'box-sizing': 'border-box', 'line-height': '360px'
          }
        }
      },
      gridStyle: {
        type: Object, default: function () {
          return {
            top: '25%',
            left: '2%',
            right: '10%',
            bottom: '10%',
            containLabel: false
          }
        }
      },
      extraName: {
        type: String,
        default: '【差异率】'
      },
      extraColor: {
        type: String,
        default: 'red'
      },
      symbol: {
        type: String,
        default: '%'
      },
      legendPistion: {
        type: Object,
      }
    },
    data() {
      return {
        that: this,
        randrom: "",
        period: 0,
        pageLoading: false,
        listLoading: false,
        procode: '',
        myChart: null,
      }
    },
    created() {
      var e = 10;
      var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
        a = t.length,
        n = "";
      for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
      this.randrom = n;
    },
    mounted() {
      this.initcharts();
      window.addEventListener('resize', this.handleResizeChart);
    },
    destroyed() {
      window.removeEventListener('resize', this.handleResizeChart);
    },
    methods: {
      // 新增方法：获取字段颜色
      getFieldColor(value) {
        return value > 0 ? 'red' : 'green';
      },

      // 新增方法：获取字段值
      getFieldValue(value) {
        return Math.abs(value);
      },
      // 新增方法：格式化提示内容
      formatTooltip(name, value, formatFn, formatConfig) {
        // 检查是否包含"率"字
        const isRateValue = name.includes('率') || name.includes('占比') || name.includes('%');
        // 应用格式化函数（如果提供）
        let formattedValue = value;
        // 如果有格式化配置且是比率类型，应用乘数
        if (isRateValue && formatConfig && formatConfig.rateMultiplier) {
          formattedValue = value * formatConfig.rateMultiplier;
        }
        // 应用自定义格式化函数（如果提供）
        if (formatFn) {
          formattedValue = formatFn(formattedValue);
        }
        // 为包含"率"的指标添加百分号
        return isRateValue ? formattedValue + '%' : formattedValue;
      },

      //显示数值
      onHandleShow(showlable) {
        // var chartDom = document.getElementById('buschar' + this.randrom);
        //     this.myChart = echarts.init(chartDom);
        //     this.myChart.clear();
        var option = this.Getoptions(this.analysisData);
        if (showlable) {
          option.series.forEach(item => {
            item.label = {
              show: true,
              position: 'top',
              fontSize: 12,
              formatter: (params) => {
                if (params.seriesName.indexOf("%") > -1 || params.seriesName.indexOf("占比") > -1 || params.seriesName.indexOf("率") > -1) {
                  return params.value + '%'
                }
              }
            }
          })
        } else {
          option.series.forEach(item => {
            item.label = {
              show: false,
            }
          })
        }
        option && this.myChart.setOption(option);
      },
      initcharts() {
        let that = this;
        this.$nextTick(() => {
          var chartDom = document.getElementById('buschar' + that.randrom);
          this.myChart = echarts.init(chartDom);
          this.myChart.clear();
          if (this.analysisData && this.analysisData.series) {
            var option = this.Getoptions(this.analysisData);
            option && this.myChart.setOption(option);
          }
          // if (this.action != null) {
          this.myChart.on('click', async params => {
            if (params.seriesType == "line") {
              await this.$emit('action', params);
            } else if (params.seriesType == "bar") {
              if (that.end) {
                return
              }
              var option = that.Getoptions(that.analysisData);
              await this.$emit('baraction', params);
            }
          })
          // }
        });
      },
      initchartsone(val) {
        let that = this;
        this.$nextTick(() => {
          var chartDom = document.getElementById('buschar' + that.randrom);
          this.myChart = echarts.init(chartDom);
          this.myChart.clear();
          if (val && val.series) {
            var option = this.Getoptions(val);
            option && this.myChart.setOption(option);
          }
        });
      },
      randomString() {
        var e = 10;
        var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
          a = t.length,
          n = "";
        for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
        return n
      },
      Getoptions(element) {
        var series = []

        element.series.forEach(s => {
          series.push({ smooth: true, ...s })
        })
        var yAxis = []
        if (Array.isArray(element.yAxis)) {
          element.yAxis.forEach(s => {
            yAxis.push({
              type: 'value', minInterval: 1, offset: s.offset, splitLine: s.splitLine, position: s.position, name: s.name, max: s.max, min: s.min, axisLabel: {
                formatter: function (value) {
                  if (value >= 100000000) {
                    value = (value / 100000000).toFixed(1) + 'Y';
                  }
                  if (value >= 10000000) {
                    value = (value / 10000000).toFixed(1) + 'KW';
                  }
                  if (value >= 10000) {
                    value = (value / 10000).toFixed(1) + 'W';
                  }
                  if (value >= 1000) {
                    value = (value / 1000).toFixed(1) + 'K';
                  }
                  if (value <= -100000000) {
                    value = (value / 100000000).toFixed(1) + 'Y';
                  }
                  if (value <= -10000000) {
                    value = (value / 10000000).toFixed(1) + 'KW';
                  }
                  if (value <= -10000) {
                    value = (value / 10000).toFixed(1) + 'W';
                  }
                  if (value <= -1000) {
                    value = (value / 1000).toFixed(1) + 'K';
                  }
                  return value + s.unit;
                }
              }
            })
          })
        } else {
          yAxis = { ...element.yAxis };
        }


        var selectedLegend = {};//{};
        if (element.selectedLegend) {
          element.legend.forEach(f => {
            //if(!element.selectedLegend.includes(f)) selectedLegend["'"+f+"'"]=false
            if (!element.selectedLegend.includes(f)) selectedLegend[f] = false
          })
        }
        element.toolbox = this.toolbox;
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      //   var option = {
      //     title: { text: element.title },
      //     tooltip: { trigger: 'axis' },
      //     color: element.color,
      //     legend: {
      //       selected: selectedLegend,
      //       data: element.legend,
      //       bottom: this.legendPistion?.bottom
      //     },
      //     grid: this.gridStyle,
      //     toolbox: this.toolbox,
      //     tooltip: {
      //       trigger: 'axis',
      //       axisPointer: {
      //         type: 'cross'
      //       },
      //       padding: [5, 10]
      //     },
      //     xAxis: {
      //       type: 'category',
      //       data: element.xAxis
      //     },
      //     graphic: element.graphic,
      //     yAxis: yAxis,
      //     series: series
      //   };
        // 添加或修改tooltip配置
        if (!element.tooltip) {
          element.tooltip = {};
        }
        // 获取格式化配置
        const formatConfig = element.formatConfig || {};
        // 保留原有的trigger和axisPointer设置
        element.tooltip = {
          ...element.tooltip,
          trigger: element.tooltip.trigger || 'axis',
          axisPointer: element.tooltip.axisPointer || { type: 'cross' },
          formatter: (params) => {
            let result = params[0].axisValue + '<br/>';
            params.forEach(param => {
              const markerSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;
              // 使用formatTooltip方法处理显示值
              let a = param.name.includes('率') || param.name.includes('占比') || param.name.includes('%') ? param.name : param.seriesName;
              const formattedValue = this.formatTooltip(
                a,
                param.value,
                // 这里可以传入格式化函数，例如 val => (val * 100).toFixed(2)
                null,
                formatConfig
              );
              result += markerSpan + param.seriesName + ': ' + formattedValue + '<br/>';
            });
            return result;
          }
        };
        return element;
      },
      handleResizeChart() {
        if (this.myChart) {
          this.myChart.resize();
        }
      }
    }
  }
  </script>

