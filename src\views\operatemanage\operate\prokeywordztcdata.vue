<template>
  <div style="height: 100%;">
     <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' @select='selectchange' :isSelection='true'
              :tableData='list' :tableCols='tableCols' :tableHandles='tableHandles' :showsummary='false' :summaryarry='summaryarry'
              :loading="listLoading">
        <template slot='extentbtn'>
          <el-button-group>
            <el-button style="margin: 0;">
              <el-switch
                  style="display: block"
                  v-model="filter1.periodday"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-text="日"
                  inactive-text="周">
                </el-switch>
            </el-button>
            <el-button style="padding: 0;margin: 0;" v-if="!filter1.periodday">
              <el-select v-model="filter1.jpProCode" placeholder="请选择竞品">
                <el-option
                  v-for="item in jpprocodes"
                  :key="item.proCode"
                  :label="item.proName"
                  :value="item.proCode">
                  <span style="float: left">{{ item.shopName+' --' }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.proName }}</span>
                </el-option>
            </el-select>
            </el-button>
            <el-button style="padding: 0;margin: 0;" v-if="!filter1.periodday">
              <el-select v-model="filter1.type" placeholder="请选择">
                <el-option label="全部" value/>
                <el-option label="仅自己有" value="1"/>
                <el-option label="仅对手有" value="2"/>
                <el-option label="共有的" value="3"/>
              </el-select>
            </el-button>
              <el-button type="primary" @click="onfresh">刷新</el-button>
              <el-button type="primary" @click="onpk">PK</el-button>
              <el-button type="primary" @click="onExport">导出</el-button>
          </el-button-group>
        </template>
     </ces-table>
     <div height style="padding:0px 0px 5px 5px;">
          <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist"/>
       </div>
  </div>
</template>
<script>
import {pageProductKeyWordZTC,getAllJpProducts,exportProductKeyWordZTC} from '@/api/operatemanage/operate'
import MyContainer from '@/components/my-container/nofooter'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/tablev2.vue";
import {formatTime,formatYesornoBool,formatUpNumber1,formatKeyWord} from "@/utils/tools";
const tableCols =[
      {display:true, istrue:false,prop:'keyWord',label:'关键字', width:'150',type:'html',formatter:(row)=>formatKeyWord(row.keyWord,row.keyWordIda==0?2:row.keyWordIdb==0?1:null)},
      {display:true,istrue:false,prop:'date',label:'日期',  width:'100',sortable:'custom',formatter:(row)=>formatTime(row.date,'YYYY-MM-DD')},
      {display:true,istrue:true,prop:'selfVisitors',label:'本店访客人数', width:'140',sortable:'custom',type:'html',formatter:(row)=>formatUpNumber1(row.selfVisitors,(row.selfVisitors-row.jpVisitors))},
      {display:true,istrue:true,prop:'selfVisitors_Rate',label:'访客占比本店%', width:'140'},
      {istrue:true,prop:'selfPays',label:'本店支付人数', width:'150',sortable:'custom',type:'html',formatter:(row)=>formatUpNumber1(row.selfPays,(row.selfPays-row.jpPays))},
      {display:true,istrue:true,prop:'selfPays_Rate',label:'支付占比本店%', width:'140'},
      {istrue:true,prop:'selfPayRate',label:'本店支付转化率', width:'150',sortable:'custom',type:'html',formatter:(row)=>formatUpNumber1(row.selfPayRate,(row.selfPayRate-row.jpPayRate))},
      {istrue:true,prop:'jpVisitors',label:'竞品访客人数', width:'150',sortable:'custom'},
      {istrue:true,prop:'jpPays',label:'竞品支付人数', width:'150',sortable:'custom'},
      {istrue:false,prop:'jpPayRate',label:'竞品支付转化率', width:'150',sortable:'custom'},
     ];
const tableHandles=[
        //{label:"导出", handle:(that)=>that.onExport()},
      ];
export default {
  components: {cesTable, MyContainer, MyConfirmButton },
   props:{
       filter: { }
     },
  data() {
    return {
      that:this,
      filter1: {periodday:true,jpProCode:null,type:null},
      jpprocodes:[],
      list: [],
      tableCols:tableCols,
      tableHandles:tableHandles,
      pager:{OrderBy:"id",IsAsc:false},
      summaryarry:{},
      total:0,
      sels: [],
      selids: [], 
      listLoading: false,
      pageLoading: false
    }
  },
  mounted() {
    // this.onSearch()
  },
  beforeUpdate() {
   // console.log('update')
  },
  methods: {
    onSearch() {
      this.$refs.pager.setPage(1)
      this.getlist()
    },
    async initdata() {     
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;
      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
            hasparm=true;
      }
      if(!hasparm) return; 
      const res = await getAllJpProducts({...this.filter,...{type:2}});
      if (!res?.code)  return; 
      this.jpprocodes= res.data;
    },
    async onfresh() {
      if (!this.filter1.periodday&&!this.filter1.jpProCode) {
        this.$message({message: "请先选择竞品！",type: "warning",});
        return;
      }
      this.getlist()
    },
    async getlist() {
      var hasparm=false;
      var arry= Object.keys(this.filter)
      if (arry.length==0)  return;

      for (let key of Object.keys(this.filter)) {
        if(this.filter[key])
            hasparm=true;
      }
      if(!hasparm) return;
      await this.filtercols();
      var pager = this.$refs.pager.getPager()
      const params = {
        ...pager,
        ...this.pager,
        ... this.filter,
        ... this.filter1
      }
      this.listLoading = true
      const res = await pageProductKeyWordZTC(params)
      this.listLoading = false
      if (!res?.success) {
        return
      }
      this.total = res.data.total
      const data = res.data.list
      data.forEach(d => {
        d._loading = false
      })
      this.list = data
    },
    async filtercols(){
     if (this.filter1.periodday==true) {
       this.tableCols.forEach(f=>{
         f.display=true;
         if (f.prop=='keyWord') f.display=true 
         else if (f.prop=='date') f.display=true 
         else if (f.prop=='selfVisitors') f.display=true 
         else if (f.prop=='selfVisitors_Rate') f.display=true 
         else if (f.prop=='selfPays') f.display=true 
         else if (f.prop=='selfPays_Rate') f.display=true 
         else if (f.prop=='selfPayRate') f.display=true 
         else if (f.prop=='jpVisitors') f.display=false 
         else if (f.prop=='jpPays') f.display=false 
         else if (f.prop=='jpPayRate') f.display=false 
       })
     }
     else {
       this.tableCols.forEach(f=>{
         f.display=true;
         if (f.prop=='keyWord') f.display=true 
         else if (f.prop=='date') f.display=false 
         else if (f.prop=='selfVisitors') f.display=true 
         else if (f.prop=='selfVisitors_Rate') f.display=true 
         else if (f.prop=='selfPays') f.display=true 
         else if (f.prop=='selfPays_Rate') f.display=true 
         else if (f.prop=='selfPayRate') f.display=true 
         else if (f.prop=='jpVisitors') f.display=true 
         else if (f.prop=='jpPays') f.display=true 
         else if (f.prop=='jpPayRate') f.display=true 
       })
     }
    },
   async onpk(){    
    if (this.selids.length==0) {
        this.$message({message: "请先选择！",type: "warning",});
        return;
      }
    if (!this.filter1.periodday&& !this.filter1.jpProCode) {
        this.$message({message: "请先选择竞品！",type: "warning",});
        return;
      }
      this.$emit('onpktype3',this.selids[0],this.filter1.periodday,this.filter1.jpProCode);
    },
   sortchange(column){
      if(!column.order)
        this.pager={};
      else
        this.pager={OrderBy:column.prop,IsAsc:column.order.indexOf("descending")==-1?true:false}
      this.onSearch();
    },
   async onbacthDelete() {
      if (this.selids.length==0) {
       this.$message({type: 'warning',message: '请先选择!'});
       return;
      } 
      this.$confirm('确认删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
            const res = await batchDeleteExpressFreightAP({ids:this.selids.join()})
            if (!res?.success) {return }
            this.$message({type: 'success',message: '删除成功!'});
            this.getlist()
        }).catch(() => {
          this.$message({type: 'info',message: '已取消删除'});
        });
    },
    selsChange: function(sels) {
      this.sels = sels
    },
  selectchange:function(rows,row) {
      this.selids=[];
      rows.forEach(f=>{
        this.selids.push(f.keyWordId);
      })
    },
  async onExport() {
      if (!this.filter1.periodday&& !this.filter.jpProCode) {
        this.$message({message: "请先选择竞品！",type: "warning",});
        return;
      }
      const params = {...this.pager, ... this.filter,...this.filter1}
      var res= await exportProductKeyWordZTC(params);
      if(!res?.data) return
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], {type: "application/vnd.ms-excel"})
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download','直通车流量数据_' + new Date().toLocaleString() + '.xlsx' )
      aLink.click()
     }
  }
}
</script>
