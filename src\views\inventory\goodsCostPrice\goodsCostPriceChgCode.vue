<template>
    <container v-loading="pageLoading">
        <!-- <ces-table ref="table" :that='that' :isIndex='true' @sortchange='sortchange' @select='selectchange'
            @cellclick='cellclick' :hasexpand='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :loading="listLoading" :showsummary='true' :summaryarry='summaryarry'>
        </ces-table> -->
        <vxetablebase :id="'goodsCostPriceChgSumList202301031318001'" :tableData='list' :tableCols='tableCols'
            :showsummary='true' :summaryarry='summaryarry' :loading='listLoading' :border='true' :that="that"
            :tableHandles="tableHandles" ref="vxetable1" @sortchange='sortchange' height="95%">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="margin: 0;">
                        {{ lastUpdateTime }}
                    </el-button>
                </el-button-group>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>
        <el-dialog title="合计数据明细" :visible.sync="GoodsCostChgDetail.visible" width="80%" v-dialogDrag>
            <GoodsCostChgDetail ref="GoodsCostChgDetail" :filter="GoodsCostChgDetail.filter" style="height:600px;">
            </GoodsCostChgDetail>
        </el-dialog>

        <el-dialog :title="dialogTitle" :visible.sync="dialogVisiable" width="90%" v-dialogDrag>
            <vxetablebase :id="'goodsCostPriceChgCode202408041555'" :tableData='dialogList' :tableCols='dialogTableCols' :showsummary='true'
                :summaryarry='dialogSummaryarry' :border='true' :that="that" @sortchange='dialogSortchange'
                style="height: 400px;" />
            <my-pagination :sizes="[50, 100, 200, 300]" :page-size="50" ref="pager" :total="dialogTotal"
                @page-change="dialogPagechange" @size-change="dialogSizechange" style="margin-top: 40px;" />
        </el-dialog>

    </container>
</template>

<script>
import cesTable from "@/components/Table/table.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button'
import { GetCostPriceListByGoodsCodeSumAsync, exportCostPriceListByGoodsCodeSumAsync, updateCostPriceState } from "@/api/inventory/basicgoods"
import GoodsCostChgDetail from "./GoodsCostChgDetail.vue"
import { getPurchaseDataSplitAmount, exportPurchaseDataSplitAmount, allot, getPurchaseDataSplitAmount_Person, exportPurchaseDataSplitAmount_Person } from "@/api/inventory/purchaseData"
const tableCols = [
    { istrue: true, prop: 'picture', label: '图片', type: 'images' },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', },
    { istrue: true, prop: 'targetDate', label: '变动时间', sortable: 'custom', },
    { istrue: true, prop: 'period', label: '变动周期', sortable: 'custom', },
    { istrue: true, prop: 'upAmount', label: '合计涨幅总金额', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, 'upAmount') },
    { istrue: true, prop: 'downAmount', label: '合计降幅总金额', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, 'downAmount') },
    { istrue: true, prop: 'allotUpAmount', label: '调货涨价', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, 'allotUpAmount') },
    { istrue: true, prop: 'allotDownAmount', label: '调货降价', sortable: 'custom', type: 'click', handle: (that, row) => that.openDialog(row, 'allotDownAmount') },
    { istrue: true, prop: 'brandName', label: '采购员', sortable: 'custom', },
];

const dialogTableCols = [
    { istrue: true, prop: 'buyNo', label: '采购单号', width: 90, },
    { istrue: true, prop: 'indexNo', label: 'Erp编号', width: 90, },
    { istrue: true, prop: 'goodsCode', label: '商品编码', sortable: 'custom', width: 90, },
    { istrue: true, prop: 'goodsName', label: '商品名称', sortable: 'custom', width: 90, },
    { istrue: true, prop: 'count', label: '采购量', width: 60, },
    { istrue: true, prop: 'brandName', label: '采购员', sortable: 'custom', width: 60, },
    { istrue: true, prop: 'isAllot', label: '是否调货', sortable: 'custom', width: 90, formatter: (row) => row.isAllot ? '是' : '否' },
    { istrue: true, prop: 'purchaseDate', label: '采购时间', sortable: 'custom', },
    { istrue: true, prop: 'checkDate', label: '审核日期', sortable: 'custom', },
    { istrue: true, prop: 'prevPrice', label: '原成本', sortable: 'custom', width: 60, },
    { istrue: true, prop: 'price', label: '新成本', sortable: 'custom', width: 60, },
    { istrue: true, prop: 'targetDate', label: '变动时间', sortable: 'custom', },
    { istrue: true, prop: 'dayNum', label: '间隔天数', sortable: 'custom', width: 90, },
    { istrue: true, prop: 'upAmount', label: '合计涨幅总金额', sortable: 'custom' },
    { istrue: true, prop: 'downAmount', label: '合计降幅总金额', sortable: 'custom' },
    { istrue: true, prop: 'allotUpAmount', label: '调货涨价', sortable: 'custom', width: 90, },
    { istrue: true, prop: 'allotDownAmount', label: '调货降价', sortable: 'custom', width: 90, },
]
const tableHandles = [
    { label: "导出", handle: (that) => that.onExport() },
];

export default {
    name: 'YunHanAdminGoodsCostPriceChgSum',
    components: { cesTable, container, MyConfirmButton, GoodsCostChgDetail, vxetablebase },
    props: {
        filter: {},
        lastUpdateTime: '',
    },

    data() {
        return {
            dialogTableCols,
            GoodsCostChgDetail: {
                visible: false,
                filter: {
                    id: null,
                },
            },
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: 'targetDate', IsAsc: false },
            summaryarry: {},
            total: 0,
            sels: [],
            pageLoading: false,
            listLoading: false,
            clickOrder: null,
            dialogTitle: null,
            dialogInfo: {
                isAllot: null,//是否调货
                upOrDown: null,//涨价或降价
                targetDate: null,//变动时间
                currentPage: 1,
                pageSize: 50,
                orderBy: null,
                isAsc: false,
                goodsCode: null,
                brandId: null,
            },
            dialogVisiable: false,
            dialogList: [],
            dialogSummaryarry: {},
            dialogTotal: 0,
            total: 0,
            childFilter: {
                currentPage: 1,
                pageSize: 50,
                orderBy: 'targetDate',
                isAsc: false,
                // startTime: null,
                // endTime: null,
            },
        };
    },

    async mounted() {
    },

    methods: {
        Pagechange(val) {
            this.childFilter.currentPage = val;
            this.getlist();
        },
        Sizechange(val) {
            this.childFilter.pageSize = val;
            this.childFilter.currentPage = 1;
            this.getlist();
        },
        //对接记录弹层当前页改变
        dialogPagechange(val) {
            this.dialogInfo.currentPage = val;
            this.openDialog(this.dialogInfo, this.clickOrder)
        },
        //对接记录弹层每页数量改变
        dialogSizechange(val) {
            this.dialogInfo.currentPage = 1;
            this.dialogInfo.pageSize = val;
            this.openDialog(this.dialogInfo, this.clickOrder)
        },
        dialogSortchange({ order, prop }) {
            if (prop) {
                this.dialogInfo.orderBy = prop
                this.dialogInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.openDialog(this.dialogInfo, this.clickOrder)
            }
        },
        matchingTitle(clickOrder) {
            const queryTitle = {
                upAmount: '合计涨幅总金额',
                downAmount: '合计降幅总金额',
                allotUpAmount: '调货涨价',
                allotDownAmount: '调货降价',
            }
            //根据titleOrder找出对应的value
            this.dialogTitle = queryTitle[clickOrder]
        },
        async openDialog(row, clickOrder) {
            this.matchingTitle(clickOrder)
            this.dialogInfo.targetDate = row.targetDate;
            this.dialogInfo.goodsCode = row.goodsCode;
            this.dialogInfo.brandId = row.brandId;
            this.clickOrder = clickOrder;
            if (clickOrder == 'upAmount' || clickOrder == 'downAmount') {
                this.dialogInfo.isAllot = false;
            } else {
                this.dialogInfo.isAllot = true;
            }
            if (clickOrder == 'upAmount' || clickOrder == 'allotUpAmount') {
                this.dialogInfo.upOrDown = 0;
            } else {
                this.dialogInfo.upOrDown = 1;
            }

            const { data, success } = await getPurchaseDataSplitAmount({ ... this.filter, ...this.dialogInfo })
            if (success) {
                this.dialogList = data.list;
                this.dialogSummaryarry = data.summary;
                this.dialogTotal = data.total;
                this.dialogVisiable = true;
            }
        },
        async onExport() {
            var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
            const params = { ... this.filter }

            if (params === false) {
                return;
            }
            var res = await exportPurchaseDataSplitAmount_Person(params);
            loadingInstance.close();
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '合计数据导出_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        showGoodsCostChgDetail(row) {
            this.GoodsCostChgDetail.filter.id = row.id;

            this.GoodsCostChgDetail.visible = true;
            setTimeout(async () => {
                await this.$refs.GoodsCostChgDetail.onSearch();
            }, 100);
        },


        async onSearch(type) {
            //this.$refs.pager.setPage(1)
            this.getlist(type);
        },
        async getlist(type) {
            var pager = this.$refs.pager.getPager()
            if (type == 'search') {
                this.childFilter = { ...this.childFilter, ...this.filter }
                this.childFilter.pageSize = 50
                this.childFilter.currentPage = 1
            }
            this.listLoading = true
            const { data, success } = await getPurchaseDataSplitAmount_Person(this.childFilter)
            this.listLoading = false
            if (!success) return
            this.total = data.total
            this.list = data.list
            this.summaryarry = data.summary;
        },
        async changeStatus(row) {
            this.$confirm('此操作将设置为调货, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                var res = await updateCostPriceState({ id: row.id, isAllot: row.isAllot })
                if (!res?.success) {
                    row.isAllot = !row.isAllot;
                    return;
                }
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                });
            }).catch(() => {
                row.isAllot = !row.isAllot;
                this.$message({
                    type: 'info',
                    message: '已取消操作'
                });
            });

        },
        async cellclick(row, column, cell, event) {

        },
        sortchange({ order, prop }) {
            if (prop) {
                this.childFilter.orderBy = prop
                this.childFilter.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getlist()
            }
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f);
            })
        },
    },
};
</script>

<style lang="scss" scoped></style>
