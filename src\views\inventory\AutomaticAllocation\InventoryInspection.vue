<template>
    <my-container v-loading="pageLoading">
      <!--顶部操作-->
      <template #header>
        <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent></el-form>
      </template>
      <!--列表-->
      <ces-table v-if="showtable" ref="table" :that="that" :isIndex="true" :hasexpand="false" @sortchange="sortchange"
        :tableData="pddcontributeinfolist" @select="selectchange" :isSelection="false" :tableCols="tableCols"
        :loading="listLoading" :summaryarry='summaryarry'>
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <el-table :data="props.row.detaildata" style="width: 100%">
                <el-table-column v-for="col in props.row.detailcols" :prop="col.prop" :label="col.label" :key="col">
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <template slot="extentbtn">
          <el-button-group>
        
            <el-button style="padding: 0;margin: 0;">
              <el-input v-model.trim="Filter.GoodsCode" maxlength="20" clearable placeholder="商品编码" style="width:150px;" />
            </el-button>
       
            <el-button type="primary" @click="onSearch">查询</el-button>
            <!-- <el-button type="primary" @click="onstartImport">导入</el-button>
            <el-button type="primary" @click="onExportType">导出</el-button> -->
          </el-button-group>
        </template>
      </ces-table>
      <!--分页-->
      <template #footer>
        <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
      </template>
  

    </my-container>
  </template>
  <script>
  
  import { pageAutomaticAllocation } from '@/api/inventory/purchaseordernew'
  import { importSaleAfterTx } from '@/api/bookkeeper/import'
  import dayjs from "dayjs";
  import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
  import cesTable from "@/components/Table/table.vue";
  import MyContainer from "@/components/my-container";
  import MyConfirmButton from "@/components/my-confirm-button";
  import MySearch from "@/components/my-search";
  import MySearchWindow from "@/components/my-search-window";
  import { formatPlatform, formatLinkProCode, formatTime } from "@/utils/tools";
  import { getListByStyleCode } from "@/api/inventory/basicgoods"
    
  const startDate = formatTime(dayjs().subtract(30,'day'), "YYYY-MM-DD");
  const endDate = formatTime(new Date(), "YYYY-MM-DD");
  
  export default {
    name: "AfterSaleTx",
    components: {
      MyContainer,
      MyConfirmButton,
      MySearch,
      MySearchWindow,
      cesTable,
    },
    data() {
      return {
        onExportDialogVisible: false,
        importfilter: {
          version: ''
        },
        that: this,
        Filter: {
          GoodsCode: null,
        },
        shopList: [],
        styleCode: null,
        userList: [],
        groupList: [],
        pddcontributeinfolist: [],
        tableCols: this.gettableCols(),
        total: 0,
        summaryarry: {},
        pager: { OrderBy: "GoodsCode", IsAsc: false },
        sels: [], // 列表选中列
        listLoading: false,
        pageLoading: false,
        selids: [],
        fileList: [],
        dialogVisible: false,
        uploadLoading: false,
        showtable: true
      };
    },
    async created() { 
    //   await this.getShopList();
      
    },  
    methods: {
   
      gettableCols() {
        return [
        { istrue: true,  prop: 'goodsCode', label: '商品编码',  width: '80' },
        {istrue: true, prop: 'tgsj', label: `义乌市供应链管理有限公司`, merge: true, prop: 'mergeField', permission: "cgcoltxpddprsi",
    cols: [
        { istrue: true,  prop: 'warehouseStockYW', label: '库存数', width: '80' },
        { istrue: true,  prop: 'inventoryDayYW',  label: '周转天数', width: '80'},
        { istrue: true,  prop: 'safeDayDownYW', label: '安全天数', width: '80'},
        { istrue: true,  prop: 'inTransitNumYW', label: '采购在途数', width: '90' },
        { istrue: true,  prop: 'orderWaitSendYW', label: '订单占有数',  width: '90', formatter: (row) => row.orderWaitSendYW},
        ]},
       
        {istrue: true, prop: 'tgsj', label: `JD-昀晗义乌仓`, merge: true, prop: 'mergeField1', permission: "cgcoltxpddprsi",
    cols: [
    { istrue: true,  prop: 'warehouseStockJDYW', label: '库存数', width: '80' },
        { istrue: true,  prop: 'inventoryDayJDYW',  label: '周转天数', width: '80' },
        { istrue: true,  prop: 'safeDayDownJDYW', label: '安全天数', width: '80', },
        { istrue: true,  prop: 'inTransitNumJDYW', label: '采购在途数', width: '90' },
        { istrue: true,  prop: 'orderWaitSendJDYW', label: '订单占有数',  width: '90',sortable: 'custom', formatter: (row) => row.orderWaitSendJDYW},
        ]},
        {istrue: true, prop: 'tgsj', label: `义乌圆通`, merge: true, prop: 'mergeField2', permission: "cgcoltxpddprsi",
    cols: [
    { istrue: true,  prop: 'warehouseStockYWYT', label: '库存数', width: '80' },
        { istrue: true,  prop: 'inventoryDayYWYT',  label: '周转天数', width: '80' },
        { istrue: true,  prop: 'safeDayDownYWYT', label: '安全天数', width: '80', },
        { istrue: true,  prop: 'inTransitNumYWYT', label: '采购在途数', width: '90' },
        { istrue: true,  prop: 'orderWaitSendYWYT', label: '订单占有数',  width: '90',sortable: 'custom', formatter: (row) => row.orderWaitSendYWYT},
        ]},
        {istrue: true, prop: 'tgsj', label: `圆通孵化仓`, merge: true, prop: 'mergeField3', permission: "cgcoltxpddprsi",
    cols: [
    { istrue: true,  prop: 'warehouseStockYTFH', label: '库存数', width: '80' },
        { istrue: true,  prop: 'inventoryDayYTFH',  label: '周转天数', width: '80' },
        { istrue: true,  prop: 'safeDayDownYTFH', label: '安全天数', width: '80', },
        { istrue: true,  prop: 'inTransitNumYTFH', label: '采购在途数', width: '90' },
        { istrue: true,  prop: 'orderWaitSendYTFH', label: '订单占有数',  width: '90', formatter: (row) => row.orderWaitSendYTFH},
        ]},
        {istrue: true, prop: 'tgsj', label: `圆通5楼仓`, merge: true, prop: 'mergeField4', permission: "cgcoltxpddprsi",
    cols: [
    { istrue: true,  prop: 'warehouseStockYT5F', label: '库存数', width: '80' },
        { istrue: true,  prop: 'inventoryDayYT5F',  label: '周转天数', width: '80' },
        { istrue: true,  prop: 'safeDayDownYT5F', label: '安全天数', width: '80', },
        { istrue: true,  prop: 'inTransitNumYT5F', label: '采购在途数', width: '90' },
        { istrue: true,  prop: 'orderWaitSendYT5F', label: '订单占有数',  width: '90', formatter: (row) => row.orderWaitSendYT5F},
        ]},
        ]
      },
      showClo(){
        return this.Filter.startTime==this.Filter.endTime;
      },
      changeSelectType() { 
        this.getList();
      },
      sortchange(column) {
        if (!column.order) this.pager = {};
        else
          this.pager = {
            OrderBy: column.prop,
            IsAsc: column.order.indexOf("descending") == -1 ? true : false,
          };
        this.onSearch();
      },
      onSearch() {
        this.$refs.pager.setPage(1);
        this.getList();
      },
      async getList() {
   
        const para = { ...this.Filter };
        let pager = this.$refs.pager.getPager();
        const params = {
          ...pager,
          ...this.pager,
          ...para,
        };
        this.listLoading = true;
        const res = await pageAutomaticAllocation(params);
        if (!res.success) {
          this.listLoading = false;
          return;
        }
        this.listLoading = false;
        this.total = res.data.total;
        this.pddcontributeinfolist = res.data.list;
        this.summaryarry = res.data.summary;
      },
      selectchange: function (rows, row) {
        this.selids = [];
        rows.forEach((f) => {
          this.selids.push(f.id);
        });
      },
  
    },
  };
  
  
  </script>
  <style lang="scss" scoped>
  .my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  </style>
    