<template>
  <my-container>
    <template #header>
        <el-date-picker style="width: 280px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          range-separator="至" start-placeholder="起始计划发货日期" end-placeholder="结束计划发货日期" :picker-options="pickerOptions"
          @change="handleDateChange">
        </el-date-picker>
        <el-date-picker style="width: 280px" v-model="timeRange2" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          range-separator="至" start-placeholder="起始支付日期" end-placeholder="结束支付日期" :picker-options="pickerOptions2"
          @change="handleDateChange">
        </el-date-picker>
        <el-input v-model="filter.o_id" v-model.trim="filter.o_id" placeholder="内部订单号(多个，隔开)" style="width:180px;" clearable >
        </el-input>
        <el-input v-model="filter.so_id" v-model.trim="filter.so_id" placeholder="线上订单号(多个，隔开)" style="width:180px;" clearable >
        </el-input>
        <el-select v-model="filter.shop_name" placeholder="店铺名称" multiple clearable collapse-tags filterable style="width:190px;" >
          <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.label">
          </el-option>
        </el-select>
        <el-input v-model="filter.shop_i_id" v-model.trim="filter.shop_i_id" placeholder="宝贝ID(多个ID，隔开)" style="width:180px;" clearable >
        </el-input>
        <el-select v-model="filter.groups" placeholder="运营组" multiple clearable collapse-tags filterable style="width:190px;" >
          <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="filter.shop_status" placeholder="状态" multiple clearable collapse-tags filterable style="width:190px;" >
          <el-option v-for="item in statusList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>

        <el-input v-model="filter.question_type" v-model.trim="filter.question_type" placeholder="异常类型(多个，隔开)" style="width:180px;" clearable >
        </el-input>

        <el-input v-model="filter.brandName" v-model.trim="filter.brandName" placeholder="采购(多个，隔开)" style="width:180px;" clearable >
        </el-input>
        <el-select v-model="filter.allocater" placeholder="分配人" multiple clearable collapse-tags filterable style="width:180px;" >
          <el-option v-for="item in allocaterList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>

        <el-input v-model="filter.styleCode" v-model.trim="filter.styleCode" placeholder="系列编码" style="width:180px;" clearable></el-input>
        <el-select v-model="filter.platformList" placeholder="平台" multiple clearable collapse-tags filterable style="width:180px;" >
          <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>

      <div class="top" style="justify-content: flex-end; margin-right: 20px;">
        <el-button type="primary" @click="onAllocateSet" v-if="checkPermission(':api:operatemanage:operate:setorderprocessrule')">分配设置</el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </div>
    </template>

    <vxetablebase :id="'OrderProcess202410151527'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
      @sortchange='sortchange' :tableData='tableData1' :tableCols='tableCols1' :isSelection="false" :isSelectColumn="false"
      style="width: 100%;margin: 0" v-loading="listLoading" :summaryarry="summaryarry" :showsummary='true'
      :height="'100%'" >
    </vxetablebase>

      <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="分配设置" :visible.sync="setVisable" width="640px" @closeDialog="closeSetDialog"
      v-dialogDrag @close="setCancle">
      <el-button type="primary" @click="onSetRule">新建规则</el-button>

      <vxetablebase :id="'OrderProcessDialog202410151750'" ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
        :tableData='tableData2' :tableCols='tableCols2' :isSelection="false" :isSelectColumn="false"
        style="width: 100%;margin: 0; height: 400px" v-loading="ruleLoading"
        :height="'100%'" >
      </vxetablebase>
    </el-dialog>

    <el-dialog title="规则编辑" :visible.sync="createVisible" width="400px" height="70%" @closeDialog="closeCreateDialog"
      v-dialogDrag @close="createCancle">

      <el-form ref="formCreate" :model="formCreate" label-width="100px" :rules="rules" >
        <el-form-item label="组长：" prop="groups">
          <el-select v-model="formCreate.groups" placeholder="组长" multiple clearable collapse-tags filterable style="width:180px;" >
            <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="中台：" prop="middlePlatformUsers">
          <el-select v-model="formCreate.middlePlatformUsers" placeholder="中台" multiple clearable collapse-tags filterable style="width:180px;" >
            <el-option v-for="item in allocaterList" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div style="text-align: center;">
        <el-button type="primary" @click="createCancle">取消</el-button>
        <el-button type="primary" @click="createSubmit">确认</el-button>
      </div>

    </el-dialog>
  </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import YhUserelectorMulti from '@/components/YhCom/yh-userselectormulti.vue'
// import { getList as getshopList } from '@/api/operatemanage/base/shop';
import {getAllList as getAllShopList} from '@/api/operatemanage/base/shop';
import { getDirectorGroupList } from '@/api/operatemanage/base/shop';
import { getClaimUserList } from "@/api/operatemanage/OperationalMiddleOfficeManage/BrushOrderProcess";
import { getOrderProcess, exportOrderProcess, getOrderProcessRule, deleteOrderProcessRule, setOrderProcessRule, editOrderProcessRule } from "@/api/operatemanage/OperationalMiddleOfficeManage/OrderProcess";

const tableCols1 = [
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '内部订单号', prop: 'o_id', formatter: (row) => { return row.o_id.toString(); } },
  { sortable: 'custom', istrue: true, width: '160', align: 'center', label: '线上订单号', prop: 'so_id' },
  { sortable: 'custom', istrue: true, width: '155', align: 'center', label: '店铺名称', prop: 'shop_name' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '系列编码', prop: 'styleCode' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '宝贝ID', prop: 'shop_i_id' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '分配人', prop: 'allocater' },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '运营组', prop: 'groups' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '运营专员', prop: 'groupSpecialUser' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '运营助理', prop: 'groupAssistanter' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '付款时间', prop: 'pay_date' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '已付金额', prop: 'paid_amount' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '状态', prop: 'shop_status',
    type: "colorClick", style: (that, row) => that.shopStatusColorFormatter(row.shop_status)
 },

  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '异常类型', prop: 'question_type' },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '计划发货日期', prop: 'plan_delivery_date' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '商品编码', prop: 'sku_id' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '商品名称', prop: 'name' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '采购', prop: 'brandName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '平台', prop: 'platform', formatter: (row) => {
    if (2 == row.platform) return '拼多多';
    else if (1 == row.platform) return '天猫';
    else if (9 == row.platform) return '淘宝';
    else if (6 == row.platform) return '抖音';
  }
  },
];

const tableCols2 = [
  { width: '150', align: 'center', label: '组长人员', prop: 'groups' },
  { width: '150', align: 'center', label: '中台人员', prop: 'middlePlatformUsers' },
  { width: '160', align: 'center', label: '创建时间', prop: 'createTime' },
  {
    istrue: true, type: 'button', width: '100', label: '操作', btnList: [
      { label: "删除", handle: (that, row) => that.onDelete(row) },
      { label: "编辑", handle: (that, row) => that.onEdit(row) }
    ]
  }
];

export default {
  name: "OrderProcess",
  components: { MyContainer, vxetablebase, YhUserelectorMulti,
    getDirectorGroupList, getClaimUserList,
    getOrderProcess, exportOrderProcess, getOrderProcessRule, deleteOrderProcessRule, setOrderProcessRule, editOrderProcessRule
  },
  data() {
    return {
      that: this,
      listLoading: false,
      ruleLoading: false,
      createLoading: false,
      createVisible: false,
      setVisable: false,// 规则
      timeRange: [],// 计划发货日期
      timeRange2: [],// 支付日期
      shopList: [],// 店铺列表
      groupList: [
        {value: 0, label: '空白'},
      ],// 运营组列表
      statusList: [ "已发货", "发货中", "未发货" ],// 状态列表
      allocaterList: [],// 分配人列表
      // 过滤条件
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        //过滤条件
        startDate: null,// 开始计划发货日期
        endDate: null,// 结束计划发货日期
        startDate2: null,// 开始付款日期
        endDate2: null,// 结束付款日期
        o_id: null,// 内部订单号
        so_id: null,// 线上订单号
        shop_name: [],// 店铺名称
        shop_i_id: null,// 宝贝ID
        group: [],// 运营组
        shop_status: [],// 状态
        question_type: null,// 异常类型
        brandName: null,// 采购
        allocater: [],// 分配人
        styleCode: null,//系列编码
        platformList: [],//平台
      },
      ruleFilter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: "createTime",
        isAsc: false,
      },
      // 新建规则
      formCreate: {
        groups: [],// 组长
        middlePlatformUsers: [],// 中台
        createTime: null,// 创建时间
      },
      tableData1: [],
      tableCols1: tableCols1,
      total: 0,
      summaryarry:{},
      tableData2: [],
      tableCols2: tableCols2,
      ruleTotal: 0,
      pickerOptions: {
        shortcuts: [{
        text: '前一天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
          end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          end.setTime(end.getTime());
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一个月',
        onClick(picker) {
          const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
          const date2 = new Date(); date2.setDate(date2.getDate());
          picker.$emit('pick', [date1, date2]);
        }
        }]
      },
      pickerOptions2: {
        shortcuts: [{
        text: '前一天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
          end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          end.setTime(end.getTime());
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一个月',
        onClick(picker) {
          const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
          const date2 = new Date(); date2.setDate(date2.getDate());
          picker.$emit('pick', [date1, date2]);
        }
        }]
      },
      rules: {
        groups: [
          { required: true, message: '请选择组长', trigger: 'change' }
        ],
        middlePlatformUsers: [
          { required: true, message: '请选择运营中台', trigger: 'change' }
        ],
      },
      platformList: [
        { value: 2, label: '拼多多' },
        { value: 1, label: '天猫' },
        { value: 9, label: '淘宝' },
        { value: 6, label: '抖音' },
        { value: 8, label: '淘工厂' },
        { value: 20, label: '视频号' },
      ]
    }
  },
  async mounted() {
    this.init();
    this.getList();
  },
  methods: {
    async init() {
      //默认当天日期
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
      //   this.timeRange = [start, end];
      //   this.filter.startDate = start;
      //   this.filter.endDate = end;

      this.timeRange2 = [start, end];
      this.filter.startDate2 = start;
      this.filter.endDate2= end;

      // 店铺-拼多多店铺
      // const shopName  = await getshopList({ platform: null, CurrentPage: 1, PageSize: 100000 });
      // this.shopList = Array.from(
      //   new Set(
      //     shopName.data.list
      //       .map(x => x.shopName)
      //       .filter(name => name.includes('拼多多-')
      //     )
      //   )
      // );//去重，取拼多多店铺
      const shopName = await getAllShopList({ platform: null, CurrentPage: 1, PageSize: 100000 });
      console.log(shopName);
      this.shopList = shopName.data.map(
        (item) => ({ label: item.shopName, value: item.shopName })
      );


      // 运营组-所有的运营+空白+中台组+左玉玲+朋意
      const group = await getDirectorGroupList({});
      this.groupList = this.groupList.concat( group.data.map(item => { return { value: item.key, label: item.value } }) );

      // 分配人-拼多多运营中台
      var { data } = await getClaimUserList();
      this.allocaterList = data;
    },
    // 已完成状态颜色为绿色
    shopStatusColorFormatter(row) {
        let color;
        if (row == "已发货") color = "green";
        else if (row == "发货中") color = "black";
        else color = "red";
        const map = {
            "green" : "color: green",
            "black" : "color: black",
            "red" : "color: red"
        };
        return map[color];
    },
    // 检查是否选择时间
    handleDateChange() {
      this.filter.startDate = this.timeRange ? this.timeRange[0] : null;
      this.filter.endDate = this.timeRange ? this.timeRange[1] : null;
      this.filter.startDate2 = this.timeRange2 ? this.timeRange2[0] : null;
      this.filter.endDate2 = this.timeRange2 ? this.timeRange2[1] : null;
      //   if (!this.filter.startDate || !this.filter.endDate) {
      //     this.$message.error("请选择计划发货日期！");
      //     return false;
      //   }
      if ((!this.filter.startDate || !this.filter.endDate) && (!this.filter.startDate2 || !this.filter.endDate2)) {
        this.$message.error("请选择计划发货日期或支付日期！");
        return false;
      }
      return true;
    },
    // 排序查询
    sortchange(column) {
      if (column.order) {
        this.filter.orderBy = column.prop;
        this.filter.isAsc = column.order.indexOf("descending") == -1 ? true : false;
        this.getList();
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.listLoading = true;
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
      this.listLoading = false;
    },
    // 当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList();
    },
    // 查询
    onSearch() {
      if (!this.handleDateChange()) {
        return;
      }
      //点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.filter.startDate = this.timeRange ? this.timeRange[0] : null;
      this.filter.endDate = this.timeRange ? this.timeRange[1] : null;
      this.filter.startDate2 = this.timeRange2 ? this.timeRange2[0] : null;
      this.filter.endDate2 = this.timeRange2 ? this.timeRange2[1] : null;
      this.listLoading = true;
      const { data, success } = await getOrderProcess(this.filter);
      this.listLoading = false;
      if (success) {
        this.tableData1 = data.list;
        this.total = data.total;
        this.summaryarry= data.summary;
      } else {
        this.$message.error("获取订单处理数据失败");
      }
    },
    // 导出
    async onExport() {
      this.filter.startDate = this.timeRange ? this.timeRange[0] : null;
      this.filter.endDate = this.timeRange ? this.timeRange[1] : null;
      this.filter.startDate2 = this.timeRange2 ? this.timeRange2[0] : null;
      this.filter.endDate2 = this.timeRange2 ? this.timeRange2[1] : null;
      this.listLoading = true;
      const res = await exportOrderProcess(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", '订单处理_' + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
    // 每页规则数量改变
    ruleSizeChange(val) {
      this.ruleLoading = true;
      this.ruleFilter.currentPage = 1;
      this.ruleFilter.pageSize = val;
      this.getRuleList();
      this.ruleLoading = false;
    },
    // 当前规则页改变
    rulePageChange(val) {
      this.ruleFilter.currentPage = val;
      this.getRuleList();
    },
    async getRuleList() {
      this.ruleLoading = true;
      const { data, success } = await getOrderProcessRule(this.ruleFilter);
      this.ruleLoading = false;
      if (success) {
        this.tableData2 = data.list;
        this.ruleTotal = data.total;
      } else {
        this.$message.error("获取配置规则数据失败");
      }
    },
    // 显示规则弹窗
    async onAllocateSet() {
      this.setVisable = true;
      this.getRuleList();
    },
    // 关闭规则弹窗
    setCancle() {
      this.setVisable = false;
    },
    // 关闭规则弹窗
    closeSetDialog() {
      this.setVisable = false;
    },
    // 删除规则
    async onDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrderProcessRule(row).then(response => {
        if (response.data) {
          this.$message.success('删除成功!');
          this.getRuleList();
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 编辑规则
    async onEdit(row) {
        const data = row.groups.split(',');
        this.formCreate.groups = this.groupList.filter(element => data.includes(element.label));
      this.formCreate.middlePlatformUsers = row.middlePlatformUsers.split(',');
    //   this.formCreate.groups = [];
    //   this.formCreate.middlePlatformUsers = [];
      this.formCreate.createTime = row.createTime;
      this.createVisible = true;
      this.$nextTick(() => {
        this.$refs.formCreate.clearValidate(); // 清除上次的校验结果
      });
    },
    // 显示新建规则弹窗
    onSetRule() {
      this.formCreate.groups = [];
      this.formCreate.middlePlatformUsers = [];
      this.formCreate.createTime = null;
      this.createVisible = true;
      this.$nextTick(() => {
        this.$refs.formCreate.clearValidate(); // 清除上次的校验结果
      });
    },
    // 取消新建规则弹窗
    createCancle() {
      this.createVisible = false;
    },
    // 关闭新建规则弹窗
    closeCreateDialog() {
      this.createVisible = false;
    },
    // 提交新建规则
    createSubmit() {
      var admitSubmit = false;
      this.$refs.formCreate.validate((valid) => {
        if (valid) {
          admitSubmit = true;
        } else {
          // 表单验证失败，提示用户
          this.$message.error('请检查填写的信息!');
        }
      });
      if (!admitSubmit) return;

      this.$refs.formCreate.validate(async valid => {
        if (valid && this.formCreate.createTime == null) {
          const res = await setOrderProcessRule(this.formCreate);
          if (res.success) {
            this.$message.success('创建成功！');
            this.createVisible = false;
            this.getRuleList();
          }
        }else if (valid && this.formCreate.createTime != null) {
          editOrderProcessRule(this.formCreate).then(response => {
            if (response.data) {
              this.$message.success("编辑成功!");
              this.createVisible = false;
              this.getRuleList();
            }
          });
        }
      });
    },
  }
};
</script>
<style lang="scss" scoped>
  .top {
    display: flex;
    margin-bottom: 10px;
  }
  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
  //解决下拉菜单多选由文字太长导致样式问题
  ::v-deep .el-select__tags-text {
    max-width: 60px;
  }
</style>
