<template>
    <div class="chat-window">
        <div class="chat-messages" v-if="AIConversations && AIConversations?.length > 0">
            <div v-for="(item, i) in AIConversations" :key="i" :class="[
                'message',
                item.authorRole == 'user' ? 'sent' : 'received',
            ]">
                <div v-if="item.authorRole == 'assistant'"
                    style="display: flex; align-items: self-start; max-width: 600px">
                    <div class="message-content">
                        <div v-html="renderMdText(item.content)"></div>
                    </div>
                </div>
                <div v-else style="display: flex; align-items: center; max-width: 600px">
                    <div class="message-content" style=" max-width: 600px">
                        <div v-html="renderMdText(item.content)"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import MarkdownIt from "markdown-it";
import "highlight.js/styles/default.css";
export default {
    data() {
        return {
            markdownRender: new MarkdownIt({
                html: true,
                linkify: true,
                typographer: true,
            }),
        }
    },
    props: {
        AIConversations: {
            type: Array,
            default: () => []
        }
    },
    mounted() { },
    methods: {
        renderMdText(text) {
            return this.markdownRender.render(text);
        },
    }
}
</script>

<style scoped lang="scss">
.chat-window {
    flex-direction: column;
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #fff;
    justify-content: center;
    max-height: 600px;
    overflow: auto;

    .chat-messages {
        flex: 1;
        padding: 10px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;

        &::-webkit-scrollbar {
            width: 8px;
        }

        /* 消息样式 */
        .message {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            max-width: 70%;
        }

        .message.received {
            align-self: flex-start;
        }

        .message.sent {
            align-self: flex-end;
        }

        .message-content {
            padding: 5px 15px;
            box-sizing: border-box;
            border-radius: 15px;
            background-color: #fff;
            color: #000;
            transition: background-color 0.3s;
            word-wrap: break-word;
            max-width: 600px;
            word-break: break-all;

            .persistent {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .message.received .message-content {
            border: 1px solid #ccc;
        }

        .message.sent .message-content {
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
        }
    }

}
</style>