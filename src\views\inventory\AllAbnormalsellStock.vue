<template>
    <my-container v-loading="pageLoading">
        <el-table :data="list" border style="width: 100%">
            <el-table-column prop="warehouseName" label="仓库" width="auto">
            </el-table-column>
            <el-table-column prop="sellStock" label="库存数" width="auto">
            </el-table-column>
        </el-table>
    </my-container>
</template>
<script>
import MyContainer from "@/components/my-container";
import {getAllAbnormalsellStock} from '@/api/inventory/abnormal'
export default {
    name: 'YunHanAdminAllAbnormalsellStock',
    components: { MyContainer },
    data() {
        return {
            that:this,
            list: [],
            pageLoading: false
        };
    },
    async mounted() {
    },
    methods: {
        async loadData({ goodsCode }) {
            this.pageLoading = true;
            console.log('产生怒', goodsCode)
            var params = {goodsCode};
            console.log('产生怒11', params)
            const res = await getAllAbnormalsellStock(params)
            this.pageLoading = false
            if (!res?.success) return
            const data = res.data
            this.list = data;
        } 
    },
};
</script>
<style lang="scss" scoped></style>