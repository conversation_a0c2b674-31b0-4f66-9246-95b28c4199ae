<template>
    <MyContainer>
        <template #header>
            <div class="top">
                <el-date-picker v-model="ListInfo.date" type="date" placeholder="选择日期" :clearable="false"
                    value-format="yyyy-MM-dd" class="publicCss" :picker-options="pickerOptionsDate" />
                <inputYunhan ref="goodsCodes" :inputt.sync="ListInfo.goodsCodes" v-model="ListInfo.goodsCodes"
                    placeholder="商品编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback($event, 1)" title="商品编码"
                    style="width: 200px;margin:0 5px 0 0;" class="publicCss">
                </inputYunhan>
                <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable
                    class="publicCss" />
                <inputYunhan ref="styleCodes" :inputt.sync="ListInfo.styleCodes" v-model="ListInfo.styleCodes"
                    placeholder="款式编码/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="500"
                    :maxlength="1000000" @callback="proCodeCallback($event, 2)" title="款式编码"
                    style="width: 200px;margin:0 5px 0 0;" class="publicCss">
                </inputYunhan>
                <el-select filterable v-model="ListInfo.companies" clearable placeholder="分公司" class="publicCss"
                    collapse-tags multiple style="width: 150px;">
                    <el-option label="义乌" value="义乌" />
                    <el-option label="南昌" value="南昌" />
                    <el-option label="武汉" value="武汉" />
                    <el-option label="深圳" value="深圳" />
                    <el-option label="其他" value="其他" />
                </el-select>
                <el-select v-model="ListInfo.titles" clearable filterable placeholder="请选择岗位" class="publicCss"
                    collapse-tags multiple>
                    <el-option v-for="item in positionList" :key="item.titleName" :label="item.titleName"
                        :value="item.titleName" />
                </el-select>
                <el-select v-model="ListInfo.brandIds" clearable filterable placeholder="请选择采购员" class="publicCss"
                    collapse-tags multiple>
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="ListInfo.deptIds" clearable filterable placeholder="请选择架构" class="publicCss"
                    collapse-tags multiple>
                    <el-option v-for="item in purchasegrouplist" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <!-- <chooseWareHouse v-model="ListInfo.wmsIds" class="publicCss" multiple :filter="sendWmsesFilter" /> -->
                <number-range :min.sync="ListInfo.wmsTurnoverDayMin" :max.sync="ListInfo.wmsTurnoverDayMax"
                    min-label="云仓周转天数 - 最小值" max-label="云仓周转天数 - 最大值" class="publicCss" :precision="2" />
                <number-range :min.sync="ListInfo.actualTheoryGoodsRateSurplusMin1"
                    :max.sync="ListInfo.actualTheoryGoodsRateSurplusMax1" min-label="理论实际占比差 - 最小值"
                    max-label="理论实际占比差 - 最大值" class="publicCss" :precision="2" />
                <el-select v-model="ListInfo.wmsTurnoverDayType" clearable placeholder="云仓周转天数<N+2" class="publicCss"
                    style="width: 110px;">
                    <el-option label="是" :value="1" />
                </el-select>
                <el-select v-model="ListInfo.hasRemark" clearable placeholder="是否备注" class="publicCss"
                    style="width: 120px;">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                </el-select>
                <el-select v-model="ListInfo.isNew" clearable filterable placeholder="新增编码" class="publicCss"
                    style="width: 120px;">
                    <el-option label="是" :value="true"></el-option>
                    <el-option label="否" :value="false"></el-option>
                </el-select>
                <div style="height: 28px;">
                    <el-cascader class="publicCss cascaderCss" style="width: 250px;height: 100%;" collapse-tags
                        clearable :options="options1" placeholder="请选择发货地"
                        :props="{ multiple: true, filterable: true, checkStrictly: true, value: 'label' }"
                        v-model="ListInfo.provinceCityDistricts1" filterable></el-cascader>
                </div>
                <el-select v-model="ListInfo.hasBom" clearable filterable placeholder="是否半成品" class="publicCss"
                    style="width: 120px;">
                    <el-option label="是" :value="true"></el-option>
                </el-select>
                <div>
                    <el-button type="primary" @click="getList('search')">搜索</el-button>
                    <el-button type="primary" size="mini" :disabled="isExport" @click="exportProps">导出</el-button>
                    <el-button type="primary" size="mini" @click="createOrder" v-throttle="1000">生成云仓采购单</el-button>
                    <el-button type="primary" size="mini" @click="batchRemark" v-throttle="1000">批量备注</el-button>
                </div>
                <div style="display: flex;justify-content: end;flex: 1;padding-right: 25px;align-items: center;">
                    <!-- <el-radio-group v-model="radio" @change="radioChange" style="margin-right: 10px;">
                            <el-radio label="全部">全部</el-radio>
                            <el-radio label="云仓需补货">云仓需补货</el-radio>
                            <el-radio label="本仓需补货">本仓需补货</el-radio>
                        </el-radio-group> -->
                    <el-checkbox-group v-model="checkList" style="margin-right: 10px;" @change="radioChange">
                        <el-checkbox label="云仓需补货">云仓需补货</el-checkbox>
                        <el-checkbox label="本仓需补货">本仓需补货</el-checkbox>
                    </el-checkbox-group>
                    <div>
                        <el-button type="primary" size="mini" @click="preallocation">预调拨</el-button>
                    </div>
                </div>
            </div>
        </template>
        <vxetablebase ref="table" id="20241201103911" :loading="loading" :that="that" :is-index="true" :hasexpand="true"
            :tablefixed="true" :has-seq="false" :border="true" :table-data="data.list" :table-cols="tableCols"
            :condition="ListInfo" :is-selection="false" :is-select-column="true" :is-index-fixed="false"
            class="mainTable" style="width: 100%; margin: 0;" height="100%" :showsummary="data.summary ? true : false"
            :summaryarry="data.summary" :showTrendChart="false" @sortchange="sortchange" :cstmExportFunc="onExport"
            @onTrendChart="trendChart" :showheaderoverflow="false" :enabledArr="enabledArr"
            :customExportColumn="customExportColumn" @cellStyle="cellStyle" cellStyle @select="select"
            @footerCellStyle="footerCellStyle">
            <template slot="right">
                <vxe-column title="操作" width="120" fixed="right">
                    <template #default="{ row, $index }">
                        <div style="display: flex;justify-content: center;">
                            <el-tooltip class="item" effect="dark" placement="top-start" :content="row.remark">
                                <div style="margin-right: 10px;">
                                    <el-button type="text" @click="logDetails(row)">备注</el-button>
                                    <i
                                        :class="['el-icon-star-on', row.remark || row.remarkPicture ? 'isActive' : '']"></i>
                                </div>
                            </el-tooltip>
                            <el-button type="text" @click="() => {
                                isSummary = false
                                $refs.table.onTrendChart(row)
                            }">趋势图</el-button>
                        </div>
                    </template>
                    <template #footer="{ row, _columnIndex }">
                        <el-button type="text" @click="() => {
                            isSummary = true
                            $refs.table.onTrendChart(data.list[0])
                        }">趋势图</el-button>
                    </template>
                </vxe-column>
            </template>
            <template #wmsLastInTransitDay_header="{ column }">
                <div class="headerSolt">
                    <div @click="headerSortable('wmsLastInTransitDay')">
                        {{ column.title }}
                        <span class="headerSort">
                            <i
                                :class="['el-icon-caret-top', isActiveOderBy('wmsLastInTransitDay', 'asc') ? 'iconcolor' : 'startcol']" />
                            <i
                                :class="['el-icon-caret-bottom', isActiveOderBy('wmsLastInTransitDay', 'descending') ? 'iconcolor' : 'startcol']" />
                        </span>
                    </div>
                    <div @click="headerSortable('wmsAvgInTransitDay')">
                        云仓平均在途时长
                        <span class="headerSort">
                            <i
                                :class="['el-icon-caret-top', isActiveOderBy('wmsAvgInTransitDay', 'asc') ? 'iconcolor' : 'startcol']" />
                            <i
                                :class="['el-icon-caret-bottom', isActiveOderBy('wmsAvgInTransitDay', 'descending') ? 'iconcolor' : 'startcol']" />
                        </span>
                    </div>
                </div>
            </template>
            <template #wmsLastInTransitDay="{ row }">
                <div class="headerSolt">
                    <div>{{ row.wmsLastInTransitDay }}</div>
                    <div>{{ row.wmsAvgInTransitDay }}</div>
                </div>
            </template>
            <template #wmsLastInTransitDay_footer>
                <div class="headerSolt">
                    <div>{{ data.summary.wmsLastInTransitDay_sum }}</div>
                    <div>{{ data.summary.wmsAvgInTransitDay_sum }}</div>
                </div>
            </template>
            <template #brandWmsLastInTransitDay_header="{ column }">
                <div style="display:flex;align-items: center;justify-content: space-between;padding-left:5px">
                    <el-tooltip class="item" effect="dark" content="取值计划采购建议最近在途时长" placement="top-start">
                        <i class="el-icon-question"></i>

                    </el-tooltip>
                    <div class="headerSolt">
                        <div @click="headerSortable('brandWmsLastInTransitDay')">
                            {{ column.title }}
                            <span class="headerSort">
                                <i
                                    :class="['el-icon-caret-top', isActiveOderBy('brandWmsLastInTransitDay', 'asc') ? 'iconcolor' : 'startcol']" />
                                <i
                                    :class="['el-icon-caret-bottom', isActiveOderBy('brandWmsLastInTransitDay', 'descending') ? 'iconcolor' : 'startcol']" />
                            </span>
                        </div>
                        <div @click="headerSortable('brandWmsAvgInTransitDay')">
                            品牌仓平均在途
                            <span class="headerSort">
                                <i
                                    :class="['el-icon-caret-top', isActiveOderBy('brandWmsAvgInTransitDay', 'asc') ? 'iconcolor' : 'startcol']" />
                                <i
                                    :class="['el-icon-caret-bottom', isActiveOderBy('brandWmsAvgInTransitDay', 'descending') ? 'iconcolor' : 'startcol']" />
                            </span>
                        </div>
                    </div>
                </div>
            </template>
            <template #brandWmsLastInTransitDay="{ row }">
                <div class="headerSolt">
                    <div>{{ row.brandWmsLastInTransitDay }}</div>
                    <div>{{ row.brandWmsAvgInTransitDay }}</div>
                </div>
            </template>
            <template #brandWmsLastInTransitDay_footer>
                <div class="headerSolt">
                    <div>{{ data.summary.brandWmsLastInTransitDay_sum }}</div>
                    <div>{{ data.summary.brandWmsAvgInTransitDay_sum }}</div>
                </div>
            </template>

            <!-- <template #inWmsLackQty1_header>
                <div class="headerSolt">
                    <div @click="headerSortable('inWmsLackQty1')">
                        本仓缺货数1
                        <span class="headerSort">
                            <i
                                :class="['el-icon-caret-top', isActiveOderBy('inWmsLackQty1', 'asc') ? 'iconcolor' : 'startcol']" />
                            <i
                                :class="['el-icon-caret-bottom', isActiveOderBy('inWmsLackQty1', 'descending') ? 'iconcolor' : 'startcol']" />
                        </span>
                    </div>
                    <div @click="headerSortable('inWmsLackQty2')">
                        本仓缺货数2
                        <span class="headerSort">
                            <i
                                :class="['el-icon-caret-top', isActiveOderBy('inWmsLackQty2', 'asc') ? 'iconcolor' : 'startcol']" />
                            <i
                                :class="['el-icon-caret-bottom', isActiveOderBy('inWmsLackQty2', 'descending') ? 'iconcolor' : 'startcol']" />
                        </span>
                    </div>
                </div>
            </template>
            <template #inWmsLackQty1="{ row }">
                <div class="headerSolt">
                    <div>{{ formatNum(row.inWmsLackQty1) }}</div>
                    <div>{{ formatNum(row.inWmsLackQty2) }}</div>
                </div>
            </template>
            <template #inWmsLackQty1_footer>
                <div class="headerSolt">
                    <div>{{ formatNum(data.summary.inWmsLackQty1_sum) }}</div>
                    <div>{{ formatNum(data.summary.inWmsLackQty2_sum) }}</div>
                </div>
            </template> -->
            <template #wmsTurnoverDay="{ row }">
                <div>
                    {{ row.wmsTurnoverDay !== null ? String(row.wmsTurnoverDay.toFixed(1)) : row.wmsTurnoverDay }}
                </div>
            </template>
            <template #inWmsTurnoverDay="{ row }">
                <div>
                    {{ row.inWmsTurnoverDay !== null ? String(row.inWmsTurnoverDay.toFixed(1)) : row.inWmsTurnoverDay }}
                </div>
            </template>
            <template #inWmsSellStock="{ row }">
                <el-button type="text" @click="wmsAllocate(row, 0)" style="text-decoration: underline;">{{
                    row.inWmsSellStock }}</el-button>
            </template>
            <template #wmsSellStock="{ row }">
                <el-button type="text" @click="wmsAllocate(row, 1)" style="text-decoration: underline;">{{
                    row.wmsSellStock }}</el-button>
            </template>
            <template #brandWmsSellStock="{ row }">
                <el-button type="text" @click="wmsAllocate(row, 0)" style="text-decoration: underline;">{{
                    row.brandWmsSellStock }}</el-button>
            </template>
            <template #goodsCode="{ row }">
                <div :style="{ color: row.hasBom ? '#409eff' : '', cursor: row.hasBom ? 'pointer' : '' }"
                    @click="openProductDetails(row)">{{ row.goodsCode }}
                </div>
            </template>

            <template #inWmsMonthLackCount="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['inWmsMonthLackCount', 'fullWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }">{{
                    row.inWmsMonthLackCount }}</el-button>
            </template>
            <template #fullWmsMonthLackCount="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['inWmsMonthLackCount', 'fullWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }">{{
                    row.fullWmsMonthLackCount }}</el-button>
            </template>
            <template #wmsMonthLackCount="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['inWmsMonthLackCount', 'fullWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }">{{
                    row.wmsMonthLackCount }}</el-button>
            </template>
            <template #brandWmsMonthLackCount="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['inWmsMonthLackCount', 'fullWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }">{{
                    row.brandWmsMonthLackCount }}</el-button>
            </template>
            <template #wmsTheoryRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsRate'])
                }">{{
                    row.wmsTheoryRate ?
                        (row.wmsTheoryRate *
                            100).toFixed(1) + '%' :
                        row.wmsTheoryRate }}</el-button>
            </template>
            <template #wmsTheoryRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.wmsTheoryRate_sum) }}
                </div>
            </template>
            <template #brandWmsRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsRate'])
                }">{{
                    row.brandWmsRate ?
                        (row.brandWmsRate *
                            100).toFixed(1) + '%' :
                        row.brandWmsRate }}</el-button>
            </template>
            <template #brandWmsRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.brandWmsRate_sum) }}
                </div>
            </template>
            <template #brandWmsTheoryRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['brandWmsActualRate', 'brandWmsActualGoodsRate', 'brandWmsTheoryRate'])
                }">{{
                    row.brandWmsTheoryRate ?
                        (row.brandWmsTheoryRate *
                            100).toFixed(1) + '%' :
                        row.brandWmsTheoryRate }}</el-button>
            </template>
            <template #brandWmsTheoryRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['brandWmsActualRate', 'brandWmsActualGoodsRate', 'brandWmsTheoryRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.brandWmsTheoryRate_sum) }}
                </div>
            </template>
            <!-- 云仓订单实际占比 -->
            <template #wmsActualRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualRate'])
                }">{{
                    row.wmsActualRate ?
                        (row.wmsActualRate *
                            100).toFixed(1) + '%' :
                        row.wmsActualRate }}</el-button>
            </template>
            <template #wmsActualRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.wmsActualRate_sum) }}
                </div>
            </template>
            <!-- 品牌仓实际订单占比 -->
            <template #brandWmsActualRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualRate'])
                }">{{
                    row.brandWmsActualRate ?
                        (row.brandWmsActualRate *
                            100).toFixed(1) + '%' :
                        row.brandWmsActualRate }}</el-button>
            </template>
            <template #brandWmsActualRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.brandWmsActualRate_sum) }}
                </div>
            </template>
            <template #wmsActualGoodsRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualGoodsRate'])
                }">{{
                    row.wmsActualGoodsRate ?
                        (row.wmsActualGoodsRate *
                            100).toFixed(1) + '%' :
                        row.wmsActualGoodsRate }}</el-button>
            </template>
            <template #wmsActualGoodsRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualGoodsRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.wmsActualGoodsRate_sum) }}
                </div>
            </template>
            //品牌仓实际销量占比
            <template #brandWmsActualGoodsRate="{ row }">
                <el-button type="text" @click="() => {
                    isSummary = false
                    wmsTheoryRateChart(row, ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualGoodsRate'])
                }">{{
                    row.brandWmsActualGoodsRate ?
                        (row.brandWmsActualGoodsRate *
                            100).toFixed(1) + '%' :
                        row.brandWmsActualGoodsRate }}</el-button>
            </template>
            <template #brandWmsActualGoodsRate_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['wmsActualRate', 'wmsActualGoodsRate', 'wmsTheoryRate', 'brandWmsActualGoodsRate'])
                }" style="cursor: pointer;color: red;">
                    {{ formatRate(data.summary.brandWmsActualGoodsRate_sum) }}
                </div>
            </template>
            <template #fullWmsMonthLackCount_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['fullWmsMonthLackCount', 'inWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }" style="cursor: pointer;color: red;">
                    {{ formatNum(data.summary.fullWmsMonthLackCount_sum) }}
                </div>
            </template>
            <template #inWmsMonthLackCount_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['fullWmsMonthLackCount', 'inWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }" style="cursor: pointer;color: red;">
                    {{ formatNum(data.summary.inWmsMonthLackCount_sum) }}
                </div>
            </template>
            <template #wmsMonthLackCount_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['fullWmsMonthLackCount', 'inWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }" style="cursor: pointer;color: red;">
                    {{ formatNum(data.summary.wmsMonthLackCount_sum) }}
                </div>
            </template>
            <template #brandWmsMonthLackCount_footer="{ row }">
                <div @click="() => {
                    isSummary = true
                    wmsTheoryRateChart(data.list[0], ['fullWmsMonthLackCount', 'inWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount'])
                }" style="cursor: pointer;color: red;">
                    {{ formatNum(data.summary.brandWmsMonthLackCount_sum) }}
                </div>
            </template>
            <template #brandWmsLackRate_footer="{ row }">
                <div>
                    {{ data.summary.brandWmsLackRate_sum !== null ? data.summary.brandWmsLackRate_sum.toFixed(1) + '%'
                        : data.summary.brandWmsLackRate_sum }}
                </div>
            </template>
            <template #inWmsSellStockTunDay="{ row }">
                <div>
                    {{ row.inWmsSellStockTunDay !== null ? row.inWmsSellStockTunDay.toFixed(1) :
                        row.inWmsSellStockTunDay }}
                </div>
            </template>
            <template #wmsSellStockTunDay="{ row }">
                <div>
                    {{ row.wmsSellStockTunDay !== null ? row.wmsSellStockTunDay.toFixed(1) :
                        row.wmsSellStockTunDay }}
                </div>
            </template>
            <template #ytdOrder="{ row }">
                <el-button type="text" @click="openYtdOrderCharts(row, false)">{{ row.ytdOrder
                }}</el-button>
            </template>
            <template #tdyHourQty="{ row }">
                <el-button type="text" @click="openYtdOrderCharts(row, true)">{{ row.tdyHourQty
                }}</el-button>
            </template>
        </vxetablebase>
        <template #footer>
            <my-pagination ref="pager" :total="data.total" @page-change="Pagechange" @size-change="Sizechange" />
        </template>

        <el-drawer :title="chatProp.data._title" :visible.sync="chatProp.chatDialog" size="90%"
            :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        }, row, prop)
                        " />
                <buschar v-if="!chatProp.chatLoading" :analysis-data="chatProp.data"
                    :thisStyle="{ width: '100%', height: '690px', 'box-sizing': 'border-box', 'line-height': '360px' }" />
            </div>
            <div v-else v-loading="chatProp.chatLoading" />
        </el-drawer>

        <el-drawer :title="chatProp1.data._title" :visible.sync="chatProp1.chatDialog" size="90%"
            :close-on-click-modal="false" direction="btt">
            <div v-if="!chatProp1.chatLoading">
                <el-date-picker v-model="chatProp1.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions" style="margin: 10px" @change="
                        trendChart1({
                            ...chatPropOption,
                            startDate: $event[0],
                            endDate: $event[1],
                        })
                        " />
                <buschar v-if="!chatProp1.chatLoading" :analysis-data="chatProp1.data"
                    :thisStyle="{ width: '100%', height: '690px', 'box-sizing': 'border-box', 'line-height': '360px' }" />
            </div>
            <div v-else v-loading="chatProp1.chatLoading" />
        </el-drawer>

        <el-dialog title="预调拨" :visible.sync="preallocationVisible" width="70%" v-dialogDrag
            :close-on-click-modal="false">
            <preallocationPageIndex v-if="preallocationVisible" @close="close" @getList="getList" />
        </el-dialog>

        <el-dialog :title="title" :visible.sync="wmsAllocateVisible" width="75%" v-dialogDrag
            :close-on-click-modal="false">
            <wmsAllocatePage v-if="wmsAllocateVisible" :query="query" @close="close" @getList="getList" :type="type" />
        </el-dialog>

        <el-dialog title="备注" :visible.sync="proLogDetailsVisible" width="50%" v-dialogDrag
            :close-on-click-modal="false">
            <proLogDetails v-if="proLogDetailsVisible" :query="query" @close="close" @getList="getList"
                :isBatch="isBatchRemark" :batchProps="selectList" />
        </el-dialog>

        <el-dialog title="数据时间" :visible.sync="propHistoryRecordVisible" width="60%" v-dialogDrag
            :close-on-click-modal="false">
            <propHistoryRecord v-if="propHistoryRecordVisible" :query="query" @close="close" @getList="getList" />
        </el-dialog>

        <el-dialog :title="inventoryDetailsTitle" :visible.sync="inventoryDetailsVisible" width="46%" v-dialogDrag
            :close-on-click-modal="false">
            <inventoryDetails v-if="inventoryDetailsVisible" :query="query" @close="close" @getList="getList"
                :type="type" />
        </el-dialog>

        <el-dialog :title="productTitle" :visible.sync="productVisible" width="80%" v-dialogDrag
            :close-on-click-modal="false">
            <finishedOrSemiFinished v-if="productVisible" :query="query" />
        </el-dialog>

        <el-drawer :title="ytdOrderProp.data._title" :visible.sync="ytdOrderProp.chatDialog" size="90%"
            :close-on-click-modal="false" direction="btt">
            <div>
                <div style="display: flex;margin: 10px 0;">
                    <el-select v-model="ytdOrderInfo.isHour" placeholder="请选择" style="width: 200px;margin:0 10px;"
                        @change="openYtdOrderCharts(row, ytdOrderInfo.isHour, false)">
                        <el-option label="小时" :value="true" />
                        <el-option label="天" :value="false" />
                    </el-select>
                    <el-date-picker v-if="ytdOrderInfo.isHour" v-model="ytdOrderInfo.startDate" type="date"
                        placeholder="选择日期" :clearable="false" value-format="yyyy-MM-dd" class="publicCss"
                        @change="openYtdOrderCharts(row, true, true)" style="width: 200px;margin:0 10px 0 0;" />
                    <dateRange v-else :startDate.sync="ytdOrderInfo.startDate" :endDate.sync="ytdOrderInfo.endDate"
                        @change="openYtdOrderCharts(row, false, true)" class="publicCss" :clearable="false"
                        style="width: 200px;margin:0 10px 0 0;" />
                </div>
                <buschar v-if="!ytdOrderProp.chatLoading" :analysis-data="ytdOrderProp.data"
                    :thisStyle="{ width: '100%', height: '630px', 'box-sizing': 'border-box', 'line-height': '360px' }" />
            </div>
        </el-drawer>

    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, platformlist, pickerOptionsDate } from '@/utils/tools'
import numberRange from "@/components/number-range/index.vue";
import request from '@/utils/request'
import dayjs from 'dayjs'
import dateRange from "@/components/date-range/index.vue";
import buschar from "@/components/Bus/buschar";
import { download } from "@/utils/download";
import inputYunhan from "@/components/Comm/inputYunhan";
const api = '/api/verifyOrder/SaleItems/CodeStatBrand/'
const api1 = '/api/verifyOrder/OrderGoods/DurationStat/GetTrendChart'
import { mergeTableCols } from '@/utils/getCols'
import { getAllProBrand, getBianManPositionListV2 } from '@/api/inventory/warehouse'
import { getPurchaseNewPlanTurnDayDeptList } from '@/api/inventory/purchaseordernew'
import chooseWareHouse from "@/components/choose-wareHouse/index.vue";
import { getUserInfo } from "@/api/operatemanage/productalllink/alllink";
import propHistoryRecord from './propHistoryRecord.vue'
import inventoryDetails from './inventoryDetails.vue'
import finishedOrSemiFinished from './finishedOrSemiFinished.vue'
import { pagePurchaseNewPlan2, } from "@/api/inventory/purchaseordernew";
import decimal from "@/utils/decimalToFixed";
import decimal1 from "@/utils/decimal";
import { savePurchaseManual } from '@/api/inventory/purchase'
import preallocationPageIndex from './preallocationPageIndex.vue'
import wmsAllocatePage from './wmsAllocatePage.vue'
import proLogDetails from './proLogDetails.vue'
import {
    GetProvinceCityDistrict
} from '@/api/inventory/purchase'
const orderByList = [
    // { isAsc: 'descending', label: '应有库存1', orderBy: 'inWmsShoudInv1', },
    // { isAsc: 'descending', label: '应有库存2', orderBy: 'inWmsShoudInv2', },
    // { isAsc: 'descending', label: '应有库存1', orderBy: 'wmsShoudInv1', },
    // { isAsc: 'descending', label: '应有库存2', orderBy: 'wmsShoudInv2', },
    // { isAsc: 'descending', label: '缺货数1', orderBy: 'wmsLackQty1', },
    // { isAsc: 'descending', label: '缺货数2', orderBy: 'wmsLackQty2', },
    // { isAsc: 'descending', label: '库存占比1', orderBy: 'wmsLackRate1', },
    // { isAsc: 'descending', label: '库存占比2', orderBy: 'wmsLackRate2', },
    // { isAsc: 'descending', label: '本仓缺货数1', orderBy: 'inWmsLackQty1', },
    // { isAsc: 'descending', label: '本仓缺货数2', orderBy: 'inWmsLackQty2', },
    { isAsc: 'descending', label: '最近在途时长', orderBy: 'wmsLastInTransitDay', },
    { isAsc: 'descending', label: '平均在途时长', orderBy: 'wmsAvgInTransitDay', },
]
export default {
    name: "scanCodePage",
    components: {
        MyContainer,
        vxetablebase,
        dateRange,
        buschar,
        numberRange,
        inputYunhan,
        chooseWareHouse,
        preallocationPageIndex,
        wmsAllocatePage,
        proLogDetails,
        propHistoryRecord,
        inventoryDetails,
        finishedOrSemiFinished
    },
    data() {
        return {
            orderByList,
            api,
            api1,
            platformlist,
            pickerOptionsDate,
            that: this,
            ListInfo: {
                date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
                currentPage: 1,
                pageSize: 50,
                orderBy: 'wmsLackRate1',
                isAsc: true,
                brandIds: [],
                summarys: [],
                inWmsLack: null,
                wmsLack: null,
                isNew: null,
                isBrand: true
            },
            chatProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: {}, // 趋势图数据
            },
            // radio: '全部',
            checkList: [],
            data: {},
            timeRanges: [],
            tableCols: [],
            tableData: [],
            total: 0,
            loading: true,
            pickerOptions,
            isExport: false,
            purchasegrouplist: [],
            brandlist: [],
            brandlist1: [],
            positionList: [],
            isSummary: false,
            activeSort: {
                prop: '',
                order: 'descending'
            },
            enabledArr: [],
            selectList: [],
            preallocationVisible: false,
            title: '',
            wmsAllocateVisible: false,
            proLogDetailsVisible: false,
            propHistoryRecordVisible: false,
            inventoryDetailsVisible: false,
            inventoryDetailsTitle: '',
            productVisible: false,
            productTitle: '',
            query: {
                goodsCode: '',
                brandId: ''
            },
            type: null,
            chatProp1: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: {}, // 趋势图数据
            },
            isBatchRemark: false,
            customExportColumn: {
                collectColumn: [],
            },
            prop: '',
            row: {},
            options1: [],
            ytdOrderProp: {
                chatDialog: false, // 趋势图弹窗
                chatTime: null, // 趋势图时间
                chatLoading: true, // 趋势图loading
                data: {}, // 趋势图数据
            },
            ytdOrderInfo: {
                goodsCode: null,
                startDate: null,
                endDate: null,
                isHour: false,
                isSequential: false,
                type: '',
                hourRange: '',
            },
        }
    },
    async mounted() {
        await this.init()
        await this.getUser()
        await this.getCol();
        await this.getList()
    },
    methods: {
        async commonChart(row) {
            this.$refs.table.onTrendChart(row)
        },
        async openYtdOrderCharts(row, isHour, isOperation) {
            this.row = row
            this.ytdOrderInfo.goodsCode = row.goodsCode
            this.ytdOrderInfo.isHour = isHour
            if (!isOperation) {
                if (this.ytdOrderInfo.isHour) {
                    this.ytdOrderInfo.startDate = dayjs().format('YYYY-MM-DD')
                    this.ytdOrderInfo.endDate = this.ytdOrderInfo.startDate
                    this.ytdOrderInfo.isSequential = true
                } else {
                    this.ytdOrderInfo.startDate = dayjs().subtract(29, 'day').format('YYYY-MM-DD')
                    this.ytdOrderInfo.endDate = dayjs().format('YYYY-MM-DD')
                    this.ytdOrderInfo.isSequential = false
                }
            } else if (this.ytdOrderInfo.isHour) {
                this.ytdOrderInfo.endDate = this.ytdOrderInfo.startDate
            }
            const res = {
                isHour: this.ytdOrderInfo.isHour,
                isSequential: this.ytdOrderInfo.isSequential,
                startDate: this.ytdOrderInfo.startDate,
                endDate: this.ytdOrderInfo.endDate,
                key: [
                    "GoodsCode"
                ],
                dateField: "Date",
                filter: {
                    logic: "And",
                    filters: [
                        {
                            field: "goodsCode",
                            operator: "Eq",
                            value: this.ytdOrderInfo.goodsCode
                        },
                        {
                            field: "date",
                            operator: "GreaterThanOrEqual",
                            value: this.ytdOrderInfo.startDate
                        },
                        {
                            field: "date",
                            operator: "LessThanOrEqual",
                            value: this.ytdOrderInfo.endDate
                        }
                    ]
                },
                fields: [
                    {
                        field: "OrderCount",
                        label: "订单数"
                    },

                    {
                        field: "GoodsQty",
                        label: "销量"
                    }
                ],
                condition: {
                    GoodsCode: this.ytdOrderInfo.goodsCode
                }
            }
            this.ytdOrderProp.chatLoading = true
            const { data, success } = await request.post(this.api1, res)
            if (success) {
                data.series.forEach(item => {
                    item.type = 'bar'
                })
                data.title = ''
                this.ytdOrderProp.data = data
                this.ytdOrderProp.data._title = `${row.goodsCode} - 趋势图`
            }
            this.ytdOrderProp.chatLoading = false
            this.ytdOrderProp.chatDialog = true
        },
        async getGetProvinceCityDistrict() {
            const { data, success } = await GetProvinceCityDistrict();
            if (!success) {
                return;
            }
            this.options1 = data ? data : [];

        },
        wmsTheoryRateChart(row, prop, type) {
            this.prop = prop;
            var cols = [];
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1);
                    });
                } else {
                    cols.push(item);
                }
            });
            cols = [...cols, ...this.enabledArr];
            var dateCol = cols.find(a => a.isSeriesDate);
            var keys = cols.filter(a => a.isSeriesKey).map(a => a.prop);
            var fields = cols.filter(a => a.isSeriesField && prop.includes(a.prop)).sort((a, b) => a.columnIndex - b.columnIndex).map(a => {
                let label1 = a.format && (a.format.indexOf('%') == a.format.length - 1) ? a.label + '(%)' : a.label;
                if (a.mergeName) {
                    label1 = a.mergeName + ' - ' + label1
                }
                return {
                    field: a.prop,
                    label: label1,
                    isRate: a.format && (a.format.indexOf('%') == a.format.length - 1) ? true : false
                };
            })
            let filter = { logic: 'And', filters: [] };
            for (const p in row) {
                if (keys.indexOf(p) > -1) {
                    filter.filters.push({ field: p, operator: 'Eq', value: row[p] })
                }
            }
            var date = new Date(row[dateCol.prop]);
            var option = { key: keys, dateField: dateCol.prop, fields: fields, filter: filter, date: date, condition: this.ListInfo }
            if (type) {
                return option
            }
            this.trendChart(option, row, prop)
        },
        radioChange(e) {
            if (e.length == 1 && e.includes('本仓需补货')) {
                this.ListInfo.inWmsLack = true
                this.ListInfo.wmsLack = false
                this.ListInfo.orderBy = 'inWmsTurnoverDay'
                this.ListInfo.isAsc = true
            } else if (e.length == 1 && e.includes('云仓需补货')) {
                this.ListInfo.inWmsLack = false
                this.ListInfo.wmsLack = true
                this.ListInfo.orderBy = 'wmsTurnoverDay'
                this.ListInfo.isAsc = true
            } else if (e.length == 2) {
                this.ListInfo.inWmsLack = true
                this.ListInfo.wmsLack = true
                this.ListInfo.orderBy = null
                this.ListInfo.isAsc = true
            } else if (e.length == 0) {
                this.ListInfo.inWmsLack = null
                this.ListInfo.wmsLack = null
                this.ListInfo.orderBy = null
                this.ListInfo.isAsc = true
            }
            this.getList()
        },
        async openProductDetails(row) {
            if (!row.hasBom) return
            this.productTitle = `${row.goodsCode} - 成品半成品`
            this.query = row
            this.productVisible = true
        },
        async batchRemark() {
            if (this.selectList.length == 0) return this.$message.error('请选择数据')
            const res = this.selectList[0]
            const sameStyleCode = this.selectList.every(item => item.styleCode === res.styleCode)
            if (!sameStyleCode) return this.$message.error('所选数据存在多【款式编码】，请核实')
            const flag = this.selectList.every(item =>
                item.company === res.company &&
                item.deptName === res.deptName &&
                item.title === res.title &&
                item.brandId === res.brandId
            )
            if (!flag) return this.$message.error('所选数据存在多【公司】、【架构】、【岗位】、【采购】，请核实')
            this.isBatchRemark = true
            this.proLogDetailsVisible = true
        },
        async sale30(row) {
            const { data, success } = await request.post(`/api/verifyOrder/SaleItems/Sale30/GetColumns`)
            if (!success) return
            var cols = [];
            data.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1);
                    });
                } else {
                    cols.push(item);
                }
            });
            var dateCol = cols.find(a => a.isSeriesDate);
            var keys = cols.filter(a => a.isSeriesKey).map(a => a.prop);
            var fields = cols.filter(a => a.isSeriesField).sort((a, b) => a.columnIndex - b.columnIndex).map(a => {
                let label1 = a.format && (a.format.indexOf('%') == a.format.length - 1) ? a.label + '(%)' : a.label;
                if (a.mergeName) {
                    label1 = a.mergeName + ' - ' + label1
                }
                return {
                    field: a.prop,
                    label: label1,
                    isRate: a.format && (a.format.indexOf('%') == a.format.length - 1) ? true : false
                };
            })
            let filter = { logic: 'And', filters: [] };
            for (const p in row) {
                if (keys.indexOf(p) > -1) {
                    filter.filters.push({ field: p, operator: 'Eq', value: row[p] })
                }
            }
            var date = new Date(row.date)
            date.setDate(date.getDate() + 1);
            var option = { key: keys, dateField: dateCol.prop, fields: fields, filter: filter, date: date, }
            this.trendChart1(option)
        },
        async trendChart1(option) {
            var endDate = null;
            var startDate = null;
            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });
            if (this.isSummary) {
                option.filter.filters = option.filter.filters.filter((item) => option.key.indexOf(item.field) <= -1);
            }
            option.key = !this.isSummary ? option.key : [];
            option.startDate = startDate;
            option.endDate = endDate;
            this.chatProp1.chatTime = [startDate, endDate];
            this.chatProp1.chatLoading = true;
            const { data, success } = await request.post(`/api/verifyOrder/SaleItems/Sale30/GetTrendChart`, option);
            if (success) {
                data.series.forEach(item => {
                    item.type = 'bar'
                });
                if (option.key?.length > 0) {
                    data._title = data.title + ' - 趋势图'
                } else {
                    data._title = '汇总趋势图'
                }
                data.title = null
                this.chatProp1.data = data;
            }
            this.chatProp1.chatLoading = false;
            this.chatProp1.chatDialog = true;
            this.chatPropOption = option;
        },
        openPropsHistory(row) {
            this.query = row
            this.propHistoryRecordVisible = true
        },
        close() {
            this.preallocationVisible = false
            this.proLogDetailsVisible = false
            this.wmsAllocateVisible = false
            this.propHistoryRecordVisible = false
            this.selectList = []
        },
        logDetails(row) {
            this.isBatchRemark = false
            this.query = row
            this.proLogDetailsVisible = true
        },
        openInventoryDetails(row, val) {
            this.inventoryDetailsTitle = `${row.goodsCode}-${val == 0 ? '本仓' : '云仓'}-库存明细`
            this.query = row
            this.type = val
            this.inventoryDetailsVisible = true
        },
        wmsAllocate(row, val) {
            this.query = row
            this.title = `${row.goodsCode}-${val == 0 ? '本调云' : '云调本'}`
            this.type = val
            this.wmsAllocateVisible = true
        },
        preallocation(row) {
            this.preallocationVisible = true
        },
        select(val) {
            this.selectList = val
        },
        async createOrder() {
            if (this.selectList.length == 0) return this.$message.error('请选择数据')
            const res = this.selectList[0]
            const sameWms = this.selectList.every(item => item.wmsId === res.wmsId)
            if (!sameWms) return this.$message.error('所选数据存在多【应去云仓】，请核实')
            const flag = this.selectList.every(item =>
                item.company === res.company &&
                item.deptName === res.deptName &&
                item.title === res.title &&
                item.brandId === res.brandId
            )
            if (!flag) return this.$message.error('所选数据存在多【公司】、【架构】、【岗位】、【采购】，请核实')
            let dtlGoods = []
            let supplier = {
                name: '',
                supplier_id: 0
            }
            //获取供应商
            const goodsCodes = this.selectList.map(item => item['goodsCode'])
            const { data: data3, success } = await request.post(`${this.api}LastSupplier`, goodsCodes);
            if (success && data3) {
                supplier = data3
            }
            //从计划采购建议查询包装数量,商品编码,商品名称,成本价,计算总金额
            const { data: data2 } = await pagePurchaseNewPlan2({
                currentPage: 1,
                pageSize: 100000,
                orderBy: null,
                isAsc: false,
                goodsCode: this.selectList.map(item => item['goodsCode']).join(','),
                warehouses: `${this.selectList[0].wmsId}`
            })
            if (data2) {
                dtlGoods = data2.list.map(item => {
                    const res = this.selectList.find(item1 => item1.goodsCode == item.goodsCode)
                    const count = Math.abs(res.wmsLackQty2)
                    return {
                        goodsCode: item.goodsCode,
                        goodsName: item.goodsName,
                        price: item.cost,
                        count,
                        amount: decimal1(count, item.cost, 2, "*"),
                        isNotified: false,
                        packCount: item.packCount
                    }
                })
            } else {
                return this.$message.error('未获取到匹配的数据')
            }
            const sumbitPar = {
                warehouse: this.selectList[0].wmsId,
                supplier_id: supplier.supplier_id ? supplier.supplier_id : 0,
                supplier: supplier.name ? supplier.name : '',
                dtlGoods,
            }
            const { success: success1 } = await savePurchaseManual(sumbitPar)
            if (success1) {
                this.$message.success('生成采购单成功，可前往采购单管理页面查看')
                this.getList()
            }
        },
        async getUser() {
            const map = [
                {
                    label: '采购1组',
                    name: ['陈兵', '汪立文', '余福盛'],
                    value: 847145224
                },
                {
                    label: '采购2组',
                    name: ['骆明'],
                    value: 847424184
                },
                {
                    label: '采购3组',
                    name: ['罗众章', '梅佳萌'],
                    value: 847548248
                },
                {
                    label: '采购4组',
                    name: ['邹泽兵'],
                    value: 853666070
                },
                {
                    label: '采购一组',
                    name: ['周红梅'],
                    value: 929876873
                },
                {
                    label: '采购二组',
                    name: ['万志强'],
                    value: 930349042
                }
            ]
            const { data, success } = await getUserInfo()
            if (success && data && data.brandId) {
                if (data.nickName?.includes('熊涛')) return
                const dept = map.find(item => item.name.includes(data.nickName))?.label
                if (dept) {
                    const value = this.purchasegrouplist.find(item => item.label.includes(dept))?.value
                    this.ListInfo.deptIds = [value]
                } else {
                    this.ListInfo.brandIds.push(String(data.brandId))
                }
            }
        },
        footerCellStyle(row, callback) {
            let cols = []
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1)
                    })
                } else {
                    cols.push(item)
                }
            })
            const res = cols.find(item => item.prop == row.column.field)
            for (let i = 0; i <= cols.length; i++) {
                if (res.headerBgColor) {
                    callback({ backgroundColor: res.headerBgColor })
                }
            }
        },
        async cellStyle(row, column, callback) {
            let cols = []
            this.tableCols.forEach(item => {
                if (item.cols && item.cols.length > 0) {
                    item.cols.forEach(item1 => {
                        cols.push(item1)
                    })
                } else {
                    cols.push(item)
                }
            })
            const res = cols.find(item => item.prop == column.field)
            if (column.field == 'styleCode' && (row.hasInWmsLack || row.hasWmsLack)) {
                callback({ backgroundColor: '#FFC1C1' })
            } else {
                callback({ backgroundColor: res ? res.headerBgColor : '' })
            }
        },
        formatNum(val) {
            return val !== null && val !== undefined ? val.toLocaleString() : '-'
        },
        formatRate(val) {
            return val !== null && val !== undefined ? (val * 100).toFixed(1) + '%' : '-'
        },
        isActiveOderBy(orderBy, isAsc) {
            return this.activeSort.prop == orderBy && this.activeSort.order == isAsc
        },
        headerSortable(orderBy) {
            if (orderBy != this.activeSort.prop) {
                this.activeSort.prop = orderBy
                this.activeSort.order = 'descending'
            } else {
                this.activeSort.order = this.activeSort.order == 'descending' ? 'asc' : 'descending'
            }
            this.orderByList.forEach(item => {
                if (item.orderBy == orderBy) {
                    item.isAsc = this.activeSort.order
                }
            })
            this.$refs.table.$refs.xTable.clearSort();
            this.sortchange(this.activeSort)
        },
        sendWmsesFilter(wmses) {
            this.wmsesList = wmses;
            return wmses.filter((a) => a.name.includes('【昀晗-'));
        },
        async trendChart(option, row, prop) {
            this.row = row;
            this.prop = prop;
            var endDate = null;
            var startDate = null;
            if (option.startDate && option.endDate) {
                startDate = option.startDate;
                endDate = option.endDate;
            } else {
                endDate = option.date;
                startDate = new Date(option.date);
                startDate.setDate(option.date.getDate() - 30);

                startDate = dayjs(startDate).format("YYYY-MM-DD");
                endDate = dayjs(endDate).format("YYYY-MM-DD");
            }
            option.filter.filters = option.filter.filters.filter((item) => item.field !== option.dateField);
            option.filter.filters.push({
                field: option.dateField,
                operator: "GreaterThanOrEqual",
                value: startDate,
            });
            option.filter.filters.push({
                field: option.dateField,
                operator: "LessThanOrEqual",
                value: endDate,
            });
            if (this.isSummary) {
                option.filter.filters = option.filter.filters.filter((item) => option.key.indexOf(item.field) <= -1);
            }
            option.key = !this.isSummary ? option.key : [];
            option.startDate = startDate;
            option.endDate = endDate;
            this.chatProp.chatTime = [startDate, endDate];
            this.chatProp.chatLoading = true;
            if (option.key?.length == 0) {
                const arr = ['inWmsMonthLackCount', 'fullWmsMonthLackCount', 'wmsMonthLackCount', 'brandWmsMonthLackCount']
                const res = option.fields.filter(item => !arr.includes(item.field))
                if (res.length > 0) {
                    option.fields = option.fields.filter(item => !arr.includes(item.field))
                }
            }
            const { data, success } = await await request.post(`${this.api}GetTrendChart`, option);
            if (success) {
                if (option.key?.length > 0) {
                    data._title = data.title + ' - 趋势图'
                } else {
                    data._title = '汇总趋势图'
                }
                data.title = null
                if (!this.prop) {
                    data.selectedLegend = []
                } else {
                    data.selectedLegend = null
                }
                this.chatProp.data = data;
            }
            this.chatProp.chatLoading = false;
            this.chatProp.chatDialog = true;
            this.chatPropOption = option;
        },
        async init() {
            var res2 = await getAllProBrand();
            this.brandlist1 = res2.data;
            this.brandlist = res2.data.map(item => {
                return { value: item.key, label: item.value };
            });
            var resPosition = await getBianManPositionListV2();
            this.positionList = resPosition?.data;
            //采购组
            let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
            if (success) {
                this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
            }
            const { data: data2, success: success1 } = await GetProvinceCityDistrict();
            if (!success1) {
                return;
            }
            this.options1 = data2 ? data2 : [];
            //自定义导出列
        },
        proCodeCallback(e, val) {
            if (val == 1) {
                this.ListInfo.goodsCodes = e;
            } else {
                this.ListInfo.styleCodes = e;
            }
        },
        // 导出数据,这里前端可以封装一个方法
        async exportProps() {
            this.$refs.table.setExportCols()
        },
        async onExport(val) {
            this.isExport = true
            await request.post(`${this.api}ExportData`, { ...this.ListInfo, ...val }, { responseType: 'blob' }).then(download).finally(() => {
                this.isExport = false
            })
        },
        async getCol() {
            const { data, success } = await request.post(`${this.api}GetColumns?isBrand=${true}`)
            if (success) {
                const arrList = ['wmsLastInTransitDay', 'wmsAvgInTransitDay']
                const hideCol = ['wmsAvgInTransitDay', 'brandWmsAvgInTransitDay']
                // const commonChart = ['brandWmsLackRate','brandWmsLackQty']
                const dontComputedWidthList = ['setTime', 'picture', 'goodsCode', 'goodsName', 'styleCode', 'company', 'deptName', 'title', 'brandName', 'wmsName', 'provinceCityDistrict', 'wmsLastInTransitDay']
                // const arrList = ['inWmsShoudInv1', 'inWmsShoudInv2', 'wmsShoudInv1', 'wmsShoudInv2', 'wmsLackQty1', 'wmsLackQty2', 'wmsLackRate1', 'wmsLackRate2', 'wmsLastInTransitDay', 'wmsAvgInTransitDay', 'inWmsLackQty1', 'inWmsLackQty2']
                // const hideCol = ['inWmsShoudInv2', 'wmsShoudInv2', 'wmsLackQty2', 'wmsLackRate2', 'inWmsLackQty2', 'wmsAvgInTransitDay']
                this.enabledArr = data.filter(item => hideCol.includes(item.prop))
                this.tableColsData = JSON.parse(JSON.stringify(data))
                let columnIndex = 0
                const res = mergeTableCols(JSON.parse(JSON.stringify(data)).map((item, i) => {
                    item.id = i
                    return item
                }))
                this.customExportColumn.collectColumn = res
                data.unshift({ type: 'checkbox', label: '', visible: true })
                data.forEach(item => {
                    item.columnIndex = columnIndex++
                    if (arrList.includes(item.prop)) {
                        item.tipmesg = null
                        item.sortable = false
                    }
                    if (hideCol.includes(item.prop)) {
                        item.enabled = false
                    }
                    if (item.prop == 'salesDay30') {
                        item.type = 'click'
                        item.handle = (that, row) => that.sale30(row, 2)
                    }
                    if (item.prop == 'setTime') {
                        item.type = 'click'
                        item.handle = (that, row) => that.openPropsHistory(row)
                    }
                    if (item.prop == 'inWmsInv') {
                        item.type = 'click'
                        item.handle = (that, row) => that.openInventoryDetails(row, 0)
                    }
                    if (item.prop == 'wmsInv') {
                        item.type = 'click'
                        item.handle = (that, row) => that.openInventoryDetails(row, 1)
                    }
                    if (item.prop == 'brandWmsInv') {
                        item.type = 'click'
                        item.handle = (that, row) => that.openInventoryDetails(row, 0)
                    }
                    // if(commonChart.includes(item.prop)) {
                    //     item.type = 'click'
                    //     item.handle = (that, row) => that.commonChart(row)
                    // }
                    // if (!dontComputedWidthList.includes(item.prop)) {
                    //     item.width = ((item.width ? item.width : 0) / 1.8).toFixed(0)
                    // }
                })
                this.tableCols = mergeTableCols(data)
                this.ListInfo.summarys = data
                    .filter((a) => a.summaryType)
                    .map((a) => {
                        return { column: a["sort-by"], summaryType: a.summaryType };
                    });
            }
        },
        async getList(type) {
            if (type === "search") {
                this.ListInfo.currentPage = 1;
                this.$refs.pager.setPage(1);
            }
            if (this.ListInfo.provinceCityDistricts1?.length > 0) {
                this.ListInfo.provinceCityDistricts = this.ListInfo.provinceCityDistricts1.map(item => {
                    return item.join('-')
                })
            } else {
                this.ListInfo.provinceCityDistricts = []
            }
            function processRate(rateKey1, rateKey2) {
                const value = this.ListInfo[rateKey1];
                if (value || value === 0) {
                    this.ListInfo[rateKey2] = (value / 100).toFixed(2);
                } else {
                    this.ListInfo[rateKey2] = null;
                }
            }
            processRate.call(this, 'actualTheoryGoodsRateSurplusMax1', 'actualTheoryGoodsRateSurplusMax');
            processRate.call(this, 'actualTheoryGoodsRateSurplusMin1', 'actualTheoryGoodsRateSurplusMin');
            this.loading = true;
            // 使用时将下面的方法替换成自己的接口
            try {
                const { data, success } = await request.post(`${this.api}PageGetData`, this.ListInfo)
                if (success) {
                    for (let key in data.summary) {
                        if (key == 'wmsLackRate2_sum') {
                            data.summary[key] = data.summary[key] !== null ? decimal(data.summary[key], 100, '*').toFixed(1) + '%' : data.summary[key]
                        }
                    }
                    this.data = data;
                }
            } catch (error) {
                console.log(error);
            } finally {
                this.loading = false;
            }
        },
        //每页数量改变
        Sizechange(val) {
            this.ListInfo.currentPage = 1;
            this.ListInfo.pageSize = val;
            this.getList()
        },
        //当前页改变
        Pagechange(val) {
            this.ListInfo.currentPage = val;
            this.getList()
        },
        sortchange({ order, prop }) {
            // const arrList = ['inWmsShoudInv1', 'inWmsShoudInv2', 'wmsShoudInv1', 'wmsShoudInv2', 'wmsLackQty1', 'wmsLackQty2', 'wmsLackRate1', 'wmsLackRate2', 'wmsLastInTransitDay', 'inWmsLackQty1', 'inWmsLackQty2', 'wmsAvgInTransitDay']
            const arrList = ['wmsLastInTransitDay', 'wmsAvgInTransitDay', 'brandWmsAvgInTransitDay', 'brandWmsLastInTransitDay']
            if (!arrList.includes(prop)) {
                this.activeSort = {
                    prop: null,
                    order: null
                }
            }
            if (prop) {
                this.ListInfo.orderBy = prop
                this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
                this.getList()
            }
        },
    }
}
</script>

<style scoped lang="scss">
.top {
    display: flex;
    margin-bottom: 5px;
    flex-wrap: wrap;

    .publicCss {
        height: 30px;
        width: 150px;
        margin: 0 5px 5px 0;
    }
}

.headerSolt {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: end;
    line-height: 16px;

    div {
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: end;
    }
}

.iconcolor {
    color: #409EFF;
    line-height: 8px;
}

.startcol {
    color: #C0C4CC;
    line-height: 8px;
}

.headerSort {
    margin-left: 5px;
    font-size: 16px;
    display: flex;
    flex-direction: column;

    i {
        cursor: pointer;
    }
}

::v-deep .vxe-table--render-default.size--small .vxe-header--column:not(.col--ellipsis) {
    padding: 5px 0 !important;
}

::v-deep .vxe-table--render-default.size--small .vxe-footer--column:not(.col--ellipsis) {
    padding: 3px 0 !important;
}

::v-deep .mainTable .vxe-table td {
    border-right: solid 1px #888 !important;
    border-top: solid 1px #888 !important;
    box-sizing: content-box;

    & :last-child {
        border-top: none !important;
    }
}

.isActive {
    color: red;
}

::v-deep .el-select__tags-text {
    max-width: 40px;
}

::v-deep .el-cascader__search-input {
    margin: 0 0 0 2px;
}

.cascaderCss ::v-deep .el-input__inner {
    height: 28px !important;
}
</style>
