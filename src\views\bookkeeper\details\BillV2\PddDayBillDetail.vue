<template>
    <container>
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter" @submit.native.prevent>
                <el-form-item label="发生时间">
                    <el-date-picker style="width: 249px" v-model="filter.timerange" type="daterange" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始" :clearable="false"
                        end-placeholder="结束" :picker-options="pickerOptions" @change="onSearch"></el-date-picker>
                </el-form-item>
                <el-form-item label="商户订单号:">
                    <el-input v-model="filter.merchantOrderNumber" :clearable="true" maxlength="20" placeholder="商户订单号"
                        style="width:130px;" />
                </el-form-item>
                <el-form-item label="所属店铺:">
                    <el-input v-model="filter.shopName" :clearable="true" maxlength="20" placeholder="所属店铺"
                        style="width:130px;" />
                </el-form-item>
                <el-form-item label="财务类型:">
                    <el-input v-model="filter.accountType" :clearable="true" maxlength="20" placeholder="财务类型"
                        style="width:130px;" />
                </el-form-item>
                <el-form-item label="业务描述:">
                    <el-input v-model="filter.businessDesc" :clearable="true" maxlength="20" placeholder="业务描述"
                        style="width:130px;" />
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input v-model="filter.remark" :clearable="true" maxlength="20" placeholder="备注"
                        style="width:130px;" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onExport">导出</el-button>
                </el-form-item>
            </el-form>
        </template>
        <ces-table ref="table" :that='that' tablekey="PddDayBillDetail20240719" :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :showsummary='true' 
            :isSelection="false" @cellclick="cellclick" :loading="listLoading" @summaryClick='onsummaryClick'>
        </ces-table>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>
        <el-dialog :visible.sync="buscharDialog.visible" width="80%" v-dialogDrag>
            <span>
                <buschar ref="buschar" v-if="buscharDialog.visible" :analysisData="buscharDialog.data"
                    :loading="buscharDialog.loading"></buschar>
            </span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="buscharDialog.visible = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>

<script>
import container from '@/components/my-container'
import MyConfirmButton from '@/components/my-confirm-button'
import cesTable from "@/components/Table/table.vue";
import dayjs from "dayjs";
import { platformlist} from '@/utils/tools'
import { formatTime, formatTime1 } from "@/utils";
import { Loading } from 'element-ui';
import buschar from '@/components/Bus/buschar'
import {pddDaybillDetailPage,pddDaybillDetailExport,queryPddDaybillDetailSumChart} from '@/api/bladegateway/pddDaybillDetail'


const tableCols = [
    { istrue: true, prop: 'occurDate', label: '发生时间', tipmesg: '', width: '150', sortable: 'custom', },
    { istrue: true, prop: 'merchantOrderNumber', label: '商户订单号', tipmesg: '', width: '200', sortable: 'custom', },
    { istrue: true, prop: 'shopName', label: '店铺名', tipmesg: '', width: '200',  sortable: 'custom', },
    { istrue: true, prop: 'accountType', label: '账务类型', tipmesg: '', width: '100', sortable: 'custom', },
    { istrue: true, summaryEvent: true, prop: 'incomeAmount', label: '收入金额', tipmesg: '', width: '100', sortable: 'custom',},
    { istrue: true, summaryEvent: true, prop: 'expenditureAmount', label: '支出金额', tipmesg: '', width: '100', sortable: 'custom',},
    { istrue: true, summaryEvent: true, prop: 'pureAmount', label: '合计金额', tipmesg: '', width: '100',},
    { istrue: true, prop: 'businessDesc', label: '业务描述', tipmesg: '', width: '400',},
    { istrue: true, prop: 'remark', label: '备注', tipmesg: '', width: 'auto',},
]

const startTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");
const endTime = formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD");

export default {
    name: 'YunHanAdminIndex',
    components: { container, cesTable, MyConfirmButton, buschar },

    data() {
        return {
            that: this,
            filter: {
                startTime: null,
                endTime: null,
                timerange: [startTime, endTime],
            },
            platformlist:platformlist,
            importDialog: {
                filter: {
                    YearMonthDay: null,
                    PlatForm: null
                }
            },
            list: [],
            summaryarry: {},
            pager: { OrderBy: "occurDate", IsAsc: false },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            pickOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },
            // onHandNumber: null,
            tableCols: tableCols,
            total: 0,
            sels: [],
            // editparmLoading: false,
            uploadLoading: false,
            // editparmLoading1: false,
            // editparmLoading2: false,
            // editparmVisible: false,
            // editparmVisible1: false,
            // editparmVisible2: false,
            dialogVisible: false,
            listLoading: false,
            // showDetailVisible: false,
            fileList: [],
            buscharDialog: { visible: false, title: "", data: {}, loading: false }
        };
    },

    async mounted() {
        await this.onSearch()
    },

    methods: {
        async onExport() {
            let loadingInstance = Loading.service({ fullscreen: true, lock: true, text: '正在导出中...', background: "rgba(0, 0, 0, 0.7)" });
            try {
                this.filter.startTime = null;
                this.filter.endTime = null;
                if (this.filter.timerange) {
                    this.filter.startTime = this.filter.timerange[0];
                    this.filter.endTime = this.filter.timerange[1];
                }
                const params = { ...this.pager, ...this.filter }
                pddDaybillDetailExport(params).then(response => {
                    this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
                        loadingInstance.close();
                    });
                    const aLink = document.createElement("a");
                    var blob = new Blob([response], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '日账单明细.xlsx')
                    aLink.click()
                });

            } catch (err) {
                this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
                    loadingInstance.close();
                });
                console.log(err)
                console.log(err.message);
            }
        },
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager();
            let page = this.pager;
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            const params = { ...pager, ...page, ... this.filter }
            if (params === false) {
                return;
            }
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await pddDaybillDetailPage(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }
            this.total = res.data.total;
            const data = res.data.list;
            this.summaryarry = res.data.summary;
            this.list = data
        },
        async nSearch() {
            await this.getlist()
        },
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        async onsummaryClick(property) {
            this.filter.startTime = null;
            this.filter.endTime = null;
            if (this.filter.timerange) {
                this.filter.startTime = this.filter.timerange[0];
                this.filter.endTime = this.filter.timerange[1];
            }
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            params.column = property;
            let that = this;
            that.buscharDialog.visible = true;
            that.buscharDialog.loading = true;
            const res = await queryPddDaybillDetailSumChart(params).then(res => {
                that.buscharDialog.loading = false;
                that.buscharDialog.data = res.data
                that.buscharDialog.title = res.data.legend[0]
            });
            this.$nextTick(async () => { await that.$refs.buschar.initcharts(); });
        },
        selectchange: function (rows, row) {
            this.selids = []; console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        cellclick(row, column, cell, event) {

        },
    }
};
</script>

<style lang="scss" scoped>

</style>
