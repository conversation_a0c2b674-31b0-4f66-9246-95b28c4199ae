/**
 * 表格汇总行合并功能的 mixin
 * 用于统一处理 vxe-table 的汇总行合并逻辑
 */
export default {
  methods: {
    /**
     * 通用的表格汇总行合并函数
     * @param {Object} params - 参数对象
     * @param {Object} params.row - 行数据
     * @param {number} params._rowIndex - 行索引
     * @param {Object} params.column - 列配置
     * @param {Array} params.visibleData - 可见数据
     * @returns {Object} 合并配置 {rowspan, colspan}
     */
    footerSpanMethod({ row, _rowIndex, column, visibleData }) {
      // 当使用 footer-data 时，row 是汇总数据对象，不是数组
      // 根据 mergeColumn 的 default 字段动态检查 row 对象
      const defaultField = this.mergeColumn?.default;

      // 基本检查：确保 row 存在
      if (!row) {
        return { rowspan: 1, colspan: 1 };
      }

      // 注意：不再检查 row[checkField] 是否存在，允许 null 值也进行合并

      // 从 mergeColumn 配置中获取需要合并的列
      const mergeFields = this.mergeColumn?.column || [];
      if (mergeFields.length === 0) {
        return { rowspan: 1, colspan: 1 };
      }

      // 检查当前列是否需要参与合并
      if (mergeFields.includes(column.field)) {
        // 查找第一个需要合并的列（按照 mergeColumn.column 数组的顺序）
        const tableColumns = this.$refs.newtable.getColumns();
        // 按照 mergeFields 数组的顺序找到第一个在表格中存在的列
        let firstMergeColumnField = null;
        for (let field of mergeFields) {
          if (tableColumns.find(col => col.field === field)) {
            firstMergeColumnField = field;
            break;
          }
        }
        if (firstMergeColumnField && column.field === firstMergeColumnField) {
          // 这是第一个合并列，计算需要合并的列数
          let mergeCount = 0;
          for (let col of tableColumns) {
            if (mergeFields.includes(col.field)) {
              mergeCount++;
            }
          }
          return {
            rowspan: 1,
            colspan: mergeCount
          };
        } else {
          // 这是后续的合并列，隐藏
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }

      // 不参与合并的列正常显示
      return { rowspan: 1, colspan: 1 };
    },

    /**
     * 通用的获取合并列显示值函数
     * @param {Object} row - 行数据
     * @returns {string} 显示值
     */
    getMergeDisplayValue(row) {
      // 如果配置了默认显示字段且不为空，使用该字段的值
      if (this.mergeColumn?.default && this.mergeColumn.default.trim() !== '') {
        const value = row[this.mergeColumn.default];
        // 如果值为 null 或 undefined，返回空字符串
        return value != null ? value : '';
      }
      // 如果 default 为空字符串，返回空字符串
      if (this.mergeColumn?.default === '') {
        return '';
      }
      // 否则使用第一个合并列的值
      const firstColumn = this.mergeColumn?.column?.[0];
      if (firstColumn) {
        const value = row[firstColumn];
        // 如果值为 null 或 undefined，返回空字符串
        return value != null ? value : '';
      }
      return '';
    }
  }
};
