<template>
  <div style="height: 100%;">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="70px" class="demo-ruleForm">
      <el-form-item label="仓库">
        {{ ruleForm.warehouse }}
      </el-form-item>
      <el-form-item label="申请人">
        <el-input v-model.trim="ruleForm.applicantUser" placeholder="请输入申请人" maxlength="50" clearable disabled
          class="formCss" />
      </el-form-item>
      <el-form-item label="申请部门 ">
        <el-input v-model.trim="ruleForm.applicantUserDept" placeholder="请输入申请部门" maxlength="50" clearable disabled
          class="formCss" />
      </el-form-item>
      <el-form-item label="使用部门">
        <el-select v-model="ruleForm.applicantUseDept" placeholder="请选择使用部门" class="formCss" clearable filterable>
          <el-option v-for="item in deptList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入备注" v-model="ruleForm.remark">
        </el-input>
      </el-form-item>
      <el-row>
        <div style="display: flex;justify-content: space-between;align-items: center;">
          <span style="font-size: 16px;font-weight: bold;">商品明细</span>
          <el-button type="primary" @click="addRow">新增一行</el-button>
        </div>
        <div style="height: 250px;">
          <vxetablebase :id="'goodsdetailList202504021520'" :tablekey="'goodsdetailList202504021520'" ref="table"
            :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' :tableData='ruleForm.goodsdetailList'
            :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
            :height="'100%'" :isNeedExpend="false" :border="true">
            <template #imageList="{ row, index }">
              <div style="display: flex;justify-content: center;align-items: center;">
                <el-image style="width: 100%; height: 100%;"
                  :src="row.imageList && row.imageList.length > 0 ? row.imageList[0] : 'https://nanc.yunhanmy.com:10010/media/video/20250331/1906541420273197056.png'"
                  :preview-src-list="row.imageList">
                </el-image>
              </div>
            </template>
            <template #goodsNumber="{ row, index }">
              <el-select v-model="row.goodsNumber" placeholder="请输入商品编号" clearable filterable remote
                :remote-method="(query) => getdepartmentlist(query, index)" @change="getdepartmentChange(row, index)">
                <el-option v-for="item in row.departmentList" :key="item.goodsNumber" :label="item.goodsNumber"
                  :value="item.goodsNumber" />
              </el-select>
            </template>
            <template #estimatedServiceLife="{ row, index }">
              <el-date-picker v-model="row.estimatedServiceLife" type="datetime" format="yyyy-MM-dd HH:mm"
                :value-format="'yyyy-MM-dd HH:mm'" placeholder="选择预计归还时间" style="width: 100%;">
              </el-date-picker>
            </template>
            <template slot="right">
              <vxe-column title="操作" width="auto" fixed="right">
                <template #default="{ row, $index }">
                  <div style="display: flex; justify-content: center; width: 100%;">
                    <el-button size="mini" type="danger" @click="handleDelete(row, $index)">删除</el-button>
                  </div>
                </template>
              </vxe-column>
            </template>
          </vxetablebase>
        </div>
      </el-row>
    </el-form>
    <div style="display: flex; justify-content: center;align-items: center;margin-top: 10px;">
      <el-button @click="cancelForm">取消</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </div>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions } from '@/utils/tools'
import { addSampleReceiveApplication, getSampleRegistration } from '@/api/inventory/sampleGoods';
import { getUserInfo } from '@/api/operatemanage/productalllink/alllink'
import { getGoodsDocCgProvidersDto } from "@/api/inventory/basicgoods"
import dayjs from 'dayjs'
import _ from "lodash";
const tableCols = [
  { sortable: 'custom', width: '90', align: 'center', prop: 'imageList', label: '商品图片', },
  { sortable: 'custom', width: '150', align: 'center', prop: 'goodsNumber', label: '商品编号', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'goodsCode', label: '商品编码', },
  { sortable: 'custom', width: '140', align: 'center', prop: 'warehouseBitCode', label: '库位', },
  { sortable: 'custom', width: '180', align: 'center', prop: 'estimatedServiceLife', label: '预计归还时间', },
]
export default {
  name: "receiveApplication",
  components: {
    MyContainer, vxetablebase
  },
  props: {
    selectList: {
      type: Array,
      default: () => []
    },
    deptList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableCols,
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
      },
      ruleForm: {
        applicantUser: '',
        applicantUserDept: '',
        applicantUseDept: '',
        remark: '',
        warehouse: '',
        goodsdetailList: []
      },
      rules: {
        region: [{ required: true, message: '请选择使用部门', trigger: 'change' }],
        textarea2: [{ required: true, message: '请输入备注', trigger: 'blur' }],
      },
      timeRanges: [],
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      timing: null,//定时器
    }
  },
  async mounted() {
    // await this.getList()
    const { data } = await getUserInfo();
    this.ruleForm.applicantUser = data.nickName;
    this.deptList.forEach(item => {
      if (data.fullName.includes(item)) {
        this.ruleForm.applicantUserDept = item;
      }
    })
    this.ruleForm.warehouse = this.selectList[0].warehouse;
    let a = []
    a = this.selectList.map(item => {
      return {
        ...item,
        imageList: item.images ? JSON.parse(item.images).map(img => img.url) : [],
        warehouseBitCode: item.bitNumber,
        estimatedServiceLife: null,
      }
    })
    this.ruleForm.goodsdetailList = a
  },
  methods: {
    getdepartmentChange(row, index) {
      // this.ruleForm.goodsdetailList[index].goodsCode = row.goodsCode;
      row.departmentList.forEach(item => {
        if (item.goodsNumber == row.goodsNumber) {
          row.goodsCode = item.goodsCode;
          row.warehouseBitCode = item.bitNumber;
          row.estimatedServiceLife = item.estimatedServiceLife;
          row.imageList = item.images ? [item.images] : []
        }
      })
    },
    async getdepartmentlist(value, index) {
      clearTimeout(this.timing);
      this.timing = setTimeout(async () => {
        try {
          const res = await getSampleRegistration({
            goodsNumber: value,
            pageSize: 999999,
            currentPage: 1
          });
          if (res.success && res.data.list && res.data.list.length > 0) {
            if (!this.ruleForm.goodsdetailList[index]) {
              console.error(`goodsdetailList[${index}] 未定义`);
              return;
            }
            this.$set(this.ruleForm.goodsdetailList[index], 'departmentList', res.data.list);
          } else {
            console.warn('API 返回的数据为空');
          }
        } catch (err) {
          console.error('API 请求失败', err);
        }
      }, 500);
    },
    cancelForm() {
      this.$emit('close')
    },
    submit: _.debounce(async function () {
      if (this.ruleForm.goodsdetailList.length == 0) {
        this.$message.error('商品明细 - 请至少选择一个商品');
        return;
      }
      const goodsList = this.ruleForm.goodsdetailList;
      // 1️⃣ 检查 storageStatus 是否为 3
      const invalidItems = goodsList
        .filter(item => item.storageStatus !== 3)
        .map(item => item.goodsCode || '未知商品编码');
      if (invalidItems.length > 0) {
        this.$message.error(`${invalidItems.join('、')} 状态不为在库，不可发起申请`);
        return;
      }
      // 2️⃣ 检查是否有重复的 goodsNumber
      const goodsNumberMap = new Map();
      const duplicateGoodsNumbers = new Set();
      goodsList.forEach(item => {
        const goodsNumber = item.goodsNumber;
        if (goodsNumber) {
          if (goodsNumberMap.has(goodsNumber)) {
            duplicateGoodsNumbers.add(goodsNumber);
          } else {
            goodsNumberMap.set(goodsNumber, true);
          }
        }
      });
      if (duplicateGoodsNumbers.size > 0) {
        this.$message.error(`以下商品编号重复：${Array.from(duplicateGoodsNumbers).join('、')}`);
        return;
      }

      // 3️⃣ 校验 estimatedServiceLife（预计归还时间）是否为空
      const missingEstimatedServiceLife = goodsList
        .map((item, index) => ({ index: index + 1, goodsCode: item.goodsCode, estimatedServiceLife: item.estimatedServiceLife, goodsNumber: item.goodsNumber }))
        .filter(item => !item.estimatedServiceLife) // 过滤出预计归还时间为空的项
        .map(item => `第 ${item.index} 行（商品编号: ${item.goodsNumber || '未知'}）`);

      if (missingEstimatedServiceLife.length > 0) {
        this.$message.error(`以下行的预计归还时间未填写：\n${missingEstimatedServiceLife.join('、')}`);
        return;
      }
      this.ruleForm.goodsdetailList.forEach(item => {
        if (item.departmentList) {
          delete item.departmentList;
        }
      });
      const { data, success } = await addSampleReceiveApplication(this.ruleForm);
      if (success) {
        if (data.success) {
          this.$message.success('提交成功');
          this.$emit('cancelFormMethod');
          this.cancelForm();
        } else {
          this.$message.error(data.msg ? data.msg : '提交失败')
        }
      } else {
        this.$message.error('提交失败')
      }
    }, 1000, { leading: true, trailing: false }), // 1秒防抖，立即执行，禁用结束后额外执行,
    handleDelete(row, index) {
      this.ruleForm.goodsdetailList = this.ruleForm.goodsdetailList.filter(item => item.id !== row.id);
    },
    addRow() {
      this.ruleForm.goodsdetailList.push({
        goodsCode: '',
        goodsNumber: '',
        warehouseBitCode: '',
        departmentList: [],
      })
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给近7天时间
        this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await pageGetVoOrder(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
}

.formCss {
  width: 100%;
}
</style>
