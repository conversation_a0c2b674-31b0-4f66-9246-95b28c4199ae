<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-form class="ad-form-query" :inline="true">
                <el-form-item>
                    <el-input v-model.trim="filter.goodsCodeHalf" style="width: 140px" placeholder="半成品编码" @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-input v-model.trim="filter.goodsCode" style="width: 140px" placeholder="成品编码" @keyup.enter.native="onSearch" clearable maxlength="40" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </template>
        <template>
            <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :tableData='list' :tableCols='tableCols' :isSelection="false" :loading="listLoading" :tableHandles="tableHandles1">
            </ces-table>
        </template>
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getlist" />
        </template>

        <el-dialog title="设置" :visible.sync="dialogData.dialogVisible" width="40%" :close-on-click-modal="false" @close="onCloseDialog1" v-dialogDrag>
            <span>
                <el-form ref="addForm" :model="dialogData.addForm" :rules="dialogData.addFormRules" label-width="110px">
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="goodsCodeHalf" label="半成品编码">
                                <el-input v-model.trim="dialogData.addForm.goodsCodeHalf" auto-complete="off" maxlength="40" @blur="getGoodsNameHalf()" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="goodsCode" label="成品编码">
                                <el-input v-model.trim="dialogData.addForm.goodsCode" auto-complete="off" maxlength="40" :disabled="(dialogData.mode!=1)" @blur="getGoodsName()" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="goodsNameHalf" label="半成品名称">
                                <el-input v-model="dialogData.addForm.goodsNameHalf" auto-complete="off" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="goodsName" label="成品名称">
                                <el-input v-model="dialogData.addForm.goodsName" auto-complete="off" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="specLength" label="半成品长度m">
                                <el-input-number v-model="dialogData.addForm.specLengthHalf" :min="0" :max="10000" auto-complete="off" :precision="4" style="width:160px" :disabled="dialogData.specHalfDisabled">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="specLength" label="成品长度m">
                                <el-input-number v-model="dialogData.addForm.specLength" :min="0" :max="10000" auto-complete="off" :precision="4" style="width:160px">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="specWidth" label="半成品宽度m">
                                <el-input-number v-model="dialogData.addForm.specWidthHalf" :min="0" :max="10000" auto-complete="off" :precision="4" style="width:160px" :disabled="dialogData.specHalfDisabled">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                            <el-form-item prop="specWidth" label="成品宽度m">
                                <el-input-number v-model="dialogData.addForm.specWidth" :min="0" :max="10000" auto-complete="off" :precision="4" style="width:160px">
                                </el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </span>
            <span slot="footer" class="dialog-footer">
                <my-confirm-button type="submit" :validate="addFormValidate" @click="onSave" style="margin-right: 10px;">
                    保存
                </my-confirm-button>
                <el-button @click="dialogData.dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>

<script>
    import MyContainer from "@/components/my-container";
    import MyConfirmButton from '@/components/my-confirm-button'
    import cesTable from "@/components/Table/table.vue";
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import MySearch from "@/components/my-search";
    import MySearchWindow from "@/components/my-search-window";
    import { getTailorLossGoodsSetPageList, saveTailorLossGoodsSet, deleteTailorLossGoodsSet, 
        getMyGoodsByCode, getTailorLossGoodsSetAllGoodsCodeHalf,exportTailorLossGoodsSetList 
    } from '@/api/order/tailorloss';
    const tableCols = [
        { istrue: true, prop: 'goodsCodeHalf', label: '半成品编码', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'goodsNameHalf', label: '半成品名称', width: '260' },
        { istrue: true, prop: 'specHalf', label: '半成品规格(m)', width: '180' },
        { istrue: true, prop: 'goodsCode', label: '成品编码', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'goodsName', label: '成品名称', width: '260' },
        { istrue: true, prop: 'spec', label: '成品规格(m)', width: '180' },
        { istrue: true, prop: 'createdTime', label: '添加时间', width: '150', sortable: 'custom' },
        { istrue: true, prop: 'modifiedTime', label: '编辑时间', width: '150', sortable: 'custom' },
        {
            istrue: false, type: 'button', label: '操作', width: '120',
            btnList: [
                { label: "编辑", handle: (that, row) => that.onEdit(row) },
                { label: "删除", handle: (that, row) => that.onDelete(row) }
            ]
        }
    ]
    const tableHandles1 = [
        { label: "新增", handle: (that) => that.onAdd() },
        { label: "复制当前所有半成品编码", handle: (that) => that.onCopyGoodsCode(1) },
        { label: "复制当前所有成品编码", handle: (that) => that.onCopyGoodsCode(0) },
        { label: "导出", handle: (that) => that.onExport() },
    ];
    export default {
        name: 'tailorlossgoodsset',
        components: { cesTable, MyContainer, MyConfirmButton, MySearch, MySearchWindow },
        props: {

        },
        data() {
            return {
                that: this,
                tableHandles1: tableHandles1,
                filter: {
                    timerange: [
                        formatTime(dayjs().subtract(1, "month"), "YYYY-MM-DD"),
                        formatTime(new Date(), "YYYY-MM-DD"),
                    ],
                    startDate: null,
                    endDate: null,
                },
                list: [],
                summaryarry: {},
                pager: { OrderBy: "createdTime", IsAsc: false },
                tableCols: tableCols,
                total: 0,
                sels: [],
                listLoading: false,
                pageLoading: false,
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '昨天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 1);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime());
                                picker.$emit('pick', [start, start]);
                            }
                        }, {
                            text: '近三天',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime());
                                const end = new Date(new Date(tdate.toLocaleDateString()));
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一周',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 5);
                                const end = new Date(new Date(tdate.toLocaleDateString()).getTime() + 3600 * 1000 * 24 * 5);
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '近一个月',
                            onClick(picker) {
                                const tdate = new Date(new Date().getTime() - 3600 * 1000 * 24 * 31);
                                console.log("获取前一个月的时间", tdate.getDay());
                                const end = new Date(new Date(new Date().toLocaleDateString()).getTime());
                                const start = new Date(new Date(tdate.toLocaleDateString()).getTime());
                                start.setTime(start.getTime() - 3600 * 1000 * 24);
                                end.setTime(end.getTime() - 3600 * 1000 * 24);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                },
                dialogData: {
                    mode: 0,
                    dialogVisible: false,
                    dialogLonding: false,
                    addForm: {
                        goodsCodeHalf: null,
                        goodsNameHalf: null,
                        specLengthHalf: null,
                        specWidthHalf: null,

                        goodsCode: null,
                        goodsName: null,
                        specLength: null,
                        specWidth: null,
                    },
                    addFormRules: {
                        goodsCodeHalf: [{ required: true, message: '请输入半成品编码', trigger: 'blur' }],
                        specLengthHalf: [{ required: true, message: '请输入成品长度', trigger: 'blur' }],
                        specWidthHalf: [{ required: true, message: '请输入成品宽度', trigger: 'blur' }],

                        goodsCode: [{ required: true, message: '请输入成品编码', trigger: 'blur' }],
                        specLength: [{ required: true, message: '请输入成品长度', trigger: 'blur' }],
                        specWidth: [{ required: true, message: '请输入成品宽度', trigger: 'blur' }],
                    },
                    specHalfDisabled: false
                }
            };
        },
        async mounted() {
            await this.onSearch()
        },
        methods: {
            async getGoodsNameHalf() {
                const res = await getMyGoodsByCode({ goodsCode: this.dialogData.addForm.goodsCodeHalf });
                if (res?.success) {
                    this.dialogData.addForm.goodsNameHalf = res.data.goodsName;
                }
                const params = {
                    currentPage: 1,
                    pageSize: 1,
                    isAccurateGoodsCodeHalf: 1,
                    goodsCodeHalf: this.dialogData.addForm.goodsCodeHalf
                }
                const res2 = await getTailorLossGoodsSetPageList(params);
                this.dialogData.specHalfDisabled = false;
                if (res2?.success && res2.data.list.length > 0) {
                    let data = res2.data.list[0];
                    this.dialogData.addForm.specLengthHalf = data.specLengthHalf;
                    this.dialogData.addForm.specWidthHalf = data.specWidthHalf;
                    this.dialogData.specHalfDisabled = true;
                }
            },
            async getGoodsName() {
                const res = await getMyGoodsByCode({ goodsCode: this.dialogData.addForm.goodsCode });
                if (!res?.success) {
                    return;
                }
                this.dialogData.addForm.goodsName = res.data.goodsName;
            },
            //查询第一页
            async onSearch() {
                console.log(this.filter)
                if (!this.filter.timerange) {
                    this.$message({ message: "请选择日期", type: "warning", });
                    return;
                }
                this.$refs.pager.setPage(1)
                await this.getlist();
            },
            //获取查询条件
            getCondition() {
                this.filter.startDate = null;
                this.filter.endDate = null;
                if (this.filter.timerange && this.filter.timerange.length > 1) {
                    this.filter.startDate = this.filter.timerange[0];
                    this.filter.endDate = this.filter.timerange[1];
                }
                else {
                    this.$message({ message: "请先选择时间", type: "warning" });
                    return false;
                }
                var pager = this.$refs.pager.getPager();
                var page = this.pager;
                const params = {
                    ...pager,
                    ...page,
                    ... this.filter
                }
                return params;
            },
            //分页查询
            async getlist() {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                console.log(params, 'params')
                this.listLoading = true;
                const res = await getTailorLossGoodsSetPageList(params)
                this.listLoading = false;
                if (!res?.success) {
                    return
                }
                this.total = res.data.total;
                const data = res.data.list;
                //this.summaryarry = res.data.summaryarry;
                console.log(res.data, 'resdata')
                data.forEach(d => {
                    d._loading = false;
                })
                this.list = data;
            },
            //排序查询
            async sortchange(column) {
                if (!column.order)
                    this.pager = {};
                else {
                    this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
                }
                await this.onSearch();
            },
            selectchange: function (rows, row) {
                this.selids = []; console.log(rows)
                rows.forEach(f => {
                    this.selids.push(f.id);
                })
            },
            async onAdd() {
                this.dialogData.dialogVisible = true;
                this.dialogData.mode = 1;
            },
            //验证
            addFormValidate: function () {
                let isValid = false
                this.$refs.addForm.validate(valid => {
                    isValid = valid
                })
                return isValid
            },
            async onSave() {
                if (this.dialogData.addForm.specLengthHalf <= 0 || this.dialogData.addForm.specWidthHalf <= 0 ||
                    this.dialogData.addForm.specLength <= 0 || this.dialogData.addForm.specWidth <= 0) {
                    this.$message({ message: '请输入长宽', type: 'error' });
                    return;
                }
                const params = {
                    ...this.dialogData.addForm,
                    mode: this.dialogData.mode
                };
                params.id = 0;
                params.enabled = false;
                console.log(params, "params")
                var res = await saveTailorLossGoodsSet(params);
                if (!res?.success) {
                    return;
                }
                this.$message({ message: this.$t('保存成功'), type: 'success' })
                Object.keys(this.dialogData.addForm).forEach(key => {
                    this.dialogData.addForm[key] = null;
                });
                this.dialogData.dialogVisible = false;
                await this.onSearch();
            },
            async onEdit(row) {
                this.dialogData.dialogVisible = true;
                this.dialogData.mode = 2;
                const params = {
                    currentPage: 1,
                    pageSize: 1,
                    isAccurateGoodsCode: 1,
                    goodsCode: row.goodsCode
                }
                const res = await getTailorLossGoodsSetPageList(params);
                if (!res?.success) {
                    return
                } if (res.data.list.length <= 0) {
                    return;
                }
                this.dialogData.addForm = res.data.list[0];
                this.dialogData.specHalfDisabled = false;
            },
            async onDelete(row) {
                this.$confirm('确认要执行删除操作吗?', '提示', {
                    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
                }).then(async () => {
                    var res = await deleteTailorLossGoodsSet({ goodsCode: row.goodsCode });
                    if (!res?.success) {
                        return;
                    }
                    this.$message({ message: this.$t('删除成功'), type: 'success' });
                    await this.onSearch();
                }).catch(() => {
                });
            },
            async onCloseDialog1() {
                Object.keys(this.dialogData.addForm).forEach(key => {
                    this.dialogData.addForm[key] = null;
                });
            },
            async onCopyGoodsCode(isHalf) {
                const res = await getTailorLossGoodsSetAllGoodsCodeHalf({ isHalf: isHalf });
                console.log(res)
                if (!res?.success) {
                    this.$message({ message: "复制失败，请刷新后重试", type: "warning" });
                    return;
                }
                if (!res.data) {
                    this.$message({ message: "未找到可复制的编码，请刷新后重试", type: "warning" });
                    return;
                }
                let that = this;
                this.$copyText(res.data).then(function (e) {
                    that.$message({ message: "编码信息已复制到剪切板！", type: "success" });
                }, function (e) {
                    that.$message({ message: "复制失败，请刷新后重试", type: "warning" });
                });
            },
            async onExport()
            {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                console.log(params, 'params')
                this.listLoading = true;
                const rlt = await exportTailorLossGoodsSetList(params);
                this.listLoading = false;
                if (rlt && rlt.data) {
                    const aLink = document.createElement("a");
                    let blob = new Blob([rlt.data], { type: "application/vnd.ms-excel" })
                    aLink.href = URL.createObjectURL(blob)
                    aLink.setAttribute('download', '裁剪损耗-成品半成品设置_' + new Date().toLocaleString() + '_.xlsx')
                    aLink.click()
                }
            },
        },
    };
</script>

<style lang="scss" scoped></style>
