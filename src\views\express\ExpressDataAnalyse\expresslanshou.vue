<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="filter.platform" placeholder="平台" style="width: 120px" disabled>
                        <el-option label="抖音" :value="6" />
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="支付开始日期" end-placeholder="支付结束日期" :picker-options="pickerOptions"
                        clearable style="width: 280px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-button>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="filter.expressCompanyName" style="width: 240px" :maxlength="40"
                        placeholder="物流公司" clearable/>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="importProps">导入聚水潭快递揽收信息</el-button>
            </el-button-group>
        </template>

        <vxetablebase ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true'
            @sortchange='sortchange' :tableData='tableData' :tableCols='tableCols' :isSelection="false"
            @summaryClick='onsummaryClick' :showsummary='true' :summaryarry='summaryarry' :isSelectColumn="false"
            style="width: 100%;  margin: 0" v-loading="listLoading" :height="'100%'">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getList" />
        </template>


        <!-- 导入数据 -->
        <el-dialog title="导入数据" :visible.sync="importDialog.visible" width="30%" v-dialogDrag
            v-loading="importDialog.loading">
            <el-upload class="upload-demo" action="/api/uploadnew/file/UploadCommonFileAsync" :limit="1"
                :on-remove="removeFile" :file-list="importDialog.fileList" accept=".xlsx" :http-request="uploadFile">
                <el-tooltip class="item" effect="dark" content="只能上传一个XLSX文件" placement="top-start">
                    <el-button size="small" type="primary">点击上传</el-button>
                </el-tooltip>
            </el-upload>
            <div class="btnGroup">
                <el-button @click="importDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="sumbit">确定</el-button>
            </div>
        </el-dialog>

        <!-- 趋势图 -->
        <el-drawer :title="chatProp.title" :visible.sync="chatProp.chatDialog" size="80%" :close-on-click-modal="false"
            direction="btt">
            <div v-if="!chatProp.chatLoading">
                <el-date-picker v-model="chatProp.chatTime" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="chatSearch($event, dialogType)"
                    :picker-options="pickerOptions" style="margin: 10px;" :clearable="false" />
                <buschar :analysisData="chatProp.data" v-if="!chatProp.chatLoading"></buschar>
            </div>
            <div v-else v-loading="chatProp.chatLoading"></div>
        </el-drawer>

    </my-container>
</template>
<script>
import {
    importExpressDataAnalyseDouYinLanShou, getExpressLanShouPageList, getExpressLanShouChart
} from '@/api/express/expressdataanalyse'
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { formatTime } from "@/utils";
import { getTime as get30chatdays } from '@/utils/getCols'
import { pickerOptions } from '@/utils/tools'
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from "dayjs";
import buschar from '@/components/Bus/buschar'
const tableCols = [
    { istrue: true, prop: 'platform', label: '平台', width: '200', sortable: 'custom', formatter: (row) => row.platformName },
    { istrue: true, prop: 'expressCompanyName', label: '物流公司', width: '300', sortable: 'custom' },
    { istrue: true, prop: 'orderCount', label: '总订单量', width: '200', sortable: 'custom', summaryEvent: true, },
    { istrue: true, prop: 'zhiFu_LanShou_24', label: '24小时揽收订单量', width: '200', sortable: 'custom', summaryEvent: true, },
    { istrue: true, prop: 'zhiFu_LanShou_24Rate', label: '24小时支-揽率', width: '200', sortable: 'custom', formatter: (row) => row.zhiFu_LanShou_24Rate + "%", summaryEvent: true, },
    {
        istrue: true, label: '趋势图', width: '70', type: 'button', btnList:
            [
                { istrue: true, label: '趋势图', handle: (that, row) => that.openDialog(row) },
            ]
    },
];
export default {
    name: "expressroute",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, vxetablebase, buschar },
    data() {
        return {
            that: this,
            filter: {
                timerange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')],
                startDate: null,
                endDate: null,
                platform: 6,
                expressCompanyName: null,
            },
            tableData: [],
            tableCols: tableCols,
            total: 0,
            summaryarry: { count_sum: 10 },
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selids: [],
            pickerOptions,
            importDialog: {
                visible: false,
                loading: false,
                fileList: [],
                file: null,
            },
            chatProp: {
                title: "",
                chatDialog: false,//趋势图弹窗
                chatTime: null,//趋势图时间
                chatLoading: true,//趋势图loading
                data: [],//趋势图数据
            },
            chatFilter: {

            }
        };
    },
    async mounted() {
    },
    methods: {
        async getList() {
            if (this.filter.timerange && this.filter.timerange.length == 2) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.$message({ message: "请选择日期", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            this.listLoading = true;
            const res = await getExpressLanShouPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.tableData = res.data.list;
            // this.summaryarry = res.data.summary;

            let summary = res.data.summary || {}

            const resultsum = {};
            Object.entries(summary).forEach(([key, value]) => {
                console.log(value, 1111)
                if(value.toString().indexOf("%")>-1){
                    resultsum[key] = value;
                    return;
                }
                resultsum[key] = formatNumber(value);
            });
            function formatNumber(number) {
                const options = {
                    useGrouping: true,
                };
                return new Intl.NumberFormat('zh-CN', options).format(number);
            }
            this.summaryarry = resultsum
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getList();
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async chatSearch() {
            this.chatFilter.startDate = dayjs(this.chatProp.chatTime[0]).format('YYYY-MM-DD')
            this.chatFilter.endDate = dayjs(this.chatProp.chatTime[1]).format('YYYY-MM-DD')
            this.chatProp.chatDialog = true;
            this.chatProp.chatLoading = true;
            const data = await getExpressLanShouChart(this.chatFilter);
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },
        async openDialog(row) {
            this.chatProp.title = row.expressCompanyName;
            this.chatProp.chatDialog = true;
            let newChatTime = get30chatdays([this.filter.startDate, this.filter.endDate]);
            this.chatProp.chatTime = newChatTime;
            this.chatFilter = {
                startDate: newChatTime[0],
                endDate: newChatTime[1],
                platform: 6,
                expressCompanyName: this.filter.expressCompanyName,
                expressCompanyName2: row.expressCompanyName,
            }
            this.chatProp.chatLoading = true;
            const data = await getExpressLanShouChart(this.chatFilter);
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },
        async onsummaryClick() {
            this.chatProp.title = "趋势图";
            this.chatProp.chatDialog = true;
            let newChatTime = get30chatdays([this.filter.startDate, this.filter.endDate]);
            this.chatProp.chatTime = newChatTime;
            this.chatFilter = {
                startDate: newChatTime[0],
                endDate: newChatTime[1],
                platform: 6,
                expressCompanyName: this.filter.expressCompanyName,
                expressCompanyName2: "",
            }
            this.chatProp.chatLoading = true;
            const data = await getExpressLanShouChart(this.chatFilter);
            this.chatProp.data = data;
            this.chatProp.chatLoading = false;
        },

        importProps() {
            this.importDialog.fileList = []
            this.importDialog.file = null
            this.importDialog.visible = true
        },
        async uploadFile(data) {
            this.importDialog.file = data.file
        },
        async sumbit() {
            if (this.importDialog.file == null) return this.$message.error('请上传文件')
            this.$message.info('正在上传中,请稍后...')
            const form = new FormData();
            form.append("file", this.importDialog.file);
            this.importDialog.loading = true
            await importExpressDataAnalyseDouYinLanShou(form).then(({ success }) => {
                if (success) {
                    this.$message.success('上传成功，正在排队导入中...')
                    this.importDialog.visible = false
                }
                this.importDialog.loading = false
            }).catch(err => {
                this.importDialog.loading = false
                this.$message.error('导入失败')
            })
        },
        importProps() {
            this.importDialog.fileList = []
            this.importDialog.file = null
            this.importDialog.visible = true
        },
        removeFile(file, fileList) {
            this.importDialog.file = null
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.btnGroup {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
