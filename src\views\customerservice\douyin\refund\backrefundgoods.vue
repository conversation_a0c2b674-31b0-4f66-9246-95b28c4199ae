<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="filter">
            </el-form>
        </template>
        <!--列表-->
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='false' @sortchange='sortchange'
            :summaryarry="summaryarry" :tableData='pagelist' @select='selectchange' :isSelection='false'
            :tableCols='tableCols' :loading="listLoading">
            <template slot='extentbtn'>
                <el-button-group>
                    <el-button style="padding: 0;margin: 0;border:0">
                      <el-date-picker v-model="filter.timerange" type="daterange" unlink-panels range-separator="至"
                          start-placeholder="开始时间" end-placeholder="结束时间"
                          style="width: 250px" :value-format="'yyyy-MM-dd'" @change="changeTime">
                      </el-date-picker>
                    </el-button>

                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-input v-model.trim="filter.goodsCode" placeholder="商品编码" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-input v-model.trim="filter.goodsName" placeholder="商品名称" style="width:160px;" clearable
                            :maxlength="50" />
                    </el-button>
                    <el-button style="padding: 0;margin: 0;border:0">
                        <el-select v-model="filter.batchStr" placeholder="批次号" style="width:120px;" filterable
                            clearable>
                            <el-option v-for="item in pettyPaymentList" :key="item.batchStr" :label="item.batchStr"
                                :value="item.batchStr">
                            </el-option>
                        </el-select>
                    </el-button>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                    <el-button type="primary" @click="onExportOnlyRefund">导出</el-button>
                </el-button-group>
            </template>
        </ces-table>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="getpageList" />
        </template>
    </my-container>
</template>
<script>
import datepicker from '@/views/customerservice/datepicker'
import cesTable from "@/components/Table/table.vue";
import { formatTime } from "@/utils";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import dayjs from 'dayjs'
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import {
    getBackRefundGoodsPageList, exportBackRefundGoodsList
} from '@/api/customerservice/douyinrefund'

const tableCols = [
    { istrue: true, prop: 'goodsCode', label: '商品编码', width: '130', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbackrefundorder(row) },
    { istrue: true, prop: 'goodsName', label: '商品名称', width: '390', sortable: 'custom' },
    { istrue: true, prop: 'backOrderCount', label: '退货退款单量-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
             const number=row.backOrderCountProportion>0 && row.backOrderCountProportion!=null ? row.backOrderCountProportion :0;
             return row.backOrderCount+' / '+ number + " %"
        },
     },
    { istrue: true, prop: 'backAllAmount', label: '退货退款总金额-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
                const number=row.backAllAmountProportion>0 && row.backAllAmountProportion!=null ? row.backAllAmountProportion :0;
             return row.backAllAmount+' / '+ number + " %"
        },
        handle: (that, row) => that.jumpbackallamount(row)  
    },
    { istrue: true, prop: 'backExpressCount', label: '退回快递-占比', width: '170', sortable: 'custom',
        formatter: (row) => {
             const number=row.backExpressCountProportion>0 && row.backExpressCountProportion!=null ? row.backExpressCountProportion :0;
          return row.backExpressCount+' / '+number+"%";
        },
     },
    { istrue: true, prop: 'backExpressComCount', label: '退货物流公司', width: '120', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbackrefundgoodcom(row) },
    { istrue: true, prop: 'backSaleAfterReasonCount', label: '退货退款售后原因', width: '135', sortable: 'custom', type: 'click', handle: (that, row) => that.jumpbackrefundgoodsreason(row) },
    { istrue: true, prop: 'backYesOrderCount', label: '已退货退款单量-占比', width: '170', sortable: 'custom',
         formatter: (row) => {
             const number=row.backYesOrderCountProportion >0 && row.backYesOrderCountProportion!=null ? row.backYesOrderCountProportion :0;
          return row.backYesOrderCount+' / '+number+'%';
        },
     },
    { istrue: true, prop: 'backNoOrderCount', label: '未退货退款单量-占比', width: '170', sortable: 'custom',
         formatter: (row) => {
            const number=row.backNoOrderCountProportion>0 && row.backNoOrderCountProportion!=null ? row.backNoOrderCountProportion :0;
          return row.backNoOrderCount+' / '+ number +'%';
        },
     },
    { istrue: true, prop: 'backYesOrderAmount', label: '已退货退款金额-占比', width: '170', sortable: 'custom',
         formatter: (row) => {
            const number=row.backYesOrderAmountProportion>0 && row.backYesOrderAmountProportion!=null ? row.backYesOrderAmountProportion :0;
          return row.backYesOrderAmount+' / '+ number +'%';
        },
     },
    { istrue: true, prop: 'backNoOrderAmount', label: '未退货退款金额-占比', width: '170', sortable: 'custom',
         formatter: (row) => {
            const number=row.backNoOrderAmountProportion>0 && row.backNoOrderAmountProportion!=null ? row.backNoOrderAmountProportion :0;
          return row.backNoOrderAmount+' / '+ number +'%';
        },
     },
];
export default {
    name: "backrefundgoods",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, datepicker },
    props: ["batchStrBaseList"],
    data() {
        return {
            that: this,
            pageLoading: false,
            filter: {
                timerange: [],
                startDate: null,
                endDate: null,
                goodsCode: null,
                goodsName: null,
                batchStr: null,
            },
            tableCols: tableCols,
            listLoading: false,
            pagelist: [],
            pettyPaymentList: [],
            total: 0,
            summaryarry: {},
            pager: { OrderBy: "backOrderCount", IsAsc: false },
            sels: [], // 列表选中列
            selids: [],
        };
    },
    async mounted() {
      if (this.filter.timerange.length == 0) {
            this.filter.startDate = dayjs().format('YYYY-MM-DD')
            this.filter.endDate = dayjs().format('YYYY-MM-DD')
            this.filter.timerange = [this.filter.startDate, this.filter.endDate]
            this.batchScreening()
        }
        this.onSearch();
    },
    methods: {
        updateFilterMonthDay(row, timerange) {
          this.filter.timerange = timerange
          this.filter.goodsCode = row.styleProductCode
          this.onSearch();
        },
        async changeTime(e) {
          this.filter.startDate = e ? e[0] : null
          this.filter.endDate = e ? e[1] : null
          this.filter.timerange = e
          this.batchScreening()
        },
        batchScreening() {
          let dates = [ new Date(this.filter.timerange[0]), new Date(this.filter.timerange[1] + ' 23:59:59') ];
          this.pettyPaymentList = this.batchStrBaseList.filter(item => {
            let createdTime = new Date(item.createdTime);
            return createdTime >= dates[0] && createdTime <= dates[1];
          });
        },
        getParam() {
            if (this.filter.timerange && this.filter.timerange.length > 0) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            }
            else {
                this.filter.startDate = null;
                this.filter.endDate = null;
                this.$message({ message: "请先选择日期！", type: "warning", });
                return;
            }
            const para = { ...this.filter };
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para
            };
            return params;
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.getpageList();
        },
        async getpageList() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await getBackRefundGoodsPageList(params);
            this.listLoading = false;
            this.total = res.data.total
            this.pagelist = res.data.list;
            this.summaryarry = res.data.summary;
        },
        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        jumpbackrefundorder(row) {
            row.startDate = this.filter.startDate;
            row.endDate = this.filter.endDate;
            window.showlist_backrefundorder(row);
            window.showtab_backrefundorder();
        },
        async onExportOnlyRefund() {
            const params = this.getParam();
            this.listLoading = true;
            const res = await exportBackRefundGoodsList(params);
            this.listLoading = false;
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '抖音退货退款商品数据_' + new Date().toLocaleString() + '.xlsx')
            aLink.click()
        },
        jumpbackallamount(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/backrefundgoodsbackallamount.vue`,
                title: row.goodsCode,
                args: { filter: this.filter, goodsCode: row.goodsCode , goodsName: row.goodsName },
                height: '480px',
                width: '900px',
            });
        },
        jumpbackrefundgoodsreason(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/backrefundgoodsreason.vue`,
                title: row.goodsCode,
                args: { filter: this.filter, goodsCode: row.goodsCode , goodsName: row.goodsName },
                height: '480px',
                width: '900px',
            });
        },
        jumpbackrefundgoodcom(row) {
            this.$showDialogform({
                path: `@/views/customerservice/douyin/refund/backrefundgoodscom.vue`,
                title: row.goodsCode,
                args: { filter: this.filter, goodsCode: row.goodsCode , goodsName: row.goodsName },
                height: '480px',
                width: '900px',
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}
</style>
