<template>
    <div style="padding: 20px 0; width: 100%; display: flex; flex-direction: column; height: 100%;">
        <el-scrollbar style="height: 100%">
            <el-form :model="ruleForm" :rules="rules" ref="refruleForm" label-width="160px" class="demo-ruleForm">
                <el-form-item label="区域：" prop="regionName" :rules="[{ required: true, message: '区域不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.regionName" placeholder="区域" class="publicCss" collapse-tags>
                        <el-option v-for="item in regionNameList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工具名称：" prop="toolName" :rules="[{ required: true, message: '工具名称不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.toolName"
                    :maxlength="50" placeholder="工具名称" clearable />
                </el-form-item>
                <el-form-item label="工具中文名称：" prop="toolZhName" :rules="[{ required: true, message: '工具中文名称不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.toolZhName"
                    :maxlength="50" placeholder="工具中文名称" clearable />
                </el-form-item>
                <el-form-item label="工具描述：" prop="toolDesc" :rules="[{ required: true, message: '工具描述不能为空', trigger: 'blur' }]">
                    <el-input style="width:80%;"
                     type="textarea"
                    :autosize="{ minRows: 4, maxRows: 11 }"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.toolDesc"
                     placeholder="工具描述" clearable />
                </el-form-item>
                <el-form-item label="工具类型：" prop="type" :rules="[{ required: true, message: '工具类型不能为空', trigger: 'blur' }]">
                    <el-select v-model="ruleForm.type" placeholder="工具类型" class="publicCss" collapse-tags>
                        <el-option v-for="item in typeList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="API地址：" prop="interfaceUrl" v-if="ruleForm.type == 'API'">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.interfaceUrl"
                    :maxlength="200" placeholder="API地址" clearable />
                </el-form-item>
                <el-form-item label="请求参数：" prop="bodyParam" v-if="ruleForm.type == 'API'">
                    <el-input style="width:80%;"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 11 }"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.bodyParam"
                     placeholder="请求参数" clearable />
                </el-form-item>
                <el-form-item label="回复方式：" prop="llmConfig" v-if="ruleForm.type == 'API'">
                    <el-select v-model="ruleForm.replyMethod" placeholder="回复方式" class="publicCss" collapse-tags>
                        <el-option v-for="item in replyMethodList" :key="item" :label="item" :value="item" />
                    </el-select>

                    <!-- <el-select v-model="ruleForm.llmName" style="width: 40%;" placeholder="模型" class="publicCss" v-if="ruleForm.replyMethod && ruleForm.replyMethod != '无须分析'" collapse-tags>
                        <el-option v-for="item in llmNameList" :key="item" :label="item" :value="item" />
                    </el-select> -->
                </el-form-item>

                <!-- <el-form-item label="模型：" prop="llmConfig" v-if="ruleForm.type == 'MCP'">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.llmConfig"
                    :maxlength="50" placeholder="模型" clearable />
                </el-form-item> -->
                <el-form-item label="数据库：" prop="databaseName" v-if="ruleForm.type == 'MCP'">
                    <el-select v-model="ruleForm.databaseName" placeholder="数据库" @change="getTable" class="publicCss" clearable collapse-tags>
                        <el-option v-for="item in databaseList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="表名：" prop="tableNames" v-if="ruleForm.type == 'MCP'">
                    <el-select v-model="ruleForm.tableNames" placeholder="表名" multiple="true" class="publicCss" clearable collapse-tags>
                        <el-option v-for="item in tableList" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="默认时间范围：" prop="defaultTimeRange" v-if="ruleForm.type == 'MCP'">
                    <el-select v-model="ruleForm.defaultTimeRange" placeholder="默认时间范围" class="publicCss" clearable collapse-tags>
                        <el-option v-for="item in sectionList2" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item> -->
                
                <el-form-item label="租户：" prop="dbconConfig" v-if="ruleForm.type == 'MCP'">
                    <el-input style="width:80%;"
                     show-word-limit
                    :rows="4" v-model.trim="ruleForm.dbconConfig"
                    :maxlength="50" placeholder="租户" clearable />
                </el-form-item>

                <el-form-item label="提示词：" prop="promptTemplate" v-if="ruleForm.type == 'MCP'">
                    <el-input style="width:80%;"
                     type="textarea"
                     show-word-limit
                    :autosize="{ minRows: 4, maxRows: 11 }"
                    :rows="4" v-model.trim="ruleForm.promptTemplate"
                    placeholder="提示词" clearable />
                </el-form-item>
            </el-form>
        </el-scrollbar>
        <div style="display: flex;justify-content: end; margin: auto 30px 20px 0;">
            <el-button @click="cancellationMethod">取消</el-button>
            <my-confirm-button type="submit" @click="submitForm('refruleForm')" />
        </div>
    </div>
</template>

<script>
import inputNumberYh from "@/components/Comm/inputNumberYh.vue";
import MyConfirmButton from '@/components/my-confirm-button'
import { aiToolConfigSubmit, aiTableFieldConfigListValue } from '@/api/people/peoplessc.js';
import checkPermission from '@/utils/permission'
export default {
    name: 'departmentEdit',
    components: {
        inputNumberYh, MyConfirmButton
    },
    props: {
        editInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        districtList: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data() {
        return {
            regionNameList: ['南昌','深圳', '武汉'],
            typeList: ['MCP', 'API'],
            replyMethodList: ['内部模型', '外部模型', '无须分析'],
            llmNameList: [],
            sectionList2:['7 days','15 days','30 days'],
            databaseList: [],
            tableList: [],
            selectProfitrates: [],
            ruleForm: {
                label: '',
                name: ''
            },
            rules: {
                attendanceFigures: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalAmount: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                foreignObjects: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
                totalOrders: [
                    { required: true, message: '请输入', trigger: 'blur' }
                ],
            }
        }
    },

    async mounted() {
        await this.getxiala('databaseName', '', 'databaseList');
        this.$nextTick(() => {
            this.getxiala('tableName', this.ruleForm.databaseName, 'tableList');
            this.$refs.refruleForm.clearValidate();
        });
        this.ruleForm = { ...this.editInfo };
    },
    methods: {
        async getxiala(name, value, arr) {
          const { data } = await aiTableFieldConfigListValue(name, value)
          if (data) {
              this[arr] = data;
          }
        },
        async getTable(databasename) {
            const { data } = await aiTableFieldConfigListValue('tableName', databasename)
            console.log(21122121, data)
            if (data) {
                this['tableList'] = data;
            }
        },
        // getLLmList() {
        //     if (ruleForm.replyMethod == '内部模型') {
        //         this.llmNameList = ['qwen3:32b', ]
        //     } else if (ruleForm.replyMethod == '内部模型') {
        //         this.llmNameList = []
        //     } 
        // },
        cancellationMethod() {
            this.$emit('cancellationMethod');
        },
        submitForm(formName) {
            console.log(this.ruleForm.label, 'this.ruleForm.label');
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.ruleForm.isArchive = checkPermission("ArchiveStatusEditing")
                    if(this.ruleForm?.tableNames?.length>0){
                        this.ruleForm.tables = this.ruleForm.tableNames.join(',')
                    }else{
                        this.ruleForm.tables = ''
                    }
                    const { data, success } = await aiToolConfigSubmit(this.ruleForm)
                    if (!success) {
                        return
                    }
                    this.$message.success('操作成功')
                    await this.$emit("search");

                } else {
                    console.log('error submit!!');
                    this.$message.error('操作失败')
                    return false;
                }
            });
            //   this.$confirm('是否保存?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }).then(async () => {
            //     this.$refs[formName].validate(async(valid) => {
            //       if (valid) {
            //         const { data, success } = await aiToolConfigSubmit(this.ruleForm)
            //         if(!success){
            //             return
            //         }
            //         await this.$emit("search");

            //       } else {
            //         console.log('error submit!!');
            //         return false;
            //       }
            //     });
            //   }).catch(() => {
            //   });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
    }
}
</script>
<style scoped lang="scss">
.publicCss {
    width: 80%;
}
</style>
