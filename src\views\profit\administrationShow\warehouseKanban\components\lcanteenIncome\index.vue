<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" :clearable="false"
          @change="changeTime">
        </el-date-picker>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
        <el-button type="primary" @click="downExcel">模板下载</el-button>
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
    </template>

    <vxe-table border show-footer width="100%" height="100%" ref="newtable" :row-config="{ height: 40 }" show-overflow
      :loading="loading" :column-config="{ resizable: true }" :merge-footer-items="mergeFooterItems" :data="tableData"
      :footer-data="footerData">
      <vxe-column field="date" width="100" title="日期"></vxe-column>
      <vxe-colgroup title="邮政收银系统">
        <vxe-column field="postOrderCount" width="100" title="订单数"></vxe-column>
        <vxe-column field="postAmount" width="100" title="金额"></vxe-column>
        <vxe-column field="postSystemDiff" width="100" title="邮政系统差异"></vxe-column>
        <vxe-column field="postActualAmount" width="100" title="实际金额"></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="我司收款码">
        <vxe-column field="companyQrOrderCount" width="100" title="订单数"></vxe-column>
        <vxe-column field="companyQrAmount" width="100" title="金额"></vxe-column>
      </vxe-colgroup>
      <vxe-column field="subtotalAmount" width="100" title="小计金额"></vxe-column>
      <vxe-column field="customerUnitPrice" width="100" title="客单价"></vxe-column>
      <vxe-colgroup title="餐盒(保税)">
        <vxe-column field="boxBondedQty" width="100" title="数量"></vxe-column>
        <vxe-column field="boxBondedPrice" width="100" title="单价"></vxe-column>
        <vxe-column field="boxBondedAmount" width="100" title="金额"></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="餐盒(昀晗)">
        <vxe-column field="boxYunhanQty" width="100" title="数量"></vxe-column>
        <vxe-column field="boxYunhanPrice" width="100" title="单价"></vxe-column>
        <vxe-column field="boxYunhanAmount" width="100" title="金额"></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="餐盒(棒杰)">
        <vxe-column field="boxBangjieQty" width="100" title="数量"></vxe-column>
        <vxe-column field="boxBangjiePrice" width="100" title="单价"></vxe-column>
        <vxe-column field="boxBangjieAmount" width="100" title="金额"></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="餐盒数据合计">
        <vxe-column field="boxTotalQty" width="100" title="数量合计"></vxe-column>
        <vxe-column field="boxTotalAmount" width="100" title="餐盒金额合计"></vxe-column>
      </vxe-colgroup>
      <vxe-column field="otherIncome" width="100" title="其他收入"></vxe-column>
      <vxe-column field="dailyIncomeTotal" width="100" title="当天收入合计"></vxe-column>
      <vxe-column field="remarks" width="200" title="备注"></vxe-column>
      <vxe-column title="操作" width="100" footer-align="left" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" style="color:red" @click="handleRemove(scope.row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
      <lcanteenIncomeEdit ref="lcanteenIncomeEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
        @cancellationMethod="dialogVisibleEdit = false" />
    </el-drawer>
    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import dayjs from 'dayjs'
import { downloadLink } from "@/utils/tools.js";
import { warehouseCanteenIncomeDetailPage, warehouseCanteenIncomeDetailImport, warehouseCanteenIncomeDetailRemove } from '@/api/people/peoplessc.js';
import lcanteenIncomeEdit from "./lcanteenIncomeEdit.vue";
import checkPermission from '@/utils/permission'
const time = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
export default {
  name: "kdeliveryDataIndex",
  components: {
    MyContainer, lcanteenIncomeEdit
  },
  props: {
    // 可以添加props如果需要从父组件传递数据
  },
  data() {
    return {
      footerData: [],
      timeRanges: [time, time],
      // 工具函数
      downloadLink,
      // 对话框状态
      dialogVisibleEdit: false,
      dialogVisible: false,
      // 编辑相关
      editInfo: {},
      // 文件上传
      fileList: [],
      uploadLoading: false,
      // 表格数据
      tableData: [],
      mergeFooterItems: [],
      // 加载状态
      loading: false,
      exportloading: false,
      // 查询条件
      ListInfo: {
        startDate: time,
        endDate: time,
      }
    }
  },
  computed: {
    // 是否有选择的文件
    hasSelectedFile() {
      return this.fileList.length > 0;
    },
    // 是否可以导出
    canExport() {
      return this.tableData.length > 0;
    },
  },

  async mounted() {
    await this.getList()
  },
  methods: {
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    //上传文件
    onUploadRemove() {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess() {
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      try {
        const form = new FormData();
        form.append("file", item.file);
        const res = await warehouseCanteenIncomeDetailImport(form);
        if (res?.success) {
          this.$message({ message: res.msg || "导入成功", type: "success" });
          this.dialogVisible = false;
          await this.getList()
        } else {
          this.$message({ message: res?.msg || "导入失败", type: "error" });
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        this.$message({ message: "文件上传失败，请重试", type: "error" });
      } finally {
        this.uploadLoading = false
      }
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    downExcel() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250719/1946513675834073088.xlsx', '邮政食堂费用收入明细-导入模板.xlsx');
    },
    async exportExcel() {
      this.exportloading = true;
      this.$refs.newtable.exportData({ filename: '仓储行政看板-邮政食堂费用收入明细数据' + new Date().toLocaleString(), sheetName: 'Sheet1', type: 'xlsx' })
      this.$nextTick(() => {
        this.exportloading = false;
      })
    },
    closeGetlist() {
      this.dialogVisibleEdit = false;
      this.getList()
    },
    handleEdit(row) {
      this.editInfo = row;
      this.dialogVisibleEdit = true;
    },
    async handleRemove(row) {
      this.$confirm('是否删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (!row || !row.id) {
          this.$message.error('删除失败：数据异常');
          return;
        }
        try {
          this.loading = true
          const { success, msg } = await warehouseCanteenIncomeDetailRemove({ ids: row.id })

          if (success) {
            this.$message.success(msg || '删除成功')
            await this.getList();
          } else {
            this.$message.error(msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败，请重试');
        } finally {
          this.loading = false
        }
      }).catch(() => {
      });
    },
    async getList() {
      try {
        this.loading = true
        const { data, success, msg } = await warehouseCanteenIncomeDetailPage(this.ListInfo)
        if (success && data) {
          this.tableData = data.list || []
          this.footerData = data.summary || []
          const a = ['boxBangjiePrice', 'boxYunhanPrice', 'boxBondedPrice', 'customerUnitPrice']
          this.footerData.forEach((item) => {
            a.forEach(field => {
              if (item[field] !== null && item[field] !== undefined && !isNaN(item[field])) {
                item[field] = parseFloat(item[field]).toFixed(2)
              }
            })
          })
        } else {
          this.$message.error(msg || '获取列表失败')
          this.tableData = []
          this.footerData = []
        }
      } catch (error) {
        console.error('获取列表失败:', error);
        this.$message.error('获取列表失败，请重试');
        this.tableData = []
        this.footerData = []
      } finally {
        this.loading = false
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 150px;
    margin-right: 5px;
  }
}

:deep(.vxe-header--column) {
  background: #00937e;
  color: white;
  font-weight: 600;
}

:deep(.row-yellow1) {
  background-color: #f8e2d3;
}

:deep(.row-yellow5) {
  background-color: #00937e;
  color: white;
}

:deep(.vxe-footer--row) {
  background: #00937e;
  color: white;
  font-weight: 600;
}
</style>
