<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <span style="margin-right: 10px;">纠正时间</span>
        <el-date-picker v-model="timeRanges" type="datetimerange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
          style="width: 340px;margin-right: 10px;" format="yyyy-MM-dd HH:mm:ss" :value-format="'yyyy-MM-dd HH:mm:ss'"
          @change="changeTime($event, 1)" class="publicCss">
        </el-date-picker>
        <el-date-picker v-model="shootRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="拍摄开始时间" end-placeholder="拍摄结束时间" :picker-options="pickerOptions" style="width: 340px;"
          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" @change="changeTime($event, 2)"
          class="publicCss">
        </el-date-picker>
        <el-date-picker v-model="imageRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="识图开始时间" end-placeholder="识图结束时间" :picker-options="pickerOptions" style="width: 340px;"
          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" @change="changeTime($event, 3)"
          class="publicCss">
        </el-date-picker>
        <el-select v-model="ListInfo.createUserName" placeholder="请选择操作人" filterable class="publicCss">
          <el-option v-for="item in consumableData" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-button style="width: 80px;" type="primary" @click="getList('search')">搜索</el-button>
        <el-button style="width: 80px;" @click="reset">重置</el-button>
      </div>
    </template>
    <vxe-table border show-overflow show-header-overflow show-footer-overflow show-footer ref="xTable" height="100%"
      :sort-config="{ sortMethod: customSortMethod }" :loading="loading" :data="tableData"
      :row-config="{ height: 160, isCurrent: true, isHover: true }" :scroll-x="{ enabled: true, gt: 100 }"
      :scroll-y="{ enabled: true, gt: 100 }" :footer-method="footerMethod">
      <vxe-column field="correctionTime" title="纠正时间" width="150" show-overflow="title" sortable
        :align="'center'"></vxe-column>
      <vxe-column field="expressno" title="快递单号" width="150" show-overflow="title" sortable
        :align="'center'"></vxe-column>
      <vxe-column field="output_abnormal_path" title="快递图片" show-overflow="title" sortable>
        <template #default="{ row }">
          <el-image class="custom-image" slot="reference"
            :src="row.output_abnormal_path[0] || row.output_normal_path[0] || ''" :width="230" :height="130" fit="fill"
            :preview-src-list="row.output_abnormal_path != '' ? row.output_abnormal_path : row.output_normal_path != '' ? row.output_normal_path : ''">
          </el-image>
        </template>
      </vxe-column>
      <vxe-column field="beforeCorrectionPackage" title="纠正前耗材类型" width="130" show-overflow="title" sortable
        :align="'center'" show-header-overflow="ellipsis"></vxe-column>
      <vxe-column field="package" title="纠正后耗材类型" width="130" show-overflow="title" sortable :align="'center'"
        show-header-overflow="ellipsis"></vxe-column>
      <vxe-column field="packageCode" title="耗材编码" width="140" show-overflow="title"
        :align="'center'"></vxe-column>
      <vxe-column field="cost" title="耗材成本" width="110" show-overflow="title" sortable :align="'center'"></vxe-column>
      <vxe-column field="rltsimilarity" title="相似度" width="110" show-overflow="title" sortable :align="'center'"
        :formatter="({ cellValue }) => (cellValue * 100).toFixed(2) + '%'">
      </vxe-column>
      <vxe-column field="rltweight" title="重量(kg)" width="110" show-overflow="title" sortable
        :align="'center'"></vxe-column>
      <vxe-column field="correctionUserName" title="操作人" width="110" show-overflow="title" sortable
        :align="'center'"></vxe-column>
      <vxe-column field="rltshoottime" title="拍摄时间" width="150" show-overflow="title" sortable
        :align="'center'"></vxe-column>
      <vxe-column field="rltmaptime" title="识图时间" width="150" show-overflow="title" sortable
        :align="'center'"></vxe-column>
    </vxe-table>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import dayjs from 'dayjs'
import XEUtils from 'xe-utils'
import { pageExpressPackageMaterialCheckPackage, getExpressPackageCorrectionSelectList } from '@/api/express/expressPackage'

export default {
  name: "expressPackageCheck",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      consumableData: [],
      imageRanges: [],//识图时间
      shootRanges: [],//拍摄时间
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        correctionTimeStart: null,//开始时间
        correctionTimeEnd: null,//结束时间
        shootTimeStart: null,//拍摄开始时间
        shootTimeEnd: null,//拍摄结束时间
        mapTimeStart: null,//识图开始时间
        mapTimeEnd: null,//识图结束时间
        createUserName: null,//操作人
      },
      summaryarry: {},
      timeRanges: [],
      tableData: [],
      total: 0,
      loading: false,
      pickerOptions,
      lastSortArgs: {
        field: "",
        order: "",
      },
    }
  },
  async mounted() {
    //默认给近7天时间
    this.ListInfo.correctionTimeStart = dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss')
    this.ListInfo.correctionTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss')
    this.timeRanges = [this.ListInfo.correctionTimeStart, this.ListInfo.correctionTimeEnd]
    const res = await getExpressPackageCorrectionSelectList()
    if (res.success) {
      this.consumableData = res.data.map(item => { return { label: item.updateUserName, value: item.updateUserName }; });
    }
    await this.getList()
  },
  methods: {
    //自定义排序
    customSortMethod({ data, sortList }) {
      if (sortList && sortList.length > 0) {
        if (sortList[0].field != this.lastSortArgs.field || sortList[0].order != this.lastSortArgs.order) {
          this.lastSortArgs = { ...sortList[0] };
          let a = {
            order: (this.lastSortArgs.order.indexOf('desc') > -1 ? 'descending' : 'asc'),
            prop: this.lastSortArgs.field
          };
          this.ListInfo.orderBy = a.prop
          this.ListInfo.isAsc = a.order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      }
    },
    //重置
    reset() {
      this.ListInfo = {
        correctionTimeStart: null,//开始时间
        correctionTimeEnd: null,//结束时间
        shootTimeStart: null,//拍摄开始时间
        shootTimeEnd: null,//拍摄结束时间
        mapTimeStart: null,//识图开始时间
        mapTimeEnd: null,//识图结束时间
        createUserName: null,//操作人
      }
      this.shootRanges = []
      this.imageRanges = []
      this.timeRanges = []
      this.getList()
    },
    //合计
    footerMethod({ columns, data }) {
      // debugger
      const sums = [];
      if (!this.summaryarry)
        return sums
      var arr = Object.keys(this.summaryarry);
      if (arr.length == 0)
        return sums
      var hashj = false;
      columns.forEach((column, index) => {
        if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
          var sum = this.summaryarry[column.property + '_sum'];
          if (sum == null) return;
          sums[index] = sum
        }
        else sums[index] = ''
      });
      return [sums]
    },
    //时间改变
    async changeTime(e, val) {
      if (val == 1) {
        if (e) {
          e[1] = dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          this.ListInfo.correctionTimeStart = e[0]
          this.ListInfo.correctionTimeEnd = e[1]
        } else {
          this.ListInfo.correctionTimeStart = null
          this.ListInfo.correctionTimeEnd = null
        }
      } else if (val == 2) {
        if (e) {
          e[1] = dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          this.ListInfo.shootTimeStart = e[0]
          this.ListInfo.shootTimeEnd = e[1]
        } else {
          this.ListInfo.shootTimeStart = null
          this.ListInfo.shootTimeEnd = null
        }
      } else if (val == 3) {
        if (e) {
          e[1] = dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          this.ListInfo.mapTimeStart = e[0]
          this.ListInfo.mapTimeEnd = e[1]
        } else {
          this.ListInfo.mapTimeStart = null
          this.ListInfo.mapTimeEnd = null
        }
      }
    },
    //获取列表
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
      }
      this.loading = true
      const { data, success } = await pageExpressPackageMaterialCheckPackage(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.tableData.forEach(item => {
          if (!Array.isArray(item.output_abnormal_path)) {
            item.output_abnormal_path = [item.output_abnormal_path];
          }
          if (!Array.isArray(item.output_normal_path)) {
            item.output_normal_path = [item.output_normal_path];
          }
        })
        this.loading = false
      } else {
        //获取列表失败
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  align-items: center;

  .publicCss {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .custom-image img {
  max-width: 230px !important;
  max-height: 150px !important;
}
</style>
