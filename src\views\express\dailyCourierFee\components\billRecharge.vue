<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="付款开始日期" end-placeholder="付款结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div>
          <queryCondition ref="refqueryCondition" :valueChanged.sync="topfilter" />
        </div>
        <!-- <el-select v-model="ListInfo.expressCompanyId" clearable filterable placeholder="快递公司" class="publicCss"
          @change="getprosimstatelist(2)">
          <el-option v-for="item in expresscompanylist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.prosimstateId" clearable filterable placeholder="快递站点" class="publicCss">
          <el-option label="暂无站点" value="" />
          <el-option v-for="item in prosimstatelist" :key="item.id" :label="item.stationName" :value="item.id" />
        </el-select>
        <el-select v-model="ListInfo.warehouseId" clearable filterable placeholder="发货仓库" class="publicCss">
          <el-option v-for="item in warehouselist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-select v-model="ListInfo.status" clearable filterable placeholder="审批状态" class="publicCss">
          <el-option label="未发起" value="0" />
          <el-option label="撤销/驳回" value="2" />
          <el-option label="审批中" value="1" />
          <el-option label="已通过" value="3" />
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="onAddMethod">新增</el-button>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'billRecharge202411010929'" :tablekey="'billRecharge202411010929'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'" @cellStyle="renderRefundStatus" cellStyle>
      <template slot="right">
        <vxe-column title="操作" width="120">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" v-if="row.status == 0 || row.status == 2" @click="onEdit(row)">编辑</el-button>
              <el-button type="text" v-if="row.status == 0 || row.status == 2" @click="onDelete(row)"><span
                  style="color: red;">删除</span></el-button>
              <el-button type="text" v-if="row.status == 1 || row.status == 3" @click="onView(row)">查看</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog :title="ruleTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag style="margin-top: -13vh;"
      :close-on-click-modal="false">
      <div>
        <topUpApproval ref="reftopUpApproval" v-if="dialogVisible" :infoFormData="infoFormData"
          @closeCallback="closeCallback" :forbidden="forbidden" :verifyWhether="verifyWhether" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced(0)" :disabled="forbidden">暂 存</el-button>
        <el-button type="primary" @click="onStorageMethodDebounced(1)" :disabled="forbidden">发起流程</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import { addOrUpdateExpressFeeRecharge, deleteExpressFeeRecharge, getExpressFeeRechargeList, getExpressComanyAll, getExpressComanyStationName, getExpressDayBillsSummary, getExpressBankInfoList } from "@/api/express/express";
import dayjs from 'dayjs'
import { formatTime } from "@/utils";
import { warehouselist, formatWarehouseNew } from "@/utils/tools";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
import decimal from "@/utils/decimalToFixed"
import topUpApproval from "./topUpApproval.vue";
import queryCondition from "./queryCondition.vue";

const tableCols = [
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'expressCompanyId', label: '快递公司', formatter: (row) => row.expressCompanyName ? row.expressCompanyName : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'prosimstateId', label: '快递站点', formatter: (row) => row.prosimstateName ? row.prosimstateName : '', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseId', label: '发货仓库', formatter: (row) => formatWarehouseNew(row.warehouseId), },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'payTime', label: '付款时间', },
  { sortable: 'custom', width: 'auto', align: 'left', prop: 'createdUserName', label: '发起人', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'status', label: '审批状态', formatter: (row) => row.status == 1 ? '审批中' : row.status == 2 ? '撤销/驳回' : row.status == 3 ? '已通过' : '未发起', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentType', label: '付款类型', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'costClassification', label: '费用分类', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'warehouseClassification', label: '仓库分类', },
  { sortable: 'custom', width: 'auto', align: 'right', prop: 'quantity', label: '数量', },
  { sortable: 'custom', width: 'auto', align: 'right', prop: 'unitPrice', label: '单价', },
  { sortable: 'custom', width: 'auto', align: 'right', prop: 'amount', label: '金额（元）', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'paymentMethod', label: '支付方式', },
  { sortable: 'custom', width: 'auto', align: 'left', prop: 'accountName', label: '账户名', },
  { sortable: 'custom', width: 'auto', align: 'left', prop: 'accountNumber', label: '账号', },
  { sortable: 'custom', width: 'auto', align: 'left', prop: 'bankName', label: '开户行', },
  { sortable: 'custom', width: 'auto', align: 'left', prop: 'remark', label: '备注', },
]
export default {
  name: "billRecharge",
  components: {
    MyContainer, vxetablebase, uploadimgFile, topUpApproval, queryCondition
  },
  data() {
    return {
      topfilter: {
        expressCompanyId: null,
        prosimstate: null,
        warehouseId: null,
      },
      verifyWhether: false,//是否需要回显当前id明细数据
      timeRanges: [],
      infoFormData: {},
      ruleTitle: '新增',
      dialogVisible: false,//新增编辑弹窗
      forbidden: false,//是否禁用
      formatWarehouseNew,
      warehouselist,//发货仓库
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'payTime',
        isAsc: false,
        expressCompanyId: null,
        prosimstateId: null,
        warehouseId: null,
        startTime: null,//开始时间
        endTime: null,//结束时间
      },
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      expresscompanylist: [],//快递公司
      prosimstatelist: [],//快递站点
    }
  },
  async mounted() {
    if (this.timeRanges && this.timeRanges.length == 0) {
      //默认给近7天时间
      this.ListInfo.startTime = dayjs().startOf('month').format('YYYY-MM-DD')
      this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
      this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
    }
    await this.getList()
    await this.init()
  },
  methods: {
    //防抖
    onStorageMethodDebounced: _.debounce(function (param) {
      this.onSingleSaves(param);
    }, 1000),
    closeCallback() {
      this.dialogVisible = false
      this.getList()
    },
    onSingleSaves(val) {
      let status = val//0:暂存 1:发起
      this.$nextTick(() => {
        this.$refs.reftopUpApproval.onSingleSave(status)
      })
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    onAddMethod() {
      this.ruleTitle = '新增'
      this.infoFormData = {}
      this.forbidden = false
      this.verifyWhether = false
      this.dialogVisible = true
    },
    async renderRefundStatus(row, column, callback) {
      if (row.status == 1 && column.field == 'status') {
        callback({ color: '#FFA500' })
      } else if (row.status == 3 && column.field == 'status') {
        {
          callback({ color: '#3CB371' })
        }
      } else if (column.field == 'status') {
        {
          callback({ color: '#808080' })
        }
      }
    },
    async onDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { success } = await deleteExpressFeeRecharge({ id: row.id })
        if (success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      });
    },
    onView(row) {
      this.ruleTitle = '查看'
      this.infoFormData = {}
      this.infoFormData = JSON.parse(JSON.stringify(row))
      this.forbidden = true
      this.verifyWhether = true
      this.dialogVisible = true
    },
    async onEdit(row) {
      let id = row.expressCompanyId
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
      this.ruleTitle = '编辑'
      this.infoFormData = {}
      this.infoFormData = JSON.parse(JSON.stringify(row))
      this.verifyWhether = true
      this.forbidden = false
      this.dialogVisible = true
      this.$forceUpdate()
    },
    async init() {
      this.$nextTick(() => {
        this.$refs.refqueryCondition.init()
      })
      const res = await getExpressComanyAll({});
      if (!res?.success) return
      this.expresscompanylist = res.data;
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }


      this.loading = true
      const { data, success } = await getExpressFeeRechargeList({ ...this.ListInfo, ...this.topfilter })
      this.loading = false
      if (success && data && data.list) {
        this.tableData = data.list
        this.total = data.total
        let summary = data.summary || {}

        const resultsum = {};
        Object.entries(summary).forEach(([key, value]) => {
            resultsum[key] = formatNumber(value);
        });
        function formatNumber(number) {
            const options = {
                useGrouping: true,
            };
            return new Intl.NumberFormat('zh-CN', options).format(number);
        }
        this.summaryarry = resultsum
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    async getprosimstatelist(val) {
      let id;
      if (val == 1) {
      } else if (val == 2) {
        id = this.ListInfo.expressCompanyId
        this.ListInfo.prosimstateId = null
      }
      let res = await getExpressComanyStationName({ id: id });
      if (res?.code) {
        this.prosimstatelist = res.data
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

.editCss {
  width: 100%;
}

.containerCss {
  max-height: 90px;
  height: 90px;
  overflow-x: auto;
}

::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
