<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" style="width: 250px;margin-right: 5px;"
          :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-input v-model.trim="ListInfo.proCode" clearable maxlength="50" placeholder="产品ID" class="publicCss" />
        <el-select filterable v-model="ListInfo.accountTypeist" placeholder="请选择账单类型" multiple collapse-tags
          clearable class="publicCss">
          <el-option label="运费" value="运费" />
          <el-option label="平台优惠补贴" value="平台优惠补贴" />
          <el-option label="支付渠道费" value="支付渠道费" />
          <el-option label="服务商佣金" value="服务商佣金" />
          <el-option label="分销佣金" value="分销佣金" />
          <el-option label="佣金" value="佣金" />
          <el-option label="商品实付/实退" value="商品实付/实退" />
          <el-option label="花呗分期手续费" value="花呗分期手续费" />
        </el-select>

        <el-select filterable v-model="ListInfo.billTypeList" placeholder="请选择账单项目" multiple collapse-tags clearable
          class="publicCss">
          <el-option label="佣金" value="佣金" />
          <el-option label="分销佣金" value="分销佣金" />
          <el-option label="服务商佣金" value="服务商佣金" />
          <el-option label="支付渠道费" value="支付渠道费" />
          <el-option label="花呗分期手续费" value="花呗分期手续费" />
          <el-option label="无" value="无" />
        </el-select>
        <el-input v-model.trim="ListInfo.ShopName" clearable maxlength="100" placeholder="店铺" class="publicCss" />
        <el-input v-model.trim="ListInfo.OrderNo" clearable maxlength="100" placeholder="订单号" class="publicCss" />
        <el-input v-model.trim="ListInfo.SpecificationId" clearable maxlength="100" placeholder="规格ID"
          class="publicCss" />
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="primary" @click="startImport">导入</el-button>
      </div>
    </template>
    <vxetablebase :id="'orderPayment202412051608'" :tablekey="'orderPayment202412051608'" ref="table" :that='that'
      :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
      style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-row>
          <el-col :xs="4" :sm="6" :md="8" :lg="6">

            <el-date-picker style="width: 100%" v-model="YearMonthDay" type="date" format="yyyyMMdd"
              value-format="yyyyMMdd" placeholder="选择日期"></el-date-picker>
          </el-col>
          <el-col :xs="8" :sm="6" :md="12" :lg="12">
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
              accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
              :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
              <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
              </template>
              <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { importBillingCharge, getBillingCharge_RedBook } from '@/api/bookkeeper/reportdayV2'
import { pickerOptions, formatTime, formatLinkProCode, formatProCodeStutas3 } from '@/utils/tools'
import dayjs from 'dayjs'
const tableCols = [
  { sortable: 'custom', width: '110', align: 'center', prop: 'yearMonthDay', label: '日期', formatter: (row) => formatTime(row.yearMonthDay, 'YYYY-MM-DD') },
  { istrue: true, prop: 'proCode', fix: true, label: '产品ID', width: '90', sortable: 'custom', type: 'html', formatter: (row) => row.status == 3 ? formatProCodeStutas3(row.proCode) : formatLinkProCode(21, row.proCode) },
  { sortable: 'custom', width: '110', align: 'center', prop: 'shopName', label: '店铺', },
  { sortable: 'custom', width: '60', align: 'center', prop: 'billingItem', label: '账单类型', },
  { sortable: 'custom', width: '60', align: 'center', prop: 'billType', label: '账单项目', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'orderNo', label: '订单号', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'afterSaleNumber', label: '售后单号', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'transactionType', label: '交易类型', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'settlementAccount', label: '结算账户', },
 // { sortable: 'custom', width: '110', align: 'center', prop: 'accountMovementAmount', label: '动账金额', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productName', label: '商品名称', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'productQuantity', label: '商品数量', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'amountIncome', label: '金额', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'category', label: '类目', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'skuBarcode', label: 'SKU条码', },
  { sortable: 'custom', width: '170', align: 'center', prop: 'specificationId', label: '规格ID', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'commissionBase', label: '计佣基数', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'orderTime', label: '下单时间', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'completionTime', label: '完成时间', },
  { sortable: 'custom', width: '130', align: 'center', prop: 'settlementTime', label: '结算时间', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'paidOrRefundedAmount', label: '商品实付/实退', },
  { sortable: 'custom', width: '110', align: 'center', prop: 'discountType', label: '优惠类型', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'merchantDiscount', label: '商家优惠', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'platformDiscountSubsidy', label: '平台优惠补贴', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'platformShippingSubsidy', label: '平台运费补贴', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'commissionRate', label: '佣金率', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'rebateRate', label: '返利率', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'totalCommission', label: '佣金总额', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'taxablePriceInclusive', label: '计税价格(含税)', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'taxablePriceExclusive', label: '计税价格(未税)', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'taxRate', label: '税率', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'crossBorderTaxPaid', label: '跨境税代缴', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'productTax', label: '商品税金', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'sellerCpsCommissionRate', label: '卖家CPS佣金率', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'distributionCommission', label: '分销佣金', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'promoterId', label: '推广达人ID', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'promoterNickname', label: '达人昵称', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'merchandisingType', label: '带货类型', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'serviceProviderCommission', label: '代运营服务商佣金', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'daiServiceProviderCommission', label: '代开发服务商佣金', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'shippingFee', label: '运费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'shippingTax', label: '运费税金', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'paymentChannelFee', label: '支付渠道费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'huabeiInstallmentFee', label: '花呗分期手续费', },
  { sortable: 'custom', width: '80', align: 'center', prop: 'remarks', label: '备注', },
]
export default {
  name: "orderPayment",
  components: {
    MyContainer, vxetablebase
  },
  data() {
    return {
      dialogVisible: false,
      uploadLoading: false,
      fileList: [],
      fileparm: {},
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startTime: null,//开始时间
        endTime: null,//结束时间
        ShopName: null,//店铺
        OrderNo: null,//订单号
        SpecificationId: null,//规格ID
        accountTypeist: [],
        billTypeList: [],
        proCode: null,
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      YearMonthDay: null,
    }
  },
  async mounted() {
    await this.getList()
  },
  methods: {
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {

      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      form.append("platform", 21);
      form.append("YearMonthDay", this.YearMonthDay);
      var res = await importBillingCharge(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async changeTime(e) {
      this.ListInfo.startTime = e ? e[0] : null
      this.ListInfo.endTime = e ? e[1] : null
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给昨天时间
        this.ListInfo.startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.ListInfo.endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
      }
      this.loading = true
      const { data, success } = await getBillingCharge_RedBook(this.ListInfo)
      if (success) {
        this.tableData = data.list
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
