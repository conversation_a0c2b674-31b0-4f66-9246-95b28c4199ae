<template>
    <my-container v-loading="pageLoading">
        <template #header>
        </template>

        <template style="margin-top: 10px;">
            <vxetablebase :id="'DeductOrderNodeDtlRept420241009001'" ref="table" :that='that' :isIndex='true'
                :hasexpand='true' @sortchange='sortchange' :summaryarry="summaryarry" :showsummary="true" @select="selectchange"
                :tableData='list' :tableCols='tableCols' :isSelection="true" :loading="listLoading">
            </vxetablebase>
        </template>

        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="selids.length" @get-page="getlist" />
        </template>
    </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import MyConfirmButton from '@/components/my-confirm-button';
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { formatTime, } from "@/utils";
import { GetDeductNodeNoPassTypeRpt } from "@/api/order/deductbefore";

const tableCols = [
    { istrue: true, prop: 'rptDate', label: '时间', width: '150', sortable: 'custom', formatter: (row) => formatTime(row.rptDate, "YYYY-MM-DD") },
    { istrue: true, prop: 'noPassType', label: '违规环节', width: '200', sortable: 'custom' },
    { istrue: true, prop: 'noPassNum', label: '违规次数', width: '120', sortable: 'custom' },
]

export default {
    name: 'DeductOrderNodeDtlRept4',
    components: { MyContainer, MyConfirmButton, vxetablebase },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            list: [],
            summaryarry: {},
            pager: { OrderBy: "noPassNum", IsAsc: false },
            tableCols: tableCols,
            total: 0,
            sels: [],
            selids: [],
            listLoading: false,
            pageLoading: false,
        };
    },
    async mounted() {
        //await this.onSearch()
    },
    methods: {
        //查询第一页
        async onSearch() {
            this.$refs.pager.setPage(1)
            await this.getlist();
        },
        //获取查询条件
        getCondition() {
            if (this.filter.timerange && this.filter.timerange.length > 1) {
                this.filter.startDate = this.filter.timerange[0];
                this.filter.endDate = this.filter.timerange[1];
            } else {
                this.$message({ message: "请先选择日期", type: "warning" });
                return false;
            }
            var pager = this.$refs.pager.getPager();
            var page = this.pager;
            const params = {
                ...pager,
                ...page,
                ... this.filter
            }
            console.log(params);
            return params;
        },
        //分页查询
        async getlist() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            params.noPassUserName = "";
            this.listLoading = true
            const res = await GetDeductNodeNoPassTypeRpt(params)
            this.listLoading = false
            if (!res?.success) {
                return
            }

            this.selids = [];
            this.sels = [];

            this.total = res.data.total;
            this.list = res.data.list;
            this.summaryarry = res.data.summary;
        },
        //排序查询
        async sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false };
            }
            await this.onSearch();
        },
        selectchange: function (rows, row) {
            this.selids = [];
            this.sels = [];
            //console.log(rows)
            rows.forEach(f => {
                this.selids.push(f.id);
                this.sels.push(f);
            })
        },
        async onExport() {
            var params = this.getCondition();
            if (params === false) {
                return;
            }
            this.listLoading = true
            const res = await ExportDeductBeforeZrList(params)
            this.listLoading = false
            if (!res?.data) return
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', new Date().toLocaleString() + '导出-违规明细.xlsx');
            aLink.click()
        },
    },
};
</script>

<style lang="scss" scoped></style>
