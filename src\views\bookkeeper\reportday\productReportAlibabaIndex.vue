<template>
  <my-container v-loading="pageLoading" style="height: 100%">
    <el-tabs v-model="activeName" style="height: 94%">
      <el-tab-pane label="阿里巴巴日报" name="first1" style="height: 100%">
        <productReportAlibaba ref="productReportAlibaba" style="height: 100%"></productReportAlibaba>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaOrderDayReport')">
        <AlibabaOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="AlibabaOrderDayReport" style="height: 100%">
        </AlibabaOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaGoodCodeDayReport')">
        <AlibabaGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AlibabaGoodCodeDayReport" style="height: 100%"></AlibabaGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaIdDayReport')">
        <AlibabaIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AlibabaIdDayReport" style="height: 100%"></AlibabaIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaShopDayReport')">
        <AlibabaShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AlibabaShopDayReport" style="height: 100%"></AlibabaShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first7" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaCommodityDayReport')">
        <AlibabaCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AlibabaCommodityDayReport" style="height: 100%"></AlibabaCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaDetailDayReport')">
        <AlibabaDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AlibabaDetailDayReport" style="height: 100%"></AlibabaDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first8" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaOutgoingprofitIDorderdetail')">
        <AlibabaOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="AlibabaOutgoingprofitIDorderdetail" style="height: 100%"></AlibabaOutgoingprofitIDorderdetail>
      </el-tab-pane>

      <el-tab-pane label="SKUS日报" name="first9" :lazy="true" style="height: 100%"
        v-if="checkPermission('AlibabaOrderDayReport')">
        <AlibabaSkusDayReport @ChangeActiveName2="ChangeActiveName2" ref="AlibabaSkusDayReport" style="height: 100%">
        </AlibabaSkusDayReport>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportAlibaba from "./productReportAlibaba.vue";
import AlibabaOrderDayReport from "./AlibabaOrderDayReport.vue";
import AlibabaSkusDayReport from "./AlibabaSkusDayReport.vue";
import AlibabaGoodCodeDayReport from "./AlibabaGoodCodeDayReport.vue";
import AlibabaIdDayReport from "./AlibabaIdDayReport.vue";
import AlibabaCommodityDayReport from "./AlibabaCommodityDayReport.vue";
import AlibabaOutgoingprofitIDorderdetail from "./AlibabaOutgoingprofitIDorderdetail.vue";
import AlibabaShopDayReport from "./AlibabaShopDayReport.vue";
import AlibabaDetailDayReport from "./AlibabaDetailDayReport.vue";
import middlevue from "@/store/middle.js"
export default {
  name: "productReportGCIndex",
  components: {
    MyContainer, productReportAlibaba, AlibabaOrderDayReport, AlibabaSkusDayReport, AlibabaGoodCodeDayReport, AlibabaIdDayReport, AlibabaShopDayReport, AlibabaDetailDayReport, AlibabaCommodityDayReport, AlibabaOutgoingprofitIDorderdetail
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'albb') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first8';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.AlibabaOrderDayReport.AlibabaGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.AlibabaDetailDayReport.AlibabaDetailDayReportArgument(activeName, No, Time)
    }
  },
};
</script>

<style lang="scss" scoped></style>
