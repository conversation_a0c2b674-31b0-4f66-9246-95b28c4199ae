<template>
    <MyContainer>
      <template #header>
        <div class="top">
          <el-input v-model.trim="ListInfo.firstLevelCategoryName" placeholder="一级类目名称" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.SecondLevelCategoryName" placeholder="二级类目名称" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.ThirdLevelCategoryName" placeholder="三级类目名称" maxlength="50" clearable class="publicCss" />
          <el-input v-model.trim="ListInfo.FourthLevelCategoryName" placeholder="四级类目名称" maxlength="50" clearable class="publicCss" />
          <el-select filterable v-model="ListInfo.platform" placeholder="请选择平台"  collapse-tags
               clearable style="width: 160px">
               <el-option label="快手" value="14"></el-option>
              <el-option label="视频号" value="20"></el-option>
            </el-select>
          <el-button type="primary" @click="getList('search')">搜索</el-button>
          <el-button type="primary" @click="startImport">导入</el-button>
          <el-button type="primary" @click="onAddnewMethod">新增</el-button>
        </div>
      </template>
      <vxetablebase ref="table" :id="'ksServiceRate202111241616'" tablekey="'ksServiceRate202111241616'"
        :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
        :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :summaryarry='summaryarry' :showsummary='true'
        style="width: 100%;  margin: 0" :loading="loading" :height="'100%'">
        <template slot="right">
          <vxe-column title="操作" width="120" fixed="right">
            <template #default="{ row, $index }">
              <div style="display: flex;justify-content: center;align-items: center;">
                <el-button type="text" @click="onEditMethod(row)">编辑</el-button>
                <el-button type="text" style="color: red;" @click="onDeleteMethod(row)">删除</el-button>
              </div>
            </template>
          </vxe-column>
        </template>
      </vxetablebase>
      <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template>
  
      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <span>
          <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
              <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-select filterable v-model="platform" placeholder="请选择平台"  collapse-tags
               clearable style="width: 160px">
               <el-option label="快手" value="14"></el-option>
              <el-option label="视频号" value="20"></el-option>
            </el-select>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
              @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
          </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
  
      <el-dialog :title="editName" :visible.sync="editdialogVisible" width="35%" v-dialogDrag>
        <div style="height: auto;width: 100%;" v-loading="editloading">
          <el-form :model="editform" ref="editform" :rules="editrules" label-width="150px" class="demo-ruleForm">
            <el-form-item label="一级类目" prop="firstLevelCategoryName">
              <el-select filterable v-model="editform.platform" placeholder="请选择平台"  collapse-tags  :disabled="editName == '编辑'" 
               clearable style="width: 160px">
               <el-option label="快手" value="14"></el-option>
              <el-option label="视频号" value="20"></el-option>
            </el-select>
            </el-form-item>
            <el-form-item label="一级类目" prop="firstLevelCategoryName">
              <el-input v-model.trim="editform.firstLevelCategoryName" placeholder="请输入一级类目" maxlength="50" clearable
                :disabled="editName == '编辑'" class="editCss" />
            </el-form-item>
            <el-form-item label="二级类目" prop="secondLevelCategoryName">
              <el-input v-model.trim="editform.secondLevelCategoryName" placeholder="请输入二级类目" maxlength="50" clearable
                :disabled="editName == '编辑'" class="editCss" />
            </el-form-item>
            <el-form-item label="三级类目" prop="thirdLevelCategoryName">
              <el-input v-model.trim="editform.thirdLevelCategoryName" placeholder="请输入三级类目" maxlength="50" clearable
                :disabled="editName == '编辑'" class="editCss" />
            </el-form-item>
            <el-form-item label="四级类目" prop="fourthLevelCategoryName">
                <el-input v-model.trim="editform.fourthLevelCategoryName" placeholder="请输入四级类目" maxlength="50" clearable
                :disabled="editName == '编辑'" class="editCss" />
            </el-form-item>
            <el-form-item label="技术服务费率" prop="technicalServiceRate">
                <el-input-number v-model="editform.technicalServiceRate" placeholder="请输入技术服务费率" :min="0" :max="99999999999999"
                  :controls="false" class="editCss" />
              </el-form-item>
              <el-form-item label="达人佣金服务费率" prop="talentCommissionServiceRate">
                <el-input-number v-model="editform.talentCommissionServiceRate" placeholder="请输入达人佣金服务费率" :min="0" :max="99999999999999"
                  :controls="false" class="editCss" />
              </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="editdialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="onSaveMethod">确 定</el-button>
        </span>
      </el-dialog>
    </MyContainer>
  </template>
  
  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
  import dayjs from 'dayjs'
  import { importksServiceRateAsync as importSeriesCodePackFeesync,  getksServiceRateList as getSeriesCodePackFeeList,  addorEditksServiceRate as addorEditSeriesCodePackFee,deleteksServiceRateBatchAsync as  deleteSeriesCodePackFeeBatchAsync } from '@/api/bookkeeper/reportdayV2'
import { formatPlatform, platformlist } from "@/utils/tools";
  
  const tableCols = [
  { istrue: true, prop: 'platform', fix: true, label: '平台', width: '45', sortable: 'custom', formatter: (row) => formatPlatform(row.platform), type: 'custom' },
  { sortable: 'custom', width: '300', align: 'center', prop: 'firstLevelCategoryName', label: '一级类目名称', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'secondLevelCategoryName', label: '二级类目名称', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'thirdLevelCategoryName', label: '三级类目名称', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'fourthLevelCategoryName', label: '四级类目名称', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'technicalServiceRate', label: '技术服务费率', },
    { sortable: 'custom', width: '300', align: 'center', prop: 'talentCommissionServiceRate', label: '达人佣金服务费率', },
  ]
  export default {
    name: "ksServiceRate",
    components: {
      MyContainer, vxetablebase
    },
    data() {
      return {
        editloading: false,
        editform: {
            firstLevelCategoryName: null,
            secondLevelCategoryName: null,
            thirdLevelCategoryName: null,
            fourthLevelCategoryName: null,
            technicalServiceRate: null,
            talentCommissionServiceRate: null,
        },
        platform:null,
        platformlist:platformlist,
        editdialogVisible: false,
        editName: '新增',
        dialogVisible: false,
        uploadLoading: false,
        fileList: [],
        fileparm: {},
        that: this,
        ListInfo: {
          currentPage: 1,
          pageSize: 50,
          orderBy: null,
          isAsc: false,
          FirstLevelCategoryName: null,
          SecondLevelCategoryName: null,
          ThirdLevelCategoryName: null,
          FourthLevelCategoryName: null,
        },
        editrules: {
          platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
            firstLevelCategoryName: [{ required: true, message: '请输入一级类目', trigger: 'blur' }],
            secondLevelCategoryName: [{ required: true, message: '请输入二级类目', trigger: 'blur' }],
            thirdLevelCategoryName: [{ required: true, message: '三级类目', trigger: 'blur' }],
            fourthLevelCategoryName: [{ required: true, message: '四级类目', trigger: 'blur' }],
            technicalServiceRate: [{ required: true, message: '技术服务费率', trigger: 'blur' }],
            talentCommissionServiceRate: [{ required: true, message: '达人佣金服务费率', trigger: 'blur' }],
        },
        tableCols,
        tableData: [],
        summaryarry: {},
        total: 0,
        loading: false,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
      async onSaveMethod() {
        if (!this.editform.platform || !this.editform.firstLevelCategoryName || !this.editform.secondLevelCategoryName || !this.editform.thirdLevelCategoryName || !this.editform.fourthLevelCategoryName || !this.editform.talentCommissionServiceRate || !this.editform.technicalServiceRate ) {
          this.$message({ message: "请填写完整信息", type: "warning" });
          return false;
        }
        this.editloading = true
        var res = await addorEditSeriesCodePackFee(this.editform)
        this.editloading = false
        if (res?.success) {
          this.$message({ message: "保存成功", type: "success" });
          this.editdialogVisible = false
          await this.getList()
        }
      },
      onAddnewMethod() {
        this.openEditDialog('新增', {
            platform: null,
            firstLevelCategoryName: null,
            secondLevelCategoryName: null,
            thirdLevelCategoryName: null,
            fourthLevelCategoryName: null,
            talentCommissionServiceRate: null,
            technicalServiceRate: null,
        });
      },
      onEditMethod(row) {
        const clonedRow = JSON.parse(JSON.stringify(row));
        this.openEditDialog('编辑', clonedRow);
      },
      openEditDialog(editName, formData) {
        this.editName = editName;
        this.editform = { ...formData };
        this.editdialogVisible = true;
      },
      onDeleteMethod(row) {
        this.$confirm('是否删除该数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          var res = await deleteSeriesCodePackFeeBatchAsync({ ...row })
          if (res?.success) {
            this.$message({ message: "删除成功", type: "success" });
            await this.getList()
          }
        }).catch(() => { });
      },
      //上传文件
      onUploadRemove(file, fileList) {
        this.fileList = []
      },
      async onUploadChange(file, fileList) {
        this.fileList = fileList;
      },
      onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
      },
      async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        if(!this.platform){
          this.$message({ message: "请选择平台", type: "warning" });
          return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("upfile", item.file);
        form.append("platform", this.platform);
        var res = await importSeriesCodePackFeesync(form);
        if (res?.success)
          this.$message({ message: "上传成功,正在导入中...", type: "success" });
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
      },
      onSubmitUpload() {
        if (this.fileList.length == 0) {
          this.$message({ message: "请先上传文件", type: "warning" });
          return false;
        }
        this.$refs.upload.submit();
      },
      //导入弹窗
      startImport() {
        this.fileList = []
        this.dialogVisible = true;
      },
      async getList(type) {
        if (type == 'search') {
          this.ListInfo.currentPage = 1
          this.$refs.pager.setPage(1)
        }
        this.loading = true
        const { data, success } = await getSeriesCodePackFeeList(this.ListInfo)
        if (success) {
          this.tableData = data.list
          this.total = data.total
          this.summaryarry = data.summary
          this.loading = false
        } else {
          //获取列表失败
          this.$message.error('获取列表失败')
        }
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>
  
  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;
  
    .publicCss {
      width: 200px;
      margin-right: 5px;
    }
  }
  
  .editCss {
    width: 80%;
  }
  
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  
  ::v-deep .el-input-number.is-without-controls .el-input__inner {
    padding-left: 5px;
  }
  </style>
  