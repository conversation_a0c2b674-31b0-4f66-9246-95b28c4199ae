<template>
    <my-container v-loading="pageLoading">
        <!--顶部操作-->
        <template #header>
            <el-form class="ad-form-query" :inline="true" :model="Filter" @submit.native.prevent>

                <el-date-picker style="width: 240px" v-model="Filter.timerange" type="datetimerange" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="分配时间" end-placeholder="分配时间"
                    :clearable="false"></el-date-picker>

                <el-select v-model="Filter.distributeState" clearable placeholder="分配状态" style="width:100px;">
                    <el-option v-for="item in distributeStateOpts" :key="'distributeState' + item.label"
                        :label="item.label" :value="item.value"></el-option>
                </el-select>

                <el-select v-model="Filter.purchaseOrderGenerateState" clearable placeholder="生成采购单状态"
                    style="width:130px;">
                    <el-option v-for="item in generateStateOpts" :key="'purchaseOrderGenerateState' + item.label"
                        :label="item.label" :value="item.value"></el-option>
                </el-select>

                <el-select v-model="Filter.purchaseOrderStatus" clearable placeholder="采购单状态" style="width:100px;">
                    <el-option v-for="item in purOrderStatusList" :key="'purchaseOrderStatus' + item" :label="item"
                        :value="item"></el-option>
                </el-select>

                <el-select v-model="Filter.warehouse" clearable placeholder="仓库" style="width:130px;">
                    <!-- <el-option v-for="item in  warehouselist" :key="'warehouse'+item.label" :label="item.label" :value="item.value"></el-option> -->
                    <el-option v-for="item in warehouselist" :key="item.name" :label="item.name"
                        :value="item.wms_co_id" />

                </el-select>
                <el-select filterable v-model="Filter.company" collapse-tags clearable placeholder="采购分公司"
                    style="width: 100px">
                    <el-option key="义乌" label="义乌" value="义乌"></el-option>
                    <el-option key="南昌" label="南昌" value="南昌"></el-option>
                </el-select>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-input v-model.trim="Filter.distributeMng" type="text" maxlength="10" clearable placeholder="分配人"
                        style="width:100px;" />
                </el-button>

                <el-select filterable v-model="Filter.brandIds" placeholder="采购员" clearable style="width: 150px"
                    multiple collapse-tags>
                    <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-input v-model="Filter.chooseGroupName" type="text" maxlength="20" clearable placeholder="运营组"
                    style="width:100px;" />

                <el-input v-model="Filter.chooseUserName" type="text" maxlength="20" clearable placeholder="运营"
                    style="width:100px;" />
                <el-input v-model.trim="Filter.keywords" type="text" maxlength="100" clearable placeholder="请输入关键字..."
                    style="width:200px;">
                    <el-tooltip slot="suffix" class="item" effect="dark" :content="keywordsTip" placement="bottom">
                        <i class="el-input__icon el-icon-question"></i>
                    </el-tooltip>
                </el-input>
                <div style="width: 200px;">
                    <inputYunhan ref="productCode" :inputt.sync="Filter.goodsCompeteIds"
                        v-model="Filter.goodsCompeteIds" width="200px" placeholder="竞品ID/若输入多条请按回车" :clearable="true"
                        :clearabletext="true" :maxRows="100" :maxlength="2000" @callback="callbackGoodsCode"
                        title="竞品ID">
                    </inputYunhan>
                </div>
                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select v-model="Filter.types" placeholder="请选择" multiple collapse-tags>
                        <el-option v-for="item in options" :key="item.label" :label="item.label" :value="item.label">
                        </el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <el-select filterable v-model="Filter.isSealClose" collapse-tags clearable placeholder="查询归档数据"
                    style="width: 110px">
                    <el-option key="IsSealClose是" label="是" :value="true"></el-option>
                    <el-option key="IsSealClose否" label="否" :value="false"></el-option>
                    </el-select>
                </el-button>

                <el-button style="padding: 0;margin: 0;border:none">
                    <span>&nbsp;</span> <el-checkbox v-model="Filter.isPro"
                        @change="onIsProChange">系列编码采购员</el-checkbox><span>&nbsp;</span>
                </el-button>



                <p></p>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="() => { Filter = {}; }">清空条件</el-button>

                <el-button type="primary" @click="onShowAutoSet">自动分配采购设置</el-button>
                <el-button type="primary" @click="applyProcurement">同供应商申请采购</el-button>
                <el-button type="primary" @click="specialAssignment"
                    v-if="checkPermission('api:operatemanage:alllink:savespecialbrandallout')">特殊采购分配</el-button>


                <el-button type="primary" @click="onBatchSetBrand"
                    v-if="checkPermission('api:operatemanage:alllink:SetHotPurchasePlanBrand')">
                    批量分配采购
                </el-button>
                <el-button type="primary" @click="onShowRejectPurchasePlan"
                    v-if="checkPermission('api:operatemanage:alllink:RejectPurchasePlanGoNewPlan')">
                    批量驳回
                </el-button>

            </el-form>
        </template>

        <!--列表-->
        <vxetablebase :id="'PurchasePlanList202212171318002'" :tableData='tbdatalist' :that='that'
            @select="checkboxRangeEnd" :tableCols='tableCols' :summaryarry='summaryarry' :showsummary='true'
            :treeProp="{ rowField: 'oid', parentField: 'parentId', expandAll: false, transform: true, }"
            :loading='listLoading' :border='true' ref="vxetable" @sortchange='sortchange'>
            <template slot="right">
                <vxe-column title="操作" :field="'col_opratorcol'" width="280" fixed="right">
                    <template #default="{ row }">
                        <template v-if="row.parentId != '0'">
                            <el-button type="text"
                                v-if='row.distributeState != -1 && row.purchaseOrderGenerateState == 0 && row.brandId && row.brandId == selfInfo.brandId'
                                @click="onOpenDtl(row.id, 2)">编辑</el-button>
                            <el-button type="text" @click="onOpenDtl(row.id, 3)">详情</el-button>
                            <el-button type="text"
                                v-if="row.distributeState != -1 && row.purchaseOrderGenerateState == 0 && checkPermission('api:operatemanage:alllink:SetHotPurchasePlanBrand')"
                                @click="onSetDistribute(row, 1)">分配采购</el-button>
                            <el-button type="text"
                                v-if="row.distributeState != -1 && row.purchaseOrderGenerateState == 0 && row.brandId && row.brandId == selfInfo.brandId"
                                @click="onGeneratePurchase(row)">申请采购</el-button>
                            <el-button type="text"
                                v-if="row.distributeState == 1 && row.purchaseOrderGenerateState == 0"
                                @click="onReturnpreviousstep(row)">返回上一步</el-button>
                        </template>
                        <template v-else>
                            <el-button type="text" @click="onSetDistribute(row, 2)"
                                v-if="row.isSealClose != 1 && checkPermission('api:operatemanage:alllink:SetHotPurchasePlanBrand')">分配采购</el-button>
                            <el-button type="text"
                                v-if="row.isSealClose != 1 && checkPermission('api:operatemanage:alllink:RejectPurchasePlanGoNewPlan')"
                                @click="onShowRejectPurchasePlan(row)">驳回</el-button>
                        </template>
                    </template>
                </vxe-column>
            </template>
        </vxetablebase>
        <el-dialog title="驳回采购计划" :visible.sync="rejectPurchasePlanVisible" width="25%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag append-to-body>
            <el-form ref="rejectForm" :model="rejectForm" :rules="rejectFormRules" label-width="100px"
                label-position="right">
                <el-row :hidden="true">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="建编码id：">
                            <el-input v-model="rejectForm.docIds" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item prop="rejectRemark" label="驳回理由：">
                            <el-input v-model="rejectForm.rejectRemark" type="textarea"
                                :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入驳回理由" maxlength="200"
                                show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="rejectPurchasePlanVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onOkRejectPurchasePlan(row)"
                        :loading="rejectPurchasePlanLoading">确 认</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="自动分配采购设置" :visible.sync="autoSetVisible" width="50%" :close-on-click-modal="false"
            v-loading="autoSetListLoading" element-loading-text="拼命加载中" v-dialogDrag append-to-body>
            <template>
                <el-row>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <div style="color:red">勾选则代表该采购员参与自动分配</div>
                        <el-container style="height:580px;">
                            <el-table ref="autoSetTable" :data="autoSetTableList" @select='autosetselectchange'
                                @select-all="autosetselectall" :height="580">
                                <el-table-column type="selection" width="55">
                                </el-table-column>
                                <el-table-column prop="id" label="id" width="55" v-if="false" />
                                <el-table-column prop="setIndex" label="分配顺序" width="100" align="center">
                                    <template slot-scope="scope">
                                        <el-input disabled v-model="scope.row.setIndex" maxlength="4"
                                            oninput="value=value.replace(/[^\d]/g,'')">
                                        </el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="brandId" label="采购员编号" width="120" align="center" />
                                <el-table-column prop="brandName" label="采购员姓名" width="120" align="center" />
                                <el-table-column prop="area" label="区域" width="80" align="center" />
                                <el-table-column prop="title" label="岗位名称" width="80" align="center" />
                                <el-table-column prop="employeeStatus" label="状态" width="80" align="center" />
                                <el-table-column prop="topCount" label="设置上限" width="100" align="center">
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.topCount" maxlength="4"
                                            oninput="value=value.replace(/[^\d]/g,'')">
                                        </el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="topCount1" label="款式标签" width="160" align="center">
                                    <template slot-scope="scope">
                                        <el-select v-model="scope.row.styleTagList" style="width: 150px ;height: 29px"
                                            multiple collapse-tags placeholder="款式标签">
                                            <el-option label="春季款" :value="'春季款'" />
                                            <el-option label="夏季款" :value="'夏季款'" />
                                            <el-option label="秋季款" :value="'秋季款'" />
                                            <el-option label="冬季款" :value="'冬季款'" />
                                            <el-option label="春节款" :value="'春节款'" />
                                            <el-option label="开学季款" :value="'开学季款'" />
                                            <el-option label="强季款" :value="'强季款'" />
                                            <el-option label="四季款" :value="'四季款'" />
                                        </el-select>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-container>
                    </el-col>
                    <!-- <el-col :xs="24" :sm="24" :md="24" :lg="7" :xl="7">
                        <div style="color:red">设置[晨光指定采购]后优先按数字顺序依次自动分配</div>
                        <el-container style="height:580px;">
                            <el-table ref="chenGuangAutoSetTable" :data="chenGuangAutoSetTableList" :height="580">
                                <el-table-column prop="brandId" label="采购员编号" width="120" align="center" />
                                <el-table-column prop="brandName" label="采购员姓名" width="120" align="center" />
                                <el-table-column prop="chenGuangSelBrandIndex" label="晨光指定采购" width="120"
                                    align="center">
                                    <template #header>
                                        <span class="grid-header">
                                            <span>晨光指定采购</span>
                                            <span>
                                                <el-tooltip class="item" effect="dark"
                                                    content="大于0时遇晨光商品优先分配给此采购，且按数字依次分配，无需勾选。" placement="top-end">
                                                    <span><i class="el-icon-question"></i></span>
                                                </el-tooltip>
                                            </span>
                                        </span>
                                    </template>
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row.chenGuangSelBrandIndex" maxlength="4"
                                            oninput="value=value.replace(/[^\d]/g,'')">
                                        </el-input>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-container>
                    </el-col> -->
                </el-row>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="autoSetVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onOkAutoSet()">保 存</el-button>
                    <el-tooltip effect="dark" placement="top" content="一键分配3天内未被分配(手动/自动)的采购计划">
                        <el-button type="primary" @click="onOkAutoSet(true)">保存&一键分配</el-button>
                    </el-tooltip>
                </span>
            </template>
        </el-dialog>

        <el-dialog title="分配日志" :visible.sync="log.visible" width="40%" height='500px' :close-on-click-modal="false"
            v-loading="autoSetListLoading" element-loading-text="拼命加载中" v-dialogDrag append-to-body>
            <template>
                <el-button-group style="padding-bottom:2px;margin-top:-4px;">
                    <el-button style="padding: 0;margin: 0;border:none">
                        <el-button style="padding: 0;margin: 0;border:none">
                            <el-input v-model.trim="log.filter.distributeMng" type="text" maxlength="10" clearable
                                placeholder="分配人" style="width:100px;" />
                        </el-button>
                        <el-button type="primary" @click="onSearchLog">查询</el-button>
                        <el-button type="primary" @click="() => { log.filter.distributeMng = null; }">清空条件</el-button>
                    </el-button>
                </el-button-group>
            </template>
            <template>
                <el-container style="height:600px;">
                    <vxetablebase :id="'distributeLogList20231008171032'" :tableData='log.distributeLogTableDatas'
                        :tableCols='log.distributeLogTableCols' :showToolbar="false" :loading='log.listLoading'
                        :border='true' ref="distributeLogTable" @sortchange='distributeLogSortchange'>
                    </vxetablebase>
                </el-container>
            </template>
        </el-dialog>

        <el-dialog title="返回上一步" :visible.sync="returnVisible" width="30%" height='500px' :close-on-click-modal="false"
            v-loading="autoSetListLoading" element-loading-text="拼命加载中" v-dialogDrag append-to-body>
            <div style="display: flex;align-items: center;">
                <div style="width: 80px;">返回原因:<span style="color:red">*</span></div>
                <el-input type="textarea" :rows="5" placeholder="返回原因" :maxlength="500"
                    v-model="returnInfo.rollBackRemark" />
            </div>
            <div style="margin-top: 20px;">
                <uploadimgFile ref="uploadimgFile" :accepttyes="accepttyes" :isImage="true"
                    :uploadInfo="returnInfo.chatUrls" :keys="[1, 1]" v-if="returnVisible" @callback="getImg"
                    :imgmaxsize="9" :limit="9" :multiple="true">
                </uploadimgFile>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="returnVisible = false">取 消</el-button>
                <el-button type="primary" @click="returnSubmit">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="同供应商申请采购" :visible.sync="applyDialogVisable" width="30%" height='500px'
            :close-on-click-modal="false" v-dialogDrag append-to-body>
            <el-form :model="ruleForm" status-icon ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="供应商:" prop="supplierName">
                    <el-input v-model="ruleForm.supplierName" disabled style="width: 200px;" />
                </el-form-item>
                <el-form-item label="仓库:" prop="warehouseName">
                    <el-input v-model="ruleForm.warehouseName" disabled style="width: 200px;" />
                </el-form-item>
                <el-form-item label="采购人员:" prop="brandName">
                    <el-input v-model="ruleForm.brandName" disabled style="width: 200px;" />
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="applyDialogVisable = false">取 消</el-button>
                <el-button type="primary" @click="applySubmit">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog title="特殊分配采购" :visible.sync="specialVisible" width="50%" height='500px'
            :close-on-click-modal="false" element-loading-text="拼命加载中" v-dialogDrag append-to-body>
            <div style="margin-bottom: 10px;">
                <el-button @click="addPlan">新建采购计划</el-button>
                <el-button @click="delPlan">删除采购计划</el-button>
            </div>
            <el-container style="height: 500px; border: 1px solid #eee" v-loading="specialLoading">
                <el-aside width="150px" style="background-color: rgb(238, 241, 246);padding: 10px;">
                    <div style="display: flex;align-items: center;justify-content: center;height: 30px;"
                        v-for="(item, i) in planList">
                        <div style="width: 15px;">
                            <el-checkbox v-model="item.isChecked" size="medium" @change="changeBox($event, i)" />
                        </div>
                        <div style="flex: 1;text-align: center;">
                            <el-tooltip class="item" effect="dark" :content="item.planName" placement="top-start">
                                <el-button :class="[specialId == item.specialId ? 'active' : '', 'btnClass']"
                                    @click="getCheckPlanList(item.specialId)">{{
        item.planName
    }}</el-button>
                            </el-tooltip>

                        </div>
                    </div>
                </el-aside>

                <el-container>
                    <el-main>
                        <el-table :data="planProp" height="400">
                            <el-table-column prop="brandId" label="采购员编号" width="140" />
                            <el-table-column prop="brandName" label="采购员姓名" width="120" />
                            <el-table-column prop="area" label="区域" />
                            <el-table-column prop="title" label="岗位名称" />
                            <el-table-column prop="employeeStatus" label="状态" />
                            <el-table-column prop="allotSort" label="指定采购">
                                <template #header>
                                    <span class="grid-header">
                                        <span>指定采购</span>
                                        <span>
                                            <el-tooltip class="item" effect="dark"
                                                content="大于0时与指定分配商品优先分配给此采购，且按数字依次分配，无需勾选。" placement="top-end">
                                                <span><i class="el-icon-question"></i></span>
                                            </el-tooltip>
                                        </span>
                                    </span>
                                </template>
                                <template #default="{ row }">
                                    <el-input-number v-model="row.allotSort" :min="0" :max="9999" placeholder="指定采购"
                                        :controls="false" style="width: 100px;" :precision="0" />
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-main>
                </el-container>
            </el-container>
            <div style="margin-top: 10px;display: flex;justify-content: end;">
                <el-button @click="specialVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitPlan(false)">保 存</el-button>
                <el-tooltip class="item" effect="dark" content="一键分配3天内未被分配(手动/自动)的采购计划" placement="top-end">
                    <el-button type="primary" @click="submitPlan(true)">保存&一键分配</el-button>
                </el-tooltip>

            </div>
        </el-dialog>

        <el-dialog title="添加采购计划" :visible.sync="addPlavVisable" width="15%" :close-on-click-modal="false"
            element-loading-text="拼命加载中" v-dialogDrag append-to-body>
            <el-input v-model="planName" placeholder="请输入计划" :maxlength="50" clearable />
            <div style="margin-top: 10px;display: flex;justify-content: end;">
                <el-button type="primary" @click="submitAddPlan">保 存</el-button>
            </div>
        </el-dialog>
        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sels.length" @get-page="gettbdatalist1" />
        </template>
    </my-container>
</template>
<script>

import {
    PageHotPurchasePlanAsync,
    GeneratePurchaseOrder,
    getUserInfo,
    rejectPurchasePlanGoNewPlan,
    rollBackHotPurchasePlan,
    getPurchasePlanAutoSetList,
    savePurchasePlanAutoSetList,
    onekeyPurchasePlanAutoSetList,
    getHotPurchasePlanDistributeLogAsync,
    getPurchasePlanAutoSetOtherList,
    batchPlanCreatePurchaseOrder,
    getSpecialBrandAllot,
    saveSpecialBrandAllout,
    getAutoSetSpecialPlan,
    saveAutoSetSpecialPlan,
    delAutoSetSpecialPlan,
} from '@/api/operatemanage/productalllink/alllink'
import { getAllProBrand } from '@/api/inventory/warehouse'
import dayjs from "dayjs";
import cesTable from "@/components/Table/table.vue";
import {
    formatmoney, formatPercen, getUrlParam, platformlist,
    formatPlatform, formatTime, setStore, getStore,
    formatLinkProCode, formatWarehouse
} from "@/utils/tools";
import MyContainer from "@/components/my-container";
import { getAllWarehouse } from '@/api/inventory/warehouse'
import MyConfirmButton from "@/components/my-confirm-button";
import inputYunhan from "@/components/Comm/inputYunhan";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import YhImgUpload from "@/components/upload/yh-img-upload.vue";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import uploadimgFile from "@/components/Comm/uploadimgFile.vue";
const distributeStateOpts = [
    { label: '待分配', value: 0 },
    { label: '已分配', value: 1 },
    { label: '已归档', value: -1 }
];
const distributeStateFmt = (val) => {
    let opt = distributeStateOpts.find(x => x.value == val);
    if (opt)
        return opt.label;

    return val;
}

const generateStateOpts = [
    { label: '未生成', value: 0 },
    { label: '已生成', value: 1 }
];
const generateStateFmt = (val) => {
    let opt = generateStateOpts.find(x => x.value == val);
    if (opt)
        return opt.label;

    return val;
}

const auditStateOpts = [
    { label: '已拒绝', value: -1 },
    { label: '未发起', value: 0 },
    { label: '审核中', value: 1 },
    { label: '已审核', value: 2 },
]
const auditStateFmt = (val) => {
    let opt = auditStateOpts.find(x => x.value == val);
    if (opt)
        return opt.label;
    return val;
}
// 普通、专利、著作、商标
const options = [
    { label: '普通' },
    { label: '专利' },
    { label: '著作' },
    { label: '商标' }
]

const distributeLogTableCols = [
    { istrue: true, prop: 'distributeMng', label: '分配人', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '分配时间', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'brandName', label: '采购人员', align: 'center', sortable: 'custom' },
]

const tableCols = [
    { label: '', type: 'checkbox' },
    //  { istrue: true, prop: 'goodsCompeteId', label: '竞品ID', width: '160', sortable: 'custom',
    //  type: 'html', formatter: (row) => formatLinkProCode(row.platform, row.goodsCompeteId) ,
    //  treeNode:true,fixed:'left'},
    // { istrue: true, prop: 'goodsCompeteName', label: '竞品标题', width: '220', sortable: 'custom',fixed:'left' },

    {
        istrue: true, prop: 'styleCode', label: '竞品ID/款式编码(供应商)', width: '220', sortable: 'custom', treeNode: true, fixed: 'left',
        type: 'html',
        formatter: (row) => {
            return (row.parentId == "0" ? formatLinkProCode(row.platform, row.styleCode) : row.styleCode) +
                //supplierName
                (row.supplierName ? `(${row.supplierName})` : (row.referSupplierName ? `(${row.referSupplierName})` : ''))

        }
    },
    // { istrue: true, prop: 'referSupplierName', label: '参考供应商', width: '110', align:'left',sortable: 'custom' ,fixed:'left'},
    { istrue: true, prop: 'goodsCompeteShortName', label: '竞品标题/产品简称', width: '170', sortable: 'custom', fixed: 'left' },
    //{ istrue: false, prop: 'proBrand', label: '系列编码采购员', width: '120' },
    { istrue: true, prop: 'goodsCompeteImgUrl', label: '竞品图', width: '62', type: 'images' },
    { istrue: true, prop: 'proBrand', label: '系列编码采购员', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'goodsCount', label: '编码数', width: '100', align: 'center' },
    { istrue: true, prop: 'createdTime', label: '生成时间', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'distributeState', label: '分配状态', width: '98', align: 'center', sortable: 'custom', formatter: (row) => distributeStateFmt(row.distributeState) },
    { istrue: true, prop: 'distributeMng', label: '分配人', width: '98', align: 'center', sortable: 'custom', type: 'clickLink', handle: (that, row) => that.onOpenLog(row) },
    { istrue: true, prop: 'distributeTime', label: '分配时间', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'brandName', label: '采购人员', width: '100', align: 'center', sortable: 'custom' },
    // { istrue: true, prop: 'purchaseApplyState', label: '计划审核状态', width: '120',align:'center', sortable: 'custom', formatter: (row) => auditStateFmt(row.purchaseApplyState) },
    { istrue: true, prop: 'purchaseOrderGenerateTime', label: '生成采购单时间', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'purchaseOrderCheckDate', label: '采购单审核时间', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'buyNo', label: '采购单号', width: '94', sortable: 'custom', align: 'center', }, // type: 'html', formatter: (row) => formatNoLink(row.buyNo == '0' ? ' ' :  row.buyNo)
    { istrue: true, prop: 'purchaseOrderIndexNo', label: 'ERP单号', width: '90', sortable: 'custom', align: 'center', }, //type: 'html', formatter: (row) => formatNoLink(row.purchaseOrderIndexNo == '0' ? ' ' :  row.purchaseOrderIndexNo)
    { istrue: true, prop: 'purchaseOrderStatus', label: '采购单状态', width: '110', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'yyUserName', label: '运营', width: '100', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'yyGroupName', label: '运营组', width: '100', sortable: 'custom', align: 'center' },
    { istrue: true, prop: 'wareInDate', label: '首次入库时间', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'purchaseOrderGenerateReason', label: '生成失败原因', width: '140', align: 'center', sortable: 'custom' },
    { istrue: true, prop: 'inspectionReportImgUrl', label: '质检报告', width: '80', type: 'images' },
    { istrue: true, prop: 'patentQualificationImgUrls', label: '专利资质', width: '80', type: 'images' },
    { istrue: true, prop: 'patentQualificationPdfUrls', label: '专利PDF', width: '80', type: 'files' },
    { istrue: true, prop: 'packingImgUrls', label: '包装图片', width: '80', type: 'images' },
    // { istrue: true, prop: 'supplierPlatForm', label: '供应商平台', width: '120', sortable: 'custom' },
    { istrue: true, prop: 'supplierName', label: '供应商名称', width: '120', sortable: 'custom' },
    // { istrue: true, prop: 'warehouse', label: '仓库', width: '120', sortable: 'custom', formatter: (row) => formatWarehouse(row.warehouse) },
    { istrue: true, prop: 'warehouse', label: '仓库', width: '120', sortable: 'custom', formatter: (row) => row.warehouseName },
    // { istrue: true, prop: 'supplierLink', label: '供应商链接', width: '120'},
    // { istrue: true, prop: 'supplierGoodLink', label: '供应商产品链接', width: '140'},
];
const startTime = formatTime(dayjs().subtract(7, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
const star = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
export default {
    name: "HotPurchasePlanList",
    components: { MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable, vxetablebase, YhImgUpload, uploadimgFile, inputYunhan },
    data() {
        return {
            summaryarry: {},
            accepttyes: '.png,.jpg,.bmp,.webp,.jpeg,.gif',
            returnVisible: false,
            returnInfo: {
                planId: null,
                rollBackRemark: null,//返回原因
                chatUrls: [],
                pictures: '',
            },
            options,
            that: this,
            //warehouselist: warehouselist,
            warehouselist: [],
            distributeStateOpts: distributeStateOpts,
            generateStateOpts: generateStateOpts,
            purOrderStatusList: ['待审核', '已确认', '作废', '完成'],
            Filter: {
                distributeState: 0,
                purchaseOrderGenerateState: 0,
                purchaseOrderStatus: null,
                warehouse: null,
                keywords: '',
                goodsCompeteIds: '',
                brandIds: null,
                chooseGroupName: null,
                chooseUserName: null,
                company: null,
                timerange: [],
                startTime: null,
                endTime: null,
                isSealClose:null
            },
            brandlist: [],
            platformlist: platformlist,
            tbdatalist: [],
            tableCols: tableCols,
            total: 0,
            pager: { OrderBy: "", IsAsc: false },
            sels: [], // 列表选中列
            listLoading: false,
            pageLoading: false,
            selfInfo: {

            },
            selids: [],
            curRow: {},
            keywordsTip: '支持搜索的内容：商品简称、款式编码、供应商、采购、运营、采购单号、竞品Id标题、生成采购单失败原因',

            rejectPurchasePlanVisible: false,
            rejectPurchasePlanLoading: false,
            rejectForm: { docIds: "", rejectRemark: "" },
            rejectFormRules: {
                rejectRemark: [{ required: true, message: '请输入驳回理由', trigger: 'blur' }]
            },

            autoSetVisible: false,
            autoSetListLoading: false,
            autoSetTableList: [],
            selAutoSetTableList: [],
            log: {
                visible: false,
                pager: { OrderBy: "createdTime", IsAsc: false },
                distributeLogTableCols: distributeLogTableCols,
                filter: {
                    planId: null,
                    distributeMng: null
                },
                listLoading: false,
                distributeLogTableDatas: []
            },
            chenGuangAutoSetTableList: [],
            applyProcurementList: [],
            applyProcurementParentList: [],
            applyDialogVisable: false,
            ruleForm: {
                supplierName: '',//供应商
                supplierId: '',//供应商id
                warehouse: '',//仓库
                warehouseName: '',//仓库名称
                brandId: '',//采购员
                brandName: '',//采购员名称
                hotBuildDocPurchasePlanIds: [],//采购计划id
            },
            applyProcurementList1: [],
            specialVisible: false,
            planList: [],
            checkList: [],
            planProp: [],
            specialLoading: false,
            addPlavVisable: false,
            planName: null,
            specialId: null,
        };
    },
    async mounted() {
        this.$nextTick(() => {
            this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('proBrand'))
        })
        this.selfInfo = (await getUserInfo()).data;
        this.init();
        this.onSearch();
    },
    methods: {
        async getCheckPlanList(specialId) {
            this.specialId = specialId
            await this.getPlanProps(specialId)
        },
        async submitPlan(isAllot) {
            if (isAllot && this.checkList.length == 0) return this.$message.error('请选择需要分配的采购计划')
            let params = {}
            params.dtoList = this.planProp
            params.specialId = this.specialId
            if (isAllot) {
                params.isAllot = isAllot
                params.executeAllotId = this.checkList
            }
            const { success } = await saveSpecialBrandAllout(params)
            if (success) {
                this.$message.success('保存成功')
                this.specialVisible = false
            } else {
                this.$message.error('保存失败')
            }
        },
        async submitAddPlan() {
            if (!this.planName) return this.$message.error('请输入计划名称')
            const { success } = await saveAutoSetSpecialPlan({ planName: this.planName })
            if (success) {
                this.addPlavVisable = false
                this.specialAssignment()
                this.$message.success('保存成功')
            }
        },
        addPlan() {
            this.planName = null
            this.addPlavVisable = true
        },
        async delPlan() {
            if (this.checkList == 0) return this.$message.error('请选择需要删除的数据');
            this.$confirm('此操作将永久删除该计划, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const { success } = await delAutoSetSpecialPlan({ specialIdList: this.checkList })
                if (success) {
                    await this.specialAssignment()
                    this.$message.success('删除成功')
                }
            }).catch((error) => {
                console.log(error);
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        async getPlanProps(specialId) {
            this.specialLoading = true
            const { data, success } = await getSpecialBrandAllot({ specialId })
            if (success) {
                this.planProp = data
            }
            this.specialLoading = false
        },
        changeBox(e, i) {
            if (e) {
                this.checkList.push(this.planList[i].specialId)
            } else {
                this.checkList = this.checkList.filter(item => item != this.planList[i].specialId)
            }
        },
        async specialAssignment() {
            this.specialId = null
            this.checkList = []
            const { data, success } = await getAutoSetSpecialPlan()
            if (success) {
                data.forEach((item, i) => {
                    item.isChecked = false
                })
                this.planList = data
            }
            if (data && data.length > 0) {
                this.specialId = data[0].specialId
                await this.getPlanProps(data[0].specialId)
            } else {
                this.planProp = []
            }
            this.specialVisible = true
        },
        //多个竞品ID
        callbackGoodsCode(val) {
            this.Filter.goodsCompeteIds = val;
        },
        getImg(data) {
            if (data) {
                this.returnInfo.chatUrls = data ? data : []
                this.returnInfo.pictures = data.map(item => item.url).join(',')
            }
        },
        async applySubmit() {
            const { success } = await batchPlanCreatePurchaseOrder(this.ruleForm);
            if (!success) return this.$message.error('申请采购失败');
            this.applyDialogVisable = false;
            this.onSearch();
            this.$message.success('申请采购成功');
        },
        applyProcurement() {
            if (this.applyProcurementList.length == 0) {
                return this.$message.error('请选择需要申请采购的数据');
            }
            this.ruleForm = {
                supplierName: '',//供应商
                supplierId: '',//供应商id
                warehouse: '',//仓库
                warehouseName: '',//仓库名称
                brandId: '',//采购员
                brandName: '',//采购员名称
                hotBuildDocPurchasePlanIds: [],//采购计划id
            }
            this.applyProcurementList.forEach(item => {
                if (!item.brandId) {
                    this.$message.error('选中数据中存在未分配采购员的数据，请分配后再操作！');
                    throw new Error('选中数据中存在未分配采购员的数据，请分配后再操作！');
                }
                if (item.purchaseOrderGenerateTime) {
                    this.$message.error('选中数据中存在已生成采购单的数据，请重新选择！');
                    throw new Error('选中数据中存在已生成采购单的数据，请重新选择！');
                }
            })
            let brandId = this.applyProcurementList[0].brandId;
            let brandName = this.applyProcurementList[0].brandName;
            let supplierName = this.applyProcurementList[0].supplierName;
            let warehouse = this.applyProcurementList[0].warehouse;
            let warehouseName = this.applyProcurementList[0].warehouseName;
            let flag = this.applyProcurementList.every(item => {
                return item.supplierName == supplierName && item.warehouseName == warehouseName && item.brandId == brandId && item.brandName == brandName;
            });
            if (!flag) {
                return this.$message.error('请选择同一供应商，同一仓库的数据,并且同一采购员');
            }
            this.ruleForm = {
                supplierName: supplierName,
                supplierId: this.applyProcurementList[0].supplierId,
                warehouse: warehouse,
                warehouseName: this.applyProcurementList[0].warehouseName,
                brandId: brandId,
                brandName: this.applyProcurementList[0].brandName,
                hotBuildDocPurchasePlanIds: this.applyProcurementList.map(item => item.id),
            }
            this.applyDialogVisable = true;
        },
        checkboxRangeEnd(row) {
            // this.applyProcurementList1 = row.filter(item => item.parentId == 0);
            this.applyProcurementList = row.filter(item => item.parentId != 0);
            this.applyProcurementParentList = row.filter(item => item.parentId == 0);
        },
        async returnSubmit() {
            if (!this.returnInfo.rollBackRemark) return this.$message.error('请填写退回原因');
            //如果没有上传图片,给提示
            if (this.returnInfo.chatUrls.length == 0) return this.$message.error('请上传图片');
            const { success } = await rollBackHotPurchasePlan(this.returnInfo);
            if (success) {
                this.$message({ type: 'success', message: '返回上一步成功!' });
                this.returnVisible = false
                this.onSearch();
            } else {
                this.$message({ type: 'error', message: '返回上一步失败!' });
            }
        },
        formatLinkProCode: formatLinkProCode,
        formatWarehouse,
        onIsProChange(e) {
            if (!e) {
                this.$nextTick(() => {
                    this.$refs.vxetable.$refs.xTable.hideColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('proBrand'))
                })
            } else {
                this.$nextTick(() => {
                    this.$refs.vxetable.$refs.xTable.showColumn(this.$refs.vxetable.$refs.xTable.getColumnByField('proBrand'))
                })
            }
            this.onSearch();
        },
        async onReturnpreviousstep(row) {
            this.returnInfo = {
                planId: row.id,
                rollBackRemark: null,//返回原因
                chatUrls: [],
                pictures: '',
            },
                this.returnVisible = true
        },
        onOpenDtl(oid, mode) {
            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/skuenquiry/HotPurchasePlanForm.vue`,
                args: { oid: oid, mode: mode },
                title: "新品进货",
                height: 400,
                callOk: () => {
                    self.onRefresh();
                }
            });
        },
        onSetDistribute(row, mode) {
            let type = mode

            //let planIds = []
            // if (this.applyProcurementList.length != 0) {
            //     planIds = this.applyProcurementList.map(item => item.id);
            //     type = 3
            // } else {
            //     planIds = [row.id]
            // }
            let planIds = [row.id];

            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/skuenquiry/HotPurchasePlanFormDistributePurchase.vue`,
                args: { ...row, oid: row.id, mode: type, planIds },
                title: "分配采购",
                width: '400px',
                height: 400,
                callOk: () => {
                    self.onRefresh();
                    self.purchaseClose()
                }
            });
        },
        onBatchSetBrand() {
            if (this.applyProcurementList.length <= 0) {
                this.$message({ type: 'error', message: '请至少勾选一行数据!' });
                return;
            }
            let planIds = this.applyProcurementList.map(item => item.id);
            let self = this;
            this.$showDialogform({
                path: `@/views/operatemanage/productalllink/skuenquiry/HotPurchasePlanFormDistributePurchase.vue`,
                args: { ...this.applyProcurementList[0], oid: this.applyProcurementList[0].id, mode: 3, planIds },
                title: "分配采购",
                width: '400px',
                height: 400,
                callOk: () => {
                    self.onRefresh();
                    self.purchaseClose()
                }
            });
        },
        //清除复选框
        purchaseClose() {
            this.applyProcurementList = [];
            this.applyProcurementParentList = [];
        },
        async onOpenLog(row) {
            if (row.parentId == 0 && row.children != null && (row.children.length > 1 || row.children.length <= 0)) {
                this.$message.warning("请点击子级分配人查看具体分配日志明细！")
                return;
            }
            if (row.parentId == 0 && row.children.length == 1) {
                this.log.filter.planId = row.children[0].oid;
            } else {
                this.log.filter.planId = row.oid;
            }
            this.log.filter.distributeMng = null;
            this.log.distributeLogTableDatas = [];
            await this.onSearchLog();
            this.$nextTick(() => {
                this.log.visible = true;
            });
        },
        async onSearchLog() {
            const params = {
                ...this.log.pager,
                ...this.log.filter
            };
            this.log.listLoading = true;
            const res = await getHotPurchasePlanDistributeLogAsync(params);
            this.log.listLoading = false;
            this.log.distributeLogTableDatas = res.data;
        },
        distributeLogSortchange(column) {
            if (!column.order)
                this.log.pager = {};
            else {
                let orderField = column.prop;
                this.log.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            }
            this.onSearchLog();
        },
        async init() {
            var res = await getAllProBrand();
            this.brandlist = res?.data.map(item => {
                return { value: item.key, label: item.value };
            });

            var res3 = await getAllWarehouse();
            this.warehouselist = res3.data;
        },
        async onGeneratePurchase(row) {
            this.listLoading = true;
            let rlt = await GeneratePurchaseOrder({ planId: row.id });
            if (rlt && rlt.success) {
                this.$message.success('生成成功');
                this.onRefresh();
            }
            this.listLoading = false;
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else {
                var orderField = column.prop;
                // var bFields = [];// ['skuDataState', 'skuDataTime', 'cmRefInfoState', 'cmRefInfoLastOkTime'];
                // if (orderField == "orgPlatformName") {
                //     orderField = "PlatformName";
                // } else if (bFields.indexOf(orderField) > -1) {
                //     orderField = "b." + orderField;
                // }

                this.pager = { OrderBy: orderField, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            }
            this.onSearch();
        },
        onRefresh() {
            this.onSearch()
        },
        onSearch() {
            this.$refs.pager.setPage(1);
            this.gettbdatalist1();
        },
        async gettbdatalist1() {
            // if (this.Filter.gDate) {
            //     this.Filter.startGDate = this.Filter.gDate[0];
            //     this.Filter.endGDate = this.Filter.gDate[1];
            // }
            // else {
            //     this.Filter.startGDate = null;
            //     this.Filter.endGDate = null;

            // }
            this.Filter.startTime = null;
            this.Filter.endTime = null;
            if (this.Filter.timerange) {
                this.Filter.startTime = this.Filter.timerange[0];
                this.Filter.endTime = this.Filter.timerange[1];
            }
            let keywordsArray = this.Filter.goodsCompeteIds;
            let para = { ...this.Filter };
            para.goodsCompeteIds = keywordsArray ? keywordsArray.split(",") : [];
            var pager = this.$refs.pager.getPager();
            const params = {
                ...pager,
                ...this.pager,
                ...para,
            };

            this.listLoading = true;
            const res = await PageHotPurchasePlanAsync(params);

            this.listLoading = false;

            this.total = res.data.total
            this.summaryarry = res.data.summary;
            this.tbdatalist = res.data.list;

            //console.log( this.$refs.vxetable.$refs.xTable);
            //let self = this;
            // _.delay(function () {
            //     self.$refs.vxetable.$refs.xTable.setAllTreeExpand(true);
            // }, 20)
        },

        selectchange: function (rows, row) {
            this.selids = [];
            rows.forEach(f => {
                this.selids.push(f.id);
            })
        },
        async onShowRejectPurchasePlan(row) {
            console.log(row, "rowrowrow");
            console.log(this.applyProcurementParentList, "this.applyProcurementParentList");
            this.rejectForm.rejectRemark = "";
            if (row && row.parentId == 0) {
                this.rejectForm.docIds = row.buildDocId;
            }
            else {
                if (this.applyProcurementParentList.length <= 0) {
                    this.$message.error('请至少勾选一个父级');
                    return;
                }
                let docIdList = this.applyProcurementParentList.map(item => item.buildDocId);
                let docIds = docIdList.join(',');
                this.rejectForm.docIds = docIds;
            }
            this.rejectPurchasePlanVisible = true;
        },
        async onOkRejectPurchasePlan() {
            if (!this.rejectForm.docIds) {
                this.$message.error('检测到数据异常，请填刷新后重试');
                return;
            }
            if (!this.rejectForm.rejectRemark) {
                this.$message.error('请填写驳回理由');
                return;
            }
            this.$confirm('驳回将无法恢复采购计划，需运营重新发起进货审批，确定要驳回采购计划吗？', '提醒', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.rejectPurchasePlanLoading = true;
                rejectPurchasePlanGoNewPlan({ docIds: this.rejectForm.docIds, rejectRemark: this.rejectForm.rejectRemark }).then((reqRlt) => {
                    if (reqRlt.success) {
                        if (reqRlt.data)
                            this.$message.success(reqRlt.data);
                        else
                            this.$message.success('驳回成功');
                        this.rejectPurchasePlanLoading = false;
                        this.rejectPurchasePlanVisible = false;
                        this.onRefresh();
                    }
                    else {
                        this.rejectPurchasePlanLoading = false;
                    }
                });
            }).catch(() => {
                this.rejectPurchasePlanLoading = false;
            });
        },
        onBatchReject() {
            if (this.applyProcurementList.length <= 0) {
                this.$message({ type: 'error', message: '请至少勾选一行数据!' });
                return;
            }
            let planIds = this.applyProcurementList.map(item => item.id);
            let docIds = planIds.join(',');
            this.$confirm('驳回将无法恢复采购计划，需运营重新发起进货审批，确定要驳回勾选的采购计划吗？', '提醒', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                rejectPurchasePlanGoNewPlan({ docIds: docIds, rejectRemark: this.rejectForm.rejectRemark }).then((reqRlt) => {
                    if (reqRlt.success) {
                        this.$message.success(reqRlt.msg);
                        this.rejectPurchasePlanVisible = false;
                        this.onRefresh();
                    }
                });
            }).catch(() => {
            });
        },
        async onShowAutoSet() {
            this.autoSetVisible = true;
            this.selAutoSetTableList = [];
            this.autoSetListLoading = true;
            const res = await getPurchasePlanAutoSetList();
            this.autoSetListLoading = false;
            if (res?.success) {
                this.autoSetTableList = res.data;
                this.autoSetTableList.forEach((item, index) => {
                    item.setIndex = index + 1
                })
                if (this.autoSetTableList && this.autoSetTableList.length > 0) {
                    this.autoSetTableList.forEach(f => {
                        if (f.id > 0) {
                            this.selAutoSetTableList.push(f);
                        }
                    });

                    this.$nextTick(() => {
                        this.selAutoSetTableList.forEach(f => {
                            if (f.id > 0) {
                                this.$refs.autoSetTable.toggleRowSelection(f, true);
                            }
                        });
                    });
                }
                this.chenGuangAutoSetTableList = res.data.map(item => {
                    return {
                        brandId: item.brandId, brandName: item.brandName,
                        userId: item.userId, chenGuangSelBrandIndex: null
                    };
                });
                const res2 = await getPurchasePlanAutoSetOtherList();
                if (res2?.success) {
                    this.chenGuangAutoSetTableList.forEach(f => {
                        let find = res2.data.find(w => w.brandId == f.brandId);
                        if (find)
                            f.chenGuangSelBrandIndex = find.chenGuangSelBrandIndex;
                    });
                }
            }
        },
        async onOkAutoSet(isFenPei) {
            this.autoSetListLoading = true;
            let dto = { dates: this.selAutoSetTableList, otherdates: this.chenGuangAutoSetTableList };
            console.log(dto, "dto");
            const res = await savePurchasePlanAutoSetList(dto);
            this.autoSetListLoading = false;
            if (res?.success) {
                if (isFenPei == true) {
                    this.autoSetListLoading = true;
                    const res2 = await onekeyPurchasePlanAutoSetList(dto);
                    this.autoSetListLoading = false;
                    if (res2?.success) {
                        this.$message.success('保存&一键分配成功');
                    }
                }
                else {
                    this.$message.success('保存成功');
                }
            }
        },
        autosetselectchange: function (rows, row) {
            //先把当前也的数据全部移除
            this.selAutoSetTableList = [];
            this.index = 1
            // //把选中的添加
            rows.forEach(f => {
                let index = this.selAutoSetTableList.findIndex((v) => (v === f.brandId));
                if (index === -1) {
                    this.selAutoSetTableList.push(f);
                }
            });
        },
        autosetselectall: function (rows) {
            this.selAutoSetTableList = rows;
        }
    },
};
</script>
<style lang="scss" scoped>
.my-search ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

.ad-form-query {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}
</style>
<style>
.el-image__inner {
    max-width: 50px;
    max-height: 50px;
}

.active {
    color: #409EFF;
}

.btnClass {
    border: none;
    background-color: rgb(238, 241, 246);
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
