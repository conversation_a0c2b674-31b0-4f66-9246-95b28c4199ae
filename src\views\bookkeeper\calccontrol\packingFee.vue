<template>
    <container>
        <template #header>
        </template>
        <ces-table ref="table" :that='that' :isIndex='true' :hasexpand='true' @sortchange='sortchange'
            @select='selectchange' :isSelection='false' :isSelectColumn='true' :tableData='list' :tableCols='tableCols'
            :tableHandles='tableHandles' :showsummary='false' :loading="listLoading" />
        <template #footer>
            <my-pagination ref="pager" :total="total" :checked-count="sel.length" @get-page="getlist" />
        </template>


        <el-dialog title="包装费" :visible.sync="dialogVisibleSyj" width="30%" :close-on-click-modal="false"
            v-dialogDrag>
            <el-upload ref="upload2" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
                accept=".xlsx" :http-request="uploadFile" :on-success="uploadSuccess" :on-change="onUploadChange"
                :on-remove="onUploadRemove">
                <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                </template>
                <my-confirm-button style="margin-left: 10px;" size="small" type="success" :loading="dialogLoadingSyj"
                    @click="onSubmitupload">上传</my-confirm-button>
            </el-upload>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisibleSyj = false">关闭</el-button>
            </span>
        </el-dialog>
    </container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { importPackingFee } from '@/api/monthbookkeeper/import'
import { GetPackingFeeList } from '@/api/monthbookkeeper/financialDetail'
import container from '@/components/my-container/noheader'
import MyConfirmButton from "@/components/my-confirm-button";
import cesTable from "@/components/Table/table.vue";
const tableCols = [
    { istrue: true, prop: 'yearMonth', label: '月份', sortable: 'custom', width: '100' },
    { istrue: true, prop: 'wareName', label: '仓库', sortable: 'custom', width: '260' },
    { istrue: true, prop: 'totalPacking', label: '总包装费', sortable: 'custom', width: '160' },
    { istrue: true, prop: 'sharePackingFee', label: '参与分摊的包装费', sortable: 'custom', width: '160' },
    //以前这个字段是存出仓成本（运），20250416财务提需求改成了出仓成本，实际后台也从存出仓成本，只不过字段名还叫chuCangYunYing
    { istrue: true, prop: 'packing', label: '包装费', sortable: 'custom', width: '160' },
];
const tableHandles = [
    { label: "导入", handle: (that) => that.onImportShow() },
    { label: "导入模板", handle: (that) => that.onImportModel() },
]
export default {
    name: 'packingFee',
    components: { cesTable, container, vxetablebase, MyConfirmButton },
    props: {
        filter: {}
    },
    data() {
        return {
            that: this,
            list: [],
            tableCols: tableCols,
            tableHandles: tableHandles,
            pager: { OrderBy: "totalPacking", IsAsc: false },
            total: 0,
            sel: [],
            listLoading: false,
            dialogVisibleSyj: false,
            dialogLoadingSyj: false,
            fileList: [],
        }
    },
    mounted() {

    },
    beforeUpdate() { },
    methods: {
        onSearch() {
            this.$refs.pager.setPage(1)
            this.getlist()
        },
        async getlist() {
            let pager = this.$refs.pager.getPager()
            const params = { ...pager, ...this.pager, ... this.filter }

            if (!params.yearMonth) {
                this.$message({ message: "请选择月份", type: "warning" });
                return;
            }
            this.listLoading = true
            const res = await GetPackingFeeList(params)
            this.listLoading = false
            if (!res?.success) return
            this.total = res.data?.total ?? 0
            const data = res.data?.list
            data?.forEach(d => {
                d._loading = false
            })
            this.list = data
        },
        onImportModel() {
            window.open("/static/excel/book/月报计算包装费导入模板.xlsx", "_blank");
        },
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        async onImportShow() {
            this.dialogVisibleSyj = true;
        },
        async onUploadChange(file, fileList) {
            this.fileList = fileList;
        },
        async onUploadRemove(file, fileList) {
            this.fileList = [];
        },
        async uploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            const form = new FormData();
            form.append("upfile", item.file);
            this.dialogLoadingSyj = true;
            const res = await importPackingFee(form);
            this.dialogLoadingSyj = false;
            if (res?.success) {
                this.$message({ message: '上传成功,正在导入中...', type: "success" });
                this.dialogVisibleSyj = false;
            }
        },
        async uploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
        },
        async onSubmitupload() {
            if (this.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload2.submit();
        },
    }
}
</script>
