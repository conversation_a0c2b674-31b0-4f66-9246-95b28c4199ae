<template>
    <container>
        <el-tabs v-model="activeName" style="height: calc(100% - 40px) ;">
            <el-tab-pane label="待操作" name="first" style="height: 100%;">
                <index ref="index" @toStyleCodeLoss="toStyleCodeLoss"></index>
            </el-tab-pane>
            <el-tab-pane label="已认领" name="second" style="height: 100%;">
                <goodscodeallocation ref="goodscodeallocation"></goodscodeallocation>
            </el-tab-pane>
            <el-tab-pane label="已申报" name="three" style="height: 100%;">
                <ApplyStockGoodsList ref="ApplyStockGoodsList"></ApplyStockGoodsList>
            </el-tab-pane>
            <el-tab-pane label="系列编码亏损统计" name="four" style="height: 100%;" :lazy="true">
                <styleCodeContinuLosses ref="styleCodeContinuLosses"></styleCodeContinuLosses>
            </el-tab-pane>
        </el-tabs>
    </container>
</template>

<script>
import { formatTime } from "@/utils";
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import container from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import index from './index.vue'
import goodscodeallocation from "./goodscodeallocation.vue";
import ApplyStockGoodsList from "./ApplyStockGoodsList.vue";
import styleCodeContinuLosses from '@/views/order/procodesimilarity/serialLoseMoney/index.vue';
import inputYunhan from "@/components/Comm/inputYunhan";

const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
const endTime = formatTime(new Date(), "YYYY-MM-DD");
export default {
    name: 'YunHanAdminGoodscodestocktab',
    components: { container, MyConfirmButton, vxetablebase, index, goodscodeallocation, inputYunhan, ApplyStockGoodsList, styleCodeContinuLosses },
    data() {
        return {
            that: this,
            filter: {
                startDate: null,
                endDate: null,
                goodsCode: null,
                timerange: [startTime, endTime],
            },
            newWareHouseList: [],
            activeName: 'first',
            pickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    this.selectDate = minDate.getTime()
                    if (maxDate) {
                        this.selectDate = ''
                    }
                },
                disabledDate: (time) => {
                    if (this.selectDate !== '') {
                        const one = 30 * 24 * 3600 * 1000
                        const minTime = this.selectDate - one
                        const maxTime = this.selectDate + one
                        return time.getTime() < minTime || time.getTime() > maxTime
                    }
                }
            },
        };
    },

    async mounted() {
        await this.onSearch();
    },
    methods: {
        async onSearch() {
            this.$nextTick(() => {
                if (this.activeName == 'first') this.$refs.index.onSearch();
                if (this.activeName == 'second') this.$refs.warehousingordervidelate.onSearch();
                if (this.activeName == 'three') this.$refs.warehousingordervidelateUrgent.onSearch();
            })
        },
        async callbackGoodsCode(val) {
            // this.inputedit = true;
            this.filter.goodsCode = val;
            this.onSearch();
        },
        toStyleCodeLoss(){
            this.activeName = 'four';
        },
    },
};
</script>

<style lang="scss" scoped>
.three .el-tabs__item {
    color: red;
}
</style>
