<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-input v-model.trim="ListInfo.goodsCode" placeholder="商品编码" maxlength="50" clearable class="publicCss" />
        <el-input v-model.trim="ListInfo.goodsName" placeholder="商品名称" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.warehouse" placeholder="仓库" class="publicCss" clearable filterable>
          <el-option v-for="item in stashList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.goodsNumber" placeholder="商品编号" maxlength="50" clearable class="publicCss" />
        <el-select v-model="ListInfo.bitNumber" placeholder="模糊搜索并选择库位" :remote-method="remoteMethod" remote clearable
          filterable :loading="searchloading" class="publicCss" style="width: 145px;">
          <el-option v-for="item in locationList" :key="item.warehouseBitCode" :label="item.warehouseBitCode"
            :value="item.warehouseBitCode" />
        </el-select>
        <el-select v-model="ListInfo.groupId" placeholder="运营组" class="publicCss" clearable filterable>
          <el-option v-for="item in directorGroupList" :key="item.key" :label="item.value" :value="item.key" />
        </el-select>
        <el-select v-model="ListInfo.brandId" placeholder="采购组" class="publicCss" clearable filterable>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="ListInfo.storageStatus" placeholder="入库状态" class="publicCss" clearable filterable>
          <el-option :key="'1'" label="待入库" value="1" />
          <el-option :key="'2'" label="待归还" value="2" />
          <el-option :key="'3'" label="在库" value="3" />
          <el-option :key="'4'" label="待领取" value="4" />
        </el-select>
        <el-select v-model="ListInfo.sampleSource" placeholder="请选择样品来源" class="publicCss" clearable filterable>
          <el-option label="对手样品" value="对手样品"></el-option>
          <el-option label="商家样品" value="商家样品"></el-option>
          <el-option label="公司样品" value="公司样品"></el-option>
        </el-select>
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="借出开始时间" end-placeholder="借出结束时间" :picker-options="pickerOptions"
          style="width: 210px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <div class="publicCss" style="width: 190px;">
          <inputYunhan ref="refyhGoodsCode" :inputt.sync="ListInfo.yhGoodsCode" v-model="ListInfo.yhGoodsCode"
            width="190px" placeholder="公司商品编码/Enter输入多条" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="2000" @callback="callbackMethod($event, 'yhGoodsCode')" title="公司商品编码">
          </inputYunhan>
        </div>
        <el-button type="primary" @click="getList('search')">查询</el-button>
      </div>
      <div class="top">
        <el-input v-model.trim="ListInfo.buyNo" placeholder="采购单号" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <number-range :min.sync="ListInfo.lendTimeCount" :max.sync="ListInfo.lendEndTimeCount" min-label="借出时长最小值"
          max-label="借出时长最大值" class="publicCss" style="width: 165px;" />
        <el-select v-model="ListInfo.isYuQi" placeholder="是否逾期" class="publicCss" clearable filterable
          style="width: 100px;">
          <el-option :key="'1'" label="是" :value="1" />
          <el-option :key="'0'" label="否" :value="0" />
        </el-select>
        <number-range :min.sync="ListInfo.startDaysOverdue" :max.sync="ListInfo.endLendEndTime" min-label="逾期开始天数"
          max-label="逾期结束天数" class="publicCss" style="width: 165px;" />
        <el-input v-model.trim="ListInfo.manufacturerName" placeholder="厂家名称" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <el-select v-model="ListInfo.isPai" placeholder="已拍/待拍" class="publicCss" style="width: 100px;" clearable
          filterable>
          <el-option label="已拍" :value="1"></el-option>
          <el-option label="待拍" :value="2"></el-option>
        </el-select>
        <el-input v-model.trim="ListInfo.createdUserName" placeholder="拍摄人" maxlength="50" clearable class="publicCss"
          style="width: 100px;" />
        <el-select v-model="ListInfo.goodsState" style="width: 90px" placeholder="编码状态" clearable class="publicCss">
          <el-option label="换厂" :value="1" />
          <el-option label="新品" :value="2" />
          <el-option label="采购降价需要比样" :value="10" />
          <el-option label="月首单且以往都有采购记录" :value="11" />
        </el-select>
        <el-button type="primary" class="top_button" @click="downLoadFile">导入模版</el-button>
        <el-button type="primary" class="top_button" @click="startImport">导入</el-button>
        <el-button type="primary" class="top_button" @click="exportProps">导出</el-button>
        <el-button type="primary" class="top_button" @click="onNewSample">新增样品</el-button>
        <el-button type="primary" class="top_button" @click="onSampleApply">样品领用申请</el-button>
        <el-button type="primary" class="top_button" @click="printBarcode">打印条码</el-button>
        <el-button type="primary" class="top_button" @click="changePackage">更换包材</el-button>
        <el-button type="primary" class="top_button" @click="checkSample" :disabled="banReview"
          v-if="checkPermission('SampleRegistrationReview')">复核</el-button>
        <el-switch v-model="ListInfo.isLast" active-text="明细" inactive-text="最新" :active-value="2" :inactive-value="1"
          @change="getList" />
      </div>
    </template>
    <vxetablebase :id="'sampleRegistration202503061707'" :tablekey="'sampleRegistration202503061707'" ref="table"
      :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange' :tableData='tableData'
      :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" style="width: 100%;  margin: 0"
      :loading="loading" :height="'100%'" @select="selectchange" @checkbox-range-end="selectchange" :border="true"
      :treeProp="ListInfo.isLast == 2 ? { transform: true, rowField: 'id', parentField: 'parentId', accordion: true } : {}">
      <template slot="right">
        <vxe-column title="操作" width="200" fixed="right">
          <template #default="{ row, $index }">
            <div style="display: flex;justify-content: center;">
              <el-button type="text" class="Inside_button" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" class="Inside_button" @click="handleInspection(row)"
                v-if="row.numberShots && row.numberShots > 1 && (row.isLastEqual === null || row.isLastEqual === undefined || row.isLastEqual === '')">质检</el-button>
              <el-button type="text" class="Inside_button" @click="handleCopy(row)">复制</el-button>
              <el-button type="text" class="Inside_button" @click="handlePrint(row)"
                v-if="row.goodsNumber">打印</el-button>
              <el-button type="text" class="Inside_button" @click="replayMethod(row)"
                v-if="row.goodsNumber">补打</el-button>
              <el-button type="text" class="Inside_button" @click="handleLog(row)">日志</el-button>
            </div>
          </template>
        </vxe-column>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="样品登记" :visible.sync="sampleRegistrationVisible" width="55%" v-dialogDrag
      style="margin-top: -11vh;">
      <registrationPopUp v-if="sampleRegistrationVisible" ref="refregistrationPopUp"
        @closingMethodClose="closingMethodClose" :interactiveData="interactiveData" :stashList="stashList"
        @close="sampleRegistrationVisible = false" />
    </el-dialog>

    <el-dialog title="打印" :visible.sync="printVisible" width="40%" v-dialogDrag @close="printCloseMethod">
      <div style="height: 500px;display: flex;flex-direction: column;">
        <div>
          <el-select v-model="printSize" placeholder="请选择打印尺寸" clearable style="width: 110px;margin-right: 5px;"
            @change="changePrintSize($event, 2)">
            <el-option v-for="item in printSizeList" :key="item.label" :label="item.label" :value="item.value" />
          </el-select>
          高(厘米):
          <el-input-number v-model="qrCodeHeight" :min="1" :max="999" :controls="false" :precision="1"
            placeholder="请输入高度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
          宽(厘米):
          <el-input-number v-model="qrCodeWidth" :min="1" :max="999" :controls="false" :precision="1"
            placeholder="请输入宽度(厘米)" style="width:90px;margin-right: 5px;" @blur="onPrintMethod(1)" />
          <el-input-number v-model="numQRCode" :min="1" :max="2000" placeholder="二维码张数"
            style="width: 20%;margin-right: 5px;" :precision="0" v-if="!islinePrinting"></el-input-number>
          <el-button v-if="!islinePrinting" type="primary" @click="generateQRCode">生成</el-button>
          <el-button type="primary" @click="printQRCode('#printid')">打印</el-button>
        </div>
        <div style="margin-top: 5px;">
          <el-switch @change="onPrintMethod(6)" v-model="switchshow" active-text="显示id" inactive-text="隐藏id">
          </el-switch>
        </div>
        <el-scrollbar ref="refscrollbar" style="height: 95%;margin-top: 10px;">
          <div id="printid" v-if="printVisible" v-loading="printLoading" :style="qrcodeContainerStyle">
            <div v-for="(item, index) in goodsCodeList" :key="index" class="qrcode-item">
              <!-- <canvas :id="`qrcode${index}`"></canvas> -->
              <img :id="'barcode1' + item" :style="qrcodeContainerStyle" />
              <!-- <div v-show="switchshow" class="qrcode-id">{{ item }}</div> -->
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>

    <el-dialog title="样品领用申请" :visible.sync="applicationVisible" width="45%" v-dialogDrag style="margin-top: -7vh;">
      <receiveApplication v-if="applicationVisible" ref="refreceiveApplication" @cancelFormMethod="cancelFormMethod"
        @close="applicationVisible = false" :deptList="deptList" :selectList="selectList" />
    </el-dialog>

    <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
      <span>
        <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
          accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
          :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
          <template #trigger>
            <el-button size="small" type="primary">选取文件</el-button>
          </template>
          <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
            @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
        </el-upload>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="补打" :visible.sync="replayInfo.visible" width="45%" v-dialogDrag style="margin-top: -7vh;">
      <replayComponent ref="refreplayComponent" v-if="replayInfo.visible" :rowData="replayInfo.row" />
    </el-dialog>

    <el-dialog title="更换包材" :visible.sync="packageInfo.visible" width="20%" v-dialogDrag>
      <div style="display: flex;align-items: center;padding: 20px;">
        <span style="color: red;">*</span>
        <el-select v-model="packageInfo.packageGoodsCode" placeholder="请选择包材商品编码" clearable filterable
          style="width: 80%;margin-left: 5px;">
          <el-option v-for="item in materialsList" :key="item.goodsCode" :label="item.shortName"
            :value="item.goodsCode" />
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="packageInfo.visible = false">关闭</el-button>
        <el-button type="primary" @click="onSavePackage">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="复核" :visible.sync="reviewInfo.visible" width="25%" v-dialogDrag>
      <div>
        <el-form :model="reviewInfo.form" label-width="100px" :rules="reviewrules" ref="refreviewForm">
          <el-form-item label="复核结果" prop="isFuHeTongGuo">
            <el-select v-model="reviewInfo.form.isFuHeTongGuo" placeholder="请选择复核结果" clearable filterable
              style="width: 80%;">
              <el-option label="通过" :value="1"></el-option>
              <el-option label="不通过" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="复核备注" v-if="reviewInfo.form.isFuHeTongGuo == 0" prop="noFuHeTongGuoRemark">
            <el-input v-model="reviewInfo.form.noFuHeTongGuoRemark" placeholder="请输入复核备注" type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }" :rows="4" style="width: 80%;"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="reviewInfo.visible = false">关闭</el-button>
        <el-button type="primary" @click="onSaveReview">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="质检" :visible.sync="inspectionInfo.visible" width="70%" v-dialogDrag
      style="margin-top: -6vh!important;">
      <qualityInspection v-if="inspectionInfo.visible" :rowData="inspectionInfo.rowData"
        @close="inspectionInfo.visible = false" :displayData="inspectionInfo.displayData" />
    </el-dialog>

    <vxe-modal title="日志" v-model="logInfo.visible" :esc-closable="true" :width='1200' :height='500' marginSize='-500'>
      <vxetablebase :id="'sampleRegistrationLog202506181750'" :tablekey="'sampleRegistrationLog202506181750'"
        ref="table1" :tableCols='logTableCols' :isSelection="false" :isSelectColumn="false" :tableData='logInfo.data'
        style="width: 99%;  margin: 0" :loading="logInfo.loadingLog" :height="'100%'" :border="true"
        @sortchange='sortchangeLog'>
      </vxetablebase>
    </vxe-modal>
  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { pickerOptions, downloadLink } from '@/utils/tools'
import { getAllProBrand } from '@/api/inventory/warehouse'
import { getDirectorGroupList } from '@/api/operatemanage/base/shop'
import registrationPopUp from "./registrationPopUp.vue"
import receiveApplication from "./receiveApplication.vue"
import { importSampleRegistrationAsync, getSampleRegistration, exportSampleRegistrationAsync, getSampleWarehouse, getBathSuiJiGoodsCode, getWarehouseLocationManagement, addOrEditSampleRegistration, addOrEditSampleRegistrationPackage, getSampleRegistrationPackageLog, editSampleRegistrationTongGuo, getSampleRegistrationVerifyData } from '@/api/inventory/sampleGoods';
import dayjs from 'dayjs'
import numberRange from "@/components/number-range/index.vue";
import QRCode from "qrcode";
import Print from 'print-js'
import { printJS } from '@/utils/print'
import printQRCode from "@/utils/printQRCode";
import _ from 'lodash'
import decimal from '@/utils/decimal'
import replayComponent from "./replayComponent.vue"
import inputYunhan from "@/components/Comm/inputYunhan";
import { getList } from "@/api/inventory/basicgoods";
import qualityInspection from "./qualityInspection.vue"
const tableCols = [
  { istrue: true, width: '80', type: "checkbox" },
  { sortable: 'custom', width: '140', align: 'center', prop: 'goodsCode', label: '商品编码', treeNode: true, },
  { sortable: 'custom', width: '140', align: 'center', prop: 'goodsName', label: '商品名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'sampleSource', label: '样品来源', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'goodsNumber', label: '商品编号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'yhGoodsCode', label: '公司商品编码', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'styleCode', label: '款式名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'ckGoodsCode', label: '参考商品编码', },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'goodsState', label: '编码状态', formatter: (row) => {
      return row.goodsStateStr;
    }
  },
  { sortable: 'custom', width: '100', align: 'center', prop: 'bitNumber', label: '库位', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'warehouse', label: '仓库', },
  {
    sortable: 'custom', width: '100', align: 'center', prop: 'storageStatus', label: '入库状态', formatter: (row) => {
      return row.storageStatus == 1 ? '待入库' : row.storageStatus == 2 ? '待归还' : row.storageStatus == 3 ? '在库' : row.storageStatus == 4 ? '待领取' : ''
    }
  },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo', label: '采购单号', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'numberShots', label: '拍摄次数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'shootingTime', label: '拍摄时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '拍摄人', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'images', label: '商品图片', type: "images", },
  { sortable: 'custom', width: '100', align: 'left', prop: 'outerPackingPicture', label: '外包装图片', type: "images", },
  { sortable: 'custom', width: '100', align: 'left', prop: 'physicalPicture', label: '实物图片', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isChao40CM', label: '折叠单边是否超40CM', formatter: (row) => row.isChao40CM == 1 ? '是' : row.isChao40CM == 0 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isZheDie', label: '是否可折叠', formatter: (row) => row.isZheDie == 1 ? '是' : row.isZheDie == 0 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredWeight', label: '测量重(g)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredWeightImages', label: '测重图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredChang', label: '打包长(mm)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredChangImages', label: '测打包长图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredWidth', label: '打包宽(mm)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredWidthImages', label: '测打包宽图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'measuredHeight', label: '打包高(mm)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'measuredHeightImages', label: '测打包高图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredChang', label: '展开长(mm)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'zhankaiMeasuredChangImages', label: '测展开长图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredWidth', label: '展开宽(mm)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'zhankaiMeasuredWidthImages', label: '测展开宽图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'zhankaiMeasuredHeight', label: '展开高(mm)', },
  { sortable: 'custom', width: '100', align: 'left', prop: 'zhankaiMeasuredHeightImages', label: '测展开高图', type: "images", },
  { sortable: 'custom', width: '100', align: 'center', prop: 'recentLender', label: '最近借出人', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'lastExitTime', label: '最近借出时间', },
  { width: '100', align: 'center', prop: 'deliveryTime', label: '借出时长', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'estimatedServiceLife', label: '预计归还时间', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'actualReturnTime', label: '实际归还时间', },
  { width: '100', align: 'center', prop: 'daysOverdue', label: '逾期天数', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'colour', label: '颜色', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isJianCe', label: '是否检测', formatter: (row) => row.isJianCe == 1 ? '是' : row.isJianCe == 2 ? '否' : '', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'groupName', label: '运营组', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购员', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandDeptName', label: '采购组', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'manufacturerName', label: '厂家名称', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'manufacturerLink', label: '厂家链接', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'opponentShopName', label: '对手店铺', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'opponentLink', label: '对手链接', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'expressNo', label: '快递单号/标识', },
  { width: '120', align: 'center', prop: 'packageGoodsCode', label: '包材商品编码', },
  { width: '100', align: 'center', prop: 'package', label: '包材名称', },
  { width: '100', align: 'center', prop: 'packageImage', label: '包材图片', type: "images", },
  { width: '100', align: 'center', prop: 'packageSize', label: '包材尺寸', },
  { width: '100', align: 'center', prop: 'damageRate', label: '破损率', formatter: (row) => row.damageRate || row.damageRate == 0 ? row.damageRate + '%' : '' },
  { width: '120', align: 'center', prop: 'newPackageGoodsCode', label: '新包材商品编码', },
  { width: '100', align: 'center', prop: 'newPackage', label: '新包材名称', },
  { width: '100', align: 'center', prop: 'newPackageImage', label: '新包材图片', type: "images", },
  { width: '100', align: 'center', prop: 'newPackageSize', label: '新包材尺寸', },
  { width: '100', align: 'center', prop: 'newDamageRate', label: '新破损率', formatter: (row) => row.newDamageRate || row.newDamageRate == 0 ? row.newDamageRate + '%' : '' },
  { width: '100', align: 'center', prop: 'isFuHeTongGuo', label: '复核结果', formatter: (row) => row.isFuHeTongGuo == 1 ? '复核通过' : row.isFuHeTongGuo == 0 ? '复核不通过' : '', },
  { width: '100', align: 'center', prop: 'noFuHeTongGuoRemark', label: '未通过复核原因', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'isRevert', label: '是否同品归还', formatter: (row) => row.isRevert == 1 ? '是' : row.isRevert == 0 ? '否' : '', },
]

const logTableCols = [
  { sortable: 'custom', width: '150', align: 'center', prop: 'packageGoods', label: '包材编码', },
  { sortable: 'custom', width: 'auto', align: 'center', prop: 'package', label: '包材名称', },
  { width: '100', align: 'center', prop: 'packageImage', label: '包材图片', type: "images", },
  { sortable: 'custom', width: '250', align: 'center', prop: 'packageSize', label: '包材尺寸', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'damageRate', label: '破损率', formatter: (row) => row.damageRate || row.damageRate == 0 ? row.damageRate + '%' : '' },
  { sortable: 'custom', width: '100', align: 'center', prop: 'createdUserName', label: '创建人', },
  { sortable: 'custom', width: '120', align: 'center', prop: 'createdTime', label: '创建时间', },
]
export default {
  name: "sampleRegistration",
  components: {
    MyContainer, vxetablebase, registrationPopUp, receiveApplication, numberRange, replayComponent, inputYunhan, qualityInspection
  },
  props: {
    deptList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      logInfo: {
        visible: false,
        data: [],
        loadingLog: false,
        orderBy: 'createdTime',
        isAsc: false,
        row: {},
      },
      reviewInfo: {
        visible: false,
        form: {
          isFuHeTongGuo: null,
          noFuHeTongGuoRemark: null,
        }
      },
      reviewrules: {
        isFuHeTongGuo: [{ required: true, message: '请选择复核结果', trigger: 'change' }],
        noFuHeTongGuoRemark: [{ required: true, message: '请输入复核备注', trigger: 'blur' }],
      },
      materialsList: [],
      packageInfo: {
        visible: false,
        packageGoodsCode: null,
      },
      replayInfo: {
        visible: false,
        row: {},
      },
      //质检
      inspectionInfo: {
        visible: false,
        rowData: {},
        displayData: {},
      },
      banReview: false,//勾选复核通过就禁用复核按钮
      islinePrinting: false,
      searchloading: false,
      locationList: [],
      stashList: [],
      interactiveData: {},
      dialogVisible: false,
      fileList: [],
      uploadLoading: false,
      fileparm: {},
      applicationVisible: false,
      sampleRegistrationVisible: false,
      brandlist: [],
      directorGroupList: [],
      that: this,
      ListInfo: {
        isPai: null,//已拍/待拍
        currentPage: 1,
        pageSize: 50,
        orderBy: 'createdTime',
        isAsc: false,
        goodsCode: null,//商品编码
        goodsName: null,//商品名称
        warehouse: null,//仓库
        goodsNumber: null,//商品编号
        bitNumber: null,//库位
        groupId: null,//运营组
        brandId: null,//采购组
        storageStatus: null,//入库状态
        lendTime: null,//借出开始时间
        lendEndTime: null,//借出结束时间
        lendTimeCount: null,//借出时长最小值
        lendEndTimeCount: null,//借出时长最大值
        startDaysOverdue: null,//借出开始时间
        endLendEndTime: null,//借出结束时间
        isYuQi: null,//是否逾期
        manufacturerName: null,//厂家名称
        createdUserName: null,//拍摄人
        yhGoodsCode: null,//公司商品编码
        buyNo: null,//采购单号
        sampleSource: null,//样品来源
        isLast: 1,//是否明细
      },
      timeRanges: [],
      tableCols,
      logTableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      printVisible: false,
      printLoading: false,
      printSizeList: [
        { label: '宽10*高10', value: '10x10' },
        { label: '宽8*高8', value: '8x8' },
        { label: '宽5*高5', value: '5x5' },
        { label: '宽5*高4', value: '5x4' },
        { label: '宽4*高4', value: '4x4' },
        { label: '宽3*高3', value: '3x3' },
        { label: '宽2.5*高2.5', value: '2.5x2.5' },
        { label: '宽1.5*高1.5', value: '1.5x1.5' }
      ],
      printSize: '4x4',
      qrCodeWidth: 4,
      qrCodeHeight: 4,
      numQRCode: 0,
      scrollbarWidth: 0,
      rowDisplayNum: 1,
      goodsCodeList: [],
      selectList: [],
      switchshow: true,
    }
  },
  computed: {
    qrcodeContainerStyle() {
      const containerWidth = this.scrollbarWidth
      const qrCodeWidthWithMargin = this.qrCodeWidth * 37.795 + 20; // 200px宽度 + 20px的间距
      const maxRowDisplayNum = Math.floor(containerWidth / qrCodeWidthWithMargin); // 最大能显示的二维码个数
      // 计算每行显示的二维码个数，如果二维码宽度超出总宽度，则自动换行
      const rowDisplayNum = this.rowDisplayNum > maxRowDisplayNum ? maxRowDisplayNum : this.rowDisplayNum;
      return {
        width: `${decimal(decimal(this.qrCodeWidth, 37.795, 0, '*'), 90, 0, '+')}px`,
        height: `${decimal(decimal(this.qrCodeHeight, 37.795, 0, '*'), 15, 0, '+')}px`,
        pageBreakInside: 'avoid',
        // display: 'grid',
        // justifyContent: 'center',
        // alignItems: 'center',
        // pageBreakInside: 'avoid',
      };
    }
  },
  watch: {
    printVisible(val) {
      if (val) {
        this.getScrollbarWidth()
      }
    },
  },
  async mounted() {
    await this.getList()
    await this.init()
  },
  methods: {
    async handleInspection(row) {
      this.loading = true;
      const { data, success } = await getSampleRegistrationVerifyData({ id: row.id });
      this.loading = false;
      if (!success) return
      this.$nextTick(() => {
        this.inspectionInfo.rowData = row
        this.inspectionInfo.displayData = data
        this.inspectionInfo.visible = true
      })
    },
    sortchangeLog({ order, prop }) {
      if (prop) {
        this.logInfo.orderBy = prop
        this.logInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.handleLog(this.logInfo.row)
      }
    },
    async handleLog(row) {
      this.logInfo.loadingLog = true
      const { data, success } = await getSampleRegistrationPackageLog({
        ids: [row.id],
        orderBy: this.logInfo.orderBy,
        isAsc: this.logInfo.isAsc,
      })
      this.logInfo.loadingLog = false
      this.logInfo.row = row
      if (success) {
        data.list.map(item => {
          const processAnalysis = (voucher) => {
            if (voucher) {
              let a = voucher.split(',')
              let newarr = a.map(itemm => ({ url: itemm }))
              return JSON.stringify(newarr)
            }
            return voucher
          }
          item.packageImage = processAnalysis(item.packageImage)
        })
        this.logInfo.data = data.list
        this.logInfo.visible = true
      }
    },
    checkSample() {
      if (this.selectList.length == 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.reviewInfo.visible = true
      this.$nextTick(() => {
        this.$refs.refreviewForm.clearValidate()
        this.reviewInfo.form.isFuHeTongGuo = null
        this.reviewInfo.form.noFuHeTongGuoRemark = null
      })
    },
    onSaveReview() {
      this.$refs.refreviewForm.validate(async (valid) => {
        if (valid) {
          const { data, success } = await editSampleRegistrationTongGuo({
            ids: this.selectList.map(item => item.id),
            isFuHeTongGuo: this.reviewInfo.form.isFuHeTongGuo,
            noFuHeTongGuoRemark: this.reviewInfo.form.noFuHeTongGuoRemark
          })
          if (success) {
            this.$message.success('复核成功')
            this.reviewInfo.visible = false
            this.selectList = []
            this.getList()
          }
        }
      })
    },
    changePackage() {
      if (this.selectList.length == 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.packageInfo.packageGoodsCode = null
      this.packageInfo.visible = true
    },
    async onSavePackage() {
      if (!this.packageInfo.packageGoodsCode) {
        this.$message.warning('请输入包材商品编码')
        return
      }
      const { data, success } = await addOrEditSampleRegistrationPackage({
        packageGoodsCode: this.packageInfo.packageGoodsCode,
        ids: this.selectList.map(item => item.id)
      })
      if (success) {
        this.$message.success('更换包材成功')
        this.getList()
        this.selectList = []
        this.packageInfo.visible = false
      }
    },
    callbackMethod(val, type) {
      const map = {
        yhGoodsCode: () => (this.ListInfo.yhGoodsCode = val),
      };
      map[type]?.();
    },
    replayMethod(row) {
      this.replayInfo.row = JSON.parse(JSON.stringify(row))
      this.$nextTick(() => {
        this.replayInfo.visible = true
      })
    },
    async handlePrint(row) {
      this.printSize = 4
      this.qrCodeWidth = 4
      this.qrCodeHeight = 4
      this.numQRCode = 1
      this.goodsCodeList = [row.goodsNumber]
      this.islinePrinting = true
      await this.onPrintMethod()
      this.printVisible = true
    },
    selectchange(val) {
      this.selectList = val;
      this.banReview = this.selectList.some(item => item.isFuHeTongGuo == 1)
    },
    parseJsonSafely(str) {
      try {
        return str && str.trim() ? JSON.parse(str) : [];
      } catch (error) {
        // 如果不是 JSON 字符串，直接返回包含该字符串的数组
        return str ? [{ url: str }] : [];
      }
    },
    // 处理数据格式
    processData(data) {
      const processedData = { ...data };
      // 处理 goodsVido
      if (data.goodsVido) {
        if (typeof data.goodsVido === 'string') {
          processedData.goodsVido = data.goodsVido.split(',').map(item => ({
            url: item,
            fileName: item,
            relativePath: null,
            domain: null
          }));
        } else if (Array.isArray(data.goodsVido)) {
          processedData.goodsVido = data.goodsVido;
        } else {
          processedData.goodsVido = [];
        }
      } else {
        processedData.goodsVido = [];
      }
      // 需要处理为数组的字段
      const mediaFields = [
        'images',
        'outerPackingPicture',
        'physicalPicture',
        'measuredWeightImages',
        'measuredWidthImages',
        'zhankaiMeasuredHeightImages',
        'zhankaiMeasuredWidthImages',
        'zhankaiMeasuredChangImages',
        'measuredHeightImages',
        'measuredChangImages',
        'measuringThicknessImages'
      ];
      mediaFields.forEach(key => {
        if (data[key]) {
          if (typeof data[key] === 'string') {
            // 尝试解析为JSON，如果失败则当作普通URL处理
            try {
              const parsed = JSON.parse(data[key]);
              processedData[key] = Array.isArray(parsed) ? parsed : [{ url: data[key] }];
            } catch {
              processedData[key] = data[key].split(',').map(url => ({ url }));
            }
          } else if (Array.isArray(data[key])) {
            processedData[key] = data[key];
          } else {
            processedData[key] = [];
          }
        } else {
          processedData[key] = [];
        }
      });
      return processedData;
    },
    // 复制操作
    async handleCopy(row) {
      this.$confirm('确定复制该行数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const processedRow = this.processData(row); // 处理数据格式
        // 准备API参数
        let params = {
          ...processedRow,
          // 转换数组字段为逗号分隔的字符串
          images: processedRow.images.map(item => item.url).join(','),
          outerPackingPicture: processedRow.outerPackingPicture.map(item => item.url).join(','),
          physicalPicture: processedRow.physicalPicture.map(item => item.url).join(','),
          measuredWeightImages: processedRow.measuredWeightImages.map(item => item.url).join(','),
          measuredWidthImages: processedRow.measuredWidthImages.map(item => item.url).join(','),
          measuredHeightImages: processedRow.measuredHeightImages.map(item => item.url).join(','),
          measuredChangImages: processedRow.measuredChangImages.map(item => item.url).join(','),
          measuringThicknessImages: processedRow.measuringThicknessImages.map(item => item.url).join(','),
          zhankaiMeasuredHeightImages: processedRow.zhankaiMeasuredHeightImages.map(item => item.url).join(','),
          zhankaiMeasuredWidthImages: processedRow.zhankaiMeasuredWidthImages.map(item => item.url).join(','),
          zhankaiMeasuredChangImages: processedRow.zhankaiMeasuredChangImages.map(item => item.url).join(','),
          // 特殊处理字段
          goodsVido: processedRow.goodsVido.map(item => item.url).join(','),
          // 重置必要字段
          storageStatus: 1,
          id: null,
          goodsNumber: null,
          goodsCode: null,
          yhGoodsCode: null,
          recentLender: null,
          lastExitTime: null,
          deliveryTime: null,
          bitNumber: null,
          daysOverdue: null,
          actualReturnTime: null,
          estimatedServiceLife: null,
        };
        params.buyNo = null
        params.shootingTime = null
        params.numberShots = null
        params.goodsNumber = null
        const { data, success } = await addOrEditSampleRegistration(params);
        if (success) {
          this.$message.success('复制成功');
          this.getList();
        }
      });
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.searchloading == true
        const res = await getWarehouseLocationManagement({ currentPage: 1, pageSize: 50, warehouseBitCode: query })
        this.searchloading = false
        this.locationList = res?.data?.list
      }
      else {
        this.locationList = []
      }
    },
    downLoadFile() {
      downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250723/1947858024303927296.xlsx', '样品导入模板.xlsx');
    },
    async handleEdit(row) {
      this.interactiveData = JSON.parse(JSON.stringify(row))
      await this.onAcquireWarehouse()
      this.sampleRegistrationVisible = true
    },
    async changeTime(e) {
      this.ListInfo.lendTime = e ? e[0] : null
      this.ListInfo.lendEndTime = e ? e[1] : null
    },
    cancelFormMethod() {
      this.applicationVisible = false
      this.getList()
    },
    onSampleApply() {
      if (this.selectList.length == 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      const warehouseSet = new Set(this.selectList.map(item => item.warehouse));
      if (warehouseSet.size > 1) {
        this.$message.warning('请选择同一仓库的样品进行申请')
        return
      }
      this.interactiveData = {}
      this.applicationVisible = true
    },
    printCloseMethod() {
      this.goodsCodeList = []
      this.printVisible = false
    },
    getScrollbarWidth() {
      this.$nextTick(() => {
        if (this.$refs.refscrollbar && this.$refs.refscrollbar.$el) {
          this.scrollbarWidth = this.$refs.refscrollbar.$el.offsetWidth;
        } else {
          console.warn('Scrollbar ref not found.');
        }
      });
    },
    printQRCode(val) {
      if (this.goodsCodeList && this.goodsCodeList.length == 0) {
        this.$message.error('暂无可打印的二维码');
        return;
      }
      printJS({
        id: 'printid'
      })
      // Print({
      //   printable: 'printid',
      //   type: 'html',
      //   scanStyles: true,
      //   targetStyles: ['*'],
      //   onCancel: function () {
      //     console.log('');
      //   },
      //   onPrint: function () {
      //     console.log('');
      //   },
      // });
    },
    async generateQRCode() {
      if (!this.numQRCode || this.numQRCode <= 0) {
        this.$message.warning('请输入要打印的二维码数量')
        return
      }
      const { data } = await getBathSuiJiGoodsCode({ num: this.numQRCode })
      this.goodsCodeList = data
      this.onPrintMethod()
    },
    async onPrintMethod(val) {
      this.goodsCodeList.forEach((item, index) => {
        setTimeout(() => {
          JsBarcode(`#barcode1${item}`, item, {
            displayValue: this.switchshow,
            fontSize: 20,
            fontOptions: "bold",
            font: "Arial",
            textAlign: "center"
          })
        }, 0);
      })
      this.printLoading = false;
    },
    changePrintSize(e, val) {
      const [width, height] = e.split('x').map(Number);
      this.qrCodeWidth = width;
      this.qrCodeHeight = height;
      this.onPrintMethod(val)
    },
    printBarcode() {
      this.printVisible = true
      this.islinePrinting = false
      this.goodsCodeList = []
    },
    closingMethodClose() {
      this.sampleRegistrationVisible = false
      this.getList()
    },
    async onNewSample() {
      this.interactiveData = {}
      await this.onAcquireWarehouse()
      this.sampleRegistrationVisible = true
    },
    async onAcquireWarehouse() {
      const { data, success } = await getSampleWarehouse()
      if (success) {
        this.stashList = [...new Set(data)];
      }
    },
    async init() {
      var res = await getAllProBrand();
      this.brandlist = res.data.map(item => {
        return { value: item.key, label: item.value };
      });
      const res2 = await getDirectorGroupList({})
      this.directorGroupList = res2.data || []
      await this.onAcquireWarehouse()
      const { data, success } = await getList({ isEnabled: 1, currentPage: 1, pageSize: 1000000, brandId: [5] })
      if (success) {
        this.materialsList = data.list.filter(item => !item.styleCode.includes('合格证'))
      }
    },
    //上传文件
    onUploadRemove(file, fileList) {
      this.fileList = []
    },
    async onUploadChange(file, fileList) {
      this.fileList = fileList;
    },
    onUploadSuccess(response, file, fileList) {
      fileList.splice(fileList.indexOf(file), 1);
      this.fileList = [];
      this.dialogVisible = false;
    },
    async onUploadFile(item) {
      if (!item || !item.file || !item.file.size) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.uploadLoading = true
      const form = new FormData();
      form.append("upfile", item.file);
      var res = await importSampleRegistrationAsync(form);
      if (res?.success)
        this.$message({ message: "上传成功,正在导入中...", type: "success" });
      this.uploadLoading = false
      this.dialogVisible = false;
      await this.getList()
    },
    onSubmitUpload() {
      if (this.fileList.length == 0) {
        this.$message({ message: "请先上传文件", type: "warning" });
        return false;
      }
      this.$refs.upload.submit();
    },
    //导入弹窗
    startImport() {
      this.fileList = []
      this.dialogVisible = true;
    },
    async exportProps() {
      this.loading = true
      const res = await exportSampleRegistrationAsync(this.ListInfo)
      this.loading = false
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
      aLink.href = URL.createObjectURL(blob)
      aLink.setAttribute('download', '样品登记数据' + new Date().toLocaleString() + '.xlsx')
      aLink.click()
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      // if (this.timeRanges && this.timeRanges.length == 0) {
      //   //默认给近7天时间
      //   this.ListInfo.lendTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      //   this.ListInfo.lendEndTime = dayjs().format('YYYY-MM-DD')
      //   this.timeRanges = [this.ListInfo.lendTime, this.ListInfo.lendEndTime]
      // }
      this.loading = true
      const { data, success } = await getSampleRegistration(this.ListInfo)
      if (success) {
        const fieldsToProcess = [
          'images',
          'outerPackingPicture',
          'physicalPicture',
          'measuredWeightImages',
          'measuringThicknessImages',
          'measuredWidthImages',
          'measuredHeightImages',
          'measuredChangImages',
          'zhankaiMeasuredHeightImages',
          'zhankaiMeasuredWidthImages',
          'zhankaiMeasuredChangImages',
          'packageImage',
          'newPackageImage',
        ];
        const processVoucher = (voucher) => {
          if (voucher) {
            return JSON.stringify(voucher.split(',').map(url => ({ url })));
          }
          return voucher;
        };
        data.list.forEach(item => {
          fieldsToProcess.forEach(field => {
            item[field] = processVoucher(item[field]);
          });
        });
        this.tableData = data.list
        this.tableData.forEach(item => {
          if (item.storageStatus === 3) {
            item.deliveryTime = ''; // storageStatus 为 在库，deliveryTime 为空
          } else if (item.lastExitTime) {
            const now = dayjs(); // 获取当前时间
            const lastExit = dayjs(item.lastExitTime);
            const diffInDays = now.diff(lastExit, 'minute') / 1440; // 计算天数（分钟差除以1440）
            item.deliveryTime = diffInDays.toFixed(1) + '天'; // 保留一位小数
            item.lastExitTime = lastExit.format('YYYY-MM-DD HH:mm:ss');
          } else {
            item.deliveryTime = ''; // lastExitTime 为空时，deliveryTime 也为空
            item.lastExitTime = '';
          }
          item.estimatedServiceLife = item.estimatedServiceLife ? dayjs(item.estimatedServiceLife).format('YYYY-MM-DD HH:mm') : '';
        });
        this.total = data.total
        this.summaryarry = data.summary
        this.loading = false
        this.selectList = []
      } else {
        this.$message.error('获取列表失败')
      }
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;
  // flex-wrap: wrap;

  .publicCss {
    width: 130px;
    margin-right: 2px;
  }
}

#printid {
  page-break-inside: avoid;

  .qrcode-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    page-break-inside: avoid;
    // margin-top: 5px;
  }

  .qrcode-id {
    font-size: 9px;
    display: flex;
    justify-content: center;
  }
}

::v-deep(.el-button.top_button + .el-button.top_button) {
  margin-left: 1px;
}

::v-deep(.el-button.Inside_button + .el-button.Inside_button) {
  margin-left: 5px;
}
</style>
