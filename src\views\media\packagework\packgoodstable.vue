<template>
 <div :style="[{ height: (tableHandles != null && tableHandles.length>0 ? '95%' : '100%') }, { width: '100%' }, { 'margin': ' 0' }]">
     <el-button-group v-if="tableHandles.length>0">
         <template v-for='item in tableHandles'>
             <el-button v-if="(!item.permission || (item.permission && checkPermission(item.permission)))"
                 :key='item.label' :size="item.size || size" :type="item.type || type" :icon='item.icon || ""'
                 v-throttle="item.throttle || 1000" @click="item.handle(that)">{{ item.label }}</el-button>
         </template>
         <slot name="extentbtn" />
     </el-button-group>
     <vxe-toolbar ref="xToolbar" custom class="vxetoolbar20221212" v-if="toolbarshow">
           <template #buttons>
             <slot name="tbHeader" />
           </template>
     </vxe-toolbar>
     <vxe-table ref="xTable" border="default" height="100%" :show-footer="true" style="width: 100%;"
        class="vxetable202212161323 mytable-scrollbar20221212" resizable stripe :id="id" show-overflow
        :show-footer-overflow="'tooltip'" keep-source size="mini" :loading="loading" :data="tableData"
        :scroll-y="{ gt: 100 }" :scroll-x="{ gt: 100 }" :footer-method="footerMethod"
        @checkbox-change="selectChangeEvent"  @checkbox-all="checkboxall" :sort-config="{sortMethod: customSortMethod}"
          @cell-click="selectAllEvent"
        :row-config="{ isCurrent: true, isHover: true }">
     <!--  -->
     <!-- border show-overflow-->
 
         <vxe-column show-header-overflow v-if="hasSeq" type="seq" width="28" :fixed="isIndexFixed?'left':''" :align="'left'">
             <template v-if="isSelectLvl" #header>
                 <el-select v-model="currentLvl" placeholder="超小尺寸" size="mini" @change="lvlChang" style="width:50px;">
                     <el-option v-for="num in 9" :key="num" :value="num" :label="`+${num}`"></el-option>
                 </el-select>
             </template>
             <template #footer="{ items, _columnIndex }">
                 <span ></span>
             </template>
         </vxe-column>
 
 
 
         <slot name="left" ></slot>
 
         <template v-for="(col,colIndex) in tableCols">
                 <template v-if="!col.type&&!col.merge">
                     <vxe-column show-header-overflow :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                     :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                     :title-help="col.tipmesg?{message: col.tipmesg}:null"
                     :tree-node="!!col.treeNode? true:false"
                     :width="col.width"
                     :min-width="col.minwidth?col.minwidth:null"
                     :sortable="!!col.sortable"
                     :fixed="col.fixed?col.fixed:''"
                     :align="col.align?col.align:'center'"
                     >
                         <template #default="{ row }" v-if="col.formatter">
                             {{col.formatter? col.formatter(row): row[col.prop]}}
                         </template>
                         <template  #footer="{ items, _columnIndex }">
                             <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
                         </template>
                     </vxe-column>
 
                 </template>
 
                 <vxe-column show-header-overflow type="checkbox" width="60" v-else-if="col.type=='checkbox'"  fixed="left" :key="colIndex"></vxe-column>
 
                 <vxe-column show-header-overflow  v-else-if="col.type=='colslot'"
                 :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                 :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                 show-overflow  :tree-node="!!col.treeNode? true:false"
                 :width="col.width"
                 :title-help="col.tipmesg?{message: col.tipmesg}:null"
                 :min-width="col.minwidth?col.minwidth:null"
                 :sortable="!!col.sortable"
                 :fixed="col.fixed?col.fixed:''"
                 :align="col.align?col.align:'center'"
                 >
                     <template #default="{ row }">
                         <slot name="colslot" :col="row"></slot>
                     </template>
                 </vxe-column>
 
                 <vxe-column show-header-overflow  v-else-if="col.type=='images'"
                 :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                 :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                 show-overflow  :tree-node="!!col.treeNode? true:false"
                 :width="col.width"
                 :title-help="col.tipmesg?{message: col.tipmesg}:null"
                 :min-width="col.minwidth?col.minwidth:null"
                 :sortable="!!col.sortable"
                 :fixed="col.fixed?col.fixed:''"
                 :align="col.align?col.align:'center'"
                 >
                     <template #default="{ row }">
 
                         <template v-if=" row && !!row[col.prop] && row[col.prop].length>2">
 
                             <template v-if="row[col.prop][0]=='[' && JSON.parse(row[col.prop]).length>1">
                                 <el-badge
                                     class="badgeimage20221212"
                                     :value="JSON.parse(row[col.prop]).length" style="margin-top:0px;margin-right:40px;">
                                     <el-image  :src="(row[col.prop][0]=='['?(JSON.parse(row[col.prop])[0].url):row[col.prop] )"
                                     class="images20221212"
                                     :preview-src-list="(row[col.prop][0]=='['
                             ?(()=>{
                                 let tempArray=JSON.parse(row[col.prop]);
                                 let tempRltArr=[];
                                 tempArray.forEach(x=>tempRltArr.push(x.url));
                                 return tempRltArr
                             })()
                             :(()=>{
                                 return [row[col.prop]]
                             })()  )">
                                     </el-image>
                                 </el-badge>
                             </template>
                             <template v-else>
                                 <el-image  :src="(row[col.prop][0]=='['?(JSON.parse(row[col.prop])[0].url):row[col.prop] )"
 
                                 class="images20221212"
                                 :preview-src-list="(row[col.prop][0]=='['
                             ?(()=>{
                                 let tempArray=JSON.parse(row[col.prop]);
                                 let tempRltArr=[];
                                 tempArray.forEach(x=>tempRltArr.push(x.url));
                                 return tempRltArr
                             })()
                             :(()=>{
                                 return [row[col.prop]]
                             })()  )">
                                 </el-image>
                             </template>
                         </template>
 
                     </template>
                 </vxe-column>
 
                 <vxe-column show-header-overflow  v-else-if="col.type=='imagess'"
                 :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.field"
                 :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                 show-overflow  :tree-node="!!col.treeNode? true:false"
                 :width="col.width"
                 :title-help="col.tipmesg?{message: col.tipmesg}:null"
                 :min-width="col.minwidth?col.minwidth:null"
                 :sortable="!!col.sortable"
                 :fixed="col.fixed?col.fixed:''"
                 :align="col.align?col.align:'center'"
                 >
                     <template #default="{ row }">
                         <template v-if="col.type==='imagess'">
                             <template>
                                 <el-badge :value="row[col.prop].length" style="margin-top:10px;margin-right:40px;">
                                     <el-image  class="imgstyle" :src="row[col.prop][0]" fit="fill" :preview-src-list="row[col.prop]">
                                     </el-image>
                                 </el-badge>
                             </template>
                         </template>
 
                     </template>
                 </vxe-column>
 
                 <template v-else-if="col.merge" :width="col.width" >
                     <vxe-colgroup :title="col.label" show-header-overflow :key="col.label" :field="col.label">
                         <vxe-column show-header-overflow :field="coll.prop? coll.prop : ('col'+ colIndex)"
                         :title="coll.label?coll.label:((coll.type && coll.type=='color' || coll.type=='split')?'|':'')"
                         show-overflow :tree-node="!!coll.treeNode? true:false"
                         :width="coll.width"
                         :title-help="coll.tipmesg?{message: coll.tipmesg}:null"
                         v-for="(coll, colindex) in col.cols" :key="colindex"
                         :min-width="coll.minwidth?coll.minwidth:null"
                         :sortable="!!coll.sortable"
                         :fixed="coll.fixed?coll.fixed:''"
                         :align="coll.align?coll.align:'center'"
                         >
                             <template #default="{ row }" v-if="coll.formatter">
                                 {{coll.formatter? coll.formatter(row): row[coll.prop]}}
                             </template>
                             <template #default="scope">
                             <span v-if="coll.type=='color' || coll.type=='split'" >
                                 |
                             </span>
                             <template  v-if="coll.type==='button'">
                                 <template v-for="(btn,btnIndex) in coll.btnList" >
                                     <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                     v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                                     :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                     :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                         {{btn.label}}
                                     </el-link>
                                 </template>
                             </template>
                             <el-button v-else-if="coll.type=='click'"  type="text" @click="coll.handle&&coll.handle(that,scope.row,coll,scope.row[coll.prop])">
                                 {{(coll.formatter && coll.formatter(scope.row)) || scope.row[coll.prop]}}
                             </el-button>
                             <div v-else-if="coll.type=='html'"  v-html="coll.formatter? coll.formatter(scope.row): scope.row[coll.prop]  "></div>
                             <el-switch v-else-if="coll.type=='switch'" v-model="scope.row[coll.prop]" @change='coll.change && coll.change(scope.row,that)'></el-switch>
 
                             <span v-if="coll.type=='custom'||!coll.type" :style="coll.itemStyle && coll.itemStyle(scope.row)" :size="size || btn.size" :class="coll.itemClass && coll.column.itemClass(scope.row)">
                             {{ (()=>{
                                 if(coll.formatter)
                                     return coll.formatter(scope.row);
                                 else
                                     return scope.row[coll.prop];
                             })() }}</span>
                             <el-progress v-if="coll.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[coll.prop])" :status="scope.row[coll.prop]==100?'success':null"></el-progress>
                         </template>
                         <template  #footer="{ items, _columnIndex }">
                             <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
                         </template>
                         </vxe-column>
                     </vxe-colgroup>
                 </template>
 
 
                 <vxe-column  v-else
                 show-header-overflow
                 :field="col.prop? col.prop : ('col'+ colIndex)"  :key="col.prop"
                 :title="col.label?col.label:((col.type && col.type=='color' || col.type=='split')?'|':'')"
                 show-overflow  :tree-node="!!col.treeNode? true:false"
                 :width="col.width"
                 :title-help="col.tipmesg?{message: col.tipmesg}:null"
                 :min-width="col.minwidth?col.minwidth:null"
                 :sortable="!!col.sortable"
                 :fixed="col.fixed?col.fixed:''"
                 :align="(col.type && (col.type=='color' || col.type=='split' || col.type=='images' || col.type=='image'))?'center':  (col.align?col.align:'left')"
                 >
                     <template #default="scope">
                         <span v-if="col.type=='color' || col.type=='split'" >
                             |
                         </span>
                        <template v-if="col.type==='newstar'">
                         <i v-if="scope.row.star==null"></i>
                         <i v-else-if="scope.row.star==0"></i>
                         <i v-else-if="scope.row.star==1" class="el-icon-star-on" style="color:red"></i>
                         <i v-else-if="scope.row.star==2" class="el-icon-star-on" style="color:orange"></i>
                         <i v-else-if="scope.row.star==3" class="el-icon-star-on" style="color:yellow"></i>
                         <i v-else-if="scope.row.star==4" class="el-icon-star-on" style="color:green"></i>
                         <i v-else-if="scope.row.star==5" class="el-icon-star-on" style="color:blue"></i>
                         <i v-else-if="scope.row.star==6" class="el-icon-star-on" style="color:indigo"></i>
                         <i v-else-if="scope.row.star==7" class="el-icon-star-on" style="color:purple"></i>
                         <i v-else style="color:gray" class="el-icon-star-on"></i>
                        </template>
                        <template v-if="col.type==='flag'">
                         <i v-if="scope.row.flag==null"></i>
                         <i v-else-if="scope.row.flag==0"></i>
                         <i v-else-if="scope.row.flag==1" class="el-icon-s-flag
                         " style="color:red"></i>
                         <i v-else-if="scope.row.flag==2" class="el-icon-s-flag
                         " style="color:orange"></i>
                         <i v-else-if="scope.row.flag==3" class="el-icon-s-flag
                         " style="color:yellow"></i>
                         <i v-else-if="scope.row.flag==4" class="el-icon-s-flag
                         " style="color:green"></i>
                         <i v-else-if="scope.row.flag==5" class="el-icon-s-flag
                         " style="color:blue"></i>
                         <i v-else-if="scope.row.flag==6" class="el-icon-s-flag
                         " style="color:indigo"></i>
                         <i v-else-if="scope.row.flag==7" class="el-icon-s-flag
                         " style="color:purple"></i>
                         <i v-else style="color:gray" class="el-icon-s-flag
                         "></i>
                        </template>
 
                        <template v-else-if="col.type==='copy'">
                             <div class="relativebox">
                                 <el-tooltip effect="dark" :content="scope.row[col.prop]" placement="top-start">
                                 <div class="textover" style="width: 80%;">{{ scope.row[col.prop] }}</div>
                                 </el-tooltip>
 
                                 <div class="copyhover" @click="copytext(scope.row[col.prop])">
                                     <i class="el-icon-document-copy"></i>
                                 </div>
                             </div>
                         </template>
                         <template v-else-if="col.type==='echarts'">
                             <!--
                                 1、行图表目前比较单一
                                 2、行图表里col定义prop是用来指定排序的字段名，chartProp是用来指定图表数据的字段名
                                 3、chartProp字段用后台类：Row7DayEchartsDtoSingle来进行输出
                              -->
                             <div  v-loading="echartsLoading" style="height: 40px;width:100%;" >
                                 <div style="height: 40px;width:100%;"
                                 :id="'rptIdecharts'+id+'_'+scope.columnIndex+'_'+scope.rowIndex"
                                 :ref="'rptIdecharts'+id+'_'+scope.columnIndex+'_'+scope.rowIndex"
                                 v-bind="{chartData:JSON.stringify(scope.row[col.chartProp])}"
                                 ></div>
                             </div>
                         </template>
 
                         <template  v-if="col.type==='button'">
                             <template v-for="(btn,btnIndex) in col.btnList" >
                                 <el-link :key="btn.label" :style="''+ (btnIndex>0?'margin-left:5px;':'')"
                                 v-if="(!btn.permission||(btn.permission&&checkPermission(btn.permission)))&&!(btn.hasOwnProperty('ishide')&&((typeof btn.ishide=='function'&& btn.ishide(that,scope.row)==true)||btn.ishide==true))"
                                 :disabled="(btn.hasOwnProperty('display')&&((typeof btn.display=='function'&& btn.display(scope.row)==true)||btn.display==true))"
                                 :type="((!!btn.type)? btn.type: 'primary')" :size="btn.size || size " :icon="btn.icon" @click="btn.handle(that,scope.row)">
                                     {{btn.label}}
                                     <span v-if="btn.htmlformatter" v-html="btn.htmlformatter(scope.row)"></span>
                                 </el-link>
                             </template>
                         </template>
                         <span v-if="col.type==='clickLink'" :style="col.style==null?'color:blue;cursor:pointer;':typeof(col.style)=='function'?col.style(that,scope.row,col,scope.row[col.prop]):column.style" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">{{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}</span>
                         <el-button v-else-if="col.type=='click'"  type="text" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                             {{(col.formatter && col.formatter(scope.row)) || scope.row[col.prop]}}
                         </el-button>
                         <div v-else-if="col.type=='ellips'"  type="text" @click="col.handle&&col.handle(that,scope.row,col,scope.row[col.prop])">
                             <i class="vxe-icon-ellipsis-h"></i>
                         </div>
                         <div v-else-if="col.type=='html'"  v-html="col.formatter? col.formatter(scope.row): scope.row[col.prop]  "></div>
                         <el-switch v-else-if="col.type=='switch'" v-model="scope.row[col.prop]" @change='col.change && col.change(scope.row,that)'></el-switch>
                         <div class="wendang" v-else-if="col.type==='editor'" v-html="scope.row[col.prop]" @click="showImg($event)">{{scope.row[col.prop]}}</div>
                         <span v-if="col.type=='custom'||!col.type" :style="col.itemStyle && col.itemStyle(scope.row)" :size="size || btn.size" :class="col.itemClass && col.column.itemClass(scope.row)">
                         {{ (()=>{
                             if(col.formatter)
                                 return col.formatter(scope.row);
                             else
                                 return scope.row[col.prop];
                         })() }}</span>
                         <el-progress v-if="col.type==='progress'" :text-inside="true" :stroke-width="20" :percentage="Number(scope.row[col.prop])" :status="scope.row[col.prop]==100?'success':null"></el-progress>
                     </template>
                     <template  #footer="{ items, _columnIndex }">
                         <span :style="col.summaryEvent?'color: red;cursor:pointer;':'' ">{{ items[_columnIndex] }}</span>
                     </template>
                 </vxe-column>
 
         </template>
 
 
         <slot name="right" ></slot>
 
     </vxe-table>
 
 </div>
 </template>
 
 <script>
     import * as echarts from 'echarts'
 
     import { getTableColumnCache, setTableColumnCache } from '@/api/admin/business'
 
     export default {
         name:"vxetablebase",
         props: {
             dftLvl:{type:Number,default:()=>{return 1;}},
             isSelectLvl:{ type: Boolean, default: () => { return false } },
             editconfig:{ type: Object, default: () => { return {  } } },
             treeProp: { type: Object, default: () => { return { rowField: 'id', parentField: 'parentId' } } },
             hasSeq: { type: Boolean, default: () => { return true } },
             // 表格数据
             tableData: { type: Array, default: () => [] },
             // 表格型号：mini,medium,small
             size: { type: String, default: 'small' },
             type: { type: String, default: 'primary' },
             isBorder: { type: Boolean, default: true },
             // 表格列配置
             tableCols: { type: Array, default: () => [] },
             isRemoteSort:{ type: Boolean, default: () => { return true } },
             id:{type:String,default:()=>{ return new Date().valueOf().toString()}},
             that:{type:Object,default:()=>{return null}},
             border:{type:Boolean | Object ,default:()=>{return 'default'}},
             tableHandles: { type: Array, default: () => [] },
             showsummary: { type: Boolean, default: false },
             align: { type: String, default: 'center' }, //对齐方式
             summaryarry: { type: Object, default: () => { } },
             tablekey: { type: String, default: '' },//表格key
             isstorage:  { type: Boolean, default: true }, //true为本地缓存，false为后端缓存
             enabled:  { type: Boolean, default: true },
             height: { type: String, default: '100%' },
             ygt: { type: Number, default: 100 },
             xgt: { type: Number, default: 100 },
             somerow: { type: String, default: '' },
             toolbarshow: { type: Boolean, default: () => { return true } },
             loading: { type: Boolean, default: () => { return false } },
             showheaderoverflow: { type: String, default: '' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
             showoverflow: { type: String, default: 'title' },//ellipsis（只显示省略号）,title（并且显示为原生 title）,tooltip（并且显示为 tooltip 提示）
             isDisableCheckBox:{ type: Boolean, default: () => { return false } },
             isIndexFixed:{type: Boolean, default: () => { return true } },
             enableCheckRange:{type:Boolean,default:()=>{return true}}
         },
         data() {
             return {
                 currentLvl:1,
                 lastSortArgs:{
                     field:"",
                     order:"",
                 },
                 summarycolumns: [],
                 checkBoxGroup: [],
                 echartsLoading:false,
                 echarts:echarts
             }
         },
         created(){
             this.$nextTick(() => {
               // 手动将表格和工具栏进行关联
               this.$refs.xTable.connect(this.$refs.xToolbar)
             })
         },
         async mounted(){
                 let _this = this;
                 this.currentLvl=this.dftLvl;
                 this.$nextTick(()=>{
                     this.columns = this.$refs.xTable.getColumns()
                 })
                 var arrlist = [];
                 this.tableCols.map((item)=>{
                     if(item.fixed){
                         arrlist.push(item.prop)
                     }
                 })
                 this.arrlist = arrlist;
 
                 // 获取数据
                 if(!this.isstorage){
                     let key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                     let res = await getTableColumnCache({ key: key })
                     await this.changecolumn(res.data.hideContent);
                 }
 
                 //window.addEventListener('resize', this.loadRowEcharts);
 
         },
         methods:{
             // 图片点击放大
             showImg(e) {
                 if (e.target.tagName == 'IMG') {
                     this.$emit('showImg', e)
                 }
             },
             toggleTreeMethod   ({ expanded, column, columnIndex, row, rowIndex }) {
               if (!expanded) return;
               return new Promise(resolve => {
                 this.$emit('toggleTreeMethod',row)
                 return true;
               })
             },
             copytext(e) {
                 let textarea = document.createElement("textarea")
                 textarea.value = e
                 textarea.readOnly = "readOnly"
                 document.body.appendChild(textarea)
                 textarea.select()
                 let result = document.execCommand("copy")
                 if (result) {
                     this.$message({
                     message: '复制成功',
                     type: 'success'
                     })
                 }
                 textarea.remove()
             },
             //层级切换
             lvlChang(v1){
                 if(v1==9){
                     this.$refs.xTable.setAllTreeExpand(true);
                 }
                 else if(v1==1){
                     this.$refs.xTable.setAllTreeExpand(false)
                 }
                 else{
                     let rows=this.$refs.xTable.getTableData().fullData;
                     this.lvlShow(rows,1,v1);
                 }
             },
             lvlShow(rows,curI,maxLvl){
                 if(!rows || rows==undefined || rows==null)
                     return;
 
                 if(curI>=maxLvl){
                     rows.forEach(r=>{
                         this.$refs.xTable.setTreeExpand(r,false)
                     })
                     return;
                 }else{
                     rows.forEach(r=>{
                         this.$refs.xTable.setTreeExpand(r,true);
                         this.lvlShow(r.children,curI+1,maxLvl);
                     });
                 }
 
             },
             // 通用行合并函数（将相同多列数据合并为一行）
             mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
               const fields = [this.somerow]
               const cellValue = row[column.property]
               if (cellValue && fields.includes(column.property)) {
                 const prevRow = visibleData[_rowIndex - 1]
                 let nextRow = visibleData[_rowIndex + 1]
                 if (prevRow && prevRow[column.property] === cellValue) {
                   return { rowspan: 0, colspan: 0 }
                 } else {
                   let countRowspan = 1
                   while (nextRow && nextRow[column.property] === cellValue) {
                     nextRow = visibleData[++countRowspan + _rowIndex]
                   }
                   if (countRowspan > 1) {
                     return { rowspan: countRowspan, colspan: 1 }
                   }
                 }
               }
             },
             changecolumn(val){
                 setTimeout(() => {
                 this.columns.forEach(column => {
                     if(val!=null){
                         if (val.includes(column.property)) {
                             column.visible = false
                         }
                     }
                 })
                 if (this.$refs.xTable) {
                     this.$refs.xTable.refreshColumn()
                 }
                 }, 800)
             },
             changecolumn_setTrue(val){
                 setTimeout(() => {
                 this.columns.forEach(column => {
                     if(val!=null){
                         if (val.includes(column.property)) {
                             column.visible = true
                         }
                     }
                 })
                 if (this.$refs.xTable) {
                     this.$refs.xTable.refreshColumn()
                 }
                 }, 800)
             },
             async tooclick(params){
                 console.log(params)
                 //行数据
                 const visibleColumn = this.$refs.xTable.getColumns()
                 const Columnevn = this.$refs.xTable;
 
                 switch (params.type) {
                     case 'confirm': {
                         var checked = [];
                         var nochecked = [];
                         var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                         visibleColumn.forEach(item=>{
                             if(item?.property)
                             checked.push(item.property)
                         })
 
                         var arrList = Columnevn.tableFullColumn;
                         var newList = visibleColumn;
                         arrList = arrList.filter((item) => {
                             return newList.every((item2) => {
                                 return item.property != item2.property;
                             });
                         });
                         arrList.forEach(item=>{
                             nochecked.push(item.property)
                         })
                         if(!this.isstorage)
                             await setTableColumnCache({ key: key, displays: checked, hides: nochecked });
 
                         //获取label未选中的值
                         // setTimeout(async() => {
                         //     var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                         //     let res = await getTableColumnCache({ key: key })
                         //     console.log("返回的数据",res)
                         // }, 200);
                         break
                     }
                     case 'reset': {
                         var checked = [];
                         var nochecked = [];
                         var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                         visibleColumn.forEach(item=>{
                             if(item?.property)
                             checked.push(item.property)
                         })
                         if(!this.isstorage)
                             await setTableColumnCache({ key: key, displays: checked, hides: [] });
 
                         break
                     }
                     case 'close': {
                         var checked = [];
                         var nochecked = [];
                         var key = window.location.origin + window.location.pathname + this.tablekey + 'v1';
                         visibleColumn.forEach(item=>{
                             if(item?.property)
                             checked.push(item.property)
                         })
 
                         var arrList = Columnevn.tableFullColumn;
                         var newList = visibleColumn;
                         arrList = arrList.filter((item) => {
                             return newList.every((item2) => {
                                 return item.property != item2.property;
                             });
                         });
                         arrList.forEach(item=>{
                             nochecked.push(item.property)
                         })
                         if(!this.isstorage)
                             await setTableColumnCache({ key: key, displays: checked, hides: nochecked });
 
                         // VXETable.modal.message({ content: `关闭了面板，显示为 ${visibleColumn.length} 列`, status: 'info' })
                         break
                     }
                     case 'open': {
                         break
                     }
                 }
 
                 if(params.type && params.type!='open')
                     this.loadRowEcharts();
             },
             // 清空全选
             clearSelection(){
                 this.$refs.xTable.clearCheckboxRow()
             },
             async checkboxall(){
                 const records = this.$refs.xTable.getCheckboxRecords()
                 this.$emit('checkbox-range-end', records);
             },
             async toggleRowSelection(val){
                 await this.$refs.xTable.clearCheckboxRow()
                 await this.$refs.xTable.setCheckboxRow(val, true)
                 console.log("组件内数据",val)
             },
             selectAllEvent ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {
               const records = this.$refs.xTable.getCheckboxRecords()
               this.$emit('checkbox-range-end', records);
               this.$emit("cellClick",{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });
             //   console.log(checked ? '所有勾选事件' : '所有取消事件', records)
             },
             cellClickEvent({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }){
                 this.$emit("cellClick",{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event });
 
             },
             selectChangeEvent ({ checked }) {
               const records = this.$refs.xTable.getCheckboxRecords()
             //   console.log(checked ? '勾选事件' : '取消事件', records)
               this.$emit('select', records);
             },
             cellStyleFun({ row, rowIndex, column }){
                 let rltStyle={};
                 let col=column;
                 var colArg=this.tableCols.find(x=>x.prop==col.property );
 
                 if(colArg ){
                     if(colArg.type && (colArg.type=="images" ||colArg.type=="image"))
                         rltStyle={
                             ...rltStyle,
                             ...{
                                 textAlign:"center"
                             }
                         };
 
                     if(colArg.align)
                         rltStyle={
                             ...rltStyle,
                             ...{
                                 textAlign:colArg.align
                             }
                         };
 
                 }
                 return rltStyle;
             },
             customSortMethod({ data, sortList }){
                 if(this.isRemoteSort){
                     if(sortList && sortList.length>0){
                         if(sortList[0].field != this.lastSortArgs.field || sortList[0].order!=this.lastSortArgs.order){
                             this.lastSortArgs={...sortList[0]};
                             this.$emit('sortchange',{
                                 order:(this.lastSortArgs.order.indexOf('desc')>-1?'descending':'asc'),
                                 prop:this.lastSortArgs.field
                             });
                         }
                     }
                 }else{
                     this.$refs.xTable.sort(sortList[0].field, sortList[0].order)
                 }
             },
             headerCellClassName ({ column, columnIndex }) {
                 let className='';
                 var col=this.tableCols.find(x=>x.prop==column.property );
 
                 if (col && col.align ) {
                     className=' '+`vxetableheadercell-${column.align}-20221216`;
                 }else if(col && col.type && (col.type=="images" || col.type=="image")){
                     className=' '+`vxetableheadercell-center-20221216`;
                 }
 
                 return className;
             },
             footerMethod ({ columns, data }) {
                 const sums = [];
                 if (!this.summaryarry)
                     return sums
                 var arr = Object.keys(this.summaryarry);
                 if (arr.length == 0)
                     return sums
                 //const { columns, data } = param;
                 var hashj = false;
                 columns.forEach((column, index) => {
                     if (this.summaryarry.hasOwnProperty(column.property + '_sum')) {
                         var sum = this.summaryarry[column.property + '_sum'];
                         if (sum == null) return;
                         else if ((typeof sum == 'string') && sum.constructor == String) sums[index] = sum;
                         else if (Math.abs(parseInt(sum)) < 100) sums[index] = sum.toFixed(2)
                         else sums[index] = sum.toFixed(0)
                     }
                     else if (index == 0) sums[index] = '合计'
                     else sums[index] = ''
                 });
                 if (this.summarycolumns.length == 0) {
                     this.summarycolumns = columns;
                     //this.initsummaryEvent();
                 }
                 return [sums]
             },
             initsummaryEvent() {
                 let self = this;
                 let table;
                 this.$nextTick(() => {
                     if(this.tablekey) table = document.querySelector('[name='+this.tablekey+'] .vxe-table--footer-wrapper>table');
                     else table = document.querySelectorAll('.vxe-table--footer-wrapper>table');
                     if(table?.length>0) table = table[0]
                     this.$nextTick(() => {
                         self.summarycolumns.forEach((column, index) => {
                             if (column.property) {
                                 var col = findcol(self.tableCols, column.property);
                                 if (col && col.summaryEvent) {
                                     table.rows[0].cells[index].style.color = "red";
                                     table.rows[0].cells[index].style.cursor= "pointer";
                                 }
                             }
                         })
                     })
 
                 })
 
                 function findcol(cols, property) {
                     let column;
                     for (var i = 0; i < cols.length; i++) {
                         var c = cols[i];
                         if (column) break
                         else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                             column = c;
                             break
                         }
                         else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                     }
                     return column
                 }
             },
             footercellclick({ items, $rowIndex, column, columnIndex, $columnIndex, $event }){
                 let self = this;
                 var  col = findcol(self.tableCols, column.property);
                 if (col && col.summaryEvent)
                     self.$emit('summaryClick', column.property)
 
                 function findcol(cols, property) {
                     let column;
                     for (var i = 0; i < cols.length; i++) {
                         var c = cols[i];
                         if (column) break
                         else if (c.prop && c.prop.toLowerCase() == property.toLowerCase()) {
                             column = c;
                             break
                         }
                         else if (c.cols && c.cols.length > 0) column = findcol(c.cols, property)
                     }
                     return column
                 }
             },
             checCheckboxkMethod2({ row }) {
                 if(this.isDisableCheckBox){
                     let result;
                     this.$emit('checCheckboxkMethod', {row}, val=>{result = val})
                     return result;
                 }else {
                     return true;
                 }
             },
             /* 加载行图表 */
             loadRowEcharts(){
                 let self=this;
                 let echarts=self.echarts;
                 self.echartsLoading = true;
                 setTimeout(_ => {
 
                     let idPrex='rptIdecharts'+self.id;
                     let ids=[];
                     for(let p in self.$refs){
                         if(p.startsWith(idPrex))
                             ids.push(p);
                     }
 
                     ids.forEach(e => {
                         var idNode=self.$refs[e];
 
                         if(!idNode || idNode.length==0 || !idNode[0].id ||  !(idNode[0]?.attributes?.chartdata?.value))
                         {
                             return;
                         }
 
 
                         //检测是否已经存在echarts实例，如果不存在，则不再去初始化
                         // var myEchart = echarts.init(document.getElementById('main'));
                         let el=document.getElementById(idNode[0].id);
 
                         let myChart = echarts.getInstanceByDom(el);
                         if (!myChart) {
                             myChart = echarts.init(el);
                         }
                         myChart.clear();
 
                         let dataStr=idNode[0].attributes.chartdata.value;
                         if(dataStr==undefined|| dataStr==null|| dataStr==""){
                             return;
                         }
 
 
                         let rowChartData=JSON.parse(dataStr);
                         let series = [];
                         if(rowChartData==undefined|| rowChartData==null){
                             return;
                         }
 
                         rowChartData.series.forEach(s => {
                             series.push({ type: 'line', smooth: true, showSymbol: false, ...s })
                         });
 
                         var xAxis = { ...rowChartData.xAxis };
                         xAxis.type = "category";
                         //xAxis.boundaryGap=false;
                         xAxis.show = false;
                         xAxis.boundaryGap = false;
 
                         myChart.setOption({
                             legend: {
                                 show: false,
                             },
                             grid: {
                                 left: "0",
                                 top: "1",
                                 right: "6",
                                 bottom: "1",
                                 containLabel: false,
                             },
                             xAxis: xAxis,
                             yAxis: {
                                 type: 'value',
                                 show: false,
                             },
                             series: series
                         });
 
                         let width =el.clientWidth;
                         let height= el.clientHeight;
 
                         myChart.resize({width,height});
 
                     });
 
                     self.echartsLoading = false
                 }, 1000)
             },
             resizableChange({ $rowIndex, column, columnIndex, $columnIndex, $event }){
                 this.loadRowEcharts();
             }
         },
     }
 </script>
 
 <style lang="scss" scoped>
 
         /*滚动条整体部分*/
         .mytable-scrollbar20221212 ::-webkit-scrollbar {
           width: 10px;
           height: 10px;
         }
         /*滚动条的轨道*/
         .mytable-scrollbar20221212 ::-webkit-scrollbar-track {
           background-color: #FFFFFF;
         }
         /*滚动条里面的小方块，能向上向下移动*/
         .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb {
           background-color: #bfbfbf;
           border-radius: 5px;
           border: 1px solid #F1F1F1;
           box-shadow: inset 0 0 6px rgba(0,0,0,.3);
         }
        .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:hover {
           background-color: #A8A8A8;
         }
         .mytable-scrollbar20221212 ::-webkit-scrollbar-thumb:active {
           background-color: #787878;
         }
         /*边角，即两个滚动条的交汇处*/
        .mytable-scrollbar20221212 ::-webkit-scrollbar-corner {
           background-color: #FFFFFF;
         }
 
         // 图片大小
         .mytable-scrollbar20221212  .images20221212{
           max-width: 150px;max-height: 150px;
           width:40px !important;
           height:40px  !important;
         }
 
         // 图片张数标记
         .mytable-scrollbar20221212 .badgeimage20221212 .el-badge__content.is-fixed{
             top:10px;
         }
 
         /*  工具箱位置  */
         .vxetoolbar20221212{
             position:absolute ;
             top: 30px;
             right: 0px;
             padding-top:0;
             padding-bottom:0;
             z-index: 999;
             background-color: rgb(255 255 255 / 0%);
         }
 
         .vxetoolbar20221212 ::v-deep .vxe-custom--wrapper{
             margin-left:0px !important;
         }
 
         .vxetableheadercell-left-20221216
         {
             text-align: left;
         }
 
         .vxetableheadercell-center-20221216
         {
             text-align: center;
         }
 
         .vxetableheadercell-right-20221216
         {
             text-align: right;
         }
 
         ::v-deep .vxe-table .vxe-cell--sort{
             width: 10px !important;
         }
         ::v-deep .vxe-table--render-default .vxe-cell{
             padding-right: 0 !important;
             padding-left: 0 !important;
         }
 
         .copyhover{
         display: none;
         }
         .relativebox{
         width: 80%;
         -webkit-line-clamp: 1;
         overflow: hidden;
         text-overflow: ellipsis;
         -webkit-box-orient: vertical;
         }
         .relativebox:hover{
         width: 80%;
         }
         .textover{
         -webkit-line-clamp: 1;
         overflow: hidden;
         text-overflow: ellipsis;
         -webkit-box-orient: vertical;
         }
         .relativebox:hover .textover{
         -webkit-line-clamp: 1;
         overflow: hidden;
         text-overflow: ellipsis;
         -webkit-box-orient: vertical;
         }
 
         .relativebox:hover .copyhover{
         display: block;
         position: absolute;
         top: 50%;
         left: 75%;
         margin: 0 10px;
         z-index: 99;
         transform: translate(-50%,-50%);
         color: #409EFF;
         font-weight: 600;
         }
 
         .vxe-icon-ellipsis-h:hover {
             color: #409EFF;
             margin-left: 2px;
             background-color: #F1F1F1;
         }
 
         .vxe-icon-ellipsis-h {
             color: #999;
             font-size: 15px;
         }
 
 </style>
 