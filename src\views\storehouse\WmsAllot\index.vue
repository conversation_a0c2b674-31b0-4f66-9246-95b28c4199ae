<template>
    <MyContainer>
        <el-tabs v-model="activeName" style="height: 95%">
            <el-tab-pane label="调拨记录" name="first" style="height: 100%" lazy>
                <recordsOfTransfers />
            </el-tab-pane>
            <el-tab-pane label="配件设置" name="second" style="height: 100%" lazy>
                <accessorySettings />
            </el-tab-pane>
        </el-tabs>
    </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import recordsOfTransfers from "./component/recordsOfTransfers.vue";
import accessorySettings from "./component/accessorySettings.vue";
export default {
    components: {
        MyContainer, recordsOfTransfers,accessorySettings
    },
    data() {
        return {
            activeName: 'first',
        };
    },
    mounted() {

    },
    methods: {},
};
</script>

<style lang="scss" scoped></style>
