<template>
  <my-container v-loading="pageLoading">
    <el-tabs v-model="activeName" style="height: 95%">
      <el-tab-pane label="拼多多日报" name="first1" style="height: 100%">
        <productReportPdd ref="productReportPdd" style="height: 100%"></productReportPdd>
      </el-tab-pane>
      <el-tab-pane label="订单日报" name="first2" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddOrderDayReport')">
        <PddOrderDayReport @ChangeActiveName2="ChangeActiveName2" ref="PddOrderDayReport" style="height: 100%">
        </PddOrderDayReport>
      </el-tab-pane>
      <el-tab-pane label="编码日报" name="first3" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddGoodCodeDayReport')">
        <PddGoodCodeDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="PddGoodCodeDayReport" style="height: 100%"></PddGoodCodeDayReport>
      </el-tab-pane>
      <el-tab-pane label="ID日报" name="first4" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddIdDayReport')">
        <PddIdDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName" ref="PddIdDayReport"
          style="height: 100%"></PddIdDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺日报" name="first5" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddShopDayReport')">
        <PddShopDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="PddShopDayReport" style="height: 100%"></PddShopDayReport>
      </el-tab-pane>
      <el-tab-pane label="店铺SKU日报" name="first10" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddCommodityDayReport')">
        <PddCommodityDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="PddCommodityDayReport" style="height: 100%"></PddCommodityDayReport>
      </el-tab-pane>
      <el-tab-pane label="明细日报" name="first6" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddDetailDayReport')">
        <PddDetailDayReport @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="PddDetailDayReport" style="height: 100%"></PddDetailDayReport>
      </el-tab-pane>
      <el-tab-pane label="出仓负利润ID订单明细" name="first11" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddOutgoingprofitIDorderdetail')">
        <PddOutgoingprofitIDorderdetail @ChangeActiveName2="ChangeActiveName2" @ChangeActiveName="ChangeActiveName"
          ref="PddOutgoingprofitIDorderdetail" style="height: 100%"></PddOutgoingprofitIDorderdetail>
      </el-tab-pane>
      <el-tab-pane label="编码利润" name="first20" :lazy="true" style="height: 100%">
        <productReportPddGoods ref="productReportPddGoods" style="height: 100%"></productReportPddGoods>
      </el-tab-pane>
      <el-tab-pane
        v-if="checkPermission('api:OperateManage:DistributionGoodsOnline:GetDistributionGoodsOnlineRecordAsync')"
        label="铺货记录" name="first7" :lazy="true" style="height: 100%">
        <productReportPddOnlineList ref="productReportPddOnlineList" style="height: 100%"></productReportPddOnlineList>
      </el-tab-pane>
      <el-tab-pane
        v-if="checkPermission('api:OperateManage:DistributionGoodsOnline:GetDistributionGoodsOnlineRecordAsync')"
        label="铺货后续操作" name="first8" :lazy="true" style="height: 100%">
        <productReportPddOnlineOperateList ref="productReportPddOnlineList" style="height: 100%">
        </productReportPddOnlineOperateList>
      </el-tab-pane>
      <el-tab-pane
        v-if="checkPermission('api:BookKeeper:DistributionGoodsReport:GetDistributionGoodsOnlineReportAsync')"
        label="铺货利润报表" name="first9" :lazy="true" style="height: 100%">
        <productReportPddOnlineReport ref="productReportPddOnlineList" style="height: 100%">
        </productReportPddOnlineReport>
      </el-tab-pane>
      <el-tab-pane label="SKUS日报" name="first12" :lazy="true" style="height: 100%"
        v-if="checkPermission('PddOrderDayReport')">
        <PddSkusDayReport @ChangeActiveName2="ChangeActiveName2" ref="PddSkusDayReport" style="height: 100%">
        </PddSkusDayReport>
      </el-tab-pane>
    </el-tabs>
  </my-container>
</template>

<script>
import MyContainer from "@/components/my-container";
import productReportPdd from "./productReportPdd.vue";
import PddOrderDayReport from "./PddOrderDayReport.vue";
import PddSkusDayReport from "./PddSkusDayReport.vue";
import productReportPddOnlineList from "./productReportPddOnlineList.vue";
import productReportPddOnlineReport from "./productReportPddOnlineReport.vue";
import productReportPddOnlineOperateList from "./productReportPddOnlineOperateList.vue";
import PddGoodCodeDayReport from "./PddGoodCodeDayReport.vue";
import PddIdDayReport from "./PddIdDayReport.vue";
import PddCommodityDayReport from "./PddCommodityDayReport.vue";
import PddOutgoingprofitIDorderdetail from "./PddOutgoingprofitIDorderdetail.vue";
import PddShopDayReport from "./PddShopDayReport.vue";
import PddDetailDayReport from "./PddDetailDayReport.vue";
import productReportPddGoods from "./productReportPddGoods.vue";
import middlevue from "@/store/middle.js"

export default {
  name: "StoreStockTakingIndex",
  components: {
    MyContainer, productReportPdd, productReportPddOnlineList, productReportPddOnlineReport, productReportPddOnlineOperateList,
    PddOrderDayReport, PddSkusDayReport, PddGoodCodeDayReport, PddIdDayReport, PddCommodityDayReport, PddOutgoingprofitIDorderdetail, PddShopDayReport, PddDetailDayReport, productReportPddGoods
  },
  data() {
    return {
      that: this,
      pageLoading: false,
      activeName: "first1",
    };
  },
  async mounted() {
    middlevue.$on('toLinkTxDetailDayReport', (data) => {
      if (data.isTrue && data.plat == 'pdd') {
        this.activeName = 'first6';
        setTimeout(() => {
          middlevue.$emit('toLinkTxDetailDayReportQuery', data)
        }, 500);

      } else {
        this.activeName = 'first11';
        setTimeout(() => {
          middlevue.$emit('toLinkTxOutgoingprofitIDorderdetailQuery', data)
        }, 500);
      }
    })
  },
  beforeDestroy() {
    middlevue.$off('toLinkTxDetailDayReport')
  },
  methods: {
    ChangeActiveName(activeName) {
      this.activeName = 'first2';
      this.$refs.PddOrderDayReport.PddGoodCodeDayReportArgument(activeName)
    },
    ChangeActiveName2(activeName, No, Time) {
      this.activeName = 'first6';
      this.$refs.PddDetailDayReport.PddDetailDayReportArgument(activeName, No, Time)
    }

  },
};
</script>

<style lang="scss" scoped></style>
