<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button style="padding: 0;margin:0px 5px 0px 0px;" clearable>
                <el-radio-group v-model="payTimeSel" @change="payTimeSelChange">
                    <el-radio-button label="1">昨天至今日</el-radio-button>
                    <el-radio-button label="2">近7天</el-radio-button>
                    <el-radio-button label="3">近14天</el-radio-button>
                    <el-radio-button label="4">近30天</el-radio-button>
                </el-radio-group>
            </el-button>
            <el-button style="padding: 0;margin:0px 5px 0px 0px;">
                <el-radio-group v-model="filter.isHistory">
                    <el-radio-button label="false">超时未处理</el-radio-button>
                    <el-radio-button label="true">超时已处理</el-radio-button>
                </el-radio-group>
            </el-button>
            <el-button style="padding: 0;margin:0px 5px 0px 0px;">
                <el-radio-group v-model="filter.warehouseStr">
                    <el-radio-button label="全仓">全仓</el-radio-button>
                    <el-radio-button label="义乌仓">义乌仓</el-radio-button>
                    <el-radio-button label="南昌仓">南昌仓</el-radio-button>
                    <el-radio-button label="南昌定制仓">南昌定制仓</el-radio-button>
                    <el-radio-button label="其他">其他</el-radio-button>
                </el-radio-group>
            </el-button>
            <el-button style="padding: 0;margin:0px 5px 0px 0px;">
                <el-date-picker style="width:220px;height: 30px;" v-model="filter.paytimerange" type="datetimerange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="支付开始时间" end-placeholder="支付结束时间" @change="payTimeSelChangePick" clearable></el-date-picker>
            </el-button>
            <el-button type="primary" @click="getData">查询</el-button>
        </template>
        <template>
            <el-collapse v-model="activeNames">
                <el-collapse-item v-for="stat in statList" :title="stat.dataSourceStr" :name="stat.dataSourceStr">
                    <div v-for="details in stat.details" class="divMainParent">
                        <div class="divWare">
                            <el-radio-group v-model="filter.warehouseStrObject[stat.dataSourceStr+details.responsibleDepartment+details.responsiblePerson]" 
                             @input="onWareDtrChange(filter.warehouseStrObject[stat.dataSourceStr+details.responsibleDepartment+details.responsiblePerson],stat.dataSourceStr,details.responsibleDepartment)">
                                <el-radio-button label="全仓">全仓</el-radio-button>
                                <el-radio-button label="义乌仓">义乌仓</el-radio-button>
                                <el-radio-button label="南昌仓">南昌仓</el-radio-button>
                                <el-radio-button label="南昌定制仓">南昌定制仓</el-radio-button>
                                <el-radio-button label="其他">其他</el-radio-button>
                            </el-radio-group>
                        </div>
                        <div class="divMain" v-on:click="clickDiv(stat.dataSourceStr,0,details.responsibleDepartment,details.sumOrderCount)">
                            <div class="divMain-title">
                                <span>{{details.responsibleDepartment}}</span>
                                <span>负责人：{{details.responsiblePerson}}</span>
                            </div>
                            <div class="divMain-sum" v-on:click.stop="clickDiv(stat.dataSourceStr,0,details.responsibleDepartment,details.sumOrderCount)">
                                <span>
                                    全部订单数：
                                </span>
                                <span>
                                    {{details.sumOrderCount}}&nbsp;
                                    <img src="@/static/images/qushitu.png" alt="趋势图" width="17" height="17" />&nbsp;
                                    <img src="@/static/images/daochu.png" alt="导出" width="17" height="17" v-on:click.stop="clickExport(stat.dataSourceStr,0,details.responsibleDepartment,filter.warehouseStrObject[stat.dataSourceStr+details.responsibleDepartment+details.responsiblePerson])"/>
                                </span>
                            </div>
                            <div v-for="datas in details.datas" class="divMain-text" v-on:click.stop="clickDiv(stat.dataSourceStr,datas.timeFilterType,details.responsibleDepartment,datas.orderCount)">
                                <span>
                                    {{datas.remainingTimeStr}}：
                                </span> 
                                <span>
                                    {{datas.orderCount}}&nbsp;
                                    <img src="@/static/images/qushitu.png" alt="趋势图" width="17" height="17" />&nbsp;
                                    <img src="@/static/images/daochu.png" alt="导出" width="17" height="17" v-on:click.stop="clickExport(stat.dataSourceStr,datas.timeFilterType,details.responsibleDepartment,filter.warehouseStrObject[stat.dataSourceStr+details.responsibleDepartment+details.responsiblePerson])"/>
                                </span>
                            </div>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
        </template>
        <el-dialog title="订单趋势图" v-if="dialogVisible" :visible.sync="dialogVisible" width="70%" height="600px" v-dialogDrag>
            <buschar ref="logisticsEarlyWarChart" :analysisData="logisticsEarlyWarChartData" :thisStyle="thisStyle" :GridStyle="gridStyle" v-loading="chartLoading"></buschar>
        </el-dialog>
    </my-container>
</template>

<script>
    import dayjs from "dayjs";
    import { formatTime } from "@/utils";
    import MyContainer from '@/components/my-container'
    import buschar from '@/components/Bus/buschar'
    import { getLogisticsEarlyWarStatDataAsync, getLogisticsEarlyWarStatAnalysisAsync ,exportLogisticsEarlyWarStatDataAsync ,getLogisticsEarlyWarStatDataByTypeAsync } from "@/api/order/logisticsEarlyWarPage";
import { assertLiteral } from "@babel/types";
    const startTime = formatTime(dayjs().subtract(30, 'day'), "YYYY-MM-DD");
    const endTime = formatTime(new Date(), "YYYY-MM-DD");
    export default {
        name: 'LogisticsEarlyWarStat',
        components: { MyContainer, buschar },
        data () {
            return {
                filter: {
                    paytimerange: [startTime, endTime],
                    isHistory: false,
                    dataType: 1,
                    responsibleDepartment: "",
                    timeFilterType: 0,
                    warehouseStr:"全仓",
                    warehouseStrObject:{},
                },
                statList: [],
                activeNames: ["发货快超时", "揽收快超时", "揽收未更新"],
                payTimeSel: "",
                pageLoading: false,
                dialogVisible: false,
                chartLoading: false,
                logisticsEarlyWarChartData: {},
                thisStyle: { width: '100%', height: '600px', 'box-sizing': 'border-box', 'line-height': '260px' },
                gridStyle: {
                    left: 0,
                    right: 0,
                    bottom: 20,
                    top: '12%',
                    containLabel: true
                },
            }
        },
        async mounted () {
            await this.getData();
        },
        methods: {
            async clickDiv (dataSourceStr, timeFilterType, responsibleDepartment, orderCount) {
                //大于0才展示趋势图
                if (orderCount > 0) {
                    switch (dataSourceStr) {
                        case "发货快超时":
                            this.filter.dataType = 1;
                            break;
                        case "揽收快超时":
                            this.filter.dataType = 2;
                            break;
                        case "揽收未更新":
                            this.filter.dataType = 3;
                            break;
                    }
                    this.filter.timeFilterType = timeFilterType;
                    this.filter.responsibleDepartment = responsibleDepartment;
                    this.dialogVisible = true;
                    await this.getAnalysisData();

                }
            },
            async getAnalysisData () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.chartLoading = true;
                this.logisticsEarlyWarChartData = await getLogisticsEarlyWarStatAnalysisAsync(params);
                await this.$refs.logisticsEarlyWarChart.initcharts();
                this.chartLoading = false;
            },
            async getData () {
                var params = this.getCondition();
                if (params === false) {
                    return;
                }
                this.pageLoading = true
                this.statList = await getLogisticsEarlyWarStatDataAsync(params);
                this.pageLoading = false;

                //创建每个框框的仓库数据源
                await this.createWareStrObject();
            },
            getCondition () {
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    this.filter.payStartdate = this.filter.paytimerange[0];
                    this.filter.payEnddate = this.filter.paytimerange[1];
                } else {
                    this.filter.payStartdate = null;
                    this.filter.payEnddate = null;
                }
                const params = {
                    ...this.filter
                }
                return params;
            },
            //时间设置
            payTimeSelChangePick () {
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    if (this.filter.paytimerange[1] != formatTime(new Date(), "YYYY-MM-DD")) {
                        this.payTimeSel = "";
                        return;
                    }
                    var d1 = dayjs(this.filter.paytimerange[0]);
                    var d2 = dayjs(this.filter.paytimerange[1]);
                    switch (d2.diff(d1, "day")) {
                        //昨天至今日
                        case 1:
                            this.payTimeSel = "1";
                            break;
                        //近7天
                        case 6:
                            this.payTimeSel = "2";
                            break;
                        //近14天
                        case 13:
                            this.payTimeSel = "3";
                            break;
                        //近30天
                        case 29:
                            this.payTimeSel = "4";
                            break;
                        //默认
                        default:
                            this.payTimeSel = "";
                            break;
                    }
                }
                else {

                    this.payTimeSel = "";
                }

            },
            //付款时间设置默认值
            payTimeSelChange () {
                let oneDayTime = 24 * 60 * 60 * 1000;
                switch (this.payTimeSel) {
                    //昨天至今日
                    case "1":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 1 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近7天
                    case "2":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 6 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近14天
                    case "3":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 13 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //近30天
                    case "4":
                        this.filter.paytimerange = [
                            formatTime(new Date(new Date().getTime() - 29 * oneDayTime), "YYYY-MM-DD 00:00:00"),
                            formatTime(new Date(), "YYYY-MM-DD 23:59:59")
                        ]
                        break;
                    //默认
                    default:
                        this.filter.paytimerange = [];
                        break;

                }
            },
            //获取参数
            async getExportParam(exdataSourceStr,extimeFilterType,exresponsibleDepartment)
            {
               let dataType=null;
               if(exdataSourceStr=="发货快超时")
                   dataType=1;
               if(exdataSourceStr=="揽收快超时")
                   dataType=2;
               if(exdataSourceStr=="揽收未更新")
                   dataType=3;
                let payStartdate = null;
                let payEnddate = null;
                if (this.filter.paytimerange && this.filter.paytimerange.length > 1) {
                    payStartdate = this.filter.paytimerange[0];
                    payEnddate = this.filter.paytimerange[1];
                } 
                let params=
                {
                    currentPage:1,
                    pageSize:50,
                    dataType:dataType,
                    isHistory:this.filter.isHistory,
                    warehouseStr:this.filter.warehouseStr,
                    timeFilterType:extimeFilterType,
                    responsibleDepartment:exresponsibleDepartment,
                    payStartdate:payStartdate,
                    payEnddate:payEnddate,
                };
                return params;
            },
            async clickExport(exdataSourceStr,extimeFilterType,exresponsibleDepartment,ware)
            {
                let params=await this.getExportParam(exdataSourceStr,extimeFilterType,exresponsibleDepartment);
                params.warehouseStr=ware;
                var loadingInstance = this.$loading({ text: "正在导出，请稍后", fullscreen: false });
                var res = await exportLogisticsEarlyWarStatDataAsync(params);
                loadingInstance.close();
                if (!res?.data) return
                var fileName = res.headers['content-disposition'].split("=")[2];
                fileName = decodeURIComponent(fileName.split("'")[2]);
                const aLink = document.createElement("a");
                let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
                aLink.href = URL.createObjectURL(blob)
                aLink.setAttribute('download', fileName);
                aLink.click();
            },
            //创建每个框框的仓库数据源
            async createWareStrObject()
            {
                if(this.statList.length>0)
                {
                    let wareKeyValue=[]; 
                    this.statList.forEach(f1 => 
                    {
                        if(f1.details.length>0)
                        {
                            f1.details.forEach(f2 => 
                            {
                                let fKey=(f1.dataSourceStr+f2.responsibleDepartment+f2.responsiblePerson);
                                wareKeyValue.push({key:fKey,value:this.filter.warehouseStr});
                            });
                        }
                    });
                    if(wareKeyValue.length>0)
                    {
                        var jsonObject={};
                        for (let i = 0; i < wareKeyValue.length; i++) {
                            this.$set(jsonObject, wareKeyValue[i].key, wareKeyValue[i].value);
                        }
                        this.filter.warehouseStrObject=jsonObject;
                    }
                }
            },
            async onWareDtrChange(value,dataSourceStr,responsibleDepartment)
            {
                let params=await this.getExportParam(dataSourceStr,0,responsibleDepartment);
                params.warehouseStr=value;
                let changeList= await getLogisticsEarlyWarStatDataByTypeAsync(params);
                if(changeList&&changeList.length>0)
                {
                    this.statList.forEach(f1 => 
                    {
                        if(changeList[0].dataSourceStr==f1.dataSourceStr)
                        {
                            f1.details.forEach(f2 => 
                            {
                                if(changeList[0].details[0].responsibleDepartment==f2.responsibleDepartment)
                                {
                                    f2.sumOrderCount=changeList[0].details[0].sumOrderCount;
                                    f2.datas=changeList[0].details[0].datas;
                                }
                            });
                        }
                    });
                }
            }
        },
    }
</script>

<style scoped>
    ::v-deep .el-radio-button--mini .el-radio-button__inner
    {
        padding: 7px 9px;
    }
    ::v-deep .el-link.el-link--primary {
        margin-right: 7px;
    }
    ::v-deep #app .el-container .is-vertical .main .el-main .el-tabs--top {
        height: 95% !important;
        background-color: red;
    }
    ::v-deep .el-collapse-item__header {
        font-size: 18px;
        font-weight: 800;
    }
    .divMainParent {
        display: inline-block;
        box-sizing: border-box;
    }
    .divWare {
        margin-top: 10px;
        margin-left: 47px;
        width: 285px;
        text-align: center;
        box-sizing: border-box;
    }
    .divMain {
        margin-top: 10px;
        margin-left: 50px;
        width: 280px;
        border-radius: 20px;
        border: 1px solid rgb(61, 127, 255);
        text-align: center;
        cursor: pointer;
        color: #fff;
        box-sizing: border-box;
    }
    .divMain:hover {
        margin-top: 10px;
        margin-left: 50px;
        width: 280px;
        border-radius: 20px;
        border: 1px solid rgb(61, 127, 255);
        text-align: center;
        cursor: pointer;
        color: #fff;
        box-shadow: rgb(61 127 255 / 37%) 0px 0px 12px;
        transform: scale(1.05);
        box-sizing: border-box;
    }
    .divMain-title {
        margin-top: 5px;
        color: black;
        font-size: 16px;
        font-weight: 800;
        display: flex;
        margin-left: 15px;
        margin-right: 15px;
        justify-content: space-between;
    }
    .divMain-sum {
        margin-top: 5px;
        color: black;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        margin-left: 15px;
        margin-right: 15px;
        justify-content: space-between;
    }
    .divMain-text {
        margin-top: 1px;
        color: black;
        display: flex;
        margin-left: 15px;
        margin-right: 15px;
        justify-content: space-between;
    }
    .divMain-text:hover {
        margin-top: 1px;
        color: rgb(61, 127, 255);
        display: flex;
        margin-left: 15px;
        margin-right: 15px;
        transform: scale(1.04);
        justify-content: space-between;
    }
</style>
